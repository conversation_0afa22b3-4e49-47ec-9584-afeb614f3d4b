const fs = require('fs');

// 读取implementation.ts文件
const content = fs.readFileSync('./src/data/ecma/promises/implementation.ts', 'utf8');

// 提取visualization字段
const match = content.match(/visualization:\s*`([^`]+(?:`[^`]+`[^`]*)*)`/s);
if (match) {
  const visualizationContent = match[1];
  console.log('=== Visualization Content ===');
  console.log(visualizationContent.substring(0, 500) + '...');
  
  // 查找所有Mermaid代码块
  const mermaidBlocks = visualizationContent.match(/```mermaid[\s\S]*?```/g);
  
  if (mermaidBlocks) {
    console.log('\n=== Found', mermaidBlocks.length, 'Mermaid Blocks ===');
    
    mermaidBlocks.forEach((block, index) => {
      console.log(`\n--- Block ${index + 1} ---`);
      
      // 提取纯Mermaid代码（去掉```mermaid和```）
      const mermaidCode = block.replace(/```mermaid\s*\n?/, '').replace(/\n?```/, '');
      
      console.log('Raw block length:', block.length);
      console.log('Cleaned code length:', mermaidCode.length);
      
      // 检查前几行
      const lines = mermaidCode.split('\n');
      console.log('First 3 lines:');
      lines.slice(0, 3).forEach((line, i) => {
        console.log(`  ${i + 1}: "${line}"`);
      });
      
      // 检查是否有问题字符
      const problemChars = mermaidCode.match(/[^\x00-\x7F]/g);
      if (problemChars) {
        console.log('Non-ASCII characters found:', [...new Set(problemChars)]);
      }
      
      // 检查是否有markdown标题混入
      const markdownTitles = lines.filter(line => line.includes('##') || line.includes('**'));
      if (markdownTitles.length > 0) {
        console.log('Markdown titles found:', markdownTitles);
      }
    });
  } else {
    console.log('No Mermaid blocks found');
  }
} else {
  console.log('No visualization field found');
}
