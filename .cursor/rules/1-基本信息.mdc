---
description: 
globs: 
alwaysApply: false
---
# 📋 Tab 1: 基本信息 (basic-info.ts) 专用提示词

## 🚨 重要编码约束（最高优先级）

### 🛡️ 数据一致性强约束（零容忍错误）
**⚠️ 生成基本信息后必须同时更新 index.ts 文件，确保数据一致性**

```typescript
// ❌ 常见错误 - 只更新 basic-info.ts，忘记更新 index.ts
// 导致页面显示 {API_BRIEF_DESCRIPTION} 等骨架内容！

// ✅ 正确做法 - 同时更新两个文件
// 1. 更新 basic-info.ts 的实际内容
const basicInfo: BasicInfo = {
  definition: "useId是React 18中用于生成唯一标识符的Hook...",
  syntax: "function useId(): string",
  commonUseCases: [
    {
      title: "表单元素关联",
      code: "function LoginForm() { const id = useId(); return <><label htmlFor={id}>用户名</label><input id={id} /></>; }"
    }
  ],
  limitations: ["不能用作React元素的key属性"],
  // ...
};

// 2. 【关键】同时更新 index.ts 的顶层字段
const useIdData: ApiItem = {
  description: "useId是React 18中用于生成唯一标识符的Hook...", // 从 basicInfo.definition 复制
  syntax: "function useId(): string",                              // 从 basicInfo.syntax 复制
  example: "function LoginForm() { ... }",                        // 从 basicInfo.commonUseCases[0].code 复制
  notes: "不能用作React元素的key属性",                             // 从 basicInfo.limitations[0] 复制
  version: "React 18.0.0+",                                       // 根据API版本设置
  tags: ["ID生成", "SSR兼容", "无障碍性"],                         // 根据API特性设置
  // ...
  basicInfo, // Tab内容
};
```

**🚨 强制检查清单**:
- [ ] basic-info.ts 已更新实际内容
- [ ] index.ts 的 description 字段已从 basicInfo.definition 同步
- [ ] index.ts 的 syntax 字段已从 basicInfo.syntax 同步
- [ ] index.ts 的 example 字段已从 basicInfo.commonUseCases[0].code 同步
- [ ] index.ts 的 notes 字段已从 basicInfo.limitations[0] 同步
- [ ] index.ts 的 version 和 tags 字段已根据API特性设置
- [ ] 页面不再显示 {XXX} 格式的骨架内容

### ❌ 严格禁止的语法
**绝对不能使用任何形式的模板字符串插值语法 `${xxx}`**

```typescript
// ❌ 绝对禁止 - 基本信息代码示例中的错误写法
const apiDescription = `${apiName}用于${purpose}`;
const exampleCode = `const [${stateName}, set${StateName}] = useState(${initialValue})`;
const typeDefinition = `${paramName}: ${paramType}`;
const warningMessage = `注意：${apiName}在${condition}时会${behavior}`;

// ✅ 强制要求 - 正确的基本信息写法
const apiDescription = apiName + '用于' + purpose;
const exampleCode = 'const [' + stateName + ', set' + StateName + '] = useState(' + initialValue + ')';
const typeDefinition = paramName + ': ' + paramType;
const warningMessage = '注意：' + apiName + '在' + condition + '时会' + behavior;
```

### 🛡️ 基本信息编码特殊要求
- **API定义**: 语法说明中不使用模板字符串
- **参数示例**: 参数演示代码使用标准语法
- **返回值说明**: 返回值示例避免模板字符串
- **最佳实践**: 推荐的代码模式要符合规范

## 🎯 Tab定位与价值
基本信息Tab是API文档的**核心入口和快速参考**，必须在3分钟内让开发者完全理解API的本质、用法和限制。体现"知识考古者"的精神，深度挖掘API的历史背景、设计理念和技术演进。

## 🎨 样式与实现要求

### Header设计
```tsx
{/* 简洁Header - 注意垂直居中 */}
<div className="flex items-center gap-3 mb-6">
  <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
    <span className="text-white text-xs">📖</span>
  </div>
  <Title level={4} className="!mb-0 text-slate-900 font-medium">基本信息</Title>
  <span className="text-slate-500 text-sm">核心概念与基础语法</span>
</div>
```

### 内容样式 - 专业博客风格
- **主色调**: 黑白灰专业配色，避免花哨色块
- **可读性**: 确保文字对比度足够，易于长时间阅读
- **层次感**: 通过字体大小和颜色深浅建立信息层次
- **代码块**: 使用统一的代码高亮和格式

## 📊 内容结构要求

### 必须包含的9个核心部分：

```typescript
export const basicInfo = {
  definition: string,        // 一句话定义（30-50字）
  introduction: string,      // 详细介绍（100-200字）
  syntax: string,           // 完整TypeScript语法

  // 🆕 新增字段 - 核心概览优化
  quickExample: string,      // 完整且简单的基础用法示例（带中文注释）
  scenarioDiagram: string | Array<{  // 业务场景Mermaid图表（支持多个图表）
    title: string;           // 图表标题
    description: string;     // 图表描述
    diagram: string;         // Mermaid图表代码
  }>,

  parameters: Parameter[],   // 详细参数说明
  returnValue: ReturnValue,  // 返回值类型和说明
  keyFeatures: Feature[],    // 3-5个核心特性
  limitations: string[],     // 3-5条使用限制
  bestPractices: string[],   // 5-8条最佳实践
  warnings: string[]         // 3-5条重要警告
};
```

## ✍️ 编写指南

### 1. 📝 definition - 一句话定义
**目标**: 让完全不懂这个API的人立即理解其核心价值

**🆕 强化要求（基于深度分析需求）**:
- **战略定位**: 明确API在整个框架生态中的核心角色
- **核心使命**: 说明它解决的根本性问题
- **存在价值**: 解释若缺少它会带来什么问题

**要求**:
- 30-50字精准描述
- 突出API的独特价值
- 避免技术术语堆砌
- 必须回答"这个API解决什么问题"
- **🆕 体现在框架中的战略地位**

**模板**:
```
"【战略定位】{API名称}是{框架名称}中{技术领域}的{核心组件}，【核心使命】专门用于{核心功能}，【存在价值】解决了{关键问题}，【技术优势】其核心优势是{主要优势}。"
```

**示例**:
```
"【战略定位】useState是React Hooks体系中状态管理的基础组件，【核心使命】专门用于在函数组件中添加状态管理功能，【存在价值】解决了函数组件无法保存状态的根本问题，【技术优势】其核心优势是简单直观且性能高效。"
```

### 2. 📖 introduction - 详细介绍
**目标**: 在100-200字内全面展示API的应用场景和技术特点

**🆕 强化要求（深度技术分析）**:
- **设计哲学**: 阐述背后的技术理念和设计权衡
- **历史背景**: 说明为什么需要这个API
- **技术创新**: 相比传统方案的创新点
- **生态地位**: 在整个技术生态中的位置

**要求**:
- 包含使用场景（何时用）
- 说明技术特点（如何用）
- 对比替代方案（为什么用）
- 提及常见应用领域
- **🆕 设计理念和技术权衡**
- **🆕 历史演进背景**

**结构模板**:
```
"【历史背景】{API名称}是为了解决{历史问题}而在{版本}中引入的{技术方案}。

【设计哲学】它遵循{设计理念}（如：声明式编程、组合优于继承、不可变性等），在{性能}和{易用性}之间做出了{权衡选择}。

【核心功能】主要用于{使用场景1}、{使用场景2}和{使用场景3}。相比{替代方案}，它的创新在于{技术创新点}。

【生态地位】在{框架名称}生态中，它是{技术层级}的{核心地位}，常见于{应用领域}，特别适合{特定需求}的场景。

【技术优势】核心优势包括{优势1}、{优势2}，但也需要注意{限制或代价}。"
```

### 3. 💻 quickExample - 完整基础示例
**目标**: 提供完整且简单的基础用法，替代原来的半截代码示例

**🆕 新增要求**:
- **完整可运行**: 必须是完整的组件代码，不能是代码片段
- **中文注释**: 关键部分必须有中文注释说明
- **基础用法**: 展示最基本最常用的使用方式
- **简洁明了**: 代码简洁但功能完整

**模板**:
```typescript
quickExample: `function ${API_NAME}Example() {
  // [API调用说明 - 中文注释]
  const [变量名] = ${API_NAME}([参数说明]);

  return (
    <div>
      {/* [使用场景说明 - 中文注释] */}
      <[组件名]
        [属性名]={变量名}
        [其他属性]="[值]"
      >
        [内容说明]
      </[组件名]>
    </div>
  );
}`,
```

### 4. 🗺️ scenarioDiagram - 业务场景图表
**目标**: 通过多个Mermaid图表直观展示API的使用场景和相关技术

**🆕 新增要求**:
- **多图表支持**: 支持单个图表或多个图表数组
- **业务场景**: 展示什么业务场景下使用这个API
- **相关技术**: 显示与此API配合使用的其他API或技术
- **技术特性**: 突出API的核心技术特性
- **中文标注**: 所有节点和连线都用中文标注
- **详细描述**: 每个图表都有标题和描述

**单图表模板**:
```typescript
scenarioDiagram: `graph TD
    A[{API_NAME}核心场景] --> B[场景1]
    A --> C[场景2]
    A --> D[场景3]

    B --> B1[具体用法1]
    B --> B2[具体用法2]

    C --> C1[具体用法3]
    C --> C2[具体用法4]

    D --> D1[具体用法5]
    D --> D2[具体用法6]

    E[相关API] --> F[API1]
    E --> G[API2]
    E --> H[API3]

    style A fill:#e1f5fe
    style E fill:#f3e5f5`,
```

**多图表模板**:
```typescript
scenarioDiagram: [
  {
    title: "核心应用场景",
    description: "{API_NAME}在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用这个API",
    diagram: `graph LR
    A[{API_NAME}核心场景] --> B[场景1]
    A --> C[场景2]
    A --> D[场景3]

    B --> B1["📊 具体应用1<br/>详细描述"]
    B --> B2["🔍 具体应用2<br/>详细描述"]

    C --> C1["👀 具体应用3<br/>详细描述"]
    C --> C2["📈 具体应用4<br/>详细描述"]

    D --> D1["👥 具体应用5<br/>详细描述"]
    D --> D2["🎓 具体应用6<br/>详细描述"]

    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
  },
  {
    title: "技术实现架构",
    description: "{API_NAME}的技术实现架构，展示其核心机制和与其他技术的集成关系",
    diagram: `graph TB
    A[{API_NAME}技术架构] --> B[核心层]
    A --> C[集成层]
    A --> D[应用层]

    B --> B1["🔗 核心机制1<br/>技术实现"]
    B --> B2["🎨 核心机制2<br/>技术实现"]

    C --> C1["🏗️ 集成方式1<br/>技术细节"]
    C --> C2["🛠️ 集成方式2<br/>技术细节"]

    D --> D1["🔧 应用场景1<br/>实际使用"]
    D --> D2["📦 应用场景2<br/>实际使用"]

    style A fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    style B fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    style C fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    style D fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px`
  },
  {
    title: "生态系统集成",
    description: "{API_NAME}在技术生态系统中的位置和与其他API的协作关系",
    diagram: `graph TD
    A[技术生态系统] --> B[核心APIs]
    A --> C[开发工具链]
    A --> D[社区生态]

    B --> B1["API1<br/>基础功能"]
    B --> B2["API2<br/>扩展功能"]
    B --> B3["{API_NAME}<br/>目标功能"]
    B --> B4["API4<br/>相关功能"]

    C --> C1["工具1<br/>开发支持"]
    C --> C2["工具2<br/>构建支持"]

    D --> D1["库1<br/>社区方案"]
    D --> D2["库2<br/>第三方集成"]

    B3 -.-> B1
    B3 -.-> B2
    B3 -.-> C1
    B3 -.-> D1

    style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
    style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style B3 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
  }
],
```

### 5. 🔧 syntax - 完整TypeScript语法
**目标**: 提供可直接复制使用的完整语法定义

**🆕 强化要求（深度类型分析）**:
- **源码定位**: 指出类型定义的源码位置
- **泛型解析**: 详细解释泛型参数的含义和约束
- **重载分析**: 说明不同重载形式的使用场景
- **类型推导**: 解释TypeScript如何进行类型推导

**要求**:
- 包含完整的TypeScript类型定义
- 标注可选参数和默认值
- 提供泛型支持的完整写法
- 包含所有重载形式
- **🆕 源码位置说明**
- **🆕 类型推导机制**

**格式要求**:
```typescript
// 🆕 源码定位信息
/**
 * 类型定义位置：
 * - 主要类型：@types/react/index.d.ts:1089
 * - 实现文件：packages/react/src/ReactHooks.js:90
 * - 内部类型：packages/react-reconciler/src/ReactInternalTypes.js:144
 */

// 基础语法
const [state, setState] = useState<T>(initialState: T | (() => T)): [T, Dispatch<SetStateAction<T>>];

// 🆕 泛型约束分析
/**
 * 泛型参数 T 的约束：
 * - T 可以是任意类型，包括原始类型、对象、数组、函数等
 * - 当 T 是函数类型时，需要使用函数形式的初始值避免立即执行
 * - TypeScript 会根据 initialState 自动推导 T 的类型
 */

// 重载形式（如果有）
function useState<S>(initialState: S | (() => S)): [S, Dispatch<SetStateAction<S>>];
function useState<S = undefined>(): [S | undefined, Dispatch<SetStateAction<S | undefined>>];

// 🆕 类型推导示例
const [count, setCount] = useState(0);        // T 推导为 number
const [user, setUser] = useState<User | null>(null); // 显式指定类型
const [items, setItems] = useState(() => []);  // T 推导为 never[]，建议显式指定
```

**🆕 类型推导机制说明**:
```typescript
// TypeScript 类型推导规则
interface TypeInferenceRules {
  // 1. 基于初始值推导
  basicInference: "useState(0) → [number, Dispatch<SetStateAction<number>>]";
  
  // 2. 泛型显式指定
  explicitGeneric: "useState<string>('') → [string, Dispatch<SetStateAction<string>>]";
  
  // 3. 联合类型推导
  unionType: "useState<User | null>(null) → [User | null, Dispatch<SetStateAction<User | null>>]";
  
  // 4. 函数形式初始值
  lazyInitial: "useState(() => expensive()) → 延迟执行，避免每次渲染都计算";
}
```

### 🆕 可视化架构图
```mermaid
graph TD
    A["API调用层"] --> B["TypeScript类型层"]
    B --> C["React Hooks层"]
    C --> D["Fiber调度层"]
    D --> E["状态存储层"]
    
    subgraph "类型系统"
        B1["泛型推导"]
        B2["类型约束"]
        B3["重载解析"]
    end
    
    subgraph "运行时系统"
        C1["Hook链表"]
        C2["状态队列"]
        C3["更新调度"]
    end
    
    B --> B1
    B --> B2
    B --> B3
    
    C --> C1
    C --> C2
    C --> C3
    
    style A fill:#e3f2fd
    style E fill:#e8f5e8
    style B1 fill:#fff3e0
    style C1 fill:#fce4ec
```

### 4. 📋 parameters - 详细参数说明
**目标**: 让开发者完全理解每个参数的作用和使用方法

**🆕 强化要求（深度参数分析）**:
- **参数设计原理**: 解释为什么这样设计参数
- **类型约束分析**: 详细说明类型约束的原因
- **性能影响**: 不同参数对性能的影响
- **最佳实践**: 参数使用的最佳实践

**每个参数必须包含**:
```typescript
interface Parameter {
  name: string;          // 参数名
  type: string;          // TypeScript类型
  required: boolean;     // 是否必需
  defaultValue?: string; // 默认值
  description: string;   // 详细说明
  example: string;       // 使用示例
  designRationale: string; // 🆕 设计原理
  performanceImpact: string; // 🆕 性能影响
  bestPractices: string[]; // 🆕 最佳实践
}
```

**🆕 参数深度分析模板**:
```typescript
{
  name: "initialState",
  type: "T | (() => T)",
  required: true,
  description: "组件的初始状态值，可以是具体值或返回初始值的函数",
  
  // 🆕 设计原理
  designRationale: "支持函数形式是为了延迟计算昂贵的初始值，避免每次渲染都重新计算。这体现了React的性能优化理念。",
  
  // 🆕 性能影响
  performanceImpact: "函数形式只在初始化时执行一次，可以避免每次渲染的不必要计算。直接传值形式在每次渲染时都会重新创建（如果是对象或数组）。",
  
  // 🆕 最佳实践
  bestPractices: [
    "昂贵计算使用函数形式：useState(() => expensiveCalculation())",
    "简单值直接传递：useState(0)",
    "对象/数组初始值使用函数形式避免重复创建：useState(() => [])",
    "避免在渲染过程中改变初始值的引用"
  ],
  
  example: `
// ✅ 推荐：昂贵计算使用函数形式
const [data, setData] = useState(() => processLargeDataset());

// ✅ 推荐：简单值直接传递  
const [count, setCount] = useState(0);

// ❌ 不推荐：每次渲染都创建新数组
const [items, setItems] = useState([]);

// ✅ 推荐：使用函数形式
const [items, setItems] = useState(() => []);
  `
}
```

### 5. 🔄 returnValue - 返回值说明
**目标**: 清晰说明API返回的数据结构和使用方法

**必须包含**:
```typescript
interface ReturnValue {
  type: string;        // 返回值类型
  description: string; // 返回值说明
  properties?: PropertyDescription[]; // 对象属性（如果适用）
  example: string;     // 使用示例
}
```

### 6. ⭐ keyFeatures - 核心特性
**目标**: 突出API的3-5个最重要的技术亮点

**每个特性包含**:
```typescript
interface Feature {
  title: string;       // 特性标题
  description: string; // 详细描述
  benefit: string;     // 带来的好处
  example?: string;    // 代码示例（可选）
}
```

**选择标准**:
- 与竞争API的差异化优势
- 解决特定问题的独特能力
- 对开发效率的显著提升
- 性能或安全方面的突出表现

### 7. ⚠️ limitations - 使用限制
**目标**: 客观诚实地说明API的使用限制和约束

**要求**:
- 3-5条最重要的限制
- 说明限制的技术原因
- 提供替代解决方案
- 帮助开发者避免常见陷阱

**格式**:
```
"不能在{场景}中使用，因为{技术原因}。建议使用{替代方案}。"
```

### 8. ✅ bestPractices - 最佳实践
**目标**: 基于真实项目经验，提供5-8条实用的开发建议

**来源**:
- 官方文档推荐
- 社区最佳实践
- 真实项目经验
- 性能优化经验

**格式要求**:
- 每条建议要具体actionable
- 解释为什么这样做
- 提供具体的实现方法
- 避免空泛的理论建议

### 9. 🚨 warnings - 重要警告
**目标**: 标注3-5个最容易犯错的问题和安全隐患

**重点覆盖**:
- 常见错误模式
- 安全风险提示
- 性能陷阱警告
- 兼容性问题说明

## 🎯 质量标准

### 内容质量检查
- [ ] **定义精准**: 一句话准确概括API核心价值
- [ ] **介绍全面**: 覆盖场景、特点、优势、应用
- [ ] **语法完整**: TypeScript定义准确且完整
- [ ] **参数详细**: 每个参数都有清晰说明和示例
- [ ] **返回值明确**: 数据结构和使用方法清楚
- [ ] **特性突出**: 3-5个关键亮点明确有价值
- [ ] **限制客观**: 诚实说明使用约束和技术原因
- [ ] **实践实用**: 最佳实践基于真实经验且可执行
- [ ] **警告重要**: 警告涵盖最常见和最严重的问题

### 技术质量检查
- [ ] **类型准确**: 所有TypeScript类型定义正确
- [ ] **示例可运行**: 所有代码示例完整且可执行
- [ ] **中文表达**: 所有用户可见内容使用中文
- [ ] **术语统一**: 技术术语使用一致
- [ ] **格式规范**: 遵循Markdown和代码格式标准

### 用户价值检查
- [ ] **新手友好**: 初学者能理解基本概念
- [ ] **专家有用**: 有经验的开发者能找到深度信息
- [ ] **快速查阅**: 支持快速定位关键信息
- [ ] **解决问题**: 帮助开发者解决实际开发问题

## 📝 编写流程建议

### 1. 前期准备
- 深入研究API的官方文档
- 收集社区最佳实践和常见问题
- 分析类似API的优缺点
- 准备真实的使用案例

### 2. 内容创作
- 从definition开始，确保核心价值清晰
- 基于真实场景编写introduction
- 仔细验证syntax的TypeScript类型
- 通过实际测试确保parameters准确
- 用具体示例说明returnValue
- 选择最有价值的keyFeatures
- 客观诚实地描述limitations
- 收集并验证bestPractices
- 突出最重要的warnings

### 3. 质量检查
- 让其他开发者review内容
- 测试所有代码示例
- 检查中文表达的准确性
- 验证技术信息的正确性
- 确保对目标用户有价值

## 💡 常见问题避免

### ❌ 避免的问题
- **过于简单**: 仅仅复制官方文档
- **过于复杂**: 包含太多高级细节
- **不够客观**: 只说优点不说缺点
- **示例无效**: 代码示例无法运行
- **表达模糊**: 使用过多技术黑话

### ✅ 追求的目标
- **价值明确**: 立即理解API的核心价值
- **信息完整**: 涵盖所有关键信息点
- **表达清晰**: 中文表达准确易懂
- **示例实用**: 代码示例贴近真实场景
- **平衡客观**: 优缺点都诚实说明

---

**🎯 核心原则**: 基本信息Tab是整个API文档的基础，必须在保证完整性的同时突出实用性，让开发者能够快速上手并避免常见错误。

