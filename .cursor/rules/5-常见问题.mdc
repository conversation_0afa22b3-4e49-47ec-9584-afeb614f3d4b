---
description: 
globs: 
alwaysApply: false
---
# ❓ Tab 5: 常见问题 (common-questions.ts) 专用提示词

## 🚨 重要编码约束（最高优先级）

### ❌ 严格禁止的语法
**绝对不能使用任何形式的模板字符串插值语法 `${xxx}`**

```typescript
// ❌ 绝对禁止 - 常见问题代码示例中的错误写法
const errorMessage = `组件${componentName}在第${lineNumber}行出错`;
const debugInfo = `当前状态：${JSON.stringify(state)}`;
const solutionCode = `修复方案：将${oldCode}改为${newCode}`;
const tipMessage = `提示：${tipContent}`;

// ✅ 强制要求 - 正确的常见问题写法
const errorMessage = '组件' + componentName + '在第' + lineNumber + '行出错';
const debugInfo = '当前状态：' + JSON.stringify(state);
const solutionCode = '修复方案：将' + oldCode + '改为' + newCode;
const tipMessage = '提示：' + tipContent;
```

### 🛡️ 常见问题编码特殊要求
- **错误示例**: 展示错误代码时不使用模板字符串
- **解决方案**: 修复代码中避免模板字符串语法
- **调试技巧**: 调试代码使用标准输出方式
- **最佳实践**: 推荐代码遵循项目编码规范

## 🎯 Tab定位与价值
常见问题Tab是**开发实战的救命指南**，收集真实开发中遇到的高频问题和解决方案，帮助开发者快速解决实际问题，避免重复踩坑。体现"真问题"的精神，聚焦于开发者真正遇到的实际问题。

## 🎨 样式与实现要求

### Header设计
```tsx
{/* 简洁Header - 注意垂直居中 */}
<div className="flex items-center gap-3 mb-6">
  <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
    <span className="text-white text-xs">❓</span>
  </div>
  <Title level={4} className="!mb-0 text-slate-900 font-medium">常见问题</Title>
  <span className="text-slate-500 text-sm">开发中的常见问题与解决方案</span>
</div>
```

### 内容样式 - 专业博客风格
- **主色调**: 黑白灰专业配色，避免花哨色块
- **问题卡片**: 使用简洁的卡片设计展示问题和解决方案
- **严重程度标识**: 清晰的严重程度和频率标识
- **真问题导向**: 聚焦于开发者真正遇到的实际问题，体现"真问题"精神

## 📊 内容结构要求

### 必须包含的问题数组：

```typescript
interface CommonQuestion {
  id: string;              // 问题ID
  question: string;        // 问题描述
  severity: 'low' | 'medium' | 'high' | 'critical'; // 严重程度
  frequency: 'high' | 'medium' | 'low'; // 出现频率
  category: string;        // 问题分类
  answer: {
    solution: string;      // 解决方案
    explanation: string;   // 原因解释
    prevention: string;    // 预防措施
    code?: {
      wrong: string;       // 错误示例
      correct: string;     // 正确示例
    };
    relatedIssues?: string[]; // 相关问题
  };
  tags: string[];          // 相关标签
  sources: string[];       // 问题来源
}

export const commonQuestions: CommonQuestion[] = [
  // 5-8个真实开发问题
];
```

## ✍️ 编写指南

### 📋 问题收集策略

#### 问题来源优先级：
1. **Stack Overflow高频问题** - 全球开发者的真实困惑
2. **GitHub Issues** - 开源项目中的实际问题
3. **技术社区讨论** - 掘金、知乎、V2EX等平台
4. **个人开发经验** - 实际项目中遇到的问题
5. **团队内部分享** - 团队成员的踩坑经验

#### 必须覆盖的问题类型：
1. **🚨 Critical（1-2个）**: 影响应用正常运行的严重问题
2. **⚠️ High（2-3个）**: 影响功能实现或性能的重要问题  
3. **🔶 Medium（2-3个）**: 影响开发效率或代码质量的问题
4. **ℹ️ Low（1-2个）**: 次要问题或优化建议

### 🎯 具体编写要求

#### 1. question - 问题描述
**要求**:
- 描述清晰具体，避免模糊表述
- 使用开发者熟悉的术语
- 包含关键的错误信息或症状
- 能够让遇到类似问题的人快速识别

**问题描述模板**:

**错误类型**:
```
"使用{API名称}时出现'{错误信息}'错误"
"{API名称}在{特定场景}下不工作/表现异常"
"为什么{API名称}{异常行为}？"
```

**性能类型**:
```
"{API名称}在{场景}下性能很差"
"如何优化{API名称}的{性能维度}？"
"{API名称}导致{性能问题}怎么办？"
```

**使用类型**:
```
"如何正确使用{API名称}实现{功能}？"
"{API名称}与{其他技术}一起使用时的问题"
"什么时候应该避免使用{API名称}？"
```

#### 2. severity - 严重程度评估
**评估标准**:
- **critical**: 导致应用崩溃或无法使用
- **high**: 严重影响功能或用户体验
- **medium**: 影响开发效率或代码质量
- **low**: 次要问题，有workaround方案

#### 3. frequency - 出现频率
**评估依据**:
- **high**: 大多数开发者都会遇到（>60%）
- **medium**: 经常遇到但不是普遍性（20-60%）
- **low**: 在特定场景下才会遇到（<20%）

#### 4. category - 问题分类
**常用分类**:
```
"使用错误"     // 错误的使用方式
"性能问题"     // 性能相关问题
"兼容性问题"   // 浏览器或版本兼容性
"配置问题"     // 环境或配置错误
"集成问题"     // 与其他技术集成时的问题
"调试困难"     // 难以调试和定位的问题
"最佳实践"     // 如何更好地使用
"边界条件"     // 特殊情况下的问题
```

#### 5. answer.solution - 解决方案
**要求**:
- 提供具体可执行的解决步骤
- 包含完整的代码示例
- 考虑不同情况的解决方案
- 优先提供最简单有效的方法

**解决方案结构**:
```
"**快速解决**：
{最直接的解决方法}

**完整方案**：
1. {步骤1}
2. {步骤2}
3. {步骤3}

**代码示例**：
{完整可运行的代码}

**替代方案**：
{其他可能的解决方法}"
```

#### 6. answer.explanation - 原因解释
**要求**:
- 深入解释问题产生的根本原因
- 帮助开发者理解技术原理
- 连接问题现象和底层机制
- 使用通俗易懂的语言

**解释模板**:
```
"这个问题的根本原因是{技术原因}。

{API名称}在{内部机制}时，{具体过程}。当{触发条件}时，就会导致{问题现象}。

具体来说：
1. {原因分析1}
2. {原因分析2}
3. {原因分析3}"
```

#### 7. answer.prevention - 预防措施
**要求**:
- 提供预防问题的具体方法
- 包含最佳实践建议
- 考虑开发流程和代码规范
- 提供可检测问题的工具或方法

**预防措施类型**:
```
"代码规范"：{编码时的注意事项}
"类型检查"：{TypeScript类型定义}
"测试用例"：{针对此问题的测试}
"开发工具"：{辅助检测的工具}
"团队规范"：{团队层面的预防措施}
```

#### 8. answer.code - 代码示例
**要求**:
- 同时提供错误和正确的代码对比
- 代码注释详细，标注关键点
- 可以直接复制运行
- 体现最佳实践

**代码对比格式**:
```typescript
// ❌ 错误做法 - 导致问题的代码
function WrongExample() {
  // 错误的实现
  const [state, setState] = useState(null);
  
  // 直接使用可能为null的state
  return <div>{state.value}</div>; // 这里会报错
}

// ✅ 正确做法 - 修复后的代码
function CorrectExample() {
  // 正确的实现
  const [state, setState] = useState(null);
  
  // 安全的空值检查
  return (
    <div>
      {state ? state.value : '加载中...'}
    </div>
  );
}

// 🚀 最佳实践 - 更完善的解决方案
function BestPracticeExample() {
  // 使用更好的初始状态
  const [state, setState] = useState({ value: '', loading: true });
  
  return (
    <div>
      {state.loading ? '加载中...' : state.value}
    </div>
  );
}
```

### 🎯 质量标准

#### 内容质量检查
- [ ] **问题真实**: 基于真实开发中的实际问题
- [ ] **描述准确**: 问题描述清晰，易于识别
- [ ] **解决有效**: 解决方案经过验证，确实可行
- [ ] **原因清晰**: 问题原因分析深入准确
- [ ] **预防实用**: 预防措施具体可执行
- [ ] **覆盖全面**: 涵盖不同严重程度和类型的问题

#### 技术质量检查
- [ ] **代码正确**: 所有代码示例语法正确且可运行
- [ ] **方案最优**: 提供的解决方案是最佳实践
- [ ] **兼容性好**: 考虑不同环境和版本的兼容性
- [ ] **性能友好**: 解决方案不会引入新的性能问题

#### 实用价值检查
- [ ] **快速定位**: 开发者能快速找到对应问题
- [ ] **立即解决**: 提供的方案能立即解决问题
- [ ] **举一反三**: 帮助理解类似问题的解决思路
- [ ] **避免重复**: 有效预防同类问题再次发生

### 📝 编写流程建议

#### 1. 问题收集
- 监控Stack Overflow、GitHub Issues
- 收集团队内部的问题反馈
- 分析用户社区的讨论热点
- 记录个人开发中的踩坑经验

#### 2. 问题筛选
- 按照频率和严重程度排序
- 选择最具代表性的5-8个问题
- 确保覆盖不同类型和难度
- 验证问题的普遍性

#### 3. 解决方案验证
- 测试每个解决方案的有效性
- 确保代码示例可以运行
- 考虑不同场景下的适用性
- 寻找最简单有效的解决方法

#### 4. 内容组织
- 按照重要性和相关性组织
- 添加清晰的分类和标签
- 提供问题间的关联信息
- 确保易于搜索和查找

## 💡 问题分类指南

### 🚨 Critical级问题示例：
- **应用崩溃**: 导致整个应用无法启动或运行
- **内存泄漏**: 严重的内存泄漏导致页面卡死
- **数据丢失**: 状态更新丢失或数据不一致
- **安全漏洞**: 可能导致安全问题的使用方式

### ⚠️ High级问题示例：
- **功能无效**: API在特定条件下不工作
- **性能严重**: 严重影响用户体验的性能问题
- **兼容性**: 在某些环境下完全无法使用
- **错误处理**: 错误信息不清晰或处理不当

### 🔶 Medium级问题示例：
- **开发困难**: 使用复杂或容易出错
- **代码质量**: 影响代码可维护性
- **性能优化**: 可以优化但不是严重问题
- **最佳实践**: 如何更好地使用API

### ℹ️ Low级问题示例：
- **使用疑问**: 关于使用方法的疑问
- **边界情况**: 特殊场景下的处理
- **代码风格**: 代码组织和规范建议
- **工具集成**: 与开发工具的集成问题

## ❌ 常见问题避免

### 避免的问题：
- **问题虚假**: 编造不存在的问题
- **解决无效**: 提供的解决方案无法解决问题
- **过于简单**: 只是API使用说明，不是真实问题
- **过于复杂**: 超出API本身范围的复杂问题
- **缺乏预防**: 只解决问题，不提供预防措施

### 追求的目标：
- **问题精准**: 准确识别开发者的真实痛点
- **解决有效**: 提供立即可用的解决方案
- **理解深入**: 帮助开发者理解问题根源
- **预防完善**: 避免同类问题反复出现
- **经验传承**: 传递实战经验和最佳实践

## 🔍 问题优先级评估

### 评估维度：
1. **影响范围** - 有多少开发者会遇到
2. **严重程度** - 对开发或用户的影响有多大
3. **解决难度** - 开发者是否容易自己解决
4. **学习价值** - 解决这个问题能学到什么

### 优先收录标准：
- 高频次 + 高严重程度 = 必须收录
- 高频次 + 中等严重程度 = 优先收录
- 中等频次 + 高严重程度 = 优先收录
- 低频次但学习价值高 = 可以收录

---

**🎯 核心原则**: 常见问题Tab要成为开发者的问题解决宝库，通过收集真实问题和提供有效解决方案，帮助开发者避免重复踩坑，提高开发效率。
