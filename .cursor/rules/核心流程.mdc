---
description: 
globs: 
alwaysApply: true
---
# 🚀 API文档智能工作流程 - 核心流程

创建和完善文档的核心流程，请一定要记住：/scripts/api-workflow.md

## 📋 API命名规范（重要！）

### 🎯 统一命名约定
遵循 **React 官方命名约定**，确保与开发者习惯和 IDE 智能提示一致：

#### 🔧 Hook APIs（小写开头驼峰）
```
useEffect/          useState/           useCallback/
useContext/         useMemo/            useRef/
useReducer/         useOptimistic/      useTransition/
useDeferredValue/   useSyncExternalStore/
```
**规则**：React Hooks 按约定都以 `use` 开头，使用**小写驼峰命名**

#### 🧩 Component APIs（大写开头帕斯卡）
```
Fragment/           Suspense/           Portal/
Memo/               ForwardRef/         StrictMode/
ComponentClass/     PureComponent/      FunctionComponent/
ReactElement/       ReactNode/          ErrorBoundary/
```
**规则**：React 组件和 API 按约定使用**帕斯卡命名**（首字母大写）

### ✅ 命名优势

1. **🔍 快速识别**：
   - `use*` → 立即知道是 Hook 相关
   - 大写开头 → 立即知道是组件/API 相关

2. **📚 符合学习习惯**：
   ```javascript
   // React 官方命名示例
   import { useState, useEffect } from 'react';        // ✅ 小写
   import { Fragment, Suspense, memo } from 'react';   // ✅ 大写
   ```

3. **🛠️ IDE 友好**：自动补全按类型分组，搜索时按模式过滤

4. **📖 文档直观**：URL 路径直接反映 API 类型

### 🚨 严格禁止
- ❌ 带小数点的命名：`React.ComponentClass`
- ❌ 不一致的前缀：`ReactuseState`
- ❌ 混合大小写错误：`UseMemo`, `fragment`

## 🎯 新版重大更新（2025.06）

### 📂 统一平铺目录结构（已重构完成）
**重构优势**：简化维护，统一路径，消除路由错误

#### 新结构（当前）
```
src/data/
├── react/
│   ├── useOptimistic/     # Hook APIs 直接平铺
│   ├── createContext/     # Component APIs 直接平铺
│   ├── useState/
│   └── ... (35个API统一平铺)
└── vue/
    ├── reactive/          # Vue APIs 直接平铺
    ├── ref/
    └── ... (所有API统一平铺)
```

#### 旧结构（已废弃）
```
❌ react/hooks/useOptimistic/
❌ react/components/createContext/
❌ vue/composition-api/reactive/
```

### 🔧 新增重构工具
```bash
# React目录重构
tsx scripts/restructure-react-apis.ts --execute

# Vue目录重构
tsx scripts/restructure-vue-apis.ts --execute

# 增强版检测（替代旧版check-content-completion）
tsx scripts/enhanced-content-completion.ts <api-name>

# 🆕 Puppeteer验收测试（关键质量保障）
tsx scripts/puppeteer/index.ts <api-name> --headless
```

## 📊 完整工作流程俯瞰图 (新增Puppeteer验收测试版)

```mermaid
flowchart TD
    Start([🚀 开始: API文档创建]) --> ValidateApiName{🚨 验证API名称格式}
    
    ValidateApiName -->|包含小数点| ApiNameError[❌ API名称错误: 不能包含小数点]
    ValidateApiName -->|格式正确| DetectAPI{🔍 检测API状态}
    
    DetectAPI --> CheckFiles{📁 检查文件存在}
    CheckFiles -->|文件不存在| Phase1[🏗️ 阶段1: 骨架生成]
    CheckFiles -->|文件存在| SkeletonCheck{🔍 骨架字符串检测}
    
    %% 阶段1: 骨架生成
    Phase1 --> GenerateSkeleton[🎯 生成API骨架结构]
    GenerateSkeleton --> CreateDirs[📂 创建平铺目录结构]
    CreateDirs --> Generate9Tabs[📄 生成9个Tab占位符文件]
    Generate9Tabs --> SetSkeletonTokens[🏷️ 设置骨架占位符]
    SetSkeletonTokens --> Phase2[🎨 阶段2: 内容填充]
    
    %% 骨架字符串检测
    SkeletonCheck -->|发现骨架字符串| Phase2
    SkeletonCheck -->|无骨架字符串| BuildCheck[🏗️ 阶段3: 构建检测]
    
    %% 阶段2: 内容填充（核心工作）
    Phase2 --> PriorityFlow{📋 按优先级处理Tab}
    
    %% 高优先级Tab
    PriorityFlow -->|High Priority| HighPriorityTabs[🔥 高优先级Tab处理]
    HighPriorityTabs --> BasicInfo[📖 basic-info.ts 检测API_NAME]
    BasicInfo --> BusinessScenarios[💼 business-scenarios.ts 检测SCENARIO_1_TITLE]
    BusinessScenarios --> CheckHighSkeleton{🔍 高优先级骨架检测}
    
    %% 中优先级Tab
    CheckHighSkeleton -->|无骨架字符串| MediumPriorityTabs[⚡ 中优先级Tab处理]
    MediumPriorityTabs --> Implementation[🔧 implementation.ts 检测IMPLEMENTATION_MECHANISM]
    Implementation --> InterviewQuestions[🎯 interview-questions.ts 检测QUESTION_1]
    InterviewQuestions --> CommonQuestions[❓ common-questions.ts 检测QUESTION_1]
    CommonQuestions --> Performance[⚡ performance-optimization.ts 检测STRATEGY_1]
    Performance --> Debugging[🐛 debugging-tips.ts 检测INTRODUCTION]
    Debugging --> CheckMediumSkeleton{🔍 中优先级骨架检测}
    
    %% 低优先级Tab
    CheckMediumSkeleton -->|无骨架字符串| LowPriorityTabs[📚 低优先级Tab处理]
    LowPriorityTabs --> Knowledge[🏛️ knowledge-archaeology.ts 检测INTRODUCTION]
    Knowledge --> Essence[🧠 essence-insights.ts 检测CORE_QUESTION]
    Essence --> CheckLowSkeleton{🔍 低优先级骨架检测}
    
    %% 内容生成子流程
    CheckHighSkeleton -->|发现骨架字符串| ContentGeneration
    CheckMediumSkeleton -->|发现骨架字符串| ContentGeneration
    CheckLowSkeleton -->|发现骨架字符串| ContentGeneration
    
    subgraph ContentGeneration [🎨 内容生成子流程]
        OpenFile[📝 打开包含骨架字符串的文件] --> UseMDC[📋 参考对应MDC模板]
        UseMDC --> ReplaceTokens[🔄 替换骨架占位符为实际内容]
        ReplaceTokens --> UseCursor[🤖 使用Cursor AI辅助生成内容]
        UseCursor --> ValidateContent[✅ 验证内容质量]
        ValidateContent --> RunSkeletonCheck[🔍 运行增强版骨架检测]
    end
    
    %% 完成所有Tab后的流程
    CheckLowSkeleton -->|无骨架字符串| SyncIndexFile[🔄 同步index.ts文件]
    SyncIndexFile --> ValidateInterface[🔍 验证TypeScript接口一致性]
    ValidateInterface --> BuildCheck
    
    %% 阶段3: 构建检测阶段
    subgraph BuildCheck [🏗️ 阶段3: 构建检测与修复]
        RunBuild[🏗️ npm run build] --> ParseErrors{🔍 解析构建错误}
        ParseErrors -->|无错误| BuildSuccess[✅ 构建成功]
        ParseErrors -->|有错误| ErrorAnalysis[📊 错误分析和分类]
        ErrorAnalysis --> AutoFix[🔧 自动修复尝试]
        AutoFix --> RetryBuild[🔄 重新构建]
        RetryBuild --> ParseErrors
    end
    
    %% 🚨 新增阶段4: Puppeteer验收测试
    BuildSuccess --> PuppeteerTest[🎭 阶段4: Puppeteer验收测试]
    
    subgraph PuppeteerTest [🎭 阶段4: Puppeteer验收测试 - 关键质量保障]
        StartPuppeteer[🚀 启动无头浏览器] --> LoadPage[📄 加载API文档页面]
        LoadPage --> CheckRender{🔍 页面渲染检查}
        CheckRender -->|渲染失败| RenderErrors[❌ 捕获渲染错误]
        CheckRender -->|渲染成功| ComponentCheck[🧩 组件渲染验证]
        ComponentCheck --> MermaidCheck[📊 Mermaid图表验证]
        MermaidCheck --> RuntimeCheck[⚡ 运行时错误检测]
        RuntimeCheck --> GenerateReport[📋 生成验收报告]
        
        RenderErrors --> AnalyzeErrors[📊 分析渲染错误]
        AnalyzeErrors --> FixRequired[🔧 需要修复]
    end
    
    %% 验收测试结果分支
    GenerateReport --> TestPassed{✅ 验收测试通过?}
    TestPassed -->|通过| AcceptanceSuccess[🎉 验收成功]
    TestPassed -->|失败| AcceptanceFailure[❌ 验收失败]
    FixRequired --> AcceptanceFailure
    
    %% 验收成功流程
    AcceptanceSuccess --> FinalValidation[✅ 最终验证]
    FinalValidation --> UpdateProgressMD[📝 更新progress.md状态]
    UpdateProgressMD --> GenerateArtifacts[📦 生成最终产物]
    GenerateArtifacts --> Complete([🎉 完成: API文档已就绪])
    
    %% 验收失败处理
    AcceptanceFailure --> ManualIntervention[⚠️ 需要人工干预]
    ManualIntervention --> Manual[👨‍💻 人工修复问题]
    Manual --> PuppeteerTest
    
    %% 构建失败处理
    AutoFix -->|修复失败| ManualIntervention
    
    %% 特殊情况处理
    ApiNameError --> FixApiName[🔧 修正API名称]
    FixApiName --> Start
    
    %% 检测脚本回流
    RunSkeletonCheck -->|仍有骨架| ContentGeneration
    RunSkeletonCheck -->|无骨架| SyncIndexFile
    
    %% 样式设置
    classDef phaseBox fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef highPriority fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef mediumPriority fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef lowPriority fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef buildPhase fill:#fff8e1,stroke:#f9a825,stroke-width:2px
    classDef puppeteerPhase fill:#ffe0e6,stroke:#d81b60,stroke-width:3px
    classDef errorPhase fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef detectionPhase fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    classDef successPhase fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    
    class Phase1,Phase2 phaseBox
    class HighPriorityTabs,BasicInfo,BusinessScenarios highPriority
    class MediumPriorityTabs,Implementation,InterviewQuestions,CommonQuestions,Performance,Debugging mediumPriority
    class LowPriorityTabs,Knowledge,Essence lowPriority
    class BuildCheck,RunBuild,ParseErrors,ErrorAnalysis,AutoFix,RetryBuild,BuildSuccess buildPhase
    class PuppeteerTest,StartPuppeteer,LoadPage,CheckRender,ComponentCheck,MermaidCheck,RuntimeCheck,GenerateReport,RenderErrors,AnalyzeErrors,FixRequired puppeteerPhase
    class ManualIntervention,ApiNameError,FixApiName,AcceptanceFailure errorPhase
    class SkeletonCheck,CheckHighSkeleton,CheckMediumSkeleton,CheckLowSkeleton,RunSkeletonCheck detectionPhase
    class AcceptanceSuccess,FinalValidation,Complete successPhase
```

## 🎭 Puppeteer验收测试流程详解

```mermaid
flowchart TD
    StartTest([🎭 启动Puppeteer验收测试]) --> InitBrowser[🚀 初始化无头浏览器]
    
    InitBrowser --> LoadAPI[📄 加载API文档页面]
    LoadAPI --> WaitLoad[⏳ 等待页面完全加载]
    
    WaitLoad --> PageCheck{🔍 页面基础检查}
    PageCheck -->|加载失败| LoadError[❌ 页面加载错误]
    PageCheck -->|加载成功| ComponentScan[🧩 组件渲染扫描]
    
    ComponentScan --> ReactCheck[⚛️ React组件检查]
    ReactCheck --> ObjectRenderCheck[📦 对象渲染验证]
    ObjectRenderCheck --> MermaidScan[📊 Mermaid图表扫描]
    
    MermaidScan --> ErrorCapture[⚡ JavaScript错误捕获]
    ErrorCapture --> PerformanceCheck[📈 性能指标检测]
    
    PerformanceCheck --> GenerateTestReport[📋 生成详细测试报告]
    GenerateTestReport --> TestResult{✅ 测试结果判定}
    
    TestResult -->|全部通过| TestSuccess[🎉 验收测试成功]
    TestResult -->|存在问题| TestFailure[❌ 验收测试失败]
    
    LoadError --> ErrorDetails[📝 错误详情记录]
    TestFailure --> ErrorDetails
    ErrorDetails --> FixGuidance[🛠️ 修复指导建议]
    
    TestSuccess --> CleanUp[🧹 清理测试环境]
    FixGuidance --> CleanUp
    CleanUp --> End([📊 测试完成])
    
    classDef startNode fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef processNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef checkNode fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef successNode fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    classDef errorNode fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef endNode fill:#e0f2f1,stroke:#00695c,stroke-width:3px
    
    class StartTest,InitBrowser startNode
    class LoadAPI,WaitLoad,ComponentScan,ReactCheck,ObjectRenderCheck,MermaidScan,ErrorCapture,PerformanceCheck,GenerateTestReport processNode
    class PageCheck,TestResult checkNode
    class TestSuccess,CleanUp successNode
    class LoadError,TestFailure,ErrorDetails,FixGuidance errorNode
    class End endNode
```

## 🔍 新版骨架字符串检测机制

```mermaid
flowchart TD
    StartDetection([🎯 启动内容检测]) --> LoadConfig[📋 加载检测规则配置]
    
    LoadConfig --> TabLoop{📂 遍历9个Tab文件}
    
    TabLoop --> CheckFile[📄 检查文件存在性]
    CheckFile -->|文件不存在| MarkIncomplete[❌ 标记为未完成]
    CheckFile -->|文件存在| ReadContent[📖 读取文件内容]
    
    ReadContent --> SkeletonScan[🔍 扫描骨架字符串]
    
    subgraph SkeletonRules [📋 检测规则映射]
        BasicInfoRule[basic-info 检测 API_NAME]
        BusinessRule[business-scenarios 检测 SCENARIO_1_TITLE]
        CommonQRule[common-questions 检测 QUESTION_1]
        DebuggingRule[debugging-tips 检测 INTRODUCTION]
        EssenceRule[essence-insights 检测 CORE_QUESTION]
        ImplRule[implementation 检测 IMPLEMENTATION_MECHANISM]
        InterviewRule[interview-questions 检测 QUESTION_1]
        KnowledgeRule[knowledge-archaeology 检测 INTRODUCTION]
        PerfRule[performance-optimization 检测 STRATEGY_1]
    end
    
    SkeletonScan --> FoundSkeleton{🔍 发现骨架字符串?}
    FoundSkeleton -->|是| MarkIncomplete
    FoundSkeleton -->|否| MarkComplete[✅ 标记为已完成]
    
    MarkIncomplete --> NextFile{📁 还有文件?}
    MarkComplete --> NextFile
    
    NextFile -->|是| TabLoop
    NextFile -->|否| GenerateReport[📊 生成完成度报告]
    
    GenerateReport --> CalcPercentage[📈 计算完成百分比]
    CalcPercentage --> ShowResults[🎨 显示彩色结果]
    
    ShowResults --> ReturnStatus{📊 返回状态}
    ReturnStatus -->|100%完成| ExitSuccess[✅ 退出码: 0]
    ReturnStatus -->|未完成| ExitIncomplete[⚠️ 退出码: 1]
    
    classDef ruleBox fill:#f3e5f5,stroke:#7b1fa2,stroke-width:1px
    classDef processBox fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef resultBox fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    
    class SkeletonRules,BasicInfoRule,BusinessRule,CommonQRule,DebuggingRule,EssenceRule,ImplRule,InterviewRule,KnowledgeRule,PerfRule ruleBox
    class LoadConfig,ReadContent,SkeletonScan,GenerateReport,CalcPercentage processBox
    class MarkComplete,MarkIncomplete,ShowResults,ExitSuccess,ExitIncomplete resultBox
```

## 🏗️ 构建错误检测与修复流程

```mermaid
stateDiagram-v2
    [*] --> 运行构建
    
    运行构建 --> 解析输出: npm run build
    解析输出 --> 构建成功: 无错误输出
    解析输出 --> 错误分析: 发现错误
    
    错误分析 --> TypeScript错误: TS错误模式匹配
    错误分析 --> 语法错误: 语法错误模式匹配
    错误分析 --> 导入错误: 模块导入错误
    错误分析 --> 未知错误: 其他错误类型
    
    TypeScript错误 --> 类型修复: 自动修复类型问题
    语法错误 --> 模板字符串修复: 修复语法
    语法错误 --> 其他语法修复: 修复括号引号等
    导入错误 --> 路径检查: 检查导入路径
    
    类型修复 --> 重新构建: 修复完成
    模板字符串修复 --> 重新构建: 修复完成
    其他语法修复 --> 重新构建: 修复完成
    路径检查 --> 重新构建: 修复完成
    
    重新构建 --> 解析输出: 验证修复结果
    
    TypeScript错误 --> 手动干预: 自动修复失败
    未知错误 --> 手动干预: 无法自动处理
    导入错误 --> 手动干预: 复杂导入问题
    
    手动干预 --> 人工修复: 开发者介入
    人工修复 --> 运行构建: 修复完成后重试
    
    构建成功 --> Puppeteer验收测试: 进入验收阶段
    Puppeteer验收测试 --> [*]: 流程完成
```

## 🎯 新版核心工作流程要点

### ⚠️ 关键改进点

1. **🚨 强制API名称验证**
   - 不能包含小数点 (React.ComponentClass ❌)
   - 使用驼峰命名 (ReactComponentClass ✅)

2. **📂 统一平铺目录结构**
   - React/Vue都采用平铺结构 (react/xxx, vue/xxx)
   - 自动重构工具：restructure-react-apis.ts, restructure-vue-apis.ts
   - 路径统一：framework/api-name

3. **🔍 骨架字符串检测替代completionStatus**
   - 直接检测特定占位符字符串
   - 每个Tab文件有独特的检测规则
   - 100% 准确率，无误判

4. **🏗️ 集成构建检测**
   - 自动运行 npm run build
   - 智能解析构建错误类型
   - 自动修复常见语法错误

5. **🎭 Puppeteer验收测试**（🚨 **新增关键环节**）
   - 页面渲染验证：确保所有页面正确显示
   - 组件渲染检查：验证React组件正确渲染对象
   - JavaScript错误监控：捕获运行时错误
   - Mermaid图表验证：确保图表正确渲染
   - **质量保障**：防止骨架检测通过但页面渲染失败

6. **📊 简化检测流程**
   - 骨架字符串：直接文件内容分析
   - 页面渲染：Puppeteer验收测试
   - 更快、更准确的检测

7. **🔄 智能修复机制**
   - 模板字符串语法自动修复
   - TypeScript类型错误提示
   - 构建失败自动重试
   - 页面渲染错误诊断

### 📋 使用命令

```bash
# 1. 🎯 增强版骨架检测（推荐）
tsx scripts/enhanced-content-completion.ts <api-name>

# 2. 🏗️ 构建错误检测与修复
tsx scripts/build-error-detector.ts --auto-fix

# 3. 🎭 Puppeteer验收测试（必需）⭐
tsx scripts/puppeteer/index.ts <api-name> --headless

# 4. 🔧 目录结构重构
tsx scripts/restructure-react-apis.ts --execute    # React重构
tsx scripts/restructure-vue-apis.ts --execute      # Vue重构

# 5. 📦 完整工作流程
tsx scripts/generate-api-skeleton.ts <api-name> --test --auto-fix
```

### 🆕 新增脚本功能
- **增强版检测**: 48种骨架字符串模式，行级精确定位
- **自动化重构**: 批量目录迁移，安全备份，引用更新
- **构建集成**: 自动错误检测与修复
- **🎭 Puppeteer验收**: 页面渲染验证，质量保障
- **平铺结构支持**: 同时支持新旧目录结构

### 🚨 验收测试关键检查项
- ✅ 页面加载无错误
- ✅ React组件正确渲染（避免"Objects are not valid as a React child"错误）
- ✅ Mermaid图表正常显示
- ✅ JavaScript运行时零错误
- ✅ 数据结构与前端组件匹配