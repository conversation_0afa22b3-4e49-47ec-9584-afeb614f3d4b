---
description: 
globs: 
alwaysApply: false
---
# 💼 Tab 2: 业务场景 (business-scenarios.ts) 专用提示词

## 🚨 重要编码约束（最高优先级）

### 🛡️ 数据一致性提醒
**⚠️ 如果同时更新了基本信息，记得同步更新 index.ts 文件的顶层字段，避免页面显示骨架内容**

### ❌ 严格禁止的语法
**绝对不能使用任何形式的模板字符串插值语法 `${xxx}`**

```typescript
// ❌ 绝对禁止 - 业务场景代码示例中常见错误
const apiUrl = `/api/users/${userId}/posts/${postId}`;
const itemKey = `todo-${item.id}`;
const statusClass = `status-${status}`;
const message = `用户${user.name}完成了${taskCount}个任务`;

// ✅ 强制要求 - 正确的业务场景写法
const apiUrl = '/api/users/' + userId + '/posts/' + postId;
const itemKey = 'todo-' + item.id;
const statusClass = status === 'active' ? 'status-active' : 'status-inactive';
const message = '用户' + user.name + '完成了' + taskCount + '个任务';
```

### 🛡️ 业务场景编码特殊要求
- **API调用**: URL拼接使用字符串拼接，不用模板字符串
- **数据展示**: 动态内容直接在JSX中渲染，避免字符串插值
- **状态管理**: 状态更新函数中避免模板字符串
- **事件处理**: 事件处理函数中的日志输出使用多参数形式

## 🎯 Tab定位与价值
业务场景Tab是**理论与实践的桥梁**，通过3个难度递进的真实案例，让开发者深刻理解API在实际项目中的应用价值和实现技巧。体现"大白话"的精神，用通俗易懂的语言解释复杂的技术概念。

## 🎨 样式与实现要求

### Header设计
```tsx
{/* 简洁Header - 注意垂直居中 */}
<div className="flex items-center justify-between mb-6">
  <div className="flex items-center gap-3">
    <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
      <span className="text-white text-xs">💼</span>
    </div>
    <Title level={4} className="!mb-0 text-slate-900 font-medium">使用示例</Title>
    <span className="text-slate-500 text-sm">业务场景与代码实现</span>
  </div>
  <div className="px-3 py-1.5 bg-slate-100 rounded-full">
    <span className="text-slate-700 text-sm font-medium">{scenarios.length} 个场景</span>
  </div>
</div>
```

### 内容样式 - 专业博客风格
- **主色调**: 黑白灰专业配色，避免花哨色块
- **场景卡片**: 使用简洁的卡片设计展示业务场景
- **代码高亮**: 统一的代码块样式和语法高亮
- **大白话解释**: 用通俗语言解释技术概念，体现"大白话"精神

## 📊 内容结构要求

### 必须包含的3个场景：

```typescript
export const businessScenarios = {
  intro: string,           // 场景综述（50-100字）
  basic: ScenarioCase,     // 🟢 简单场景：基础应用
  intermediate: ScenarioCase, // 🟡 中级场景：实际业务
  advanced: ScenarioCase   // 🔴 高级场景：复杂架构
};

interface ScenarioCase {
  title: string;          // 场景标题
  description: string;    // 场景描述
  businessContext: string; // 业务背景
  technicalRequirements: string[]; // 技术要求
  implementation: string; // 完整实现代码
  keyPoints: string[];    // 关键技术点
  pros: string[];         // 方案优势
  cons: string[];         // 方案局限
  alternativeApproaches: string[]; // 替代方案
}
```

## ✍️ 编写指南

### 1. 📝 intro - 场景综述
**目标**: 50-100字概括API在不同复杂度场景下的应用价值

**要求**:
- 说明API适用的主要业务领域
- 强调学习路径的递进性
- 突出实际项目价值

**模板**:
```
"{API名称}广泛应用于{业务领域1}、{业务领域2}等场景。从{简单应用}到{复杂应用}，展示了{核心价值}。以下三个案例覆盖了从{入门级}到{专家级}的完整应用路径。"
```

### 2. 🟢 basic - 简单场景
**定位**: 新手入门，理解API基础用法的完整流程

#### 选择标准：
- **技术难度**: 初学者能理解和实现
- **业务复杂度**: 单一功能，逻辑清晰
- **代码量**: 30-50行核心代码
- **学习价值**: 覆盖API的基本用法模式

#### 编写要求：

**title**: 直接说明解决的具体问题
```
"实现{具体功能}"
"构建{简单组件}"
"处理{基础场景}"
```

**description**: 30-50字说明这个场景的价值
```
"展示{API}最基础的使用方式，适合{目标用户}快速理解{核心概念}"
```

**businessContext**: 真实业务背景（100-150字）
- 具体的产品需求
- 用户使用场景
- 期望达到的效果

**technicalRequirements**: 3-5个具体技术要求
```
["需要管理表单输入状态", "实时显示用户输入", "处理表单提交逻辑", "提供输入验证反馈"]
```

**implementation**: 完整可运行的代码（30-50行）
- 包含所有必要的import
- 代码结构清晰，注释详细
- 可以直接复制运行
- 体现API的核心用法

**keyPoints**: 3-4个关键技术点
- API的核心概念
- 重要的实现细节
- 新手容易忽略的要点

**pros/cons**: 客观分析优缺点
- pros: 这种实现方式的优势
- cons: 存在的限制和不足

**alternativeApproaches**: 1-2个替代实现方案

### 3. 🟡 intermediate - 中级场景
**定位**: 实际项目应用，展示API在真实业务中的价值

#### 选择标准：
- **技术难度**: 有一定经验的开发者能实现
- **业务复杂度**: 多功能集成，有状态管理
- **代码量**: 50-100行核心代码
- **学习价值**: 展示API的实际项目应用模式

#### 编写要求：

**真实业务场景**:
- 基于实际项目需求
- 包含多个功能模块
- 体现API的核心价值

**技术集成**:
- 与其他API/库的配合使用
- 状态管理和数据流处理
- 错误处理和边界条件

**代码实现**:
- 50-100行完整实现
- 包含错误处理逻辑
- 性能考虑和优化
- 可维护性设计

### 4. 🔴 advanced - 高级场景
**定位**: 复杂架构应用，展示API的高级用法和最佳实践

#### 选择标准：
- **技术难度**: 需要深入理解和丰富经验
- **业务复杂度**: 企业级应用，架构复杂
- **代码量**: 100+行核心代码
- **学习价值**: 展示API的高级模式和架构设计

#### 编写要求：

**企业级应用**:
- 大型项目的实际需求
- 复杂的数据流和状态管理
- 性能优化和可扩展性考虑

**架构设计**:
- 模块化和可复用性
- 类型安全和错误处理
- 测试和调试考虑

**高级技术**:
- API的高级用法和技巧
- 与其他技术栈的深度集成
- 性能优化和最佳实践

## 🎯 质量标准

### 内容质量检查
- [ ] **场景真实**: 基于实际项目需求，不是纯理论案例
- [ ] **难度递进**: 三个场景在技术复杂度上明显递进
- [ ] **代码完整**: 所有代码示例完整且可运行
- [ ] **业务清晰**: 每个场景的业务背景和需求明确
- [ ] **技术准确**: 实现方案技术正确且符合最佳实践
- [ ] **价值突出**: 每个场景都能体现API的实际价值
- [ ] **分析客观**: 优缺点分析客观真实
- [ ] **替代方案**: 提供合理的替代实现思路

### 技术质量检查
- [ ] **类型安全**: 使用完整的TypeScript类型定义
- [ ] **错误处理**: 包含适当的错误处理逻辑
- [ ] **性能考虑**: 体现性能优化和最佳实践
- [ ] **可维护性**: 代码结构清晰，易于理解和维护
- [ ] **注释详细**: 关键代码都有中文注释说明

### 学习价值检查
- [ ] **入门友好**: 简单场景新手能理解和实现
- [ ] **实用价值**: 中级场景能解决实际项目问题
- [ ] **深度学习**: 高级场景展示复杂应用和架构思维
- [ ] **知识递进**: 三个场景形成完整的学习路径
- [ ] **实践指导**: 提供可直接应用的实现方案

## 📝 编写流程建议

### 1. 场景选择
- 研究API的主要应用领域
- 收集真实项目案例
- 分析不同复杂度的需求
- 确定三个代表性场景

### 2. 内容创作
**简单场景**:
- 从API的最基本用法开始
- 确保新手能完全理解
- 代码简洁但功能完整

**中级场景**:
- 基于实际项目需求
- 展示API与其他技术的配合
- 包含实用的错误处理

**高级场景**:
- 体现企业级应用的复杂性
- 展示架构设计和性能优化
- 提供可扩展的解决方案

### 3. 质量检查
- 测试所有代码示例
- 验证业务场景的真实性
- 检查技术实现的正确性
- 确保学习路径的连贯性

## 💡 场景选择指南

### 🟢 简单场景典型模式：
- **表单组件**: 基础输入和状态管理
- **计数器**: 简单状态更新逻辑
- **开关切换**: 布尔状态控制
- **列表渲染**: 基本数据展示

### 🟡 中级场景典型模式：
- **购物车**: 复杂状态管理和业务逻辑
- **搜索功能**: 异步数据处理和状态同步
- **表单验证**: 多字段关联和错误处理
- **主题切换**: 全局状态和上下文管理

### 🔴 高级场景典型模式：
- **数据大屏**: 性能优化和大量数据处理
- **实时协作**: 复杂状态同步和冲突处理
- **可视化编辑器**: 复杂交互和状态管理
- **企业后台**: 权限控制和模块化架构

## ❌ 常见问题避免

### 避免的问题：
- **场景虚假**: 编造不存在的业务需求
- **难度失衡**: 三个场景难度差异不明显
- **代码无效**: 示例代码无法运行或有错误
- **过于复杂**: 高级场景超出API本身的范围
- **缺乏实用性**: 纯粹的演示代码，没有实际价值

### 追求的目标：
- **场景真实**: 基于实际项目需求和用户反馈
- **路径清晰**: 形成完整的从入门到精通的学习路径
- **代码实用**: 可以直接应用到实际项目中
- **价值明确**: 每个场景都能解决具体的开发问题
- **经验传递**: 分享实际开发中的技巧和坑点

---

**🎯 核心原则**: 业务场景Tab要成为开发者的实战指南，通过真实案例传递API的实际应用价值和最佳实践经验。
