---
description: API骨架生成器 - 基于useSyncExternalStore完美实现的升级版
globs: 
alwaysApply: false
---
# 🦴 API骨架生成器 - 完美体系版本

## 🚨 核心原则 - 基于useSyncExternalStore完美实现

### 🎯 设计目标
基于当前useSyncExternalStore的完美实现，生成具有相同体系、质量、规范和效果的API文档骨架。

### ⚠️ 强制约束（零容忍违反）
1. **禁止生成任何具体内容** - 仅生成占位符模板
2. **禁止代码实现细节** - 所有代码用`{占位符}`替代
3. **禁止业务场景描述** - 所有描述用占位符表示
4. **禁止性能数据** - 所有数据用占位符表示
5. **禁止具体API信息** - 连API名称都用占位符
6. **禁止复制其他API的具体内容** - 如"外部状态同步"、"四重境界"等
7. **禁止具体的技术术语** - 如"useSyncExternalStore"、"状态撕裂"等
8. **禁止具体的注释描述** - 如"🎯 核心问题 - 技术灵魂考古"等
9. **禁止任何实际的代码示例** - 必须用占位符替代
10. **禁止真实的面试题内容** - 问题和答案都必须是占位符

### 🛡️ 数据一致性强约束（最高优先级）
**⚠️ 重要提醒：生成骨架后，必须提醒用户同时更新 index.ts 文件**

```typescript
// ❌ 常见错误 - 只更新Tab文件，忘记更新index.ts
// 这会导致页面显示骨架内容而不是实际内容！

// 生成骨架后必须提醒：
// "⚠️ 重要：请记得同时更新 index.ts 文件中的以下字段：
// - description: 从 basicInfo.definition 获取
// - syntax: 从 basicInfo.syntax 获取
// - example: 从 basicInfo.commonUseCases[0].code 获取
// - notes: 从 basicInfo.limitations[0] 获取
// - version: 根据API版本设置
// - tags: 根据API特性设置
//
// 否则页面会显示 {API_BRIEF_DESCRIPTION} 等骨架内容！"
```

### 🚨 生成完成后的强制提醒
**每次生成骨架后，必须输出以下提醒：**

```
🛡️ 重要提醒：数据一致性检查

生成骨架完成后，请务必：

1. ✅ 更新各个Tab文件的实际内容
2. ⚠️ 【关键】同时更新 index.ts 文件中的顶层字段：
   - description: '{API_BRIEF_DESCRIPTION}' → 实际描述
   - syntax: '{API_SYNTAX}' → 实际语法
   - example: '{API_EXAMPLE}' → 实际示例
   - notes: '{API_NOTES}' → 实际注意事项
   - version: '{API_VERSION}' → 实际版本
   - tags: ['{API_TAG_1}'] → 实际标签

3. 🔍 检查页面是否还显示 {XXX} 格式的骨架内容
4. 🚀 访问 http://localhost:8080/react 验证效果

如果忘记更新 index.ts，页面会显示骨架内容而不是实际内容！
```

### 🌟 体现核心精神
- **不停追问**: 永远不满足于表面答案，持续深挖
- **知识考古**: 挖掘技术背后的历史脉络和深层动机
- **大白话**: 用通俗易懂的语言表达深刻的洞察
- **真问题**: 聚焦于真正重要的根本性问题

### 🎯 基于useSyncExternalStore完美实现
当前useSyncExternalStore的实现已经达到完美状态，具备：
- ✅ **专业博客风格**: 黑白灰配色，内容为王
- ✅ **垂直居中对齐**: 所有Header完美对齐
- ✅ **统一Tab导航**: 圆角卡片式设计，视觉一致
- ✅ **四重境界本质洞察**: 核心问题→设计智慧→应用洞察→架构思考
- ✅ **深度内容质量**: 每个Tab都有实质性的技术洞察
- ✅ **新手友好**: 专业但不晦涩，体现"大白话"精神

**生成的骨架必须100%复制这种完美的体系、质量、规范和效果！**

### 🔴 最高优先级安全约束（防范运行时错误）

#### 🚨 绝对禁止模板字符串语法
**在任何情况下都不能使用 `${}` 模板字符串插值语法**
```typescript
// ❌ 绝对禁止
const content = `${apiName}用于${purpose}`;
const code = `const [${state}, set${State}] = useState(${initial})`;

// ✅ 必须使用占位符
const content = `{API_DESCRIPTION}`;
const code = `{CODE_EXAMPLE}`;
```

#### 🚨 TypeScript接口一致性检查协议
**创建数据文件时，必须严格遵循TypeScript接口定义**

**A. 接口字段完全匹配原则**
```typescript
// ❌ 绝对禁止字段名不匹配
interface DebuggingTips {
  troubleshooting: Array<{
    symptom: string;     // 接口要求 symptom
    solutions: string[]; // 接口要求 solutions
  }>;
}

// 错误的数据文件
const debuggingTips = {
  troubleshooting: [{
    symbol: '{SYMPTOM}',      // ❌ symbol !== symptom
    debugSteps: ['{STEP}'],   // ❌ debugSteps !== solutions
  }]
};

// ✅ 正确匹配接口
const debuggingTips = {
  troubleshooting: [{
    symptom: '{SYMPTOM}',     // ✅ 严格匹配
    solutions: ['{SOLUTION}'] // ✅ 严格匹配
  }]
};
```

**B. 数据结构类型匹配原则**
```typescript
// ❌ 绝对禁止结构类型不匹配
interface PerformanceOptimization {
  performanceMetrics: {      // 接口要求对象
    [key: string]: MetricInfo;
  };
  commonPitfalls: Array<{    // 接口要求对象数组
    issue: string;
    cause: string;
    solution: string;
  }>;
}

// 错误的数据文件
const performanceOptimization = {
  performanceMetrics: [      // ❌ 数组 !== 对象
    '{METRIC_1}', '{METRIC_2}'
  ],
  commonPitfalls: [          // ❌ 字符串数组 !== 对象数组
    '{PITFALL_1}', '{PITFALL_2}'
  ]
};

// ✅ 正确匹配接口
const performanceOptimization = {
  performanceMetrics: {      // ✅ 对象结构匹配
    '{METRIC_1_NAME}': {
      description: '{METRIC_1_DESCRIPTION}',
      tool: '{METRIC_1_TOOL}',
      example: '{METRIC_1_EXAMPLE}'
    }
  },
  commonPitfalls: [          // ✅ 对象数组匹配
    {
      issue: '{PITFALL_1_ISSUE}',
      cause: '{PITFALL_1_CAUSE}',
      solution: '{PITFALL_1_SOLUTION}'
    }
  ]
};
```

#### 🚨 防范字段重复错误
**绝对禁止在同一对象中重复定义字段**
```typescript
// ❌ 绝对禁止重复字段
const knowledgeArchaeology = {
  evolution: `{EVOLUTION_CONTENT_1}`,
  timeline: [...],
  evolution: `{EVOLUTION_CONTENT_2}` // ❌ 重复的evolution字段
};

// ✅ 正确的单一字段定义
const knowledgeArchaeology = {
  evolution: `{EVOLUTION_CONTENT}`,
  timeline: [...]
};
```

#### 🚨 强制接口检查清单
生成骨架时必须执行：
- [ ] ✅ 已查看对应的TypeScript接口定义
- [ ] ✅ 所有字段名严格匹配接口定义
- [ ] ✅ 所有数据类型严格匹配接口定义
- [ ] ✅ 没有多余的未定义字段
- [ ] ✅ 没有缺失的必需字段
- [ ] ✅ 没有重复的字段定义
- [ ] ✅ 绝对没有使用模板字符串语法

## 🎨 样式与实现要求 - 基于useSyncExternalStore标准

### 统一Header设计
所有Tab都必须使用以下Header结构：
```tsx
{/* 简洁Header - 注意垂直居中 */}
<div className="flex items-center gap-3 mb-6">
  <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
    <span className="text-white text-xs">{ICON}</span>
  </div>
  <Title level={4} className="!mb-0 text-slate-900 font-medium">{TAB_TITLE}</Title>
  <span className="text-slate-500 text-sm">{TAB_DESCRIPTION}</span>
</div>
```

### 统一Tab导航设计
```tsx
{/* Tab导航 - 与其他Tab保持一致 */}
<div className="flex gap-2 p-1.5 bg-slate-100/80 backdrop-blur-sm rounded-xl border border-slate-200/50 mb-6">
  {subTabs.map(tab => (
    <button
      key={tab.key}
      onClick={() => setSubTab(tab.key)}
      className={`relative flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 group ${
        subTab === tab.key
          ? 'bg-white shadow-lg shadow-slate-200/50 text-gray-900 border border-slate-200/50'
          : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
      }`}
    >
      <span className="text-sm font-medium">{tab.label}</span>
      {subTab === tab.key && (
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-slate-600 to-gray-700 rounded-full"></div>
      )}
    </button>
  ))}
</div>
```

### 专业博客风格内容
```tsx
{/* 内容区域 - 专业博客风格 */}
<div className="prose prose-slate max-w-none">
  <h3 className="text-lg font-semibold text-slate-900 mb-4">{SECTION_TITLE}</h3>

  <div className="bg-slate-50 border-l-4 border-slate-400 p-4 mb-6">
    <p className="text-slate-800 leading-relaxed mb-0">
      {CORE_CONTENT}
    </p>
  </div>

  <div className="space-y-4">
    <div className="border-l-2 border-slate-300 pl-4">
      <h5 className="text-sm font-medium text-slate-800 mb-1">{SUB_TITLE}</h5>
      <p className="text-sm text-slate-600 leading-relaxed">
        {SUB_CONTENT}
      </p>
    </div>
  </div>
</div>
```

### 🎯 生成目标
- ✅ **结构完全正确** - TypeScript类型安全
- ✅ **占位符格式统一** - 使用`{PLACEHOLDER}`格式
- ✅ **立即可运行** - 瀑布流显示、抽屉打开、Tab切换正常
- ✅ **速度极快** - 无内容生成，仅骨架结构
- ✅ **零运行时错误** - 严格遵循接口定义
- ✅ **样式统一** - 与useSyncExternalStore保持一致的设计语言
- ✅ **专业博客风格** - 黑白灰配色，内容为王

## 📁 目录结构模板

```
src/data/react/hooks/{API_NAME}/
├── index.ts                    // 主配置文件
├── basic-info.ts              // 1-基本信息
├── business-scenarios.ts      // 2-业务场景
├── implementation.ts          // 3-原理解析
├── interview-questions.ts     // 4-面试准备
├── common-questions.ts        // 5-常见问题
├── knowledge-archaeology.ts   // 6-知识考古
├── performance-optimization.ts // 7-性能优化
├── debugging-tips.ts          // 8-调试技巧
└── essence-insights.ts        // 9-本质洞察
```

## 🔧 骨架代码模板

### 1. index.ts - 主配置文件
```typescript
import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const {API_NAME}Data: ApiItem = {
  id: '{API_NAME}',
  title: '{API_DISPLAY_NAME}',
  description: '{API_BRIEF_DESCRIPTION}',
  category: '{API_CATEGORY}',
  difficulty: 'easy',
  syntax: '{API_SYNTAX}',
  example: `{API_EXAMPLE}`,
  notes: '{API_NOTES}',
  version: '{API_VERSION}',
  tags: ['{API_TAG_1}', '{API_TAG_2}', '{API_TAG_3}'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default {API_NAME}Data;
```

### 2. basic-info.ts - 基本信息骨架（包含完成状态标识）
```typescript
import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
  completionStatus: '当前tab内容未完成',
  
  definition: "{API_NAME}是{FRAMEWORK}中用于{CORE_PURPOSE}的{API_TYPE}",

  introduction: `{API_NAME}是{FRAMEWORK} {VERSION}引入的{API_TYPE}，主要用于{PRIMARY_USE_CASE}、{SECONDARY_USE_CASE}和{TERTIARY_USE_CASE}。它采用{DESIGN_PATTERN}的设计模式，提供了{KEY_BENEFIT}。`,

  syntax: `{FULL_SYNTAX}`,

  // 🆕 快速示例 - 完整且简单的基础用法
  quickExample: `function {API_NAME}Example() {
  // {API调用说明 - 中文注释}
  const [变量名] = {API_NAME}({参数说明});

  return (
    <div>
      {/* {使用场景说明 - 中文注释} */}
      <{组件名}
        {属性名}={变量名}
        {其他属性}="{值}"
      >
        {内容说明}
      </{组件名}>
    </div>
  );
}`,

  // 🆕 业务场景图表 - 展示使用场景和相关API
  scenarioDiagram: [
    {
      title: '{DIAGRAM_1_TITLE}',
      description: '{DIAGRAM_1_DESCRIPTION}',
      diagram: `graph TD
        A[{DIAGRAM_1_NODE_A}] --> B[{DIAGRAM_1_NODE_B}]
        // ...`
    },
    {
      title: '{DIAGRAM_2_TITLE}',
      description: '{DIAGRAM_2_DESCRIPTION}',
      diagram: `graph TD
        X[{DIAGRAM_2_NODE_X}] --> Y[{DIAGRAM_2_NODE_Y}]
        // ...`
    }
  ],
  
  parameters: [
    {
      name: "{PARAM_NAME}",
      type: "{PARAM_TYPE}",
      required: true,
      description: "{PARAM_DESCRIPTION}",
      example: "{PARAM_EXAMPLE}"
    }
  ],
  
  returnValue: {
    type: "{RETURN_TYPE}",
    description: "{RETURN_DESCRIPTION}",
    example: "{RETURN_EXAMPLE}"
  },
  
  keyFeatures: [
    {
      title: "{FEATURE_1_TITLE}",
      description: "{FEATURE_1_DESCRIPTION}",
      benefit: "{FEATURE_1_BENEFIT}"
    }
  ],
  
  limitations: [
    "{LIMITATION_1}",
    "{LIMITATION_2}",
    "{LIMITATION_3}"
  ],
  
  bestPractices: [
    "{BEST_PRACTICE_1}",
    "{BEST_PRACTICE_2}",
    "{BEST_PRACTICE_3}",
    "{BEST_PRACTICE_4}",
    "{BEST_PRACTICE_5}"
  ],
  
  warnings: [
    "{WARNING_1}",
    "{WARNING_2}",
    "{WARNING_3}"
  ]
};

// 占位内容，具体内容请参考 @1-基本信息.mdc
export default basicInfo;
```

### 3. business-scenarios.ts - 业务场景骨架
```typescript
import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '当前tab内容未完成',
    
    id: 'scenario-1',
    title: '{SCENARIO_1_TITLE}',
    description: '{SCENARIO_1_DESCRIPTION}',
    businessValue: '{SCENARIO_1_BUSINESS_VALUE}',
    scenario: '{SCENARIO_1_SCENARIO}',
    code: `{SCENARIO_1_CODE}`,
    explanation: '{SCENARIO_1_EXPLANATION}',
    benefits: [
      '{SCENARIO_1_BENEFIT_1}',
      '{SCENARIO_1_BENEFIT_2}',
      '{SCENARIO_1_BENEFIT_3}'
    ],
    metrics: {
      performance: '{SCENARIO_1_PERFORMANCE_METRIC}',
      userExperience: '{SCENARIO_1_UX_METRIC}',
      technicalMetrics: '{SCENARIO_1_TECHNICAL_METRIC}'
    },
    difficulty: 'easy',
    tags: ['{SCENARIO_1_TAG_1}', '{SCENARIO_1_TAG_2}', '{SCENARIO_1_TAG_3}']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '当前tab内容未完成',
    
    id: 'scenario-2',
    title: '{SCENARIO_2_TITLE}',
    description: '{SCENARIO_2_DESCRIPTION}',
    businessValue: '{SCENARIO_2_BUSINESS_VALUE}',
    scenario: '{SCENARIO_2_SCENARIO}',
    code: `{SCENARIO_2_CODE}`,
    explanation: '{SCENARIO_2_EXPLANATION}',
    benefits: [
      '{SCENARIO_2_BENEFIT_1}',
      '{SCENARIO_2_BENEFIT_2}',
      '{SCENARIO_2_BENEFIT_3}',
      '{SCENARIO_2_BENEFIT_4}'
    ],
    metrics: {
      performance: '{SCENARIO_2_PERFORMANCE_METRIC}',
      userExperience: '{SCENARIO_2_UX_METRIC}',
      technicalMetrics: '{SCENARIO_2_TECHNICAL_METRIC}'
    },
    difficulty: 'medium',
    tags: ['{SCENARIO_2_TAG_1}', '{SCENARIO_2_TAG_2}', '{SCENARIO_2_TAG_3}']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '当前tab内容未完成',
    
    id: 'scenario-3',
    title: '{SCENARIO_3_TITLE}',
    description: '{SCENARIO_3_DESCRIPTION}',
    businessValue: '{SCENARIO_3_BUSINESS_VALUE}',
    scenario: '{SCENARIO_3_SCENARIO}',
    code: `{SCENARIO_3_CODE}`,
    explanation: '{SCENARIO_3_EXPLANATION}',
    benefits: [
      '{SCENARIO_3_BENEFIT_1}',
      '{SCENARIO_3_BENEFIT_2}',
      '{SCENARIO_3_BENEFIT_3}',
      '{SCENARIO_3_BENEFIT_4}',
      '{SCENARIO_3_BENEFIT_5}'
    ],
    metrics: {
      performance: '{SCENARIO_3_PERFORMANCE_METRIC}',
      userExperience: '{SCENARIO_3_UX_METRIC}',
      technicalMetrics: '{SCENARIO_3_TECHNICAL_METRIC}'
    },
    difficulty: 'hard',
    tags: ['{SCENARIO_3_TAG_1}', '{SCENARIO_3_TAG_2}', '{SCENARIO_3_TAG_3}', '{SCENARIO_3_TAG_4}']
  }
];

// 占位内容，具体内容请参考 @2-业务场景.mdc
export default businessScenarios;
```

### 4. implementation.ts - 原理解析骨架
```typescript
import { Implementation } from '@/types/api';

const implementation: Implementation = {
  // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
  completionStatus: '当前tab内容未完成',
  
  mechanism: `{IMPLEMENTATION_MECHANISM}`,
  
  visualization: `graph TD
    A["{NODE_A}"] --> B["{NODE_B}"]
    B --> C["{NODE_C}"]
    C --> D["{NODE_D}"]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0`,
    
  plainExplanation: `{IMPLEMENTATION_PLAIN_EXPLANATION}`,
  
  designConsiderations: [
    '{DESIGN_CONSIDERATION_1}',
    '{DESIGN_CONSIDERATION_2}',
    '{DESIGN_CONSIDERATION_3}',
    '{DESIGN_CONSIDERATION_4}',
    '{DESIGN_CONSIDERATION_5}'
  ],
  
  relatedConcepts: [
    '{RELATED_CONCEPT_1}',
    '{RELATED_CONCEPT_2}',
    '{RELATED_CONCEPT_3}',
    '{RELATED_CONCEPT_4}'
  ]
};

// 占位内容，具体内容请参考 @3-原理解析.mdc
export default implementation;
```

### 5. interview-questions.ts - 面试准备骨架
```typescript
import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '当前tab内容未完成',
    
    id: 1,
    question: '{BASIC_QUESTION}',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: '{BASIC_BRIEF_ANSWER}',
      detailed: `{BASIC_DETAILED_ANSWER}`,
      code: `{BASIC_CODE_EXAMPLE}`
    },
    tags: ['{BASIC_TAG_1}', '{BASIC_TAG_2}', '{BASIC_TAG_3}']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '当前tab内容未完成',
    
    id: 2,
    question: '{IMPLEMENTATION_QUESTION}',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: '{IMPLEMENTATION_BRIEF_ANSWER}',
      detailed: `{IMPLEMENTATION_DETAILED_ANSWER}`,
      code: `{IMPLEMENTATION_CODE_EXAMPLE}`
    },
    tags: ['{IMPLEMENTATION_TAG_1}', '{IMPLEMENTATION_TAG_2}']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '当前tab内容未完成',
    
    id: 3,
    question: '{PRACTICAL_QUESTION}',
    difficulty: 'medium',
    frequency: 'medium',
    category: '实战应用',
    answer: {
      brief: '{PRACTICAL_BRIEF_ANSWER}',
      detailed: `{PRACTICAL_DETAILED_ANSWER}`,
      code: `{PRACTICAL_CODE_EXAMPLE}`
    },
    tags: ['{PRACTICAL_TAG_1}', '{PRACTICAL_TAG_2}', '{PRACTICAL_TAG_3}']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '当前tab内容未完成',
    
    id: 4,
    question: '{ADVANCED_QUESTION}',
    difficulty: 'hard',
    frequency: 'medium',
    category: '高级应用',
    answer: {
      brief: '{ADVANCED_BRIEF_ANSWER}',
      detailed: `{ADVANCED_DETAILED_ANSWER}`,
      code: `{ADVANCED_CODE_EXAMPLE}`
    },
    tags: ['{ADVANCED_TAG_1}', '{ADVANCED_TAG_2}']
  }
];

// 占位内容，具体内容请参考 @4-面试准备.mdc
export default interviewQuestions;
```

### 6. common-questions.ts - 常见问题骨架
```typescript
import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '当前tab内容未完成',
    
    id: 'usage-error',
    question: '{USAGE_ERROR_QUESTION}',
    answer: '{USAGE_ERROR_ANSWER}',
    code: `{USAGE_ERROR_CODE}`,
    tags: ['{USAGE_ERROR_TAG_1}', '{USAGE_ERROR_TAG_2}'],
    relatedQuestions: ['{USAGE_ERROR_RELATED_1}', '{USAGE_ERROR_RELATED_2}']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '当前tab内容未完成',
    
    id: 'performance-issue',
    question: '{PERFORMANCE_ISSUE_QUESTION}',
    answer: '{PERFORMANCE_ISSUE_ANSWER}',
    code: `{PERFORMANCE_ISSUE_CODE}`,
    tags: ['{PERFORMANCE_ISSUE_TAG_1}', '{PERFORMANCE_ISSUE_TAG_2}'],
    relatedQuestions: ['{PERFORMANCE_ISSUE_RELATED_1}', '{PERFORMANCE_ISSUE_RELATED_2}']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '当前tab内容未完成',
    
    id: 'compatibility-issue',
    question: '{COMPATIBILITY_ISSUE_QUESTION}',
    answer: '{COMPATIBILITY_ISSUE_ANSWER}',
    code: `{COMPATIBILITY_ISSUE_CODE}`,
    tags: ['{COMPATIBILITY_ISSUE_TAG_1}', '{COMPATIBILITY_ISSUE_TAG_2}'],
    relatedQuestions: ['{COMPATIBILITY_ISSUE_RELATED_1}', '{COMPATIBILITY_ISSUE_RELATED_2}']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '当前tab内容未完成',
    
    id: 'debugging-difficulty',
    question: '{DEBUGGING_DIFFICULTY_QUESTION}',
    answer: '{DEBUGGING_DIFFICULTY_ANSWER}',
    code: `{DEBUGGING_DIFFICULTY_CODE}`,
    tags: ['{DEBUGGING_DIFFICULTY_TAG_1}', '{DEBUGGING_DIFFICULTY_TAG_2}'],
    relatedQuestions: ['{DEBUGGING_DIFFICULTY_RELATED_1}', '{DEBUGGING_DIFFICULTY_RELATED_2}']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '当前tab内容未完成',
    
    id: 'best-practice',
    question: '{BEST_PRACTICE_QUESTION}',
    answer: '{BEST_PRACTICE_ANSWER}',
    code: `{BEST_PRACTICE_CODE}`,
    tags: ['{BEST_PRACTICE_TAG_1}', '{BEST_PRACTICE_TAG_2}'],
    relatedQuestions: ['{BEST_PRACTICE_RELATED_1}', '{BEST_PRACTICE_RELATED_2}']
  }
];

// 占位内容，具体内容请参考 @5-常见问题.mdc
export default commonQuestions;
```

### 7. knowledge-archaeology.ts - 知识考古骨架
```typescript
import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
  completionStatus: '当前tab内容未完成',
  
  introduction: `{KNOWLEDGE_INTRODUCTION}`,
  
  background: `{KNOWLEDGE_BACKGROUND}`,
  
  evolution: `{KNOWLEDGE_EVOLUTION}`,
  
  timeline: [
    {
      year: '{TIMELINE_YEAR_1}',
      event: '{TIMELINE_EVENT_1}',
      description: '{TIMELINE_DESCRIPTION_1}',
      significance: '{TIMELINE_SIGNIFICANCE_1}'
    },
    {
      year: '{TIMELINE_YEAR_2}',
      event: '{TIMELINE_EVENT_2}',
      description: '{TIMELINE_DESCRIPTION_2}',
      significance: '{TIMELINE_SIGNIFICANCE_2}'
    },
    {
      year: '{TIMELINE_YEAR_3}',
      event: '{TIMELINE_EVENT_3}',
      description: '{TIMELINE_DESCRIPTION_3}',
      significance: '{TIMELINE_SIGNIFICANCE_3}'
    }
  ],
  
  keyFigures: [
    {
      name: '{KEY_FIGURE_1_NAME}',
      role: '{KEY_FIGURE_1_ROLE}',
      contribution: '{KEY_FIGURE_1_CONTRIBUTION}',
      significance: '{KEY_FIGURE_1_SIGNIFICANCE}'
    },
    {
      name: '{KEY_FIGURE_2_NAME}',
      role: '{KEY_FIGURE_2_ROLE}',
      contribution: '{KEY_FIGURE_2_CONTRIBUTION}',
      significance: '{KEY_FIGURE_2_SIGNIFICANCE}'
    }
  ],
  
  concepts: [
    {
      term: '{CONCEPT_1_TERM}',
      definition: '{CONCEPT_1_DEFINITION}',
      evolution: '{CONCEPT_1_EVOLUTION}',
      modernRelevance: '{CONCEPT_1_MODERN_RELEVANCE}'
    },
    {
      term: '{CONCEPT_2_TERM}',
      definition: '{CONCEPT_2_DEFINITION}',
      evolution: '{CONCEPT_2_EVOLUTION}',
      modernRelevance: '{CONCEPT_2_MODERN_RELEVANCE}'
    }
  ],
  
  designPhilosophy: `{DESIGN_PHILOSOPHY}`,
  
  impact: `{KNOWLEDGE_IMPACT}`,
  
  modernRelevance: `{MODERN_RELEVANCE}`
};

// 占位内容，具体内容请参考 @6-知识考古.mdc
export default knowledgeArchaeology;
```

### 8. performance-optimization.ts - 性能优化骨架
```typescript
import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
  completionStatus: '当前tab内容未完成',
  
  optimizationStrategies: [
    {
      title: '{OPTIMIZATION_STRATEGY_1_TITLE}',
      description: '{OPTIMIZATION_STRATEGY_1_DESCRIPTION}',
      techniques: [
        {
          name: '{OPTIMIZATION_TECHNIQUE_1_NAME}',
          description: '{OPTIMIZATION_TECHNIQUE_1_DESCRIPTION}',
          code: `{OPTIMIZATION_TECHNIQUE_1_CODE}`,
          impact: 'high',
          difficulty: 'medium'
        },
        {
          name: '{OPTIMIZATION_TECHNIQUE_2_NAME}',
          description: '{OPTIMIZATION_TECHNIQUE_2_DESCRIPTION}',
          code: `{OPTIMIZATION_TECHNIQUE_2_CODE}`,
          impact: 'medium',
          difficulty: 'easy'
        }
      ]
    },
    {
      title: '{OPTIMIZATION_STRATEGY_2_TITLE}',
      description: '{OPTIMIZATION_STRATEGY_2_DESCRIPTION}',
      techniques: [
        {
          name: '{OPTIMIZATION_TECHNIQUE_3_NAME}',
          description: '{OPTIMIZATION_TECHNIQUE_3_DESCRIPTION}',
          code: `{OPTIMIZATION_TECHNIQUE_3_CODE}`,
          impact: 'high',
          difficulty: 'hard'
        }
      ]
    }
  ],

  performanceMetrics: {
    '{METRIC_1_NAME}': {
      description: '{METRIC_1_DESCRIPTION}',
      tool: '{METRIC_1_TOOL}',
      example: '{METRIC_1_EXAMPLE}'
    },
    '{METRIC_2_NAME}': {
      description: '{METRIC_2_DESCRIPTION}',
      tool: '{METRIC_2_TOOL}',
      example: '{METRIC_2_EXAMPLE}'
    }
  },

  bestPractices: [
    '{BEST_PRACTICE_1}',
    '{BEST_PRACTICE_2}',
    '{BEST_PRACTICE_3}',
    '{BEST_PRACTICE_4}',
    '{BEST_PRACTICE_5}'
  ],

  commonPitfalls: [
    {
      issue: '{PITFALL_1_ISSUE}',
      cause: '{PITFALL_1_CAUSE}',
      solution: '{PITFALL_1_SOLUTION}'
    },
    {
      issue: '{PITFALL_2_ISSUE}',
      cause: '{PITFALL_2_CAUSE}',
      solution: '{PITFALL_2_SOLUTION}'
    },
    {
      issue: '{PITFALL_3_ISSUE}',
      cause: '{PITFALL_3_CAUSE}',
      solution: '{PITFALL_3_SOLUTION}'
    }
  ]
};

// 占位内容，具体内容请参考 @7-性能优化.mdc
export default performanceOptimization;
```

### 9. debugging-tips.ts - 调试技巧骨架
```typescript
import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
  completionStatus: '当前tab内容未完成',
  
  troubleshooting: [
    {
      symptom: '{TROUBLESHOOTING_1_TITLE}',
      description: '{TROUBLESHOOTING_1_DESCRIPTION}',
      steps: ['{TROUBLESHOOTING_1_STEP_1}', '{TROUBLESHOOTING_1_STEP_2}', '{TROUBLESHOOTING_1_STEP_3}'],
      code: `{TROUBLESHOOTING_1_CODE}`
    },
    {
      symptom: '{TROUBLESHOOTING_2_TITLE}',
      description: '{TROUBLESHOOTING_2_DESCRIPTION}',
      steps: ['{TROUBLESHOOTING_2_STEP_1}', '{TROUBLESHOOTING_2_STEP_2}'],
      code: `{TROUBLESHOOTING_2_CODE}`
    }
  ],
  
  commonErrors: [
    {
      error: '{COMMON_ERROR_1}',
      cause: '{COMMON_ERROR_1_CAUSE}',
      solution: '{COMMON_ERROR_1_SOLUTION}',
      prevention: '{COMMON_ERROR_1_PREVENTION}',
      code: `{COMMON_ERROR_1_CODE}`
    },
    {
      error: '{COMMON_ERROR_2}',
      cause: '{COMMON_ERROR_2_CAUSE}',
      solution: '{COMMON_ERROR_2_SOLUTION}',
      prevention: '{COMMON_ERROR_2_PREVENTION}',
      code: `{COMMON_ERROR_2_CODE}`
    }
  ],
  
  devToolsTips: [
    {
      tool: '{DEV_TOOLS_TOOL_1}',
      technique: '{DEV_TOOLS_TECHNIQUE_1}',
      example: '{DEV_TOOLS_EXAMPLE_1}'
    },
    {
      tool: '{DEV_TOOLS_TOOL_2}',
      technique: '{DEV_TOOLS_TECHNIQUE_2}',
      example: '{DEV_TOOLS_EXAMPLE_2}'
    }
  ]
};

// 占位内容，具体内容请参考 @8-调试技巧.mdc
export default debuggingTips;
```

### 10. essence-insights.ts - 本质洞察骨架
```typescript
import { EssenceInsights } from '@/types/api';

const essenceInsights: EssenceInsights = {
  // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
  completionStatus: '当前tab内容未完成',
  
  coreQuestion: `{CORE_QUESTION}`,
  
  designPhilosophy: {
    worldview: `{DESIGN_WORLDVIEW}`,
    methodology: `{DESIGN_METHODOLOGY}`,
    tradeoffs: `{DESIGN_TRADEOFFS}`,
    evolution: `{DESIGN_EVOLUTION}`
  },
  
  hiddenTruth: {
    surfaceProblem: `{SURFACE_PROBLEM}`,
    realProblem: `{REAL_PROBLEM}`,
    hiddenCost: `{HIDDEN_COST}`,
    deeperValue: `{DEEPER_VALUE}`
  },
  
  deeperQuestions: [
    {
      layer: 1,
      question: '{LAYER_1_QUESTION}',
      why: '{LAYER_1_WHY}',
      implications: [
        '{LAYER_1_IMPLICATION_1}',
        '{LAYER_1_IMPLICATION_2}'
      ]
    },
    {
      layer: 2,
      question: '{LAYER_2_QUESTION}',
      why: '{LAYER_2_WHY}',
      implications: [
        '{LAYER_2_IMPLICATION_1}',
        '{LAYER_2_IMPLICATION_2}',
        '{LAYER_2_IMPLICATION_3}'
      ]
    },
    {
      layer: 3,
      question: '{LAYER_3_QUESTION}',
      why: '{LAYER_3_WHY}',
      implications: [
        '{LAYER_3_IMPLICATION_1}',
        '{LAYER_3_IMPLICATION_2}'
      ]
    },
    {
      layer: 4,
      question: '{LAYER_4_QUESTION}',
      why: '{LAYER_4_WHY}',
      implications: [
        '{LAYER_4_IMPLICATION_1}',
        '{LAYER_4_IMPLICATION_2}',
        '{LAYER_4_IMPLICATION_3}'
      ]
    },
    {
      layer: 5,
      question: '{LAYER_5_QUESTION}',
      why: '{LAYER_5_WHY}',
      implications: [
        '{LAYER_5_IMPLICATION_1}',
        '{LAYER_5_IMPLICATION_2}'
      ]
    }
  ],
  
  paradigmShift: {
    oldParadigm: {
      assumption: `{OLD_PARADIGM_ASSUMPTION}`,
      limitation: `{OLD_PARADIGM_LIMITATION}`,
      worldview: `{OLD_PARADIGM_WORLDVIEW}`
    },
    newParadigm: {
      breakthrough: `{NEW_PARADIGM_BREAKTHROUGH}`,
      possibility: `{NEW_PARADIGM_POSSIBILITY}`,
      cost: `{NEW_PARADIGM_COST}`
    },
    transition: {
      resistance: `{TRANSITION_RESISTANCE}`,
      catalyst: `{TRANSITION_CATALYST}`,
      tippingPoint: `{TRANSITION_TIPPING_POINT}`
    }
  },
  
  universalPrinciples: [
    "{UNIVERSAL_PRINCIPLE_1_NAME}：{UNIVERSAL_PRINCIPLE_1_DESCRIPTION} {UNIVERSAL_PRINCIPLE_1_INSIGHT} 这种原理广泛应用于{UNIVERSAL_PRINCIPLE_1_APPLICATION_1}、{UNIVERSAL_PRINCIPLE_1_APPLICATION_2}、{UNIVERSAL_PRINCIPLE_1_APPLICATION_3}等领域。",
    
    "{UNIVERSAL_PRINCIPLE_2_NAME}：{UNIVERSAL_PRINCIPLE_2_DESCRIPTION} {UNIVERSAL_PRINCIPLE_2_INSIGHT} 这种原理是{UNIVERSAL_PRINCIPLE_2_APPLICATION_1}、{UNIVERSAL_PRINCIPLE_2_APPLICATION_2}等系统的基础。",
    
    "{UNIVERSAL_PRINCIPLE_3_NAME}：{UNIVERSAL_PRINCIPLE_3_DESCRIPTION} {UNIVERSAL_PRINCIPLE_3_INSIGHT} 这种原理被广泛应用于{UNIVERSAL_PRINCIPLE_3_APPLICATION_1}、{UNIVERSAL_PRINCIPLE_3_APPLICATION_2}等领域。"
  ]
};

// 占位内容，具体内容请参考 @9-本质洞察.mdc
export default essenceInsights;
```

## 📊 阅读体验优化规范（2024年新增）

### 🎯 分层Tab系统设计

**基于顶级技术博客最佳实践，长篇幅内容必须采用分层Tab架构**

#### **📋 原理解析Tab分层结构**
```typescript
// ✅ 强制要求的5层分层结构
const implementationSubTabs = [
  { key: 'overview', label: '📋 原理概览' },     // 1分钟快速了解
  { key: 'mechanism', label: '⚙️ 核心机制' },   // 深度技术机制  
  { key: 'visualization', label: '📊 可视化图解' }, // Mermaid图表
  { key: 'explanation', label: '💡 通俗解释' },  // 类比和简化
  { key: 'design', label: '🎯 设计考量' }        // 架构权衡
];
```

#### **🏛️ 知识考古Tab分层结构**
```typescript
const archaeologySubTabs = [
  { key: 'timeline', label: '发展时间线' },    // 时间轴展示
  { key: 'background', label: '🌟 历史背景' },   // 技术演进背景
  { key: 'figures', label: '👥 关键人物' },      // 重要贡献者
  { key: 'concepts', label: '💡 核心概念' }      // 概念演进史
];
```

#### **💎 本质洞察Tab分层结构（四重境界版）**
```typescript
const essenceSubTabs = [
  { key: 'problem', label: '🎯 核心问题' },      // 第一重：技术灵魂考古
  { key: 'design', label: '🧠 设计智慧' },       // 第二重：设计哲学解构
  { key: 'insight', label: '💡 应用洞察' },      // 第三重：真相与幻象识别
  { key: 'architecture', label: '🏗️ 架构思考' } // 第四重：普世智慧提炼
];
```

### 🎨 视觉设计规范

#### **颜色系统标准化**
```css
/* 主Tab颜色 */
📋 原理解析: bg-gradient-to-r from-blue-50 to-indigo-50 + border-l-4 border-blue-500
🏛️ 知识考古: bg-gradient-to-r from-amber-50 to-orange-50 + border-l-4 border-amber-500  
💎 本质洞察: bg-gradient-to-r from-purple-50 to-pink-50 + border-l-4 border-purple-500

/* 子Tab内容区颜色 */
📋 概览: Alert info类型 + bg-blue-50
⚙️ 机制: bg-gray-50 (专业简洁)
📊 图表: bg-blue-50 (突出可视化)
💡 解释: bg-amber-50 + border-l-4 border-amber-400
🎯 设计: bg-green-50 (成熟稳重)
```

#### **布局规范**
```css
/* 强制布局参数 */
.tab-content-area {
  min-height: 400px;        /* 避免内容跳跃 */
  padding: 1.5rem;          /* 内容呼吸感 */
  margin-bottom: 1.5rem;    /* 视觉舒适间距 */
}

/* 卡片间距 */
.space-y-6 > * + * {
  margin-top: 1.5rem;
}
```

### 📝 内容组织最佳实践

#### **mechanism字段标准模板**
```typescript
// ✅ 支持分层显示的mechanism结构
mechanism: `
📍 **战略定位**：{占位符_API在生态中的位置}

🏗️ **深度源码分析**：
核心实现位于 {占位符_文件路径}

**🧠 认知跃迁三层次**：
- **使用者层次**：{占位符_基础使用说明}
- **理解者层次**：{占位符_机制理解}
- **洞察者层次**：{占位符_架构洞察}

**核心数据结构**：
- {占位符_结构1}：{占位符_作用说明}
- {占位符_结构2}：{占位符_作用说明}

**🔬 关键算法实现**：
{占位符_算法步骤说明}
`
```

#### **plainExplanation字段标准模板**
```typescript
plainExplanation: `
### 💡 日常生活类比
{占位符_生活化比喻}

### 🔧 技术类比
{占位符_与其他技术对比}

### 🎯 概念本质
{占位符_抽象概念具体化}

### 📊 可视化帮助
{占位符_配合图表说明}
`
```

#### **强制可视化要求**
```typescript
// ✅ 每个implementation必须包含visualization
visualization: `
graph TD
  A["{占位符_起始节点}"] --> B["{占位符_处理节点}"]
  B --> C["{占位符_结果节点}"]
  
  style A fill:#e1f5fe
  style B fill:#f3e5f5  
  style C fill:#e8f5e8
`
```

### 🔍 阅读体验质量检查

#### **📋 概览Tab质量标准**
- [ ] 能否在1分钟内理解核心概念
- [ ] 是否提取了mechanism的前10行关键信息
- [ ] 是否使用了Alert提示组件提供指引

#### **⚙️ 机制Tab质量标准**  
- [ ] 技术细节是否准确完整
- [ ] 是否避免了过长的单段文字
- [ ] 是否使用了视觉断点分层

#### **📊 图表Tab质量标准**
- [ ] 是否包含至少1个Mermaid图表
- [ ] 图表节点命名是否使用占位符格式
- [ ] 是否有图表说明文字

#### **💡 解释Tab质量标准**
- [ ] 是否使用了生活化类比
- [ ] 是否避免了专业术语堆砌
- [ ] 语言是否适合初学者理解

#### **🎯 设计Tab质量标准**
- [ ] 是否说明了架构选择原因
- [ ] designConsiderations是否为数组格式
- [ ] relatedConcepts是否提供了相关概念标签

### 🚨 必须避免的阅读体验问题

#### **❌ 内容密度过高**
```typescript
// 错误：大段文字无断点
mechanism: "{占位符_一大段技术说明没有任何视觉分层...}"

// ✅ 正确：有层次的结构
mechanism: `
📍 **战略定位**：{占位符_简短定位}

🏗️ **核心实现**：
{占位符_实现要点}

**关键特性**：
- {占位符_特性1}
- {占位符_特性2}
`
```

#### **❌ 缺乏视觉层次**
```typescript
// 错误：所有内容看起来重要性相同
designConsiderations: [
  "{占位符_考虑点1}",
  "{占位符_考虑点2}", 
  "{占位符_考虑点3}"
]

// ✅ 正确：有视觉优先级的内容
designConsiderations: [
  "🎯 **核心权衡**：{占位符_最重要的考虑}",
  "⚡ **性能影响**：{占位符_性能相关}", 
  "🔒 **安全考虑**：{占位符_安全相关}"
]
```

#### **❌ 扫描困难**
```typescript
// 错误：缺乏扫描标识
plainExplanation: "{占位符_长段解释没有小标题...}"

// ✅ 正确：易于扫描的结构  
plainExplanation: `
### 💡 {占位符_类比标题}
{占位符_类比内容}

### 🔧 {占位符_技术对比标题}
{占位符_对比内容}
`
```

### 🎯 阅读体验升级目标

通过这些规范，确保每个Tab都能：

**⚡ 快速扫描** - 1分钟掌握要点
- 概览Tab：核心概念速览
- 其他Tab：小标题和视觉标识

**🔍 深度学习** - 渐进式内容深度
- 从概览到细节的合理过渡
- 技术深度的层次化展现

**🎨 视觉友好** - 减少阅读疲劳
- 颜色系统化和一致性
- 充足的视觉断点和呼吸感

**🤝 门槛友好** - 照顾不同背景用户
- 类比和解释降低技术门槛
- 分层满足不同深度需求

**🔄 导航便利** - 支持非线性阅读
- Tab切换流畅直观
- 每个Tab独立完整

## 🔄 使用流程

### 1. 替换占位符步骤
1. 将`{API_NAME}`替换为具体API名称（如：useState）
2. 将`{API_DISPLAY_NAME}`替换为显示名称（如：useState Hook）
3. 其他所有`{PLACEHOLDER}`保持原样，等待内容填充

### 2. 验证完整性
- [ ] 所有TypeScript类型正确
- [ ] 所有import路径正确
- [ ] 所有export语句正确
- [ ] 所有Tab配置正确
- [ ] 可以正常运行和显示

### 3. 质量控制
- [ ] 没有任何具体内容
- [ ] 所有占位符格式统一
- [ ] 结构完全匹配类型定义
- [ ] 注释指向对应.mdc文件

## 🎯 成功标准

骨架生成成功的标志：
1. ✅ **立即可运行** - 无TypeScript错误
2. ✅ **UI正常显示** - 瀑布流、抽屉、Tab切换正常
3. ✅ **结构正确** - 所有字段类型匹配
4. ✅ **占位符完整** - 没有遗漏的占位符
5. ✅ **生成极快** - 秒级完成骨架生成

## 🚨 特别注意

### Mermaid图表安全语法
在 `implementation.ts` 的 `visualization` 字段中，使用安全的Mermaid语法：
```typescript
visualization: `graph TD
  A["{NODE_A}"] --> B["{NODE_B}"]
  B --> C["{NODE_C}"]
  C --> D["{NODE_D}"]
  
  style A fill:#e1f5fe
  style B fill:#f3e5f5
  style C fill:#e8f5e8
  style D fill:#fff3e0`
```

**避免的语法**：
- 复杂的子图
- 特殊字符和长文本
- 过多的样式定义
- 不标准的节点命名

### ⚠️ 关键代码块安全约束

#### 🚨 禁止JavaScript代码块语法
**在TypeScript文件的字符串中绝对禁止使用```javascript代码块**
```typescript
// ❌ 绝对禁止 - 会导致TypeScript编译错误
const mechanism = `
**关键算法实现**：
```javascript
function mountHook() {
  // 代码内容
}
`;

// ✅ 正确 - 使用普通代码注释或占位符
const mechanism = `
**关键算法实现**：
// 挂载阶段核心逻辑（简化版源码）
function mountHook() {
  // {CODE_IMPLEMENTATION}
}
`;
```

#### 🚨 安全的代码示例格式
**所有代码示例必须使用安全格式**
```typescript
// ✅ 正确的代码示例格式
const businessScenarios = [{
  code: `{SCENARIO_CODE_EXAMPLE}`,  // 直接字符串，无代码块标记
  explanation: '{SCENARIO_EXPLANATION}'
}];

const implementation = {
  mechanism: `{MECHANISM_DESCRIPTION}`,  // 避免复杂的代码块语法
  plainExplanation: `{PLAIN_EXPLANATION}`
};
```

#### 🚨 字符串内容安全检查
生成骨架时的字符串内容必须满足：
- [ ] ✅ 不包含```javascript标记
- [ ] ✅ 不包含```typescript标记  
- [ ] ✅ 不包含任何代码块标记
- [ ] ✅ 使用简单的占位符格式
- [ ] ✅ 避免复杂的嵌套引号
- [ ] ✅ 避免特殊字符和转义符

---

**🎯 强约束承诺**：此骨架生成器专注于结构正确性和生成速度，绝不填充任何具体内容，确保快速创建可运行的占位符骨架，并严格遵循所有安全约束以防范运行时错误。