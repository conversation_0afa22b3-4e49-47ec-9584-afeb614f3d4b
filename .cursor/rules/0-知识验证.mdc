---
description: 
globs: 
alwaysApply: true
---
# 🛡️ 知识验证与准确性保障

## 🚨 核心强约束（最高优先级）

### ⚠️ 知识错误严重后果
面试失败影响职业发展 | 生产环境bug造成经济损失 | 技术决策错误影响项目成败 | 学习方向偏差浪费时间 | 团队误导影响技术水平

### 🎯 零幻觉率目标
实现接近零的幻觉率，确保每个知识点都是准确可信的。

### 📋 强制验证步骤（每个知识点必须执行）

#### 1. 🔍 源头验证 (Critical)
- [x] **官方文档确认**：所有技术细节必须有官方文档支撑
- [x] **版本准确性**：确认API的版本信息和兼容性  
- [x] **语法正确性**：所有代码示例必须经过实际测试
- [x] **类型定义准确**：TypeScript类型定义与官方保持一致

#### 2. 🧪 实际测试验证 (Critical)
- [x] **代码可运行**：所有代码示例必须能够实际运行
- [x] **测试环境**：在主流环境下测试兼容性
- [x] **边界条件**：测试异常情况和边界条件
- [x] **性能数据**：性能相关数据必须有实际测试支撑

#### 3. 📚 多源交叉验证 (Important)
- [x] **多个权威来源**：至少2-3个权威来源确认
- [x] **社区验证**：检查社区讨论和实际使用情况
- [x] **专家确认**：重要知识点需要领域专家确认
- [x] **时效性检查**：确认信息的时效性和最新状态

## 🔒 分级验证策略

### 🔴 关键知识点 (Critical)
**涉及**：语法定义、类型签名、核心概念、面试重点
**验证要求**：官方文档直接支撑 + 代码实际测试 + 至少3个独立来源确认

```typescript
// 验证模板
const criticalKnowledge = {
  statement: "useState的类型签名是...",
  officialSource: "https://react.dev/reference/react/useState",
  testedCode: "实际测试的代码示例",
  additionalSources: ["TypeScript React types", "React源码analysis"],
  verificationStatus: "VERIFIED",
  lastChecked: "2024-01-15"
};
```

### 🟡 重要知识点 (Important)
**涉及**：最佳实践、性能数据、设计原理
**验证要求**：可靠来源支撑 + 代码示例测试 + 至少2个来源确认

### 🟢 补充知识点 (Supplementary)  
**涉及**：历史背景、趋势分析、个人见解
**验证要求**：基本来源支撑 + 标明信息性质 + 提供信息来源

## 🚫 严格禁止内容

### ❌ 绝对禁止
编造的API方法或参数 | 虚假的性能数据 | 不存在的版本特性 | 错误的类型定义 | 未经验证的"最佳实践"

### ⚠️ 需要特别标注
实验性特性(明确标注"实验性") | 个人观点(标注"观点/建议") | 历史信息(标注时间和上下文) | 社区实践(区分官方推荐和社区习惯)

## 📝 准确性编写指南

### 官方信息优先级
```
1. 官方文档 (react.dev, vuejs.org等)
2. 官方GitHub仓库和源码
3. 官方博客和发布说明  
4. 官方团队成员的权威声明
5. 权威技术书籍
6. 知名技术专家的深度分析
7. 大型公司的技术实践分享
```

### 标准表述模板
```markdown
<!-- 事实性信息 -->
根据[官方文档](mdc:链接)，useState的返回值类型是...

<!-- 性能数据 -->  
在React 18环境下测试显示...(测试环境：Chrome 120, React 18.2.0)

<!-- 最佳实践 -->
React官方推荐...[来源]，这种做法的优势在于...

<!-- 个人观点 -->
💡 **分析观点**：基于以上事实，我们可以认为...
```

### 代码验证流程
```bash
# 1. 创建测试项目
npx create-react-app verification-test && cd verification-test

# 2. 测试代码示例（将文档中的代码示例复制到测试项目中，确保能够正常运行）

# 3. 类型检查
npm run type-check

# 4. 边界测试（测试异常情况和边界条件）
```

## 🔍 常见幻觉类型及防范

### 🚨 技术幻觉防范

#### 1. API签名幻觉
**问题**：编造不存在的参数或返回值 | **防范**：直接查看TypeScript定义文件

```typescript
// ❌ 错误示例（幻觉）
const [state, setState, resetState] = useState(0); // resetState不存在

// ✅ 正确示例（验证）  
const [state, setState] = useState(0); // 根据@types/react确认
```

#### 2. 性能数据幻觉
**问题**：编造未经测试的性能数据 | **防范**：提供实际测试环境和方法

```javascript
// ❌ 错误示例（幻觉）
"useState比useReducer快50%" // 未经测试的声明

// ✅ 正确示例（验证）
"在我们的测试环境下(Chrome 120, 1000次更新)，useState平均耗时2.3ms，useReducer平均耗时2.8ms"
```

#### 3. 版本特性幻觉
**问题**：将功能归因到错误的版本 | **防范**：查阅官方发布记录

```markdown
❌ 错误：useState是React 17引入的
✅ 正确：useState是React 16.8引入的 [来源：React 16.8发布说明]
```

## 📊 质量控制检查表

### 🔍 内容发布前检查 (MANDATORY)

#### 技术准确性
- [ ] 所有API签名已对照官方文档确认
- [ ] 所有代码示例已实际测试通过  
- [ ] 所有类型定义与@types包一致
- [ ] 版本信息准确且最新

#### 数据准确性
- [ ] 性能数据有测试环境说明
- [ ] 统计数据有明确来源
- [ ] 对比数据有测试方法说明
- [ ] 历史数据有时间和上下文

#### 表述准确性  
- [ ] 区分了事实和观点
- [ ] 重要声明有来源链接
- [ ] 避免了绝对化表述
- [ ] 标注了信息的性质和来源

#### 时效性检查
- [ ] 确认信息的最新状态
- [ ] 标注了信息的时间节点
- [ ] 检查了废弃和变更信息
- [ ] 提供了版本兼容性说明

## 🛠️ 验证工具和资源

### 📚 权威信息源

**React生态**：官方文档(https://react.dev) | TypeScript类型(@types/react) | 源码仓库(https://github.com/facebook/react) | RFC提案(https://github.com/reactjs/rfcs) | 官方博客(https://react.dev/blog)

**Vue生态**：官方文档(https://vuejs.org) | TypeScript类型(vue package自带) | 源码仓库(https://github.com/vuejs/core) | RFC提案(https://github.com/vuejs/rfcs) | 官方博客(https://blog.vuejs.org)

### 🧪 验证工具

**在线工具**：TypeScript Playground(验证类型定义) | CodeSandbox(测试代码示例) | NPM包信息(查看版本和依赖) | Can I Use(检查浏览器兼容性)

**本地工具**：
```bash
npx tsc --noEmit          # 类型检查
npm info react versions --json  # 包信息查询  
npm ls react              # 依赖分析
```

## 🎯 持续改进机制

### 📈 准确性监控
用户反馈收集(建立错误报告机制) | 专家Review(定期请专家审核重要内容) | 自动化检查(开发自动化验证脚本) | 版本跟踪(跟踪技术更新，及时修正内容)

### 🔄 错误修正流程
1. **错误识别**：发现知识错误
2. **影响评估**：评估错误的影响范围  
3. **紧急修正**：立即修正关键错误
4. **根因分析**：分析错误产生原因
5. **预防改进**：完善验证机制

---

**🎯 核心承诺**：我们承诺为每个开发者提供准确、可信、经过验证的技术知识，绝不让知识错误成为影响职业发展的导火索。准确性是我们的生命线，也是对每个信任我们的开发者的责任。
