---
description: 
globs: 
alwaysApply: false
---
# ⚡ Tab 7: 性能优化 (performance-optimization.ts) 专用提示词

## �� 重要编码约束（最高优先级）

### 🛡️ TypeScript接口一致性检查（零容忍错误）
**必须严格遵循 PerformanceOptimization 接口定义**

```typescript
// ✅ 正确的数据结构
interface PerformanceOptimization {
  optimizationStrategies: Array<{
    title: string;
    description: string;
    techniques: Array<{
      name: string;
      description: string;
      code: string;
      impact: 'high' | 'medium' | 'low';
      difficulty: 'easy' | 'medium' | 'hard';
    }>;
  }>;
  performanceMetrics?: {
    [key: string]: {              // ❌ 不是数组！
      description: string;
      tool: string;
      example: string;
    };
  };
  bestPractices: string[];
  commonPitfalls: Array<{         // ❌ 不是字符串数组！
    issue: string;
    cause: string;
    solution: string;
  }>;
}

// ❌ 常见错误 - performanceMetrics使用数组
performanceMetrics: [
  { metric: 'xxx', target: 'xxx' }  // 错误结构
];

// ✅ 正确写法 - performanceMetrics使用对象
performanceMetrics: {
  'metric-key': {
    description: 'xxx',
    tool: 'xxx', 
    example: 'xxx'
  }
};

// ❌ 常见错误 - commonPitfalls使用字符串数组
commonPitfalls: ['问题1', '问题2'];

// ✅ 正确写法 - commonPitfalls使用对象数组
commonPitfalls: [
  {
    issue: '问题描述',
    cause: '原因分析', 
    solution: '解决方案'
  }
];
```

**🚨 强制检查步骤**:
1. 编写前查看 `/src/types/api.ts` 中的 PerformanceOptimization 接口
2. 确保所有字段名称、类型、结构完全匹配
3. 特别注意 performanceMetrics 是对象不是数组
4. 特别注意 commonPitfalls 是对象数组不是字符串数组

### ❌ 严格禁止的语法
**绝对不能使用任何形式的模板字符串插值语法 `${xxx}`**

### 🛡️ 性能优化编码特殊要求
- **性能测试**: 测试代码中避免模板字符串开销
- **基准测试**: 基准测试结果输出使用高效方式
- **性能监控**: 监控代码本身要高性能
- **优化示例**: 展示的优化代码要是最佳实践

## 🎯 Tab定位与价值
性能优化Tab是**性能提升的专家指南**，专门针对性能敏感的API，提供深度的性能分析、优化策略和最佳实践，帮助开发者构建高性能应用。体现"真问题"的精神，聚焦于真正影响性能的关键问题。

## 🎨 样式与实现要求

### Header设计
```tsx
{/* 简洁Header - 注意垂直居中 */}
<div className="flex items-center gap-3 mb-6">
  <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
    <span className="text-white text-xs">⚡</span>
  </div>
  <Title level={4} className="!mb-0 text-slate-900 font-medium">性能优化</Title>
  <span className="text-slate-500 text-sm">性能分析与优化策略</span>
</div>
```

### 内容样式 - 专业博客风格
- **主色调**: 黑白灰专业配色，避免花哨色块
- **性能数据**: 使用图表和数据可视化展示性能指标
- **优化策略**: 清晰的优化步骤和代码示例
- **真问题导向**: 聚焦于真正影响性能的关键问题，体现"真问题"精神

## 📊 适用API类型
此Tab主要适用于以下类型的API：
- **状态管理API**: useState、useReducer、useContext
- **性能优化API**: useMemo、useCallback、React.memo
- **渲染控制API**: useTransition、useDeferredValue
- **数据处理API**: 大数据量处理相关的API
- **异步API**: 影响异步性能的API

## 📊 内容结构要求

### 必须包含的5个核心部分：

```typescript
export const performanceOptimization = {
  introduction: string,           // 性能重要性说明（100-150字）
  performanceAnalysis: PerformanceAnalysis, // 性能特征分析
  optimizationStrategies: OptimizationStrategy[], // 优化策略
  benchmarkTests: BenchmarkTest[], // 性能基准测试
  bestPractices: BestPractice[]   // 性能最佳实践
};

interface PerformanceAnalysis {
  timeComplexity: string;        // 时间复杂度
  spaceComplexity: string;       // 空间复杂度
  renderingImpact: string;       // 渲染性能影响
  memoryUsage: string;          // 内存使用特征
  concurrencyBehavior: string;   // 并发行为
}

interface OptimizationStrategy {
  strategy: string;             // 优化策略名称
  scenario: string;             // 适用场景
  implementation: string;       // 实现方法
  codeExample: string;          // 代码示例
  improvement: string;          // 性能提升
  tradeoffs: string;           // 权衡考虑
}
```

## ✍️ 编写指南

### 1. 📝 introduction - 性能重要性说明
**目标**: 100-150字说明为什么这个API需要特别关注性能

**要求**:
- 明确API的性能敏感性
- 说明性能问题的常见场景
- 强调优化的业务价值
- 概述优化的主要方向

**模板**:
```
"{API名称}在{应用场景}中是性能的关键因素。不当使用可能导致{性能问题}，严重影响{用户体验}。

常见的性能陷阱包括{陷阱1}、{陷阱2}和{陷阱3}。通过合理的{优化策略}，可以显著提升{性能指标}，从而改善{业务指标}。

本指南将深入分析{API名称}的性能特征，提供{优化数量}种优化策略，并通过{测试方法}验证优化效果。"
```

### 2. 📊 performanceAnalysis - 性能特征分析
**目标**: 全面分析API的性能特征和影响因素

#### timeComplexity - 时间复杂度
**要求**:
- 分析API执行的时间复杂度
- 考虑不同使用场景下的复杂度变化
- 说明影响执行时间的关键因素

**格式**:
```
"**基础场景**: O(1) - {具体说明}
**复杂场景**: O(n) - {具体说明}
**最坏情况**: O(n²) - {具体说明}

**关键影响因素**:
1. {因素1} - {影响说明}
2. {因素2} - {影响说明}
3. {因素3} - {影响说明}"
```

#### spaceComplexity - 空间复杂度
**要求**:
- 分析内存使用模式
- 考虑内存泄漏的可能性
- 说明内存优化的关键点

#### renderingImpact - 渲染性能影响
**要求**:
- 分析对组件渲染的影响
- 说明渲染次数和渲染成本
- 考虑Virtual DOM的影响

#### memoryUsage - 内存使用特征
**要求**:
- 分析内存分配模式
- 考虑垃圾回收的影响
- 识别内存泄漏风险

#### concurrencyBehavior - 并发行为
**要求**:
- 分析并发安全性
- 考虑异步操作的影响
- 说明React并发特性的影响

### 3. 🎯 optimizationStrategies - 优化策略
**目标**: 提供3-5个具体可行的优化策略

**策略选择标准**:
- 解决真实性能问题
- 适用于常见使用场景
- 有明显的性能提升
- 实现成本合理

**每个策略包含**:

#### strategy - 优化策略名称
```
"依赖数组优化"
"批量状态更新"
"组件懒加载"
"缓存机制优化"
```

#### scenario - 适用场景
**要求**:
- 具体描述适用的业务场景
- 说明性能问题的表现
- 明确优化的必要性

#### implementation - 实现方法
**要求**:
- 提供详细的实现步骤
- 解释技术原理
- 考虑实现的复杂度

#### codeExample - 代码示例
**要求**:
- 同时提供优化前后的代码对比
- 代码完整可运行
- 包含详细注释说明关键点

**代码对比格式**:
```typescript
// ❌ 性能问题 - 优化前
function SlowComponent({ items }) {
  // 每次渲染都会重新计算
  const expensiveValue = calculateExpensiveValue(items);
  
  return (
    <div>
      {items.map(item => (
        <ExpensiveChild key={item.id} value={expensiveValue} />
      ))}
    </div>
  );
}

// ✅ 性能优化 - 优化后
function FastComponent({ items }) {
  // 使用useMemo缓存计算结果
  const expensiveValue = useMemo(() => {
    return calculateExpensiveValue(items);
  }, [items]); // 只有items变化时才重新计算
  
  return (
    <div>
      {items.map(item => (
        <ExpensiveChild key={item.id} value={expensiveValue} />
      ))}
    </div>
  );
}

// 📊 性能对比
// 优化前：每次渲染 50ms
// 优化后：初次渲染 50ms，后续渲染 2ms
// 性能提升：25倍
```

#### improvement - 性能提升
**要求**:
- 提供具体的性能数据
- 说明测试环境和条件
- 考虑不同场景下的提升效果

#### tradeoffs - 权衡考虑
**要求**:
- 说明优化带来的代价
- 考虑代码复杂度的增加
- 分析适用边界和限制

### 4. 📈 benchmarkTests - 性能基准测试
**目标**: 提供可复现的性能测试和数据

**测试要求**:
- 使用真实的测试数据
- 涵盖不同规模的场景
- 提供测试代码和环境

**测试类型**:

#### 渲染性能测试
```typescript
// 性能测试示例
function renderPerformanceTest() {
  const sizes = [100, 1000, 5000, 10000];
  
  sizes.forEach(size => {
    const items = generateTestData(size);
    
    const startTime = performance.now();
    render(<TestComponent items={items} />);
    const endTime = performance.now();
    
    console.log(`${size} items: ${endTime - startTime}ms`);
  });
}
```

#### 内存使用测试
```typescript
// 内存测试示例
function memoryUsageTest() {
  const initialMemory = performance.memory.usedJSHeapSize;
  
  // 执行操作
  for (let i = 0; i < 1000; i++) {
    // 测试操作
  }
  
  const finalMemory = performance.memory.usedJSHeapSize;
  console.log(`Memory usage: ${finalMemory - initialMemory} bytes`);
}
```

### 5. ✅ bestPractices - 性能最佳实践
**目标**: 提供5-8条经过验证的性能最佳实践

**实践类型**:

#### 开发时期最佳实践
- 正确的API使用模式
- 性能友好的代码结构
- 避免常见性能陷阱

#### 生产环境最佳实践
- 监控和测量方法
- 性能问题的诊断
- 持续优化策略

#### 团队协作最佳实践
- 性能规范和标准
- Code Review检查点
- 性能测试流程

### 🎯 质量标准

#### 内容质量检查
- [ ] **分析准确**: 性能分析基于实际测试和理论分析
- [ ] **策略实用**: 优化策略针对真实问题且效果明显
- [ ] **数据可靠**: 性能数据真实可信，测试环境明确
- [ ] **实践可行**: 最佳实践经过验证且易于执行
- [ ] **覆盖全面**: 涵盖不同场景和性能维度

#### 技术质量检查
- [ ] **代码正确**: 所有代码示例可运行且性能优化有效
- [ ] **测试完整**: 性能测试覆盖关键场景
- [ ] **分析深入**: 不仅给出结果，更要解释原因
- [ ] **权衡客观**: 公正分析优化的得失

#### 实用价值检查
- [ ] **问题导向**: 解决实际开发中的性能问题
- [ ] **效果明显**: 优化效果可观测且有意义
- [ ] **易于实施**: 开发者容易理解和应用
- [ ] **可持续性**: 提供长期的性能优化指导

### 📝 编写流程建议

#### 1. 性能问题识别
- 收集真实的性能问题案例
- 分析性能瓶颈的根本原因
- 确定优化的重点方向
- 设定性能优化目标

#### 2. 优化方案设计
- 研究相关的优化技术
- 设计针对性的优化策略
- 考虑实现的可行性
- 评估优化的投入产出比

#### 3. 测试验证
- 设计全面的性能测试
- 在不同环境下验证效果
- 测量优化前后的性能差异
- 分析优化的适用边界

#### 4. 最佳实践总结
- 总结成功的优化经验
- 识别常见的性能陷阱
- 制定可执行的性能规范
- 建立性能监控机制

## 💡 不同性能维度的重点

### ⚡ 执行性能
- 算法复杂度优化
- 计算缓存策略
- 批量处理技术
- 异步优化方案

### 🖼️ 渲染性能
- 组件渲染优化
- Virtual DOM效率
- 重渲染控制
- 懒加载策略

### 💾 内存性能
- 内存使用优化
- 垃圾回收优化
- 内存泄漏预防
- 缓存策略设计

### 🌐 网络性能
- 数据获取优化
- 缓存策略
- 预加载技术
- 请求合并

## ❌ 常见问题避免

### 避免的问题：
- **过度优化**: 为了优化而优化，忽略实际需求
- **测试不足**: 缺乏充分的性能测试验证
- **数据虚假**: 提供不真实或误导性的性能数据
- **忽略权衡**: 只强调优化效果，不考虑代价
- **脱离实际**: 理论分析与实际应用脱节

### 追求的目标：
- **问题导向**: 针对真实性能问题提供解决方案
- **效果验证**: 所有优化都有可靠的性能数据支撑
- **平衡考虑**: 综合考虑性能、可维护性和开发效率
- **实用价值**: 提供可立即应用的优化策略
- **持续改进**: 建立持续的性能优化体系

---

**🎯 核心原则**: 性能优化Tab要成为高性能应用的技术指南，通过深入的性能分析和实用的优化策略，帮助开发者构建响应迅速、体验出色的应用程序。
