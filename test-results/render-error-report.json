{"timestamp": "2025-07-04T10:17:31.572Z", "summary": {"total": 1, "successful": 1, "failed": 0, "totalErrors": 0, "totalWarnings": 0, "averageLoadTime": 8150, "skeletonApis": 0, "partialApis": 0, "completeApis": 1, "averageCompletionRate": 100}, "results": [{"apiName": "ecma:array-flat", "url": "http://localhost:8080/full-tab-render/ecma:array-flat", "success": true, "loadTime": 8150, "errors": [], "warnings": [], "tabStatus": {"🏗️ Tab完成状态检测": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-4": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-5": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "📊 渲染状态总结": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-9": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-10": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "1. 基本信息 TabOK": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-14": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "1.1 📋 核心概览": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-19": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-20": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "1.2 🔧 语法详解": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-24": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-25": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "1.3 🎯 使用模式": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-29": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-30": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "1.4 ⚖️ 对比分析": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-34": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-35": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "2. 业务场景 TabOK": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-39": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "2.1 多源异构数据规范化": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-44": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-45": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "2.2 RBAC权限系统中的权限聚合": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-49": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-50": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "2.3 动态渲染React组件列表": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-54": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-55": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "3. 原理解析 TabOK": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-59": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "3.1 ⚙️ 实现机制": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-64": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-65": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "3.2 📊 可视化说明": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-69": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-70": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "3.3 💡 通俗解释": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-74": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-75": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "3.4 🎯 设计考虑": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-79": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-80": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "3.5 🔗 相关概念": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-84": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-85": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "4. 面试准备 TabOK": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-89": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "4.1 `Array.prototype.flat()` 的作用是什么？请举一个基本例子。": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-94": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-95": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "4.2 `flat()` 和 `flatMap()` 有什么区别？什么场景下应该优先使用 `flatMap()`？": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-99": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-100": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "4.3 `Array.flat()` 如何处理数组中的空槽（empty slots）、`undefined` 和 `null`？": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-104": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-105": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "4.4 如何使用 `flat()` 将一个任意深度的嵌套数组完全展平为一维数组？": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-109": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-110": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "4.5 如果需要在不支持ES2019的环境中使用 `flat`，请你手写一个它的 Polyfill。": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-114": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-115": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "5. 常见问题 TabOK": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-119": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "5.1 `flat()` 和 `flatMap()` 我总是搞混，到底应该用哪个？": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-124": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-125": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "5.2 为什么 `[1, 2, , 4].flat()` 的结果是 `[1, 2, 4]`？那个空位去哪了？": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-129": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-130": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "5.3 我有一个非常深的嵌套数组，用 `flat(Infinity)` 会不会有性能问题或导致崩溃？": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-134": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-135": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "5.4 `flat()` 能不能扁平化对象数组或者其他可迭代对象？": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-139": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-140": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "5.5 为什么 `flat()` 不改变原数组？这有什么好处？": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-144": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-145": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "6. 知识考古 TabOK": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-149": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "6.1 📖 介绍": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-154": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-155": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "6.2 🏛️ 历史背景": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-159": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-160": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "6.3 🔄 演进历程": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-164": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-165": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "6.4 📅 发展时间线": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-169": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-170": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "6.5 👥 关键人物": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-174": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-175": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "6.6 💡 核心概念": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-179": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-180": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "6.7 🎯 设计哲学": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-184": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-185": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "6.8 🌍 历史影响": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-189": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-190": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "6.9 🚀 现代意义": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-194": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-195": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "7. 性能优化 TabOK": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-199": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "7.1 ✨ 最佳实践": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-204": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-205": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "7.2 🎯 优化策略": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-209": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-210": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "7.3 📊 性能基准测试": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-214": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-215": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "8. 调试技巧 TabOK": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-219": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "8.1 🚨 常见错误": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-224": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-225": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "8.2 ⚙️ 调试技巧": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-229": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-230": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "9. 本质洞察 TabOK": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-234": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "9.1 ❓ 核心问题": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-239": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-240": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "9.5 🤔 哲学意义": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-244": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-245": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "9.6 🚀 未来影响": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-249": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}, "Tab-250": {"rendered": true, "hasError": false, "isSkeletonContent": true, "contentQuality": "skeleton"}}, "contentStatus": {"overallQuality": "complete", "skeletonTabs": [], "partialTabs": [], "completeTabs": [], "totalTabs": 9, "completionRate": 100}, "screenshots": {"full": "/Users/<USER>/Documents/2026/guidebook/test-results/screenshots/ecma:array-flat-full.png", "errors": []}}]}