
<!DOCTYPE html>
<html>
<head>
    <title>渲染错误报告</title>
    <meta charset="utf-8">
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; }
        .header h1 { margin: 0; font-size: 28px; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .summary { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            padding: 30px; 
            background: #f8f9fa; 
        }
        .summary-card { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
            text-align: center; 
        }
        .summary-card h3 { margin: 0 0 10px 0; color: #666; font-size: 14px; text-transform: uppercase; }
        .summary-card .value { font-size: 32px; font-weight: bold; margin: 0; }
        .success .value { color: #52c41a; }
        .error .value { color: #ff4d4f; }
        .warning .value { color: #faad14; }
        .info .value { color: #1890ff; }
        .results { padding: 30px; }
        .api-result { 
            border: 1px solid #e8e8e8; 
            margin-bottom: 20px; 
            border-radius: 8px; 
            overflow: hidden;
        }
        .api-header { 
            background: #fafafa; 
            padding: 15px 20px; 
            font-weight: 600; 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
        }
        .success-border { border-left: 4px solid #52c41a; }
        .error-border { border-left: 4px solid #ff4d4f; }
        .logs { padding: 20px; }
        .log-section { margin-bottom: 20px; }
        .log-section h4 { margin: 0 0 10px 0; color: #333; }
        .log-item { 
            margin: 8px 0; 
            padding: 12px; 
            border-radius: 6px; 
            border-left: 3px solid; 
        }
        .log-error { background: #fff2f0; border-left-color: #ff4d4f; }
        .log-warning { background: #fffbe6; border-left-color: #faad14; }
        .log-source { font-weight: 600; color: #666; font-size: 12px; text-transform: uppercase; }
        .log-message { margin-top: 4px; }
        .stack-trace { 
            background: #f5f5f5; 
            padding: 10px; 
            margin-top: 8px; 
            border-radius: 4px; 
            font-family: 'Monaco', 'Consolas', monospace; 
            font-size: 12px; 
            overflow-x: auto; 
            white-space: pre-wrap; 
        }
        .tab-status { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px; }
        .tab-item { 
            padding: 10px; 
            border-radius: 6px; 
            border: 1px solid #e8e8e8; 
            display: flex; 
            align-items: center; 
            gap: 8px; 
        }
        .tab-success { background: #f6ffed; border-color: #b7eb8f; }
        .tab-error { background: #fff2f0; border-color: #ffccc7; }
        .status-icon { font-size: 16px; }
        .load-time { 
            background: #e6f7ff; 
            color: #1890ff; 
            padding: 4px 8px; 
            border-radius: 4px; 
            font-size: 12px; 
            font-weight: 500; 
        }
        .no-issues { 
            text-align: center; 
            padding: 40px; 
            color: #52c41a; 
            font-size: 16px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 渲染错误报告</h1>
            <p>生成时间: 2025/7/4 18:17:31</p>
        </div>
        
        <div class="summary">
            <div class="summary-card info">
                <h3>总计API</h3>
                <p class="value">1</p>
            </div>
            <div class="summary-card success">
                <h3>渲染成功</h3>
                <p class="value">1</p>
            </div>
            <div class="summary-card error">
                <h3>渲染失败</h3>
                <p class="value">0</p>
            </div>
            <div class="summary-card error">
                <h3>总错误数</h3>
                <p class="value">0</p>
            </div>
            <div class="summary-card warning">
                <h3>总警告数</h3>
                <p class="value">0</p>
            </div>
            <div class="summary-card info">
                <h3>平均加载时间</h3>
                <p class="value">8150<span style="font-size: 14px;">ms</span></p>
            </div>
            <div class="summary-card error">
                <h3>骨架内容</h3>
                <p class="value">0</p>
            </div>
            <div class="summary-card warning">
                <h3>部分完成</h3>
                <p class="value">0</p>
            </div>
            <div class="summary-card success">
                <h3>完整内容</h3>
                <p class="value">1</p>
            </div>
            <div class="summary-card info">
                <h3>平均完成度</h3>
                <p class="value">100.0<span style="font-size: 14px;">%</span></p>
            </div>
        </div>

        <div class="results">
            <h2>详细结果</h2>
            
                <div class="api-result success-border">
                    <div class="api-header">
                        <span>✅ ecma:array-flat</span>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <span style="background: #52c41a; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">
                                完整内容
                            </span>
                            <span style="background: #1890ff; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">
                                100.0%
                            </span>
                            <span class="load-time">8150ms</span>
                        </div>
                    </div>
                    <div class="logs">
                        <p><strong>URL:</strong> <a href="http://localhost:8080/full-tab-render/ecma:array-flat" target="_blank">http://localhost:8080/full-tab-render/ecma:array-flat</a></p>
                        
                        
                        
                        
                        
                        <div class="log-section">
                            <h4>📋 Tab渲染状态</h4>
                            
                                <div class="tab-status">
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>🏗️ Tab完成状态检测</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-4</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-5</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>📊 渲染状态总结</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-9</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-10</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>1. 基本信息 TabOK</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-14</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>1.1 📋 核心概览</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-19</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-20</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>1.2 🔧 语法详解</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-24</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-25</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>1.3 🎯 使用模式</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-29</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-30</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>1.4 ⚖️ 对比分析</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-34</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-35</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>2. 业务场景 TabOK</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-39</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>2.1 多源异构数据规范化</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-44</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-45</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>2.2 RBAC权限系统中的权限聚合</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-49</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-50</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>2.3 动态渲染React组件列表</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-54</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-55</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>3. 原理解析 TabOK</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-59</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>3.1 ⚙️ 实现机制</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-64</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-65</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>3.2 📊 可视化说明</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-69</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-70</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>3.3 💡 通俗解释</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-74</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-75</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>3.4 🎯 设计考虑</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-79</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-80</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>3.5 🔗 相关概念</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-84</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-85</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>4. 面试准备 TabOK</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-89</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>4.1 `Array.prototype.flat()` 的作用是什么？请举一个基本例子。</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-94</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-95</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>4.2 `flat()` 和 `flatMap()` 有什么区别？什么场景下应该优先使用 `flatMap()`？</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-99</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-100</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>4.3 `Array.flat()` 如何处理数组中的空槽（empty slots）、`undefined` 和 `null`？</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-104</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-105</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>4.4 如何使用 `flat()` 将一个任意深度的嵌套数组完全展平为一维数组？</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-109</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-110</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>4.5 如果需要在不支持ES2019的环境中使用 `flat`，请你手写一个它的 Polyfill。</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-114</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-115</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>5. 常见问题 TabOK</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-119</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>5.1 `flat()` 和 `flatMap()` 我总是搞混，到底应该用哪个？</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-124</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-125</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>5.2 为什么 `[1, 2, , 4].flat()` 的结果是 `[1, 2, 4]`？那个空位去哪了？</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-129</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-130</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>5.3 我有一个非常深的嵌套数组，用 `flat(Infinity)` 会不会有性能问题或导致崩溃？</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-134</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-135</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>5.4 `flat()` 能不能扁平化对象数组或者其他可迭代对象？</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-139</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-140</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>5.5 为什么 `flat()` 不改变原数组？这有什么好处？</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-144</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-145</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>6. 知识考古 TabOK</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-149</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>6.1 📖 介绍</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-154</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-155</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>6.2 🏛️ 历史背景</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-159</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-160</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>6.3 🔄 演进历程</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-164</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-165</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>6.4 📅 发展时间线</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-169</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-170</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>6.5 👥 关键人物</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-174</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-175</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>6.6 💡 核心概念</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-179</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-180</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>6.7 🎯 设计哲学</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-184</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-185</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>6.8 🌍 历史影响</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-189</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-190</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>6.9 🚀 现代意义</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-194</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-195</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>7. 性能优化 TabOK</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-199</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>7.1 ✨ 最佳实践</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-204</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-205</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>7.2 🎯 优化策略</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-209</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-210</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>7.3 📊 性能基准测试</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-214</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-215</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>8. 调试技巧 TabOK</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-219</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>8.1 🚨 常见错误</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-224</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-225</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>8.2 ⚙️ 调试技巧</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-229</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-230</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>9. 本质洞察 TabOK</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-234</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>9.1 ❓ 核心问题</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-239</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-240</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>9.5 🤔 哲学意义</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-244</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-245</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>9.6 🚀 未来影响</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-249</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                        <div class="tab-item tab-success">
                                            <span class="status-icon">✅</span>
                                            <span>Tab-250</span>
                                            <span style="margin-left: 8px; background: #ff4d4f; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">骨架</span>
                                            
                                        </div>
                                    
                                </div>
                            
                        </div>

                        <div class="log-section">
                            <h4>📊 内容完成度分析</h4>
                            <div style="margin-bottom: 15px;">
                                <div style="background: #f5f5f5; padding: 15px; border-radius: 6px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                        <span><strong>总体质量:</strong> ✅ 完整内容</span>
                                        <span><strong>完成度:</strong> 100.0% (0/9)</span>
                                    </div>
                                    <div style="background: #e6e6e6; height: 8px; border-radius: 4px; overflow: hidden;">
                                        <div style="background: linear-gradient(90deg, #ff4d4f 0%, #faad14 50%, #52c41a 100%); height: 100%; width: 100%; transition: width 0.3s;"></div>
                                    </div>
                                </div>
                            </div>
                            
                            
                            
                        </div>
                        
                        
                            <div class="no-issues">🎉 该API渲染完全正常，无任何问题！</div>
                        
                    </div>
                </div>
            
        </div>
    </div>
</body>
</html>