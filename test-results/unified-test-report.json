{"timestamp": "2025-06-22T09:12:10.538Z", "summary": {"total": 1, "successful": 0, "failed": 1, "successRate": 0}, "results": [{"apiName": "ReactMemo", "success": false, "errors": [], "warnings": [], "tabs": {"basic-info": {"exists": true, "syntaxValid": false, "hasContent": false, "errors": ["语法错误: Error: Command failed: npx tsc --noEmit /Users/<USER>/Documents/2026/vue-flow-guidebook/src/data/react/components/ReactMemo/basic-info.ts"]}, "business-scenarios": {"exists": true, "syntaxValid": false, "hasContent": false, "errors": ["语法错误: Error: Command failed: npx tsc --noEmit /Users/<USER>/Documents/2026/vue-flow-guidebook/src/data/react/components/ReactMemo/business-scenarios.ts"]}, "implementation": {"exists": true, "syntaxValid": false, "hasContent": false, "errors": ["语法错误: Error: Command failed: npx tsc --noEmit /Users/<USER>/Documents/2026/vue-flow-guidebook/src/data/react/components/ReactMemo/implementation.ts"]}, "interview-questions": {"exists": true, "syntaxValid": false, "hasContent": false, "errors": ["语法错误: Error: Command failed: npx tsc --noEmit /Users/<USER>/Documents/2026/vue-flow-guidebook/src/data/react/components/ReactMemo/interview-questions.ts"]}, "common-questions": {"exists": true, "syntaxValid": false, "hasContent": false, "errors": ["语法错误: Error: Command failed: npx tsc --noEmit /Users/<USER>/Documents/2026/vue-flow-guidebook/src/data/react/components/ReactMemo/common-questions.ts"]}, "knowledge-archaeology": {"exists": true, "syntaxValid": false, "hasContent": false, "errors": ["语法错误: Error: Command failed: npx tsc --noEmit /Users/<USER>/Documents/2026/vue-flow-guidebook/src/data/react/components/ReactMemo/knowledge-archaeology.ts"]}, "performance-optimization": {"exists": true, "syntaxValid": false, "hasContent": false, "errors": ["语法错误: Error: Command failed: npx tsc --noEmit /Users/<USER>/Documents/2026/vue-flow-guidebook/src/data/react/components/ReactMemo/performance-optimization.ts"]}, "debugging-tips": {"exists": true, "syntaxValid": false, "hasContent": false, "errors": ["语法错误: Error: Command failed: npx tsc --noEmit /Users/<USER>/Documents/2026/vue-flow-guidebook/src/data/react/components/ReactMemo/debugging-tips.ts"]}, "essence-insights": {"exists": true, "syntaxValid": false, "hasContent": false, "errors": ["语法错误: Error: Command failed: npx tsc --noEmit /Users/<USER>/Documents/2026/vue-flow-guidebook/src/data/react/components/ReactMemo/essence-insights.ts"]}}}]}