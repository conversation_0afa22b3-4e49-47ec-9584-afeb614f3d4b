/**
 * 布局相关的类型定义
 */

// 布局模式枚举
export enum LayoutMode {
  WATERFALL = 'waterfall',  // 瀑布流布局
  SIDEBAR = 'sidebar'       // 左右分栏布局
}

// 树状导航节点类型
export interface TreeNode {
  key: string;
  title: string;
  icon?: React.ReactNode;
  children?: TreeNode[];
  data?: any; // 存储相关数据，如API项、Tab信息等
  level: number; // 节点层级：1=API, 2=Tab, 3=SubTab
  parentKey?: string;
}

// 导航选中状态
export interface NavigationState {
  selectedApiId?: string;
  selectedTabKey?: string;
  selectedSubTabKey?: string;
  expandedKeys: string[];
}

// 布局配置
export interface LayoutConfig {
  mode: LayoutMode;
  sidebarWidth?: number;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

// 布局切换器属性
export interface LayoutSwitcherProps {
  currentMode: LayoutMode;
  onModeChange: (mode: LayoutMode) => void;
  className?: string;
}

// 树状导航属性
export interface TreeNavigationProps {
  treeData: TreeNode[];
  navigationState: NavigationState;
  onNavigationChange: (state: NavigationState) => void;
  className?: string;
}

// 侧边栏布局属性
export interface SidebarLayoutProps {
  treeData: TreeNode[];
  navigationState: NavigationState;
  onNavigationChange: (state: NavigationState) => void;
  children: React.ReactNode;
  config?: LayoutConfig;
  className?: string;
}
