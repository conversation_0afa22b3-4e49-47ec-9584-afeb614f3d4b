// 基础信息接口
export interface BasicInfo {
  // 🏗️ Tab完成状态标识 - 默认："当前tab内容未完成"，完成后改为："内容已完成"
  completionStatus?: string;
  
  definition?: string;
  introduction: string;
  syntax: string;
  // 新增：快速示例 - 完整且简单的基础用法，支持多个示例
  quickExamples?: Array<{
    title: string;
    description: string;
    code: string;
    diagram?: string; // 支持为每个快速示例添加Mermaid图
  }>;

  // 兼容旧版本的单个快速示例
  quickExample?: string;
  quickExampleCode?: string;
  quickExampleDiagram?: string;

  // 并行示例相关
  parallelExampleCode?: string;
  parallelExampleDiagram?: string;

  // 新增：业务场景图表 - 支持多个Mermaid图表展示使用场景和相关API
  scenarioDiagrams?: Array<{
    title: string;
    description?: string;
    diagram: string;
  }>;

  // 兼容旧版本的单个场景图
  scenarioDiagram?: string;
  parameters?: Array<{
    name: string;
    type: string;
    description: string;
    required?: boolean;
    default?: string;
    details?: string;
  }>;
  returnValue?: {
    type: string;
    description: string;
  };
  keyFeatures?: Array<{
    feature: string;
    description: string;
    importance: 'critical' | 'high' | 'medium' | 'low';
    details?: string;
  }>;
  commonUseCases?: Array<{
    title: string;
    description: string;
    code: string;
    diagram?: string; // 支持为每个使用案例添加Mermaid图
    explanation?: string; // 详细解释
    difficulty?: 'beginner' | 'intermediate' | 'advanced';
  }>;
  bestPractices?: string[];
  warnings?: string[];
  notes?: string[];
  limitations?: string[];
  // 新增：对比分析字段
  comparisonAnalysis?: {
    title: string;
    description: string;
    comparisons: Array<{
      name: string;
      description: string;
      advantages: string[];
      disadvantages: string[];
      useCases: string[];
      performance: string;
      complexity: string;
    }>;
    decisionMatrix: {
      description: string;
      scenarios: Array<{
        scenario: string;
        recommended: string;
        reason: string;
      }>;
    };
    summary: string;
  };
}

// 业务场景接口
export interface BusinessScenario {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;

  id: string;
  title: string;
  description: string;
  businessValue: string;
  scenario: string;
  code: string;
  explanation?: string;
  benefits?: string[];
  metrics?: {
    performance?: string;
    userExperience?: string;
    technicalMetrics?: string;
  };
  difficulty?: 'easy' | 'medium' | 'hard';
  tags?: string[];

  // 新增：支持多个Mermaid图表
  diagrams?: Array<{
    title: string;
    description?: string;
    diagram: string;
  }>;

  // 兼容旧版本的单个图表
  diagram?: string;
}

// 底层实现接口
export interface Implementation {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;

  mechanism: string;
  visualization?: string;
  plainExplanation?: string;
  designConsiderations?: string[];
  relatedConcepts?: string[];

  // 新增：支持多个可视化图表
  visualizations?: Array<{
    title: string;
    description?: string;
    diagram: string;
    type?: 'flowchart' | 'sequence' | 'state' | 'class' | 'other';
  }>;
}

// 面试题接口
export interface InterviewQuestion {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;

  id: number;
  question: string;
  answer: {
    brief: string;
    detailed: string;
    code?: string;
  } | string; // 兼容旧版本的字符串答案
  difficulty?: 'easy' | 'medium' | 'hard';
  frequency?: 'high' | 'medium' | 'low';
  category?: string;
  visualization?: string;

  // 新增字段
  code?: string; // 兼容旧版本的代码字段
  tags?: string[];
}

// 常见问题接口
export interface CommonQuestion {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  id: string;
  question: string;
  answer: string;
  code?: string;
  tags?: string[];
  relatedQuestions?: string[];
}

// 知识考古接口
export interface KnowledgeArchaeology {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  introduction?: string;
  background?: string;
  evolution?: string;
  comparisons?: string;
  philosophy?: string;
  presentValue?: string;
  historicalContext?: string;
  timeline?: Array<{
    year: string;
    event: string;
    description: string;
    significance: string;
  }>;
  keyFigures?: Array<{
    name: string;
    role: string;
    contribution: string;
    significance: string;
  }>;
  concepts?: Array<{
    term: string;
    definition: string;
    evolution: string;
    modernRelevance: string;
  }>;
  designPhilosophy?: string;
  impact?: string;
  modernRelevance?: string;
}

// 性能优化接口
export interface PerformanceOptimization {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  optimizationStrategies?: Array<{
    strategy: string;
    description: string;
    implementation: string;
    impact: string;
  }>;
  benchmarks?: Array<{
    scenario: string;
    description: string;
    metrics: {
      [key: string]: string;
    };
    conclusion: string;
  }>;
  monitoring?: {
    tools: Array<{
      name: string;
      description: string;
      usage: string;
    }>;
    metrics: Array<{
      metric: string;
      description: string;
      target: string;
      measurement: string;
    }>;
  };
  bestPractices: Array<{
    practice: string;
    description: string;
    example?: string;
  }>;
}

// 调试技巧接口 - 支持子tabs结构
export interface DebuggingTips {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  // 支持子tabs的结构化内容
  subTabs?: Array<{
    key: string;
    title: string;
    content: {
      introduction?: string;
      sections: Array<{
        title: string;
        description?: string;
        items: Array<{
          title: string;
          description: string;
          code?: string;
          solution?: string;
          prevention?: string;
          steps?: string[];
          tips?: string[];
        }>;
      }>;
    };
  }>;

  // 兼容旧版本的平铺结构
  commonErrors?: Array<{
    error: string;
    cause: string;
    solution: string;
    prevention: string;
    code?: string;
  }>;
  devToolsTips?: Array<{
    tool: string;
    technique: string;
    example: string;
  }>;
  troubleshooting?: Array<{
    symptom: string;
    possibleCauses: string[];
    solutions: string[];
  }>;
}

// 本质洞察接口 - 四重境界结构
// 🌟 这是我们最新的四重境界深度思辨框架，每个特性约15,000字的哲学思辨内容
//
// 四重境界框架说明：
// 🎯 第一重：技术灵魂考古 - 挖掘技术存在的根本原因和本质困境
// 🧠 第二重：设计哲学解构 - 分析设计背后的哲学思想和权衡智慧
// 💡 第三重：真相与幻象识别 - 识别表面现象与深层真相，揭示应用洞察
// 🏗️ 第四重：普世智慧提炼 - 提炼可应用于其他领域的通用原则和架构启示
//
// 目标：实现从技术细节到哲学思辨的认知跃迁，成为开发者的"认知跃迁催化剂"
export interface EssenceInsights {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;

  // 🎯 四重境界子Tab内容 - 从技术问题到普世智慧的完整思辨路径
  subTabs: {
    // 🎯 第一重：技术灵魂考古 - 核心问题
    // 深入挖掘技术特性存在的根本原因，层层剖析复杂性，探索本质困境
    problem: {
      introduction: string;

      // 新增：核心洞察汇总图
      coreInsightDiagram?: string;
      complexityAnalysis: {
        title: string;
        description: string;
        layers: Array<{
          level: string;
          question: string;
          analysis: string;
          depth: number;
        }>;
      };
      fundamentalDilemma: {
        title: string;
        description: string;
        rootCause: string;
        implications: string[];
      };
      existenceNecessity: {
        title: string;
        reasoning: string;
        alternatives: string[];
        whySpecialized: string;
      };
      layeredInquiry: {
        title: string;
        questionChain: Array<{
          layer: string;
          question: string;
          answer: string;
          nextQuestion: string;
        }>;
      };
    };

    // 🧠 第二重：设计哲学解构 - 设计智慧
    // 解构设计背后的哲学思想，分析权衡智慧，探索架构原则
    design: {
      introduction: string;
      minimalism: {
        title: string;
        interfaceDesign: string;
        designChoices: string;
        philosophy: string;
      };
      tradeoffWisdom: {
        title: string;
        tradeoffs: Array<{
          dimension1: string;
          dimension2: string;
          analysis: string;
          reasoning: string;
        }>;
      };
      designPatterns: {
        title: string;
        patterns: Array<{
          pattern: string;
          application: string;
          benefits: string;
        }>;
      };
      architecturalPhilosophy: {
        title: string;
        systemDesign: string;
        principles: string[];
        worldview: string;
      };
    };

    // 💡 第三重：真相与幻象识别 - 应用洞察
    // 识别表面现象与深层真相，分析实际应用中的洞察，揭示性能智慧
    insight: {
      introduction: string;
      stateSync: {
        title: string;
        essence: string;
        deeperUnderstanding: string;
        realValue: string;
      };
      workflowVisualization: {
        title: string;
        diagram: string;
        explanation: string;
        keyPoints: string[];
      };
      realWorldInsights: {
        title: string;
        scenarios: Array<{
          scenario: string;
          insight: string;
          deeperValue: string;
          lessons: string[];
        }>;
      };
      performanceWisdom: {
        title: string;
        builtinOptimizations: string;
        designWisdom: string;
        quantifiedBenefits: string[];
      };
    };

    // 🏗️ 第四重：普世智慧提炼 - 架构思考
    // 提炼可应用于其他领域的普世智慧，探索长远影响，总结架构启示
    architecture: {
      introduction: string;
      ecosystemEvolution: {
        title: string;
        historicalSignificance: string;
        evolutionPath: string;
        futureImpact: string;
      };
      architecturalLayers: {
        title: string;
        diagram: string;
        layers: Array<{
          layer: string;
          role: string;
          significance: string;
        }>;
      };
      designPatternEmbodiment: {
        title: string;
        patterns: Array<{
          pattern: string;
          modernApplication: string;
          deepAnalysis: string;
        }>;
      };
      futureInfluence: {
        title: string;
        longTermImpact: string;
        technologyTrends: string[];
        predictions: string[];
      };
      architecturalInsights: {
        title: string;
        universalWisdom: string;
        applicableFields: string[];
        principles: Array<{
          principle: string;
          explanation: string;
          universality: string;
        }>;
      };
    };
  };

  // 🔄 兼容性支持：旧版本的扁平结构（已废弃，仅用于向后兼容）
  // 如果存在这些字段，前端组件会自动降级到旧版本显示
  coreQuestion?: string;
  designPhilosophy?: {
    worldview: string;
    methodology: string;
    tradeoffs: string;
    evolution: string;
  };
  hiddenTruth?: {
    surfaceProblem: string;
    realProblem: string;
    hiddenCost: string;
    deeperValue: string;
  };
  deeperQuestions?: string[];
}

// 最佳实践接口
export interface BestPractice {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  id: string;
  title: string;
  description: string;
  category: string;
  priority: 'high' | 'medium' | 'low';
  practices: Array<{
    title: string;
    code: string;
    explanation: string;
  }>;
  commonMistakes?: Array<{
    title: string;
    wrongCode: string;
    correctCode: string;
    explanation: string;
  }>;
}

// 常见陷阱接口
export interface CommonPitfall {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  id: string;
  title: string;
  description: string;
  severity: 'high' | 'medium' | 'low';
  problem: string;
  wrongCode: string;
  correctCode: string;
  explanation: string;
  prevention: string;
}

// 性能提示接口
export interface PerformanceTip {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  difficulty: 'easy' | 'medium' | 'hard';
  problem: string;
  solution: string;
  implementation: string;
  metrics: string;
  tools: string[];
}

// 实际案例接口
export interface RealWorldExample {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  id: string;
  title: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  scenario: string;
  technologies: string[];
  implementation: string;
  keyFeatures: string[];
  performanceMetrics: {
    [key: string]: string;
  };
}

// 学习路径接口
export interface LearningPath {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  prerequisites: string[];
  learningSteps: Array<{
    step: number;
    title: string;
    description: string;
    resources?: string[];
    practiceExercises?: Array<{
      title: string;
      description: string;
      difficulty: 'easy' | 'medium' | 'hard';
      estimatedTime: string;
    }>;
  }>;
  nextSteps: string[];
  estimatedTime: string;
  skillLevel: 'beginner' | 'intermediate' | 'advanced';
}

// 版本迁移接口
export interface VersionMigration {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  vue2vs3: string;
  breakingChanges?: Array<{
    change: string;
    impact: string;
    migration: string;
    codeExample?: string;
  }>;
  migrationGuide: string;
  deprecatedAlternatives?: string[];
  futureChanges?: string;
}

// 生态工具接口
export interface EcosystemTools {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  relatedAPIs: Array<{
    name: string;
    relationship: string;
    useCase: string;
  }>;
  libraryIntegration: Array<{
    name: string;
    description: string;
    example: string;
    popularity: 'high' | 'medium' | 'low';
  }>;
  devTools: Array<{
    name: string;
    description: string;
    category: string;
  }>;
  communityResources: string[];
}

// 实战项目接口
export interface RealWorldProjects {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  projects: Array<{
    title: string;
    description: string;
    codeExample: string;
    complexity: 'simple' | 'medium' | 'complex';
    useCase: string;
    technologies?: string[];
    githubLink?: string;
  }>;
  patterns: Array<{
    name: string;
    description: string;
    when: string;
    code: string;
  }>;
}

// 🆕 自定义扩展Tab接口
export interface ExtensionTabs {
  // 🏗️ Tab完成状态标识
  completionStatus?: string;
  
  title: string;
  description: string;
  tabs: Array<{
    key: string;          // 文件名（如：state-management-comparison）
    title: string;        // Tab显示标题
    content: any;         // Tab内容对象
  }>;
}

// 主接口
export interface ApiItem {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  syntax: string;
  example: string;
  notes?: string;
  isNew?: boolean;
  version?: string;
  tags?: string[];

  // 核心维度（6个基础Tab）
  basicInfo?: BasicInfo;
  businessScenarios?: BusinessScenario[];
  bestPractices?: BestPractice[];
  commonPitfalls?: CommonPitfall[];
  performanceTips?: PerformanceTip[];
  realWorldExamples?: RealWorldExample[];
  implementation?: Implementation;
  interviewQuestions?: InterviewQuestion[];
  knowledgeArchaeology?: KnowledgeArchaeology;
  commonQuestions?: CommonQuestion[];

  // 高级维度（6个扩展Tab）
  performanceOptimization?: PerformanceOptimization;
  learningPath?: LearningPath;
  versionMigration?: VersionMigration;
  ecosystemTools?: EcosystemTools;
  realWorldProjects?: RealWorldProjects;
  debuggingTips?: DebuggingTips;
  
  // 🆕 本质洞察Tab
  essenceInsights?: EssenceInsights;
  
  // 🆕 自定义扩展Tab系统
  extensionTabs?: ExtensionTabs;
}

// 搜索选项接口
export interface SearchOptions {
  title: boolean;
  description: boolean;
  syntax: boolean;
  example: boolean;
  notes: boolean;
  beginnerGuide: boolean;
  businessScenarios: boolean;
  implementation: boolean;
  category: boolean;
} 

// 兼容性接口（支持复数形式）
export type InterviewQuestions = InterviewQuestion[];
export type CommonQuestions = CommonQuestion[];
export type BusinessScenarios = BusinessScenario[]; 