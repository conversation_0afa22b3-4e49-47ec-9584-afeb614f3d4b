import React, { useState, useEffect } from 'react';
import { Button, Card, Progress, Alert, Collapse, Tag, Space } from 'antd';
import { PlayCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { allReactApis } from '@/data/react';

interface TestResult {
  api: string;
  tab: string;
  status: 'pending' | 'pass' | 'fail' | 'warning';
  error?: string;
  warnings?: string[];
  renderTime?: number;
}

interface ApiTestSuite {
  api: string;
  results: TestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    warnings: number;
  };
}

const TABS = [
  { key: 'basic', label: '基本信息' },
  { key: 'business', label: '业务场景' },
  { key: 'implementation', label: '原理解析' },
  { key: 'interview', label: '面试准备' },
  { key: 'common', label: '常见问题' },
  { key: 'archaeology', label: '知识考古' },
  { key: 'performance', label: '性能优化' },
  { key: 'debugging', label: '调试技巧' },
  { key: 'essence', label: '本质洞察' }
];

export const ApiTester: React.FC = () => {
  const [testResults, setTestResults] = useState<ApiTestSuite[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');
  const [progress, setProgress] = useState(0);

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    setProgress(0);

    const totalTests = allReactApis.length * TABS.length;
    let completedTests = 0;

    for (const api of allReactApis) {
      setCurrentTest(`测试 ${api.title}...`);
      
      const apiResults: TestResult[] = [];
      
      for (const tab of TABS) {
        const testResult = await testTabRendering(api, tab.key);
        apiResults.push(testResult);
        
        completedTests++;
        setProgress((completedTests / totalTests) * 100);
        
        // 实时更新结果
        setTestResults(prev => {
          const existing = prev.find(r => r.api === api.id);
          if (existing) {
            existing.results = [...apiResults];
            existing.summary = calculateSummary(apiResults);
            return [...prev];
          } else {
            return [...prev, {
              api: api.id,
              results: apiResults,
              summary: calculateSummary(apiResults)
            }];
          }
        });
        
        // 短暂延迟，避免阻塞UI
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    setIsRunning(false);
    setCurrentTest('测试完成');
  };

  const testTabRendering = async (api: any, tabKey: string): Promise<TestResult> => {
    const startTime = performance.now();
    
    try {
      // 模拟渲染测试
      const result = await simulateTabRender(api, tabKey);
      const renderTime = performance.now() - startTime;
      
      return {
        api: api.id,
        tab: tabKey,
        status: result.status,
        error: result.error,
        warnings: result.warnings,
        renderTime
      };
    } catch (error) {
      return {
        api: api.id,
        tab: tabKey,
        status: 'fail',
        error: error instanceof Error ? error.message : '未知错误',
        renderTime: performance.now() - startTime
      };
    }
  };

  const simulateTabRender = async (api: any, tabKey: string) => {
    const warnings: string[] = [];
    
    // 检查数据完整性
    const tabData = getTabData(api, tabKey);
    
    if (!tabData) {
      return { status: 'fail' as const, error: `${tabKey} 数据不存在` };
    }

    // 检查基本信息的Mermaid图表
    if (tabKey === 'basic' && api.basicInfo?.scenarioDiagram) {
      if (Array.isArray(api.basicInfo.scenarioDiagram)) {
        // 新的多图表结构
        api.basicInfo.scenarioDiagram.forEach((diagram: any, index: number) => {
          if (!diagram.diagram || !diagram.title) {
            warnings.push(`图表${index + 1}缺少必要字段`);
          }
          if (!diagram.diagram.includes('graph') && !diagram.diagram.includes('flowchart')) {
            warnings.push(`图表${index + 1}可能缺少Mermaid语法`);
          }
        });
      } else if (typeof api.basicInfo.scenarioDiagram === 'string') {
        warnings.push('使用旧的单图表结构');
      }
    }

    // 检查调试技巧的subTabs结构
    if (tabKey === 'debugging' && api.debuggingTips) {
      if (!api.debuggingTips.subTabs) {
        warnings.push('缺少subTabs结构');
      } else if (api.debuggingTips.subTabs.length === 0) {
        warnings.push('subTabs为空');
      }
    }

    // 检查空数组和TODO
    const dataStr = JSON.stringify(tabData);
    if (dataStr.includes('TODO')) {
      warnings.push('包含TODO标记');
    }

    // 模拟渲染时间
    await new Promise(resolve => setTimeout(resolve, Math.random() * 200 + 50));

    return {
      status: warnings.length > 0 ? 'warning' as const : 'pass' as const,
      warnings
    };
  };

  const getTabData = (api: any, tabKey: string) => {
    switch (tabKey) {
      case 'basic': return api.basicInfo;
      case 'business': return api.businessScenarios;
      case 'implementation': return api.implementation;
      case 'interview': return api.interviewQuestions;
      case 'common': return api.commonQuestions;
      case 'archaeology': return api.knowledgeArchaeology;
      case 'performance': return api.performanceOptimization;
      case 'debugging': return api.debuggingTips;
      case 'essence': return api.essenceInsights;
      default: return null;
    }
  };

  const calculateSummary = (results: TestResult[]) => {
    return {
      total: results.length,
      passed: results.filter(r => r.status === 'pass').length,
      failed: results.filter(r => r.status === 'fail').length,
      warnings: results.filter(r => r.status === 'warning').length
    };
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'fail': return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'warning': return <WarningOutlined style={{ color: '#faad14' }} />;
      default: return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'success';
      case 'fail': return 'error';
      case 'warning': return 'warning';
      default: return 'default';
    }
  };

  const overallSummary = testResults.reduce(
    (acc, apiResult) => ({
      total: acc.total + apiResult.summary.total,
      passed: acc.passed + apiResult.summary.passed,
      failed: acc.failed + apiResult.summary.failed,
      warnings: acc.warnings + apiResult.summary.warnings
    }),
    { total: 0, passed: 0, failed: 0, warnings: 0 }
  );

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">🧪 API渲染测试工具</h1>
        <p className="text-gray-600 mb-4">
          自动测试所有API的每个Tab是否能正常渲染，检测数据结构问题、Mermaid图表问题等。
        </p>
        
        <Space>
          <Button 
            type="primary" 
            icon={<PlayCircleOutlined />}
            onClick={runAllTests}
            loading={isRunning}
            size="large"
          >
            {isRunning ? '测试中...' : '开始测试'}
          </Button>
          
          {testResults.length > 0 && (
            <div className="flex items-center gap-4">
              <span>总计: {overallSummary.total}</span>
              <Tag color="success">通过: {overallSummary.passed}</Tag>
              <Tag color="error">失败: {overallSummary.failed}</Tag>
              <Tag color="warning">警告: {overallSummary.warnings}</Tag>
              <span>成功率: {((overallSummary.passed / overallSummary.total) * 100).toFixed(1)}%</span>
            </div>
          )}
        </Space>
      </div>

      {isRunning && (
        <Card className="mb-6">
          <div className="text-center">
            <p className="mb-2">{currentTest}</p>
            <Progress percent={Math.round(progress)} status="active" />
          </div>
        </Card>
      )}

      {testResults.length > 0 && (
        <Collapse>
          {testResults.map(apiResult => (
            <Collapse.Panel
              key={apiResult.api}
              header={
                <div className="flex items-center justify-between">
                  <span className="font-medium">{apiResult.api}</span>
                  <Space>
                    <Tag color="success">{apiResult.summary.passed}</Tag>
                    <Tag color="error">{apiResult.summary.failed}</Tag>
                    <Tag color="warning">{apiResult.summary.warnings}</Tag>
                  </Space>
                </div>
              }
            >
              <div className="space-y-2">
                {apiResult.results.map(result => (
                  <div key={result.tab} className="flex items-center justify-between p-2 border rounded">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.status)}
                      <span>{TABS.find(t => t.key === result.tab)?.label || result.tab}</span>
                      {result.renderTime && (
                        <span className="text-xs text-gray-500">({result.renderTime.toFixed(0)}ms)</span>
                      )}
                    </div>
                    
                    <div>
                      {result.error && (
                        <Alert message={result.error} type="error" size="small" />
                      )}
                      {result.warnings && result.warnings.length > 0 && (
                        <div className="text-xs text-orange-600">
                          {result.warnings.join(', ')}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </Collapse.Panel>
          ))}
        </Collapse>
      )}
    </div>
  );
};
