import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, Settings, <PERSON>, Co<PERSON>, <PERSON>, Eye } from "lucide-react";
import { SettingOutlined } from "@ant-design/icons";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Dropdown, Menu } from 'antd';
import { SearchOptions } from "@/types/api";
import { FRAMEWORK_CONFIG } from './TabConfig';

interface HeaderSectionProps {
  title: string;
  subtitle: string;
  themeColor: string;
  searchTerm: string;
  searchOptions: SearchOptions;
  filteredDataLength: number;
  currentSelectedCategory: string;
  versionCategories: Array<{ category: string; count: number; color: string }>;
  onThemeChange?: (theme: 'vue' | 'react' | 'nextjs' | 'typescript' | 'ecma' | 'default') => void;
  onSearchTermChange: (term: string) => void;
  onSearchOptionChange: (option: keyof SearchOptions, checked: boolean) => void;
  onCategoryChange: (category: string) => void;
  getActiveSearchCount: () => number;
  setAllSearchOptions: (value: boolean) => void;
}

const HeaderSection: React.FC<HeaderSectionProps> = ({
  title,
  subtitle,
  themeColor,
  searchTerm,
  searchOptions,
  filteredDataLength,
  currentSelectedCategory,
  versionCategories,
  onThemeChange,
  onSearchTermChange,
  onSearchOptionChange,
  onCategoryChange,
  getActiveSearchCount,
  setAllSearchOptions
}) => {
  const navigate = useNavigate();

  const getColorStyles = (color: string, isActive: boolean) => {
    const baseStyles = isActive 
      ? 'bg-white text-gray-900 border-gray-300 shadow-md' 
      : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100';
    
    if (isActive) {
      switch (color) {
        case 'blue': return `${baseStyles} ring-2 ring-blue-200`;
        case 'green': return `${baseStyles} ring-2 ring-green-200`;
        case 'purple': return `${baseStyles} ring-2 ring-purple-200`;
        case 'orange': return `${baseStyles} ring-2 ring-orange-200`;
        case 'red': return `${baseStyles} ring-2 ring-red-200`;
        case 'yellow': return `${baseStyles} ring-2 ring-yellow-200`;
        default: return `${baseStyles} ring-2 ring-gray-200`;
      }
    }
    return baseStyles;
  };

  return (
    <>
      {/* 顶部专业切换器 */}
      <div className="flex justify-end mb-6">
        <Dropdown
          overlay={
            <Menu className="min-w-[200px] rounded-xl shadow-xl border-0 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm">
              {FRAMEWORK_CONFIG.map(({ key, label, version, icon, description }) => (
                <Menu.Item 
                  key={key}
                  onClick={() => {
                    // 如果是当前页面，不做跳转
                    if (key === themeColor) return;
                    
                    // 根据选择的框架进行路由跳转
                    if (key === 'vue') {
                      navigate('/vue');
                    } else if (key === 'react') {
                      navigate('/react');
                    } else if (key === 'ecma') {
                      navigate('/ecma');
                    } else if (key === 'nextjs') {
                      navigate('/nextjs');
                    } else if (key === 'typescript') {
                      navigate('/typescript');
                    } else {
                      navigate('/');
                    }
                    
                    // 如果有回调函数，也调用它
                    onThemeChange?.(key as 'vue' | 'react' | 'nextjs' | 'typescript' | 'ecma' | 'default');
                  }}
                  className={`px-4 py-3 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors ${
                    themeColor === key ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <span className="text-lg mt-0.5">{icon}</span>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className={`font-medium ${
                          themeColor === key ? 'text-blue-600 dark:text-blue-400' : 'text-neutral-900 dark:text-neutral-100'
                        }`}>
                          {label}
                        </span>
                        {version && (
                          <span className="text-xs text-neutral-500 dark:text-neutral-400">v{version}</span>
                        )}
                      </div>
                      <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                        {description}
                      </div>
                    </div>
                    {themeColor === key && (
                      <Check className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-1" />
                    )}
                  </div>
                </Menu.Item>
              ))}
            </Menu>
          }
          trigger={['click']}
          placement="bottomRight"
        >
          <Button 
            variant="outline" 
            className="bg-white/80 dark:bg-neutral-900/80 backdrop-blur-sm border-neutral-200/50 dark:border-neutral-700/50 hover:bg-white dark:hover:bg-neutral-800 transition-all duration-300 shadow-sm hover:shadow-md"
          >
            <span className="mr-2">
              {FRAMEWORK_CONFIG.find(f => f.key === themeColor)?.icon || '⚪'}
            </span>
            <span className="font-medium">
              {FRAMEWORK_CONFIG.find(f => f.key === themeColor)?.label || 'General'}
            </span>
          </Button>
        </Dropdown>
      </div>

      {/* 现代化标题区域 */}
      <div className="text-center mb-8 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl blur-3xl"></div>
        <div className="relative bg-white/60 dark:bg-neutral-900/60 backdrop-blur-xl rounded-3xl p-8 border border-neutral-200/30 dark:border-neutral-700/30 shadow-xl">
          <div className="flex items-center justify-center gap-4 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
              <span className="text-white text-xl font-bold">
                {FRAMEWORK_CONFIG.find(f => f.key === themeColor)?.icon || '📚'}
              </span>
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-neutral-900 via-blue-800 to-purple-800 dark:from-neutral-100 dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent">
                {title}
              </h1>
              <p className="text-lg text-neutral-600 dark:text-neutral-400 mt-2 font-medium">
                {subtitle}
              </p>
            </div>
          </div>
          
          {/* 统计信息 */}
          <div className="flex items-center justify-center gap-8 mt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{filteredDataLength}</div>
              <div className="text-sm text-neutral-500 dark:text-neutral-400">API 总数</div>
            </div>
            
            <div className="w-px h-8 bg-neutral-300 dark:bg-neutral-600"></div>
            
            <div className="flex items-center gap-3">
              <div className={`w-1.5 h-1.5 rounded-full ${
                themeColor === 'vue' ? 'bg-green-500/50' :
                themeColor === 'react' ? 'bg-blue-500/50' :
                themeColor === 'ecma' ? 'bg-orange-500/50' :
                themeColor === 'nextjs' ? 'bg-indigo-500/50' :
                themeColor === 'typescript' ? 'bg-slate-500/50' :
                'bg-slate-500/50'
              }`}></div>
              <div className={`w-1.5 h-1.5 rounded-full ${
                themeColor === 'vue' ? 'bg-blue-500' :
                themeColor === 'react' ? 'bg-cyan-500' :
                themeColor === 'ecma' ? 'bg-orange-500' :
                themeColor === 'nextjs' ? 'bg-purple-500' :
                themeColor === 'typescript' ? 'bg-gray-500' :
                'bg-blue-500'
              }`}></div>
              <div className="w-12 h-px bg-gradient-to-l from-transparent via-neutral-300 dark:via-neutral-600 to-transparent"></div>
            </div>
          </div>
          
          {/* 专业特性标签 */}
          <div className="flex flex-wrap justify-center items-center gap-6">
            <div className="group flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
              <div className="relative">
                <div className="w-1.5 h-1.5 bg-orange-400 rounded-full animate-pulse"></div>
                <div className="absolute inset-0 w-1.5 h-1.5 bg-orange-400/30 rounded-full animate-ping"></div>
              </div>
              <span className="font-medium group-hover:text-orange-500 transition-colors">Live Updates</span>
            </div>
            
            <div className="group flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
              <Star className="w-3.5 h-3.5 text-yellow-500 group-hover:scale-110 transition-transform" />
              <span className="font-medium group-hover:text-yellow-600 transition-colors">Bookmarkable</span>
            </div>
            
            <div className="group flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
              <Copy className="w-3.5 h-3.5 text-blue-500 group-hover:scale-110 transition-transform" />
              <span className="font-medium group-hover:text-blue-600 transition-colors">One-Click Copy</span>
            </div>
            
            <div className="group flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
              <SettingOutlined className="w-3.5 h-3.5 text-purple-500 group-hover:scale-110 transition-transform" />
              <span className="font-medium group-hover:text-purple-600 transition-colors">Smart Tabs</span>
            </div>
          </div>
        </div>
      </div>

      {/* 现代化搜索区域 */}
      <div className="mb-6 space-y-4 max-w-5xl mx-auto">
        <div className="relative group">
          {/* 搜索框背景光晕 */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          
          <div className="relative bg-white/80 dark:bg-neutral-900/80 backdrop-blur-xl rounded-2xl border border-neutral-200/50 dark:border-neutral-700/50 p-1.5 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
              <Input
                placeholder="搜索 API、函数、组件..."
                value={searchTerm}
                onChange={(e) => onSearchTermChange(e.target.value)}
                className="pl-12 pr-32 h-14 text-lg bg-transparent border-0 focus:ring-0 focus:outline-none"
              />
              
              {/* 搜索选项按钮 */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-10 px-4 bg-neutral-50/80 hover:bg-neutral-100/80 dark:bg-neutral-800/80 dark:hover:bg-neutral-700/80 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50 rounded-xl transition-all duration-300 hover:scale-105"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    <span className="text-sm font-medium">搜索选项</span>
                    {getActiveSearchCount() > 0 && (
                      <div className="ml-2 bg-blue-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                        {getActiveSearchCount()}
                      </div>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-96 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-700/50 shadow-xl rounded-2xl" align="end">
                  <div className="space-y-6 p-2">
                    <div className="font-semibold text-lg text-neutral-900 dark:text-neutral-100">Search Options</div>
                    <div className="grid grid-cols-2 gap-4">
                      {[
                        { key: 'title', label: 'Title' },
                        { key: 'description', label: 'Description' },
                        { key: 'syntax', label: 'Syntax' },
                        { key: 'example', label: 'Code Examples' },
                        { key: 'notes', label: 'Notes' },
                        { key: 'category', label: 'Category' },
                      ].map(({ key, label }) => (
                        <div key={key} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-neutral-50 dark:hover:bg-neutral-800/50 transition-colors group">
                          <Checkbox
                            id={key}
                            checked={searchOptions[key as keyof SearchOptions]}
                            onCheckedChange={(checked) => 
                              onSearchOptionChange(key as keyof SearchOptions, checked as boolean)
                            }
                            className="data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                          />
                          <label
                            htmlFor={key}
                            className="text-sm font-medium leading-none cursor-pointer group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"
                          >
                            {label}
                          </label>
                        </div>
                      ))}
                    </div>
                    
                    <div className="flex gap-2 pt-4 border-t border-neutral-200 dark:border-neutral-700">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setAllSearchOptions(true)}
                        className="flex-1"
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        全选
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setAllSearchOptions(false)}
                        className="flex-1"
                      >
                        清空
                      </Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        {/* 版本分类筛选 */}
        <div className="flex flex-wrap gap-2 justify-center">
          {versionCategories.map(({ category, count, color }) => {
            const isActive = currentSelectedCategory === category;
            const colorStyles = getColorStyles(color, isActive);

            return (
              <Badge
                key={category}
                variant="outline"
                className={`cursor-pointer transition-all duration-300 px-3 py-1 text-sm font-medium rounded-lg border ${colorStyles} ${
                  isActive ? 'shadow-sm hover:shadow-md' : ''
                }`}
                onClick={() => onCategoryChange(category)}
              >
                <span className="mr-2">{category}</span>
                <span className={`text-xs px-1.5 py-0.5 rounded-full ${
                  isActive ? 'bg-gray-200 text-gray-700' : 'bg-gray-100 text-gray-600'
                }`}>
                  {count}
                </span>
              </Badge>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default HeaderSection;
