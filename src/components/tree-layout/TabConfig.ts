// Tab 配置和常量
export const TAB_CONFIG = {
  core: [
    { key: 'basic', label: '基本信息', icon: '📖', description: '核心概念与基础语法' },
    { key: 'business', label: '业务场景', icon: '💼', description: '实际应用场景与案例' },
    { key: 'implementation', label: '实现原理', icon: '🔬', description: '底层机制与架构设计' },
    { key: 'interview', label: '面试题', icon: '🎯', description: '常见面试问题与解答' },
    { key: 'questions', label: '常见问题', icon: '❓', description: '开发中的常见疑问' },
    { key: 'archaeology', label: '知识考古', icon: '🏛️', description: '历史背景与发展脉络' },
    { key: 'performance', label: '性能优化', icon: '⚡', description: '性能提升策略与技巧' },
    { key: 'learning', label: '学习路径', icon: '📚', description: '系统化学习建议' },
    { key: 'debugging', label: '调试技巧', icon: '🐛', description: '问题排查与解决方案' },
    { key: 'essence', label: '本质洞察', icon: '🔮', description: '深层原理与设计思想' }
  ],
  advanced: [
    { key: 'migration', label: '版本迁移', icon: '🔄', description: '版本升级指南' },
    { key: 'ecosystem', label: '生态工具', icon: '🔗', description: '相关工具与库' },
    { key: 'projects', label: '实战项目', icon: '🚀', description: '完整项目案例' },
    { key: 'extensionTabs', label: '扩展内容', icon: '🎨', description: '自定义扩展内容' }
  ]
};

// 推荐的Tab组合
export const RECOMMENDED_TABS = {
  learner: ['basic', 'business', 'learning', 'questions'],
  interviewer: ['basic', 'interview', 'implementation', 'essence'],
  developer: ['basic', 'business', 'implementation', 'performance', 'debugging'],
  all: [
    'basic', 'business', 'implementation', 'interview', 'questions', 
    'archaeology', 'performance', 'learning', 'debugging', 'essence',
    'migration', 'ecosystem', 'projects', 'extensionTabs'
  ]
};

// 主题颜色配置
export const THEME_COLORS = {
  vue: { 
    borderColor: 'border-l-green-500',
    bgColor: 'bg-green-100',
    textColor: 'text-green-700',
    hoverColor: 'hover:bg-green-50'
  },
  react: { 
    borderColor: 'border-l-blue-500',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-700',
    hoverColor: 'hover:bg-blue-50'
  },
  ecma: { 
    borderColor: 'border-l-orange-500',
    bgColor: 'bg-orange-100',
    textColor: 'text-orange-700',
    hoverColor: 'hover:bg-orange-50'
  },
  nextjs: { 
    borderColor: 'border-l-purple-500',
    bgColor: 'bg-purple-100',
    textColor: 'text-purple-700',
    hoverColor: 'hover:bg-purple-50'
  },
  typescript: { 
    borderColor: 'border-l-indigo-500',
    bgColor: 'bg-indigo-100',
    textColor: 'text-indigo-700',
    hoverColor: 'hover:bg-indigo-50'
  },
  default: { 
    borderColor: 'border-l-neutral-500',
    bgColor: 'bg-neutral-100',
    textColor: 'text-neutral-700',
    hoverColor: 'hover:bg-neutral-50'
  }
};

// 框架信息配置
export const FRAMEWORK_CONFIG = [
  { key: 'vue', label: 'Vue.js', version: '3.5', icon: '🟢', description: 'Progressive JS Framework' },
  { key: 'react', label: 'React', version: '18', icon: '🔵', description: 'A JavaScript library' },
  { key: 'ecma', label: 'ECMAScript', version: 'ES2024', icon: '🟡', description: 'Modern JavaScript Features' },
  { key: 'nextjs', label: 'Next.js', version: '14', icon: '⚡', description: 'The React Framework' },
  { key: 'typescript', label: 'TypeScript', version: '5.3', icon: '🔷', description: 'JavaScript with Types' },
  { key: 'default', label: 'General', version: '', icon: '⚪', description: 'Framework agnostic' }
];
