import React from 'react';
import { Star } from "lucide-react";
import { ApiItem } from "@/types/api";
import { TAB_CONFIG } from './TabConfig';

interface TreeNavigationProps {
  filteredData: ApiItem[];
  selectedItem: ApiItem | null;
  favorites: string[];
  themeColor: string;
  searchTerm: string;
  getActiveSearchCount: () => number;
  onItemClick: (item: ApiItem) => void;
  onToggleFavorite: (id: string) => void;
  getVisibleTabs: (item: ApiItem) => string[];
}

const TreeNavigation: React.FC<TreeNavigationProps> = ({
  filteredData,
  selectedItem,
  favorites,
  themeColor,
  searchTerm,
  getActiveSearchCount,
  onItemClick,
  onToggleFavorite,
  getVisibleTabs
}) => {
  const getThemeColors = (theme: string) => {
    switch (theme) {
      case 'vue': return 'bg-green-100 text-green-700';
      case 'react': return 'bg-blue-100 text-blue-700';
      case 'ecma': return 'bg-orange-100 text-orange-700';
      case 'nextjs': return 'bg-purple-100 text-purple-700';
      case 'typescript': return 'bg-indigo-100 text-indigo-700';
      default: return 'bg-neutral-100 text-neutral-700';
    }
  };

  return (
    <div className="w-80 bg-white/90 dark:bg-neutral-900/90 backdrop-blur-xl rounded-2xl border border-neutral-200/50 dark:border-neutral-700/50 shadow-lg overflow-hidden">
      <div className="h-full flex flex-col">
        {/* 导航头部 */}
        <div className="p-4 border-b border-neutral-200/50 dark:border-neutral-700/50">
          <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">API 导航</h3>
          <p className="text-sm text-neutral-500 dark:text-neutral-400 mt-1">
            {filteredData.length} 个 API
          </p>
        </div>
        
        {/* 树状列表 */}
        <div className="flex-1 overflow-y-auto">
          {filteredData.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">没有找到匹配的 API</p>
              {searchTerm && getActiveSearchCount() === 0 && (
                <p className="text-gray-400 text-sm mt-2">请在搜索选项中至少选择一个搜索范围</p>
              )}
            </div>
          ) : (
            <div className="p-2 space-y-1">
              {filteredData.map((item) => (
                <div
                  key={item.id}
                  className={`group cursor-pointer rounded-lg p-3 transition-all duration-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 ${
                    selectedItem?.id === item.id ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700' : ''
                  }`}
                  onClick={() => onItemClick(item)}
                >
                  <div className="flex items-start gap-3">
                    {/* API 图标 */}
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 ${getThemeColors(themeColor)}`}>
                      <span className="text-xs font-bold">
                        {item.title.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    
                    {/* API 信息 */}
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-neutral-900 dark:text-neutral-100 truncate">
                        {item.title}
                      </h4>
                      <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1 line-clamp-2">
                        {item.description}
                      </p>
                      
                      {/* Tab 指示器 */}
                      <div className="flex items-center gap-1 mt-2">
                        {getVisibleTabs(item).slice(0, 3).map(tabKey => {
                          const coreTab = TAB_CONFIG.core.find(t => t.key === tabKey);
                          const advancedTab = TAB_CONFIG.advanced.find(t => t.key === tabKey);
                          const tabConfig = coreTab || advancedTab;
                          
                          return tabConfig ? (
                            <span
                              key={tabKey}
                              className="inline-flex items-center gap-1 px-2 py-0.5 bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-300 text-xs rounded-md"
                              title={tabConfig.description}
                            >
                              {tabConfig.icon}
                              <span className="hidden sm:inline">{tabConfig.label}</span>
                            </span>
                          ) : null;
                        })}
                        {getVisibleTabs(item).length > 3 && (
                          <span className="text-xs text-neutral-400">+{getVisibleTabs(item).length - 3}</span>
                        )}
                      </div>
                    </div>
                    
                    {/* 收藏按钮 */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onToggleFavorite(item.id);
                      }}
                      className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-neutral-200 dark:hover:bg-neutral-700 rounded"
                    >
                      <Star className={`w-4 h-4 ${favorites.includes(item.id) ? 'fill-yellow-400 text-yellow-400' : 'text-neutral-400'}`} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TreeNavigation;
