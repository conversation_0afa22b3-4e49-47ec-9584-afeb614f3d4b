import React from 'react';
import { Star, Copy, ExternalLink, BookOpen } from "lucide-react";
import { ApiItem } from "@/types/api";
import { TAB_CONFIG } from './TabConfig';
import ErrorBoundary from '../ErrorBoundary';

interface ContentPanelProps {
  selectedItem: ApiItem | null;
  favorites: string[];
  activeTab: string;
  onToggleFavorite: (id: string) => void;
  onCopyToClipboard: (text: string) => void;
  onSetActiveTab: (tab: string) => void;
  getVisibleTabs: (item: ApiItem) => string[];
  LazyTabContent: React.ComponentType<any>;
  secondaryTabStates: Record<string, number>;
  setSecondaryTabStates: React.Dispatch<React.SetStateAction<Record<string, number>>>;
}

const ContentPanel: React.FC<ContentPanelProps> = ({
  selectedItem,
  favorites,
  activeTab,
  onToggleFavorite,
  onCopyToClipboard,
  onSetActiveTab,
  getVisibleTabs,
  LazyTabContent,
  secondaryTabStates,
  setSecondaryTabStates
}) => {
  return (
    <div className="flex-1 bg-white/90 dark:bg-neutral-900/90 backdrop-blur-xl rounded-2xl border border-neutral-200/50 dark:border-neutral-700/50 shadow-lg overflow-hidden">
      {selectedItem ? (
        <div className="h-full flex flex-col">
          {/* 内容头部 */}
          <div className="p-6 border-b border-neutral-200/50 dark:border-neutral-700/50">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">
                  {selectedItem.title}
                </h2>
                <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed">
                  {selectedItem.description}
                </p>
              </div>
              
              {/* 操作按钮 */}
              <div className="flex items-center gap-2 ml-4">
                <button
                  onClick={() => onToggleFavorite(selectedItem.id)}
                  className="p-2 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-colors"
                  title={favorites.includes(selectedItem.id) ? '取消收藏' : '添加收藏'}
                >
                  <Star className={`w-5 h-5 ${favorites.includes(selectedItem.id) ? 'fill-yellow-400 text-yellow-400' : 'text-neutral-400'}`} />
                </button>
                
                <button
                  onClick={() => onCopyToClipboard(selectedItem.example || selectedItem.syntax || '')}
                  className="p-2 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-colors"
                  title="复制代码"
                >
                  <Copy className="w-5 h-5 text-neutral-400" />
                </button>
                
                <button
                  onClick={() => {
                    const url = `/full-tab-render/${selectedItem.id}`;
                    window.open(url, '_blank', 'noopener,noreferrer');
                  }}
                  className="p-2 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-colors"
                  title="在新窗口中打开"
                >
                  <ExternalLink className="w-5 h-5 text-neutral-400" />
                </button>
              </div>
            </div>
          </div>
          
          {/* Tab 导航 */}
          <div className="border-b border-neutral-200/50 dark:border-neutral-700/50 px-6">
            <div className="flex gap-0 -mb-px overflow-x-auto">
              {getVisibleTabs(selectedItem).map(tabKey => {
                const coreTab = TAB_CONFIG.core.find(t => t.key === tabKey);
                const advancedTab = TAB_CONFIG.advanced.find(t => t.key === tabKey);
                const tabConfig = coreTab || advancedTab;

                if (!tabConfig) return null;

                return (
                  <button
                    key={tabKey}
                    onClick={() => onSetActiveTab(tabKey)}
                    className={`relative px-4 py-3 text-sm font-medium border-b-2 transition-all duration-200 whitespace-nowrap ${
                      activeTab === tabKey
                        ? 'border-blue-500 text-blue-600 bg-blue-50/50 dark:bg-blue-900/20'
                        : 'border-transparent text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100 hover:border-neutral-300 dark:hover:border-neutral-600'
                    }`}
                    title={tabConfig.description}
                  >
                    <div className="flex items-center gap-2">
                      <span className={`text-sm ${activeTab === tabKey ? 'text-blue-600' : 'text-neutral-500'}`}>
                        {tabConfig.icon}
                      </span>
                      <span>{tabConfig.label}</span>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
          
          {/* Tab 内容 */}
          <div className="flex-1 overflow-y-auto p-6">
            <ErrorBoundary
              onError={(error, errorInfo) => {
                console.group('🚨 Tab渲染错误');
                console.error('API名称:', selectedItem.title);
                console.error('当前Tab:', activeTab);
                console.error('错误:', error);
                console.error('错误信息:', errorInfo);
                console.groupEnd();
              }}
              fallback={
                <div className="text-center py-12">
                  <div className="text-red-500 mb-4">Tab内容渲染错误</div>
                  <button
                    onClick={() => onSetActiveTab('basic')}
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    返回基本信息
                  </button>
                </div>
              }
            >
              <LazyTabContent
                tabKey={activeTab}
                item={selectedItem}
                copyToClipboard={onCopyToClipboard}
                secondaryTabStates={secondaryTabStates}
                setSecondaryTabStates={setSecondaryTabStates}
              />
            </ErrorBoundary>
          </div>
        </div>
      ) : (
        <div className="h-full flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-neutral-100 dark:bg-neutral-800 rounded-2xl flex items-center justify-center mb-4 mx-auto">
              <BookOpen className="w-8 h-8 text-neutral-400" />
            </div>
            <h3 className="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-2">
              选择一个 API
            </h3>
            <p className="text-neutral-500 dark:text-neutral-400">
              从左侧列表中选择一个 API 来查看详细信息
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContentPanel;
