import React, { useState, useMemo } from 'react';
import { Layout, Card, Typography, Empty, Spin } from 'antd';
import { ApiOutlined } from '@ant-design/icons';
import TreeNavigation, { generateTreeData } from './TreeNavigation';
import { SidebarLayoutProps, NavigationState } from '@/types/layout';
import { ApiItem } from '@/types/api';

const { Sider, Content } = Layout;
const { Title, Paragraph } = Typography;

/**
 * 左右分栏布局组件
 * 左侧：树状导航
 * 右侧：内容详情
 */
const SidebarLayout: React.FC<SidebarLayoutProps> = ({
  treeData,
  navigationState,
  onNavigationChange,
  children,
  config = { mode: 'sidebar', sidebarWidth: 320, collapsible: true },
  className = ''
}) => {
  const [collapsed, setCollapsed] = useState(config.defaultCollapsed || false);

  // 渲染右侧内容
  const renderContent = () => {
    const { selectedApiId, selectedTabKey, selectedSubTabKey } = navigationState;
    
    if (!selectedApiId) {
      return (
        <div className="flex items-center justify-center h-full">
          <Empty
            image={<ApiOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
            description={
              <div className="text-center">
                <Title level={4} type="secondary">选择一个API开始探索</Title>
                <Paragraph type="secondary">
                  从左侧导航中选择任意API或Tab来查看详细内容
                </Paragraph>
              </div>
            }
          />
        </div>
      );
    }

    // 找到选中的API
    const selectedApi = findApiInTreeData(treeData, selectedApiId);
    if (!selectedApi) {
      return (
        <div className="flex items-center justify-center h-full">
          <Empty description="未找到选中的API" />
        </div>
      );
    }

    // 如果只选中了API，显示API概览
    if (!selectedTabKey) {
      return renderApiOverview(selectedApi);
    }

    // 渲染具体的Tab内容
    return renderTabContent(selectedApi, selectedTabKey, selectedSubTabKey);
  };

  // 渲染API概览
  const renderApiOverview = (api: ApiItem) => {
    return (
      <div className="p-6 space-y-6">
        <div className="border-b pb-4">
          <Title level={2} className="mb-2 flex items-center gap-3">
            <ApiOutlined className="text-blue-500" />
            {api.title}
          </Title>
          <Paragraph className="text-lg text-gray-600 mb-4">
            {api.description}
          </Paragraph>
          <div className="flex gap-2">
            {api.tags?.map(tag => (
              <span key={tag} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
                {tag}
              </span>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card title="基本信息" size="small">
            <div className="space-y-2">
              <div><strong>分类:</strong> {api.category}</div>
              <div><strong>难度:</strong> {api.difficulty || 'medium'}</div>
              <div><strong>版本:</strong> {api.version || 'N/A'}</div>
            </div>
          </Card>

          <Card title="语法预览" size="small">
            <pre className="text-sm bg-gray-50 p-2 rounded overflow-x-auto">
              {api.syntax}
            </pre>
          </Card>
        </div>

        <Card title="快速示例" size="small">
          <pre className="text-sm bg-gray-50 p-3 rounded overflow-x-auto">
            {api.example}
          </pre>
        </Card>

        {api.notes && (
          <Card title="重要提示" size="small">
            <Paragraph>{api.notes}</Paragraph>
          </Card>
        )}
      </div>
    );
  };

  // 渲染Tab内容
  const renderTabContent = (api: ApiItem, tabKey: string, subTabKey?: string) => {
    // 这里应该渲染具体的Tab内容
    // 由于内容渲染逻辑比较复杂，我们先显示一个占位符
    // 实际实现中，这里会调用CheatSheet中的LazyTabContent组件
    return (
      <div className="p-6">
        <div className="border-b pb-4 mb-6">
          <Title level={3} className="mb-2">
            {api.title} - {getTabLabel(tabKey)}
            {subTabKey && ` - ${subTabKey}`}
          </Title>
        </div>
        
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <Paragraph>
            <strong>内容渲染占位符</strong>
          </Paragraph>
          <Paragraph type="secondary">
            这里将显示 {api.title} 的 {getTabLabel(tabKey)} 内容
            {subTabKey && `，子标签: ${subTabKey}`}
          </Paragraph>
          <Paragraph type="secondary" className="text-xs">
            实际实现中，这里会集成CheatSheet的LazyTabContent组件来渲染具体内容
          </Paragraph>
        </div>
      </div>
    );
  };

  return (
    <Layout className={`sidebar-layout ${className}`} style={{ minHeight: '100vh' }}>
      <Sider
        width={config.sidebarWidth}
        collapsible={config.collapsible}
        collapsed={collapsed}
        onCollapse={setCollapsed}
        theme="light"
        className="border-r border-gray-200"
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        <div className="p-4 border-b border-gray-200">
          {!collapsed && (
            <Title level={4} className="mb-0 text-gray-800">
              API导航
            </Title>
          )}
        </div>
        
        <div className="p-2">
          <TreeNavigation
            treeData={treeData}
            navigationState={navigationState}
            onNavigationChange={onNavigationChange}
          />
        </div>
      </Sider>

      <Layout style={{ marginLeft: collapsed ? 80 : config.sidebarWidth }}>
        <Content className="bg-white">
          {renderContent()}
        </Content>
      </Layout>
    </Layout>
  );
};

// 辅助函数
const findApiInTreeData = (treeData: any[], apiId: string): ApiItem | null => {
  for (const node of treeData) {
    if (node.key === apiId && node.data?.api) {
      return node.data.api;
    }
  }
  return null;
};

const getTabLabel = (tabKey: string): string => {
  const tabLabels: Record<string, string> = {
    basic: '基本信息',
    business: '使用示例',
    implementation: '原理解析',
    interview: '面试准备',
    questions: '常见问题',
    archaeology: '知识考古',
    performance: '性能优化',
    debugging: '调试技巧',
    essence: '本质洞察'
  };
  return tabLabels[tabKey] || tabKey;
};

export default SidebarLayout;
