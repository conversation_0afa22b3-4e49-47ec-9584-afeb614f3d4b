import React, { useState, useEffect } from 'react';
import { Button, Card, Progress, Alert, Collapse, Tag, Space, Spin } from 'antd';
import { PlayCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { allReactApis } from '@/data/react';

interface TabTestResult {
  api: string;
  tab: string;
  status: 'pending' | 'pass' | 'fail' | 'warning';
  error?: string;
  warnings?: string[];
  renderTime?: number;
}

const TABS = [
  { key: 'basic', label: '基本信息', component: 'BasicInfoComponent' },
  { key: 'business', label: '业务场景', component: 'BusinessScenariosComponent' },
  { key: 'implementation', label: '原理解析', component: 'ImplementationComponent' },
  { key: 'interview', label: '面试准备', component: 'InterviewQuestionsComponent' },
  { key: 'common', label: '常见问题', component: 'CommonQuestionsComponent' },
  { key: 'archaeology', label: '知识考古', component: 'KnowledgeArchaeologyComponent' },
  { key: 'performance', label: '性能优化', component: 'PerformanceOptimizationComponent' },
  { key: 'debugging', label: '调试技巧', component: 'DebuggingTipsComponent' },
  { key: 'essence', label: '本质洞察', component: 'EssenceInsightsComponent' }
];

export const TabTester: React.FC = () => {
  const [testResults, setTestResults] = useState<TabTestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');
  const [progress, setProgress] = useState(0);

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    setProgress(0);

    const totalTests = allReactApis.length * TABS.length;
    let completedTests = 0;

    for (const api of allReactApis) {
      setCurrentTest(`测试 ${api.title}...`);
      
      for (const tab of TABS) {
        const testResult = await testTabRendering(api, tab.key);
        
        setTestResults(prev => [...prev, testResult]);
        
        completedTests++;
        setProgress((completedTests / totalTests) * 100);
        
        // 短暂延迟，避免阻塞UI
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }

    setIsRunning(false);
    setCurrentTest('测试完成');
  };

  const testTabRendering = async (api: any, tabKey: string): Promise<TabTestResult> => {
    const startTime = performance.now();
    
    try {
      const result = await simulateTabRender(api, tabKey);
      const renderTime = performance.now() - startTime;
      
      return {
        api: api.id,
        tab: tabKey,
        status: result.status,
        error: result.error,
        warnings: result.warnings,
        renderTime
      };
    } catch (error) {
      return {
        api: api.id,
        tab: tabKey,
        status: 'fail',
        error: error instanceof Error ? error.message : '未知错误',
        renderTime: performance.now() - startTime
      };
    }
  };

  const simulateTabRender = async (api: any, tabKey: string) => {
    const warnings: string[] = [];
    
    try {
      // 检查数据完整性
      const tabData = getTabData(api, tabKey);
      
      if (!tabData) {
        return { status: 'fail' as const, error: `${tabKey} 数据不存在` };
      }

      // 检查数据类型
      if (typeof tabData === 'string') {
        if (tabData.includes('TODO') || tabData.includes('{{')) {
          warnings.push('包含占位符内容');
        }
      } else if (Array.isArray(tabData)) {
        if (tabData.length === 0) {
          warnings.push('数组为空');
        } else {
          tabData.forEach((item, index) => {
            const itemStr = JSON.stringify(item);
            if (itemStr.includes('TODO') || itemStr.includes('{{')) {
              warnings.push(`项目${index + 1}包含占位符`);
            }
          });
        }
      } else if (typeof tabData === 'object') {
        const dataStr = JSON.stringify(tabData);
        if (dataStr.includes('TODO') || dataStr.includes('{{')) {
          warnings.push('对象包含占位符');
        }

        // 特殊检查：基本信息的Mermaid图表
        if (tabKey === 'basic' && tabData.scenarioDiagram) {
          if (Array.isArray(tabData.scenarioDiagram)) {
            // 新的多图表结构
            tabData.scenarioDiagram.forEach((diagram: any, index: number) => {
              if (!diagram.diagram || !diagram.title) {
                warnings.push(`图表${index + 1}缺少必要字段`);
              }
              if (diagram.diagram && !diagram.diagram.includes('graph') && !diagram.diagram.includes('flowchart')) {
                warnings.push(`图表${index + 1}可能缺少Mermaid语法`);
              }
            });
          } else if (typeof tabData.scenarioDiagram === 'string') {
            warnings.push('使用旧的单图表结构');
          }
        }

        // 特殊检查：调试技巧的subTabs结构
        if (tabKey === 'debugging' && tabData) {
          if (!tabData.subTabs) {
            warnings.push('缺少subTabs结构');
          } else if (tabData.subTabs.length === 0) {
            warnings.push('subTabs为空');
          }
        }
      }

      // 模拟渲染时间
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 20));

      return {
        status: warnings.length > 0 ? 'warning' as const : 'pass' as const,
        warnings
      };
    } catch (error) {
      return {
        status: 'fail' as const,
        error: error instanceof Error ? error.message : '渲染失败'
      };
    }
  };

  const getTabData = (api: any, tabKey: string) => {
    switch (tabKey) {
      case 'basic': return api.basicInfo;
      case 'business': return api.businessScenarios;
      case 'implementation': return api.implementation;
      case 'interview': return api.interviewQuestions;
      case 'common': return api.commonQuestions;
      case 'archaeology': return api.knowledgeArchaeology;
      case 'performance': return api.performanceOptimization;
      case 'debugging': return api.debuggingTips;
      case 'essence': return api.essenceInsights;
      default: return null;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'fail': return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'warning': return <WarningOutlined style={{ color: '#faad14' }} />;
      default: return <Spin size="small" />;
    }
  };

  const groupedResults = testResults.reduce((acc, result) => {
    if (!acc[result.api]) {
      acc[result.api] = [];
    }
    acc[result.api].push(result);
    return acc;
  }, {} as Record<string, TabTestResult[]>);

  const overallSummary = testResults.reduce(
    (acc, result) => {
      acc.total++;
      if (result.status === 'pass') acc.passed++;
      else if (result.status === 'fail') acc.failed++;
      else if (result.status === 'warning') acc.warnings++;
      return acc;
    },
    { total: 0, passed: 0, failed: 0, warnings: 0 }
  );

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">🧪 Tab渲染实时测试</h1>
        <p className="text-gray-600 mb-4">
          在浏览器中实时测试所有API的每个Tab是否能正常渲染，检测数据结构问题。
        </p>
        
        <Space>
          <Button 
            type="primary" 
            icon={<PlayCircleOutlined />}
            onClick={runAllTests}
            loading={isRunning}
            size="large"
          >
            {isRunning ? '测试中...' : '开始测试'}
          </Button>
          
          {testResults.length > 0 && (
            <div className="flex items-center gap-4">
              <span>总计: {overallSummary.total}</span>
              <Tag color="success">通过: {overallSummary.passed}</Tag>
              <Tag color="error">失败: {overallSummary.failed}</Tag>
              <Tag color="warning">警告: {overallSummary.warnings}</Tag>
              {overallSummary.total > 0 && (
                <span>成功率: {((overallSummary.passed / overallSummary.total) * 100).toFixed(1)}%</span>
              )}
            </div>
          )}
        </Space>
      </div>

      {isRunning && (
        <Card className="mb-6">
          <div className="text-center">
            <p className="mb-2">{currentTest}</p>
            <Progress percent={Math.round(progress)} status="active" />
          </div>
        </Card>
      )}

      {Object.keys(groupedResults).length > 0 && (
        <Collapse>
          {Object.entries(groupedResults).map(([apiId, results]) => {
            const apiSummary = results.reduce(
              (acc, result) => {
                acc.total++;
                if (result.status === 'pass') acc.passed++;
                else if (result.status === 'fail') acc.failed++;
                else if (result.status === 'warning') acc.warnings++;
                return acc;
              },
              { total: 0, passed: 0, failed: 0, warnings: 0 }
            );

            return (
              <Collapse.Panel
                key={apiId}
                header={
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{apiId}</span>
                    <Space>
                      <Tag color="success">{apiSummary.passed}</Tag>
                      <Tag color="error">{apiSummary.failed}</Tag>
                      <Tag color="warning">{apiSummary.warnings}</Tag>
                    </Space>
                  </div>
                }
              >
                <div className="space-y-2">
                  {results.map(result => (
                    <div key={result.tab} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(result.status)}
                        <span>{TABS.find(t => t.key === result.tab)?.label || result.tab}</span>
                        {result.renderTime && (
                          <span className="text-xs text-gray-500">({result.renderTime.toFixed(0)}ms)</span>
                        )}
                      </div>
                      
                      <div className="text-right">
                        {result.error && (
                          <div className="text-red-600 text-sm">{result.error}</div>
                        )}
                        {result.warnings && result.warnings.length > 0 && (
                          <div className="text-orange-600 text-xs">
                            {result.warnings.join(', ')}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </Collapse.Panel>
            );
          })}
        </Collapse>
      )}
    </div>
  );
};
