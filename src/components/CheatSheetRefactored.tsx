import React, { useState, useMemo, useCallback } from 'react';
import { Layout, Drawer, Tabs, Typography, message } from 'antd';
import { ApiItem } from '@/types/api';
import { LayoutMode, NavigationState } from '@/types/layout';

// 导入拆分的组件
import LayoutSwitcher from './LayoutSwitcher';
import SidebarLayout from './SidebarLayout';
import { generateTreeData } from './TreeNavigation';
import SearchAndFilters from './cheatsheet/SearchAndFilters';
import ApiCard from './cheatsheet/ApiCard';
import BasicInfoTab from './cheatsheet/BasicInfoTab';
import BusinessScenariosTab from './cheatsheet/BusinessScenariosTab';

// 导入hooks
import { useCheatSheetFilters } from '@/hooks/useCheatSheetFilters';

const { Content } = Layout;
const { Title } = Typography;

interface CheatSheetRefactoredProps {
  title: string;
  subtitle: string;
  apiData: ApiItem[];
  themeColor?: 'vue' | 'react' | 'nextjs' | 'typescript' | 'ecma' | 'default';
  onThemeChange?: (theme: 'vue' | 'react' | 'nextjs' | 'typescript' | 'ecma' | 'default') => void;
}

/**
 * 重构后的CheatSheet组件
 * 支持瀑布流和侧边栏两种布局模式
 */
const CheatSheetRefactored: React.FC<CheatSheetRefactoredProps> = ({
  title,
  subtitle,
  apiData,
  themeColor = 'default',
  onThemeChange
}) => {
  // 布局状态
  const [layoutMode, setLayoutMode] = useState<LayoutMode>(LayoutMode.WATERFALL);
  const [navigationState, setNavigationState] = useState<NavigationState>({
    selectedApiId: undefined,
    selectedTabKey: undefined,
    selectedSubTabKey: undefined,
    expandedKeys: []
  });

  // 抽屉状态（瀑布流模式使用）
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<ApiItem | null>(null);
  const [activeTab, setActiveTab] = useState('basic');

  // 收藏状态
  const [favorites, setFavorites] = useState<string[]>(() => {
    try {
      const saved = localStorage.getItem('react-cheatsheet-favorites');
      return saved ? JSON.parse(saved) : [];
    } catch {
      return [];
    }
  });

  // 使用过滤hooks
  const {
    searchTerm,
    searchOptions,
    selectedVersions,
    selectedDifficulties,
    selectedCategories,
    sortBy,
    sortOrder,
    showFavoritesOnly,
    filteredData,
    categories,
    difficulties,
    categoryList,
    setSearchTerm,
    setSelectedVersions,
    setSelectedDifficulties,
    setSelectedCategories,
    setSortBy,
    setSortOrder,
    setShowFavoritesOnly,
    handleSearchOptionChange,
    getActiveSearchCount,
    setAllSearchOptions,
    resetFilters
  } = useCheatSheetFilters({ apiData, favorites, themeColor });

  // 生成树状数据
  const treeData = useMemo(() => generateTreeData(filteredData), [filteredData]);

  // 收藏操作
  const toggleFavorite = useCallback((id: string) => {
    setFavorites(prev => {
      const newFavorites = prev.includes(id)
        ? prev.filter(fav => fav !== id)
        : [...prev, id];
      
      try {
        localStorage.setItem('react-cheatsheet-favorites', JSON.stringify(newFavorites));
      } catch (error) {
        console.error('Failed to save favorites:', error);
      }
      
      return newFavorites;
    });
  }, []);

  // 复制到剪贴板
  const copyToClipboard = useCallback((text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  }, []);

  // 卡片点击处理（瀑布流模式）
  const handleCardClick = useCallback((item: ApiItem) => {
    setSelectedItem(item);
    setDrawerVisible(true);
    setActiveTab('basic');
  }, []);

  // 抽屉关闭处理
  const handleDrawerClose = useCallback(() => {
    setDrawerVisible(false);
    setSelectedItem(null);
  }, []);

  // 导航状态变化处理（侧边栏模式）
  const handleNavigationChange = useCallback((newState: NavigationState) => {
    setNavigationState(newState);
  }, []);

  // 渲染Tab内容
  const renderTabContent = useCallback((tabKey: string, item: ApiItem) => {
    switch (tabKey) {
      case 'basic':
        return <BasicInfoTab item={item} copyToClipboard={copyToClipboard} />;
      case 'business':
        return <BusinessScenariosTab item={item} copyToClipboard={copyToClipboard} />;
      // TODO: 添加其他Tab组件
      default:
        return <div className="p-6">该Tab内容正在开发中...</div>;
    }
  }, [copyToClipboard]);

  // 渲染瀑布流布局
  const renderWaterfallLayout = () => (
    <Layout>
      <Content className="p-6">
        {/* 标题区域 */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <Title level={1} className="mb-2">{title}</Title>
              <p className="text-gray-600 text-lg">{subtitle}</p>
            </div>
            <LayoutSwitcher
              currentMode={layoutMode}
              onModeChange={setLayoutMode}
            />
          </div>
        </div>

        {/* 搜索和过滤 */}
        <SearchAndFilters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          searchOptions={searchOptions}
          onSearchOptionChange={handleSearchOptionChange}
          getActiveSearchCount={getActiveSearchCount}
          setAllSearchOptions={setAllSearchOptions}
          selectedVersions={selectedVersions}
          onVersionsChange={setSelectedVersions}
          selectedDifficulties={selectedDifficulties}
          onDifficultiesChange={setSelectedDifficulties}
          selectedCategories={selectedCategories}
          onCategoriesChange={setSelectedCategories}
          sortBy={sortBy}
          onSortByChange={setSortBy}
          sortOrder={sortOrder}
          onSortOrderChange={setSortOrder}
          showFavoritesOnly={showFavoritesOnly}
          onShowFavoritesOnlyChange={setShowFavoritesOnly}
          categories={categories}
          difficulties={difficulties}
          categoryList={categoryList}
          onResetFilters={resetFilters}
          totalCount={apiData.length}
          filteredCount={filteredData.length}
        />

        {/* API卡片网格 */}
        <div className="columns-1 sm:columns-2 lg:columns-3 xl:columns-4 2xl:columns-5 gap-6">
          {filteredData.map(item => (
            <div key={item.id} className="break-inside-avoid mb-6">
              <ApiCard
                item={item}
                isFavorite={favorites.includes(item.id)}
                onCardClick={handleCardClick}
                onToggleFavorite={toggleFavorite}
                onCopyToClipboard={copyToClipboard}
                themeColor={themeColor}
              />
            </div>
          ))}
        </div>

        {/* 详情抽屉 */}
        <Drawer
          title={selectedItem?.title}
          placement="right"
          size="large"
          onClose={handleDrawerClose}
          open={drawerVisible}
          className="api-detail-drawer"
        >
          {selectedItem && (
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={[
                {
                  key: 'basic',
                  label: '基本信息',
                  children: renderTabContent('basic', selectedItem)
                },
                {
                  key: 'business',
                  label: '使用示例',
                  children: renderTabContent('business', selectedItem)
                }
                // TODO: 添加更多Tab
              ]}
            />
          )}
        </Drawer>
      </Content>
    </Layout>
  );

  // 渲染侧边栏布局
  const renderSidebarLayout = () => (
    <div className="sidebar-layout-container">
      {/* 顶部标题栏 */}
      <div className="flex items-center justify-between mb-4 p-6 bg-white border-b">
        <div>
          <Title level={1} className="mb-2">{title}</Title>
          <p className="text-gray-600 text-lg">{subtitle}</p>
        </div>
        <LayoutSwitcher
          currentMode={layoutMode}
          onModeChange={setLayoutMode}
        />
      </div>

      {/* 侧边栏布局 */}
      <SidebarLayout
        treeData={treeData}
        navigationState={navigationState}
        onNavigationChange={handleNavigationChange}
        config={{
          mode: LayoutMode.SIDEBAR,
          sidebarWidth: 320,
          collapsible: true
        }}
      >
        {/* 右侧内容区域 - 这里会被SidebarLayout内部处理 */}
        <div></div>
      </SidebarLayout>
    </div>
  );

  return (
    <div className="cheat-sheet-refactored">
      {layoutMode === LayoutMode.WATERFALL ? renderWaterfallLayout() : renderSidebarLayout()}
    </div>
  );
};

export default CheatSheetRefactored;
