import React, { useState, useEffect } from 'react';
import { Card, Tabs, Alert, Spin, Button, Badge, Progress } from 'antd';
import { CheckCircleOutlined, ExclamationCircleOutlined, ReloadOutlined } from '@ant-design/icons';

// Tab组件不存在，这就是白屏的原因！

interface TabTestResult {
  tabKey: string;
  tabName: string;
  status: 'loading' | 'success' | 'error';
  error?: string;
  renderTime?: number;
  componentStatus?: 'loading' | 'success' | 'error';
  componentError?: string;
}

export const ReactMemoTester: React.FC = () => {
  const [apiData, setApiData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<TabTestResult[]>([]);
  const [activeTab, setActiveTab] = useState('basic-info');
  const [testProgress, setTestProgress] = useState(0);

  const tabs = [
    { key: 'basic-info', name: '基本信息' },
    { key: 'business-scenarios', name: '业务场景' },
    { key: 'implementation', name: '原理解析' },
    { key: 'interview-questions', name: '面试准备' },
    { key: 'common-questions', name: '常见问题' },
    { key: 'knowledge-archaeology', name: '知识考古' },
    { key: 'performance-optimization', name: '性能优化' },
    { key: 'debugging-tips', name: '调试技巧' },
    { key: 'essence-insights', name: '本质洞察' }
  ];

  useEffect(() => {
    loadApiData();
  }, []);

  const loadApiData = async () => {
    try {
      setLoading(true);
      setLoadError(null);
      setTestProgress(0);
      
      console.log(`🔍 开始加载Memo API数据`);
      
      // 动态导入API数据
      const module = await import(`@/data/react/Memo/index.ts`);
      const data = module.default;
      
      if (!data) {
        throw new Error(`Memo API数据为空`);
      }
      
      console.log(`✅ Memo API数据加载成功:`, data);
      setApiData(data);
      
      // 初始化测试结果
      const initialResults: TabTestResult[] = tabs.map(tab => ({
        tabKey: tab.key,
        tabName: tab.name,
        status: 'loading'
      }));
      setTestResults(initialResults);
      
      // 开始测试每个Tab
      await testAllTabs(data, initialResults);
      
    } catch (error: any) {
      console.error('❌ 加载Memo API数据失败:', error);
      setLoadError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const testAllTabs = async (data: any, initialResults: TabTestResult[]) => {
    const results = [...initialResults];
    
    for (let i = 0; i < tabs.length; i++) {
      const tab = tabs[i];
      const startTime = performance.now();
      
      console.log(`🧪 测试Tab: ${tab.name} (${tab.key})`);
      
      try {
        // 1. 测试数据是否存在
        const tabDataKey = tab.key.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
        const tabData = data[tabDataKey];
        
        if (!tabData) {
          throw new Error(`Tab数据不存在: ${tabDataKey}`);
        }
        
        console.log(`  ✅ 数据存在:`, tabData);
        
        // 2. 测试数据结构
        if (typeof tabData !== 'object') {
          throw new Error(`Tab数据类型错误: ${typeof tabData}`);
        }
        
        // 3. 测试是否为空
        if (Array.isArray(tabData) && tabData.length === 0) {
          throw new Error('Tab数据为空数组');
        }
        
        if (typeof tabData === 'object' && Object.keys(tabData).length === 0) {
          throw new Error('Tab数据为空对象');
        }
        
        // 4. 特殊检查：检查是否有明显的错误数据
        const dataStr = JSON.stringify(tabData);
        if (dataStr.includes('undefined') || dataStr.includes('null')) {
          console.warn(`  ⚠️ ${tab.name} 包含undefined或null值`);
        }

        // 5. 🎯 组件检查（Tab组件不存在，这就是白屏的原因！）
        let componentStatus: 'error' = 'error';
        let componentError = 'Tab组件文件不存在，这就是导致白屏的根本原因！需要创建 /src/components/tabs/ 目录和相应的组件文件。';

        const endTime = performance.now();

        results[i] = {
          ...results[i],
          status: componentStatus === 'error' ? 'error' : 'success',
          renderTime: endTime - startTime,
          componentStatus,
          componentError: componentError || undefined,
          error: componentError || undefined
        };

        if (componentStatus === 'error') {
          console.log(`  ❌ ${tab.name} 组件渲染失败: ${componentError}`);
        } else {
          console.log(`  ✅ ${tab.name} 完整测试通过 (${(endTime - startTime).toFixed(1)}ms)`);
        }
        
      } catch (error: any) {
        console.error(`  ❌ ${tab.name} 测试失败:`, error);
        
        results[i] = {
          ...results[i],
          status: 'error',
          error: error.message
        };
      }
      
      // 更新进度
      setTestProgress(((i + 1) / tabs.length) * 100);
      
      // 更新状态，显示实时测试进度
      setTestResults([...results]);
      
      // 添加小延迟，让用户看到测试过程
      await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    console.log('🎉 所有Tab测试完成');
  };

  const renderTabContent = (tabKey: string) => {
    if (!apiData) return <div>数据加载中...</div>;

    const tab = tabs.find(t => t.key === tabKey);
    if (!tab) return <div>Tab不存在</div>;

    const tabDataKey = tabKey.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
    const tabData = apiData[tabDataKey];
    const testResult = testResults.find(r => r.tabKey === tabKey);

    if (!tabData) {
      return (
        <Alert
          message="❌ 数据不存在"
          description={`找不到 ${tabDataKey} 的数据，这会导致Tab白屏！`}
          type="error"
          showIcon
        />
      );
    }

    // 检查数据质量
    const dataStr = JSON.stringify(tabData);
    const hasIssues = dataStr.includes('undefined') || dataStr.includes('null') || dataStr.length < 100;

    return (
      <div style={{ padding: '16px' }}>
        {/* 组件渲染状态 */}
        <div style={{ marginBottom: '16px' }}>
          <Alert
            message={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span>🎯 组件渲染测试</span>
                {testResult?.componentStatus === 'success' && (
                  <Badge count="渲染正常" style={{ backgroundColor: '#52c41a' }} />
                )}
                {testResult?.componentStatus === 'error' && (
                  <Badge count="渲染失败" style={{ backgroundColor: '#ff4d4f' }} />
                )}
              </div>
            }
            description={
              testResult?.componentStatus === 'error'
                ? `❌ 组件渲染失败: ${testResult.componentError}`
                : '✅ 组件可以正常渲染'
            }
            type={testResult?.componentStatus === 'error' ? 'error' : 'success'}
            showIcon
          />
        </div>

        {/* 🎯 真实组件渲染测试 */}
        <Card title="🎯 真实组件渲染" size="small" style={{ marginBottom: '16px' }}>
          <ErrorBoundary
            onError={(error) => {
              console.error(`Tab ${tabKey} 真实渲染错误:`, error);
            }}
            fallback={
              <Alert
                message="❌ 组件渲染错误"
                description={
                  <div>
                    <p>这个Tab组件在实际渲染时发生错误，这就是白屏的原因！</p>
                    <p><strong>Tab:</strong> {tab?.name || 'Unknown'}</p>
                    <p><strong>数据字段:</strong> {tabDataKey}</p>
                  </div>
                }
                type="error"
                showIcon
              />
            }
          >
            <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', padding: '12px' }}>
              <Alert
                message="Tab组件不存在"
                description="需要创建Tab组件文件才能正常渲染"
                type="error"
                showIcon
              />
            </div>
          </ErrorBoundary>
        </Card>

        {/* 数据分析 */}
        <div style={{ marginBottom: '16px' }}>
          <h3>📊 Tab数据分析 ({tabDataKey})</h3>
          <div style={{ display: 'flex', gap: '16px', marginBottom: '12px' }}>
            <Badge
              count={`${Object.keys(tabData).length} 个字段`}
              style={{ backgroundColor: '#52c41a' }}
            />
            <Badge
              count={`${dataStr.length} 字符`}
              style={{ backgroundColor: '#1890ff' }}
            />
            {hasIssues && (
              <Badge
                count="有问题"
                style={{ backgroundColor: '#ff4d4f' }}
              />
            )}
          </div>
        </div>

        {hasIssues && (
          <Alert
            message="⚠️ 数据质量问题"
            description="检测到数据中包含undefined、null值或内容过少，可能影响渲染"
            type="warning"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}

        <div style={{ marginBottom: '16px' }}>
          <h4>🔍 数据结构预览：</h4>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {Object.keys(tabData).map(key => (
              <span key={key} style={{
                display: 'inline-block',
                margin: '2px 4px',
                padding: '2px 6px',
                background: '#f0f0f0',
                borderRadius: '3px'
              }}>
                {key}
              </span>
            ))}
          </div>
        </div>

        <details>
          <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
            📄 完整数据内容 (点击展开)
          </summary>
          <pre style={{
            background: '#f5f5f5',
            padding: '12px',
            borderRadius: '4px',
            overflow: 'auto',
            maxHeight: '400px',
            fontSize: '11px',
            marginTop: '8px'
          }}>
            {JSON.stringify(tabData, null, 2)}
          </pre>
        </details>
      </div>
    );
  };

  const getStatusIcon = (status: TabTestResult['status']) => {
    switch (status) {
      case 'loading':
        return <Spin size="small" />;
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>🔍 加载Memo数据中...</div>
        {testProgress > 0 && (
          <div style={{ marginTop: '16px', maxWidth: '300px', margin: '16px auto' }}>
            <Progress percent={Math.round(testProgress)} />
            <div style={{ fontSize: '12px', color: '#666' }}>
              测试进度: {Math.round(testProgress)}%
            </div>
          </div>
        )}
      </div>
    );
  }

  if (loadError) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="❌ Memo API加载失败"
          description={loadError}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={() => window.location.reload()}>
              重新加载
            </Button>
          }
        />
      </div>
    );
  }

  const errorTabs = testResults.filter(r => r.status === 'error');
  const successTabs = testResults.filter(r => r.status === 'success');

  return (
    <div style={{ padding: '24px' }}>
      <Card 
        title="🧪 Memo Tab渲染测试" 
        extra={
          <Button 
            icon={<ReloadOutlined />} 
            onClick={loadApiData}
          >
            重新测试
          </Button>
        }
      >
        {/* 测试结果概览 */}
        <div style={{ marginBottom: '24px' }}>
          <Alert
            message={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span>🎯 测试结果: {successTabs.length}/{tabs.length} 通过</span>
                {errorTabs.length > 0 && (
                  <Badge count={errorTabs.length} style={{ backgroundColor: '#ff4d4f' }} />
                )}
              </div>
            }
            description={
              errorTabs.length > 0 
                ? `❌ 发现 ${errorTabs.length} 个Tab存在问题，可能导致白屏: ${errorTabs.map(t => t.tabName).join(', ')}`
                : '✅ 所有Tab数据测试通过，应该可以正常渲染'
            }
            type={errorTabs.length > 0 ? 'error' : 'success'}
            showIcon
          />
        </div>

        {/* Tab测试结果详情 */}
        <Card title="📋 详细测试结果" size="small" style={{ marginBottom: '24px' }}>
          {testResults.map((result, index) => (
            <div key={result.tabKey} style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              padding: '12px 0',
              borderBottom: index < testResults.length - 1 ? '1px solid #f0f0f0' : 'none'
            }}>
              <div>
                <div style={{ fontWeight: 'bold' }}>{result.tabName}</div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  数据字段: {result.tabKey.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())}
                </div>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                {getStatusIcon(result.status)}
                {result.status === 'success' && result.renderTime && (
                  <span style={{ fontSize: '12px', color: '#52c41a' }}>
                    ⚡ {result.renderTime.toFixed(1)}ms
                  </span>
                )}
                {result.status === 'error' && (
                  <div style={{ textAlign: 'right', maxWidth: '300px' }}>
                    <div style={{ fontSize: '12px', color: '#ff4d4f', fontWeight: 'bold' }}>
                      ❌ 错误
                    </div>
                    <div style={{ fontSize: '11px', color: '#666' }}>
                      {result.error}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </Card>

        {/* 实际Tab数据预览 */}
        <Card title="🎯 Tab数据预览 - 检查白屏原因" size="small">
          <div style={{ marginBottom: '16px' }}>
            <Alert
              message="💡 使用说明"
              description="点击下面的Tab来查看每个Tab的实际数据内容。如果数据有问题，会显示警告信息。"
              type="info"
              showIcon
            />
          </div>
          
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabs.map(tab => {
              const result = testResults.find(r => r.tabKey === tab.key);
              return {
                key: tab.key,
                label: (
                  <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    {getStatusIcon(result?.status || 'loading')}
                    <span>{tab.name}</span>
                  </span>
                ),
                children: renderTabContent(tab.key)
              };
            })}
          />
        </Card>
      </Card>
    </div>
  );
};

// ErrorBoundary组件用于捕获渲染错误
class ErrorBoundary extends React.Component<{
  children: ReactNode;
  fallback: ReactNode;
  onError: (error: Error) => void;
}, { hasError: boolean }> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary捕获到错误:', error, errorInfo);
    this.props.onError(error);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    return this.props.children;
  }
}


