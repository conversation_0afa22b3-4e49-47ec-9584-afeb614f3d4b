import React from 'react';
import { Button, Tooltip } from 'antd';
import { AppstoreOutlined, MenuOutlined } from '@ant-design/icons';
import { LayoutMode, LayoutSwitcherProps } from '@/types/layout';

/**
 * 布局切换器组件
 * 支持在瀑布流和左右分栏布局之间切换
 */
const LayoutSwitcher: React.FC<LayoutSwitcherProps> = ({
  currentMode,
  onModeChange,
  className = ''
}) => {
  const handleModeChange = (mode: LayoutMode) => {
    if (mode !== currentMode) {
      onModeChange(mode);
    }
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-sm text-gray-600 mr-2">布局模式:</span>
      
      <Button.Group>
        <Tooltip title="瀑布流布局 - 卡片式展示">
          <Button
            type={currentMode === LayoutMode.WATERFALL ? 'primary' : 'default'}
            icon={<AppstoreOutlined />}
            onClick={() => handleModeChange(LayoutMode.WATERFALL)}
            className="flex items-center gap-1"
          >
            瀑布流
          </Button>
        </Tooltip>
        
        <Tooltip title="树状导航布局 - 左侧导航，右侧详情">
          <Button
            type={currentMode === LayoutMode.SIDEBAR ? 'primary' : 'default'}
            icon={<MenuOutlined />}
            onClick={() => handleModeChange(LayoutMode.SIDEBAR)}
            className="flex items-center gap-1"
          >
            树状导航
          </Button>
        </Tooltip>
      </Button.Group>
    </div>
  );
};

export default LayoutSwitcher;
