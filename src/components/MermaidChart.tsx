import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import mermaid from 'mermaid';
import { Maximize2, X, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';

interface MermaidChartProps {
  chart: string;
  id?: string;
  title?: string;
}

const MermaidChart: React.FC<MermaidChartProps> = ({ chart, id, title }) => {
  const ref = useRef<HTMLDivElement>(null);
  const fullscreenRef = useRef<HTMLDivElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  // 清理和验证图表内容函数移到组件外部以便复用
  const cleanChart = (chartContent: string): string => {
    // 如果内容包含```mermaid代码块，提取第一个代码块
    const mermaidBlockMatch = chartContent.match(/```mermaid\s*\n?([\s\S]*?)\n?```/);
    if (mermaidBlockMatch) {
      return mermaidBlockMatch[1].trim();
    }

    // 如果没有代码块标记，直接处理内容
    let cleaned = chartContent.trim();

    // 移除可能的markdown标题和其他非Mermaid内容
    const lines = cleaned.split('\n');
    const mermaidStartIndex = lines.findIndex(line =>
      line.trim().match(/^(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|gitgraph|pie|journey|gantt|erDiagram|C4Context)/)
    );

    if (mermaidStartIndex >= 0) {
      // 从Mermaid图表类型声明开始提取
      cleaned = lines.slice(mermaidStartIndex).join('\n').trim();
    }

    // 确保图表有正确的类型声明
    if (!cleaned.match(/^(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|gitgraph|pie|journey|gantt|erDiagram|C4Context)/)) {
      // 如果没有类型声明，默认为flowchart
      cleaned = `flowchart TD\n${cleaned}`;
    }

    return cleaned;
  };

  useEffect(() => {
    // 初始化 mermaid
    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
      themeVariables: {
        primaryColor: '#3b82f6',
        primaryTextColor: '#374151',
        primaryBorderColor: '#d1d5db',
        lineColor: '#6b7280',
        sectionBkgColor: '#f3f4f6',
        altSectionBkgColor: '#ffffff',
        gridColor: '#e5e7eb',
        secondaryColor: '#10b981',
        tertiaryColor: '#f59e0b'
      },
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'basis'
      }
    });

    // 渲染图表
    const renderChart = (container: HTMLDivElement) => {
      if (container && chart) {
        const chartId = id || `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const cleanedChart = cleanChart(chart);
        
        try {
          // 清空之前的内容
          container.innerHTML = '';
          
          // 验证语法
          if (!cleanedChart || cleanedChart.length < 10) {
            throw new Error('图表内容过短或为空');
          }
          
          // 渲染新的图表
          mermaid.render(chartId, cleanedChart).then(({ svg }) => {
            if (container) {
              container.innerHTML = svg;
              
              // 添加响应式样式
              const svgElement = container.querySelector('svg');
              if (svgElement) {
                svgElement.style.maxWidth = '100%';
                svgElement.style.height = 'auto';
              }
            }
          }).catch((error) => {
            console.error('Mermaid rendering error:', error);
            if (container) {
              container.innerHTML = `
                <div style="padding: 16px; background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; color: #dc2626;">
                  <p><strong>图表渲染失败:</strong></p>
                  <p style="margin: 8px 0; font-size: 14px;">${error.message || '未知错误'}</p>
                  <details style="margin-top: 12px;">
                    <summary style="cursor: pointer; font-weight: 500;">查看原始内容</summary>
                    <pre style="margin-top: 8px; font-size: 12px; background: #fff; padding: 8px; border-radius: 4px; overflow: auto; white-space: pre-wrap;">${cleanedChart}</pre>
                  </details>
                </div>
              `;
            }
          });
        } catch (error) {
          console.error('Mermaid error:', error);
          if (container) {
            container.innerHTML = `
              <div style="padding: 16px; background: #f9fafb; border: 1px solid #d1d5db; border-radius: 8px;">
                <p style="margin: 0; color: #6b7280; font-size: 14px;">
                  <strong>无法渲染Mermaid图表</strong><br/>
                  ${error instanceof Error ? error.message : '请检查图表语法'}
                </p>
              </div>
            `;
          }
        }
      }
    };

    // 渲染普通视图
    if (ref.current) {
      renderChart(ref.current);
    }
  }, [chart, id]);

  // 处理全屏
  const handleFullscreen = () => {
    setIsFullscreen(true);
    setScale(1);
    setPosition({ x: 0, y: 0 });
    
    // 延迟一下以确保DOM已更新，然后在全屏容器中重新渲染图表
    setTimeout(() => {
      if (fullscreenRef.current && chart) {
        const chartId = `fullscreen-${id || Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const cleanedChart = cleanChart(chart);
        
        try {
          fullscreenRef.current.innerHTML = '';
          
          mermaid.render(chartId, cleanedChart).then(({ svg }) => {
            if (fullscreenRef.current) {
              fullscreenRef.current.innerHTML = svg;
              
              // 添加全屏样式
              const svgElement = fullscreenRef.current.querySelector('svg');
              if (svgElement) {
                svgElement.style.maxWidth = 'none';
                svgElement.style.width = 'auto';
                svgElement.style.height = 'auto';
              }
            }
          }).catch((error) => {
            console.error('Fullscreen Mermaid rendering error:', error);
            if (fullscreenRef.current) {
              fullscreenRef.current.innerHTML = `
                <div style="padding: 32px; background: #fef2f2; border: 2px solid #fecaca; border-radius: 16px; color: #dc2626; text-align: center;">
                  <h3 style="margin: 0 0 16px 0;">全屏图表渲染失败</h3>
                  <p style="margin: 8px 0; font-size: 16px;">${error.message || '未知错误'}</p>
                </div>
              `;
            }
          });
        } catch (error) {
          console.error('Fullscreen Mermaid error:', error);
          if (fullscreenRef.current) {
            fullscreenRef.current.innerHTML = `
              <div style="padding: 32px; background: #f9fafb; border: 2px solid #d1d5db; border-radius: 16px; text-align: center;">
                <h3 style="margin: 0 0 16px 0;">无法渲染全屏图表</h3>
                <p style="margin: 0; color: #6b7280; font-size: 16px;">
                  ${error instanceof Error ? error.message : '请检查图表语法'}
                </p>
              </div>
            `;
          }
        }
      }
    }, 100);
  };

  const handleCloseFullscreen = () => {
    setIsFullscreen(false);
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };

  // 处理缩放
  const handleZoomIn = () => {
    setScale(prev => Math.min(prev + 0.1, 10)); // 增加到1000%
  };

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev - 0.1, 0.1)); // 最小10%
  };

  const handleReset = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };

  // 处理拖拽
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button === 0) { // 只响应左键
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 处理滚轮缩放
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    setScale(prev => Math.max(0.1, Math.min(10, prev + delta))); // 支持10%到1000%
  };

  // 处理ESC键退出全屏
  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isFullscreen) {
        handleCloseFullscreen();
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isFullscreen]);

  if (!chart || !chart.trim()) {
    return null;
  }

  return (
    <>
      <div className="mermaid-wrapper relative group">
        <div 
          ref={ref} 
          className="mermaid-container"
          style={{ 
            textAlign: 'center',
            padding: '16px',
            background: '#ffffff',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            overflow: 'auto',
            position: 'relative'
          }}
        />
        
        {/* 全屏按钮 */}
        <button
          onClick={handleFullscreen}
          className="absolute top-4 right-4 p-2 bg-white/90 hover:bg-gray-100 rounded-lg shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          title="全屏查看"
        >
          <Maximize2 className="w-4 h-4 text-gray-600" />
        </button>
      </div>

      {/* 全屏模式 */}
      {isFullscreen && createPortal(
        <div className="fixed inset-0 bg-black/95 z-[9999] flex flex-col">
          {/* 工具栏 */}
          <div className="flex items-center justify-between p-4 bg-black/50">
            <div className="flex items-center gap-4">
              {title && (
                <h3 className="text-white text-lg font-medium">{title}</h3>
              )}
              <div className="flex items-center gap-2">
                <button
                  onClick={handleZoomOut}
                  className="p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
                  title="缩小"
                >
                  <ZoomOut className="w-5 h-5 text-white" />
                </button>
                <span className="text-white min-w-[60px] text-center">
                  {Math.round(scale * 100)}%
                </span>
                <button
                  onClick={handleZoomIn}
                  className="p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
                  title="放大"
                >
                  <ZoomIn className="w-5 h-5 text-white" />
                </button>
                <button
                  onClick={handleReset}
                  className="p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors ml-2"
                  title="重置"
                >
                  <RotateCcw className="w-5 h-5 text-white" />
                </button>
              </div>
            </div>
            
            <button
              onClick={handleCloseFullscreen}
              className="p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
              title="关闭"
            >
              <X className="w-5 h-5 text-white" />
            </button>
          </div>

          {/* 图表容器 */}
          <div 
            className="flex-1 overflow-hidden relative bg-white"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            onWheel={handleWheel}
            style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
          >
            <div
              ref={fullscreenRef}
              className="absolute inset-0 flex items-center justify-center p-8"
              style={{
                transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
                transformOrigin: 'center',
                transition: isDragging ? 'none' : 'transform 0.1s'
              }}
            />
          </div>

          {/* 使用提示 */}
          <div className="absolute bottom-4 left-4 text-white/60 text-sm">
            <p>滚轮缩放 • 拖拽移动 • ESC退出</p>
          </div>
        </div>,
        document.body
      )}
    </>
  );
};

export default MermaidChart; 