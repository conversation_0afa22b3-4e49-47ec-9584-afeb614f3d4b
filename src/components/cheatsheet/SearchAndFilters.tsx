import React, { memo } from 'react';
import { Input, Select, Button, Popover, Checkbox, Space, Badge, Tooltip, Card } from 'antd';
import { 
  SearchOutlined, 
  FilterOutlined, 
  SortAscendingOutlined, 
  SortDescendingOutlined,
  StarOutlined,
  ReloadOutlined,
  SettingOutlined,
  CheckOutlined
} from '@ant-design/icons';
import { SearchOptions } from '@/hooks/useCheatSheetFilters';

const { Option } = Select;

interface SearchAndFiltersProps {
  // 搜索相关
  searchTerm: string;
  onSearchChange: (value: string) => void;
  searchOptions: SearchOptions;
  onSearchOptionChange: (option: keyof SearchOptions, checked: boolean) => void;
  getActiveSearchCount: () => number;
  setAllSearchOptions: (value: boolean) => void;
  
  // 过滤相关
  selectedVersions: string[];
  onVersionsChange: (versions: string[]) => void;
  selectedDifficulties: string[];
  onDifficultiesChange: (difficulties: string[]) => void;
  selectedCategories: string[];
  onCategoriesChange: (categories: string[]) => void;
  
  // 排序相关
  sortBy: 'name' | 'difficulty' | 'version' | 'category';
  onSortByChange: (sortBy: 'name' | 'difficulty' | 'version' | 'category') => void;
  sortOrder: 'asc' | 'desc';
  onSortOrderChange: (order: 'asc' | 'desc') => void;
  
  // 收藏过滤
  showFavoritesOnly: boolean;
  onShowFavoritesOnlyChange: (show: boolean) => void;
  
  // 数据选项
  categories: string[];
  difficulties: string[];
  categoryList: string[];
  
  // 操作
  onResetFilters: () => void;
  
  // 结果统计
  totalCount: number;
  filteredCount: number;
}

/**
 * 搜索和过滤组件
 */
const SearchAndFilters: React.FC<SearchAndFiltersProps> = memo(({
  searchTerm,
  onSearchChange,
  searchOptions,
  onSearchOptionChange,
  getActiveSearchCount,
  setAllSearchOptions,
  selectedVersions,
  onVersionsChange,
  selectedDifficulties,
  onDifficultiesChange,
  selectedCategories,
  onCategoriesChange,
  sortBy,
  onSortByChange,
  sortOrder,
  onSortOrderChange,
  showFavoritesOnly,
  onShowFavoritesOnlyChange,
  categories,
  difficulties,
  categoryList,
  onResetFilters,
  totalCount,
  filteredCount
}) => {
  // 搜索选项弹窗内容
  const searchOptionsContent = (
    <div className="w-64 p-2">
      <div className="flex items-center justify-between mb-3">
        <span className="font-medium text-gray-700">搜索范围</span>
        <div className="flex gap-1">
          <Button 
            size="small" 
            type="text" 
            onClick={() => setAllSearchOptions(true)}
            className="text-xs"
          >
            全选
          </Button>
          <Button 
            size="small" 
            type="text" 
            onClick={() => setAllSearchOptions(false)}
            className="text-xs"
          >
            清空
          </Button>
        </div>
      </div>
      <div className="space-y-2">
        <Checkbox
          checked={searchOptions.title}
          onChange={(e) => onSearchOptionChange('title', e.target.checked)}
        >
          标题
        </Checkbox>
        <Checkbox
          checked={searchOptions.description}
          onChange={(e) => onSearchOptionChange('description', e.target.checked)}
        >
          描述
        </Checkbox>
        <Checkbox
          checked={searchOptions.tags}
          onChange={(e) => onSearchOptionChange('tags', e.target.checked)}
        >
          标签
        </Checkbox>
        <Checkbox
          checked={searchOptions.syntax}
          onChange={(e) => onSearchOptionChange('syntax', e.target.checked)}
        >
          语法
        </Checkbox>
        <Checkbox
          checked={searchOptions.example}
          onChange={(e) => onSearchOptionChange('example', e.target.checked)}
        >
          示例代码
        </Checkbox>
      </div>
    </div>
  );

  // 获取版本显示名称和颜色
  const getVersionInfo = (version: string) => {
    // ECMAScript版本信息
    const ecmaVersions: Record<string, { name: string; color: string; desc: string }> = {
      'ES6 (2015)': { name: 'ES6', color: 'gold', desc: '2015年发布，引入了许多重要特性' },
      'ES2016': { name: 'ES7', color: 'orange', desc: '2016年发布，引入了指数运算符等' },
      'ES2017': { name: 'ES8', color: 'red', desc: '2017年发布，引入了async/await等' },
      'ES2018': { name: 'ES9', color: 'purple', desc: '2018年发布，引入了异步迭代等' },
      'ES2019': { name: 'ES10', color: 'blue', desc: '2019年发布，引入了Array.flat等' },
      'ES2020': { name: 'ES11', color: 'cyan', desc: '2020年发布，引入了可选链等' },
      'ES2021': { name: 'ES12', color: 'green', desc: '2021年发布，引入了逻辑赋值等' },
      'ES2022': { name: 'ES13', color: 'lime', desc: '2022年发布，引入了顶层await等' },
      'ES2023': { name: 'ES14', color: 'volcano', desc: '2023年发布，最新特性' }
    };
    
    return ecmaVersions[version] || { name: version, color: 'default', desc: '' };
  };

  // 计算该版本的特性数量
  const getVersionCount = (version: string) => {
    if (version === 'All') return totalCount;
    // 这里应该根据实际数据计算，暂时返回0
    return 0;
  };

  return (
    <Card className="search-filters-card mb-6" bodyStyle={{ padding: '16px' }}>
      <div className="space-y-4">
        {/* 第一行：搜索框和基本操作 */}
        <div className="flex flex-wrap items-center gap-3">
          <div className="flex-1 min-w-64">
            <Input
              placeholder="搜索API、描述、标签..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              prefix={<SearchOutlined className="text-gray-400" />}
              suffix={
                <Popover
                  content={searchOptionsContent}
                  title="搜索选项"
                  trigger="click"
                  placement="bottomRight"
                >
                  <Badge count={getActiveSearchCount()} size="small" offset={[-2, 2]}>
                    <Button
                      type="text"
                      size="small"
                      icon={<SettingOutlined />}
                      className="text-gray-400 hover:text-blue-500"
                    />
                  </Badge>
                </Popover>
              }
              size="large"
              className="rounded-lg"
            />
          </div>
          
          <Tooltip title={showFavoritesOnly ? '显示所有' : '只看收藏'}>
            <Button
              type={showFavoritesOnly ? 'primary' : 'default'}
              icon={<StarOutlined />}
              onClick={() => onShowFavoritesOnlyChange(!showFavoritesOnly)}
              size="large"
            >
              收藏
            </Button>
          </Tooltip>
          
          <Tooltip title="重置所有过滤条件">
            <Button
              icon={<ReloadOutlined />}
              onClick={onResetFilters}
              size="large"
            >
              重置
            </Button>
          </Tooltip>
        </div>

        {/* 第二行：过滤器 */}
        <div className="flex flex-wrap items-center gap-3">
          {/* 版本过滤 */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 whitespace-nowrap">版本:</span>
            <Select
              mode="multiple"
              placeholder="选择版本"
              value={selectedVersions}
              onChange={onVersionsChange}
              style={{ minWidth: 120 }}
              size="middle"
              maxTagCount={2}
              maxTagTextLength={8}
            >
              <Option value="All">
                <div className="flex items-center justify-between">
                  <span>全部</span>
                  <Badge count={totalCount} size="small" color="blue" />
                </div>
              </Option>
              {categories.map(version => {
                const versionInfo = getVersionInfo(version);
                const count = getVersionCount(version);
                return (
                  <Option key={version} value={version}>
                    <div className="flex items-center justify-between">
                      <span>{versionInfo.name}</span>
                      {count > 0 && <Badge count={count} size="small" color={versionInfo.color} />}
                    </div>
                  </Option>
                );
              })}
            </Select>
          </div>

          {/* 难度过滤 */}
          {difficulties.length > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600 whitespace-nowrap">难度:</span>
              <Select
                mode="multiple"
                placeholder="选择难度"
                value={selectedDifficulties}
                onChange={onDifficultiesChange}
                style={{ minWidth: 100 }}
                size="middle"
              >
                {difficulties.map(difficulty => (
                  <Option key={difficulty} value={difficulty}>
                    {difficulty === 'easy' ? '简单' : 
                     difficulty === 'medium' ? '中等' : 
                     difficulty === 'hard' ? '困难' : difficulty}
                  </Option>
                ))}
              </Select>
            </div>
          )}

          {/* 分类过滤 */}
          {categoryList.length > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600 whitespace-nowrap">分类:</span>
              <Select
                mode="multiple"
                placeholder="选择分类"
                value={selectedCategories}
                onChange={onCategoriesChange}
                style={{ minWidth: 100 }}
                size="middle"
              >
                {categoryList.map(category => (
                  <Option key={category} value={category}>
                    {category}
                  </Option>
                ))}
              </Select>
            </div>
          )}

          {/* 排序 */}
          <div className="flex items-center gap-2 ml-auto">
            <span className="text-sm text-gray-600 whitespace-nowrap">排序:</span>
            <Select
              value={sortBy}
              onChange={onSortByChange}
              style={{ width: 80 }}
              size="middle"
            >
              <Option value="name">名称</Option>
              <Option value="difficulty">难度</Option>
              <Option value="version">版本</Option>
              <Option value="category">分类</Option>
            </Select>
            
            <Button
              type="text"
              icon={sortOrder === 'asc' ? <SortAscendingOutlined /> : <SortDescendingOutlined />}
              onClick={() => onSortOrderChange(sortOrder === 'asc' ? 'desc' : 'asc')}
              size="middle"
              className="text-gray-600 hover:text-blue-500"
            />
          </div>
        </div>

        {/* 结果统计 */}
        <div className="flex items-center justify-between text-sm text-gray-500 pt-2 border-t border-gray-100">
          <div className="flex items-center gap-4">
            <span>
              显示 <span className="font-medium text-blue-600">{filteredCount}</span> / {totalCount} 个API
            </span>
            {filteredCount !== totalCount && (
              <span className="text-orange-600">
                已过滤 {totalCount - filteredCount} 个
              </span>
            )}
          </div>
          
          {(searchTerm || selectedVersions.length > 1 || selectedDifficulties.length > 0 || 
            selectedCategories.length > 0 || showFavoritesOnly) && (
            <Button
              type="link"
              size="small"
              onClick={onResetFilters}
              className="text-gray-400 hover:text-blue-500"
            >
              清除所有过滤条件
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
});

SearchAndFilters.displayName = 'SearchAndFilters';

export default SearchAndFilters;
