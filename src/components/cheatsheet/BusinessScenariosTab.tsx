import React, { useState, memo } from 'react';
import { Typo<PERSON>, Card, Button, Tag, Divider, Alert, Steps, Timeline } from 'antd';
import { 
  BulbOutlined, 
  CodeOutlined, 
  RocketOutlined, 
  CheckCircleOutlined,
  PlayCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { ApiItem } from '@/types/api';
import CodeHighlight from '../CodeHighlight';

const { Title, Paragraph, Text } = Typography;

interface BusinessScenariosTabProps {
  item: ApiItem;
  copyToClipboard: (text: string) => void;
}

/**
 * 使用示例Tab组件
 * 展示API的业务场景和实际应用案例
 */
const BusinessScenariosTab: React.FC<BusinessScenariosTabProps> = memo(({ item, copyToClipboard }) => {
  const [subTab, setSubTab] = useState(0);
  const scenarios = item.businessScenarios || [];

  if (!scenarios || scenarios.length === 0) {
    return (
      <div className="p-6">
        <Alert
          message="使用示例暂未完善"
          description="该API的使用示例正在完善中，请稍后查看。"
          type="info"
          showIcon
        />
      </div>
    );
  }

  const currentScenario = scenarios[subTab];

  const renderScenarioNavigation = () => (
    <div className="mb-6">
      <div className="flex flex-wrap gap-2">
        {scenarios.map((scenario, index) => (
          <Button
            key={index}
            type={subTab === index ? 'primary' : 'default'}
            onClick={() => setSubTab(index)}
            className="mb-2"
            icon={<BulbOutlined />}
          >
            {scenario.title}
          </Button>
        ))}
      </div>
    </div>
  );

  const renderScenarioHeader = (scenario: any) => (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-200/50 mb-6">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <Title level={3} className="text-blue-800 mb-2 flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <BulbOutlined className="text-white text-sm" />
            </div>
            {scenario.title}
          </Title>
          <Paragraph className="text-blue-700 text-lg leading-relaxed mb-3">
            {scenario.description}
          </Paragraph>
        </div>
        <div className="flex flex-col gap-2 ml-4">
          {scenario.difficulty && (
            <Tag color={
              scenario.difficulty === 'advanced' ? 'red' :
              scenario.difficulty === 'intermediate' ? 'orange' : 'green'
            }>
              {scenario.difficulty === 'advanced' ? '高级' :
               scenario.difficulty === 'intermediate' ? '中级' : '初级'}
            </Tag>
          )}
          {scenario.category && (
            <Tag color="blue">{scenario.category}</Tag>
          )}
        </div>
      </div>

      {/* 场景特点 */}
      {scenario.features && scenario.features.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {scenario.features.map((feature: string, index: number) => (
            <div key={index} className="flex items-center gap-2 text-blue-600">
              <CheckCircleOutlined className="text-green-500" />
              <Text className="text-sm">{feature}</Text>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderImplementationSteps = (scenario: any) => {
    if (!scenario.implementation || !scenario.implementation.steps) {
      return null;
    }

    return (
      <div className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm mb-6">
        <Title level={4} className="text-gray-800 mb-4 flex items-center gap-2">
          <SettingOutlined />
          实现步骤
        </Title>
        <Steps
          direction="vertical"
          current={-1}
          items={scenario.implementation.steps.map((step: any, index: number) => ({
            title: step.title,
            description: (
              <div className="mt-2">
                <Paragraph className="text-gray-600 mb-3">{step.description}</Paragraph>
                {step.code && (
                  <CodeHighlight
                    code={step.code}
                    language="javascript"
                    title={`步骤 ${index + 1} 代码`}
                    onCopy={() => copyToClipboard(step.code)}
                  />
                )}
                {step.explanation && (
                  <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200/50">
                    <Text className="text-blue-700 text-sm">{step.explanation}</Text>
                  </div>
                )}
              </div>
            ),
            icon: <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">{index + 1}</div>
          }))}
        />
      </div>
    );
  };

  const renderCodeExample = (scenario: any) => {
    if (!scenario.code && !scenario.example) {
      return null;
    }

    return (
      <div className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm mb-6">
        <Title level={4} className="text-gray-800 mb-4 flex items-center gap-2">
          <CodeOutlined />
          完整示例
        </Title>
        <CodeHighlight
          code={scenario.code || scenario.example}
          language="javascript"
          title={scenario.title}
          onCopy={() => copyToClipboard(scenario.code || scenario.example)}
          showLineNumbers
        />
        
        {scenario.explanation && (
          <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200/50">
            <Title level={5} className="text-green-800 mb-2">代码说明</Title>
            <Text className="text-green-700">{scenario.explanation}</Text>
          </div>
        )}
      </div>
    );
  };

  const renderBestPractices = (scenario: any) => {
    if (!scenario.bestPractices || scenario.bestPractices.length === 0) {
      return null;
    }

    return (
      <div className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm mb-6">
        <Title level={4} className="text-gray-800 mb-4 flex items-center gap-2">
          <RocketOutlined />
          最佳实践
        </Title>
        <div className="space-y-4">
          {scenario.bestPractices.map((practice: any, index: number) => (
            <div key={index} className="p-4 bg-yellow-50 rounded-lg border border-yellow-200/50">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs font-bold">{index + 1}</span>
                </div>
                <div className="flex-1">
                  <Text strong className="text-yellow-800 block mb-1">
                    {practice.title || practice.practice}
                  </Text>
                  <Text className="text-yellow-700 text-sm">
                    {practice.description || practice.reason}
                  </Text>
                  {practice.example && (
                    <div className="mt-2">
                      <CodeHighlight
                        code={practice.example}
                        language="javascript"
                        title="示例"
                        onCopy={() => copyToClipboard(practice.example)}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderCommonPitfalls = (scenario: any) => {
    if (!scenario.commonPitfalls || scenario.commonPitfalls.length === 0) {
      return null;
    }

    return (
      <div className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm mb-6">
        <Title level={4} className="text-gray-800 mb-4 flex items-center gap-2">
          <Alert.ErrorIcon />
          常见陷阱
        </Title>
        <div className="space-y-4">
          {scenario.commonPitfalls.map((pitfall: any, index: number) => (
            <div key={index} className="p-4 bg-red-50 rounded-lg border border-red-200/50">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs font-bold">!</span>
                </div>
                <div className="flex-1">
                  <Text strong className="text-red-800 block mb-1">
                    {pitfall.title || pitfall.pitfall}
                  </Text>
                  <Text className="text-red-700 text-sm mb-2">
                    {pitfall.description || pitfall.why}
                  </Text>
                  {pitfall.wrongExample && (
                    <div className="mb-2">
                      <Text className="text-red-600 text-xs block mb-1">❌ 错误示例：</Text>
                      <CodeHighlight
                        code={pitfall.wrongExample}
                        language="javascript"
                        title="错误用法"
                      />
                    </div>
                  )}
                  {pitfall.correctExample && (
                    <div>
                      <Text className="text-green-600 text-xs block mb-1">✅ 正确示例：</Text>
                      <CodeHighlight
                        code={pitfall.correctExample}
                        language="javascript"
                        title="正确用法"
                        onCopy={() => copyToClipboard(pitfall.correctExample)}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderUseCases = (scenario: any) => {
    if (!scenario.useCases || scenario.useCases.length === 0) {
      return null;
    }

    return (
      <div className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm mb-6">
        <Title level={4} className="text-gray-800 mb-4">应用场景</Title>
        <Timeline
          items={scenario.useCases.map((useCase: any, index: number) => ({
            dot: <PlayCircleOutlined className="text-blue-500" />,
            children: (
              <div>
                <Title level={5} className="mb-2">{useCase.title}</Title>
                <Paragraph className="text-gray-600 mb-3">{useCase.description}</Paragraph>
                {useCase.code && (
                  <CodeHighlight
                    code={useCase.code}
                    language="javascript"
                    title={useCase.title}
                    onCopy={() => copyToClipboard(useCase.code)}
                  />
                )}
              </div>
            )
          }))}
        />
      </div>
    );
  };

  return (
    <div className="business-scenarios-tab">
      {renderScenarioNavigation()}
      
      <div className="scenario-content">
        {renderScenarioHeader(currentScenario)}
        {renderImplementationSteps(currentScenario)}
        {renderCodeExample(currentScenario)}
        {renderUseCases(currentScenario)}
        {renderBestPractices(currentScenario)}
        {renderCommonPitfalls(currentScenario)}
      </div>
    </div>
  );
});

BusinessScenariosTab.displayName = 'BusinessScenariosTab';

export default BusinessScenariosTab;
