import React, { useState, memo } from 'react';
import { <PERSON><PERSON><PERSON>, Tabs, Card, Tag, Divider, Al<PERSON>, List, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'antd';
import { 
  FileTextOutlined, 
  BulbOutlined, 
  CodeOutlined, 
  QuestionCircleOutlined,
  CopyOutlined,
  CheckCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { ApiItem } from '@/types/api';
import CodeHighlight from '../CodeHighlight';

const { Title, Paragraph, Text } = Typography;

interface BasicInfoTabProps {
  item: ApiItem;
  copyToClipboard: (text: string) => void;
}

/**
 * 基本信息Tab组件
 * 显示API的基本信息、语法、参数、特性等
 */
const BasicInfoTab: React.FC<BasicInfoTabProps> = memo(({ item, copyToClipboard }) => {
  const [subTab, setSubTab] = useState('overview');

  const subTabs = [
    { key: 'overview', label: '概览', icon: <FileTextOutlined /> },
    { key: 'syntax', label: '语法', icon: <CodeOutlined /> },
    { key: 'features', label: '特性', icon: <ThunderboltOutlined /> },
    { key: 'examples', label: '示例', icon: <BulbOutlined /> }
  ];

  if (!item.basicInfo) {
    return (
      <div className="p-6">
        <Alert
          message="基本信息暂未完善"
          description="该API的基本信息正在完善中，请稍后查看。"
          type="info"
          showIcon
        />
      </div>
    );
  }

  const renderOverview = () => (
    <div className="space-y-6">
      {/* 定义和介绍 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-200/50">
        <Title level={4} className="text-blue-800 mb-4 flex items-center gap-2">
          <FileTextOutlined />
          API定义
        </Title>
        <Paragraph className="text-blue-700 text-lg leading-relaxed mb-4">
          {item.basicInfo.definition || item.description}
        </Paragraph>
        <Paragraph className="text-blue-600 leading-relaxed">
          {item.basicInfo.introduction}
        </Paragraph>
      </div>

      {/* 核心特性预览 */}
      {item.basicInfo.keyFeatures && item.basicInfo.keyFeatures.length > 0 && (
        <div className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center">
              <ThunderboltOutlined className="text-white text-sm" />
            </div>
            <Title level={4} className="mb-0 text-gray-800">
              核心特性
            </Title>
            <div className="px-3 py-1 bg-emerald-100 text-emerald-700 rounded-full text-sm font-medium">
              {item.basicInfo.keyFeatures.length} 项特性
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {item.basicInfo.keyFeatures.slice(0, 4).map((feature, index) => (
              <div
                key={index}
                className="group p-4 bg-emerald-50 hover:bg-emerald-100 rounded-xl border border-emerald-200/50 hover:border-emerald-300 transition-all duration-300 cursor-pointer hover:shadow-sm"
              >
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-emerald-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-xs font-bold">{index + 1}</span>
                  </div>
                  <div className="flex-1">
                    <Text strong className="text-emerald-800 block mb-1 group-hover:text-emerald-900 transition-colors">
                      {feature.feature}
                    </Text>
                    <Text className="text-emerald-700 text-sm leading-relaxed">
                      {feature.description}
                    </Text>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 参数信息 */}
      {item.basicInfo.parameters && item.basicInfo.parameters.length > 0 && (
        <div className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm">
          <Title level={4} className="text-gray-800 mb-4">参数说明</Title>
          <div className="space-y-3">
            {item.basicInfo.parameters.map((param, index) => (
              <div key={index} className="p-4 bg-gray-50 rounded-lg border border-gray-200/50">
                <div className="flex items-center gap-3 mb-2">
                  <code className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-mono">
                    {param.name}
                  </code>
                  <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs">
                    {param.type}
                  </span>
                  {param.required && (
                    <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
                      必需
                    </span>
                  )}
                </div>
                <Text className="text-gray-700">{param.description}</Text>
                {param.details && (
                  <Text className="text-gray-600 text-sm block mt-1">{param.details}</Text>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 返回值 */}
      {item.basicInfo.returnValue && (
        <div className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm">
          <Title level={4} className="text-gray-800 mb-4">返回值</Title>
          <div className="p-4 bg-green-50 rounded-lg border border-green-200/50">
            <div className="flex items-center gap-3 mb-2">
              <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">
                {item.basicInfo.returnValue.type}
              </span>
            </div>
            <Text className="text-green-700">{item.basicInfo.returnValue.description}</Text>
          </div>
        </div>
      )}
    </div>
  );

  const renderSyntax = () => (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm">
        <Title level={4} className="text-gray-800 mb-4">语法格式</Title>
        <CodeHighlight
          code={item.basicInfo.syntax}
          language="typescript"
          title="TypeScript 语法"
          onCopy={() => copyToClipboard(item.basicInfo.syntax)}
        />
      </div>
      
      {item.syntax && item.syntax !== item.basicInfo.syntax && (
        <div className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm">
          <Title level={4} className="text-gray-800 mb-4">基础语法</Title>
          <CodeHighlight
            code={item.syntax}
            language="javascript"
            title="JavaScript 语法"
            onCopy={() => copyToClipboard(item.syntax)}
          />
        </div>
      )}
    </div>
  );

  const renderFeatures = () => (
    <div className="space-y-6">
      {item.basicInfo.keyFeatures && item.basicInfo.keyFeatures.length > 0 && (
        <div className="space-y-4">
          {item.basicInfo.keyFeatures.map((feature, index) => (
            <div key={index} className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm">
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-white font-bold">{index + 1}</span>
                </div>
                <div className="flex-1">
                  <Title level={5} className="text-gray-800 mb-2">{feature.feature}</Title>
                  <Paragraph className="text-gray-600 mb-3">{feature.description}</Paragraph>
                  {feature.details && (
                    <div className="p-3 bg-blue-50 rounded-lg border border-blue-200/50">
                      <Text className="text-blue-700 text-sm">{feature.details}</Text>
                    </div>
                  )}
                  <div className="mt-3">
                    <Tag color={
                      feature.importance === 'critical' ? 'red' :
                      feature.importance === 'high' ? 'orange' :
                      feature.importance === 'medium' ? 'blue' : 'default'
                    }>
                      {feature.importance === 'critical' ? '关键' :
                       feature.importance === 'high' ? '重要' :
                       feature.importance === 'medium' ? '中等' : '一般'}
                    </Tag>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderExamples = () => (
    <div className="space-y-6">
      {/* 快速示例 */}
      {(item.basicInfo.quickExample || item.basicInfo.quickExampleCode) && (
        <div className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm">
          <Title level={4} className="text-gray-800 mb-4">快速示例</Title>
          <CodeHighlight
            code={item.basicInfo.quickExampleCode || item.basicInfo.quickExample || item.example}
            language="javascript"
            title="基础用法"
            onCopy={() => copyToClipboard(item.basicInfo.quickExampleCode || item.basicInfo.quickExample || item.example)}
          />
        </div>
      )}

      {/* 常见用例 */}
      {item.basicInfo.commonUseCases && item.basicInfo.commonUseCases.length > 0 && (
        <div className="space-y-4">
          <Title level={4} className="text-gray-800">常见用例</Title>
          {item.basicInfo.commonUseCases.map((useCase, index) => (
            <div key={index} className="bg-white p-6 rounded-2xl border border-gray-200/50 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <Title level={5} className="mb-0">{useCase.title}</Title>
                {useCase.difficulty && (
                  <Tag color={
                    useCase.difficulty === 'advanced' ? 'red' :
                    useCase.difficulty === 'intermediate' ? 'orange' : 'green'
                  }>
                    {useCase.difficulty === 'advanced' ? '高级' :
                     useCase.difficulty === 'intermediate' ? '中级' : '初级'}
                  </Tag>
                )}
              </div>
              <Paragraph className="text-gray-600 mb-4">{useCase.description}</Paragraph>
              <CodeHighlight
                code={useCase.code}
                language="javascript"
                title={useCase.title}
                onCopy={() => copyToClipboard(useCase.code)}
              />
              {useCase.explanation && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200/50">
                  <Text className="text-blue-700 text-sm">{useCase.explanation}</Text>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className="basic-info-tab">
      <Tabs
        activeKey={subTab}
        onChange={setSubTab}
        items={subTabs.map(tab => ({
          key: tab.key,
          label: (
            <span className="flex items-center gap-2">
              {tab.icon}
              {tab.label}
            </span>
          ),
          children: (
            <div className="p-4">
              {tab.key === 'overview' && renderOverview()}
              {tab.key === 'syntax' && renderSyntax()}
              {tab.key === 'features' && renderFeatures()}
              {tab.key === 'examples' && renderExamples()}
            </div>
          )
        }))}
      />
    </div>
  );
});

BasicInfoTab.displayName = 'BasicInfoTab';

export default BasicInfoTab;
