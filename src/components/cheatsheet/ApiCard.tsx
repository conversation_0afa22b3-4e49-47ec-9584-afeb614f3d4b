import React, { memo, useMemo, useCallback } from 'react';
import { Card, Typography, Tag, Button, Space, Tooltip } from 'antd';
import { 
  StarOutlined, 
  StarFilled, 
  CopyOutlined, 
  ExternalLinkOutlined,
  CodeOutlined,
  BulbOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { ApiItem } from '@/types/api';

const { Title, Paragraph, Text } = Typography;

interface ApiCardProps {
  item: ApiItem;
  isFavorite: boolean;
  onCardClick: (item: ApiItem) => void;
  onToggleFavorite: (id: string) => void;
  onCopyToClipboard: (text: string) => void;
  themeColor?: 'vue' | 'react' | 'nextjs' | 'typescript' | 'ecma' | 'default';
}

/**
 * API卡片组件
 * 展示单个API的基本信息和操作按钮
 */
const ApiCard: React.FC<ApiCardProps> = memo(({
  item,
  isFavorite,
  onCardClick,
  onToggleFavorite,
  onCopyToClipboard,
  themeColor = 'default'
}) => {
  // 记忆化计算卡片内容
  const cardContent = useMemo(() => {
    // 安全处理example字段，防止undefined导致的split错误
    const exampleText = item.example || '';
    const exampleLines = exampleText.split('\n');
    const previewLines = exampleLines.slice(0, 3);
    const hasMoreLines = exampleLines.length > 3;
    
    return {
      examplePreview: previewLines.join('\n'),
      hasMoreExample: hasMoreLines,
      totalLines: exampleLines.length
    };
  }, [item.example]);

  // 优化点击处理函数
  const handleClick = useCallback(() => {
    onCardClick(item);
  }, [onCardClick, item]);

  const handleFavoriteClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleFavorite(item.id);
  }, [onToggleFavorite, item.id]);

  const handleCopyClick = useCallback((e: React.MouseEvent, text: string) => {
    e.stopPropagation();
    onCopyToClipboard(text);
  }, [onCopyToClipboard]);

  // 获取难度颜色
  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 border-green-300';
      case 'medium': return 'text-yellow-600 border-yellow-300';
      case 'hard': return 'text-red-600 border-red-300';
      default: return 'text-blue-600 border-blue-300';
    }
  };

  // 获取主题边框颜色
  const getThemeBorderColor = () => {
    const themeColors = {
      vue: 'border-l-green-500',
      react: 'border-l-blue-500',
      nextjs: 'border-l-black',
      typescript: 'border-l-blue-600',
      ecma: 'border-l-yellow-500',
      default: 'border-l-gray-500'
    };
    return themeColors[themeColor];
  };

  // 获取版本标签颜色
  const getVersionColor = (version?: string) => {
    if (!version) return 'default';
    
    // ECMAScript版本颜色
    if (version.startsWith('ES') || version.includes('2015') || version.includes('2016') || version.includes('2017')) {
      return 'gold';
    }
    
    // React版本颜色
    if (version.includes('16') || version.includes('17') || version.includes('18')) {
      return 'blue';
    }
    
    return 'default';
  };

  return (
    <Card
      className={`api-card group hover:shadow-lg transition-all duration-300 cursor-pointer border-l-4 ${getThemeBorderColor()} hover:border-l-6`}
      onClick={handleClick}
      hoverable
      size="small"
      bodyStyle={{ padding: '16px' }}
      actions={[
        <Tooltip title={isFavorite ? '取消收藏' : '添加收藏'} key="favorite">
          <Button
            type="text"
            icon={isFavorite ? <StarFilled className="text-yellow-500" /> : <StarOutlined />}
            onClick={handleFavoriteClick}
            className="hover:text-yellow-500"
          />
        </Tooltip>,
        <Tooltip title="复制示例代码" key="copy">
          <Button
            type="text"
            icon={<CopyOutlined />}
            onClick={(e) => handleCopyClick(e, item.example || item.syntax || '')}
            className="hover:text-blue-500"
          />
        </Tooltip>,
        <Tooltip title="查看详情" key="detail">
          <Button
            type="text"
            icon={<ExternalLinkOutlined />}
            onClick={handleClick}
            className="hover:text-green-500"
          />
        </Tooltip>
      ]}
    >
      {/* 卡片头部 */}
      <div className="mb-3">
        <div className="flex items-start justify-between mb-2">
          <Title level={5} className="mb-0 text-gray-800 group-hover:text-blue-600 transition-colors">
            {item.title}
          </Title>
          <div className="flex gap-1 ml-2">
            {item.version && (
              <Tag color={getVersionColor(item.version)} size="small">
                {item.version}
              </Tag>
            )}
            {item.difficulty && (
              <Tag className={`text-xs ${getDifficultyColor(item.difficulty)}`} size="small">
                {item.difficulty === 'easy' ? '简单' : 
                 item.difficulty === 'medium' ? '中等' : 
                 item.difficulty === 'hard' ? '困难' : item.difficulty}
              </Tag>
            )}
          </div>
        </div>
        
        <Paragraph className="text-gray-600 text-sm mb-3 line-clamp-2">
          {item.description}
        </Paragraph>

        {/* 标签 */}
        {item.tags && item.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {item.tags.slice(0, 3).map(tag => (
              <Tag key={tag} size="small" className="text-xs">
                {tag}
              </Tag>
            ))}
            {item.tags.length > 3 && (
              <Tag size="small" className="text-xs text-gray-500">
                +{item.tags.length - 3}
              </Tag>
            )}
          </div>
        )}
      </div>

      {/* 语法预览 */}
      {item.syntax && (
        <div className="mb-3">
          <div className="flex items-center gap-2 mb-2">
            <CodeOutlined className="text-blue-500 text-sm" />
            <Text className="text-xs text-gray-500 font-medium">语法</Text>
          </div>
          <div className="bg-gray-50 rounded p-2 border">
            <pre className="text-xs font-mono text-gray-700 overflow-hidden">
              {item.syntax.length > 80 ? `${item.syntax.substring(0, 80)}...` : item.syntax}
            </pre>
          </div>
        </div>
      )}

      {/* 示例预览 */}
      {cardContent.examplePreview && (
        <div className="mb-3">
          <div className="flex items-center gap-2 mb-2">
            <BulbOutlined className="text-green-500 text-sm" />
            <Text className="text-xs text-gray-500 font-medium">示例</Text>
            {cardContent.hasMoreExample && (
              <Text className="text-xs text-gray-400">
                ({cardContent.totalLines} 行)
              </Text>
            )}
          </div>
          <div className="bg-gray-50 rounded p-2 border">
            <pre className="text-xs font-mono text-gray-700 overflow-hidden">
              {cardContent.examplePreview}
              {cardContent.hasMoreExample && (
                <Text className="text-gray-400 block mt-1">...</Text>
              )}
            </pre>
          </div>
        </div>
      )}

      {/* 特性预览 */}
      {item.basicInfo?.keyFeatures && item.basicInfo.keyFeatures.length > 0 && (
        <div className="mb-3">
          <div className="flex items-center gap-2 mb-2">
            <ThunderboltOutlined className="text-orange-500 text-sm" />
            <Text className="text-xs text-gray-500 font-medium">核心特性</Text>
          </div>
          <div className="space-y-1">
            {item.basicInfo.keyFeatures.slice(0, 2).map((feature, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className="w-1 h-1 bg-orange-400 rounded-full"></div>
                <Text className="text-xs text-gray-600 line-clamp-1">
                  {feature.feature}
                </Text>
              </div>
            ))}
            {item.basicInfo.keyFeatures.length > 2 && (
              <Text className="text-xs text-gray-400 ml-3">
                +{item.basicInfo.keyFeatures.length - 2} 个特性
              </Text>
            )}
          </div>
        </div>
      )}

      {/* 底部信息 */}
      <div className="flex items-center justify-between text-xs text-gray-400 pt-2 border-t border-gray-100">
        <div className="flex items-center gap-3">
          {item.category && (
            <span>分类: {item.category}</span>
          )}
          {item.basicInfo?.parameters && (
            <span>参数: {item.basicInfo.parameters.length}</span>
          )}
        </div>
        <div className="flex items-center gap-1">
          <span className="w-2 h-2 bg-green-400 rounded-full"></span>
          <span>可用</span>
        </div>
      </div>
    </Card>
  );
});

ApiCard.displayName = 'ApiCard';

export default ApiCard;
