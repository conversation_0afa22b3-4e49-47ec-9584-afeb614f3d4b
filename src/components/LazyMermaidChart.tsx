import React, { Suspense, lazy, useState } from 'react';
import { Alert } from 'antd';

// 懒加载MermaidChart组件
const MermaidChart = lazy(() => import('./MermaidChart'));

interface LazyMermaidChartProps {
  chart: string;
  title?: string;
  id?: string;
  className?: string;
}

// 加载状态组件
const MermaidSkeleton: React.FC = () => (
  <div className="bg-slate-50 rounded-lg p-8 animate-pulse">
    <div className="flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 bg-slate-200 rounded-lg mx-auto mb-4"></div>
        <div className="h-4 bg-slate-200 rounded w-32 mx-auto mb-2"></div>
        <div className="h-3 bg-slate-200 rounded w-24 mx-auto"></div>
      </div>
    </div>
  </div>
);

// 错误状态组件
const MermaidError: React.FC<{ onRetry: () => void }> = ({ onRetry }) => (
  <Alert
    message="图表加载失败"
    description="Mermaid图表渲染出现问题，请稍后重试"
    type="warning"
    showIcon
    action={
      <button
        onClick={onRetry}
        className="text-blue-600 hover:text-blue-700 text-sm font-medium hover:underline"
      >
        重新加载
      </button>
    }
    className="rounded-lg"
  />
);

// 错误边界组件
class MermaidErrorBoundary extends React.Component<
  { children: React.ReactNode; onError: () => void },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; onError: () => void }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Mermaid chart error:', error, errorInfo);
    this.props.onError();
  }

  render() {
    if (this.state.hasError) {
      return (
        <MermaidError 
          onRetry={() => {
            this.setState({ hasError: false });
            window.location.reload();
          }} 
        />
      );
    }

    return this.props.children;
  }
}

const LazyMermaidChart: React.FC<LazyMermaidChartProps> = ({ 
  chart, 
  title, 
  id, 
  className = "" 
}) => {
  const [retryCount, setRetryCount] = useState(0);

  const handleError = () => {
    setRetryCount(prev => prev + 1);
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
  };

  return (
    <div className={`mermaid-chart-container ${className}`}>
      <MermaidErrorBoundary onError={handleError}>
        <Suspense fallback={<MermaidSkeleton />}>
          <MermaidChart 
            key={`${id}-${retryCount}`} // 强制重新渲染
            chart={chart}
            title={title}
            id={id}
          />
        </Suspense>
      </MermaidErrorBoundary>
    </div>
  );
};

export default LazyMermaidChart;
