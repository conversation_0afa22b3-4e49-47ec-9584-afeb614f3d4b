import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import { Typography, Alert } from 'antd';
import CodeHighlight from './CodeHighlight';
import MermaidChart from './MermaidChart';

const { Title, Paragraph, Text } = Typography;

interface MarkdownContentProps {
  content: string;
}

// 安全地将children转换为字符串
const childrenToString = (children: ReactNode): string => {
  if (typeof children === 'string') {
    return children;
  }
  if (typeof children === 'number') {
    return String(children);
  }
  if (Array.isArray(children)) {
    return children.map(childrenToString).join('');
  }
  if (React.isValidElement(children)) {
    return childrenToString(children.props.children);
  }
  return '';
};

// Markdown组件配置
const MarkdownContent: React.FC<MarkdownContentProps> = ({ content }) => {
  // 调试：检查content的类型和内容
  console.log('MarkdownContent received content:', {
    type: typeof content,
    isString: typeof content === 'string',
    length: content?.length,
    preview: typeof content === 'string' ? content.slice(0, 100) : 'NOT STRING',
    content: content
  });

  return (
    <div className="markdown-content">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight, rehypeRaw]}
        components={{
          h1: ({ children }) => <Title level={2} style={{ marginTop: '2rem', marginBottom: '1rem' }}>{childrenToString(children)}</Title>,
          h2: ({ children }) => <Title level={3} style={{ marginTop: '1.5rem', marginBottom: '0.75rem' }}>{childrenToString(children)}</Title>,
          h3: ({ children }) => <Title level={4} style={{ marginTop: '1.25rem', marginBottom: '0.5rem' }}>{childrenToString(children)}</Title>,
          h4: ({ children }) => <Title level={5} style={{ marginTop: '1rem', marginBottom: '0.5rem' }}>{childrenToString(children)}</Title>,
          p: ({ children }) => <Paragraph style={{ marginBottom: '1rem', lineHeight: '1.6' }}>{children}</Paragraph>,
          code: ({ children, className, ...props }) => {
            const match = /language-(\w+)/.exec(className || '');
            const language = match ? match[1] : '';
            const inline = !match;
            
            if (!inline && match) {
              const codeString = childrenToString(children).replace(/\n$/, '');
              
              // 如果是mermaid图表，使用MermaidChart组件
              if (language === 'mermaid') {
                return <MermaidChart chart={codeString} />;
              }
              
              // 其他代码使用CodeHighlight组件
              return <CodeHighlight code={codeString} language={language} />;
            }
            
            // 行内代码
            return <Text code style={{ padding: '2px 4px', fontSize: '0.875em' }}>{childrenToString(children)}</Text>;
          },
          blockquote: ({ children }) => (
            <Alert
              message={children}
              type="info"
              showIcon
              style={{ margin: '16px 0', border: 'none', backgroundColor: '#f6f8fa' }}
            />
          ),
          ul: ({ children }) => <ul style={{ paddingLeft: '24px', marginBottom: '16px' }}>{children}</ul>,
          ol: ({ children }) => <ol style={{ paddingLeft: '24px', marginBottom: '16px' }}>{children}</ol>,
          li: ({ children }) => <li style={{ marginBottom: '4px', lineHeight: '1.6' }}>{children}</li>,
          strong: ({ children }) => <Text strong>{childrenToString(children)}</Text>,
          em: ({ children }) => <Text italic>{childrenToString(children)}</Text>,
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownContent; 