import React from 'react';
import { Tree } from 'antd';
import { 
  FileTextOutlined, 
  BulbOutlined, 
  CodeOutlined, 
  QuestionCircleOutlined, 
  ExperimentOutlined, 
  HistoryOutlined,
  RocketOutlined,
  BookOutlined,
  RetweetOutlined,
  ApiOutlined
} from '@ant-design/icons';
import { TreeNavigationProps, TreeNode } from '@/types/layout';
import { ApiItem } from '@/types/api';

/**
 * Tab配置 - 与CheatSheet保持一致
 */
const TAB_CONFIG = {
  core: [
    { key: 'basic', label: '基本信息', icon: <FileTextOutlined /> },
    { key: 'business', label: '使用示例', icon: <BulbOutlined /> },
    { key: 'implementation', label: '原理解析', icon: <CodeOutlined /> },
    { key: 'interview', label: '面试准备', icon: <QuestionCircleOutlined /> },
    { key: 'questions', label: '常见问题', icon: <ExperimentOutlined /> },
    { key: 'archaeology', label: '知识考古', icon: <HistoryOutlined /> }
  ],
  advanced: [
    { key: 'performance', label: '性能优化', icon: <RocketOutlined /> },
    { key: 'debugging', label: '调试技巧', icon: <BookOutlined /> },
    { key: 'essence', label: '本质洞察', icon: <RetweetOutlined /> }
  ]
};

/**
 * 生成树状数据结构
 */
export const generateTreeData = (apiData: ApiItem[]): TreeNode[] => {
  return apiData.map(api => {
    const children: TreeNode[] = [];
    
    // 添加核心Tab
    TAB_CONFIG.core.forEach(tab => {
      const tabData = getTabData(api, tab.key);
      if (tabData) {
        const tabNode: TreeNode = {
          key: `${api.id}-${tab.key}`,
          title: tab.label,
          icon: tab.icon,
          level: 2,
          parentKey: api.id,
          data: { api, tabKey: tab.key, tabData }
        };
        
        // 添加子Tab（如果存在）
        const subTabs = getSubTabs(tabData, tab.key);
        if (subTabs.length > 0) {
          tabNode.children = subTabs.map(subTab => ({
            key: `${api.id}-${tab.key}-${subTab.key}`,
            title: subTab.title,
            level: 3,
            parentKey: `${api.id}-${tab.key}`,
            data: { api, tabKey: tab.key, subTabKey: subTab.key, subTabData: subTab.data }
          }));
        }
        
        children.push(tabNode);
      }
    });
    
    // 添加高级Tab
    TAB_CONFIG.advanced.forEach(tab => {
      const tabData = getTabData(api, tab.key);
      if (tabData) {
        const tabNode: TreeNode = {
          key: `${api.id}-${tab.key}`,
          title: tab.label,
          icon: tab.icon,
          level: 2,
          parentKey: api.id,
          data: { api, tabKey: tab.key, tabData }
        };
        
        // 添加子Tab（如果存在）
        const subTabs = getSubTabs(tabData, tab.key);
        if (subTabs.length > 0) {
          tabNode.children = subTabs.map(subTab => ({
            key: `${api.id}-${tab.key}-${subTab.key}`,
            title: subTab.title,
            level: 3,
            parentKey: `${api.id}-${tab.key}`,
            data: { api, tabKey: tab.key, subTabKey: subTab.key, subTabData: subTab.data }
          }));
        }
        
        children.push(tabNode);
      }
    });
    
    return {
      key: api.id,
      title: api.title,
      icon: <ApiOutlined />,
      level: 1,
      children,
      data: { api }
    };
  });
};

/**
 * 获取Tab数据
 */
const getTabData = (api: ApiItem, tabKey: string) => {
  switch (tabKey) {
    case 'basic': return api.basicInfo;
    case 'business': return api.businessScenarios;
    case 'implementation': return api.implementation;
    case 'interview': return api.interviewQuestions;
    case 'questions': return api.commonQuestions;
    case 'archaeology': return api.knowledgeArchaeology;
    case 'performance': return api.performanceOptimization;
    case 'debugging': return api.debuggingTips;
    case 'essence': return api.essenceInsights;
    default: return null;
  }
};

/**
 * 获取子Tab
 */
const getSubTabs = (tabData: any, tabKey: string): Array<{key: string, title: string, data: any}> => {
  if (!tabData) return [];
  
  // 本质洞察的子Tab
  if (tabKey === 'essence' && tabData.subTabs) {
    return [
      { key: 'problem', title: '核心问题', data: tabData.subTabs.problem },
      { key: 'design', title: '设计智慧', data: tabData.subTabs.design },
      { key: 'insight', title: '应用洞察', data: tabData.subTabs.insight },
      { key: 'architecture', title: '架构思考', data: tabData.subTabs.architecture }
    ].filter(item => item.data);
  }
  
  // 调试技巧的子Tab
  if (tabKey === 'debugging' && tabData.subTabs) {
    return tabData.subTabs.map((subTab: any) => ({
      key: subTab.key,
      title: subTab.title,
      data: subTab
    }));
  }
  
  return [];
};

/**
 * 树状导航组件
 */
const TreeNavigation: React.FC<TreeNavigationProps> = ({
  treeData,
  navigationState,
  onNavigationChange,
  className = ''
}) => {
  const handleSelect = (selectedKeys: React.Key[], info: any) => {
    if (selectedKeys.length === 0) return;
    
    const selectedKey = selectedKeys[0] as string;
    const node = info.node;
    const nodeData = node.data;
    
    if (!nodeData) return;
    
    const newState = { ...navigationState };
    
    // 根据节点层级更新状态
    if (node.level === 1) {
      // API级别
      newState.selectedApiId = nodeData.api.id;
      newState.selectedTabKey = undefined;
      newState.selectedSubTabKey = undefined;
    } else if (node.level === 2) {
      // Tab级别
      newState.selectedApiId = nodeData.api.id;
      newState.selectedTabKey = nodeData.tabKey;
      newState.selectedSubTabKey = undefined;
    } else if (node.level === 3) {
      // SubTab级别
      newState.selectedApiId = nodeData.api.id;
      newState.selectedTabKey = nodeData.tabKey;
      newState.selectedSubTabKey = nodeData.subTabKey;
    }
    
    onNavigationChange(newState);
  };
  
  const handleExpand = (expandedKeys: React.Key[]) => {
    onNavigationChange({
      ...navigationState,
      expandedKeys: expandedKeys as string[]
    });
  };
  
  // 构建当前选中的key
  const getSelectedKeys = (): string[] => {
    const { selectedApiId, selectedTabKey, selectedSubTabKey } = navigationState;
    
    if (selectedSubTabKey && selectedTabKey && selectedApiId) {
      return [`${selectedApiId}-${selectedTabKey}-${selectedSubTabKey}`];
    } else if (selectedTabKey && selectedApiId) {
      return [`${selectedApiId}-${selectedTabKey}`];
    } else if (selectedApiId) {
      return [selectedApiId];
    }
    
    return [];
  };
  
  return (
    <div className={`tree-navigation ${className}`}>
      <Tree
        treeData={treeData}
        selectedKeys={getSelectedKeys()}
        expandedKeys={navigationState.expandedKeys}
        onSelect={handleSelect}
        onExpand={handleExpand}
        showIcon
        className="custom-tree"
      />
    </div>
  );
};

export default TreeNavigation;
