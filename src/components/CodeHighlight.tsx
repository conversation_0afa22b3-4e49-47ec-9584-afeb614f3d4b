import React from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Copy, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface CodeHighlightProps {
  code: string;
  language?: string;
  title?: string;
  onCopy?: () => void;
  showLineNumbers?: boolean;
  isPreview?: boolean; // 新增：用于首页预览的简化样式
}

const CodeHighlight: React.FC<CodeHighlightProps> = ({
  code,
  language = 'javascript',
  title,
  onCopy,
  showLineNumbers = true,
  isPreview = false
}) => {
  const [copied, setCopied] = React.useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(code);
    setCopied(true);
    onCopy?.();
    setTimeout(() => setCopied(false), 2000);
  };

  // 预览模式的简化样式
  if (isPreview) {
    return (
      <div className="relative bg-slate-50 rounded-lg border overflow-hidden">
        <div className="overflow-x-auto custom-scrollbar">
          <SyntaxHighlighter
            language={language}
            style={oneLight}
            customStyle={{
              margin: 0,
              padding: '12px',
              fontSize: '12px',
              lineHeight: '1.4',
              background: '#f8fafc',
              border: 'none',
              fontFamily: '"Fira Code", "JetBrains Mono", "SF Mono", Monaco, Inconsolata, "Roboto Mono", "Source Code Pro", monospace',
              whiteSpace: 'nowrap' // 防止代码换行，触发横向滚动
            }}
            showLineNumbers={false}
            wrapLines={false}
            wrapLongLines={false}
          >
            {code}
          </SyntaxHighlighter>
        </div>
      </div>
    );
  }

  return (
    <div className="relative bg-white rounded-lg border shadow-sm overflow-hidden">
      {title && (
        <div className="flex items-center justify-between px-4 py-2 bg-gray-50 border-b">
          <span className="text-sm font-medium text-gray-700">{title}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="h-6 px-2"
          >
            {copied ? (
              <Check className="w-3 h-3 text-green-600" />
            ) : (
              <Copy className="w-3 h-3" />
            )}
          </Button>
        </div>
      )}
      
      <div className="relative">
        <div className="overflow-x-auto custom-scrollbar">
          <SyntaxHighlighter
            language={language}
            style={oneLight}
            customStyle={{
              margin: 0,
              padding: '16px',
              fontSize: '14px',
              lineHeight: '1.5',
              background: '#ffffff',
              border: 'none',
              fontFamily: '"Fira Code", "JetBrains Mono", "SF Mono", Monaco, Inconsolata, "Roboto Mono", "Source Code Pro", monospace'
            }}
            showLineNumbers={showLineNumbers}
            wrapLines={true}
            wrapLongLines={true}
          >
            {code}
          </SyntaxHighlighter>
        </div>
        
        {!title && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="absolute top-2 right-2 h-6 px-2 bg-white/80 hover:bg-white shadow-sm"
          >
            {copied ? (
              <Check className="w-3 h-3 text-green-600" />
            ) : (
              <Copy className="w-3 h-3" />
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

export default CodeHighlight; 