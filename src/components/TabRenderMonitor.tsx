import React, { Component, ReactNode } from 'react';
import { <PERSON><PERSON>, Card, Typography, Tag, Button, Collapse } from 'antd';
import { ExclamationCircleOutlined, CheckCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;
const { Panel } = Collapse;

interface Props {
  children: ReactNode;
  tabName: string;
  tabIndex: number;
  apiName: string;
  onRenderSuccess?: (tabName: string) => void;
  onRenderError?: (tabName: string, error: Error) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

class TabRenderMonitor extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const { tabName, apiName, onRenderError } = this.props;

    console.group(`🚨 Tab渲染错误 - ${apiName}.${tabName}`);
    console.error('Tab名称:', tabName);
    console.error('API名称:', apiName);
    console.error('错误:', error);
    console.error('错误信息:', errorInfo);
    console.error('组件堆栈:', errorInfo.componentStack);
    console.groupEnd();

    // 调用错误回调
    if (onRenderError) {
      onRenderError(tabName, error);
    }
  }

  componentDidMount() {
    // 首次渲染成功
    if (!this.state.hasError && this.props.onRenderSuccess) {
      this.props.onRenderSuccess(this.props.tabName);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null
    });
  };

  render() {
    const { children, tabName, tabIndex } = this.props;
    const { hasError, error } = this.state;

    if (hasError && error) {
      return (
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
              <span>{tabIndex}. {tabName} Tab - 渲染失败</span>
              <Tag color="red">ERROR</Tag>
            </div>
          }
          style={{ marginBottom: '24px', border: '2px solid #ff4d4f' }}
        >
          <Alert
            message={`${tabName} Tab 渲染错误`}
            description={error.message}
            type="error"
            showIcon
            style={{ marginBottom: '16px' }}
            action={
              <Button size="small" onClick={this.handleRetry}>
                重试渲染
              </Button>
            }
          />

          <Collapse size="small">
            <Panel header="🔍 详细错误信息" key="error-details">
              <pre style={{
                background: '#f5f5f5',
                padding: '8px',
                borderRadius: '4px',
                fontSize: '11px',
                overflow: 'auto',
                maxHeight: '150px',
                border: '1px solid #d9d9d9'
              }}>
                {error.stack}
              </pre>
            </Panel>
          </Collapse>
        </Card>
      );
    }

    // 渲染成功的情况 - 简化显示
    return (
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
            <span>{tabIndex}. {tabName} Tab</span>
            <Tag color="green">OK</Tag>
          </div>
        }
        style={{ marginBottom: '24px' }}
      >
        {children}
      </Card>
    );
  }
}

export default TabRenderMonitor;
