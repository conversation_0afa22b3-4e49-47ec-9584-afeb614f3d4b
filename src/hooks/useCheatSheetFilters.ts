import { useState, useMemo, useCallback } from 'react';
import { ApiItem } from '@/types/api';

export interface SearchOptions {
  title: boolean;
  description: boolean;
  tags: boolean;
  syntax: boolean;
  example: boolean;
}

export interface FilterState {
  searchTerm: string;
  searchOptions: SearchOptions;
  selectedVersions: string[];
  selectedDifficulties: string[];
  selectedCategories: string[];
  sortBy: 'name' | 'difficulty' | 'version' | 'category';
  sortOrder: 'asc' | 'desc';
  showFavoritesOnly: boolean;
}

export interface UseCheatSheetFiltersProps {
  apiData: ApiItem[];
  favorites: string[];
  themeColor?: 'vue' | 'react' | 'nextjs' | 'typescript' | 'ecma' | 'default';
}

/**
 * CheatSheet过滤和搜索逻辑Hook
 */
export const useCheatSheetFilters = ({ 
  apiData, 
  favorites, 
  themeColor = 'default' 
}: UseCheatSheetFiltersProps) => {
  // 搜索状态
  const [searchTerm, setSearchTerm] = useState('');
  const [searchOptions, setSearchOptions] = useState<SearchOptions>({
    title: true,
    description: true,
    tags: true,
    syntax: false,
    example: false
  });

  // 过滤状态
  const [selectedVersions, setSelectedVersions] = useState<string[]>(['All']);
  const [selectedDifficulties, setSelectedDifficulties] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<FilterState['sortBy']>('name');
  const [sortOrder, setSortOrder] = useState<FilterState['sortOrder']>('asc');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

  // 记忆化计算版本列表（支持ECMAScript和React）
  const categories = useMemo(() => {
    const versionSet = new Set(apiData.map(item => {
      const version = item.version || '';
      
      // ECMAScript版本处理
      if (themeColor === 'ecma') {
        if (version.includes('2015') || version === 'ES6') return 'ES6 (2015)';
        if (version.includes('2016') || version === 'ES7') return 'ES2016';
        if (version.includes('2017') || version === 'ES8') return 'ES2017';
        if (version.includes('2018') || version === 'ES9') return 'ES2018';
        if (version.includes('2019') || version === 'ES10') return 'ES2019';
        if (version.includes('2020') || version === 'ES11') return 'ES2020';
        if (version.includes('2021') || version === 'ES12') return 'ES2021';
        if (version.includes('2022') || version === 'ES13') return 'ES2022';
        if (version.includes('2023') || version === 'ES14') return 'ES2023';
        return version || 'Other';
      }
      
      // React版本处理
      if (themeColor === 'react') {
        if (version.includes('16')) return 'React 16';
        if (version.includes('17')) return 'React 17';
        if (version.includes('18')) return 'React 18';
        if (version.includes('19')) return 'React 19';
        return version || 'Other';
      }
      
      return version || 'Other';
    }));
    
    return Array.from(versionSet).sort();
  }, [apiData, themeColor]);

  // 记忆化计算难度列表
  const difficulties = useMemo(() => {
    const difficultySet = new Set(apiData.map(item => item.difficulty).filter(Boolean));
    return Array.from(difficultySet).sort();
  }, [apiData]);

  // 记忆化计算分类列表
  const categoryList = useMemo(() => {
    const categorySet = new Set(apiData.map(item => item.category).filter(Boolean));
    return Array.from(categorySet).sort();
  }, [apiData]);

  // 记忆化计算过滤数据
  const filteredData = useMemo(() => {
    let filtered = apiData;

    // 版本过滤
    if (selectedVersions.length > 0 && !selectedVersions.includes('All')) {
      filtered = filtered.filter(item => {
        const version = item.version || '';
        return selectedVersions.some(selectedVersion => {
          if (themeColor === 'ecma') {
            // ECMAScript版本匹配
            if (selectedVersion === 'ES6 (2015)') {
              return version.includes('2015') || version === 'ES6';
            }
            if (selectedVersion === 'ES2016') {
              return version.includes('2016') || version === 'ES7';
            }
            if (selectedVersion === 'ES2017') {
              return version.includes('2017') || version === 'ES8';
            }
            if (selectedVersion === 'ES2018') {
              return version.includes('2018') || version === 'ES9';
            }
            // 其他版本直接匹配
            return version.includes(selectedVersion.replace('ES', ''));
          }
          
          if (themeColor === 'react') {
            // React版本匹配
            return version.includes(selectedVersion.replace('React ', ''));
          }
          
          return version === selectedVersion;
        });
      });
    }

    // 难度过滤
    if (selectedDifficulties.length > 0) {
      filtered = filtered.filter(item => 
        selectedDifficulties.includes(item.difficulty || '')
      );
    }

    // 分类过滤
    if (selectedCategories.length > 0) {
      filtered = filtered.filter(item => 
        selectedCategories.includes(item.category || '')
      );
    }

    // 收藏过滤
    if (showFavoritesOnly) {
      filtered = filtered.filter(item => favorites.includes(item.id));
    }

    // 搜索过滤
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(item => {
        const searchFields: string[] = [];
        
        if (searchOptions.title) searchFields.push(item.title.toLowerCase());
        if (searchOptions.description) searchFields.push(item.description.toLowerCase());
        if (searchOptions.tags) searchFields.push(...(item.tags || []).map(tag => tag.toLowerCase()));
        if (searchOptions.syntax) searchFields.push((item.syntax || '').toLowerCase());
        if (searchOptions.example) searchFields.push((item.example || '').toLowerCase());
        
        return searchFields.some(field => field.includes(term));
      });
    }

    // 排序
    filtered.sort((a, b) => {
      let aValue: string | number = '';
      let bValue: string | number = '';
      
      switch (sortBy) {
        case 'name':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'difficulty':
          const difficultyOrder = { 'easy': 1, 'medium': 2, 'hard': 3 };
          aValue = difficultyOrder[a.difficulty as keyof typeof difficultyOrder] || 0;
          bValue = difficultyOrder[b.difficulty as keyof typeof difficultyOrder] || 0;
          break;
        case 'version':
          aValue = a.version || '';
          bValue = b.version || '';
          break;
        case 'category':
          aValue = a.category || '';
          bValue = b.category || '';
          break;
      }
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else {
        return sortOrder === 'asc' 
          ? (aValue as number) - (bValue as number)
          : (bValue as number) - (aValue as number);
      }
    });

    return filtered;
  }, [
    apiData, 
    searchTerm, 
    searchOptions, 
    selectedVersions, 
    selectedDifficulties, 
    selectedCategories, 
    sortBy, 
    sortOrder, 
    showFavoritesOnly, 
    favorites, 
    themeColor
  ]);

  // 搜索选项处理函数
  const handleSearchOptionChange = useCallback((option: keyof SearchOptions, checked: boolean) => {
    setSearchOptions(prev => ({
      ...prev,
      [option]: checked
    }));
  }, []);

  const getActiveSearchCount = useCallback(() => {
    return Object.values(searchOptions).filter(Boolean).length;
  }, [searchOptions]);

  const setAllSearchOptions = useCallback((value: boolean) => {
    const newOptions = Object.keys(searchOptions).reduce((acc, key) => {
      acc[key as keyof SearchOptions] = value;
      return acc;
    }, {} as SearchOptions);
    setSearchOptions(newOptions);
  }, [searchOptions]);

  // 重置所有过滤器
  const resetFilters = useCallback(() => {
    setSearchTerm('');
    setSelectedVersions(['All']);
    setSelectedDifficulties([]);
    setSelectedCategories([]);
    setSortBy('name');
    setSortOrder('asc');
    setShowFavoritesOnly(false);
    setSearchOptions({
      title: true,
      description: true,
      tags: true,
      syntax: false,
      example: false
    });
  }, []);

  return {
    // 状态
    searchTerm,
    searchOptions,
    selectedVersions,
    selectedDifficulties,
    selectedCategories,
    sortBy,
    sortOrder,
    showFavoritesOnly,
    
    // 计算值
    filteredData,
    categories,
    difficulties,
    categoryList,
    
    // 操作函数
    setSearchTerm,
    setSearchOptions,
    setSelectedVersions,
    setSelectedDifficulties,
    setSelectedCategories,
    setSortBy,
    setSortOrder,
    setShowFavoritesOnly,
    handleSearchOptionChange,
    getActiveSearchCount,
    setAllSearchOptions,
    resetFilters
  };
};
