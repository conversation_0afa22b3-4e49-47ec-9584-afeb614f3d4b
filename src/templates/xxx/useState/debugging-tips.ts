import { DebuggingTips } from '@/types/api';

export const debuggingTips: DebuggingTips = {
  commonErrors: [
    {
      error: "状态更新后立即读取状态值仍是旧值",
      cause: "setState是异步的，状态更新不会立即反映",
      solution: "使用useEffect监听状态变化，或在下一次渲染中访问新值",
      code: `// ❌ 错误示例
function Counter() {
  const [count, setCount] = useState(0);
  
  const handleClick = () => {
    setCount(count + 1);
    console.log(count); // 仍然是旧值！
    
    // 试图基于"更新后"的值再次更新
    if (count === 5) { // 这里的判断可能不符合预期
      alert('Count is 5!');
    }
  };
}

// ✅ 正确做法1：使用useEffect
function Counter() {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    console.log('Count updated:', count);
    if (count === 5) {
      alert('Count is 5!');
    }
  }, [count]);
  
  const handleClick = () => {
    setCount(count + 1);
  };
}

// ✅ 正确做法2：使用函数式更新
function Counter() {
  const [count, setCount] = useState(0);
  
  const handleClick = () => {
    setCount(prev => {
      const newCount = prev + 1;
      console.log('New count:', newCount);
      if (newCount === 5) {
        alert('Count is 5!');
      }
      return newCount;
    });
  };
}`
    },
    {
      error: "在循环或条件语句中调用useState",
      cause: "违反了Hooks的调用规则，导致Hook调用顺序不一致",
      solution: "将useState调用移到组件顶层，使用状态或条件渲染代替",
      code: `// ❌ 错误：条件调用
function Component({ shouldTrack }) {
  if (shouldTrack) {
    const [count, setCount] = useState(0); // 错误！
  }
}

// ❌ 错误：循环调用
function TodoList({ items }) {
  for (let i = 0; i < items.length; i++) {
    const [checked, setChecked] = useState(false); // 错误！
  }
}

// ✅ 正确：使用数组或对象管理多个状态
function TodoList({ items }) {
  const [checkedStates, setCheckedStates] = useState(
    () => new Array(items.length).fill(false)
  );
  
  const toggleCheck = (index) => {
    setCheckedStates(prev => {
      const newStates = [...prev];
      newStates[index] = !newStates[index];
      return newStates;
    });
  };
}`
    },
    {
      error: "直接修改状态对象或数组",
      cause: "React通过Object.is比较新旧状态，直接修改不会触发重渲染",
      solution: "创建新的对象或数组",
      code: `// ❌ 错误：直接修改对象
function UserProfile() {
  const [user, setUser] = useState({ name: 'John', age: 25 });
  
  const updateAge = () => {
    user.age = 26; // 直接修改
    setUser(user); // React认为是同一个对象，不会重渲染
  };
}

// ❌ 错误：直接修改数组
function TodoList() {
  const [todos, setTodos] = useState([1, 2, 3]);
  
  const addTodo = () => {
    todos.push(4); // 直接修改
    setTodos(todos); // 不会触发重渲染
  };
}

// ✅ 正确：创建新对象
function UserProfile() {
  const [user, setUser] = useState({ name: 'John', age: 25 });
  
  const updateAge = () => {
    setUser({ ...user, age: 26 }); // 创建新对象
    // 或者
    setUser(prev => ({ ...prev, age: 26 }));
  };
}

// ✅ 正确：创建新数组
function TodoList() {
  const [todos, setTodos] = useState([1, 2, 3]);
  
  const addTodo = () => {
    setTodos([...todos, 4]); // 创建新数组
    // 或者
    setTodos(prev => [...prev, 4]);
  };
}`
    },
    {
      error: "异步操作中的状态更新导致警告",
      cause: "组件卸载后仍然尝试更新状态",
      solution: "在useEffect中正确清理异步操作",
      code: `// ❌ 错误：可能导致内存泄漏警告
function UserData() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    fetchUserData().then(result => {
      setData(result); // 如果组件已卸载，会报警告
    });
  }, []);
}

// ✅ 正确：使用清理函数
function UserData() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    let cancelled = false;
    
    fetchUserData().then(result => {
      if (!cancelled) {
        setData(result);
      }
    });
    
    return () => {
      cancelled = true;
    };
  }, []);
}

// ✅ 更好的方案：使用AbortController
function UserData() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    const controller = new AbortController();
    
    fetch('/api/user', { signal: controller.signal })
      .then(res => res.json())
      .then(setData)
      .catch(err => {
        if (err.name !== 'AbortError') {
          console.error(err);
        }
      });
    
    return () => controller.abort();
  }, []);
}`
    }
  ],
  
  devToolsTips: [
    {
      tool: "React DevTools",
      technique: "使用React DevTools查看和修改状态",
      example: `// 1. 安装React DevTools浏览器扩展
// 2. 打开开发者工具，切换到React标签
// 3. 在组件树中找到目标组件
// 4. 在右侧面板查看State
// 5. 可以直接修改状态值进行调试

// 给状态添加调试标签
function useDebugState(initialValue, name) {
  const [value, setValue] = useState(initialValue);
  
  // 在开发环境显示状态名称
  if (process.env.NODE_ENV === 'development') {
    useDebugValue(name + ': ' + JSON.stringify(value));
  }
  
  return [value, setValue];
}

// 使用
function Counter() {
  const [count, setCount] = useDebugState(0, 'counter');
  // 在DevTools中会显示 "counter: 0"
}`
    },
    {
      tool: "console.log调试",
      technique: "策略性地放置console.log来跟踪状态变化",
      example: `function ComplexComponent() {
  const [state, setState] = useState({ count: 0, text: '' });
  
  // 调试：跟踪每次渲染
  console.log('Render with state:', state);
  
  // 调试：在更新函数中查看新旧值
  const updateCount = () => {
    setState(prev => {
      console.log('Previous state:', prev);
      const next = { ...prev, count: prev.count + 1 };
      console.log('Next state:', next);
      return next;
    });
  };
  
  // 调试：使用useEffect监视特定状态
  useEffect(() => {
    console.log('Count changed to:', state.count);
  }, [state.count]);
}`
    },
    {
      tool: "自定义Hook调试",
      technique: "创建调试版本的useState",
      example: `// 创建一个带日志的useState
function useStateWithLog(initialValue, name = 'state') {
  const [value, setValue] = useState(initialValue);
  
  // 记录初始值
  useEffect(() => {
    console.log("[" + name + "] Initial:", initialValue);
  }, []);
  
  // 包装setState函数
  const setValueWithLog = useCallback((newValue) => {
    setValue(prevValue => {
      const nextValue = typeof newValue === 'function' 
        ? newValue(prevValue) 
        : newValue;
      
      console.log("[" + name + "] Update:", {
        from: prevValue,
        to: nextValue,
        timestamp: new Date().toISOString()
      });
      
      return nextValue;
    });
  }, [name]);
  
  return [value, setValueWithLog];
}

// 使用示例
function App() {
  const [user, setUser] = useStateWithLog({ name: 'John' }, 'user');
  const [count, setCount] = useStateWithLog(0, 'count');
  
  // 所有状态更新都会被记录
}`
    }
  ],
  
  troubleshooting: [
    {
      symptom: "组件不断重渲染（无限循环）",
      possibleCauses: [
        "在渲染过程中直接调用setState",
        "useEffect依赖项设置错误",
        "每次渲染都创建新的对象/数组作为状态"
      ],
      solutions: [
        "将setState调用移到事件处理器或useEffect中",
        "检查useEffect的依赖数组",
        "使用useMemo或useCallback稳定引用"
      ]
    },
    {
      symptom: "状态更新不生效",
      possibleCauses: [
        "直接修改了状态对象/数组",
        "闭包陷阱导致访问旧状态",
        "多个setState被批处理合并"
      ],
      solutions: [
        "确保创建新的对象/数组",
        "使用函数式更新",
        "理解React的批处理机制"
      ]
    },
    {
      symptom: "状态丢失或重置",
      possibleCauses: [
        "组件的key属性变化导致重新挂载",
        "父组件重渲染导致子组件重新创建",
        "条件渲染导致组件卸载"
      ],
      solutions: [
        "检查组件的key属性",
        "使用React.memo优化父组件渲染",
        "将状态提升到稳定的父组件"
      ]
    }
  ]
};

export default debuggingTips; 