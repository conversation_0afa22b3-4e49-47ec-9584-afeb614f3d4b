import { CommonQuestion } from '@/types/api';

export const commonQuestions: CommonQuestion[] = [
  {
    question: '为什么连续调用setState，只有最后一个生效？',
    tags: ['异步更新', '批处理', '闭包'],
    answer: `**原因：**
这是由于React的批处理机制和闭包问题导致的。

**问题分析：**
1. setState是异步的，不会立即更新状态
2. 多个setState调用会被批量处理
3. 如果使用当前状态值，会受到闭包影响

**解决方案：**
使用函数式更新，接收前一个状态作为参数`,
    code: `// ❌ 错误方式 - 只会增加1
const [count, setCount] = useState(0);

const increment = () => {
  setCount(count + 1); // count仍然是0
  setCount(count + 1); // count仍然是0
  setCount(count + 1); // count仍然是0
  // 最终结果：1
};

// ✅ 正确方式 - 会增加3
const incrementCorrect = () => {
  setCount(prev => prev + 1); // prev是最新值
  setCount(prev => prev + 1); // prev是更新后的值
  setCount(prev => prev + 1); // prev是再次更新后的值
  // 最终结果：3
};`
  },
  
  {
    question: '如何在useState中处理复杂对象的更新？',
    tags: ['对象更新', '不可变性', '扩展运算符'],
    answer: `**核心原则：**
React使用Object.is()比较状态，直接修改对象不会触发重新渲染。

**最佳实践：**
1. 始终创建新对象
2. 使用扩展运算符进行浅拷贝
3. 对于深层嵌套，考虑使用useReducer或状态管理库`,
    code: `const [user, setUser] = useState({
  name: 'John',
  age: 25,
  address: {
    city: 'Beijing',
    street: 'Main St'
  }
});

// ❌ 错误方式 - 直接修改
const updateUserWrong = () => {
  user.name = 'Jane'; // 不会触发重新渲染
  setUser(user);
};

// ✅ 正确方式 - 浅层更新
const updateUser = () => {
  setUser(prev => ({
    ...prev,
    name: 'Jane'
  }));
};

// ✅ 深层更新
const updateAddress = () => {
  setUser(prev => ({
    ...prev,
    address: {
      ...prev.address,
      city: 'Shanghai'
    }
  }));
};`
  }
]; 