import { ApiItem } from '@/types/api';
import { basicInfo } from './basic-info';
import { businessScenarios } from './business-scenarios';
import { implementation } from './implementation';
import interviewQuestions from './interview-questions';
import { commonQuestions } from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';

const useStateApi: ApiItem = {
  id: 'use-state',
  title: 'useState()',
  description: '管理组件的局部状态，返回状态值和更新函数',
  category: 'React Hooks',
  difficulty: 'easy',
  syntax: 'const [state, setState] = useState(initialState)',
  example: `import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>
        Increment
      </button>
      <button onClick={() => setCount(prev => prev - 1)}>
        Decrement
      </button>
    </div>
  );
}

export default Counter;`,
  notes: '注意：setState是异步的，使用函数式更新来避免闭包问题',
  isNew: false,
  version: 'React 16.8+',
  tags: ['状态管理', '函数组件', 'Hook'],
  
  // 扩展内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology
};

export default useStateApi; 