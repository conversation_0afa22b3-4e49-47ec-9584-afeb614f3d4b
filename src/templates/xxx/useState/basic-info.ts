import { BasicInfo } from '@/types/api';

export const basicInfo: BasicInfo = {
  introduction: 'useState 是 React 中最基础的 Hook，用于在函数组件中添加状态管理能力。它接收一个初始状态值，返回一个数组，包含当前状态值和更新状态的函数。',
  syntax: 'const [state, setState] = useState(initialValue)',
  parameters: [
    {
      name: 'initialValue',
      type: 'any | () => any',
      description: '初始状态值，可以是任意类型的值或返回初始值的函数',
      required: false,
      default: 'undefined'
    }
  ],
  returnValue: {
    type: '[any, (newValue: any | (prev: any) => any) => void]',
    description: '返回一个数组，第一个元素是当前状态值，第二个元素是更新状态的函数'
  },
  limitations: [
    '状态更新是异步的，不会立即反映在当前渲染中',
    '直接修改状态对象不会触发重新渲染，需要创建新对象',
    '在严格模式下，初始化函数会被调用两次',
    '状态更新会导致组件重新渲染，需要注意性能优化'
  ]
}; 