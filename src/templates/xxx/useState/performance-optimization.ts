import { PerformanceOptimization } from '@/types/api';

export const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: "状态更新优化",
      description: "优化useState的状态更新，避免不必要的重渲染",
      techniques: [
        {
          name: "使用函数式更新",
          description: "当新状态依赖于旧状态时，使用函数式更新可以避免闭包陷阱和批量更新问题",
          impact: "high",
          difficulty: "easy",
          code: `// ❌ 错误：直接使用状态值
function Counter() {
  const [count, setCount] = useState(0);
  
  const handleMultipleClicks = () => {
    // 这三次更新会被合并，只增加1
    setCount(count + 1);
    setCount(count + 1);
    setCount(count + 1);
  };
}

// ✅ 正确：使用函数式更新
function Counter() {
  const [count, setCount] = useState(0);
  
  const handleMultipleClicks = () => {
    // 每次更新都基于最新值，正确增加3
    setCount(prev => prev + 1);
    setCount(prev => prev + 1);
    setCount(prev => prev + 1);
  };
}`
        },
        {
          name: "状态拆分",
          description: "将相关的状态分组，不相关的状态分离，减少更新范围",
          impact: "medium",
          difficulty: "easy",
          code: `// ❌ 错误：所有状态放在一起
function UserProfile() {
  const [state, setState] = useState({
    user: null,
    posts: [],
    comments: [],
    isLoading: false,
    error: null
  });
  
  // 更新任何字段都会触发整体重渲染
  const updateUser = (user) => {
    setState({ ...state, user });
  };
}

// ✅ 正确：状态拆分
function UserProfile() {
  const [user, setUser] = useState(null);
  const [posts, setPosts] = useState([]);
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // 只更新需要的部分
  const updateUser = (newUser) => {
    setUser(newUser);
  };
}`
        },
        {
          name: "惰性初始化",
          description: "对于计算成本高的初始值，使用函数进行惰性初始化",
          impact: "high",
          difficulty: "easy",
          code: `// ❌ 错误：每次渲染都会执行昂贵计算
function ExpensiveComponent() {
  // computeExpensiveValue在每次渲染时都会执行
  const [value, setValue] = useState(computeExpensiveValue());
}

// ✅ 正确：惰性初始化
function ExpensiveComponent() {
  // computeExpensiveValue只在首次渲染时执行
  const [value, setValue] = useState(() => computeExpensiveValue());
}

// 实际示例：从localStorage读取初始值
function Settings() {
  const [theme, setTheme] = useState(() => {
    // 这个函数只在组件挂载时执行一次
    const saved = localStorage.getItem('theme');
    return saved || 'light';
  });
}`
        }
      ]
    },
    {
      title: "避免不必要的重渲染",
      description: "通过合理的状态设计和更新策略，减少组件重渲染次数",
      techniques: [
        {
          name: "状态提升vs状态下沉",
          description: "根据状态使用范围，决定状态应该放在哪个层级",
          impact: "high",
          difficulty: "medium",
          code: `// ❌ 错误：状态提升过高
function App() {
  const [searchTerm, setSearchTerm] = useState('');
  
  return (
    <>
      <Header />
      <SearchBar value={searchTerm} onChange={setSearchTerm} />
      <Content />
      <Footer />
    </>
  );
  // searchTerm变化会导致整个App重渲染
}

// ✅ 正确：状态下沉到使用它的组件
function App() {
  return (
    <>
      <Header />
      <SearchSection /> {/* 状态在这里面 */}
      <Content />
      <Footer />
    </>
  );
}

function SearchSection() {
  const [searchTerm, setSearchTerm] = useState('');
  // 只有SearchSection会重渲染
}`
        },
        {
          name: "使用多个useState vs useReducer",
          description: "当状态逻辑复杂时，考虑使用useReducer替代多个useState",
          impact: "medium",
          difficulty: "medium",
          code: `// ❌ 多个相关的useState可能导致更新不同步
function Form() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleSubmit = async () => {
    setIsSubmitting(true);
    setErrors({});
    // 多个setState调用
    try {
      await api.submit({ name, email });
    } catch (err) {
      setErrors(err.errors);
    } finally {
      setIsSubmitting(false);
    }
  };
}

// ✅ 使用useReducer管理复杂状态
function Form() {
  const [state, dispatch] = useReducer(formReducer, {
    name: '',
    email: '',
    errors: {},
    isSubmitting: false
  });
  
  const handleSubmit = async () => {
    dispatch({ type: 'SUBMIT_START' });
    try {
      await api.submit({ name: state.name, email: state.email });
      dispatch({ type: 'SUBMIT_SUCCESS' });
    } catch (err) {
      dispatch({ type: 'SUBMIT_ERROR', errors: err.errors });
    }
  };
}`
        }
      ]
    },
    {
      title: "内存优化",
      description: "避免内存泄漏和优化内存使用",
      techniques: [
        {
          name: "避免在状态中存储派生数据",
          description: "可以通过计算得出的数据不应该存储在状态中",
          impact: "medium",
          difficulty: "easy",
          code: `// ❌ 错误：存储派生数据
function TodoList() {
  const [todos, setTodos] = useState([]);
  const [completedCount, setCompletedCount] = useState(0);
  const [activeCount, setActiveCount] = useState(0);
  
  const addTodo = (todo) => {
    setTodos([...todos, todo]);
    // 需要手动同步更新派生数据
    if (todo.completed) {
      setCompletedCount(completedCount + 1);
    } else {
      setActiveCount(activeCount + 1);
    }
  };
}

// ✅ 正确：计算派生数据
function TodoList() {
  const [todos, setTodos] = useState([]);
  
  // 每次渲染时计算，保证数据一致性
  const completedCount = todos.filter(t => t.completed).length;
  const activeCount = todos.filter(t => !t.completed).length;
  
  const addTodo = (todo) => {
    setTodos([...todos, todo]);
  };
}`
        },
        {
          name: "清理大对象引用",
          description: "在组件卸载或状态更新时，清理不再需要的大对象引用",
          impact: "low",
          difficulty: "medium",
          code: `// 处理大文件上传的组件
function FileUploader() {
  const [file, setFile] = useState(null);
  const [preview, setPreview] = useState(null);
  
  const handleFileSelect = (e) => {
    const selectedFile = e.target.files[0];
    setFile(selectedFile);
    
    // 创建预览URL
    if (selectedFile && selectedFile.type.startsWith('image/')) {
      const url = URL.createObjectURL(selectedFile);
      setPreview(url);
    }
  };
  
  // 清理资源
  useEffect(() => {
    return () => {
      if (preview) {
        URL.revokeObjectURL(preview);
      }
    };
  }, [preview]);
  
  const handleClear = () => {
    // 清理状态时释放资源
    if (preview) {
      URL.revokeObjectURL(preview);
    }
    setFile(null);
    setPreview(null);
  };
}`
        }
      ]
    }
  ],
  
  performanceMetrics: {
    renderCount: {
      tool: "React DevTools Profiler",
      description: "监控组件渲染次数",
      example: "使用React DevTools的Profiler面板查看组件渲染频率和耗时"
    },
    stateUpdateBatching: {
      tool: "React 18 自动批处理",
      description: "React 18自动批处理多个状态更新",
      example: "在事件处理器中的多个setState会被自动批处理为一次重渲染"
    }
  },
  
  bestPractices: [
    "优先使用函数式更新，特别是在异步操作中",
    "将相关状态组合在一起，不相关的状态分离",
    "对计算成本高的初始值使用惰性初始化",
    "避免在状态中存储可以计算得出的值",
    "合理选择状态的存储位置（提升vs下沉）",
    "考虑使用useReducer管理复杂的状态逻辑"
  ],
  
  commonPitfalls: [
    {
      issue: "直接修改状态对象",
      cause: "JavaScript对象是引用类型，直接修改不会触发重渲染",
      solution: "始终创建新对象：setState({...state, key: value})"
    },
    {
      issue: "在循环中调用setState",
      cause: "可能导致过多的重渲染或状态更新丢失",
      solution: "收集所有更新，一次性更新状态"
    },
    {
      issue: "异步操作中的闭包陷阱",
      cause: "异步回调中访问的是旧的状态值",
      solution: "使用函数式更新或useRef保存最新值"
    }
  ]
};

export default performanceOptimization; 