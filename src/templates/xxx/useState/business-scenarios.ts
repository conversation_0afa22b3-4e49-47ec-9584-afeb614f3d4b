import { BusinessScenario } from '@/types/api';

export const businessScenarios: BusinessScenario[] = [
  {
    title: '计数器组件',
    description: '最基础的状态管理示例，演示useState的基本用法和状态更新',
    difficulty: 'easy',
    tags: ['基础用法', '状态更新', '事件处理'],
    code: `import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);

  const increment = () => setCount(count + 1);
  const decrement = () => setCount(prev => prev - 1);
  const reset = () => setCount(0);

  return (
    <div className="counter">
      <h2>计数器: {count}</h2>
      <div className="controls">
        <button onClick={increment}>+1</button>
        <button onClick={decrement}>-1</button>
        <button onClick={reset}>重置</button>
      </div>
      <p>当前计数: {count}</p>
    </div>
  );
}

export default Counter;`,
    explanation: `这个计数器示例展示了useState的基本使用模式：

**核心概念：**
- 状态声明和初始化
- 两种状态更新方式：直接值和函数式更新
- 状态变化触发组件重新渲染

**最佳实践：**
- 使用函数式更新避免闭包陷阱
- 将相关操作封装成函数
- 保持状态结构简单清晰`
  },
  
  {
    title: '表单输入管理',
    description: '管理复杂表单状态，包括多个输入字段、验证和提交处理',
    difficulty: 'medium',
    tags: ['表单处理', '对象状态', '验证'],
    code: `import React, { useState } from 'react';

function UserForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    age: '',
    password: ''
  });
  
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = '姓名不能为空';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = '邮箱不能为空';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = '邮箱格式不正确';
    }
    
    if (!formData.age || formData.age < 18) {
      newErrors.age = '年龄必须大于18岁';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('注册成功！');
      
      // 重置表单
      setFormData({
        name: '',
        email: '',
        age: '',
        password: ''
      });
    } catch (error) {
      alert('注册失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label>姓名:</label>
        <input
          type="text"
          name="name"
          value={formData.name}
          onChange={handleChange}
          disabled={isSubmitting}
        />
        {errors.name && <span className="error">{errors.name}</span>}
      </div>
      
      <div>
        <label>邮箱:</label>
        <input
          type="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
          disabled={isSubmitting}
        />
        {errors.email && <span className="error">{errors.email}</span>}
      </div>
      
      <div>
        <label>年龄:</label>
        <input
          type="number"
          name="age"
          value={formData.age}
          onChange={handleChange}
          disabled={isSubmitting}
        />
        {errors.age && <span className="error">{errors.age}</span>}
      </div>
      
      <button type="submit" disabled={isSubmitting}>
        {isSubmitting ? '提交中...' : '提交'}
      </button>
    </form>
  );
}

export default UserForm;`,
    explanation: `表单管理案例展示了useState在复杂场景中的应用：

**设计模式：**
- 使用对象状态管理多个相关字段
- 分离数据状态和UI状态（loading、errors）
- 通过展开运算符实现不可变更新

**实际应用：**
- 实时验证和错误提示
- 异步提交处理
- 用户体验优化（禁用状态、加载提示）
- 表单重置和状态清理`
  },
  
  {
    title: '待办事项管理',
    description: '复杂的列表状态管理，包括增删改查、过滤、本地存储等功能',
    difficulty: 'hard',
    tags: ['列表管理', '本地存储', '过滤搜索'],
    code: `import React, { useState, useEffect } from 'react';

function TodoApp() {
  const [todos, setTodos] = useState([]);
  const [newTodo, setNewTodo] = useState('');
  const [filter, setFilter] = useState('all'); // all, active, completed
  const [editingId, setEditingId] = useState(null);
  const [editingText, setEditingText] = useState('');

  // 从本地存储加载数据
  useEffect(() => {
    const savedTodos = localStorage.getItem('todos');
    if (savedTodos) {
      setTodos(JSON.parse(savedTodos));
    }
  }, []);

  // 保存到本地存储
  useEffect(() => {
    localStorage.setItem('todos', JSON.stringify(todos));
  }, [todos]);

  const addTodo = () => {
    if (newTodo.trim()) {
      const todo = {
        id: Date.now(),
        text: newTodo.trim(),
        completed: false,
        createdAt: new Date().toISOString()
      };
      
      setTodos(prev => [...prev, todo]);
      setNewTodo('');
    }
  };

  const deleteTodo = (id) => {
    setTodos(prev => prev.filter(todo => todo.id !== id));
  };

  const toggleTodo = (id) => {
    setTodos(prev => prev.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  };

  const startEditing = (id, text) => {
    setEditingId(id);
    setEditingText(text);
  };

  const saveEdit = () => {
    if (editingText.trim()) {
      setTodos(prev => prev.map(todo =>
        todo.id === editingId 
          ? { ...todo, text: editingText.trim() }
          : todo
      ));
    }
    setEditingId(null);
    setEditingText('');
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditingText('');
  };

  const clearCompleted = () => {
    setTodos(prev => prev.filter(todo => !todo.completed));
  };

  const filteredTodos = todos.filter(todo => {
    switch (filter) {
      case 'active':
        return !todo.completed;
      case 'completed':
        return todo.completed;
      default:
        return true;
    }
  });

  const stats = {
    total: todos.length,
    active: todos.filter(todo => !todo.completed).length,
    completed: todos.filter(todo => todo.completed).length
  };

  return (
    <div className="todo-app">
      <h1>待办事项</h1>
      
      {/* 添加新待办 */}
      <div className="add-todo">
        <input
          type="text"
          value={newTodo}
          onChange={(e) => setNewTodo(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && addTodo()}
          placeholder="添加新的待办事项..."
        />
        <button onClick={addTodo}>添加</button>
      </div>

      {/* 过滤器 */}
      <div className="filters">
        <button 
          className={filter === 'all' ? 'active' : ''}
          onClick={() => setFilter('all')}
        >
          全部 ({stats.total})
        </button>
        <button 
          className={filter === 'active' ? 'active' : ''}
          onClick={() => setFilter('active')}
        >
          待完成 ({stats.active})
        </button>
        <button 
          className={filter === 'completed' ? 'active' : ''}
          onClick={() => setFilter('completed')}
        >
          已完成 ({stats.completed})
        </button>
      </div>

      {/* 待办列表 */}
      <ul className="todo-list">
        {filteredTodos.map(todo => (
          <li key={todo.id} className={todo.completed ? 'completed' : ''}>
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => toggleTodo(todo.id)}
            />
            
            {editingId === todo.id ? (
              <div className="editing">
                <input
                  type="text"
                  value={editingText}
                  onChange={(e) => setEditingText(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') saveEdit();
                    if (e.key === 'Escape') cancelEdit();
                  }}
                  autoFocus
                />
                <button onClick={saveEdit}>保存</button>
                <button onClick={cancelEdit}>取消</button>
              </div>
            ) : (
              <div className="todo-content">
                <span 
                  onDoubleClick={() => startEditing(todo.id, todo.text)}
                  className="todo-text"
                >
                  {todo.text}
                </span>
                <div className="todo-actions">
                  <button onClick={() => startEditing(todo.id, todo.text)}>
                    编辑
                  </button>
                  <button onClick={() => deleteTodo(todo.id)}>
                    删除
                  </button>
                </div>
              </div>
            )}
          </li>
        ))}
      </ul>

      {/* 批量操作 */}
      {stats.completed > 0 && (
        <div className="bulk-actions">
          <button onClick={clearCompleted}>
            清除已完成 ({stats.completed})
          </button>
        </div>
      )}
    </div>
  );
}

export default TodoApp;`,
    explanation: `待办事项应用展示了useState在复杂应用中的综合运用：

**架构设计：**
- 多个相关状态的协调管理
- 状态更新的不可变模式
- 组件状态与外部存储的同步

**高级特性：**
- 条件渲染和动态交互
- 本地存储集成
- 复杂的状态更新逻辑
- 性能优化考虑

**业务价值：**
- 完整的CRUD操作实现
- 用户友好的交互体验
- 数据持久化和状态恢复
- 可扩展的架构设计`
  }
]; 