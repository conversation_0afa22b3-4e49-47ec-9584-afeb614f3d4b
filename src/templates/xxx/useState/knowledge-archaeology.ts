import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  background: `**React Hooks 的诞生背景**

2018年，React团队在React Conf上首次提出了Hooks概念，这是React发展史上的一个重要转折点。

**技术痛点和动机：**
- 类组件代码复杂，难以复用状态逻辑
- 生命周期方法分散，相关逻辑被拆分到不同方法中
- 高阶组件和render props模式导致"包装地狱"
- 类组件的this绑定问题和学习成本高

**设计目标：**
- 让函数组件具备状态管理能力
- 提供更简洁的状态逻辑复用方式
- 减少样板代码，提高开发效率
- 保持向后兼容，渐进式迁移`,

  evolution: `**Hooks 演进历程**

**React 16.8 (2019年2月) - Hooks正式发布：**
- useState - 基础状态管理
- useEffect - 副作用处理
- useContext - 上下文消费
- useReducer - 复杂状态管理
- useMemo, useCallback - 性能优化

**React 16.9-17.x - 稳定发展期：**
- 修复边缘情况和性能问题
- 完善开发者工具支持
- 社区生态快速发展

**React 18 (2022年3月) - 并发特性：**
- useId - 服务端渲染优化
- useDeferredValue - 延迟状态更新
- useTransition - 过渡状态管理
- useSyncExternalStore - 外部状态同步

**React 19 (2024年) - 最新发展：**
- use - 异步数据获取
- useOptimistic - 乐观更新
- useFormStatus/useFormState - 表单状态管理`,

  comparisons: `**与其他状态管理方案对比**

**useState vs 类组件 state：**
\`\`\`javascript
// 类组件 - 传统方式
class Counter extends React.Component {
  constructor(props) {
    super(props);
    this.state = { count: 0 };
    this.increment = this.increment.bind(this);
  }
  
  increment() {
    this.setState({ count: this.state.count + 1 });
  }
  
  render() {
    return <button onClick={this.increment}>{this.state.count}</button>;
  }
}

// Hooks - 现代方式
function Counter() {
  const [count, setCount] = useState(0);
  return <button onClick={() => setCount(count + 1)}>{count}</button>;
}
\`\`\`

**与Vue Composition API对比：**
\`\`\`javascript
// React useState
const [count, setCount] = useState(0);

// Vue ref
const count = ref(0);
\`\`\`

**与Angular Signal对比：**
\`\`\`javascript
// React - 显式更新
const [count, setCount] = useState(0);
setCount(count + 1);

// Angular - 响应式更新
const count = signal(0);
count.set(count() + 1);
\`\`\``,

  philosophy: `**设计哲学和核心理念**

**1. 函数式编程思维**
Hooks体现了React向函数式编程范式的转移：
- 纯函数组件更容易测试和调试
- 状态更新基于不可变性原则
- 副作用分离，逻辑更清晰

**2. 组合优于继承**
\`\`\`javascript
// 可组合的自定义Hook
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  const increment = () => setCount(c => c + 1);
  const decrement = () => setCount(c => c - 1);
  return { count, increment, decrement };
}
\`\`\`

**3. 声明式状态管理**
- 描述"是什么"而不是"怎么做"
- 状态变化通过声明式的方式表达
- 减少命令式的DOM操作

**4. 最小惊讶原则**
- API设计简洁直观
- 行为可预测，减少学习成本
- 错误提示清晰明了`,

  presentValue: `**现实应用价值和影响**

**1. 开发效率提升**
- 减少70%的样板代码
- 状态逻辑复用更简单
- 组件拆分更灵活

**2. 性能优化能力**
- 细粒度的状态更新
- 避免不必要的重新渲染
- 更好的代码分割支持

**3. 团队协作改善**
- 代码结构更一致
- 新人学习成本降低
- 代码审查更容易

**4. 生态系统影响**
- 促进了React生态的发展
- 影响了其他框架的设计（Vue 3 Composition API）
- 推动了前端状态管理的演进

**5. 企业级应用价值**
- 大型项目维护成本降低
- 技术债务减少
- 招聘和培训成本优化

**6. 技术发展趋势**
- 推动了Suspense、并发渲染等新特性
- 为服务端组件和流式渲染奠定基础
- 影响了Web开发的整体方向`
}; 

export default knowledgeArchaeology;
