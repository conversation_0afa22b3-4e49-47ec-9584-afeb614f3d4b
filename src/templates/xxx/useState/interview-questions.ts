import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 'useState-working-principle',
    question: 'useState的工作原理是什么？setState为什么是异步的？',
    difficulty: 'high',
    frequency: 'high',
    category: 'React原理',
    answer: {
      brief: 'useState基于React Fiber架构实现，通过Hook链表存储状态，setState是异步的主要是为了性能优化、保持一致性和支持可中断渲染。',
      detailed: `**useState工作原理：**

1. **Hook链表存储：** 每个组件的Hook按调用顺序存储在链表中
2. **状态更新队列：** setState不会立即修改状态，而是创建更新对象加入队列
3. **批量处理：** React在合适时机批量处理所有状态更新
4. **重新渲染：** 状态更新完成后触发组件重新渲染

**为什么setState是异步的：**

**性能优化：**
- 批量处理多个状态更新，减少重渲染次数
- 避免在单次事件处理中多次更新DOM

**保持一致性：**
- 确保子组件看到的props和state是一致的
- 避免在渲染过程中状态发生变化

**可中断渲染：**
- 支持React的时间切片机制
- 允许高优先级任务中断低优先级更新`,
      code: `// 演示setState的异步特性
function AsyncStateDemo() {
  const [count, setCount] = useState(0);
  
  const handleClick = () => {
    console.log('点击前:', count); // 0
    
    setCount(count + 1);
    console.log('设置后:', count); // 仍然是0!
    
    setCount(count + 1);
    setCount(count + 1);
    // 最终结果是1，不是3！
  };
  
  // 使用函数式更新解决
  const handleClickCorrect = () => {
    setCount(prev => prev + 1);
    setCount(prev => prev + 1);
    setCount(prev => prev + 1);
    // 最终结果是3
  };
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={handleClick}>错误方式+3</button>
      <button onClick={handleClickCorrect}>正确方式+3</button>
    </div>
  );
}`
    },
    tags: ['Hook原理', '异步更新', '性能优化']
  },
  
  {
    id: 'useState-batching',
    question: '为什么连续调用setState，只有最后一个生效？如何解决这个问题？',
    difficulty: 'medium',
    frequency: 'high',
    category: '常见问题',
    answer: {
      brief: '这是由于React的批处理机制和闭包问题导致的。解决方案是使用函数式更新，接收前一个状态作为参数。',
      detailed: `**原因分析：**
1. setState是异步的，不会立即更新状态
2. 多个setState调用会被批量处理
3. 如果使用当前状态值，会受到闭包影响

**问题表现：**
- 连续多次setState使用相同的旧值
- 最终只执行了一次状态更新
- 状态值没有累加效果`,
      code: `// ❌ 错误方式 - 只会增加1
const [count, setCount] = useState(0);

const increment = () => {
  setCount(count + 1); // count仍然是0
  setCount(count + 1); // count仍然是0
  setCount(count + 1); // count仍然是0
  // 最终结果：1
};

// ✅ 正确方式 - 会增加3
const incrementCorrect = () => {
  setCount(prev => prev + 1); // prev是最新值
  setCount(prev => prev + 1); // prev是更新后的值
  setCount(prev => prev + 1); // prev是再次更新后的值
  // 最终结果：3
};

// 更复杂的例子
const handleMultipleUpdates = () => {
  // ❌ 错误：使用当前值
  setUser({ ...user, name: 'John' });
  setUser({ ...user, age: 25 }); // user仍然是旧值，name更新丢失
  
  // ✅ 正确：使用函数式更新
  setUser(prev => ({ ...prev, name: 'John' }));
  setUser(prev => ({ ...prev, age: 25 })); // 基于上一次更新的结果
};`
    },
    tags: ['批处理', '函数式更新', '闭包陷阱']
  },

  {
    id: 'useState-object-updates',
    question: '如何在useState中正确处理对象和数组的更新？',
    difficulty: 'medium',
    frequency: 'high',
    category: '最佳实践',
    answer: {
      brief: 'React使用Object.is()比较状态，直接修改对象不会触发重新渲染。需要创建新对象或数组，使用扩展运算符进行浅拷贝。',
      detailed: `**核心原则：**
React使用Object.is()比较状态，直接修改对象不会触发重新渲染。

**对象更新最佳实践：**
1. 始终创建新对象
2. 使用扩展运算符进行浅拷贝
3. 对于深层嵌套，考虑使用useReducer或状态管理库

**数组更新最佳实践：**
1. 使用不可变方法（map、filter、concat等）
2. 避免使用可变方法（push、pop、splice等）
3. 对于复杂数组操作，考虑使用immer库`,
      code: `const [user, setUser] = useState({
  name: 'John',
  age: 25,
  address: {
    city: 'Beijing',
    street: 'Main St'
  }
});

// ❌ 错误方式 - 直接修改
const updateUserWrong = () => {
  user.name = 'Jane'; // 不会触发重新渲染
  setUser(user);
};

// ✅ 正确方式 - 浅层更新
const updateUser = () => {
  setUser(prev => ({
    ...prev,
    name: 'Jane'
  }));
};

// ✅ 深层更新
const updateAddress = () => {
  setUser(prev => ({
    ...prev,
    address: {
      ...prev.address,
      city: 'Shanghai'
    }
  }));
};

// 数组操作示例
const [items, setItems] = useState(['a', 'b', 'c']);

// ❌ 错误：直接修改数组
const addItemWrong = () => {
  items.push('d'); // 不会触发重渲染
  setItems(items);
};

// ✅ 正确：创建新数组
const addItem = () => {
  setItems(prev => [...prev, 'd']);
};

const removeItem = (index) => {
  setItems(prev => prev.filter((_, i) => i !== index));
};

const updateItem = (index, newValue) => {
  setItems(prev => prev.map((item, i) => 
    i === index ? newValue : item
  ));
};`
    },
    tags: ['不可变性', '对象更新', '数组操作']
  }
];

export default interviewQuestions; 