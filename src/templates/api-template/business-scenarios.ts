import { BusinessScenario } from '../../types/api';

/**
 * 🔧 业务场景模板说明
 * 
 * 这个文件定义API的业务应用场景，包括：
 * - 3个不同难度的业务场景（简单、中级、高级）
 * - 每个场景包含：标题、描述、业务价值、代码示例、实现解析
 * - 突出实际应用价值和技术优势
 * 
 * 💡 编写技巧：
 * - 从简单到复杂的递进式设计
 * - 结合真实业务需求
 * - 提供完整可运行的代码
 * - 解释技术选择的原因
 */

const businessScenarios: BusinessScenario[] = [
  // === 🟢 简单场景：基础应用 ===
  {
    id: 'basic-usage',
    title: '基础数据处理',
    description: '在简单的数据展示组件中使用API进行基础数据处理',
    businessValue: '提升用户体验，简化数据处理逻辑，降低开发复杂度',
    scenario: `
在一个用户信息展示页面中，我们需要处理从服务器获取的用户数据，
包括格式化、验证和显示。使用此API可以简化处理流程。

**适用场景**：
- 数据展示页面
- 简单的数据转换
- 基础的状态管理

🔴 **请修改**: 根据实际API调整场景描述
    `,
    code: `
import React from 'react';
import { useApiTemplate } from 'your-library'; // 🔴 修改导入路径

function UserProfile({ userId }) {
  // 🔴 请修改：替换为实际API使用
  const userData = useApiTemplate(userId, {
    format: 'display',
    validate: true
  });

  if (!userData) {
    return <div>加载中...</div>;
  }

  return (
    <div className="user-profile">
      <h2>{userData.name}</h2>
      <p>邮箱：{userData.email}</p>
      <p>注册时间：{userData.joinDate}</p>
      
      {/* 展示处理后的数据 */}
      <div className="user-stats">
        <span>状态：{userData.status}</span>
        <span>等级：{userData.level}</span>
      </div>
    </div>
  );
}

export default UserProfile;
    `,
    explanation: `
### 🔍 实现解析

1. **数据获取**：通过 \`useApiTemplate\` 获取用户数据
2. **自动处理**：API自动完成数据格式化和验证
3. **状态管理**：内置加载状态处理
4. **类型安全**：TypeScript支持确保数据类型正确

### 💡 技术优势

- ✅ **简化代码**：减少数据处理样板代码
- ✅ **自动验证**：内置数据验证机制
- ✅ **性能优化**：智能缓存和更新策略
- ✅ **错误处理**：优雅的错误处理机制

🔴 **请修改**: 根据实际API特性调整解析内容
    `,
    difficulty: 'easy',
    tags: ['基础', '数据处理', '用户界面'],
    benefits: [
      '降低开发门槛，新手也能快速上手',
      '减少70%的数据处理代码',
      '内置最佳实践，避免常见错误',
      '提升代码可读性和维护性'
    ],
    metrics: {
      performance: '减少50%的渲染时间',
      userExperience: '提升40%的页面响应速度',
      technicalMetrics: '代码量减少70%，bug率降低60%'
    }
  },

  // === 🟡 中级场景：复杂应用 ===
  {
    id: 'intermediate-usage',
    title: '动态表单验证系统',
    description: '构建一个具有实时验证、条件显示的复杂表单系统',
    businessValue: '提升表单交互体验，减少用户输入错误，提高数据质量',
    scenario: `
在企业级应用中，我们需要构建一个复杂的表单系统，支持：
- 实时数据验证
- 条件字段显示/隐藏
- 多步骤表单流程
- 自动保存草稿

**适用场景**：
- 用户注册/设置页面
- 复杂的配置表单
- 多步骤向导流程
- 动态表单生成

🔴 **请修改**: 根据实际API调整场景描述
    `,
    code: `
import React, { useState, useEffect } from 'react';
import { useApiTemplate } from 'your-library'; // 🔴 修改导入路径

function DynamicForm({ formConfig, onSubmit }) {
  const [currentStep, setCurrentStep] = useState(0);
  
  // 🔴 请修改：替换为实际API使用
  const formState = useApiTemplate(formConfig, {
    validation: 'real-time',
    autoSave: true,
    stepMode: true
  });

  // 处理表单提交
  const handleSubmit = async (data) => {
    try {
      await onSubmit(data);
      formState.reset();
    } catch (error) {
      formState.setError(error.message);
    }
  };

  // 条件字段渲染
  const renderField = (field) => {
    if (!formState.shouldShowField(field.id)) {
      return null;
    }

    return (
      <div key={field.id} className="form-field">
        <label>{field.label}</label>
        <input
          type={field.type}
          value={formState.getValue(field.id)}
          onChange={(e) => formState.setValue(field.id, e.target.value)}
          onBlur={() => formState.validateField(field.id)}
        />
        {formState.getError(field.id) && (
          <span className="error">{formState.getError(field.id)}</span>
        )}
      </div>
    );
  };

  return (
    <div className="dynamic-form">
      <div className="form-progress">
        步骤 {currentStep + 1} / {formConfig.steps.length}
      </div>
      
      <form onSubmit={handleSubmit}>
        {formConfig.steps[currentStep].fields.map(renderField)}
        
        <div className="form-actions">
          {currentStep > 0 && (
            <button 
              type="button"
              onClick={() => setCurrentStep(currentStep - 1)}
            >
              上一步
            </button>
          )}
          
          {currentStep < formConfig.steps.length - 1 ? (
            <button 
              type="button"
              onClick={() => setCurrentStep(currentStep + 1)}
              disabled={!formState.isStepValid(currentStep)}
            >
              下一步
            </button>
          ) : (
            <button 
              type="submit"
              disabled={!formState.isFormValid()}
            >
              提交
            </button>
          )}
        </div>
      </form>
      
      {/* 自动保存状态 */}
      {formState.isDraftSaved && (
        <div className="draft-indicator">
          ✅ 草稿已自动保存
        </div>
      )}
    </div>
  );
}

export default DynamicForm;
    `,
    explanation: `
### 🔍 实现解析

1. **状态管理**：统一管理表单状态、验证和错误
2. **实时验证**：输入时即时验证，提升用户体验
3. **条件逻辑**：根据用户输入动态显示/隐藏字段
4. **步骤控制**：支持多步骤表单流程
5. **自动保存**：定期保存用户输入，防止数据丢失

### 💡 技术亮点

- ✅ **智能验证**：基于规则的实时验证引擎
- ✅ **状态同步**：多组件间的状态自动同步
- ✅ **性能优化**：只重新渲染发生变化的字段
- ✅ **扩展性强**：易于添加新的验证规则和字段类型

### 🏆 业务价值

- 减少80%的表单错误
- 提升60%的表单完成率
- 降低50%的开发时间
- 提供一致的用户体验

🔴 **请修改**: 根据实际API特性调整解析内容
    `,
    difficulty: 'medium',
    tags: ['表单', '验证', '状态管理', '用户体验'],
    benefits: [
      '大幅提升表单交互体验',
      '减少用户输入错误率',
      '支持复杂的业务逻辑',
      '自动化的错误处理机制'
    ],
    metrics: {
      performance: '表单响应时间提升75%',
      userExperience: '用户完成率提升60%',
      technicalMetrics: '开发效率提升80%，代码复用率85%'
    }
  },

  // === 🔴 高级场景：企业级应用 ===
  {
    id: 'advanced-usage',
    title: '企业级数据可视化Dashboard',
    description: '构建高性能、实时更新的企业级数据可视化仪表板系统',
    businessValue: '提供实时业务洞察，支持数据驱动决策，提升运营效率',
    scenario: `
在企业级应用中，我们需要构建一个复杂的数据可视化仪表板：
- 实时数据流处理
- 多维度数据分析
- 交互式图表组件
- 大数据量性能优化
- 权限控制和个性化配置

**适用场景**：
- 业务监控仪表板
- 数据分析平台
- 实时报表系统
- 企业决策支持系统

🔴 **请修改**: 根据实际API调整场景描述
    `,
    code: `
import React, { useState, useEffect, useMemo } from 'react';
import { useApiTemplate } from 'your-library'; // 🔴 修改导入路径

function EnterpriseDashboard({ userId, permissions }) {
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedMetrics, setSelectedMetrics] = useState([]);
  
  // 🔴 请修改：替换为实际API使用
  const dashboardData = useApiTemplate({
    userId,
    timeRange,
    metrics: selectedMetrics,
    realTime: true
  }, {
    cache: true,
    pooling: 30000, // 30秒轮询
    optimize: 'performance',
    permission: permissions
  });

  // 计算衍生数据
  const analyticsData = useMemo(() => {
    if (!dashboardData?.metrics) return null;
    
    return {
      trends: dashboardData.calculateTrends(),
      comparisons: dashboardData.getComparisons(),
      forecasts: dashboardData.generateForecasts(),
      alerts: dashboardData.getAlerts()
    };
  }, [dashboardData]);

  // 处理用户交互
  const handleMetricSelect = (metric) => {
    setSelectedMetrics(prev => 
      prev.includes(metric) 
        ? prev.filter(m => m !== metric)
        : [...prev, metric]
    );
  };

  // 导出报表
  const handleExport = async (format) => {
    try {
      const exportData = await dashboardData.export(format);
      downloadFile(exportData, \`dashboard-\${Date.now()}.\${format}\`);
    } catch (error) {
      showNotification('导出失败', 'error');
    }
  };

  if (dashboardData.isLoading) {
    return <DashboardSkeleton />;
  }

  return (
    <div className="enterprise-dashboard">
      {/* 控制面板 */}
      <div className="dashboard-controls">
        <TimeRangeSelector 
          value={timeRange}
          onChange={setTimeRange}
        />
        
        <MetricSelector
          available={dashboardData.availableMetrics}
          selected={selectedMetrics}
          onSelect={handleMetricSelect}
          permissions={permissions}
        />
        
        <ExportButton onClick={handleExport} />
      </div>

      {/* 关键指标卡片 */}
      <div className="kpi-cards">
        {analyticsData?.trends.map(trend => (
          <KPICard
            key={trend.id}
            title={trend.name}
            value={trend.current}
            change={trend.change}
            trend={trend.direction}
            onClick={() => drillDown(trend.id)}
          />
        ))}
      </div>

      {/* 图表网格 */}
      <div className="charts-grid">
        {selectedMetrics.map(metric => (
          <ChartWidget
            key={metric.id}
            data={dashboardData.getMetricData(metric.id)}
            config={metric.chartConfig}
            onInteraction={(event) => handleChartInteraction(metric.id, event)}
            realTimeUpdate={true}
          />
        ))}
      </div>

      {/* 实时数据流 */}
      <div className="realtime-stream">
        <h3>实时数据流</h3>
        <DataStream
          source={dashboardData.streamSource}
          filters={selectedMetrics}
          onUpdate={(newData) => dashboardData.updateStream(newData)}
        />
      </div>

      {/* 警报中心 */}
      {analyticsData?.alerts.length > 0 && (
        <AlertCenter
          alerts={analyticsData.alerts}
          onDismiss={(alertId) => dashboardData.dismissAlert(alertId)}
          onEscalate={(alertId) => escalateAlert(alertId)}
        />
      )}
    </div>
  );
}

// 高性能图表组件
const ChartWidget = React.memo(({ data, config, onInteraction, realTimeUpdate }) => {
  // 图表实现逻辑
  return (
    <div className="chart-widget">
      {/* 高性能图表渲染 */}
    </div>
  );
});

export default EnterpriseDashboard;
    `,
    explanation: `
### 🔍 实现解析

1. **实时数据处理**：支持30秒轮询和WebSocket实时更新
2. **性能优化**：使用useMemo缓存计算结果，React.memo防止不必要渲染
3. **权限控制**：基于用户权限动态显示数据和功能
4. **交互分析**：支持钻取、筛选、对比等高级分析功能
5. **数据导出**：支持多种格式的报表导出

### 💡 技术亮点

- ✅ **高性能**：大数据量下依然流畅运行
- ✅ **实时性**：毫秒级数据更新响应
- ✅ **扩展性**：模块化设计，易于添加新功能
- ✅ **智能化**：自动趋势分析和预警系统

### 🏆 企业价值

- 实时监控业务关键指标
- 支持数据驱动的决策制定
- 提升运营效率和响应速度
- 降低数据分析门槛

### 📊 性能指标

- 支持百万级数据点实时渲染
- 图表交互响应时间 < 100ms
- 内存使用优化 60%
- 支持10+并发用户同时操作

🔴 **请修改**: 根据实际API特性调整解析内容
    `,
    difficulty: 'hard',
    tags: ['企业级', '可视化', '实时数据', '性能优化', '权限控制'],
    benefits: [
      '支持企业级数据处理需求',
      '提供实时业务洞察能力',
      '大幅提升决策效率',
      '降低数据分析技术门槛'
    ],
    metrics: {
      performance: '数据处理能力提升10倍',
      userExperience: '决策响应时间缩短80%',
      technicalMetrics: '系统稳定性99.9%，并发处理能力提升5倍'
    }
  }
];

export default businessScenarios; 