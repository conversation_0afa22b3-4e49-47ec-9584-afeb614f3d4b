import { CommonQuestion } from '../../types/api';

/**
 * 🔧 常见问题模板说明
 * 
 * 这个文件定义API的常见问题和解答，包括：
 * - 3个典型的FAQ问题
 * - 每个问题包含：问题描述、详细解答、代码示例
 * - 覆盖使用疑问、错误处理、最佳实践等方面
 * 
 * 💡 编写技巧：
 * - 问题来源于实际开发中的困惑
 * - 解答详细且具有实操性
 * - 提供对比示例说明正确做法
 * - 标注相关问题便于扩展阅读
 */

const commonQuestions: CommonQuestion[] = [
  // === ❓ 问题1：基础使用 ===
  {
    id: 'basic-usage-question',
    question: 'API模板名称什么时候使用？与其他类似API有什么区别？',
    answer: `
### 🎯 使用时机

API模板名称适用于以下场景：

1. **[场景1]**：当你需要[具体需求]时
   - 传统方案：[传统做法的问题]
   - 使用此API：[改进效果]

2. **[场景2]**：当你遇到[具体问题]时
   - 问题描述：[具体问题说明]
   - 解决方案：[API如何解决]

3. **[场景3]**：当你想要[具体目标]时
   - 优化前：[当前状况]
   - 优化后：[使用API的效果]

### ⚖️ 与其他API的对比

| 特性 | API模板名称 | [对比API1] | [对比API2] |
|------|------------|-----------|-----------|
| 使用难度 | [评价] | [评价] | [评价] |
| 性能表现 | [数据] | [数据] | [数据] |
| 功能完整性 | [评价] | [评价] | [评价] |
| 社区支持 | [状况] | [状况] | [状况] |

### 💡 选择建议

**推荐使用API模板名称的情况：**
- ✅ [具体条件1]
- ✅ [具体条件2]
- ✅ [具体条件3]

**不推荐使用的情况：**
- ❌ [不适用场景1]
- ❌ [不适用场景2]
- ❌ [不适用场景3]

🔴 **请修改**: 根据实际API特性调整对比内容
    `,
    code: `
// 使用时机示例对比

// ❌ 不适合的场景
function WrongUsage() {
  // 🔴 请修改：展示不适合使用的场景
  const simpleData = useApiTemplate(staticValue); // 过度使用
  return <div>{simpleData}</div>;
}

// ✅ 适合的场景
function CorrectUsage() {
  // 🔴 请修改：展示适合使用的场景
  const complexData = useApiTemplate(dynamicParams, {
    transform: true,
    validate: true,
    cache: true
  });
  
  return (
    <div>
      {complexData.map(item => (
        <DataItem key={item.id} data={item} />
      ))}
    </div>
  );
}

// 与其他API的对比示例
function ComparisonExample() {
  // 传统方案
  const [data1, setData1] = useState(null);
  useEffect(() => {
    // 手动处理逻辑
    fetchData().then(result => {
      const processed = processData(result);
      setData1(processed);
    });
  }, []);

  // 使用API模板名称
  const data2 = useApiTemplate(params, {
    autoProcess: true,
    errorHandling: true
  });

  return (
    <div>
      <h3>对比效果</h3>
      <p>传统方案代码行数: ~20行</p>
      <p>使用新API代码行数: ~3行</p>
    </div>
  );
}
    `,
    tags: ['使用时机', '对比分析', '选择建议'],
    relatedQuestions: [
      '如何选择合适的参数配置？',
      'API模板名称的性能如何？',
      '有哪些常见的使用错误？'
    ]
  },

  // === ❓ 问题2：错误处理 ===
  {
    id: 'error-handling-question',
    question: '使用API模板名称时遇到错误怎么办？如何调试和解决常见问题？',
    answer: `
### 🚨 常见错误类型

#### 1. 参数错误
**错误信息**: \`Invalid parameter: [parameter name]\`
**原因**: 传入的参数类型或格式不正确
**解决方案**:
- 检查参数类型是否匹配
- 确认必需参数是否提供
- 使用TypeScript获得类型提示

#### 2. 配置错误
**错误信息**: \`Configuration error: [config issue]\`
**原因**: 配置选项设置不当
**解决方案**:
- 查看官方文档确认配置项
- 使用默认配置进行测试
- 逐步添加自定义配置

#### 3. 运行时错误
**错误信息**: \`Runtime error: [specific error]\`
**原因**: 运行过程中的异常情况
**解决方案**:
- 添加错误边界处理
- 使用try-catch包装调用
- 实现降级方案

### 🛠️ 调试技巧

#### 开发模式调试
1. **启用调试模式**
   ```javascript
   const result = useApiTemplate(params, { debug: true });
   ```

2. **查看详细日志**
   - 控制台会输出详细的执行信息
   - 包括参数处理、状态变化、错误堆栈

3. **使用浏览器开发工具**
   - React DevTools查看组件状态
   - Network面板查看网络请求
   - Console查看错误信息

#### 生产环境监控
1. **错误上报**
   ```javascript
   const result = useApiTemplate(params, {
     onError: (error) => {
       // 上报到错误监控服务
       errorReporting.report(error);
     }
   });
   ```

2. **性能监控**
   - 监控API调用成功率
   - 跟踪响应时间
   - 分析错误趋势

### 🔧 解决方案速查

| 问题症状 | 可能原因 | 解决方案 |
|----------|----------|----------|
| 返回undefined | 参数错误或异步未完成 | 检查参数，添加loading状态 |
| 性能缓慢 | 缓存未启用或数据量大 | 启用缓存，考虑分页 |
| 内存泄漏 | 组件卸载后仍有订阅 | 正确清理副作用 |
| 状态不同步 | 多个实例冲突 | 使用全局状态管理 |

🔴 **请修改**: 根据实际API的错误类型调整内容
    `,
    code: `
// 错误处理和调试示例

// 1. 基础错误处理
function ErrorHandlingExample() {
  // 🔴 请修改：根据实际API调整错误处理
  const { data, loading, error } = useApiTemplate(params, {
    onError: (error) => {
      // 自定义错误处理
      console.error('API Error:', error);
      
      // 根据错误类型处理
      if (error.type === 'VALIDATION_ERROR') {
        alert('参数验证失败，请检查输入');
      } else if (error.type === 'NETWORK_ERROR') {
        alert('网络异常，请稍后重试');
      }
    },
    retry: 3, // 自动重试3次
    retryDelay: 1000 // 重试间隔1秒
  });

  // 渲染时的错误处理
  if (error) {
    return (
      <div className="error-container">
        <h3>出现错误</h3>
        <p>错误类型: {error.type}</p>
        <p>错误信息: {error.message}</p>
        <button onClick={() => window.location.reload()}>
          重新加载
        </button>
      </div>
    );
  }

  if (loading) return <div>加载中...</div>;
  
  return <div>{/* 正常内容 */}</div>;
}

// 2. 高级调试功能
function DebugExample() {
  // 启用调试模式
  const result = useApiTemplate(params, {
    debug: true,
    onStateChange: (state) => {
      console.log('State changed:', state);
    },
    onPerformance: (metrics) => {
      console.log('Performance metrics:', metrics);
    }
  });

  // 开发环境下显示调试信息
  if (process.env.NODE_ENV === 'development') {
    return (
      <div>
        <div className="debug-panel">
          <h4>调试信息</h4>
          <pre>{JSON.stringify(result.debugInfo, null, 2)}</pre>
        </div>
        {/* 正常内容 */}
      </div>
    );
  }

  return <div>{/* 生产环境内容 */}</div>;
}

// 3. 错误边界组件
class ApiErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // 记录错误到监控服务
    console.error('API Error Boundary:', error, errorInfo);
    
    // 可以发送错误报告
    this.reportError(error, errorInfo);
  }

  reportError = (error, errorInfo) => {
    // 发送到错误监控服务
    if (window.errorReporting) {
      window.errorReporting.captureException(error, {
        extra: errorInfo,
        tags: { component: 'ApiErrorBoundary' }
      });
    }
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h2>出现了意外错误</h2>
          <details>
            <summary>错误详情</summary>
            <pre>{this.state.error?.toString()}</pre>
          </details>
          <button onClick={() => this.setState({ hasError: false, error: null })}>
            重试
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// 4. 使用错误边界
function App() {
  return (
    <ApiErrorBoundary>
      <ErrorHandlingExample />
      <DebugExample />
    </ApiErrorBoundary>
  );
}
    `,
    tags: ['错误处理', '调试技巧', '故障排查'],
    relatedQuestions: [
      'API模板名称支持哪些配置选项？',
      '如何优化API模板名称的性能？',
      '生产环境下如何监控API状态？'
    ]
  },

  // === ❓ 问题3：性能优化 ===
  {
    id: 'performance-optimization-question',
    question: '如何优化API模板名称的性能？有哪些最佳实践？',
    answer: `
### ⚡ 性能优化策略

#### 1. 缓存优化
**启用智能缓存**
- 自动缓存频繁访问的数据
- 基于依赖的缓存失效
- 支持自定义缓存策略

**缓存配置选项**
```javascript
{
  cache: true,           // 启用缓存
  cacheKey: 'custom-key', // 自定义缓存键
  cacheTTL: 300000,      // 缓存5分钟
  cacheStorage: 'memory'  // 缓存存储方式
}
```

#### 2. 请求优化
**批量处理**
- 合并多个相似请求
- 减少网络开销
- 提升整体性能

**并发控制**
- 限制同时执行的请求数量
- 避免资源竞争
- 防止浏览器连接数限制

#### 3. 渲染优化
**懒加载**
- 按需加载数据
- 减少初始渲染时间
- 提升页面响应速度

**虚拟化**
- 大数据集的虚拟渲染
- 减少DOM节点数量
- 保持流畅的滚动体验

### 📊 性能指标

#### 关键指标
- **首次渲染时间 (FCP)**: < 1.5s
- **最大内容渲染 (LCP)**: < 2.5s
- **累积布局偏移 (CLS)**: < 0.1
- **首次输入延迟 (FID)**: < 100ms

#### 监控工具
- Chrome DevTools Performance
- React DevTools Profiler  
- Web Vitals扩展
- 自定义性能监控

### 🏆 最佳实践

#### 代码层面
1. **避免不必要的重新计算**
   - 使用useMemo缓存计算结果
   - 使用useCallback缓存函数引用
   - 合理设置依赖数组

2. **优化组件结构**
   - 避免深层嵌套
   - 使用React.memo防止不必要渲染
   - 分离纯展示组件和逻辑组件

3. **数据处理优化**
   - 在合适的时机进行数据转换
   - 避免在渲染过程中进行复杂计算
   - 使用Web Workers处理CPU密集型任务

#### 配置层面
1. **启用生产优化**
   ```javascript
   {
     optimize: 'production',
     minify: true,
     compression: true
   }
   ```

2. **选择合适的更新策略**
   ```javascript
   {
     updateStrategy: 'lazy',    // 懒更新
     batchUpdates: true,       // 批量更新
     debounceTime: 300         // 防抖时间
   }
   ```

🔴 **请修改**: 根据实际API的性能特性调整内容
    `,
    code: `
// 性能优化示例

// 1. 缓存优化
function CacheOptimizedComponent() {
  // 🔴 请修改：根据实际API调整缓存配置
  const expensiveData = useApiTemplate(params, {
    cache: true,
    cacheKey: \`data-\${params.id}\`,
    cacheTTL: 5 * 60 * 1000, // 5分钟缓存
    staleWhileRevalidate: true // 使用过期数据同时后台更新
  });

  return <ExpensiveComponent data={expensiveData} />;
}

// 2. 批量处理优化
function BatchProcessingExample() {
  const [requests, setRequests] = useState([]);
  
  // 批量处理多个请求
  const batchedData = useApiTemplate(requests, {
    batch: true,
    batchSize: 10,
    batchDelay: 100, // 100ms内的请求合并处理
    onBatchComplete: (results) => {
      console.log('Batch completed:', results.length);
    }
  });

  return (
    <div>
      {batchedData.map((item, index) => (
        <DataItem key={index} data={item} />
      ))}
    </div>
  );
}

// 3. 虚拟化大列表
import { FixedSizeList } from 'react-window';

function VirtualizedList() {
  const largeDataset = useApiTemplate(params, {
    pagination: true,
    pageSize: 100,
    virtualScroll: true
  });

  const Row = ({ index, style }) => (
    <div style={style}>
      <DataItem data={largeDataset.items[index]} />
    </div>
  );

  return (
    <FixedSizeList
      height={600}
      itemCount={largeDataset.total}
      itemSize={50}
      onItemsRendered={({ startIndex, endIndex }) => {
        // 预加载即将显示的数据
        largeDataset.preload(startIndex, endIndex);
      }}
    >
      {Row}
    </FixedSizeList>
  );
}

// 4. 性能监控
function PerformanceMonitoredComponent() {
  const startTime = useRef(Date.now());
  
  const data = useApiTemplate(params, {
    onStart: () => {
      performance.mark('api-start');
    },
    onComplete: () => {
      performance.mark('api-end');
      performance.measure('api-duration', 'api-start', 'api-end');
      
      // 获取性能指标
      const measure = performance.getEntriesByName('api-duration')[0];
      console.log('API调用耗时:', measure.duration, 'ms');
    }
  });

  useEffect(() => {
    const endTime = Date.now();
    const renderTime = endTime - startTime.current;
    
    // 记录渲染性能
    if (renderTime > 100) {
      console.warn('组件渲染时间过长:', renderTime, 'ms');
    }
  }, [data]);

  return <div>{/* 组件内容 */}</div>;
}

// 5. 内存优化
function MemoryOptimizedComponent() {
  const data = useApiTemplate(params, {
    // 自动清理不需要的数据
    autoCleanup: true,
    // 限制内存使用
    maxMemoryUsage: '50MB',
    // 使用弱引用避免内存泄漏
    weakReferences: true
  });

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      data.cleanup();
    };
  }, []);

  return <div>{/* 组件内容 */}</div>;
}

// 6. 综合优化示例
function OptimizedApp() {
  return (
    <React.Suspense fallback={<Loading />}>
      <ErrorBoundary>
        <CacheOptimizedComponent />
        <BatchProcessingExample />
        <VirtualizedList />
        <PerformanceMonitoredComponent />
        <MemoryOptimizedComponent />
      </ErrorBoundary>
    </React.Suspense>
  );
}
    `,
    tags: ['性能优化', '缓存策略', '最佳实践'],
    relatedQuestions: [
      'API模板名称的缓存机制是如何工作的？',
      '如何处理大量数据的渲染性能问题？',
      '生产环境下如何监控性能指标？'
    ]
  }
];

export default commonQuestions; 