import { ApiItem } from '../../types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
// 可选Tab导入（按需启用）
// import performanceOptimization from './performance-optimization';
// import learningPath from './learning-path';
// import debuggingTips from './debugging-tips';
// import versionMigration from './version-migration';
// import ecosystemTools from './ecosystem-tools';
// import realWorldProjects from './real-world-projects';

// 🆕 ExtensionTabs导入（按需启用）
// import stateManagementComparison from './extensions/state-management-comparison';
// import performanceBenchmarks from './extensions/performance-benchmarks';

/**
 * 🔧 API模板配置指南
 * 
 * 1. 基本信息配置：
 *    - id: API唯一标识符（必须唯一）
 *    - title: API名称（显示名称）
 *    - category: 分类（如： *    - description: 简要描述（一句话说明用途）
 *    - version: API版本号（如：'18.0.0'）
 *    - difficulty: 难度等级（'easy' | 'medium' | 'hard'）
 * 
 * 2. 语法和示例：
 *    - syntax: API语法格式
 *    - example: 完整使用示例
 *    - notes: 重要注意事项
 * 
 * 3. Tab内容配置：
 *    - 必选Tab（6个）：basicInfo, businessScenarios, implementation, interviewQuestions, commonQuestions, knowledgeArchaeology
 *    - 可选Tab：根据API特性选择性实现
 *    - 🆕 ExtensionTabs：特殊内容使用extensions文件夹管理
 */

const apiTemplateData: ApiItem = {
  // === 基本配置 ===
  id: 'apiTemplateName', // 🔴 必须修改：API唯一标识符
  title: 'API模板名称', // 🔴 必须修改：API显示名称
  category: 'templates', // 🔴 必须修改：API分类
  description: '这是一个API模板示例，用于创建标准化的API文档结构', // 🔴 必须修改：API描述
  version: '1.0.0', // 🔴 必须修改：API版本
  difficulty: 'medium', // 🔴 必须修改：难度等级 ('easy' | 'medium' | 'hard')
  
  // === 语法和示例 ===
  syntax: 'const result = useApiTemplate(parameter1, parameter2)', // 🔴 必须修改：API语法
  example: `import { useApiTemplate } from 'library';

function ExampleComponent() {
  // 🔴 必须修改：完整的使用示例
  const result = useApiTemplate('参数1', {
    option1: 'value1',
    option2: true
  });

  return (
    <div>
      <h3>API模板示例</h3>
      <p>结果: {result}</p>
    </div>
  );
}

export default ExampleComponent;`, // 🔴 必须修改：完整代码示例
  
  notes: '这是重要的使用注意事项，请根据实际API特性修改', // 🔴 必须修改：重要提示
  
  // === 可选标识 ===
  isNew: false, // 🔧 可选：是否为新API
  tags: ['模板', '示例', '参考'], // 🔧 可选：标签数组
  
  // === 必选Tab内容（6个） ===
  basicInfo, // ✅ 必须实现：基本信息
  businessScenarios, // ✅ 必须实现：业务场景
  implementation, // ✅ 必须实现：原理解析
  interviewQuestions, // ✅ 必须实现：面试题
  commonQuestions, // ✅ 必须实现：常见问题
  knowledgeArchaeology, // ✅ 必须实现：知识考古
  
  // === 可选Tab内容（按API特性选择） ===
  // 根据API特点，选择性启用以下Tab：
  
  // 🔧 性能敏感API必须实现（如setState、大数据处理API）：
  // performanceOptimization,
  
  // 📚 复杂API必须实现（如useReducer、复杂状态管理）：
  // learningPath,
  
  // 🐛 易错API必须实现（如useEffect、异步操作API）：
  // debuggingTips,
  
  // 🔄 有版本变化的API必须实现（如React 16→17→18的API变更）：
  // versionMigration,
  
  // 🌐 生态丰富的API必须实现（如核心Hook、常用工具API）：
  // ecosystemTools,
  
  // 🏗️ 常用API推荐实现（如状态管理、数据处理API）：
  // realWorldProjects,
  
  // === 🆕 ExtensionTabs系统（特殊内容） ===
  // 🎯 当API需要特殊对比分析或深度专题时使用：
  // extensionTabs: {
  //   title: '高级特性', // 扩展Tab组标题
  //   description: '专门针对此API的深度分析内容', // 扩展Tab组描述
  //   tabs: [
  //     {
  //       key: 'state-management-comparison', // 文件名（短横线分隔）
  //       title: '状态管理对比', // Tab显示标题
  //       content: stateManagementComparison // 导入的内容对象
  //     },
  //     {
  //       key: 'performance-benchmarks', // 文件名（短横线分隔）
  //       title: '性能基准测试', // Tab显示标题
  //       content: performanceBenchmarks // 导入的内容对象
  //     }
  //   ]
  // }
};

/**
 * 🆕 ExtensionTabs使用指南
 * 
 * ### 文件夹结构
 * ```
 * src/templates/api-template/
 * ├── extensions/                    # 🆕 扩展内容文件夹
 * │   ├── state-management-comparison.ts  # 状态管理对比
 * │   ├── performance-benchmarks.ts       # 性能基准测试
 * │   ├── migration-guide.ts              # 迁移指南
 * │   └── advanced-patterns.ts            # 高级模式
 * ├── index.ts                       # 主配置文件
 * └── (其他标准Tab文件)
 * ```
 * 
 * ### 命名规范
 * - 文件名：短横线分隔（kebab-case）
 * - 变量名：驼峰命名（camelCase）
 * - Tab标题：中文描述性标题
 * 
 * ### 适用场景
 * - 🔄 状态管理API → state-management-comparison
 * - ⚡ 性能敏感API → performance-benchmarks  
 * - 🔧 复杂API → migration-guide, advanced-patterns
 * - 🌐 生态丰富API → ecosystem-integration
 * - 📊 数据处理API → data-transformation-examples
 * 
 * ### 质量要求
 * - 每个ExtensionTab必须是完整的知识单元
 * - 提供独特价值，不与标准12个Tab重复
 * - 包含实际代码示例和最佳实践
 * - 遵循统一的文档格式和风格
 */

export default apiTemplateData; 