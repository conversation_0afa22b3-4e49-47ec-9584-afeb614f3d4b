/**
 * 🔧 调试技巧模板说明
 * 
 * 这是一个可选Tab，适用于易错API的调试指导。
 * 包含常见错误诊断、调试工具使用和问题解决方案。
 * 
 * 💡 适用场景：
 * - 易错的API（如useEffect、复杂状态管理）
 * - 有调试难度的API
 * - 容易出现性能问题的API
 * - 需要特殊调试技巧的API
 */

const debuggingTips = `
# 🐛 调试技巧大全

## 🔍 常见问题诊断

### 问题分类和快速识别

#### 1. 状态更新问题
**症状**: 组件不重新渲染、状态值不正确、延迟更新
**快速诊断**:
\`\`\`javascript
// ❌ 常见错误：直接修改状态
function Component() {
  const [items, setItems] = useState([]);
  
  const addItem = () => {
    items.push(newItem); // 🚫 错误：直接修改数组
    setItems(items);     // 状态没有变化，不会重新渲染
  };
}

// ✅ 正确方式：创建新的状态对象
function Component() {
  const [items, setItems] = useState([]);
  
  const addItem = () => {
    setItems(prevItems => [...prevItems, newItem]); // ✅ 正确
  };
}

// 🔧 调试技巧：添加日志检查状态变化
function Component() {
  const [items, setItems] = useState([]);
  
  // 监控状态变化
  useEffect(() => {
    console.log('Items changed:', items);
  }, [items]);
  
  const addItem = () => {
    console.log('Before update:', items);
    setItems(prevItems => {
      const newItems = [...prevItems, newItem];
      console.log('After update:', newItems);
      return newItems;
    });
  };
}
\`\`\`

#### 2. 依赖项问题
**症状**: useEffect无限循环、回调函数过时、内存泄漏
**快速诊断**:
\`\`\`javascript
// ❌ 常见错误：缺少依赖项
function Component({ userId }) {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    fetchUser(userId).then(setUser); // userId变化时不会重新执行
  }, []); // 🚫 缺少userId依赖
}

// ✅ 正确方式：包含所有依赖项
function Component({ userId }) {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    fetchUser(userId).then(setUser);
  }, [userId]); // ✅ 包含userId依赖
}

// 🔧 调试技巧：使用ESLint规则检查
// eslint-plugin-react-// 在.eslintrc.js中启用：
// "react-\`\`\`

#### 3. 异步操作问题
**症状**: Promise未正确处理、竞态条件、组件卸载后状态更新
**快速诊断**:
\`\`\`javascript
// ❌ 常见错误：竞态条件
function Component({ searchTerm }) {
  const [results, setResults] = useState([]);
  
  useEffect(() => {
    search(searchTerm).then(setResults); // 可能会出现竞态条件
  }, [searchTerm]);
}

// ✅ 正确方式：使用AbortController
function Component({ searchTerm }) {
  const [results, setResults] = useState([]);
  
  useEffect(() => {
    const controller = new AbortController();
    
    search(searchTerm, { signal: controller.signal })
      .then(setResults)
      .catch(error => {
        if (error.name !== 'AbortError') {
          console.error('Search failed:', error);
        }
      });
    
    return () => controller.abort(); // 清理函数
  }, [searchTerm]);
}

// 🔧 调试技巧：添加请求标识符
function Component({ searchTerm }) {
  const [results, setResults] = useState([]);
  const requestId = useRef(0);
  
  useEffect(() => {
    const currentRequestId = ++requestId.current;
    
    search(searchTerm).then(data => {
      // 只有最新请求的结果才会被应用
      if (currentRequestId === requestId.current) {
        setResults(data);
      }
    });
  }, [searchTerm]);
}
\`\`\`

## 🛠️ 调试工具和方法

### React DevTools使用技巧

#### 1. Profiler性能分析
\`\`\`javascript
// 添加Profiler包装器
import { Profiler } from 'react';

function App() {
  const onRenderCallback = (id, phase, actualDuration, baseDuration, startTime, commitTime) => {
    console.log('组件渲染性能:', {
      id,           // 组件标识
      phase,        // 'mount' 或 'update'
      actualDuration, // 本次渲染耗时
      baseDuration,  // 估计渲染耗时
      startTime,     // 开始渲染时间
      commitTime     // 提交更改时间
    });
  };

  return (
    <Profiler id="ApiTemplate" onRender={onRenderCallback}>
      <ApiTemplateComponent />
    </Profiler>
  );
}
\`\`\`

#### 2. 组件状态检查
\`\`\`javascript
// 在开发环境中暴露调试信息
function useApiTemplate(initialState) {
  const [state, setState] = useState(initialState);
  
  // 开发环境下暴露到window对象
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      window.debugApiTemplate = {
        state,
        setState,
        history: [] // 状态变更历史
      };
    }
  }, [state]);
  
  return [state, setState];
}

// 在浏览器控制台中使用：
// window.debugApiTemplate.state // 查看当前状态
// window.debugApiTemplate.setState(newState) // 手动设置状态
\`\`\`

### 自定义调试Hook

#### 1. 状态变化跟踪器
\`\`\`typescript
function useStateTracker<T>(value: T, name: string) {
  const prevValue = useRef<T>();
  
  useEffect(() => {
    if (prevValue.current !== undefined && prevValue.current !== value) {
      console.log(\`\${name} 状态变化:\`, {
        from: prevValue.current,
        to: value,
        timestamp: new Date().toISOString()
      });
    }
    prevValue.current = value;
  }, [value, name]);
}

// 使用示例
function Component() {
  const [count, setCount] = useState(0);
  
  // 跟踪count状态变化
  useStateTracker(count, 'count');
  
  return (
    <button onClick={() => setCount(c => c + 1)}>
      点击: {count}
    </button>
  );
}
\`\`\`

#### 2. 渲染次数计数器
\`\`\`typescript
function useRenderCount(componentName: string) {
  const renderCount = useRef(0);
  const startTime = useRef(Date.now());
  
  renderCount.current++;
  
  useEffect(() => {
    console.log(\`\${componentName} 渲染次数: \${renderCount.current}\`);
  });
  
  useEffect(() => {
    return () => {
      const totalTime = Date.now() - startTime.current;
      console.log(\`\${componentName} 总生命周期: \${totalTime}ms, 总渲染次数: \${renderCount.current}\`);
    };
  }, [componentName]);
  
  return renderCount.current;
}

// 使用示例
function Component() {
  const renderCount = useRenderCount('Component');
  
  return <div>第 {renderCount} 次渲染</div>;
}
\`\`\`

#### 3. 依赖项变化检测器
\`\`\`typescript
function useDependencyTracker(dependencies: any[], name: string) {
  const prevDeps = useRef<any[]>();
  
  useEffect(() => {
    if (prevDeps.current) {
      dependencies.forEach((dep, index) => {
        if (prevDeps.current![index] !== dep) {
          console.log(\`\${name} 依赖项 [\${index}] 变化:\`, {
            from: prevDeps.current![index],
            to: dep
          });
        }
      });
    }
    prevDeps.current = dependencies;
  });
}

// 使用示例
function Component({ userId, filters }) {
  const deps = [userId, filters];
  useDependencyTracker(deps, 'useEffect依赖项');
  
  useEffect(() => {
    // 某些副作用
  }, deps);
}
\`\`\`

## 🚨 常见错误排查指南

### 错误类型1: 无限循环
**症状**: 页面卡死、控制台显示"Maximum update depth exceeded"
**排查步骤**:

1. **检查useEffect依赖项**
\`\`\`javascript
// 🔍 问题定位
function Component() {
  const [data, setData] = useState({});
  
  useEffect(() => {
    setData({ ...data, newField: 'value' }); // 🚫 无限循环
  }, [data]); // data变化导致effect重新执行
}

// ✅ 解决方案1：使用函数式更新
function Component() {
  const [data, setData] = useState({});
  
  useEffect(() => {
    setData(prev => ({ ...prev, newField: 'value' }));
  }, []); // 只执行一次
}

// ✅ 解决方案2：使用useCallback
function Component() {
  const [data, setData] = useState({});
  
  const updateData = useCallback(() => {
    setData(prev => ({ ...prev, newField: 'value' }));
  }, []);
  
  useEffect(() => {
    updateData();
  }, [updateData]);
}
\`\`\`

2. **使用调试工具定位**
\`\`\`javascript
// 添加断点调试
function Component() {
  const [data, setData] = useState({});
  
  useEffect(() => {
    console.log('Effect执行', { data });
    debugger; // 添加断点
    setData({ ...data, newField: 'value' });
  }, [data]);
}
\`\`\`

### 错误类型2: 内存泄漏
**症状**: 页面越来越慢、内存占用持续增长
**排查步骤**:

1. **检查清理函数**
\`\`\`javascript
// ❌ 常见错误：没有清理定时器
function Component() {
  useEffect(() => {
    const timer = setInterval(() => {
      console.log('定时器执行');
    }, 1000);
    
    // 🚫 没有清理定时器
  }, []);
}

// ✅ 正确方式：添加清理函数
function Component() {
  useEffect(() => {
    const timer = setInterval(() => {
      console.log('定时器执行');
    }, 1000);
    
    return () => clearInterval(timer); // ✅ 清理定时器
  }, []);
}
\`\`\`

2. **检查事件监听器**
\`\`\`javascript
// ❌ 常见错误：没有移除事件监听器
function Component() {
  useEffect(() => {
    const handleResize = () => console.log('窗口大小变化');
    window.addEventListener('resize', handleResize);
    
    // 🚫 没有移除监听器
  }, []);
}

// ✅ 正确方式：移除事件监听器
function Component() {
  useEffect(() => {
    const handleResize = () => console.log('窗口大小变化');
    window.addEventListener('resize', handleResize);
    
    return () => window.removeEventListener('resize', handleResize); // ✅ 移除监听器
  }, []);
}
\`\`\`

### 错误类型3: 状态不同步
**症状**: 显示的状态与实际状态不一致
**排查步骤**:

1. **检查状态更新逻辑**
\`\`\`javascript
// ❌ 常见错误：基于过时状态更新
function Component() {
  const [count, setCount] = useState(0);
  
  const handleDoubleClick = () => {
    setCount(count + 1); // 第一次更新
    setCount(count + 1); // 第二次更新，但count还是旧值
  };
}

// ✅ 正确方式：使用函数式更新
function Component() {
  const [count, setCount] = useState(0);
  
  const handleDoubleClick = () => {
    setCount(prev => prev + 1); // 基于最新状态更新
    setCount(prev => prev + 1); // 基于最新状态更新
  };
}
\`\`\`

## 📊 性能调试技巧

### 1. 渲染性能分析
\`\`\`javascript
// 使用React.memo优化不必要的重新渲染
const ExpensiveComponent = React.memo(function ExpensiveComponent({ data }) {
  console.log('ExpensiveComponent 渲染'); // 调试渲染次数
  
  // 复杂的渲染逻辑
  return <div>{/* 渲染内容 */}</div>;
}, (prevProps, nextProps) => {
  // 自定义比较函数，打印比较结果
  const isEqual = prevProps.data === nextProps.data;
  console.log('Props比较结果:', isEqual, { prevProps, nextProps });
  return isEqual;
});
\`\`\`

### 2. 状态更新性能监控
\`\`\`javascript
function usePerformanceMonitor(name: string) {
  const startTime = useRef<number>();
  
  useLayoutEffect(() => {
    startTime.current = performance.now();
  });
  
  useEffect(() => {
    if (startTime.current) {
      const duration = performance.now() - startTime.current;
      console.log(\`\${name} 更新耗时: \${duration.toFixed(2)}ms\`);
    }
  });
}

// 使用示例
function Component() {
  const [data, setData] = useState([]);
  
  usePerformanceMonitor('Component');
  
  return <div>{/* 渲染内容 */}</div>;
}
\`\`\`

## 🎯 调试最佳实践

### 1. 分层调试策略
\`\`\`javascript
// Level 1: 基础日志
console.log('状态更新:', newState);

// Level 2: 详细日志
console.group('状态更新详情');
console.log('旧状态:', oldState);
console.log('新状态:', newState);
console.log('更新时间:', new Date().toISOString());
console.groupEnd();

// Level 3: 性能调试
console.time('状态更新耗时');
setState(newState);
console.timeEnd('状态更新耗时');

// Level 4: 条件调试
if (process.env.NODE_ENV === 'development' && window.DEBUG_API) {
  console.log('调试信息:', debugInfo);
}
\`\`\`

### 2. 错误边界使用
\`\`\`javascript
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('API使用错误:', error, errorInfo);
    
    // 发送错误报告
    if (process.env.NODE_ENV === 'production') {
      sendErrorReport(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      return <div>API使用出现错误，请检查控制台</div>;
    }

    return this.props.children;
  }
}

// 使用方式
function App() {
  return (
    <ErrorBoundary>
      <ApiTemplateComponent />
    </ErrorBoundary>
  );
}
\`\`\`

### 3. 开发环境调试配置
\`\`\`javascript
// 创建调试配置
const DEBUG_CONFIG = {
  logStateChanges: true,
  logRenderCounts: true,
  logPerformance: true,
  enableTimeTravel: true
};

// 在开发环境中启用调试
if (process.env.NODE_ENV === 'development') {
  window.DEBUG_API = DEBUG_CONFIG;
  
  // 添加全局错误处理
  window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
  });
  
  window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
  });
}
\`\`\`

🔴 **请修改**: 根据实际API的常见错误和调试难点调整调试技巧
`;

export default debuggingTips; 