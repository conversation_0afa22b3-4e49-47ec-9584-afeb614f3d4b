/**
 * 🔧 性能优化模板说明
 * 
 * 这是一个可选Tab，适用于对性能有特殊要求的API。
 * 包含详细的性能优化策略、测试方法和最佳实践。
 * 
 * 💡 适用场景：
 * - 性能敏感的API（如状态管理、数据处理类）
 * - 大数据量处理的API
 * - 实时性要求高的API
 * - 需要特殊优化策略的API
 */

const performanceOptimization = `
# 🚀 性能优化指南

## 📊 性能基准

### 关键性能指标
- **响应时间**: < 100ms (目标) / < 200ms (可接受)
- **内存占用**: < 50MB (轻量) / < 100MB (正常)  
- **CPU使用率**: < 10% (空闲) / < 30% (高负载)
- **渲染性能**: 60fps (流畅) / 30fps (可接受)

### 测试环境
- **设备**: 中等配置设备作为基准
- **网络**: 3G网络环境测试
- **数据量**: 1000条记录作为标准测试集
- **并发**: 支持10个并发用户

## ⚡ 核心优化策略

### 1. 缓存优化
\`\`\`javascript
// 智能缓存配置
const optimizedData = useApiTemplate(params, {
  cache: {
    strategy: 'LRU',           // 最近最少使用策略
    maxSize: 100,              // 最大缓存条目
    ttl: 5 * 60 * 1000,       // 5分钟过期
    persistToLocal: true,      // 持久化到本地存储
    compressionLevel: 6        // 压缩级别
  }
});
\`\`\`

### 2. 批量处理
\`\`\`javascript
// 批量API调用优化
const batchResults = useApiTemplate(requestQueue, {
  batch: {
    enabled: true,
    maxBatchSize: 50,          // 每批最大50个请求
    flushInterval: 100,        // 100ms刷新间隔
    deduplicate: true,         // 去重相同请求
    priority: 'fifo'           // 先进先出处理
  }
});
\`\`\`

### 3. 虚拟化渲染
\`\`\`javascript
// 大数据集虚拟化
import { VirtualizedList } from 'react-window';

function OptimizedDataList() {
  const { data, isLoading } = useApiTemplate(params, {
    virtualization: {
      enabled: true,
      itemHeight: 50,            // 每项高度
      overscan: 5,               // 预渲染项目数
      threshold: 100             // 启用虚拟化的阈值
    }
  });

  const ItemRenderer = ({ index, style }) => (
    <div style={style}>
      <DataItem data={data[index]} />
    </div>
  );

  return (
    <VirtualizedList
      height={600}
      itemCount={data.length}
      itemSize={50}
    >
      {ItemRenderer}
    </VirtualizedList>
  );
}
\`\`\`

### 4. 内存管理
\`\`\`javascript
// 内存优化配置
const memoryEfficientData = useApiTemplate(params, {
  memory: {
    autoCleanup: true,         // 自动清理
    maxMemoryUsage: '100MB',   // 内存限制
    gcThreshold: 0.8,          // GC触发阈值
    weakReferences: true,      // 使用弱引用
    pooling: true              // 对象池化
  }
});
\`\`\`

## 🔍 性能监控

### 实时监控
\`\`\`javascript
function PerformanceMonitor() {
  const { data, performance } = useApiTemplate(params, {
    monitoring: {
      enabled: true,
      metrics: ['responseTime', 'memoryUsage', 'cacheHitRate'],
      onMetricUpdate: (metric, value) => {
        // 发送到监控服务
        analytics.track(\`api_\${metric}\`, value);
        
        // 性能警告
        if (metric === 'responseTime' && value > 200) {
          console.warn('API响应时间过长:', value);
        }
      }
    }
  });

  return (
    <div>
      <div className="performance-stats">
        <span>响应时间: {performance.responseTime}ms</span>
        <span>内存使用: {performance.memoryUsage}MB</span>
        <span>缓存命中率: {performance.cacheHitRate}%</span>
      </div>
      {/* 数据展示 */}
    </div>
  );
}
\`\`\`

### 性能分析工具
\`\`\`javascript
// 开发环境性能分析
if (process.env.NODE_ENV === 'development') {
  const profiler = useApiTemplate.createProfiler({
    sampleRate: 0.1,           // 10%采样率
    trackMemory: true,         // 跟踪内存使用
    trackRender: true,         // 跟踪渲染性能
    exportFormat: 'json'       // 导出格式
  });
  
  profiler.start();
  
  // 定期导出性能报告
  setInterval(() => {
    const report = profiler.export();
    console.log('性能报告:', report);
  }, 30000);
}
\`\`\`

## 🏆 优化最佳实践

### 开发阶段
1. **性能预算**: 设定明确的性能目标和限制
2. **渐进式优化**: 从最影响用户体验的部分开始优化
3. **基准测试**: 建立性能基准，持续对比改进效果
4. **工具集成**: 集成性能监控到CI/CD流程

### 生产环境
1. **CDN加速**: 使用CDN分发静态资源
2. **压缩传输**: 启用gzip/brotli压缩
3. **资源预加载**: 预加载关键资源
4. **错误监控**: 监控性能异常和错误

### 用户体验
1. **加载状态**: 提供清晰的加载反馈
2. **骨架屏**: 使用骨架屏提升感知性能
3. **渐进加载**: 优先加载关键内容
4. **离线支持**: 提供离线缓存能力

## 📈 性能优化效果

### 优化前后对比
| 指标 | 优化前 | 优化后 | 提升幅度 |
|------|--------|--------|----------|
| 首屏渲染时间 | 2.5s | 1.2s | 52% ⬆️ |
| 内存占用 | 120MB | 45MB | 62% ⬇️ |
| API响应时间 | 300ms | 85ms | 72% ⬆️ |
| 用户交互延迟 | 150ms | 45ms | 70% ⬆️ |

### 真实案例
- **电商平台**: 商品列表加载时间从3.2s降至0.8s
- **数据看板**: 大数据可视化内存使用减少80%
- **社交应用**: 消息列表滚动帧率从25fps提升至60fps

🔴 **请修改**: 根据实际API的性能特性调整优化策略和数据
`;

export default performanceOptimization; 