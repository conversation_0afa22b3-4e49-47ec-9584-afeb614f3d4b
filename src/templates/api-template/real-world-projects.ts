/**
 * 🔧 实战项目模板说明
 * 
 * 这是一个可选Tab，适用于常用的核心API。
 * 包含完整的项目案例、架构设计和最佳实践示例。
 * 
 * 💡 适用场景：
 * - 常用的核心API（如useState、useEffect、数据处理类API）
 * - 需要展示实际应用场景的API
 * - 有复杂集成需求的API
 * - 适合构建完整项目的API
 */

const realWorldProjects = `
# 🏗️ 实战项目案例

## 📋 项目概览

本部分包含3个完整的实战项目，展示API模板名称在真实场景中的应用，从简单到复杂，覆盖不同的业务需求和技术挑战。

## 🚀 项目1：任务管理应用 (简单级)

### 项目背景
构建一个类似Todoist的任务管理应用，支持任务的增删改查、状态切换、优先级设置等功能。

### 技术栈
- React 18 + TypeScript
- API模板名称 (核心状态管理)
- Ant Design (UI组件)
- localStorage (数据持久化)

### 核心功能
- ✅ 任务列表展示
- ✅ 添加/删除任务
- ✅ 任务状态切换
- ✅ 任务优先级设置
- ✅ 数据本地持久化

### 实现代码

#### 1. 数据模型定义
\`\`\`typescript
// types/task.ts
export interface Task {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  createdAt: Date;
  updatedAt: Date;
  dueDate?: Date;
}

export interface TaskListState {
  tasks: Task[];
  filter: 'all' | 'active' | 'completed';
  sortBy: 'priority' | 'dueDate' | 'createdAt';
}
\`\`\`

#### 2. 核心状态管理
\`\`\`typescript
//import { useApiTemplate } from 'api-library';

export function useTaskManager() {
  // 🔴 请修改：根据实际API调整使用方式
  const { data: taskState, loading, error, updateData } = useApiTemplate(
    'taskManager',
    {
      initialState: {
        tasks: [],
        filter: 'all' as const,
        sortBy: 'createdAt' as const
      },
      persist: true, // 启用持久化
      storage: 'localStorage'
    }
  );

  // 添加任务
  const addTask = useCallback((taskData: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newTask: Task = {
      ...taskData,
      id: crypto.randomUUID(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    updateData(prevState => ({
      ...prevState,
      tasks: [...prevState.tasks, newTask]
    }));
  }, [updateData]);

  // 更新任务
  const updateTask = useCallback((taskId: string, updates: Partial<Task>) => {
    updateData(prevState => ({
      ...prevState,
      tasks: prevState.tasks.map(task =>
        task.id === taskId
          ? { ...task, ...updates, updatedAt: new Date() }
          : task
      )
    }));
  }, [updateData]);

  // 删除任务
  const deleteTask = useCallback((taskId: string) => {
    updateData(prevState => ({
      ...prevState,
      tasks: prevState.tasks.filter(task => task.id !== taskId)
    }));
  }, [updateData]);

  // 切换任务完成状态
  const toggleTaskCompletion = useCallback((taskId: string) => {
    updateTask(taskId, { 
      completed: !taskState.tasks.find(t => t.id === taskId)?.completed 
    });
  }, [updateTask, taskState.tasks]);

  // 设置过滤器
  const setFilter = useCallback((filter: TaskListState['filter']) => {
    updateData(prevState => ({ ...prevState, filter }));
  }, [updateData]);

  // 设置排序
  const setSortBy = useCallback((sortBy: TaskListState['sortBy']) => {
    updateData(prevState => ({ ...prevState, sortBy }));
  }, [updateData]);

  // 计算过滤和排序后的任务列表
  const filteredAndSortedTasks = useMemo(() => {
    if (!taskState) return [];

    let filtered = taskState.tasks;

    // 应用过滤器
    switch (taskState.filter) {
      case 'active':
        filtered = filtered.filter(task => !task.completed);
        break;
      case 'completed':
        filtered = filtered.filter(task => task.completed);
        break;
      default:
        break;
    }

    // 应用排序
    return filtered.sort((a, b) => {
      switch (taskState.sortBy) {
        case 'priority':
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        case 'dueDate':
          if (!a.dueDate && !b.dueDate) return 0;
          if (!a.dueDate) return 1;
          if (!b.dueDate) return -1;
          return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        case 'createdAt':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });
  }, [taskState]);

  return {
    // 状态
    tasks: filteredAndSortedTasks,
    filter: taskState?.filter || 'all',
    sortBy: taskState?.sortBy || 'createdAt',
    loading,
    error,
    
    // 操作
    addTask,
    updateTask,
    deleteTask,
    toggleTaskCompletion,
    setFilter,
    setSortBy,
    
    // 统计信息
    stats: {
      total: taskState?.tasks.length || 0,
      completed: taskState?.tasks.filter(t => t.completed).length || 0,
      active: taskState?.tasks.filter(t => !t.completed).length || 0
    }
  };
}
\`\`\`

#### 3. 主要组件实现
\`\`\`typescript
// components/TaskManager.tsx
import React from 'react';
import { Button, Input, Select, Card, Checkbox, Tag, Space } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';


const { Option } = Select;

export function TaskManager() {
  const {
    tasks,
    filter,
    sortBy,
    loading,
    stats,
    addTask,
    updateTask,
    deleteTask,
    toggleTaskCompletion,
    setFilter,
    setSortBy
  } = useTaskManager();

  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [newTaskPriority, setNewTaskPriority] = useState<'low' | 'medium' | 'high'>('medium');

  const handleAddTask = () => {
    if (!newTaskTitle.trim()) return;

    addTask({
      title: newTaskTitle.trim(),
      priority: newTaskPriority,
      completed: false
    });

    setNewTaskTitle('');
    setNewTaskPriority('medium');
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'default';
    }
  };

  if (loading) {
    return <div className="text-center p-8">加载中...</div>;
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">任务管理器</h1>
      
      {/* 统计信息 */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <Card size="small">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <div className="text-gray-500">总任务</div>
          </div>
        </Card>
        <Card size="small">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
            <div className="text-gray-500">已完成</div>
          </div>
        </Card>
        <Card size="small">
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{stats.active}</div>
            <div className="text-gray-500">进行中</div>
          </div>
        </Card>
      </div>

      {/* 添加任务 */}
      <Card className="mb-6">
        <div className="flex gap-4 items-end">
          <div className="flex-1">
            <label className="block text-sm font-medium mb-1">任务标题</label>
            <Input
              value={newTaskTitle}
              onChange={(e) => setNewTaskTitle(e.target.value)}
              placeholder="输入新任务..."
              onPressEnter={handleAddTask}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">优先级</label>
            <Select
              value={newTaskPriority}
              onChange={setNewTaskPriority}
              style={{ width: 120 }}
            >
              <Option value="low">低</Option>
              <Option value="medium">中</Option>
              <Option value="high">高</Option>
            </Select>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddTask}
            disabled={!newTaskTitle.trim()}
          >
            添加
          </Button>
        </div>
      </Card>

      {/* 过滤和排序 */}
      <div className="flex gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium mb-1">过滤</label>
          <Select value={filter} onChange={setFilter} style={{ width: 120 }}>
            <Option value="all">全部</Option>
            <Option value="active">进行中</Option>
            <Option value="completed">已完成</Option>
          </Select>
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">排序</label>
          <Select value={sortBy} onChange={setSortBy} style={{ width: 120 }}>
            <Option value="createdAt">创建时间</Option>
            <Option value="priority">优先级</Option>
            <Option value="dueDate">截止日期</Option>
          </Select>
        </div>
      </div>

      {/* 任务列表 */}
      <div className="space-y-3">
        {tasks.map(task => (
          <Card
            key={task.id}
            size="small"
            className={\`transition-all \${task.completed ? 'opacity-60' : ''}\`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 flex-1">
                <Checkbox
                  checked={task.completed}
                  onChange={() => toggleTaskCompletion(task.id)}
                />
                <div className="flex-1">
                  <div className={\`\${task.completed ? 'line-through text-gray-500' : ''}\`}>
                    {task.title}
                  </div>
                  {task.description && (
                    <div className="text-sm text-gray-500 mt-1">
                      {task.description}
                    </div>
                  )}
                </div>
                <Tag color={getPriorityColor(task.priority)}>
                  {task.priority === 'high' ? '高' : task.priority === 'medium' ? '中' : '低'}
                </Tag>
              </div>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => deleteTask(task.id)}
                size="small"
              />
            </div>
          </Card>
        ))}
        
        {tasks.length === 0 && (
          <div className="text-center py-12 text-gray-500">
            {filter === 'all' ? '暂无任务' : \`暂无\${filter === 'active' ? '进行中' : '已完成'}的任务\`}
          </div>
        )}
      </div>
    </div>
  );
}
\`\`\`

### 项目亮点
- ✅ **完整的CRUD操作**：展示API的完整生命周期管理
- ✅ **数据持久化**：利用API的存储能力
- ✅ **实时状态同步**：多组件间的状态一致性
- ✅ **性能优化**：合理使用memoization
- ✅ **用户体验**：流畅的交互和反馈

## 🌟 项目2：实时聊天应用 (中级)

### 项目背景
构建一个支持多房间的实时聊天应用，包含消息发送、用户在线状态、消息历史等功能。

### 技术栈
- React 18 + TypeScript
- API模板名称 (状态管理)
- WebSocket (实时通信)
- IndexedDB (消息存储)

### 核心功能
- 💬 实时消息收发
- 👥 多聊天房间
- 🟢 用户在线状态
- 📚 消息历史记录
- 🔍 消息搜索

### 实现要点
\`\`\`typescript
// 核心状态管理
const { chatState, updateState } = useApiTemplate('chatApp', {
  realTime: true,
  websocket: {
    url: 'ws://localhost:8080',
    autoReconnect: true
  },
  storage: {
    type: 'indexedDB',
    version: 1
  }
});

// 实时消息处理
useEffect(() => {
  const ws = new WebSocket('ws://localhost:8080');
  
  ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    updateState(prev => ({
      ...prev,
      messages: [...prev.messages, message],
      lastActivity: Date.now()
    }));
  };
  
  return () => ws.close();
}, [updateState]);
\`\`\`

## 🏢 项目3：企业级数据面板 (高级)

### 项目背景
构建一个企业级的数据可视化面板，支持实时数据更新、多维度分析、权限控制等企业级功能。

### 技术栈
- React 18 + TypeScript
- API模板名称 (复杂状态管理)
- Recharts (数据可视化)
- React Query (数据获取)
- WebWorker (大数据处理)

### 核心功能
- 📊 实时数据可视化
- 🔄 多数据源集成
- 👤 权限控制
- 📈 性能监控
- 🎯 智能告警

### 架构设计
\`\`\`typescript
// 企业级状态架构
const useEnterpriseDataPlatform = () => {
  const { 
    dashboardState, 
    updateDashboard,
    performance 
  } = useApiTemplate('enterprise-dashboard', {
    // 高性能配置
    optimization: {
      virtualScrolling: true,
      dataStreaming: true,
      backgroundProcessing: true
    },
    
    // 权限控制
    security: {
      rbac: true,
      auditLog: true,
      dataEncryption: true
    },
    
    // 监控配置
    monitoring: {
      metrics: ['performance', 'errors', 'usage'],
      alerting: true
    }
  });

  return {
    dashboardState,
    updateDashboard,
    performance,
    // ... 其他企业级功能
  };
};
\`\`\`

## 📊 项目对比分析

| 特性 | 任务管理 | 实时聊天 | 企业面板 |
|------|----------|----------|----------|
| **复杂度** | 简单 | 中等 | 高级 |
| **数据量** | 小(KB级) | 中等(MB级) | 大(GB级) |
| **实时性** | 无要求 | 高要求 | 中等要求 |
| **并发用户** | 1人 | 100人 | 1000+人 |
| **状态管理** | 本地状态 | 实时同步 | 分布式状态 |
| **性能要求** | 基础 | 中等 | 极高 |

## 🎯 学习路径建议

### 初学者
1. 从**任务管理应用**开始
2. 掌握基础的CRUD操作
3. 理解状态管理原理

### 进阶开发者
1. 实现**实时聊天应用**
2. 学习WebSocket集成
3. 掌握复杂状态同步

### 资深开发者
1. 构建**企业级数据面板**
2. 优化性能和扩展性
3. 实现企业级功能

## 💡 最佳实践总结

### 状态设计
- 🎯 **单一数据源**: 避免状态分散
- 🔄 **不可变更新**: 确保状态一致性
- 📊 **合理拆分**: 避免过度复杂

### 性能优化
- ⚡ **懒加载**: 按需加载数据
- 🎯 **缓存策略**: 合理使用缓存
- 🔧 **代码分割**: 优化包体积

### 用户体验
- 🚀 **响应性**: 及时的状态反馈
- 🛡️ **错误处理**: 优雅的异常处理
- 💫 **加载状态**: 清晰的加载指示

🔴 **请修改**: 根据实际API的特性调整项目案例和代码示例
`;

export default realWorldProjects; 