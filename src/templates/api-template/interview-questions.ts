import { InterviewQuestion } from '../../types/api';

/**
 * 🔧 面试题模板说明
 * 
 * 这个文件定义API相关的面试题，包括：
 * - 3道不同难度的面试题（简单、中等、困难）
 * - 每道题包含：问题、简要答案、详细解析、代码示例
 * - 突出高频考点和实际应用
 * 
 * 💡 编写技巧：
 * - 问题设计贴近实际工作场景
 * - 答案结构化，要点清晰
 * - 提供代码示例加深理解
 * - 标注难度和出现频率
 */

const interviewQuestions: InterviewQuestion[] = [
  // === 🟢 简单题：基础概念 ===
  {
    id: 1,
    question: 'API模板名称是什么？它解决了什么问题？请简要说明其核心功能。',
    answer: {
      brief: 'API模板名称是一个[功能描述]工具，主要解决[核心问题]，通过[关键特性]提升开发效率。',
      detailed: `
### 📋 定义和功能

API模板名称是[详细定义]，它的核心功能包括：

1. **[功能1]**：[具体说明功能1的作用和实现方式]
2. **[功能2]**：[具体说明功能2的作用和实现方式]  
3. **[功能3]**：[具体说明功能3的作用和实现方式]

### 🎯 解决的问题

- **问题1**：[传统方案的痛点] → [API如何解决]
- **问题2**：[开发中的困难] → [API提供的改进]
- **问题3**：[性能或体验问题] → [API带来的优化]

### 💡 核心价值

- ✅ **简化开发**：减少[具体数据]的代码量
- ✅ **提升性能**：优化[具体指标]的性能
- ✅ **增强体验**：改善[具体方面]的用户体验

🔴 **请修改**: 根据实际API特性调整答案内容
      `,
      code: `
// 基础使用示例
import { useApiTemplate } from 'your-library';

function BasicExample() {
  // 🔴 请修改：替换为实际API调用
  const result = useApiTemplate('参数');
  
  return (
    <div>
      <h3>基础用法演示</h3>
      <p>结果: {result}</p>
    </div>
  );
}
      `
    },
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念'
  },

  // === 🟡 中等题：实现原理 ===
  {
    id: 2,
    question: 'API模板名称的底层实现原理是什么？它是如何工作的？请结合具体场景说明。',
    answer: {
      brief: 'API模板名称基于[核心技术]实现，通过[关键机制]处理数据，采用[设计模式]确保[核心特性]。',
      detailed: `
### 🔍 底层实现原理

#### 1. 核心架构
API模板名称采用[架构模式]，主要组成部分：
- **[组件1]**：负责[具体职责]
- **[组件2]**：处理[具体功能]
- **[组件3]**：管理[具体任务]

#### 2. 工作流程
\`\`\`
[步骤1] → [步骤2] → [步骤3] → [步骤4]
\`\`\`

1. **初始化阶段**：[详细说明初始化过程]
2. **处理阶段**：[详细说明核心处理逻辑]
3. **输出阶段**：[详细说明结果生成过程]
4. **清理阶段**：[详细说明资源清理机制]

#### 3. 关键技术点

**[技术点1]**：[技术实现原理和优势]
- 解决问题：[具体问题]
- 实现方式：[具体方案]
- 技术优势：[具体优势]

**[技术点2]**：[技术实现原理和优势]
- 应用场景：[具体场景]
- 性能特点：[性能数据]
- 最佳实践：[使用建议]

### 🏆 设计亮点

- **[亮点1]**：[设计理念和实现效果]
- **[亮点2]**：[技术创新和业务价值]
- **[亮点3]**：[性能优化和用户体验]

🔴 **请修改**: 根据实际API的实现原理调整内容
      `,
      code: `
// 实现原理演示
import { useApiTemplate } from 'your-library';

function ImplementationDemo() {
  // 🔴 请修改：展示API的内部工作机制
  const { data, loading, error } = useApiTemplate('复杂参数', {
    onInit: () => console.log('初始化阶段'),
    onProcess: (data) => console.log('处理阶段:', data),
    onComplete: (result) => console.log('完成阶段:', result),
    debug: true // 开启调试模式查看内部流程
  });

  // 展示不同状态的处理
  if (loading) return <div>处理中...</div>;
  if (error) return <div>错误: {error.message}</div>;
  
  return (
    <div>
      <h3>实现原理演示</h3>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
}

// 高级用法：自定义处理器
function AdvancedDemo() {
  const customProcessor = (input) => {
    // 自定义处理逻辑
    return input.map(item => ({
      ...item,
      processed: true,
      timestamp: Date.now()
    }));
  };

  const result = useApiTemplate(data, {
    processor: customProcessor,
    cache: true,
    optimize: 'performance'
  });

  return <div>高级用法结果</div>;
}
      `
    },
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    visualization: `
\`\`\`mermaid
sequenceDiagram
    participant U as 用户组件
    participant A as API模板
    participant P as 处理器
    participant C as 缓存层
    
    U->>A: 调用API(参数)
    A->>A: 参数验证
    A->>C: 检查缓存
    C-->>A: 缓存结果/空
    
    alt 缓存未命中
        A->>P: 执行处理逻辑
        P->>P: 数据转换
        P-->>A: 处理结果
        A->>C: 更新缓存
    end
    
    A-->>U: 返回最终结果
    
    Note over U,C: 🔴 请根据实际API调整时序图
\`\`\`
    `
  },

  // === 🔴 困难题：高级应用 ===
  {
    id: 3,
    question: '在大型项目中使用API模板名称时，如何处理性能优化、错误处理和状态管理？请提供完整的解决方案。',
    answer: {
      brief: '大型项目中需要考虑缓存策略、错误边界、状态同步等多个维度，通过合理的架构设计和最佳实践确保系统稳定性和性能。',
      detailed: `
### 🚀 大型项目解决方案

#### 1. 性能优化策略

**缓存机制**
- **多层缓存**：内存缓存 + 本地存储 + 远程缓存
- **智能失效**：基于时间和依赖的缓存失效策略
- **预加载**：预测性数据加载和后台更新

**渲染优化**
- **懒加载**：按需加载API数据和组件
- **虚拟化**：大列表和复杂数据的虚拟渲染
- **批量更新**：合并多个状态更新操作

**网络优化**
- **请求合并**：批量API调用减少网络开销
- **并发控制**：限制同时请求数量
- **重试机制**：智能重试和指数退避

#### 2. 错误处理体系

**分层错误处理**
```typescript
interface ErrorHandlingStrategy {
  // 网络层错误
  networkError: (error: NetworkError) => void;
  // 业务层错误  
  businessError: (error: BusinessError) => void;
  // 用户层错误
  userError: (error: UserError) => void;
}
```

**错误恢复机制**
- **自动重试**：网络错误的自动重试
- **降级策略**：服务不可用时的备选方案
- **用户提示**：友好的错误信息和操作建议

#### 3. 状态管理架构

**全局状态设计**
- **模块化状态**：按功能域划分状态
- **状态同步**：多组件间的状态一致性
- **状态持久化**：关键状态的本地持久化

**状态更新模式**
- **乐观更新**：先更新UI再同步服务器
- **悲观更新**：服务器确认后更新UI
- **混合模式**：根据操作类型选择合适模式

#### 4. 监控和调试

**性能监控**
- **API调用耗时**：监控每个API的响应时间
- **错误率统计**：实时监控错误发生率
- **用户体验指标**：FCP、LCP等关键指标

**调试工具**
- **开发模式**：详细的调试信息和警告
- **日志系统**：结构化的日志记录
- **性能分析**：内存使用和渲染性能分析

### 🏆 最佳实践总结

1. **架构设计**：分层设计，职责明确
2. **性能优先**：缓存为王，优化为本
3. **错误友好**：预期错误，优雅处理
4. **状态清晰**：单一数据源，状态可追踪
5. **监控完善**：实时监控，快速定位

🔴 **请修改**: 根据实际API特性调整解决方案
      `,
      code: `
// 大型项目架构示例
import { 
  useApiTemplate, 
  ErrorBoundary, 
  PerformanceMonitor 
} from 'your-library';

// 1. 高级错误处理
class ApiErrorHandler {
  static handleError(error, context) {
    // 根据错误类型采用不同策略
    if (error.type === 'NETWORK_ERROR') {
      return this.handleNetworkError(error, context);
    } else if (error.type === 'BUSINESS_ERROR') {
      return this.handleBusinessError(error, context);
    }
    
    // 默认错误处理
    this.logError(error, context);
    this.notifyUser(error);
  }
  
  static async handleNetworkError(error, context) {
    // 网络错误自动重试
    const maxRetries = 3;
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await context.retry();
      } catch (retryError) {
        if (i === maxRetries - 1) throw retryError;
        await this.delay(Math.pow(2, i) * 1000); // 指数退避
      }
    }
  }
}

// 2. 性能优化Hook
function useOptimizedApi(params, options = {}) {
  const {
    cache = true,
    preload = false,
    batchSize = 10,
    retryCount = 3
  } = options;

  // 缓存策略
  const cacheKey = useMemo(() => 
    generateCacheKey(params), [params]
  );
  
  // 批量处理
  const [batchQueue, setBatchQueue] = useState([]);
  
  // 🔴 请修改：替换为实际API调用
  const { data, loading, error } = useApiTemplate(params, {
    cache,
    onError: (error) => ApiErrorHandler.handleError(error, { params }),
    onSuccess: (data) => {
      // 预加载相关数据
      if (preload) {
        preloadRelatedData(data);
      }
    }
  });

  // 性能监控
  useEffect(() => {
    PerformanceMonitor.track('api_call', {
      endpoint: params.endpoint,
      duration: Date.now() - startTime,
      success: !error
    });
  }, [data, error]);

  return { data, loading, error };
}

// 3. 企业级组件
function EnterpriseApiProvider({ children }) {
  const [globalState, dispatch] = useReducer(apiReducer, initialState);
  
  return (
    <ErrorBoundary
      fallback={<ErrorFallback />}
      onError={(error, errorInfo) => {
        // 错误上报
        reportError(error, errorInfo);
      }}
    >
      <PerformanceMonitor>
        <ApiContext.Provider value={{ globalState, dispatch }}>
          {children}
        </ApiContext.Provider>
      </PerformanceMonitor>
    </ErrorBoundary>
  );
}

// 4. 监控和调试
function useApiMonitoring() {
  useEffect(() => {
    // 性能指标收集
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'measure') {
          analytics.track('api_performance', {
            name: entry.name,
            duration: entry.duration,
            timestamp: entry.startTime
          });
        }
      });
    });
    
    observer.observe({ entryTypes: ['measure'] });
    
    return () => observer.disconnect();
  }, []);
  
  return {
    trackApiCall: (name, fn) => {
      performance.mark(\`\${name}-start\`);
      const result = fn();
      performance.mark(\`\${name}-end\`);
      performance.measure(name, \`\${name}-start\`, \`\${name}-end\`);
      return result;
    }
  };
}

// 使用示例
function ProductionApp() {
  const { trackApiCall } = useApiMonitoring();
  
  const { data, loading, error } = useOptimizedApi(
    { endpoint: '/api/data', params: { id: 1 } },
    { 
      cache: true, 
      preload: true, 
      retryCount: 3 
    }
  );

  return (
    <EnterpriseApiProvider>
      <div>
        {/* 应用内容 */}
      </div>
    </EnterpriseApiProvider>
  );
}
      `
    },
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计'
  }
];

export default interviewQuestions; 