import { BasicInfo } from '../../types/api';

/**
 * 🔧 基本信息模板说明
 * 
 * 这个文件定义API的基础信息，包括：
 * - 简介和定义
 * - 语法和参数说明
 * - 返回值描述
 * - 核心特性
 * - 使用场景
 * - 最佳实践
 * - 注意事项和限制
 */

const basicInfo: BasicInfo = {
  // === 📝 API简介 ===
  introduction: `
API模板名称是一个[功能描述]的工具，主要用于[主要用途]。它提供了[核心能力]，
帮助开发者[解决的问题]。

🔴 **请修改**: 
- 将"API模板名称"替换为实际API名称
- 描述实际功能和用途
- 说明解决的核心问题

💡 **编写技巧**:
- 2-3句话概括核心功能
- 突出API的独特价值
- 使用开发者容易理解的语言
  `,

  // === ⚡ 语法格式 ===
  syntax: `
// 基础用法
const result = useApiTemplate(parameter1, parameter2);

// 带配置的用法
const result = useApiTemplate(parameter1, {
  option1: 'value1',
  option2: true,
  option3: 100
});

🔴 **请修改**: 替换为实际API语法
  `,

  // === 📋 参数说明 ===
  parameters: [
    {
      name: 'parameter1',
      type: 'string',
      description: '第一个参数的描述，说明其作用和用途',
      required: true,
      details: '详细说明参数的使用方法、约束条件等'
    },
    {
      name: 'parameter2',
      type: 'object',
      description: '配置对象，包含各种选项',
      required: false,
      default: '{}',
      details: '配置对象的具体属性和默认值说明'
    },
    {
      name: 'options.option1',
      type: 'string',
      description: '配置选项1的说明',
      required: false,
      default: 'defaultValue'
    },
    {
      name: 'options.option2',
      type: 'boolean',
      description: '配置选项2的说明',
      required: false,
      default: 'false'
    }
    // 🔴 **请修改**: 根据实际API参数调整
  ],

  // === 📤 返回值说明 ===
  returnValue: {
    type: 'ReturnType', // 🔴 请修改为实际返回类型
    description: '返回值的详细描述，包括类型、结构、用途等信息'
  },

  // === ⭐ 核心特性 ===
  keyFeatures: [
    {
      feature: '特性1名称',
      description: '特性1的详细描述，说明其优势和应用场景',
      importance: 'critical' as const,
      details: '特性1的深入分析和技术细节'
    },
    {
      feature: '特性2名称', 
      description: '特性2的详细描述',
      importance: 'high' as const,
      details: '特性2的技术实现和使用建议'
    },
    {
      feature: '特性3名称',
      description: '特性3的详细描述',
      importance: 'medium' as const
    }
    // 🔴 **请修改**: 根据实际API特性调整
  ],

  // === 🎯 常见使用场景 ===
  commonUseCases: [
    {
      title: '场景1：基础用法',
      description: '最基本的使用场景，适合新手入门',
      code: `
// 场景1示例代码
const result = useApiTemplate('基础参数');
console.log(result);
      `
    },
    {
      title: '场景2：高级配置',
      description: '带有复杂配置的使用场景',
      code: `
// 场景2示例代码  
const result = useApiTemplate('参数', {
  option1: 'customValue',
  option2: true
});
      `
    },
    {
      title: '场景3：实际应用',
      description: '在真实项目中的应用示例',
      code: `
// 场景3示例代码
function RealWorldComponent() {
  const data = useApiTemplate(props.input, {
    transform: true,
    cache: true
  });
  
  return <div>{data}</div>;
}
      `
    }
    // 🔴 **请修改**: 根据实际使用场景调整
  ],

  // === ✅ 最佳实践 ===
  bestPractices: [
    '🎯 实践1：清晰描述最佳使用方法',
    '⚡ 实践2：性能优化相关的建议',
    '🔒 实践3：安全性和稳定性建议',
    '📚 实践4：代码可读性和维护性建议',
    '🐛 实践5：错误处理和调试建议'
    // 🔴 **请修改**: 根据实际API调整最佳实践
  ],

  // === ⚠️ 注意事项 ===
  warnings: [
    '⚠️ 警告1：重要的使用限制或注意事项',
    '🚨 警告2：可能导致问题的使用方式',
    '💡 警告3：版本兼容性相关提醒'
    // 🔴 **请修改**: 根据实际API调整警告信息
  ],

  // === 📝 补充说明 ===
  notes: [
    '📝 说明1：补充的技术细节',
    '🔍 说明2：与其他API的关系',
    '📊 说明3：性能特性说明'
    // 🔴 **请修改**: 根据需要添加补充说明
  ],

  // === 🚫 使用限制 ===
  limitations: [
    '🚫 限制1：明确的使用限制',
    '📱 限制2：平台或环境限制',  
    '🔢 限制3：参数或数据限制'
    // 🔴 **请修改**: 根据实际API调整限制条件
  ]
};

export default basicInfo; 