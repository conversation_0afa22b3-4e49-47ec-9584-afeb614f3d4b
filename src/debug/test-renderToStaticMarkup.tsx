import React from 'react';
import { allReactApis } from '../data/react/index';

const TestRenderToStaticMarkup: React.FC = () => {
  const renderToStaticMarkup = allReactApis.find(api => api.id === 'renderToStaticMarkup');
  
  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h2>🔍 renderToStaticMarkup 数据结构测试</h2>
      
      {renderToStaticMarkup ? (
        <div>
          <h3>✅ API Found</h3>
          <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
            {JSON.stringify({
              id: renderToStaticMarkup.id,
              title: renderToStaticMarkup.title,
              category: renderToStaticMarkup.category,
              hasBasicInfo: !!renderToStaticMarkup.basicInfo,
              hasBusinessScenarios: !!renderToStaticMarkup.businessScenarios,
              businessScenariosLength: renderToStaticMarkup.businessScenarios?.length || 0,
              hasInterviewQuestions: !!renderToStaticMarkup.interviewQuestions,
              interviewQuestionsLength: renderToStaticMarkup.interviewQuestions?.length || 0,
              hasCommonQuestions: !!renderToStaticMarkup.commonQuestions,
              commonQuestionsLength: renderToStaticMarkup.commonQuestions?.length || 0,
              hasImplementation: !!renderToStaticMarkup.implementation,
              hasKnowledgeArchaeology: !!renderToStaticMarkup.knowledgeArchaeology,
              hasPerformanceOptimization: !!renderToStaticMarkup.performanceOptimization,
              hasDebuggingTips: !!renderToStaticMarkup.debuggingTips,
              hasEssenceInsights: !!renderToStaticMarkup.essenceInsights
            }, null, 2)}
          </pre>
          
          {renderToStaticMarkup.basicInfo && (
            <div>
              <h4>📋 Basic Info</h4>
              <pre style={{ background: '#e8f5e8', padding: '10px', borderRadius: '4px' }}>
                Completion Status: {renderToStaticMarkup.basicInfo.completionStatus}
                Description: {renderToStaticMarkup.basicInfo.description?.substring(0, 100)}...
              </pre>
            </div>
          )}
          
          {renderToStaticMarkup.interviewQuestions && (
            <div>
              <h4>🎯 Interview Questions</h4>
              <pre style={{ background: '#e8f5e8', padding: '10px', borderRadius: '4px' }}>
                Questions Count: {renderToStaticMarkup.interviewQuestions.length}
                First Question: {renderToStaticMarkup.interviewQuestions[0]?.question?.substring(0, 100)}...
              </pre>
            </div>
          )}
        </div>
      ) : (
        <div>
          <h3>❌ API Not Found</h3>
          <p>Available APIs: {allReactApis.map(api => api.id).join(', ')}</p>
        </div>
      )}
    </div>
  );
};

export default TestRenderToStaticMarkup;
