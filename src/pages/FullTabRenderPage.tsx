import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Alert, Spin, Typography, Card, Tag, Button, Divider } from 'antd';
import { CheckCircleOutlined, ExclamationCircleOutlined, CopyOutlined } from '@ant-design/icons';
import ErrorBoundary from '@/components/ErrorBoundary';
import TabRenderMonitor from '@/components/TabRenderMonitor';
import MermaidChart from '@/components/MermaidChart';

const { Title, Text, Paragraph } = Typography;

// 复制CheatSheet中的CodeHighlight组件
const CodeHighlight: React.FC<{
  code: string;
  language: string;
  title?: string;
  onCopy?: () => void;
}> = ({ code, language, title, onCopy }) => {
  return (
    <div style={{ marginBottom: '16px' }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        padding: '8px 12px',
        backgroundColor: '#f5f5f5',
        borderRadius: '6px 6px 0 0',
        border: '1px solid #d9d9d9',
        borderBottom: 'none'
      }}>
        <Text strong>{title || '代码示例'}</Text>
        <Button 
          size="small" 
          icon={<CopyOutlined />} 
          onClick={onCopy}
          type="text"
        >
          复制
        </Button>
      </div>
      <pre style={{
        background: '#f6f8fa',
        padding: '16px',
        borderRadius: '0 0 6px 6px',
        overflow: 'auto',
        fontSize: '13px',
        lineHeight: '1.4',
        border: '1px solid #d9d9d9',
        margin: 0
      }}>
        <code style={{ color: '#24292e' }}>{code}</code>
      </pre>
    </div>
  );
};

// 基本信息组件 - 展开所有内部tabs，与CheatSheet保持一致
const BasicInfoComponent: React.FC<{ item: any, copyToClipboard: (text: string) => void }> = ({ item, copyToClipboard }) => {
  return (
    <div className="space-y-8">
      {/* 简洁Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">📖</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">基本信息</Title>
        <span className="text-slate-500 text-sm">核心概念与基础语法</span>
      </div>

      {/* 1.1 核心概览 */}
      <Card title="1.1 📋 核心概览" size="small" style={{ marginBottom: '16px' }}>
        <div className="space-y-4">
          {/* 核心定义 */}
          <div className="bg-white border border-slate-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <div className="w-6 h-6 bg-blue-500 rounded-md flex items-center justify-center">
                <span className="text-white text-xs">📋</span>
              </div>
              <Title level={5} className="mb-0 text-slate-900 font-medium text-base">核心定义</Title>
            </div>
            <div className="bg-blue-50 p-3 rounded-md border border-blue-200/50">
              <Text className="text-blue-900 font-normal text-sm leading-relaxed">
                {item.description}
              </Text>
            </div>
          </div>

          {/* 核心特性 */}
          {item.basicInfo?.keyFeatures && (
            <div className="bg-white border border-slate-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-6 h-6 bg-emerald-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-xs">⭐</span>
                </div>
                <Title level={5} className="mb-0 text-slate-900 font-medium text-base">核心特性</Title>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {item.basicInfo.keyFeatures.slice(0, 4).map((feature: any, index: number) => (
                  <div key={index} className="p-3 bg-emerald-50 rounded-md border border-emerald-200/50">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="w-2 h-2 bg-emerald-500 rounded-full"></span>
                      <Text strong className="text-emerald-800 text-xs">{feature.feature}</Text>
                    </div>
                    <Text className="text-emerald-700 text-xs leading-relaxed">{feature.description}</Text>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 快速示例 */}
          <div className="bg-white border border-slate-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <div className="w-6 h-6 bg-purple-500 rounded-md flex items-center justify-center">
                <span className="text-white text-xs">💻</span>
              </div>
              <Title level={5} className="mb-0 text-slate-900 font-medium text-base">快速示例</Title>
            </div>
            <CodeHighlight
              code={item.basicInfo?.quickExample || item.example}
              language="javascript"
              title="基本用法"
              onCopy={() => copyToClipboard(item.basicInfo?.quickExample || item.example)}
            />
          </div>

          {/* 业务场景图表 */}
          {item.basicInfo?.scenarioDiagram && (
            <div className="bg-white border border-slate-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-6 h-6 bg-cyan-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-xs">🗺️</span>
                </div>
                <Title level={5} className="mb-0 text-slate-900 font-medium text-base">业务场景图</Title>
              </div>

              {/* 处理单个图表或多个图表 */}
              {typeof item.basicInfo.scenarioDiagram === 'string' ? (
                <div className="bg-cyan-50 p-4 rounded-md border border-cyan-200/50">
                  <MermaidChart chart={item.basicInfo.scenarioDiagram} title="业务场景图" />
                </div>
              ) : (
                <div className="space-y-6">
                  {item.basicInfo.scenarioDiagram.map((diagram: any, index: number) => (
                    <div key={index} className="bg-cyan-50 p-4 rounded-md border border-cyan-200/50">
                      <div className="mb-3">
                        <h6 className="text-slate-900 font-medium text-sm mb-1">{diagram.title}</h6>
                        <p className="text-slate-600 text-xs leading-relaxed">{diagram.description}</p>
                      </div>
                      <MermaidChart chart={diagram.diagram} title={diagram.title} />
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </Card>

      {/* 1.2 语法详解 */}
      <Card title="1.2 🔧 语法详解" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-white border border-slate-200 rounded-lg p-4">
          <div className="space-y-4">
            <div>
              <Text strong className="text-slate-800 block mb-2">基本用法</Text>
              <CodeHighlight
                code={item.basicInfo?.syntax || item.syntax || `// ${item.title || 'API'} 基本用法示例\nimport React from 'react';\n\nfunction Example() {\n  return <div>示例代码</div>;\n}`}
                language="javascript"
                title="基本语法"
                onCopy={() => copyToClipboard(item.basicInfo?.syntax || item.syntax || '')}
              />
            </div>

            {/* 参数说明 */}
            {item.parameters && item.parameters.length > 0 && (
              <div>
                <Text strong className="text-slate-800 block mb-3">参数说明</Text>
                <div className="space-y-2">
                  {item.parameters.slice(0, 3).map((param: any, index: number) => (
                    <div key={index} className="p-3 bg-slate-50 rounded-md border border-slate-200/50">
                      <div className="flex items-center gap-2 mb-1">
                        <Text strong className="text-slate-900">{param.name}</Text>
                        {param.required && <Tag color="red">必需</Tag>}
                        <Text code className="text-xs">{param.type}</Text>
                      </div>
                      <Text className="text-slate-600 text-sm">{param.description}</Text>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* 1.3 使用模式 */}
      <Card title="1.3 🎯 使用模式" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-white border border-slate-200 rounded-lg p-4">
          <div className="space-y-3">
            <div className="p-3 bg-indigo-50 rounded-md border border-indigo-200/50">
              <Text strong className="text-indigo-800 block mb-1 font-medium text-sm">✅ 推荐用法</Text>
              <Text className="text-indigo-700 text-xs leading-relaxed">
                {typeof item.basicInfo?.bestPractices?.[0] === 'string'
                  ? item.basicInfo.bestPractices[0]
                  : item.basicInfo?.bestPractices?.[0]?.practice || '根据API特性动态展示推荐用法'}
              </Text>
            </div>

            <div className="p-3 bg-amber-50 rounded-md border border-amber-200/50">
              <Text strong className="text-amber-800 block mb-1 font-medium text-sm">⚠️ 注意事项</Text>
              <Text className="text-amber-700 text-xs leading-relaxed">
                {item.notes || item.basicInfo?.warnings?.[0] || '根据API特性动态展示注意事项'}
              </Text>
            </div>
          </div>
        </div>
      </Card>

      {/* 1.4 对比分析 */}
      <Card title="1.4 ⚖️ 对比分析" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-white border border-slate-200 rounded-lg p-4">
          {item.basicInfo?.comparisonAnalysis ? (
            <div className="space-y-6">
              {/* 对比分析标题和描述 */}
              <div className="mb-4">
                <Title level={5} className="text-slate-900 mb-2">
                  {item.basicInfo.comparisonAnalysis.title}
                </Title>
                <Text className="text-slate-600">
                  {item.basicInfo.comparisonAnalysis.description}
                </Text>
              </div>

              {/* 对比表格 */}
              <div className="space-y-4">
                {item.basicInfo.comparisonAnalysis.comparisons.map((comparison: any, index: number) => (
                  <div key={index} className="border border-slate-200 rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <div className={"w-3 h-3 rounded-full " + (
                        index === 0 ? 'bg-blue-500' : 
                        index === 1 ? 'bg-green-500' : 
                        index === 2 ? 'bg-purple-500' : 'bg-orange-500'
                      )}></div>
                      <Title level={5} className="text-slate-900 mb-0">
                        {comparison.name}
                      </Title>
                      <Tag color="default">{comparison.complexity}</Tag>
                    </div>
                    
                    <Text className="text-slate-600 text-sm mb-3">
                      {comparison.description}
                    </Text>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* 优势 */}
                      <div className="bg-green-50 p-3 rounded-md border border-green-200/50">
                        <Text strong className="text-green-800 block mb-2">✅ 优势</Text>
                        <div className="space-y-1">
                          {comparison.advantages.map((advantage: string, idx: number) => (
                            <div key={idx} className="flex items-start gap-2">
                              <span className="w-1.5 h-1.5 bg-green-600 rounded-full mt-2 flex-shrink-0"></span>
                              <Text className="text-green-700 text-sm">{advantage}</Text>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* 劣势 */}
                      <div className="bg-red-50 p-3 rounded-md border border-red-200/50">
                        <Text strong className="text-red-800 block mb-2">❌ 劣势</Text>
                        <div className="space-y-1">
                          {comparison.disadvantages.map((disadvantage: string, idx: number) => (
                            <div key={idx} className="flex items-start gap-2">
                              <span className="w-1.5 h-1.5 bg-red-600 rounded-full mt-2 flex-shrink-0"></span>
                              <Text className="text-red-700 text-sm">{disadvantage}</Text>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="mt-3 flex flex-wrap gap-2">
                      <Text strong className="text-slate-700 text-sm">适用场景: </Text>
                      {comparison.useCases.map((useCase: string, idx: number) => (
                        <Tag key={idx} color="blue">{useCase}</Tag>
                      ))}
                    </div>

                    <div className="mt-2">
                      <Text className="text-slate-600 text-sm">
                        <strong>性能表现:</strong> {comparison.performance}
                      </Text>
                    </div>
                  </div>
                ))}
              </div>

              {/* 决策矩阵 */}
              {item.basicInfo.comparisonAnalysis.decisionMatrix && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200/50">
                  <Title level={5} className="text-blue-900 mb-2">
                    📊 选择决策矩阵
                  </Title>
                  <Text className="text-blue-700 text-sm mb-3">
                    {item.basicInfo.comparisonAnalysis.decisionMatrix.description}
                  </Text>
                  <div className="space-y-2">
                    {item.basicInfo.comparisonAnalysis.decisionMatrix.scenarios.map((scenario: any, index: number) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-white rounded-md">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                          <Text strong className="text-slate-900 text-sm">
                            {scenario.scenario} → {scenario.recommended}
                          </Text>
                          <Text className="text-slate-600 text-xs block mt-1">
                            {scenario.reason}
                          </Text>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 总结 */}
              <div className="bg-slate-50 p-4 rounded-md border border-slate-200/50">
                <Text strong className="text-slate-800 block mb-2">📝 总结</Text>
                <Text className="text-slate-700 text-sm leading-relaxed">
                  {item.basicInfo.comparisonAnalysis.summary}
                </Text>
              </div>
            </div>
          ) : (
            <div className="bg-slate-50 p-4 rounded-md border border-slate-200/50">
              <Text className="text-slate-700 text-sm leading-relaxed">
                对比分析内容将根据具体API动态生成，展示与相关API的差异和优势。
              </Text>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

// 业务场景组件 - 展开所有场景
const BusinessScenariosComponent: React.FC<{ item: any, copyToClipboard: (text: string) => void }> = ({ item, copyToClipboard }) => {
  const scenarios = item.businessScenarios || [];

  if (!scenarios || scenarios.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">💼</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无业务场景</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">💼</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">使用示例</Title>
        <span className="text-slate-500 text-sm">实际业务场景与代码示例</span>
      </div>

      {/* 展开所有场景 */}
      {scenarios.map((scenario: any, index: number) => (
        <Card key={index} title={`2.${index + 1} ${scenario.title}`} size="small" style={{ marginBottom: '16px' }}>
          <div className="relative border border-slate-200/50 rounded-2xl bg-white/90 backdrop-blur-sm p-6">
            <div className="space-y-6">
              {/* 场景标题 */}
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-gradient-to-br from-slate-600 to-gray-700 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold">{index + 1}</span>
                </div>
                <div>
                  <Title level={5} className="mb-1 text-slate-900">{scenario.title}</Title>
                  <Text className="text-slate-600">{scenario.description}</Text>
                </div>
              </div>

              {/* 业务价值 */}
              <div className="bg-emerald-50 p-4 rounded-xl border border-emerald-200/50">
                <Text strong className="text-emerald-800 block mb-2">💰 业务价值</Text>
                <Text className="text-emerald-700">{scenario.businessValue}</Text>
              </div>

              {/* 应用场景 */}
              <div className="bg-blue-50 p-4 rounded-xl border border-blue-200/50">
                <Text strong className="text-blue-800 block mb-2">🎯 应用场景</Text>
                <Text className="text-blue-700">{scenario.scenario}</Text>
              </div>

              {/* 代码示例 */}
              <div>
                <Text strong className="text-slate-800 block mb-3">💻 代码实现</Text>
                <CodeHighlight
                  code={scenario.code}
                  language="javascript"
                  title={scenario.title}
                  onCopy={() => copyToClipboard(scenario.code)}
                />
              </div>

              {/* 核心优势 */}
              {scenario.benefits && (
                <div className="bg-purple-50 p-4 rounded-xl border border-purple-200/50">
                  <Text strong className="text-purple-800 block mb-3">✨ 核心优势</Text>
                  <div className="space-y-2">
                    {scenario.benefits.map((benefit: string, idx: number) => (
                      <div key={idx} className="flex items-start gap-2">
                        <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></span>
                        <Text className="text-purple-700">{benefit}</Text>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

// 原理解析组件
const ImplementationComponent: React.FC<{ item: any }> = ({ item }) => {
  const implementation = item.implementation;

  if (!implementation) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">🔧</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无原理解析内容</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">🔧</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">原理解析</Title>
        <span className="text-slate-500 text-sm">底层实现与可视化</span>
      </div>

      {/* 3.1 实现机制 */}
      <Card title="3.1 ⚙️ 实现机制" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-blue-50 p-4 rounded-xl border border-blue-200/50">
          <div className="text-blue-700 whitespace-pre-line">{implementation.mechanism}</div>
        </div>
      </Card>

      {/* 3.2 可视化说明 */}
      <Card title="3.2 📊 可视化说明" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-green-50 p-4 rounded-xl border border-green-200/50">
          {implementation.visualization ? (
            <MermaidChart chart={implementation.visualization} title="可视化图解" />
          ) : (
            <div className="text-green-700 whitespace-pre-line">{implementation.visualization}</div>
          )}
        </div>
      </Card>

      {/* 3.3 通俗解释 */}
      <Card title="3.3 💡 通俗解释" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-amber-50 p-4 rounded-xl border border-amber-200/50">
          <div className="text-amber-700 whitespace-pre-line">{implementation.plainExplanation}</div>
        </div>
      </Card>

      {/* 3.4 设计考虑 */}
      {implementation.designConsiderations && implementation.designConsiderations.length > 0 && (
        <Card title="3.4 🎯 设计考虑" size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-2">
            {implementation.designConsiderations.map((consideration: string, index: number) => (
              <div key={index} className="flex items-start gap-2 p-3 bg-purple-50 rounded-lg">
                <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></span>
                <Text className="text-purple-700">{consideration}</Text>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 3.5 相关概念 */}
      {implementation.relatedConcepts && implementation.relatedConcepts.length > 0 && (
        <Card title="3.5 🔗 相关概念" size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-2">
            {implementation.relatedConcepts.map((concept: string, index: number) => (
              <div key={index} className="flex items-start gap-2 p-3 bg-indigo-50 rounded-lg">
                <span className="w-2 h-2 bg-indigo-500 rounded-full mt-2 flex-shrink-0"></span>
                <Text className="text-indigo-700">{concept}</Text>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

const InterviewQuestionsComponent: React.FC<{ item: any, copyToClipboard: (text: string) => void }> = ({ item, copyToClipboard }) => {
  const questions = item.interviewQuestions || [];

  if (!questions || questions.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">❓</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无面试题</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">❓</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">面试准备</Title>
        <span className="text-slate-500 text-sm">高频面试题集</span>
      </div>

      {/* 展开所有面试题 */}
      {questions.map((question: any, index: number) => (
        <Card key={index} title={`4.${index + 1} ${question.question}`} size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-4">
            {/* 题目信息 */}
            <div className="flex items-center gap-2 mb-4">
              <Tag color={question.difficulty === 'easy' ? 'green' : question.difficulty === 'medium' ? 'orange' : 'red'}>
                {question.difficulty}
              </Tag>
              <Tag color={question.frequency === 'high' ? 'red' : question.frequency === 'medium' ? 'orange' : 'blue'}>
                {question.frequency}频率
              </Tag>
              <Tag color="blue">{question.category}</Tag>
            </div>

            {/* 简要答案 */}
            <div className="bg-blue-50 p-4 rounded-xl border border-blue-200/50">
              <Text strong className="text-blue-800 block mb-2">💡 简要答案</Text>
              <Text className="text-blue-700">{question.answer.brief}</Text>
            </div>

            {/* 详细答案 */}
            <div className="bg-green-50 p-4 rounded-xl border border-green-200/50">
              <Text strong className="text-green-800 block mb-2">📝 详细解答</Text>
              <div className="text-green-700 whitespace-pre-line">{question.answer.detailed}</div>
            </div>

            {/* 代码示例 */}
            {question.answer.code && (
              <div>
                <Text strong className="text-slate-800 block mb-3">💻 代码示例</Text>
                <CodeHighlight
                  code={question.answer.code}
                  language="javascript"
                  title={`面试题${index + 1}代码示例`}
                  onCopy={() => copyToClipboard(question.answer.code)}
                />
              </div>
            )}

            {/* 标签 */}
            {question.tags && question.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 pt-3 border-t border-gray-200">
                {question.tags.map((tag: string, tagIndex: number) => (
                  <Tag key={tagIndex} color="default">{tag}</Tag>
                ))}
              </div>
            )}
          </div>
        </Card>
      ))}
    </div>
  );
};

const CommonQuestionsComponent: React.FC<{ item: any, copyToClipboard: (text: string) => void }> = ({ item, copyToClipboard }) => {
  const questions = item.commonQuestions || [];

  if (!questions || questions.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">🔍</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无常见问题</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">🔍</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">常见问题</Title>
        <span className="text-slate-500 text-sm">FAQ和解决方案</span>
      </div>

      {/* 展开所有常见问题 */}
      {questions.map((question: any, index: number) => (
        <Card key={index} title={`5.${index + 1} ${question.question}`} size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-4">
            {/* 答案 */}
            <div className="bg-green-50 p-4 rounded-xl border border-green-200/50">
              <Text strong className="text-green-800 block mb-2">💡 解答</Text>
              <div className="text-green-700 whitespace-pre-line">{question.answer}</div>
            </div>

            {/* 代码示例 */}
            {question.code && (
              <div>
                <Text strong className="text-slate-800 block mb-3">💻 解决方案代码</Text>
                <CodeHighlight
                  code={question.code}
                  language="javascript"
                  title={`常见问题${index + 1}解决方案`}
                  onCopy={() => copyToClipboard(question.code)}
                />
              </div>
            )}

            {/* 标签 */}
            {question.tags && question.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 pt-3 border-t border-gray-200">
                {question.tags.map((tag: string, tagIndex: number) => (
                  <Tag key={tagIndex} color="default">{tag}</Tag>
                ))}
              </div>
            )}

            {/* 相关问题 */}
            {question.relatedQuestions && question.relatedQuestions.length > 0 && (
              <div className="bg-blue-50 p-3 rounded-md border border-blue-200/50">
                <Text strong className="text-blue-800 block mb-2">🔗 相关问题</Text>
                <div className="text-blue-700">
                  {question.relatedQuestions.map((related: string, relatedIndex: number) => (
                    <div key={relatedIndex}>• {related}</div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Card>
      ))}
    </div>
  );
};

const KnowledgeArchaeologyComponent: React.FC<{ item: any }> = ({ item }) => {
  const archaeology = item.knowledgeArchaeology;

  if (!archaeology) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">📚</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无知识考古内容</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">📚</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">知识考古</Title>
        <span className="text-slate-500 text-sm">历史背景和设计哲学</span>
      </div>

      {/* 6.1 介绍 */}
      <Card title="6.1 📖 介绍" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-blue-50 p-4 rounded-xl border border-blue-200/50">
          <Text className="text-blue-700">{archaeology.introduction}</Text>
        </div>
      </Card>

      {/* 6.2 历史背景 */}
      <Card title="6.2 🏛️ 历史背景" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-amber-50 p-4 rounded-xl border border-amber-200/50">
          <div className="text-amber-700 whitespace-pre-line">{archaeology.background}</div>
        </div>
      </Card>

      {/* 6.3 演进历程 */}
      <Card title="6.3 🔄 演进历程" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-green-50 p-4 rounded-xl border border-green-200/50">
          <div className="text-green-700 whitespace-pre-line">{archaeology.evolution}</div>
        </div>
      </Card>

      {/* 6.4 时间线 */}
      {archaeology.timeline && archaeology.timeline.length > 0 && (
        <Card title="6.4 📅 发展时间线" size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-4">
            {archaeology.timeline.map((event: any, index: number) => (
              <div key={index} className="flex items-start gap-4 p-4 bg-slate-50 rounded-lg">
                <div className={`w-3 h-3 rounded-full mt-2 ${
                  event.significance === 'critical' ? 'bg-red-500' :
                  event.significance === 'high' ? 'bg-orange-500' : 'bg-blue-500'
                }`}></div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Text strong className="text-slate-900">{event.year}</Text>
                    <Tag color={
                      event.significance === 'critical' ? 'red' :
                      event.significance === 'high' ? 'orange' : 'blue'
                    }>
                      {event.significance}
                    </Tag>
                  </div>
                  <Title level={5} className="text-slate-800 mb-1">{event.event}</Title>
                  <Text className="text-slate-600">{event.description}</Text>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 6.5 关键人物 */}
      {archaeology.keyFigures && archaeology.keyFigures.length > 0 && (
        <Card title="6.5 👥 关键人物" size="small" style={{ marginBottom: '16px' }}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {archaeology.keyFigures.map((figure: any, index: number) => (
              <div key={index} className="p-4 bg-purple-50 rounded-lg border border-purple-200/50">
                <div className="flex items-center gap-2 mb-2">
                  <Text strong className="text-purple-900">{figure.name}</Text>
                  <Tag color="purple">{figure.role}</Tag>
                </div>
                <Text className="text-purple-700 text-sm mb-2">{figure.contribution}</Text>
                <div className="flex items-center gap-2">
                  <Text className="text-purple-600 text-xs">期间: {figure.period}</Text>
                  <Tag color={
                    figure.significance === 'critical' ? 'red' :
                    figure.significance === 'high' ? 'orange' : 'blue'
                  }>
                    {figure.significance}
                  </Tag>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 6.6 核心概念 */}
      {archaeology.concepts && archaeology.concepts.length > 0 && (
        <Card title="6.6 💡 核心概念" size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-4">
            {archaeology.concepts.map((concept: any, index: number) => (
              <div key={index} className="p-4 bg-indigo-50 rounded-lg border border-indigo-200/50">
                <Title level={5} className="text-indigo-900 mb-2">{concept.term}</Title>
                <Text className="text-indigo-700 mb-2">{concept.definition}</Text>
                <div className="space-y-1">
                  <Text className="text-indigo-600 text-sm"><strong>应用场景:</strong> {concept.context}</Text>
                  <Text className="text-indigo-600 text-sm"><strong>演进过程:</strong> {concept.evolution}</Text>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 6.7 设计哲学 */}
      <Card title="6.7 🎯 设计哲学" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-slate-50 p-4 rounded-xl border border-slate-200/50">
          <div className="text-slate-700 whitespace-pre-line">{archaeology.designPhilosophy}</div>
        </div>
      </Card>

      {/* 6.8 历史影响 */}
      <Card title="6.8 🌍 历史影响" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-emerald-50 p-4 rounded-xl border border-emerald-200/50">
          <div className="text-emerald-700 whitespace-pre-line">{archaeology.impact}</div>
        </div>
      </Card>

      {/* 6.9 现代意义 */}
      <Card title="6.9 🚀 现代意义" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-cyan-50 p-4 rounded-xl border border-cyan-200/50">
          <div className="text-cyan-700 whitespace-pre-line">{archaeology.modernRelevance}</div>
        </div>
      </Card>
    </div>
  );
};

const PerformanceOptimizationComponent: React.FC<{ item: any }> = ({ item }) => {
  const performance = item.performanceOptimization;

  if (!performance) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">⚡</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无性能优化内容</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">⚡</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">性能优化</Title>
        <span className="text-slate-500 text-sm">性能分析与优化策略</span>
      </div>

      {/* 7.1 最佳实践 */}
      {performance.bestPractices && performance.bestPractices.length > 0 && (
        <Card title="7.1 ✨ 最佳实践" size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-4">
            {performance.bestPractices.map((practice: any, index: number) => (
              <div key={index} className="p-4 bg-cyan-50 rounded-lg border border-cyan-200/50">
                <Title level={5} className="text-cyan-900 mb-2">{practice.practice}</Title>
                <Text className="text-cyan-700 mb-3">{practice.description}</Text>

                {practice.example && (
                  <div>
                    <Text strong className="text-cyan-800 block mb-2">💻 代码示例</Text>
                    <pre style={{
                      background: '#f6f8fa',
                      padding: '12px',
                      borderRadius: '6px',
                      overflow: 'auto',
                      fontSize: '12px',
                      lineHeight: '1.4',
                      border: '1px solid #e1e4e8',
                      margin: 0
                    }}>
                      <code style={{ color: '#24292e' }}>{practice.example}</code>
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 7.2 优化策略 (兼容旧格式) */}
      {performance.optimizationStrategies && performance.optimizationStrategies.length > 0 && (
        <Card title="7.2 🎯 优化策略" size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-4">
            {performance.optimizationStrategies.map((strategy: any, index: number) => (
              <div key={index} className="p-4 bg-blue-50 rounded-lg border border-blue-200/50">
                <Title level={5} className="text-blue-900 mb-2">{strategy.strategy}</Title>
                <Text className="text-blue-700 mb-2">{strategy.description}</Text>
                <div className="space-y-1">
                  <Text className="text-blue-600 text-sm"><strong>实施方法:</strong> {strategy.implementation}</Text>
                  <Text className="text-blue-600 text-sm"><strong>预期效果:</strong> {strategy.impact}</Text>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 7.3 性能基准测试 (兼容旧格式) */}
      {performance.benchmarks && performance.benchmarks.length > 0 && (
        <Card title="7.3 📊 性能基准测试" size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-4">
            {performance.benchmarks.map((benchmark: any, index: number) => (
              <div key={index} className="p-4 bg-green-50 rounded-lg border border-green-200/50">
                <Title level={5} className="text-green-900 mb-2">{benchmark.scenario}</Title>
                <Text className="text-green-700 mb-3">{benchmark.description}</Text>

                <div className="bg-white p-3 rounded-md border border-green-200/50 mb-3">
                  <Text strong className="text-green-800 block mb-2">📈 性能指标对比</Text>
                  <div className="space-y-1">
                    {Object.entries(benchmark.metrics).map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <Text className="text-green-700">{key}:</Text>
                        <Text code className="text-green-600">{value as string}</Text>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-emerald-50 p-3 rounded-md border border-emerald-200/50">
                  <Text strong className="text-emerald-800 block mb-1">💡 结论</Text>
                  <Text className="text-emerald-700">{benchmark.conclusion}</Text>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 7.4 监控工具 (兼容旧格式) */}
      {performance.monitoring && (
        <Card title="7.4 🔍 性能监控" size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-6">
            {/* 监控工具 */}
            {performance.monitoring.tools && performance.monitoring.tools.length > 0 && (
              <div>
                <Title level={5} className="text-slate-900 mb-3">监控工具</Title>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {performance.monitoring.tools.map((tool: any, index: number) => (
                    <div key={index} className="p-4 bg-purple-50 rounded-lg border border-purple-200/50">
                      <Title level={5} className="text-purple-900 mb-2">{tool.name}</Title>
                      <Text className="text-purple-700 mb-2">{tool.description}</Text>
                      <Text className="text-purple-600 text-sm"><strong>使用方法:</strong> {tool.usage}</Text>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 性能指标 */}
            {performance.monitoring.metrics && performance.monitoring.metrics.length > 0 && (
              <div>
                <Title level={5} className="text-slate-900 mb-3">关键指标</Title>
                <div className="space-y-3">
                  {performance.monitoring.metrics.map((metric: any, index: number) => (
                    <div key={index} className="p-3 bg-orange-50 rounded-lg border border-orange-200/50">
                      <div className="flex items-center gap-2 mb-2">
                        <Text strong className="text-orange-900">{metric.metric}</Text>
                        <Tag color="orange">目标: {metric.target}</Tag>
                      </div>
                      <Text className="text-orange-700 mb-1">{metric.description}</Text>
                      <Text className="text-orange-600 text-sm"><strong>测量方法:</strong> {metric.measurement}</Text>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  );
};

const DebuggingTipsComponent: React.FC<{ item: any, copyToClipboard: (text: string) => void }> = ({ item, copyToClipboard }) => {
  const debuggingTips = item.debuggingTips;

  if (!debuggingTips || !debuggingTips.subTabs || debuggingTips.subTabs.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">🐛</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无调试技巧</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">🐛</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">调试技巧</Title>
        <span className="text-slate-500 text-sm">调试方法与工具</span>
      </div>

      {/* 展开所有subTabs */}
      {debuggingTips.subTabs.map((subTab: any, index: number) => (
        <Card key={subTab.key} title={`8.${index + 1} ${subTab.title}`} size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-6">
            {/* 介绍 */}
            <div className="bg-blue-50 p-4 rounded-xl border border-blue-200/50">
              <Text strong className="text-blue-800 block mb-2">📖 介绍</Text>
              <Text className="text-blue-700">{subTab.content.introduction}</Text>
            </div>

            {/* 展开所有sections */}
            {subTab.content.sections.map((section: any, sectionIndex: number) => (
              <div key={sectionIndex} className="space-y-4">
                <div className="bg-slate-50 p-4 rounded-lg">
                  <Title level={5} className="text-slate-900 mb-2">{section.title}</Title>
                  <Text className="text-slate-600">{section.description}</Text>
                </div>

                {/* 展开所有items */}
                {section.items.map((item: any, itemIndex: number) => (
                  <div key={itemIndex} className="bg-white border border-slate-200 rounded-lg p-4">
                    <Title level={5} className="text-slate-900 mb-3">{item.title}</Title>
                    <Text className="text-slate-700 mb-4">{item.description}</Text>

                    {/* 解决方案 */}
                    {item.solution && (
                      <div className="bg-green-50 p-3 rounded-md border border-green-200/50 mb-4">
                        <Text strong className="text-green-800 block mb-1">✅ 解决方案</Text>
                        <Text className="text-green-700">{item.solution}</Text>
                      </div>
                    )}

                    {/* 预防措施 */}
                    {item.prevention && (
                      <div className="bg-amber-50 p-3 rounded-md border border-amber-200/50 mb-4">
                        <Text strong className="text-amber-800 block mb-1">🛡️ 预防措施</Text>
                        <Text className="text-amber-700">{item.prevention}</Text>
                      </div>
                    )}

                    {/* 步骤 */}
                    {item.steps && (
                      <div className="bg-indigo-50 p-3 rounded-md border border-indigo-200/50 mb-4">
                        <Text strong className="text-indigo-800 block mb-2">📋 操作步骤</Text>
                        <ol className="text-indigo-700 space-y-1">
                          {item.steps.map((step: string, stepIndex: number) => (
                            <li key={stepIndex}>{stepIndex + 1}. {step}</li>
                          ))}
                        </ol>
                      </div>
                    )}

                    {/* 技巧 */}
                    {item.tips && (
                      <div className="bg-purple-50 p-3 rounded-md border border-purple-200/50 mb-4">
                        <Text strong className="text-purple-800 block mb-2">💡 实用技巧</Text>
                        <ul className="text-purple-700 space-y-1">
                          {item.tips.map((tip: string, tipIndex: number) => (
                            <li key={tipIndex}>• {tip}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* 代码示例 */}
                    {item.code && (
                      <div>
                        <Text strong className="text-slate-800 block mb-3">💻 代码示例</Text>
                        <CodeHighlight
                          code={item.code}
                          language="javascript"
                          title={item.title}
                          onCopy={() => copyToClipboard(item.code)}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ))}
          </div>
        </Card>
      ))}
    </div>
  );
};

const EssenceInsightsComponent: React.FC<{ item: any }> = ({ item }) => {
  const insights = item.essenceInsights;

  if (!insights) {
    return (
      <div className="flex items-center justify-center h-64 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border border-slate-200/50">
        <div className="text-center">
          <div className="w-16 h-16 bg-slate-200 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">💡</span>
          </div>
          <Text className="text-slate-600 font-medium">暂无本质洞察内容</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-6 h-6 bg-slate-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-xs">💡</span>
        </div>
        <Title level={4} className="!mb-0 text-slate-900 font-medium">本质洞察</Title>
        <span className="text-slate-500 text-sm">深层理解与哲学思考</span>
      </div>

      {/* 9.1 核心问题 */}
      <Card title="9.1 ❓ 核心问题" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-red-50 p-4 rounded-xl border border-red-200/50">
          <div className="text-red-700 whitespace-pre-line">{insights.coreQuestion}</div>
        </div>
      </Card>

      {/* 9.2 设计哲学 */}
      {insights.designPhilosophy && (
        <Card title="9.2 🎯 设计哲学" size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200/50">
              <Text strong className="text-blue-800 block mb-2">🌍 世界观</Text>
              <div className="text-blue-700 whitespace-pre-line">{insights.designPhilosophy.worldview}</div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg border border-green-200/50">
              <Text strong className="text-green-800 block mb-2">🔬 方法论</Text>
              <div className="text-green-700 whitespace-pre-line">{insights.designPhilosophy.methodology}</div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg border border-purple-200/50">
              <Text strong className="text-purple-800 block mb-2">💎 价值观</Text>
              <div className="text-purple-700 whitespace-pre-line">{insights.designPhilosophy.values}</div>
            </div>
          </div>
        </Card>
      )}

      {/* 9.3 隐藏真相 */}
      {insights.hiddenTruths && insights.hiddenTruths.length > 0 && (
        <Card title="9.3 🔍 隐藏真相" size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-3">
            {insights.hiddenTruths.map((truth: string, index: number) => (
              <div key={index} className="p-4 bg-amber-50 rounded-lg border border-amber-200/50">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-xs font-bold">{index + 1}</span>
                  </div>
                  <Text className="text-amber-700">{truth}</Text>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 9.4 通用原理 */}
      {insights.universalPrinciples && insights.universalPrinciples.length > 0 && (
        <Card title="9.4 ⚖️ 通用原理" size="small" style={{ marginBottom: '16px' }}>
          <div className="space-y-4">
            {insights.universalPrinciples.map((principleObj: any, index: number) => (
              <div key={index} className="p-4 bg-indigo-50 rounded-lg border border-indigo-200/50">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-xs font-bold">{index + 1}</span>
                  </div>
                  <div className="flex-1">
                    <Text strong className="text-indigo-800 block mb-2">{principleObj.principle}</Text>
                    <Text className="text-indigo-700 block mb-2">{principleObj.explanation}</Text>
                    <div className="bg-indigo-100 p-3 rounded-md">
                      <Text className="text-indigo-600 text-sm font-medium">应用:</Text>
                      <Text className="text-indigo-600 text-sm block mt-1">{principleObj.application}</Text>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 9.5 哲学意义 */}
      <Card title="9.5 🤔 哲学意义" size="small" style={{ marginBottom: '16px' }}>
        <div className="bg-slate-50 p-4 rounded-xl border border-slate-200/50">
          <div className="text-slate-700 whitespace-pre-line">{insights.philosophicalImplications}</div>
        </div>
      </Card>

      {/* 9.6 未来影响 */}
      <Card title="9.6 🚀 未来影响" size="small" style={{ marginBottom: '16px' }}>
        {insights.futureImplications && typeof insights.futureImplications === 'object' ? (
          <div className="space-y-4">
            {/* 趋势 */}
            {insights.futureImplications.trends && insights.futureImplications.trends.length > 0 && (
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200/50">
                <Text strong className="text-blue-800 block mb-2">📈 发展趋势</Text>
                <div className="space-y-2">
                  {insights.futureImplications.trends.map((trend: string, index: number) => (
                    <div key={index} className="flex items-start gap-2">
                      <span className="text-blue-600 text-sm">•</span>
                      <Text className="text-blue-700 text-sm">{trend}</Text>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 挑战 */}
            {insights.futureImplications.challenges && insights.futureImplications.challenges.length > 0 && (
              <div className="bg-orange-50 p-4 rounded-lg border border-orange-200/50">
                <Text strong className="text-orange-800 block mb-2">⚠️ 面临挑战</Text>
                <div className="space-y-2">
                  {insights.futureImplications.challenges.map((challenge: string, index: number) => (
                    <div key={index} className="flex items-start gap-2">
                      <span className="text-orange-600 text-sm">•</span>
                      <Text className="text-orange-700 text-sm">{challenge}</Text>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 机遇 */}
            {insights.futureImplications.opportunities && insights.futureImplications.opportunities.length > 0 && (
              <div className="bg-green-50 p-4 rounded-lg border border-green-200/50">
                <Text strong className="text-green-800 block mb-2">🚀 发展机遇</Text>
                <div className="space-y-2">
                  {insights.futureImplications.opportunities.map((opportunity: string, index: number) => (
                    <div key={index} className="flex items-start gap-2">
                      <span className="text-green-600 text-sm">•</span>
                      <Text className="text-green-700 text-sm">{opportunity}</Text>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-cyan-50 p-4 rounded-xl border border-cyan-200/50">
            <div className="text-cyan-700 whitespace-pre-line">
              {typeof insights.futureImplications === 'string' ? insights.futureImplications : '未来影响数据格式错误'}
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

const FullTabRenderPage: React.FC = () => {
  const { apiName } = useParams<{ apiName: string }>();
  const [apiData, setApiData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [renderStatus, setRenderStatus] = useState<Record<string, { success: boolean; error?: Error; renderTime?: number }>>({});

  // 复制功能
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // 渲染状态更新函数
  const handleRenderSuccess = (tabName: string) => {
    console.log(`✅ ${tabName} Tab 渲染成功`);
    setRenderStatus(prev => ({
      ...prev,
      [tabName]: { success: true, renderTime: performance.now() }
    }));
  };

  const handleRenderError = (tabName: string, error: Error) => {
    console.error(`❌ ${tabName} Tab 渲染失败:`, error);
    setRenderStatus(prev => ({
      ...prev,
      [tabName]: { success: false, error }
    }));
  };

  useEffect(() => {
    const loadApiData = async () => {
      if (!apiName) {
        setError('API名称未提供');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        console.log(`🔍 开始加载API数据: ${apiName}`);

        // 解析框架前缀
        let framework = 'react'; // 默认框架
        let actualApiName = apiName;

        if (apiName.includes(':')) {
          const [prefix, name] = apiName.split(':');
          framework = prefix.toLowerCase();
          actualApiName = name;
          console.log(`🎯 检测到框架前缀: ${framework}, API名称: ${actualApiName}`);
        }

        // 动态导入API数据
        let apiModule;
        try {
          console.log(`📦 尝试加载: @/data/${framework}/${actualApiName}/index.ts`);
          apiModule = await import(`@/data/${framework}/${actualApiName}/index.ts`);
        } catch (importError) {
          console.warn(`❌ 加载失败，尝试备用路径: @/data/react/${apiName}/index.ts`);
          // 尝试原有路径作为备用
          apiModule = await import(`@/data/react/${apiName}/index.ts`);
        }
        
        const data = apiModule.default;
        
        if (!data) {
          throw new Error(`未找到API数据: ${apiName}`);
        }

        console.log(`✅ API数据加载成功:`, data);
        setApiData(data);
      } catch (err) {
        console.error('加载API数据失败:', err);
        setError(`加载失败: ${err instanceof Error ? err.message : '未知错误'}`);
      } finally {
        setLoading(false);
      }
    };

    loadApiData();
  }, [apiName]);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <Spin size="large" />
        <Text>正在加载 {apiName} 数据...</Text>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="加载错误"
          description={error}
          type="error"
          showIcon
          icon={<ExclamationCircleOutlined />}
        />
      </div>
    );
  }

  if (!apiData) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="数据为空"
          description="未找到API数据"
          type="warning"
          showIcon
        />
      </div>
    );
  }

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.group('🚨 FullTabRenderPage Error Details');
        console.error('API Name:', apiName);
        console.error('API Data:', apiData);
        console.error('Error:', error);
        console.error('Error Info:', errorInfo);
        console.groupEnd();
      }}
    >
      <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
        <div style={{ marginBottom: '24px' }}>
          <Title level={2}>
            <CheckCircleOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
            {apiName} 完整Tab渲染测试
          </Title>
          <Text type="secondary">
            渲染真正的Tab组件内容，就像在抽屉中看到的一样
          </Text>
        </div>

        <Alert
          message={`✅ ${apiName} 开始渲染所有Tab组件`}
          description="直接渲染真正的Tab组件内容，无Tab结构"
          type="success"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        {/* Tab完成状态检测区域 - 供脚本检测用 */}
        <Card
          title="🏗️ Tab完成状态检测"
          size="small"
          style={{ marginBottom: '24px', backgroundColor: '#f8f9fa' }}
        >
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '12px' }}>
            {/* 基本信息状态 */}
            <div className="completion-status-item" data-tab="basic-info">
              <Text strong>1. 基本信息:</Text>
              <Tag 
                color={apiData.basicInfo?.completionStatus === '当前tab内容未完成' ? 'orange' : 'green'}
                className="completion-status-tag"
                data-status={apiData.basicInfo?.completionStatus || '内容已完成'}
              >
                {apiData.basicInfo?.completionStatus || '内容已完成'}
              </Tag>
            </div>

            {/* 业务场景状态 */}
            <div className="completion-status-item" data-tab="business-scenarios">
              <Text strong>2. 业务场景:</Text>
              <Tag 
                color={apiData.businessScenarios?.some((s: any) => s.completionStatus === '当前tab内容未完成') ? 'orange' : 'green'}
                className="completion-status-tag"
                data-status={apiData.businessScenarios?.some((s: any) => s.completionStatus === '当前tab内容未完成') ? '当前tab内容未完成' : '内容已完成'}
              >
                {apiData.businessScenarios?.some((s: any) => s.completionStatus === '当前tab内容未完成') ? '当前tab内容未完成' : '内容已完成'}
              </Tag>
            </div>

            {/* 原理解析状态 */}
            <div className="completion-status-item" data-tab="implementation">
              <Text strong>3. 原理解析:</Text>
              <Tag 
                color={apiData.implementation?.completionStatus === '当前tab内容未完成' ? 'orange' : 'green'}
                className="completion-status-tag"
                data-status={apiData.implementation?.completionStatus || '内容已完成'}
              >
                {apiData.implementation?.completionStatus || '内容已完成'}
              </Tag>
            </div>

            {/* 面试准备状态 */}
            <div className="completion-status-item" data-tab="interview-questions">
              <Text strong>4. 面试准备:</Text>
              <Tag 
                color={apiData.interviewQuestions?.some((q: any) => q.completionStatus === '当前tab内容未完成') ? 'orange' : 'green'}
                className="completion-status-tag"
                data-status={apiData.interviewQuestions?.some((q: any) => q.completionStatus === '当前tab内容未完成') ? '当前tab内容未完成' : '内容已完成'}
              >
                {apiData.interviewQuestions?.some((q: any) => q.completionStatus === '当前tab内容未完成') ? '当前tab内容未完成' : '内容已完成'}
              </Tag>
            </div>

            {/* 常见问题状态 */}
            <div className="completion-status-item" data-tab="common-questions">
              <Text strong>5. 常见问题:</Text>
              <Tag 
                color={apiData.commonQuestions?.some((q: any) => q.completionStatus === '当前tab内容未完成') ? 'orange' : 'green'}
                className="completion-status-tag"
                data-status={apiData.commonQuestions?.some((q: any) => q.completionStatus === '当前tab内容未完成') ? '当前tab内容未完成' : '内容已完成'}
              >
                {apiData.commonQuestions?.some((q: any) => q.completionStatus === '当前tab内容未完成') ? '当前tab内容未完成' : '内容已完成'}
              </Tag>
            </div>

            {/* 知识考古状态 */}
            <div className="completion-status-item" data-tab="knowledge-archaeology">
              <Text strong>6. 知识考古:</Text>
              <Tag 
                color={apiData.knowledgeArchaeology?.completionStatus === '当前tab内容未完成' ? 'orange' : 'green'}
                className="completion-status-tag"
                data-status={apiData.knowledgeArchaeology?.completionStatus || '内容已完成'}
              >
                {apiData.knowledgeArchaeology?.completionStatus || '内容已完成'}
              </Tag>
            </div>

            {/* 性能优化状态 */}
            <div className="completion-status-item" data-tab="performance-optimization">
              <Text strong>7. 性能优化:</Text>
              <Tag 
                color={apiData.performanceOptimization?.completionStatus === '当前tab内容未完成' ? 'orange' : 'green'}
                className="completion-status-tag"
                data-status={apiData.performanceOptimization?.completionStatus || '内容已完成'}
              >
                {apiData.performanceOptimization?.completionStatus || '内容已完成'}
              </Tag>
            </div>

            {/* 调试技巧状态 */}
            <div className="completion-status-item" data-tab="debugging-tips">
              <Text strong>8. 调试技巧:</Text>
              <Tag 
                color={apiData.debuggingTips?.completionStatus === '当前tab内容未完成' ? 'orange' : 'green'}
                className="completion-status-tag"
                data-status={apiData.debuggingTips?.completionStatus || '内容已完成'}
              >
                {apiData.debuggingTips?.completionStatus || '内容已完成'}
              </Tag>
            </div>

            {/* 本质洞察状态 */}
            <div className="completion-status-item" data-tab="essence-insights">
              <Text strong>9. 本质洞察:</Text>
              <Tag 
                color={apiData.essenceInsights?.completionStatus === '当前tab内容未完成' ? 'orange' : 'green'}
                className="completion-status-tag"
                data-status={apiData.essenceInsights?.completionStatus || '内容已完成'}
              >
                {apiData.essenceInsights?.completionStatus || '内容已完成'}
              </Tag>
            </div>
          </div>

          {/* 总体完成状态 - 供脚本全局检测 */}
          <Divider />
          <div className="overall-completion-status" data-overall-status="检测区域">
            <Text strong style={{ marginRight: '12px' }}>总体完成状态:</Text>
            <Tag 
              color={
                [
                  apiData.basicInfo?.completionStatus || '内容已完成',
                  apiData.businessScenarios?.some((s: any) => s.completionStatus === '当前tab内容未完成') ? '当前tab内容未完成' : '内容已完成',
                  apiData.implementation?.completionStatus || '内容已完成',
                  apiData.interviewQuestions?.some((q: any) => q.completionStatus === '当前tab内容未完成') ? '当前tab内容未完成' : '内容已完成',
                  apiData.commonQuestions?.some((q: any) => q.completionStatus === '当前tab内容未完成') ? '当前tab内容未完成' : '内容已完成',
                  apiData.knowledgeArchaeology?.completionStatus || '内容已完成',
                  apiData.performanceOptimization?.completionStatus || '内容已完成',
                  apiData.debuggingTips?.completionStatus || '内容已完成',
                  apiData.essenceInsights?.completionStatus || '内容已完成'
                ].some(status => status === '当前tab内容未完成') ? 'orange' : 'green'
              }
              className="overall-completion-tag"
              style={{ fontSize: '14px', padding: '4px 8px' }}
            >
              {[
                apiData.basicInfo?.completionStatus || '内容已完成',
                apiData.businessScenarios?.some((s: any) => s.completionStatus === '当前tab内容未完成') ? '当前tab内容未完成' : '内容已完成',
                apiData.implementation?.completionStatus || '内容已完成',
                apiData.interviewQuestions?.some((q: any) => q.completionStatus === '当前tab内容未完成') ? '当前tab内容未完成' : '内容已完成',
                apiData.commonQuestions?.some((q: any) => q.completionStatus === '当前tab内容未完成') ? '当前tab内容未完成' : '内容已完成',
                apiData.knowledgeArchaeology?.completionStatus || '内容已完成',
                apiData.performanceOptimization?.completionStatus || '内容已完成',
                apiData.debuggingTips?.completionStatus || '内容已完成',
                apiData.essenceInsights?.completionStatus || '内容已完成'
              ].some(status => status === '当前tab内容未完成') ? '🏗️ 包含未完成内容' : '✅ 全部完成'}
            </Tag>
          </div>
        </Card>

        {/* 渲染状态总结 */}
        {Object.keys(renderStatus).length > 0 && (
          <Card
            title="📊 渲染状态总结"
            size="small"
            style={{ marginBottom: '24px', backgroundColor: '#fafafa' }}
          >
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
              {Object.entries(renderStatus).map(([tabName, status]) => (
                <Tag
                  key={tabName}
                  color={status.success ? 'green' : 'red'}
                  style={{ margin: '2px' }}
                >
                  {status.success ? '✅' : '❌'} {tabName}
                  {status.renderTime && ` (${status.renderTime.toFixed(1)}ms)`}
                </Tag>
              ))}
            </div>
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              成功: {Object.values(renderStatus).filter(s => s.success).length} /
              总计: {Object.keys(renderStatus).length}
            </div>
          </Card>
        )}

        {/* 每个Tab用TabRenderMonitor监控 */}
        <TabRenderMonitor
          tabName="基本信息"
          tabIndex={1}
          apiName={apiName || 'Unknown'}
          onRenderSuccess={handleRenderSuccess}
          onRenderError={handleRenderError}
        >
          <BasicInfoComponent item={apiData} copyToClipboard={copyToClipboard} />
        </TabRenderMonitor>

        <TabRenderMonitor
          tabName="业务场景"
          tabIndex={2}
          apiName={apiName || 'Unknown'}
          onRenderSuccess={handleRenderSuccess}
          onRenderError={handleRenderError}
        >
          <BusinessScenariosComponent item={apiData} copyToClipboard={copyToClipboard} />
        </TabRenderMonitor>

        <TabRenderMonitor
          tabName="原理解析"
          tabIndex={3}
          apiName={apiName || 'Unknown'}
          onRenderSuccess={handleRenderSuccess}
          onRenderError={handleRenderError}
        >
          <ImplementationComponent item={apiData} />
        </TabRenderMonitor>

        <TabRenderMonitor
          tabName="面试准备"
          tabIndex={4}
          apiName={apiName || 'Unknown'}
          onRenderSuccess={handleRenderSuccess}
          onRenderError={handleRenderError}
        >
          <InterviewQuestionsComponent item={apiData} copyToClipboard={copyToClipboard} />
        </TabRenderMonitor>

        <TabRenderMonitor
          tabName="常见问题"
          tabIndex={5}
          apiName={apiName || 'Unknown'}
          onRenderSuccess={handleRenderSuccess}
          onRenderError={handleRenderError}
        >
          <CommonQuestionsComponent item={apiData} copyToClipboard={copyToClipboard} />
        </TabRenderMonitor>

        <TabRenderMonitor
          tabName="知识考古"
          tabIndex={6}
          apiName={apiName || 'Unknown'}
          onRenderSuccess={handleRenderSuccess}
          onRenderError={handleRenderError}
        >
          <KnowledgeArchaeologyComponent item={apiData} />
        </TabRenderMonitor>

        <TabRenderMonitor
          tabName="性能优化"
          tabIndex={7}
          apiName={apiName || 'Unknown'}
          onRenderSuccess={handleRenderSuccess}
          onRenderError={handleRenderError}
        >
          <PerformanceOptimizationComponent item={apiData} />
        </TabRenderMonitor>

        <TabRenderMonitor
          tabName="调试技巧"
          tabIndex={8}
          apiName={apiName || 'Unknown'}
          onRenderSuccess={handleRenderSuccess}
          onRenderError={handleRenderError}
        >
          <DebuggingTipsComponent item={apiData} copyToClipboard={copyToClipboard} />
        </TabRenderMonitor>

        <TabRenderMonitor
          tabName="本质洞察"
          tabIndex={9}
          apiName={apiName || 'Unknown'}
          onRenderSuccess={handleRenderSuccess}
          onRenderError={handleRenderError}
        >
          <EssenceInsightsComponent item={apiData} />
        </TabRenderMonitor>
      </div>
    </ErrorBoundary>
  );
};

export default FullTabRenderPage;
