import React, { useState, useEffect } from 'react';
import { Button, Tooltip } from 'antd';
import { AppstoreOutlined, BarsOutlined } from '@ant-design/icons';
import CheatSheet from '@/components/CheatSheet';
import CheatSheetTreeLayout from '@/components/CheatSheet-TreeLayout';
import ecmaApis from '@/data/ecma';

type LayoutMode = 'card' | 'tree';

const EcmaCheatSheet: React.FC = () => {
  // 从 localStorage 读取用户偏好，默认为卡片布局
  const [layoutMode, setLayoutMode] = useState<LayoutMode>(() => {
    const saved = localStorage.getItem('ecma-layout-mode');
    return (saved as LayoutMode) || 'card';
  });

  // 保存用户偏好到 localStorage
  useEffect(() => {
    localStorage.setItem('ecma-layout-mode', layoutMode);
  }, [layoutMode]);

  const toggleLayout = () => {
    setLayoutMode(prev => prev === 'card' ? 'tree' : 'card');
  };

  return (
    <div className="relative">
      {/* 布局切换按钮 */}
      <div className="fixed top-4 right-4 z-50">
        <Button
          type="primary"
          icon={layoutMode === 'card' ? <BarsOutlined /> : <AppstoreOutlined />}
          onClick={toggleLayout}
          className="shadow-lg"
        >
          {layoutMode === 'card' ? '树状布局' : '卡片布局'}
        </Button>
      </div>

      {/* 根据模式渲染不同的组件 */}
      {layoutMode === 'card' ? (
        <CheatSheet
          title="ECMAScript API 速查手册"
          subtitle="包含现代 JavaScript/ECMAScript 核心特性和语法 - 点击卡片查看详细信息"
          apiData={ecmaApis}
          themeColor="ecma"
        />
      ) : (
        <CheatSheetTreeLayout
          title="ECMAScript API 速查手册"
          subtitle="包含现代 JavaScript/ECMAScript 核心特性和语法 - 使用树状导航浏览"
          apiData={ecmaApis}
          themeColor="ecma"
          selectedCategory="All"
        />
      )}
    </div>
  );
};

export default EcmaCheatSheet;
