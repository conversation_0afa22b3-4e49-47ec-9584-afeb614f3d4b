import React, { useState, useEffect } from 'react';
import { Button } from 'antd';
import { AppstoreOutlined, BarsOutlined } from '@ant-design/icons';
import CheatSheet from '@/components/CheatSheet';
import CheatSheetTreeLayout from '@/components/CheatSheet-TreeLayout';
import { allVueApis } from '@/data/vue';

type LayoutMode = 'card' | 'tree';

const VueCheatSheet: React.FC = () => {
  // 从 localStorage 读取用户偏好，默认为卡片布局
  const [layoutMode, setLayoutMode] = useState<LayoutMode>(() => {
    const saved = localStorage.getItem('vue-layout-mode');
    return (saved as LayoutMode) || 'card';
  });

  // 保存用户偏好到 localStorage
  useEffect(() => {
    localStorage.setItem('vue-layout-mode', layoutMode);
  }, [layoutMode]);

  const toggleLayout = () => {
    setLayoutMode(prev => prev === 'card' ? 'tree' : 'card');
  };

  return (
    <div className="relative">
      {/* 布局切换按钮 */}
      <div className="fixed top-6 right-6 z-50">
        <Button
          type="primary"
          size="large"
          icon={layoutMode === 'card' ? <BarsOutlined /> : <AppstoreOutlined />}
          onClick={toggleLayout}
          className="shadow-lg hover:shadow-xl transition-all duration-200 bg-gradient-to-r from-green-500 to-emerald-500 border-0 hover:from-green-600 hover:to-emerald-600"
          style={{
            borderRadius: '12px',
            fontWeight: 600,
            padding: '8px 16px',
            height: 'auto'
          }}
        >
          {layoutMode === 'card' ? '树状布局' : '卡片布局'}
        </Button>
      </div>

      {/* 根据模式渲染不同的组件 */}
      {layoutMode === 'card' ? (
        <CheatSheet
          title="Vue 3.5 API 速查手册"
          subtitle="包含最新 Vue 3.5 所有核心 API 和知识点 - 点击卡片查看详细信息"
          apiData={allVueApis}
          themeColor="vue"
        />
      ) : (
        <CheatSheetTreeLayout
          title="Vue 3.5 API 速查手册"
          subtitle="包含最新 Vue 3.5 所有核心 API 和知识点 - 使用树状导航浏览"
          apiData={allVueApis}
          themeColor="vue"
          selectedCategory="All"
        />
      )}
    </div>
  );
};

export default VueCheatSheet;
