import React from 'react';
import { Card, Typography } from 'antd';
import MermaidChart from '@/components/MermaidChart';

const { Title, Text } = Typography;

const MermaidTest: React.FC = () => {
  const testChart1 = `graph TD
    A[用户交互场景] --> B[列表渲染优化]
    A --> C[组件缓存]
    A --> D[昂贵计算]

    B --> B1["📋 大型列表<br/>减少列表项重渲染"]
    B --> B2["🔄 动态列表<br/>优化增删改性能"]

    C --> C1["🎯 稳定组件<br/>props不常变化"]
    C --> C2["📦 重型组件<br/>渲染开销较大"]

    D --> D1["⚡ 复杂UI<br/>计算密集型组件"]
    D --> D2["📊 数据可视化<br/>图表和报表组件"]

    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`;

  const testChart2 = `graph TB
    A[React.memo技术架构] --> B[比较层]
    A --> C[缓存层]
    A --> D[渲染层]

    B --> B1["🔍 浅比较机制<br/>Object.is比较"]
    B --> B2["⚙️ 自定义比较<br/>areEqual函数"]

    C --> C1["💾 组件缓存<br/>NamedExoticComponent"]
    C --> C2["🏷️ 标记系统<br/>_status标记"]

    D --> D1["⚡ 跳过渲染<br/>bailout机制"]
    D --> D2["🔄 强制更新<br/>props变化时"]

    style A fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    style B fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    style C fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    style D fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px`;

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>Mermaid图表测试页面</Title>
      <Text type="secondary" style={{ display: 'block', marginBottom: '24px' }}>
        测试MermaidChart组件的渲染和全屏功能
      </Text>

      <Card title="测试图表1：React.memo 核心应用场景" style={{ marginBottom: '24px' }}>
        <Text style={{ display: 'block', marginBottom: '16px' }}>
          这个图表测试了基本的Mermaid渲染功能。点击右上角的全屏按钮测试全屏功能。
        </Text>
        <MermaidChart 
          chart={testChart1} 
          title="React.memo 核心应用场景"
          id="test-chart-1"
        />
      </Card>

      <Card title="测试图表2：React.memo 技术实现架构">
        <Text style={{ display: 'block', marginBottom: '16px' }}>
          这个图表测试了复杂的Mermaid图表渲染。应该支持缩放、拖拽和全屏查看。
        </Text>
        <MermaidChart 
          chart={testChart2} 
          title="React.memo 技术实现架构"
          id="test-chart-2"
        />
      </Card>

      <Card title="测试说明" style={{ marginTop: '24px', backgroundColor: '#f6f8fa' }}>
        <div style={{ color: '#586069' }}>
          <h4>测试项目：</h4>
          <ul>
            <li>✅ 图表是否正常渲染</li>
            <li>✅ 右上角全屏按钮是否显示</li>
            <li>✅ 点击全屏后是否正确显示图表</li>
            <li>✅ 全屏模式下缩放功能（滚轮或工具栏）</li>
            <li>✅ 全屏模式下拖拽功能</li>
            <li>✅ ESC键或X按钮退出全屏</li>
            <li>✅ 重置按钮功能</li>
          </ul>
          
          <h4>常见问题排查：</h4>
          <ul>
            <li>如果图表不显示：检查Mermaid语法是否正确</li>
            <li>如果全屏功能异常：检查浏览器控制台错误</li>
            <li>如果缩放不工作：尝试滚轮操作</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default MermaidTest; 