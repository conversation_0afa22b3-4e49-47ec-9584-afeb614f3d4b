import React from 'react';
import CheatSheet from '@/components/CheatSheet';
import { ApiItem } from '@/types/api';

// 测试数据
const testApiData: ApiItem[] = [
  {
    id: 'array-flat',
    title: 'Array.flat()',
    description: '将嵌套数组扁平化为一维数组',
    syntax: 'arr.flat([depth])',
    example: `const arr = [1, [2, 3], [4, [5, 6]]];
console.log(arr.flat()); // [1, 2, 3, 4, [5, 6]]
console.log(arr.flat(2)); // [1, 2, 3, 4, 5, 6]`,
    version: 'ES2019',
    difficulty: 'medium',
    category: 'Array Methods',
    tags: ['array', 'flatten', 'es2019'],
    basicInfo: {
      definition: 'Array.flat() 方法会按照一个可指定的深度递归遍历数组，并将所有元素与遍历到的子数组中的元素合并为一个新数组返回。',
      introduction: '这是一个非常实用的数组方法，可以处理多层嵌套的数组结构。',
      syntax: 'arr.flat([depth])',
      parameters: [
        {
          name: 'depth',
          type: 'number',
          required: false,
          description: '指定要提取嵌套数组的结构深度，默认值为 1'
        }
      ],
      returnValue: {
        type: 'Array',
        description: '一个包含将数组与子数组中所有元素的新数组'
      },
      keyFeatures: [
        {
          feature: '递归扁平化',
          description: '可以指定深度进行递归扁平化',
          importance: 'high'
        },
        {
          feature: '不修改原数组',
          description: '返回新数组，不会修改原始数组',
          importance: 'critical'
        }
      ],
      quickExample: `const nested = [1, [2, 3], [4, [5, 6]]];
console.log(nested.flat()); // [1, 2, 3, 4, [5, 6]]`,
      commonUseCases: [
        {
          title: '基本扁平化',
          description: '将二维数组转换为一维数组',
          code: `const arr = [[1, 2], [3, 4], [5, 6]];
const flattened = arr.flat();
console.log(flattened); // [1, 2, 3, 4, 5, 6]`,
          difficulty: 'beginner'
        }
      ]
    },
    businessScenarios: [
      {
        title: '数据处理场景',
        description: '在实际开发中处理嵌套数据结构',
        category: '数据处理',
        difficulty: 'intermediate',
        features: ['数据扁平化', '结构简化', '便于遍历'],
        code: `// 处理嵌套的用户数据
const userGroups = [
  [{ id: 1, name: 'Alice' }, { id: 2, name: 'Bob' }],
  [{ id: 3, name: 'Charlie' }],
  [{ id: 4, name: 'David' }, { id: 5, name: 'Eve' }]
];

const allUsers = userGroups.flat();
console.log(allUsers); // 所有用户的扁平数组`,
        explanation: '这种场景在处理分组数据时非常常见',
        bestPractices: [
          {
            practice: '指定合适的深度',
            reason: '避免过度扁平化导致数据结构丢失',
            example: 'arr.flat(2) // 只扁平化两层'
          }
        ]
      }
    ]
  },
  {
    id: 'promises',
    title: 'Promises',
    description: 'JavaScript异步编程的核心概念',
    syntax: 'new Promise((resolve, reject) => { ... })',
    example: `const promise = new Promise((resolve, reject) => {
  setTimeout(() => {
    resolve('Success!');
  }, 1000);
});

promise.then(result => console.log(result));`,
    version: 'ES6',
    difficulty: 'hard',
    category: 'Async Programming',
    tags: ['promise', 'async', 'es6'],
    basicInfo: {
      definition: 'Promise 对象用于表示一个异步操作的最终完成（或失败）及其结果值。',
      introduction: 'Promise 是异步编程的一种解决方案，比传统的解决方案——回调函数和事件——更合理和更强大。',
      syntax: 'new Promise((resolve, reject) => { ... })',
      parameters: [
        {
          name: 'executor',
          type: 'function',
          required: true,
          description: '执行器函数，接收resolve和reject两个参数'
        }
      ],
      returnValue: {
        type: 'Promise',
        description: '返回一个Promise对象'
      },
      keyFeatures: [
        {
          feature: '状态管理',
          description: '有三种状态：pending、fulfilled、rejected',
          importance: 'critical'
        },
        {
          feature: '链式调用',
          description: '支持.then()和.catch()链式调用',
          importance: 'high'
        }
      ]
    },
    businessScenarios: [
      {
        title: 'API请求处理',
        description: '使用Promise处理HTTP请求',
        category: '网络请求',
        difficulty: 'intermediate',
        code: `function fetchUserData(userId) {
  return new Promise((resolve, reject) => {
    fetch(\`/api/users/\${userId}\`)
      .then(response => {
        if (response.ok) {
          return response.json();
        }
        throw new Error('Network response was not ok');
      })
      .then(data => resolve(data))
      .catch(error => reject(error));
  });
}

// 使用
fetchUserData(123)
  .then(user => console.log('User:', user))
  .catch(error => console.error('Error:', error));`
      }
    ]
  }
];

/**
 * 测试CheatSheet组件
 */
const TestRefactoredCheatSheet: React.FC = () => {
  return (
    <div className="test-refactored-cheatsheet">
      <CheatSheet
        title="重构测试"
        subtitle="测试CheatSheet组件功能"
        apiData={testApiData}
        themeColor="ecma"
      />
    </div>
  );
};

export default TestRefactoredCheatSheet;
