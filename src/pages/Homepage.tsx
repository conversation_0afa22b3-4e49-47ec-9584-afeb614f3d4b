import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { motion, useScroll, useTransform, useSpring, Variants } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import Lenis from 'lenis';
import { 
  ArrowRight, 
  Code2, 
  Sparkles, 
  BookOpen, 
  Github,
  ChevronDown,
  Zap,
  Users,
  Target,
  Moon,
  Sun
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Framework {
  id: string;
  name: string;
  status: 'live' | 'coming' | 'planned';
  path: string;
  description: string;
  stats: {
    apis: string;
    examples: string;
  };
}

const Homepage: React.FC = () => {
  const [darkMode, setDarkMode] = useState(false);
  const heroRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll();
  
  // Smooth scroll setup
  useEffect(() => {
    const lenis = new Lenis({
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
    });

    function raf(time: number) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }

    requestAnimationFrame(raf);

    return () => {
      lenis.destroy();
    };
  }, []);

  // Parallax effects
  const heroY = useTransform(scrollYProgress, [0, 1], ['0%', '50%']);
  const heroOpacity = useTransform(scrollYProgress, [0, 0.3], [1, 0]);

  // Toggle dark mode
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    document.documentElement.classList.toggle('dark');
  };

  // Framework data - designed for easy expansion
  const frameworks: Framework[] = [
    {
      id: 'vue',
      name: 'Vue',
      status: 'live',
      path: '/vue',
      description: 'Complete Vue 3.5 guide with Composition API',
      stats: { apis: '50+', examples: '200+' }
    },
    {
      id: 'react',
      name: 'React',
      status: 'live',
      path: '/react',
      description: 'Modern React patterns and best practices',
      stats: { apis: '40+', examples: '150+' }
    },
    {
      id: 'nextjs',
      name: 'Next.js',
      status: 'coming',
      path: '#',
      description: 'Full-stack React framework deep dive',
      stats: { apis: '0', examples: '0' }
    },
    {
      id: 'typescript',
      name: 'TypeScript',
      status: 'planned',
      path: '#',
      description: 'Advanced TypeScript patterns and practices',
      stats: { apis: '0', examples: '0' }
    },
  ];

  // Core metrics
  const metrics = [
    { label: 'Frameworks', value: '2', active: true },
    { label: 'API docs', value: '90+', active: true },
    { label: 'Code examples', value: '350+', active: true },
  ];

  // Animation variants with professional easing
  const fadeInUp: Variants = {
    initial: { opacity: 0, y: 24 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  const staggerContainer: Variants = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  // Component for framework cards
  const FrameworkCard = ({ framework, index }: { framework: Framework; index: number }) => {
    const [ref, inView] = useInView({
      threshold: 0.3,
      triggerOnce: true
    });

    const getStatusColor = (status: string) => {
      switch (status) {
        case 'live': return 'bg-green-500/10 text-green-600 dark:text-green-400';
        case 'coming': return 'bg-blue-500/10 text-blue-600 dark:text-blue-400';
        case 'planned': return 'bg-neutral-500/10 text-neutral-600 dark:text-neutral-400';
        default: return 'bg-neutral-500/10 text-neutral-600';
      }
    };

    const getStatusText = (status: string) => {
      switch (status) {
        case 'live': return 'Available';
        case 'coming': return 'Coming Soon';
        case 'planned': return 'Planned';
        default: return 'Unknown';
      }
    };

    const isClickable = framework.status === 'live';

    const cardContent = (
      <motion.div
        ref={ref}
        initial={{ opacity: 0, y: 40 }}
        animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
        transition={{ duration: 0.6, delay: index * 0.1, ease: "easeOut" }}
        className={`group relative bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-2xl p-8 transition-all duration-300 ${
          isClickable 
            ? 'hover:border-neutral-300 dark:hover:border-neutral-700 hover:-translate-y-1 hover:shadow-lg cursor-pointer' 
            : 'opacity-75'
        }`}
      >
        {/* Status indicator */}
        <div className="flex items-center justify-between mb-6">
          <div className="w-12 h-12 bg-neutral-100 dark:bg-neutral-800 rounded-xl flex items-center justify-center">
            <Code2 className="w-6 h-6 text-neutral-600 dark:text-neutral-400" />
          </div>
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(framework.status)}`}>
            {getStatusText(framework.status)}
          </span>
        </div>

        {/* Content */}
        <h3 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-3">
          {framework.name}
        </h3>
        <p className="text-neutral-600 dark:text-neutral-400 mb-6 leading-relaxed">
          {framework.description}
        </p>

        {/* Stats */}
        {framework.status === 'live' && (
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <div className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                {framework.stats.apis}
              </div>
              <div className="text-xs text-neutral-500">APIs</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                {framework.stats.examples}
              </div>
              <div className="text-xs text-neutral-500">Examples</div>
            </div>
          </div>
        )}

        {/* Action */}
        {isClickable && (
          <div className="flex items-center text-neutral-600 dark:text-neutral-400 group-hover:text-neutral-900 dark:group-hover:text-neutral-100 transition-colors">
            <span className="text-sm font-medium">Explore docs</span>
            <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
          </div>
        )}
      </motion.div>
    );

    return isClickable ? (
      <Link to={framework.path}>{cardContent}</Link>
    ) : (
      cardContent
    );
  };

  return (
    <div className={`min-h-screen bg-white dark:bg-neutral-950 ${darkMode ? 'dark' : ''}`}>
      {/* Navigation */}
      <motion.nav
        className="fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-neutral-950/80 backdrop-blur-xl border-b border-neutral-200/50 dark:border-neutral-800/50"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="max-w-6xl mx-auto px-6 py-4 flex items-center justify-between">
          <motion.div 
            className="flex items-center space-x-3"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="w-8 h-8 bg-neutral-900 dark:bg-neutral-100 rounded-lg flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white dark:text-neutral-900" />
            </div>
            <span className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
              DevDocs
            </span>
          </motion.div>
          
          <div className="flex items-center space-x-6">
            <button
              onClick={toggleDarkMode}
              className="p-2 rounded-lg bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors"
            >
              {darkMode ? 
                <Sun className="w-4 h-4 text-neutral-600 dark:text-neutral-400" /> : 
                <Moon className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
              }
            </button>
            <Button variant="ghost" size="sm" className="hidden md:flex text-neutral-600 dark:text-neutral-400">
              <Github className="w-4 h-4 mr-2" />
              GitHub
            </Button>
          </div>
        </div>
      </motion.nav>

      {/* Hero Section */}
      <motion.section 
        ref={heroRef}
        className="relative pt-32 pb-20 px-6 min-h-screen flex items-center"
        style={{ y: heroY, opacity: heroOpacity }}
      >
        <div className="max-w-6xl mx-auto w-full">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <motion.div
              className="max-w-4xl"
              initial="initial"
              animate="animate"
              variants={staggerContainer}
            >
              <motion.h1
                variants={fadeInUp}
                className="text-5xl md:text-7xl lg:text-8xl font-bold text-neutral-900 dark:text-neutral-100 mb-8 leading-tight tracking-tight"
              >
                Frontend
                <br />
                <span className="text-neutral-500 dark:text-neutral-400">documentation</span>
                <br />
                done right
              </motion.h1>

              <motion.p
                variants={fadeInUp}
                className="text-xl md:text-2xl text-neutral-600 dark:text-neutral-400 mb-12 max-w-2xl leading-relaxed"
              >
                Professional API documentation and code examples for modern frontend frameworks.
              </motion.p>

              <motion.div
                variants={fadeInUp}
                className="flex flex-col sm:flex-row gap-4 mb-16"
              >
                <Link to="/vue">
                  <Button 
                    className="bg-neutral-900 dark:bg-neutral-100 text-white dark:text-neutral-900 hover:bg-neutral-800 dark:hover:bg-neutral-200 px-8 py-4 text-lg font-medium"
                  >
                    Start with Vue
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Button>
                </Link>
                <Link to="/react">
                  <Button 
                    variant="outline"
                    className="border-neutral-300 dark:border-neutral-700 text-neutral-900 dark:text-neutral-100 hover:bg-neutral-50 dark:hover:bg-neutral-900 px-8 py-4 text-lg font-medium"
                  >
                    Explore React
                  </Button>
                </Link>
              </motion.div>

              {/* Metrics */}
              <motion.div
                variants={fadeInUp}
                className="flex items-center space-x-12"
              >
                {metrics.map((metric, index) => (
                  <div key={metric.label} className="text-center">
                    <div className="text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-1">
                      {metric.value}
                    </div>
                    <div className="text-sm text-neutral-500 dark:text-neutral-500">
                      {metric.label}
                    </div>
                  </div>
                ))}
              </motion.div>
            </motion.div>

            {/* Right Visual */}
            <motion.div
              className="relative hidden lg:block"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              {/* Multi-layered Code Windows Stack */}
              <div className="relative w-[600px] h-[500px] ml-16">
                {/* Background Window Layer 3 */}
                <motion.div
                  className="absolute top-8 left-8 w-full h-full bg-gradient-to-br from-neutral-800 to-neutral-900 rounded-3xl shadow-2xl opacity-30 blur-sm"
                  initial={{ scale: 0.8, rotate: -3 }}
                  animate={{ scale: 0.95, rotate: -2 }}
                  transition={{ duration: 1.2, delay: 0.1 }}
                />
                
                {/* Background Window Layer 2 */}
                <motion.div
                  className="absolute top-4 left-4 w-full h-full bg-gradient-to-br from-neutral-850 to-neutral-900 rounded-3xl shadow-2xl opacity-50 blur-[1px]"
                  initial={{ scale: 0.85, rotate: 2 }}
                  animate={{ scale: 0.97, rotate: 1 }}
                  transition={{ duration: 1.1, delay: 0.2 }}
                />

                {/* Main Code Editor - Front Layer */}
                <motion.div
                  className="relative bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 rounded-3xl p-8 shadow-2xl border border-neutral-700/50 backdrop-blur-xl overflow-hidden"
                  initial={{ scale: 0.9, rotateY: -15 }}
                  animate={{ scale: 1, rotateY: 0 }}
                  transition={{ duration: 1, delay: 0.3 }}
                  style={{ 
                    transformStyle: 'preserve-3d',
                    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 80px rgba(59, 130, 246, 0.15)',
                  }}
                >
                  {/* Glassmorphism overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-3xl"></div>
                  
                  {/* Enhanced Window Controls */}
                  <div className="flex items-center justify-between mb-6 relative z-10">
                    <div className="flex items-center space-x-3">
                      <motion.div 
                        className="w-4 h-4 bg-gradient-to-br from-red-400 to-red-600 rounded-full shadow-lg"
                        whileHover={{ scale: 1.2 }}
                      ></motion.div>
                      <motion.div 
                        className="w-4 h-4 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full shadow-lg"
                        whileHover={{ scale: 1.2 }}
                      ></motion.div>
                      <motion.div 
                        className="w-4 h-4 bg-gradient-to-br from-green-400 to-green-600 rounded-full shadow-lg"
                        whileHover={{ scale: 1.2 }}
                      ></motion.div>
                    </div>
                  </div>

                  {/* Enhanced Code Content */}
                  <div className="font-mono text-sm leading-relaxed relative z-10">
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.8, duration: 1 }}
                    >
                      {/* Template Section */}
                      <div className="mb-6">
                        <div className="text-cyan-400">&lt;<span className="text-red-400">template</span>&gt;</div>
                        <div className="ml-4 text-gray-300">
                          &lt;<span className="text-blue-400">div</span> <span className="text-yellow-400">class</span>=<span className="text-green-400">"counter"</span>&gt;
                        </div>
                        <div className="ml-8 text-gray-300">
                          &lt;<span className="text-blue-400">h1</span>&gt;{`{{ count }}`}&lt;/<span className="text-blue-400">h1</span>&gt;
                        </div>
                        <div className="ml-8 text-gray-300">
                          &lt;<span className="text-blue-400">button</span> @<span className="text-yellow-400">click</span>=<span className="text-green-400">"increment"</span>&gt;
                        </div>
                        <div className="ml-12 text-purple-300">+1</div>
                        <div className="ml-8 text-gray-300">&lt;/<span className="text-blue-400">button</span>&gt;</div>
                        <div className="ml-4 text-gray-300">&lt;/<span className="text-blue-400">div</span>&gt;</div>
                        <div className="text-cyan-400">&lt;/<span className="text-red-400">template</span>&gt;</div>
                      </div>

                      {/* Script Section */}
                      <div>
                        <div className="text-cyan-400">&lt;<span className="text-red-400">script</span> <span className="text-yellow-400">setup</span> <span className="text-yellow-400">lang</span>=<span className="text-green-400">"ts"</span>&gt;</div>
                        <div className="mt-2 text-gray-500">// Vue 3 + TypeScript</div>
                        <div className="text-blue-400 mt-1">
                          <span className="text-purple-400">import</span> {`{ ref, computed }`} 
                          <span className="text-purple-400"> from</span> <span className="text-green-400">'vue'</span>
                        </div>
                        
                        <div className="mt-3 text-blue-400">
                          <span className="text-purple-400">const</span> <span className="text-blue-300">count</span> = 
                          <span className="text-yellow-400"> ref</span>&lt;<span className="text-cyan-400">number</span>&gt;(
                          <span className="text-orange-400">0</span>)
                        </div>
                        
                        <div className="mt-2 text-blue-400">
                          <span className="text-purple-400">const</span> <span className="text-blue-300">increment</span> = () =&gt; {`{`}
                        </div>
                        <div className="text-blue-300 ml-4 flex items-center">
                          <span>count.value<span className="text-purple-400">++</span></span>
                          <motion.div
                            className="w-2 h-5 bg-gradient-to-b from-blue-400 to-cyan-400 ml-1 rounded-sm"
                            animate={{ 
                              opacity: [1, 0, 1],
                              scaleY: [1, 0.8, 1]
                            }}
                            transition={{ 
                              duration: 1.2, 
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                          />
                        </div>
                        <div className="text-blue-400">{`}`}</div>
                        <div className="text-cyan-400 mt-2">&lt;/<span className="text-red-400">script</span>&gt;</div>
                      </div>
                    </motion.div>
                  </div>

                  {/* Code execution indicator */}
                  <motion.div
                    className="absolute top-6 right-6 flex items-center space-x-2 bg-green-500/20 px-3 py-1 rounded-full border border-green-500/30"
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 1.5, duration: 0.5 }}
                  >
                    <motion.div
                      className="w-2 h-2 bg-green-400 rounded-full"
                      animate={{ scale: [1, 1.3, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                    <span className="text-green-400 text-xs font-medium">Running</span>
                  </motion.div>

                  {/* Enhanced Sparkle particles */}
                  {[...Array(8)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 bg-blue-400 rounded-full"
                      style={{
                        left: `${15 + i * 12}%`,
                        top: `${25 + (i % 3) * 15}%`,
                      }}
                      animate={{
                        scale: [0, 1, 0],
                        opacity: [0, 1, 0],
                        rotate: [0, 180, 360],
                      }}
                      transition={{
                        duration: 2.5,
                        repeat: Infinity,
                        delay: i * 0.3,
                        ease: "easeInOut"
                      }}
                    />
                  ))}
                </motion.div>

                {/* Enhanced Floating Tags with whole-area micro animations */}
                <motion.div
                  className="absolute -top-8 -left-8 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-2xl text-sm font-semibold shadow-2xl border border-blue-400/30 backdrop-blur-sm"
                  initial={{ scale: 0, rotateX: -45 }}
                  animate={{ 
                    scale: 1, 
                    rotateX: 0,
                    y: [-3, 3, -3],
                    rotateZ: [-1, 1, -1]
                  }}
                  transition={{
                    scale: { delay: 1, duration: 0.6 },
                    rotateX: { delay: 1, duration: 0.6 },
                    y: { duration: 4, repeat: Infinity, ease: "easeInOut" },
                    rotateZ: { duration: 6, repeat: Infinity, ease: "easeInOut" }
                  }}
                  style={{
                    transformStyle: 'preserve-3d',
                    boxShadow: '0 20px 40px rgba(59, 130, 246, 0.4), 0 0 60px rgba(59, 130, 246, 0.2)',
                  }}
                  whileHover={{ 
                    scale: 1.1, 
                    rotateX: -10,
                    boxShadow: '0 25px 50px rgba(59, 130, 246, 0.5), 0 0 80px rgba(59, 130, 246, 0.3)',
                  }}
                >
                  TypeScript
                </motion.div>

                <motion.div
                  className="absolute -bottom-6 -right-6 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-2xl text-sm font-semibold shadow-2xl border border-green-400/30 backdrop-blur-sm"
                  initial={{ scale: 0, rotateX: 45 }}
                  animate={{
                    scale: 1, 
                    rotateX: 0,
                    y: [3, -3, 3],
                    rotateZ: [1, -1, 1]
                  }}
                  transition={{
                    scale: { delay: 1.2, duration: 0.6 },
                    rotateX: { delay: 1.2, duration: 0.6 },
                    y: { duration: 3.5, repeat: Infinity, ease: "easeInOut", delay: 1 },
                    rotateZ: { duration: 5, repeat: Infinity, ease: "easeInOut", delay: 2 }
                  }}
                  style={{
                    transformStyle: 'preserve-3d',
                    boxShadow: '0 20px 40px rgba(34, 197, 94, 0.4), 0 0 60px rgba(34, 197, 94, 0.2)',
                  }}
                  whileHover={{ 
                    scale: 1.1, 
                    rotateX: 10,
                    boxShadow: '0 25px 50px rgba(34, 197, 94, 0.5), 0 0 80px rgba(34, 197, 94, 0.3)',
                  }}
                >
                  Hot Reload
                </motion.div>

                <motion.div
                  className="absolute top-1/2 -right-10 bg-gradient-to-r from-purple-500 to-violet-500 text-white px-4 py-2 rounded-2xl text-sm font-semibold shadow-2xl border border-purple-400/30 backdrop-blur-sm"
                  initial={{ scale: 0, rotateY: 45 }}
                  animate={{
                    scale: 1, 
                    rotateY: 0,
                    x: [-3, 3, -3],
                    rotateZ: [-0.5, 0.5, -0.5]
                  }}
                  transition={{
                    scale: { delay: 1.4, duration: 0.6 },
                    rotateY: { delay: 1.4, duration: 0.6 },
                    x: { duration: 4.5, repeat: Infinity, ease: "easeInOut", delay: 0.5 },
                    rotateZ: { duration: 7, repeat: Infinity, ease: "easeInOut", delay: 1 }
                  }}
                  style={{
                    transformStyle: 'preserve-3d',
                    boxShadow: '0 20px 40px rgba(139, 92, 246, 0.4), 0 0 60px rgba(139, 92, 246, 0.2)',
                  }}
                  whileHover={{ 
                    scale: 1.1, 
                    rotateY: -10,
                    boxShadow: '0 25px 50px rgba(139, 92, 246, 0.5), 0 0 80px rgba(139, 92, 246, 0.3)',
                  }}
                >
                  Reactive
                </motion.div>

                {/* Background ambient light */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-cyan-500/5 rounded-3xl blur-3xl scale-150 -z-10"></div>
                
                {/* Additional glow layers */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-3xl blur-2xl scale-110 -z-10"></div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Scroll indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 8, 0] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
        >
          <ChevronDown className="w-5 h-5 text-neutral-400" />
        </motion.div>
      </motion.section>

      {/* Frameworks Section */}
      <section className="py-20 px-6 bg-neutral-50 dark:bg-neutral-900/50">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
              Choose your framework
            </h2>
            <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto">
              Comprehensive guides for the tools you use every day, with more coming soon.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {frameworks.map((framework, index) => (
              <FrameworkCard 
                key={framework.id} 
                framework={framework} 
                index={index} 
              />
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="grid md:grid-cols-3 gap-8"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="text-center"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              <div className="w-12 h-12 bg-neutral-100 dark:bg-neutral-800 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Zap className="w-6 h-6 text-neutral-600 dark:text-neutral-400" />
              </div>
              <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
                Lightning Fast
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400">
                Optimized search and instant code examples
              </p>
            </motion.div>

            <motion.div
              className="text-center"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              <div className="w-12 h-12 bg-neutral-100 dark:bg-neutral-800 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-neutral-600 dark:text-neutral-400" />
              </div>
              <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
                Developer First
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400">
                Built by developers, for developers
              </p>
            </motion.div>

            <motion.div
              className="text-center"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
              viewport={{ once: true }}
            >
              <div className="w-12 h-12 bg-neutral-100 dark:bg-neutral-800 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Target className="w-6 h-6 text-neutral-600 dark:text-neutral-400" />
              </div>
              <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
                Always Current
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400">
                Updated with the latest framework versions
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-neutral-200 dark:border-neutral-800 py-12 px-6">
        <div className="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center space-x-3 mb-4 md:mb-0">
            <div className="w-6 h-6 bg-neutral-900 dark:bg-neutral-100 rounded-md flex items-center justify-center">
              <Sparkles className="w-4 h-4 text-white dark:text-neutral-900" />
            </div>
            <span className="text-neutral-600 dark:text-neutral-400">
              © 2024 DevDocs. Built for developers.
            </span>
          </div>
          
          <div className="flex items-center space-x-6 text-sm text-neutral-500 dark:text-neutral-500">
            <a href="#" className="hover:text-neutral-900 dark:hover:text-neutral-100 transition-colors">
              About
            </a>
            <a href="#" className="hover:text-neutral-900 dark:hover:text-neutral-100 transition-colors">
              Contact
            </a>
            <a href="#" className="hover:text-neutral-900 dark:hover:text-neutral-100 transition-colors">
              GitHub
            </a>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Homepage; 