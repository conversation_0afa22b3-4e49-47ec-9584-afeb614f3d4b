import React, { useState, useEffect } from 'react';
import { But<PERSON>, Drawer } from 'antd';
import { BugOutlined, AppstoreOutlined, BarsOutlined } from '@ant-design/icons';
import CheatSheet from '@/components/CheatSheet';
import CheatSheetTreeLayout from '@/components/CheatSheet-TreeLayout';
import { TabTester } from '@/components/TabTester';
import { allReactApis } from '@/data/react';

type LayoutMode = 'card' | 'tree';

const ReactCheatSheet: React.FC = () => {
  const [testDrawerOpen, setTestDrawerOpen] = useState(false);

  // 从 localStorage 读取用户偏好，默认为卡片布局
  const [layoutMode, setLayoutMode] = useState<LayoutMode>(() => {
    const saved = localStorage.getItem('react-layout-mode');
    return (saved as LayoutMode) || 'card';
  });

  // 保存用户偏好到 localStorage
  useEffect(() => {
    localStorage.setItem('react-layout-mode', layoutMode);
  }, [layoutMode]);

  const toggleLayout = () => {
    setLayoutMode(prev => prev === 'card' ? 'tree' : 'card');
  };

  return (
    <div className="relative">
      {/* 布局切换按钮 */}
      <div className="fixed top-6 right-6 z-50">
        <Button
          type="primary"
          size="large"
          icon={layoutMode === 'card' ? <BarsOutlined /> : <AppstoreOutlined />}
          onClick={toggleLayout}
          className="shadow-lg hover:shadow-xl transition-all duration-200 bg-gradient-to-r from-blue-500 to-cyan-500 border-0 hover:from-blue-600 hover:to-cyan-600"
          style={{
            borderRadius: '12px',
            fontWeight: 600,
            padding: '8px 16px',
            height: 'auto'
          }}
        >
          {layoutMode === 'card' ? '树状布局' : '卡片布局'}
        </Button>
      </div>

      {/* 根据模式渲染不同的组件 */}
      {layoutMode === 'card' ? (
        <CheatSheet
          title="React API 速查手册"
          subtitle="包含 React Hooks 和核心 API - 点击卡片查看详细信息"
          apiData={allReactApis}
          themeColor="react"
        />
      ) : (
        <CheatSheetTreeLayout
          title="React API 速查手册"
          subtitle="包含 React Hooks 和核心 API - 使用树状导航浏览"
          apiData={allReactApis}
          themeColor="react"
          selectedCategory="All"
        />
      )}

      {/* 测试按钮 - 固定在右下角 */}
      <Button
        type="primary"
        icon={<BugOutlined />}
        onClick={() => setTestDrawerOpen(true)}
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          zIndex: 1000,
          borderRadius: '50%',
          width: '56px',
          height: '56px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
        }}
        title="打开Tab测试工具"
      />

      {/* 测试抽屉 */}
      <Drawer
        title="🧪 Tab渲染测试工具"
        placement="right"
        width={800}
        open={testDrawerOpen}
        onClose={() => setTestDrawerOpen(false)}
        destroyOnClose
      >
        <TabTester />
      </Drawer>
    </div>
  );
};

export default ReactCheatSheet; 