import React, { useState } from 'react';
import { But<PERSON>, Drawer } from 'antd';
import { BugOutlined } from '@ant-design/icons';
import CheatSheet from '@/components/CheatSheet';
import { TabTester } from '@/components/TabTester';
import { allReactApis } from '@/data/react';

const ReactCheatSheet: React.FC = () => {
  const [testDrawerOpen, setTestDrawerOpen] = useState(false);

  return (
    <div className="relative">
      <CheatSheet
        title="React API 速查手册"
        subtitle="包含 React Hooks 和核心 API - 点击卡片查看详细信息"
        apiData={allReactApis}
        themeColor="react"
      />

      {/* 测试按钮 - 固定在右下角 */}
      <Button
        type="primary"
        icon={<BugOutlined />}
        onClick={() => setTestDrawerOpen(true)}
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          zIndex: 1000,
          borderRadius: '50%',
          width: '56px',
          height: '56px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
        }}
        title="打开Tab测试工具"
      />

      {/* 测试抽屉 */}
      <Drawer
        title="🧪 Tab渲染测试工具"
        placement="right"
        width={800}
        open={testDrawerOpen}
        onClose={() => setTestDrawerOpen(false)}
        destroyOnClose
      >
        <TabTester />
      </Drawer>
    </div>
  );
};

export default ReactCheatSheet; 