// 性能监测工具
interface PerformanceMetrics {
  renderTime: number;
  domNodes: number;
  memoryUsage: number;
  interactionTime: number;
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private observer?: PerformanceObserver;

  constructor() {
    this.initPerformanceObserver();
  }

  private initPerformanceObserver() {
    if (typeof PerformanceObserver !== 'undefined') {
      this.observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'measure') {
            console.log(`🚀 Performance: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
          }
        });
      });
      
      this.observer.observe({ entryTypes: ['measure', 'navigation', 'resource'] });
    }
  }

  // 测量组件渲染时间
  measureRender(componentName: string, renderFn: () => void) {
    const startTime = performance.now();
    renderFn();
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    performance.mark(`${componentName}-render-start`);
    performance.mark(`${componentName}-render-end`);
    performance.measure(`${componentName}-render`, `${componentName}-render-start`, `${componentName}-render-end`);
    
    if (duration > 16) { // 超过一帧的时间
      console.warn(`⚠️ Slow render detected: ${componentName} took ${duration.toFixed(2)}ms`);
    }
    
    return duration;
  }

  // 测量交互延迟
  measureInteraction(actionName: string, actionFn: () => void) {
    const startTime = performance.now();
    actionFn();
    
    // 使用 requestAnimationFrame 来测量到下一次绘制的时间
    requestAnimationFrame(() => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      performance.mark(`${actionName}-interaction-start`);
      performance.mark(`${actionName}-interaction-end`);
      performance.measure(`${actionName}-interaction`, `${actionName}-interaction-start`, `${actionName}-interaction-end`);
      
      if (duration > 100) { // 超过100ms被认为是慢交互
        console.warn(`⚠️ Slow interaction detected: ${actionName} took ${duration.toFixed(2)}ms`);
      }
    });
  }

  // 监测内存使用
  checkMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usedMemory = memory.usedJSHeapSize / 1024 / 1024; // MB
      const totalMemory = memory.totalJSHeapSize / 1024 / 1024; // MB
      
      console.log(`💾 Memory Usage: ${usedMemory.toFixed(2)}MB / ${totalMemory.toFixed(2)}MB`);
      
      if (usedMemory > 100) { // 超过100MB警告
        console.warn(`⚠️ High memory usage detected: ${usedMemory.toFixed(2)}MB`);
      }
      
      return usedMemory;
    }
    return 0;
  }

  // 监测DOM节点数量
  checkDOMComplexity(): number {
    const totalNodes = document.querySelectorAll('*').length;
    const visibleNodes = document.querySelectorAll('*:not([style*="display: none"])').length;
    
    console.log(`🌳 DOM Nodes: ${totalNodes} total, ${visibleNodes} visible`);
    
    if (totalNodes > 5000) {
      console.warn(`⚠️ High DOM complexity: ${totalNodes} nodes`);
    }
    
    return totalNodes;
  }

  // 获取性能建议
  getPerformanceAdvice(): string[] {
    const advice: string[] = [];
    const metrics = this.getCurrentMetrics();
    
    if (metrics.domNodes > 3000) {
      advice.push('🔧 考虑使用虚拟滚动来减少DOM节点数量');
    }
    
    if (metrics.memoryUsage > 80) {
      advice.push('💾 内存使用过高，考虑使用React.memo优化组件');
    }
    
    if (metrics.renderTime > 16) {
      advice.push('⚡ 渲染时间过长，考虑代码分割和懒加载');
    }
    
    if (metrics.interactionTime > 100) {
      advice.push('🖱️ 交互响应慢，考虑使用防抖和节流');
    }
    
    return advice;
  }

  private getCurrentMetrics(): PerformanceMetrics {
    return {
      renderTime: 0, // 需要在实际渲染时测量
      domNodes: this.checkDOMComplexity(),
      memoryUsage: this.checkMemoryUsage(),
      interactionTime: 0 // 需要在实际交互时测量
    };
  }

  // 开始性能监控会话
  startSession(sessionName: string) {
    console.log(`🎯 Starting performance session: ${sessionName}`);
    performance.mark(`${sessionName}-session-start`);
  }

  // 结束性能监控会话
  endSession(sessionName: string) {
    performance.mark(`${sessionName}-session-end`);
    performance.measure(`${sessionName}-session`, `${sessionName}-session-start`, `${sessionName}-session-end`);
    
    const measure = performance.getEntriesByName(`${sessionName}-session`)[0];
    console.log(`✅ Session ${sessionName} completed in ${measure.duration.toFixed(2)}ms`);
    
    // 输出性能建议
    const advice = this.getPerformanceAdvice();
    if (advice.length > 0) {
      console.log('💡 Performance Suggestions:');
      advice.forEach(suggestion => console.log(`   ${suggestion}`));
    }
  }

  // 清理资源
  dispose() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// React Hook for performance monitoring
export const usePerformanceMonitor = () => {
  const measureComponentRender = (componentName: string) => {
    return (renderFn: () => void) => {
      return performanceMonitor.measureRender(componentName, renderFn);
    };
  };

  const measureUserInteraction = (actionName: string) => {
    return (actionFn: () => void) => {
      performanceMonitor.measureInteraction(actionName, actionFn);
    };
  };

  return {
    measureComponentRender,
    measureUserInteraction,
    checkMemoryUsage: () => performanceMonitor.checkMemoryUsage(),
    checkDOMComplexity: () => performanceMonitor.checkDOMComplexity(),
    getPerformanceAdvice: () => performanceMonitor.getPerformanceAdvice(),
    startSession: (name: string) => performanceMonitor.startSession(name),
    endSession: (name: string) => performanceMonitor.endSession(name)
  };
};

// 工具函数：检测渲染阻塞
export const detectRenderBlocking = () => {
  let frameCount = 0;
  let lastFrameTime = performance.now();
  
  const checkFrame = () => {
    const currentTime = performance.now();
    const frameDuration = currentTime - lastFrameTime;
    
    if (frameDuration > 16.67) { // 超过60fps的帧时间
      console.warn(`🐌 Dropped frame detected: ${frameDuration.toFixed(2)}ms (should be <16.67ms)`);
    }
    
    frameCount++;
    lastFrameTime = currentTime;
    
    if (frameCount < 300) { // 监控5秒钟（60fps * 5s = 300帧）
      requestAnimationFrame(checkFrame);
    } else {
      console.log('✅ Frame monitoring completed');
    }
  };
  
  console.log('🎬 Starting frame rate monitoring...');
  requestAnimationFrame(checkFrame);
};

// 工具函数：内存泄漏检测
export const detectMemoryLeaks = () => {
  const initialMemory = performanceMonitor.checkMemoryUsage();
  
  // 每10秒检查一次内存使用
  const checkInterval = setInterval(() => {
    const currentMemory = performanceMonitor.checkMemoryUsage();
    const memoryIncrease = currentMemory - initialMemory;
    
    if (memoryIncrease > 50) { // 增长超过50MB
      console.warn(`🚨 Potential memory leak detected: +${memoryIncrease.toFixed(2)}MB`);
      clearInterval(checkInterval);
    }
  }, 10000);
  
  // 60秒后停止监控
  setTimeout(() => {
    clearInterval(checkInterval);
    console.log('✅ Memory leak monitoring completed');
  }, 60000);
}; 