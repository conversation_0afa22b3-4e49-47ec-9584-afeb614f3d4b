/**
 * 🛡️ API数据一致性验证器
 * 防止顶层字段与Tab内容不一致的问题
 */

import { ApiItem, BasicInfo } from '@/types/api';

// 🚨 数据一致性错误类型
export class DataInconsistencyError extends Error {
  constructor(
    public field: string,
    public topLevelValue: any,
    public tabValue: any,
    public apiId: string
  ) {
    super(`数据不一致: ${apiId}.${field}\n顶层值: ${topLevelValue}\nTab值: ${tabValue}`);
    this.name = 'DataInconsistencyError';
  }
}

// 🔍 骨架内容检测器
export const SKELETON_PATTERNS = [
  /\{[A-Z_]+\}/g,           // {API_SYNTAX}
  /\{[A-Z_]+_[A-Z_]+\}/g,   // {API_BRIEF_DESCRIPTION}
  /\{[A-Z_]+_[0-9]+\}/g,    // {API_TAG_1}
];

export function isSkeletonContent(value: string): boolean {
  if (!value || typeof value !== 'string') return false;
  
  return SKELETON_PATTERNS.some(pattern => {
    pattern.lastIndex = 0; // 重置正则表达式状态
    return pattern.test(value);
  });
}

// 🛡️ 数据一致性验证器
export function validateApiDataConsistency(apiData: ApiItem): void {
  const errors: DataInconsistencyError[] = [];
  
  // 1. 检查骨架内容
  checkSkeletonContent(apiData, errors);
  
  // 2. 检查数据一致性
  checkDataConsistency(apiData, errors);
  
  // 3. 抛出错误
  if (errors.length > 0) {
    const errorMessage = errors.map(e => e.message).join('\n\n');
    throw new Error(`❌ 发现 ${errors.length} 个数据一致性问题:\n\n${errorMessage}`);
  }
}

// 🔍 检查骨架内容
function checkSkeletonContent(apiData: ApiItem, errors: DataInconsistencyError[]): void {
  const fieldsToCheck = [
    'description', 'syntax', 'example', 'notes', 'version'
  ] as const;
  
  fieldsToCheck.forEach(field => {
    const value = apiData[field];
    if (value && isSkeletonContent(value)) {
      errors.push(new DataInconsistencyError(
        field,
        value,
        '骨架内容',
        apiData.id
      ));
    }
  });
  
  // 检查tags数组
  if (apiData.tags?.some(tag => isSkeletonContent(tag))) {
    const skeletonTags = apiData.tags.filter(tag => isSkeletonContent(tag));
    errors.push(new DataInconsistencyError(
      'tags',
      skeletonTags.join(', '),
      '骨架内容',
      apiData.id
    ));
  }
}

// 🔍 检查数据一致性
function checkDataConsistency(apiData: ApiItem, errors: DataInconsistencyError[]): void {
  if (!apiData.basicInfo) {
    errors.push(new DataInconsistencyError(
      'basicInfo',
      'undefined',
      '必须存在',
      apiData.id
    ));
    return;
  }
  
  const basicInfo = apiData.basicInfo;
  
  // 检查关键字段一致性
  const consistencyChecks = [
    {
      field: 'description',
      topLevel: apiData.description,
      tabLevel: basicInfo.definition,
      shouldMatch: true
    },
    {
      field: 'syntax', 
      topLevel: apiData.syntax,
      tabLevel: basicInfo.syntax,
      shouldMatch: true
    }
  ];
  
  consistencyChecks.forEach(check => {
    if (check.shouldMatch && check.topLevel !== check.tabLevel) {
      errors.push(new DataInconsistencyError(
        check.field,
        check.topLevel,
        check.tabLevel,
        apiData.id
      ));
    }
  });
}

// 🔧 自动修复器
export function autoFixApiData(apiData: ApiItem): ApiItem {
  if (!apiData.basicInfo) {
    throw new Error(`无法自动修复 ${apiData.id}: basicInfo 不存在`);
  }
  
  const basicInfo = apiData.basicInfo;
  
  return {
    ...apiData,
    // 🔧 自动从basicInfo派生顶层字段
    description: basicInfo.definition,
    syntax: basicInfo.syntax,
    example: basicInfo.commonUseCases?.[0]?.code || apiData.example,
    notes: basicInfo.limitations?.[0] || apiData.notes,
    version: extractVersionFromBasicInfo(basicInfo) || apiData.version,
    tags: extractTagsFromBasicInfo(basicInfo) || apiData.tags,
  };
}

// 🔧 辅助函数：从basicInfo提取版本信息
function extractVersionFromBasicInfo(basicInfo: BasicInfo): string | undefined {
  // 从syntax或其他字段中提取版本信息
  const syntax = basicInfo.syntax;
  if (syntax?.includes('React 18')) return 'React 18.0.0+';
  if (syntax?.includes('React 17')) return 'React 17.0.0+';
  if (syntax?.includes('React 16')) return 'React 16.8.0+';
  return undefined;
}

// 🔧 辅助函数：从basicInfo提取标签
function extractTagsFromBasicInfo(basicInfo: BasicInfo): string[] | undefined {
  const tags: string[] = [];
  
  // 从keyFeatures提取标签
  basicInfo.keyFeatures?.forEach(feature => {
    if (feature.feature.includes('SSR')) tags.push('SSR兼容');
    if (feature.feature.includes('性能')) tags.push('性能优化');
    if (feature.feature.includes('无障碍')) tags.push('无障碍性');
  });
  
  return tags.length > 0 ? tags : undefined;
}

// 🛡️ 创建安全的API数据
export function createSafeApiData(
  baseData: Omit<ApiItem, 'description' | 'syntax' | 'example' | 'notes' | 'version' | 'tags'>,
  basicInfo: BasicInfo
): ApiItem {
  const apiData: ApiItem = {
    ...baseData,
    basicInfo,
    // 自动派生字段
    description: basicInfo.definition,
    syntax: basicInfo.syntax,
    example: basicInfo.commonUseCases?.[0]?.code || '',
    notes: basicInfo.limitations?.[0] || '',
    version: extractVersionFromBasicInfo(basicInfo) || '',
    tags: extractTagsFromBasicInfo(basicInfo) || [],
  };
  
  // 验证一致性
  validateApiDataConsistency(apiData);
  
  return apiData;
}
