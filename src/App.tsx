import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Homepage from "./pages/Homepage";
import Index from "./pages/Index";
import VueCheatSheet from "./pages/VueCheatSheet";
import ReactCheatSheet from "./pages/ReactCheatSheet";
import EcmaCheatSheet from "./pages/EcmaCheatSheet";
import { ApiTester } from "./components/ApiTester";
import { TabTester } from "./components/TabTester";
import { ReactMemoTester } from "./components/ReactMemoTester";
import FullTabRenderPage from "./pages/FullTabRenderPage";
import MermaidTest from './pages/test-api/MermaidTest';
import TestRenderToStaticMarkup from "./debug/test-renderToStaticMarkup";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          {/* 首页 */}
          <Route path="/" element={<Homepage />} />
          
          {/* 各框架模块路由 */}
          <Route path="/vue" element={<VueCheatSheet />} />
          <Route path="/vue/:apiName" element={<VueCheatSheet />} />
          <Route path="/react" element={<ReactCheatSheet />} />
          <Route path="/react/:apiName" element={<ReactCheatSheet />} />
          <Route path="/ecma" element={<EcmaCheatSheet />} />
          <Route path="/ecma/:apiName" element={<EcmaCheatSheet />} />

          {/* 测试工具 */}
          <Route path="/test-api/mermaid" element={<MermaidTest />} />
          <Route path="/test-tab" element={<TabTester />} />
          <Route path="/test-reactmemo" element={<ReactMemoTester />} />
          <Route path="/test-renderToStaticMarkup" element={<TestRenderToStaticMarkup />} />
          <Route path="/full-tab-render/:apiName" element={<FullTabRenderPage />} />

          {/* 其他路由 */}
          <Route path="/index" element={<Index />} />
          
          {/* 捕获所有未匹配的路由 */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
