import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: '箭头函数和普通函数有什么区别？请举例说明。',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础语法',
    answer: {
      brief: '箭头函数具有更简洁的语法、词法this绑定、不能用作构造函数、没有arguments对象等特点',
      detailed: `箭头函数与普通函数的主要区别包括：

**1. 语法差异**
- 箭头函数：更简洁的语法，使用=>符号
- 普通函数：使用function关键字

**2. this绑定**
- 箭头函数：词法绑定，继承外层作用域的this
- 普通函数：动态绑定，取决于调用方式

**3. 构造函数**
- 箭头函数：不能用作构造函数，没有prototype属性
- 普通函数：可以用作构造函数

**4. arguments对象**
- 箭头函数：没有arguments对象，需要使用rest参数
- 普通函数：有arguments对象

**5. 提升行为**
- 箭头函数：遵循变量提升规则
- 普通函数声明：会被提升到作用域顶部`,
      code: `// 1. 语法对比
const arrow = (a, b) => a + b;
function regular(a, b) { return a + b; }

// 2. this绑定对比
const obj = {
  name: 'Test',
  arrowMethod: () => {
    console.log(this.name); // undefined (继承全局this)
  },
  regularMethod: function() {
    console.log(this.name); // 'Test' (指向obj)
  }
};

// 3. 构造函数对比
function RegularConstructor(name) {
  this.name = name;
}
const ArrowConstructor = (name) => {
  this.name = name;
};

new RegularConstructor('Alice'); // ✅ 正常工作
// new ArrowConstructor('Bob'); // ❌ TypeError

// 4. arguments对比
function regularFunc() {
  console.log(arguments[0]); // 可以访问arguments
}

const arrowFunc = (...args) => {
  console.log(args[0]); // 使用rest参数代替arguments
};

// 5. 提升行为对比
console.log(regularFunc()); // ✅ 可以调用（函数提升）

function regularFunc() {
  return 'hoisted';
}

// console.log(arrowFunc()); // ❌ ReferenceError
const arrowFunc = () => 'not hoisted';`
    }
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '请解释箭头函数中this的绑定机制，并说明在什么情况下会出现问题？',
    difficulty: 'medium',
    frequency: 'high',
    category: 'this绑定',
    answer: {
      brief: '箭头函数使用词法this绑定，this值在函数定义时确定，继承自外层作用域，无法通过call、apply、bind改变',
      detailed: `箭头函数的this绑定机制是面试中的重点考察内容：

**词法this绑定原理**
- 箭头函数在创建时就确定了this的值
- this值来自于定义时的外层作用域
- 不受调用方式影响，无法通过call、apply、bind改变

**常见问题场景**

1. **对象方法中的误用**
   - 在对象方法中使用箭头函数，this不指向对象本身

2. **事件处理器中的混乱**
   - DOM事件处理器中，this不指向触发事件的元素

3. **原型方法的错误使用**
   - 在原型方法中使用箭头函数，this不指向实例

**正确使用场景**
- 回调函数中需要保持外层this
- React组件的事件处理器
- 避免在嵌套函数中丢失this`,
      code: `// ❌ 问题1：对象方法中的误用
const user = {
  name: 'Alice',
  greet: () => {
    console.log('Hello, ' + this.name); // this不指向user
  }
};

// ✅ 正确：使用普通函数
const userFixed = {
  name: 'Alice',
  greet: function() {
    console.log('Hello, ' + this.name); // this指向user
  }
};

// ❌ 问题2：事件处理器误用
button.addEventListener('click', () => {
  this.classList.add('clicked'); // this不指向button
});

// ✅ 正确：根据需求选择
button.addEventListener('click', function() {
  this.classList.add('clicked'); // this指向button
});

// ✅ 箭头函数的正确使用场景
class Timer {
  constructor() {
    this.seconds = 0;
  }
  
  start() {
    // 箭头函数保持this指向Timer实例
    setInterval(() => {
      this.seconds++;
      console.log('Timer: ' + this.seconds + 's');
    }, 1000);
  }
}`
    }
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 3,
    question: '在什么情况下应该优先使用箭头函数？在什么情况下应该避免使用？请从性能、可读性、功能性等角度分析。',
    difficulty: 'hard',
    frequency: 'medium',
    category: '最佳实践',
    answer: {
      brief: '箭头函数适用于简短的回调函数、需要保持this的场景和函数式编程，但应避免在对象方法、构造函数、需要动态this的场景中使用',
      detailed: `这是一个综合性的高级问题，需要从多个维度考虑：

**优先使用箭头函数的场景**

1. **功能性角度**
   - 数组方法的回调函数（map、filter、reduce）
   - Promise链和异步操作
   - 需要保持外层this的回调函数
   - 简短的工具函数

2. **性能角度**
   - 避免不必要的函数绑定
   - 减少内存分配（相比bind）
   - 在React中减少不必要的重渲染

3. **可读性角度**
   - 函数式编程风格
   - 简化代码结构
   - 减少认知负担

**应该避免使用的场景**

1. **功能性限制**
   - 对象方法定义
   - 构造函数
   - 需要arguments对象
   - 原型方法

2. **性能考虑**
   - 在循环中重复创建
   - 大型复杂函数
   - 需要函数名称的调试场景

3. **可读性问题**
   - 复杂的业务逻辑
   - 需要明确this指向的场景

**权衡决策原则**
- 简洁性 vs 明确性
- 性能 vs 可读性
- 团队约定 vs 个人偏好`,
      code: `// ✅ 优先使用箭头函数的场景

// 1. 数组方法回调
const users = [
  { id: 1, name: 'Alice', age: 25 },
  { id: 2, name: 'Bob', age: 30 }
];

const adults = users
  .filter(user => user.age >= 18)
  .map(user => ({ ...user, displayName: user.name.toUpperCase() }));

// 2. Promise链
fetch('/api/users')
  .then(response => response.json())
  .then(data => data.map(user => user.name))
  .catch(error => console.error(error));

// 3. 简短工具函数
const add = (a, b) => a + b;
const isEven = n => n % 2 === 0;

// ❌ 应该避免使用箭头函数的场景

// 1. 对象方法
const calculator = {
  value: 0,
  // ❌ 错误：this不指向calculator
  add: (num) => {
    this.value += num;
  },
  // ✅ 正确：使用普通函数
  addCorrect: function(num) {
    this.value += num;
    return this;
  }
};

// 2. 构造函数
// ❌ 错误：不能用作构造函数
const Person = (name) => {
  this.name = name;
};

// ✅ 正确：使用函数声明
function PersonCorrect(name) {
  this.name = name;
}`
    }
  }
];

export default interviewQuestions; 