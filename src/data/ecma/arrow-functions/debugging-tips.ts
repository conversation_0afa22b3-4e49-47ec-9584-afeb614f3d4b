import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: '箭头函数虽然简洁，但也带来了一些特有的问题。掌握这些常见问题的识别和解决方法，能够大大提高开发效率。',
        sections: [
          {
            title: 'this绑定相关问题',
            description: '箭头函数的this绑定是词法的，这是最常见的困惑来源',
            items: [
              {
                title: '对象方法中的this指向错误',
                description: '在对象方法中使用箭头函数，this不会指向对象本身',
                solution: '使用传统函数语法定义对象方法，或者在需要的地方使用bind',
                prevention: '理解箭头函数的词法this特性，在需要动态this的场景避免使用箭头函数',
                code: `// ❌ 问题代码
const obj = {
  name: 'MyObject',
  greet: () => {
    console.log(\`Hello from \${this.name}\`); // this不指向obj
  }
};

// ✅ 解决方案1: 使用传统函数
const obj = {
  name: 'MyObject',
  greet: function() {
    console.log(\`Hello from \${this.name}\`); // this正确指向obj
  }
};

// ✅ 解决方案2: 在嵌套函数中使用箭头函数
const obj = {
  name: 'MyObject',
  greet: function() {
    const sayHello = () => {
      console.log(\`Hello from \${this.name}\`); // this继承自greet方法
    };
    sayHello();
  }
};`
              },
              {
                title: '事件处理器中this丢失',
                description: '在DOM事件处理器中，箭头函数的this不会指向触发事件的元素',
                solution: '根据需求选择使用传统函数或箭头函数',
                prevention: '明确是否需要this指向事件元素，选择合适的函数类型',
                code: `// 场景1: 需要this指向元素
button.addEventListener('click', function() {
  this.classList.add('clicked'); // this指向button元素
});

// 场景2: 需要this指向外层作用域
class MyComponent {
  handleClick = (event) => {
    this.setState({ clicked: true }); // this指向组件实例
    console.log('Button clicked:', event.target);
  };
  
  render() {
    return <button onClick={this.handleClick}>Click me</button>;
  }
}`
              }
            ]
          },
          {
            title: '语法和使用错误',
            description: '箭头函数的语法虽然简洁，但也容易出现一些特定的语法错误',
            items: [
              {
                title: '返回对象字面量时的语法错误',
                description: '直接返回对象字面量时忘记使用括号包裹',
                solution: '使用括号包裹对象字面量，或者使用明确的return语句',
                prevention: '记住对象字面量返回的语法规则',
                code: `// ❌ 错误：被解析为函数体
const getUser = id => { name: 'User', id: id };

// ✅ 正确方式1：使用括号
const getUser = id => ({ name: 'User', id: id });

// ✅ 正确方式2：明确return
const getUser = id => {
  return { name: 'User', id: id };
};`
              },
              {
                title: '误用作构造函数',
                description: '尝试使用new操作符调用箭头函数',
                solution: '使用传统函数或class语法创建构造函数',
                prevention: '记住箭头函数不能用作构造函数',
                code: `// ❌ 错误：箭头函数不能用作构造函数
const Person = (name) => {
  this.name = name;
};
// new Person('Alice'); // TypeError: Person is not a constructor

// ✅ 正确方式1：传统函数
function Person(name) {
  this.name = name;
}

// ✅ 正确方式2：class语法
class Person {
  constructor(name) {
    this.name = name;
  }
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '现代开发工具为调试箭头函数提供了强大的支持，掌握这些工具能够快速定位和解决问题。',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '利用浏览器提供的调试功能有效调试箭头函数',
            items: [
              {
                title: '断点调试箭头函数',
                description: '在浏览器中为箭头函数设置断点并观察变量',
                solution: '使用Sources面板设置断点，观察作用域链和this值',
                prevention: '定期使用断点调试验证代码逻辑',
                code: `// 在浏览器中调试这段代码
const numbers = [1, 2, 3, 4, 5];

const processNumbers = () => {
  debugger; // 设置断点
  return numbers
    .filter(n => {
      debugger; // 在这里观察n的值
      return n % 2 === 0;
    })
    .map(n => {
      debugger; // 在这里观察n的值和this
      return n * 2;
    });
};

console.log(processNumbers());`
              },
              {
                title: '使用Console进行快速测试',
                description: '在控制台中快速测试箭头函数的行为',
                solution: '使用console.log、console.table等方法观察数据流',
                prevention: '在开发过程中及时使用console验证预期',
                code: `// 使用console进行调试
const users = [
  { id: 1, name: 'Alice', active: true },
  { id: 2, name: 'Bob', active: false },
  { id: 3, name: 'Carol', active: true }
];

// 调试数据处理流程
const activeUsers = users
  .filter(user => {
    console.log('Filtering user:', user);
    return user.active;
  })
  .map(user => {
    console.log('Mapping user:', user);
    return { ...user, displayName: \`👤 \${user.name}\` };
  });

console.table(activeUsers);`
              }
            ]
          },
          {
            title: 'IDE和编辑器支持',
            description: '现代编辑器提供了丰富的箭头函数开发支持',
            items: [
              {
                title: 'TypeScript类型检查',
                description: '使用TypeScript获得更好的类型安全和IDE支持',
                solution: '配置TypeScript，利用类型推断和检查功能',
                prevention: '在项目中启用TypeScript strict模式',
                code: `// TypeScript中的箭头函数类型
type UserProcessor = (user: User) => ProcessedUser;

interface User {
  id: number;
  name: string;
  email: string;
}

interface ProcessedUser {
  id: number;
  displayName: string;
  avatar: string;
}

// 类型安全的箭头函数
const processUser: UserProcessor = (user) => ({
  id: user.id,
  displayName: user.name.toUpperCase(),
  avatar: \`https://avatar.com/\${user.id}\`
});

// IDE会提供完整的类型提示和错误检查`
              },
              {
                title: 'ESLint规则配置',
                description: '使用ESLint规则避免常见的箭头函数问题',
                solution: '配置合适的ESLint规则，自动检测潜在问题',
                prevention: '在项目中集成ESLint，使用推荐的规则集',
                code: `// .eslintrc.js 配置示例
module.exports = {
  extends: ['eslint:recommended'],
  rules: {
    // 要求箭头函数的参数使用圆括号
    'arrow-parens': ['error', 'always'],
    
    // 要求箭头函数体使用大括号
    'arrow-body-style': ['error', 'as-needed'],
    
    // 禁止在可能与比较操作符相混淆的地方使用箭头函数
    'no-confusing-arrow': 'error',
    
    // 强制在箭头函数前后使用一致的空格
    'arrow-spacing': 'error',
    
    // 避免箭头函数与比较运算符混淆
    'no-mixed-operators': 'error'
  }
};`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;