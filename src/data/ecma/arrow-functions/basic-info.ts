import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `箭头函数是ES6引入的一种更简洁的函数定义语法，使用"=>"符号定义函数。它不仅提供了更简洁的语法，还具有词法this绑定、没有arguments对象、不能用作构造函数等重要特性。箭头函数的this值在定义时就确定了，继承自外层作用域，不会因调用方式而改变，这使得它特别适合用作回调函数、事件处理器和函数式编程场景。`,
  
  introduction: `箭头函数是ES6(ES2015)引入的函数表达式，主要用于简化函数语法、解决this绑定问题和提供更简洁的回调函数写法。它采用Lambda表达式的设计模式，提供了更直观的函数式编程体验。`,

  syntax: `
// 🆕 ECMAScript规范定位信息
/**
 * 规范定义位置：
 * - ECMAScript 2015 (ES6) 规范：14.2 Arrow Function Definitions
 * - 语法产生式：ArrowFunction : ArrowParameters => ConciseBody
 * - 语义规范：14.2.16 Runtime Semantics: Evaluation
 * - MDN文档：https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Functions/Arrow_functions
 */

// === 基础语法形式 ===

// 1. 标准形式：多参数 + 块语句
const functionName = (param1, param2, param3) => {
  // 函数体
  const result = param1 + param2 + param3;
  return result;
};

// 2. 简化形式：单参数（可省略括号）
const singleParam = param => param * 2;
const singleParamWithParens = (param) => param * 2; // 推荐保持括号

// 3. 表达式形式：隐式返回
const add = (a, b) => a + b;
const multiply = (x, y) => x * y;

// 4. 无参数形式
const getCurrentTime = () => new Date();
const getRandomNumber = () => Math.random();

// 5. 返回对象字面量（必须用括号包裹）
const createUser = (name, age) => ({ name, age, id: Date.now() });
const getCoordinates = () => ({ x: 10, y: 20 });

// === 🆕 高级语法模式 ===

// 6. 解构参数
const processUser = ({ name, age, email }) => {
  return 'User: ' + name + ', Age: ' + age + ', Email: ' + email;
};

// 7. 剩余参数
const sum = (...numbers) => numbers.reduce((acc, num) => acc + num, 0);
const combineStrings = (separator, ...strings) => strings.join(separator);

// 8. 默认参数
const greet = (name = 'World', greeting = 'Hello') => greeting + ', ' + name + '!';
const calculateArea = (width = 1, height = 1) => width * height;

// 9. 异步箭头函数
const fetchData = async (url) => {
  const response = await fetch(url);
  return await response.json();
};

// 10. 高阶函数返回箭头函数
const createMultiplier = (factor) => (number) => number * factor;
const createValidator = (rule) => (value) => rule.test(value);

// === 🆕 类型推导和约束（TypeScript环境） ===

// 11. 泛型箭头函数
const identity = <T>(value: T): T => value;
const mapArray = <T, U>(array: T[], mapper: (item: T) => U): U[] => array.map(mapper);

// 12. 联合类型处理
const processValue = (value: string | number): string => {
  return typeof value === 'string' ? value.toUpperCase() : value.toString();
};

// 13. 可选参数和联合类型
const formatName = (first: string, last?: string): string => {
  return last ? first + ' ' + last : first;
};

// === 🆕 边界情况和特殊用法 ===

// 14. 立即执行箭头函数（IIFE）
const result = ((x, y) => x + y)(5, 3); // 8

// 15. 条件表达式中的箭头函数
const operation = isAdd ? (a, b) => a + b : (a, b) => a - b;

// 16. 数组方法链式调用
const processNumbers = (numbers) => numbers
  .filter(n => n > 0)
  .map(n => n * 2)
  .reduce((sum, n) => sum + n, 0);

// 17. 对象方法中的箭头函数（注意this绑定）
const calculator = {
  value: 0,
  // ❌ 错误：箭头函数不绑定this
  wrongAdd: (num) => {
    this.value += num; // this不指向calculator
  },
  // ✅ 正确：普通函数绑定this
  correctAdd: function(num) {
    this.value += num;
    // 内部使用箭头函数保持this
    const logResult = () => {
      console.log('Current value:', this.value); // this指向calculator
    };
    logResult();
  }
};

// === 🆕 性能和内存考虑 ===

// 18. 避免在循环中重复创建箭头函数
// ❌ 性能问题：每次循环都创建新函数
items.forEach((item, index) => {
  const processor = (data) => data.transform(); // 重复创建
  processor(item);
});

// ✅ 性能优化：函数定义提取到外部
const processor = (data) => data.transform();
items.forEach((item, index) => {
  processor(item);
});`,

  quickExample: `// 基本用法 - 简化函数表达式
const add = (a, b) => a + b;
const greet = name => 'Hello, ' + name + '!';
const square = x => x * x;

console.log(add(5, 3)); // 8
console.log(greet("开发者")); // "Hello, 开发者!"
console.log(square(4)); // 16

// 数组方法中的应用
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
const evens = numbers.filter(n => n % 2 === 0);
const sum = numbers.reduce((acc, n) => acc + n, 0);

console.log('原数组:', numbers); // [1, 2, 3, 4, 5]
console.log('翻倍后:', doubled); // [2, 4, 6, 8, 10]
console.log('偶数:', evens); // [2, 4]
console.log('总和:', sum); // 15

// this绑定示例
const obj = {
  name: 'Object',
  regularFunction: function() {
    console.log('Regular function this:', this.name); // 'Object'

    const arrowFunction = () => {
      console.log('Arrow function this:', this.name); // 'Object' (继承外层this)
    };
    arrowFunction();
  }
};

obj.regularFunction();

// 返回对象字面量
const createUser = (name, age) => ({ name, age, id: Date.now() });
console.log(createUser('Alice', 25)); // { name: 'Alice', age: 25, id: 1640995200000 }

// 异步箭头函数
const fetchData = async (url) => {
  try {
    const response = await fetch(url);
    return await response.json();
  } catch (error) {
    console.error('Fetch error:', error);
    return null;
  }
};`,

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "箭头函数在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用箭头函数",
      diagram: `graph LR
        A[箭头函数核心场景] --> B[数组方法回调]
        A --> C[事件处理函数]
        A --> D[函数式编程]
        A --> E[异步编程]

        B --> B1["📊 map数据转换<br/>users.map(u => u.name)"]
        B --> B2["🔍 filter数据过滤<br/>items.filter(i => i.active)"]
        B --> B3["📈 reduce数据聚合<br/>nums.reduce((a,b) => a+b)"]

        C --> C1["👆 点击事件处理<br/>onClick={() => handle()}"]
        C --> C2["📝 表单变化监听<br/>onChange={e => setValue(e.target.value)}"]
        C --> C3["⏰ 定时器回调<br/>setTimeout(() => action(), 1000)"]

        D --> D1["🔧 高阶函数<br/>const add = x => y => x + y"]
        D --> D2["🔗 函数组合<br/>compose(f, g, h)"]
        D --> D3["🎯 柯里化应用<br/>curry(fn)(a)(b)(c)"]

        E --> E1["🌐 Promise链<br/>.then(data => process(data))"]
        E --> E2["🔄 async/await<br/>const result = await fetch()"]
        E --> E3["📡 API调用<br/>api.get().then(res => res.data)"]

        style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
        style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
        style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
        style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px
        style E fill:#fce4ec,stroke:#c2185b,stroke-width:2px`
    },
    {
      title: "this绑定机制对比",
      description: "箭头函数与普通函数在this绑定方面的核心差异，这是理解箭头函数的关键",
      diagram: `graph TB
        A[函数调用] --> B{函数类型}

        B -->|普通函数| C[动态this绑定]
        B -->|箭头函数| D[词法this绑定]

        C --> C1[调用时确定this]
        C --> C2[可通过call/apply/bind改变]
        C --> C3[严格模式下可能为undefined]

        D --> D1[定义时确定this]
        D --> D2[继承外层作用域this]
        D --> D3[无法改变this指向]

        C1 --> E1["🎯 obj.method() → this = obj"]
        C2 --> E2["🔧 fn.call(obj) → this = obj"]
        C3 --> E3["⚠️ fn() → this = undefined"]

        D1 --> F1["📍 定义时this = window/global"]
        D2 --> F2["🔗 class method内 → this = instance"]
        D3 --> F3["🚫 call/apply/bind无效"]

        style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
        style B fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
        style C fill:#ffebee,stroke:#d32f2f,stroke-width:2px
        style D fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px`
    },
    {
      title: "性能和使用权衡",
      description: "箭头函数在不同场景下的性能表现和使用建议，帮助做出正确的技术选择",
      diagram: `graph TD
        A[箭头函数使用决策] --> B{场景分析}

        B --> C[✅ 推荐使用]
        B --> D[❌ 避免使用]
        B --> E[⚖️ 权衡考虑]

        C --> C1["🔄 数组方法回调<br/>性能好，代码简洁"]
        C --> C2["🎯 事件处理器<br/>避免this绑定问题"]
        C --> C3["🔗 Promise链<br/>提高可读性"]
        C --> C4["🧮 简单计算函数<br/>减少代码量"]

        D --> D1["🏗️ 对象方法定义<br/>this指向错误"]
        D --> D2["🔨 构造函数<br/>没有prototype"]
        D --> D3["🎭 需要arguments对象<br/>箭头函数没有"]
        D --> D4["🔄 频繁调用的热点<br/>可能性能略差"]

        E --> E1["📦 内存使用<br/>vs 函数声明"]
        E --> E2["🚀 执行速度<br/>vs 普通函数"]
        E --> E3["📖 代码可读性<br/>vs 函数表达式"]
        E --> E4["🛠️ 调试体验<br/>vs 具名函数"]

        style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
        style C fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
        style D fill:#ffebee,stroke:#d32f2f,stroke-width:2px
        style E fill:#fff3e0,stroke:#ef6c00,stroke-width:2px`
    }
  ],
  
  parameters: [
    {
      name: "parameters",
      type: "any[]",
      required: false,
      description: "函数参数列表，支持0个或多个参数，可以使用解构、默认值、剩余参数等高级特性",
      details: "(a, b) 或 name 或 () 或 ({x, y}) 或 (...args)",
      // 🆕 设计原理
      designRationale: "箭头函数的参数设计遵循ECMAScript函数参数的通用规范，但在语法上更加简洁。单参数时可省略括号是为了减少视觉噪音，提高函数式编程的可读性。这种设计平衡了简洁性和一致性。",
      // 🆕 性能影响
      performanceImpact: "参数数量和类型对性能影响：1) 参数越多，函数调用开销越大；2) 解构参数会增加额外的解构开销；3) 默认参数需要运行时计算；4) 剩余参数会创建新数组，影响内存使用。",
      // 🆕 最佳实践
      bestPractices: [
        "单参数时建议保留括号以保持一致性：(param) => result",
        "使用解构参数提高可读性：({name, age}) => processUser(name, age)",
        "合理使用默认参数避免undefined检查：(value = 0) => value * 2",
        "剩余参数用于处理不定数量参数：(...args) => args.reduce(sum)",
        "避免过多参数，超过3个考虑使用对象参数"
      ]
    },
    {
      name: "expression",
      type: "any",
      required: true,
      description: "函数体，可以是单个表达式（隐式返回）或语句块（需要显式return）",
      details: "a + b（隐式返回）或 { const result = a + b; return result; }（显式返回）",
      // 🆕 设计原理
      designRationale: "箭头函数支持两种函数体形式：表达式体（ConciseBody）和块语句体（BlockStatement）。表达式体的隐式返回特性是函数式编程的核心特征，减少了return关键字的使用，让代码更加简洁。这种设计借鉴了Lambda演算的数学基础。",
      // 🆕 性能影响
      performanceImpact: "表达式体vs块语句体的性能差异：1) 表达式体略快，因为省略了return语句的解析；2) 块语句体提供更多控制，但增加了语法开销；3) 复杂表达式可能影响可读性和调试；4) V8引擎对两种形式都有优化。",
      // 🆕 最佳实践
      bestPractices: [
        "简单计算使用表达式体：(x, y) => x + y",
        "复杂逻辑使用块语句体：(data) => { /* 复杂处理 */ return result; }",
        "返回对象字面量时使用括号：() => ({ key: value })",
        "避免在表达式体中使用复杂的三元运算符",
        "块语句体中确保所有路径都有return语句"
      ]
    }
  ],
  
  returnValue: {
    type: "any",
    description: "表达式的结果或显式return的值，如果没有return则返回undefined"
  },

  coreFeatures: [
    {
      feature: "简洁语法",
      description: "使用=>符号定义函数，语法更简洁",
      importance: "high" as const,
      details: "减少代码量，提高可读性"
    },
    {
      feature: "词法this绑定",
      description: "this值继承自外层作用域",
      importance: "critical" as const,
      details: "解决传统函数this指向问题"
    },
    {
      feature: "隐式返回",
      description: "单表达式可省略return关键字",
      importance: "medium" as const,
      details: "进一步简化函数定义"
    },
    {
      feature: "不能用作构造函数",
      description: "没有prototype属性，不能使用new",
      importance: "high" as const,
      details: "明确函数用途，避免误用"
    }
  ],
  
  keyFeatures: [
    {
      feature: "简洁的语法",
      description: "相比传统函数表达式，箭头函数语法更简洁明了",
      importance: "high" as const,
      details: "减少代码量，提高可读性，特别适合简单的回调函数"
    },
    {
      feature: "词法this绑定",
      description: "箭头函数不绑定自己的this，而是继承外层作用域的this",
      importance: "critical" as const,
      details: "解决传统函数this指向混乱的问题，无需bind或that=this"
    },
    {
      feature: "隐式返回",
      description: "单表达式箭头函数可以省略return关键字",
      importance: "medium" as const,
      details: "进一步简化代码，函数式编程更优雅"
    },
    {
      feature: "不能用作构造函数",
      description: "箭头函数没有prototype属性，不能使用new调用",
      importance: "high" as const,
      details: "避免误用，明确函数用途，提高代码安全性"
    }
  ],
  
  limitations: [
    "不绑定this、arguments、super或new.target",
    "不能用作构造函数，不能使用new关键字",
    "没有prototype属性",
    "不能用作Generator函数(不能使用yield关键字)",
    "在对象方法中使用时this不指向对象本身"
  ],
  
  bestPractices: [
    // === 🎯 语法和格式最佳实践 ===
    "【语法一致性】单参数时建议保留括号以保持一致性: (item) => item.id，避免混合风格",
    "【对象返回】返回对象字面量时必须用括号包裹: () => ({ key: value })，避免语法歧义",
    "【多行处理】复杂逻辑使用块语句体: (a, b) => { const sum = a + b; return sum; }，提高可读性",
    "【参数解构】善用解构参数简化代码: ({ name, age }) => processUser(name, age)，而不是 (user) => processUser(user.name, user.age)",

    // === 🔧 this绑定最佳实践 ===
    "【避免对象方法】不要在对象方法中使用箭头函数: 使用 method: function() {} 而不是 method: () => {}",
    "【类方法绑定】在React类组件中，使用箭头函数避免手动绑定: onClick = () => {} 而不是 constructor中的bind",
    "【回调函数优选】在需要保持外层this的回调中优先使用箭头函数: setTimeout(() => this.update(), 1000)",

    // === 📊 性能优化最佳实践 ===
    "【避免重复创建】不要在render或循环中重复创建箭头函数: 将函数定义提取到外部或使用useCallback",
    "【内存管理】大量数据处理时考虑使用普通函数: 箭头函数的闭包可能导致内存占用增加",
    "【热点代码优化】在性能敏感的热点代码中，普通函数可能比箭头函数略快",

    // === 🎨 函数式编程最佳实践 ===
    "【数组方法链】在数组方法链中使用箭头函数: data.filter(x => x.active).map(x => x.name).sort((a, b) => a.localeCompare(b))",
    "【高阶函数】创建高阶函数时使用箭头函数: const createValidator = (rule) => (value) => rule.test(value)",
    "【函数组合】在函数组合中使用箭头函数提高可读性: compose(f, g, h) 中的每个函数都可以是箭头函数",

    // === 🚀 异步编程最佳实践 ===
    "【Promise链】在Promise链中使用箭头函数: fetch(url).then(res => res.json()).then(data => processData(data))",
    "【async/await】结合async/await使用箭头函数: const fetchData = async (url) => { const res = await fetch(url); return res.json(); }",
    "【错误处理】在异步箭头函数中正确处理错误: 使用try-catch或.catch()方法",

    // === 🛠️ 调试和维护最佳实践 ===
    "【函数命名】为复杂的箭头函数赋予有意义的变量名: const calculateTotalPrice = (items) => items.reduce((sum, item) => sum + item.price, 0)",
    "【代码分割】避免过长的箭头函数: 超过3行的逻辑考虑提取为独立函数",
    "【类型注解】在TypeScript中为箭头函数添加类型注解: const process = (data: UserData): ProcessedData => ({ ...data, processed: true })",

    // === 🎯 团队协作最佳实践 ===
    "【代码风格】团队内统一箭头函数的使用规范: 建立ESLint规则确保一致性",
    "【文档说明】为复杂的箭头函数添加注释说明: 特别是高阶函数和函数组合的场景",
    "【渐进迁移】在现有项目中渐进式引入箭头函数: 优先在新代码中使用，避免大规模重构"
  ],
  
  warnings: [
    "箭头函数中的this是词法绑定的，不能通过call、apply或bind改变",
    "在React类组件的方法中，如果需要this指向组件实例，要谨慎使用箭头函数",
    "箭头函数没有arguments对象，如需要使用参数列表请用rest参数(...args)",
    "箭头函数不能用作构造函数，没有prototype属性，不能使用new关键字",
    "在对象字面量的方法中使用箭头函数会导致this指向错误"
  ],

  // 🆕 对比分析 - 箭头函数 vs 其他函数类型
  comparisonAnalysis: {
    title: "箭头函数与其他函数类型的全面对比",
    description: "深入分析箭头函数与普通函数、函数表达式、方法定义等不同函数类型的差异，帮助开发者在不同场景下做出正确的技术选择。",
    comparisons: [
      {
        name: "箭头函数 vs 普通函数声明",
        description: "箭头函数与传统function声明的核心差异对比",
        advantages: [
          "语法更简洁，减少代码量",
          "词法this绑定，避免this指向混乱",
          "隐式返回，函数式编程更优雅",
          "不会被提升，避免意外的函数调用"
        ],
        disadvantages: [
          "不能用作构造函数",
          "没有arguments对象",
          "调试时函数名显示为anonymous",
          "不能使用call、apply、bind改变this"
        ],
        useCases: [
          "数组方法的回调函数：map、filter、reduce",
          "事件处理器，特别是需要保持外层this的场景",
          "Promise链和异步操作",
          "简短的工具函数和计算函数"
        ],
        performance: "在大多数现代JavaScript引擎中性能相近，箭头函数在某些场景下可能略快",
        complexity: "语法简单，学习成本低，但需要理解this绑定机制"
      },
      {
        name: "箭头函数 vs 函数表达式",
        description: "箭头函数与传统函数表达式的详细对比",
        advantages: [
          "语法更简洁：(x) => x * 2 vs function(x) { return x * 2; }",
          "this绑定更可预测，继承外层作用域",
          "支持隐式返回，减少return语句",
          "在函数式编程中可读性更好"
        ],
        disadvantages: [
          "缺少函数名，调试时不够友好",
          "不支持递归调用（除非赋值给变量）",
          "在复杂逻辑中可读性可能不如具名函数",
          "团队成员需要适应新语法"
        ],
        useCases: [
          "替代简单的函数表达式",
          "作为高阶函数的参数",
          "在闭包中保持this指向",
          "函数式编程的管道操作"
        ],
        performance: "性能基本相同，V8引擎对两者都有优化",
        complexity: "语法学习成本低，但概念理解需要时间"
      },
      {
        name: "箭头函数 vs 对象方法",
        description: "在对象方法定义中箭头函数与普通方法的对比",
        advantages: [
          "在某些场景下可以保持外层this",
          "语法一致性，与其他箭头函数保持统一",
          "在回调中不需要额外的bind操作"
        ],
        disadvantages: [
          "this不指向对象本身，破坏面向对象语义",
          "无法访问对象的其他属性和方法",
          "不符合对象方法的常规期望",
          "在原型链中使用会导致意外行为"
        ],
        useCases: [
          "❌ 不推荐：对象方法定义",
          "❌ 不推荐：原型方法定义",
          "✅ 可考虑：需要保持外层this的特殊场景",
          "✅ 推荐：对象内部的回调函数"
        ],
        performance: "性能差异不大，但语义错误可能导致运行时问题",
        complexity: "容易误用，需要深入理解this绑定机制"
      },
      {
        name: "箭头函数 vs Class方法",
        description: "在ES6类中箭头函数与普通方法的使用对比",
        advantages: [
          "自动绑定this，无需在constructor中bind",
          "在React组件中避免性能问题",
          "语法简洁，减少样板代码",
          "避免忘记绑定this的常见错误"
        ],
        disadvantages: [
          "每个实例都会创建新的函数，增加内存使用",
          "不在原型上，无法被子类继承",
          "在某些框架中可能影响性能优化",
          "调试时函数名不够清晰"
        ],
        useCases: [
          "React类组件的事件处理器",
          "需要作为回调传递的类方法",
          "避免this绑定问题的场景",
          "简单的类内部工具方法"
        ],
        performance: "内存使用略高，但避免了bind的运行时开销",
        complexity: "使用简单，但需要理解内存和继承的影响"
      }
    ],
    decisionMatrix: {
      description: "基于不同场景和需求的箭头函数使用决策矩阵",
      scenarios: [
        {
          scenario: "数组方法回调",
          recommendation: "箭头函数",
          reason: "语法简洁，this绑定清晰，函数式编程风格"
        },
        {
          scenario: "对象方法定义",
          recommendation: "普通函数",
          reason: "需要this指向对象本身，符合面向对象语义"
        },
        {
          scenario: "事件处理器",
          recommendation: "根据this需求决定",
          reason: "需要元素this用普通函数，需要外层this用箭头函数"
        },
        {
          scenario: "构造函数",
          recommendation: "普通函数",
          reason: "箭头函数不能用作构造函数"
        },
        {
          scenario: "递归函数",
          recommendation: "普通函数",
          reason: "需要函数名进行递归调用"
        },
        {
          scenario: "高阶函数",
          recommendation: "箭头函数",
          reason: "语法简洁，函数组合更清晰"
        },
        {
          scenario: "异步操作",
          recommendation: "箭头函数",
          reason: "Promise链和async/await中语法更简洁"
        }
      ]
    }
  }
};

export default basicInfo;