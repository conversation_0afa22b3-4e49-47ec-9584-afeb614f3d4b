import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const arrowfunctionsData: ApiItem = {
  id: 'arrow-functions',
  title: 'arrow-functions',
  description: '箭头函数是ES6(ES2015)中引入的新函数表达式语法，提供更简洁的函数定义方式和词法this绑定',
  category: 'ECMA/JavaScript',
  difficulty: 'medium',
  
  syntax: `// 基本语法
const func = (param1, param2) => expression;
const func = (param1, param2) => { return statement; };

// 特殊形式
const single = param => expression;     // 单参数可省略括号
const simple = () => expression;        // 无参数
const obj = () => ({ prop: value });    // 返回对象字面量`,

  example: `function ArrowFunctionExample() {
  // 基本用法 - 简化函数表达式
  const add = (a, b) => a + b;
  const greet = name => \`Hello, \${name}!\`;
  
  // 数组方法中的应用
  const numbers = [1, 2, 3, 4, 5];
  const doubled = numbers.map(n => n * 2);
  const evens = numbers.filter(n => n % 2 === 0);

  return (
    <div>
      {/* 展示箭头函数计算结果 */}
      <p>5 + 3 = {add(5, 3)}</p>
      <p>{greet("开发者")}</p>
      <p>原数组: {numbers.join(', ')}</p>
      <p>翻倍后: {doubled.join(', ')}</p>
      <p>偶数: {evens.join(', ')}</p>
    </div>
  );
}`,

  notes: '不绑定this、arguments、super或new.target',
  
  version: 'ES6 (ES2015)',
  tags: ["ES6", "JavaScript", "函数", "Lambda", "函数式编程"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default arrowfunctionsData;