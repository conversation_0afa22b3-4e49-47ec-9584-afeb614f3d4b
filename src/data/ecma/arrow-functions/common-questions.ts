import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么我的箭头函数中this是undefined或者不是我期望的值？',
    answer: `这是箭头函数最常见的困惑。箭头函数的this是词法绑定的，意思是它会"继承"定义时所在作用域的this值，而不是运行时的this。

常见情况：
1. **在对象方法中使用**：箭头函数的this不会指向对象本身，而是指向定义时的外层作用域
2. **在全局作用域中定义**：箭头函数的this指向全局对象（浏览器中是window，严格模式下是undefined）
3. **在React类组件中**：如果在render方法中定义箭头函数，this会正确指向组件实例

解决方案：
- 在对象方法中使用传统函数语法
- 在需要动态this的场景使用function关键字
- 理解箭头函数的词法作用域特性，合理选择使用场景`,
    code: `// ❌ 错误用法：对象方法使用箭头函数
const obj = {
  name: 'Alice',
  greet: () => {
    console.log(this.name); // undefined，this不指向obj
  }
};

// ✅ 正确用法：对象方法使用传统函数
const objFixed = {
  name: 'Alice',
  greet: function() {
    console.log(this.name); // 'Alice'，this指向obj
  },
  
  // 但在嵌套函数中，箭头函数很有用
  delayedGreet: function() {
    setTimeout(() => {
      console.log(this.name); // 'Alice'，继承外层this
    }, 1000);
  }
};

// React组件中的正确用法
class MyComponent extends React.Component {
  constructor(props) {
    super(props);
    this.state = { count: 0 };
  }
  
  // ✅ 箭头函数自动绑定this
  handleClick = () => {
    this.setState({ count: this.state.count + 1 });
  };
  
  render() {
    return <button onClick={this.handleClick}>Count: {this.state.count}</button>;
  }
}`,
    tags: ['this绑定', '词法作用域'],
    relatedQuestions: ['箭头函数和普通函数的区别', '什么时候使用箭头函数']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '箭头函数和普通函数到底有什么区别？',
    answer: `箭头函数和普通函数有几个关键区别：

**1. this绑定**
- 普通函数：动态绑定，取决于调用方式
- 箭头函数：词法绑定，继承外层作用域

**2. arguments对象**
- 普通函数：有自己的arguments对象
- 箭头函数：没有arguments，需要使用rest参数

**3. 构造函数**
- 普通函数：可以用作构造函数（new操作符）
- 箭头函数：不能用作构造函数，没有prototype属性

**4. 提升行为**
- 函数声明：会被提升到作用域顶部
- 箭头函数：变量提升规则，不能在定义前调用

**5. 语法简洁性**
- 箭头函数：更简洁，适合短小函数
- 普通函数：更明确，适合复杂逻辑`,
    code: `// 1. this绑定区别
const context = {
  name: 'Context',
  
  regularFunction: function() {
    console.log(this.name); // 'Context'
  },
  
  arrowFunction: () => {
    console.log(this.name); // undefined (或全局环境的name)
  }
};

// 2. arguments对象区别
function regularFunc() {
  console.log(arguments); // [1, 2, 3]
  console.log(arguments.length); // 3
}

const arrowFunc = (...args) => {
  // console.log(arguments); // ReferenceError: arguments is not defined
  console.log(args); // [1, 2, 3]
  console.log(args.length); // 3
};

regularFunc(1, 2, 3);
arrowFunc(1, 2, 3);

// 3. 构造函数区别
function RegularConstructor(name) {
  this.name = name;
}

const ArrowConstructor = (name) => {
  this.name = name;
};

const obj1 = new RegularConstructor('Alice'); // ✅ 正常工作
// const obj2 = new ArrowConstructor('Bob'); // ❌ TypeError: ArrowConstructor is not a constructor

// 4. 提升行为区别
console.log(regularFunction()); // ✅ "提升成功" - 函数声明被提升

function regularFunction() {
  return "提升成功";
}

// console.log(arrowFunction()); // ❌ ReferenceError: Cannot access 'arrowFunction' before initialization
const arrowFunction = () => "不能提升";`,
    tags: ['函数区别', 'this', 'arguments', '构造函数'],
    relatedQuestions: ['this绑定问题', '何时使用箭头函数']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: '什么时候应该使用箭头函数，什么时候不应该使用？',
    answer: `选择箭头函数还是普通函数需要根据具体场景：

**适合使用箭头函数的场景：**
1. **数组方法回调**：map、filter、reduce等
2. **事件处理器**：React组件中的事件处理
3. **Promise链**：then、catch回调
4. **短小的工具函数**：简单的数据转换
5. **需要保持外层this的回调**：setTimeout、API回调等

**不适合使用箭头函数的场景：**
1. **对象方法**：需要this指向对象本身
2. **构造函数**：需要new操作符调用
3. **需要动态this的场景**：事件监听器
4. **需要arguments对象**：处理可变参数
5. **原型方法**：需要this指向实例

**总结：**
当你需要简洁语法且不关心this指向时，优先选择箭头函数；当你需要动态this或特殊函数特性时，使用普通函数。`,
    code: `// ✅ 适合使用箭头函数的场景

// 1. 数组方法回调
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
const evens = numbers.filter(n => n % 2 === 0);

// 2. React事件处理
class Button extends React.Component {
  handleClick = (event) => {
    console.log('按钮被点击', this.props.id);
  };
  
  render() {
    return <button onClick={this.handleClick}>点击</button>;
  }
}

// 3. Promise链
fetch('/api/data')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error(error));

// 4. 短小工具函数
const add = (a, b) => a + b;
const getName = user => user.name;

// ❌ 不适合使用箭头函数的场景

// 1. 对象方法
const calculator = {
  value: 0,
  
  // ❌ 错误：this不指向calculator
  addWrong: (num) => {
    this.value += num; // this不是calculator
  },
  
  // ✅ 正确：this指向calculator
  add: function(num) {
    this.value += num;
    return this;
  }
};

// 2. 需要动态this的事件监听
document.getElementById('button').addEventListener('click', function() {
  console.log(this); // this是被点击的元素
  this.classList.add('clicked');
});

// 3. 构造函数
function Person(name) {
  this.name = name;
  this.greet = function() {
    return \`Hello, I'm \${this.name}\`;
  };
}

// 4. 原型方法
Person.prototype.sayGoodbye = function() {
  return \`Goodbye from \${this.name}\`;
};

// 5. 需要arguments的函数
function sum() {
  return Array.from(arguments).reduce((a, b) => a + b, 0);
}

console.log(sum(1, 2, 3, 4)); // 10`,
    tags: ['使用场景', '最佳实践', '选择指南'],
    relatedQuestions: ['函数区别对比', 'this绑定问题']
  }
];

export default commonQuestions;