import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: 'Object.entries()使用中的常见错误和解决方案',
        sections: [
          {
            title: '类型和参数错误',
            description: 'Object.entries()参数相关的常见问题',
            items: [
              {
                title: 'TypeError: Object.entries called on null or undefined',
                description: '对null或undefined调用Object.entries()',
                solution: '在调用前检查参数是否为有效对象',
                prevention: '使用可选链或默认值处理',
                code: `// ❌ 错误：对null/undefined调用Object.entries()
const data = null;
// Object.entries(data); // TypeError

// ✅ 正确：添加安全检查
function safeEntries(obj) {
  if (obj === null || obj === undefined) {
    return [];
  }
  return Object.entries(obj);
}

// ✅ 使用可选链和默认值
const entries = Object.entries(data ?? {});

// ✅ 类型检查函数
function getObjectEntries(obj) {
  if (typeof obj !== 'object' || obj === null) {
    console.warn('Expected object, got:', typeof obj);
    return [];
  }
  return Object.entries(obj);
}

// 调试辅助函数
function debugEntries(obj, label = 'object') {
  console.log(\`Debugging \${label}:\`);
  console.log('Type:', typeof obj);
  console.log('Value:', obj);
  console.log('Is null:', obj === null);
  console.log('Is undefined:', obj === undefined);
  
  try {
    const entries = Object.entries(obj);
    console.log('Entries:', entries);
    return entries;
  } catch (error) {
    console.error('Error getting entries:', error.message);
    return [];
  }
}`
              },
              {
                title: '意外的属性遗漏',
                description: '期望的属性没有出现在Object.entries()结果中',
                solution: '检查属性的可枚举性和继承关系',
                prevention: '理解Object.entries()只返回可枚举的自有属性',
                code: `// 属性遗漏调试
function analyzeObjectProperties(obj) {
  console.log('=== Object Property Analysis ===');
  
  // 所有自有属性（包括不可枚举）
  const ownProps = Object.getOwnPropertyNames(obj);
  console.log('Own properties:', ownProps);
  
  // 可枚举的自有属性
  const enumerableProps = Object.keys(obj);
  console.log('Enumerable properties:', enumerableProps);
  
  // Object.entries()结果
  const entries = Object.entries(obj);
  console.log('Object.entries():', entries);
  
  // Symbol属性
  const symbolProps = Object.getOwnPropertySymbols(obj);
  console.log('Symbol properties:', symbolProps);
  
  // 检查每个属性的描述符
  ownProps.forEach(prop => {
    const descriptor = Object.getOwnPropertyDescriptor(obj, prop);
    console.log(\`Property "\${prop}":\`, {
      enumerable: descriptor.enumerable,
      configurable: descriptor.configurable,
      writable: descriptor.writable,
      value: descriptor.value
    });
  });
  
  // 检查原型链
  const proto = Object.getPrototypeOf(obj);
  if (proto && proto !== Object.prototype) {
    console.log('Prototype properties:', Object.keys(proto));
  }
}

// 测试对象
const testObj = {
  visible: 'I am visible'
};

// 添加不可枚举属性
Object.defineProperty(testObj, 'hidden', {
  value: 'I am hidden',
  enumerable: false
});

// 添加Symbol属性
const sym = Symbol('symbol prop');
testObj[sym] = 'I am a symbol property';

// 分析属性
analyzeObjectProperties(testObj);`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '调试Object.entries()相关问题的工具和技巧',
        sections: [
          {
            title: 'Object.entries()调试工具',
            description: '专门用于调试Object.entries()行为的工具函数',
            items: [
              {
                title: '对象遍历比较工具',
                description: '比较不同遍历方法的结果差异',
                solution: '使用专门的比较函数分析差异',
                prevention: '在开发时验证遍历方法的正确性',
                code: `// 对象遍历方法比较工具
function compareObjectTraversalMethods(obj) {
  console.log('=== Object Traversal Comparison ===');
  console.log('Original object:', obj);
  
  // Object.entries()
  const entries = Object.entries(obj);
  console.log('\\nObject.entries():', entries);
  
  // Object.keys() + manual access
  const keysMethod = Object.keys(obj).map(key => [key, obj[key]]);
  console.log('Object.keys() + access:', keysMethod);
  
  // for...in loop
  const forInResults = [];
  for (const key in obj) {
    forInResults.push([key, obj[key]]);
  }
  console.log('for...in (all):', forInResults);
  
  // for...in with hasOwnProperty
  const forInOwnResults = [];
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      forInOwnResults.push([key, obj[key]]);
    }
  }
  console.log('for...in (own only):', forInOwnResults);
  
  // 比较结果
  const entriesStr = JSON.stringify(entries);
  const keysStr = JSON.stringify(keysMethod);
  const forInOwnStr = JSON.stringify(forInOwnResults);
  
  console.log('\\n=== Comparison Results ===');
  console.log('entries === keys+access:', entriesStr === keysStr);
  console.log('entries === for...in+own:', entriesStr === forInOwnStr);
  
  // 找出差异
  if (forInResults.length !== entries.length) {
    console.log('\\n⚠️  for...in includes inherited properties:');
    const inherited = forInResults.filter(([key]) => 
      !entries.some(([entryKey]) => entryKey === key)
    );
    console.log('Inherited properties:', inherited);
  }
  
  return {
    entries,
    keysMethod,
    forInResults,
    forInOwnResults
  };
}

// 深度对象分析工具
function deepAnalyzeObject(obj, maxDepth = 3, currentDepth = 0) {
  const indent = '  '.repeat(currentDepth);
  console.log(\`\${indent}Analyzing object at depth \${currentDepth}:\`);
  
  if (currentDepth >= maxDepth) {
    console.log(\`\${indent}Max depth reached\`);
    return;
  }
  
  const entries = Object.entries(obj);
  console.log(\`\${indent}Entries count: \${entries.length}\`);
  
  entries.forEach(([key, value], index) => {
    console.log(\`\${indent}[\${index}] \${key}: \${typeof value}\`);
    
    if (typeof value === 'object' && value !== null) {
      deepAnalyzeObject(value, maxDepth, currentDepth + 1);
    }
  });
}

// 性能分析工具
function benchmarkObjectMethods(obj, iterations = 1000) {
  console.log('=== Performance Benchmark ===');
  console.log(\`Testing with \${Object.keys(obj).length} properties, \${iterations} iterations\`);
  
  // Object.entries()
  console.time('Object.entries()');
  for (let i = 0; i < iterations; i++) {
    Object.entries(obj);
  }
  console.timeEnd('Object.entries()');
  
  // Object.keys()
  console.time('Object.keys()');
  for (let i = 0; i < iterations; i++) {
    Object.keys(obj);
  }
  console.timeEnd('Object.keys()');
  
  // for...in
  console.time('for...in');
  for (let i = 0; i < iterations; i++) {
    const result = [];
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        result.push([key, obj[key]]);
      }
    }
  }
  console.timeEnd('for...in');
}

// 使用示例
const complexObj = {
  name: 'Test Object',
  nested: {
    level1: {
      level2: 'deep value'
    }
  },
  array: [1, 2, 3],
  func: () => 'function'
};

// 添加继承属性
const parent = { inherited: 'parent value' };
const child = Object.create(parent);
Object.assign(child, complexObj);

compareObjectTraversalMethods(child);
deepAnalyzeObject(complexObj);
benchmarkObjectMethods(complexObj);`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
