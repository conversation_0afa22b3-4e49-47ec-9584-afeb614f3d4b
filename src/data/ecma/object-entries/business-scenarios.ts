import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'form-data-processing',
    title: '表单数据处理',
    description: '使用Object.entries()处理表单数据，包括验证、序列化、过滤等操作',
    businessValue: '简化表单数据处理流程，提高数据验证和转换的效率',
    scenario: '企业应用需要处理复杂的表单数据，包括用户输入验证、数据清理、格式转换等，需要高效的对象遍历和处理机制。',
    code: `// 表单数据处理器
class FormDataProcessor {
  constructor() {
    this.validators = new Map();
    this.transformers = new Map();
    this.serializers = new Map();
  }
  
  // 注册验证器
  addValidator(field, validator) {
    this.validators.set(field, validator);
  }
  
  // 注册转换器
  addTransformer(field, transformer) {
    this.transformers.set(field, transformer);
  }
  
  // 验证表单数据
  validate(formData) {
    const errors = {};
    
    for (const [field, value] of Object.entries(formData)) {
      const validator = this.validators.get(field);
      if (validator) {
        const error = validator(value);
        if (error) {
          errors[field] = error;
        }
      }
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
  
  // 清理表单数据
  sanitize(formData) {
    return Object.fromEntries(
      Object.entries(formData)
        .filter(([key, value]) => {
          // 过滤空值和无效字段
          return value !== null && 
                 value !== undefined && 
                 value !== '' && 
                 !key.startsWith('_');
        })
        .map(([key, value]) => {
          // 应用转换器
          const transformer = this.transformers.get(key);
          return [key, transformer ? transformer(value) : value];
        })
    );
  }
  
  // 序列化为查询字符串
  toQueryString(formData) {
    return Object.entries(formData)
      .filter(([key, value]) => value !== null && value !== undefined)
      .map(([key, value]) => {
        const encodedKey = encodeURIComponent(key);
        const encodedValue = encodeURIComponent(String(value));
        return \`\${encodedKey}=\${encodedValue}\`;
      })
      .join('&');
  }
  
  // 序列化为FormData
  toFormData(formData) {
    const form = new FormData();
    
    for (const [key, value] of Object.entries(formData)) {
      if (value instanceof File) {
        form.append(key, value);
      } else if (Array.isArray(value)) {
        value.forEach(item => form.append(\`\${key}[]\`, String(item)));
      } else if (value !== null && value !== undefined) {
        form.append(key, String(value));
      }
    }
    
    return form;
  }
  
  // 比较表单数据变化
  getChanges(originalData, currentData) {
    const changes = {};
    const allKeys = new Set([
      ...Object.keys(originalData),
      ...Object.keys(currentData)
    ]);
    
    for (const key of allKeys) {
      const originalValue = originalData[key];
      const currentValue = currentData[key];
      
      if (originalValue !== currentValue) {
        changes[key] = {
          from: originalValue,
          to: currentValue
        };
      }
    }
    
    return changes;
  }
  
  // 生成表单摘要
  generateSummary(formData) {
    const summary = {
      totalFields: 0,
      filledFields: 0,
      emptyFields: 0,
      fieldTypes: {},
      fieldSizes: {}
    };
    
    for (const [key, value] of Object.entries(formData)) {
      summary.totalFields++;
      
      if (value === null || value === undefined || value === '') {
        summary.emptyFields++;
      } else {
        summary.filledFields++;
      }
      
      const type = Array.isArray(value) ? 'array' : typeof value;
      summary.fieldTypes[type] = (summary.fieldTypes[type] || 0) + 1;
      
      if (typeof value === 'string') {
        summary.fieldSizes[key] = value.length;
      }
    }
    
    summary.completionRate = summary.filledFields / summary.totalFields;
    return summary;
  }
}

// 使用示例
const processor = new FormDataProcessor();

// 注册验证器
processor.addValidator('email', (value) => {
  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
  return emailRegex.test(value) ? null : 'Invalid email format';
});

processor.addValidator('age', (value) => {
  const age = Number(value);
  return age >= 18 && age <= 120 ? null : 'Age must be between 18 and 120';
});

// 注册转换器
processor.addTransformer('name', (value) => 
  value.trim().replace(/\\s+/g, ' ')
);

processor.addTransformer('email', (value) => 
  value.toLowerCase().trim()
);

// 处理表单数据
const formData = {
  name: '  John  Doe  ',
  email: '  <EMAIL>  ',
  age: '25',
  phone: '',
  newsletter: true,
  _token: 'csrf-token'
};

const validation = processor.validate(formData);
console.log('Validation:', validation);

const sanitized = processor.sanitize(formData);
console.log('Sanitized:', sanitized);

const queryString = processor.toQueryString(sanitized);
console.log('Query String:', queryString);

const summary = processor.generateSummary(formData);
console.log('Summary:', summary);`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'configuration-management',
    title: '配置管理系统',
    description: '使用Object.entries()构建灵活的配置管理系统，支持环境变量、默认值、验证等',
    businessValue: '提供统一的配置管理接口，简化应用配置的维护和部署',
    scenario: '微服务架构中需要管理大量配置项，包括数据库连接、API密钥、功能开关等，需要支持多环境配置和动态更新。',
    code: `// 配置管理系统
class ConfigurationManager {
  constructor() {
    this.configs = new Map();
    this.validators = new Map();
    this.transformers = new Map();
    this.watchers = new Map();
  }
  
  // 加载配置
  loadConfig(source, priority = 0) {
    const entries = Object.entries(source);
    
    for (const [key, value] of entries) {
      const existing = this.configs.get(key);
      
      // 根据优先级决定是否覆盖
      if (!existing || priority >= existing.priority) {
        this.configs.set(key, {
          value: this.transformValue(key, value),
          priority,
          source: source.constructor.name
        });
        
        // 触发监听器
        this.notifyWatchers(key, value);
      }
    }
  }
  
  // 获取配置值
  get(key, defaultValue = null) {
    const config = this.configs.get(key);
    return config ? config.value : defaultValue;
  }
  
  // 设置配置值
  set(key, value, priority = 100) {
    const transformedValue = this.transformValue(key, value);
    const error = this.validateValue(key, transformedValue);
    
    if (error) {
      throw new Error(\`Invalid config value for \${key}: \${error}\`);
    }
    
    this.configs.set(key, {
      value: transformedValue,
      priority,
      source: 'manual'
    });
    
    this.notifyWatchers(key, transformedValue);
  }
  
  // 获取所有配置
  getAll() {
    return Object.fromEntries(
      Array.from(this.configs.entries())
        .map(([key, config]) => [key, config.value])
    );
  }
  
  // 获取配置子集
  getNamespace(prefix) {
    return Object.fromEntries(
      Object.entries(this.getAll())
        .filter(([key]) => key.startsWith(prefix))
        .map(([key, value]) => [key.substring(prefix.length), value])
    );
  }
  
  // 验证所有配置
  validateAll() {
    const errors = {};
    
    for (const [key, config] of this.configs.entries()) {
      const error = this.validateValue(key, config.value);
      if (error) {
        errors[key] = error;
      }
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
  
  // 导出配置
  export(format = 'json') {
    const config = this.getAll();
    
    switch (format) {
      case 'json':
        return JSON.stringify(config, null, 2);
      
      case 'env':
        return Object.entries(config)
          .map(([key, value]) => \`\${key.toUpperCase()}=\${value}\`)
          .join('\\n');
      
      case 'yaml':
        return this.toYaml(config);
      
      default:
        return config;
    }
  }
  
  // 配置差异比较
  diff(otherConfig) {
    const current = this.getAll();
    const differences = {
      added: {},
      removed: {},
      changed: {}
    };
    
    // 检查新增和修改
    for (const [key, value] of Object.entries(otherConfig)) {
      if (!(key in current)) {
        differences.added[key] = value;
      } else if (current[key] !== value) {
        differences.changed[key] = {
          from: current[key],
          to: value
        };
      }
    }
    
    // 检查删除
    for (const [key, value] of Object.entries(current)) {
      if (!(key in otherConfig)) {
        differences.removed[key] = value;
      }
    }
    
    return differences;
  }
  
  // 注册验证器
  addValidator(key, validator) {
    this.validators.set(key, validator);
  }
  
  // 注册转换器
  addTransformer(key, transformer) {
    this.transformers.set(key, transformer);
  }
  
  // 注册监听器
  watch(key, callback) {
    if (!this.watchers.has(key)) {
      this.watchers.set(key, []);
    }
    this.watchers.get(key).push(callback);
  }
  
  // 私有方法
  transformValue(key, value) {
    const transformer = this.transformers.get(key);
    return transformer ? transformer(value) : value;
  }
  
  validateValue(key, value) {
    const validator = this.validators.get(key);
    return validator ? validator(value) : null;
  }
  
  notifyWatchers(key, value) {
    const watchers = this.watchers.get(key);
    if (watchers) {
      watchers.forEach(callback => callback(value, key));
    }
  }
  
  toYaml(obj, indent = 0) {
    const spaces = ' '.repeat(indent);
    return Object.entries(obj)
      .map(([key, value]) => {
        if (typeof value === 'object' && value !== null) {
          return \`\${spaces}\${key}:\\n\${this.toYaml(value, indent + 2)}\`;
        }
        return \`\${spaces}\${key}: \${value}\`;
      })
      .join('\\n');
  }
}

// 使用示例
const config = new ConfigurationManager();

// 添加验证器
config.addValidator('port', (value) => {
  const port = Number(value);
  return port >= 1 && port <= 65535 ? null : 'Port must be between 1 and 65535';
});

config.addValidator('email', (value) => {
  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
  return emailRegex.test(value) ? null : 'Invalid email format';
});

// 添加转换器
config.addTransformer('port', (value) => Number(value));
config.addTransformer('debug', (value) => value === 'true' || value === true);

// 加载不同来源的配置
const defaultConfig = {
  port: 3000,
  host: 'localhost',
  debug: false,
  database_url: 'sqlite://memory'
};

const envConfig = {
  port: process.env.PORT || 3000,
  debug: process.env.DEBUG || false,
  api_key: process.env.API_KEY
};

const userConfig = {
  port: 8080,
  debug: true,
  custom_feature: true
};

// 按优先级加载配置
config.loadConfig(defaultConfig, 0);  // 最低优先级
config.loadConfig(envConfig, 50);     // 中等优先级
config.loadConfig(userConfig, 100);   // 最高优先级

// 使用配置
console.log('Server port:', config.get('port'));
console.log('Debug mode:', config.get('debug'));
console.log('Database config:', config.getNamespace('database_'));

// 验证配置
const validation = config.validateAll();
console.log('Config validation:', validation);

// 导出配置
console.log('JSON config:', config.export('json'));
console.log('ENV config:', config.export('env'));`
  }
];

export default businessScenarios;
