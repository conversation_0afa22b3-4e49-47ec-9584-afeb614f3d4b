import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: 'Object.entries()会包含Symbol属性吗？',
    answer: '不会。Object.entries()只返回对象的可枚举字符串键属性，不包含Symbol属性。如果需要获取Symbol属性，应该使用Object.getOwnPropertySymbols()方法。这种设计保持了与传统对象遍历方法的一致性。',
    code: `// Symbol属性测试
const sym1 = Symbol('description');
const sym2 = Symbol.for('global');

const obj = {
  // 字符串键属性
  name: '<PERSON>',
  age: 30,
  
  // Symbol键属性
  [sym1]: 'symbol value 1',
  [sym2]: 'symbol value 2'
};

// Object.entries()只返回字符串键
console.log('Object.entries():', Object.entries(obj));
// [['name', 'John'], ['age', 30]]

// 获取Symbol属性需要专门的方法
console.log('Symbol keys:', Object.getOwnPropertySymbols(obj));
// [Symbol(description), Symbol(global)]

// 获取Symbol属性的值
const symbolEntries = Object.getOwnPropertySymbols(obj)
  .map(sym => [sym, obj[sym]]);
console.log('Symbol entries:', symbolEntries);
// [[Symbol(description), 'symbol value 1'], [Symbol(global), 'symbol value 2']]

// 获取所有属性（字符串 + Symbol）
function getAllEntries(obj) {
  const stringEntries = Object.entries(obj);
  const symbolEntries = Object.getOwnPropertySymbols(obj)
    .map(sym => [sym, obj[sym]]);
  
  return [...stringEntries, ...symbolEntries];
}

console.log('All entries:', getAllEntries(obj));

// 实际应用：处理混合键类型的对象
const metadata = Symbol('metadata');
const config = {
  host: 'localhost',
  port: 3000,
  debug: true,
  [metadata]: {
    version: '1.0.0',
    author: 'Developer'
  }
};

// 只序列化字符串键属性
const serializable = Object.fromEntries(Object.entries(config));
console.log('Serializable config:', JSON.stringify(serializable));

// 访问元数据
console.log('Metadata:', config[metadata]);`,
    tags: ['Symbol属性', '属性枚举', '对象遍历'],
    relatedQuestions: ['Symbol属性访问', 'Object.getOwnPropertySymbols']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: 'Object.entries()的返回顺序是什么？',
    answer: 'Object.entries()返回的键值对顺序遵循JavaScript对象属性的枚举顺序：首先是非负整数键（按数值升序），然后是其他字符串键（按创建顺序），最后是Symbol键（但Object.entries()不包含Symbol键）。这个顺序在ES2015+中是确定的。',
    code: `// 属性顺序测试
const obj = {};

// 添加不同类型的属性
obj.b = 'second string';
obj[2] = 'integer 2';
obj.a = 'first string';
obj[1] = 'integer 1';
obj[10] = 'integer 10';
obj.c = 'third string';

console.log('Object.entries() order:', Object.entries(obj));
// 输出顺序：
// [['1', 'integer 1'], ['2', 'integer 2'], ['10', 'integer 10'], 
//  ['b', 'second string'], ['a', 'first string'], ['c', 'third string']]

// 数组索引的特殊处理
const arrayLike = {
  2: 'third',
  0: 'first',
  1: 'second',
  length: 3,
  name: 'array-like'
};

console.log('Array-like entries:', Object.entries(arrayLike));
// [['0', 'first'], ['1', 'second'], ['2', 'third'], ['length', 3], ['name', 'array-like']]

// 动态添加属性的顺序
const dynamic = { z: 'last' };
dynamic.a = 'first';
dynamic[5] = 'number';
dynamic.m = 'middle';

console.log('Dynamic entries:', Object.entries(dynamic));
// [['5', 'number'], ['z', 'last'], ['a', 'first'], ['m', 'middle']]

// 实际应用：利用顺序进行排序
function createOrderedObject(pairs) {
  const obj = {};
  
  // 先添加数字键（会自动排序）
  pairs
    .filter(([key]) => /^\\d+$/.test(key))
    .sort(([a], [b]) => Number(a) - Number(b))
    .forEach(([key, value]) => obj[key] = value);
  
  // 再添加字符串键（保持顺序）
  pairs
    .filter(([key]) => !/^\\d+$/.test(key))
    .forEach(([key, value]) => obj[key] = value);
  
  return obj;
}

const pairs = [['name', 'John'], ['2', 'second'], ['age', 30], ['1', 'first']];
const ordered = createOrderedObject(pairs);
console.log('Ordered object entries:', Object.entries(ordered));

// 顺序的实际意义
const formFields = {
  3: { type: 'submit', label: 'Submit' },
  1: { type: 'text', label: 'Name' },
  2: { type: 'email', label: 'Email' },
  title: 'User Registration',
  description: 'Please fill out the form'
};

// 表单字段会按数字顺序排列
const fieldOrder = Object.entries(formFields)
  .filter(([key]) => /^\\d+$/.test(key))
  .map(([key, field]) => ({ order: Number(key), ...field }));

console.log('Form field order:', fieldOrder);
// 按1, 2, 3的顺序排列

// 注意：删除和重新添加会改变顺序
const mutable = { a: 1, b: 2, c: 3 };
delete mutable.b;
mutable.b = 'new b';

console.log('After delete and re-add:', Object.entries(mutable));
// [['a', 1], ['c', 3], ['b', 'new b']] - b移到了最后`,
    tags: ['属性顺序', '枚举顺序', '对象属性'],
    relatedQuestions: ['属性枚举规则', '对象键顺序', 'ES2015规范']
  }
];

export default commonQuestions;
