import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'Object.entries()与for...in循环有什么区别？',
    answer: {
      brief: 'Object.entries()只返回自有可枚举属性的键值对数组，而for...in会遍历包括继承属性在内的所有可枚举属性',
      detailed: `Object.entries()与for...in循环的主要区别：

**属性范围**：
- Object.entries()只返回自有可枚举属性
- for...in会遍历继承的可枚举属性

**返回格式**：
- Object.entries()返回键值对数组
- for...in只提供键，需要手动获取值

**使用便利性**：
- Object.entries()可以直接使用数组方法
- for...in需要额外的属性检查

**性能特点**：
- Object.entries()一次性创建数组
- for...in是惰性遍历`
    },
   
    difficulty: 'medium',
    frequency: 'high',
    category: '方法对比',
    tags: ['Object.entries', 'for...in', '对象遍历'],
    
    code: `// 测试对象
const parent = { inherited: 'parent value' };
const child = Object.create(parent);
child.own1 = 'child value 1';
child.own2 = 'child value 2';

// Object.entries() - 只返回自有属性
console.log('Object.entries():');
Object.entries(child).forEach(([key, value]) => {
  console.log(\`\${key}: \${value}\`);
});
// 输出：
// own1: child value 1
// own2: child value 2

// for...in - 包含继承属性
console.log('\\nfor...in:');
for (const key in child) {
  console.log(\`\${key}: \${child[key]}\`);
}
// 输出：
// own1: child value 1
// own2: child value 2
// inherited: parent value

// for...in with hasOwnProperty - 过滤继承属性
console.log('\\nfor...in with hasOwnProperty:');
for (const key in child) {
  if (child.hasOwnProperty(key)) {
    console.log(\`\${key}: \${child[key]}\`);
  }
}
// 输出：
// own1: child value 1
// own2: child value 2

// 性能对比示例
const largeObject = {};
for (let i = 0; i < 10000; i++) {
  largeObject[\`key\${i}\`] = \`value\${i}\`;
}

// Object.entries() - 函数式处理
console.time('Object.entries');
const filtered1 = Object.entries(largeObject)
  .filter(([key, value]) => key.includes('100'))
  .map(([key, value]) => \`\${key}=\${value}\`);
console.timeEnd('Object.entries');

// for...in - 命令式处理
console.time('for...in');
const filtered2 = [];
for (const key in largeObject) {
  if (largeObject.hasOwnProperty(key) && key.includes('100')) {
    filtered2.push(\`\${key}=\${largeObject[key]}\`);
  }
}
console.timeEnd('for...in');

// 不可枚举属性测试
const obj = {};
Object.defineProperty(obj, 'nonEnumerable', {
  value: 'hidden',
  enumerable: false
});
obj.enumerable = 'visible';

console.log('Object.entries():', Object.entries(obj));
// [['enumerable', 'visible']]

console.log('for...in:');
for (const key in obj) {
  console.log(key); // 只输出 'enumerable'
}

// Symbol属性测试
const sym = Symbol('symbol key');
const objWithSymbol = {
  stringKey: 'string value',
  [sym]: 'symbol value'
};

console.log('Object.entries():', Object.entries(objWithSymbol));
// [['stringKey', 'string value']] - 不包含Symbol属性

// 数组方法的优势
const data = {
  apple: 1.2,
  banana: 0.8,
  orange: 1.5,
  grape: 2.0
};

// 使用Object.entries()进行复杂操作
const expensiveFruits = Object.entries(data)
  .filter(([fruit, price]) => price > 1.0)
  .sort(([, a], [, b]) => b - a)
  .map(([fruit, price]) => \`\${fruit}: $\${price.toFixed(2)}\`);

console.log('Expensive fruits:', expensiveFruits);
// ['grape: $2.00', 'orange: $1.50', 'apple: $1.20']

// for...in实现相同功能会更复杂
const expensiveFruits2 = [];
for (const fruit in data) {
  if (data.hasOwnProperty(fruit) && data[fruit] > 1.0) {
    expensiveFruits2.push([fruit, data[fruit]]);
  }
}
expensiveFruits2.sort(([, a], [, b]) => b - a);
const result = expensiveFruits2.map(([fruit, price]) => \`\${fruit}: $\${price.toFixed(2)}\`);`,
    
    followUp: [
      'Object.keys()和Object.values()的区别？',
      '如何处理Symbol属性？',
      '什么时候使用for...in更合适？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'Object.entries()和Object.fromEntries()如何配合使用？',
    answer: {
      brief: 'Object.entries()将对象转换为键值对数组，Object.fromEntries()将键值对数组转换回对象，两者配合可以实现对象的函数式处理',
      detailed: `Object.entries()和Object.fromEntries()是一对互补的方法，形成了对象和键值对数组之间的双向转换：

**转换流程**：
- Object.entries()：对象 → 键值对数组
- Object.fromEntries()：键值对数组 → 对象

**常见用途**：
- 对象属性的过滤和转换
- 对象的深度处理
- 数据格式转换
- 对象的函数式编程操作`
    },
   
    difficulty: 'medium',
    frequency: 'high',
    category: '方法组合',
    tags: ['Object.entries', 'Object.fromEntries', '对象转换'],
    
    code: `// 基本的双向转换
const original = { a: 1, b: 2, c: 3 };

// 对象 → 键值对数组
const entries = Object.entries(original);
console.log('Entries:', entries); // [['a', 1], ['b', 2], ['c', 3]]

// 键值对数组 → 对象
const restored = Object.fromEntries(entries);
console.log('Restored:', restored); // { a: 1, b: 2, c: 3 }

// 对象属性过滤
const data = {
  name: 'John',
  age: 30,
  password: 'secret',
  email: '<EMAIL>',
  _internal: 'hidden'
};

// 过滤掉敏感信息
const publicData = Object.fromEntries(
  Object.entries(data)
    .filter(([key, value]) => !key.startsWith('_') && key !== 'password')
);
console.log('Public data:', publicData);
// { name: 'John', age: 30, email: '<EMAIL>' }

// 对象属性转换
const prices = { apple: '1.20', banana: '0.80', orange: '1.50' };

// 字符串价格转换为数字
const numericPrices = Object.fromEntries(
  Object.entries(prices)
    .map(([fruit, price]) => [fruit, parseFloat(price)])
);
console.log('Numeric prices:', numericPrices);
// { apple: 1.2, banana: 0.8, orange: 1.5 }

// 对象键名转换
const camelCaseObj = {
  firstName: 'John',
  lastName: 'Doe',
  emailAddress: '<EMAIL>'
};

// 转换为snake_case
const snakeCaseObj = Object.fromEntries(
  Object.entries(camelCaseObj)
    .map(([key, value]) => [
      key.replace(/[A-Z]/g, letter => \`_\${letter.toLowerCase()}\`),
      value
    ])
);
console.log('Snake case:', snakeCaseObj);
// { first_name: 'John', last_name: 'Doe', email_address: '<EMAIL>' }

// 对象值的批量处理
const userScores = {
  alice: 85,
  bob: 92,
  charlie: 78,
  diana: 96
};

// 计算等级
const userGrades = Object.fromEntries(
  Object.entries(userScores)
    .map(([name, score]) => {
      let grade;
      if (score >= 90) grade = 'A';
      else if (score >= 80) grade = 'B';
      else if (score >= 70) grade = 'C';
      else grade = 'F';
      
      return [name, { score, grade }];
    })
);
console.log('User grades:', userGrades);

// 对象排序
const unsortedData = { c: 3, a: 1, b: 2 };

// 按键排序
const sortedByKey = Object.fromEntries(
  Object.entries(unsortedData)
    .sort(([a], [b]) => a.localeCompare(b))
);
console.log('Sorted by key:', sortedByKey); // { a: 1, b: 2, c: 3 }

// 按值排序
const sortedByValue = Object.fromEntries(
  Object.entries(unsortedData)
    .sort(([, a], [, b]) => a - b)
);
console.log('Sorted by value:', sortedByValue); // { a: 1, b: 2, c: 3 }

// 对象合并和去重
const obj1 = { a: 1, b: 2 };
const obj2 = { b: 3, c: 4 };
const obj3 = { c: 5, d: 6 };

// 合并多个对象（后面的覆盖前面的）
const merged = Object.fromEntries([
  ...Object.entries(obj1),
  ...Object.entries(obj2),
  ...Object.entries(obj3)
]);
console.log('Merged:', merged); // { a: 1, b: 3, c: 5, d: 6 }

// 条件对象构建
const formData = {
  name: 'John',
  email: '<EMAIL>',
  phone: '',
  address: null,
  age: 30
};

// 只保留有效值
const cleanData = Object.fromEntries(
  Object.entries(formData)
    .filter(([key, value]) => 
      value !== null && 
      value !== undefined && 
      value !== ''
    )
);
console.log('Clean data:', cleanData);
// { name: 'John', email: '<EMAIL>', age: 30 }

// 深度对象处理
const nestedData = {
  user: { name: 'John', age: 30 },
  settings: { theme: 'dark', notifications: true },
  preferences: { language: 'en', timezone: 'UTC' }
};

// 扁平化对象
const flattened = Object.fromEntries(
  Object.entries(nestedData)
    .flatMap(([category, obj]) =>
      Object.entries(obj).map(([key, value]) => [\`\${category}.\${key}\`, value])
    )
);
console.log('Flattened:', flattened);
// { 'user.name': 'John', 'user.age': 30, 'settings.theme': 'dark', ... }

// 实用工具函数
function transformObject(obj, keyTransform, valueTransform) {
  return Object.fromEntries(
    Object.entries(obj)
      .map(([key, value]) => [
        keyTransform ? keyTransform(key) : key,
        valueTransform ? valueTransform(value) : value
      ])
  );
}

// 使用工具函数
const transformed = transformObject(
  { firstName: 'john', lastName: 'doe' },
  key => key.toUpperCase(),
  value => value.charAt(0).toUpperCase() + value.slice(1)
);
console.log('Transformed:', transformed); // { FIRSTNAME: 'John', LASTNAME: 'Doe' }`,
    
    followUp: [
      '如何处理嵌套对象的转换？',
      'Map和Object的转换？',
      '性能考虑和最佳实践？'
    ]
  }
];

export default interviewQuestions;
