import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const objectEntriesData: ApiItem = {
  id: 'object-entries',
  title: 'Object.entries()',
  description: 'ES2017对象条目方法，将对象转换为键值对数组，支持对象遍历和数据转换',
  category: 'ECMA特性',
  difficulty: 'easy',
  
  syntax: `Object.entries(obj); // 返回 [key, value] 数组`,
  example: `const obj = { a: 1, b: 2 }; Object.entries(obj); // [['a', 1], ['b', 2]]`,
  notes: 'Object.entries()是ES2017引入的静态方法，提供了遍历对象属性的标准方式',
  
  version: 'ES2017',
  tags: ['ES2017', 'JavaScript', 'Object.entries', '对象遍历', '键值对'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default objectEntriesData;
