/**
 * 🎯 Tab 4: 面试准备 (interview-questions.ts) - 核心指导原则
 *
 * 🚨 重要编码约束（最高优先级）
 *
 * ❌ 严格禁止的语法
 * 绝对不能使用任何形式的模板字符串插值语法 `${xxx}`
 *
 * 🛡️ 面试准备编码特殊要求
 * - 代码演示: 手写代码示例不使用模板字符串
 * - 算法实现: 算法题解中避免模板字符串语法
 * - 调试输出: console.log使用多参数形式
 * - 测试用例: 测试代码中使用标准字符串拼接
 *
 * 🎯 Tab定位与价值 - 实战升级版
 *
 * 🎭 **身份定位**：你是一位既当过面试官也当过求职者的技术前辈
 * 深知面试官真正想考察什么，也理解求职者内心的焦虑和困惑
 *
 * 💡 **核心信念**：真正的面试智慧不在于背诵答案，而在于理解问题的本质
 * 好的面试准备不是倾倒知识，而是点亮思路
 *
 * 🏗️ **承重墙思维**：帮助求职者识别面试中的：
 * - 🏗️ **承重墙问题**：不会就直接挂掉的核心概念（Promise状态、微任务）
 * - 🎨 **装饰品问题**：答对加分但不致命的细节（某些边缘API）
 * - 🚪 **暗门问题**：答好就能让面试官眼前一亮的深度思考
 *
 * 🌊 **表达温度**：像咖啡馆里的推心置腹对话：
 * "面试官问Promise状态，其实是想知道你对异步编程的理解深度..."
 *
 * 🎯 **终极目标**：让求职者面试结束后能自信地说：
 * "我不只是回答了问题，更是展现了我对异步编程的深度理解"
 *
 * 📊 内容结构要求 - 必须包含的面试题数组：
 * 每个面试题必须包含：
 * - id: 题目ID
 * - question: 🆕 真实面试问题（基于实际面试经验）
 * - difficulty: 难度等级 ('easy' | 'medium' | 'hard')
 * - frequency: 🆕 真实出现频率 ('high' | 'medium' | 'low')
 * - category: 问题分类
 * - answer: 答案对象
 *   - brief: 🆕 精准简短回答（30-50字，面试官满意的标准答案）
 *   - detailed: 🆕 分层详细解析（基础概念→深入原理→实际应用）
 *   - code: 🆕 完整可运行代码示例（包含注释和对比）
 *   - followUp: 🆕 真实追问题目（面试官常问的后续问题）
 * - tags: 相关标签
 * - 🆕 已实现高质量题目：Promise状态、Promise.all vs allSettled、微任务队列、错误处理等
 *
 * 🎯 质量标准
 * - 问题真实：基于真实面试经验，不是人为构造的问题
 * - 难度合理：覆盖初级、中级、高级不同层次
 * - 答案准确：技术答案准确无误，经过验证
 * - 解析深入：不仅给出答案，还解释为什么这样回答
 * - 代码可运行：所有代码示例完整且可执行
 * - 追问准备：预测面试官可能的追问，提前准备
 * - 公司针对性：标注哪些公司常考这类问题
 *
 * 💡 面试策略
 * - 基础必答：确保基础概念问题能够流利回答
 * - 原理深入：能够解释底层实现原理和设计思路
 * - 实战结合：结合实际项目经验回答问题
 * - 对比分析：能够对比不同方案的优缺点
 * - 扩展思考：展示对相关技术的深度理解
 */

import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'Promise的三种状态是什么？状态如何转换？',
    answer: {
      brief: 'Promise有三种状态：pending（等待中）、fulfilled（已成功）、rejected（已失败）。状态只能从pending转换为fulfilled或rejected，且转换不可逆。',
      detailed: `Promise有三种状态：

**pending（进行中）**：
- 初始状态，既不是成功也不是失败
- 可以转换为fulfilled或rejected

**fulfilled（已成功）**：
- 操作成功完成
- 从pending转换而来，不可逆

**rejected（已失败）**：
- 操作失败
- 从pending转换而来，不可逆

**状态转换规则**：
- 只能从pending转换为fulfilled或rejected
- 状态一旦改变就不能再变
- 这种设计确保了异步操作的可预测性`,
      code: `// Promise状态演示
const promise = new Promise((resolve, reject) => {
  console.log('Promise状态: pending');

  setTimeout(() => {
    const success = Math.random() > 0.5;
    if (success) {
      resolve('操作成功');
      console.log('Promise状态: fulfilled');
    } else {
      reject(new Error('操作失败'));
      console.log('Promise状态: rejected');
    }
  }, 1000);
});

promise
  .then(value => {
    console.log('成功:', value);
  })
  .catch(error => {
    console.log('失败:', error.message);
  });

// 状态不可逆示例
const resolvedPromise = Promise.resolve('已解决');
// 无法再改变状态
resolvedPromise.then(value => {
  console.log(value); // "已解决"
});`
    },
   
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    tags: ['Promise状态', '状态转换', '异步编程'],
    
    followUp: [
      'Promise状态改变后会发生什么？',
      '如何检查Promise的当前状态？',
      'Promise状态与微任务的关系？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'Promise.all和Promise.allSettled有什么区别？',
    answer: {
      brief: 'Promise.all在任一Promise失败时立即失败，Promise.allSettled等待所有Promise完成并返回每个的状态和结果。',
      detailed: `Promise.all和Promise.allSettled的主要区别：

**Promise.all**：
- 所有Promise都成功才返回成功
- 任何一个Promise失败就立即返回失败
- 返回结果数组，顺序与输入顺序一致
- 适用于所有操作都必须成功的场景

**Promise.allSettled**：
- 等待所有Promise完成（无论成功失败）
- 返回每个Promise的状态和结果
- 不会因为某个Promise失败而提前结束
- 适用于需要知道所有操作结果的场景`,
      code: `// Promise.all示例
const promises1 = [
  Promise.resolve(1),
  Promise.resolve(2),
  Promise.reject(new Error('失败'))
];

Promise.all(promises1)
  .then(results => {
    console.log('全部成功:', results);
  })
  .catch(error => {
    console.log('有失败:', error.message); // "失败"
  });

// Promise.allSettled示例
Promise.allSettled(promises1)
  .then(results => {
    console.log('所有结果:', results);
    // [
    //   { status: 'fulfilled', value: 1 },
    //   { status: 'fulfilled', value: 2 },
    //   { status: 'rejected', reason: Error('失败') }
    // ]
  });`
    },
   
    difficulty: 'medium',
    frequency: 'high',
    category: '静态方法',
    tags: ['Promise.all', 'Promise.allSettled', '并行处理'],

    followUp: [
      'Promise.all和Promise.allSettled的性能差异？',
      '如何处理Promise.all中的部分失败？',
      'Promise.allSettled的返回值结构是什么？'
    ]
  },

  {
    id: 3,
    question: '请手写实现一个简单的Promise',
    answer: {
      brief: '实现Promise需要处理状态管理、回调队列、微任务调度和链式调用等核心机制。',
      detailed: `
手写Promise的核心要点：

**1. 状态管理**
- 维护三种状态：pending、fulfilled、rejected
- 状态只能从pending转换，且不可逆

**2. 回调队列**
- 维护成功和失败的回调队列
- 状态改变时执行相应回调

**3. 微任务调度**
- 使用queueMicrotask或setTimeout模拟异步执行
- 确保回调在下一个事件循环中执行

**4. 链式调用**
- then方法返回新的Promise
- 处理回调返回值的不同情况
      `,
      code: `
// 手写Promise实现
class MyPromise {
  constructor(executor) {
    // 初始状态
    this.state = 'pending';
    this.value = undefined;
    this.reason = undefined;

    // 回调队列
    this.onFulfilledCallbacks = [];
    this.onRejectedCallbacks = [];

    // resolve方法
    const resolve = (value) => {
      if (this.state === 'pending') {
        this.state = 'fulfilled';
        this.value = value;

        // 执行所有成功回调
        this.onFulfilledCallbacks.forEach(callback => {
          queueMicrotask(() => callback(value));
        });
      }
    };

    // reject方法
    const reject = (reason) => {
      if (this.state === 'pending') {
        this.state = 'rejected';
        this.reason = reason;

        // 执行所有失败回调
        this.onRejectedCallbacks.forEach(callback => {
          queueMicrotask(() => callback(reason));
        });
      }
    };

    // 立即执行executor
    try {
      executor(resolve, reject);
    } catch (error) {
      reject(error);
    }
  }

  // then方法实现
  then(onFulfilled, onRejected) {
    // 返回新的Promise实现链式调用
    return new MyPromise((resolve, reject) => {

      // 处理fulfilled状态
      const handleFulfilled = (value) => {
        try {
          if (typeof onFulfilled === 'function') {
            const result = onFulfilled(value);

            // 处理返回值
            if (result instanceof MyPromise) {
              result.then(resolve, reject);
            } else {
              resolve(result);
            }
          } else {
            resolve(value); // 值穿透
          }
        } catch (error) {
          reject(error);
        }
      };

      // 处理rejected状态
      const handleRejected = (reason) => {
        try {
          if (typeof onRejected === 'function') {
            const result = onRejected(reason);

            if (result instanceof MyPromise) {
              result.then(resolve, reject);
            } else {
              resolve(result); // 错误恢复
            }
          } else {
            reject(reason); // 错误传播
          }
        } catch (error) {
          reject(error);
        }
      };

      // 根据当前状态处理
      if (this.state === 'fulfilled') {
        queueMicrotask(() => handleFulfilled(this.value));
      } else if (this.state === 'rejected') {
        queueMicrotask(() => handleRejected(this.reason));
      } else {
        // pending状态，将回调加入队列
        this.onFulfilledCallbacks.push(handleFulfilled);
        this.onRejectedCallbacks.push(handleRejected);
      }
    });
  }

  // catch方法
  catch(onRejected) {
    return this.then(null, onRejected);
  }

  // finally方法
  finally(onFinally) {
    return this.then(
      value => {
        return MyPromise.resolve(onFinally()).then(() => value);
      },
      reason => {
        return MyPromise.resolve(onFinally()).then(() => {
          throw reason;
        });
      }
    );
  }

  // 静态方法
  static resolve(value) {
    if (value instanceof MyPromise) {
      return value;
    }

    return new MyPromise(resolve => {
      resolve(value);
    });
  }

  static reject(reason) {
    return new MyPromise((_, reject) => {
      reject(reason);
    });
  }

  static all(promises) {
    return new MyPromise((resolve, reject) => {
      if (promises.length === 0) {
        resolve([]);
        return;
      }

      const results = [];
      let completedCount = 0;

      promises.forEach((promise, index) => {
        MyPromise.resolve(promise).then(value => {
          results[index] = value;
          completedCount++;

          if (completedCount === promises.length) {
            resolve(results);
          }
        }).catch(reject);
      });
    });
  }
}

// 测试用例
const promise = new MyPromise((resolve, reject) => {
  setTimeout(() => {
    resolve('成功');
  }, 1000);
});

promise
  .then(value => {
    console.log('第一个then:', value);
    return '链式调用';
  })
  .then(value => {
    console.log('第二个then:', value);
  })
  .catch(error => {
    console.log('错误:', error);
  });
      `
    },
    difficulty: 'hard',
    frequency: 'high',
    category: '手写实现'
  },

  {
    id: 4,
    question: '解释Promise的微任务机制和事件循环',
    answer: {
      brief: 'Promise回调在微任务队列中执行，优先级高于宏任务，这确保了Promise的执行顺序和性能。',
      detailed: `
**事件循环和任务队列：**

1. **调用栈（Call Stack）**：同步代码的执行环境
2. **宏任务队列（Macro Task Queue）**：setTimeout、setInterval、I/O操作
3. **微任务队列（Micro Task Queue）**：Promise回调、queueMicrotask、MutationObserver

**执行顺序：**
1. 执行所有同步代码
2. 执行所有微任务
3. 执行一个宏任务
4. 重复步骤2-3

**Promise微任务特点：**
- then/catch/finally回调都是微任务
- 微任务优先级高于宏任务
- 微任务会在当前事件循环结束前全部执行完
      `,
      code: `
// 事件循环和微任务示例
console.log('1: 同步代码开始');

setTimeout(() => {
  console.log('2: 宏任务 - setTimeout');
}, 0);

Promise.resolve().then(() => {
  console.log('3: 微任务 - Promise.then');

  // 微任务中的微任务
  Promise.resolve().then(() => {
    console.log('4: 嵌套微任务');
  });
});

queueMicrotask(() => {
  console.log('5: 微任务 - queueMicrotask');
});

console.log('6: 同步代码结束');

// 输出顺序：
// 1: 同步代码开始
// 6: 同步代码结束
// 3: 微任务 - Promise.then
// 5: 微任务 - queueMicrotask
// 4: 嵌套微任务
// 2: 宏任务 - setTimeout

// 复杂的执行顺序示例
async function complexExample() {
  console.log('A: async函数开始');

  setTimeout(() => console.log('B: setTimeout 1'), 0);

  await Promise.resolve();
  console.log('C: await后的代码');

  setTimeout(() => console.log('D: setTimeout 2'), 0);

  Promise.resolve().then(() => {
    console.log('E: Promise.then');
  });

  console.log('F: async函数结束');
}

complexExample();
console.log('G: 主线程代码');

// 输出顺序：
// A: async函数开始
// G: 主线程代码
// C: await后的代码
// F: async函数结束
// E: Promise.then
// B: setTimeout 1
// D: setTimeout 2

// 微任务队列清空示例
function demonstrateMicrotaskQueue() {
  console.log('开始');

  // 添加多个微任务
  for (let i = 0; i < 3; i++) {
    Promise.resolve().then(() => {
      console.log(\`微任务 \${i}\`);

      // 在微任务中添加新的微任务
      if (i === 1) {
        Promise.resolve().then(() => {
          console.log('嵌套微任务');
        });
      }
    });
  }

  setTimeout(() => {
    console.log('宏任务');
  }, 0);

  console.log('结束');
}

demonstrateMicrotaskQueue();
// 输出：
// 开始
// 结束
// 微任务 0
// 微任务 1
// 微任务 2
// 嵌套微任务
// 宏任务
      `
    },
    difficulty: 'hard',
    frequency: 'high',
    category: '事件循环'
  },

  {
    id: 5,
    question: '如何实现Promise的超时控制和取消机制？',
    answer: {
      brief: '可以使用Promise.race结合setTimeout实现超时，使用AbortController实现取消机制。',
      detailed: `
**超时控制方案：**
1. **Promise.race + setTimeout**：竞速机制实现超时
2. **AbortController**：标准的取消机制
3. **自定义取消Token**：更灵活的控制方式

**实现要点：**
- 超时后应该清理资源
- 取消操作应该是幂等的
- 提供清晰的错误信息
      `,
      code: `
// 1. 基础超时实现
function withTimeout(promise, timeout) {
  return Promise.race([
    promise,
    new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(\`操作超时: \${timeout}ms\`));
      }, timeout);
    })
  ]);
}

// 使用示例
const slowOperation = new Promise(resolve => {
  setTimeout(() => resolve('完成'), 2000);
});

withTimeout(slowOperation, 1000)
  .then(result => console.log('成功:', result))
  .catch(error => console.log('失败:', error.message));

// 2. 支持取消的Promise包装器
class CancellablePromise {
  constructor(executor) {
    this.isCancelled = false;
    this.cancelReason = null;

    this.promise = new Promise((resolve, reject) => {
      this.resolve = resolve;
      this.reject = reject;

      // 执行原始executor
      executor(
        (value) => {
          if (!this.isCancelled) {
            resolve(value);
          }
        },
        (reason) => {
          if (!this.isCancelled) {
            reject(reason);
          }
        }
      );
    });
  }

  cancel(reason = '操作被取消') {
    if (!this.isCancelled) {
      this.isCancelled = true;
      this.cancelReason = reason;
      this.reject(new Error(reason));
    }
  }

  then(onFulfilled, onRejected) {
    return this.promise.then(onFulfilled, onRejected);
  }

  catch(onRejected) {
    return this.promise.catch(onRejected);
  }
}

// 3. 使用AbortController的现代实现
class ModernPromiseController {
  constructor() {
    this.controller = new AbortController();
    this.signal = this.controller.signal;
  }

  // 创建可取消的fetch请求
  fetch(url, options = {}) {
    return fetch(url, {
      ...options,
      signal: this.signal
    });
  }

  // 创建可取消的延时
  delay(ms) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(resolve, ms);

      this.signal.addEventListener('abort', () => {
        clearTimeout(timeoutId);
        reject(new Error('延时被取消'));
      });
    });
  }

  // 取消所有操作
  cancel() {
    this.controller.abort();
  }

  // 检查是否已取消
  get isCancelled() {
    return this.signal.aborted;
  }
}

// 4. 高级超时和重试机制
class AdvancedPromiseUtils {
  // 带超时的Promise
  static withTimeout(promise, timeout, timeoutMessage) {
    let timeoutId;

    const timeoutPromise = new Promise((_, reject) => {
      timeoutId = setTimeout(() => {
        reject(new Error(timeoutMessage || \`操作超时: \${timeout}ms\`));
      }, timeout);
    });

    return Promise.race([
      promise.finally(() => clearTimeout(timeoutId)),
      timeoutPromise
    ]);
  }

  // 重试机制
  static async retry(operation, maxAttempts = 3, delay = 1000) {
    let lastError;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        console.warn(\`尝试 \${attempt} 失败:\`, error.message);

        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }
      }
    }

    throw lastError;
  }

  // 组合超时和重试
  static async retryWithTimeout(operation, timeout, maxAttempts = 3) {
    return this.retry(
      () => this.withTimeout(operation(), timeout),
      maxAttempts
    );
  }
}

// 使用示例
const controller = new ModernPromiseController();

// 可取消的网络请求
controller.fetch('/api/data')
  .then(response => response.json())
  .then(data => console.log('数据:', data))
  .catch(error => {
    if (error.name === 'AbortError') {
      console.log('请求被取消');
    } else {
      console.log('请求失败:', error.message);
    }
  });

// 5秒后取消
setTimeout(() => {
  controller.cancel();
}, 5000);

// 使用高级工具
AdvancedPromiseUtils.retryWithTimeout(
  () => fetch('/api/unreliable-endpoint'),
  3000, // 3秒超时
  3     // 最多重试3次
)
.then(response => console.log('成功获取数据'))
.catch(error => console.log('最终失败:', error.message));
      `
    },
    difficulty: 'hard',
    frequency: 'medium',
    category: '高级应用'
  }
];

export default interviewQuestions;
