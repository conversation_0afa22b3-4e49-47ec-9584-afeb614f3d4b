/**
 * ❓ Tab 5: 常见问题 (common-questions.ts) - 核心指导原则
 *
 * 🚨 重要编码约束（最高优先级）
 *
 * ❌ 严格禁止的语法
 * 绝对不能使用任何形式的模板字符串插值语法 `${xxx}`
 *
 * 🛡️ 常见问题编码特殊要求
 * - 错误示例: 展示错误代码时不使用模板字符串
 * - 解决方案: 修复代码中避免模板字符串语法
 * - 调试技巧: 调试代码使用标准输出方式
 * - 最佳实践: 推荐代码遵循项目编码规范
 *
 * 🎯 Tab定位与价值 - 实战救援版
 *
 * 🎭 **身份定位**：你是一位在各种项目中踩过无数坑的老司机
 * 还记得深夜被Promise错误折磨到怀疑人生的痛苦经历
 *
 * 💡 **核心使命**：常见问题Tab是**深夜救援的明灯**
 * 当开发者在凌晨2点被Promise问题困扰时，这里就是他们的救命稻草
 *
 * 🏗️ **价值序列**：
 * - 实用性 >>> 全面性：能立即解决问题的，比理论完整更重要
 * - 深度 > 广度：一个问题挖透，胜过十个问题浅尝
 * - 连接 > 孤立：展现问题间的关系网，避免重复踩坑
 *
 * 🌊 **表达温度**：像一位愿意分享踩坑经验的老友：
 * "这个问题我也遇到过，当时差点让整个项目延期，后来发现原来是..."
 *
 * 🎨 **美学追求**：每个问题的解答都应该让人有"原来如此"的顿悟感
 * 不只是给出解决方案，更要解释为什么会出现这个问题
 *
 * 📊 内容结构要求 - 必须包含的问题数组：
 * 每个常见问题必须包含：
 * - id: 问题ID
 * - question: 🆕 真实开发问题（基于实际项目经验）
 * - answer: 🆕 简化结构 - 直接提供解决方案和代码示例
 * - code: 🆕 完整代码示例（包含执行顺序注释和复杂示例）
 * - tags: 相关标签
 * - relatedQuestions: 🆕 相关问题（替代原来的复杂结构）
 *
 * 🆕 已实现高质量问题：
 * - 微任务队列执行顺序（包含复杂示例和输出注释）
 * - Promise未捕获错误处理（Node.js和浏览器环境）
 * - Promise链式调用中的错误传播
 * - async/await与Promise的性能对比
 * - Promise内存泄漏问题和解决方案
 * - Promise超时处理和取消机制
 *
 * 🎯 质量标准
 * - 问题真实：基于真实开发经验，不是人为构造的问题
 * - 频率准确：反映问题在实际开发中的真实出现频率
 * - 解决方案有效：提供的解决方案经过验证，确实能解决问题
 * - 原因解释清晰：不仅给出解决方案，还解释问题产生的根本原因
 * - 预防措施实用：提供实用的预防措施，避免问题再次发生
 * - 代码示例对比：通过错误和正确示例的对比，加深理解
 * - 相关问题关联：关联相似或相关的问题，形成知识网络
 *
 * 💡 问题收集策略
 * - 社区反馈：从GitHub Issues、Stack Overflow等收集真实问题
 * - 项目经验：基于实际项目开发中遇到的问题
 * - 新手困惑：关注新手开发者容易遇到的困惑点
 * - 版本升级：收集版本升级过程中的兼容性问题
 * - 最佳实践：整理违反最佳实践导致的常见问题
 */

import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么Promise回调在微任务队列中执行？',
    answer: 'Promise回调在微任务队列中执行是为了确保正确的执行顺序和性能。微任务的优先级高于宏任务，会在当前执行栈清空后立即执行，这样可以保证Promise回调的及时执行，同时避免阻塞其他重要操作。这种设计让异步操作更加可预测。',
    code: `// 微任务执行顺序演示
console.log('1: 同步代码');

setTimeout(() => {
  console.log('4: 宏任务 - setTimeout');
}, 0);

Promise.resolve().then(() => {
  console.log('3: 微任务 - Promise');
});

console.log('2: 同步代码');

// 输出顺序：
// 1: 同步代码
// 2: 同步代码  
// 3: 微任务 - Promise
// 4: 宏任务 - setTimeout

// 复杂示例
Promise.resolve().then(() => {
  console.log('Promise 1');
  return Promise.resolve();
}).then(() => {
  console.log('Promise 2');
});

setTimeout(() => {
  console.log('setTimeout 1');
  Promise.resolve().then(() => {
    console.log('Promise in setTimeout');
  });
}, 0);`,
    tags: ['微任务', '事件循环', '执行顺序'],
    relatedQuestions: ['事件循环机制', '宏任务与微任务']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '如何处理Promise的未捕获错误？',
    answer: 'Promise的未捕获错误可能导致应用崩溃。处理方法包括：1) 为每个Promise链添加catch；2) 使用全局错误处理器；3) 在Node.js中监听unhandledRejection事件；4) 在浏览器中监听unhandledrejection事件。最佳实践是主动处理所有可能的错误情况。',
    code: `// 1. 主动错误处理
promise
  .then(result => {
    // 处理成功
  })
  .catch(error => {
    // 处理错误
    console.error('Promise错误:', error);
  });

// 2. 全局错误处理 - Node.js
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  console.error('Promise:', promise);
  // 记录错误日志
  // 可能需要优雅关闭应用
});

// 3. 全局错误处理 - 浏览器
window.addEventListener('unhandledrejection', event => {
  console.error('未处理的Promise拒绝:', event.reason);
  console.error('Promise:', event.promise);
  
  // 阻止默认行为（控制台错误）
  event.preventDefault();
  
  // 发送错误报告
  sendErrorReport(event.reason);
});

// 4. 错误恢复示例
async function robustOperation() {
  try {
    const result = await riskyOperation();
    return result;
  } catch (error) {
    console.warn('主操作失败，尝试备用方案:', error.message);
    
    try {
      return await fallbackOperation();
    } catch (fallbackError) {
      console.error('备用方案也失败:', fallbackError.message);
      throw new Error('所有操作都失败了');
    }
  }
}`,
    tags: ['错误处理', '未捕获错误', '全局处理器'],
    relatedQuestions: ['错误处理最佳实践', '异步错误调试']
  },

  {
    id: 'promise-vs-callback',
    question: 'Promise相比回调函数有什么优势？',
    answer: `Promise相比回调函数的主要优势包括：

**1. 解决回调地狱**
- 回调函数嵌套导致代码难以阅读和维护
- Promise通过链式调用提供更清晰的代码结构

**2. 统一的错误处理**
- 回调函数需要在每个层级处理错误
- Promise提供统一的catch机制

**3. 更好的组合性**
- Promise.all、Promise.race等方法支持复杂的异步编排
- 回调函数难以实现复杂的并发控制

**4. 状态管理**
- Promise有明确的状态（pending/fulfilled/rejected）
- 回调函数没有状态概念，难以追踪异步操作

**5. 可测试性**
- Promise返回值可以被测试框架更好地处理
- 回调函数的测试需要复杂的模拟机制`,
    code: `
// 回调地狱示例
function callbackHell() {
  getData(function(a) {
    getMoreData(a, function(b) {
      getEvenMoreData(b, function(c) {
        getFinalData(c, function(d) {
          // 终于得到最终结果
          console.log('最终结果:', d);
        }, function(error) {
          console.error('获取最终数据失败:', error);
        });
      }, function(error) {
        console.error('获取更多数据失败:', error);
      });
    }, function(error) {
      console.error('获取更多数据失败:', error);
    });
  }, function(error) {
    console.error('获取数据失败:', error);
  });
}

// Promise链式调用
function promiseChain() {
  return getData()
    .then(a => getMoreData(a))
    .then(b => getEvenMoreData(b))
    .then(c => getFinalData(c))
    .then(d => {
      console.log('最终结果:', d);
      return d;
    })
    .catch(error => {
      console.error('操作失败:', error);
      throw error;
    });
}

// async/await进一步简化
async function asyncAwaitVersion() {
  try {
    const a = await getData();
    const b = await getMoreData(a);
    const c = await getEvenMoreData(b);
    const d = await getFinalData(c);

    console.log('最终结果:', d);
    return d;
  } catch (error) {
    console.error('操作失败:', error);
    throw error;
  }
}

// 并发处理对比
// 回调方式的并发处理
function callbackConcurrent(callback) {
  let results = [];
  let completed = 0;
  const total = 3;

  function checkComplete() {
    if (completed === total) {
      callback(null, results);
    }
  }

  getData1(function(err, data1) {
    if (err) return callback(err);
    results[0] = data1;
    completed++;
    checkComplete();
  });

  getData2(function(err, data2) {
    if (err) return callback(err);
    results[1] = data2;
    completed++;
    checkComplete();
  });

  getData3(function(err, data3) {
    if (err) return callback(err);
    results[2] = data3;
    completed++;
    checkComplete();
  });
}

// Promise方式的并发处理
async function promiseConcurrent() {
  try {
    const [data1, data2, data3] = await Promise.all([
      getData1(),
      getData2(),
      getData3()
    ]);

    return [data1, data2, data3];
  } catch (error) {
    console.error('并发操作失败:', error);
    throw error;
  }
}
    `,
    tags: ['Promise优势', '回调地狱', '代码组织'],
    relatedQuestions: ['async/await与Promise', '异步编程最佳实践']
  },

  {
    id: 'promise-memory-leaks',
    question: 'Promise会导致内存泄漏吗？如何避免？',
    answer: `Promise本身不会导致内存泄漏，但不当使用可能导致内存问题：

**可能导致内存泄漏的情况：**
1. **长时间pending的Promise** - 持有大量回调函数
2. **循环引用** - Promise链中的闭包引用外部变量
3. **未清理的定时器** - Promise中的setTimeout未清理
4. **大对象引用** - Promise回调中引用大对象

**避免内存泄漏的方法：**
1. 及时resolve/reject Promise
2. 避免创建不必要的闭包
3. 清理定时器和事件监听器
4. 使用WeakMap存储临时数据
5. 合理使用AbortController取消操作`,
    code: `
// ❌ 可能导致内存泄漏的代码
function memoryLeakExample() {
  const largeData = new Array(1000000).fill('data');

  // 永远不会resolve的Promise
  return new Promise((resolve, reject) => {
    // 这个Promise永远pending，持有largeData的引用
    // 导致largeData无法被垃圾回收
  });
}

// ❌ 循环引用导致的内存泄漏
function circularReference() {
  const obj = { data: 'large data' };

  return new Promise((resolve) => {
    obj.promise = new Promise((innerResolve) => {
      // obj引用promise，promise的闭包引用obj
      setTimeout(() => {
        innerResolve(obj);
      }, 1000);
    });

    resolve(obj);
  });
}

// ✅ 正确的内存管理
class PromiseManager {
  constructor() {
    this.activePromises = new Set();
    this.abortController = new AbortController();
  }

  // 创建可管理的Promise
  createManagedPromise(executor) {
    const promise = new Promise((resolve, reject) => {
      const managedResolve = (value) => {
        this.activePromises.delete(promise);
        resolve(value);
      };

      const managedReject = (reason) => {
        this.activePromises.delete(promise);
        reject(reason);
      };

      // 支持取消
      this.abortController.signal.addEventListener('abort', () => {
        managedReject(new Error('操作被取消'));
      });

      executor(managedResolve, managedReject);
    });

    this.activePromises.add(promise);
    return promise;
  }

  // 创建带超时的Promise
  createTimeoutPromise(executor, timeout = 5000) {
    return this.createManagedPromise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('操作超时'));
      }, timeout);

      executor(
        (value) => {
          clearTimeout(timeoutId);
          resolve(value);
        },
        (reason) => {
          clearTimeout(timeoutId);
          reject(reason);
        }
      );
    });
  }

  // 清理所有活跃的Promise
  cleanup() {
    this.abortController.abort();
    this.activePromises.clear();
  }

  // 获取活跃Promise数量
  getActiveCount() {
    return this.activePromises.size;
  }
}

// 使用WeakMap避免内存泄漏
const promiseCache = new WeakMap();

function getCachedPromise(obj) {
  if (promiseCache.has(obj)) {
    return promiseCache.get(obj);
  }

  const promise = new Promise((resolve) => {
    // 处理obj的异步操作
    setTimeout(() => {
      resolve(obj.data);
    }, 1000);
  });

  promiseCache.set(obj, promise);
  return promise;
}

// 正确的资源清理
class ResourceManager {
  constructor() {
    this.resources = [];
    this.cleanupTasks = [];
  }

  async loadResource(url) {
    const controller = new AbortController();
    this.cleanupTasks.push(() => controller.abort());

    try {
      const response = await fetch(url, {
        signal: controller.signal
      });

      const resource = await response.json();
      this.resources.push(resource);

      return resource;
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('资源加载被取消');
      } else {
        console.error('资源加载失败:', error);
      }
      throw error;
    }
  }

  // 清理所有资源
  cleanup() {
    this.cleanupTasks.forEach(task => {
      try {
        task();
      } catch (error) {
        console.warn('清理任务失败:', error);
      }
    });

    this.resources.length = 0;
    this.cleanupTasks.length = 0;
  }
}

// 使用示例
const manager = new PromiseManager();

// 创建管理的Promise
const promise = manager.createTimeoutPromise((resolve) => {
  // 模拟异步操作
  setTimeout(() => {
    resolve('操作完成');
  }, 2000);
}, 3000);

promise
  .then(result => console.log('结果:', result))
  .catch(error => console.error('错误:', error));

// 在适当时机清理
setTimeout(() => {
  manager.cleanup();
  console.log('活跃Promise数量:', manager.getActiveCount());
}, 10000);
    `,
    tags: ['内存泄漏', '资源管理', '性能优化'],
    relatedQuestions: ['Promise性能优化', '垃圾回收机制']
  },

  {
    id: 'promise-testing',
    question: '如何测试Promise相关的代码？',
    answer: `测试Promise代码需要处理异步性和各种状态，主要方法包括：

**1. 使用async/await**
- 最直观的测试方式
- 可以使用try-catch处理错误

**2. 返回Promise**
- 测试框架会等待Promise完成
- 适合简单的异步测试

**3. 使用done回调**
- 传统的异步测试方式
- 需要手动调用done()

**4. 模拟和存根**
- 使用sinon、jest等工具模拟异步操作
- 控制Promise的状态和时机

**5. 测试错误情况**
- 确保错误被正确处理
- 测试各种失败场景`,
    code: `
// 1. 使用async/await测试
describe('Promise测试', () => {
  test('成功情况', async () => {
    const result = await fetchUserData(123);
    expect(result.id).toBe(123);
    expect(result.name).toBeDefined();
  });

  test('错误情况', async () => {
    await expect(fetchUserData(-1)).rejects.toThrow('无效的用户ID');
  });

  test('超时情况', async () => {
    const slowPromise = new Promise(resolve => {
      setTimeout(() => resolve('slow'), 2000);
    });

    await expect(
      Promise.race([
        slowPromise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('超时')), 1000)
        )
      ])
    ).rejects.toThrow('超时');
  });
});

// 2. 返回Promise的测试
test('返回Promise', () => {
  return fetchUserData(123).then(result => {
    expect(result.id).toBe(123);
  });
});

// 3. 使用done回调
test('使用done回调', (done) => {
  fetchUserData(123)
    .then(result => {
      expect(result.id).toBe(123);
      done();
    })
    .catch(done);
});

// 4. 模拟Promise
describe('模拟测试', () => {
  beforeEach(() => {
    // 模拟fetch
    global.fetch = jest.fn();
  });

  test('模拟成功响应', async () => {
    const mockData = { id: 123, name: 'Test User' };

    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockData
    });

    const result = await fetchUserData(123);
    expect(result).toEqual(mockData);
    expect(fetch).toHaveBeenCalledWith('/api/users/123');
  });

  test('模拟网络错误', async () => {
    fetch.mockRejectedValueOnce(new Error('网络错误'));

    await expect(fetchUserData(123)).rejects.toThrow('网络错误');
  });
});

// 5. 测试Promise.all
test('测试Promise.all', async () => {
  const promises = [
    Promise.resolve(1),
    Promise.resolve(2),
    Promise.resolve(3)
  ];

  const results = await Promise.all(promises);
  expect(results).toEqual([1, 2, 3]);
});

test('测试Promise.all失败', async () => {
  const promises = [
    Promise.resolve(1),
    Promise.reject(new Error('失败')),
    Promise.resolve(3)
  ];

  await expect(Promise.all(promises)).rejects.toThrow('失败');
});

// 6. 测试自定义Promise工具
class PromiseUtils {
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  static timeout(promise, ms) {
    return Promise.race([
      promise,
      this.delay(ms).then(() => {
        throw new Error('超时');
      })
    ]);
  }
}

describe('PromiseUtils', () => {
  test('delay方法', async () => {
    const start = Date.now();
    await PromiseUtils.delay(100);
    const elapsed = Date.now() - start;

    expect(elapsed).toBeGreaterThanOrEqual(90);
    expect(elapsed).toBeLessThan(150);
  });

  test('timeout方法 - 成功', async () => {
    const fastPromise = Promise.resolve('快速完成');
    const result = await PromiseUtils.timeout(fastPromise, 1000);

    expect(result).toBe('快速完成');
  });

  test('timeout方法 - 超时', async () => {
    const slowPromise = new Promise(resolve => {
      setTimeout(() => resolve('慢速完成'), 2000);
    });

    await expect(
      PromiseUtils.timeout(slowPromise, 100)
    ).rejects.toThrow('超时');
  });
});

// 7. 测试错误恢复
test('错误恢复机制', async () => {
  const unreliableOperation = jest.fn()
    .mockRejectedValueOnce(new Error('第一次失败'))
    .mockRejectedValueOnce(new Error('第二次失败'))
    .mockResolvedValueOnce('第三次成功');

  async function retryOperation(operation, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await operation();
      } catch (error) {
        if (i === maxRetries - 1) throw error;
        await PromiseUtils.delay(100);
      }
    }
  }

  const result = await retryOperation(unreliableOperation);
  expect(result).toBe('第三次成功');
  expect(unreliableOperation).toHaveBeenCalledTimes(3);
});

// 8. 性能测试
test('Promise性能测试', async () => {
  const start = Date.now();

  // 并行执行1000个Promise
  const promises = Array.from({ length: 1000 }, (_, i) =>
    Promise.resolve(i * 2)
  );

  const results = await Promise.all(promises);
  const elapsed = Date.now() - start;

  expect(results).toHaveLength(1000);
  expect(results[999]).toBe(1998);
  expect(elapsed).toBeLessThan(100); // 应该很快完成
});
    `,
    tags: ['单元测试', '异步测试', '模拟测试'],
    relatedQuestions: ['Jest异步测试', '测试最佳实践']
  }
];

export default commonQuestions;
