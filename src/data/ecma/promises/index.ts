import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const promisesData: ApiItem = {
  id: 'promises',
  title: 'ES6 Promises',
  description: 'ES6 Promise异步编程对象，提供更优雅的异步操作处理方式，支持链式调用和错误处理',
  category: 'ECMA特性',
  difficulty: 'medium',
  
  syntax: `new Promise((resolve, reject) => {}); promise.then().catch().finally();`,
  example: `const promise = new Promise(resolve => resolve('success')); promise.then(result => console.log(result));`,
  notes: 'Promise是异步编程的基础，解决了回调地狱问题，为async/await提供了底层支持',
  
  version: 'ES6 (ES2015)',
  tags: ['ES6', 'JavaScript', '异步编程', 'Promise', '链式调用'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default promisesData;
