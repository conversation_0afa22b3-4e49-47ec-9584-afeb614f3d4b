/**
 * ⚡ Tab 7: 性能优化 (performance-optimization.ts) - 核心指导原则
 *
 * 🚨 重要编码约束（最高优先级）
 *
 * 🛡️ TypeScript接口一致性检查（零容忍错误）
 * 必须严格遵循 PerformanceOptimization 接口定义
 *
 * 🚨 强制检查步骤:
 * 1. 编写前查看 `/src/types/api.ts` 中的 PerformanceOptimization 接口
 * 2. 确保所有字段名称、类型、结构完全匹配
 * 3. 特别注意 performanceMetrics 是对象不是数组
 * 4. 特别注意 commonPitfalls 是对象数组不是字符串数组
 *
 * ❌ 严格禁止的语法
 * 绝对不能使用任何形式的模板字符串插值语法 `${xxx}`
 *
 * 🛡️ 性能优化编码特殊要求
 * - 性能测试: 测试代码中避免模板字符串开销
 * - 基准测试: 基准测试结果输出使用高效方式
 * - 优化代码: 优化示例代码遵循最佳性能实践
 * - 监控代码: 性能监控代码使用高效的实现方式
 *
 * 🎯 Tab定位与价值 - 性能大师版
 *
 * 🎭 **身份定位**：你是一位经历过高并发洗礼的性能优化大师
 * 见过系统在流量洪峰下的生死时刻，深知每一毫秒的珍贵
 *
 * 💡 **核心信念**：真正的性能优化不在于炫技，而在于找到系统的瓶颈点
 * 好的优化策略不是倾倒技巧，而是点亮关键路径
 *
 * 🏗️ **承重墙思维**：帮助开发者识别性能优化中的：
 * - 🏗️ **承重墙优化**：影响系统生死的核心瓶颈（并发控制、内存泄漏）
 * - 🎨 **装饰品优化**：锦上添花但非必需的微调（某些边缘场景优化）
 * - 🚪 **暗门优化**：知道就能事半功倍的性能窍门（Promise池、缓存策略）
 *
 * 🌊 **表达温度**：像一位分享战场经验的老兵：
 * "那次双11，我们的Promise并发没控制好，差点把数据库打垮..."
 *
 * ⚡ **价值序列**：
 * - 实测数据 > 理论分析：能量化的优化，比"应该更快"更重要
 * - 瓶颈突破 > 全面优化：解决关键问题，胜过十个小优化
 * - 生产验证 > 本地测试：经过生产环境验证的策略最可靠
 *
 * 📊 内容结构要求 - 必须包含的4个核心部分：
 * - optimizationStrategies: 🆕 企业级优化策略数组
 *   - strategy: 策略名称
 *   - description: 策略描述
 *   - implementation: 🆕 完整代码实现（包含对比示例：❌错误做法 vs ✅正确做法）
 *   - impact: 🆕 量化影响描述（具体性能提升数据）
 *   - 🆕 已实现策略：并行执行优化、Promise池并发控制、缓存策略、错误重试机制等
 * - performanceMetrics: 🆕 性能指标对象（注意：是对象不是数组）
 *   - [key: string]: 指标对象
 *     - description: 指标描述
 *     - tool: 测量工具
 *     - example: 示例值
 *     - 🆕 已实现：响应时间、吞吐量、内存使用、错误率等关键指标
 * - bestPractices: 🆕 最佳实践字符串数组（基于生产环境经验）
 * - commonPitfalls: 🆕 常见陷阱对象数组（注意：是对象数组不是字符串数组）
 *   - issue: 问题描述
 *   - cause: 原因分析
 *   - solution: 解决方案
 *   - 🆕 已实现：内存泄漏、并发控制、错误处理等生产环境常见问题
 *
 * 🎯 质量标准
 * - 策略系统：提供系统性的优化策略，覆盖不同层面
 * - 技巧实用：优化技巧具有实际应用价值，经过验证
 * - 指标可量化：性能指标可以实际测量和验证
 * - 实践可行：最佳实践在真实项目中可以执行
 * - 陷阱真实：常见陷阱基于真实开发经验
 * - 影响评估：准确评估优化技巧的影响程度和实施难度
 */

import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  optimizationStrategies: [
    {
      strategy: '并行执行优化',
      description: '使用Promise.all并行执行独立的异步操作，减少总执行时间',
      implementation: `// ❌ 串行执行 - 慢
async function fetchDataSerial() {
  const user = await fetchUser();      // 1秒
  const posts = await fetchPosts();    // 1秒  
  const comments = await fetchComments(); // 1秒
  return { user, posts, comments };     // 总计3秒
}

// ✅ 并行执行 - 快
async function fetchDataParallel() {
  const [user, posts, comments] = await Promise.all([
    fetchUser(),      // 并行执行
    fetchPosts(),     // 并行执行
    fetchComments()   // 并行执行
  ]);
  return { user, posts, comments }; // 总计1秒（最慢的那个）
}

// 混合策略：部分依赖的并行执行
async function fetchDataSmart() {
  // 第一批：独立操作并行执行
  const [user, categories] = await Promise.all([
    fetchUser(),
    fetchCategories()
  ]);
  
  // 第二批：依赖第一批结果的操作并行执行
  const [posts, preferences] = await Promise.all([
    fetchUserPosts(user.id),
    fetchUserPreferences(user.id)
  ]);
  
  return { user, categories, posts, preferences };
}`,
      impact: '显著减少异步操作的总执行时间，提高应用响应速度'
    },
    {
      strategy: 'Promise池和并发控制',
      description: '控制并发Promise数量，避免资源耗尽和API限流',
      implementation: `
// Promise并发控制器
class PromiseConcurrencyController {
  constructor(maxConcurrency = 3) {
    this.maxConcurrency = maxConcurrency;
    this.running = 0;
    this.queue = [];
  }

  async add(promiseFactory) {
    return new Promise((resolve, reject) => {
      this.queue.push({
        promiseFactory,
        resolve,
        reject
      });

      this.process();
    });
  }

  async process() {
    if (this.running >= this.maxConcurrency || this.queue.length === 0) {
      return;
    }

    this.running++;
    const { promiseFactory, resolve, reject } = this.queue.shift();

    try {
      const result = await promiseFactory();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.running--;
      this.process(); // 处理队列中的下一个
    }
  }
}

// 使用示例
const controller = new PromiseConcurrencyController(3);

// 批量处理大量异步任务
async function processBatchRequests(urls) {
  const promises = urls.map(url =>
    controller.add(() => fetch(url))
  );

  return Promise.all(promises);
}

// 高级并发控制：支持优先级
class AdvancedPromisePool {
  constructor(maxConcurrency = 3) {
    this.maxConcurrency = maxConcurrency;
    this.running = 0;
    this.highPriorityQueue = [];
    this.normalQueue = [];
  }

  add(promiseFactory, priority = 'normal') {
    const queue = priority === 'high' ? this.highPriorityQueue : this.normalQueue;

    return new Promise((resolve, reject) => {
      queue.push({ promiseFactory, resolve, reject });
      this.process();
    });
  }

  async process() {
    if (this.running >= this.maxConcurrency) return;

    // 优先处理高优先级任务
    const queue = this.highPriorityQueue.length > 0
      ? this.highPriorityQueue
      : this.normalQueue;

    if (queue.length === 0) return;

    this.running++;
    const { promiseFactory, resolve, reject } = queue.shift();

    try {
      const result = await promiseFactory();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.running--;
      this.process();
    }
  }
}
      `,
      impact: '避免过多并发请求导致的性能问题和API限流'
    },

    {
      strategy: 'Promise缓存和记忆化',
      description: '缓存Promise结果，避免重复的异步操作',
      implementation: `
// Promise结果缓存
class PromiseCache {
  constructor(ttl = 5 * 60 * 1000) { // 默认5分钟TTL
    this.cache = new Map();
    this.ttl = ttl;
  }

  async get(key, promiseFactory) {
    const cached = this.cache.get(key);

    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.promise;
    }

    const promise = promiseFactory();
    this.cache.set(key, {
      promise,
      timestamp: Date.now()
    });

    // 清理过期缓存
    promise.finally(() => {
      setTimeout(() => {
        const entry = this.cache.get(key);
        if (entry && Date.now() - entry.timestamp >= this.ttl) {
          this.cache.delete(key);
        }
      }, this.ttl);
    });

    return promise;
  }

  clear() {
    this.cache.clear();
  }
}

// 使用示例
const cache = new PromiseCache(10 * 60 * 1000); // 10分钟缓存

async function fetchUserData(userId) {
  return cache.get(\`user:\${userId}\`, () =>
    fetch(\`/api/users/\${userId}\`).then(r => r.json())
  );
}

// 记忆化装饰器
function memoizePromise(fn, keyGenerator = (...args) => JSON.stringify(args)) {
  const cache = new Map();

  return async function(...args) {
    const key = keyGenerator(...args);

    if (cache.has(key)) {
      return cache.get(key);
    }

    const promise = fn.apply(this, args);
    cache.set(key, promise);

    // 失败时清除缓存
    promise.catch(() => cache.delete(key));

    return promise;
  };
}

// 使用记忆化
const memoizedFetch = memoizePromise(
  async (url) => fetch(url).then(r => r.json()),
  (url) => url // 使用URL作为缓存键
);
      `,
      impact: '减少重复的网络请求和计算，显著提升应用性能'
    },

    {
      strategy: '智能错误重试',
      description: '实现指数退避和智能重试机制，提高异步操作的成功率',
      implementation: `
// 智能重试工具
class SmartRetry {
  constructor(options = {}) {
    this.maxAttempts = options.maxAttempts || 3;
    this.baseDelay = options.baseDelay || 1000;
    this.maxDelay = options.maxDelay || 30000;
    this.backoffFactor = options.backoffFactor || 2;
    this.jitter = options.jitter || true;
  }

  async execute(operation, shouldRetry = this.defaultShouldRetry) {
    let lastError;

    for (let attempt = 1; attempt <= this.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        if (attempt === this.maxAttempts || !shouldRetry(error, attempt)) {
          throw error;
        }

        const delay = this.calculateDelay(attempt);
        console.warn(\`重试第 \${attempt} 次失败，\${delay}ms后重试:\`, error.message);

        await this.delay(delay);
      }
    }

    throw lastError;
  }

  calculateDelay(attempt) {
    let delay = this.baseDelay * Math.pow(this.backoffFactor, attempt - 1);
    delay = Math.min(delay, this.maxDelay);

    // 添加随机抖动避免雷群效应
    if (this.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }

    return Math.floor(delay);
  }

  defaultShouldRetry(error, attempt) {
    // 网络错误或5xx服务器错误才重试
    return error.name === 'TypeError' ||
           (error.status >= 500 && error.status < 600);
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 使用示例
const retry = new SmartRetry({
  maxAttempts: 3,
  baseDelay: 1000,
  backoffFactor: 2
});

async function reliableFetch(url) {
  return retry.execute(async () => {
    const response = await fetch(url);

    if (!response.ok) {
      const error = new Error(\`HTTP \${response.status}\`);
      error.status = response.status;
      throw error;
    }

    return response.json();
  });
}

// 批量重试
class BatchRetry {
  constructor(retryOptions = {}) {
    this.retry = new SmartRetry(retryOptions);
  }

  async executeAll(operations) {
    const results = await Promise.allSettled(
      operations.map(op => this.retry.execute(op))
    );

    const successful = results
      .filter(r => r.status === 'fulfilled')
      .map(r => r.value);

    const failed = results
      .filter(r => r.status === 'rejected')
      .map(r => r.reason);

    return { successful, failed };
  }
}
      `,
      impact: '提高异步操作的可靠性，减少因临时错误导致的失败'
    }
  ],
  
  benchmarks: [
    {
      scenario: '并行vs串行执行性能对比',
      description: '对比串行和并行执行多个异步操作的性能',
      metrics: {
        '串行执行时间': '3000ms',
        '并行执行时间': '1000ms'
      },
      conclusion: '并行执行可以显著提高异步操作的整体性能'
    }
  ],

  bestPractices: [
    {
      practice: '避免不必要的Promise包装',
      description: '直接返回Promise而不是用new Promise包装',
      example: '// ✅ return fetch(url); // ❌ return new Promise(resolve => fetch(url).then(resolve));'
    },
    {
      practice: '使用Promise.allSettled处理部分失败',
      description: '当部分操作失败不影响整体时使用allSettled',
      example: 'const results = await Promise.allSettled(promises);'
    }
  ]
};

export default performanceOptimization;
