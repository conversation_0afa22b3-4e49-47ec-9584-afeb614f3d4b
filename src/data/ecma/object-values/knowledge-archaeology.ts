import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: `Object.values()的历史反映了JavaScript社区对完整对象操作API的需求。作为Object.keys()的补充，它完善了对象属性访问的工具集，体现了API设计的一致性和完整性原则。`,
  
  background: `在Object.values()出现之前，开发者需要结合Object.keys()和属性访问来获取对象的值，这种方式不够直观且容易出错。`,

  evolution: `Object.values()的引入与Object.entries()一起，完善了JavaScript的对象操作API，为函数式编程提供了更好的支持。`,

  timeline: [
    {
      year: '2017',
      event: 'ES2017标准化',
      description: 'ECMAScript 2017正式引入Object.values()方法',
      significance: '完善了对象属性访问的API工具集'
    }
  ],

  keyFigures: [
    {
      name: 'TC39委员会',
      role: 'ECMAScript标准制定者',
      contribution: '设计和标准化Object.values()方法',
      significance: '推动了JavaScript对象操作API的完善'
    }
  ],

  concepts: [
    {
      term: '对象值提取',
      definition: '获取对象所有属性值的编程模式',
      evolution: '从手动遍历发展为专门的静态方法',
      modernRelevance: '现代JavaScript数据处理的基础操作'
    }
  ],

  designPhilosophy: `Object.values()体现了"API完整性"的设计哲学，与Object.keys()和Object.entries()形成完整的对象操作工具集。`,

  impact: `Object.values()的引入简化了对象值的提取和处理，促进了函数式编程在JavaScript中的应用。`,

  modernRelevance: `在现代JavaScript开发中，Object.values()已成为数据处理和统计分析的重要工具。`
};

export default knowledgeArchaeology;
