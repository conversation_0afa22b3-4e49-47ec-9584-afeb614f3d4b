import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `Object.values()是ES2017引入的静态方法，用于提取对象的所有可枚举属性值，返回一个包含这些值的数组。与Object.keys()提取键不同，Object.values()专注于提取值，为函数式编程和数据处理提供了便利。它只处理对象的可枚举自有属性，忽略继承属性和Symbol属性，与Object.keys()和Object.entries()保持一致的行为。`,

  syntax: `// 基本语法
Object.values(obj)

// 参数
obj: 要提取值的对象

// 返回值
Array<any> - 包含所有可枚举属性值的数组

// 使用示例
const obj = { a: 1, b: 2, c: 3 };
const values = Object.values(obj); // [1, 2, 3]

// 与数组方法结合
Object.values(obj)
  .filter(value => value > 1)
  .map(value => value * 2)
  .reduce((sum, value) => sum + value, 0);`,

  quickExample: `// 基础示例
const user = {
  name: 'John',
  age: 30,
  city: 'New York'
};

const values = Object.values(user);
console.log(values); // ['John', 30, 'New York']

// 数值计算
const scores = { math: 95, english: 88, science: 92 };
const average = Object.values(scores)
  .reduce((sum, score) => sum + score, 0) / Object.values(scores).length;
console.log(average); // 91.67

// 数据验证
const formData = {
  username: 'john_doe',
  email: '<EMAIL>',
  password: 'secret123'
};

const hasEmptyFields = Object.values(formData)
  .some(value => !value || value.trim() === '');
console.log(hasEmptyFields); // false

// 类型统计
const data = {
  name: 'Product',
  price: 99.99,
  inStock: true,
  category: 'Electronics',
  rating: 4.5
};

const typeCount = Object.values(data)
  .reduce((acc, value) => {
    const type = typeof value;
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});
console.log(typeCount); // { string: 2, number: 2, boolean: 1 }

// 数组扁平化
const nested = {
  fruits: ['apple', 'banana'],
  vegetables: ['carrot', 'broccoli'],
  grains: ['rice', 'wheat']
};

const allItems = Object.values(nested).flat();
console.log(allItems); // ['apple', 'banana', 'carrot', 'broccoli', 'rice', 'wheat']

// 最值查找
const temperatures = {
  monday: 22,
  tuesday: 25,
  wednesday: 19,
  thursday: 28,
  friday: 24
};

const maxTemp = Math.max(...Object.values(temperatures));
const minTemp = Math.min(...Object.values(temperatures));
console.log(\`Max: \${maxTemp}°C, Min: \${minTemp}°C\`); // Max: 28°C, Min: 19°C`,

  coreFeatures: [
    {
      feature: "值提取",
      description: "提取对象的所有可枚举属性值",
      importance: "high" as const,
      details: "返回包含所有值的数组，便于进一步处理"
    },
    {
      feature: "可枚举属性",
      description: "只处理可枚举的自有属性",
      importance: "medium" as const,
      details: "与Object.keys()行为一致"
    },
    {
      feature: "数组方法兼容",
      description: "返回数组，可使用所有数组方法",
      importance: "high" as const,
      details: "支持map、filter、reduce等函数式编程"
    },
    {
      feature: "类型保持",
      description: "保持原始值的类型不变",
      importance: "medium" as const,
      details: "不进行类型转换，保持数据完整性"
    }
  ],

  keyFeatures: [
    {
      feature: "函数式编程",
      description: "支持函数式编程范式",
      importance: "high" as const,
      details: "与数组方法完美配合"
    },
    {
      feature: "数据聚合",
      description: "便于数据统计和聚合操作",
      importance: "high" as const,
      details: "适用于求和、平均值、最值等计算"
    },
    {
      feature: "类型检查",
      description: "便于批量类型检查和验证",
      importance: "medium" as const,
      details: "可以快速检查所有值的类型或有效性"
    }
  ],

  limitations: [
    "只处理可枚举的自有属性",
    "不包括Symbol属性",
    "不包括原型链上的属性",
    "对于大对象可能有性能影响",
    "返回的数组顺序依赖于属性定义顺序"
  ],

  bestPractices: [
    "用于数值计算和统计分析",
    "结合数组方法进行数据处理",
    "用于表单验证和数据检查",
    "与Object.keys()配合进行对象操作",
    "避免在性能敏感的代码中频繁调用"
  ],

  warnings: [
    "只处理可枚举属性，注意属性描述符",
    "大对象处理可能影响性能",
    "返回的是浅拷贝，修改对象值会影响原对象"
  ]
};

export default basicInfo;
