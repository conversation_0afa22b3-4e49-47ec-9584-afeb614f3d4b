import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const objectValuesData: ApiItem = {
  id: 'object-values',
  title: 'Object.values()',
  description: 'ES2017对象值方法，提取对象的所有可枚举属性值，返回值数组',
  category: 'ECMA特性',
  difficulty: 'easy',
  
  syntax: `Object.values(obj); // 返回值数组`,
  example: `const obj = { a: 1, b: 2 }; Object.values(obj); // [1, 2]`,
  notes: 'Object.values()是ES2017引入的静态方法，提供了提取对象值的标准方式',
  
  version: 'ES2017',
  tags: ['ES2017', 'JavaScript', 'Object.values', '对象值', '数组转换'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default objectValuesData;
