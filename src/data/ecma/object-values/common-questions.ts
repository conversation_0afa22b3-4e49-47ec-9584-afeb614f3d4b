import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: 'Object.values()会包含undefined值吗？',
    answer: '会的。Object.values()会包含所有可枚举属性的值，包括undefined。如果对象的某个属性值是undefined，它会出现在返回的数组中。这与属性是否存在不同，只要属性存在且可枚举，其值就会被包含，无论值是什么。',
    code: `// undefined值的处理
const obj = {
  a: 1,
  b: undefined,
  c: null,
  d: 0,
  e: '',
  f: false
};

const values = Object.values(obj);
console.log('Values:', values);
// [1, undefined, null, 0, '', false]

// 过滤undefined值
const definedValues = Object.values(obj)
  .filter(value => value !== undefined);
console.log('Defined values:', definedValues);
// [1, null, 0, '', false]

// 过滤所有falsy值
const truthyValues = Object.values(obj)
  .filter(Boolean);
console.log('Truthy values:', truthyValues);
// [1]

// 检查是否包含undefined
const hasUndefined = Object.values(obj)
  .includes(undefined);
console.log('Has undefined:', hasUndefined); // true

// 实际应用：清理数据
function cleanObjectValues(obj) {
  return Object.fromEntries(
    Object.entries(obj)
      .filter(([key, value]) => value !== undefined)
  );
}

const cleaned = cleanObjectValues(obj);
console.log('Cleaned object:', cleaned);
// { a: 1, c: null, d: 0, e: '', f: false }`,
    tags: ['undefined值', '数据清理', '过滤'],
    relatedQuestions: ['falsy值处理', '数据验证']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: 'Object.values()的性能如何？什么时候需要考虑性能？',
    answer: 'Object.values()的性能是O(n)，其中n是对象的属性数量。对于小到中等大小的对象，性能通常不是问题。但对于大对象（数千个属性）或频繁调用的场景，需要考虑性能影响。可以通过缓存结果、分批处理或使用其他遍历方法来优化。',
    code: `// 性能测试
function performanceTest() {
  // 创建大对象
  const largeObject = {};
  for (let i = 0; i < 100000; i++) {
    largeObject[\`key\${i}\`] = i;
  }
  
  // 测试Object.values()性能
  console.time('Object.values()');
  const values = Object.values(largeObject);
  console.timeEnd('Object.values()');
  
  // 测试for...in性能
  console.time('for...in');
  const values2 = [];
  for (const key in largeObject) {
    if (largeObject.hasOwnProperty(key)) {
      values2.push(largeObject[key]);
    }
  }
  console.timeEnd('for...in');
  
  console.log('Results equal:', values.length === values2.length);
}

// 性能优化策略
class OptimizedObjectProcessor {
  constructor() {
    this.cache = new Map();
  }
  
  // 缓存Object.values()结果
  getValues(obj, cacheKey) {
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    const values = Object.values(obj);
    this.cache.set(cacheKey, values);
    return values;
  }
  
  // 分批处理大对象
  *processValuesInBatches(obj, batchSize = 1000) {
    const values = Object.values(obj);
    
    for (let i = 0; i < values.length; i += batchSize) {
      yield values.slice(i, i + batchSize);
    }
  }
  
  // 条件处理，避免不必要的Object.values()调用
  processIfNeeded(obj, condition) {
    if (!condition(obj)) {
      return [];
    }
    
    return Object.values(obj);
  }
}

// 使用示例
const processor = new OptimizedObjectProcessor();

// 大对象分批处理
const hugeObject = {};
for (let i = 0; i < 50000; i++) {
  hugeObject[\`item\${i}\`] = { id: i, value: Math.random() };
}

console.time('Batch processing');
let processedCount = 0;
for (const batch of processor.processValuesInBatches(hugeObject, 5000)) {
  // 处理每个批次
  processedCount += batch.length;
}
console.timeEnd('Batch processing');
console.log('Processed items:', processedCount);

// 性能最佳实践
function efficientDataProcessing(data) {
  // ❌ 避免：重复调用Object.values()
  // const sum = Object.values(data).reduce((a, b) => a + b, 0);
  // const avg = Object.values(data).reduce((a, b) => a + b, 0) / Object.values(data).length;
  
  // ✅ 推荐：缓存结果
  const values = Object.values(data);
  const sum = values.reduce((a, b) => a + b, 0);
  const avg = sum / values.length;
  
  return { sum, avg, count: values.length };
}`,
    tags: ['性能优化', '大对象处理', '缓存策略'],
    relatedQuestions: ['性能测试', '内存管理', '批处理']
  }
];

export default commonQuestions;
