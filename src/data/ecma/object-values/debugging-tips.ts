import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: 'Object.values()使用中的常见错误和解决方案',
        sections: [
          {
            title: '类型和参数错误',
            description: 'Object.values()参数相关的常见问题',
            items: [
              {
                title: 'TypeError: Object.values called on null or undefined',
                description: '对null或undefined调用Object.values()',
                solution: '在调用前检查参数是否为有效对象',
                prevention: '使用可选链或默认值处理',
                code: `// ❌ 错误：对null/undefined调用Object.values()
const data = null;
// Object.values(data); // TypeError

// ✅ 正确：添加安全检查
function safeValues(obj) {
  if (obj === null || obj === undefined) {
    return [];
  }
  return Object.values(obj);
}

// ✅ 使用默认值
const values = Object.values(data ?? {});

// 调试辅助函数
function debugValues(obj, label = 'object') {
  console.log(\`Debugging \${label} values:\`);
  console.log('Type:', typeof obj);
  console.log('Value:', obj);
  
  try {
    const values = Object.values(obj);
    console.log('Values:', values);
    console.log('Values count:', values.length);
    return values;
  } catch (error) {
    console.error('Error getting values:', error.message);
    return [];
  }
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '调试Object.values()相关问题的工具和技巧',
        sections: [
          {
            title: 'Object.values()分析工具',
            description: '分析Object.values()返回结果的工具函数',
            items: [
              {
                title: '值类型分析器',
                description: '分析Object.values()返回的值的类型分布',
                solution: '使用专门的分析函数了解数据结构',
                prevention: '在处理复杂数据前先分析值的特征',
                code: `// 值类型分析器
function analyzeObjectValues(obj) {
  console.log('=== Object Values Analysis ===');
  
  const values = Object.values(obj);
  console.log('Total values:', values.length);
  
  // 类型统计
  const typeStats = values.reduce((stats, value) => {
    const type = value === null ? 'null' : typeof value;
    stats[type] = (stats[type] || 0) + 1;
    return stats;
  }, {});
  
  console.log('Type distribution:', typeStats);
  
  // 值统计
  const valueStats = {
    undefined: values.filter(v => v === undefined).length,
    null: values.filter(v => v === null).length,
    empty: values.filter(v => v === '').length,
    zero: values.filter(v => v === 0).length,
    false: values.filter(v => v === false).length,
    truthy: values.filter(Boolean).length,
    falsy: values.filter(v => !v).length
  };
  
  console.log('Value statistics:', valueStats);
  
  // 数值统计（如果有数值）
  const numbers = values.filter(v => typeof v === 'number' && !isNaN(v));
  if (numbers.length > 0) {
    const numberStats = {
      count: numbers.length,
      sum: numbers.reduce((a, b) => a + b, 0),
      avg: numbers.reduce((a, b) => a + b, 0) / numbers.length,
      min: Math.min(...numbers),
      max: Math.max(...numbers)
    };
    console.log('Number statistics:', numberStats);
  }
  
  return { values, typeStats, valueStats };
}

// 使用示例
const testData = {
  name: 'John',
  age: 30,
  active: true,
  score: null,
  description: '',
  count: 0,
  flag: false,
  data: undefined,
  items: [1, 2, 3]
};

analyzeObjectValues(testData);`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
