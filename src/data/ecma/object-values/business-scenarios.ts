import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'data-analytics',
    title: '数据分析和统计',
    description: '使用Object.values()进行数据分析、统计计算和报表生成',
    businessValue: '简化数据分析流程，提供快速的统计计算能力',
    scenario: '电商平台需要分析销售数据，包括总销售额、平均订单价值、最高销售额等统计指标。',
    code: `// 销售数据分析器
class SalesAnalytics {
  constructor(salesData) {
    this.salesData = salesData;
  }
  
  // 计算总销售额
  getTotalSales() {
    return Object.values(this.salesData)
      .reduce((total, amount) => total + amount, 0);
  }
  
  // 计算平均销售额
  getAverageSales() {
    const values = Object.values(this.salesData);
    return values.reduce((sum, amount) => sum + amount, 0) / values.length;
  }
  
  // 获取最高和最低销售额
  getSalesRange() {
    const values = Object.values(this.salesData);
    return {
      max: Math.max(...values),
      min: Math.min(...values)
    };
  }
  
  // 销售额分布分析
  getSalesDistribution() {
    const values = Object.values(this.salesData);
    const ranges = {
      low: values.filter(v => v < 1000).length,
      medium: values.filter(v => v >= 1000 && v < 5000).length,
      high: values.filter(v => v >= 5000).length
    };
    
    const total = values.length;
    return {
      low: { count: ranges.low, percentage: (ranges.low / total * 100).toFixed(2) },
      medium: { count: ranges.medium, percentage: (ranges.medium / total * 100).toFixed(2) },
      high: { count: ranges.high, percentage: (ranges.high / total * 100).toFixed(2) }
    };
  }
}

// 使用示例
const monthlySales = {
  january: 15000,
  february: 18000,
  march: 22000,
  april: 19000,
  may: 25000,
  june: 21000
};

const analytics = new SalesAnalytics(monthlySales);
console.log('总销售额:', analytics.getTotalSales());
console.log('平均销售额:', analytics.getAverageSales());
console.log('销售范围:', analytics.getSalesRange());`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'form-validation',
    title: '表单验证系统',
    description: '使用Object.values()构建高效的表单验证系统',
    businessValue: '提供统一的表单验证接口，简化验证逻辑',
    scenario: '用户注册表单需要验证多个字段，包括必填检查、格式验证、长度限制等。',
    code: `// 表单验证器
class FormValidator {
  constructor(rules) {
    this.rules = rules;
  }
  
  // 验证所有字段
  validateAll(formData) {
    const errors = {};
    
    // 检查所有必填字段
    const requiredFields = Object.keys(this.rules)
      .filter(field => this.rules[field].required);
    
    const missingFields = requiredFields
      .filter(field => !formData[field] || formData[field].trim() === '');
    
    if (missingFields.length > 0) {
      missingFields.forEach(field => {
        errors[field] = 'This field is required';
      });
    }
    
    // 检查所有值是否有效
    const hasInvalidValues = Object.values(formData)
      .some(value => typeof value === 'string' && value.includes('<script>'));
    
    if (hasInvalidValues) {
      errors.security = 'Invalid characters detected';
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
  
  // 检查数据完整性
  checkCompleteness(formData) {
    const totalFields = Object.keys(this.rules).length;
    const filledFields = Object.values(formData)
      .filter(value => value !== null && value !== undefined && value !== '').length;
    
    return {
      completeness: (filledFields / totalFields * 100).toFixed(2),
      filledFields,
      totalFields
    };
  }
}

// 使用示例
const validationRules = {
  username: { required: true, minLength: 3 },
  email: { required: true, pattern: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/ },
  password: { required: true, minLength: 8 },
  confirmPassword: { required: true },
  phone: { required: false }
};

const validator = new FormValidator(validationRules);
const formData = {
  username: 'john_doe',
  email: '<EMAIL>',
  password: 'password123',
  confirmPassword: 'password123',
  phone: ''
};

const validation = validator.validateAll(formData);
const completeness = validator.checkCompleteness(formData);
console.log('Validation:', validation);
console.log('Completeness:', completeness);`
  }
];

export default businessScenarios;
