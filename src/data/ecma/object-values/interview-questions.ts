import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'Object.values()与Object.keys()有什么区别和联系？',
    answer: {
      brief: 'Object.keys()返回属性名数组，Object.values()返回属性值数组，两者都只处理可枚举的自有属性且顺序一致',
      detailed: `Object.values()与Object.keys()的区别和联系：

**主要区别**：
- Object.keys()返回属性名数组
- Object.values()返回属性值数组

**共同点**：
- 都只处理可枚举的自有属性
- 都按相同的顺序返回结果
- 都不包含Symbol属性和继承属性

**使用场景**：
- Object.keys()用于获取属性名，进行属性操作
- Object.values()用于获取属性值，进行数值计算`
    },
   
    difficulty: 'easy',
    frequency: 'high',
    category: '方法对比',
    tags: ['Object.values', 'Object.keys', '对象方法'],
    
    code: `// 对比示例
const user = {
  name: '<PERSON>',
  age: 30,
  city: 'New York'
};

// Object.keys() - 获取属性名
const keys = Object.keys(user);
console.log('Keys:', keys); // ['name', 'age', 'city']

// Object.values() - 获取属性值
const values = Object.values(user);
console.log('Values:', values); // ['John', 30, 'New York']

// 顺序一致性
console.log('Keys and values match order:', 
  keys.every((key, index) => user[key] === values[index])
); // true

// 实际应用对比
const scores = { math: 95, english: 88, science: 92 };

// 使用Object.keys()进行属性操作
const subjects = Object.keys(scores);
console.log('Subjects:', subjects); // ['math', 'english', 'science']

// 使用Object.values()进行数值计算
const allScores = Object.values(scores);
const average = allScores.reduce((sum, score) => sum + score, 0) / allScores.length;
console.log('Average score:', average); // 91.67

// 组合使用
const report = Object.keys(scores).map(subject => ({
  subject,
  score: scores[subject],
  grade: scores[subject] >= 90 ? 'A' : scores[subject] >= 80 ? 'B' : 'C'
}));
console.log('Report:', report);

// 性能对比
const largeObject = {};
for (let i = 0; i < 10000; i++) {
  largeObject[\`key\${i}\`] = i;
}

console.time('Object.keys()');
const largeKeys = Object.keys(largeObject);
console.timeEnd('Object.keys()');

console.time('Object.values()');
const largeValues = Object.values(largeObject);
console.timeEnd('Object.values()');

// 通常性能相近，都是O(n)复杂度`,
    
    followUp: [
      'Object.entries()如何结合两者？',
      '什么时候使用哪个方法？',
      '性能考虑有哪些？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'Object.values()在数据处理中有哪些常见应用？',
    answer: {
      brief: 'Object.values()常用于数值计算（求和、平均值）、数据验证（类型检查、有效性验证）和数据转换（配合数组方法处理）',
      detailed: `Object.values()在数据处理中的常见应用：

**数值计算**：
- 求和、平均值、最值计算
- 统计分析和数据聚合

**数据验证**：
- 检查所有值的有效性
- 批量类型检查

**数据转换**：
- 配合数组方法进行数据处理
- 扁平化和重组数据结构`
    },
   
    difficulty: 'medium',
    frequency: 'high',
    category: '实际应用',
    tags: ['Object.values', '数据处理', '函数式编程'],
    
    code: `// 1. 数值计算应用
const salesData = {
  january: 15000,
  february: 18000,
  march: 22000,
  april: 19000
};

// 总销售额
const totalSales = Object.values(salesData)
  .reduce((sum, amount) => sum + amount, 0);
console.log('Total sales:', totalSales); // 74000

// 平均销售额
const averageSales = Object.values(salesData)
  .reduce((sum, amount) => sum + amount, 0) / Object.values(salesData).length;
console.log('Average sales:', averageSales); // 18500

// 最高和最低销售额
const maxSales = Math.max(...Object.values(salesData));
const minSales = Math.min(...Object.values(salesData));
console.log(\`Range: \${minSales} - \${maxSales}\`); // Range: 15000 - 22000

// 2. 数据验证应用
const formData = {
  username: 'john_doe',
  email: '<EMAIL>',
  password: 'secret123',
  confirmPassword: 'secret123'
};

// 检查是否有空值
const hasEmptyFields = Object.values(formData)
  .some(value => !value || value.trim() === '');
console.log('Has empty fields:', hasEmptyFields); // false

// 检查所有值的类型
const allStrings = Object.values(formData)
  .every(value => typeof value === 'string');
console.log('All values are strings:', allStrings); // true

// 检查最小长度
const allValidLength = Object.values(formData)
  .every(value => value.length >= 3);
console.log('All values have valid length:', allValidLength); // true

// 3. 数据转换应用
const inventory = {
  apples: ['red', 'green', 'yellow'],
  oranges: ['navel', 'blood'],
  bananas: ['cavendish', 'plantain', 'red']
};

// 扁平化所有水果品种
const allVarieties = Object.values(inventory).flat();
console.log('All varieties:', allVarieties);
// ['red', 'green', 'yellow', 'navel', 'blood', 'cavendish', 'plantain', 'red']

// 统计总品种数
const totalVarieties = Object.values(inventory)
  .reduce((total, varieties) => total + varieties.length, 0);
console.log('Total varieties:', totalVarieties); // 8

// 4. 复杂数据处理
const employees = {
  john: { salary: 50000, department: 'IT', experience: 3 },
  jane: { salary: 60000, department: 'Marketing', experience: 5 },
  bob: { salary: 55000, department: 'IT', experience: 4 }
};

// 计算平均薪资
const averageSalary = Object.values(employees)
  .map(emp => emp.salary)
  .reduce((sum, salary) => sum + salary, 0) / Object.values(employees).length;
console.log('Average salary:', averageSalary); // 55000

// 按部门分组
const departmentGroups = Object.values(employees)
  .reduce((groups, emp) => {
    const dept = emp.department;
    if (!groups[dept]) groups[dept] = [];
    groups[dept].push(emp);
    return groups;
  }, {});
console.log('Department groups:', departmentGroups);

// 查找高薪员工
const highEarners = Object.values(employees)
  .filter(emp => emp.salary > 55000);
console.log('High earners:', highEarners);

// 5. 配置和设置处理
const config = {
  maxRetries: 3,
  timeout: 5000,
  enableLogging: true,
  apiKey: 'abc123',
  debugMode: false
};

// 检查配置完整性
const hasAllSettings = Object.values(config)
  .every(value => value !== null && value !== undefined);
console.log('Configuration complete:', hasAllSettings); // true

// 统计配置类型
const configTypes = Object.values(config)
  .reduce((types, value) => {
    const type = typeof value;
    types[type] = (types[type] || 0) + 1;
    return types;
  }, {});
console.log('Config types:', configTypes); // { number: 2, boolean: 2, string: 1 }`,
    
    followUp: [
      '如何优化大数据集的处理？',
      '与其他数组方法的最佳组合？',
      '内存使用的注意事项？'
    ]
  }
];

export default interviewQuestions;
