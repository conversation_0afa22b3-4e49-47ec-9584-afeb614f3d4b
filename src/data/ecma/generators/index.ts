import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const generatorsData: ApiItem = {
  id: 'generators',
  title: 'ES6 Generators',
  description: 'ES6生成器函数，提供可暂停和恢复的函数执行，支持yield关键字和迭代器模式',
  category: 'ECMA特性',
  difficulty: 'medium',
  
  syntax: `function* generatorFunction() { yield value; } const gen = generatorFunction(); gen.next();`,
  example: `function* count() { yield 1; yield 2; yield 3; } for (const num of count()) { console.log(num); }`,
  notes: '生成器函数是ES6引入的特殊函数，可以暂停和恢复执行，是实现迭代器的简洁方式',
  
  version: 'ES6 (ES2015)',
  tags: ['ES6', 'JavaScript', '生成器', 'yield', '迭代器'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default generatorsData;
