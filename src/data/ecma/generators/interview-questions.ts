import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: '生成器函数和普通函数有什么区别？',
    answer: `生成器函数和普通函数的主要区别：

**语法差异**：
- 生成器函数使用function*语法
- 包含yield关键字
- 不能使用箭头函数语法

**执行方式**：
- 普通函数一次性执行完成
- 生成器函数可以暂停和恢复执行
- 生成器函数返回生成器对象

**状态管理**：
- 普通函数执行完成后销毁上下文
- 生成器保持执行状态直到完成

**返回值**：
- 普通函数返回单个值
- 生成器可以产生多个值序列`,
   
    difficulty: 'medium',
    frequency: 'high',
    category: '基础概念',
    tags: ['生成器函数', '函数对比', 'yield'],
    
    code: `// 普通函数
function normalFunction() {
  console.log('开始执行');
  console.log('继续执行');
  console.log('执行完成');
  return '结果';
}

const result = normalFunction(); // 一次性执行完成
console.log(result); // '结果'

// 生成器函数
function* generatorFunction() {
  console.log('开始执行');
  yield '第一个值';
  console.log('继续执行');
  yield '第二个值';
  console.log('执行完成');
  return '最终结果';
}

const gen = generatorFunction(); // 返回生成器对象，不立即执行
console.log(gen.next()); // { value: '第一个值', done: false }
console.log(gen.next()); // { value: '第二个值', done: false }
console.log(gen.next()); // { value: '最终结果', done: true }

// 状态保持示例
function* counter() {
  let count = 0;
  while (true) {
    const increment = yield count;
    count += increment || 1;
  }
}

const counterGen = counter();
console.log(counterGen.next());    // { value: 0, done: false }
console.log(counterGen.next(5));   // { value: 5, done: false }
console.log(counterGen.next(3));   // { value: 8, done: false }

// 普通函数无法实现类似的状态保持
function normalCounter() {
  let count = 0; // 每次调用都重新初始化
  return count++;
}

console.log(normalCounter()); // 0
console.log(normalCounter()); // 0 (无法保持状态)`,
    
    followUp: [
      '生成器的性能特点？',
      'yield*的作用？',
      '异步生成器如何工作？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'yield和yield*有什么区别？',
    answer: `yield和yield*的主要区别：

**yield**：
- 产生单个值
- 暂停生成器执行
- 可以接收外部传入的值

**yield***：
- 委托给另一个生成器或可迭代对象
- 将控制权转移给被委托的对象
- 自动处理被委托对象的所有值

**使用场景**：
- yield用于产生单个值
- yield*用于组合多个生成器或展开可迭代对象`,
   
    difficulty: 'medium',
    frequency: 'medium',
    category: 'yield语法',
    tags: ['yield', 'yield*', '生成器委托'],
    
    code: `// yield - 产生单个值
function* simpleGenerator() {
  yield 1;
  yield 2;
  yield 3;
}

// yield* - 委托给其他生成器
function* innerGenerator() {
  yield 'a';
  yield 'b';
}

function* outerGenerator() {
  yield 'start';
  yield* innerGenerator(); // 委托
  yield 'end';
}

for (const value of outerGenerator()) {
  console.log(value); // 'start', 'a', 'b', 'end'
}

// yield* 与数组
function* arrayDelegation() {
  yield* [1, 2, 3]; // 委托给数组
  yield* 'hello';   // 委托给字符串
}

console.log([...arrayDelegation()]); // [1, 2, 3, 'h', 'e', 'l', 'l', 'o']

// 对比：不使用yield*
function* withoutDelegation() {
  yield 'start';
  
  // 手动遍历
  for (const value of innerGenerator()) {
    yield value;
  }
  
  yield 'end';
}

// yield*的双向通信
function* delegatingGenerator() {
  const result = yield* innerCommunicator();
  yield \`最终结果: \${result}\`;
}

function* innerCommunicator() {
  const input = yield '请输入值';
  return \`处理了: \${input}\`;
}

const delGen = delegatingGenerator();
console.log(delGen.next());        // { value: '请输入值', done: false }
console.log(delGen.next('测试'));  // { value: '最终结果: 处理了: 测试', done: false }

// yield*的错误传播
function* errorPropagation() {
  try {
    yield* errorGenerator();
  } catch (error) {
    yield \`捕获错误: \${error.message}\`;
  }
}

function* errorGenerator() {
  yield '正常值';
  throw new Error('生成器错误');
}

const errorGen = errorPropagation();
console.log(errorGen.next());                    // { value: '正常值', done: false }
console.log(errorGen.next());                    // { value: '捕获错误: 生成器错误', done: false }`,
    
    followUp: [
      'yield*如何处理返回值？',
      '生成器的错误处理机制？',
      '如何实现生成器的组合模式？'
    ]
  }
];

export default interviewQuestions;
