import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '生成器如何实现无限序列？',
    answer: '生成器通过惰性求值可以轻松实现无限序列。由于生成器只在需要时计算下一个值，不会预先计算所有值，因此可以表示理论上无限的数据序列。常见的应用包括斐波那契数列、素数序列、随机数生成等。',
    code: `// 无限斐波那契数列
function* fibonacci() {
  let a = 0, b = 1;
  while (true) {
    yield a;
    [a, b] = [b, a + b];
  }
}

const fib = fibonacci();
console.log(fib.next().value); // 0
console.log(fib.next().value); // 1
console.log(fib.next().value); // 1
console.log(fib.next().value); // 2

// 取前10个斐波那契数
function* take(generator, count) {
  let taken = 0;
  for (const value of generator) {
    if (taken >= count) break;
    yield value;
    taken++;
  }
}

const first10Fib = [...take(fibonacci(), 10)];
console.log(first10Fib); // [0, 1, 1, 2, 3, 5, 8, 13, 21, 34]

// 无限素数序列
function* primes() {
  const primeList = [];
  let candidate = 2;
  
  while (true) {
    if (isPrime(candidate, primeList)) {
      primeList.push(candidate);
      yield candidate;
    }
    candidate++;
  }
}

function isPrime(num, knownPrimes) {
  for (const prime of knownPrimes) {
    if (prime * prime > num) break;
    if (num % prime === 0) return false;
  }
  return true;
}

// 无限随机数序列
function* randomNumbers(min = 0, max = 1) {
  while (true) {
    yield Math.random() * (max - min) + min;
  }
}

const random = randomNumbers(1, 100);
console.log(random.next().value); // 随机数
console.log(random.next().value); // 另一个随机数`,
    tags: ['无限序列', '惰性求值', '斐波那契'],
    relatedQuestions: ['惰性求值原理', '生成器性能']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '如何在生成器中处理异步操作？',
    answer: '生成器可以通过异步生成器（async function*）处理异步操作，也可以在普通生成器中yield Promise对象。异步生成器支持await关键字，可以与for await...of循环配合使用，非常适合处理异步数据流。',
    code: `// 异步生成器
async function* asyncDataStream() {
  for (let i = 1; i <= 5; i++) {
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 1000));
    yield \`数据 \${i}\`;
  }
}

// 使用异步生成器
async function consumeAsyncStream() {
  for await (const data of asyncDataStream()) {
    console.log(data); // 每秒输出一个数据
  }
}

// 普通生成器yield Promise
function* promiseGenerator() {
  yield fetch('/api/user/1').then(r => r.json());
  yield fetch('/api/user/2').then(r => r.json());
  yield fetch('/api/user/3').then(r => r.json());
}

// 处理Promise生成器
async function handlePromiseGenerator() {
  for (const promise of promiseGenerator()) {
    const result = await promise;
    console.log(result);
  }
}

// 异步数据管道
async function* fetchUserData(userIds) {
  for (const id of userIds) {
    try {
      const response = await fetch(\`/api/users/\${id}\`);
      const userData = await response.json();
      yield userData;
    } catch (error) {
      yield { error: \`Failed to fetch user \${id}\`, id };
    }
  }
}

// 批量处理异步数据
async function* batchProcess(asyncGenerator, batchSize = 3) {
  let batch = [];
  
  for await (const item of asyncGenerator) {
    batch.push(item);
    
    if (batch.length >= batchSize) {
      yield batch;
      batch = [];
    }
  }
  
  if (batch.length > 0) {
    yield batch;
  }
}

// 使用示例
async function processUsers() {
  const userIds = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  const userStream = fetchUserData(userIds);
  const batchedStream = batchProcess(userStream, 3);
  
  for await (const batch of batchedStream) {
    console.log('处理批次:', batch.length, '个用户');
    // 批量处理用户数据
  }
}`,
    tags: ['异步生成器', 'async function*', 'for await...of'],
    relatedQuestions: ['异步迭代器', 'Promise处理', '数据流']
  }
];

export default commonQuestions;
