import { Implementation } from '@/types/api';

const implementation: Implementation = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  mechanism: `生成器的实现机制基于协程和状态机。JavaScript引擎为每个生成器维护一个执行上下文，包括局部变量、执行位置等状态信息。当遇到yield时，引擎保存当前状态并暂停执行，当调用next()时恢复执行。

核心实现原理：

1. **执行上下文保存**
   - 保存局部变量和执行位置
   - 维护调用栈状态
   - 支持嵌套生成器调用

2. **yield暂停机制**
   - 暂停函数执行
   - 返回yield的值
   - 保持函数状态不变

3. **双向通信**
   - next()可以传递值给生成器
   - yield表达式可以接收传入的值
   - 支持错误传播机制

4. **迭代器协议集成**
   - 自动实现Symbol.iterator
   - 返回符合迭代器协议的对象
   - 支持for...of循环`,

  visualization: `graph TD
    A[Generator Function] --> B[Generator Object]
    B --> C[next Method]
    C --> D{Yield Encountered}
    D -->|Yes| E[Pause Execution]
    D -->|No| F[Continue Execution]
    E --> G[Save State]
    G --> H[Return Value]
    F --> I[Function Complete]
    
    J[next Call] --> K[Restore State]
    K --> L[Resume Execution]
    L --> D
    
    style A fill:#e1f5fe
    style E fill:#fff3e0
    style G fill:#e8f5e8`,
    
  plainExplanation: `简单来说，生成器就像是一个"可暂停的函数"。

想象一下：
- 普通函数就像是一口气跑完的马拉松
- 生成器函数就像是可以随时暂停休息的马拉松
- yield就像是"休息点"，可以停下来喝水
- next()就像是"继续跑"的指令
- 生成器会记住你在哪里停下的，下次从那里继续

这种设计让你可以处理很长的数据序列，而不用一次性把所有数据都准备好。`,

  designConsiderations: [
    '状态保持 - 在暂停期间保持函数的执行状态',
    '惰性求值 - 只在需要时计算和返回值',
    '双向通信 - 支持外部向生成器传递数据',
    '迭代器集成 - 自动实现迭代器协议',
    '错误处理 - 支持错误的传播和处理'
  ],
  
  relatedConcepts: [
    '协程：生成器是协程的一种实现形式',
    '状态机：生成器内部使用状态机管理执行状态',
    '迭代器协议：生成器自动实现迭代器接口',
    '惰性求值：按需计算值，提高性能',
    '异步编程：异步生成器支持异步操作'
  ]
};

export default implementation;
