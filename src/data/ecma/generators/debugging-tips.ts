import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: '生成器使用中的常见错误和解决方案',
        sections: [
          {
            title: '生成器语法错误',
            description: '生成器定义和使用相关的常见问题',
            items: [
              {
                title: 'SyntaxError: Unexpected token yield',
                description: '在非生成器函数中使用yield',
                solution: '确保在function*定义的生成器函数中使用yield',
                prevention: '理解yield只能在生成器函数内使用',
                code: `// ❌ 错误：在普通函数中使用yield
function normalFunction() {
  yield 1; // SyntaxError
}

// ❌ 错误：在箭头函数中使用yield
const arrowGen = () => {
  yield 1; // SyntaxError
};

// ✅ 正确：在生成器函数中使用yield
function* generatorFunction() {
  yield 1; // 正确
}

// ✅ 正确：生成器方法
const obj = {
  *generatorMethod() {
    yield 1;
  }
};

// ✅ 正确：类中的生成器方法
class MyClass {
  *generatorMethod() {
    yield 1;
  }
}`
              },
              {
                title: '生成器状态错误',
                description: '错误地重复使用已完成的生成器',
                solution: '理解生成器的一次性特性，需要时重新创建',
                prevention: '跟踪生成器的状态，避免重复使用',
                code: `// ❌ 错误：重复使用已完成的生成器
function* numbers() {
  yield 1;
  yield 2;
  yield 3;
}

const gen = numbers();
console.log([...gen]); // [1, 2, 3]
console.log([...gen]); // [] - 生成器已耗尽

// ✅ 正确：重新创建生成器
const gen1 = numbers();
console.log([...gen1]); // [1, 2, 3]

const gen2 = numbers();
console.log([...gen2]); // [1, 2, 3]

// ✅ 正确：创建生成器工厂
function createNumberGenerator() {
  return numbers();
}

const factory = createNumberGenerator;
console.log([...factory()]); // [1, 2, 3]
console.log([...factory()]); // [1, 2, 3]`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '使用开发工具调试生成器相关问题',
        sections: [
          {
            title: '生成器执行跟踪',
            description: '跟踪生成器的执行状态和值产生过程',
            items: [
              {
                title: '生成器状态调试',
                description: '监控生成器的执行状态和产生的值',
                solution: '添加调试日志和状态跟踪',
                prevention: '定期检查生成器的执行流程',
                code: `// 调试生成器执行
function* debugGenerator() {
  console.log('生成器开始执行');
  
  try {
    const input1 = yield 'first';
    console.log('收到输入1:', input1);
    
    const input2 = yield 'second';
    console.log('收到输入2:', input2);
    
    return 'completed';
  } catch (error) {
    console.log('生成器捕获错误:', error.message);
    yield 'error handled';
  } finally {
    console.log('生成器清理');
  }
}

// 调试生成器使用
function debugGeneratorUsage() {
  const gen = debugGenerator();
  
  console.log('=== 开始调试 ===');
  let result = gen.next();
  console.log('第一次next():', result);
  
  result = gen.next('input1');
  console.log('第二次next():', result);
  
  result = gen.next('input2');
  console.log('第三次next():', result);
  
  console.log('=== 调试结束 ===');
}

// 生成器包装器用于调试
function createDebugWrapper(generatorFn, name = 'Generator') {
  return function* (...args) {
    console.log(\`\${name} 开始执行，参数:\`, args);
    
    const generator = generatorFn(...args);
    let stepCount = 0;
    let lastInput;
    
    try {
      let result = generator.next();
      
      while (!result.done) {
        stepCount++;
        console.log(\`\${name} 步骤 \${stepCount}:\`, {
          yielded: result.value,
          input: lastInput
        });
        
        lastInput = yield result.value;
        result = generator.next(lastInput);
      }
      
      console.log(\`\${name} 完成，返回值:\`, result.value);
      return result.value;
      
    } catch (error) {
      console.log(\`\${name} 发生错误:\`, error.message);
      throw error;
    }
  };
}

// 使用调试包装器
function* originalGenerator(start) {
  yield start;
  yield start + 1;
  yield start + 2;
  return 'done';
}

const debugWrapped = createDebugWrapper(originalGenerator, 'NumberGen');
const gen = debugWrapped(10);

for (const value of gen) {
  console.log('外部收到:', value);
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
