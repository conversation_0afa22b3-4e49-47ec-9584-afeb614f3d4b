import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'state-machine',
    title: '状态机实现',
    description: '使用生成器实现复杂的状态机，支持状态转换和事件处理',
    businessValue: '简化复杂业务流程的状态管理，提高代码可读性和可维护性',
    scenario: '电商订单系统需要管理订单的多种状态（待支付、已支付、已发货、已完成等），每种状态有不同的操作和转换规则。',
    code: `// 订单状态机
class OrderStateMachine {
  constructor(orderId) {
    this.orderId = orderId;
    this.currentState = 'pending';
    this.history = [];
    this.stateMachine = this.createStateMachine();
  }
  
  *createStateMachine() {
    // 待支付状态
    while (this.currentState === 'pending') {
      const action = yield {
        state: 'pending',
        allowedActions: ['pay', 'cancel'],
        message: '订单待支付'
      };
      
      switch (action.type) {
        case 'pay':
          if (this.validatePayment(action.data)) {
            this.currentState = 'paid';
            this.history.push({ action: 'pay', timestamp: Date.now() });
          }
          break;
        case 'cancel':
          this.currentState = 'cancelled';
          this.history.push({ action: 'cancel', timestamp: Date.now() });
          return { state: 'cancelled', final: true };
      }
    }
    
    // 已支付状态
    while (this.currentState === 'paid') {
      const action = yield {
        state: 'paid',
        allowedActions: ['ship', 'refund'],
        message: '订单已支付，等待发货'
      };
      
      switch (action.type) {
        case 'ship':
          this.currentState = 'shipped';
          this.history.push({ 
            action: 'ship', 
            timestamp: Date.now(),
            trackingNumber: action.data.trackingNumber 
          });
          break;
        case 'refund':
          this.currentState = 'refunded';
          this.history.push({ action: 'refund', timestamp: Date.now() });
          return { state: 'refunded', final: true };
      }
    }
    
    // 已发货状态
    while (this.currentState === 'shipped') {
      const action = yield {
        state: 'shipped',
        allowedActions: ['deliver', 'return'],
        message: '订单已发货'
      };
      
      switch (action.type) {
        case 'deliver':
          this.currentState = 'delivered';
          this.history.push({ action: 'deliver', timestamp: Date.now() });
          break;
        case 'return':
          this.currentState = 'returned';
          this.history.push({ action: 'return', timestamp: Date.now() });
          return { state: 'returned', final: true };
      }
    }
    
    // 已送达状态
    if (this.currentState === 'delivered') {
      yield {
        state: 'delivered',
        allowedActions: ['complete'],
        message: '订单已送达，等待确认'
      };
      
      this.currentState = 'completed';
      this.history.push({ action: 'complete', timestamp: Date.now() });
      return { state: 'completed', final: true };
    }
  }
  
  processAction(action) {
    const result = this.stateMachine.next(action);
    return result.value;
  }
  
  validatePayment(paymentData) {
    // 支付验证逻辑
    return paymentData && paymentData.amount > 0;
  }
  
  getHistory() {
    return this.history;
  }
}

// 使用示例
const order = new OrderStateMachine('ORDER-001');

// 获取当前状态
let currentState = order.processAction();
console.log(currentState); // { state: 'pending', allowedActions: ['pay', 'cancel'] }

// 支付订单
currentState = order.processAction({ 
  type: 'pay', 
  data: { amount: 100, method: 'credit_card' } 
});
console.log(currentState); // { state: 'paid', allowedActions: ['ship', 'refund'] }

// 发货
currentState = order.processAction({ 
  type: 'ship', 
  data: { trackingNumber: 'TN123456' } 
});
console.log(currentState); // { state: 'shipped', allowedActions: ['deliver', 'return'] }`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'data-streaming',
    title: '数据流处理',
    description: '使用生成器实现高效的数据流处理，支持实时数据转换和过滤',
    businessValue: '处理大量实时数据时节省内存，提供流式处理能力',
    scenario: '日志分析系统需要实时处理大量日志数据，包括解析、过滤、转换和聚合操作。',
    code: `// 日志流处理器
class LogStreamProcessor {
  constructor() {
    this.filters = [];
    this.transformers = [];
    this.aggregators = new Map();
  }
  
  // 日志解析生成器
  *parseLogStream(logLines) {
    for (const line of logLines) {
      try {
        const parsed = this.parseLogLine(line);
        if (parsed) {
          yield parsed;
        }
      } catch (error) {
        console.warn('解析日志失败:', line, error.message);
      }
    }
  }
  
  // 过滤生成器
  *filterLogs(logStream, filters = this.filters) {
    for (const log of logStream) {
      let shouldInclude = true;
      
      for (const filter of filters) {
        if (!filter(log)) {
          shouldInclude = false;
          break;
        }
      }
      
      if (shouldInclude) {
        yield log;
      }
    }
  }
  
  // 转换生成器
  *transformLogs(logStream, transformers = this.transformers) {
    for (const log of logStream) {
      let transformedLog = log;
      
      for (const transformer of transformers) {
        transformedLog = transformer(transformedLog);
      }
      
      yield transformedLog;
    }
  }
  
  // 批处理生成器
  *batchLogs(logStream, batchSize = 100) {
    let batch = [];
    
    for (const log of logStream) {
      batch.push(log);
      
      if (batch.length >= batchSize) {
        yield batch;
        batch = [];
      }
    }
    
    // 处理剩余的日志
    if (batch.length > 0) {
      yield batch;
    }
  }
  
  // 时间窗口聚合生成器
  *timeWindowAggregation(logStream, windowSize = 60000) { // 1分钟窗口
    let currentWindow = {
      startTime: null,
      endTime: null,
      logs: [],
      stats: {
        count: 0,
        errorCount: 0,
        levels: new Map()
      }
    };
    
    for (const log of logStream) {
      const logTime = new Date(log.timestamp).getTime();
      
      // 初始化窗口
      if (!currentWindow.startTime) {
        currentWindow.startTime = logTime;
        currentWindow.endTime = logTime + windowSize;
      }
      
      // 检查是否需要新窗口
      if (logTime >= currentWindow.endTime) {
        if (currentWindow.logs.length > 0) {
          yield this.finalizeWindow(currentWindow);
        }
        
        // 创建新窗口
        currentWindow = {
          startTime: logTime,
          endTime: logTime + windowSize,
          logs: [],
          stats: {
            count: 0,
            errorCount: 0,
            levels: new Map()
          }
        };
      }
      
      // 添加日志到当前窗口
      currentWindow.logs.push(log);
      currentWindow.stats.count++;
      
      if (log.level === 'ERROR') {
        currentWindow.stats.errorCount++;
      }
      
      const levelCount = currentWindow.stats.levels.get(log.level) || 0;
      currentWindow.stats.levels.set(log.level, levelCount + 1);
    }
    
    // 处理最后一个窗口
    if (currentWindow.logs.length > 0) {
      yield this.finalizeWindow(currentWindow);
    }
  }
  
  // 完整的处理管道
  *processLogStream(rawLogLines) {
    const parsed = this.parseLogStream(rawLogLines);
    const filtered = this.filterLogs(parsed);
    const transformed = this.transformLogs(filtered);
    const windowed = this.timeWindowAggregation(transformed);
    
    yield* windowed;
  }
  
  parseLogLine(line) {
    // 简化的日志解析
    const match = line.match(/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z) \[(\w+)\] (.+)$/);
    if (match) {
      return {
        timestamp: match[1],
        level: match[2],
        message: match[3],
        raw: line
      };
    }
    return null;
  }
  
  finalizeWindow(window) {
    return {
      timeRange: {
        start: new Date(window.startTime).toISOString(),
        end: new Date(window.endTime).toISOString()
      },
      stats: {
        totalLogs: window.stats.count,
        errorRate: window.stats.errorCount / window.stats.count,
        levelDistribution: Object.fromEntries(window.stats.levels)
      },
      logs: window.logs
    };
  }
}

// 使用示例
const processor = new LogStreamProcessor();

// 添加过滤器
processor.filters.push(log => log.level !== 'DEBUG');
processor.filters.push(log => !log.message.includes('health-check'));

// 添加转换器
processor.transformers.push(log => ({
  ...log,
  processedAt: new Date().toISOString()
}));

// 模拟日志数据
function* generateLogLines() {
  const levels = ['INFO', 'WARN', 'ERROR', 'DEBUG'];
  for (let i = 0; i < 1000; i++) {
    const timestamp = new Date(Date.now() + i * 1000).toISOString();
    const level = levels[Math.floor(Math.random() * levels.length)];
    const message = \`Message \${i} - \${Math.random().toString(36).substr(2, 9)}\`;
    yield \`\${timestamp} [\${level}] \${message}\`;
  }
}

// 处理日志流
const logLines = generateLogLines();
const processedStream = processor.processLogStream(logLines);

for (const windowResult of processedStream) {
  console.log('时间窗口统计:', windowResult.stats);
}`
  }
];

export default businessScenarios;
