import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: `生成器的历史反映了编程语言对协程和惰性求值概念的探索。从早期的协程理论到现代的生成器实现，体现了函数式编程和异步编程的融合。`,
  
  background: `在生成器出现之前，JavaScript缺乏原生的协程支持，开发者需要使用复杂的回调或状态机来实现类似功能。生成器的引入为JavaScript带来了强大的控制流管理能力。`,

  evolution: `生成器的引入标志着JavaScript向更高级的编程范式迈进，为异步编程和函数式编程提供了新的工具。`,

  timeline: [
    {
      year: '2015',
      event: 'ES6生成器标准化',
      description: 'ECMAScript 2015引入生成器函数和yield关键字',
      significance: '为JavaScript提供了协程和惰性求值能力'
    },
    {
      year: '2018',
      event: '异步生成器标准化',
      description: 'ES2018引入异步生成器和for await...of',
      significance: '扩展了生成器到异步编程领域'
    }
  ],

  keyFigures: [
    {
      name: 'TC39委员会',
      role: 'ECMAScript标准制定者',
      contribution: '设计和标准化生成器语法',
      significance: '推动了JavaScript协程能力的发展'
    }
  ],

  concepts: [
    {
      term: '协程',
      definition: '可以暂停和恢复执行的程序组件',
      evolution: '从理论概念发展为JavaScript的生成器实现',
      modernRelevance: '现代异步编程的重要工具'
    }
  ],

  designPhilosophy: `生成器体现了"控制即数据"的设计哲学，将程序的控制流转化为可操作的数据结构。`,

  impact: `生成器的引入为JavaScript带来了强大的抽象能力，简化了复杂的异步和迭代逻辑。`,

  modernRelevance: `在现代JavaScript开发中，生成器是理解异步编程、函数式编程和数据流处理的关键概念。`
};

export default knowledgeArchaeology;
