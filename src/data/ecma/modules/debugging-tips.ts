import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: 'ES6模块使用中的常见错误和解决方案',
        sections: [
          {
            title: '模块路径和导入错误',
            description: '模块路径解析和导入语法相关的错误',
            items: [
              {
                title: 'SyntaxError: Unexpected token import',
                description: '在不支持ES6模块的环境中使用import语法',
                solution: '确保运行环境支持ES6模块或使用构建工具转换',
                prevention: '配置正确的模块系统和构建工具',
                code: `// ❌ 错误：在CommonJS环境中使用import
import { utils } from './utils.js'; // SyntaxError

// ✅ 解决方案1：使用CommonJS语法
const { utils } = require('./utils.js');

// ✅ 解决方案2：配置package.json
{
  "type": "module" // 启用ES6模块
}

// ✅ 解决方案3：使用.mjs扩展名
// utils.mjs
export const utils = {};`
              },
              {
                title: 'TypeError: Failed to resolve module specifier',
                description: '模块路径解析失败',
                solution: '使用正确的相对路径或绝对路径',
                prevention: '遵循模块路径规范，使用构建工具处理路径',
                code: `// ❌ 错误：缺少文件扩展名或路径不正确
import { utils } from './utils'; // 可能失败
import { helper } from 'helper'; // 相对路径错误

// ✅ 正确：完整的相对路径
import { utils } from './utils.js';
import { helper } from './helpers/helper.js';
import { library } from '../lib/library.js';`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '使用开发工具调试模块相关问题',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '利用浏览器工具调试模块加载和依赖问题',
            items: [
              {
                title: '网络面板模块调试',
                description: '在Network面板中查看模块加载情况',
                solution: '检查模块文件的加载状态和时间',
                prevention: '定期检查模块加载性能',
                code: `// 在浏览器中调试模块加载
// 1. 打开开发者工具 -> Network面板
// 2. 刷新页面，观察JS文件加载
// 3. 查看模块文件的状态码和加载时间

// 动态导入调试
async function debugDynamicImport() {
  console.time('module-load');
  try {
    const module = await import('./heavy-module.js');
    console.timeEnd('module-load');
    console.log('Module loaded:', module);
  } catch (error) {
    console.error('Failed to load module:', error);
  }
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
