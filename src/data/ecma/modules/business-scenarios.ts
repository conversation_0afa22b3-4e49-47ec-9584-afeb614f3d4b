import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'frontend-architecture',
    title: '前端应用架构设计',
    description: '使用ES6模块构建可维护的前端应用架构，实现组件化和模块化开发',
    businessValue: '提高代码复用性，降低维护成本，支持团队协作和代码分割优化',
    scenario: '大型前端应用需要清晰的模块结构，包括组件库、工具函数、状态管理、API服务等模块的组织和管理。',
    code: `// components/Button/index.js - 按钮组件模块
export { default } from './Button.js';
export { ButtonGroup } from './ButtonGroup.js';
export * from './types.js';

// components/Button/Button.js
import { createElement } from '../../utils/dom.js';
import { validateProps } from '../../utils/validation.js';
import './Button.css';

export default class Button {
  constructor(props) {
    this.props = validateProps(props, {
      text: 'string',
      variant: 'string',
      onClick: 'function'
    });
    this.element = this.render();
  }
  
  render() {
    const button = createElement('button', {
      className: \`btn btn-\${this.props.variant}\`,
      textContent: this.props.text,
      onClick: this.props.onClick
    });
    return button;
  }
}

// services/api.js - API服务模块
const API_BASE_URL = process.env.API_BASE_URL || 'https://api.example.com';

class ApiClient {
  constructor(baseURL = API_BASE_URL) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json'
    };
  }
  
  async request(endpoint, options = {}) {
    const url = \`\${this.baseURL}\${endpoint}\`;
    const config = {
      headers: { ...this.defaultHeaders, ...options.headers },
      ...options
    };
    
    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(\`API Error: \${response.status}\`);
    }
    
    return response.json();
  }
  
  get(endpoint, params) {
    const searchParams = new URLSearchParams(params);
    const url = params ? \`\${endpoint}?\${searchParams}\` : endpoint;
    return this.request(url);
  }
  
  post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }
}

export const apiClient = new ApiClient();
export { ApiClient };

// services/userService.js - 用户服务模块
import { apiClient } from './api.js';
import { UserModel } from '../models/User.js';

export class UserService {
  static async getUser(id) {
    const userData = await apiClient.get(\`/users/\${id}\`);
    return new UserModel(userData);
  }
  
  static async createUser(userData) {
    const response = await apiClient.post('/users', userData);
    return new UserModel(response);
  }
  
  static async updateUser(id, updates) {
    const response = await apiClient.put(\`/users/\${id}\`, updates);
    return new UserModel(response);
  }
}

// store/index.js - 状态管理模块
import { createStore } from './createStore.js';
import { userReducer } from './reducers/userReducer.js';
import { appReducer } from './reducers/appReducer.js';

const rootReducer = {
  user: userReducer,
  app: appReducer
};

export const store = createStore(rootReducer);
export * from './actions/index.js';

// app.js - 主应用入口
import Button from './components/Button/index.js';
import { UserService } from './services/userService.js';
import { store } from './store/index.js';
import { setUser, setLoading } from './store/index.js';

class App {
  constructor() {
    this.init();
  }
  
  async init() {
    try {
      store.dispatch(setLoading(true));
      
      const user = await UserService.getUser(1);
      store.dispatch(setUser(user));
      
      this.renderUI();
    } catch (error) {
      console.error('App initialization failed:', error);
    } finally {
      store.dispatch(setLoading(false));
    }
  }
  
  renderUI() {
    const loginButton = new Button({
      text: 'Login',
      variant: 'primary',
      onClick: this.handleLogin.bind(this)
    });
    
    document.body.appendChild(loginButton.element);
  }
  
  handleLogin() {
    // 动态导入登录模块
    import('./modules/auth/LoginModal.js')
      .then(({ LoginModal }) => {
        const modal = new LoginModal();
        modal.show();
      })
      .catch(error => {
        console.error('Failed to load login module:', error);
      });
  }
}

new App();`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'library-development',
    title: '工具库开发',
    description: '使用ES6模块开发可复用的JavaScript工具库，支持多种导入方式',
    businessValue: '提供标准化的工具函数，支持树摇优化，减少最终包体积',
    scenario: '开发一个通用的工具库，包含日期处理、字符串操作、数组工具等功能，需要支持按需导入和全量导入。',
    code: `// src/date/index.js - 日期工具模块
export { formatDate } from './formatDate.js';
export { parseDate } from './parseDate.js';
export { addDays, subtractDays } from './dateArithmetic.js';
export { isWeekend, isBusinessDay } from './dateValidation.js';

// src/date/formatDate.js
export function formatDate(date, format = 'YYYY-MM-DD') {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day);
}

export function formatTime(date, format = 'HH:mm:ss') {
  const d = new Date(date);
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

// src/string/index.js - 字符串工具模块
export { capitalize } from './capitalize.js';
export { slugify } from './slugify.js';
export { truncate } from './truncate.js';
export { removeAccents } from './removeAccents.js';

// src/string/capitalize.js
export function capitalize(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

export function capitalizeWords(str) {
  return str.replace(/\\b\\w/g, char => char.toUpperCase());
}

// src/array/index.js - 数组工具模块
export { chunk } from './chunk.js';
export { unique } from './unique.js';
export { groupBy } from './groupBy.js';
export { sortBy } from './sortBy.js';

// src/array/chunk.js
export function chunk(array, size) {
  if (size <= 0) return [];
  
  const chunks = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

// src/index.js - 主入口文件
// 重新导出所有模块
export * from './date/index.js';
export * from './string/index.js';
export * from './array/index.js';
export * from './object/index.js';

// 提供命名空间导出
export * as DateUtils from './date/index.js';
export * as StringUtils from './string/index.js';
export * as ArrayUtils from './array/index.js';
export * as ObjectUtils from './object/index.js';

// 默认导出包含所有工具的对象
import * as DateUtils from './date/index.js';
import * as StringUtils from './string/index.js';
import * as ArrayUtils from './array/index.js';
import * as ObjectUtils from './object/index.js';

export default {
  DateUtils,
  StringUtils,
  ArrayUtils,
  ObjectUtils
};

// 使用示例
// 1. 按需导入
import { formatDate, capitalize, chunk } from 'my-utils';

// 2. 命名空间导入
import { DateUtils, StringUtils } from 'my-utils';

// 3. 全量导入
import * as Utils from 'my-utils';

// 4. 默认导入
import MyUtils from 'my-utils';`
  }
];

export default businessScenarios;
