import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const modulesData: ApiItem = {
  id: 'modules',
  title: 'ES6 Modules',
  description: 'ES6模块系统，提供标准化的模块导入导出机制，支持静态分析和树摇优化',
  category: 'ECMA特性',
  difficulty: 'medium',
  
  syntax: `export { name }; export default value; import { name } from './module'; import defaultExport from './module';`,
  example: `export const PI = 3.14; export default function calculate() {} import { PI } from './math'; import calc from './math';`,
  notes: 'ES6模块是静态的，支持编译时优化，是现代JavaScript项目的标准模块系统',
  
  version: 'ES6 (ES2015)',
  tags: ['ES6', 'JavaScript', '模块', '导入导出', '静态分析'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default modulesData;
