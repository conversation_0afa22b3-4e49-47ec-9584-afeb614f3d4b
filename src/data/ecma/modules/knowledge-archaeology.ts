import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: `ES6模块的历史反映了JavaScript从脚本语言向应用开发平台的演进过程。从全局变量到模块化，体现了软件工程实践在JavaScript中的应用。`,
  
  background: `在ES6之前，JavaScript缺乏标准的模块系统，开发者使用各种模式（IIFE、命名空间、AMD、CommonJS）来组织代码。这种分裂状态阻碍了JavaScript生态系统的发展。`,

  evolution: `ES6模块的引入统一了JavaScript的模块标准，为现代JavaScript开发奠定了基础，推动了工具链和生态系统的发展。`,

  timeline: [
    {
      year: '2009',
      event: 'CommonJS规范发布',
      description: 'Node.js采用CommonJS模块系统',
      significance: '为服务端JavaScript提供了模块化解决方案'
    },
    {
      year: '2011',
      event: 'AMD规范发布',
      description: 'RequireJS推广异步模块定义',
      significance: '解决了浏览器端的模块加载问题'
    },
    {
      year: '2015',
      event: 'ES6模块标准化',
      description: 'ECMAScript 2015正式引入模块语法',
      significance: '统一了JavaScript的模块标准'
    },
    {
      year: '2020',
      event: '动态导入标准化',
      description: 'ES2020引入import()动态导入',
      significance: '完善了模块系统的功能'
    }
  ],

  keyFigures: [
    {
      name: 'Dave Herman',
      role: 'Mozilla研究员',
      contribution: '参与ES6模块规范的设计',
      significance: '推动了JavaScript模块化标准的制定'
    }
  ],

  concepts: [
    {
      term: '模块化编程',
      definition: '将程序分解为独立、可重用模块的编程方法',
      evolution: '从简单的代码分离发展为复杂的依赖管理系统',
      modernRelevance: '现代软件开发的基础实践'
    }
  ],

  designPhilosophy: `ES6模块的设计体现了"静态优于动态"的哲学，通过编译时分析来实现更好的优化和工具支持。`,

  impact: `ES6模块的标准化推动了整个JavaScript生态系统的发展，使得现代前端工程化成为可能。`,

  modernRelevance: `在现代JavaScript开发中，ES6模块已经成为代码组织的标准方式，是所有现代框架和工具的基础。`
};

export default knowledgeArchaeology;
