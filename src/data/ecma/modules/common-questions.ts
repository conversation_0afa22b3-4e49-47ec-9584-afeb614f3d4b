import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么导入的绑定是只读的？',
    answer: '导入的绑定是只读的是为了保持模块的封装性和一致性。这样可以防止一个模块意外修改另一个模块的导出值，避免了难以调试的副作用。如果需要修改值，应该在导出模块内部提供相应的方法。',
    code: `// math.js
export let count = 0;

export function increment() {
  count++; // ✅ 在导出模块内部可以修改
}

export function getCount() {
  return count;
}

// main.js
import { count, increment } from './math.js';

console.log(count); // 0
increment();
console.log(count); // 1 (实时绑定，值已更新)

// count = 5; // ❌ TypeError: Assignment to constant variable
// 导入的绑定是只读的，不能直接修改

// ✅ 正确的方式：通过导出的方法修改
increment();
console.log(count); // 2`,
    tags: ['只读绑定', '模块封装', '实时绑定'],
    relatedQuestions: ['模块作用域', '导出绑定']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '如何处理模块的循环依赖？',
    answer: 'ES6模块通过延迟绑定和实时绑定来处理循环依赖。当遇到循环依赖时，模块会先创建绑定，然后在执行阶段填充实际值。最佳实践是重构代码避免循环依赖，或者将共同依赖提取到单独的模块中。',
    code: `// a.js
import { b } from './b.js';
export const a = 'module a';
console.log('a.js:', b); // 可能是undefined，取决于执行顺序

// b.js  
import { a } from './a.js';
export const b = 'module b';
console.log('b.js:', a); // 可能是undefined，取决于执行顺序

// 更好的解决方案：
// shared.js
export const shared = {
  data: 'shared data'
};

// a.js
import { shared } from './shared.js';
export const a = 'module a';
export function useShared() {
  return shared.data;
}

// b.js
import { shared } from './shared.js';
export const b = 'module b';
export function useShared() {
  return shared.data;
}`,
    tags: ['循环依赖', '延迟绑定', '模块设计'],
    relatedQuestions: ['模块依赖图', '代码重构']
  }
];

export default commonQuestions;
