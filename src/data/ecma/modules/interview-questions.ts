import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'ES6模块与CommonJS模块有什么区别？',
    answer: `ES6模块与CommonJS模块的主要区别：

**加载时机**：
- ES6模块：编译时静态加载
- CommonJS：运行时动态加载

**导入导出语法**：
- ES6模块：import/export语法
- CommonJS：require()/module.exports

**绑定方式**：
- ES6模块：实时绑定，导出值变化时导入值也变化
- CommonJS：值拷贝，导入时复制值

**循环依赖**：
- ES6模块：通过延迟绑定处理
- CommonJS：可能导致部分加载

**树摇支持**：
- ES6模块：支持静态分析和树摇
- CommonJS：难以进行静态分析`,
   
    difficulty: 'medium',
    frequency: 'high',
    category: '基础概念',
    tags: ['模块系统', 'CommonJS', '静态分析'],
    
    code: `// ES6模块
// math.js
export let count = 0;
export function increment() {
  count++;
}
export default function multiply(a, b) {
  return a * b;
}

// main.js
import multiply, { count, increment } from './math.js';
console.log(count); // 0
increment();
console.log(count); // 1 (实时绑定)

// CommonJS模块
// math.js
let count = 0;
function increment() {
  count++;
}
function multiply(a, b) {
  return a * b;
}

module.exports = { count, increment, multiply };

// main.js
const { count, increment, multiply } = require('./math.js');
console.log(count); // 0
increment();
console.log(count); // 0 (值拷贝，不会变化)`,
    
    followUp: [
      '什么是树摇（Tree Shaking）？',
      '如何处理模块的循环依赖？',
      '动态导入的使用场景？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '什么是动态导入？有什么使用场景？',
    answer: `动态导入是ES2020引入的特性，允许在运行时动态加载模块。

**特点**：
- 返回Promise，支持异步加载
- 可以在任何地方使用，不限于顶层
- 支持条件加载和按需加载
- 有助于代码分割和性能优化

**使用场景**：
- 代码分割和懒加载
- 条件模块加载
- 插件系统
- 国际化资源加载`,
   
    difficulty: 'medium',
    frequency: 'medium',
    category: '高级特性',
    tags: ['动态导入', '代码分割', '懒加载'],
    
    code: `// 1. 代码分割和懒加载
async function loadChart() {
  const { Chart } = await import('./chart.js');
  return new Chart();
}

// 2. 条件模块加载
async function loadPolyfill() {
  if (!window.fetch) {
    await import('./fetch-polyfill.js');
  }
}

// 3. 路由懒加载
const routes = {
  '/home': () => import('./pages/Home.js'),
  '/about': () => import('./pages/About.js'),
  '/contact': () => import('./pages/Contact.js')
};

async function navigate(path) {
  const moduleLoader = routes[path];
  if (moduleLoader) {
    const { default: Component } = await moduleLoader();
    return new Component();
  }
}

// 4. 国际化资源加载
async function loadLocale(locale) {
  try {
    const messages = await import(\`./locales/\${locale}.js\`);
    return messages.default;
  } catch (error) {
    // 回退到默认语言
    const messages = await import('./locales/en.js');
    return messages.default;
  }
}

// 5. 插件系统
class PluginManager {
  constructor() {
    this.plugins = new Map();
  }
  
  async loadPlugin(name) {
    if (this.plugins.has(name)) {
      return this.plugins.get(name);
    }
    
    try {
      const module = await import(\`./plugins/\${name}.js\`);
      const plugin = new module.default();
      this.plugins.set(name, plugin);
      return plugin;
    } catch (error) {
      console.error(\`Failed to load plugin \${name}:\`, error);
      return null;
    }
  }
}`,
    
    followUp: [
      '动态导入的性能影响？',
      '如何处理动态导入的错误？',
      '动态导入与Webpack的关系？'
    ]
  }
];

export default interviewQuestions;
