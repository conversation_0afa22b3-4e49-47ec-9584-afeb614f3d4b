import { DebuggingTip } from '@/types/api';

const debuggingTips: DebuggingTip[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'async-iterator-flow-debugging',
    title: '调试异步迭代器的执行流程',
    problem: '异步迭代器的执行流程复杂，难以跟踪数据流和状态变化',
    solution: `
调试异步迭代器需要理解其异步执行模型和状态管理：

1. **添加详细的日志记录**：跟踪迭代器的状态变化
2. **使用调试包装器**：包装异步迭代器添加调试信息
3. **可视化数据流**：使用工具可视化异步数据流
4. **状态监控**：监控迭代器的内部状态和缓冲区
    `,
    code: `
// 调试包装器：为异步迭代器添加调试信息
function debugAsyncIterator(asyncIterable, name = 'AsyncIterator') {
  return {
    async *[Symbol.asyncIterator]() {
      console.log(\`[\${name}] Starting iteration\`);
      let count = 0;
      const startTime = Date.now();
      
      try {
        for await (const item of asyncIterable) {
          count++;
          const elapsed = Date.now() - startTime;
          console.log(\`[\${name}] Item \${count}: \`, {
            value: item,
            elapsed: \`\${elapsed}ms\`,
            rate: \`\${(count / elapsed * 1000).toFixed(2)} items/sec\`
          });
          
          yield item;
        }
      } catch (error) {
        console.error(\`[\${name}] Error at item \${count}:\`, error);
        throw error;
      } finally {
        const totalTime = Date.now() - startTime;
        console.log(\`[\${name}] Completed: \${count} items in \${totalTime}ms\`);
      }
    }
  };
}

// 状态监控的异步迭代器
class MonitoredAsyncIterator {
  constructor(source, name = 'Monitor') {
    this.source = source;
    this.name = name;
    this.stats = {
      itemsProduced: 0,
      itemsConsumed: 0,
      errors: 0,
      startTime: null,
      lastActivity: null
    };
  }

  async *[Symbol.asyncIterator]() {
    this.stats.startTime = Date.now();
    const iterator = this.source[Symbol.asyncIterator]();
    
    try {
      while (true) {
        this.stats.lastActivity = Date.now();
        
        // 监控next()调用
        console.log(\`[\${this.name}] Calling next(), stats:\`, this.getStats());
        
        const result = await iterator.next();
        
        if (result.done) {
          console.log(\`[\${this.name}] Iterator completed\`);
          break;
        }
        
        this.stats.itemsProduced++;
        this.stats.itemsConsumed++;
        
        // 检查性能指标
        if (this.stats.itemsProduced % 100 === 0) {
          this.logPerformanceMetrics();
        }
        
        yield result.value;
      }
    } catch (error) {
      this.stats.errors++;
      console.error(\`[\${this.name}] Error:\`, error);
      throw error;
    } finally {
      this.logFinalStats();
    }
  }

  getStats() {
    const now = Date.now();
    const elapsed = this.stats.startTime ? now - this.stats.startTime : 0;
    const rate = elapsed > 0 ? (this.stats.itemsProduced / elapsed * 1000) : 0;
    
    return {
      ...this.stats,
      elapsed: \`\${elapsed}ms\`,
      rate: \`\${rate.toFixed(2)} items/sec\`,
      timeSinceLastActivity: this.stats.lastActivity ? \`\${now - this.stats.lastActivity}ms\` : 'N/A'
    };
  }

  logPerformanceMetrics() {
    console.log(\`[\${this.name}] Performance metrics:\`, this.getStats());
  }

  logFinalStats() {
    console.log(\`[\${this.name}] Final statistics:\`, this.getStats());
  }
}

// 使用调试工具
async function debugExample() {
  // 原始异步迭代器
  async function* dataSource() {
    for (let i = 1; i <= 5; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      if (i === 3) {
        throw new Error('Simulated error');
      }
      yield \`data-\${i}\`;
    }
  }

  // 添加调试包装
  const debuggedSource = debugAsyncIterator(dataSource(), 'DataSource');
  const monitoredSource = new MonitoredAsyncIterator(debuggedSource, 'Monitor');

  try {
    for await (const item of monitoredSource) {
      console.log('Processing:', item);
      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  } catch (error) {
    console.log('Caught error:', error.message);
  }
}

// 高级调试：异步迭代器流水线调试
class AsyncIteratorPipeline {
  constructor() {
    this.stages = [];
    this.debugMode = false;
  }

  enableDebug() {
    this.debugMode = true;
    return this;
  }

  addStage(name, transformer) {
    this.stages.push({ name, transformer });
    return this;
  }

  async *process(source) {
    let currentStream = source;
    
    for (const [index, stage] of this.stages.entries()) {
      if (this.debugMode) {
        console.log(\`Starting stage \${index + 1}: \${stage.name}\`);
      }
      
      currentStream = this.debugStage(
        stage.transformer(currentStream),
        \`Stage\${index + 1}-\${stage.name}\`
      );
    }
    
    yield* currentStream;
  }

  debugStage(asyncIterable, stageName) {
    if (!this.debugMode) {
      return asyncIterable;
    }
    
    return {
      async *[Symbol.asyncIterator]() {
        let count = 0;
        const startTime = Date.now();
        
        try {
          for await (const item of asyncIterable) {
            count++;
            console.log(\`[\${stageName}] Processing item \${count}:\`, item);
            yield item;
          }
        } catch (error) {
          console.error(\`[\${stageName}] Error at item \${count}:\`, error);
          throw error;
        } finally {
          const elapsed = Date.now() - startTime;
          console.log(\`[\${stageName}] Completed \${count} items in \${elapsed}ms\`);
        }
      }
    };
  }
}

// 使用流水线调试
const pipeline = new AsyncIteratorPipeline()
  .enableDebug()
  .addStage('Filter', async function*(source) {
    for await (const item of source) {
      if (item.includes('important')) {
        yield item;
      }
    }
  })
  .addStage('Transform', async function*(source) {
    for await (const item of source) {
      yield item.toUpperCase();
    }
  })
  .addStage('Batch', async function*(source) {
    const batch = [];
    for await (const item of source) {
      batch.push(item);
      if (batch.length >= 3) {
        yield batch.slice();
        batch.length = 0;
      }
    }
    if (batch.length > 0) {
      yield batch;
    }
  });

// 处理数据
for await (const result of pipeline.process(dataSource())) {
  console.log('Final result:', result);
}
    `,
    category: '流程调试',
    difficulty: 'medium'
  },

  {
    id: 'backpressure-debugging',
    title: '调试背压控制和内存问题',
    problem: '异步迭代器的背压控制失效，导致内存溢出或性能问题',
    solution: `
调试背压控制需要监控生产者和消费者的速度差异：

1. **监控缓冲区状态**：跟踪缓冲区的大小变化
2. **测量生产和消费速率**：识别速度不匹配的问题
3. **内存使用监控**：检测内存泄漏和过度使用
4. **流量控制调试**：验证背压机制是否正常工作
    `,
    code: `
// 背压控制调试器
class BackpressureDebugger {
  constructor(options = {}) {
    this.maxBufferSize = options.maxBufferSize || 100;
    this.monitorInterval = options.monitorInterval || 1000;
    this.buffer = [];
    this.stats = {
      produced: 0,
      consumed: 0,
      maxBufferSize: 0,
      backpressureEvents: 0,
      memoryUsage: []
    };
    this.isProducing = true;
    this.waitingConsumers = [];
    this.waitingProducers = [];
    
    // 定期监控
    this.monitorTimer = setInterval(() => {
      this.logStats();
    }, this.monitorInterval);
  }

  async *createStream(producer) {
    // 启动生产者
    this.startProducer(producer);
    
    try {
      while (this.isProducing || this.buffer.length > 0) {
        // 等待数据可用
        if (this.buffer.length === 0 && this.isProducing) {
          await this.waitForData();
        }
        
        if (this.buffer.length > 0) {
          const item = this.buffer.shift();
          this.stats.consumed++;
          
          // 通知等待的生产者
          this.notifyProducers();
          
          yield item;
        } else if (!this.isProducing) {
          break;
        }
      }
    } finally {
      clearInterval(this.monitorTimer);
      this.logFinalStats();
    }
  }

  async startProducer(producer) {
    try {
      for await (const item of producer) {
        // 检查缓冲区是否已满
        while (this.buffer.length >= this.maxBufferSize) {
          console.warn(\`🚨 Backpressure activated! Buffer size: \${this.buffer.length}\`);
          this.stats.backpressureEvents++;
          await this.waitForBufferSpace();
        }
        
        this.buffer.push(item);
        this.stats.produced++;
        this.stats.maxBufferSize = Math.max(this.stats.maxBufferSize, this.buffer.length);
        
        // 通知等待的消费者
        this.notifyConsumers();
      }
    } catch (error) {
      console.error('Producer error:', error);
    } finally {
      this.isProducing = false;
      this.notifyConsumers();
    }
  }

  waitForData() {
    return new Promise(resolve => {
      this.waitingConsumers.push(resolve);
    });
  }

  waitForBufferSpace() {
    return new Promise(resolve => {
      this.waitingProducers.push(resolve);
    });
  }

  notifyConsumers() {
    while (this.waitingConsumers.length > 0 && 
           (this.buffer.length > 0 || !this.isProducing)) {
      const resolve = this.waitingConsumers.shift();
      resolve();
    }
  }

  notifyProducers() {
    while (this.waitingProducers.length > 0 && 
           this.buffer.length < this.maxBufferSize) {
      const resolve = this.waitingProducers.shift();
      resolve();
    }
  }

  logStats() {
    const memUsage = process.memoryUsage();
    this.stats.memoryUsage.push({
      timestamp: Date.now(),
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal
    });
    
    const productionRate = this.stats.produced / (Date.now() / 1000);
    const consumptionRate = this.stats.consumed / (Date.now() / 1000);
    
    console.log(\`📊 Backpressure Stats:\`, {
      bufferSize: this.buffer.length,
      maxBufferSize: this.stats.maxBufferSize,
      produced: this.stats.produced,
      consumed: this.stats.consumed,
      productionRate: \`\${productionRate.toFixed(2)}/sec\`,
      consumptionRate: \`\${consumptionRate.toFixed(2)}/sec\`,
      backpressureEvents: this.stats.backpressureEvents,
      heapUsed: \`\${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB\`
    });
  }

  logFinalStats() {
    console.log(\`🏁 Final Backpressure Report:\`, {
      totalProduced: this.stats.produced,
      totalConsumed: this.stats.consumed,
      maxBufferSize: this.stats.maxBufferSize,
      backpressureEvents: this.stats.backpressureEvents,
      memoryPeak: Math.max(...this.stats.memoryUsage.map(m => m.heapUsed))
    });
  }
}

// 内存泄漏检测器
class MemoryLeakDetector {
  constructor(threshold = 100 * 1024 * 1024) { // 100MB
    this.threshold = threshold;
    this.samples = [];
    this.isMonitoring = false;
  }

  startMonitoring(interval = 5000) {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.monitorTimer = setInterval(() => {
      const memUsage = process.memoryUsage();
      this.samples.push({
        timestamp: Date.now(),
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external
      });
      
      // 保留最近20个样本
      if (this.samples.length > 20) {
        this.samples.shift();
      }
      
      this.checkForLeaks();
    }, interval);
  }

  stopMonitoring() {
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer);
      this.isMonitoring = false;
    }
  }

  checkForLeaks() {
    if (this.samples.length < 5) return;
    
    const recent = this.samples.slice(-5);
    const trend = this.calculateTrend(recent.map(s => s.heapUsed));
    
    if (trend > this.threshold / 5) { // 如果内存增长趋势超过阈值的1/5
      console.warn(\`🚨 Potential memory leak detected!\`, {
        currentHeap: \`\${(recent[recent.length - 1].heapUsed / 1024 / 1024).toFixed(2)}MB\`,
        trend: \`+\${(trend / 1024 / 1024).toFixed(2)}MB/sample\`,
        samples: recent.length
      });
    }
  }

  calculateTrend(values) {
    if (values.length < 2) return 0;
    
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = values.reduce((sum, y, x) => sum + x * y, 0);
    const sumX2 = values.reduce((sum, _, x) => sum + x * x, 0);
    
    return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
  }
}

// 使用示例
async function debugBackpressureExample() {
  // 快速生产者
  async function* fastProducer() {
    for (let i = 1; i <= 1000; i++) {
      await new Promise(resolve => setTimeout(resolve, 10)); // 快速生产
      yield \`item-\${i}\`;
    }
  }

  const debugger = new BackpressureDebugger({
    maxBufferSize: 10,
    monitorInterval: 2000
  });

  const leakDetector = new MemoryLeakDetector();
  leakDetector.startMonitoring();

  try {
    for await (const item of debugger.createStream(fastProducer())) {
      // 慢速消费
      await new Promise(resolve => setTimeout(resolve, 100));
      console.log('Processed:', item);
    }
  } finally {
    leakDetector.stopMonitoring();
  }
}
    `,
    category: '内存调试',
    difficulty: 'hard'
  },

  {
    id: 'error-propagation-debugging',
    title: '调试异步迭代器中的错误传播',
    problem: '异步迭代器中的错误传播复杂，难以定位错误源和处理错误恢复',
    solution: `
调试异步迭代器的错误处理需要理解错误传播机制：

1. **错误源追踪**：标识错误发生的具体位置
2. **错误传播路径**：跟踪错误在迭代器链中的传播
3. **错误恢复机制**：实现和测试错误恢复逻辑
4. **错误聚合分析**：收集和分析错误模式
    `,
    code: `
// 错误追踪的异步迭代器包装器
class ErrorTrackingIterator {
  constructor(source, name = 'Iterator') {
    this.source = source;
    this.name = name;
    this.errorHistory = [];
    this.recoveryAttempts = 0;
    this.maxRecoveryAttempts = 3;
  }

  async *[Symbol.asyncIterator]() {
    const iterator = this.source[Symbol.asyncIterator]();
    let itemCount = 0;
    
    while (true) {
      try {
        const result = await iterator.next();
        
        if (result.done) {
          this.logErrorSummary();
          break;
        }
        
        itemCount++;
        yield result.value;
        
        // 重置恢复计数器（成功处理后）
        this.recoveryAttempts = 0;
        
      } catch (error) {
        const errorInfo = {
          timestamp: new Date().toISOString(),
          itemCount,
          error: error.message,
          stack: error.stack,
          recoveryAttempt: this.recoveryAttempts + 1
        };
        
        this.errorHistory.push(errorInfo);
        
        console.error(\`[\${this.name}] Error at item \${itemCount}:\`, {
          message: error.message,
          recoveryAttempt: errorInfo.recoveryAttempt,
          maxAttempts: this.maxRecoveryAttempts
        });
        
        // 尝试错误恢复
        if (this.recoveryAttempts < this.maxRecoveryAttempts) {
          this.recoveryAttempts++;
          console.log(\`[\${this.name}] Attempting recovery \${this.recoveryAttempts}/\${this.maxRecoveryAttempts}\`);
          
          // 等待一段时间后重试
          await new Promise(resolve => 
            setTimeout(resolve, 1000 * this.recoveryAttempts)
          );
          
          continue; // 重试
        } else {
          console.error(\`[\${this.name}] Max recovery attempts reached, propagating error\`);
          throw error;
        }
      }
    }
  }

  logErrorSummary() {
    if (this.errorHistory.length > 0) {
      console.log(\`[\${this.name}] Error Summary:\`, {
        totalErrors: this.errorHistory.length,
        errorTypes: this.getErrorTypes(),
        recoverySuccessRate: this.calculateRecoveryRate()
      });
    }
  }

  getErrorTypes() {
    const types = {};
    this.errorHistory.forEach(error => {
      const type = error.error.split(':')[0];
      types[type] = (types[type] || 0) + 1;
    });
    return types;
  }

  calculateRecoveryRate() {
    const recoveredErrors = this.errorHistory.filter(
      error => error.recoveryAttempt <= this.maxRecoveryAttempts
    ).length;
    return this.errorHistory.length > 0 
      ? (recoveredErrors / this.errorHistory.length * 100).toFixed(2) + '%'
      : '0%';
  }
}

// 错误注入器（用于测试）
class ErrorInjector {
  constructor(source, errorRate = 0.1, errorTypes = ['network', 'timeout', 'parse']) {
    this.source = source;
    this.errorRate = errorRate;
    this.errorTypes = errorTypes;
    this.itemCount = 0;
  }

  async *[Symbol.asyncIterator]() {
    for await (const item of this.source) {
      this.itemCount++;
      
      // 随机注入错误
      if (Math.random() < this.errorRate) {
        const errorType = this.errorTypes[
          Math.floor(Math.random() * this.errorTypes.length)
        ];
        
        console.log(\`💥 Injecting \${errorType} error at item \${this.itemCount}\`);
        
        switch (errorType) {
          case 'network':
            throw new Error(\`Network error: Connection failed at item \${this.itemCount}\`);
          case 'timeout':
            throw new Error(\`Timeout error: Request timed out at item \${this.itemCount}\`);
          case 'parse':
            throw new Error(\`Parse error: Invalid data format at item \${this.itemCount}\`);
          default:
            throw new Error(\`Unknown error at item \${this.itemCount}\`);
        }
      }
      
      yield item;
    }
  }
}

// 错误聚合分析器
class ErrorAnalyzer {
  constructor() {
    this.errors = [];
    this.patterns = new Map();
  }

  recordError(source, error, context = {}) {
    const errorRecord = {
      timestamp: Date.now(),
      source,
      message: error.message,
      type: this.classifyError(error),
      context,
      stack: error.stack
    };
    
    this.errors.push(errorRecord);
    this.updatePatterns(errorRecord);
  }

  classifyError(error) {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('connection')) {
      return 'network';
    } else if (message.includes('timeout')) {
      return 'timeout';
    } else if (message.includes('parse') || message.includes('json')) {
      return 'parse';
    } else {
      return 'unknown';
    }
  }

  updatePatterns(errorRecord) {
    const key = \`\${errorRecord.source}:\${errorRecord.type}\`;
    const pattern = this.patterns.get(key) || {
      count: 0,
      firstSeen: errorRecord.timestamp,
      lastSeen: errorRecord.timestamp,
      frequency: 0
    };
    
    pattern.count++;
    pattern.lastSeen = errorRecord.timestamp;
    pattern.frequency = pattern.count / (pattern.lastSeen - pattern.firstSeen + 1) * 1000; // per second
    
    this.patterns.set(key, pattern);
  }

  generateReport() {
    const report = {
      totalErrors: this.errors.length,
      errorsByType: this.getErrorsByType(),
      errorsBySource: this.getErrorsBySource(),
      topPatterns: this.getTopPatterns(),
      timeDistribution: this.getTimeDistribution()
    };
    
    console.log('📊 Error Analysis Report:', report);
    return report;
  }

  getErrorsByType() {
    const types = {};
    this.errors.forEach(error => {
      types[error.type] = (types[error.type] || 0) + 1;
    });
    return types;
  }

  getErrorsBySource() {
    const sources = {};
    this.errors.forEach(error => {
      sources[error.source] = (sources[error.source] || 0) + 1;
    });
    return sources;
  }

  getTopPatterns() {
    return Array.from(this.patterns.entries())
      .sort((a, b) => b[1].count - a[1].count)
      .slice(0, 5)
      .map(([key, pattern]) => ({ pattern: key, ...pattern }));
  }

  getTimeDistribution() {
    if (this.errors.length === 0) return {};
    
    const timeSpan = this.errors[this.errors.length - 1].timestamp - this.errors[0].timestamp;
    const buckets = 10;
    const bucketSize = timeSpan / buckets;
    const distribution = new Array(buckets).fill(0);
    
    this.errors.forEach(error => {
      const bucket = Math.floor((error.timestamp - this.errors[0].timestamp) / bucketSize);
      if (bucket < buckets) {
        distribution[bucket]++;
      }
    });
    
    return distribution;
  }
}

// 使用示例
async function debugErrorPropagationExample() {
  // 数据源
  async function* dataSource() {
    for (let i = 1; i <= 50; i++) {
      await new Promise(resolve => setTimeout(resolve, 100));
      yield \`data-\${i}\`;
    }
  }

  const analyzer = new ErrorAnalyzer();
  
  // 创建错误处理管道
  const errorInjector = new ErrorInjector(dataSource(), 0.2); // 20%错误率
  const errorTracker = new ErrorTrackingIterator(errorInjector, 'TestIterator');

  try {
    for await (const item of errorTracker) {
      console.log('Successfully processed:', item);
    }
  } catch (finalError) {
    analyzer.recordError('TestIterator', finalError, { 
      phase: 'final',
      recoveryAttempts: errorTracker.recoveryAttempts 
    });
    console.error('Final error:', finalError.message);
  } finally {
    analyzer.generateReport();
  }
}
    `,
    category: '错误调试',
    difficulty: 'hard'
  }
];

export default debuggingTips;
