import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: '异步迭代器与普通迭代器有什么区别？for-await-of与for-of的区别是什么？',
    answer: {
      brief: '异步迭代器处理异步数据流，next()方法返回Promise，for-await-of自动处理Promise的串行解析。',
      detailed: `
**核心区别：**

**迭代器接口差异：**
- 普通迭代器：\`next(): IteratorResult<T>\`
- 异步迭代器：\`next(): Promise<IteratorResult<T>>\`

**协议Symbol差异：**
- 普通迭代器：\`Symbol.iterator\`
- 异步迭代器：\`Symbol.asyncIterator\`

**语法差异：**
- for-of：同步遍历，立即获取值
- for-await-of：异步遍历，等待Promise解析

**执行模式：**
- for-of：同步执行，阻塞式
- for-await-of：串行异步执行，非阻塞

**使用场景：**
- 普通迭代器：处理同步数据集合
- 异步迭代器：处理异步数据流、网络请求、文件读取等
      `,
      code: `
// 普通迭代器
const syncIterable = {
  [Symbol.iterator]() {
    let count = 0;
    return {
      next() {
        return count < 3 
          ? { value: count++, done: false }
          : { done: true };
      }
    };
  }
};

for (const item of syncIterable) {
  console.log(item); // 0, 1, 2 (同步输出)
}

// 异步迭代器
const asyncIterable = {
  [Symbol.asyncIterator]() {
    let count = 0;
    return {
      async next() {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return count < 3 
          ? { value: count++, done: false }
          : { done: true };
      }
    };
  }
};

for await (const item of asyncIterable) {
  console.log(item); // 0, 1, 2 (每秒输出一个)
}

// 异步生成器函数
async function* asyncGenerator() {
  for (let i = 0; i < 3; i++) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    yield i;
  }
}

for await (const item of asyncGenerator()) {
  console.log(item); // 0, 1, 2 (每秒输出一个)
}
      `
    },
    difficulty: 'medium',
    frequency: 'high',
    category: '基础概念'
  },

  {
    id: 2,
    question: 'for-await-of是并行执行还是串行执行？如何实现并行处理异步迭代器？',
    answer: {
      brief: 'for-await-of是串行执行的，每次等待一个Promise完成后再处理下一个。并行处理需要使用其他方法。',
      detailed: `
**for-await-of的执行模式：**
for-await-of采用串行执行模式，这是设计上的选择，确保：
1. **处理顺序的可预测性**
2. **背压控制的自然实现**
3. **错误处理的简化**
4. **资源使用的可控性**

**并行处理的实现方法：**

1. **Promise.all + 数组处理**
2. **手动管理多个异步迭代器**
3. **使用工作池模式**
4. **流式并行处理**

**选择串行vs并行的考虑：**
- 串行：顺序保证、背压控制、简单错误处理
- 并行：更高吞吐量、更好性能、复杂的协调逻辑
      `,
      code: `
// for-await-of串行执行演示
async function* slowAsyncGenerator() {
  for (let i = 1; i <= 3; i++) {
    console.log(\`Generating \${i} at \${new Date().toLocaleTimeString()}\`);
    await new Promise(resolve => setTimeout(resolve, 1000));
    yield i;
  }
}

// 串行处理（默认行为）
console.log('Serial processing:');
for await (const item of slowAsyncGenerator()) {
  console.log(\`Processing \${item} at \${new Date().toLocaleTimeString()}\`);
  await new Promise(resolve => setTimeout(resolve, 500)); // 模拟处理时间
}

// 并行处理方法1：Promise.all + 数组
async function parallelWithArray() {
  console.log('Parallel processing with array:');
  const items = [];
  for await (const item of slowAsyncGenerator()) {
    items.push(item);
  }
  
  // 并行处理所有项目
  const results = await Promise.all(
    items.map(async item => {
      console.log(\`Processing \${item} at \${new Date().toLocaleTimeString()}\`);
      await new Promise(resolve => setTimeout(resolve, 500));
      return item * 2;
    })
  );
  
  console.log('Results:', results);
}

// 并行处理方法2：工作池模式
class AsyncIteratorPool {
  constructor(concurrency = 3) {
    this.concurrency = concurrency;
    this.active = 0;
    this.queue = [];
  }

  async process(asyncIterable, processor) {
    const results = [];
    const iterator = asyncIterable[Symbol.asyncIterator]();
    
    const workers = Array(this.concurrency).fill().map(async () => {
      let result;
      while (!(result = await iterator.next()).done) {
        const processed = await processor(result.value);
        results.push(processed);
      }
    });
    
    await Promise.all(workers);
    return results;
  }
}

// 使用工作池
const pool = new AsyncIteratorPool(2);
const results = await pool.process(
  slowAsyncGenerator(),
  async (item) => {
    console.log(\`Processing \${item} at \${new Date().toLocaleTimeString()}\`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return item * 2;
  }
);

// 并行处理方法3：手动管理多个迭代器
async function parallelIterators() {
  const iterators = [
    slowAsyncGenerator(),
    slowAsyncGenerator(),
    slowAsyncGenerator()
  ].map(gen => gen[Symbol.asyncIterator]());
  
  const activePromises = new Map();
  
  // 启动所有迭代器
  iterators.forEach((iterator, index) => {
    activePromises.set(index, iterator.next());
  });
  
  while (activePromises.size > 0) {
    const results = await Promise.allSettled(
      Array.from(activePromises.values())
    );
    
    const indices = Array.from(activePromises.keys());
    
    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      const index = indices[i];
      
      if (result.status === 'fulfilled') {
        const { value, done } = result.value;
        
        if (done) {
          activePromises.delete(index);
        } else {
          console.log(\`Iterator \${index} produced: \${value}\`);
          // 启动下一次迭代
          activePromises.set(index, iterators[index].next());
        }
      } else {
        console.error(\`Iterator \${index} error:\`, result.reason);
        activePromises.delete(index);
      }
    }
  }
}
      `
    },
    difficulty: 'hard',
    frequency: 'high',
    category: '执行模式'
  },

  {
    id: 3,
    question: '如何实现一个支持背压控制的异步迭代器？什么是背压控制？',
    answer: {
      brief: '背压控制是指消费者速度控制生产者的数据生成速度。异步迭代器天然支持背压控制，因为只有在调用next()时才生成下一个值。',
      detailed: `
**背压控制的概念：**
背压（Backpressure）是流处理中的重要概念，指当数据消费速度跟不上生产速度时，系统自动调节生产速度的机制。

**异步迭代器的背压控制：**
1. **拉取模式**：消费者主动请求数据
2. **惰性生成**：只在需要时生成数据
3. **自然流控**：消费速度自动控制生产速度

**实现背压控制的关键：**
- 生产者等待消费者的next()调用
- 避免无限制的数据缓冲
- 提供缓冲区大小控制
- 支持暂停和恢复机制
      `,
      code: `
// 支持背压控制的异步迭代器实现
class BackpressureAsyncIterator {
  constructor(dataSource, options = {}) {
    this.dataSource = dataSource;
    this.bufferSize = options.bufferSize || 10;
    this.buffer = [];
    this.isProducing = false;
    this.isFinished = false;
    this.pendingResolvers = [];
    this.error = null;
    
    // 开始生产数据
    this.startProducing();
  }

  [Symbol.asyncIterator]() {
    return this;
  }

  async next() {
    // 如果有错误，抛出错误
    if (this.error) {
      throw this.error;
    }

    // 如果缓冲区有数据，直接返回
    if (this.buffer.length > 0) {
      const value = this.buffer.shift();
      
      // 如果缓冲区有空间且还在生产，继续生产
      if (this.buffer.length < this.bufferSize && this.isProducing) {
        this.resumeProducing();
      }
      
      return { value, done: false };
    }

    // 如果已经完成，返回done
    if (this.isFinished) {
      return { done: true };
    }

    // 等待新数据
    return new Promise((resolve, reject) => {
      this.pendingResolvers.push({ resolve, reject });
      
      // 如果缓冲区有空间，恢复生产
      if (this.buffer.length < this.bufferSize) {
        this.resumeProducing();
      }
    });
  }

  async startProducing() {
    this.isProducing = true;
    
    try {
      for await (const item of this.dataSource) {
        // 检查缓冲区是否已满
        while (this.buffer.length >= this.bufferSize && this.pendingResolvers.length === 0) {
          // 背压控制：暂停生产，等待消费
          await this.waitForConsumption();
        }

        this.buffer.push(item);
        this.notifyWaitingConsumers();
      }
    } catch (error) {
      this.error = error;
      this.rejectWaitingConsumers(error);
    } finally {
      this.isFinished = true;
      this.isProducing = false;
      this.notifyWaitingConsumers();
    }
  }

  waitForConsumption() {
    return new Promise(resolve => {
      this.productionResumeResolver = resolve;
    });
  }

  resumeProducing() {
    if (this.productionResumeResolver) {
      this.productionResumeResolver();
      this.productionResumeResolver = null;
    }
  }

  notifyWaitingConsumers() {
    while (this.pendingResolvers.length > 0 && 
           (this.buffer.length > 0 || this.isFinished)) {
      const { resolve } = this.pendingResolvers.shift();
      
      if (this.buffer.length > 0) {
        const value = this.buffer.shift();
        resolve({ value, done: false });
      } else if (this.isFinished) {
        resolve({ done: true });
      }
    }
  }

  rejectWaitingConsumers(error) {
    while (this.pendingResolvers.length > 0) {
      const { reject } = this.pendingResolvers.shift();
      reject(error);
    }
  }
}

// 使用示例：模拟慢速数据源
async function* slowDataSource() {
  for (let i = 1; i <= 20; i++) {
    console.log(\`Producing data \${i}\`);
    await new Promise(resolve => setTimeout(resolve, 100)); // 快速生产
    yield \`data-\${i}\`;
  }
}

// 使用背压控制的迭代器
async function demonstrateBackpressure() {
  const iterator = new BackpressureAsyncIterator(slowDataSource(), {
    bufferSize: 3 // 小缓冲区演示背压效果
  });

  for await (const item of iterator) {
    console.log(\`Consuming: \${item}\`);
    // 模拟慢速消费
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

// 高级背压控制：可配置的流控制器
class FlowController {
  constructor(options = {}) {
    this.maxConcurrency = options.maxConcurrency || 5;
    this.bufferSize = options.bufferSize || 10;
    this.activeTasks = 0;
    this.buffer = [];
    this.waitingProducers = [];
    this.waitingConsumers = [];
  }

  async* controlledStream(sourceIterator, processor) {
    const iterator = sourceIterator[Symbol.asyncIterator]();
    let sourceFinished = false;
    
    // 启动生产者
    this.startProducer(iterator).then(() => {
      sourceFinished = true;
      this.notifyConsumers();
    });

    // 消费数据
    while (!sourceFinished || this.buffer.length > 0 || this.activeTasks > 0) {
      // 等待数据可用
      if (this.buffer.length === 0 && !sourceFinished) {
        await this.waitForData();
      }

      if (this.buffer.length > 0) {
        const item = this.buffer.shift();
        this.notifyProducers(); // 通知生产者可以继续
        
        // 处理数据
        this.activeTasks++;
        try {
          const result = await processor(item);
          yield result;
        } finally {
          this.activeTasks--;
        }
      } else if (sourceFinished && this.activeTasks === 0) {
        break;
      }
    }
  }

  async startProducer(iterator) {
    try {
      let result;
      while (!(result = await iterator.next()).done) {
        // 检查缓冲区是否已满
        while (this.buffer.length >= this.bufferSize) {
          await this.waitForBufferSpace();
        }
        
        this.buffer.push(result.value);
        this.notifyConsumers();
      }
    } catch (error) {
      console.error('Producer error:', error);
    }
  }

  waitForData() {
    return new Promise(resolve => {
      this.waitingConsumers.push(resolve);
    });
  }

  waitForBufferSpace() {
    return new Promise(resolve => {
      this.waitingProducers.push(resolve);
    });
  }

  notifyConsumers() {
    while (this.waitingConsumers.length > 0 && this.buffer.length > 0) {
      const resolve = this.waitingConsumers.shift();
      resolve();
    }
  }

  notifyProducers() {
    while (this.waitingProducers.length > 0 && this.buffer.length < this.bufferSize) {
      const resolve = this.waitingProducers.shift();
      resolve();
    }
  }
}

// 使用流控制器
const flowController = new FlowController({
  maxConcurrency: 3,
  bufferSize: 5
});

for await (const result of flowController.controlledStream(
  slowDataSource(),
  async (item) => {
    console.log(\`Processing: \${item}\`);
    await new Promise(resolve => setTimeout(resolve, 200));
    return \`processed-\${item}\`;
  }
)) {
  console.log(\`Result: \${result}\`);
}
      `
    },
    difficulty: 'hard',
    frequency: 'medium',
    category: '流控制'
  }
];

export default interviewQuestions;
