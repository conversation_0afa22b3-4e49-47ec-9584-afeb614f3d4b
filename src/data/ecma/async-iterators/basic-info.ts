import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `Async Iterators（异步迭代器）是ES2018引入的异步编程特性，扩展了ES6的迭代器协议到异步场景。它包含异步迭代器接口、for-await-of语法和异步生成器函数，为处理异步数据流提供了标准化的解决方案。异步迭代器让开发者能够以同步的语法处理异步的数据序列，是现代JavaScript异步编程和流式数据处理的重要基础设施。`,

  introduction: `在ES2018之前，处理异步数据流是一个复杂的任务。开发者需要手动管理Promise链、回调函数或使用复杂的异步控制流库。当需要处理来自网络请求、文件读取、数据库查询等异步数据源的序列时，代码往往变得复杂且难以维护。

异步迭代器的引入彻底改变了这种状况。它提供了一种统一的方式来处理异步数据流，让开发者能够使用熟悉的迭代语法来处理异步数据。for-await-of语法让异步数据的遍历变得如同同步数据一样简单直观。

这个特性不仅简化了代码，更重要的是它为JavaScript带来了强大的流式数据处理能力。无论是处理大文件、实时数据流、分页API响应，还是构建数据处理管道，异步迭代器都提供了优雅而高效的解决方案。`,

  syntax: `
// 异步迭代器接口
interface AsyncIterator<T> {
  next(): Promise<IteratorResult<T>>;
  return?(value?: any): Promise<IteratorResult<T>>;
  throw?(e?: any): Promise<IteratorResult<T>>;
}

// 异步可迭代接口
interface AsyncIterable<T> {
  [Symbol.asyncIterator](): AsyncIterator<T>;
}

// for-await-of语法
for await (const value of asyncIterable) {
  // 处理异步值
}

// 异步生成器函数
async function* asyncGenerator() {
  yield await asyncOperation();
}

// 自定义异步迭代器
const customAsyncIterable = {
  [Symbol.asyncIterator]() {
    return {
      async next() {
        return { value: await getData(), done: false };
      }
    };
  }
};
  `,

  quickExample: `
// 快速示例：处理分页API数据
async function* fetchAllPages(apiUrl) {
  let page = 1;
  let hasMore = true;
  
  while (hasMore) {
    const response = await fetch(\`\${apiUrl}?page=\${page}\`);
    const data = await response.json();
    
    // 逐个yield数据项
    for (const item of data.items) {
      yield item;
    }
    
    hasMore = data.hasNextPage;
    page++;
  }
}

// 使用for-await-of处理所有数据
async function processAllData() {
  for await (const item of fetchAllPages('/api/data')) {
    console.log('Processing:', item);
    await processItem(item);
  }
  console.log('All data processed!');
}

// 异步数据转换管道
async function* transformData(source) {
  for await (const item of source) {
    const transformed = await transform(item);
    if (transformed) {
      yield transformed;
    }
  }
}
  `,

  keyFeatures: [
    {
      feature: 'for-await-of语法',
      description: '提供简洁的异步数据遍历语法，让异步迭代如同同步迭代一样直观',
      importance: 'critical',
      details: 'for await (const item of asyncIterable) 自动处理Promise的解析和错误传播'
    },
    {
      feature: '异步生成器函数',
      description: '使用async function*语法创建异步生成器，支持yield await模式',
      importance: 'critical',
      details: '结合了生成器的惰性求值和async/await的异步处理能力'
    },
    {
      feature: 'Symbol.asyncIterator',
      description: '定义异步迭代器协议的标准Symbol，类似于Symbol.iterator',
      importance: 'high',
      details: '对象实现[Symbol.asyncIterator]方法即可成为异步可迭代对象'
    },
    {
      feature: '流式数据处理',
      description: '支持处理无限或大量的异步数据流，内存效率高',
      importance: 'high',
      details: '数据按需生成和处理，避免一次性加载大量数据到内存'
    },
    {
      feature: '错误处理机制',
      description: '提供完整的异步错误处理，支持try-catch和错误传播',
      importance: 'high',
      details: '异步迭代器中的错误会自动传播到for-await-of循环'
    },
    {
      feature: '背压控制',
      description: '天然支持背压控制，消费者速度控制生产者的数据生成',
      importance: 'medium',
      details: '迭代器的惰性特性提供了自然的流量控制机制'
    }
  ],

  commonUseCases: [
    {
      title: '分页API数据处理',
      description: '处理需要多次请求的分页API数据，自动处理分页逻辑',
      code: `
async function* fetchAllUsers() {
  let nextUrl = '/api/users?page=1';
  
  while (nextUrl) {
    const response = await fetch(nextUrl);
    const data = await response.json();
    
    for (const user of data.users) {
      yield user;
    }
    
    nextUrl = data.nextPage;
  }
}

// 处理所有用户
for await (const user of fetchAllUsers()) {
  await updateUserProfile(user);
}
      `
    },
    {
      title: '文件流处理',
      description: '逐行读取和处理大文件，避免内存溢出',
      code: `
async function* readFileLines(filePath) {
  const fileHandle = await fs.open(filePath, 'r');
  const stream = fileHandle.createReadStream();
  
  let buffer = '';
  for await (const chunk of stream) {
    buffer += chunk;
    const lines = buffer.split('\\n');
    buffer = lines.pop(); // 保留不完整的行
    
    for (const line of lines) {
      yield line;
    }
  }
  
  if (buffer) yield buffer; // 处理最后一行
  await fileHandle.close();
}

// 处理大文件
for await (const line of readFileLines('large-file.txt')) {
  await processLine(line);
}
      `
    },
    {
      title: '实时数据流',
      description: '处理WebSocket、Server-Sent Events等实时数据流',
      code: `
async function* listenToWebSocket(url) {
  const ws = new WebSocket(url);
  
  const messageQueue = [];
  let resolver = null;
  
  ws.onmessage = (event) => {
    if (resolver) {
      resolver({ value: JSON.parse(event.data), done: false });
      resolver = null;
    } else {
      messageQueue.push(JSON.parse(event.data));
    }
  };
  
  try {
    while (ws.readyState !== WebSocket.CLOSED) {
      if (messageQueue.length > 0) {
        yield messageQueue.shift();
      } else {
        yield await new Promise(resolve => {
          resolver = resolve;
        });
      }
    }
  } finally {
    ws.close();
  }
}

// 处理实时消息
for await (const message of listenToWebSocket('ws://localhost:8080')) {
  await handleMessage(message);
}
      `
    }
  ],

  bestPractices: [
    '使用异步生成器函数创建异步迭代器，语法更简洁',
    '在for-await-of循环中使用try-catch处理异步错误',
    '合理使用yield*委托给其他异步迭代器',
    '注意异步迭代器的资源清理，实现return方法',
    '避免在异步迭代器中创建过多的并发Promise',
    '使用背压控制避免内存溢出',
    '结合AbortController实现可取消的异步迭代'
  ],

  warnings: [
    'for-await-of会串行处理异步操作，不是并行执行',
    '异步迭代器中的错误会中断整个迭代过程',
    '需要注意异步迭代器的资源清理和内存泄漏',
    '在浏览器中使用时需要考虑兼容性',
    '异步生成器函数的性能开销比普通函数高'
  ],

  notes: [
    'Async Iterators是ES2018的重要特性，为异步编程提供了新的抽象',
    'for-await-of语法让异步数据处理变得直观和简洁',
    '异步迭代器天然支持背压控制和流式处理',
    '可以与现有的Promise、async/await无缝集成',
    '为构建复杂的异步数据处理管道提供了基础'
  ],

  mermaidDiagram: `
graph TD
    A[数据源] --> B[异步迭代器]
    B --> C{for-await-of}
    C --> D[next()调用]
    D --> E[Promise等待]
    E --> F{数据可用?}
    F -->|是| G[yield值]
    F -->|否| H[done: true]
    G --> I[处理数据]
    I --> J[背压控制]
    J --> D
    H --> K[迭代结束]

    L[错误处理] --> M[错误传播]
    M --> N[try-catch]

    O[资源管理] --> P[finally清理]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style K fill:#ffebee
  `
};

export default basicInfo;
