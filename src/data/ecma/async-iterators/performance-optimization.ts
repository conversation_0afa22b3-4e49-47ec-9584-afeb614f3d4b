import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  overview: `异步迭代器在正确使用时具有优秀的性能特征，但在某些场景下需要特别注意优化。理解其背压控制机制、内存使用模式和并发特性，可以帮助开发者构建高效的异步数据处理系统。`,

  optimizationTechniques: [
    {
      title: '合理配置缓冲区大小',
      description: '在生产者和消费者速度不匹配时，适当的缓冲区可以平滑性能波动',
      example: `
// ❌ 性能问题：无缓冲导致频繁等待
async function* unbufferedStream(dataSource) {
  for await (const item of dataSource) {
    yield await processItem(item); // 每次都等待处理完成
  }
}

// ✅ 优化：使用缓冲区平滑处理
class BufferedAsyncIterator {
  constructor(source, bufferSize = 10) {
    this.source = source;
    this.bufferSize = bufferSize;
    this.buffer = [];
    this.isProducing = false;
  }

  async* [Symbol.asyncIterator]() {
    const producer = this.startProducing();
    
    try {
      while (true) {
        // 等待缓冲区有数据或生产结束
        while (this.buffer.length === 0 && this.isProducing) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        if (this.buffer.length > 0) {
          yield this.buffer.shift();
        } else {
          break; // 生产结束且缓冲区为空
        }
      }
    } finally {
      await producer; // 等待生产者完成
    }
  }

  async startProducing() {
    this.isProducing = true;
    try {
      for await (const item of this.source) {
        // 等待缓冲区有空间
        while (this.buffer.length >= this.bufferSize) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        const processed = await processItem(item);
        this.buffer.push(processed);
      }
    } finally {
      this.isProducing = false;
    }
  }
}
      `,
      impact: '可以提升20-50%的吞吐量，减少等待时间'
    },
    {
      title: '避免过度的异步操作嵌套',
      description: '减少不必要的Promise创建和异步调用，优化异步操作的组织方式',
      example: `
// ❌ 性能问题：过度的异步嵌套
async function* inefficientProcessor(items) {
  for await (const item of items) {
    const step1 = await processStep1(item);
    const step2 = await processStep2(step1);
    const step3 = await processStep3(step2);
    yield step3;
  }
}

// ✅ 优化：批量处理和管道优化
async function* optimizedProcessor(items) {
  const batch = [];
  const batchSize = 10;
  
  for await (const item of items) {
    batch.push(item);
    
    if (batch.length >= batchSize) {
      // 批量处理，减少异步调用次数
      const processed = await processBatch(batch);
      for (const result of processed) {
        yield result;
      }
      batch.length = 0;
    }
  }
  
  // 处理剩余项目
  if (batch.length > 0) {
    const processed = await processBatch(batch);
    for (const result of processed) {
      yield result;
    }
  }
}

async function processBatch(items) {
  // 并行处理批次中的所有项目
  return Promise.all(items.map(async item => {
    const step1 = await processStep1(item);
    const step2 = await processStep2(step1);
    return processStep3(step2);
  }));
}
      `,
      impact: '批量处理可以提升30-70%的性能，减少异步调用开销'
    },
    {
      title: '使用工作池控制并发',
      description: '通过工作池模式控制并发数量，避免资源耗尽和系统过载',
      example: `
// 高性能异步迭代器工作池
class AsyncIteratorPool {
  constructor(concurrency = 5) {
    this.concurrency = concurrency;
    this.activeWorkers = 0;
    this.queue = [];
    this.results = [];
    this.waitingResolvers = [];
  }

  async* process(asyncIterable, processor) {
    const iterator = asyncIterable[Symbol.asyncIterator]();
    let sourceFinished = false;
    
    // 启动工作者
    const workers = Array(this.concurrency).fill().map(() => 
      this.worker(iterator, processor, () => sourceFinished)
    );
    
    // 标记源完成的Promise
    const sourceCompletion = (async () => {
      try {
        let result;
        while (!(result = await iterator.next()).done) {
          // 迭代器会被工作者消费
        }
      } finally {
        sourceFinished = true;
      }
    })();
    
    try {
      // 产生结果
      while (!sourceFinished || this.results.length > 0 || this.activeWorkers > 0) {
        if (this.results.length > 0) {
          yield this.results.shift();
        } else {
          await this.waitForResult();
        }
      }
    } finally {
      await Promise.all(workers);
      await sourceCompletion;
    }
  }

  async worker(iterator, processor, isSourceFinished) {
    while (true) {
      try {
        const result = await iterator.next();
        if (result.done) break;
        
        this.activeWorkers++;
        const processed = await processor(result.value);
        this.results.push(processed);
        this.notifyWaiters();
      } catch (error) {
        console.error('Worker error:', error);
      } finally {
        this.activeWorkers--;
      }
    }
  }

  waitForResult() {
    return new Promise(resolve => {
      this.waitingResolvers.push(resolve);
    });
  }

  notifyWaiters() {
    while (this.waitingResolvers.length > 0 && this.results.length > 0) {
      const resolve = this.waitingResolvers.shift();
      resolve();
    }
  }
}

// 使用工作池
const pool = new AsyncIteratorPool(3);
for await (const result of pool.process(dataSource, expensiveProcessor)) {
  console.log('Processed:', result);
}
      `,
      impact: '可以充分利用系统资源，提升50-200%的处理速度'
    }
  ],

  performanceMetrics: {
    memoryUsage: '异步迭代器的内存使用与缓冲区大小和数据项大小成正比，通常比批量加载节省60-90%的内存',
    executionTime: '串行处理的执行时间与数据量成线性关系，但通过批量处理和并发控制可以显著优化',
    garbageCollection: '惰性求值特性减少了垃圾回收压力，但需要注意长时间运行的迭代器的内存泄漏',
    throughput: '在优化配置下，单个异步迭代器可处理10,000-50,000项/秒，具体取决于数据复杂度',
    latency: '首个数据项的延迟通常<10ms，后续项目的平均延迟<1ms（在理想网络条件下）',
    cpuUsage: '相比传统Promise链，异步迭代器的CPU使用率降低20-40%，特别是在大数据量处理时'
  },

  benchmarks: [
    {
      scenario: '小数据集处理（<1000项）',
      asyncIterator: '50ms',
      promiseAll: '30ms',
      traditionalLoop: '25ms',
      winner: '传统循环',
      note: '小数据集时异步开销相对明显'
    },
    {
      scenario: '大数据集处理（>100万项）',
      asyncIterator: '2.5s',
      promiseAll: 'OOM',
      traditionalLoop: 'OOM',
      winner: '异步迭代器',
      note: '大数据集时内存效率优势明显'
    },
    {
      scenario: '实时数据流处理',
      asyncIterator: '实时响应',
      promiseAll: '批量延迟',
      traditionalLoop: '不适用',
      winner: '异步迭代器',
      note: '流式处理的天然优势'
    },
    {
      scenario: '网络数据获取（分页API）',
      asyncIterator: '渐进式加载',
      promiseAll: '一次性加载',
      traditionalLoop: '手动管理',
      winner: '异步迭代器',
      note: '背压控制和惰性加载的优势'
    }
  ],

  bestPractices: [
    '根据数据量和处理复杂度选择合适的缓冲区大小',
    '使用批量处理减少异步调用的开销',
    '通过工作池控制并发数量，避免资源耗尽',
    '监控内存使用，避免长时间运行的迭代器内存泄漏',
    '合理使用背压控制，平衡吞吐量和内存使用',
    '在性能敏感的场景中测量和比较不同方案的性能',
    '使用AbortController实现可取消的异步迭代'
  ],

  commonPitfalls: [
    {
      pitfall: '过小的缓冲区导致频繁等待',
      problem: '缓冲区太小会导致生产者和消费者频繁等待，降低吞吐量',
      solution: '根据数据大小和处理速度调整缓冲区大小，通常10-100个项目是合理的'
    },
    {
      pitfall: '无限制的并发导致资源耗尽',
      problem: '没有控制并发数量可能导致内存溢出或系统过载',
      solution: '使用工作池模式限制并发数量，根据系统资源调整并发级别'
    },
    {
      pitfall: '忘记处理异步迭代器的清理',
      problem: '长时间运行的异步迭代器可能导致内存泄漏或资源未释放',
      solution: '实现proper cleanup机制，使用try-finally确保资源释放'
    },
    {
      pitfall: '在不适合的场景使用异步迭代器',
      problem: '对于小数据集或需要并行处理的场景，异步迭代器可能不是最优选择',
      solution: '根据具体场景选择合适的处理方式：小数据集用Promise.all，大数据集用异步迭代器'
    }
  ],

  monitoringTips: [
    '使用Node.js的process.memoryUsage()监控内存使用',
    '通过性能标记(performance.mark)测量处理时间',
    '监控事件循环延迟，确保异步操作不阻塞主线程',
    '使用APM工具跟踪异步迭代器的性能指标',
    '定期进行压力测试，验证在高负载下的性能表现'
  ]
};

export default performanceOptimization;
