import { ApiItem } from '@/types/api';

// 导入各个维度的数据
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const asyncIteratorsApi: ApiItem = {
  id: 'async-iterators',
  title: 'Async Iterators',
  description: 'ES2018引入的异步迭代器，提供了处理异步数据流的标准化方法，支持for-await-of语法、流式数据处理和异步生成器，是现代JavaScript异步编程的重要基础设施。',
  category: 'ECMAScript',
  difficulty: 'hard',
  syntax: `
// 异步迭代器接口
interface AsyncIterator<T> {
  next(): Promise<IteratorResult<T>>;
}

// for-await-of语法
for await (const item of asyncIterable) {
  console.log(item);
}

// 异步生成器函数
async function* asyncGenerator() {
  yield await fetchData();
  yield await fetchMoreData();
}

// 自定义异步迭代器
const asyncIterable = {
  [Symbol.asyncIterator]() {
    return this;
  },
  async next() {
    return { value: await getData(), done: false };
  }
};
  `,
  example: `
// 基础异步迭代器使用
async function* numberStream() {
  for (let i = 1; i <= 5; i++) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    yield i;
  }
}

// 使用for-await-of遍历
async function processNumbers() {
  for await (const num of numberStream()) {
    console.log(\`Processing: \${num}\`);
  }
}

// 处理API数据流
async function* fetchUserPages(baseUrl) {
  let page = 1;
  let hasMore = true;
  
  while (hasMore) {
    const response = await fetch(\`\${baseUrl}?page=\${page}\`);
    const data = await response.json();
    
    yield data.users;
    
    hasMore = data.hasNextPage;
    page++;
  }
}

// 流式处理用户数据
async function processAllUsers() {
  for await (const users of fetchUserPages('/api/users')) {
    for (const user of users) {
      await processUser(user);
    }
  }
}

// 异步数据管道
async function* transformStream(source) {
  for await (const item of source) {
    yield await transform(item);
  }
}
  `,
  tags: ['ES2018', 'ES9', 'Async', 'Iterator', 'Generator', 'Stream', 'for-await-of'],
  version: 'ES2018',
  isNew: false,

  // 核心维度数据
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default asyncIteratorsApi;
