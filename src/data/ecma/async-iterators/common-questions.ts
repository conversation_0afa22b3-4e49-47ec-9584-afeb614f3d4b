import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'async-iterator-vs-promise-all',
    question: '什么时候使用异步迭代器，什么时候使用Promise.all()？',
    answer: `
这两种方法适用于不同的场景：

**使用异步迭代器的场景：**
- 处理流式数据或无限序列
- 需要背压控制，避免内存溢出
- 数据需要按顺序处理
- 数据源是动态的或实时的
- 需要惰性求值，只在需要时生成数据

**使用Promise.all()的场景：**
- 处理固定数量的异步操作
- 需要并行执行以提高性能
- 所有操作都是独立的，没有依赖关系
- 数据量较小，内存不是问题
- 需要等待所有操作完成后再继续

**选择原则：**
- 流式数据 → 异步迭代器
- 批量并行 → Promise.all()
- 内存敏感 → 异步迭代器
- 性能优先 → Promise.all()
    `,
    code: `
// 适合异步迭代器的场景：处理大文件
async function* readLargeFile(filePath) {
  const fileHandle = await fs.open(filePath, 'r');
  const stream = fileHandle.createReadStream();
  
  for await (const chunk of stream) {
    yield chunk.toString();
  }
  
  await fileHandle.close();
}

// 流式处理，内存效率高
for await (const chunk of readLargeFile('huge-file.txt')) {
  await processChunk(chunk);
}

// 适合Promise.all()的场景：并行API调用
const userIds = [1, 2, 3, 4, 5];
const users = await Promise.all(
  userIds.map(id => fetch(\`/api/users/\${id}\`).then(r => r.json()))
);

// 所有请求并行执行，快速获取结果
console.log('All users:', users);
    `,
    tags: ['性能', '内存管理', '并发'],
    relatedQuestions: ['backpressure-control', 'streaming-vs-batch']
  },

  {
    id: 'async-generator-vs-async-function',
    question: '异步生成器函数与普通异步函数有什么区别？',
    answer: `
**异步生成器函数 (async function*)：**
- 返回AsyncGenerator对象
- 支持yield关键字，可以产生多个值
- 惰性执行，只在需要时计算下一个值
- 天然支持流式处理和背压控制
- 可以暂停和恢复执行

**普通异步函数 (async function)：**
- 返回Promise对象
- 只能返回一个值
- 一次性执行完成
- 适合单次异步操作
- 执行过程不可暂停

**使用场景：**
- 需要产生序列数据 → 异步生成器
- 单次异步操作 → 普通异步函数
- 流式处理 → 异步生成器
- 批量处理 → 普通异步函数
    `,
    code: `
// 异步生成器函数：产生数据序列
async function* fetchUserPages() {
  let page = 1;
  let hasMore = true;
  
  while (hasMore) {
    const response = await fetch(\`/api/users?page=\${page}\`);
    const data = await response.json();
    
    // 逐个产生用户数据
    for (const user of data.users) {
      yield user;
    }
    
    hasMore = data.hasNextPage;
    page++;
  }
}

// 流式处理用户数据
for await (const user of fetchUserPages()) {
  await processUser(user); // 逐个处理
}

// 普通异步函数：一次性操作
async function fetchAllUsers() {
  const response = await fetch('/api/users');
  const data = await response.json();
  return data.users; // 返回所有用户
}

// 批量处理用户数据
const users = await fetchAllUsers();
await Promise.all(users.map(processUser)); // 并行处理
    `,
    tags: ['生成器', '异步函数', '流处理'],
    relatedQuestions: ['generator-vs-iterator', 'lazy-evaluation']
  },

  {
    id: 'error-handling-async-iterators',
    question: '如何在异步迭代器中处理错误？',
    answer: `
异步迭代器的错误处理有多种方式：

**1. try-catch包围for-await-of：**
捕获迭代过程中的所有错误，包括迭代器内部错误和处理逻辑错误。

**2. 迭代器内部错误处理：**
在异步生成器函数内部使用try-catch处理特定错误。

**3. 错误传播：**
让错误自然传播到调用者，由调用者决定如何处理。

**4. 错误恢复：**
实现错误恢复机制，允许迭代器在错误后继续执行。

**最佳实践：**
- 在迭代器内部处理可恢复的错误
- 让不可恢复的错误传播到调用者
- 使用finally块进行资源清理
- 提供错误上下文信息
    `,
    code: `
// 错误处理示例
async function* robustDataProcessor(dataSource) {
  try {
    for await (const item of dataSource) {
      try {
        // 处理单个数据项
        const processed = await processItem(item);
        yield processed;
      } catch (itemError) {
        // 处理单项错误，继续处理下一项
        console.warn('Item processing failed:', itemError);
        yield { error: itemError.message, originalItem: item };
      }
    }
  } catch (sourceError) {
    // 数据源错误，无法继续
    console.error('Data source error:', sourceError);
    throw sourceError;
  } finally {
    // 清理资源
    console.log('Cleaning up resources');
  }
}

// 使用错误处理
async function processWithErrorHandling() {
  try {
    for await (const result of robustDataProcessor(dataSource)) {
      if (result.error) {
        console.log('Skipping failed item:', result.error);
        continue;
      }
      
      console.log('Processed:', result);
    }
  } catch (error) {
    console.error('Fatal error:', error);
    // 执行错误恢复逻辑
  }
}

// 带重试机制的异步迭代器
async function* withRetry(asyncIterable, maxRetries = 3) {
  const iterator = asyncIterable[Symbol.asyncIterator]();
  let retryCount = 0;
  
  while (true) {
    try {
      const result = await iterator.next();
      if (result.done) break;
      
      yield result.value;
      retryCount = 0; // 重置重试计数
    } catch (error) {
      if (retryCount < maxRetries) {
        retryCount++;
        console.log(\`Retry \${retryCount}/\${maxRetries}\`);
        await new Promise(resolve => 
          setTimeout(resolve, 1000 * retryCount)
        );
      } else {
        throw error;
      }
    }
  }
}
    `,
    tags: ['错误处理', '异常恢复', '重试机制'],
    relatedQuestions: ['error-propagation', 'resource-cleanup']
  }
];

export default commonQuestions;
