import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '如果目标长度小于原字符串长度，padStart()和padEnd()会截断字符串吗？',
    answer: '不会。如果目标长度小于或等于原字符串长度，padStart()和padEnd()会直接返回原字符串，不会进行任何截断操作。这是设计上的考虑，填充方法只负责填充，不负责截断。如果需要截断功能，应该使用substring()或slice()方法。',
    code: `// 目标长度小于原字符串长度的情况
const longString = 'Hello World';

// 目标长度小于原长度
console.log(longString.padStart(5, '0'));  // 'Hello World' (不截断)
console.log(longString.padEnd(5, '0'));    // 'Hello World' (不截断)

// 目标长度等于原长度
console.log(longString.padStart(11, '0')); // 'Hello World' (不填充)
console.log(longString.padEnd(11, '0'));   // 'Hello World' (不填充)

// 如果需要截断和填充的组合功能
function truncateAndPad(str, maxLength, padChar = ' ', method = 'padEnd') {
  // 先截断到最大长度
  const truncated = str.length > maxLength ? 
    str.substring(0, maxLength) : str;
  
  // 再进行填充
  return method === 'padStart' ? 
    truncated.padStart(maxLength, padChar) :
    truncated.padEnd(maxLength, padChar);
}

console.log(truncateAndPad('Hello World', 8));        // 'Hello Wo'
console.log(truncateAndPad('Hi', 8));                 // 'Hi      '
console.log(truncateAndPad('Hello World', 8, '0', 'padStart')); // 'Hello Wo'

// 实用的字符串格式化函数
function formatString(str, width, align = 'left', fillChar = ' ', truncate = false) {
  let result = str;
  
  // 如果允许截断且字符串过长
  if (truncate && str.length > width) {
    result = str.substring(0, width - 3) + '...';
  }
  
  // 根据对齐方式进行填充
  switch (align) {
    case 'left':
      return result.padEnd(width, fillChar);
    case 'right':
      return result.padStart(width, fillChar);
    case 'center':
      const totalPadding = width - result.length;
      const leftPadding = Math.floor(totalPadding / 2);
      const rightPadding = totalPadding - leftPadding;
      return ''.padEnd(leftPadding, fillChar) + result + ''.padEnd(rightPadding, fillChar);
    default:
      return result;
  }
}

// 测试格式化函数
console.log('|' + formatString('Hello', 10, 'left') + '|');     // |Hello     |
console.log('|' + formatString('Hello', 10, 'right') + '|');    // |     Hello|
console.log('|' + formatString('Hello', 10, 'center') + '|');   // |  Hello   |
console.log('|' + formatString('Very Long String', 10, 'left', '.', true) + '|'); // |Very Lo...|`,
    tags: ['字符串截断', '长度处理', '边界情况'],
    relatedQuestions: ['字符串截断', 'substring方法', '字符串长度']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: 'padStart()和padEnd()如何处理Unicode字符和emoji？',
    answer: 'padStart()和padEnd()基于JavaScript字符串的length属性进行长度计算，这可能导致Unicode字符（特别是emoji和组合字符）的处理出现问题。因为某些Unicode字符在JavaScript中占用多个代码单元，但在视觉上只显示为一个字符。对于需要精确视觉对齐的场景，可能需要使用专门的Unicode处理库。',
    code: `// Unicode字符和emoji的处理问题

// 基本ASCII字符 - 正常工作
console.log('Hello'.padStart(10, '0'));  // '00000Hello'

// Unicode字符 - 可能出现问题
const chinese = '你好';
console.log('Length:', chinese.length);  // 2 (正确)
console.log(chinese.padStart(5, '0'));   // '000你好'

// Emoji - 问题更明显
const emoji = '👋🌍';
console.log('Length:', emoji.length);    // 4 (实际视觉上是2个字符)
console.log(emoji.padStart(6, '0'));     // '00👋🌍'

// 组合字符 - 复杂情况
const combined = '👨‍👩‍👧‍👦'; // 家庭emoji (组合字符)
console.log('Length:', combined.length); // 11 (但视觉上是1个字符)
console.log(combined.padStart(15, '0')); // '0000👨‍👩‍👧‍👦'

// 解决方案1：使用Intl.Segmenter (现代浏览器)
function getVisualLength(str) {
  if (typeof Intl !== 'undefined' && Intl.Segmenter) {
    const segmenter = new Intl.Segmenter('en', { granularity: 'grapheme' });
    return Array.from(segmenter.segment(str)).length;
  }
  
  // 回退方案：使用正则表达式近似计算
  // 这个正则表达式可以处理大部分常见的Unicode情况
  const graphemeRegex = /[\\u{1F600}-\\u{1F64F}]|[\\u{1F300}-\\u{1F5FF}]|[\\u{1F680}-\\u{1F6FF}]|[\\u{1F1E0}-\\u{1F1FF}]|[\\u{2600}-\\u{26FF}]|[\\u{2700}-\\u{27BF}]|[\\u{1F900}-\\u{1F9FF}]|[\\u{1F018}-\\u{1F270}]|[\\u{238C}-\\u{2454}]|[\\u{20D0}-\\u{20FF}]|[\\u{FE20}-\\u{FE2F}]|[\\u{1AB0}-\\u{1AFF}]|[\\u{1DC0}-\\u{1DFF}]/gu;
  
  // 简化版本：只计算基本的emoji
  return str.replace(/[\\u{1F600}-\\u{1F64F}]|[\\u{1F300}-\\u{1F5FF}]|[\\u{1F680}-\\u{1F6FF}]|[\\u{2600}-\\u{26FF}]/gu, 'X').length;
}

// 解决方案2：自定义Unicode感知的填充函数
function unicodePadStart(str, targetLength, padString = ' ') {
  const visualLength = getVisualLength(str);
  const paddingNeeded = targetLength - visualLength;
  
  if (paddingNeeded <= 0) {
    return str;
  }
  
  const padding = padString.repeat(Math.ceil(paddingNeeded / padString.length))
    .substring(0, paddingNeeded);
  
  return padding + str;
}

function unicodePadEnd(str, targetLength, padString = ' ') {
  const visualLength = getVisualLength(str);
  const paddingNeeded = targetLength - visualLength;
  
  if (paddingNeeded <= 0) {
    return str;
  }
  
  const padding = padString.repeat(Math.ceil(paddingNeeded / padString.length))
    .substring(0, paddingNeeded);
  
  return str + padding;
}

// 测试Unicode感知的填充
console.log('\\n=== Unicode感知的填充测试 ===');

const testStrings = [
  'Hello',      // ASCII
  '你好',       // 中文
  '👋🌍',       // Emoji
  'café',       // 带重音符号
  'नमस्ते'      // 印地语
];

testStrings.forEach(str => {
  console.log(\`原字符串: "\${str}"\`);
  console.log(\`JavaScript长度: \${str.length}\`);
  console.log(\`视觉长度: \${getVisualLength(str)}\`);
  console.log(\`标准padStart: "\${str.padStart(10, '.')}"\`);
  console.log(\`Unicode padStart: "\${unicodePadStart(str, 10, '.')}"\`);
  console.log('---');
});

// 实际应用：创建Unicode友好的表格
function createUnicodeTable(data, columnWidths) {
  const rows = [];
  
  data.forEach(row => {
    const formattedRow = row.map((cell, index) => {
      const width = columnWidths[index];
      return unicodePadEnd(cell.toString(), width);
    }).join(' | ');
    
    rows.push(formattedRow);
  });
  
  return rows.join('\\n');
}

const unicodeData = [
  ['Name', 'Greeting', 'Flag'],
  ['English', 'Hello 👋', '🇺🇸'],
  ['Chinese', '你好', '🇨🇳'],
  ['Japanese', 'こんにちは', '🇯🇵'],
  ['Hindi', 'नमस्ते', '🇮🇳']
];

console.log('\\n=== Unicode友好的表格 ===');
console.log(createUnicodeTable(unicodeData, [10, 12, 5]));

// 注意事项和最佳实践
console.log('\\n=== 最佳实践建议 ===');
console.log('1. 对于纯ASCII文本，使用标准的padStart/padEnd');
console.log('2. 对于包含Unicode的文本，考虑使用专门的库如"string-width"');
console.log('3. 在需要精确视觉对齐的场景中，测试各种Unicode字符');
console.log('4. 考虑使用等宽字体来减少对齐问题');`,
    tags: ['Unicode处理', 'emoji支持', '国际化'],
    relatedQuestions: ['Unicode长度', '字符编码', '国际化文本处理']
  }
];

export default commonQuestions;
