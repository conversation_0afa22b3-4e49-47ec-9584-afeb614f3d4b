import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'financial-reporting',
    title: '财务报表格式化',
    description: '使用字符串填充方法创建专业的财务报表，包括数字对齐、货币格式化、表格布局等',
    businessValue: '提供清晰易读的财务报表，提高数据可读性和专业性',
    scenario: '财务系统需要生成各种格式的报表，包括损益表、资产负债表、现金流量表等，要求数字对齐、格式统一。',
    code: `// 财务报表生成器
class FinancialReportGenerator {
  constructor() {
    this.currency = 'USD';
    this.locale = 'en-US';
  }
  
  // 格式化货币金额
  formatCurrency(amount, width = 12) {
    const formatted = new Intl.NumberFormat(this.locale, {
      style: 'currency',
      currency: this.currency,
      minimumFractionDigits: 2
    }).format(Math.abs(amount));
    
    const sign = amount < 0 ? '(' : ' ';
    const closeParen = amount < 0 ? ')' : ' ';
    
    return (sign + formatted + closeParen).padStart(width);
  }
  
  // 格式化百分比
  formatPercentage(value, width = 8) {
    const percentage = (value * 100).toFixed(1) + '%';
    return percentage.padStart(width);
  }
  
  // 生成损益表
  generateIncomeStatement(data) {
    const report = [];
    const lineWidth = 60;
    
    // 标题
    report.push('INCOME STATEMENT'.padStart(lineWidth / 2 + 8));
    report.push('For the Year Ended December 31, 2023'.padStart(lineWidth / 2 + 17));
    report.push('='.repeat(lineWidth));
    report.push('');
    
    // 收入部分
    report.push('REVENUE:');
    report.push('  Sales Revenue' + this.formatCurrency(data.salesRevenue, 45));
    report.push('  Service Revenue' + this.formatCurrency(data.serviceRevenue, 43));
    report.push('  Other Revenue' + this.formatCurrency(data.otherRevenue, 45));
    report.push('-'.repeat(lineWidth));

    const totalRevenue = data.salesRevenue + data.serviceRevenue + data.otherRevenue;
    report.push('  Total Revenue' + this.formatCurrency(totalRevenue, 45));
    report.push('');

    // 费用部分
    report.push('EXPENSES:');
    report.push('  Cost of Goods Sold' + this.formatCurrency(data.cogs, 40));
    report.push('  Operating Expenses' + this.formatCurrency(data.operatingExpenses, 41));
    report.push('  Interest Expense' + this.formatCurrency(data.interestExpense, 43));
    report.push('  Tax Expense' + this.formatCurrency(data.taxExpense, 47));
    report.push('-'.repeat(lineWidth));

    const totalExpenses = data.cogs + data.operatingExpenses + data.interestExpense + data.taxExpense;
    report.push('  Total Expenses' + this.formatCurrency(totalExpenses, 44));
    report.push('');

    // 净收入
    const netIncome = totalRevenue - totalExpenses;
    report.push('='.repeat(lineWidth));
    report.push('NET INCOME' + this.formatCurrency(netIncome, 48));
    report.push('='.repeat(lineWidth));
    
    return report.join('\\n');
  }
  
  // 生成比率分析表
  generateRatioAnalysis(currentYear, previousYear) {
    const report = [];
    const nameWidth = 25;
    const valueWidth = 12;
    const changeWidth = 10;
    
    report.push('FINANCIAL RATIO ANALYSIS');
    report.push('='.repeat(50));
    report.push('');
    
    // 表头
    const header = 'Ratio'.padEnd(nameWidth) + 
                  'Current'.padStart(valueWidth) + 
                  'Previous'.padStart(valueWidth) + 
                  'Change'.padStart(changeWidth);
    report.push(header);
    report.push('-'.repeat(50));
    
    // 计算和显示比率
    const ratios = [
      {
        name: 'Current Ratio',
        current: currentYear.currentAssets / currentYear.currentLiabilities,
        previous: previousYear.currentAssets / previousYear.currentLiabilities
      },
      {
        name: 'Quick Ratio',
        current: (currentYear.currentAssets - currentYear.inventory) / currentYear.currentLiabilities,
        previous: (previousYear.currentAssets - previousYear.inventory) / previousYear.currentLiabilities
      },
      {
        name: 'Debt to Equity',
        current: currentYear.totalDebt / currentYear.totalEquity,
        previous: previousYear.totalDebt / previousYear.totalEquity
      },
      {
        name: 'ROE (%)',
        current: (currentYear.netIncome / currentYear.totalEquity) * 100,
        previous: (previousYear.netIncome / previousYear.totalEquity) * 100
      }
    ];
    
    ratios.forEach(ratio => {
      const change = ((ratio.current - ratio.previous) / ratio.previous) * 100;
      const changeStr = change > 0 ? \`+\${change.toFixed(1)}%\` : \`\${change.toFixed(1)}%\`;
      
      const line = ratio.name.padEnd(nameWidth) +
                   ratio.current.toFixed(2).padStart(valueWidth) +
                   ratio.previous.toFixed(2).padStart(valueWidth) +
                   changeStr.padStart(changeWidth);
      report.push(line);
    });
    
    return report.join('\\n');
  }
  
  // 生成现金流量表摘要
  generateCashFlowSummary(data) {
    const report = [];
    const labelWidth = 35;
    const amountWidth = 15;
    
    report.push('CASH FLOW STATEMENT SUMMARY');
    report.push('='.repeat(50));
    report.push('');
    
    // 经营活动现金流
    report.push('Operating Activities:');
    report.push(\`  Net Income\${this.formatCurrency(data.netIncome, amountWidth).padStart(labelWidth + amountWidth)}\`);
    report.push(\`  Depreciation\${this.formatCurrency(data.depreciation, amountWidth).padStart(labelWidth + amountWidth)}\`);
    report.push(\`  Working Capital Changes\${this.formatCurrency(data.workingCapitalChanges, amountWidth).padStart(labelWidth + amountWidth)}\`);
    
    const operatingCashFlow = data.netIncome + data.depreciation + data.workingCapitalChanges;
    report.push('-'.repeat(50));
    report.push(\`Net Cash from Operating\${this.formatCurrency(operatingCashFlow, amountWidth).padStart(labelWidth + amountWidth)}\`);
    report.push('');
    
    // 投资活动现金流
    report.push('Investing Activities:');
    report.push(\`  Capital Expenditures\${this.formatCurrency(data.capex, amountWidth).padStart(labelWidth + amountWidth)}\`);
    report.push(\`  Asset Sales\${this.formatCurrency(data.assetSales, amountWidth).padStart(labelWidth + amountWidth)}\`);
    
    const investingCashFlow = data.capex + data.assetSales;
    report.push('-'.repeat(50));
    report.push(\`Net Cash from Investing\${this.formatCurrency(investingCashFlow, amountWidth).padStart(labelWidth + amountWidth)}\`);
    report.push('');
    
    // 筹资活动现金流
    report.push('Financing Activities:');
    report.push(\`  Debt Proceeds\${this.formatCurrency(data.debtProceeds, amountWidth).padStart(labelWidth + amountWidth)}\`);
    report.push(\`  Debt Payments\${this.formatCurrency(data.debtPayments, amountWidth).padStart(labelWidth + amountWidth)}\`);
    report.push(\`  Dividends Paid\${this.formatCurrency(data.dividends, amountWidth).padStart(labelWidth + amountWidth)}\`);
    
    const financingCashFlow = data.debtProceeds + data.debtPayments + data.dividends;
    report.push('-'.repeat(50));
    report.push(\`Net Cash from Financing\${this.formatCurrency(financingCashFlow, amountWidth).padStart(labelWidth + amountWidth)}\`);
    report.push('');
    
    // 净现金流变化
    const netCashChange = operatingCashFlow + investingCashFlow + financingCashFlow;
    report.push('='.repeat(50));
    report.push(\`NET CHANGE IN CASH\${this.formatCurrency(netCashChange, amountWidth).padStart(labelWidth + amountWidth)}\`);
    report.push('='.repeat(50));
    
    return report.join('\\n');
  }
}

// 使用示例
const reportGenerator = new FinancialReportGenerator();

// 损益表数据
const incomeData = {
  salesRevenue: 1250000,
  serviceRevenue: 350000,
  otherRevenue: 25000,
  cogs: 750000,
  operatingExpenses: 400000,
  interestExpense: 15000,
  taxExpense: 92000
};

// 生成损益表
const incomeStatement = reportGenerator.generateIncomeStatement(incomeData);
console.log(incomeStatement);

// 比率分析数据
const currentYear = {
  currentAssets: 500000,
  currentLiabilities: 200000,
  inventory: 150000,
  totalDebt: 300000,
  totalEquity: 800000,
  netIncome: 120000
};

const previousYear = {
  currentAssets: 450000,
  currentLiabilities: 180000,
  inventory: 140000,
  totalDebt: 280000,
  totalEquity: 750000,
  netIncome: 100000
};

// 生成比率分析
const ratioAnalysis = reportGenerator.generateRatioAnalysis(currentYear, previousYear);
console.log(ratioAnalysis);`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'log-formatting',
    title: '日志系统格式化',
    description: '使用字符串填充创建结构化的日志系统，包括时间戳、日志级别、模块名称等的对齐',
    businessValue: '提供清晰可读的日志输出，便于问题排查和系统监控',
    scenario: '企业应用需要统一的日志格式，支持不同级别的日志、模块标识、性能指标等，便于运维和调试。',
    code: `// 高级日志格式化系统
class AdvancedLogger {
  constructor(options = {}) {
    this.options = {
      timestampFormat: 'ISO',
      levelWidth: 7,
      moduleWidth: 15,
      showPid: true,
      showMemory: false,
      colorize: false,
      ...options
    };
    
    this.levels = {
      TRACE: { priority: 0, color: '\\x1b[90m' },  // 灰色
      DEBUG: { priority: 1, color: '\\x1b[36m' },  // 青色
      INFO:  { priority: 2, color: '\\x1b[32m' },  // 绿色
      WARN:  { priority: 3, color: '\\x1b[33m' },  // 黄色
      ERROR: { priority: 4, color: '\\x1b[31m' },  // 红色
      FATAL: { priority: 5, color: '\\x1b[35m' }   // 紫色
    };
    
    this.reset = '\\x1b[0m';
  }
  
  // 格式化时间戳
  formatTimestamp() {
    const now = new Date();
    
    switch (this.options.timestampFormat) {
      case 'ISO':
        return now.toISOString();
      case 'LOCAL':
        return now.toLocaleString();
      case 'TIME_ONLY':
        return now.toTimeString().split(' ')[0];
      case 'CUSTOM':
        const year = now.getFullYear();
        const month = (now.getMonth() + 1).toString().padStart(2, '0');
        const day = now.getDate().toString().padStart(2, '0');
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const seconds = now.getSeconds().toString().padStart(2, '0');
        const ms = now.getMilliseconds().toString().padStart(3, '0');
        
        return \`\${year}-\${month}-\${day} \${hours}:\${minutes}:\${seconds}.\${ms}\`;
      default:
        return now.toISOString();
    }
  }
  
  // 格式化日志级别
  formatLevel(level) {
    const levelInfo = this.levels[level] || this.levels.INFO;
    let formatted = level.padEnd(this.options.levelWidth);
    
    if (this.options.colorize) {
      formatted = levelInfo.color + formatted + this.reset;
    }
    
    return formatted;
  }
  
  // 格式化模块名
  formatModule(module) {
    if (!module) return ''.padEnd(this.options.moduleWidth);
    
    // 如果模块名太长，截断并添加省略号
    if (module.length > this.options.moduleWidth) {
      return module.substring(0, this.options.moduleWidth - 3) + '...';
    }
    
    return module.padEnd(this.options.moduleWidth);
  }
  
  // 格式化进程信息
  formatProcessInfo() {
    const parts = [];
    
    if (this.options.showPid) {
      parts.push(\`PID:\${process.pid.toString().padStart(6)}\`);
    }
    
    if (this.options.showMemory) {
      const memUsage = process.memoryUsage();
      const rss = Math.round(memUsage.rss / 1024 / 1024);
      parts.push(\`MEM:\${rss.toString().padStart(4)}MB\`);
    }
    
    return parts.length > 0 ? \`[\${parts.join(' ')}]\` : '';
  }
  
  // 格式化性能指标
  formatPerformanceMetrics(metrics = {}) {
    const parts = [];
    
    if (metrics.duration !== undefined) {
      const duration = metrics.duration.toFixed(2);
      parts.push(\`\${duration.padStart(8)}ms\`);
    }
    
    if (metrics.requestId) {
      parts.push(\`REQ:\${metrics.requestId.substring(0, 8)}\`);
    }
    
    if (metrics.userId) {
      parts.push(\`USER:\${metrics.userId.toString().padStart(6)}\`);
    }
    
    return parts.length > 0 ? \`[\${parts.join(' ')}]\` : '';
  }
  
  // 主要日志方法
  log(level, message, module = '', metrics = {}) {
    const timestamp = this.formatTimestamp();
    const formattedLevel = this.formatLevel(level);
    const formattedModule = this.formatModule(module);
    const processInfo = this.formatProcessInfo();
    const perfMetrics = this.formatPerformanceMetrics(metrics);
    
    // 构建日志行
    const logParts = [
      timestamp,
      formattedLevel,
      formattedModule,
      processInfo,
      perfMetrics,
      message
    ].filter(part => part.trim() !== '');
    
    console.log(logParts.join(' '));
  }
  
  // 便捷方法
  trace(message, module, metrics) { this.log('TRACE', message, module, metrics); }
  debug(message, module, metrics) { this.log('DEBUG', message, module, metrics); }
  info(message, module, metrics) { this.log('INFO', message, module, metrics); }
  warn(message, module, metrics) { this.log('WARN', message, module, metrics); }
  error(message, module, metrics) { this.log('ERROR', message, module, metrics); }
  fatal(message, module, metrics) { this.log('FATAL', message, module, metrics); }
  
  // 请求日志
  logRequest(req, res, duration) {
    const method = req.method.padEnd(6);
    const status = res.statusCode.toString().padStart(3);
    const url = req.url.padEnd(30);
    const userAgent = (req.headers['user-agent'] || '').substring(0, 50);
    
    const message = \`\${method} \${url} \${status} - \${userAgent}\`;
    const metrics = {
      duration,
      requestId: req.id,
      userId: req.user?.id
    };
    
    this.info(message, 'HTTP', metrics);
  }
  
  // 数据库查询日志
  logQuery(query, duration, rowCount = 0) {
    const truncatedQuery = query.replace(/\\s+/g, ' ').substring(0, 100);
    const message = \`Query executed: \${truncatedQuery} | Rows: \${rowCount}\`;
    const metrics = { duration };
    
    if (duration > 1000) {
      this.warn(message, 'DATABASE', metrics);
    } else {
      this.debug(message, 'DATABASE', metrics);
    }
  }
  
  // 错误日志
  logError(error, context = {}) {
    const errorInfo = [
      \`Error: \${error.message}\`,
      \`Stack: \${error.stack?.split('\\n')[1]?.trim() || 'No stack trace'}\`,
      context.userId ? \`User: \${context.userId}\` : '',
      context.requestId ? \`Request: \${context.requestId}\` : ''
    ].filter(Boolean).join(' | ');
    
    this.error(errorInfo, context.module || 'ERROR');
  }
}

// 使用示例
const logger = new AdvancedLogger({
  timestampFormat: 'CUSTOM',
  levelWidth: 7,
  moduleWidth: 12,
  showPid: true,
  showMemory: true,
  colorize: true
});

// 应用启动日志
logger.info('Application starting...', 'STARTUP');
logger.info('Database connected successfully', 'DATABASE');
logger.info('Server listening on port 3000', 'SERVER');

// 请求处理日志
const mockReq = {
  method: 'GET',
  url: '/api/users/123',
  id: 'req-abc123',
  user: { id: 456 },
  headers: { 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' }
};

const mockRes = { statusCode: 200 };
logger.logRequest(mockReq, mockRes, 45.67);

// 数据库查询日志
logger.logQuery('SELECT * FROM users WHERE id = ? AND status = ?', 23.45, 1);
logger.logQuery('SELECT COUNT(*) FROM orders WHERE created_at > ?', 1234.56, 5000);

// 错误日志
try {
  throw new Error('Database connection timeout');
} catch (error) {
  logger.logError(error, {
    module: 'DATABASE',
    userId: 456,
    requestId: 'req-abc123'
  });
}

// 性能监控日志
logger.debug('Cache hit for user data', 'CACHE', { duration: 2.34, userId: 456 });
logger.warn('High memory usage detected', 'MONITOR', { duration: 0 });`
  }
];

export default businessScenarios;
