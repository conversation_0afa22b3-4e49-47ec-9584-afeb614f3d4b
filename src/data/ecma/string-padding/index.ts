import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const stringPaddingData: ApiItem = {
  id: 'string-padding',
  title: 'String Padding (padStart/padEnd)',
  description: 'ES2017字符串填充方法，提供字符串左右填充功能，用于格式化和对齐',
  category: 'ECMA特性',
  difficulty: 'easy',
  
  syntax: `str.padStart(length, fillString); str.padEnd(length, fillString);`,
  example: `'5'.padStart(3, '0'); // '005'  '5'.padEnd(3, '0'); // '500'`,
  notes: 'String.prototype.padStart()和padEnd()是ES2017引入的字符串方法，用于字符串填充和格式化',
  
  version: 'ES2017',
  tags: ['ES2017', 'JavaScript', 'String.padStart', 'String.padEnd', '字符串填充'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default stringPaddingData;
