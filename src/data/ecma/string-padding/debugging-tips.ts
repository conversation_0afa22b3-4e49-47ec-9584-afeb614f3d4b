import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: '字符串填充使用中的常见错误和解决方案',
        sections: [
          {
            title: '参数类型错误',
            description: '字符串填充方法参数相关的常见问题',
            items: [
              {
                title: 'TypeError: padStart/padEnd called on null or undefined',
                description: '对null或undefined调用字符串填充方法',
                solution: '在调用前检查字符串是否有效',
                prevention: '使用安全的字符串转换',
                code: `// ❌ 错误：对null/undefined调用填充方法
const data = null;
// data.padStart(5, '0'); // TypeError

// ✅ 正确：安全的字符串处理
function safePadStart(value, length, fillChar = ' ') {
  if (value === null || value === undefined) {
    return ''.padStart(length, fillChar);
  }
  return String(value).padStart(length, fillChar);
}

// ✅ 使用默认值
const result = (data ?? '').padStart(5, '0');

// 调试辅助函数
function debugPadding(value, length, fillChar, method = 'padStart') {
  console.log(\`Debugging \${method}:\`);
  console.log('Value:', value, 'Type:', typeof value);
  console.log('Length:', length, 'Type:', typeof length);
  console.log('Fill char:', fillChar, 'Type:', typeof fillChar);
  
  try {
    const str = String(value);
    const result = method === 'padStart' ? 
      str.padStart(length, fillChar) : 
      str.padEnd(length, fillChar);
    console.log('Result:', result);
    return result;
  } catch (error) {
    console.error('Error:', error.message);
    return '';
  }
}`
              },
              {
                title: '意外的填充结果',
                description: '填充结果与预期不符',
                solution: '检查参数类型和值',
                prevention: '理解填充方法的行为规则',
                code: `// 常见的意外情况分析
function analyzePaddingIssues() {
  console.log('=== 填充行为分析 ===');
  
  // 1. 目标长度为负数
  console.log('负数长度:', 'test'.padStart(-5, '0')); // 'test' (不填充)
  
  // 2. 目标长度为小数
  console.log('小数长度:', 'test'.padStart(5.7, '0')); // '0test' (转换为5)
  
  // 3. 填充字符为空字符串
  console.log('空填充字符:', 'test'.padStart(8, '')); // 'test' (使用空格)
  
  // 4. 填充字符为数字
  console.log('数字填充字符:', 'test'.padStart(8, 123)); // '1231test'
  
  // 5. 填充字符为对象
  console.log('对象填充字符:', 'test'.padStart(10, {})); // '[object Object]test'
  
  // 6. Unicode字符长度问题
  const emoji = '👋';
  console.log('Emoji长度:', emoji.length); // 2 (不是1)
  console.log('Emoji填充:', emoji.padStart(5, '0')); // '000👋'
}

analyzePaddingIssues();`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '调试字符串填充相关问题的工具和技巧',
        sections: [
          {
            title: '填充行为分析工具',
            description: '分析和可视化字符串填充的详细过程',
            items: [
              {
                title: '填充过程可视化',
                description: '详细展示填充的每个步骤',
                solution: '使用可视化工具理解填充逻辑',
                prevention: '在复杂填充场景中验证结果',
                code: `// 填充过程可视化工具
function visualizePadding(str, targetLength, padString = ' ', method = 'padStart') {
  console.log(\`\\n=== \${method.toUpperCase()} 可视化分析 ===\`);
  console.log(\`原字符串: "\${str}" (长度: \${str.length})\`);
  console.log(\`目标长度: \${targetLength}\`);
  console.log(\`填充字符: "\${padString}" (长度: \${padString.length})\`);
  
  const currentLength = str.length;
  const paddingNeeded = targetLength - currentLength;
  
  console.log(\`需要填充: \${paddingNeeded} 个字符\`);
  
  if (paddingNeeded <= 0) {
    console.log('⚠️  无需填充，返回原字符串');
    return str;
  }
  
  // 计算填充字符串
  const padLength = padString.length;
  const fullRepeats = Math.floor(paddingNeeded / padLength);
  const remainder = paddingNeeded % padLength;
  
  console.log(\`填充字符串重复: \${fullRepeats} 次\`);
  console.log(\`剩余字符: \${remainder} 个\`);
  
  let padding = padString.repeat(fullRepeats);
  if (remainder > 0) {
    const truncated = padString.substring(0, remainder);
    padding += truncated;
    console.log(\`截断部分: "\${truncated}"\`);
  }
  
  console.log(\`完整填充: "\${padding}"\`);
  
  const result = method === 'padStart' ? padding + str : str + padding;
  
  // 可视化结果
  console.log('\\n结果可视化:');
  if (method === 'padStart') {
    console.log(\`[\${padding}][\${str}]\`);
    console.log(\` \${'↑'.repeat(padding.length)} \${'↑'.repeat(str.length)}\`);
    console.log(\` \${'填充'.padEnd(padding.length)} \${'原文'.padEnd(str.length)}\`);
  } else {
    console.log(\`[\${str}][\${padding}]\`);
    console.log(\` \${'↑'.repeat(str.length)} \${'↑'.repeat(padding.length)}\`);
    console.log(\` \${'原文'.padEnd(str.length)} \${'填充'.padEnd(padding.length)}\`);
  }
  
  console.log(\`\\n最终结果: "\${result}" (长度: \${result.length})\`);
  
  return result;
}

// 批量测试工具
function batchTestPadding(testCases) {
  console.log('\\n=== 批量填充测试 ===');
  
  testCases.forEach((testCase, index) => {
    const { str, length, padChar, method } = testCase;
    console.log(\`\\n测试 \${index + 1}:\`);
    
    try {
      const result = visualizePadding(str, length, padChar, method);
      console.log('✅ 测试通过');
    } catch (error) {
      console.log('❌ 测试失败:', error.message);
    }
  });
}

// 使用示例
const testCases = [
  { str: 'Hi', length: 5, padChar: '0', method: 'padStart' },
  { str: 'Hello', length: 10, padChar: '.-', method: 'padEnd' },
  { str: 'Test', length: 8, padChar: 'abc', method: 'padStart' },
  { str: '👋', length: 5, padChar: '0', method: 'padStart' }
];

batchTestPadding(testCases);

// 对齐验证工具
function verifyAlignment(strings, width, method = 'padStart', padChar = ' ') {
  console.log(\`\\n=== 对齐验证 (\${method}) ===\`);
  console.log('|' + ''.padEnd(width, '-') + '|');
  
  strings.forEach(str => {
    const padded = method === 'padStart' ? 
      str.padStart(width, padChar) : 
      str.padEnd(width, padChar);
    
    console.log('|' + padded + '|');
  });
  
  console.log('|' + ''.padEnd(width, '-') + '|');
}

// 测试对齐
const testStrings = ['Hi', 'Hello', 'World', 'JavaScript'];
verifyAlignment(testStrings, 12, 'padStart', '.');
verifyAlignment(testStrings, 12, 'padEnd', '.');`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
