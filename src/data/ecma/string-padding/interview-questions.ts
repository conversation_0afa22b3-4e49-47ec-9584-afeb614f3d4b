import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'padStart()和padEnd()的区别是什么？在什么场景下使用？',
    answer: {
      brief: 'padStart()在字符串开头填充实现右对齐，padEnd()在字符串结尾填充实现左对齐，分别适用于不同的格式化场景',
      detailed: `padStart()和padEnd()的主要区别：

**填充位置**：
- padStart()在字符串开头填充（左填充）
- padEnd()在字符串结尾填充（右填充）

**对齐效果**：
- padStart()实现右对齐效果
- padEnd()实现左对齐效果

**使用场景**：
- padStart()：数字补零、右对齐显示、时间格式化
- padEnd()：左对齐显示、表格列对齐、进度条显示`
    },
   
    difficulty: 'easy',
    frequency: 'high',
    category: '方法对比',
    tags: ['padStart', 'padEnd', '字符串填充'],
    
    code: `// padStart() vs padEnd() 对比
const number = '42';
const text = 'Hello';

// padStart() - 左填充，右对齐
console.log(number.padStart(5, '0'));  // '00042'
console.log(text.padStart(10, '.'));   // '.....Hello'

// padEnd() - 右填充，左对齐  
console.log(number.padEnd(5, '0'));    // '42000'
console.log(text.padEnd(10, '.'));     // 'Hello.....'

// 实际应用场景对比

// 1. 数字格式化 - 使用padStart()
function formatNumber(num, width, fillChar = '0') {
  return num.toString().padStart(width, fillChar);
}

console.log(formatNumber(5, 3));     // '005'
console.log(formatNumber(42, 4));    // '0042'
console.log(formatNumber(123, 2));   // '123' (不截断)

// 2. 时间格式化 - 使用padStart()
function formatTime(hours, minutes, seconds) {
  const h = hours.toString().padStart(2, '0');
  const m = minutes.toString().padStart(2, '0');
  const s = seconds.toString().padStart(2, '0');
  return \`\${h}:\${m}:\${s}\`;
}

console.log(formatTime(9, 5, 3));    // '09:05:03'
console.log(formatTime(14, 30, 45)); // '14:30:45'

// 3. 表格对齐 - padStart()用于数字，padEnd()用于文本
const tableData = [
  { name: 'Alice', score: 95, grade: 'A' },
  { name: 'Bob', score: 87, grade: 'B+' },
  { name: 'Charlie', score: 92, grade: 'A-' }
];

console.log('Name'.padEnd(10) + 'Score'.padStart(6) + 'Grade'.padStart(6));
console.log('-'.repeat(22));

tableData.forEach(row => {
  const name = row.name.padEnd(10);
  const score = row.score.toString().padStart(6);
  const grade = row.grade.padStart(6);
  console.log(name + score + grade);
});

// 输出：
// Name      Score Grade
// ----------------------
// Alice        95     A
// Bob          87    B+
// Charlie      92    A-

// 4. 进度条 - 使用padEnd()
function createProgressBar(progress, total, width = 20) {
  const percentage = Math.round((progress / total) * 100);
  const filled = Math.round((progress / total) * width);
  const bar = '█'.repeat(filled).padEnd(width, '░');
  
  return \`[\${bar}] \${percentage}%\`;
}

console.log(createProgressBar(3, 10));  // [██████░░░░░░░░░░░░░░] 30%
console.log(createProgressBar(7, 10));  // [██████████████░░░░░░] 70%

// 5. 货币格式化 - 使用padStart()
function formatCurrency(amount, width = 10) {
  const formatted = '$' + amount.toFixed(2);
  return formatted.padStart(width);
}

console.log(formatCurrency(5.99));     // '     $5.99'
console.log(formatCurrency(123.45));   // '   $123.45'
console.log(formatCurrency(1234.56));  // '  $1234.56'

// 6. 日志级别对齐 - 使用padEnd()
const logLevels = ['INFO', 'WARN', 'ERROR', 'DEBUG'];

logLevels.forEach(level => {
  const paddedLevel = level.padEnd(5);
  console.log(\`[\${paddedLevel}] This is a \${level.toLowerCase()} message\`);
});

// 输出：
// [INFO ] This is a info message
// [WARN ] This is a warn message
// [ERROR] This is a error message
// [DEBUG] This is a debug message

// 7. 文件大小对齐 - 使用padStart()
const fileSizes = [
  { name: 'document.pdf', size: 1024 },
  { name: 'image.jpg', size: 2048576 },
  { name: 'video.mp4', size: 104857600 }
];

function formatFileSize(bytes) {
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
  if (bytes < 1073741824) return (bytes / 1048576).toFixed(1) + ' MB';
  return (bytes / 1073741824).toFixed(1) + ' GB';
}

console.log('Filename'.padEnd(15) + 'Size'.padStart(10));
console.log('-'.repeat(25));

fileSizes.forEach(file => {
  const name = file.name.padEnd(15);
  const size = formatFileSize(file.size).padStart(10);
  console.log(name + size);
});

// 输出：
// Filename       Size
// -------------------------
// document.pdf   1024 B
// image.jpg      2.0 MB
// video.mp4    100.0 MB`,
    
    followUp: [
      '如何处理Unicode字符的填充？',
      '填充字符串长度大于1时的行为？',
      '性能考虑和最佳实践？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '如果填充字符串长度大于1，padStart()和padEnd()如何处理？',
    answer: {
      brief: '当填充字符串长度大于1时，会重复使用整个填充字符串，如果最后一次重复超出目标长度则截断多余部分',
      detailed: `当填充字符串长度大于1时，padStart()和padEnd()会重复使用整个填充字符串，直到达到目标长度。如果最后一次重复超出了需要的长度，会截断多余的部分。

**处理机制**：
- 重复整个填充字符串
- 按需截断最后一次重复
- 保证结果长度精确匹配目标长度

**应用场景**：
- 创建装饰性边框
- 生成分隔线
- 制作视觉效果`
    },
   
    difficulty: 'medium',
    frequency: 'medium',
    category: '高级用法',
    tags: ['字符串填充', '重复模式', '截断处理'],
    
    code: `// 多字符填充字符串的处理

// 基本示例
console.log('Hello'.padStart(15, '.-'));
// 输出: '.-.-.-Hello' (重复'.-'直到长度为15)

console.log('Hello'.padEnd(15, '.-'));
// 输出: 'Hello.-.-.-.-' (重复'.-'直到长度为15)

// 截断示例
console.log('Hi'.padStart(7, 'abc'));
// 输出: 'abcabHi' (需要5个字符，'abc'重复1次后还需2个，截断为'ab')

console.log('Hi'.padEnd(7, 'abc'));
// 输出: 'Hiabcab' (需要5个字符，'abc'重复1次后还需2个，截断为'ab')

// 详细分析填充过程
function analyzePadding(str, targetLength, padString, method = 'padStart') {
  console.log(\`\\n=== 分析 \${method}('\${str}', \${targetLength}, '\${padString}') ===\`);
  
  const currentLength = str.length;
  const paddingNeeded = targetLength - currentLength;
  
  console.log(\`原字符串: '\${str}' (长度: \${currentLength})\`);
  console.log(\`目标长度: \${targetLength}\`);
  console.log(\`需要填充: \${paddingNeeded} 个字符\`);
  
  if (paddingNeeded <= 0) {
    console.log('无需填充，返回原字符串');
    return str;
  }
  
  const padLength = padString.length;
  const fullRepeats = Math.floor(paddingNeeded / padLength);
  const remainder = paddingNeeded % padLength;
  
  console.log(\`填充字符串: '\${padString}' (长度: \${padLength})\`);
  console.log(\`完整重复次数: \${fullRepeats}\`);
  console.log(\`剩余字符数: \${remainder}\`);
  
  let padding = padString.repeat(fullRepeats);
  if (remainder > 0) {
    padding += padString.substring(0, remainder);
    console.log(\`截断部分: '\${padString.substring(0, remainder)}'\`);
  }
  
  console.log(\`最终填充: '\${padding}'\`);
  
  const result = method === 'padStart' ? padding + str : str + padding;
  console.log(\`结果: '\${result}' (长度: \${result.length})\`);
  
  return result;
}

// 测试各种情况
analyzePadding('Hi', 10, 'abc', 'padStart');
analyzePadding('Hi', 10, 'abc', 'padEnd');
analyzePadding('Test', 12, '.-=', 'padStart');
analyzePadding('Test', 12, '.-=', 'padEnd');

// 实际应用：创建装饰性边框
function createBorder(text, width, borderChar = '=') {
  const textLength = text.length;
  const totalPadding = width - textLength;
  
  if (totalPadding <= 0) return text;
  
  const leftPadding = Math.floor(totalPadding / 2);
  const rightPadding = totalPadding - leftPadding;
  
  const left = ''.padStart(leftPadding, borderChar);
  const right = ''.padEnd(rightPadding, borderChar);
  
  return left + text + right;
}

console.log('\\n=== 装饰性边框示例 ===');
console.log(createBorder('TITLE', 20, '=-'));
console.log(createBorder('SUBTITLE', 20, '.-'));
console.log(createBorder('CONTENT', 20, '*+'));

// 输出：
// =-=-=-=-=-TITLE=-=-=-=-=-
// .-.-.-SUBTITLE.-.-.-.-
// *+*+*+CONTENT*+*+*+*

// 创建分隔线
function createSeparator(length, pattern = '-=') {
  return ''.padEnd(length, pattern);
}

console.log('\\n=== 分隔线示例 ===');
console.log(createSeparator(30, '-='));
console.log(createSeparator(25, '.*'));
console.log(createSeparator(20, '<>'));

// 输出：
// -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=
// .*.*.*.*.*.*.*.*.*.*.*.*.*
// <><><><><><><><><><>

// 创建进度条
function createAdvancedProgressBar(progress, total, width = 20, fillPattern = '█▓', emptyPattern = '░▒') {
  const percentage = progress / total;
  const filledWidth = Math.round(percentage * width);
  const emptyWidth = width - filledWidth;
  
  const filled = ''.padEnd(filledWidth, fillPattern);
  const empty = ''.padEnd(emptyWidth, emptyPattern);
  
  const percentText = \`\${Math.round(percentage * 100)}%\`.padStart(4);
  
  return \`[\${filled}\${empty}] \${percentText}\`;
}

console.log('\\n=== 高级进度条示例 ===');
console.log(createAdvancedProgressBar(3, 10));
console.log(createAdvancedProgressBar(7, 10));
console.log(createAdvancedProgressBar(10, 10));

// 输出：
// [█▓█▓█▓░▒░▒░▒░▒░▒░▒░▒░▒] 30%
// [█▓█▓█▓█▓█▓█▓█▓█▓░▒░▒░▒] 70%
// [█▓█▓█▓█▓█▓█▓█▓█▓█▓█▓█▓] 100%

// 表格列分隔符
function createTableRow(columns, widths, separator = ' | ') {
  return columns.map((col, index) => {
    const width = widths[index];
    return col.toString().padEnd(width);
  }).join(separator);
}

console.log('\\n=== 表格示例 ===');
const headers = ['Name', 'Age', 'City'];
const widths = [12, 5, 15];
const data = [
  ['Alice Johnson', 25, 'New York'],
  ['Bob Smith', 30, 'Los Angeles'],
  ['Charlie Brown', 35, 'Chicago']
];

console.log(createTableRow(headers, widths));
console.log(createSeparator(widths.reduce((sum, w) => sum + w, 0) + (widths.length - 1) * 3, '-'));

data.forEach(row => {
  console.log(createTableRow(row, widths));
});

// 输出：
// Name         | Age   | City           
// ------------------------------------
// Alice Johnson | 25    | New York       
// Bob Smith     | 30    | Los Angeles    
// Charlie Brown | 35    | Chicago`,
    
    followUp: [
      '如何优化多字符填充的性能？',
      '处理Unicode字符时的注意事项？',
      '创建复杂模式的最佳实践？'
    ]
  }
];

export default interviewQuestions;
