import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `String.prototype.padStart()和String.prototype.padEnd()是ES2017引入的字符串方法，用于在字符串的开头或结尾填充指定字符，直到达到目标长度。padStart()在字符串开头填充（左填充），padEnd()在字符串结尾填充（右填充）。这两个方法主要用于字符串格式化、数据对齐、补零操作等场景，提供了简洁而强大的字符串处理能力。`,

  syntax: `// padStart() - 左填充
str.padStart(targetLength)
str.padStart(targetLength, padString)

// padEnd() - 右填充  
str.padEnd(targetLength)
str.padEnd(targetLength, padString)

// 参数
targetLength: 目标字符串长度
padString: 填充字符串（可选，默认为空格）

// 返回值
string - 填充后的新字符串

// 基本用法
'5'.padStart(3, '0')     // '005'
'5'.padEnd(3, '0')       // '500'
'hello'.padStart(10)     // '     hello'
'hello'.padEnd(10, '.')  // 'hello.....'`,

  quickExample: `// 数字补零
const number = 5;
const paddedNumber = number.toString().padStart(3, '0');
console.log(paddedNumber); // '005'

// 时间格式化
function formatTime(hours, minutes, seconds) {
  const h = hours.toString().padStart(2, '0');
  const m = minutes.toString().padStart(2, '0');
  const s = seconds.toString().padStart(2, '0');
  return \`\${h}:\${m}:\${s}\`;
}

console.log(formatTime(9, 5, 3)); // '09:05:03'
console.log(formatTime(14, 30, 45)); // '14:30:45'

// 表格对齐
const data = [
  { name: 'Alice', score: 95 },
  { name: 'Bob', score: 87 },
  { name: 'Charlie', score: 92 }
];

console.log('Name'.padEnd(10) + 'Score'.padStart(5));
console.log('-'.repeat(15));
data.forEach(item => {
  const name = item.name.padEnd(10);
  const score = item.score.toString().padStart(5);
  console.log(name + score);
});
// Name      Score
// ---------------
// Alice        95
// Bob          87
// Charlie      92

// 进度条显示
function createProgressBar(progress, total, width = 20) {
  const percentage = Math.round((progress / total) * 100);
  const filled = Math.round((progress / total) * width);
  const empty = width - filled;
  
  const bar = '█'.repeat(filled) + '░'.repeat(empty);
  const label = \`\${percentage}%\`.padStart(4);
  
  return \`[\${bar}] \${label}\`;
}

console.log(createProgressBar(7, 10));  // [██████████████░░░░░░] 70%
console.log(createProgressBar(3, 10));  // [██████░░░░░░░░░░░░░░] 30%

// 货币格式化
function formatCurrency(amount, currency = 'USD') {
  const formatted = amount.toFixed(2);
  const [integer, decimal] = formatted.split('.');
  
  // 添加千位分隔符
  const withCommas = integer.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');
  
  return \`\${currency} \${withCommas}.\${decimal}\`.padStart(15);
}

console.log(formatCurrency(1234.56));    // '   USD 1,234.56'
console.log(formatCurrency(56789.12));   // '  USD 56,789.12'

// 日志格式化
function formatLogLevel(level) {
  const levels = {
    ERROR: level.padEnd(5),
    WARN: level.padEnd(5),
    INFO: level.padEnd(5),
    DEBUG: level.padEnd(5)
  };
  return levels[level] || level.padEnd(5);
}

function log(level, message) {
  const timestamp = new Date().toISOString();
  const formattedLevel = formatLogLevel(level);
  console.log(\`[\${timestamp}] \${formattedLevel} \${message}\`);
}

log('ERROR', 'Database connection failed');
log('INFO', 'Server started successfully');
log('DEBUG', 'Processing user request');

// 文件大小格式化
function formatFileSize(bytes) {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  const formattedSize = size.toFixed(1);
  const unit = units[unitIndex];
  
  return \`\${formattedSize.padStart(7)} \${unit}\`;
}

console.log(formatFileSize(1024));      // '    1.0 KB'
console.log(formatFileSize(1048576));   // '    1.0 MB'
console.log(formatFileSize(1073741824)); // '    1.0 GB'

// 二进制/十六进制格式化
function formatBinary(number, bits = 8) {
  return number.toString(2).padStart(bits, '0');
}

function formatHex(number, digits = 2) {
  return '0x' + number.toString(16).toUpperCase().padStart(digits, '0');
}

console.log(formatBinary(5));    // '00000101'
console.log(formatBinary(255));  // '11111111'
console.log(formatHex(255));     // '0xFF'
console.log(formatHex(15, 4));   // '0x000F'

// 字符串截断和填充
function truncateAndPad(str, maxLength, padChar = '.') {
  if (str.length > maxLength) {
    return str.substring(0, maxLength - 3) + '...';
  }
  return str.padEnd(maxLength, padChar);
}

console.log(truncateAndPad('Hello', 10));           // 'Hello.....'
console.log(truncateAndPad('Very long string', 10)); // 'Very lo...'`,

  coreFeatures: [
    {
      feature: "左填充 (padStart)",
      description: "在字符串开头添加填充字符",
      importance: "high" as const,
      details: "常用于数字补零、右对齐等场景"
    },
    {
      feature: "右填充 (padEnd)",
      description: "在字符串结尾添加填充字符",
      importance: "high" as const,
      details: "常用于左对齐、表格格式化等场景"
    },
    {
      feature: "自定义填充字符",
      description: "可以指定任意字符串作为填充内容",
      importance: "medium" as const,
      details: "支持单字符或多字符填充模式"
    },
    {
      feature: "长度控制",
      description: "精确控制最终字符串的长度",
      importance: "high" as const,
      details: "如果原字符串已达到或超过目标长度，则不进行填充"
    }
  ],

  keyFeatures: [
    {
      feature: "不修改原字符串",
      description: "返回新字符串，不改变原始字符串",
      importance: "high" as const,
      details: "符合字符串不可变的特性"
    },
    {
      feature: "Unicode友好",
      description: "正确处理Unicode字符",
      importance: "medium" as const,
      details: "支持emoji和多字节字符"
    },
    {
      feature: "重复填充",
      description: "填充字符串会重复使用直到达到目标长度",
      importance: "medium" as const,
      details: "如果填充字符串长度大于1，会重复使用"
    }
  ],

  limitations: [
    "如果目标长度小于等于原字符串长度，不会进行截断",
    "填充字符串为空时使用空格作为默认填充",
    "目标长度过大可能导致内存问题",
    "不支持负数长度参数",
    "填充字符串的最后一次使用可能被截断"
  ],

  bestPractices: [
    "用于数字格式化和补零操作",
    "表格数据的对齐显示",
    "日志格式化和结构化输出",
    "进度条和状态显示",
    "文件名和路径的格式化"
  ],

  warnings: [
    "目标长度过大会消耗大量内存",
    "注意Unicode字符的长度计算",
    "填充字符串为null或undefined时会转换为字符串"
  ]
};

export default basicInfo;
