import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: `空值合并操作符的历史反映了JavaScript社区对精确默认值处理的需求。从逻辑或操作符的局限性到专门的空值处理，体现了语言设计向更精确语义的演进。`,
  
  background: `在空值合并操作符出现之前，开发者主要使用逻辑或操作符(||)来提供默认值，但这会错误地替换有效的falsy值，导致许多微妙的bug。`,

  evolution: `空值合并操作符的引入为JavaScript提供了精确的默认值处理机制，解决了长期存在的falsy值处理问题。`,

  timeline: [
    {
      year: '2018',
      event: '空值合并提案提出',
      description: 'TC39开始讨论空值合并操作符的设计',
      significance: '响应社区对精确默认值处理的需求'
    },
    {
      year: '2020',
      event: 'ES2020标准化',
      description: 'ECMAScript 2020正式引入空值合并操作符',
      significance: '成为JavaScript的官方语法特性'
    },
    {
      year: '2021',
      event: '空值合并赋值标准化',
      description: 'ES2021引入??=赋值操作符',
      significance: '完善了空值合并的操作符家族'
    }
  ],

  keyFigures: [
    {
      name: 'TC39委员会',
      role: 'ECMAScript标准制定者',
      contribution: '设计和标准化空值合并语法',
      significance: '推动了JavaScript语义精确性的提升'
    }
  ],

  concepts: [
    {
      term: '默认值处理',
      definition: '为可能缺失的值提供备选值的编程模式',
      evolution: '从粗糙的||检查发展为精确的??检查',
      modernRelevance: '现代JavaScript开发的重要实践'
    }
  ],

  designPhilosophy: `空值合并体现了"精确性优于便利性"的设计哲学，通过更严格的语义来避免常见的编程错误。`,

  impact: `空值合并操作符的引入显著提高了JavaScript默认值处理的准确性，减少了因falsy值误判导致的bug。`,

  modernRelevance: `在现代JavaScript开发中，空值合并已成为处理默认值的首选方式，特别是在配置管理和API响应处理中。`
};

export default knowledgeArchaeology;
