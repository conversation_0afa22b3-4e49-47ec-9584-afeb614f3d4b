import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: '空值合并操作符使用中的常见错误和解决方案',
        sections: [
          {
            title: '操作符混淆错误',
            description: '??与||操作符的混淆使用',
            items: [
              {
                title: '错误地使用||替代??',
                description: '在需要保留falsy值时错误使用||操作符',
                solution: '理解??和||的区别，在需要保留0、false、空字符串时使用??',
                prevention: '明确区分"提供默认值"和"获取truthy值"的场景',
                code: `// ❌ 错误：使用||导致有效值被替换
const config = {
  port: 0,        // 有效的端口号
  debug: false,   // 有效的调试设置
  name: ''        // 有效的空名称
};

const port = config.port || 3000;     // 3000 (错误！0被替换)
const debug = config.debug || true;   // true (错误！false被替换)
const name = config.name || 'default'; // 'default' (错误！空字符串被替换)

// ✅ 正确：使用??保留有效的falsy值
const portCorrect = config.port ?? 3000;     // 0 (保留有效值)
const debugCorrect = config.debug ?? true;   // false (保留有效值)
const nameCorrect = config.name ?? 'default'; // '' (保留有效值)

// 调试技巧：添加日志验证行为
function debugOperatorBehavior(value, defaultValue) {
  const orResult = value || defaultValue;
  const nullishResult = value ?? defaultValue;
  
  console.log('Value:', value);
  console.log('Using ||:', orResult);
  console.log('Using ??:', nullishResult);
  console.log('Are they equal?', orResult === nullishResult);
  
  return { orResult, nullishResult };
}`
              },
              {
                title: 'SyntaxError: 与&&或||混合使用',
                description: '直接将??与&&或||组合使用导致语法错误',
                solution: '使用括号明确操作符优先级',
                prevention: '记住??不能与&&或||直接组合',
                code: `// ❌ 错误：直接组合导致语法错误
// const result = a || b ?? c; // SyntaxError
// const result2 = a && b ?? c; // SyntaxError

// ✅ 正确：使用括号明确优先级
const a = null, b = 0, c = 'default';

const result1 = (a || b) ?? c;  // (null || 0) ?? 'default' = 0
const result2 = a || (b ?? c);  // null || (0 ?? 'default') = 0
const result3 = (a && b) ?? c;  // (null && 0) ?? 'default' = 'default'
const result4 = a && (b ?? c);  // null && (0 ?? 'default') = null

// 调试函数：测试不同的组合
function testOperatorCombinations(a, b, c) {
  console.log('Testing values:', { a, b, c });
  
  try {
    const combinations = {
      '(a || b) ?? c': (a || b) ?? c,
      'a || (b ?? c)': a || (b ?? c),
      '(a && b) ?? c': (a && b) ?? c,
      'a && (b ?? c)': a && (b ?? c)
    };
    
    for (const [expr, result] of Object.entries(combinations)) {
      console.log(\`\${expr} = \${result}\`);
    }
  } catch (error) {
    console.error('Syntax error:', error.message);
  }
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '调试空值合并相关问题的工具和技巧',
        sections: [
          {
            title: '空值合并调试技巧',
            description: '如何有效调试??操作符的行为',
            items: [
              {
                title: '值类型检查工具',
                description: '创建工具函数来检查值的类型和??的行为',
                solution: '使用专门的调试函数来分析值的处理过程',
                prevention: '在复杂的默认值逻辑中添加类型检查',
                code: `// 调试工具：值类型分析器
function analyzeValue(value, label = 'value') {
  const analysis = {
    label,
    value,
    type: typeof value,
    isNull: value === null,
    isUndefined: value === undefined,
    isNullish: value === null || value === undefined,
    isFalsy: !value,
    isTruthy: !!value
  };
  
  console.table(analysis);
  return analysis;
}

// 调试工具：??行为预测器
function predictNullishCoalescing(left, right) {
  const leftAnalysis = analyzeValue(left, 'left');
  const rightAnalysis = analyzeValue(right, 'right');
  
  const willUseRight = leftAnalysis.isNullish;
  const result = left ?? right;
  
  console.log('Nullish Coalescing Analysis:');
  console.log('Left operand:', left);
  console.log('Right operand:', right);
  console.log('Will use right operand?', willUseRight);
  console.log('Actual result:', result);
  
  return result;
}

// 使用示例
const testValues = [null, undefined, 0, '', false, NaN, 'hello', 42];

testValues.forEach(value => {
  console.log(\`\\n--- Testing: \${JSON.stringify(value)} ---\`);
  predictNullishCoalescing(value, 'DEFAULT');
});

// 复杂场景调试器
function debugComplexNullishChain(obj, path, defaultValue) {
  console.group(\`Debugging nullish chain: \${path}\`);
  
  const steps = path.split('.');
  let current = obj;
  
  for (let i = 0; i < steps.length; i++) {
    const step = steps[i];
    const previous = current;
    current = current?.[step];
    
    console.log(\`Step \${i + 1} (\${step}):\`);
    console.log('  From:', previous);
    console.log('  To:', current);
    console.log('  Is nullish?', current === null || current === undefined);
    
    if (current === null || current === undefined) {
      console.log('  Chain will break here');
      break;
    }
  }
  
  const finalResult = current ?? defaultValue;
  console.log('Final result:', finalResult);
  console.log('Used default?', finalResult === defaultValue);
  console.groupEnd();
  
  return finalResult;
}

// 使用调试器
const complexObj = {
  user: {
    profile: {
      settings: null
    }
  }
};

debugComplexNullishChain(complexObj, 'user.profile.settings.theme', 'light');`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
