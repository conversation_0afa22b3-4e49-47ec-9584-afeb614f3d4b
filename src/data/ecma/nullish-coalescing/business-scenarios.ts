import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'configuration-management',
    title: '配置管理系统',
    description: '使用空值合并操作符构建健壮的配置管理系统，正确处理各种配置值',
    businessValue: '确保配置系统的可靠性，避免因错误的默认值处理导致的系统异常',
    scenario: '企业应用需要处理来自多个来源的配置（环境变量、配置文件、数据库等），需要正确处理0、false、空字符串等有效值。',
    code: `// 配置管理器
class ConfigurationManager {
  constructor() {
    this.sources = [];
    this.cache = new Map();
    this.defaults = new Map();
  }
  
  // 添加配置源
  addSource(source, priority = 0) {
    this.sources.push({ source, priority });
    this.sources.sort((a, b) => b.priority - a.priority);
  }
  
  // 设置默认值
  setDefault(key, value) {
    this.defaults.set(key, value);
  }
  
  // 获取配置值
  get(key) {
    // 检查缓存
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }
    
    let value = undefined;
    
    // 按优先级查找配置值
    for (const { source } of this.sources) {
      const sourceValue = source.get(key);
      if (sourceValue !== null && sourceValue !== undefined) {
        value = sourceValue;
        break;
      }
    }
    
    // 使用空值合并提供默认值
    const finalValue = value ?? this.defaults.get(key);
    
    // 缓存结果
    this.cache.set(key, finalValue);
    return finalValue;
  }
  
  // 获取数字配置
  getNumber(key, defaultValue = 0) {
    const value = this.get(key);
    const parsed = Number(value);
    
    // 使用??确保0是有效值
    return isNaN(parsed) ? defaultValue : parsed;
  }
  
  // 获取布尔配置
  getBoolean(key, defaultValue = false) {
    const value = this.get(key);
    
    if (value === null || value === undefined) {
      return defaultValue;
    }
    
    // 处理字符串形式的布尔值
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    
    return Boolean(value);
  }
  
  // 获取数组配置
  getArray(key, defaultValue = []) {
    const value = this.get(key);
    
    if (Array.isArray(value)) {
      return value;
    }
    
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        return Array.isArray(parsed) ? parsed : defaultValue;
      } catch {
        // 尝试按逗号分割
        return value.split(',').map(item => item.trim());
      }
    }
    
    return value ?? defaultValue;
  }
  
  // 获取对象配置
  getObject(key, defaultValue = {}) {
    const value = this.get(key);
    
    if (typeof value === 'object' && value !== null) {
      return value;
    }
    
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        return typeof parsed === 'object' ? parsed : defaultValue;
      } catch {
        return defaultValue;
      }
    }
    
    return value ?? defaultValue;
  }
}

// 环境变量配置源
class EnvironmentSource {
  get(key) {
    return process.env[key] ?? null;
  }
}

// 文件配置源
class FileSource {
  constructor(filePath) {
    this.config = this.loadConfig(filePath);
  }
  
  loadConfig(filePath) {
    try {
      const fs = require('fs');
      const content = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(content);
    } catch {
      return {};
    }
  }
  
  get(key) {
    return this.getNestedValue(this.config, key);
  }
  
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current?.[key];
    }, obj) ?? null;
  }
}

// 数据库配置源
class DatabaseSource {
  constructor(db) {
    this.db = db;
    this.cache = new Map();
  }
  
  async get(key) {
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }
    
    try {
      const result = await this.db.query(
        'SELECT value FROM config WHERE key = ?',
        [key]
      );
      
      const value = result?.[0]?.value ?? null;
      this.cache.set(key, value);
      return value;
    } catch {
      return null;
    }
  }
}

// 应用配置示例
class ApplicationConfig {
  constructor() {
    this.config = new ConfigurationManager();
    this.setupSources();
    this.setupDefaults();
  }
  
  setupSources() {
    // 优先级：环境变量 > 配置文件 > 数据库
    this.config.addSource(new EnvironmentSource(), 100);
    this.config.addSource(new FileSource('./config.json'), 50);
    // this.config.addSource(new DatabaseSource(db), 10);
  }
  
  setupDefaults() {
    // 服务器配置
    this.config.setDefault('server.port', 3000);
    this.config.setDefault('server.host', 'localhost');
    this.config.setDefault('server.timeout', 30000);
    
    // 数据库配置
    this.config.setDefault('database.maxConnections', 10);
    this.config.setDefault('database.timeout', 5000);
    this.config.setDefault('database.ssl', false);
    
    // 功能开关
    this.config.setDefault('features.newUI', false);
    this.config.setDefault('features.analytics', true);
    this.config.setDefault('features.cache', true);
    
    // 日志配置
    this.config.setDefault('logging.level', 'info');
    this.config.setDefault('logging.maxFiles', 5);
    this.config.setDefault('logging.maxSize', '10MB');
  }
  
  // 获取服务器配置
  getServerConfig() {
    return {
      port: this.config.getNumber('server.port'),
      host: this.config.get('server.host'),
      timeout: this.config.getNumber('server.timeout'),
      
      // 使用??确保0是有效的端口号
      adminPort: this.config.getNumber('server.adminPort') ?? 0,
      
      // SSL配置可能为false，使用??而不是||
      ssl: this.config.getBoolean('server.ssl') ?? false
    };
  }
  
  // 获取功能开关
  getFeatureFlags() {
    return {
      newUI: this.config.getBoolean('features.newUI'),
      analytics: this.config.getBoolean('features.analytics'),
      cache: this.config.getBoolean('features.cache'),
      
      // 实验性功能，默认关闭
      experimental: this.config.getBoolean('features.experimental') ?? false
    };
  }
  
  // 获取完整配置
  getAllConfig() {
    const serverConfig = this.getServerConfig();
    const features = this.getFeatureFlags();
    
    return {
      server: serverConfig,
      features,
      database: {
        maxConnections: this.config.getNumber('database.maxConnections'),
        timeout: this.config.getNumber('database.timeout'),
        ssl: this.config.getBoolean('database.ssl')
      },
      logging: {
        level: this.config.get('logging.level'),
        maxFiles: this.config.getNumber('logging.maxFiles'),
        maxSize: this.config.get('logging.maxSize')
      }
    };
  }
}`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'user-preferences',
    title: '用户偏好设置',
    description: '使用空值合并操作符处理用户偏好设置，确保各种有效值得到正确保留',
    businessValue: '提供准确的用户体验个性化，避免因错误的默认值覆盖用户的有效设置',
    scenario: '用户管理系统需要处理各种偏好设置，包括主题、通知、隐私等，其中0、false、空字符串都可能是用户的有效选择。',
    code: `// 用户偏好管理器
class UserPreferencesManager {
  constructor(userId) {
    this.userId = userId;
    this.preferences = new Map();
    this.defaults = this.getDefaultPreferences();
  }
  
  // 获取默认偏好设置
  getDefaultPreferences() {
    return {
      // 界面设置
      theme: 'light',
      language: 'en',
      fontSize: 14,
      compactMode: false,
      
      // 通知设置
      emailNotifications: true,
      pushNotifications: true,
      soundEnabled: true,
      notificationFrequency: 'immediate',
      
      // 隐私设置
      profileVisibility: 'public',
      showOnlineStatus: true,
      allowDirectMessages: true,
      
      // 功能设置
      autoSave: true,
      autoBackup: false,
      experimentalFeatures: false,
      
      // 显示设置
      itemsPerPage: 20,
      showThumbnails: true,
      animationsEnabled: true
    };
  }
  
  // 加载用户偏好
  async loadPreferences() {
    try {
      const stored = await this.fetchFromStorage();
      const server = await this.fetchFromServer();
      
      // 合并偏好设置，服务器优先
      this.preferences = this.mergePreferences(stored, server);
      return this.preferences;
    } catch (error) {
      console.error('Failed to load preferences:', error);
      return this.defaults;
    }
  }
  
  // 合并偏好设置
  mergePreferences(stored = {}, server = {}) {
    const merged = { ...this.defaults };
    
    // 应用本地存储的偏好
    for (const [key, value] of Object.entries(stored)) {
      // 使用??确保0、false、''等有效值不被默认值覆盖
      merged[key] = value ?? merged[key];
    }
    
    // 应用服务器偏好（优先级更高）
    for (const [key, value] of Object.entries(server)) {
      merged[key] = value ?? merged[key];
    }
    
    return merged;
  }
  
  // 获取偏好值
  get(key) {
    const value = this.preferences.get?.(key) ?? this.preferences[key];
    return value ?? this.defaults[key];
  }
  
  // 设置偏好值
  set(key, value) {
    this.preferences[key] = value;
    this.saveToStorage(key, value);
  }
  
  // 批量设置偏好
  setBatch(preferences) {
    for (const [key, value] of Object.entries(preferences)) {
      this.preferences[key] = value;
    }
    this.saveToStorage();
  }
  
  // 获取主题设置
  getThemeSettings() {
    return {
      theme: this.get('theme'),
      fontSize: this.get('fontSize'),
      compactMode: this.get('compactMode'),
      
      // 高对比度可能被设置为false，使用??
      highContrast: this.get('highContrast') ?? false,
      
      // 动画可能被用户关闭（false），使用??
      animationsEnabled: this.get('animationsEnabled') ?? true,
      
      // 自定义CSS可能为空字符串，使用??
      customCSS: this.get('customCSS') ?? ''
    };
  }
  
  // 获取通知设置
  getNotificationSettings() {
    return {
      emailNotifications: this.get('emailNotifications'),
      pushNotifications: this.get('pushNotifications'),
      soundEnabled: this.get('soundEnabled'),
      
      // 通知延迟可能为0（立即），使用??
      notificationDelay: this.get('notificationDelay') ?? 0,
      
      // 免打扰时间可能为空字符串，使用??
      quietHoursStart: this.get('quietHoursStart') ?? '',
      quietHoursEnd: this.get('quietHoursEnd') ?? '',
      
      // 通知频率
      frequency: this.get('notificationFrequency')
    };
  }
  
  // 获取隐私设置
  getPrivacySettings() {
    return {
      profileVisibility: this.get('profileVisibility'),
      
      // 在线状态可能被设置为false，使用??
      showOnlineStatus: this.get('showOnlineStatus') ?? true,
      
      // 允许私信可能被设置为false，使用??
      allowDirectMessages: this.get('allowDirectMessages') ?? true,
      
      // 搜索可见性可能被设置为false，使用??
      searchable: this.get('searchable') ?? true,
      
      // 数据分享可能被设置为false，使用??
      allowAnalytics: this.get('allowAnalytics') ?? false
    };
  }
  
  // 获取显示设置
  getDisplaySettings() {
    return {
      language: this.get('language'),
      
      // 每页项目数可能为0（显示全部），使用??
      itemsPerPage: this.get('itemsPerPage') ?? 20,
      
      // 缩略图显示可能被关闭（false），使用??
      showThumbnails: this.get('showThumbnails') ?? true,
      
      // 侧边栏可能被折叠（false），使用??
      sidebarExpanded: this.get('sidebarExpanded') ?? true,
      
      // 工具栏可能被隐藏（false），使用??
      showToolbar: this.get('showToolbar') ?? true
    };
  }
  
  // 重置偏好设置
  reset(keys = null) {
    if (keys) {
      // 重置指定的偏好
      for (const key of keys) {
        this.preferences[key] = this.defaults[key];
      }
    } else {
      // 重置所有偏好
      this.preferences = { ...this.defaults };
    }
    
    this.saveToStorage();
  }
  
  // 导出偏好设置
  export() {
    const exported = {};
    
    for (const [key, defaultValue] of Object.entries(this.defaults)) {
      const currentValue = this.get(key);
      
      // 只导出与默认值不同的偏好
      if (currentValue !== defaultValue) {
        exported[key] = currentValue;
      }
    }
    
    return exported;
  }
  
  // 导入偏好设置
  import(preferences) {
    for (const [key, value] of Object.entries(preferences)) {
      // 验证偏好键是否有效
      if (this.defaults.hasOwnProperty(key)) {
        // 使用??确保null/undefined被过滤，但保留有效的falsy值
        this.preferences[key] = value ?? this.defaults[key];
      }
    }
    
    this.saveToStorage();
  }
  
  // 保存到本地存储
  saveToStorage(key = null, value = null) {
    try {
      if (key && value !== undefined) {
        localStorage.setItem(\`pref_\${this.userId}_\${key}\`, JSON.stringify(value));
      } else {
        localStorage.setItem(\`preferences_\${this.userId}\`, JSON.stringify(this.preferences));
      }
    } catch (error) {
      console.error('Failed to save preferences:', error);
    }
  }
  
  // 从本地存储加载
  fetchFromStorage() {
    try {
      const stored = localStorage.getItem(\`preferences_\${this.userId}\`);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Failed to load preferences from storage:', error);
      return {};
    }
  }
  
  // 从服务器加载
  async fetchFromServer() {
    try {
      const response = await fetch(\`/api/users/\${this.userId}/preferences\`);
      return response.ok ? await response.json() : {};
    } catch (error) {
      console.error('Failed to load preferences from server:', error);
      return {};
    }
  }
}`
  }
];

export default businessScenarios;
