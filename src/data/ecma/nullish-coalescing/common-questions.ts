import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么空值合并操作符不检查其他falsy值？',
    answer: '空值合并操作符只检查null和undefined是有意的设计决定。其他falsy值（0、\'\'、false、NaN）在很多情况下都是有效的值，不应该被默认值替换。这种设计确保了操作符的语义明确：只在值真正"不存在"时才使用默认值，而不是在值"不够真"时替换。',
    code: `// 为什么其他falsy值是有效的
const userSettings = {
  volume: 0,           // 0是有效的音量设置（静音）
  username: '',        // 空字符串可能是有效的用户名
  darkMode: false,     // false是有效的主题设置
  notifications: NaN,  // NaN可能表示未设置的数值
  avatar: null,        // null表示没有头像
  bio: undefined       // undefined表示未提供简介
};

// 使用??保留有效的falsy值
const volume = userSettings.volume ?? 50;           // 0 (保留静音设置)
const username = userSettings.username ?? 'guest';  // '' (保留空用户名)
const darkMode = userSettings.darkMode ?? true;     // false (保留明亮主题)
const avatar = userSettings.avatar ?? '/default.png'; // '/default.png' (使用默认头像)
const bio = userSettings.bio ?? 'No bio provided';    // 'No bio provided' (使用默认简介)

// 如果使用||会导致问题
const volumeOr = userSettings.volume || 50;         // 50 (错误！静音被替换)
const usernameOr = userSettings.username || 'guest'; // 'guest' (错误！空用户名被替换)
const darkModeOr = userSettings.darkMode || true;   // true (错误！明亮主题被替换)

console.log('使用??:', { volume, username, darkMode, avatar, bio });
console.log('使用||:', { volumeOr, usernameOr, darkModeOr });

// 实际场景：表单验证
function validateForm(formData) {
  return {
    // 这些字段可能有有效的falsy值
    age: formData.age ?? null,              // 0岁是有效年龄
    score: formData.score ?? null,          // 0分是有效分数
    agreed: formData.agreed ?? false,       // false是有效的不同意
    comments: formData.comments ?? '',      // 空字符串是有效的无评论
    
    // 这些字段需要默认值
    name: formData.name ?? 'Anonymous',
    email: formData.email ?? '<EMAIL>'
  };
}

// API响应处理
function processApiResponse(response) {
  const data = response?.data;
  
  return {
    // 数值字段：0是有效值
    count: data?.count ?? 0,
    price: data?.price ?? 0,
    rating: data?.rating ?? 0,
    
    // 布尔字段：false是有效值
    isActive: data?.isActive ?? false,
    isPublic: data?.isPublic ?? true,
    
    // 字符串字段：空字符串可能是有效值
    description: data?.description ?? '',
    tags: data?.tags ?? [],
    
    // 真正需要默认值的字段
    id: data?.id ?? 'unknown',
    name: data?.name ?? 'Untitled'
  };
}`,
    tags: ['falsy值', '设计原理', '语义明确性'],
    relatedQuestions: ['falsy值处理', '默认值策略']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '空值合并操作符的优先级是怎样的？',
    answer: '空值合并操作符(??)的优先级低于大多数其他操作符，但高于赋值操作符。它不能与&&或||直接组合使用，需要用括号明确优先级。这种设计避免了歧义，确保代码的可读性和正确性。',
    code: `// 优先级示例
const a = null;
const b = 0;
const c = 5;

// ??的优先级低于算术运算符
const result1 = a ?? b + c;  // null ?? (0 + 5) = 5
const result2 = (a ?? b) + c; // (null ?? 0) + 5 = 0 + 5 = 5

console.log({ result1, result2 }); // { result1: 5, result2: 5 }

// ??不能与&&或||直接组合
// const invalid = a || b ?? c; // SyntaxError
// const invalid2 = a && b ?? c; // SyntaxError

// 需要使用括号明确优先级
const valid1 = (a || b) ?? c;  // (null || 0) ?? 5 = 0 ?? 5 = 0
const valid2 = a || (b ?? c);  // null || (0 ?? 5) = null || 0 = 0
const valid3 = (a && b) ?? c;  // (null && 0) ?? 5 = null ?? 5 = 5
const valid4 = a && (b ?? c);  // null && (0 ?? 5) = null && 0 = null

console.log({ valid1, valid2, valid3, valid4 });

// 与其他操作符的组合
const obj = { prop: null };
const defaultValue = 'default';

// 属性访问的优先级
const prop1 = obj.prop ?? defaultValue;        // null ?? 'default' = 'default'
const prop2 = obj?.prop ?? defaultValue;       // null ?? 'default' = 'default'

// 函数调用的优先级
function getValue() {
  console.log('getValue called');
  return null;
}

const func1 = getValue() ?? defaultValue;      // 调用函数，然后应用??
const func2 = getValue?.() ?? defaultValue;    // 安全调用，然后应用??

// 三元运算符的优先级
const condition = true;
const ternary1 = condition ? a ?? b : c;       // true ? (null ?? 0) : 5 = 0
const ternary2 = (condition ? a : b) ?? c;     // (true ? null : 0) ?? 5 = null ?? 5 = 5

// 赋值操作符的优先级
let variable = null;
variable = variable ?? 'assigned';             // 'assigned'

// 复杂表达式的优先级
const complex = {
  data: {
    items: null,
    count: 0
  }
};

// 正确的优先级理解
const items = complex?.data?.items ?? [];                    // []
const count = complex?.data?.count ?? 10;                    // 0
const total = (complex?.data?.count ?? 0) + 5;              // 5
const message = \`Total: \${complex?.data?.count ?? 'unknown'}\`; // 'Total: 0'

// 链式空值合并的优先级
const chain = null ?? undefined ?? 0 ?? 'default';         // 0
const chainWithParens = ((null ?? undefined) ?? 0) ?? 'default'; // 0

console.log({ chain, chainWithParens });

// 实际应用中的优先级考虑
function processConfig(config) {
  return {
    // 明确的优先级
    timeout: (config?.timeout ?? 5000) * 1000,           // 转换为毫秒
    retries: Math.max(config?.retries ?? 3, 1),          // 至少重试1次
    url: config?.baseUrl ?? 'https://api.example.com' + '/v1', // 字符串拼接
    
    // 复杂表达式需要括号
    headers: {
      'Authorization': (config?.token ?? '') ? \`Bearer \${config.token}\` : undefined,
      'Content-Type': config?.contentType ?? 'application/json'
    }
  };
}`,
    tags: ['操作符优先级', '语法规则', '表达式组合'],
    relatedQuestions: ['操作符优先级表', '表达式求值顺序']
  }
];

export default commonQuestions;
