import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `空值合并操作符（??）是ES2020引入的逻辑操作符，用于为null或undefined值提供默认值。与逻辑或操作符（||）不同，空值合并操作符只在左侧操作数为null或undefined时才返回右侧操作数，而不会对其他falsy值（如0、''、false）进行替换。这使得它在处理默认值时更加精确和安全。`,

  syntax: `// 基本语法
leftExpr ?? rightExpr

// 常见用法
const result = value ?? defaultValue;

// 链式使用
const result = value1 ?? value2 ?? value3 ?? defaultValue;

// 与可选链结合
const result = obj?.prop ?? defaultValue;

// 赋值操作
variable ??= defaultValue; // ES2021 空值赋值

// 函数参数默认值
function greet(name = user?.name ?? 'Guest') {
  return 'Hello, ' + name + '!';
}`,

  quickExample: `// 基础示例
const user = {
  name: 'John',
  age: 0,        // 注意：0是有效值
  email: '',     // 注意：空字符串是有效值
  avatar: null,  // null需要默认值
  bio: undefined // undefined需要默认值
};

// 使用空值合并操作符
const name = user.name ?? 'Anonymous';        // 'John'
const age = user.age ?? 18;                   // 0 (保持原值)
const email = user.email ?? 'no-email';       // '' (保持原值)
const avatar = user.avatar ?? '/default.png'; // '/default.png'
const bio = user.bio ?? 'No bio available';   // 'No bio available'

// 对比逻辑或操作符
const ageOr = user.age || 18;           // 18 (错误地替换了0)
const emailOr = user.email || 'no-email'; // 'no-email' (错误地替换了空字符串)

console.log('使用??:', { name, age, email, avatar, bio });
console.log('使用||:', { ageOr, emailOr });

// 配置对象的默认值处理
const config = {
  timeout: 0,        // 0是有效的超时值
  retries: null,     // null需要默认值
  debug: false,      // false是有效值
  apiKey: undefined  // undefined需要默认值
};

const finalConfig = {
  timeout: config.timeout ?? 5000,     // 0 (保持原值)
  retries: config.retries ?? 3,        // 3 (使用默认值)
  debug: config.debug ?? false,        // false (保持原值)
  apiKey: config.apiKey ?? 'default-key' // 'default-key'
};

// API响应处理
function processApiResponse(response) {
  return {
    id: response?.data?.id ?? 'unknown',
    name: response?.data?.name ?? 'Unnamed',
    count: response?.data?.count ?? 0,
    
    // 嵌套对象的默认值
    settings: {
      theme: response?.data?.settings?.theme ?? 'light',
      notifications: response?.data?.settings?.notifications ?? true,
      autoSave: response?.data?.settings?.autoSave ?? false
    },
    
    // 数组的默认值
    tags: response?.data?.tags ?? [],
    permissions: response?.data?.permissions ?? ['read']
  };
}

// 链式空值合并
const fallbackChain = primaryValue ?? secondaryValue ?? tertiaryValue ?? 'default';

// 函数参数的默认值
function createUser(options = {}) {
  return {
    name: options.name ?? 'Anonymous',
    role: options.role ?? 'user',
    active: options.active ?? true,
    permissions: options.permissions ?? ['read']
  };
}

// 环境变量处理
const serverConfig = {
  port: process.env.PORT ?? 3000,
  host: process.env.HOST ?? 'localhost',
  debug: process.env.DEBUG ?? false
};

// 空值赋值操作符 (ES2021)
let cache = null;
cache ??= new Map(); // 只在cache为null/undefined时创建新Map

let settings = undefined;
settings ??= { theme: 'light' }; // 只在settings为null/undefined时设置默认值`,

  coreFeatures: [
    {
      feature: "精确的null/undefined检查",
      description: "只对null和undefined值进行替换",
      importance: "high" as const,
      details: "不会错误地替换0、''、false等有效的falsy值"
    },
    {
      feature: "短路求值",
      description: "左侧不为null/undefined时不会计算右侧表达式",
      importance: "high" as const,
      details: "提高性能，避免不必要的计算"
    },
    {
      feature: "链式操作",
      description: "支持多个??操作符的链式使用",
      importance: "medium" as const,
      details: "可以设置多级回退值"
    },
    {
      feature: "与可选链配合",
      description: "与?.操作符完美配合使用",
      importance: "high" as const,
      details: "obj?.prop ?? defaultValue是常见的安全访问模式"
    }
  ],

  keyFeatures: [
    {
      feature: "类型安全",
      description: "在TypeScript中提供更准确的类型推断",
      importance: "medium" as const,
      details: "帮助TypeScript更好地理解默认值类型"
    },
    {
      feature: "可读性",
      description: "明确表达了'仅在null/undefined时使用默认值'的意图",
      importance: "high" as const,
      details: "比||操作符更清晰地表达了开发者的意图"
    },
    {
      feature: "向后兼容",
      description: "不影响现有代码，可以渐进式采用",
      importance: "medium" as const,
      details: "可以与现有的||操作符混合使用"
    }
  ],

  limitations: [
    "只检查null和undefined，不检查其他falsy值",
    "需要较新的JavaScript环境支持",
    "与||操作符的行为差异可能导致迁移时的困惑",
    "在某些边缘情况下可能需要额外的类型检查",
    "不能用于检查对象属性是否存在"
  ],

  bestPractices: [
    "优先使用??而不是||来设置默认值",
    "与可选链操作符(?.)结合使用",
    "在配置对象和API响应处理中广泛使用",
    "使用??=进行条件赋值",
    "在TypeScript中利用其类型推断优势"
  ],

  warnings: [
    "??只检查null和undefined，不检查其他falsy值",
    "迁移现有||代码时需要仔细检查行为差异",
    "不要与||操作符混淆使用场景"
  ]
,

  scenarioDiagram: `graph TD
    A[空值合并使用场景] --> B[默认值设置]
    A --> C[配置对象处理]
    A --> D[API响应处理]
    A --> E[表单数据处理]

    B --> B1[函数参数默认值]
    B --> B2[对象属性默认值]
    B --> B3[环境变量处理]

    C --> C1[用户偏好设置]
    C --> C2[主题配置]
    C --> C3[功能开关]

    D --> D1[可选字段处理]
    D --> D2[嵌套对象访问]
    D --> D3[错误数据兜底]

    E --> E1[输入框默认值]
    E --> E2[选择器默认选项]
    E --> E3[复选框状态]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`
};

export default basicInfo;
