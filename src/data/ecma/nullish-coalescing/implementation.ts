import { Implementation } from '@/types/api';

const implementation: Implementation = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  mechanism: `空值合并操作符的实现基于精确的null/undefined检查和短路求值机制。JavaScript引擎在解析时会将??表达式转换为条件检查，只有当左侧操作数严格等于null或undefined时才会求值右侧表达式。

核心实现原理：

1. **精确类型检查**
   - 只检查null和undefined
   - 不检查其他falsy值（0, '', false, NaN）
   - 使用严格相等比较（===）

2. **短路求值**
   - 左侧不为null/undefined时不计算右侧
   - 提高性能，避免副作用
   - 支持链式操作

3. **语法转换**
   - a ?? b 转换为 a !== null && a !== undefined ? a : b
   - 编译器优化，减少运行时开销
   - 支持复杂表达式的组合

4. **类型推断**
   - 在TypeScript中提供准确的类型推断
   - 排除null/undefined类型
   - 支持联合类型的处理`,

  visualization: `graph TD
    A[leftExpr ?? rightExpr] --> B{leftExpr === null?}
    B -->|Yes| C[Return rightExpr]
    B -->|No| D{leftExpr === undefined?}
    D -->|Yes| C
    D -->|No| E[Return leftExpr]
    
    F[Short Circuit] --> G[No Side Effects]
    G --> H[Performance Optimized]
    
    style A fill:#e1f5fe
    style C fill:#e8f5e8
    style E fill:#e8f5e8`,
    
  plainExplanation: `简单来说，空值合并操作符就像是一个"精确的替补队员"。

想象一下：
- 逻辑或（||）就像是一个"过度热心的替补"，只要主力队员表现不够强势（falsy），就立刻上场
- 空值合并（??）就像是一个"专业的替补"，只有当主力队员真的不在场（null/undefined）时才上场
- 即使主力队员今天状态不好（0分、空字符串、false），专业替补也不会贸然替换

这种设计让你能够保留所有有意义的值，只在真正"缺席"时才使用备选方案。`,

  designConsiderations: [
    '精确性 - 只处理真正的"空值"，保留所有有效的falsy值',
    '性能 - 短路求值避免不必要的计算',
    '可读性 - 明确表达"仅在null/undefined时使用默认值"的意图',
    '类型安全 - 在TypeScript中提供更准确的类型推断',
    '向后兼容 - 不影响现有的||操作符使用'
  ],
  
  relatedConcepts: [
    '短路求值：逻辑运算符的求值策略',
    '可选链：与??配合的安全属性访问',
    '类型守卫：TypeScript中的类型安全检查',
    '默认参数：函数参数的默认值处理',
    '逻辑运算符：||、&&等传统逻辑操作符'
  ]
};

export default implementation;
