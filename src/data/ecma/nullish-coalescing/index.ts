import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const nullishCoalescingData: ApiItem = {
  id: 'nullish-coalescing',
  title: 'Nullish Coalescing (??)',
  description: 'ES2020空值合并操作符，提供精确的默认值处理，只对null和undefined生效',
  category: 'ECMA特性',
  difficulty: 'easy',
  
  syntax: `leftExpr ?? rightExpr; value ?? defaultValue;`,
  example: `const name = user.name ?? 'Anonymous'; const count = data.count ?? 0;`,
  notes: '空值合并操作符是ES2020引入的逻辑操作符，只在左侧为null或undefined时返回右侧值',
  
  version: 'ES2020',
  tags: ['ES2020', 'JavaScript', '空值合并', '默认值', '逻辑操作符'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default nullishCoalescingData;
