import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: '空值合并操作符(??)与逻辑或操作符(||)有什么区别？',
    answer: `空值合并操作符(??)与逻辑或操作符(||)的主要区别：

**检查范围不同**：
- ?? 只检查null和undefined
- || 检查所有falsy值（null, undefined, 0, '', false, NaN）

**使用场景不同**：
- ?? 用于提供默认值，保留有效的falsy值
- || 用于逻辑判断，替换所有falsy值

**类型安全性**：
- ?? 在TypeScript中提供更准确的类型推断
- || 可能导致意外的类型转换

**语义明确性**：
- ?? 明确表达"仅在null/undefined时使用默认值"
- || 表达"使用第一个truthy值"`,
   
    difficulty: 'medium',
    frequency: 'high',
    category: '操作符对比',
    tags: ['空值合并', '逻辑或', '操作符对比'],
    
    code: `// 对比示例
const config = {
  port: 0,           // 0是有效的端口号
  debug: false,      // false是有效的调试设置
  name: '',          // 空字符串可能是有效的名称
  timeout: null,     // null需要默认值
  retries: undefined // undefined需要默认值
};

// 使用逻辑或操作符 (||)
const portOr = config.port || 3000;           // 3000 (错误！0被替换)
const debugOr = config.debug || true;         // true (错误！false被替换)
const nameOr = config.name || 'default';      // 'default' (错误！空字符串被替换)
const timeoutOr = config.timeout || 5000;     // 5000 (正确)
const retriesOr = config.retries || 3;        // 3 (正确)

// 使用空值合并操作符 (??)
const portNull = config.port ?? 3000;         // 0 (正确！保留有效值)
const debugNull = config.debug ?? true;       // false (正确！保留有效值)
const nameNull = config.name ?? 'default';    // '' (正确！保留有效值)
const timeoutNull = config.timeout ?? 5000;   // 5000 (正确)
const retriesNull = config.retries ?? 3;      // 3 (正确)

console.log('使用 ||:', { portOr, debugOr, nameOr, timeoutOr, retriesOr });
console.log('使用 ??:', { portNull, debugNull, nameNull, timeoutNull, retriesNull });

// 实际应用场景
function createServer(options = {}) {
  return {
    // ❌ 错误：使用||可能导致问题
    port: options.port || 3000,        // 如果传入0会被替换为3000
    debug: options.debug || false,     // 如果传入false会保持false（这里还好）
    
    // ✅ 正确：使用??保留有效值
    host: options.host ?? 'localhost',
    timeout: options.timeout ?? 30000,
    ssl: options.ssl ?? false
  };
}

// 测试
const server1 = createServer({ port: 0, debug: false });
console.log(server1); // port会被错误地设置为3000而不是0

const server2 = createServer({ port: 0, debug: false, host: 'example.com' });
// 使用??的版本会正确保留port: 0

// 链式使用的对比
const value1 = null;
const value2 = 0;
const value3 = 'default';

// 逻辑或链式
const resultOr = value1 || value2 || value3; // 'default' (跳过了有效的0)

// 空值合并链式
const resultNull = value1 ?? value2 ?? value3; // 0 (保留了有效的0)

// TypeScript中的类型推断差异
interface Config {
  count?: number;
  enabled?: boolean;
  name?: string;
}

function processConfig(config: Config) {
  // 使用||的类型推断
  const countOr = config.count || 10;     // 类型: number
  const enabledOr = config.enabled || false; // 类型: boolean
  
  // 使用??的类型推断更准确
  const countNull = config.count ?? 10;      // 类型: number
  const enabledNull = config.enabled ?? false; // 类型: boolean
  
  // ??能更好地处理联合类型
  const nameNull = config.name ?? 'unknown'; // 保留空字符串
}`,
    
    followUp: [
      '什么时候应该使用??而不是||？',
      '??=赋值操作符如何工作？',
      '在TypeScript中??的类型推断优势？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '空值合并赋值操作符(??=)是如何工作的？',
    answer: `空值合并赋值操作符(??=)是ES2021引入的复合赋值操作符，它只在左侧变量为null或undefined时才进行赋值。

**工作原理**：
- x ??= y 等价于 x ?? (x = y)
- 只有当x为null或undefined时才执行赋值
- 具有短路特性，避免不必要的赋值操作

**使用场景**：
- 初始化可能为null/undefined的变量
- 缓存和懒加载
- 配置对象的默认值设置
- 条件性的属性添加`,
   
    difficulty: 'medium',
    frequency: 'medium',
    category: '赋值操作符',
    tags: ['空值合并赋值', '??=', 'ES2021'],
    
    code: `// 基本用法
let config = null;
config ??= { theme: 'light' }; // 赋值，因为config是null
console.log(config); // { theme: 'light' }

config ??= { theme: 'dark' }; // 不赋值，因为config不是null/undefined
console.log(config); // 仍然是 { theme: 'light' }

// 对比其他赋值操作符
let value1 = 0;
let value2 = 0;
let value3 = 0;

value1 ||= 10;  // 赋值，因为0是falsy
value2 &&= 10;  // 不赋值，因为0是falsy
value3 ??= 10;  // 不赋值，因为0不是null/undefined

console.log({ value1, value2, value3 }); // { value1: 10, value2: 0, value3: 0 }

// 实际应用：缓存和懒加载
class DataManager {
  constructor() {
    this.cache = new Map();
  }
  
  async getData(key) {
    // 使用??=实现缓存
    this.cache.get(key) ??= await this.fetchData(key);
    return this.cache.get(key);
  }
  
  async fetchData(key) {
    // 模拟API调用
    console.log(\`Fetching data for \${key}\`);
    return { key, data: \`Data for \${key}\`, timestamp: Date.now() };
  }
}

// 配置对象初始化
class ConfigManager {
  constructor() {
    this.config = {};
  }
  
  setDefaults() {
    // 只在属性不存在时设置默认值
    this.config.theme ??= 'light';
    this.config.language ??= 'en';
    this.config.timeout ??= 5000;
    this.config.retries ??= 3;
    
    // 嵌套对象的初始化
    this.config.ui ??= {};
    this.config.ui.sidebar ??= { expanded: true };
    this.config.ui.toolbar ??= { visible: true };
  }
  
  updateConfig(updates) {
    // 合并配置，保留现有的非null/undefined值
    for (const [key, value] of Object.entries(updates)) {
      this.config[key] ??= value;
    }
  }
}

// 数组和对象的初始化
class UserPreferences {
  constructor(userId) {
    this.userId = userId;
    this.preferences = {};
  }
  
  initializePreferences() {
    // 初始化各种类型的偏好设置
    this.preferences.notifications ??= [];
    this.preferences.bookmarks ??= new Set();
    this.preferences.history ??= new Map();
    this.preferences.settings ??= {
      theme: 'auto',
      language: 'en',
      timezone: 'UTC'
    };
  }
  
  addNotification(notification) {
    this.preferences.notifications ??= [];
    this.preferences.notifications.push(notification);
  }
  
  addBookmark(bookmark) {
    this.preferences.bookmarks ??= new Set();
    this.preferences.bookmarks.add(bookmark);
  }
}

// 函数参数的默认值处理
function processOptions(options = {}) {
  // 确保选项对象有默认值
  options.retries ??= 3;
  options.timeout ??= 5000;
  options.cache ??= true;
  
  // 嵌套选项的处理
  options.headers ??= {};
  options.headers['Content-Type'] ??= 'application/json';
  options.headers['Accept'] ??= 'application/json';
  
  return options;
}

// 性能对比：??= vs 传统方式
function traditionalWay(obj) {
  if (obj.property === null || obj.property === undefined) {
    obj.property = 'default';
  }
}

function modernWay(obj) {
  obj.property ??= 'default';
}

// 链式初始化
class ChainedInit {
  constructor() {
    this.data = {};
  }
  
  init() {
    // 链式初始化多个属性
    this.data.level1 ??= {};
    this.data.level1.level2 ??= {};
    this.data.level1.level2.level3 ??= [];
    
    return this;
  }
  
  addItem(item) {
    this.init(); // 确保结构存在
    this.data.level1.level2.level3.push(item);
    return this;
  }
}

// 使用示例
const manager = new ChainedInit();
manager.addItem('item1').addItem('item2');`,
    
    followUp: [
      '??=与||=、&&=的区别？',
      '??=的性能特点？',
      '在什么情况下??=比传统赋值更好？'
    ]
  }
];

export default interviewQuestions;
