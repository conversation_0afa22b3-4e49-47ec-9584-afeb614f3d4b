/**
 * ❓ Tab 5: 常见问题 (common-questions.ts) - 核心指导原则
 *
 * 🚨 重要编码约束（最高优先级）
 *
 * ❌ 严格禁止的语法
 * 绝对不能使用任何形式的模板字符串插值语法 `${xxx}`
 *
 * 🛡️ 常见问题编码特殊要求
 * - 错误示例: 展示错误代码时不使用模板字符串
 * - 解决方案: 修复代码中避免模板字符串语法
 * - 调试技巧: 调试代码使用标准输出方式
 * - 最佳实践: 推荐代码遵循项目编码规范
 *
 * 🎯 Tab定位与价值 - 实战救援版
 *
 * 🎭 **身份定位**：你是一位在各种项目中踩过无数坑的老司机
 * 还记得深夜被Promise错误折磨到怀疑人生的痛苦经历
 *
 * 💡 **核心使命**：常见问题Tab是**深夜救援的明灯**
 * 当开发者在凌晨2点被Promise问题困扰时，这里就是他们的救命稻草
 *
 * 🏗️ **价值序列**：
 * - 实用性 >>> 全面性：能立即解决问题的，比理论完整更重要
 * - 深度 > 广度：一个问题挖透，胜过十个问题浅尝
 * - 连接 > 孤立：展现问题间的关系网，避免重复踩坑
 *
 * 🌊 **表达温度**：像一位愿意分享踩坑经验的老友：
 * "这个问题我也遇到过，当时差点让整个项目延期，后来发现原来是..."
 *
 * 🎨 **美学追求**：每个问题的解答都应该让人有"原来如此"的顿悟感
 * 不只是给出解决方案，更要解释为什么会出现这个问题
 *
 * 📊 内容结构要求 - 必须包含的问题数组：
 * 每个常见问题必须包含：
 * - id: 问题ID
 * - question: 🆕 真实开发问题（基于实际项目经验）
 * - answer: 🆕 简化结构 - 直接提供解决方案和代码示例
 * - code: 🆕 完整代码示例（包含执行顺序注释和复杂示例）
 * - tags: 相关标签
 * - relatedQuestions: 🆕 相关问题（替代原来的复杂结构）
 *
 * 🆕 已实现高质量问题：
 * - 微任务队列执行顺序（包含复杂示例和输出注释）
 * - Promise未捕获错误处理（Node.js和浏览器环境）
 * - Promise链式调用中的错误传播
 * - async/await与Promise的性能对比
 * - Promise内存泄漏问题和解决方案
 * - Promise超时处理和取消机制
 *
 * 🎯 质量标准
 * - 问题真实：基于真实开发经验，不是人为构造的问题
 * - 频率准确：反映问题在实际开发中的真实出现频率
 * - 解决方案有效：提供的解决方案经过验证，确实能解决问题
 * - 原因解释清晰：不仅给出解决方案，还解释问题产生的根本原因
 * - 预防措施实用：提供实用的预防措施，避免问题再次发生
 * - 代码示例对比：通过错误和正确示例的对比，加深理解
 * - 相关问题关联：关联相似或相关的问题，形成知识网络
 *
 * 💡 问题收集策略
 * - 社区反馈：从GitHub Issues、Stack Overflow等收集真实问题
 * - 项目经验：基于实际项目开发中遇到的问题
 * - 新手困惑：关注新手开发者容易遇到的困惑点
 * - 版本升级：收集版本升级过程中的兼容性问题
 * - 最佳实践：整理违反最佳实践导致的常见问题
 */

import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'flat-vs-flatmap-confusion',
    question: '`flat()` 和 `flatMap()` 我总是搞混，到底应该用哪个？',
    answer: '一个简单的判断方法：如果你需要先对数组的每个元素进行处理（映射），并且处理结果可能是一个数组，然后你希望将这些结果合并成一个大数组，那么就用 `flatMap()`。如果你的数组已经是嵌套好的，你只是单纯地想把它"拍平"，那就用 `flat()`。',
    code: `
// 场景1：你已经有了一个嵌套数组，只想拍平它
const nestedArray = [[1, 2], [3, 4], [5]];
// 需求：得到 [1, 2, 3, 4, 5]
// ✅ 最佳选择: flat()
const flattened = nestedArray.flat(); 

// 场景2：你想从一个数组生成一个新数组，且生成过程可能产生数组
const people = [{ name: 'Alice', skills: ['React', 'CSS'] }, { name: 'Bob', skills: ['Node.js'] }];
// 需求：得到所有人的技能列表 ['React', 'CSS', 'Node.js']

// ❌ 错误的方式：只用 map
const skillsNested = people.map(p => p.skills);
// 结果是 [['React', 'CSS'], ['Node.js']]，不是我们想要的

// ✅ 正确方式1：map + flat
const skillsFlattened = people.map(p => p.skills).flat();

// ✅ 最佳方式：直接用 flatMap
const skillsViaFlatMap = people.flatMap(p => p.skills);

console.log('flatMap 的结果:', skillsViaFlatMap);
// flatMap 在这个场景下更简洁、高效。
    `,
    tags: ['flat', 'flatMap', '选择困难'],
    relatedQuestions: ['`flat()` 和 `flatMap()` 的性能差异', '`flatMap()`的深度为什么总是1']
  },
  {
    id: 'empty-slot-removal',
    question: '为什么 `[1, 2, , 4].flat()` 的结果是 `[1, 2, 4]`？那个空位去哪了？',
    answer: '这是 `flat()` 方法的一个明确设计：它会在扁平化过程中自动移除（跳过）数组中的空槽（empty slots）。这通常是期望的行为，因为它能帮助清理数据。空槽在JavaScript中是一个特殊的存在，很多现代数组方法都会以特定方式处理它，`flat()` 的方式就是直接无视它。',
    code: `
const arrayWithHoles = [1, , 3, [4, , 5]];

// flat() 会移除所有层级的空槽
const flattened = arrayWithHoles.flat();

console.log(flattened); // 输出: [1, 3, 4, 5]
console.log('结果数组的长度:', flattened.length); // 4

// 对比其他方法对空槽的处理
let mapCount = 0;
arrayWithHoles.map(x => {
  mapCount++; // map会跳过空槽，所以这里只执行4次
  return x * 2;
});
console.log('map 循环次数:', mapCount); // 4

let forEachCount = 0;
arrayWithHoles.forEach(() => forEachCount++); // forEach 也会跳过
console.log('forEach 循环次数:', forEachCount); // 4
    `,
    tags: ['flat', '空槽', '稀疏数组', '数组特性'],
    relatedQuestions: ['JavaScript中空槽和 `undefined` 的区别', '还有哪些数组方法会忽略空槽？']
  },
  {
    id: 'flat-infinity-performance',
    question: '我有一个非常深的嵌套数组，用 `flat(Infinity)` 会不会有性能问题或导致崩溃？',
    answer: '对于大多数常规尺寸的数组，即使嵌套很深，现代JavaScript引擎对 `flat(Infinity)` 的优化也很好，不太可能导致崩溃。引擎内部使用迭代方式而非真正无限递归，避免了"栈溢出"。但需要注意，如果数组本身极大（例如，包含数百万个元素），扁平化过程会创建一个同样巨大的新数组，这可能会消耗大量内存，导致页面卡顿或内存溢出，但这主要是由数据大小而非深度引起的。',
    code: `
// 一个深度嵌套但元素不多的数组
const deepArray = [1, [2, [3, [4, [5, [6, [7, [8, [9]]]]]]]]];
const start1 = performance.now();
deepArray.flat(Infinity);
const end1 = performance.now();
console.log('深度嵌套处理耗时:', end1 - start1, 'ms'); // 通常非常快

// 一个嵌套不深但元素极多的数组
const largeArray = [];
for (let i = 0; i < 1000000; i++) {
  largeArray.push([i]);
}
const start2 = performance.now();
try {
  const largeFlattened = largeArray.flat();
  console.log('巨大数组处理耗时:', performance.now() - start2, 'ms'); // 耗时明显增加
  console.log('创建的新数组长度:', largeFlattened.length);
} catch (e) {
  console.error('可能发生内存溢出:', e);
}

// 结论：性能瓶颈通常在于最终生成数组的"总元素数量"，而非"嵌套深度"。
    `,
    tags: ['flat', 'Infinity', '性能', '内存', '栈溢出'],
    relatedQuestions: ['手写一个不会栈溢出的 `flat` polyfill', 'JavaScript引擎如何优化递归？']
  },
  {
    id: 'flat-non-array-types',
    question: '`flat()` 能不能扁平化对象数组或者其他可迭代对象？',
    answer: '不能。`flat()` 方法是专门为数组设计的，它只会检查和展平类型为 `Array` 的元素。如果数组中包含对象、字符串、Set、Map或其他可迭代对象，`flat()` 会将它们视为普通元素，直接原封不动地复制到新数组中，并不会尝试去遍历或"拉平"它们的内容。',
    code: `
const mixedContent = [
  1,
  { id: 1, items: [10, 20] }, // 一个对象
  new Set([3, 4]),             // 一个 Set
  'a string',                  // 一个字符串
  [5, 6]                       // 一个数组
];

const flattened = mixedContent.flat();

console.log(flattened);
// 输出: 
// [
//   1,
//   { id: 1, items: [10, 20] }, // 对象原样保留
//   Set(2) { 3, 4 },             // Set 原样保留
//   'a string',                  // 字符串原样保留
//   5,                           // 数组被展平
//   6
// ]
    `,
    tags: ['flat', '类型', '对象', '可迭代对象'],
    relatedQuestions: ['如何扁平化一个包含可迭代对象的数组？', '`Array.from()` 和 `flat()` 有什么关系？']
  },
  {
    id: 'immutability-of-flat',
    question: '为什么 `flat()` 不改变原数组？这有什么好处？',
    answer: '`flat()` 及许多现代JavaScript数组方法（如 `map`, `filter`, `reduce`）被设计为不修改原数组，而是返回一个新数组。这遵循了函数式编程中的"不可变性"（Immutability）原则。好处是巨大的：\n1. **可预测性**: 代码行为更可预测，你不用担心一个函数在不经意间修改了你的数据。\n2. **易于调试**: 当数据发生变化时，追溯其来源更容易，因为它是新创建的，而不是在某个地方被修改了。\n3. **状态管理**: 在React、Vue等现代框架中，不可变性是高效进行状态变更检测的核心，可以优化渲染性能。',
    code: `
const originalArray = [1, [2, 3]];

// flat() 返回一个新数组
const newArray = originalArray.flat();

console.log('新数组:', newArray);       // [1, 2, 3]
console.log('原数组:', originalArray); // [1, [2, 3]] (没有被改变)

// 对比会改变原数组的方法，如 splice
const mutableArray = [1, 2, 3, 4];
// splice 会在原数组上操作
const removed = mutableArray.splice(1, 2); 

console.log('被移除的元素:', removed);      // [2, 3]
console.log('被修改后的原数组:', mutableArray); // [1, 4] (发生了变化)

// 在React中的好处 (伪代码)
// function MyComponent({ list }) {
//   const handleAddItem = () => {
//     const newList = [...list, newItem]; // 使用扩展运算符创建新数组（不可变）
//     // const mutableList = list.push(newItem); // 错误的方式，修改了state
//     setList(newList); // React能轻易检测到状态变化
//   }
// }
    `,
    tags: ['flat', '不可变性', '函数式编程', 'React'],
    relatedQuestions: ['除了`flat`还有哪些数组方法是不可变的？', '什么是纯函数？']
  }
];

export default commonQuestions;
