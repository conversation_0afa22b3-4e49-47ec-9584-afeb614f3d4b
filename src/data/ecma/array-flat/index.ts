import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const arrayFlatData: ApiItem = {
  id: 'array-flat',
  title: 'Array.flat()',
  description: 'ES2019 引入的数组实例方法，用于将嵌套的数组结构按照指定的深度递归展开，返回一个新的扁平化后的一维数组。',
  category: 'ECMA特性',
  difficulty: 'easy',

  syntax: `arr.flat(depth?)`,
  example: `const arr = [1, 2, [3, 4, [5, 6]]]; arr.flat(2); // [1, 2, 3, 4, 5, 6]`,
  notes: 'flat() 不会改变原数组，它会返回一个新数组。如果想原地修改，这不是合适的工具。',

  version: 'ES2019 (ES10)',
  tags: ['ES2019', 'JavaScript', 'Array', 'Flatten', 'Data Manipulation'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default arrayFlatData;
