/**
 * 🎯 Tab 4: 面试准备 (interview-questions.ts) - 核心指导原则
 *
 * 🚨 重要编码约束（最高优先级）
 *
 * ❌ 严格禁止的语法
 * 绝对不能使用任何形式的模板字符串插值语法 `${xxx}`
 *
 * 🛡️ 面试准备编码特殊要求
 * - 代码演示: 手写代码示例不使用模板字符串
 * - 算法实现: 算法题解中避免模板字符串语法
 * - 调试输出: console.log使用多参数形式
 * - 测试用例: 测试代码中使用标准字符串拼接
 *
 * 🎯 Tab定位与价值 - 实战升级版
 *
 * 🎭 **身份定位**：你是一位既当过面试官也当过求职者的技术前辈
 * 深知面试官真正想考察什么，也理解求职者内心的焦虑和困惑
 *
 * 💡 **核心信念**：真正的面试智慧不在于背诵答案，而在于理解问题的本质
 * 好的面试准备不是倾倒知识，而是点亮思路
 *
 * 🏗️ **承重墙思维**：帮助求职者识别面试中的：
 * - 🏗️ **承重墙问题**：不会就直接挂掉的核心概念（Promise状态、微任务）
 * - 🎨 **装饰品问题**：答对加分但不致命的细节（某些边缘API）
 * - 🚪 **暗门问题**：答好就能让面试官眼前一亮的深度思考
 *
 * 🌊 **表达温度**：像咖啡馆里的推心置腹对话：
 * "面试官问Promise状态，其实是想知道你对异步编程的理解深度..."
 *
 * 🎯 **终极目标**：让求职者面试结束后能自信地说：
 * "我不只是回答了问题，更是展现了我对异步编程的深度理解"
 *
 * 📊 内容结构要求 - 必须包含的面试题数组：
 * 每个面试题必须包含：
 * - id: 题目ID
 * - question: 🆕 真实面试问题（基于实际面试经验）
 * - difficulty: 难度等级 ('easy' | 'medium' | 'hard')
 * - frequency: 🆕 真实出现频率 ('high' | 'medium' | 'low')
 * - category: 问题分类
 * - answer: 答案对象
 *   - brief: 🆕 精准简短回答（30-50字，面试官满意的标准答案）
 *   - detailed: 🆕 分层详细解析（基础概念→深入原理→实际应用）
 *   - code: 🆕 完整可运行代码示例（包含注释和对比）
 *   - followUp: 🆕 真实追问题目（面试官常问的后续问题）
 * - tags: 相关标签
 * - 🆕 已实现高质量题目：Promise状态、Promise.all vs allSettled、微任务队列、错误处理等
 *
 * 🎯 质量标准
 * - 问题真实：基于真实面试经验，不是人为构造的问题
 * - 难度合理：覆盖初级、中级、高级不同层次
 * - 答案准确：技术答案准确无误，经过验证
 * - 解析深入：不仅给出答案，还解释为什么这样回答
 * - 代码可运行：所有代码示例完整且可执行
 * - 追问准备：预测面试官可能的追问，提前准备
 * - 公司针对性：标注哪些公司常考这类问题
 *
 * 💡 面试策略
 * - 基础必答：确保基础概念问题能够流利回答
 * - 原理深入：能够解释底层实现原理和设计思路
 * - 实战结合：结合实际项目经验回答问题
 * - 对比分析：能够对比不同方案的优缺点
 * - 扩展思考：展示对相关技术的深度理解
 */

import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 'flat-basic-usage',
    question: '`Array.prototype.flat()` 的作用是什么？请举一个基本例子。',
    answer: {
      brief: '`flat()` 用于将嵌套的数组"拉平"成一个新的一维数组。它默认只会拉平一层。',
      detailed: `\`Array.prototype.flat()\` 是ES2019引入的一个数组实例方法，它的主要作用是将一个包含嵌套数组的数组，按照指定的深度（默认为1），递归地"展平"并返回一个全新的数组。这个过程不会修改原始数组。`,
      code: `
const nestedArray = [1, 2, [3, 4]];

// 默认深度为 1
const flattened = nestedArray.flat();

console.log(flattened); // 输出: [1, 2, 3, 4]
console.log(nestedArray); // 输出: [1, 2, [3, 4]] (原数组不变)

// 包含更深嵌套的例子
const deeperArray = [1, [2, [3, [4]]]];
const flattenedOnce = deeperArray.flat(); // 只拉平一层
console.log(flattenedOnce); // 输出: [1, 2, [3, [4]]]
      `
    },
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    tags: ['flat', '数组', '扁平化'],
    followUp: [
      '如果我想拉平多层，应该怎么做？',
      '这个方法会改变原数组吗？',
      '如果数组里包含非数组元素，会发生什么？'
    ]
  },
  {
    id: 'flat-vs-flatMap',
    question: '`flat()` 和 `flatMap()` 有什么区别？什么场景下应该优先使用 `flatMap()`？',
    answer: {
      brief: '`flatMap()` 等同于先对数组进行 `map()` 操作，然后再执行深度为1的 `flat()` 操作。当需要先映射数据再展平时，应优先使用 `flatMap()`，因为它更高效。',
      detailed: `**主要区别**:
1.  **功能组合**: \`flatMap()\` 是 \`map()\` 和 \`flat(1)\` 的结合体。它在一个步骤中完成了映射和展平两件事。
2.  **效率**: 因为 \`flatMap\` 是一次性操作，它比先调用 \`map()\` 再调用 \`flat()\` 更高效，因为它不需要创建中间数组。
3.  **深度**: \`flatMap()\` 的展平深度固定为1，无法自定义。而 \`flat()\` 可以通过参数指定任意深度。

**使用场景**:
当你的逻辑是"对数组中每个元素进行处理，并且每个元素的处理结果可能是一个数组，最后需要将这些结果合并成一个单一数组"时，就应该优先使用 \`flatMap()\`。`,
      code: `
const sentences = ["hello world", "how are you"];

// 场景：将句子分割成单词列表

// 方法一：使用 map + flat
const words1 = sentences.map(s => s.split(' ')).flat();
console.log(words1); // ["hello", "world", "how", "are", "you"]
// 这个过程创建了一个中间数组: [["hello", "world"], ["how", "are", "you"]]

// 方法二：使用 flatMap
const words2 = sentences.flatMap(s => s.split(' '));
console.log(words2); // ["hello", "world", "how", "are", "you"]
// 这个过程一步到位，没有中间数组，效率更高。
      `
    },
    difficulty: 'medium',
    frequency: 'high',
    category: '方法对比',
    tags: ['flat', 'flatMap', 'map', '性能优化'],
    followUp: [
      '既然有了 `flatMap`，为什么还需要 `flat`？',
      '如果 `flatMap` 的映射函数返回一个深层嵌套数组，会发生什么？',
      '你能手写一个 `flatMap` 的 polyfill 吗？'
    ]
  },
  {
    id: 'flat-edge-cases',
    question: '`Array.flat()` 如何处理数组中的空槽（empty slots）、`undefined` 和 `null`？',
    answer: {
      brief: '`flat()` 会移除数组中的空槽，但会保留 `undefined` 和 `null` 元素。',
      detailed: `这是 \`flat()\` 的一个重要特性，也是面试中常考察的细节：
- **空槽 (Empty Slots)**: 在扁平化过程中，数组中的空槽会被直接忽略和移除，不会在返回的新数组中留下任何痕迹（如 \`undefined\`）。
- **\`undefined\` 和 \`null\`**: 这两个值被视为数组的有效元素，会在扁平化过程中被保留下来。`,
      code: `
const mixedArray = [1, , 3, [4, undefined, 5], null];
// 注意索引1的位置是一个空槽

const flattened = mixedArray.flat();

console.log(flattened); 
// 输出: [1, 3, 4, undefined, 5, null]

console.log('新数组长度:', flattened.length); // 6
console.log('新数组是否包含undefined:', flattened.includes(undefined)); // true
console.log('原数组长度:', mixedArray.length); // 5 (空槽也占长度)
      `
    },
    difficulty: 'medium',
    frequency: 'medium',
    category: '边界情况',
    tags: ['flat', '空槽', 'undefined', 'null'],
    followUp: [
      '为什么 `flat` 要设计成移除空槽？这样做有什么好处？',
      '除了 `flat`，还有哪些数组方法会特殊处理空槽？（例如 `map`, `forEach` 会跳过）',
    ]
  },
  {
    id: 'flat-infinity-depth',
    question: '如何使用 `flat()` 将一个任意深度的嵌套数组完全展平为一维数组？',
    answer: {
      brief: '向 `flat()` 方法传入 `Infinity` 作为参数，即 `arr.flat(Infinity)`，可以实现对任意深度嵌套数组的完全扁平化。',
      detailed: `\`flat()\` 方法接受一个可选的 \`depth\` 参数，用于指定要展平的嵌套深度。如果提供一个非常大的数字或者直接使用全局属性 \`Infinity\`，\`flat()\` 将会递归地展平所有层级的嵌套数组，直到数组中不再包含任何数组为止。`,
      code: `
const deeplyNestedArray = [1, [2, [3, [4, [5]]]]]];

// 使用 Infinity 来完全"拍平"数组
const completelyFlattened = deeplyNestedArray.flat(Infinity);

console.log(completelyFlattened); // 输出: [1, 2, 3, 4, 5]

// 对比指定有限的深度
const flattenedToDepth2 = deeplyNestedArray.flat(2);
console.log(flattenedToDepth2); // 输出: [1, 2, 3, [4, [5]]]
      `
    },
    difficulty: 'easy',
    frequency: 'medium',
    category: '高级用法',
    tags: ['flat', 'Infinity', '递归', '深度'],
    followUp: [
      '使用 `Infinity` 会有性能问题吗？什么情况下需要注意？',
      '除了 `Infinity`，我还可以传入其他什么值来实现同样的效果吗？（比如一个足够大的数字）',
    ]
  },
  {
    id: 'flat-polyfill',
    question: '如果需要在不支持ES2019的环境中使用 `flat`，请你手写一个它的 Polyfill。',
    answer: {
      brief: '可以通过递归或迭代（使用栈）的方式来实现。迭代法能更好地避免因递归深度过大导致的栈溢出问题。核心是遍历数组，判断元素是否为数组以及当前深度是否大于0，然后决定是继续递归/入栈还是直接添加到结果中。',
      detailed: `手写 \`flat\` 的 Polyfill 时，需要考虑以下几点：
1.  **处理 \`this\`**: 确保方法在 \`null\` 或 \`undefined\` 上调用时会抛出错误。
2.  **depth 参数**: 正确处理默认深度（为1）和传入的自定义深度。
3.  **递归与迭代**: 递归实现思路简单，但有栈溢出风险。使用栈的迭代实现更健壮。
4.  **空槽处理**: 规范要求跳过空槽，实现时需要检查属性是否存在。
5.  **返回新数组**: 必须返回一个新数组，不能修改原数组。`,
      code: `
function flatPolyfill(depth = 1) {
  // 1. 检查调用上下文
  if (this == null) {
    throw new TypeError('Array.prototype.flat called on null or undefined');
  }

  const result = [];

  // 辅助函数，使用递归实现
  function flatten(arr, d) {
    for (let i = 0; i < arr.length; i++) {
      // 4. 处理空槽
      if (Object.prototype.hasOwnProperty.call(arr, i)) {
        const element = arr[i];
        // 3. 判断是否是数组以及深度是否足够
        if (Array.isArray(element) && d > 0) {
          flatten(element, d - 1);
        } else {
          result.push(element);
        }
      }
    }
  }

  flatten(this, depth);
  return result;
}

// ---- 测试 Polyfill ----
const array = [1, [2, [3, 4]], 5];

// 模拟绑定到 Array.prototype
Array.prototype.myFlat = flatPolyfill;

console.log('深度为1:', array.myFlat());      // [1, 2, [3, 4], 5]
console.log('深度为2:', array.myFlat(2));      // [1, 2, 3, 4, 5]
console.log('完全展平:', array.myFlat(Infinity)); // [1, 2, 3, 4, 5]

const withEmptySlots = [1, , [2, , 3]];
console.log('处理空槽:', withEmptySlots.myFlat()); // [1, 2, 3]
      `
    },
    difficulty: 'hard',
    frequency: 'medium',
    category: '手写实现',
    tags: ['flat', 'polyfill', '递归', '数据结构'],
    followUp: [
      '你这个递归实现有什么潜在问题吗？（栈溢出）',
      '如何把它改写成迭代（非递归）的版本来解决栈溢出问题？',
    ]
  }
];

export default interviewQuestions;
