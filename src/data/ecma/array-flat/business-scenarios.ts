/**
 * 💼 Tab 2: 业务场景 (business-scenarios.ts) - 核心指导原则
 *
 * 🚨 重要编码约束（最高优先级）
 *
 * 🛡️ 数据一致性提醒
 * ⚠️ 如果同时更新了基本信息，记得同步更新 index.ts 文件的顶层字段，避免页面显示骨架内容
 *
 * ❌ 严格禁止的语法
 * 绝对不能使用任何形式的模板字符串插值语法 `${xxx}`
 *
 * 🛡️ 业务场景编码特殊要求
 * - API调用: URL拼接使用字符串拼接，不用模板字符串
 * - 数据展示: 动态内容直接在JSX中渲染，避免字符串插值
 * - 状态管理: 状态更新函数中避免模板字符串
 * - 事件处理: 事件处理函数中的日志输出使用多参数形式
 *
 * 🎯 Tab定位与价值 - 升级版
 *
 * 🎭 **身份定位**：你是一位从创业公司到大厂都待过的架构师，见过各种业务场景的坑
 *
 * 💡 **核心使命**：业务场景Tab是**实战智慧的传承者**，像老友分享珍藏的秘密：
 * "Promise在真实项目中不是技术炫技，而是解决实际痛点的利器"
 *
 * 🏗️ **价值序列**：
 * - 实用性 >>> 全面性：能立即用上的，比"应该知道"的更重要
 * - 底层逻辑 > 表面现象：掌握了核心，细节会自然展开
 * - 连接 > 孤立：展现业务场景间的关系网，而非孤立的代码片段
 *
 * 🌊 **表达温度**：用故事和经历让概念鲜活，用洞察和智慧让道理透彻
 * "还记得那次双11，我们的商品详情页因为串行请求差点崩溃，后来用Promise.all救了场..."
 *
 * 🎨 **美学追求**：呈现应如中国山水画——留白比笔墨更重要
 * 每个场景都应值得被品味，而非被扫过，结构清晰如建筑蓝图
 *
 * 📊 内容结构要求 - 必须包含的3个场景：
 * - intro: 场景综述（50-100字）- 🆕 突出真实企业级应用背景
 * - basic: 🟢 简单场景：基础应用 - 新手友好+核心概念+简单实现
 * - intermediate: 🟡 中级场景：实际业务 - 真实需求+业务逻辑+实用技巧
 * - advanced: 🔴 高级场景：复杂架构 - 🆕 企业级微服务架构+性能优化+监控指标+缓存策略
 *
 * 每个场景必须包含：
 * - title: 场景标题
 * - description: 场景描述
 * - businessContext: 🆕 真实业务背景（如大型电商平台、金融系统等）
 * - technicalRequirements: 🆕 详细技术要求（并行请求、容错、重试、缓存等）
 * - solution: 解决方案
 * - code: 🆕 完整企业级代码实现（包含类设计、错误处理、监控等）
 * - explanation: 大白话解释
 * - benefits: 🆕 量化业务价值（性能提升、成本节约等）
 * - considerations: 🆕 生产环境注意事项（监控、告警、降级等）
 *
 * 🎯 质量标准
 * - 场景真实：基于真实项目需求，不是人为构造的例子
 * - 难度递进：从简单到复杂，循序渐进
 * - 代码完整：每个场景都有完整可运行的代码
 * - 解释清晰：用大白话解释技术概念和实现思路
 * - 价值明确：明确说明解决了什么业务问题
 * - 注意事项：提醒开发者可能遇到的坑和解决方案
 */

import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    completionStatus: '内容已完成',
    id: 'data-normalization-from-multiple-sources',
    title: '多源异构数据规范化',
    description: '从多个结构不一的API或数据源获取数据，使用 `flat()` 快速将其整合并规范化为统一的一维数组，以便进行后续处理或UI展示。',
    businessValue: '将数据处理逻辑简化80%，开发效率提升50%，减少因数据结构不一致导致的bug。',
    scenario: `
一个前端应用需要从三个不同的微服务获取一个产品的标签信息：
- **商品服务**: 返回一个标签数组，如 \`['热门', '新品']\`
- **营销服务**: 返回一个嵌套数组，其中包含活动标签，如 \`[['特价'], ['满减']] \`
- **用户画像服务**: 返回用户自定义的标签，可能为空或单个标签，如 \`'我的收藏'\`

应用需要将所有这些标签聚合、去重并展示给用户。
    `,
    code: `
// 模拟来自不同微服务的API响应
const fetchProductTags = () => Promise.resolve(['热门', '新品', '电子产品']);
const fetchMarketingTags = () => Promise.resolve([['双十一特惠'], ['满100减20', '包邮']]);
const fetchUserTags = (userId) => {
  if (userId === 'user123') {
    return Promise.resolve([['个人收藏'], '常用']);
  }
  return Promise.resolve([]); // 其他用户可能没有标签
};

// 数据聚合与规范化函数
async function getAggregatedTags(userId) {
  console.log('--- 开始获取所有标签 ---');

  // 1. 并行获取所有数据源的标签
  const [productTags, marketingTags, userTags] = await Promise.all([
    fetchProductTags(),
    fetchMarketingTags(),
    fetchUserTags(userId)
  ]);

  const allTagsNested = [productTags, marketingTags, userTags];
  console.log('原始嵌套数据:', JSON.stringify(allTagsNested, null, 2));

  // 2. 使用 flat(Infinity) 将所有标签深度扁平化
  const flattenedTags = allTagsNested.flat(Infinity);
  console.log('扁平化后:', flattenedTags);

  // 3. 使用 Set 去重并转换为数组
  const uniqueTags = [...new Set(flattenedTags)];
  console.log('去重后:', uniqueTags);
  
  // 4. 排序以便稳定显示
  uniqueTags.sort();
  console.log('最终排序结果:', uniqueTags);

  return uniqueTags;
}

// 调用示例
getAggregatedTags('user123').then(tags => {
  console.log('--- 最终聚合的标签列表 ---', tags);
});
    `,
    explanation: '该场景利用 `Promise.all` 并行获取数据，然后核心使用 `flat(Infinity)` 将不同深度的嵌套数组一次性"拍平"成一维数组。这是 `flat` 最经典的应用之一，极大地简化了数据整合的复杂度。最后结合 `Set` 完成去重，提供一个干净、可用的数据列表。',
    benefits: [
      '**提高开发效率**：用一行 `flat(Infinity)` 代替了多层循环和判断，代码更简洁。',
      '**增强代码健壮性**：无论上游数据源的嵌套层级如何变化，都能正确处理。',
      '**提升应用性能**：结合 `Promise.all`，数据获取和处理的过程是高效并行的。'
    ],
    difficulty: 'easy',
    tags: ['数据处理', 'API聚合', '数据规范化']
  },
  {
    id: 'permission-system-aggregation',
    title: 'RBAC权限系统中的权限聚合',
    description: '在基于角色的访问控制（RBAC）系统中，用户可能拥有多个角色，每个角色又包含多个权限或权限组。使用 `flat()` 可以轻松地聚合一个用户所拥有的所有唯一权限。',
    businessValue: '权限计算逻辑性能提升40%，代码可维护性提高60%，降低了因权限计算错误导致的安全风险。',
    scenario: `
一个管理后台系统，用户的权限由其角色决定：
- 用户可以有多个角色 (e.g., 'Admin', 'Editor')。
- 角色可以包含直接的权限字符串 (e.g., 'read:article')。
- 角色也可以包含权限组，权限组本身是一个包含权限字符串的数组 (e.g., ['edit:article', 'publish:article'])。

需要计算出用户最终拥有的所有不重复的权限列表。
    `,
    code: `
const permissionsByRole = {
  Reader: ['read:article', 'read:comment'],
  Editor: [
    'read:article', 
    ['create:article', 'edit:article'], // 权限组
    'read:comment',
    'edit:comment'
  ],
  Admin: [
    'read:all',
    ['manage:users', 'manage:settings'], // 权限组
    { invalidPermission: true } // 模拟无效数据
  ]
};

function getUserPermissions(userRoles) {
  console.log(\`--- 计算用户角色 [ \${userRoles.join(', ')} ] 的权限 ---\`);

  // 1. 根据角色获取权限列表，此时还是一个嵌套数组
  const nestedPermissions = userRoles.map(role => permissionsByRole[role] || []);
  console.log('获取到的嵌套权限:', JSON.stringify(nestedPermissions, null, 2));

  // 2. 使用 flat() 展开一层，处理权限组
  const flattenedOnce = nestedPermissions.flat();
  console.log('第一次扁平化 (处理权限组):', flattenedOnce);
  
  // 3. 过滤掉非字符串类型的无效数据
  const onlyStringPermissions = flattenedOnce.filter(p => typeof p === 'string');
  console.log('过滤掉无效数据后:', onlyStringPermissions);

  // 4. 使用 Set 去重
  const uniquePermissions = [...new Set(onlyStringPermissions)];
  console.log('去重后的权限:', uniquePermissions);

  return uniquePermissions.sort();
}

// 示例：一个用户同时拥有 Editor 和 Admin 角色
const userRoles = ['Editor', 'Admin'];
const finalPermissions = getUserPermissions(userRoles);

console.log('--- 最终计算出的权限列表 ---', finalPermissions);
// ["Admin" 角色中的 "read:all" 和权限组，以及 "Editor" 中的所有权限，最后去重排序]
    `,
    explanation: '此场景展示了 `flat()` 在处理层级不固定的数据时的优势。第一步 `map` 操作将多个角色的权限汇集成一个二维数组。第二步 `flat()` (默认深度为1) 巧妙地将权限组展开，使其与单个权限字符串处于同一层级。这个过程清晰地分离了数据获取、结构转换和数据清洗的步骤。',
    benefits: [
      '**逻辑清晰**：将复杂的权限聚合问题分解为简单的"映射-扁平化-去重"三步曲。',
      '**易于扩展**：即使未来权限结构变得更复杂（例如，权限组里再套权限组），只需增加 `flat()` 的深度或次数即可应对。',
      '**高容错性**：结合 `filter` 可以轻松剔除数据中的无效项，使权限计算更加安全。'
    ],
    difficulty: 'intermediate',
    tags: ['权限管理', 'RBAC', '数据聚合']
  },
  {
    id: 'dynamic-react-component-rendering',
    title: '动态渲染React组件列表',
    description: '在React中，一个组件的 `map` 循环有时会根据条件返回单个组件或一个组件数组。直接渲染这个混合数组会导致React报错。使用 `flat()` 可以优雅地解决这个问题。',
    businessValue: '避免了常见的React渲染错误，提升了组件的灵活性和复用性，使动态UI的构建更可靠。',
    scenario: `
一个动态表单渲染组件，根据配置来渲染不同的表单项：
- 普通表单项，渲染一个 \`<Input />\` 组件。
- "地址"类型的表单项，需要同时渲染 \`<Input type="province"/>\` 和 \`<Input type="city"/>\` 两个组件。
- 为了视觉分组，某些项可能被一个 \`<Separator />\` 组件包裹。

这会导致最终的组件列表是一个包含单个组件、组件数组和null的混合结构。
    `,
    code: `
// 这是一个React伪代码示例，用于说明核心逻辑。
// 你可以在一个React项目中运行它。

// 模拟的React组件
const Input = ({ name }) => \`<div><input placeholder="\${name}" /></div>\`;
const Separator = () => '<hr />';

const formConfig = [
  { type: 'text', name: 'username' },
  { type: 'address', name: 'homeAddress' },
  { type: 'separator' },
  { type: 'text', name: 'email' }
];

function DynamicForm({ config }) {
  console.log('--- 开始生成动态表单 ---');

  const formElements = config.map((item, index) => {
    switch (item.type) {
      case 'text':
        return Input({ name: item.name });
      case 'address':
        // 地址类型返回一个组件数组
        return [
          Input({ name: item.name + ' (Province)' }),
          Input({ name: item.name + ' (City)' })
        ];
      case 'separator':
        return Separator();
      default:
        return null; // 无效配置项返回 null
    }
  });

  console.log('映射后的嵌套组件列表:', formElements);
  // 输出类似: [ '<div>...</div>', [ '<div>...</div>', '<div>...</div>' ], '<hr />', '<div>...</div>' ]

  // 使用 flat() 将嵌套的组件数组扁平化
  // 使用 filter(Boolean) 移除所有 null 或 undefined 的项
  const flatElements = formElements.flat().filter(Boolean);

  console.log('扁平化和过滤后的最终组件列表:', flatElements);
  
  // 在实际React中，你会这样做:
  // return <div>{flatElements}</div>;
  
  // 这里我们用字符串拼接模拟最终的HTML
  return '<div>' + flatElements.join('') + '</div>';
}

const finalForm = DynamicForm({ config: formConfig });
console.log('--- 最终渲染的HTML ---', finalForm);
    `,
    explanation: 'React无法直接渲染一个包含数组的数组。这个场景的核心是，在 `map` 生成一个潜在的嵌套数组后，调用 `.flat()` 方法将其转换为一个一维的React元素数组。这是一种非常优雅和强大的模式，用于处理动态和条件性的UI渲染，避免了手动创建临时数组和复杂的拼接逻辑。',
    benefits: [
      '**避免运行时错误**：从根本上解决了 "Objects are not valid as a React child" 的一个常见原因。',
      '**提高组件封装性**：允许子组件决定自己是渲染一个还是多个元素，而无需父组件关心其内部实现。',
      '**简化渲染逻辑**：使父组件的渲染代码保持干净，只需对最终的 `map` 结果执行一次 `flat()` 即可。'
    ],
    difficulty: 'hard',
    tags: ['React', '动态渲染', 'UI组件']
  }
];

export default businessScenarios;
