/**
 * 🐛 Tab 8: 调试技巧 (debugging-tips.ts) - 核心指导原则
 *
 * 🎯 Tab定位与价值 - 调试侦探版
 *
 * 🎭 **身份定位**：你是一位在无数个深夜与Bug搏斗的调试侦探
 * 见过各种诡异的Promise问题，练就了一双火眼金睛
 *
 * 💡 **核心信念**：真正的调试智慧不在于工具的多少，而在于思路的清晰
 * 好的调试方法不是盲目尝试，而是有序推理
 *
 * 🔍 **侦探思维**：每个Bug都是一个待解的谜案
 * - 🕵️ **现场勘查**：仔细观察错误现象和上下文
 * - 🧩 **线索收集**：收集日志、堆栈、状态等关键信息
 * - 💡 **假设验证**：基于经验提出假设，逐一验证
 * - 🎯 **真相还原**：找到根本原因，而非表面症状
 *
 * 🌊 **表达温度**：像一位分享破案经验的老侦探：
 * "这种Promise状态异常，我见过好几次，通常是因为..."
 *
 * 🎨 **美学追求**：每个调试技巧都应该让人有"原来还能这样查"的惊喜
 * 不只是告诉怎么调试，更要解释为什么这样调试有效
 *
 * 📊 适用API类型
 * 此Tab主要适用于以下类型的API：
 * - 异步API: useEffect、自定义Hooks等
 * - 复杂状态API: useReducer、useContext等
 * - 性能相关API: useMemo、useCallback等
 * - 生命周期API: 涉及组件生命周期的API
 * - 错误频发API: 开发者经常使用错误的API
 *
 * 📊 内容结构要求 - 支持子tabs结构的内容组织：
 *
 * 🆕 新增：支持子tabs的结构化内容（已实现高质量内容）
 * - subTabs: 🆕 子标签数组（Promise调试的系统化方法）
 *   - key: 子标签键
 *   - title: 子标签标题
 *   - content: 内容对象
 *     - introduction: 🆕 调试方法介绍
 *     - sections: 🆕 结构化章节数组
 *       - title: 章节标题
 *       - description: 章节描述（可选）
 *       - items: 🆕 详细调试项目数组
 *         - title: 项目标题
 *         - description: 项目描述
 *         - code: 🆕 完整调试代码示例
 *         - solution: 🆕 具体解决方案
 *         - prevention: 🆕 预防措施
 *         - steps: 🆕 调试步骤数组
 *         - tips: 🆕 实用调试技巧
 *
 * 🆕 已实现调试内容：
 * - Promise状态调试和监控
 * - 异步错误追踪和定位
 * - 微任务队列调试技巧
 * - Promise链调试方法
 * - 性能调试和优化建议
 *
 * 🎯 质量标准
 * - 问题真实：基于真实开发中遇到的调试问题
 * - 方法系统：提供系统化的调试方法和流程
 * - 工具实用：介绍实用的调试工具和技巧
 * - 步骤清晰：调试步骤清晰明确，易于跟随
 * - 预防有效：提供有效的问题预防措施
 * - 案例丰富：包含丰富的实际调试案例
 *
 * 💡 调试策略
 * - 分层诊断：从表面现象到深层原因的分层诊断
 * - 工具组合：结合多种调试工具的综合使用
 * - 经验总结：总结常见问题的调试经验和模式
 * - 预防为主：强调预防性编程和错误预防
 * - 效率优先：提供高效的问题定位和解决方法
 */

import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: '在使用 `flat()` 时，开发者最常遇到的问题通常与深度、稀疏数组和类型有关。理解这些常见错误可以帮助你快速定位问题。',
        sections: [
          {
            title: '扁平化结果不符合预期',
            items: [
              {
                title: '错误现象: 数组没有被完全"拉平"',
                description: '调用 `flat()` 后，返回的数组中依然包含数组。',
                cause: '这是最常见的问题，原因是 `flat()` 的默认深度是1。如果你的数组嵌套超过一层，默认调用就无法将其完全扁平化。',
                solution: '明确指定一个足够大的 `depth` 参数，或者使用 `Infinity` 来确保完全扁平化。',
                prevention: '在处理不确定深度的数据时，养成使用 `flat(Infinity)` 的习惯，或在使用前先分析数据结构。',
                code: `
const deeplyNested = [1, [2, [3, [4]]]];

// ❌ 错误做法：使用默认深度
const flattenedOnce = deeplyNested.flat();
console.log('默认深度(1)的结果:', flattenedOnce); // 输出: [1, 2, [3, [4]]] - 未完全展平

// ✅ 正确做法：提供足够的深度
const flattenedTwice = deeplyNested.flat(2);
console.log('深度为2的结果:', flattenedTwice); // 输出: [1, 2, 3, [4]] - 仍未完全展平

// ✅ 最佳做法：当不确定深度时使用 Infinity
const fullyFlattened = deeplyNested.flat(Infinity);
console.log('使用Infinity的结果:', fullyFlattened); // 输出: [1, 2, 3, 4] - 完全展平
`
              },
              {
                title: '错误现象: 数组中的某些元素"消失"了',
                description: '调用 `flat()` 后，结果数组的长度小于预期，似乎有些元素不见了。',
                cause: '这是因为 `flat()` 会自动移除数组中的"空槽"（empty slots）。如果原数组是稀疏的，这些空槽在扁平化后就会消失。',
                solution: '调试时，首先检查原数组。可以在调用 `flat()` 前打印原数组，观察它是否包含空槽。理解这是 `flat()` 的设计特性，而非bug。',
                prevention: '在处理可能包含空槽的数据时，要意识到 `flat()` 会进行数据清理。如果需要保留空位（例如用 `undefined` 占位），需要预处理数组。',
                code: `
const sparseArray = [1, , 3, [4, , 5]]; // 注意索引1和4是空槽
console.log('原稀疏数组:', sparseArray);
console.log('原数组长度:', sparseArray.length); // 5

// flat() 会移除所有空槽
const flattened = sparseArray.flat();

console.log('扁平化后的结果:', flattened); // 输出: [1, 3, 4, 5]
console.log('结果数组长度:', flattened.length); // 4, 长度变短了
`
              }
            ]
          },
          {
            title: '环境和类型错误',
            items: [
              {
                title: '错误: `TypeError: xxx.flat is not a function`',
                description: '当你在一个对象上调用 `.flat()` 时，会报这个类型错误。',
                cause: '这通常发生在两个情况下：1. 你试图调用该方法的变量根本不是一个数组。2. 在不支持ES2019的环境中（如旧版浏览器或Node.js）运行代码。',
                solution: '在调用 `.flat()` 之前，请务必使用 `Array.isArray()` 来确保变量是一个数组。同时，检查你的目标运行环境是否支持ES2019，如果不支持，需要引入Polyfill。',
                prevention: '在处理动态数据时，增加类型检查的防御性代码。在项目开始时确认目标环境的兼容性。',
                code: `
function processData(data) {
  // ❌ 错误做法: 假设 data 一定是数组
  // return data.flat(); 

  // ✅ 正确做法: 先进行类型检查
  if (Array.isArray(data)) {
    return data.flat();
  }
  console.error('数据格式错误，期望一个数组，但收到了:', typeof data);
  return [];
}

const goodData = [1, [2, 3]];
const badData = { items: [1, [2, 3]] }; // 这是一个对象，不是数组

console.log('处理好数据:', processData(goodData));
console.log('处理坏数据:', processData(badData));
`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-techniques',
      title: '⚙️ 调试技巧',
      content: {
        introduction: '除了修复错误，一些主动的调试技巧可以帮助你更好地理解 `flat()` 的行为，并快速定位问题所在。',
        sections: [
          {
            title: '逐步调试和探测',
            items: [
              {
                title: '技巧: 使用"深度探测器"',
                description: '当你不确定一个复杂数据结构的嵌套深度时，可以从深度1开始，逐步增加 `depth` 参数并打印结果，观察每一层扁平化的效果。',
                steps: [
                  '从 `data.flat(1)` 开始，打印结果。',
                  '观察结果中是否还包含数组。',
                  '如果包含，增加深度至 `data.flat(2)`，再次打印。',
                  '重复此过程，直到数组被完全扁平化或达到你期望的结构。',
                  '这可以帮助你理解数据的实际嵌套层级。'
                ],
                code: `
const unknownStructure = [
  { type: 'A', children: [ { type: 'B' } ] },
  { type: 'C', children: [ { type: 'D', children: [ { type: 'E' } ] } ] }
];

// 假设我们需要获取所有对象到一个列表
function getAllNodes(nodes) {
  return nodes.flatMap(node => [node, ...(node.children ? getAllNodes(node.children) : [])]);
}

const allNodes = getAllNodes(unknownStructure);
console.log('使用flatMap递归获取所有节点:', allNodes);

// 使用 flat 进行探测
const firstLevel = unknownStructure.flat(); // flat 对对象数组无效
console.log('flat(1) 对对象数组无效:', firstLevel); 
// 需要先用 map 提取 children
const childrenOnly = unknownStructure.map(n => n.children);
console.log('提取children后:', JSON.stringify(childrenOnly));

console.log('探测深度1:', childrenOnly.flat(1));
console.log('探测深度2:', childrenOnly.flat(2));
console.log('探测深度 Infinity:', childrenOnly.flat(Infinity));
                `
              },
              {
                title: '技巧: 分解 `flatMap`',
                description: '`flatMap` 非常方便，但当它产生意外结果时，问题可能出在 `map` 部分，也可能出在 `flat` 部分。将其分解可以帮助快速定位问题。',
                steps: [
                  '原始代码: `const result = data.flatMap(item => complexFunction(item));`',
                  '第一步，只做 `map`: `const mapped = data.map(item => complexFunction(item));`',
                  '第二步，打印 `mapped` 的结果: `console.log(JSON.stringify(mapped, null, 2));`',
                  '现在你可以清晰地看到 `complexFunction` 返回的到底是什么。如果这里的结构不符合预期，说明是映射逻辑有问题。',
                  '如果 `mapped` 的结果正确，再对其调用 `.flat()`，如果最终结果不正确，说明你对 `flat` 的行为理解有误（例如，它只会展平一层）。'
                ],
                code: `
const items = ['1,2', '3,4', '5'];

// 原始 flatMap
const numsFromFlatMap = items.flatMap(item => item.split(',').map(Number));

// 分解调试
const mappedFirst = items.map(item => item.split(',').map(Number));
console.log('1. map后的中间结果:', JSON.stringify(mappedFirst)); 
// 输出: [[1,2],[3,4],[5]]

const finalResult = mappedFirst.flat();
console.log('2. flat后的最终结果:', finalResult); // [1,2,3,4,5]

// 通过打印中间结果，我们确认了每一步的行为都符合预期。
`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
