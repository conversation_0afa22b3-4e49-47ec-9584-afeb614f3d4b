import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const symbolsData: ApiItem = {
  id: 'symbols',
  title: 'ES6 Symbols',
  description: 'ES6 Symbol原始类型，提供唯一标识符和元编程能力，支持内置符号和自定义符号',
  category: 'ECMA特性',
  difficulty: 'medium',
  
  syntax: `Symbol(); Symbol('description'); Symbol.for('key'); Symbol.iterator;`,
  example: `const sym = Symbol('id'); const obj = { [sym]: 'value' }; console.log(obj[sym]);`,
  notes: 'Symbol是ES6新增的原始数据类型，主要用于创建唯一标识符和实现元编程',
  
  version: 'ES6 (ES2015)',
  tags: ['ES6', 'JavaScript', 'Symbol', '原始类型', '元编程'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default symbolsData;
