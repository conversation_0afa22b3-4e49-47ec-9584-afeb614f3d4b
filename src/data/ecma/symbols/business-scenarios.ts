import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'plugin-system',
    title: '插件系统架构',
    description: '使用Symbol构建可扩展的插件系统，避免命名冲突和提供私有API',
    businessValue: '提供安全的插件扩展机制，避免插件间冲突，保护核心API',
    scenario: '应用需要支持第三方插件扩展功能，同时确保插件间不会相互干扰，核心系统的私有方法不被意外访问。',
    code: `// 插件系统核心
const PLUGIN_METADATA = Symbol('plugin.metadata');
const PLUGIN_LIFECYCLE = Symbol('plugin.lifecycle');
const CORE_API = Symbol('core.api');

class PluginManager {
  constructor() {
    this.plugins = new Map();
    this[CORE_API] = {
      registerHook: this.registerHook.bind(this),
      emitEvent: this.emitEvent.bind(this)
    };
  }
  
  // 注册插件
  registerPlugin(plugin) {
    if (!plugin[PLUGIN_METADATA]) {
      throw new Error('Invalid plugin: missing metadata');
    }
    
    const metadata = plugin[PLUGIN_METADATA];
    this.plugins.set(metadata.name, plugin);
    
    // 初始化插件
    if (plugin[PLUGIN_LIFECYCLE]?.init) {
      plugin[PLUGIN_LIFECYCLE].init(this[CORE_API]);
    }
    
    return metadata.name;
  }
  
  // 私有方法 - 只能通过Symbol访问
  registerHook(event, callback) {
    // 核心钩子注册逻辑
  }
  
  emitEvent(event, data) {
    // 事件发射逻辑
  }
}

// 插件定义
class AuthPlugin {
  constructor() {
    this[PLUGIN_METADATA] = {
      name: 'auth-plugin',
      version: '1.0.0',
      dependencies: []
    };
    
    this[PLUGIN_LIFECYCLE] = {
      init: (coreAPI) => {
        this.coreAPI = coreAPI;
        this.setupAuthHooks();
      },
      destroy: () => {
        this.cleanup();
      }
    };
  }
  
  setupAuthHooks() {
    this.coreAPI.registerHook('user.login', this.handleLogin.bind(this));
    this.coreAPI.registerHook('user.logout', this.handleLogout.bind(this));
  }
  
  handleLogin(userData) {
    console.log('Auth plugin: User logged in', userData);
  }
  
  handleLogout() {
    console.log('Auth plugin: User logged out');
  }
}`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'data-model-system',
    title: '数据模型系统',
    description: '使用Symbol实现数据模型的私有属性和元数据管理',
    businessValue: '提供安全的数据封装，支持ORM功能，避免属性名冲突',
    scenario: '构建一个数据模型系统，需要为每个模型实例添加元数据、状态跟踪等私有信息，同时不影响用户数据。',
    code: `// 数据模型系统
const MODEL_META = Symbol('model.meta');
const MODEL_STATE = Symbol('model.state');
const MODEL_ORIGINAL = Symbol('model.original');
const MODEL_DIRTY = Symbol('model.dirty');

class BaseModel {
  constructor(data = {}) {
    // 私有元数据
    this[MODEL_META] = {
      tableName: this.constructor.tableName,
      primaryKey: this.constructor.primaryKey || 'id',
      timestamps: this.constructor.timestamps !== false
    };
    
    // 私有状态
    this[MODEL_STATE] = {
      isNew: !data[this[MODEL_META].primaryKey],
      isDeleted: false,
      isSaving: false
    };
    
    // 原始数据副本
    this[MODEL_ORIGINAL] = { ...data };
    
    // 脏字段跟踪
    this[MODEL_DIRTY] = new Set();
    
    // 设置数据属性
    Object.assign(this, data);
    
    // 代理属性变更
    return new Proxy(this, {
      set(target, prop, value) {
        if (typeof prop === 'string' && !prop.startsWith('_')) {
          target[MODEL_DIRTY].add(prop);
        }
        target[prop] = value;
        return true;
      }
    });
  }
  
  // 检查是否有未保存的更改
  isDirty() {
    return this[MODEL_DIRTY].size > 0;
  }
  
  // 获取变更的字段
  getDirtyFields() {
    return Array.from(this[MODEL_DIRTY]);
  }
  
  // 保存模型
  async save() {
    if (!this.isDirty() && !this[MODEL_STATE].isNew) {
      return this;
    }
    
    this[MODEL_STATE].isSaving = true;
    
    try {
      const meta = this[MODEL_META];
      const isNew = this[MODEL_STATE].isNew;
      
      if (isNew) {
        await this.constructor.create(this.toJSON());
        this[MODEL_STATE].isNew = false;
      } else {
        const changes = {};
        for (const field of this[MODEL_DIRTY]) {
          changes[field] = this[field];
        }
        await this.constructor.update(this[meta.primaryKey], changes);
      }
      
      // 重置状态
      this[MODEL_DIRTY].clear();
      this[MODEL_ORIGINAL] = { ...this.toJSON() };
      
      return this;
    } finally {
      this[MODEL_STATE].isSaving = false;
    }
  }
  
  // 序列化为普通对象
  toJSON() {
    const result = {};
    for (const key in this) {
      if (typeof this[key] !== 'function' && typeof key === 'string') {
        result[key] = this[key];
      }
    }
    return result;
  }
}

// 用户模型
class User extends BaseModel {
  static tableName = 'users';
  static primaryKey = 'id';
  
  static async create(data) {
    // 数据库创建逻辑
    return data;
  }
  
  static async update(id, changes) {
    // 数据库更新逻辑
    return changes;
  }
}

// 使用示例
const user = new User({ name: 'John', email: '<EMAIL>' });
user.name = 'Jane'; // 触发脏字段跟踪
console.log(user.isDirty()); // true
console.log(user.getDirtyFields()); // ['name']`
  }
];

export default businessScenarios;
