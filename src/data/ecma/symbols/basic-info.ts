import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `Symbol是ES6引入的第七种原始数据类型，用于创建唯一的标识符。每个Symbol值都是唯一的，即使描述相同。Symbol主要用于对象属性的键，避免属性名冲突，同时提供了元编程的能力。Symbol不能被for...in遍历，也不会被JSON.stringify序列化，提供了真正的"私有"属性机制。`,

  syntax: `// 创建Symbol
const sym1 = Symbol();
const sym2 = Symbol('description');
const sym3 = Symbol.for('global-key'); // 全局Symbol注册表

// 内置Symbol
Symbol.iterator
Symbol.toStringTag
Symbol.hasInstance
Symbol.toPrimitive

// 使用Symbol作为属性键
const obj = {
  [sym1]: 'value1',
  [Symbol('key')]: 'value2'
};

// 获取Symbol属性
Object.getOwnPropertySymbols(obj);
Reflect.ownKeys(obj);`,

  quickExample: `// 创建唯一标识符
const ID = Symbol('id');
const NAME = Symbol('name');

const user = {
  [ID]: 12345,
  [NAME]: 'John Doe',
  email: '<EMAIL>'
};

console.log(user[ID]); // 12345
console.log(user[NAME]); // 'John Doe'

// Symbol属性不会被常规方法遍历
console.log(Object.keys(user)); // ['email']
console.log(Object.getOwnPropertyNames(user)); // ['email']
console.log(Object.getOwnPropertySymbols(user)); // [Symbol(id), Symbol(name)]

// 全局Symbol注册表
const globalSym1 = Symbol.for('app.config');
const globalSym2 = Symbol.for('app.config');
console.log(globalSym1 === globalSym2); // true

// 获取Symbol的key
console.log(Symbol.keyFor(globalSym1)); // 'app.config'

// 内置Symbol - 自定义迭代器
const iterableObj = {
  data: [1, 2, 3],
  [Symbol.iterator]() {
    let index = 0;
    const data = this.data;
    return {
      next() {
        if (index < data.length) {
          return { value: data[index++], done: false };
        }
        return { done: true };
      }
    };
  }
};

for (const value of iterableObj) {
  console.log(value); // 1, 2, 3
}

// 自定义toString标签
const customObj = {
  [Symbol.toStringTag]: 'CustomObject'
};
console.log(customObj.toString()); // '[object CustomObject]'`,

  coreFeatures: [
    {
      feature: "唯一性",
      description: "每个Symbol值都是唯一的，即使描述相同",
      importance: "high" as const,
      details: "提供了真正唯一的标识符，避免属性名冲突"
    },
    {
      feature: "不可枚举",
      description: "Symbol属性不会被for...in、Object.keys等遍历",
      importance: "high" as const,
      details: "提供了类似私有属性的效果"
    },
    {
      feature: "全局注册表",
      description: "Symbol.for()可以创建全局共享的Symbol",
      importance: "medium" as const,
      details: "允许跨模块共享Symbol标识符"
    },
    {
      feature: "内置Symbol",
      description: "JavaScript提供了多个内置Symbol用于元编程",
      importance: "medium" as const,
      details: "如Symbol.iterator、Symbol.toStringTag等"
    }
  ],

  keyFeatures: [
    {
      feature: "元编程支持",
      description: "通过内置Symbol自定义对象行为",
      importance: "high" as const,
      details: "可以自定义迭代、类型转换等行为"
    },
    {
      feature: "属性隐藏",
      description: "Symbol属性不会被常规方法发现",
      importance: "medium" as const,
      details: "提供了数据封装的能力"
    },
    {
      feature: "向后兼容",
      description: "不会影响现有的字符串属性",
      importance: "medium" as const,
      details: "安全地扩展对象功能"
    }
  ],

  limitations: [
    "Symbol不能被隐式转换为字符串",
    "不能使用new操作符创建Symbol",
    "Symbol属性不会被JSON.stringify序列化",
    "需要特殊方法才能获取Symbol属性",
    "在某些调试工具中可能不易查看"
  ],

  bestPractices: [
    "为Symbol提供有意义的描述",
    "使用Symbol.for()创建需要跨模块共享的Symbol",
    "利用内置Symbol实现自定义行为",
    "将Symbol常量定义在模块顶层",
    "使用Symbol创建真正的私有属性"
  ],

  warnings: [
    "Symbol()和Symbol.for()创建的Symbol是不同的",
    "Symbol属性需要使用特殊方法才能遍历",
    "不要尝试将Symbol转换为字符串进行比较"
  ]
,

  scenarioDiagram: `graph TD
    A[Symbol使用场景] --> B[私有属性]
    A --> C[元编程]
    A --> D[迭代器协议]
    A --> E[库开发]

    B --> B1[类私有字段]
    B --> B2[对象隐藏属性]
    B --> B3[命名空间隔离]

    C --> C1[Symbol.iterator]
    C --> C2[Symbol.toPrimitive]
    C --> C3[Symbol.hasInstance]

    D --> D1[自定义迭代]
    D --> D2[生成器函数]
    D --> D3[for...of支持]

    E --> E1[框架内部实现]
    E --> E2[插件系统]
    E --> E3[API扩展]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`
};

export default basicInfo;
