import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: `Symbol的历史反映了JavaScript对真正私有属性和元编程能力的长期需求。从早期的命名约定到Symbol的引入，体现了语言设计者对开发者需求的回应。`,
  
  background: `在Symbol出现之前，JavaScript缺乏真正的私有属性机制，开发者只能通过命名约定（如下划线前缀）或闭包来模拟私有性。同时，缺乏标准的元编程接口也限制了库和框架的发展。`,

  evolution: `Symbol的引入为JavaScript带来了真正的私有属性和标准化的元编程接口，推动了现代JavaScript框架和库的发展。`,

  timeline: [
    {
      year: '2013',
      event: 'Symbol提案提出',
      description: 'TC39开始讨论Symbol的设计',
      significance: '为解决属性命名冲突和私有属性需求'
    },
    {
      year: '2015',
      event: 'ES6 Symbol标准化',
      description: 'ECMAScript 2015正式引入Symbol',
      significance: '成为JavaScript的第七种原始数据类型'
    },
    {
      year: '2018',
      event: 'Symbol.asyncIterator标准化',
      description: 'ES2018引入异步迭代器Symbol',
      significance: '支持异步迭代模式'
    }
  ],

  keyFigures: [
    {
      name: 'Allen Wirfs-Brock',
      role: 'ES6规范编辑',
      contribution: '参与Symbol的设计和标准化',
      significance: '推动了Symbol在JavaScript中的实现'
    }
  ],

  concepts: [
    {
      term: '私有属性',
      definition: '只能在特定作用域内访问的对象属性',
      evolution: '从命名约定发展为Symbol实现',
      modernRelevance: 'Symbol提供了真正的私有属性机制'
    }
  ],

  designPhilosophy: `Symbol的设计体现了"最小惊讶原则"，通过不影响现有代码的方式添加新功能。`,

  impact: `Symbol的引入为JavaScript生态系统带来了更好的封装能力和元编程支持。`,

  modernRelevance: `在现代JavaScript开发中，Symbol广泛用于库和框架的内部实现，提供了安全的扩展机制。`
};

export default knowledgeArchaeology;
