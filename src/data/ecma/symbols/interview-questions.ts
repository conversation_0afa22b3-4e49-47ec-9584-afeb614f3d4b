import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'Symbol()和Symbol.for()有什么区别？',
    answer: `Symbol()和Symbol.for()的主要区别：

**Symbol()**：
- 每次调用都创建新的唯一Symbol
- 即使描述相同，返回的Symbol也不同
- 不会在全局注册表中注册
- 无法通过Symbol.keyFor()获取key

**Symbol.for()**：
- 在全局Symbol注册表中查找或创建Symbol
- 相同key返回相同的Symbol
- 可以跨模块、跨realm共享
- 可以通过Symbol.keyFor()获取key

**使用场景**：
- Symbol()：需要完全唯一的标识符
- Symbol.for()：需要跨模块共享的标识符`,
   
    difficulty: 'medium',
    frequency: 'high',
    category: '基础概念',
    tags: ['Symbol创建', '全局注册表', '唯一性'],
    
    code: `// Symbol() - 每次都创建新的
const sym1 = Symbol('test');
const sym2 = Symbol('test');
console.log(sym1 === sym2); // false

// Symbol.for() - 全局注册表
const globalSym1 = Symbol.for('app.config');
const globalSym2 = Symbol.for('app.config');
console.log(globalSym1 === globalSym2); // true

// 获取Symbol的key
console.log(Symbol.keyFor(globalSym1)); // 'app.config'
console.log(Symbol.keyFor(sym1)); // undefined

// 跨模块共享示例
// module1.js
export const CONFIG_KEY = Symbol.for('app.config');

// module2.js
import { CONFIG_KEY } from './module1.js';
const sameKey = Symbol.for('app.config');
console.log(CONFIG_KEY === sameKey); // true

// 实际应用
const PRIVATE_DATA = Symbol('private');
const SHARED_EVENT = Symbol.for('app.event.user.login');

class User {
  constructor(name) {
    this.name = name;
    this[PRIVATE_DATA] = { createdAt: new Date() };
  }
  
  getPrivateData() {
    return this[PRIVATE_DATA];
  }
}`,
    
    followUp: [
      'Symbol属性如何遍历？',
      '内置Symbol有哪些？',
      'Symbol在元编程中的应用？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '常用的内置Symbol有哪些？各有什么作用？',
    answer: `常用的内置Symbol及其作用：

**Symbol.iterator**：
- 定义对象的默认迭代器
- 使对象可以被for...of遍历
- 返回一个迭代器对象

**Symbol.toStringTag**：
- 自定义Object.prototype.toString()的返回值
- 影响对象的类型字符串表示

**Symbol.hasInstance**：
- 自定义instanceof操作符的行为
- 定义在构造函数上

**Symbol.toPrimitive**：
- 自定义对象到原始值的转换
- 接收hint参数：'number'、'string'、'default'`,
   
    difficulty: 'medium',
    frequency: 'medium',
    category: '内置Symbol',
    tags: ['内置Symbol', '元编程', '迭代器'],
    
    code: `// Symbol.iterator - 自定义迭代器
const range = {
  start: 1,
  end: 5,
  [Symbol.iterator]() {
    let current = this.start;
    const end = this.end;
    return {
      next() {
        if (current <= end) {
          return { value: current++, done: false };
        }
        return { done: true };
      }
    };
  }
};

for (const num of range) {
  console.log(num); // 1, 2, 3, 4, 5
}

// Symbol.toStringTag - 自定义类型标签
class CustomArray {
  constructor(...items) {
    this.items = items;
  }
  
  get [Symbol.toStringTag]() {
    return 'CustomArray';
  }
}

const arr = new CustomArray(1, 2, 3);
console.log(arr.toString()); // '[object CustomArray]'

// Symbol.hasInstance - 自定义instanceof
class MyArray {
  static [Symbol.hasInstance](instance) {
    return Array.isArray(instance);
  }
}

console.log([] instanceof MyArray); // true
console.log({} instanceof MyArray); // false

// Symbol.toPrimitive - 自定义类型转换
const obj = {
  [Symbol.toPrimitive](hint) {
    switch (hint) {
      case 'number':
        return 42;
      case 'string':
        return 'hello';
      case 'default':
        return 'default';
    }
  }
};

console.log(+obj); // 42 (number)
console.log(\`\${obj}\`); // 'hello' (string)
console.log(obj + ''); // 'default' (default)`,
    
    followUp: [
      'Symbol.asyncIterator的用法？',
      '如何自定义Symbol.species？',
      'Symbol.match在正则表达式中的作用？'
    ]
  }
];

export default interviewQuestions;
