import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: 'Symbol使用中的常见错误和解决方案',
        sections: [
          {
            title: 'Symbol使用错误',
            description: 'Symbol创建和使用相关的常见问题',
            items: [
              {
                title: 'TypeError: Symbol is not a constructor',
                description: '尝试使用new操作符创建Symbol',
                solution: '直接调用Symbol()函数，不使用new',
                prevention: '记住Symbol是函数，不是构造函数',
                code: `// ❌ 错误：使用new操作符
const sym = new Symbol('test'); // TypeError

// ✅ 正确：直接调用函数
const sym = Symbol('test');

// ✅ 正确：使用Symbol.for()
const globalSym = Symbol.for('test');`
              },
              {
                title: 'Symbol属性无法访问',
                description: 'Symbol属性不会被常规方法遍历到',
                solution: '使用Object.getOwnPropertySymbols()或Reflect.ownKeys()',
                prevention: '了解Symbol属性的特殊性',
                code: `const sym = Symbol('hidden');
const obj = { [sym]: 'secret', visible: 'public' };

// ❌ 无法获取Symbol属性
console.log(Object.keys(obj)); // ['visible']
console.log(obj.sym); // undefined

// ✅ 正确获取Symbol属性
console.log(Object.getOwnPropertySymbols(obj)); // [Symbol(hidden)]
console.log(obj[sym]); // 'secret'
console.log(Reflect.ownKeys(obj)); // ['visible', Symbol(hidden)]`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '使用开发工具调试Symbol相关问题',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '利用浏览器工具调试Symbol属性和行为',
            items: [
              {
                title: 'Symbol属性查看',
                description: '在控制台中查看对象的Symbol属性',
                solution: '使用console.dir()和特殊方法查看Symbol',
                prevention: '定期检查对象的完整属性列表',
                code: `const sym = Symbol('debug');
const obj = { 
  name: 'test', 
  [sym]: 'hidden value',
  [Symbol.toStringTag]: 'CustomObject'
};

// 调试Symbol属性
console.dir(obj); // 在浏览器中显示完整对象结构
console.log('Symbol properties:', Object.getOwnPropertySymbols(obj));
console.log('All keys:', Reflect.ownKeys(obj));

// 调试Symbol描述
const symbols = Object.getOwnPropertySymbols(obj);
symbols.forEach(s => {
  console.log('Symbol description:', s.description);
  console.log('Symbol toString:', s.toString());
});

// 调试全局Symbol
const globalSym = Symbol.for('global.test');
console.log('Global symbol key:', Symbol.keyFor(globalSym));`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
