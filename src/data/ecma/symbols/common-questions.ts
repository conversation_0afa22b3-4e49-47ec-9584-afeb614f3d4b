import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么Symbol属性不会被for...in遍历？',
    answer: 'Symbol属性不会被for...in遍历是设计上的考虑，目的是提供真正的"私有"属性机制。这样可以避免意外访问到内部实现细节，同时保持向后兼容性。如果需要获取Symbol属性，可以使用Object.getOwnPropertySymbols()或Reflect.ownKeys()。',
    code: `const sym = Symbol('private');
const obj = {
  name: '<PERSON>',
  age: 30,
  [sym]: 'secret data'
};

// 常规遍历方法看不到Symbol属性
console.log(Object.keys(obj)); // ['name', 'age']
console.log(Object.getOwnPropertyNames(obj)); // ['name', 'age']

for (const key in obj) {
  console.log(key); // 'name', 'age'
}

// 专门的方法可以获取Symbol属性
console.log(Object.getOwnPropertySymbols(obj)); // [Symbol(private)]
console.log(Reflect.ownKeys(obj)); // ['name', 'age', Symbol(private)]

// 实际应用：模拟私有属性
const PRIVATE_METHOD = Symbol('privateMethod');

class MyClass {
  constructor() {
    this.publicProp = 'public';
    this[PRIVATE_METHOD] = function() {
      return 'private method called';
    };
  }
  
  callPrivateMethod() {
    return this[PRIVATE_METHOD]();
  }
}

const instance = new MyClass();
console.log(Object.keys(instance)); // ['publicProp']`,
    tags: ['属性遍历', '私有属性', '封装'],
    relatedQuestions: ['对象属性遍历', 'JavaScript封装机制']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: 'Symbol能否被JSON.stringify序列化？',
    answer: 'Symbol属性不会被JSON.stringify序列化，这是有意的设计。Symbol通常用于存储元数据或私有信息，这些信息不应该出现在JSON中。如果需要序列化Symbol属性，需要手动处理或使用自定义的序列化方法。',
    code: `const sym = Symbol('metadata');
const obj = {
  name: 'John',
  age: 30,
  [sym]: 'private data'
};

// Symbol属性不会被序列化
console.log(JSON.stringify(obj)); // '{"name":"John","age":30}'

// 自定义序列化方法
function customStringify(obj) {
  const result = {};
  
  // 复制常规属性
  Object.assign(result, obj);
  
  // 处理Symbol属性
  const symbols = Object.getOwnPropertySymbols(obj);
  symbols.forEach(sym => {
    const key = sym.toString();
    result[key] = obj[sym];
  });
  
  return JSON.stringify(result);
}

console.log(customStringify(obj)); 
// '{"name":"John","age":30","Symbol(metadata)":"private data"}'

// 使用toJSON方法
const objWithToJSON = {
  name: 'John',
  [sym]: 'private',
  toJSON() {
    const result = { ...this };
    // 可以选择性地包含Symbol属性
    result.metadata = this[sym];
    return result;
  }
};

console.log(JSON.stringify(objWithToJSON)); 
// '{"name":"John","metadata":"private"}'`,
    tags: ['JSON序列化', 'Symbol序列化', '数据转换'],
    relatedQuestions: ['JSON.stringify原理', '自定义序列化']
  }
];

export default commonQuestions;
