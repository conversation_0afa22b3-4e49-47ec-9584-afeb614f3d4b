import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'ecommerce-system',
    title: '电商系统商品管理',
    description: '使用ES6类构建电商系统的商品管理模块，包括商品基类、不同类型商品的继承关系',
    businessValue: '提供清晰的商品层次结构，便于扩展新商品类型，提高代码复用性和维护性',
    scenario: '电商平台需要管理多种类型的商品（实体商品、数字商品、服务商品），每种商品有不同的属性和行为，需要统一的管理接口。',
    code: `// 商品基类
class Product {
  constructor(id, name, price, category) {
    this.id = id;
    this.name = name;
    this.price = price;
    this.category = category;
    this.createdAt = new Date();
    this.isActive = true;
  }
  
  // 计算折扣价格
  calculateDiscountPrice(discountPercent) {
    return this.price * (1 - discountPercent / 100);
  }
  
  // 获取商品信息
  getProductInfo() {
    return {
      id: this.id,
      name: this.name,
      price: this.price,
      category: this.category,
      isActive: this.isActive
    };
  }
  
  // 激活/停用商品
  toggleStatus() {
    this.isActive = !this.isActive;
    return this.isActive;
  }
  
  // 静态方法：验证商品数据
  static validateProductData(data) {
    const required = ['id', 'name', 'price', 'category'];
    return required.every(field => data.hasOwnProperty(field));
  }
  
  // 抽象方法（子类必须实现）
  getShippingInfo() {
    throw new Error('getShippingInfo must be implemented by subclass');
  }
}

// 实体商品类
class PhysicalProduct extends Product {
  constructor(id, name, price, category, weight, dimensions) {
    super(id, name, price, category);
    this.weight = weight;
    this.dimensions = dimensions;
    this.inventory = 0;
  }
  
  // 实现抽象方法
  getShippingInfo() {
    return {
      weight: this.weight,
      dimensions: this.dimensions,
      shippingRequired: true,
      estimatedDays: this.calculateShippingDays()
    };
  }
  
  // 计算配送天数
  calculateShippingDays() {
    const baseShippingDays = 3;
    const weightFactor = this.weight > 10 ? 2 : 0;
    return baseShippingDays + weightFactor;
  }
  
  // 库存管理
  updateInventory(quantity) {
    this.inventory += quantity;
    return this.inventory;
  }
  
  // 检查库存
  isInStock(requestedQuantity = 1) {
    return this.inventory >= requestedQuantity;
  }
}

// 数字商品类
class DigitalProduct extends Product {
  constructor(id, name, price, category, downloadUrl, fileSize) {
    super(id, name, price, category);
    this.downloadUrl = downloadUrl;
    this.fileSize = fileSize;
    this.downloadLimit = 5;
  }
  
  // 实现抽象方法
  getShippingInfo() {
    return {
      shippingRequired: false,
      deliveryMethod: 'instant_download',
      downloadUrl: this.downloadUrl
    };
  }
  
  // 生成下载链接
  generateDownloadLink(userId) {
    const token = this.generateSecureToken(userId);
    return \`\${this.downloadUrl}?token=\${token}&user=\${userId}\`;
  }
  
  // 生成安全令牌
  generateSecureToken(userId) {
    return btoa(\`\${userId}-\${this.id}-\${Date.now()}\`);
  }
}

// 商品管理器
class ProductManager {
  constructor() {
    this.products = new Map();
    this.categories = new Set();
  }
  
  // 添加商品
  addProduct(product) {
    if (!(product instanceof Product)) {
      throw new Error('Invalid product instance');
    }
    
    this.products.set(product.id, product);
    this.categories.add(product.category);
    return product;
  }
  
  // 获取商品
  getProduct(id) {
    return this.products.get(id);
  }
  
  // 按类别获取商品
  getProductsByCategory(category) {
    return Array.from(this.products.values())
      .filter(product => product.category === category);
  }
  
  // 搜索商品
  searchProducts(query) {
    const searchTerm = query.toLowerCase();
    return Array.from(this.products.values())
      .filter(product => 
        product.name.toLowerCase().includes(searchTerm) ||
        product.category.toLowerCase().includes(searchTerm)
      );
  }
}`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'ui-component-system',
    title: 'UI组件系统设计',
    description: '使用ES6类构建可复用的UI组件系统，实现组件继承和组合模式',
    businessValue: '提供统一的组件接口，支持主题定制和事件处理，提高UI开发效率',
    scenario: '前端应用需要一套统一的UI组件库，包括基础组件和复合组件，支持主题切换和事件处理。',
    code: `// UI组件基类
class UIComponent {
  constructor(element, options = {}) {
    this.element = element;
    this.options = { ...this.getDefaultOptions(), ...options };
    this.isInitialized = false;
    this.eventListeners = new Map();
    
    this.init();
  }
  
  // 默认选项（子类可重写）
  getDefaultOptions() {
    return {
      theme: 'default',
      disabled: false,
      className: ''
    };
  }
  
  // 初始化组件
  init() {
    this.render();
    this.bindEvents();
    this.isInitialized = true;
    this.emit('initialized');
  }
  
  // 渲染组件（抽象方法）
  render() {
    throw new Error('render method must be implemented');
  }
  
  // 绑定事件
  bindEvents() {
    // 子类实现具体事件绑定
  }
  
  // 事件系统
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }
  
  emit(event, data) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }
  
  // 销毁组件
  destroy() {
    this.eventListeners.clear();
    this.element.innerHTML = '';
    this.isInitialized = false;
  }
  
  // 静态方法：创建组件实例
  static create(selector, options) {
    const element = document.querySelector(selector);
    if (!element) {
      throw new Error(\`Element not found: \${selector}\`);
    }
    return new this(element, options);
  }
}

// 按钮组件
class Button extends UIComponent {
  getDefaultOptions() {
    return {
      ...super.getDefaultOptions(),
      text: 'Button',
      variant: 'primary',
      size: 'medium'
    };
  }
  
  render() {
    const { text, variant, size, className } = this.options;
    this.element.className = \`btn btn-\${variant} btn-\${size} \${className}\`;
    this.element.innerHTML = text;
    this.element.type = 'button';
  }
  
  bindEvents() {
    this.element.addEventListener('click', (e) => {
      if (!this.options.disabled) {
        this.emit('click', e);
      }
    });
  }
  
  // 设置文本
  setText(text) {
    this.options.text = text;
    this.element.innerHTML = text;
  }
  
  // 设置禁用状态
  setDisabled(disabled) {
    this.options.disabled = disabled;
    this.element.disabled = disabled;
    this.element.classList.toggle('disabled', disabled);
  }
}

// 模态框组件
class Modal extends UIComponent {
  getDefaultOptions() {
    return {
      ...super.getDefaultOptions(),
      title: 'Modal',
      content: '',
      closable: true,
      backdrop: true
    };
  }
  
  render() {
    const { title, content, closable } = this.options;
    
    this.element.className = 'modal';
    this.element.innerHTML = \`
      <div class="modal-backdrop"></div>
      <div class="modal-dialog">
        <div class="modal-header">
          <h3>\${title}</h3>
          \${closable ? '<button class="modal-close">&times;</button>' : ''}
        </div>
        <div class="modal-body">
          \${content}
        </div>
      </div>
    \`;
  }
  
  bindEvents() {
    if (this.options.closable) {
      const closeBtn = this.element.querySelector('.modal-close');
      closeBtn?.addEventListener('click', () => this.close());
    }
    
    if (this.options.backdrop) {
      const backdrop = this.element.querySelector('.modal-backdrop');
      backdrop?.addEventListener('click', () => this.close());
    }
  }
  
  // 显示模态框
  show() {
    this.element.style.display = 'block';
    document.body.classList.add('modal-open');
    this.emit('show');
  }
  
  // 隐藏模态框
  close() {
    this.element.style.display = 'none';
    document.body.classList.remove('modal-open');
    this.emit('close');
  }
}`
  }
];

export default businessScenarios;
