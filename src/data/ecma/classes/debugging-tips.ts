import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: 'ES6类使用中的常见错误和解决方案',
        sections: [
          {
            title: '类声明和调用错误',
            description: '类声明不提升和调用方式相关的错误',
            items: [
              {
                title: 'ReferenceError: Cannot access before initialization',
                description: '在类声明之前使用类',
                solution: '确保在类声明之后再使用类',
                prevention: '遵循先声明后使用的原则',
                code: `// ❌ 错误：类声明不提升
const obj = new MyClass(); // ReferenceError

class MyClass {
  constructor() {}
}

// ✅ 正确：先声明后使用
class MyClass {
  constructor() {}
}

const obj = new MyClass();`
              },
              {
                title: 'TypeError: Class constructor cannot be invoked without new',
                description: '直接调用类而不使用new关键字',
                solution: '始终使用new关键字调用类构造函数',
                prevention: '使用ESLint规则检测此类问题',
                code: `class MyClass {
  constructor(name) {
    this.name = name;
  }
}

// ❌ 错误：直接调用类
const obj = MyClass('test'); // TypeError

// ✅ 正确：使用new关键字
const obj = new MyClass('test');`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '使用开发工具调试类相关问题',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '利用浏览器工具调试类和继承问题',
            items: [
              {
                title: '原型链查看',
                description: '在控制台中查看对象的原型链',
                solution: '使用console.dir()和__proto__属性查看继承关系',
                prevention: '定期检查对象的原型链结构',
                code: `class Parent {
  parentMethod() {}
}

class Child extends Parent {
  childMethod() {}
}

const obj = new Child();

// 查看原型链
console.dir(obj);
console.log(obj.__proto__); // Child.prototype
console.log(obj.__proto__.__proto__); // Parent.prototype

// 检查继承关系
console.log(obj instanceof Child); // true
console.log(obj instanceof Parent); // true`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
