import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'ES6类与传统构造函数有什么区别？',
    answer: {
      brief: 'ES6类提供了更清晰的语法、自动严格模式、不提升、必须用new调用，以及更简洁的继承语法，而传统构造函数需要手动设置原型链和严格模式。',
      detailed: `ES6类与传统构造函数的主要区别：

**语法差异**：
- 类使用class关键字，语法更清晰
- 构造函数使用function关键字

**提升行为**：
- 类声明不会被提升
- 函数声明会被提升

**严格模式**：
- 类内部自动使用严格模式
- 构造函数需要手动启用

**调用方式**：
- 类必须使用new调用
- 构造函数可以直接调用（虽然不推荐）

**继承语法**：
- 类使用extends和super
- 构造函数需要手动设置原型链`
    },
   
    difficulty: 'medium',
    frequency: 'high',
    category: '基础概念',
    tags: ['类', '构造函数', '继承'],
    
    code: `// ES6类
class Person {
  constructor(name) {
    this.name = name;
  }
  
  greet() {
    return \`Hello, I'm \${this.name}\`;
  }
}

// 传统构造函数
function PersonConstructor(name) {
  this.name = name;
}

PersonConstructor.prototype.greet = function() {
  return \`Hello, I'm \${this.name}\`;
};

// 继承对比
class Student extends Person {
  constructor(name, grade) {
    super(name);
    this.grade = grade;
  }
}

// 传统继承
function StudentConstructor(name, grade) {
  PersonConstructor.call(this, name);
  this.grade = grade;
}
StudentConstructor.prototype = Object.create(PersonConstructor.prototype);
StudentConstructor.prototype.constructor = StudentConstructor;`,
    
    followUp: [
      '类的私有字段如何实现？',
      '静态方法和实例方法的区别？',
      '类的继承链是如何工作的？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '什么是类的私有字段？如何使用？',
    answer: {
      brief: 'ES2022引入的私有字段使用#前缀定义，只能在类内部访问，提供真正的封装性，防止外部直接访问内部状态。',
      detailed: `类的私有字段是ES2022引入的特性，使用#前缀定义，只能在类内部访问。

**特点**：
- 真正的私有性，外部无法访问
- 编译时检查，访问私有字段会报错
- 不会出现在对象的属性列表中
- 可以有私有方法和私有字段

**使用场景**：
- 内部状态管理
- 实现细节隐藏
- 防止外部误操作`
    },
   
    difficulty: 'medium',
    frequency: 'medium',
    category: '高级特性',
    tags: ['私有字段', '封装', 'ES2022'],
    
    code: `class BankAccount {
  // 私有字段
  #balance = 0;
  #accountNumber;
  
  constructor(accountNumber, initialBalance = 0) {
    this.#accountNumber = accountNumber;
    this.#balance = initialBalance;
  }
  
  // 私有方法
  #validateAmount(amount) {
    return amount > 0 && typeof amount === 'number';
  }
  
  // 公共方法
  deposit(amount) {
    if (this.#validateAmount(amount)) {
      this.#balance += amount;
      return this.#balance;
    }
    throw new Error('Invalid amount');
  }
  
  withdraw(amount) {
    if (this.#validateAmount(amount) && amount <= this.#balance) {
      this.#balance -= amount;
      return this.#balance;
    }
    throw new Error('Invalid withdrawal');
  }
  
  getBalance() {
    return this.#balance;
  }
}

const account = new BankAccount('123456', 1000);
console.log(account.getBalance()); // 1000
// console.log(account.#balance); // SyntaxError: Private field '#balance' must be declared in an enclosing class`,
    
    followUp: [
      '私有字段与闭包的区别？',
      '如何在子类中访问父类的私有字段？',
      '私有字段的性能影响？'
    ]
  }
];

export default interviewQuestions;
