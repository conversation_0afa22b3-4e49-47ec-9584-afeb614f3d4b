import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么类声明不会被提升？',
    answer: '类声明不会被提升是为了避免暂时性死区（TDZ）问题和保持一致性。与let/const类似，类在声明之前处于TDZ状态，这样可以防止在类定义完成之前就使用类，避免了一些微妙的错误。这种设计让代码的执行顺序更加可预测。',
    code: `// ❌ 错误：类声明不会提升
const instance = new MyClass(); // ReferenceError

class MyClass {
  constructor() {
    this.name = 'test';
  }
}

// ✅ 正确：先声明后使用
class MyClass {
  constructor() {
    this.name = 'test';
  }
}

const instance = new MyClass(); // 正常工作

// 对比：函数声明会提升
const fn = myFunction(); // 正常工作

function myFunction() {
  return 'hello';
}`,
    tags: ['提升', '暂时性死区', '类声明'],
    relatedQuestions: ['暂时性死区', '变量提升']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '类方法中的this指向问题如何解决？',
    answer: '类方法中的this指向取决于调用方式。当方法作为回调函数使用时，this可能丢失。解决方案包括：使用箭头函数、bind方法、或在构造函数中绑定this。',
    code: `class EventHandler {
  constructor(name) {
    this.name = name;
    
    // 方案1：在构造函数中绑定
    this.handleClick = this.handleClick.bind(this);
    
    // 方案2：使用箭头函数属性
    this.handleSubmit = () => {
      console.log(\`\${this.name} submitted\`);
    };
  }
  
  // 普通方法 - this可能丢失
  handleClick() {
    console.log(\`\${this.name} clicked\`);
  }
  
  // 箭头函数方法 - this始终正确
  handleHover = () => {
    console.log(\`\${this.name} hovered\`);
  }
}

const handler = new EventHandler('Button');

// 直接调用 - this正确
handler.handleClick(); // "Button clicked"

// 作为回调 - this可能丢失
setTimeout(handler.handleClick, 1000); // 可能输出 "undefined clicked"

// 使用绑定的方法 - this正确
setTimeout(handler.handleClick, 1000); // "Button clicked"

// 使用箭头函数 - this正确
setTimeout(handler.handleHover, 1000); // "Button hovered"`,
    tags: ['this绑定', '箭头函数', '回调函数'],
    relatedQuestions: ['this指向', '箭头函数', '事件处理']
  }
];

export default commonQuestions;
