import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `ES6类是JavaScript中面向对象编程的语法糖，基于原型继承机制构建。类提供了更清晰、更直观的方式来创建对象和实现继承。类声明创建一个基于原型继承的具有给定名称的新类，支持构造函数、实例方法、静态方法、getter/setter等特性。`,

  syntax: `// 类声明
class ClassName {
  // 构造函数
  constructor(param1, param2) {
    this.property1 = param1;
    this.property2 = param2;
  }
  
  // 实例方法
  instanceMethod() {
    return this.property1;
  }
  
  // 静态方法
  static staticMethod() {
    return 'Static method called';
  }
  
  // Getter
  get computedProperty() {
    return this.property1 + this.property2;
  }
  
  // Setter
  set updateProperty(value) {
    this.property1 = value;
  }
}

// 类继承
class ChildClass extends ParentClass {
  constructor(param1, param2, param3) {
    super(param1, param2); // 调用父类构造函数
    this.property3 = param3;
  }
  
  // 方法重写
  instanceMethod() {
    return super.instanceMethod() + ' - overridden';
  }
}`,

  quickExample: `// 用户管理系统示例
class User {
  constructor(name, email, role = 'user') {
    this.name = name;
    this.email = email;
    this.role = role;
    this.createdAt = new Date();
  }
  
  // 实例方法
  getProfile() {
    return {
      name: this.name,
      email: this.email,
      role: this.role,
      memberSince: this.createdAt.getFullYear()
    };
  }
  
  // 权限检查
  hasPermission(action) {
    const permissions = {
      user: ['read'],
      admin: ['read', 'write', 'delete'],
      moderator: ['read', 'write']
    };
    return permissions[this.role]?.includes(action) || false;
  }
  
  // 静态方法
  static validateEmail(email) {
    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);
  }
  
  // Getter
  get displayName() {
    return \`\${this.name} (\${this.role})\`;
  }
  
  // Setter
  set updateRole(newRole) {
    const validRoles = ['user', 'admin', 'moderator'];
    if (validRoles.includes(newRole)) {
      this.role = newRole;
    } else {
      throw new Error('Invalid role');
    }
  }
}

// 管理员类继承
class Admin extends User {
  constructor(name, email, department) {
    super(name, email, 'admin');
    this.department = department;
    this.permissions = ['read', 'write', 'delete', 'manage'];
  }
  
  // 管理用户
  manageUser(user, action) {
    if (!this.hasPermission('manage')) {
      throw new Error('Insufficient permissions');
    }
    
    console.log(\`Admin \${this.name} performed \${action} on user \${user.name}\`);
    return true;
  }
  
  // 重写权限检查
  hasPermission(action) {
    return this.permissions.includes(action);
  }
  
  // 静态方法
  static createSystemAdmin(name, email) {
    return new Admin(name, email, 'System');
  }
}

// 使用示例
const user = new User('Alice', '<EMAIL>');
const admin = new Admin('Bob', '<EMAIL>', 'IT');

console.log(user.displayName); // "Alice (user)"
console.log(admin.manageUser(user, 'suspend')); // true
console.log(User.validateEmail('<EMAIL>')); // true`,

  coreFeatures: [
    {
      feature: "类声明",
      description: "使用class关键字声明类，提供构造函数和方法定义",
      importance: "high" as const,
      details: "类声明不会被提升，必须先声明后使用"
    },
    {
      feature: "构造函数",
      description: "constructor方法用于初始化类实例",
      importance: "high" as const,
      details: "每个类只能有一个构造函数，用于设置初始状态"
    },
    {
      feature: "继承",
      description: "使用extends关键字实现类继承",
      importance: "high" as const,
      details: "支持单继承，可以重写父类方法和属性"
    },
    {
      feature: "静态方法",
      description: "使用static关键字定义类级别的方法",
      importance: "medium" as const,
      details: "静态方法属于类本身，不能访问实例属性"
    }
  ],

  keyFeatures: [
    {
      feature: "语法简洁性",
      description: "提供清晰的面向对象编程语法",
      importance: "high" as const,
      details: "比传统的原型链语法更直观易懂"
    },
    {
      feature: "继承机制",
      description: "支持类继承和方法重写",
      importance: "high" as const,
      details: "使用extends和super关键字实现继承"
    },
    {
      feature: "封装特性",
      description: "支持私有字段和方法（ES2022）",
      importance: "medium" as const,
      details: "使用#前缀定义私有成员"
    },
    {
      feature: "静态成员",
      description: "支持静态方法和静态字段",
      importance: "medium" as const,
      details: "类级别的方法和属性，不依赖实例"
    }
  ],

  limitations: [
    "类声明不会被提升，必须先声明后使用",
    "只支持单继承，不支持多重继承",
    "私有字段语法（#）需要较新的JavaScript环境支持",
    "类本质上仍是基于原型的，只是语法糖",
    "静态方法不能访问实例的this"
  ],

  bestPractices: [
    "使用PascalCase命名类，使用camelCase命名方法和属性",
    "在构造函数中初始化所有实例属性",
    "合理使用静态方法处理类级别的逻辑",
    "使用getter/setter控制属性访问",
    "遵循单一职责原则，保持类的功能聚焦"
  ],

  warnings: [
    "类声明不会被提升，使用前必须先声明",
    "在构造函数中调用super()必须在使用this之前",
    "私有字段只能在类内部访问，外部访问会报错"
  ],

  scenarioDiagram: `graph TD
    A[ES6类使用场景] --> B[面向对象编程]
    A --> C[代码组织]
    A --> D[继承体系]
    A --> E[现代框架]

    B --> B1[封装数据和方法]
    B --> B2[实例化对象]
    B --> B3[多态实现]
    B --> B4[接口定义]

    C --> C1[模块化设计]
    C --> C2[命名空间管理]
    C --> C3[代码复用]

    D --> D1[基类定义]
    D --> D2[子类扩展]
    D --> D3[方法重写]
    D --> D4[super调用]

    E --> E1[React组件]
    E --> E2[Vue组件]
    E --> E3[Node.js服务]
    E --> E4[TypeScript类型]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`
};

export default basicInfo;
