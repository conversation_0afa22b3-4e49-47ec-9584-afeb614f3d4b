import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: `ES6类的历史可以追溯到面向对象编程概念的发展和JavaScript语言的演进过程。从原型继承到类语法，体现了JavaScript向主流面向对象语言靠拢的努力。`,
  
  background: `在ES6之前，JavaScript使用原型继承和构造函数来实现面向对象编程。虽然功能强大，但语法复杂，学习曲线陡峭，特别是继承的实现需要大量样板代码。`,

  evolution: `ES6类的引入标志着JavaScript面向对象编程的重大改进，提供了更清晰、更直观的语法，同时保持了与原型系统的兼容性。`,

  timeline: [
    {
      year: '1995',
      event: 'JavaScript诞生',
      description: '基于原型的面向对象编程模型确立',
      significance: '奠定了JavaScript独特的面向对象基础'
    },
    {
      year: '2009',
      event: 'ES5发布',
      description: '引入Object.create()等方法，改进原型继承',
      significance: '为类语法的出现做了技术准备'
    },
    {
      year: '2015',
      event: 'ES6类语法发布',
      description: '正式引入class关键字和相关语法',
      significance: '彻底改变了JavaScript面向对象编程的写法'
    },
    {
      year: '2022',
      event: '私有字段标准化',
      description: 'ES2022正式引入私有字段语法',
      significance: '实现了真正的封装，完善了类的功能'
    }
  ],

  keyFigures: [
    {
      name: 'Brendan Eich',
      role: 'JavaScript创造者',
      contribution: '设计了基于原型的面向对象模型',
      significance: '为后续的类语法发展奠定了基础'
    },
    {
      name: 'TC39委员会',
      role: 'ECMAScript标准制定者',
      contribution: '设计和标准化类语法',
      significance: '确保了类语法与JavaScript生态系统的良好集成'
    }
  ],

  concepts: [
    {
      term: '面向对象编程',
      definition: '基于对象概念的编程范式，强调封装、继承和多态',
      evolution: '从基于类的语言影响到基于原型的JavaScript实现',
      modernRelevance: '现代软件开发的主要编程范式之一'
    },
    {
      term: '原型继承',
      definition: 'JavaScript特有的继承机制，对象直接从其他对象继承',
      evolution: '从复杂的原型链操作发展为简洁的类语法',
      modernRelevance: '仍然是JavaScript继承的底层机制'
    }
  ],

  designPhilosophy: `ES6类的设计体现了"语法糖而非革命"的哲学，在保持JavaScript原型特性的同时，提供了更友好的语法界面。`,

  impact: `ES6类的引入不仅改变了JavaScript的编程方式，也降低了从其他面向对象语言转向JavaScript的学习门槛，促进了JavaScript在大型项目中的应用。`,

  modernRelevance: `在现代JavaScript开发中，类已经成为组织代码的标准方式，特别是在React、Vue等框架中得到广泛应用。`
};

export default knowledgeArchaeology;
