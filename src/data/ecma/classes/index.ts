import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const classesData: ApiItem = {
  id: 'classes',
  title: 'ES6 Classes',
  description: 'ES6类语法，提供面向对象编程的语法糖，支持继承、静态方法、私有字段等特性',
  category: 'ECMA特性',
  difficulty: 'medium',
  
  syntax: `class ClassName { constructor() {} method() {} static staticMethod() {} }`,
  example: `class User { constructor(name) { this.name = name; } greet() { return \`Hello, \${this.name}!\`; } }`,
  notes: 'ES6类是基于原型的语法糖，提供更清晰的面向对象编程方式',
  
  version: 'ES6 (ES2015)',
  tags: ['ES6', 'JavaScript', '面向对象', '类', '继承'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default classesData;
