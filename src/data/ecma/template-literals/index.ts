import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const templateliteralsData: ApiItem = {
  id: 'template-literals',
  title: '模板字符串',
  description: 'ES6模板字符串语法，支持字符串插值、多行字符串和标签模板',
  category: 'ECMA特性',
  difficulty: 'medium',
  
  syntax: `\`Hello \${name}!\`; \`多行\n字符串\`; tag\`模板\${expr}\``,
  example: `const name = 'World'; const msg = \`Hello \${name}!\`; const multi = \`Line 1\nLine 2\`;`,
  notes: '支持表达式插值、转义序列和自定义标签函数',
  
  version: 'ES6 (ES2015)',
  tags: ['ES6', 'JavaScript', '字符串', '模板'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default templateliteralsData;