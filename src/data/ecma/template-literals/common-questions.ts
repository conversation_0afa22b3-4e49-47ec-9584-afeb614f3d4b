import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '如何在模板字符串中正确使用反引号和转义字符？',
    answer: '在模板字符串中处理特殊字符需要了解转义规则和嵌套使用方法。使用反斜杠转义反引号，使用 \\$\\{\\} 来输出字面量的 ${} 文本。',
    code: `// 转义反引号示例
const message = "包含反引号 \` 的字符串";
// 转义插值表达式示例  
const literal = "显示: $" + "{variable}";`,
    tags: ['转义字符', '反引号'],
    relatedQuestions: ['模板字符串嵌套', '字符串转义规则']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '模板字符串在不同浏览器中的兼容性如何？',
    answer: `模板字符串是ES6特性，在现代浏览器中支持良好。Chrome 41+、Firefox 34+、Safari 9+、Edge 12+ 都支持，但IE不支持。推荐使用Babel转译以确保兼容性。`,
    code: `// 检测支持
if (typeof \\\`\\\` === 'string') {
  console.log('支持模板字符串');
}

// Babel转译后
var message = "Hello " + name;`,
    tags: ['浏览器兼容性', 'Babel'],
    relatedQuestions: ['ES6兼容性', 'Babel配置']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-3',
    question: '模板字符串的性能相比字符串拼接如何？',
    answer: `模板字符串在大多数情况下性能与字符串拼接相当，甚至在某些场景下更优。现代JavaScript引擎对模板字符串进行了优化，包括编译时优化和运行时缓存。但在大量循环或频繁调用的场景下，简单的字符串拼接可能略有优势。`,
    code: `// 性能测试示例
const iterations = 1000000;
const name = "张三";
const age = 25;

// 模板字符串
console.time('template');
for (let i = 0; i < iterations; i++) {
  const result = \`用户: \${name}, 年龄: \${age}\`;
}
console.timeEnd('template');

// 字符串拼接
console.time('concat');
for (let i = 0; i < iterations; i++) {
  const result = '用户: ' + name + ', 年龄: ' + age;
}
console.timeEnd('concat');`,
    tags: ['性能优化', '字符串拼接'],
    relatedQuestions: ['JavaScript性能优化', '字符串操作最佳实践']
  }
];

export default commonQuestions;