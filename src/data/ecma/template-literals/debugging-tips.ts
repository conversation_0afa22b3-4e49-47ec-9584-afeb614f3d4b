import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: '模板字符串虽然语法简单，但在实际使用中可能遇到一些常见问题。了解这些问题及其解决方案有助于提高开发效率。',
        sections: [
          {
            title: '语法错误',
            description: '模板字符串的语法错误通常与引号使用、转义字符和插值表达式相关',
            items: [
              {
                title: 'SyntaxError: Unexpected token',
                description: '在模板字符串中使用了错误的引号或转义字符',
                solution: '检查反引号的配对，确保正确转义特殊字符',
                prevention: '使用代码编辑器的语法高亮功能，及时发现语法错误',
                code: `// ❌ 错误：混用引号
const wrong = \`Hello "world'\`;

// ✅ 正确：统一使用反引号
const correct = \`Hello "world"\`;

// ❌ 错误：未转义反引号
const wrong2 = \`包含反引号 \` 的字符串\`;

// ✅ 正确：转义反引号
const correct2 = \`包含反引号 \\\` 的字符串\`;`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '现代开发工具为模板字符串的调试提供了强大的支持，合理使用这些工具可以大大提高调试效率。',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '利用浏览器开发者工具调试模板字符串相关问题',
            items: [
              {
                title: 'Console调试',
                description: '使用console.log查看模板字符串的构建过程',
                solution: '在模板字符串的关键位置添加调试输出',
                prevention: '建立良好的调试习惯，及时清理调试代码',
                code: `// 调试模板字符串构建
const name = 'Alice';
const age = 25;

console.log('name:', name);
console.log('age:', age);
const result = \`用户: \${name}, 年龄: \${age}\`;
console.log('result:', result);

// 调试复杂表达式
const user = { name: 'Bob', profile: { age: 30 } };
console.log('user:', user);
const message = \`Hello \${user.name}, you are \${user.profile?.age || 'unknown'} years old\`;
console.log('message:', message);`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;