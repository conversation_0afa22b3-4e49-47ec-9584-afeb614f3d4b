import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `模板字符串（Template Literals）是ES6引入的一种新的字符串字面量语法，使用反引号(\`)包围字符串。它支持字符串插值、多行字符串、嵌入表达式和标签模板等强大功能。模板字符串不仅简化了字符串拼接操作，还提供了更好的可读性和表达能力，特别适用于动态内容生成、HTML模板、SQL查询构建等场景。`,
  
  introduction: `模板字符串是ES6(ES2015)引入的强大字符串处理特性，主要用于字符串插值、多行字符串创建和标签模板处理。它采用反引号语法的设计模式，提供了更灵活和表达力更强的字符串操作方式。`,

  syntax: `// 基本模板字符串
\`Hello World\`

// 表达式插值
\`Hello \${name}!\`
\`The result is \${a + b}\`
\`User: \${user.name}, Age: \${user.age}\`

// 多行字符串
\`First line
Second line
Third line\`

// 嵌套表达式
\`Price: \${price * (1 + tax)}\`
\`Status: \${isActive ? 'Active' : 'Inactive'}\`

// 标签模板
tag\`Hello \${name}!\`
html\`<div>\${content}</div>\`
sql\`SELECT * FROM users WHERE id = \${userId}\`

// 转义字符
\`Line 1\\nLine 2\`  // 换行符
\`Quote: \\"\${text}\\"\`  // 引号`,

  quickExample: `// 基本字符串插值
const name = "张三";
const age = 25;
const greeting = \`你好，我是\${name}，今年\${age}岁\`;
console.log(greeting); // "你好，我是张三，今年25岁"

// 表达式计算
const a = 10, b = 20;
const calculation = \`10 + 20 = \${a + b}\`;
console.log(calculation); // "10 + 20 = 30"

// 多行字符串
const multiline = \`这是第一行
这是第二行
这是第三行\`;
console.log(multiline);

// 条件表达式
const status = \`状态: \${age >= 18 ? '成年' : '未成年'}\`;
console.log(status); // "状态: 成年"

// 函数调用
function formatDate(date) {
  return date.toLocaleDateString();
}

const message = \`今天是 \${formatDate(new Date())}\`;
console.log(message);

// HTML模板生成
const user = { name: 'Alice', email: '<EMAIL>' };
const htmlTemplate = \`
  <div class="user-card">
    <h2>\${user.name}</h2>
    <p>Email: \${user.email}</p>
    <p>注册时间: \${new Date().getFullYear()}</p>
  </div>
\`;
console.log(htmlTemplate);

// 标签模板示例
function highlight(strings, ...values) {
  return strings.reduce((result, string, i) => {
    const value = values[i] ? \`<mark>\${values[i]}</mark>\` : '';
    return result + string + value;
  }, '');
}

const searchTerm = 'JavaScript';
const text = highlight\`学习 \${searchTerm} 是很有趣的事情\`;
console.log(text); // "学习 <mark>JavaScript</mark> 是很有趣的事情"

// SQL查询构建（注意：实际使用中需要防SQL注入）
function sql(strings, ...values) {
  // 这里应该包含SQL注入防护逻辑
  return strings.reduce((query, string, i) => {
    const value = values[i] !== undefined ? values[i] : '';
    return query + string + value;
  }, '');
}

const userId = 123;
const query = sql\`SELECT * FROM users WHERE id = \${userId}\`;
console.log(query); // "SELECT * FROM users WHERE id = 123"

// 国际化模板
const locale = 'zh-CN';
const price = 99.99;
const currency = locale === 'zh-CN' ? '¥' : '$';
const priceText = \`价格: \${currency}\${price}\`;
console.log(priceText); // "价格: ¥99.99"`,

  scenarioDiagram: `graph TD
    A[模板字符串使用场景] --> B[字符串插值]
    A --> C[多行字符串]
    A --> D[标签模板]

    B --> B1[变量嵌入]
    B --> B2[表达式计算]
    B --> B3[函数调用结果]

    C --> C1[代码模板]
    C --> C2[HTML内容]
    C --> C3[配置文件]

    D --> D1[国际化处理]
    D --> D2[样式生成]
    D --> D3[SQL查询构建]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0`,
  
  parameters: [
    {
      name: "表达式",
      type: "any",
      required: false,
      description: "嵌入在${}中的JavaScript表达式，可以是变量、函数调用或任何有效表达式",
      details: "示例: ${name}, ${user.age}, ${getValue()}, ${a + b}"
    },
    {
      name: "标签函数",
      type: "function",
      required: false,
      description: "处理模板字符串的函数，接收字符串数组和插值表达式",
      details: "示例: function myTag(strings, ...values) { return processed; }"
    }
  ],
  
  returnValue: {
    type: "string",
    description: "处理后的字符串，所有插值表达式都被计算并转换为字符串"
  },

  coreFeatures: [
    {
      feature: "反引号语法",
      description: "使用反引号(`)包围字符串",
      importance: "high" as const,
      details: "区别于单引号和双引号的新语法"
    },
    {
      feature: "表达式插值",
      description: "使用${}嵌入JavaScript表达式",
      importance: "critical" as const,
      details: "支持变量、函数调用、运算等任意表达式"
    },
    {
      feature: "多行支持",
      description: "原生支持多行字符串",
      importance: "high" as const,
      details: "无需转义字符或字符串拼接"
    },
    {
      feature: "标签模板",
      description: "支持自定义处理函数",
      importance: "medium" as const,
      details: "实现高级字符串处理和DSL"
    }
  ],
  
  keyFeatures: [
    {
      feature: "表达式插值",
      description: "在字符串中嵌入JavaScript表达式，自动计算并转换为字符串",
      importance: "high" as const,
      details: "使用${}语法嵌入任何有效的JavaScript表达式"
    },
    {
      feature: "多行字符串",
      description: "直接支持跨行字符串，无需使用\\n或字符串拼接",
      importance: "high" as const,
      details: "保留原始格式，包括缩进和换行符"
    },
    {
      feature: "标签模板",
      description: "允许自定义函数处理模板字符串，实现复杂的字符串处理逻辑",
      importance: "medium" as const,
      details: "标签函数接收字符串片段和插值，可进行自定义处理"
    },
    {
      feature: "自动转义",
      description: "表达式结果自动转换为字符串，支持各种数据类型",
      importance: "medium" as const,
      details: "undefined和null转换为空字符串，对象调用toString()方法"
    }
  ],
  
  limitations: [
    "插值表达式必须是有效的JavaScript表达式",
    "嵌套模板字符串时需要注意转义问题",
    "标签模板的复杂度可能影响代码可读性",
    "在某些环境下性能可能不如字符串拼接",
    "模板字符串中的空白字符会被保留"
  ],
  
  bestPractices: [
    "对于简单的字符串插值，优先使用模板字符串而非拼接",
    "多行字符串中注意缩进对最终结果的影响",
    "复杂的模板逻辑考虑使用标签函数进行封装",
    "避免在模板字符串中嵌入过于复杂的表达式",
    "使用模板字符串创建HTML时要注意XSS安全问题"
  ],
  
  warnings: [
    "模板字符串中的所有空白字符（包括换行和缩进）都会被保留",
    "表达式求值时的错误会导致整个模板字符串失败",
    "在循环中大量使用模板字符串可能影响性能"
  ]
};

export default basicInfo;