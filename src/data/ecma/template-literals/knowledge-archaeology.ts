import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  introduction: `模板字符串的历史可以追溯到早期编程语言中的字符串插值概念。从Shell脚本的变量替换到现代编程语言的模板系统，这一特性经历了漫长的演进过程。`,

  background: `在JavaScript引入模板字符串之前，开发者主要依赖字符串拼接来构建动态字符串。这种方式不仅代码冗长，而且容易出错。随着Web应用复杂度的增加，对更优雅的字符串处理方案的需求日益迫切。`,

  evolution: `模板字符串的演进体现了编程语言设计从命令式向声明式的转变。它不仅解决了语法层面的问题，更重要的是改变了开发者思考字符串构建的方式，从程序化的拼接转向模板化的表达。`,

  timeline: [
    {
      year: '1970s',
      event: 'Shell脚本变量替换',
      description: 'Unix Shell引入了${}语法进行变量替换，成为现代模板字符串的雏形',
      significance: '确立了插值语法的基本模式，影响了后续编程语言的设计'
    },
    {
      year: '1990s',
      event: 'Perl字符串插值',
      description: 'Perl语言普及了字符串插值的概念，使其成为动态语言的标准特性',
      significance: '证明了字符串插值在提高代码可读性方面的价值'
    },
    {
      year: '2000s',
      event: '模板引擎兴起',
      description: '各种模板引擎如Mustache、Handlebars等开始流行，为Web开发提供了强大的模板功能',
      significance: '推动了模板语法的标准化和最佳实践的形成'
    },
    {
      year: '2015',
      event: 'ES6模板字符串发布',
      description: 'ECMAScript 2015正式引入模板字符串，为JavaScript带来了原生的字符串插值支持',
      significance: '标志着JavaScript在字符串处理能力上的重大突破'
    }
  ],

  keyFigures: [
    {
      name: 'Brendan Eich',
      role: 'JavaScript创造者',
      contribution: '虽然不是模板字符串的直接设计者，但他为JavaScript的演进奠定了基础',
      significance: '他的设计哲学影响了JavaScript后续特性的发展方向'
    },
    {
      name: 'TC39委员会',
      role: 'ECMAScript标准制定者',
      contribution: '负责模板字符串特性的设计、讨论和标准化工作',
      significance: '确保了模板字符串与JavaScript生态系统的良好集成'
    }
  ],

  concepts: [
    {
      term: '字符串插值',
      definition: '在字符串字面量中嵌入变量或表达式的技术',
      evolution: '从简单的变量替换发展为支持复杂表达式的模板系统',
      modernRelevance: '成为现代编程语言的标准特性，极大提高了代码的可读性和维护性'
    },
    {
      term: '标签模板',
      definition: '允许函数处理模板字符串的高级特性',
      evolution: '从简单的字符串处理发展为构建DSL的强大工具',
      modernRelevance: '为CSS-in-JS、GraphQL等现代技术提供了基础设施'
    }
  ],

  designPhilosophy: `模板字符串的设计体现了"简单性与强大性并存"的哲学。它在保持语法简洁的同时，通过标签模板等高级特性提供了强大的扩展能力。这种设计让初学者容易上手，同时为高级用户提供了足够的灵活性。`,

  impact: `模板字符串的引入不仅改变了JavaScript的字符串处理方式，更推动了整个前端生态系统的发展。它为现代前端框架、构建工具和开发工具的创新提供了基础，成为现代JavaScript开发不可或缺的特性。`,

  modernRelevance: `在当今的JavaScript开发中，模板字符串已经成为标准实践。它不仅用于简单的字符串拼接，更是构建复杂模板系统、DSL和代码生成工具的基础。随着JavaScript在各个领域的应用扩展，模板字符串的重要性还将继续增长。`
};

export default knowledgeArchaeology;