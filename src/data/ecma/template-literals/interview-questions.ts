import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: '什么是模板字符串(Template Literals)？它相比传统字符串拼接有哪些优势？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: '模板字符串是ES6引入的字符串语法，使用反引号包围，支持字符串插值、多行字符串和表达式嵌入。',
      detailed: `模板字符串(Template Literals)是ES6(ES2015)引入的新型字符串字面量语法，使用反引号(\\\`)代替普通的单引号或双引号。

核心特性：

1. **字符串插值**: 使用\\\${expression}语法嵌入变量和表达式
2. **多行字符串**: 直接支持换行，无需\\n转义符
3. **表达式计算**: 插值中可以使用任何有效的JavaScript表达式
4. **标签模板**: 支持自定义处理函数

与传统字符串拼接的优势：
- 代码可读性更强，结构更清晰
- 减少字符串拼接错误
- 原生支持多行文本
- 自动类型转换和字符串化
- 支持复杂的逻辑表达式`,
      code: `// 传统字符串拼接
const name = "张三";
const age = 25;
const traditional = "你好，我是" + name + "，今年" + age + "岁";

// 模板字符串
const modern = \\\`你好，我是\\\${name}，今年\\\${age}岁\\\`;

// 多行字符串对比
const multilineTraditional = "第一行\\n" +
  "第二行\\n" +
  "第三行";

const multilineModern = \\\`第一行
第二行
第三行\\\`;

// 表达式计算
const price = 99.99;
const discount = 0.1;
const message = \\\`原价：¥\\\${price}，折后价：¥\\\${(price * (1 - discount)).toFixed(2)}\\\`;

// 条件表达式
const user = { name: "李四", vip: true };
const greeting = \\\`欢迎\\\${user.vip ? "VIP用户" : "普通用户"} \\\${user.name}\\\`;

console.log(message); // "原价：¥99.99，折后价：¥89.99"
console.log(greeting); // "欢迎VIP用户 李四"`
    }
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '什么是标签模板(Tagged Templates)？请写一个实际的应用示例。',
    difficulty: 'medium',
    frequency: 'high',
    category: '标签模板',
    answer: {
      brief: '标签模板是模板字符串的高级特性，允许使用函数处理模板字符串，标签函数接收字符串片段数组和插值参数。',
      detailed: `标签模板(Tagged Templates)是模板字符串的高级用法，它允许使用函数来自定义模板字符串的处理逻辑。

工作原理：
1. 标签函数接收字符串片段数组作为第一个参数
2. 插值表达式的值作为后续参数传递
3. 可以在标签函数中进行自定义处理

标签函数签名：
\\\`\\\`\\\`javascript
function tagFunction(strings, ...values) {
  // strings: 字符串片段数组
  // values: 插值表达式的值
  return processedString;
}
\\\`\\\`\\\`

常见应用场景：
- 国际化(i18n)处理
- HTML转义和安全处理
- SQL查询构建(防注入)
- 样式处理和CSS-in-JS
- 日志格式化`,
      code: `// 基础标签模板示例
function highlight(strings, ...values) {
  return strings.reduce((result, string, i) => {
    const value = values[i] ? \\\`<strong>\\\${values[i]}</strong>\\\` : '';
    return result + string + value;
  }, '');
}

const name = "张三";
const score = 95;
const result = highlight\\\`学生 \\\${name} 的成绩是 \\\${score} 分\\\`;
console.log(result); // "学生 <strong>张三</strong> 的成绩是 <strong>95</strong> 分"

// 实际应用：安全的HTML模板
function safeHTML(strings, ...values) {
  const escape = (str) => String(str)
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
    
  return strings.reduce((result, string, i) => {
    const value = values[i] ? escape(values[i]) : '';
    return result + string + value;
  }, '');
}

const userInput = '<script>alert("XSS")</script>';
const userName = "用户输入";
const safeOutput = safeHTML\\\`<div>用户：\\\${userName}，内容：\\\${userInput}</div>\\\`;
console.log(safeOutput); 
// "<div>用户：用户输入，内容：&lt;script&gt;alert(&quot;XSS&quot;)&lt;/script&gt;</div>"

// 实际应用：SQL查询构建器(防注入)
function sql(strings, ...values) {
  const escapedValues = values.map(value => {
    if (typeof value === 'string') {
      return "'" + value.replace(/'/g, "''") + "'";
    }
    if (typeof value === 'number') {
      return value.toString();
    }
    return 'NULL';
  });
  
  return strings.reduce((query, string, i) => {
    return query + string + (escapedValues[i] || '');
  }, '');
}

const userId = 123;
const email = "<EMAIL>";
const query = sql\\\`SELECT * FROM users WHERE id = \\\${userId} AND email = \\\${email}\\\`;
console.log(query); // "SELECT * FROM users WHERE id = 123 AND email = '<EMAIL>'"

// 国际化应用示例
const translations = {
  'zh': { greeting: '你好 {name}，今天是 {date}' },
  'en': { greeting: 'Hello {name}, today is {date}' }
};

function i18n(strings, ...values) {
  const locale = 'zh'; // 获取当前语言
  const key = strings[0].trim();
  const template = translations[locale][key] || key;
  
  return template.replace(/\\{(\\w+)\\}/g, (match, placeholder) => {
    const index = parseInt(placeholder.replace(/\\D/g, '')) - 1;
    return values[index] || match;
  });
}

const name = "张三";
const date = "2024年1月15日";
const message = i18n\\\`greeting\\\`; // 需要配合特殊语法使用
// 实际使用中需要更复杂的实现`
    }
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 3,
    question: '模板字符串的性能特点是什么？在什么情况下应该避免使用？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '性能优化',
    answer: {
      brief: '模板字符串在编译时优化静态部分，运行时性能接近字符串拼接。应避免在高频循环中使用复杂模板和深度嵌套标签函数。',
      detailed: `模板字符串的性能特点和优化策略：

**编译时优化**：
1. 静态字符串片段在编译阶段预处理
2. 插值表达式转换为高效的函数调用
3. 字符串构建使用内部优化算法

**运行时性能**：
1. 简单插值：性能接近或略优于字符串拼接
2. 复杂表达式：可能比字符串拼接慢5-15%
3. 标签模板：增加函数调用开销

**内存管理**：
- 使用字符串构建器模式，减少临时对象
- 标签模板函数的字符串数组会被缓存
- 避免频繁创建新的字符串对象

**避免使用的场景**：
1. 高频率循环中的复杂模板
2. 性能敏感的字符串操作
3. 大量数据的批量处理
4. 深度嵌套的标签模板函数`,
      code: `// 性能对比测试
console.time('字符串拼接');
for (let i = 0; i < 100000; i++) {
  const result = "Hello " + "World " + i;
}
console.timeEnd('字符串拼接'); // 约 2-3ms

console.time('模板字符串');
for (let i = 0; i < 100000; i++) {
  const result = \\\`Hello World \\\${i}\\\`;
}
console.timeEnd('模板字符串'); // 约 3-4ms

// ❌ 性能问题：高频循环中的复杂模板
function performanceProblem(data) {
  return data.map(item => {
    // 复杂的模板计算
    const processedData = heavyProcessing(item);
    return \\\`
      <div class="item \\\${item.type}">
        <h3>\\\${processedData.title}</h3>
        <p>\\\${processedData.description.slice(0, 100)}</p>
        <span>\\\${formatDate(item.date)}</span>
        <div class="price">\\\${formatCurrency(item.price)}</div>
      </div>
    \\\`;
  });
}

// ✅ 优化方案：预处理 + 简单模板
function optimizedSolution(data) {
  // 预处理数据
  const processedData = data.map(item => ({
    ...item,
    processed: heavyProcessing(item),
    formattedDate: formatDate(item.date),
    formattedPrice: formatCurrency(item.price)
  }));
  
  // 简单模板渲染
  return processedData.map(item => \\\`
    <div class="item \\\${item.type}">
      <h3>\\\${item.processed.title}</h3>
      <p>\\\${item.processed.shortDescription}</p>
      <span>\\\${item.formattedDate}</span>
      <div class="price">\\\${item.formattedPrice}</div>
    </div>
  \\\`);
}

// ❌ 避免：复杂的标签模板嵌套
function nestedTaggedTemplates(data) {
  return data.map(item => 
    sanitize\\\`\\\${format\\\`\\\${validate\\\`\\\${item.content}\\\`}\\\`}\\\`
  );
}

// ✅ 优化：链式处理
function chainedProcessing(data) {
  return data.map(item => {
    const validated = validate(item.content);
    const formatted = format(validated);
    const sanitized = sanitize(formatted);
    return sanitized;
  });
}

// 内存优化：缓存模板函数
const memoizedTemplate = (() => {
  const cache = new Map();
  
  return function templateWithCache(type, data) {
    const key = \\\`\\\${type}-\\\${Object.keys(data).join('-')}\\\`;
    
    if (cache.has(key)) {
      return cache.get(key)(data);
    }
    
    const template = createTemplate(type);
    cache.set(key, template);
    return template(data);
  };
})();

// 大数据处理优化
function processLargeDataset(largeArray) {
  const chunkSize = 1000;
  const results = [];
  
  for (let i = 0; i < largeArray.length; i += chunkSize) {
    const chunk = largeArray.slice(i, i + chunkSize);
    
    // 批量处理，减少单次模板创建开销
    const processed = chunk.map(item => 
      \\\`\\\${item.id}: \\\${item.name}\\\`
    );
    
    results.push(...processed);
    
    // 允许事件循环处理其他任务
    if (i % (chunkSize * 10) === 0) {
      await new Promise(resolve => setTimeout(resolve, 0));
    }
  }
  
  return results;
}`
    }
  }
];

export default interviewQuestions;