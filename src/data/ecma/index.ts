import { ApiItem } from '@/types/api';

// 已实现的 ECMA 特性
import arrowFunctionsData from './arrow-functions';
import destructuringData from './destructuring';
import templateLiteralsData from './template-literals';
import constLetData from './const-let';
import asyncAwaitData from './async-await';
import classesData from './classes';
import modulesData from './modules';
import promisesData from './promises';
import symbolsData from './symbols';
import iteratorsData from './iterators';
import generatorsData from './generators';
import optionalChainingData from './optional-chaining';
import nullishCoalescingData from './nullish-coalescing';
import objectEntriesData from './object-entries';
import objectValuesData from './object-values';
import stringPaddingData from './string-padding';
import restSpreadData from './rest-spread';
import asyncIteratorsData from './async-iterators';
import arrayFlatData from './array-flat';

// 所有已实现的 ECMA APIs
const ecmaApis: ApiItem[] = [
  arrowFunctionsData,
  destructuringData,
  templateLiteralsData,
  constLetData,
  asyncAwaitData,
  classesData,
  modulesData,
  promisesData,
  symbolsData,
  iteratorsData,
  generatorsData,
  optionalChainingData,
  nullishCoalescingData,
  objectEntriesData,
  objectValuesData,
  stringPaddingData,
  restSpreadData,
  asyncIteratorsData,
  arrayFlatData
];

export default ecmaApis;

// 统计信息
export const ecmaStats = {
  totalApis: ecmaApis.length,
  implemented: ['arrow-functions', 'destructuring', 'template-literals', 'const-let', 'async-await', 'classes', 'modules', 'promises', 'symbols', 'iterators', 'generators', 'optional-chaining', 'nullish-coalescing', 'object-entries', 'object-values', 'string-padding', 'rest-spread', 'async-iterators'],
  planned: []
};
