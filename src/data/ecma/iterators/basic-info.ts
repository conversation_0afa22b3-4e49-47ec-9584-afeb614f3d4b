import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `迭代器协议是ES6引入的统一遍历标准，定义了对象如何被遍历。迭代器协议包括可迭代协议（Iterable Protocol）和迭代器协议（Iterator Protocol）。可迭代对象必须实现Symbol.iterator方法，该方法返回一个迭代器对象。迭代器对象必须实现next()方法，返回包含value和done属性的结果对象。`,

  syntax: `// 可迭代协议
const iterable = {
  [Symbol.iterator]() {
    return iterator; // 返回迭代器对象
  }
};

// 迭代器协议
const iterator = {
  next() {
    return { value: any, done: boolean };
  }
};

// for...of循环
for (const item of iterable) {
  console.log(item);
}

// 手动迭代
const iter = iterable[Symbol.iterator]();
let result = iter.next();
while (!result.done) {
  console.log(result.value);
  result = iter.next();
}

// 解构赋值
const [first, second] = iterable;

// 扩展运算符
const array = [...iterable];`,

  quickExample: `// 自定义可迭代对象
const range = {
  start: 1,
  end: 5,
  
  [Symbol.iterator]() {
    let current = this.start;
    const end = this.end;
    
    return {
      next() {
        if (current <= end) {
          return { value: current++, done: false };
        }
        return { done: true };
      }
    };
  }
};

// 使用for...of遍历
for (const num of range) {
  console.log(num); // 1, 2, 3, 4, 5
}

// 转换为数组
const numbers = [...range]; // [1, 2, 3, 4, 5]

// 解构赋值
const [first, second, ...rest] = range;
console.log(first, second, rest); // 1, 2, [3, 4, 5]

// 字符串迭代器
const str = 'Hello';
for (const char of str) {
  console.log(char); // 'H', 'e', 'l', 'l', 'o'
}

// 数组迭代器
const arr = ['a', 'b', 'c'];
const iterator = arr[Symbol.iterator]();
console.log(iterator.next()); // { value: 'a', done: false }
console.log(iterator.next()); // { value: 'b', done: false }
console.log(iterator.next()); // { value: 'c', done: false }
console.log(iterator.next()); // { done: true }

// Map迭代器
const map = new Map([['key1', 'value1'], ['key2', 'value2']]);
for (const [key, value] of map) {
  console.log(key, value);
}

// Set迭代器
const set = new Set([1, 2, 3]);
for (const value of set) {
  console.log(value);
}

// 自定义迭代器类
class Counter {
  constructor(max) {
    this.max = max;
  }
  
  [Symbol.iterator]() {
    let count = 0;
    const max = this.max;
    
    return {
      next() {
        if (count < max) {
          return { value: count++, done: false };
        }
        return { done: true };
      }
    };
  }
}

const counter = new Counter(3);
for (const num of counter) {
  console.log(num); // 0, 1, 2
}`,

  coreFeatures: [
    {
      feature: "可迭代协议",
      description: "对象必须实现Symbol.iterator方法",
      importance: "high" as const,
      details: "定义了对象如何被for...of循环遍历"
    },
    {
      feature: "迭代器协议",
      description: "迭代器对象必须实现next()方法",
      importance: "high" as const,
      details: "next()方法返回{value, done}格式的结果"
    },
    {
      feature: "for...of循环",
      description: "基于迭代器协议的新循环语法",
      importance: "high" as const,
      details: "提供了统一的遍历语法"
    },
    {
      feature: "内置可迭代对象",
      description: "Array、String、Map、Set等都实现了迭代器",
      importance: "medium" as const,
      details: "可以直接使用for...of遍历"
    }
  ],

  keyFeatures: [
    {
      feature: "统一遍历接口",
      description: "为不同类型的对象提供统一的遍历方式",
      importance: "high" as const,
      details: "简化了集合操作的复杂性"
    },
    {
      feature: "惰性求值",
      description: "迭代器支持按需生成值",
      importance: "medium" as const,
      details: "适合处理大数据集或无限序列"
    },
    {
      feature: "可组合性",
      description: "迭代器可以组合和链式调用",
      importance: "medium" as const,
      details: "支持函数式编程模式"
    }
  ],

  limitations: [
    "迭代器是一次性的，遍历完成后需要重新创建",
    "for...of循环不能获取索引信息",
    "迭代器协议相对复杂，学习成本较高",
    "自定义迭代器需要手动管理状态",
    "不支持异步迭代（需要使用异步迭代器）"
  ],

  bestPractices: [
    "优先使用内置的可迭代对象（Array、Set、Map等）",
    "自定义迭代器时确保正确实现协议",
    "使用生成器函数简化迭代器的创建",
    "考虑使用惰性求值处理大数据集",
    "为自定义类实现迭代器接口提高易用性"
  ],

  warnings: [
    "迭代器是一次性的，不能重复使用",
    "修改正在迭代的对象可能导致不可预期的行为",
    "无限迭代器需要小心处理，避免死循环"
  ]
,

  scenarioDiagram: `graph TD
    A[迭代器使用场景] --> B[自定义遍历]
    A --> C[数据结构实现]
    A --> D[惰性计算]
    A --> E[协议实现]

    B --> B1[树形结构遍历]
    B --> B2[图形算法]
    B --> B3[自定义顺序]

    C --> C1[链表实现]
    C --> C2[队列栈实现]
    C --> C3[集合类型]

    D --> D1[无限序列]
    D --> D2[按需生成]
    D --> D3[内存优化]

    E --> E1[Symbol.iterator]
    E --> E2[for...of支持]
    E --> E3[解构赋值]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`
};

export default basicInfo;
