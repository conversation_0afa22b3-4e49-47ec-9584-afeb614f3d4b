import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: `迭代器协议的历史反映了编程语言对统一遍历接口的长期追求。从早期的循环结构到现代的迭代器模式，体现了抽象层次的不断提升。`,
  
  background: `在迭代器协议出现之前，JavaScript的遍历方式不统一，不同类型的对象需要不同的遍历方法。这导致了代码的复杂性和不一致性。`,

  evolution: `迭代器协议的引入为JavaScript带来了统一的遍历标准，简化了集合操作，为生成器和异步迭代器奠定了基础。`,

  timeline: [
    {
      year: '2015',
      event: 'ES6迭代器协议标准化',
      description: 'ECMAScript 2015引入迭代器和for...of循环',
      significance: '为JavaScript提供了统一的遍历接口'
    },
    {
      year: '2018',
      event: '异步迭代器标准化',
      description: 'ES2018引入异步迭代器和for await...of',
      significance: '扩展了迭代器协议到异步场景'
    }
  ],

  keyFigures: [
    {
      name: 'TC39委员会',
      role: 'ECMAScript标准制定者',
      contribution: '设计和标准化迭代器协议',
      significance: '推动了JavaScript遍历机制的统一'
    }
  ],

  concepts: [
    {
      term: '迭代器模式',
      definition: '提供顺序访问集合元素的设计模式',
      evolution: '从设计模式发展为语言内置特性',
      modernRelevance: '现代JavaScript遍历的基础'
    }
  ],

  designPhilosophy: `迭代器协议体现了"协议优于实现"的设计哲学，通过定义标准接口而不是具体实现来实现统一。`,

  impact: `迭代器协议的标准化简化了JavaScript的集合操作，为现代框架和库的发展提供了基础。`,

  modernRelevance: `在现代JavaScript开发中，迭代器协议是理解生成器、异步编程和函数式编程的基础。`
};

export default knowledgeArchaeology;
