import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'data-processing-pipeline',
    title: '数据处理管道',
    description: '使用迭代器构建高效的数据处理管道，支持惰性求值和内存优化',
    businessValue: '处理大数据集时节省内存，提供可组合的数据转换操作',
    scenario: '需要处理大量数据，包括过滤、转换、聚合等操作，要求内存效率高且支持链式调用。',
    code: `// 数据处理管道
class DataPipeline {
  constructor(source) {
    this.source = source;
    this.operations = [];
  }
  
  // 过滤操作
  filter(predicate) {
    this.operations.push({
      type: 'filter',
      fn: predicate
    });
    return this;
  }
  
  // 映射操作
  map(transformer) {
    this.operations.push({
      type: 'map',
      fn: transformer
    });
    return this;
  }
  
  // 限制数量
  take(count) {
    this.operations.push({
      type: 'take',
      count: count
    });
    return this;
  }
  
  // 跳过元素
  skip(count) {
    this.operations.push({
      type: 'skip',
      count: count
    });
    return this;
  }
  
  // 实现迭代器协议
  [Symbol.iterator]() {
    const source = this.source;
    const operations = this.operations;
    
    return {
      sourceIterator: source[Symbol.iterator](),
      currentIndex: 0,
      takenCount: 0,
      skippedCount: 0,
      
      next() {
        while (true) {
          const result = this.sourceIterator.next();
          if (result.done) {
            return { done: true };
          }
          
          let value = result.value;
          let shouldYield = true;
          
          // 应用所有操作
          for (const operation of operations) {
            switch (operation.type) {
              case 'filter':
                if (!operation.fn(value, this.currentIndex)) {
                  shouldYield = false;
                }
                break;
                
              case 'map':
                if (shouldYield) {
                  value = operation.fn(value, this.currentIndex);
                }
                break;
                
              case 'skip':
                if (this.skippedCount < operation.count) {
                  this.skippedCount++;
                  shouldYield = false;
                }
                break;
                
              case 'take':
                if (this.takenCount >= operation.count) {
                  return { done: true };
                }
                break;
            }
            
            if (!shouldYield) break;
          }
          
          this.currentIndex++;
          
          if (shouldYield) {
            // 检查take限制
            const takeOp = operations.find(op => op.type === 'take');
            if (takeOp) {
              this.takenCount++;
            }
            return { value, done: false };
          }
        }
      }
    };
  }
  
  // 转换为数组
  toArray() {
    return [...this];
  }
  
  // 聚合操作
  reduce(reducer, initialValue) {
    let accumulator = initialValue;
    for (const item of this) {
      accumulator = reducer(accumulator, item);
    }
    return accumulator;
  }
}

// 使用示例
const data = Array.from({ length: 1000000 }, (_, i) => ({
  id: i,
  value: Math.random() * 100,
  category: i % 3 === 0 ? 'A' : i % 3 === 1 ? 'B' : 'C'
}));

const pipeline = new DataPipeline(data)
  .filter(item => item.category === 'A')
  .map(item => ({ ...item, doubled: item.value * 2 }))
  .skip(10)
  .take(100);

// 惰性求值 - 只处理需要的数据
const results = pipeline.toArray();
console.log('处理结果:', results.length);`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'pagination-system',
    title: '分页数据系统',
    description: '使用迭代器实现智能分页，支持无限滚动和按需加载',
    businessValue: '优化大数据集的加载性能，提供流畅的用户体验',
    scenario: '电商网站需要展示大量商品，支持无限滚动加载，同时要求性能优化和用户体验良好。',
    code: `// 分页数据迭代器
class PaginatedData {
  constructor(fetchFunction, pageSize = 20) {
    this.fetchFunction = fetchFunction;
    this.pageSize = pageSize;
    this.cache = new Map();
    this.totalCount = null;
  }
  
  // 异步获取页面数据
  async fetchPage(pageNumber) {
    if (this.cache.has(pageNumber)) {
      return this.cache.get(pageNumber);
    }
    
    const response = await this.fetchFunction(pageNumber, this.pageSize);
    this.cache.set(pageNumber, response.data);
    
    if (this.totalCount === null) {
      this.totalCount = response.total;
    }
    
    return response.data;
  }
  
  // 同步迭代器（预加载模式）
  [Symbol.iterator]() {
    let currentIndex = 0;
    let currentPage = 0;
    let currentPageData = [];
    let isLoading = false;
    
    return {
      next: () => {
        // 如果当前页数据已用完，加载下一页
        if (currentIndex >= currentPageData.length) {
          if (isLoading) {
            return { done: true }; // 避免重复加载
          }
          
          isLoading = true;
          // 注意：这里返回Promise，实际使用时需要异步迭代器
          return this.fetchPage(currentPage++).then(data => {
            currentPageData = data;
            currentIndex = 0;
            isLoading = false;
            
            if (data.length === 0) {
              return { done: true };
            }
            
            return { value: data[currentIndex++], done: false };
          });
        }
        
        return { value: currentPageData[currentIndex++], done: false };
      }
    };
  }
  
  // 异步迭代器
  [Symbol.asyncIterator]() {
    let currentIndex = 0;
    let currentPage = 0;
    let currentPageData = [];
    
    return {
      async next() {
        // 如果当前页数据已用完，加载下一页
        if (currentIndex >= currentPageData.length) {
          currentPageData = await this.fetchPage(currentPage++);
          currentIndex = 0;
          
          if (currentPageData.length === 0) {
            return { done: true };
          }
        }
        
        return { value: currentPageData[currentIndex++], done: false };
      }
    };
  }
  
  // 获取指定范围的数据
  async getRange(start, end) {
    const results = [];
    const startPage = Math.floor(start / this.pageSize);
    const endPage = Math.floor(end / this.pageSize);
    
    for (let page = startPage; page <= endPage; page++) {
      const pageData = await this.fetchPage(page);
      const pageStart = Math.max(0, start - page * this.pageSize);
      const pageEnd = Math.min(pageData.length, end - page * this.pageSize + 1);
      
      results.push(...pageData.slice(pageStart, pageEnd));
    }
    
    return results;
  }
}

// 模拟API调用
async function fetchProducts(page, pageSize) {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 100));
  
  const start = page * pageSize;
  const data = Array.from({ length: pageSize }, (_, i) => ({
    id: start + i,
    name: \`Product \${start + i}\`,
    price: Math.random() * 100
  }));
  
  return {
    data,
    total: 10000,
    page,
    pageSize
  };
}

// 使用示例
const paginatedProducts = new PaginatedData(fetchProducts, 50);

// 异步遍历
async function displayProducts() {
  let count = 0;
  for await (const product of paginatedProducts) {
    console.log(product.name, product.price);
    count++;
    
    // 只显示前100个产品
    if (count >= 100) break;
  }
}`
  }
];

export default businessScenarios;
