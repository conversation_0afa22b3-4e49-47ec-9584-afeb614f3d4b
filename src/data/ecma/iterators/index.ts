import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const iteratorsData: ApiItem = {
  id: 'iterators',
  title: 'ES6 Iterators',
  description: 'ES6迭代器协议，提供统一的遍历接口，支持for...of循环和自定义迭代行为',
  category: 'ECMA特性',
  difficulty: 'medium',
  
  syntax: `obj[Symbol.iterator](); for (const item of iterable) {}; const iterator = iterable[Symbol.iterator]();`,
  example: `const arr = [1, 2, 3]; for (const item of arr) { console.log(item); }`,
  notes: '迭代器协议是ES6引入的统一遍历标准，为for...of循环和解构赋值提供了基础',
  
  version: 'ES6 (ES2015)',
  tags: ['ES6', 'JavaScript', '迭代器', 'for...of', '协议'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default iteratorsData;
