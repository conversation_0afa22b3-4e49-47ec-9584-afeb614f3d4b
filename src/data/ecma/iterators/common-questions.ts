import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么迭代器是一次性的？',
    answer: '迭代器是一次性的是因为它们内部维护状态信息。一旦遍历完成，迭代器的内部状态就指向了结束位置，无法重置。这种设计简化了实现，避免了状态管理的复杂性。如果需要重复遍历，应该重新调用Symbol.iterator方法获取新的迭代器。',
    code: `const arr = [1, 2, 3];
const iterator = arr[Symbol.iterator]();

// 第一次遍历
console.log(iterator.next()); // { value: 1, done: false }
console.log(iterator.next()); // { value: 2, done: false }
console.log(iterator.next()); // { value: 3, done: false }
console.log(iterator.next()); // { done: true }

// 迭代器已耗尽，无法重复使用
console.log(iterator.next()); // { done: true }

// 需要重新获取迭代器
const newIterator = arr[Symbol.iterator]();
console.log(newIterator.next()); // { value: 1, done: false }

// 可重用的迭代器包装器
class ReusableIterator {
  constructor(iterable) {
    this.iterable = iterable;
  }
  
  [Symbol.iterator]() {
    return this.iterable[Symbol.iterator]();
  }
  
  // 每次调用都返回新的迭代器
  getIterator() {
    return this.iterable[Symbol.iterator]();
  }
}

const reusable = new ReusableIterator([1, 2, 3]);
for (const value of reusable) {
  console.log(value); // 第一次遍历
}
for (const value of reusable) {
  console.log(value); // 可以再次遍历
}`,
    tags: ['迭代器状态', '一次性使用', '重复遍历'],
    relatedQuestions: ['迭代器状态管理', '生成器重用']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '如何在for...of循环中获取索引？',
    answer: 'for...of循环本身不提供索引，但可以通过几种方式获取：1) 使用Array.prototype.entries()方法；2) 使用手动计数器；3) 使用Array.from()配合map；4) 自定义迭代器返回索引和值。',
    code: `const arr = ['a', 'b', 'c'];

// 方法1：使用entries()方法
for (const [index, value] of arr.entries()) {
  console.log(index, value); // 0 'a', 1 'b', 2 'c'
}

// 方法2：手动计数器
let index = 0;
for (const value of arr) {
  console.log(index++, value); // 0 'a', 1 'b', 2 'c'
}

// 方法3：使用Array.from()
for (const [index, value] of Array.from(arr.entries())) {
  console.log(index, value);
}

// 方法4：自定义带索引的迭代器
function* withIndex(iterable) {
  let index = 0;
  for (const value of iterable) {
    yield [index++, value];
  }
}

for (const [index, value] of withIndex(arr)) {
  console.log(index, value);
}

// 字符串也可以使用entries()
const str = 'hello';
for (const [index, char] of str.entries()) {
  console.log(index, char); // 0 'h', 1 'e', ...
}

// 对于其他可迭代对象
const set = new Set(['x', 'y', 'z']);
for (const [index, value] of Array.from(set.entries())) {
  console.log(index, value); // 注意：Set的entries()返回[value, value]
}

// 更通用的索引迭代器
class IndexedIterable {
  constructor(iterable) {
    this.iterable = iterable;
  }
  
  *[Symbol.iterator]() {
    let index = 0;
    for (const value of this.iterable) {
      yield { index: index++, value };
    }
  }
}

const indexed = new IndexedIterable(['a', 'b', 'c']);
for (const item of indexed) {
  console.log(item.index, item.value);
}`,
    tags: ['for...of索引', 'entries方法', '索引遍历'],
    relatedQuestions: ['数组遍历方法', 'entries vs keys vs values']
  }
];

export default commonQuestions;
