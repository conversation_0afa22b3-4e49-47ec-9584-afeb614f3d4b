import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: '迭代器使用中的常见错误和解决方案',
        sections: [
          {
            title: '迭代器协议错误',
            description: '迭代器实现和使用相关的常见问题',
            items: [
              {
                title: 'TypeError: object is not iterable',
                description: '尝试遍历不可迭代的对象',
                solution: '确保对象实现了Symbol.iterator方法',
                prevention: '检查对象是否为可迭代对象',
                code: `// ❌ 错误：普通对象不可迭代
const obj = { a: 1, b: 2 };
// for (const value of obj) {} // TypeError

// ✅ 解决方案1：使用Object.entries()
for (const [key, value] of Object.entries(obj)) {
  console.log(key, value);
}

// ✅ 解决方案2：实现迭代器
obj[Symbol.iterator] = function* () {
  for (const key of Object.keys(this)) {
    yield [key, this[key]];
  }
};

for (const [key, value] of obj) {
  console.log(key, value);
}

// ✅ 解决方案3：检查是否可迭代
function isIterable(obj) {
  return obj != null && typeof obj[Symbol.iterator] === 'function';
}

if (isIterable(obj)) {
  for (const value of obj) {
    console.log(value);
  }
}`
              },
              {
                title: '迭代器状态错误',
                description: '迭代器状态管理不当导致的问题',
                solution: '正确管理迭代器的内部状态',
                prevention: '理解迭代器的一次性特性',
                code: `// ❌ 错误：重复使用已耗尽的迭代器
const arr = [1, 2, 3];
const iterator = arr[Symbol.iterator]();

// 耗尽迭代器
while (!iterator.next().done) {}

// 尝试重新使用 - 不会有输出
for (const value of iterator) {
  console.log(value); // 不会执行
}

// ✅ 正确：重新获取迭代器
const newIterator = arr[Symbol.iterator]();
for (const value of newIterator) {
  console.log(value); // 正常输出
}

// ✅ 可重用的迭代器包装
class ReusableIterator {
  constructor(factory) {
    this.factory = factory;
  }
  
  [Symbol.iterator]() {
    return this.factory();
  }
}

const reusable = new ReusableIterator(() => [1, 2, 3][Symbol.iterator]());
for (const value of reusable) {
  console.log(value); // 第一次使用
}
for (const value of reusable) {
  console.log(value); // 可以再次使用
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '使用开发工具调试迭代器相关问题',
        sections: [
          {
            title: '迭代器状态调试',
            description: '调试迭代器的状态和行为',
            items: [
              {
                title: '迭代器状态跟踪',
                description: '跟踪迭代器的内部状态变化',
                solution: '添加调试信息和状态日志',
                prevention: '定期检查迭代器的状态',
                code: `// 调试迭代器状态
function createDebugIterator(iterable, name = 'Iterator') {
  return {
    [Symbol.iterator]() {
      const sourceIterator = iterable[Symbol.iterator]();
      let stepCount = 0;
      
      return {
        next() {
          const result = sourceIterator.next();
          stepCount++;
          
          console.log(\`\${name} Step \${stepCount}:\`, {
            value: result.value,
            done: result.done,
            hasValue: result.hasOwnProperty('value')
          });
          
          return result;
        }
      };
    }
  };
}

// 使用调试迭代器
const debugArray = createDebugIterator([1, 2, 3], 'ArrayIterator');
for (const value of debugArray) {
  console.log('Processing:', value);
}

// 自定义迭代器调试
function* debugGenerator() {
  console.log('Generator started');
  yield 1;
  console.log('After yield 1');
  yield 2;
  console.log('After yield 2');
  yield 3;
  console.log('Generator finished');
}

const debugGen = debugGenerator();
console.log('Created generator');
console.log(debugGen.next());
console.log(debugGen.next());
console.log(debugGen.next());
console.log(debugGen.next());`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
