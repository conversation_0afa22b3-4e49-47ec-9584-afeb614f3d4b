import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: '什么是迭代器协议？如何实现自定义迭代器？',
    answer: `迭代器协议包括两个部分：

**可迭代协议（Iterable Protocol）**：
- 对象必须实现Symbol.iterator方法
- 该方法返回一个迭代器对象
- 使对象可以被for...of遍历

**迭代器协议（Iterator Protocol）**：
- 迭代器对象必须实现next()方法
- next()返回{value, done}格式的对象
- done为true时表示迭代结束

**实现步骤**：
1. 在对象上定义Symbol.iterator方法
2. 该方法返回包含next()方法的对象
3. next()方法管理迭代状态并返回结果`,
   
    difficulty: 'medium',
    frequency: 'high',
    category: '协议实现',
    tags: ['迭代器协议', '自定义迭代器', 'Symbol.iterator'],
    
    code: `// 自定义迭代器示例
class NumberRange {
  constructor(start, end) {
    this.start = start;
    this.end = end;
  }
  
  // 实现可迭代协议
  [Symbol.iterator]() {
    let current = this.start;
    const end = this.end;
    
    // 返回迭代器对象
    return {
      // 实现迭代器协议
      next() {
        if (current <= end) {
          return { value: current++, done: false };
        }
        return { done: true };
      }
    };
  }
}

const range = new NumberRange(1, 5);

// 使用for...of遍历
for (const num of range) {
  console.log(num); // 1, 2, 3, 4, 5
}

// 手动迭代
const iterator = range[Symbol.iterator]();
console.log(iterator.next()); // { value: 1, done: false }
console.log(iterator.next()); // { value: 2, done: false }

// 转换为数组
const array = [...range]; // [1, 2, 3, 4, 5]

// 更复杂的迭代器 - 斐波那契数列
class Fibonacci {
  constructor(max) {
    this.max = max;
  }
  
  [Symbol.iterator]() {
    let prev = 0, curr = 1, count = 0;
    const max = this.max;
    
    return {
      next() {
        if (count < max) {
          const value = count === 0 ? 0 : count === 1 ? 1 : prev + curr;
          if (count >= 2) {
            [prev, curr] = [curr, prev + curr];
          }
          count++;
          return { value, done: false };
        }
        return { done: true };
      }
    };
  }
}

const fib = new Fibonacci(10);
console.log([...fib]); // [0, 1, 1, 2, 3, 5, 8, 13, 21, 34]`,
    
    followUp: [
      '迭代器和生成器的区别？',
      '如何实现可重用的迭代器？',
      '异步迭代器如何工作？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'for...of和for...in有什么区别？',
    answer: `for...of和for...in的主要区别：

**for...of循环**：
- 遍历可迭代对象的值
- 基于迭代器协议
- 适用于Array、String、Map、Set等
- 不能遍历普通对象（除非实现迭代器）

**for...in循环**：
- 遍历对象的可枚举属性名
- 基于对象的属性枚举
- 适用于所有对象
- 会遍历原型链上的属性

**使用建议**：
- 遍历数组值用for...of
- 遍历对象属性用for...in
- 需要索引时用传统for循环或forEach`,
   
    difficulty: 'easy',
    frequency: 'high',
    category: '循环对比',
    tags: ['for...of', 'for...in', '循环', '遍历'],
    
    code: `const arr = ['a', 'b', 'c'];
const obj = { x: 1, y: 2, z: 3 };

// for...of - 遍历值
for (const value of arr) {
  console.log(value); // 'a', 'b', 'c'
}

// for...in - 遍历属性名/索引
for (const key in arr) {
  console.log(key); // '0', '1', '2' (字符串类型)
}

for (const key in obj) {
  console.log(key, obj[key]); // 'x' 1, 'y' 2, 'z' 3
}

// 字符串遍历
const str = 'hello';
for (const char of str) {
  console.log(char); // 'h', 'e', 'l', 'l', 'o'
}

// Map遍历
const map = new Map([['a', 1], ['b', 2]]);
for (const [key, value] of map) {
  console.log(key, value); // 'a' 1, 'b' 2
}

// Set遍历
const set = new Set([1, 2, 3]);
for (const value of set) {
  console.log(value); // 1, 2, 3
}

// 普通对象不能用for...of（除非实现迭代器）
// for (const value of obj) {} // TypeError

// 让普通对象支持for...of
Object.prototype[Symbol.iterator] = function* () {
  for (const key of Object.keys(this)) {
    yield [key, this[key]];
  }
};

for (const [key, value] of obj) {
  console.log(key, value); // 'x' 1, 'y' 2, 'z' 3
}`,
    
    followUp: [
      '如何获取for...of循环的索引？',
      'forEach和for...of的性能对比？',
      '如何中断for...of循环？'
    ]
  }
];

export default interviewQuestions;
