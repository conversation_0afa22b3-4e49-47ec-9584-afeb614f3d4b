import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: `async/await的历史可以追溯到异步编程模式的演进过程，从回调函数到Promise，再到async/await，体现了JavaScript异步编程的不断完善。`,
  
  background: `在async/await出现之前，JavaScript异步编程主要依赖回调函数和Promise。回调函数容易导致回调地狱，Promise虽然解决了这个问题，但链式调用在复杂场景下仍然不够直观。`,

  evolution: `async/await的引入标志着JavaScript异步编程进入了新的阶段，提供了接近同步代码的异步编程体验，大大提高了代码的可读性和维护性。`,

  timeline: [
    {
      year: '2009',
      event: 'Node.js发布',
      description: '推广了基于回调的异步编程模式',
      significance: '确立了JavaScript异步编程的重要性'
    },
    {
      year: '2015',
      event: 'Promise标准化',
      description: 'ES6正式引入Promise，提供了更好的异步编程方案',
      significance: '为async/await的出现奠定了基础'
    },
    {
      year: '2017',
      event: 'async/await发布',
      description: 'ES2017引入async/await，提供语法糖简化异步编程',
      significance: '彻底改变了JavaScript异步编程的写法'
    },
    {
      year: '2022',
      event: '顶层await',
      description: 'ES2022引入顶层await，允许在模块顶层使用await',
      significance: '进一步简化了模块级别的异步操作'
    }
  ],

  keyFigures: [
    {
      name: 'TC39委员会',
      role: 'ECMAScript标准制定者',
      contribution: '设计和标准化async/await语法',
      significance: '确保了async/await与JavaScript生态系统的良好集成'
    }
  ],

  concepts: [
    {
      term: '异步编程',
      definition: '允许程序在等待操作完成时继续执行其他任务的编程模式',
      evolution: '从回调函数发展到Promise，再到async/await',
      modernRelevance: '现代JavaScript开发的核心概念，特别是在Web和Node.js开发中'
    }
  ],

  designPhilosophy: `async/await的设计体现了"简单性与强大性并存"的哲学，通过语法糖的方式让复杂的异步操作变得简单直观。`,

  impact: `async/await的引入不仅改变了JavaScript的异步编程方式，也影响了其他编程语言的异步编程设计。`,

  modernRelevance: `在现代JavaScript开发中，async/await已经成为异步编程的首选方案，广泛应用于前端和后端开发。`
};

export default knowledgeArchaeology;
