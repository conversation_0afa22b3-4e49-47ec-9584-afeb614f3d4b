import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'api-data-fetching',
    title: 'API数据获取和处理',
    description: '在现代Web应用中使用async/await进行API数据获取、处理和错误管理',
    businessValue: '提高数据获取的可靠性和用户体验，简化异步数据流的管理',
    scenario: '电商应用需要获取商品信息、用户数据、库存状态等多个API数据，并进行统一处理和错误管理。',
    code: `// 电商数据获取管理器
class ECommerceDataManager {
  constructor(apiClient) {
    this.api = apiClient;
    this.cache = new Map();
  }

  async getProductDetails(productId) {
    try {
      // 检查缓存
      if (this.cache.has(productId)) {
        return this.cache.get(productId);
      }

      // 并行获取商品基本信息和详细信息
      const [product, inventory, reviews] = await Promise.all([
        this.api.get(\`/products/\${productId}\`),
        this.api.get(\`/products/\${productId}/inventory\`),
        this.api.get(\`/products/\${productId}/reviews\`)
      ]);

      const productDetails = {
        ...product.data,
        inventory: inventory.data,
        reviews: reviews.data,
        lastUpdated: new Date().toISOString()
      };

      // 缓存结果
      this.cache.set(productId, productDetails);
      
      return productDetails;
    } catch (error) {
      console.error(\`Failed to fetch product \${productId}:\`, error);
      throw new Error(\`Product data unavailable: \${error.message}\`);
    }
  }

  async processOrder(orderData) {
    try {
      // 验证库存
      const inventory = await this.checkInventory(orderData.items);
      if (!inventory.available) {
        throw new Error('Insufficient inventory');
      }

      // 计算价格
      const pricing = await this.calculatePricing(orderData);
      
      // 创建订单
      const order = await this.api.post('/orders', {
        ...orderData,
        pricing,
        timestamp: new Date().toISOString()
      });

      // 更新库存
      await this.updateInventory(orderData.items);

      return order.data;
    } catch (error) {
      await this.handleOrderError(error, orderData);
      throw error;
    }
  }
}`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'file-processing',
    title: '文件上传和处理',
    description: '使用async/await处理文件上传、转换和存储的复杂异步流程',
    businessValue: '提供可靠的文件处理服务，支持大文件上传和多格式转换',
    scenario: '内容管理系统需要处理用户上传的图片、视频等文件，进行格式转换、压缩和存储。',
    code: `// 文件处理服务
class FileProcessingService {
  constructor(storage, converter) {
    this.storage = storage;
    this.converter = converter;
  }

  async uploadAndProcess(file, options = {}) {
    const uploadId = this.generateUploadId();
    
    try {
      // 验证文件
      await this.validateFile(file);
      
      // 上传原始文件
      const originalUrl = await this.storage.upload(file, {
        path: \`uploads/original/\${uploadId}\`,
        metadata: {
          originalName: file.name,
          size: file.size,
          type: file.type
        }
      });

      // 并行处理多种格式
      const processingTasks = [];
      
      if (options.generateThumbnail) {
        processingTasks.push(
          this.generateThumbnail(file, uploadId)
        );
      }
      
      if (options.compress) {
        processingTasks.push(
          this.compressFile(file, uploadId)
        );
      }
      
      if (options.convertFormats) {
        processingTasks.push(
          ...options.convertFormats.map(format =>
            this.convertFormat(file, format, uploadId)
          )
        );
      }

      const processedFiles = await Promise.allSettled(processingTasks);
      
      return {
        uploadId,
        originalUrl,
        processedFiles: processedFiles
          .filter(result => result.status === 'fulfilled')
          .map(result => result.value),
        errors: processedFiles
          .filter(result => result.status === 'rejected')
          .map(result => result.reason)
      };
    } catch (error) {
      await this.cleanup(uploadId);
      throw new Error(\`File processing failed: \${error.message}\`);
    }
  }
}`
  }
];

export default businessScenarios;
