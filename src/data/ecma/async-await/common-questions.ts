import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么不能在顶层代码中直接使用await？',
    answer: 'await只能在async函数内部使用，这是因为await需要一个异步上下文来管理暂停和恢复执行。在ES2022中引入了顶层await，允许在模块的顶层使用await，但仍然需要在模块环境中。在普通脚本中，需要将代码包装在async函数中或使用IIFE。',
    code: `// ❌ 错误：顶层await（在普通脚本中）
const data = await fetch('/api/data'); // SyntaxError

// ✅ 正确：包装在async函数中
async function main() {
  const data = await fetch('/api/data');
  console.log(data);
}
main();

// ✅ 正确：使用IIFE
(async () => {
  const data = await fetch('/api/data');
  console.log(data);
})();

// ✅ ES2022顶层await（在模块中）
// main.mjs
const data = await fetch('/api/data');
export { data };`,
    tags: ['顶层await', '模块', 'IIFE'],
    relatedQuestions: ['ES2022新特性', '模块系统']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: 'async函数总是返回Promise吗？',
    answer: '是的，async函数总是返回Promise，即使函数体中返回的是非Promise值。如果返回值不是Promise，JavaScript会自动将其包装为resolved Promise。如果函数抛出异常，会返回rejected Promise。这种设计确保了async函数的一致性和可预测性。',
    code: `async function example1() {
  return 'hello'; // 返回 Promise.resolve('hello')
}

async function example2() {
  return Promise.resolve('world'); // 返回 Promise.resolve('world')
}

async function example3() {
  throw new Error('error'); // 返回 Promise.reject(Error('error'))
}

// 验证返回值
console.log(example1()); // Promise {<fulfilled>: 'hello'}
console.log(example2()); // Promise {<fulfilled>: 'world'}
console.log(example3()); // Promise {<rejected>: Error: error}

// 使用返回值
example1().then(value => console.log(value)); // 'hello'
example3().catch(error => console.log(error.message)); // 'error'`,
    tags: ['Promise', '返回值', '异常处理'],
    relatedQuestions: ['Promise包装机制', '异步函数设计']
  }
];

export default commonQuestions;
