import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'async/await与Promise有什么区别？什么时候使用哪种方式？',
    answer: `async/await和Promise的主要区别：

**语法差异**：
- Promise使用链式调用(.then/.catch)
- async/await使用同步风格的语法

**错误处理**：
- Promise使用.catch()或第二个参数
- async/await使用try/catch

**可读性**：
- async/await更接近同步代码，更易读
- Promise链在复杂逻辑中可能形成回调地狱

**使用场景**：
- 简单的异步操作：两者都可以
- 复杂的异步流程：async/await更适合
- 需要并行执行：Promise.all()更直观
- 函数式编程风格：Promise链更合适`,
   
    difficulty: 'medium',
    frequency: 'high',
    category: '基础概念',
    tags: ['async/await', 'Promise', '异步编程'],
    
    code: `// Promise方式
function fetchUserData(id) {
  return fetch(\`/api/users/\${id}\`)
    .then(response => response.json())
    .then(user => {
      return fetch(\`/api/users/\${user.id}/posts\`);
    })
    .then(response => response.json())
    .catch(error => {
      console.error('Error:', error);
      throw error;
    });
}

// async/await方式
async function fetchUserData(id) {
  try {
    const response = await fetch(\`/api/users/\${id}\`);
    const user = await response.json();
    
    const postsResponse = await fetch(\`/api/users/\${user.id}/posts\`);
    const posts = await postsResponse.json();
    
    return posts;
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
}`,
    
    followUp: [
      'async/await的性能与Promise相比如何？',
      '如何在async/await中实现并行执行？',
      '什么是顶层await？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '在循环中使用async/await有什么注意事项？',
    answer: `在循环中使用async/await需要注意执行顺序和性能：

**forEach问题**：
forEach不会等待async函数完成，会并行执行所有异步操作

**串行执行**：
使用for...of或传统for循环实现串行执行

**并行执行**：
使用Promise.all()或map()实现并行执行

**性能考虑**：
- 串行执行：适合有依赖关系的操作
- 并行执行：适合独立的操作，性能更好`,
   
    difficulty: 'medium',
    frequency: 'high',
    category: '常见陷阱',
    tags: ['循环', 'forEach', '并行执行'],
    
    code: `const urls = ['/api/data1', '/api/data2', '/api/data3'];

// ❌ 错误：forEach不会等待
urls.forEach(async (url) => {
  const data = await fetch(url);
  console.log(data); // 执行顺序不确定
});

// ✅ 串行执行
for (const url of urls) {
  const data = await fetch(url);
  console.log(data); // 按顺序执行
}

// ✅ 并行执行
const promises = urls.map(url => fetch(url));
const results = await Promise.all(promises);
results.forEach(data => console.log(data));

// ✅ 并行执行（另一种方式）
const results2 = await Promise.all(
  urls.map(async (url) => {
    const response = await fetch(url);
    return response.json();
  })
);`,
    
    followUp: [
      '如何处理循环中的错误？',
      'Promise.allSettled()与Promise.all()的区别？',
      '如何限制并发数量？'
    ]
  }
];

export default interviewQuestions;
