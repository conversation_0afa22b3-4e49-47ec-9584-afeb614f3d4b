import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: 'async/await使用中的常见错误和解决方案',
        sections: [
          {
            title: '忘记使用await',
            description: '忘记在Promise前使用await导致返回Promise对象而不是实际值',
            items: [
              {
                title: '返回Promise而不是值',
                description: '函数返回Promise对象而不是期望的数据',
                solution: '确保在异步操作前添加await关键字',
                prevention: '使用TypeScript进行类型检查，及时发现类型错误',
                code: `// ❌ 错误：忘记await
async function getUserName(id) {
  const user = fetchUser(id); // 返回Promise
  return user.name; // undefined，因为user是Promise
}

// ✅ 正确：使用await
async function getUserName(id) {
  const user = await fetchUser(id); // 等待Promise解决
  return user.name; // 返回实际的name值
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '使用开发工具调试async/await相关问题',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '利用浏览器工具调试异步代码',
            items: [
              {
                title: '断点调试',
                description: '在async函数中设置断点调试异步流程',
                solution: '在Sources面板中设置断点，观察异步执行流程',
                prevention: '定期使用调试工具验证异步逻辑',
                code: `async function debugExample() {
  console.log('开始执行');
  debugger; // 设置断点
  
  const data = await fetchData();
  debugger; // 观察data的值
  
  const processed = await processData(data);
  debugger; // 观察processed的值
  
  return processed;
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
