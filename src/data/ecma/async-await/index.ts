import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const asyncAwaitData: ApiItem = {
  id: 'async-await',
  title: 'async/await',
  description: 'ES2017异步编程语法糖，基于Promise提供更直观的异步代码编写方式',
  category: 'ECMA特性',
  difficulty: 'medium',
  
  syntax: `async function name() { const result = await promise; return result; }`,
  example: `async function fetchUser(id) { const response = await fetch(\`/api/users/\${id}\`); return await response.json(); }`,
  notes: 'async函数返回Promise，await只能在async函数内使用，提供同步风格的异步编程',
  
  version: 'ES2017 (ES8)',
  tags: ['ES2017', 'JavaScript', '异步编程', 'Promise'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default asyncAwaitData;
