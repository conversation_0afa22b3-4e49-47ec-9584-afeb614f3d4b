import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `async/await是ES2017引入的异步编程语法糖，基于Promise构建。async关键字用于声明异步函数，该函数总是返回Promise。await关键字用于等待Promise解决，只能在async函数内部使用。这种语法让异步代码看起来像同步代码，大大提高了可读性和维护性。`,

  syntax: `// async函数声明
async function functionName() {
  const result = await promise;
  return result;
}

// async箭头函数
const asyncArrow = async () => {
  try {
    const data = await fetchData();
    return data;
  } catch (error) {
    console.error(error);
  }
};

// async方法
class MyClass {
  async method() {
    return await this.getData();
  }
}`,

  quickExample: `async function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchUserData() {
      try {
        setLoading(true);
        
        // 并行获取用户信息和权限
        const [userResponse, permissionsResponse] = await Promise.all([
          fetch(\`/api/users/\${userId}\`),
          fetch(\`/api/users/\${userId}/permissions\`)
        ]);

        if (!userResponse.ok || !permissionsResponse.ok) {
          throw new Error('Failed to fetch user data');
        }

        const userData = await userResponse.json();
        const permissions = await permissionsResponse.json();

        setUser({ ...userData, permissions });
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchUserData();
  }, [userId]);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div>
      <h1>{user.name}</h1>
      <p>Email: {user.email}</p>
      <p>Permissions: {user.permissions.join(', ')}</p>
    </div>
  );
}`,

  coreFeatures: [
    {
      feature: "async函数",
      description: "声明异步函数，自动返回Promise",
      importance: "high" as const,
      details: "async函数内部可以使用await，返回值自动包装为Promise"
    },
    {
      feature: "await表达式",
      description: "等待Promise解决，暂停函数执行",
      importance: "high" as const,
      details: "只能在async函数内使用，让异步代码看起来像同步代码"
    },
    {
      feature: "错误处理",
      description: "使用try/catch处理异步错误",
      importance: "high" as const,
      details: "Promise rejection会转换为可捕获的异常"
    },
    {
      feature: "并行执行",
      description: "使用Promise.all实现并行异步操作",
      importance: "medium" as const,
      details: "避免不必要的串行等待，提高性能"
    }
  ],

  keyFeatures: [
    {
      feature: "语法简洁性",
      description: "提供类似同步代码的异步编程体验",
      importance: "high" as const,
      details: "消除回调地狱和复杂的Promise链"
    },
    {
      feature: "错误处理统一",
      description: "使用标准的try/catch处理异步错误",
      importance: "high" as const,
      details: "比Promise的.catch()更直观和一致"
    },
    {
      feature: "调试友好",
      description: "支持断点调试和堆栈跟踪",
      importance: "medium" as const,
      details: "比Promise链更容易调试和理解执行流程"
    },
    {
      feature: "组合性强",
      description: "可以轻松组合多个异步操作",
      importance: "medium" as const,
      details: "支持条件执行、循环和复杂的控制流"
    }
  ],

  limitations: [
    "await只能在async函数内使用，不能在顶层代码中直接使用",
    "async函数总是返回Promise，即使返回值不是Promise",
    "过度使用await可能导致不必要的串行执行，影响性能",
    "错误处理需要正确使用try/catch，否则可能导致未捕获的Promise rejection",
    "在某些情况下，Promise链可能比async/await更适合"
  ],

  bestPractices: [
    "优先使用async/await而不是Promise链，提高代码可读性",
    "合理使用Promise.all()实现并行执行，避免不必要的串行等待",
    "始终使用try/catch处理可能的异步错误",
    "在循环中谨慎使用await，考虑使用Promise.all()或for...of",
    "为async函数提供清晰的错误处理和返回值类型"
  ],

  warnings: [
    "在forEach中使用async/await不会按预期工作，应使用for...of或Promise.all",
    "忘记await会导致函数返回Promise而不是实际值",
    "在条件语句中使用await时要注意执行顺序"
  ],

  scenarioDiagram: `graph TD
    A[async/await使用场景] --> B[API数据获取]
    A --> C[文件操作]
    A --> D[数据库操作]
    A --> E[并行处理]

    B --> B1[用户信息获取]
    B --> B2[第三方API集成]
    B --> B3[RESTful服务调用]
    B --> B4[GraphQL查询]

    C --> C1[文件读写]
    C --> C2[图片上传]
    C --> C3[批量处理]

    D --> D1[数据查询]
    D --> D2[事务处理]
    D --> D3[批量操作]

    E --> E1[Promise.all并行]
    E --> E2[Promise.allSettled容错]
    E --> E3[Promise.race竞速]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`
};

export default basicInfo;
