import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'const、let和var的区别是什么？',
    answer: `const、let和var的主要区别：

1. **作用域**：
   - var：函数作用域
   - let/const：块级作用域

2. **变量提升**：
   - var：提升并初始化为undefined
   - let/const：提升但不初始化（暂时性死区）

3. **重复声明**：
   - var：允许重复声明
   - let/const：不允许重复声明

4. **重新赋值**：
   - var/let：可以重新赋值
   - const：不可重新赋值

5. **初始化**：
   - var/let：可以先声明后赋值
   - const：必须在声明时初始化`,
   
    difficulty: 'medium',
    frequency: 'high',
    category: '基础概念',
    tags: ['作用域', '变量声明', '暂时性死区'],
    
    code: `// var的函数作用域
function varExample() {
  if (true) {
    var x = 1;
  }
  console.log(x); // 1，可以访问
}

// let/const的块级作用域
function letConstExample() {
  if (true) {
    let y = 1;
    const z = 2;
  }
  console.log(y); // ReferenceError
  console.log(z); // ReferenceError
}

// 暂时性死区
console.log(a); // undefined（var提升）
console.log(b); // ReferenceError（TDZ）
var a = 1;
let b = 2;`,
    
    followUp: [
      '什么是暂时性死区？',
      '为什么const声明的对象内容可以修改？',
      '在循环中使用let和var有什么区别？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '什么是暂时性死区（Temporal Dead Zone）？',
    answer: `暂时性死区是指从代码块开始到变量声明语句执行之前的区域，在这个区域内访问变量会抛出ReferenceError。

特点：
1. 只影响let和const声明的变量
2. 变量已经被"绑定"但未初始化
3. 防止在声明前访问变量
4. 提高代码安全性和可预测性

TDZ的存在是为了：
- 防止变量提升导致的意外行为
- 强制开发者先声明后使用
- 提供更清晰的错误信息`,
   
    difficulty: 'medium',
    frequency: 'medium',
    category: '高级概念',
    tags: ['暂时性死区', 'TDZ', '变量提升'],
    
    code: `// 暂时性死区示例
function tdz() {
  // TDZ开始
  console.log(x); // ReferenceError: Cannot access 'x' before initialization
  console.log(y); // ReferenceError: Cannot access 'y' before initialization
  
  let x = 1; // TDZ结束，x可用
  const y = 2; // TDZ结束，y可用
  
  console.log(x); // 1
  console.log(y); // 2
}

// typeof在TDZ中的行为
console.log(typeof undeclaredVar); // "undefined"
console.log(typeof declaredLater); // ReferenceError
let declaredLater = 'value';`,
    
    followUp: [
      'typeof操作符在TDZ中的行为如何？',
      'TDZ如何影响函数参数的默认值？',
      '为什么var没有TDZ？'
    ]
  }
];

export default interviewQuestions;
