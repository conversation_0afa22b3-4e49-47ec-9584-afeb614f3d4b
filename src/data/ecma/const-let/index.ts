import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const constLetData: ApiItem = {
  id: 'const-let',
  title: 'const/let 声明',
  description: 'ES6块级作用域变量声明，提供更安全的变量管理和暂时性死区特性',
  category: 'ECMA特性',
  difficulty: 'medium',
  
  syntax: `const name = value; let variable = value; { /* 块级作用域 */ }`,
  example: `const PI = 3.14159; let count = 0; if (true) { let blockScoped = 'local'; }`,
  notes: 'const声明常量，let声明变量，都具有块级作用域和暂时性死区',
  
  version: 'ES6 (ES2015)',
  tags: ['ES6', 'JavaScript', '变量声明', '块级作用域'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default constLetData;
