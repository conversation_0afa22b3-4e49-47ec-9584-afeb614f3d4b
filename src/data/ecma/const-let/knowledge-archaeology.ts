import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: `const和let的引入标志着JavaScript变量声明机制的重大革新，解决了var带来的作用域混乱和变量提升问题。`,
  
  background: `在ES6之前，JavaScript只有var和function两种声明方式，var的函数作用域和变量提升特性经常导致意外的行为和难以调试的问题。`,

  evolution: `从var到const/let的演进体现了JavaScript从"宽松"向"严格"的转变，提供了更安全、更可预测的变量管理机制。`,

  timeline: [
    {
      year: '1995',
      event: 'JavaScript诞生',
      description: '只有var声明，函数作用域和变量提升成为语言特性',
      significance: '奠定了JavaScript变量系统的基础，但也埋下了作用域问题的隐患'
    },
    {
      year: '2015',
      event: 'ES6发布',
      description: '引入let和const，提供块级作用域和更严格的变量管理',
      significance: '彻底改变了JavaScript的变量声明方式，提高了代码安全性'
    }
  ],

  keyFigures: [
    {
      name: 'Brendan Eich',
      role: 'JavaScript创造者',
      contribution: '设计了原始的var声明机制',
      significance: '虽然var有问题，但为后续改进提供了经验教训'
    }
  ],

  concepts: [
    {
      term: '块级作用域',
      definition: '变量只在声明的代码块内有效',
      evolution: '从函数作用域发展为更精确的块级作用域',
      modernRelevance: '现代JavaScript开发的标准实践，提供更好的变量管理'
    }
  ],

  designPhilosophy: `const和let的设计体现了"明确性优于便利性"的哲学，通过更严格的规则来防止常见错误。`,

  impact: `const和let的引入不仅改变了变量声明方式，更推动了整个JavaScript生态向更安全、更可维护的方向发展。`,

  modernRelevance: `在现代JavaScript开发中，const和let已经完全取代了var，成为变量声明的标准方式。`
};

export default knowledgeArchaeology;
