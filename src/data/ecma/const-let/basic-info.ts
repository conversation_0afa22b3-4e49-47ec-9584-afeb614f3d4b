import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `const和let是ES6引入的块级作用域变量声明关键字。const用于声明常量（值不可重新赋值），let用于声明变量（值可以重新赋值）。它们都具有块级作用域、暂时性死区等特性，是现代JavaScript中推荐的变量声明方式。`,

  syntax: `// const声明常量
const CONSTANT_NAME = value;

// let声明变量  
let variableName = value;

// 块级作用域
{
  const blockConst = 'block scoped';
  let blockLet = 'also block scoped';
}

// 暂时性死区
console.log(x); // ReferenceError
let x = 5;`,

  quickExample: `// const声明常量
const PI = 3.14159;
const users = ['Alice', 'Bob'];
const config = { theme: 'dark', lang: 'zh' };

console.log(PI); // 3.14159
// PI = 3.14; // TypeError: Assignment to constant variable.

// let声明变量
let count = 0;
let message = 'Hello';

count = 10; // 可以重新赋值
message = 'World';
console.log(count, message); // 10 "World"

// 块级作用域演示
if (true) {
  const blockConst = 'block scoped';
  let blockLet = 'also block scoped';
  var functionScoped = 'function scoped';

  console.log(blockConst); // "block scoped"
  console.log(blockLet); // "also block scoped"
}

// console.log(blockConst); // ReferenceError: blockConst is not defined
// console.log(blockLet); // ReferenceError: blockLet is not defined
console.log(functionScoped); // "function scoped" (var是函数作用域)

// 循环中的块级作用域
console.log('=== let在循环中 ===');
for (let i = 0; i < 3; i++) {
  setTimeout(() => console.log('let:', i), 100); // 输出 0, 1, 2
}

console.log('=== var在循环中 ===');
for (var j = 0; j < 3; j++) {
  setTimeout(() => console.log('var:', j), 100); // 输出 3, 3, 3
}

// 暂时性死区演示
function temporalDeadZoneExample() {
  console.log('开始函数执行');

  // console.log(x); // ReferenceError: Cannot access 'x' before initialization
  // console.log(y); // ReferenceError: Cannot access 'y' before initialization

  let x = 10;
  const y = 20;

  console.log(x, y); // 10 20
}

temporalDeadZoneExample();

// const对象的可变性
const person = { name: 'John', age: 30 };
person.age = 31; // 可以修改对象属性
person.city = 'New York'; // 可以添加属性
console.log(person); // { name: 'John', age: 31, city: 'New York' }

// person = {}; // TypeError: Assignment to constant variable.

// const数组的可变性
const numbers = [1, 2, 3];
numbers.push(4); // 可以修改数组内容
numbers[0] = 0; // 可以修改元素
console.log(numbers); // [0, 2, 3, 4]

// numbers = []; // TypeError: Assignment to constant variable.

// 最佳实践示例
function bestPracticeExample() {
  // 优先使用const
  const API_URL = 'https://api.example.com';
  const MAX_RETRIES = 3;

  // 需要重新赋值时使用let
  let currentRetry = 0;
  let result = null;

  // 避免使用var
  // var shouldNotUse = 'avoid var';

  while (currentRetry < MAX_RETRIES) {
    try {
      // 模拟API调用
      result = 'API call attempt ' + (currentRetry + 1);
      break;
    } catch (error) {
      currentRetry++;
    }
  }

  return result;
}

console.log(bestPracticeExample());`,

  coreFeatures: [
    {
      feature: "块级作用域",
      description: "const和let声明的变量只在声明的块内有效",
      importance: "high" as const,
      details: "与var的函数作用域不同，提供更精确的作用域控制"
    },
    {
      feature: "暂时性死区",
      description: "在声明之前访问变量会抛出ReferenceError",
      importance: "high" as const,
      details: "防止变量提升导致的意外行为，提高代码安全性"
    },
    {
      feature: "不可重复声明",
      description: "在同一作用域内不能重复声明同名变量",
      importance: "medium" as const,
      details: "避免意外覆盖变量，减少命名冲突"
    },
    {
      feature: "const常量特性",
      description: "const声明的变量不能重新赋值",
      importance: "high" as const,
      details: "对于对象和数组，内容可以修改但引用不能改变"
    }
  ],

  keyFeatures: [
    {
      feature: "块级作用域",
      description: "变量只在声明的代码块内有效，提供更精确的作用域控制",
      importance: "high" as const,
      details: "解决了var函数作用域带来的问题，如循环变量泄露"
    },
    {
      feature: "暂时性死区",
      description: "在变量声明之前访问会抛出错误，防止变量提升问题",
      importance: "high" as const,
      details: "提高代码安全性，避免undefined的意外使用"
    },
    {
      feature: "不可重复声明",
      description: "同一作用域内不能重复声明同名变量",
      importance: "medium" as const,
      details: "防止意外覆盖，提高代码可维护性"
    },
    {
      feature: "const不可变性",
      description: "const声明的变量不能重新赋值",
      importance: "high" as const,
      details: "对于引用类型，引用不可变但内容可变"
    }
  ],

  limitations: [
    "const声明时必须初始化，不能先声明后赋值",
    "const声明的对象和数组内容仍然可以修改",
    "在循环中使用const需要注意重新绑定的问题",
    "暂时性死区可能导致一些反直觉的错误",
    "不支持变量提升，必须先声明后使用"
  ],

  bestPractices: [
    "优先使用const，只有需要重新赋值时才使用let",
    "避免使用var，统一使用const/let进行变量声明",
    "在循环中正确使用let来避免闭包问题",
    "利用块级作用域来限制变量的生命周期",
    "使用const声明对象时，考虑使用Object.freeze()实现深度不可变"
  ],

  warnings: [
    "const声明的对象和数组内容仍然可以修改，不是真正的不可变",
    "暂时性死区会在声明之前抛出ReferenceError，而不是undefined",
    "在for循环中使用const可能导致TypeError"
  ],

  scenarioDiagram: `graph TD
    A[const/let使用场景] --> B[变量声明选择]
    A --> C[作用域控制]
    A --> D[循环处理]
    A --> E[模块开发]

    B --> B1[const: 常量值]
    B --> B2[const: 对象/数组引用]
    B --> B3[let: 可变变量]
    B --> B4[避免var的问题]

    C --> C1[块级作用域隔离]
    C --> C2[暂时性死区保护]
    C --> C3[避免变量提升]

    D --> D1[for循环计数器]
    D --> D2[forEach回调]
    D --> D3[闭包变量捕获]

    E --> E1[模块级常量]
    E --> E2[配置对象]
    E --> E3[API端点定义]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`
};

export default basicInfo;
