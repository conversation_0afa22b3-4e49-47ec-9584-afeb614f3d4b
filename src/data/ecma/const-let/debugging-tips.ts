import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: 'const和let使用中的常见错误和解决方案',
        sections: [
          {
            title: '暂时性死区错误',
            description: '在声明前访问变量导致的ReferenceError',
            items: [
              {
                title: 'ReferenceError: Cannot access before initialization',
                description: '在变量声明前访问变量',
                solution: '确保在声明后再访问变量',
                prevention: '使用ESLint规则检测此类问题',
                code: `// ❌ 错误：暂时性死区
console.log(x); // ReferenceError
let x = 5;

// ✅ 正确：先声明后使用
let y = 5;
console.log(y); // 5`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '使用开发工具调试const/let相关问题',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '利用浏览器工具调试作用域问题',
            items: [
              {
                title: '作用域面板',
                description: '查看当前作用域中的变量',
                solution: '在Sources面板中查看Scope部分',
                prevention: '定期检查变量作用域',
                code: `// 在断点处查看作用域
function example() {
  const outer = 'outer';
  {
    const inner = 'inner';
    debugger; // 在此处查看作用域
  }
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
