import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么const声明的对象还能修改属性？',
    answer: 'const只保护变量的引用不被重新赋值，但不保护引用指向的对象内容。对于对象和数组，const确保变量始终指向同一个对象，但对象的属性和数组的元素仍然可以修改。如果需要真正的不可变对象，可以使用Object.freeze()。',
    code: `const obj = { name: 'Alice', age: 25 };
obj.age = 26; // ✅ 可以修改属性
obj.city = 'Beijing'; // ✅ 可以添加属性
// obj = {}; // ❌ TypeError: Assignment to constant variable

// 使用Object.freeze()创建不可变对象
const frozenObj = Object.freeze({ name: 'Bob' });
frozenObj.name = 'Charlie'; // 静默失败（严格模式下抛出错误）
console.log(frozenObj.name); // 'Bob'`,
    tags: ['const', '对象', '不可变性'],
    relatedQuestions: ['Object.freeze()的作用', '深度冻结对象']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '在for循环中使用let和var有什么区别？',
    answer: '在for循环中，let会为每次迭代创建新的变量绑定，而var在整个循环中共享同一个变量。这在异步操作中尤其重要，使用let可以正确捕获每次迭代的值，而var会导致所有异步操作都使用最后一次迭代的值。',
    code: `// 使用var的问题
for (var i = 0; i < 3; i++) {
  setTimeout(() => console.log('var:', i), 100); // 输出: 3, 3, 3
}

// 使用let的正确行为
for (let i = 0; i < 3; i++) {
  setTimeout(() => console.log('let:', i), 100); // 输出: 0, 1, 2
}

// 原理：let为每次迭代创建新绑定
for (let i = 0; i < 3; i++) {
  // 每次迭代都有独立的i变量
  console.log('当前i:', i);
}`,
    tags: ['循环', '闭包', '异步'],
    relatedQuestions: ['闭包原理', '异步编程最佳实践']
  }
];

export default commonQuestions;
