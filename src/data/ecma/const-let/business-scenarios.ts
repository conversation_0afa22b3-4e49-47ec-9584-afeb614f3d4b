import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'config-management',
    title: '配置管理系统',
    description: '在应用配置管理中使用const声明不可变配置，let声明可变状态',
    businessValue: '提高配置安全性，防止意外修改关键配置参数，同时保持状态管理的灵活性',
    scenario: '企业级应用需要管理各种配置参数，包括API端点、功能开关、主题设置等。使用const/let可以明确区分不可变配置和可变状态。',
    code: `// 应用配置管理
const APP_CONFIG = {
  API_BASE_URL: 'https://api.example.com',
  VERSION: '1.0.0',
  FEATURES: {
    DARK_MODE: true,
    ANALYTICS: true
  }
};

// 运行时状态
let currentTheme = 'light';
let userPreferences = {};
let isLoading = false;

class ConfigManager {
  constructor() {
    // 配置常量
    const DEFAULT_TIMEOUT = 5000;
    const MAX_RETRIES = 3;
    
    // 可变状态
    let retryCount = 0;
    let lastError = null;
    
    this.config = Object.freeze(APP_CONFIG);
    this.state = { currentTheme, userPreferences, isLoading };
  }
  
  updateTheme(theme) {
    if (this.config.FEATURES.DARK_MODE) {
      currentTheme = theme;
      this.state.currentTheme = theme;
    }
  }
  
  async loadUserPreferences(userId) {
    isLoading = true;
    
    try {
      const response = await fetch(\`\${this.config.API_BASE_URL}/users/\${userId}/preferences\`);
      userPreferences = await response.json();
      this.state.userPreferences = userPreferences;
    } catch (error) {
      console.error('Failed to load preferences:', error);
    } finally {
      isLoading = false;
      this.state.isLoading = false;
    }
  }
}`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'event-loop-management',
    title: '事件循环和异步处理',
    description: '在异步编程中正确使用const/let来管理变量作用域和避免闭包陷阱',
    businessValue: '避免异步操作中的变量污染和内存泄漏，提高代码可靠性和性能',
    scenario: '在处理大量异步操作时，需要确保每个操作都有独立的作用域，避免变量共享导致的问题。',
    code: `// 异步任务管理器
class AsyncTaskManager {
  constructor() {
    const MAX_CONCURRENT = 5;
    const TIMEOUT_MS = 10000;
    
    let activeTasks = 0;
    let taskQueue = [];
    let results = new Map();
    
    this.processQueue = async () => {
      while (taskQueue.length > 0 && activeTasks < MAX_CONCURRENT) {
        const task = taskQueue.shift();
        activeTasks++;
        
        // 使用块级作用域确保每个任务独立
        {
          const taskId = task.id;
          const startTime = Date.now();
          let timeoutId = null;
          
          try {
            // 设置超时
            const timeoutPromise = new Promise((_, reject) => {
              timeoutId = setTimeout(() => {
                reject(new Error(\`Task \${taskId} timeout\`));
              }, TIMEOUT_MS);
            });
            
            // 执行任务
            const result = await Promise.race([
              task.execute(),
              timeoutPromise
            ]);
            
            clearTimeout(timeoutId);
            results.set(taskId, {
              success: true,
              result,
              duration: Date.now() - startTime
            });
            
          } catch (error) {
            clearTimeout(timeoutId);
            results.set(taskId, {
              success: false,
              error: error.message,
              duration: Date.now() - startTime
            });
          } finally {
            activeTasks--;
            // 继续处理队列
            this.processQueue();
          }
        }
      }
    };
  }
  
  addTask(task) {
    const taskId = \`task_\${Date.now()}_\${Math.random()}\`;
    taskQueue.push({ ...task, id: taskId });
    this.processQueue();
    return taskId;
  }
}`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'data-validation',
    title: '数据验证和类型安全',
    description: '使用const/let结合类型检查实现安全的数据验证系统',
    businessValue: '提高数据处理的安全性和可靠性，减少运行时错误和数据污染',
    scenario: '在处理用户输入和API数据时，需要严格的验证机制来确保数据的完整性和安全性。',
    code: `// 数据验证系统
class DataValidator {
  constructor() {
    // 验证规则常量
    const VALIDATION_RULES = {
      email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,
      phone: /^\\+?[1-9]\\d{1,14}$/,
      password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/
    };
    
    const ERROR_MESSAGES = {
      required: 'This field is required',
      invalid: 'Invalid format',
      tooShort: 'Value is too short',
      tooLong: 'Value is too long'
    };
    
    this.validate = (data, schema) => {
      let errors = {};
      let isValid = true;
      
      for (const field in schema) {
        const rules = schema[field];
        const value = data[field];
        let fieldErrors = [];
        
        // 块级作用域处理每个字段
        {
          const fieldValue = value;
          let hasError = false;
          
          // 必填验证
          if (rules.required && (!fieldValue || fieldValue.trim() === '')) {
            fieldErrors.push(ERROR_MESSAGES.required);
            hasError = true;
          }
          
          // 格式验证
          if (!hasError && fieldValue && rules.pattern) {
            const pattern = VALIDATION_RULES[rules.pattern] || rules.pattern;
            if (!pattern.test(fieldValue)) {
              fieldErrors.push(ERROR_MESSAGES.invalid);
              hasError = true;
            }
          }
          
          // 长度验证
          if (!hasError && fieldValue) {
            if (rules.minLength && fieldValue.length < rules.minLength) {
              fieldErrors.push(ERROR_MESSAGES.tooShort);
              hasError = true;
            }
            if (rules.maxLength && fieldValue.length > rules.maxLength) {
              fieldErrors.push(ERROR_MESSAGES.tooLong);
              hasError = true;
            }
          }
          
          if (hasError) {
            errors[field] = fieldErrors;
            isValid = false;
          }
        }
      }
      
      return { isValid, errors };
    };
  }
}`
  }
];

export default businessScenarios;
