import { Implementation } from '@/types/api';

const implementation: Implementation = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  mechanism: `可选链操作符的实现基于短路求值机制。JavaScript引擎在解析时会将可选链表达式转换为条件检查，当遇到null或undefined时立即停止求值并返回undefined。

核心实现原理：

1. **语法转换**
   - obj?.prop 转换为 obj == null ? undefined : obj.prop
   - obj?.method?.() 转换为 obj?.method == null ? undefined : obj.method()
   - 支持链式调用的递归转换

2. **短路求值**
   - 从左到右逐步检查每个环节
   - 遇到null或undefined立即返回undefined
   - 避免后续不必要的计算

3. **类型检查**
   - 只检查null和undefined
   - 不检查其他falsy值（0, '', false等）
   - 保持JavaScript的类型语义

4. **性能优化**
   - 编译时优化，减少运行时开销
   - 避免重复的null检查
   - 支持引擎级别的优化`,

  visualization: `graph TD
    A[obj?.prop?.method] --> B{obj == null?}
    B -->|Yes| C[Return undefined]
    B -->|No| D{obj.prop == null?}
    D -->|Yes| C
    D -->|No| E{obj.prop.method == null?}
    E -->|Yes| C
    E -->|No| F[Execute obj.prop.method]
    F --> G[Return Result]
    
    style A fill:#e1f5fe
    style C fill:#ffebee
    style G fill:#e8f5e8`,
    
  plainExplanation: `简单来说，可选链就像是一个"安全检查员"。

想象一下：
- 传统方式就像是在黑暗中摸索，每一步都可能撞墙
- 可选链就像是有一个检查员在前面探路
- 如果前面有障碍（null/undefined），检查员就会说"停下，这里过不去"
- 如果路是通的，检查员就会说"继续前进"

这样你就不会因为撞墙而受伤（抛出错误），而是安全地知道"这条路走不通"。`,

  designConsiderations: [
    '短路求值 - 提高性能，避免不必要的计算',
    '类型安全 - 只处理null和undefined，保持语义一致性',
    '语法简洁 - 大幅减少条件检查代码',
    '向后兼容 - 不影响现有代码，可渐进式采用',
    '调试友好 - 保持清晰的执行路径'
  ],
  
  relatedConcepts: [
    '短路求值：逻辑运算符的求值策略',
    '空值合并：与可选链配合的默认值处理',
    '类型守卫：TypeScript中的类型安全检查',
    '防御性编程：预防性的错误处理策略',
    '语法糖：简化常见操作的语法特性'
  ]
};

export default implementation;
