import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `可选链操作符（?.）是ES2020引入的语法特性，允许安全地访问嵌套对象属性，而不会因为中间值为null或undefined而抛出错误。当链中的任何部分为null或undefined时，整个表达式会短路并返回undefined，而不是抛出TypeError。这大大简化了深层属性访问的代码，提高了代码的健壮性。`,

  syntax: `// 属性访问
obj?.prop
obj?.['prop']

// 方法调用
obj?.method?.()
obj?.method?.(args)

// 数组/对象动态访问
obj?.[propName]
obj?.[++counter]
obj?.[func()]

// 嵌套链式访问
obj?.a?.b?.c?.d

// 函数调用
func?.()
obj?.method?.()

// 与其他操作符结合
obj?.prop ?? 'default'
obj?.method?.() || fallbackFunction()`,

  quickExample: `// 传统方式 - 繁琐且容易出错
function getUserCity(user) {
  if (user && user.address && user.address.city) {
    return user.address.city;
  }
  return undefined;
}

// 使用可选链 - 简洁安全
function getUserCityOptional(user) {
  return user?.address?.city;
}

// 复杂对象访问
const response = {
  data: {
    users: [
      { id: 1, profile: { name: 'Alice', settings: { theme: 'dark' } } },
      { id: 2, profile: { name: 'Bob' } }
    ]
  }
};

// 安全访问深层属性
const firstUserTheme = response?.data?.users?.[0]?.profile?.settings?.theme;
console.log(firstUserTheme); // 'dark'

const secondUserTheme = response?.data?.users?.[1]?.profile?.settings?.theme;
console.log(secondUserTheme); // undefined (不会报错)

// 方法调用
const api = {
  user: {
    getName: () => 'John',
    getProfile: () => ({ age: 30 })
  }
};

console.log(api?.user?.getName?.()); // 'John'
console.log(api?.user?.getAge?.()); // undefined (方法不存在)
console.log(api?.admin?.getName?.()); // undefined (对象不存在)

// 动态属性访问
const propName = 'address';
const user = { address: { street: '123 Main St' } };
console.log(user?.[propName]?.street); // '123 Main St'

// 数组访问
const users = [
  { name: 'Alice' },
  null,
  { name: 'Charlie' }
];

console.log(users?.[0]?.name); // 'Alice'
console.log(users?.[1]?.name); // undefined
console.log(users?.[5]?.name); // undefined

// 函数调用
const callbacks = {
  onSuccess: (data) => console.log('Success:', data),
  onError: null
};

callbacks?.onSuccess?.('Data loaded'); // 调用成功
callbacks?.onError?.('Error occurred'); // 不会调用，不会报错

// 与解构结合
const { name, email } = user?.profile ?? {};
console.log(name, email);

// 与空值合并结合
const theme = user?.settings?.theme ?? 'light';
const notifications = user?.preferences?.notifications ?? true;`,

  coreFeatures: [
    {
      feature: "安全属性访问",
      description: "访问可能不存在的属性时不会抛出错误",
      importance: "high" as const,
      details: "当中间值为null或undefined时返回undefined而不是抛出TypeError"
    },
    {
      feature: "短路求值",
      description: "遇到null或undefined时立即停止求值",
      importance: "high" as const,
      details: "提高性能，避免不必要的计算"
    },
    {
      feature: "方法调用安全",
      description: "安全地调用可能不存在的方法",
      importance: "high" as const,
      details: "obj?.method?.()确保方法存在时才调用"
    },
    {
      feature: "动态属性访问",
      description: "支持计算属性名的安全访问",
      importance: "medium" as const,
      details: "obj?.[propName]支持变量和表达式作为属性名"
    }
  ],

  keyFeatures: [
    {
      feature: "语法简洁",
      description: "大大简化了深层属性访问的代码",
      importance: "high" as const,
      details: "减少了大量的条件检查代码"
    },
    {
      feature: "类型安全",
      description: "在TypeScript中提供更好的类型推断",
      importance: "medium" as const,
      details: "帮助TypeScript更准确地推断类型"
    },
    {
      feature: "向后兼容",
      description: "不影响现有代码，可以渐进式采用",
      importance: "medium" as const,
      details: "可以与传统的条件检查混合使用"
    }
  ],

  limitations: [
    "只能用于null和undefined检查，不能检查其他falsy值",
    "过度使用可能掩盖数据结构设计问题",
    "在某些情况下可能影响性能（虽然通常很小）",
    "需要较新的JavaScript环境支持",
    "调试时可能难以确定具体哪一层返回了undefined"
  ],

  bestPractices: [
    "优先使用可选链而不是手动的null检查",
    "与空值合并操作符(??)结合使用提供默认值",
    "避免过长的可选链，考虑数据结构重构",
    "在TypeScript中结合使用以获得更好的类型安全",
    "谨慎使用在性能敏感的代码中"
  ],

  warnings: [
    "可选链只检查null和undefined，不检查其他falsy值",
    "过度使用可能掩盖API设计问题",
    "在赋值操作中不能使用可选链的左侧"
  ],

  scenarioDiagram: `graph TD
    A[可选链使用场景] --> B[API响应处理]
    A --> C[深层对象访问]
    A --> D[方法调用安全]
    A --> E[配置对象处理]

    B --> B1[用户数据获取]
    B --> B2[嵌套JSON解析]
    B --> B3[第三方API集成]
    B --> B4[错误处理简化]

    C --> C1[用户配置访问]
    C --> C2[DOM元素属性]
    C --> C3[状态管理]

    D --> D1[回调函数调用]
    D --> D2[插件方法执行]
    D --> D3[事件处理器]

    E --> E1[主题设置]
    E --> E2[功能开关]
    E --> E3[环境变量]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`
};

export default basicInfo;
