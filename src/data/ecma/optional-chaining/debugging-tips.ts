import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: '可选链使用中的常见错误和解决方案',
        sections: [
          {
            title: '语法使用错误',
            description: '可选链语法相关的常见问题',
            items: [
              {
                title: 'SyntaxError: Invalid left-hand side in assignment',
                description: '尝试在赋值操作左侧使用可选链',
                solution: '可选链只能用于读取操作，不能用于赋值',
                prevention: '理解可选链的使用限制',
                code: `// ❌ 错误：在赋值左侧使用可选链
const obj = {};
// obj?.prop = 'value'; // SyntaxError

// ✅ 正确：先检查再赋值
if (obj) {
  obj.prop = 'value';
}

// ✅ 正确：确保对象存在
obj.prop = obj.prop || {};
obj.prop.value = 'data';`
              },
              {
                title: '过度依赖可选链',
                description: '在所有地方都使用可选链，掩盖了数据结构问题',
                solution: '合理使用可选链，考虑数据结构设计',
                prevention: '分析数据流，确保必要的属性存在',
                code: `// ❌ 过度使用：掩盖了设计问题
function processUser(user) {
  const name = user?.profile?.name ?? 'Unknown';
  const email = user?.contact?.email ?? '';
  const phone = user?.contact?.phone ?? '';
  // ... 大量的可选链
}

// ✅ 更好：明确数据契约
function processUser(user) {
  // 验证必要的数据结构
  if (!user || !user.profile) {
    throw new Error('Invalid user data: missing profile');
  }
  
  const name = user.profile.name || 'Unknown';
  const email = user.contact?.email ?? '';
  const phone = user.contact?.phone ?? '';
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-tools',
      title: '🔧 调试工具',
      content: {
        introduction: '调试可选链相关问题的工具和技巧',
        sections: [
          {
            title: '可选链调试技巧',
            description: '如何有效调试可选链的执行过程',
            items: [
              {
                title: '分步调试可选链',
                description: '将长的可选链分解为多步来调试',
                solution: '逐步检查每一层的值',
                prevention: '在复杂链中添加中间变量',
                code: `// 难以调试的长链
const result = data?.response?.users?.[0]?.profile?.settings?.theme;

// 分步调试
console.log('data:', data);
const response = data?.response;
console.log('response:', response);

const users = response?.users;
console.log('users:', users);

const firstUser = users?.[0];
console.log('firstUser:', firstUser);

const profile = firstUser?.profile;
console.log('profile:', profile);

const settings = profile?.settings;
console.log('settings:', settings);

const theme = settings?.theme;
console.log('theme:', theme);

// 调试辅助函数
function debugChain(obj, path, label = 'chain') {
  const steps = path.split('.');
  let current = obj;
  
  console.group(\`Debugging \${label}: \${path}\`);
  console.log('Starting object:', obj);
  
  for (let i = 0; i < steps.length; i++) {
    const step = steps[i];
    const previousValue = current;
    current = current?.[step];
    
    console.log(\`Step \${i + 1} (\${step}):\`, {
      from: previousValue,
      to: current,
      exists: current !== undefined
    });
    
    if (current === undefined || current === null) {
      console.warn(\`Chain broken at step \${i + 1}: \${step}\`);
      break;
    }
  }
  
  console.log('Final result:', current);
  console.groupEnd();
  
  return current;
}

// 使用调试函数
const theme = debugChain(data, 'response.users.0.profile.settings.theme', 'theme-access');`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
