import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: '可选链操作符与传统的null检查有什么区别？',
    answer: `可选链操作符与传统null检查的主要区别：

**语法简洁性**：
- 传统方式需要多层条件检查
- 可选链一个操作符解决所有检查

**性能差异**：
- 传统方式可能重复访问相同属性
- 可选链优化了访问路径

**可读性**：
- 传统方式嵌套条件难以阅读
- 可选链保持线性的代码结构

**错误处理**：
- 传统方式需要手动处理每一层
- 可选链自动处理所有层级`,
   
    difficulty: 'easy',
    frequency: 'high',
    category: '基础对比',
    tags: ['可选链', 'null检查', '语法对比'],
    
    code: `// 传统null检查方式
function getUserCityTraditional(user) {
  if (user && user.address && user.address.city) {
    return user.address.city;
  }
  return undefined;
}

// 或者使用逻辑运算符
function getUserCityLogical(user) {
  return user && user.address && user.address.city;
}

// 使用可选链
function getUserCityOptional(user) {
  return user?.address?.city;
}

// 复杂嵌套的对比
const data = {
  response: {
    users: [
      { profile: { settings: { theme: 'dark' } } },
      { profile: { name: 'John' } }
    ]
  }
};

// 传统方式 - 繁琐
function getFirstUserThemeTraditional(data) {
  if (data && 
      data.response && 
      data.response.users && 
      data.response.users[0] && 
      data.response.users[0].profile && 
      data.response.users[0].profile.settings) {
    return data.response.users[0].profile.settings.theme;
  }
  return undefined;
}

// 可选链 - 简洁
function getFirstUserThemeOptional(data) {
  return data?.response?.users?.[0]?.profile?.settings?.theme;
}

// 方法调用的对比
const api = {
  user: {
    getName: () => 'John'
  }
};

// 传统方式
function callMethodTraditional(api) {
  if (api && api.user && typeof api.user.getName === 'function') {
    return api.user.getName();
  }
  return undefined;
}

// 可选链
function callMethodOptional(api) {
  return api?.user?.getName?.();
}

// 性能对比示例
const largeObject = {
  level1: {
    level2: {
      level3: {
        level4: {
          value: 'target'
        }
      }
    }
  }
};

// 传统方式可能多次访问
console.time('traditional');
for (let i = 0; i < 1000000; i++) {
  const result = largeObject && 
                 largeObject.level1 && 
                 largeObject.level1.level2 && 
                 largeObject.level1.level2.level3 && 
                 largeObject.level1.level2.level3.level4 && 
                 largeObject.level1.level2.level3.level4.value;
}
console.timeEnd('traditional');

// 可选链优化访问
console.time('optional-chaining');
for (let i = 0; i < 1000000; i++) {
  const result = largeObject?.level1?.level2?.level3?.level4?.value;
}
console.timeEnd('optional-chaining');`,
    
    followUp: [
      '可选链的性能影响？',
      '与空值合并操作符的配合使用？',
      '在TypeScript中的类型推断？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '可选链操作符只检查null和undefined吗？其他falsy值呢？',
    answer: `是的，可选链操作符只检查null和undefined，不检查其他falsy值。

**检查的值**：
- null → 返回undefined
- undefined → 返回undefined

**不检查的falsy值**：
- 0 → 继续执行
- '' (空字符串) → 继续执行  
- false → 继续执行
- NaN → 继续执行

**设计原因**：
- 保持JavaScript的类型语义一致性
- 避免意外的短路行为
- 只处理真正的"不存在"情况`,
   
    difficulty: 'medium',
    frequency: 'medium',
    category: '行为细节',
    tags: ['可选链', 'falsy值', '类型检查'],
    
    code: `// 可选链只检查null和undefined
const testObj = {
  zero: 0,
  emptyString: '',
  falseBool: false,
  nanValue: NaN,
  nullValue: null,
  undefinedValue: undefined
};

// 这些falsy值不会被短路
console.log(testObj?.zero?.toString); // undefined (0没有toString属性)
console.log(testObj?.emptyString?.length); // 0 (空字符串有length属性)
console.log(testObj?.falseBool?.toString); // undefined (false没有toString属性)
console.log(testObj?.nanValue?.toString); // undefined (NaN没有toString属性)

// 只有null和undefined会被短路
console.log(testObj?.nullValue?.anything); // undefined (短路)
console.log(testObj?.undefinedValue?.anything); // undefined (短路)

// 实际应用示例
const user = {
  name: '',  // 空字符串
  age: 0,    // 数字0
  isActive: false,  // false
  profile: null     // null
};

// 空字符串不会短路
console.log(user?.name?.length); // 0 (空字符串的长度)
console.log(user?.name?.toUpperCase?.()); // '' (空字符串转大写)

// 数字0不会短路
console.log(user?.age?.toString?.()); // '0'

// false不会短路
console.log(user?.isActive?.toString?.()); // 'false'

// null会短路
console.log(user?.profile?.name); // undefined

// 如果需要检查其他falsy值，需要额外处理
function safeAccess(obj, path) {
  const value = obj?.[path];
  // 如果需要检查所有falsy值
  return value ? value : undefined;
}

// 或者使用逻辑运算符
const safeName = user?.name || 'Default Name';
const safeAge = user?.age ?? 0; // 使用空值合并，只处理null/undefined

// 常见误区示例
const data = {
  items: [], // 空数组
  count: 0   // 数字0
};

// 空数组不会短路（数组是对象，是truthy）
console.log(data?.items?.length); // 0
console.log(data?.items?.[0]); // undefined (数组为空)

// 数字0不会短路
console.log(data?.count?.toString?.()); // '0'

// 正确的空数组检查
const firstItem = data?.items?.length > 0 ? data.items[0] : undefined;

// 或者结合其他操作符
const hasItems = (data?.items?.length ?? 0) > 0;`,
    
    followUp: [
      '如何同时检查falsy值？',
      '与逻辑运算符的组合使用？',
      '空值合并操作符的配合？'
    ]
  }
];

export default interviewQuestions;
