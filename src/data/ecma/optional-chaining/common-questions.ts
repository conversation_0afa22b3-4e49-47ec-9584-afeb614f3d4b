import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '可选链可以用在赋值操作的左侧吗？',
    answer: '不可以。可选链操作符不能用在赋值操作的左侧，因为它可能返回undefined，而undefined不能被赋值。可选链只能用于读取操作，不能用于写入操作。如果需要安全的赋值，应该先检查对象是否存在。',
    code: `// ❌ 错误：不能在赋值左侧使用可选链
const obj = {};
// obj?.prop = 'value'; // SyntaxError

// ❌ 错误：不能在赋值左侧使用可选链
const user = { profile: {} };
// user?.profile?.name = 'John'; // SyntaxError

// ✅ 正确：先检查再赋值
if (user && user.profile) {
  user.profile.name = 'John';
}

// ✅ 正确：使用传统的null检查
if (user?.profile) {
  user.profile.name = 'John';
}

// ✅ 正确：确保对象存在
user.profile = user.profile || {};
user.profile.name = 'John';

// ✅ 正确：使用空值合并创建默认结构
const profile = user?.profile ?? {};
profile.name = 'John';
user.profile = profile;

// 实用的安全赋值函数
function safeSet(obj, path, value) {
  const keys = path.split('.');
  let current = obj;
  
  // 确保路径上的所有对象都存在
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  // 设置最终值
  current[keys[keys.length - 1]] = value;
}

// 使用安全赋值函数
const data = {};
safeSet(data, 'user.profile.name', 'John');
console.log(data); // { user: { profile: { name: 'John' } } }`,
    tags: ['可选链限制', '赋值操作', '语法错误'],
    relatedQuestions: ['安全赋值', '对象属性设置']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '可选链与空值合并操作符如何配合使用？',
    answer: '可选链与空值合并操作符(??)是完美的搭配。可选链负责安全访问，空值合并负责提供默认值。这种组合可以创建非常健壮的代码，既避免了错误，又确保了有意义的默认值。',
    code: `// 可选链 + 空值合并的经典组合
const user = {
  profile: {
    name: 'John',
    settings: {
      theme: null,
      language: undefined
    }
  }
};

// 提供默认值
const theme = user?.profile?.settings?.theme ?? 'light';
const language = user?.profile?.settings?.language ?? 'en';
const avatar = user?.profile?.avatar ?? '/default-avatar.png';

console.log(theme);    // 'light'
console.log(language); // 'en'
console.log(avatar);   // '/default-avatar.png'

// 复杂的默认值处理
const config = {
  api: {
    timeout: 0,  // 注意：0是有效值
    retries: null
  }
};

// 正确处理0值（0不会被??替换）
const timeout = config?.api?.timeout ?? 5000;
const retries = config?.api?.retries ?? 3;

console.log(timeout); // 0 (保持原值)
console.log(retries); // 3 (使用默认值)

// 对比逻辑或操作符
const timeoutOr = config?.api?.timeout || 5000;  // 会把0替换为5000
const retriesOr = config?.api?.retries || 3;     // 会把null替换为3

console.log(timeoutOr); // 5000 (0被错误替换)
console.log(retriesOr); // 3 (正确替换null)

// 实际应用：API响应处理
function processApiResponse(response) {
  return {
    id: response?.data?.id ?? 'unknown',
    name: response?.data?.name ?? 'Unnamed',
    email: response?.data?.email ?? '',
    
    // 嵌套对象的默认值
    address: {
      street: response?.data?.address?.street ?? '',
      city: response?.data?.address?.city ?? '',
      country: response?.data?.address?.country ?? 'Unknown'
    },
    
    // 数组的默认值
    tags: response?.data?.tags ?? [],
    
    // 数字的默认值（注意0的处理）
    age: response?.data?.age ?? 0,
    score: response?.data?.score ?? 0,
    
    // 布尔值的默认值
    isActive: response?.data?.isActive ?? false,
    
    // 复杂计算的默认值
    displayName: response?.data?.profile?.displayName ?? 
                 response?.data?.name ?? 
                 'Anonymous User'
  };
}

// 函数式编程风格
const getNestedValue = (obj, path, defaultValue) => {
  return path.split('.').reduce((current, key) => current?.[key], obj) ?? defaultValue;
};

// 使用示例
const data = { user: { profile: { name: 'John' } } };
const name = getNestedValue(data, 'user.profile.name', 'Unknown');
const age = getNestedValue(data, 'user.profile.age', 0);

console.log(name); // 'John'
console.log(age);  // 0`,
    tags: ['可选链', '空值合并', '默认值', '组合使用'],
    relatedQuestions: ['空值合并操作符', '默认值处理', '逻辑运算符']
  }
];

export default commonQuestions;
