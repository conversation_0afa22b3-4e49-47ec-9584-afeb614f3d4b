import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const optionalChainingData: ApiItem = {
  id: 'optional-chaining',
  title: 'Optional Chaining (?.)',
  description: 'ES2020可选链操作符，提供安全的属性访问，避免因null/undefined导致的错误',
  category: 'ECMA特性',
  difficulty: 'easy',
  
  syntax: `obj?.prop; obj?.[propName]; obj?.method?.(); func?.();`,
  example: `const user = { profile: { name: '<PERSON>' } }; console.log(user?.profile?.name); // 'John'`,
  notes: '可选链操作符是ES2020引入的语法糖，简化了深层属性访问的安全检查',
  
  version: 'ES2020',
  tags: ['ES2020', 'JavaScript', '可选链', '安全访问', '语法糖'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default optionalChainingData;
