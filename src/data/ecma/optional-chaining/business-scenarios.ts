import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'api-response-handling',
    title: 'API响应数据处理',
    description: '使用可选链安全处理复杂的API响应数据，避免因数据结构变化导致的错误',
    businessValue: '提高应用稳定性，减少因API数据结构变化导致的崩溃，改善用户体验',
    scenario: '电商应用需要处理来自多个API的复杂数据结构，包括用户信息、商品详情、订单状态等，数据结构可能不完整或发生变化。',
    code: `// API响应处理器
class ApiResponseHandler {
  // 处理用户信息
  processUserData(apiResponse) {
    const user = apiResponse?.data?.user;
    
    return {
      id: user?.id,
      name: user?.profile?.name ?? 'Unknown User',
      email: user?.contact?.email,
      avatar: user?.profile?.avatar?.url ?? '/default-avatar.png',
      
      // 地址信息
      address: {
        street: user?.address?.street,
        city: user?.address?.city,
        country: user?.address?.country ?? 'Unknown',
        zipCode: user?.address?.zipCode
      },
      
      // 偏好设置
      preferences: {
        theme: user?.settings?.ui?.theme ?? 'light',
        language: user?.settings?.locale?.language ?? 'en',
        notifications: {
          email: user?.settings?.notifications?.email ?? true,
          push: user?.settings?.notifications?.push ?? false,
          sms: user?.settings?.notifications?.sms ?? false
        }
      },
      
      // 订阅信息
      subscription: {
        plan: user?.subscription?.plan?.name,
        status: user?.subscription?.status,
        expiresAt: user?.subscription?.expiresAt,
        features: user?.subscription?.plan?.features ?? []
      }
    };
  }
  
  // 处理商品列表
  processProductList(apiResponse) {
    const products = apiResponse?.data?.products ?? [];
    
    return products.map(product => ({
      id: product?.id,
      name: product?.name ?? 'Unnamed Product',
      price: {
        current: product?.pricing?.current ?? 0,
        original: product?.pricing?.original,
        currency: product?.pricing?.currency ?? 'USD',
        discount: product?.pricing?.discount?.percentage
      },
      
      // 图片信息
      images: {
        thumbnail: product?.images?.[0]?.thumbnail ?? '/placeholder.jpg',
        gallery: product?.images?.map(img => img?.url).filter(Boolean) ?? []
      },
      
      // 库存信息
      inventory: {
        inStock: product?.inventory?.quantity > 0,
        quantity: product?.inventory?.quantity ?? 0,
        warehouse: product?.inventory?.warehouse?.name
      },
      
      // 评分信息
      rating: {
        average: product?.reviews?.rating?.average ?? 0,
        count: product?.reviews?.count ?? 0,
        distribution: product?.reviews?.rating?.distribution ?? {}
      },
      
      // 分类信息
      category: {
        id: product?.category?.id,
        name: product?.category?.name,
        path: product?.category?.breadcrumb?.map(c => c?.name).filter(Boolean) ?? []
      }
    }));
  }
  
  // 处理订单详情
  processOrderDetails(apiResponse) {
    const order = apiResponse?.data?.order;
    
    return {
      id: order?.id,
      status: order?.status ?? 'unknown',
      
      // 时间信息
      dates: {
        created: order?.createdAt,
        updated: order?.updatedAt,
        shipped: order?.shipping?.shippedAt,
        delivered: order?.shipping?.deliveredAt,
        estimated: order?.shipping?.estimatedDelivery
      },
      
      // 商品信息
      items: order?.items?.map(item => ({
        productId: item?.product?.id,
        name: item?.product?.name,
        quantity: item?.quantity ?? 1,
        price: item?.price ?? 0,
        image: item?.product?.image?.thumbnail,
        variant: {
          size: item?.variant?.size,
          color: item?.variant?.color,
          style: item?.variant?.style
        }
      })) ?? [],
      
      // 价格信息
      pricing: {
        subtotal: order?.pricing?.subtotal ?? 0,
        tax: order?.pricing?.tax ?? 0,
        shipping: order?.pricing?.shipping ?? 0,
        discount: order?.pricing?.discount ?? 0,
        total: order?.pricing?.total ?? 0,
        currency: order?.pricing?.currency ?? 'USD'
      },
      
      // 配送信息
      shipping: {
        method: order?.shipping?.method?.name,
        carrier: order?.shipping?.carrier?.name,
        trackingNumber: order?.shipping?.trackingNumber,
        address: {
          name: order?.shipping?.address?.recipientName,
          street: order?.shipping?.address?.street,
          city: order?.shipping?.address?.city,
          state: order?.shipping?.address?.state,
          zipCode: order?.shipping?.address?.zipCode,
          country: order?.shipping?.address?.country
        }
      },
      
      // 支付信息
      payment: {
        method: order?.payment?.method?.type,
        status: order?.payment?.status,
        transactionId: order?.payment?.transactionId,
        lastFour: order?.payment?.card?.lastFour,
        brand: order?.payment?.card?.brand
      }
    };
  }
  
  // 处理搜索结果
  processSearchResults(apiResponse) {
    const results = apiResponse?.data?.results;
    
    return {
      query: apiResponse?.query,
      totalCount: results?.total ?? 0,
      page: results?.pagination?.current ?? 1,
      totalPages: results?.pagination?.total ?? 1,
      
      // 商品结果
      products: results?.products?.map(product => ({
        id: product?.id,
        name: product?.name,
        price: product?.price?.current,
        image: product?.images?.[0]?.thumbnail,
        rating: product?.rating?.average,
        inStock: product?.inventory?.inStock ?? false
      })) ?? [],
      
      // 分类建议
      categories: results?.suggestions?.categories?.map(cat => ({
        id: cat?.id,
        name: cat?.name,
        count: cat?.productCount ?? 0
      })) ?? [],
      
      // 品牌建议
      brands: results?.suggestions?.brands?.map(brand => ({
        id: brand?.id,
        name: brand?.name,
        count: brand?.productCount ?? 0
      })) ?? [],
      
      // 搜索建议
      suggestions: results?.suggestions?.queries ?? []
    };
  }
}`
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'form-validation',
    title: '表单数据验证',
    description: '使用可选链安全访问表单数据和配置，构建健壮的表单验证系统',
    businessValue: '提高表单处理的可靠性，减少因数据结构问题导致的验证失败',
    scenario: '企业应用需要处理复杂的表单数据，包括嵌套对象、动态字段、条件验证等，需要安全地访问各种配置和数据。',
    code: `// 表单验证器
class FormValidator {
  constructor(config) {
    this.config = config;
    this.errors = {};
  }
  
  // 验证表单数据
  validate(formData) {
    this.errors = {};
    
    // 遍历所有字段配置
    const fields = this.config?.fields ?? {};
    
    for (const [fieldName, fieldConfig] of Object.entries(fields)) {
      this.validateField(fieldName, formData, fieldConfig);
    }
    
    // 执行跨字段验证
    this.validateCrossFields(formData);
    
    return {
      isValid: Object.keys(this.errors).length === 0,
      errors: this.errors
    };
  }
  
  // 验证单个字段
  validateField(fieldName, formData, fieldConfig) {
    const value = this.getFieldValue(fieldName, formData);
    const rules = fieldConfig?.validation?.rules ?? [];
    
    // 检查必填
    if (fieldConfig?.required && this.isEmpty(value)) {
      this.addError(fieldName, fieldConfig?.validation?.messages?.required ?? 'This field is required');
      return;
    }
    
    // 如果值为空且非必填，跳过其他验证
    if (this.isEmpty(value) && !fieldConfig?.required) {
      return;
    }
    
    // 执行验证规则
    for (const rule of rules) {
      if (!this.executeRule(rule, value, formData)) {
        const message = rule?.message ?? fieldConfig?.validation?.messages?.[rule?.type] ?? 'Invalid value';
        this.addError(fieldName, message);
        break; // 一个字段只显示第一个错误
      }
    }
  }
  
  // 获取字段值（支持嵌套路径）
  getFieldValue(fieldPath, formData) {
    const pathParts = fieldPath.split('.');
    let value = formData;
    
    for (const part of pathParts) {
      value = value?.[part];
      if (value === undefined) break;
    }
    
    return value;
  }
  
  // 执行验证规则
  executeRule(rule, value, formData) {
    switch (rule?.type) {
      case 'minLength':
        return value?.length >= (rule?.params?.min ?? 0);
        
      case 'maxLength':
        return value?.length <= (rule?.params?.max ?? Infinity);
        
      case 'pattern':
        const pattern = rule?.params?.pattern;
        return pattern ? new RegExp(pattern).test(value) : true;
        
      case 'email':
        const emailPattern = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
        return emailPattern.test(value);
        
      case 'numeric':
        return !isNaN(Number(value));
        
      case 'range':
        const num = Number(value);
        const min = rule?.params?.min ?? -Infinity;
        const max = rule?.params?.max ?? Infinity;
        return num >= min && num <= max;
        
      case 'custom':
        const validator = rule?.params?.validator;
        return typeof validator === 'function' ? validator(value, formData) : true;
        
      case 'conditional':
        const condition = rule?.params?.condition;
        if (typeof condition === 'function' && !condition(formData)) {
          return true; // 条件不满足，跳过验证
        }
        return this.executeRule(rule?.params?.rule, value, formData);
        
      default:
        return true;
    }
  }
  
  // 跨字段验证
  validateCrossFields(formData) {
    const crossValidations = this.config?.crossValidation ?? [];
    
    for (const validation of crossValidations) {
      const validator = validation?.validator;
      if (typeof validator === 'function') {
        const result = validator(formData);
        if (!result?.isValid) {
          const fields = validation?.fields ?? [];
          const message = result?.message ?? 'Cross-field validation failed';
          
          for (const field of fields) {
            this.addError(field, message);
          }
        }
      }
    }
  }
  
  // 添加错误
  addError(fieldName, message) {
    if (!this.errors[fieldName]) {
      this.errors[fieldName] = [];
    }
    this.errors[fieldName].push(message);
  }
  
  // 检查值是否为空
  isEmpty(value) {
    return value === null || value === undefined || value === '';
  }
}

// 使用示例
const formConfig = {
  fields: {
    'user.name': {
      required: true,
      validation: {
        rules: [
          { type: 'minLength', params: { min: 2 } },
          { type: 'maxLength', params: { max: 50 } }
        ],
        messages: {
          required: 'Name is required',
          minLength: 'Name must be at least 2 characters',
          maxLength: 'Name cannot exceed 50 characters'
        }
      }
    },
    'user.email': {
      required: true,
      validation: {
        rules: [
          { type: 'email' }
        ],
        messages: {
          required: 'Email is required',
          email: 'Please enter a valid email address'
        }
      }
    },
    'user.age': {
      required: false,
      validation: {
        rules: [
          { type: 'numeric' },
          { type: 'range', params: { min: 18, max: 120 } }
        ]
      }
    }
  },
  crossValidation: [
    {
      fields: ['user.password', 'user.confirmPassword'],
      validator: (formData) => {
        const password = formData?.user?.password;
        const confirmPassword = formData?.user?.confirmPassword;
        
        if (password && confirmPassword && password !== confirmPassword) {
          return {
            isValid: false,
            message: 'Passwords do not match'
          };
        }
        
        return { isValid: true };
      }
    }
  ]
};

const validator = new FormValidator(formConfig);
const result = validator.validate({
  user: {
    name: 'John',
    email: '<EMAIL>',
    age: '25'
  }
});`
  }
];

export default businessScenarios;
