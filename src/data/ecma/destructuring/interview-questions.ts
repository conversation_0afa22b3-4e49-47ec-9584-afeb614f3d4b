import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: "解构赋值是什么？它解决了什么问题？",
    answer: {
      brief: "解构赋值是ES6引入的语法特性，允许从数组和对象中提取数据并赋值给变量。它简化了数据提取操作，减少了重复代码。",
      detailed: `解构赋值(Destructuring Assignment)是ES6(ES2015)引入的一种语法特性，它提供了一种更简洁的方式来从数组和对象中提取数据。

**解决的核心问题：**
1. **减少重复代码**：不再需要多次写对象.属性的代码
2. **提高可读性**：一眼就能看出需要哪些数据
3. **简化函数参数**：特别是配置对象的参数传递
4. **支持默认值**：在提取时就能设置默认值

**主要优势：**
- 模式匹配：直观地表达数据结构
- 默认值支持：避免undefined问题
- 嵌套解构：处理复杂数据结构
- 剩余操作符：灵活处理不确定数量的数据`,
      code: `// 传统方式 vs 解构赋值对比
// ❌ 传统方式 - 冗余且容易出错
const name = user.name;
const age = user.age;
const email = user.profile.email;

// ✅ 解构赋值 - 简洁明了
const { name, age, profile: { email } } = user;

// 数组解构简化多值返回
function getCoordinates() {
  return [10, 20];
}
const [x, y] = getCoordinates();

// 函数参数解构
function createUser({ name, age = 18, email }) {
  return { name, age, email };
}`
    },
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念'
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: "解构赋值中的默认值是如何工作的？什么时候会使用默认值？",
    answer: {
      brief: "默认值在解构的值为undefined时生效。默认值表达式是惰性求值的，只在需要时才计算。",
      detailed: `解构赋值中的默认值机制非常灵活且高效：

**默认值触发条件：**
- 只有当解构的值严格等于undefined时，才会使用默认值
- null、0、false、空字符串等都不会触发默认值

**惰性求值特性：**
- 默认值表达式只在需要时才计算
- 可以是函数调用、表达式或变量引用
- 可以引用之前解构的变量

**性能考虑：**
- 避免了不必要的计算开销
- 函数调用只在真正需要时发生`,
      code: `// 默认值的触发条件
const obj = { a: null, b: 0, c: false, d: '' };
const { 
  a = 'default-a',  // a为null，不触发默认值，a = null
  b = 'default-b',  // b为0，不触发默认值，b = 0
  c = 'default-c',  // c为false，不触发默认值，c = false
  d = 'default-d',  // d为空字符串，不触发默认值，d = ''
  e = 'default-e'   // e为undefined，触发默认值，e = 'default-e'
} = obj;

// 惰性求值示例
function expensive() {
  console.log('执行昂贵的计算');
  return 'computed-value';
}

const { x = expensive() } = { x: 'exists' };
// expensive()不会被调用，因为x有值

const { y = expensive() } = { z: 'other' };
// expensive()会被调用，因为y为undefined

// 引用之前的变量
const { width = 100, height = width * 0.75 } = config;`
    },
    difficulty: 'medium',
    frequency: 'high',
    category: '默认值机制'
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 3,
    question: "解构赋值如何处理嵌套对象？有什么注意事项？",
    answer: {
      brief: "嵌套解构支持任意深度的对象和数组嵌套，但需要注意中间层级不能为null/undefined，否则会抛出TypeError。",
      detailed: `嵌套解构是解构赋值的强大特性，但也有一些陷阱需要注意：

**嵌套解构的工作原理：**
1. 从外层开始逐层解构
2. 每一层都需要是有效的对象或数组
3. 支持数组和对象的混合嵌套

**常见陷阱：**
1. **中间层级为null/undefined**：会抛出TypeError
2. **过深的嵌套**：影响代码可读性
3. **默认值的层级问题**：需要在正确的层级设置

**最佳实践：**
- 为中间层级提供默认值
- 控制嵌套深度（建议不超过3层）
- 使用TypeScript提供类型保护`,
      code: `// 基本嵌套解构
const data = {
  user: {
    name: '张三',
    profile: {
      avatar: 'avatar.jpg',
      settings: {
        theme: 'dark'
      }
    }
  }
};

const { 
  user: { 
    name, 
    profile: { 
      avatar, 
      settings: { theme } 
    } 
  } 
} = data;

// ❌ 危险：中间层级可能为undefined
const badData = { user: null };
// TypeError: Cannot destructure property 'name' of 'undefined'
const { user: { name } } = badData;

// ✅ 安全：提供默认值
const { user: { name } = {} } = badData || {};

// 或者更安全的写法
const { 
  user: { 
    name = '默认姓名', 
    profile: { 
      avatar = 'default.jpg' 
    } = {} 
  } = {} 
} = data || {};

// 数组和对象混合嵌套
const complexData = {
  items: [
    { id: 1, info: { title: 'Item 1' } },
    { id: 2, info: { title: 'Item 2' } }
  ]
};

const { 
  items: [
    { info: { title: firstTitle } },
    { info: { title: secondTitle } }
  ]
} = complexData;`
    },
    difficulty: 'medium',
    frequency: 'medium',
    category: '嵌套解构'
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 4,
    question: "剩余操作符在解构中是如何工作的？有什么限制？",
    answer: {
      brief: "剩余操作符(...)用于收集解构中剩余的元素，必须放在最后位置。对象解构会创建浅拷贝，数组解构会创建新数组。",
      detailed: `剩余操作符(Rest operator)在解构中用于收集未明确解构的剩余元素：

**数组解构中的剩余操作符：**
- 收集剩余的数组元素
- 总是返回一个新数组
- 必须是最后一个元素

**对象解构中的剩余操作符：**
- 收集剩余的对象属性
- 创建一个新对象（浅拷贝）
- 不包括已解构的属性

**重要限制：**
1. 只能有一个剩余操作符
2. 必须在最后位置
3. 创建的是浅拷贝，不是深拷贝

**性能考虑：**
- 会创建新的对象/数组
- 对于大对象可能有内存开销`,
      code: `// 数组解构中的剩余操作符
const numbers = [1, 2, 3, 4, 5];
const [first, second, ...rest] = numbers;
console.log(first);  // 1
console.log(second); // 2
console.log(rest);   // [3, 4, 5]

// 对象解构中的剩余操作符
const user = {
  name: '张三',
  age: 25,
  city: '北京',
  email: '<EMAIL>'
};

const { name, age, ...otherInfo } = user;
console.log(name);      // '张三'
console.log(age);       // 25
console.log(otherInfo); // { city: '北京', email: '<EMAIL>' }

// ❌ 错误用法：剩余操作符不在最后
// const [first, ...rest, last] = array; // SyntaxError

// ❌ 错误用法：多个剩余操作符
// const { a, ...rest1, ...rest2 } = obj; // SyntaxError

// 函数参数中的应用
function processUser({ name, age, ...config }) {
  console.log(\`用户: \${name}, 年龄: \${age}\`);
  console.log('其他配置:', config);
}

processUser({
  name: '李四',
  age: 30,
  theme: 'dark',
  language: 'zh-CN'
});

// 浅拷贝注意事项
const nestedObj = {
  a: 1,
  b: { nested: 'value' }
};

const { a, ...rest } = nestedObj;
rest.b.nested = 'modified';
console.log(nestedObj.b.nested); // 'modified' - 原对象也被修改了！`
    },
    difficulty: 'medium',
    frequency: 'medium',
    category: '剩余操作符'
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 5,
    question: "解构赋值的性能如何？什么时候应该避免使用？",
    answer: {
      brief: "对于简单解构，性能与直接访问相当；但深层嵌套解构和剩余操作符可能有性能开销。在性能敏感场景下需要权衡。",
      detailed: `解构赋值的性能分析和使用建议：

**性能优势场景：**
1. **简单解构**：现代引擎优化良好，性能接近直接访问
2. **减少重复计算**：避免多次属性查找
3. **编译时优化**：静态分析可以优化简单模式

**性能劣势场景：**
1. **深层嵌套**：增加计算复杂度
2. **剩余操作符**：需要创建新对象/数组
3. **动态属性名**：无法编译时优化
4. **复杂默认值**：重复计算开销

**何时避免使用：**
- 高频调用的热点代码
- 处理大型对象的剩余操作
- 深层嵌套且性能敏感的场景
- 循环中的复杂解构

**优化建议：**
- 缓存复杂的解构结果
- 避免在循环中使用剩余操作符
- 使用TypeScript获得更好的优化`,
      code: `// 性能对比示例

// ✅ 高性能：简单解构
const { name, age } = user;  // 与 user.name, user.age 性能相当

// ⚠️ 中等性能：嵌套解构
const { profile: { avatar } } = user;  // 略有开销，但可接受

// ❌ 低性能：循环中的剩余操作符
users.forEach(user => {
  const { id, ...rest } = user;  // 每次都创建新对象
  process(rest);
});

// ✅ 优化：避免在循环中使用剩余操作符
users.forEach(user => {
  const { id, name, email } = user;  // 只提取需要的字段
  process({ name, email });
});

// 性能测试示例
function performanceTest() {
  const testObj = { a: 1, b: 2, c: 3, d: 4, e: 5 };
  const iterations = 1000000;
  
  // 直接访问
  console.time('直接访问');
  for (let i = 0; i < iterations; i++) {
    const a = testObj.a;
    const b = testObj.b;
  }
  console.timeEnd('直接访问');
  
  // 简单解构
  console.time('简单解构');
  for (let i = 0; i < iterations; i++) {
    const { a, b } = testObj;
  }
  console.timeEnd('简单解构');
  
  // 剩余操作符
  console.time('剩余操作符');
  for (let i = 0; i < iterations; i++) {
    const { a, ...rest } = testObj;
  }
  console.timeEnd('剩余操作符');
}

// 内存优化技巧
const processLargeDataset = (data) => {
  // ❌ 内存浪费：创建大量临时对象
  return data.map(item => {
    const { id, ...details } = item;
    return { id, processed: true, ...details };
  });
  
  // ✅ 内存优化：只提取需要的字段
  return data.map(({ id, name, value }) => ({
    id,
    name,
    value,
    processed: true
  }));
};`
    },
    difficulty: 'hard',
    frequency: 'medium',
    category: '性能优化'
  }
];

export default interviewQuestions; 