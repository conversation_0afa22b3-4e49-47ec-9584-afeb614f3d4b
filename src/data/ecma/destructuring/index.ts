import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const destructuringData: ApiItem = {
  id: 'destructuring',
  title: '解构赋值',
  description: 'ES6解构赋值语法，用于从数组和对象中提取数据并赋值给变量',
  category: 'ECMA特性',
  difficulty: 'medium',
  
  syntax: `const [a, b] = array; const { x, y } = object;`,
  example: `const [first, second] = ['red', 'blue']; const { name, age } = user;`,
  notes: '支持默认值、嵌套解构和变量重命名',
  
  version: 'ES6 (ES2015)',
  tags: ['ES6', 'JavaScript', '语法特性', '数据提取'],
  
  // 9个Tab内容 - 已全部实现
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default destructuringData; 