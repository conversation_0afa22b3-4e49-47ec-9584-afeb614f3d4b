import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'destructure-undefined-null',
    question: '解构undefined或null会发生什么？如何安全处理？',
    answer: `对undefined或null进行对象解构会抛出TypeError，这是JavaScript的设计行为。

**错误原因：**
- undefined和null不是对象，无法从中提取属性
- JavaScript引擎尝试访问undefined.property时会抛出错误

**安全处理方法：**
1. 提供默认值：\`const { name } = data || {}\`
2. 可选链操作符：\`const { name } = data ?? {}\`
3. 条件检查：先检查数据是否存在再解构`,
    code: `// ❌ 会抛出错误
const data = null;
const { name } = data; // TypeError: Cannot destructure property 'name' of 'null'

// ✅ 安全方法1：提供默认对象
const { name = '默认值' } = data || {};

// ✅ 安全方法2：使用空值合并操作符
const { name = '默认值' } = data ?? {};

// ✅ 安全方法3：条件检查
if (data && typeof data === 'object') {
  const { name } = data;
}

// ✅ 安全方法4：嵌套默认值
const { user: { name = '匿名' } = {} } = response || {};`,
    tags: ['错误处理', '空值检查', '默认值'],
    relatedQuestions: ['nested-destructuring-safety', 'default-value-behavior']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'array-destructuring-length',
    question: '数组解构时，如果数组长度不够会怎么样？',
    answer: `如果数组长度不够，多余的变量会被赋值为undefined。这是正常行为，不会抛出错误。

**行为说明：**
- 数组解构基于位置索引
- 超出数组长度的位置返回undefined
- 可以通过默认值来处理这种情况

**最佳实践：**
- 为可能缺失的位置设置默认值
- 使用剩余操作符处理不定长数组
- 在解构前检查数组长度（如有必要）`,
    code: `// 数组长度不够的情况
const arr = ['a', 'b'];
const [first, second, third, fourth] = arr;

console.log(first);  // 'a'
console.log(second); // 'b'
console.log(third);  // undefined
console.log(fourth); // undefined

// ✅ 使用默认值处理
const [x, y, z = 'default', w = 'default'] = arr;
console.log(z); // 'default'
console.log(w); // 'default'

// ✅ 使用剩余操作符
const [head, ...tail] = arr;
console.log(head); // 'a'
console.log(tail); // ['b']

// ✅ 跳过某些位置
const [, , third = 'missing'] = arr;
console.log(third); // 'missing'`,
    tags: ['数组解构', 'undefined处理', '默认值'],
    relatedQuestions: ['rest-operator-usage', 'default-value-behavior']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'destructuring-in-function-params',
    question: '函数参数解构有什么注意事项？如何设置默认值？',
    answer: `函数参数解构需要特别注意默认值的设置，因为涉及两个层面的默认值：参数本身和解构的属性。

**两层默认值：**
1. 参数级别：当整个参数为undefined时的默认值
2. 属性级别：当对象属性为undefined时的默认值

**常见陷阱：**
- 只设置属性默认值，不设置参数默认值
- 调用时传入null会导致错误
- 默认值的优先级问题`,
    code: `// ❌ 只设置属性默认值，缺少参数默认值
function badExample({ name = '匿名', age = 0 }) {
  return \`\${name}, \${age}岁\`;
}
// badExample(); // TypeError: Cannot destructure property 'name' of 'undefined'

// ✅ 正确设置两层默认值
function goodExample({ name = '匿名', age = 0 } = {}) {
  return \`\${name}, \${age}岁\`;
}

goodExample(); // "匿名, 0岁"
goodExample({}); // "匿名, 0岁"
goodExample({ name: '张三' }); // "张三, 0岁"

// ✅ 更复杂的配置函数
function createServer({
  port = 3000,
  host = 'localhost',
  ssl = false,
  middleware = []
} = {}) {
  return {
    port,
    host,
    ssl,
    middleware: [...middleware, 'default-middleware']
  };
}

// ✅ 嵌套对象的参数解构
function processUser({
  name = '匿名',
  profile: {
    avatar = 'default.jpg',
    theme = 'light'
  } = {}
} = {}) {
  return { name, avatar, theme };
}`,
    tags: ['函数参数', '默认值', '配置对象'],
    relatedQuestions: ['nested-destructuring-safety', 'destructure-undefined-null']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'destructuring-variable-scope',
    question: '解构赋值中的变量作用域是怎样的？能否重新赋值？',
    answer: `解构赋值创建的变量遵循JavaScript的标准作用域规则，取决于使用的声明关键字（const、let、var）。

**作用域规则：**
- const: 块级作用域，不可重新赋值
- let: 块级作用域，可重新赋值
- var: 函数作用域，可重新赋值

**重新赋值：**
- const声明的解构变量不能重新赋值
- let和var声明的可以重新赋值
- 可以对已存在的变量进行解构赋值（不使用声明关键字）`,
    code: `// 不同声明关键字的作用域
function scopeExample() {
  if (true) {
    const { name: constName } = { name: '张三' };
    let { age: letAge } = { age: 25 };
    var { city: varCity } = { city: '北京' };
  }
  
  // console.log(constName); // ReferenceError
  // console.log(letAge);    // ReferenceError
  console.log(varCity);      // "北京" - var是函数作用域
}

// 重新赋值示例
let { count } = { count: 0 };
count = 10; // ✅ 可以重新赋值

const { items } = { items: [] };
// items = []; // ❌ TypeError: Assignment to constant variable
items.push('new'); // ✅ 可以修改数组内容

// 对已存在变量的解构赋值
let x, y;
({ x, y } = { x: 1, y: 2 }); // 注意需要括号

// 在循环中使用解构
const users = [
  { name: '张三', age: 25 },
  { name: '李四', age: 30 }
];

for (const { name, age } of users) {
  console.log(\`\${name}: \${age}岁\`);
  // name和age在每次循环中都是新的const变量
}`,
    tags: ['作用域', '变量声明', 'const/let/var'],
    relatedQuestions: ['destructuring-in-loops', 'const-let-differences']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'destructuring-performance-tips',
    question: '解构赋值在性能上有什么需要注意的地方？',
    answer: `解构赋值的性能主要取决于使用场景和复杂度。简单解构性能很好，但某些情况需要注意。

**高性能场景：**
- 简单的对象/数组解构
- 替代多次属性访问
- 函数参数解构（避免重复的obj.prop访问）

**需要注意的场景：**
- 循环中的复杂解构
- 大对象的剩余操作符
- 深层嵌套解构
- 默认值是复杂计算表达式`,
    code: `// ✅ 高性能：简单解构
const { name, age } = user; // 性能很好

// ✅ 高性能：避免重复访问
// 传统方式
const x = point.coordinates.x;
const y = point.coordinates.y;
// 解构方式（更好）
const { coordinates: { x, y } } = point;

// ⚠️ 注意：循环中的剩余操作符
// 较慢
items.forEach(item => {
  const { id, ...rest } = item; // 每次都创建新对象
  process(rest);
});

// 更快
items.forEach(({ id, name, value }) => {
  process({ name, value }); // 只创建需要的对象
});

// ⚠️ 注意：昂贵的默认值计算
function expensiveCalc() {
  // 复杂计算...
  return 'result';
}

// 较慢（如果x为undefined，每次都会计算）
const { x = expensiveCalc() } = obj;

// 更好的方式：缓存结果
const defaultX = expensiveCalc();
const { x = defaultX } = obj;

// 性能测试代码示例
function performanceComparison() {
  const data = { a: 1, b: 2, c: 3, d: 4 };
  const iterations = 1000000;
  
  console.time('直接访问');
  for (let i = 0; i < iterations; i++) {
    const a = data.a;
    const b = data.b;
  }
  console.timeEnd('直接访问');
  
  console.time('解构赋值');
  for (let i = 0; i < iterations; i++) {
    const { a, b } = data;
  }
  console.timeEnd('解构赋值');
}`,
    tags: ['性能优化', '最佳实践', '循环优化'],
    relatedQuestions: ['rest-operator-performance', 'nested-destructuring-cost']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'destructuring-with-typescript',
    question: '在TypeScript中使用解构赋值有什么特殊考虑？',
    answer: `TypeScript为解构赋值提供了强大的类型推断和检查，但也需要注意一些类型相关的问题。

**类型推断：**
- TypeScript会自动推断解构变量的类型
- 支持接口和类型别名的解构
- 可以为解构变量指定具体类型

**常见问题：**
- 可选属性的处理
- 联合类型的解构
- 默认值的类型推断`,
    code: `// TypeScript中的解构类型推断
interface User {
  name: string;
  age?: number;
  profile?: {
    avatar: string;
    theme: 'light' | 'dark';
  };
}

// ✅ 基本类型推断
const user: User = { name: '张三', age: 25 };
const { name, age } = user; // name: string, age: number | undefined

// ✅ 可选属性的处理
const { age = 0 } = user; // age: number (默认值确保了类型)

// ✅ 嵌套解构的类型
const { 
  profile: { 
    avatar = 'default.jpg', 
    theme = 'light' 
  } = {} 
} = user;

// ✅ 类型断言在解构中的使用
const data = fetchData() as { items: Array<{ id: number; name: string }> };
const { items } = data;

// ✅ 泛型函数中的解构
function processApiResponse<T>({ data, status }: { data: T; status: number }) {
  return { processedData: data, isSuccess: status === 200 };
}

// ✅ 联合类型的解构
type ApiResponse = 
  | { type: 'success'; data: any }
  | { type: 'error'; message: string };

function handleResponse(response: ApiResponse) {
  if (response.type === 'success') {
    const { data } = response; // TypeScript知道这里有data属性
  } else {
    const { message } = response; // TypeScript知道这里有message属性
  }
}

// ✅ 剩余操作符的类型
const { id, ...rest } = user; // rest的类型是Pick<User, 'age' | 'profile'>`,
    tags: ['TypeScript', '类型推断', '接口解构'],
    relatedQuestions: ['typescript-optional-properties', 'union-type-destructuring']
  }
];

export default commonQuestions; 