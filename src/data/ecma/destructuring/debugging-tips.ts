import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  commonErrors: [
    {
      error: 'TypeError: Cannot destructure property \'name\' of \'undefined\'',
      cause: '尝试对undefined或null值进行对象解构',
      solution: '在解构前检查值是否存在，或提供默认对象',
      prevention: '总是为可能为空的对象提供默认值',
      code: `// ❌ 错误代码
const data = null;
const { name } = data; // TypeError

// ✅ 解决方案
const { name } = data || {};
const { name } = data ?? {};
const { user: { name } = {} } = response || {};`
    },
    {
      error: 'TypeError: rest element must be last element',
      cause: '剩余操作符(...rest)没有放在解构模式的最后位置',
      solution: '将剩余操作符移动到解构模式的最后',
      prevention: '记住剩余操作符必须是最后一个元素',
      code: `// ❌ 错误代码
const [first, ...rest, last] = array; // SyntaxError

// ✅ 解决方案
const [first, ...rest] = array;
const [first, second, ...rest] = array;`
    },
    {
      error: 'SyntaxError: Unexpected token }',
      cause: '嵌套解构时括号不匹配或语法错误',
      solution: '仔细检查括号的匹配和嵌套结构',
      prevention: '使用IDE的语法高亮和括号匹配功能',
      code: `// ❌ 错误代码
const { user: { name } = data; // 缺少右括号

// ✅ 解决方案
const { user: { name } } = data;
const { user: { name = 'default' } = {} } = data;`
    },
    {
      error: 'ReferenceError: Cannot access before initialization',
      cause: '在解构声明之前尝试访问变量（TDZ问题）',
      solution: '确保在声明并初始化后才访问变量',
      prevention: '避免在同一作用域中重复声明同名变量',
      code: `// ❌ 错误代码
console.log(name); // ReferenceError
const { name } = user;

// ✅ 解决方案
const { name } = user;
console.log(name);`
    },
    {
      error: 'TypeError: Symbol.iterator is not a function',
      cause: '尝试对不可迭代的对象进行数组解构',
      solution: '确保被解构的值是可迭代的（数组、字符串、Set等）',
      prevention: '在数组解构前验证值的类型',
      code: `// ❌ 错误代码
const obj = { 0: 'a', 1: 'b', length: 2 };
const [first, second] = obj; // TypeError

// ✅ 解决方案
const [first, second] = Array.from(obj);
const [first, second] = Object.values(obj);`
    }
  ],

  devToolsTips: [
    {
      tool: 'Chrome DevTools Console',
      technique: '使用console.log调试解构结果',
      example: `// 调试解构结果
const data = { user: { name: 'John', age: 30 } };
const { user: { name, age } } = data;
console.log({ name, age }); // 快速查看解构结果

// 调试默认值是否生效
const { theme = 'light' } = settings;
console.log('Theme:', theme, 'Settings:', settings);`
    },
    {
      tool: 'Chrome DevTools Debugger',
      technique: '在解构语句上设置断点观察变量值',
      example: `// 在这行设置断点
const { user: { profile: { avatar = 'default.jpg' } = {} } = {} } = data;
// 在断点处查看：
// - data的结构
// - 每层解构的中间结果
// - 默认值是否被使用`
    },
    {
      tool: 'VS Code调试器',
      technique: '使用变量窗口观察解构过程',
      example: `// 配置launch.json进行调试
{
  "type": "node",
  "request": "launch",
  "name": "Debug Destructuring",
  "program": "\${workspaceFolder}/test-destructuring.js"
}`
    },
    {
      tool: 'Node.js REPL',
      technique: '交互式测试解构语法',
      example: `// 在Node.js REPL中测试
> const data = { a: 1, b: { c: 2 } };
> const { a, b: { c } } = data;
> console.log(a, c); // 1 2

// 测试边界情况
> const { x = 'default' } = {};
> console.log(x); // 'default'`
    }
  ],

  troubleshooting: [
    {
      symptom: '解构后变量值为undefined',
      possibleCauses: [
        '源对象中不存在对应的属性',
        '属性名拼写错误',
        '访问了嵌套对象中不存在的路径',
        '源数据为null或undefined'
      ],
      solutions: [
        '检查源对象的结构和属性名',
        '使用console.log输出源对象验证结构',
        '为可能缺失的属性设置默认值',
        '使用可选链操作符(?.)进行安全访问'
      ]
    },
    {
      symptom: '嵌套解构时出现TypeError',
      possibleCauses: [
        '中间层级的对象为null或undefined',
        '嵌套结构与实际数据不匹配',
        '忘记为中间层级提供默认值'
      ],
      solutions: [
        '为每个嵌套层级提供默认空对象',
        '分步进行解构，逐层检查',
        '使用条件检查确保对象存在',
        '考虑使用工具函数处理深层嵌套'
      ]
    },
    {
      symptom: '解构在循环中性能很差',
      possibleCauses: [
        '在循环中使用了剩余操作符',
        '解构模式过于复杂',
        '重复计算默认值',
        '深层嵌套解构增加了开销'
      ],
      solutions: [
        '避免在循环中使用剩余操作符',
        '简化解构模式，只提取需要的字段',
        '预计算复杂的默认值',
        '考虑分步解构或使用传统属性访问'
      ]
    },
    {
      symptom: 'TypeScript类型错误',
      possibleCauses: [
        '解构的属性类型与声明不匹配',
        '可选属性没有正确处理',
        '默认值类型与属性类型不兼容'
      ],
      solutions: [
        '检查接口定义与实际数据结构',
        '为可选属性提供正确的默认值',
        '使用类型断言或类型守卫',
        '更新TypeScript类型定义'
      ]
    },
    {
      symptom: '剩余操作符不包含预期的属性',
      possibleCauses: [
        '在剩余操作符之前已经解构了相同的属性',
        '源对象中本来就不存在这些属性',
        '属性是不可枚举的'
      ],
      solutions: [
        '检查解构顺序，确保没有重复提取',
        '验证源对象的实际内容',
        '使用Object.getOwnPropertyDescriptors检查属性特性',
        '考虑使用Object.assign或展开操作符'
      ]
    }
  ]
};

export default debuggingTips; 