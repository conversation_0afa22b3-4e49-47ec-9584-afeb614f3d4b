import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `解构赋值是ES6引入的一种强大的语法特性，允许从数组或对象中提取数据，并将其赋值给变量。它使用模式匹配的方式，可以快速提取嵌套数据结构中的值，支持默认值、重命名、剩余参数等高级功能。解构赋值不仅简化了变量赋值的代码，还提高了代码的可读性和维护性，特别适用于函数参数、模块导入、数据交换等场景。`,
  
  introduction: `解构赋值是ES6(ES2015)引入的强大语法特性，主要用于简化从数组和对象中提取数据的过程、减少代码冗余和提供更优雅的变量赋值方式。它采用模式匹配的设计思想，提供了更直观的数据解包体验。`,

  syntax: `// 数组解构
const [a, b, c] = array;
const [first, , third] = array;        // 跳过元素
const [x, y, ...rest] = array;         // 剩余参数
const [a = 1, b = 2] = array;          // 默认值

// 对象解构
const { name, age } = object;
const { name: userName, age: userAge } = object;  // 重命名
const { x, y, z = defaultValue } = object;        // 默认值
const { name, ...others } = object;               // 剩余属性

// 嵌套解构
const { user: { name, profile: { avatar } } } = data;
const { a: { b: { c } } } = nestedObject;

// 函数参数解构
function processUser({ name, age, city = '未知' }) {
  return name + ', ' + age + '岁, 来自' + city;
}

// 混合解构
const [first, { name, age }] = [item, userObject];

// 交换变量
let a = 1, b = 2;
[a, b] = [b, a];`,

  quickExample: `// 数组解构示例
const colors = ['red', 'green', 'blue', 'yellow'];
const [primary, secondary, ...others] = colors;

console.log('主色:', primary); // 'red'
console.log('次色:', secondary); // 'green'
console.log('其他颜色:', others); // ['blue', 'yellow']

// 对象解构示例
const user = { name: '张三', age: 25, city: '北京' };
const { name, age, city = '未知' } = user;

console.log('用户信息:', name, age + '岁', '来自' + city);
// 用户信息: 张三 25岁 来自北京

// 函数参数解构
const processUser = ({ name, age, email = '未提供' }) => {
  return '用户: ' + name + ', 年龄: ' + age + ', 邮箱: ' + email;
};

console.log(processUser(user));
// 用户: 张三, 年龄: 25, 邮箱: 未提供

// 嵌套解构
const response = {
  data: {
    users: [
      { id: 1, profile: { name: 'Alice', settings: { theme: 'dark' } } },
      { id: 2, profile: { name: 'Bob', settings: { theme: 'light' } } }
    ]
  },
  status: 'success'
};

const {
  data: {
    users: [
      { profile: { name: firstName, settings: { theme: firstTheme } } },
      { profile: { name: secondName } }
    ]
  },
  status
} = response;

console.log(firstName, firstTheme); // 'Alice' 'dark'
console.log(secondName); // 'Bob'
console.log(status); // 'success'

// 变量交换
let a = 1, b = 2;
console.log('交换前:', a, b); // 1 2
[a, b] = [b, a];
console.log('交换后:', a, b); // 2 1

// 函数返回多个值
function getCoordinates() {
  return [10, 20];
}

const [x, y] = getCoordinates();
console.log('坐标:', x, y); // 坐标: 10 20

// 模块导入解构
// import { useState, useEffect } from 'react';
// const { readFile, writeFile } = require('fs');`,

  scenarioDiagram: `graph TD
    A[解构赋值使用场景] --> B[数组解构]
    A --> C[对象解构]
    A --> D[函数参数解构]

    B --> B1[变量交换]
    B --> B2[多值返回]
    B --> B3[忽略某些值]

    C --> C1[提取对象属性]
    C --> C2[重命名变量]
    C --> C3[设置默认值]

    D --> D1[API响应处理]
    D --> D2[配置选项解构]
    D --> D3[React Props解构]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "pattern",
      type: "Array | Object",
      required: true,
      description: "解构模式，定义如何从源数据中提取值",
      details: "[a, b] 或 {x, y} 或嵌套模式"
    },
    {
      name: "source",
      type: "Array | Object",
      required: true,
      description: "要解构的源数据",
      details: "数组、对象或任何可迭代对象"
    },
    {
      name: "defaultValue",
      type: "any",
      required: false,
      description: "当解构值为undefined时使用的默认值",
      details: "const {x = 10} = obj"
    }
  ],
  
  returnValue: {
    type: "void",
    description: "解构赋值不返回值，而是直接为变量赋值"
  },

  coreFeatures: [
    {
      feature: "数组解构",
      description: "从数组中按位置提取值",
      importance: "high" as const,
      details: "支持跳过元素、剩余参数、默认值"
    },
    {
      feature: "对象解构",
      description: "从对象中按属性名提取值",
      importance: "high" as const,
      details: "支持重命名、默认值、嵌套解构"
    },
    {
      feature: "函数参数解构",
      description: "在函数参数中直接解构",
      importance: "high" as const,
      details: "简化函数调用和参数处理"
    },
    {
      feature: "默认值机制",
      description: "为解构变量提供默认值",
      importance: "medium" as const,
      details: "避免undefined，提高代码健壮性"
    }
  ],
  
  keyFeatures: [
    {
      feature: "简洁的语法",
      description: "大幅简化从复杂数据结构中提取值的代码",
      importance: "high" as const,
      details: "减少重复的对象属性访问，一行代码完成多个变量赋值"
    },
    {
      feature: "默认值支持",
      description: "可以为解构的变量设置默认值，避免undefined",
      importance: "high" as const,
      details: "当源对象中不存在对应属性时，使用预设的默认值"
    },
    {
      feature: "嵌套解构",
      description: "支持深层次的对象和数组嵌套解构",
      importance: "medium" as const,
      details: "可以一次性从复杂的嵌套结构中提取所需数据"
    },
    {
      feature: "变量重命名",
      description: "可以在解构时重命名变量，避免命名冲突",
      importance: "medium" as const,
      details: "const {name: userName} = user 将name重命名为userName"
    }
  ],
  
  limitations: [
    "对undefined和null进行对象解构会抛出TypeError",
    "解构赋值在严格模式下不能对未声明的变量进行",
    "深层嵌套解构可能影响代码可读性",
    "过度使用解构可能让代码逻辑变得不清晰",
    "在某些情况下性能可能不如直接属性访问"
  ],
  
  bestPractices: [
    "为解构变量提供合理的默认值: const {count = 0} = data",
    "避免过深的嵌套解构，保持代码可读性",
    "在函数参数中使用解构来提高API的清晰度",
    "使用数组解构进行变量交换: [a, b] = [b, a]",
    "结合剩余参数使用: const [first, ...rest] = array"
  ],
  
  warnings: [
    "对null或undefined进行对象解构会抛出错误，需要提供默认值",
    "解构赋值的性能在大量数据处理时可能不如传统方式",
    "过度使用嵌套解构可能导致代码难以理解和维护"
  ],

  notes: [
    '解构赋值是ES6最实用的特性之一，大幅简化了数据操作',
    '支持数组和对象的深层嵌套解构，功能强大',
    '可以与函数参数、循环、赋值等多种语法结合使用',
    '在React、Vue等现代框架中被广泛使用',
    '是理解现代JavaScript编程模式的基础'
  ],

  mermaidDiagram: `
graph TD
    A[数据源] --> B{解构类型}
    B -->|对象解构| C[对象模式匹配]
    B -->|数组解构| D[数组模式匹配]

    C --> E[属性提取]
    C --> F[重命名赋值]
    C --> G[默认值设置]
    C --> H[嵌套解构]

    D --> I[位置提取]
    D --> J[跳过元素]
    D --> K[剩余操作符]
    D --> L[默认值设置]

    E --> M[变量赋值]
    F --> M
    G --> M
    H --> M
    I --> M
    J --> M
    K --> M
    L --> M

    M --> N[代码简化]
    M --> O[可读性提升]
    M --> P[错误减少]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style M fill:#e8f5e8
    style N fill:#fff3e0
    style O fill:#fff3e0
    style P fill:#fff3e0
  `
};

export default basicInfo; 