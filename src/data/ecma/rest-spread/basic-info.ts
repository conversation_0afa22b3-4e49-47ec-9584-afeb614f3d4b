import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: `Rest/Spread Properties是ES2018引入的对象操作语法，扩展了ES6中数组的展开语法到对象领域。它包含两个核心概念：Spread（展开）语法用于将对象属性展开到新对象中，Rest（剩余）语法用于收集对象中的剩余属性。这种语法提供了简洁、直观的对象操作方式，是现代JavaScript函数式编程的重要基础。`,

  introduction: `在ES2018之前，对象的复制、合并和解构操作需要使用Object.assign()或手动遍历属性，代码冗长且容易出错。Rest/Spread Properties的引入彻底改变了这种状况，提供了声明式的对象操作语法。

展开语法（...obj）可以将一个对象的所有可枚举属性展开到另一个对象中，实现浅拷贝和属性合并。剩余语法（...rest）则可以在解构赋值中收集未明确指定的属性，提供了灵活的属性提取方式。

这种语法不仅简化了代码，更重要的是它体现了函数式编程的不可变性原则，鼓励创建新对象而不是修改现有对象，从而减少副作用，提高代码的可预测性和可维护性。`,

  syntax: `
// 对象展开语法
const newObject = { ...sourceObject };
const merged = { ...obj1, ...obj2, newProp: value };

// 对象剩余参数（解构中使用）
const { prop1, prop2, ...rest } = sourceObject;

// 函数参数中的剩余参数（ES6已有）
function func(first, second, ...rest) { }

// 函数调用中的展开语法
func(...args);

// 条件展开
const obj = {
  baseProp: 'value',
  ...(condition && { conditionalProp: 'value' }),
  ...(condition ? { prop1: 'a' } : { prop2: 'b' })
};
  `,

  quickExample: `
// 快速示例：用户配置合并
const defaultConfig = {
  theme: 'light',
  language: 'en',
  notifications: true,
  autoSave: false
};

const userConfig = {
  theme: 'dark',
  autoSave: true
};

// 合并配置（用户配置覆盖默认配置）
const finalConfig = { ...defaultConfig, ...userConfig };
// 结果: { theme: 'dark', language: 'en', notifications: true, autoSave: true }

// 提取特定属性，其余收集到rest中
const { theme, language, ...otherSettings } = finalConfig;
// theme: 'dark'
// language: 'en' 
// otherSettings: { notifications: true, autoSave: true }
  `,

  keyFeatures: [
    {
      feature: '对象浅拷贝',
      description: '使用展开语法快速创建对象的浅拷贝，避免引用共享问题',
      importance: 'critical',
      details: '{ ...obj } 创建新对象，包含原对象的所有可枚举属性，但嵌套对象仍为引用'
    },
    {
      feature: '对象合并',
      description: '将多个对象的属性合并到新对象中，后面的对象属性会覆盖前面的同名属性',
      importance: 'critical',
      details: '{ ...obj1, ...obj2 } 实现对象合并，属性冲突时右侧对象优先'
    },
    {
      feature: '剩余属性收集',
      description: '在解构赋值中收集未明确指定的属性到一个新对象中',
      importance: 'high',
      details: 'const { a, b, ...rest } = obj 将除a、b外的属性收集到rest对象'
    },
    {
      feature: '条件属性展开',
      description: '根据条件动态添加属性到对象中，实现灵活的对象构建',
      importance: 'high',
      details: '使用逻辑与操作符或三元操作符实现条件属性展开'
    },
    {
      feature: '函数式编程支持',
      description: '支持不可变数据操作，鼓励创建新对象而不是修改现有对象',
      importance: 'high',
      details: '符合函数式编程原则，减少副作用，提高代码可预测性'
    },
    {
      feature: '嵌套解构支持',
      description: '可以与嵌套解构结合使用，实现复杂对象的属性提取',
      importance: 'medium',
      details: '支持多层嵌套的对象解构和剩余参数收集'
    }
  ],

  commonUseCases: [
    {
      title: '配置对象合并',
      description: '将默认配置与用户配置合并，用户配置覆盖默认值',
      code: `
const defaultOptions = {
  timeout: 5000,
  retries: 3,
  cache: true,
  debug: false
};

const userOptions = {
  timeout: 10000,
  debug: true
};

const finalOptions = { ...defaultOptions, ...userOptions };
// { timeout: 10000, retries: 3, cache: true, debug: true }
      `
    },
    {
      title: 'State更新（React风格）',
      description: '在状态管理中创建新的状态对象，保持不可变性',
      code: `
const currentState = {
  user: { name: 'Alice', age: 30 },
  isLoading: false,
  error: null
};

// 更新用户信息
const newState = {
  ...currentState,
  user: { ...currentState.user, age: 31 },
  isLoading: true
};
      `
    },
    {
      title: '属性过滤和提取',
      description: '从对象中提取特定属性，将其余属性收集到另一个对象',
      code: `
const apiResponse = {
  id: 123,
  name: 'Product',
  price: 99.99,
  description: 'A great product',
  internalId: 'internal-123',
  debugInfo: { ... }
};

// 提取公开属性，过滤内部属性
const { internalId, debugInfo, ...publicData } = apiResponse;
// publicData: { id: 123, name: 'Product', price: 99.99, description: 'A great product' }
      `
    }
  ],

  bestPractices: [
    '优先使用展开语法而不是Object.assign()进行对象合并',
    '注意展开语法只进行浅拷贝，嵌套对象需要特殊处理',
    '使用剩余参数收集未知属性，提高函数的灵活性',
    '结合条件表达式实现动态属性添加',
    '在React等框架中使用展开语法保持状态不可变性',
    '避免在性能敏感的场景中过度使用展开语法',
    '使用TypeScript时注意展开语法的类型推导'
  ],

  warnings: [
    '展开语法只复制可枚举的自有属性，不包括原型链上的属性',
    '对于嵌套对象，展开语法只进行浅拷贝，嵌套对象仍为引用',
    '大量使用展开语法可能影响性能，特别是在循环中',
    '剩余参数必须是解构模式中的最后一个元素',
    '展开语法不会触发setter，直接复制属性值'
  ],

  notes: [
    'Rest/Spread Properties是ES2018的重要特性，扩展了ES6的数组展开语法',
    '这种语法大大简化了对象操作，是现代JavaScript开发的标准做法',
    '与数组的展开语法类似，但应用于对象的属性操作',
    '支持与解构赋值、默认参数等其他ES6+特性组合使用',
    '在函数式编程和React开发中广泛使用'
  ]
};

export default basicInfo;
