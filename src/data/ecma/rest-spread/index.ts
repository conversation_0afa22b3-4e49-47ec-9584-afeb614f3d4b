import { ApiItem } from '@/types/api';

// 导入各个维度的数据
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const restSpreadApi: ApiItem = {
  id: 'rest-spread',
  title: 'Rest/Spread Properties',
  description: 'ES2018引入的对象展开语法和剩余参数，提供了优雅的对象操作方式，支持浅拷贝、属性合并和解构赋值，是现代JavaScript开发中不可或缺的语法糖。',
  category: 'ECMAScript',
  difficulty: 'medium',
  syntax: `
// 对象展开语法
const newObj = { ...obj1, ...obj2, newProp: 'value' };

// 对象剩余参数
const { prop1, prop2, ...rest } = obj;

// 函数参数展开
func(...args);

// 数组展开（ES6已有，ES2018扩展到对象）
const newArray = [...array1, ...array2];
  `,
  example: `
// 基础对象展开
const user = { name: 'Alice', age: 30 };
const profile = { ...user, city: 'New York', age: 31 };
// { name: 'Alice', age: 31, city: 'New York' }

// 对象剩余参数
const { name, ...otherInfo } = profile;
// name: 'Alice'
// otherInfo: { age: 31, city: 'New York' }

// 条件属性展开
const config = {
  baseUrl: '/api',
  ...(isDev && { debug: true }),
  ...(isProduction && { cache: true })
};

// 深度合并模拟
const defaults = { theme: 'light', lang: 'en' };
const userPrefs = { theme: 'dark' };
const settings = { ...defaults, ...userPrefs };
// { theme: 'dark', lang: 'en' }
  `,
  tags: ['ES2018', 'ES9', 'Object', 'Spread', 'Rest', 'Destructuring', 'Syntax'],
  version: 'ES2018',
  isNew: false,

  // 核心维度数据
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default restSpreadApi;
