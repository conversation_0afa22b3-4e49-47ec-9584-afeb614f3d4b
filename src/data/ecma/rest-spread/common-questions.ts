import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'rest-spread-difference',
    question: 'Rest和Spread语法看起来一样，如何区分它们？',
    answer: `
Rest和Spread虽然都使用三个点（...）语法，但使用场景和作用完全不同：

**Spread（展开）- 分解数据：**
- 用于展开对象或数组的内容
- 出现在对象字面量或函数调用中
- 作用：将集合分解为单个元素

**Rest（剩余）- 收集数据：**
- 用于收集剩余的参数或属性
- 出现在解构赋值或函数参数中
- 作用：将多个元素收集为一个集合

**记忆技巧：**
- Spread = 展开 = 一变多
- Rest = 剩余 = 多变一
    `,
    code: `
// Spread（展开）示例
const obj1 = { a: 1, b: 2 };
const obj2 = { ...obj1, c: 3 }; // 展开obj1的属性

const arr1 = [1, 2, 3];
const arr2 = [...arr1, 4, 5]; // 展开arr1的元素

func(...args); // 展开参数数组

// Rest（剩余）示例
const { a, ...rest } = obj; // 收集剩余属性到rest
const [first, ...others] = arr; // 收集剩余元素到others

function func(first, ...rest) { // 收集剩余参数
  console.log(first, rest);
}
    `,
    tags: ['语法', '概念区分', '基础'],
    relatedQuestions: ['spread-vs-assign', 'rest-parameters']
  },

  {
    id: 'spread-vs-assign',
    question: '什么时候用展开语法，什么时候用Object.assign()？',
    answer: `
**优先使用展开语法的情况：**
- 现代JavaScript环境（ES2018+）
- 需要条件属性展开
- 代码可读性要求高
- 静态属性合并

**使用Object.assign()的情况：**
- 需要兼容老版本浏览器
- 动态属性合并（属性来源不确定）
- 需要返回目标对象的引用
- 与现有Object.assign()代码保持一致

**性能考虑：**
展开语法通常有更好的引擎优化，但差异很小，选择主要基于代码风格和兼容性需求。
    `,
    code: `
// 推荐：展开语法
const config = {
  ...defaults,
  ...userSettings,
  ...(isDev && { debug: true })
};

// 兼容性：Object.assign()
const config = Object.assign(
  {},
  defaults,
  userSettings,
  isDev ? { debug: true } : {}
);

// 动态合并：Object.assign()更适合
const sources = [config1, config2, config3];
const merged = Object.assign({}, ...sources);
    `,
    tags: ['性能', '兼容性', '最佳实践'],
    relatedQuestions: ['rest-spread-difference', 'performance-tips']
  },

  {
    id: 'nested-objects',
    question: '如何处理嵌套对象的展开和合并？',
    answer: `
展开语法只进行浅拷贝，嵌套对象需要特殊处理：

**手动处理嵌套对象：**
对于已知结构的嵌套对象，手动展开每一层。

**使用工具库：**
- Lodash的merge()函数
- Ramda的mergeDeepRight()函数
- Immer库进行不可变更新

**自定义深度合并函数：**
编写递归函数处理任意深度的对象合并。

**最佳实践：**
- 保持状态结构扁平化
- 只在必要时进行深度合并
- 考虑使用专门的状态管理库
    `,
    code: `
// 问题：嵌套对象的浅拷贝
const user = {
  name: 'John',
  address: { city: 'NYC', zip: '10001' }
};

const updated = { ...user, name: 'Jane' };
updated.address.city = 'LA'; // 会影响原对象！

// 解决方案1：手动深度展开
const updated = {
  ...user,
  name: 'Jane',
  address: { ...user.address, city: 'LA' }
};

// 解决方案2：深度合并函数
function deepMerge(target, source) {
  const result = { ...target };
  for (const key in source) {
    if (isObject(source[key])) {
      result[key] = deepMerge(target[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }
  return result;
}

// 解决方案3：使用Immer
import produce from 'immer';
const updated = produce(user, draft => {
  draft.name = 'Jane';
  draft.address.city = 'LA';
});
    `,
    tags: ['嵌套对象', '深拷贝', '状态管理'],
    relatedQuestions: ['shallow-copy', 'immutable-updates']
  }
];

export default commonQuestions;
