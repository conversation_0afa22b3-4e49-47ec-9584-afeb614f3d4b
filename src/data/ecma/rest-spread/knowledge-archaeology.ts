import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: `Rest/Spread Properties的历史是JavaScript语言不断演进和完善的缩影，它体现了从命令式编程向声明式编程、从可变数据向不可变数据的重要转变。这个特性的诞生不仅解决了开发者的实际需求，更推动了整个JavaScript生态系统向更现代、更函数式的方向发展。`,

  background: `在ES2018之前，JavaScript开发者在处理对象操作时面临着诸多不便。虽然ES6引入了数组的展开语法，但对象操作仍然依赖于Object.assign()或手动属性复制，这些方法不仅语法冗长，而且不够直观。

随着React等框架的兴起，不可变数据操作变得越来越重要。开发者需要频繁地创建新对象而不是修改现有对象，以保持状态的可预测性。然而，传统的对象操作方法使得这种不可变更新变得复杂和容易出错。

同时，函数式编程理念在JavaScript社区中逐渐普及，开发者开始追求更声明式、更简洁的代码风格。这种背景下，对象的Rest/Spread语法应运而生，它不仅简化了语法，更重要的是推动了编程范式的转变。`,

  evolution: `
## 对象操作语法的演进历程

**ES3-ES5时代（1999-2009）：手工时代**
- 手动遍历对象属性
- 使用for...in循环复制属性
- 大量的样板代码和重复逻辑

**ES5时代（2009-2015）：工具化时代**
- Object.keys()提供了更好的属性遍历
- 各种工具库（如Underscore.js、Lodash）提供对象操作函数
- 仍然缺乏语言级别的简洁语法

**ES6时代（2015-2017）：部分现代化**
- 引入了数组的展开语法
- Object.assign()提供了标准的对象合并方法
- 解构赋值简化了属性提取
- 但对象展开仍然缺失

**ES2018时代（2018-至今）：声明式时代**
- Rest/Spread Properties正式引入
- 对象操作实现了语法的统一和简化
- 推动了不可变编程的普及
- 成为现代JavaScript开发的标准实践
  `,

  timeline: [
    {
      year: '2014',
      event: 'ES6数组展开语法标准化',
      description: '数组的展开语法在ES6中正式确定，为对象展开语法奠定了基础',
      significance: '建立了展开语法的基本概念和使用模式'
    },
    {
      year: '2015',
      event: 'Object.assign()引入',
      description: 'ES6引入Object.assign()方法，提供了标准的对象合并功能',
      significance: '解决了对象合并的标准化问题，但语法仍不够简洁'
    },
    {
      year: '2016',
      event: 'React普及推动不可变需求',
      description: 'React的广泛使用让不可变状态更新成为常见需求',
      significance: '增加了对简洁对象操作语法的需求'
    },
    {
      year: '2017',
      event: 'TC39提案进入Stage 4',
      description: 'Rest/Spread Properties提案在TC39委员会中达到Stage 4（完成）',
      significance: '标志着该特性正式成为ECMAScript标准的一部分'
    },
    {
      year: '2018',
      event: 'ES2018正式发布',
      description: 'Rest/Spread Properties作为ES2018的重要特性正式发布',
      significance: '为JavaScript带来了现代化的对象操作语法'
    },
    {
      year: '2019-2020',
      event: '广泛采用和生态系统适配',
      description: '主流浏览器和Node.js全面支持，开发工具和框架广泛采用',
      significance: '成为现代JavaScript开发的标准实践'
    }
  ],

  keyFigures: [
    {
      name: 'Sebastian Markbåge',
      role: 'React核心开发者',
      contribution: '推动了不可变状态更新模式的普及，为Rest/Spread语法的需求提供了重要推动力',
      significance: '他在React中的工作展示了不可变数据操作的重要性'
    },
    {
      name: 'TC39委员会',
      role: 'ECMAScript标准制定者',
      contribution: '负责Rest/Spread Properties提案的评审和标准化工作',
      significance: '确保了该特性的设计质量和标准化程度'
    },
    {
      name: 'Babel团队',
      role: '转译工具开发者',
      contribution: '提供了早期的Rest/Spread语法支持，让开发者能够提前使用这个特性',
      significance: '加速了新语法的采用和生态系统的发展'
    }
  ],

  concepts: [
    {
      term: '不可变性（Immutability）',
      definition: '数据一旦创建就不能被修改，任何"修改"操作都会创建新的数据结构',
      evolution: '从函数式编程概念逐渐成为JavaScript主流实践，Rest/Spread语法大大简化了不可变操作',
      modernRelevance: '现代前端框架和状态管理库的核心原则，提高了应用的可预测性和可调试性'
    },
    {
      term: '声明式编程（Declarative Programming）',
      definition: '描述"做什么"而不是"怎么做"的编程范式',
      evolution: '从命令式的对象操作（for循环、手动赋值）发展到声明式的展开语法',
      modernRelevance: '提高了代码的可读性和可维护性，降低了认知负担'
    },
    {
      term: '浅拷贝（Shallow Copy）',
      definition: '只复制对象的第一层属性，嵌套对象仍为引用的拷贝方式',
      evolution: '从手动实现到Object.assign()，再到Rest/Spread语法的标准化',
      modernRelevance: '在性能和功能之间找到平衡，满足大多数使用场景的需求'
    }
  ],

  designPhilosophy: `Rest/Spread Properties的设计哲学体现了现代编程语言设计的几个重要原则：

**一致性原则**：扩展已有的数组展开语法到对象，保持了语法的一致性，降低了学习成本。

**简洁性原则**：用简单的三个点语法替代复杂的函数调用，让代码更加简洁和直观。

**表达力原则**：让代码能够直接表达开发者的意图，而不是被实现细节所干扰。

**渐进增强原则**：在不破坏现有代码的基础上，提供更好的语法选择。`,

  impact: `Rest/Spread Properties对JavaScript生态系统产生了深远的影响：

**编程范式转变**：推动了从命令式向声明式编程的转变，让不可变编程成为主流实践。

**框架发展**：为React、Vue等现代框架的状态管理提供了语言级支持，简化了状态更新逻辑。

**工具链改进**：促进了TypeScript、ESLint等工具对新语法的支持和优化。

**开发体验提升**：显著改善了开发者的编码体验，减少了样板代码和潜在错误。`,

  modernRelevance: `在当今的JavaScript开发中，Rest/Spread Properties已经成为不可或缺的特性：

**现代框架必备**：几乎所有现代JavaScript框架都大量使用这种语法进行状态管理和数据处理。

**最佳实践标准**：成为了JavaScript代码质量和现代化程度的重要指标。

**生态系统基础**：为各种工具库和框架提供了统一的对象操作基础，促进了生态系统的协调发展。

**未来发展方向**：为JavaScript向更函数式、更声明式方向发展提供了重要的语法基础。`
};

export default knowledgeArchaeology;
