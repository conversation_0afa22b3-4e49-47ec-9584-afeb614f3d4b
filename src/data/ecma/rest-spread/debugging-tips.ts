import { DebuggingTip } from '@/types/api';

const debuggingTips: DebuggingTip[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'shallow-copy-debugging',
    title: '调试浅拷贝引起的引用问题',
    problem: '使用展开语法后，嵌套对象的修改仍然影响原对象，导致意外的副作用',
    solution: `
理解展开语法只进行浅拷贝，嵌套对象需要特殊处理：

1. **识别问题**：使用Object.is()或===检查对象引用
2. **可视化引用关系**：使用console.log和浏览器调试工具
3. **手动深拷贝**：对需要独立的嵌套对象进行展开
4. **使用工具**：利用Immer等库避免引用问题
    `,
    code: `
// 问题演示和调试
const original = {
  name: 'John',
  address: { city: 'NYC', zip: '10001' }
};

const copied = { ...original };

// 🔍 调试技巧1：检查引用关系
console.log('Same reference?', original.address === copied.address); // true

// 🔍 调试技巧2：使用Object.is()
console.log('Object.is check:', Object.is(original.address, copied.address)); // true

// 🔍 调试技巧3：可视化对象结构
console.table({
  'original.address': original.address,
  'copied.address': copied.address
});

// ✅ 解决方案：手动深拷贝
const properlyCopied = {
  ...original,
  address: { ...original.address }
};

console.log('Fixed reference?', original.address === properlyCopied.address); // false

// 🔍 调试技巧4：使用断点和watch
// 在浏览器中设置断点，watch original.address 和 copied.address
debugger;
copied.address.city = 'LA';
console.log('Original affected?', original.address.city); // 'LA' - 被影响了！
    `,
    category: '引用问题',
    difficulty: 'medium'
  },

  {
    id: 'property-enumeration-debugging',
    title: '调试属性枚举和展开行为',
    problem: '某些属性没有被展开，或者展开了不期望的属性',
    solution: `
理解展开语法只处理可枚举的自有属性：

1. **检查属性描述符**：使用Object.getOwnPropertyDescriptor()
2. **列出所有属性**：区分可枚举和不可枚举属性
3. **检查原型链**：确认属性来源
4. **Symbol属性处理**：了解Symbol属性的展开行为
    `,
    code: `
// 创建测试对象
const obj = { a: 1, b: 2 };

// 添加不可枚举属性
Object.defineProperty(obj, 'hidden', {
  value: 'secret',
  enumerable: false
});

// 添加Symbol属性
const sym = Symbol('test');
obj[sym] = 'symbol value';

// 🔍 调试技巧1：检查所有属性
console.log('Own property names:', Object.getOwnPropertyNames(obj));
console.log('Own property symbols:', Object.getOwnPropertySymbols(obj));
console.log('Enumerable keys:', Object.keys(obj));

// 🔍 调试技巧2：检查属性描述符
Object.getOwnPropertyNames(obj).forEach(prop => {
  const descriptor = Object.getOwnPropertyDescriptor(obj, prop);
  console.log(\`\${prop}:\`, descriptor);
});

// 🔍 调试技巧3：测试展开行为
const spread = { ...obj };
console.log('Spread result:', spread);
console.log('Has hidden?', 'hidden' in spread); // false
console.log('Has symbol?', sym in spread); // false

// ✅ 完整复制（包括不可枚举属性）
const fullCopy = Object.defineProperties({}, Object.getOwnPropertyDescriptors(obj));
console.log('Full copy:', fullCopy);
    `,
    category: '属性枚举',
    difficulty: 'hard'
  },

  {
    id: 'react-state-debugging',
    title: '调试React中的状态更新问题',
    problem: '使用展开语法更新状态后，组件没有重新渲染或渲染异常',
    solution: `
在React中调试状态更新问题：

1. **检查状态引用**：确保创建了新的对象引用
2. **使用React DevTools**：监控状态变化
3. **添加调试日志**：跟踪状态更新过程
4. **检查依赖数组**：确保useEffect等Hook的依赖正确
    `,
    code: `
import React, { useState, useEffect } from 'react';

function DebuggableComponent() {
  const [user, setUser] = useState({
    name: 'John',
    preferences: { theme: 'light' }
  });

  // 🔍 调试技巧1：添加状态变化日志
  useEffect(() => {
    console.log('User state changed:', user);
    console.log('User reference:', user);
  }, [user]);

  // ❌ 错误的更新方式
  const wrongUpdate = () => {
    user.name = 'Jane'; // 直接修改，不会触发重渲染
    setUser(user); // 引用没变，React不会更新
    console.log('After wrong update:', user);
  };

  // ✅ 正确的更新方式
  const correctUpdate = () => {
    const newUser = { ...user, name: 'Jane' };
    console.log('Old user reference:', user);
    console.log('New user reference:', newUser);
    console.log('References equal?', user === newUser); // false
    setUser(newUser);
  };

  // 🔍 调试技巧2：嵌套对象更新调试
  const updatePreferences = (newTheme) => {
    console.log('Before update:', user.preferences);
    
    // ❌ 错误：嵌套对象引用问题
    const wrongWay = {
      ...user,
      preferences: user.preferences // 仍然是引用
    };
    wrongWay.preferences.theme = newTheme; // 修改了原对象！
    
    // ✅ 正确：深度展开
    const correctWay = {
      ...user,
      preferences: { ...user.preferences, theme: newTheme }
    };
    
    console.log('Wrong way preferences ref:', user.preferences === wrongWay.preferences);
    console.log('Correct way preferences ref:', user.preferences === correctWay.preferences);
    
    setUser(correctWay);
  };

  // 🔍 调试技巧3：使用React DevTools
  // 在组件中添加调试信息
  const debugInfo = {
    userRef: user,
    userStringified: JSON.stringify(user),
    renderTime: Date.now()
  };

  console.log('Render debug info:', debugInfo);

  return (
    <div>
      <h3>{user.name}</h3>
      <p>Theme: {user.preferences.theme}</p>
      <button onClick={correctUpdate}>Update Name</button>
      <button onClick={() => updatePreferences('dark')}>
        Change Theme
      </button>
      
      {/* 调试信息显示 */}
      <details>
        <summary>Debug Info</summary>
        <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
      </details>
    </div>
  );
}

// 🔍 调试技巧4：自定义Hook进行状态调试
function useDebugState(initialState, name) {
  const [state, setState] = useState(initialState);
  
  useEffect(() => {
    console.log(\`[\${name}] State changed:\`, state);
  }, [state, name]);
  
  const debugSetState = (newState) => {
    console.log(\`[\${name}] Setting state from:\`, state);
    console.log(\`[\${name}] Setting state to:\`, newState);
    setState(newState);
  };
  
  return [state, debugSetState];
}
    `,
    category: 'React调试',
    difficulty: 'medium'
  },

  {
    id: 'performance-debugging',
    title: '调试展开语法的性能问题',
    problem: '应用中使用展开语法导致性能下降，需要定位和优化',
    solution: `
使用性能分析工具调试展开语法的性能问题：

1. **使用Performance API**：测量操作耗时
2. **浏览器DevTools**：分析内存使用和对象创建
3. **React Profiler**：分析组件渲染性能
4. **自定义性能监控**：添加性能埋点
    `,
    code: `
// 🔍 调试技巧1：性能测量
function measureSpreadPerformance() {
  const largeObject = {};
  for (let i = 0; i < 10000; i++) {
    largeObject[\`prop\${i}\`] = i;
  }

  // 测量展开操作性能
  console.time('Spread Operation');
  const copied = { ...largeObject, newProp: 'value' };
  console.timeEnd('Spread Operation');

  // 对比Object.assign性能
  console.time('Object.assign Operation');
  const assigned = Object.assign({}, largeObject, { newProp: 'value' });
  console.timeEnd('Object.assign Operation');

  // 使用Performance API进行精确测量
  const start = performance.now();
  const result = { ...largeObject };
  const end = performance.now();
  console.log(\`Spread took \${end - start} milliseconds\`);
}

// 🔍 调试技巧2：内存使用监控
function monitorMemoryUsage() {
  if (performance.memory) {
    const before = performance.memory.usedJSHeapSize;
    
    // 执行大量展开操作
    const objects = [];
    for (let i = 0; i < 1000; i++) {
      objects.push({ ...someObject, id: i });
    }
    
    const after = performance.memory.usedJSHeapSize;
    console.log(\`Memory increase: \${after - before} bytes\`);
  }
}

// 🔍 调试技巧3：React性能调试
import { Profiler } from 'react';

function onRenderCallback(id, phase, actualDuration) {
  console.log('Profiler:', { id, phase, actualDuration });
}

function PerformanceDebugComponent() {
  const [data, setData] = useState({});

  // 性能问题：每次渲染都创建新对象
  const problematicRender = () => {
    return (
      <div>
        {/* 每次都创建新对象，导致子组件重渲染 */}
        <ChildComponent config={{ ...defaultConfig, ...data }} />
      </div>
    );
  };

  // 优化：使用useMemo缓存结果
  const optimizedConfig = useMemo(() => ({
    ...defaultConfig,
    ...data
  }), [data]);

  return (
    <Profiler id="PerformanceDebug" onRender={onRenderCallback}>
      <div>
        <ChildComponent config={optimizedConfig} />
      </div>
    </Profiler>
  );
}

// 🔍 调试技巧4：自定义性能Hook
function usePerformanceMonitor(operation, dependencies) {
  useEffect(() => {
    const start = performance.now();
    
    return () => {
      const end = performance.now();
      console.log(\`Operation took \${end - start}ms\`);
    };
  }, dependencies);
}

// 使用示例
function ComponentWithMonitoring() {
  const [state, setState] = useState({});
  
  usePerformanceMonitor('State Update', [state]);
  
  const updateState = () => {
    setState(prev => ({ ...prev, timestamp: Date.now() }));
  };
  
  return <button onClick={updateState}>Update</button>;
}
    `,
    category: '性能调试',
    difficulty: 'hard'
  }
];

export default debuggingTips;
