import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'Rest/Spread Properties与Object.assign()有什么区别？什么时候应该使用哪个？',
    answer: {
      brief: 'Rest/Spread语法更简洁直观，支持条件展开，而Object.assign()是函数调用，功能相似但语法不同。',
      detailed: `
主要区别：

**语法差异：**
- Rest/Spread: \`{ ...obj1, ...obj2 }\`
- Object.assign(): \`Object.assign({}, obj1, obj2)\`

**功能差异：**
1. **条件展开支持**
   - Rest/Spread支持：\`{ ...obj, ...(condition && { prop: value }) }\`
   - Object.assign()不直接支持条件合并

2. **可读性**
   - Rest/Spread更直观，意图更清晰
   - Object.assign()需要理解函数调用语义

3. **性能**
   - Rest/Spread通常有更好的引擎优化
   - Object.assign()是函数调用，有额外开销

**使用场景：**
- 优先使用Rest/Spread，语法更现代
- 需要兼容老版本浏览器时使用Object.assign()
- 动态属性合并时Object.assign()可能更灵活
      `,
      code: `
// Rest/Spread方式
const merged1 = { ...defaults, ...userConfig, newProp: 'value' };

// Object.assign方式
const merged2 = Object.assign({}, defaults, userConfig, { newProp: 'value' });

// 条件展开（Rest/Spread优势）
const config = {
  ...baseConfig,
  ...(isDev && { debug: true }),
  ...(isProd && { minify: true })
};

// Object.assign的动态合并优势
const sources = [config1, config2, config3];
const merged3 = Object.assign({}, ...sources);
      `
    },
    difficulty: 'medium',
    frequency: 'high',
    category: '语法对比'
  },

  {
    id: 2,
    question: 'Rest/Spread只进行浅拷贝，如何处理嵌套对象的深拷贝需求？',
    answer: {
      brief: 'Rest/Spread只进行浅拷贝，嵌套对象需要手动处理或使用专门的深拷贝工具。',
      detailed: `
**浅拷贝问题：**
Rest/Spread只复制第一层属性，嵌套对象仍为引用，修改嵌套对象会影响原对象。

**解决方案：**

1. **手动深度合并**
2. **使用Lodash等工具库**
3. **JSON序列化（有限制）**
4. **递归实现深拷贝**
5. **使用Immer等不可变库**

**最佳实践：**
- 简单场景：手动处理需要深拷贝的嵌套对象
- 复杂场景：使用专门的深拷贝库
- 状态管理：使用Immer等不可变数据库
      `,
      code: `
// 问题演示：浅拷贝的限制
const original = {
  name: 'John',
  address: { city: 'New York', zip: '10001' }
};

const copied = { ...original };
copied.address.city = 'Boston'; // 会影响original！

// 解决方案1：手动深度合并
const deepCopied = {
  ...original,
  address: { ...original.address }
};

// 解决方案2：递归深拷贝函数
function deepMerge(target, source) {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object') {
      result[key] = deepMerge(target[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }
  
  return result;
}

// 解决方案3：使用Lodash
import { merge } from 'lodash';
const deepMerged = merge({}, original, updates);

// 解决方案4：使用Immer
import produce from 'immer';
const newState = produce(original, draft => {
  draft.address.city = 'Boston';
});
      `
    },
    difficulty: 'hard',
    frequency: 'high',
    category: '深度拷贝'
  },

  {
    id: 3,
    question: '在React中如何正确使用Rest/Spread进行状态更新？有哪些常见陷阱？',
    answer: {
      brief: 'React中使用Rest/Spread进行不可变状态更新，需要注意嵌套对象的处理和性能优化。',
      detailed: `
**正确用法：**
1. **简单状态更新**
2. **嵌套状态更新**
3. **数组状态更新**
4. **条件状态更新**

**常见陷阱：**
1. **嵌套对象引用问题**
2. **性能问题（过度渲染）**
3. **状态结构设计不当**
4. **useEffect依赖问题**

**最佳实践：**
- 保持状态结构扁平
- 使用useCallback优化函数
- 合理使用React.memo
- 考虑使用状态管理库
      `,
      code: `
// 正确的状态更新
function UserProfile() {
  const [user, setUser] = useState({
    name: 'John',
    email: '<EMAIL>',
    preferences: {
      theme: 'light',
      notifications: true
    }
  });

  // ✅ 正确：简单属性更新
  const updateName = (newName) => {
    setUser(prev => ({ ...prev, name: newName }));
  };

  // ✅ 正确：嵌套对象更新
  const updateTheme = (theme) => {
    setUser(prev => ({
      ...prev,
      preferences: { ...prev.preferences, theme }
    }));
  };

  // ❌ 错误：直接修改状态
  const wrongUpdate = () => {
    user.name = 'Jane'; // 不会触发重渲染
    setUser(user);
  };

  // ❌ 错误：嵌套对象引用问题
  const wrongNestedUpdate = () => {
    const newUser = { ...user };
    newUser.preferences.theme = 'dark'; // 修改了原对象
    setUser(newUser);
  };

  // ✅ 正确：使用useCallback优化
  const updatePreferences = useCallback((updates) => {
    setUser(prev => ({
      ...prev,
      preferences: { ...prev.preferences, ...updates }
    }));
  }, []);

  return (
    <div>
      <input 
        value={user.name}
        onChange={(e) => updateName(e.target.value)}
      />
    </div>
  );
}

// 性能优化：使用React.memo
const UserCard = React.memo(({ user, onUpdate }) => {
  return (
    <div>
      <h3>{user.name}</h3>
      <button onClick={() => onUpdate({ ...user, active: !user.active })}>
        Toggle Active
      </button>
    </div>
  );
});
      `
    },
    difficulty: 'hard',
    frequency: 'high',
    category: 'React应用'
  }
];

export default interviewQuestions;
