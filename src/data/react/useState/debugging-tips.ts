import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  commonErrors: [
    {
      error: '状态直接修改不触发重渲染',
      cause: '直接修改state对象或数组的属性，React无法检测到引用变化，不会触发组件重渲染',
      solution: '使用展开语法或其他方式创建新的对象/数组引用，确保状态更新的不可变性',
      prevention: '始终使用useState返回的setter函数，并确保传入的是新的引用而不是修改后的原引用',
      code: `// ❌ 错误：直接修改状态对象
function UserProfile() {
  const [user, setUser] = useState({ name: 'John', age: 25 });
  
  const updateAge = () => {
    user.age = 26; // 直接修改，不会触发重渲染
    setUser(user); // 引用没变，React认为状态没更新
  };
  
  return <div>{user.age}</div>; // 页面不会更新
}

// ✅ 正确：创建新对象
function UserProfile() {
  const [user, setUser] = useState({ name: '<PERSON>', age: 25 });
  
  const updateAge = () => {
    setUser(prev => ({ ...prev, age: 26 })); // 创建新对象
    // 或者：setUser({ ...user, age: 26 });
  };
  
  return <div>{user.age}</div>; // 页面会正确更新
}`
    },
    {
      error: '闭包陷阱导致状态值过时',
      cause: '在事件处理函数或useEffect中使用状态值时，由于闭包特性，可能获取到的是过时的状态值',
      solution: '使用函数式更新模式或将状态值添加到依赖数组中，确保获取最新的状态值',
      prevention: '养成使用函数式更新的习惯，特别是在异步操作或定时器中更新状态时',
      code: `// ❌ 错误：闭包陷阱
function Counter() {
  const [count, setCount] = useState(0);
  
  const handleDoubleClick = () => {
    setCount(count + 1); // 基于当前count值
    setCount(count + 1); // 仍然基于同一个count值，结果只增加1
  };
  
  const handleAsyncUpdate = () => {
    setTimeout(() => {
      setCount(count + 1); // 可能使用过时的count值
    }, 1000);
  };
  
  return (
    <div>
      <p>{count}</p>
      <button onClick={handleDoubleClick}>错误的双击</button>
      <button onClick={handleAsyncUpdate}>异步更新</button>
    </div>
  );
}

// ✅ 正确：使用函数式更新
function Counter() {
  const [count, setCount] = useState(0);
  
  const handleDoubleClick = () => {
    setCount(prev => prev + 1); // 基于最新值
    setCount(prev => prev + 1); // 基于上一次更新的结果
  };
  
  const handleAsyncUpdate = () => {
    setTimeout(() => {
      setCount(prev => prev + 1); // 始终获取最新值
    }, 1000);
  };
  
  return (
    <div>
      <p>{count}</p>
      <button onClick={handleDoubleClick}>正确的双击</button>
      <button onClick={handleAsyncUpdate}>异步更新</button>
    </div>
  );
}`
    },
    {
      error: '渲染期间调用setState导致无限循环',
      cause: '在组件渲染过程中直接调用setState，会导致组件重新渲染，形成无限循环',
      solution: '将状态更新逻辑移到事件处理函数、useEffect或其他副作用钩子中',
      prevention: '理解React的渲染机制，避免在render阶段执行有副作用的操作',
      code: `// ❌ 错误：渲染期间调用setState
function BadComponent() {
  const [count, setCount] = useState(0);
  
  // 渲染期间执行，会导致无限循环
  if (count > 10) {
    setCount(0); // 这会触发重渲染，然后再次执行这行代码
  }
  
  return <div>{count}</div>;
}

// ✅ 正确：在副作用中处理
function GoodComponent() {
  const [count, setCount] = useState(0);
  
  // 使用useEffect处理副作用
  useEffect(() => {
    if (count > 10) {
      setCount(0);
    }
  }, [count]);
  
  const increment = () => {
    setCount(prev => prev + 1);
  };
  
  return (
    <div>
      <p>{count}</p>
      <button onClick={increment}>增加</button>
    </div>
  );
}`
    }
  ],
  
  devToolsTips: [
    {
      tool: 'React DevTools状态监控',
      technique: '使用React DevTools扩展实时查看组件状态变化，追踪状态更新历史',
      example: '1. 安装React DevTools浏览器扩展\n2. 打开开发者工具，切换到"Components"或"Profiler"标签\n3. 选择目标组件，在右侧面板查看当前state值\n4. 点击state旁的"眼睛"图标可以编辑状态值进行测试\n5. 使用"Profiler"记录性能数据，查看哪些状态更新导致了重渲染'
    },
    {
      tool: 'Console日志调试法',
      technique: '在状态更新前后添加console.log，追踪状态变化过程和时机',
      example: `function DebuggableComponent() {
  const [user, setUser] = useState({ name: '', email: '' });
  
  const updateUser = (newData) => {
    console.log('更新前的状态:', user);
    console.log('即将更新的数据:', newData);
    
    setUser(prev => {
      const updated = { ...prev, ...newData };
      console.log('更新后的状态:', updated);
      return updated;
    });
  };
  
  // 监听状态变化
  useEffect(() => {
    console.log('用户状态已更新:', user);
    console.log('组件重渲染时间:', new Date().toISOString());
  }, [user]);
  
  return <div>{user.name}</div>;
}`
    },
    {
      tool: 'useEffect依赖监听',
      technique: '使用useEffect监听特定状态变化，帮助理解状态更新的触发时机和频率',
      example: `function StateWatcher() {
  const [count, setCount] = useState(0);
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  
  // 监听单个状态
  useEffect(() => {
    console.log('Count 发生变化:', count);
  }, [count]);
  
  // 监听多个状态
  useEffect(() => {
    console.log('用户相关状态变化:', { count, name });
  }, [count, name]);
  
  // 监听所有状态（谨慎使用）
  useEffect(() => {
    console.log('任意状态发生变化');
  });
  
  // 计算渲染次数
  const renderCount = useRef(0);
  renderCount.current += 1;
  console.log('组件渲染次数:', renderCount.current);
  
  return (
    <div>
      <p>Count: {count}</p>
      <p>Name: {name}</p>
      <p>Loading: {loading.toString()}</p>
    </div>
  );
}`
    }
  ],
  
  troubleshooting: [
    {
      symptom: '状态更新后组件没有重新渲染',
      possibleCauses: [
        '直接修改了状态对象/数组而不是创建新引用',
        '使用了错误的setter函数或者忘记调用setter',
        '状态结构过于复杂，深层嵌套的属性修改没有被正确处理'
      ],
      solutions: [
        '检查是否使用了展开语法或其他方式创建新的对象/数组引用',
        '确认使用的是useState返回的正确setter函数',
        '简化状态结构，将复杂状态拆分为多个简单状态，或使用immer库处理不可变更新'
      ]
    },
    {
      symptom: '状态更新频率过高导致性能问题',
      possibleCauses: [
        '在渲染期间意外触发了状态更新，导致无限循环',
        '多个相关状态分别更新，没有使用批处理优化'
      ],
      solutions: [
        '将状态更新逻辑移到事件处理函数或useEffect中，避免在渲染期间更新状态',
        '合并相关状态为对象，减少更新次数；或使用React 18的自动批处理特性'
      ]
    }
  ]
};

export default debuggingTips; 