import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 'basic-concept',
    question: '什么是useState？它解决了什么问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'useState是React Hooks中用于在函数组件中添加状态管理功能的Hook，解决了函数组件无法保存状态的问题。',
      detailed: `useState是React 16.8引入的核心Hook，专门为函数组件提供状态管理能力。在useState出现之前，函数组件是无状态的，只能用于展示UI，所有需要状态的组件都必须使用类组件。useState的出现彻底改变了这种情况，让函数组件也能拥有自己的状态。

核心特点：
1. **简洁的API**: 只需一行代码即可添加状态
2. **返回值**: 返回包含当前状态值和更新函数的数组
3. **类型安全**: 完整的TypeScript支持
4. **性能优化**: 支持函数式更新和惰性初始化
5. **批处理**: React会自动批处理多个状态更新

它彻底改变了React组件的编写方式，使得函数组件成为主流选择。`,
      code: `// 基础用法
function Counter() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <p>当前计数: {count}</p>
      <button onClick={() => setCount(count + 1)}>
        增加
      </button>
      <button onClick={() => setCount(prev => prev - 1)}>
        减少
      </button>
    </div>
  );
}

// 对象状态
function UserForm() {
  const [user, setUser] = useState({
    name: '',
    email: '',
    age: 0
  });
  
  const updateField = (field, value) => {
    setUser(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  return (
    <form>
      <input 
        value={user.name}
        onChange={(e) => updateField('name', e.target.value)}
      />
    </form>
  );
}`,
      followUp: [
        '为什么useState返回的是数组而不是对象？',
        'useState和类组件的this.state有什么区别？'
      ]
    },
    tags: ['React Hooks', '状态管理', '函数组件']
  },
  {
    id: 'implementation-principle',
    question: 'useState的内部实现原理是什么？React如何保证状态的正确性？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: 'useState内部通过链表结构存储Hook状态，利用调用顺序来维护状态的一致性，通过fiber架构实现状态更新和重渲染。',
      detailed: `useState的内部实现涉及React的核心机制：

**1. Hook链表结构**
React为每个组件维护一个Hook链表，每次调用useState都会在链表中创建或获取一个节点。这就是为什么Hook必须在组件顶层调用的原因：

**2. 状态存储机制**
- 每个Hook节点包含：memoizedState（当前值）、queue（更新队列）、next（下一个Hook）
- 状态值存储在fiber节点的memoizedState中
- 更新队列用于批处理多个setState调用

**3. 更新调度机制**
- setState调用不会立即更新状态，而是创建update对象放入队列
- React根据优先级调度更新，在渲染阶段批量处理
- 通过比较新旧fiber树决定是否重渲染

**4. 闭包和引用机制**
- 每次渲染都会创建新的函数作用域
- useState返回的state值是当次渲染的快照
- setter函数引用保持不变，但内部会获取最新状态`,
      code: `// 简化的useState实现原理
let currentHookIndex = 0;
let hookStates = [];

function useState(initialState) {
  const hookIndex = currentHookIndex++;
  
  // 初始化或获取已有状态
  if (hookStates[hookIndex] === undefined) {
    hookStates[hookIndex] = typeof initialState === 'function' 
      ? initialState() 
      : initialState;
  }
  
  const currentState = hookStates[hookIndex];
  
  const setState = (newValue) => {
    const nextValue = typeof newValue === 'function'
      ? newValue(hookStates[hookIndex])
      : newValue;
      
    if (Object.is(nextValue, hookStates[hookIndex])) {
      return; // 值没变，不触发更新
    }
    
    hookStates[hookIndex] = nextValue;
    // 触发重渲染（实际实现更复杂）
    scheduleRerender();
  };
  
  return [currentState, setState];
}

function scheduleRerender() {
  currentHookIndex = 0; // 重置Hook索引
  // 触发组件重新渲染
}`,
      followUp: [
        'Hook的调用顺序为什么这么重要？',
        'React如何处理多个setState的批处理？',
        'useState和useReducer在内部实现上有什么区别？'
      ]
    },
    tags: ['内部原理', 'Hook机制']
  },
  {
    id: 'practical-application',
    question: '在实际项目中，如何设计和组织useState的状态结构？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '实战应用',
    answer: {
      brief: '应该根据状态的相关性合理拆分，独立状态分开管理，相关状态合并为对象，复杂状态考虑使用useReducer。',
      detailed: `状态设计是React应用中的关键决策，好的状态设计能提升代码可维护性和性能：

**1. 状态分组原则**
- 相关性强的状态合并为对象（如用户信息）
- 独立的UI状态单独管理（如loading、error）
- 临时状态和持久状态分离

**2. 状态粒度控制**
- 避免过度细分：不要为每个属性都创建单独的state
- 避免过度合并：不要把无关状态强行合并
- 考虑更新频率：频繁更新的状态单独管理

**3. 性能优化考虑**
- 大对象状态使用useCallback优化更新函数
- 考虑状态更新对子组件的影响
- 合理使用useState vs useReducer

**4. 可维护性设计**
- 使用TypeScript定义状态类型
- 创建自定义Hook封装复杂状态逻辑
- 保持状态更新逻辑的一致性`,
      code: `// ✅ 好的状态设计示例
function UserDashboard() {
  // 相关的用户数据合并
  const [userProfile, setUserProfile] = useState({
    name: '',
    email: '',
    avatar: '',
    preferences: {
      theme: 'light',
      language: 'zh'
    }
  });
  
  // 独立的UI状态
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('profile');
  
  // 列表数据单独管理
  const [notifications, setNotifications] = useState([]);
  
  // 封装复杂更新逻辑
  const updateUserField = useCallback((field, value) => {
    setUserProfile(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);
  
  const updatePreference = useCallback((key, value) => {
    setUserProfile(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [key]: value
      }
    }));
  }, []);
  
  return (
    <div>
      {loading && <Spinner />}
      {error && <ErrorMessage error={error} />}
      
      <UserProfile 
        user={userProfile}
        onUpdate={updateUserField}
      />
      <NotificationList 
        notifications={notifications}
        onUpdate={setNotifications}
      />
    </div>
  );
}

// ❌ 避免的反模式
function BadStateDesign() {
  // 过度拆分
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [avatar, setAvatar] = useState('');
  const [theme, setTheme] = useState('light');
  
  // 过度合并
  const [everythingInOneState, setEverything] = useState({
    user: {},
    ui: {},
    data: {},
    cache: {}
  });
}`,
      followUp: [
        '什么时候应该使用useReducer代替useState？',
        '如何处理深层嵌套的状态更新？'
      ]
    },
    tags: ['状态设计', '最佳实践', '架构']
  },
  {
    id: 'advanced-scenario',
    question: '如何处理useState的性能优化和高级场景？包括闭包陷阱、批处理等。',
    difficulty: 'hard',
    frequency: 'medium',
    category: '高级应用',
    answer: {
      brief: '通过函数式更新避免闭包陷阱，利用React 18的自动批处理优化性能，使用useCallback缓存更新函数，合理设计状态结构减少不必要的重渲染。',
      detailed: `useState的高级应用涉及性能优化和边界情况处理：

**1. 闭包陷阱处理**
- 问题：异步操作中使用过时的状态值
- 解决：使用函数式更新获取最新状态
- 原理：函数式更新能获取更新队列中的最新值

**2. 批处理优化**
- React 18自动批处理所有更新（包括异步）
- 使用flushSync强制同步更新（谨慎使用）
- 理解批处理对性能的影响

**3. 性能优化策略**
- 使用useCallback缓存更新函数
- 状态结构设计影响重渲染范围
- 大状态对象的部分更新优化

**4. 高级模式**
- 惰性初始化处理昂贵计算
- 自定义Hook封装复杂状态逻辑
- 状态机模式处理复杂状态变化`,
      code: `// 1. 闭包陷阱解决方案
function CounterWithAsyncUpdate() {
  const [count, setCount] = useState(0);
  
  // ❌ 闭包陷阱
  const handleAsyncIncrement = () => {
    setTimeout(() => {
      setCount(count + 1); // 可能使用过时值
    }, 1000);
  };
  
  // ✅ 函数式更新
  const handleAsyncIncrementCorrect = () => {
    setTimeout(() => {
      setCount(prev => prev + 1); // 始终使用最新值
    }, 1000);
  };
  
  // ✅ 使用useRef保存最新值
  const countRef = useRef(count);
  countRef.current = count;
  
  const handleAsyncIncrementWithRef = () => {
    setTimeout(() => {
      setCount(countRef.current + 1);
    }, 1000);
  };
}

// 2. 性能优化模式
function OptimizedComponent() {
  const [user, setUser] = useState({ name: '', email: '' });
  const [settings, setSettings] = useState({ theme: 'light' });
  
  // 缓存更新函数，避免子组件不必要重渲染
  const updateUser = useCallback((field, value) => {
    setUser(prev => ({ ...prev, [field]: value }));
  }, []);
  
  const updateSettings = useCallback((newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  }, []);
  
  return (
    <div>
      <UserForm user={user} onUpdate={updateUser} />
      <SettingsPanel settings={settings} onUpdate={updateSettings} />
    </div>
  );
}

// 3. 惰性初始化和状态机模式
function AdvancedFormComponent() {
  // 惰性初始化避免昂贵计算
  const [formData, setFormData] = useState(() => {
    const savedData = localStorage.getItem('formData');
    return savedData ? JSON.parse(savedData) : getDefaultFormData();
  });
  
  // 状态机模式处理复杂状态
  const [formState, setFormState] = useState({
    status: 'idle', // idle, submitting, success, error
    errors: {},
    isValid: false
  });
  
  const handleSubmit = async () => {
    setFormState(prev => ({ ...prev, status: 'submitting' }));
    
    try {
      await submitForm(formData);
      setFormState({ status: 'success', errors: {}, isValid: true });
    } catch (error) {
      setFormState({ 
        status: 'error', 
        errors: error.fieldErrors || {},
        isValid: false 
      });
    }
  };
  
  // React 18 批处理示例
  const handleBatchUpdate = () => {
    // 这些更新会被自动批处理
    setFormData(prev => ({ ...prev, field1: 'value1' }));
    setFormState(prev => ({ ...prev, isValid: true }));
    // 只会触发一次重渲染
  };
}`,
      followUp: [
        '如何在大型应用中管理复杂的状态依赖关系？',
        'useState vs useReducer vs 外部状态管理库的选择标准？',
        '如何测试使用useState的组件？'
      ]
    },
    tags: ['性能优化', '高级模式']
  }
];

export default interviewQuestions; 