import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'usage-error',
    question: '为什么useState不能在条件语句或循环中使用？',
    answer: 'useState必须在组件的顶层调用，不能在条件语句、循环或嵌套函数中使用。这是因为React使用调用顺序来识别每个Hook，如果Hook在不同渲染之间的调用顺序发生变化，React就无法正确地保持状态。这被称为"Hook规则"，违反这个规则会导致状态错乱或组件崩溃。',
    code: `// ❌ 错误：在条件语句中使用useState
function MyComponent() {
  if (someCondition) {
    const [count, setCount] = useState(0); // 违反Hook规则！
  }
  return <div>Content</div>;
}

// ❌ 错误：在循环中使用useState
function MyComponent() {
  for (let i = 0; i < 3; i++) {
    const [value, setValue] = useState(i); // 违反Hook规则！
  }
  return <div>Content</div>;
}

// ✅ 正确：在组件顶层使用useState
function MyComponent() {
  const [count, setCount] = useState(0);
  const [name, setName] = useState('');
  
  // 条件逻辑在Hook调用之后
  if (someCondition) {
    // 使用状态而不是声明状态
    setCount(prev => prev + 1);
  }
  
  return <div>{count}</div>;
}`,
    tags: ['Hook规则', '使用错误'],
    relatedQuestions: ['为什么React要有Hook规则？', '如何避免Hook规则错误？']
  },
  {
    id: 'performance-issue',
    question: 'useState更新后为什么状态值没有立即改变？',
    answer: 'useState的状态更新是异步的，调用setState后不会立即更新状态值，而是会在下一次渲染时生效。这是React的设计机制，用于性能优化和批处理。如果需要基于最新状态进行计算，应该使用函数式更新或useEffect来监听状态变化。',
    code: `function Counter() {
  const [count, setCount] = useState(0);

  const handleClick = () => {
    console.log('点击前:', count); // 输出: 0
    setCount(count + 1);
    console.log('点击后:', count); // 仍然输出: 0（未立即更新）
    
    // ❌ 错误：基于旧状态值进行多次更新
    setCount(count + 1); // 实际上还是 0 + 1 = 1
    setCount(count + 1); // 还是 0 + 1 = 1，不是期望的3
  };

  const handleClickCorrect = () => {
    // ✅ 正确：使用函数式更新
    setCount(prev => prev + 1); // 1
    setCount(prev => prev + 1); // 2
    setCount(prev => prev + 1); // 3
  };

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={handleClick}>错误方式</button>
      <button onClick={handleClickCorrect}>正确方式</button>
    </div>
  );
}`,
    tags: ['异步更新', '函数式更新'],
    relatedQuestions: ['如何获取useState的最新值？', '什么是React的批处理机制？']
  },
  {
    id: 'compatibility-issue',
    question: '在React 18中useState的批处理行为有什么变化？',
    answer: 'React 18引入了自动批处理(Automatic Batching)，这意味着所有状态更新都会被自动批处理，包括在Promise、setTimeout等异步操作中的更新。在React 17中，只有React事件处理程序中的更新会被批处理。这提高了性能，但可能会改变某些组件的行为。',
    code: `function BatchingExample() {
  const [count, setCount] = useState(0);
  const [flag, setFlag] = useState(false);

  // React 18: 以下所有更新都会被批处理
  const handleClick = () => {
    setCount(c => c + 1);
    setFlag(f => !f);
    // 在React 18中，这两个更新会被批处理，只触发一次重渲染
  };

  const handleAsyncClick = () => {
    setTimeout(() => {
      setCount(c => c + 1);
      setFlag(f => !f);
      // React 18: 异步操作中的更新也会被批处理
      // React 17: 这会触发两次重渲染
    }, 1000);
  };

  // 如果需要强制同步更新（不推荐）
  const handleSyncClick = () => {
    ReactDOM.flushSync(() => {
      setCount(c => c + 1);
    });
    // 这里count已经更新
    ReactDOM.flushSync(() => {
      setFlag(f => !f);
    });
    // 这里flag也已经更新
  };

  console.log('渲染次数:', ++renderCount); // 用于观察重渲染次数

  return (
    <div>
      <p>Count: {count}</p>
      <p>Flag: {flag.toString()}</p>
      <button onClick={handleClick}>批处理更新</button>
      <button onClick={handleAsyncClick}>异步批处理</button>
    </div>
  );
}`,
    tags: ['React 18', '批处理', '兼容性'],
    relatedQuestions: ['如何禁用自动批处理？', 'flushSync的使用场景是什么？']
  },
  {
    id: 'debugging-difficulty',
    question: '如何调试useState的状态更新问题？',
    answer: '调试useState问题可以使用多种方法：1) 使用React DevTools查看状态变化；2) 添加console.log记录状态更新；3) 使用useEffect监听状态变化；4) 检查是否违反了不可变更新原则；5) 确认组件的依赖数组是否正确设置。',
    code: `function DebuggingExample() {
  const [user, setUser] = useState({ name: '', age: 0 });
  const [items, setItems] = useState([]);

  // 🔍 调试技巧1: 使用useEffect监听状态变化
  useEffect(() => {
    console.log('用户状态更新:', user);
  }, [user]);

  // 🔍 调试技巧2: 在状态更新处添加日志
  const updateUser = (newName) => {
    console.log('更新前的用户:', user);
    setUser(prev => {
      const updated = { ...prev, name: newName };
      console.log('更新后的用户:', updated);
      return updated;
    });
  };

  // ❌ 常见错误：直接修改状态对象
  const updateUserWrong = (newName) => {
    user.name = newName; // 直接修改，不会触发重渲染
    setUser(user); // 引用没变，React认为没有更新
  };

  // ✅ 正确方式：创建新对象
  const updateUserCorrect = (newName) => {
    setUser(prev => ({ ...prev, name: newName }));
  };

  // 🔍 调试技巧3: 使用自定义Hook包装状态
  const useDebugState = (initialValue, name) => {
    const [value, setValue] = useState(initialValue);
    
    const debugSetValue = useCallback((newValue) => {
      console.log(name + ' 状态变化:', value, '->', newValue);
      setValue(newValue);
    }, [value, name]);
    
    return [value, debugSetValue];
  };

  const [debugCount, setDebugCount] = useDebugState(0, 'count');

  return (
    <div>
      <p>用户: {user.name} ({user.age}岁)</p>
      <p>调试计数: {debugCount}</p>
      <button onClick={() => updateUser('张三')}>更新姓名</button>
      <button onClick={() => setDebugCount(prev => prev + 1)}>增加计数</button>
    </div>
  );
}

// 🔍 调试技巧4: 使用React DevTools
// 安装React DevTools浏览器扩展，可以实时查看组件状态

// 🔍 调试技巧5: 性能分析
function PerformanceDebug() {
  const [expensiveValue, setExpensiveValue] = useState(() => {
    console.log('昂贵的初始化计算'); // 应该只执行一次
    return heavyCalculation();
  });

  // 检查是否有不必要的重渲染
  const renderCount = useRef(0);
  renderCount.current += 1;
  console.log('组件重渲染次数:', renderCount.current);

  return <div>Value: {expensiveValue}</div>;
}`,
    tags: ['调试技巧', 'React DevTools'],
    relatedQuestions: ['如何使用React DevTools调试？', '为什么我的组件重复渲染？']
  },
  {
    id: 'best-practice',
    question: '使用useState的最佳实践有哪些？',
    answer: '使用useState的最佳实践包括：1) 保持状态结构简单和扁平；2) 相关状态可以合并为对象；3) 使用函数式更新避免闭包陷阱；4) 对象和数组更新要创建新引用；5) 昂贵初始化使用函数形式；6) 避免在渲染期间调用setState；7) 合理使用useCallback缓存事件处理函数。',
    code: `// ✅ 最佳实践示例

// 1. 状态结构设计：相关状态合并，无关状态分离
function UserProfile() {
  // ✅ 相关的用户信息合并为对象
  const [user, setUser] = useState({
    name: '',
    email: '',
    avatar: ''
  });
  
  // ✅ 独立的UI状态单独管理
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 2. ✅ 对象状态的正确更新方式
  const updateUserField = useCallback((field, value) => {
    setUser(prev => ({
      ...prev,  // 保留其他字段
      [field]: value  // 只更新指定字段
    }));
  }, []);

  // 3. ✅ 数组状态的正确更新方式
  const [todos, setTodos] = useState([]);
  
  const addTodo = useCallback((text) => {
    setTodos(prev => [...prev, { id: Date.now(), text, completed: false }]);
  }, []);

  const toggleTodo = useCallback((id) => {
    setTodos(prev => 
      prev.map(todo => 
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
      )
    );
  }, []);

  const removeTodo = useCallback((id) => {
    setTodos(prev => prev.filter(todo => todo.id !== id));
  }, []);

  // 4. ✅ 昂贵初始化的正确方式
  const [complexData, setComplexData] = useState(() => {
    // 这个函数只在初始化时执行一次
    console.log('执行昂贵的初始化计算');
    return processLargeDataSet();
  });

  // 5. ✅ 表单处理的最佳实践
  const [form, setForm] = useState({
    username: '',
    password: '',
    confirmPassword: ''
  });

  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setForm(prev => ({
      ...prev,
      [name]: value
    }));
  }, []);

  // 6. ✅ 异步操作的状态管理
  const fetchUserData = useCallback(async (userId) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.getUser(userId);
      setUser(response.data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  // 7. ✅ 条件渲染和状态重置
  const [showModal, setShowModal] = useState(false);
  const [modalData, setModalData] = useState(null);

  const openModal = useCallback((data) => {
    setModalData(data);
    setShowModal(true);
  }, []);

  const closeModal = useCallback(() => {
    setShowModal(false);
    // 清理modal相关状态
    setModalData(null);
  }, []);

  return (
    <div>
      {loading && <div>加载中...</div>}
      {error && <div>错误: {error}</div>}
      
      <form>
        <input
          name="username"
          value={form.username}
          onChange={handleInputChange}
          placeholder="用户名"
        />
        <input
          name="password"
          type="password"
          value={form.password}
          onChange={handleInputChange}
          placeholder="密码"
        />
      </form>

      <div>
        {todos.map(todo => (
          <div key={todo.id}>
            <span 
              style={{ 
                textDecoration: todo.completed ? 'line-through' : 'none' 
              }}
              onClick={() => toggleTodo(todo.id)}
            >
              {todo.text}
            </span>
            <button onClick={() => removeTodo(todo.id)}>删除</button>
          </div>
        ))}
      </div>

      {showModal && (
        <Modal data={modalData} onClose={closeModal} />
      )}
    </div>
  );
}

// ❌ 常见反模式
function BadPractices() {
  const [count, setCount] = useState(0);

  // ❌ 不要直接修改状态
  const badIncrement = () => {
    count++; // 直接修改，不会触发重渲染
    setCount(count);
  };

  // ❌ 不要在渲染期间调用setState
  if (count > 10) {
    setCount(0); // 会导致无限循环
  }

  // ❌ 不要忽视函数式更新的好处
  const badDoubleIncrement = () => {
    setCount(count + 1);
    setCount(count + 1); // 第二次调用基于旧值，结果只增加1
  };

  return <div>{count}</div>;
}`,
    tags: ['最佳实践', '性能优化'],
    relatedQuestions: ['如何避免useState的性能问题？', '什么时候应该使用useReducer？']
  }
];

export default commonQuestions; 