import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 'scenario-1',
    title: '实现用户反馈表单',
    description: '展示useState最基础的使用方式，适合初学者快速理解状态管理的核心概念',
    businessValue: '提供用户反馈渠道，提升产品用户体验和满意度',
    scenario: '构建一个简单的反馈表单，用户可以输入姓名、邮箱和反馈内容，实时显示字符计数，并提供表单重置功能。这是典型的表单状态管理场景，展示了useState在处理用户输入和界面状态同步方面的基础应用。',
    code: `import React, { useState } from 'react';

function FeedbackForm() {
  // 管理表单输入状态
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [feedback, setFeedback] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  // 处理表单提交
  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('反馈信息:', { name, email, feedback });
    setIsSubmitted(true);
    
    // 3秒后自动重置
    setTimeout(() => {
      setName('');
      setEmail('');
      setFeedback('');
      setIsSubmitted(false);
    }, 3000);
  };

  // 重置表单
  const handleReset = () => {
    setName('');
    setEmail('');
    setFeedback('');
    setIsSubmitted(false);
  };

  if (isSubmitted) {
    return (
      <div className="feedback-success">
        <h3>感谢您的反馈！</h3>
        <p>我们已收到您的意见，将尽快处理。</p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="feedback-form">
      <h2>用户反馈表单</h2>
      
      <div className="form-group">
        <label htmlFor="name">姓名：</label>
        <input
          id="name"
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="请输入您的姓名"
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="email">邮箱：</label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="请输入您的邮箱"
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="feedback">反馈内容：</label>
        <textarea
          id="feedback"
          value={feedback}
          onChange={(e) => setFeedback(e.target.value)}
          placeholder="请输入您的反馈内容"
          rows={4}
          required
        />
        <div className="char-count">
          字符数: {feedback.length}/500
        </div>
      </div>

      <div className="form-actions">
        <button type="submit" disabled={!name || !email || !feedback}>
          提交反馈
        </button>
        <button type="button" onClick={handleReset}>
          重置表单
        </button>
      </div>
    </form>
  );
}

export default FeedbackForm;`,
    explanation: '这个场景展示了useState管理多个独立状态的基础用法。每个输入框都有自己的状态变量，通过onChange事件实时更新。同时展示了条件渲染、表单验证和状态重置的常见模式。重点演示了状态的独立性和组合使用。',
    benefits: [
      '代码结构清晰，每个状态职责单一',
      '实时状态更新，用户体验良好',
      '包含表单验证和错误处理机制'
    ],
    metrics: {
      performance: '组件渲染性能优秀，每次输入只更新对应状态',
      userExperience: '实时反馈和字符计数提升用户体验',
      technicalMetrics: '代码量少，维护成本低，适合快速开发'
    },
    difficulty: 'easy',
    tags: ['表单管理', '状态同步', '用户输入', '条件渲染']
  },
  {
    id: 'scenario-2',
    title: '构建智能购物车系统',
    description: '在电商场景中实现购物车的完整功能，包括商品管理、价格计算和优惠券应用',
    businessValue: '提升电商转化率，通过智能价格计算和优惠券功能增加用户购买意愿',
    scenario: '开发一个功能完整的购物车组件，支持商品添加删除、数量调整、价格计算、优惠券使用等功能。需要处理复杂的状态逻辑，包括商品列表、总价计算、优惠券状态等。这个场景展示了useState在处理复杂业务逻辑时的应用模式。',
    code: `import React, { useState, useEffect } from 'react';

// 商品数据类型
interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  stock: number;
}

// 购物车项类型
interface CartItem extends Product {
  quantity: number;
}

// 优惠券类型
interface Coupon {
  code: string;
  discount: number;
  minAmount: number;
}

function ShoppingCart() {
  // 购物车状态管理
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [couponCode, setCouponCode] = useState('');
  const [appliedCoupon, setAppliedCoupon] = useState<Coupon | null>(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  // 可用优惠券
  const availableCoupons: Coupon[] = [
    { code: 'SAVE10', discount: 10, minAmount: 100 },
    { code: 'SAVE20', discount: 20, minAmount: 200 },
    { code: 'SAVE50', discount: 50, minAmount: 500 }
  ];

  // 计算小计
  const subtotal = cartItems.reduce((total, item) => 
    total + (item.price * item.quantity), 0
  );

  // 计算优惠金额
  const discountAmount = appliedCoupon ? 
    Math.min(appliedCoupon.discount, subtotal * 0.5) : 0;

  // 计算总金额
  const totalAmount = subtotal - discountAmount;

  // 添加商品到购物车
  const addToCart = (product: Product, quantity: number = 1) => {
    setCartItems(prev => {
      const existingItem = prev.find(item => item.id === product.id);
      if (existingItem) {
        // 更新数量
        return prev.map(item =>
          item.id === product.id
            ? { ...item, quantity: Math.min(item.quantity + quantity, item.stock) }
            : item
        );
      } else {
        // 添加新商品
        return [...prev, { ...product, quantity }];
      }
    });
    setMessage('商品已添加到购物车');
    setTimeout(() => setMessage(''), 2000);
  };

  // 更新商品数量
  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId);
      return;
    }

    setCartItems(prev =>
      prev.map(item =>
        item.id === productId
          ? { ...item, quantity: Math.min(newQuantity, item.stock) }
          : item
      )
    );
  };

  // 从购物车移除商品
  const removeFromCart = (productId: string) => {
    setCartItems(prev => prev.filter(item => item.id !== productId));
  };

  // 应用优惠券
  const applyCoupon = () => {
    const coupon = availableCoupons.find(c => c.code === couponCode.toUpperCase());
    
    if (!coupon) {
      setMessage('优惠券代码无效');
      setTimeout(() => setMessage(''), 2000);
      return;
    }

    if (subtotal < coupon.minAmount) {
      setMessage('订单金额不满足优惠券使用条件，最低消费：¥' + coupon.minAmount);
      setTimeout(() => setMessage(''), 3000);
      return;
    }

    setAppliedCoupon(coupon);
    setMessage('优惠券应用成功！');
    setTimeout(() => setMessage(''), 2000);
  };

  // 移除优惠券
  const removeCoupon = () => {
    setAppliedCoupon(null);
    setCouponCode('');
  };

  // 结算处理
  const handleCheckout = async () => {
    if (cartItems.length === 0) {
      setMessage('购物车是空的');
      return;
    }

    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 清空购物车
      setCartItems([]);
      setAppliedCoupon(null);
      setCouponCode('');
      setMessage('订单提交成功！');
    } catch (error) {
      setMessage('订单提交失败，请重试');
    } finally {
      setLoading(false);
      setTimeout(() => setMessage(''), 3000);
    }
  };

  return (
    <div className="shopping-cart">
      <h2>购物车 ({cartItems.length} 件商品)</h2>
      
      {message && (
        <div className="message">{message}</div>
      )}

      <div className="cart-content">
        {cartItems.length === 0 ? (
          <div className="empty-cart">
            <p>购物车是空的</p>
          </div>
        ) : (
          <>
            {/* 商品列表 */}
            <div className="cart-items">
              {cartItems.map(item => (
                <div key={item.id} className="cart-item">
                  <img src={item.image} alt={item.name} />
                  <div className="item-details">
                    <h4>{item.name}</h4>
                    <p>¥{item.price}</p>
                  </div>
                  <div className="quantity-controls">
                    <button onClick={() => updateQuantity(item.id, item.quantity - 1)}>
                      -
                    </button>
                    <span>{item.quantity}</span>
                    <button onClick={() => updateQuantity(item.id, item.quantity + 1)}>
                      +
                    </button>
                  </div>
                  <div className="item-total">
                    ¥{item.price * item.quantity}
                  </div>
                  <button onClick={() => removeFromCart(item.id)}>
                    删除
                  </button>
                </div>
              ))}
            </div>

            {/* 优惠券区域 */}
            <div className="coupon-section">
              <h3>优惠券</h3>
              <div className="coupon-input">
                <input
                  type="text"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                  placeholder="输入优惠券代码"
                />
                <button onClick={applyCoupon}>使用</button>
              </div>
              
              {appliedCoupon && (
                <div className="applied-coupon">
                  <span>已使用优惠券: {appliedCoupon.code} (-¥{discountAmount})</span>
                  <button onClick={removeCoupon}>移除</button>
                </div>
              )}
            </div>

            {/* 价格汇总 */}
            <div className="price-summary">
              <div className="price-row">
                <span>小计:</span>
                <span>¥{subtotal}</span>
              </div>
              {appliedCoupon && (
                <div className="price-row discount">
                  <span>优惠:</span>
                  <span>-¥{discountAmount}</span>
                </div>
              )}
              <div className="price-row total">
                <strong>总计: ¥{totalAmount}</strong>
              </div>
            </div>

            {/* 结算按钮 */}
            <button 
              className="checkout-btn"
              onClick={handleCheckout}
              disabled={loading}
            >
              {loading ? '处理中...' : '立即结算'}
            </button>
          </>
        )}
      </div>
    </div>
  );
}

export default ShoppingCart;`,
    explanation: '这个场景展示了useState在复杂业务逻辑中的应用。通过多个状态变量管理购物车的不同方面：商品列表、优惠券、加载状态等。重点演示了状态之间的关联计算、条件逻辑处理和异步操作处理。使用了数组和对象状态的更新模式，体现了不可变更新的重要性。',
    benefits: [
      '完整的电商购物车功能，用户体验优秀',
      '支持复杂的价格计算和优惠券逻辑',
      '包含错误处理和用户反馈机制',
      '代码结构清晰，便于维护和扩展'
    ],
    metrics: {
      performance: '通过合理的状态设计避免不必要的重渲染',
      userExperience: '实时价格计算和即时反馈提升用户体验',
      technicalMetrics: '支持复杂业务逻辑，代码可维护性良好'
    },
    difficulty: 'medium',
    tags: ['电商应用', '复杂状态', '价格计算', '异步处理']
  },
  {
    id: 'scenario-3',
    title: '企业级数据大屏监控系统',
    description: '构建实时数据监控大屏，包含多维度数据展示、自动刷新和告警功能',
    businessValue: '为企业提供实时业务监控能力，及时发现异常情况，提升运营效率',
    scenario: '开发一个企业级的实时数据监控大屏系统，需要展示多个业务指标、支持自动刷新、历史数据对比、告警通知等功能。系统需要处理大量状态：实时数据、图表配置、刷新控制、告警状态等。这个场景展示了useState在大型应用中的状态管理模式和性能优化策略。',
    code: `import React, { useState, useEffect, useCallback, useMemo } from 'react';

// 业务指标数据类型
interface MetricData {
  id: string;
  name: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  change: number;
  threshold: {
    warning: number;
    critical: number;
  };
}

// 告警信息类型
interface Alert {
  id: string;
  metric: string;
  level: 'warning' | 'critical';
  message: string;
  timestamp: Date;
  acknowledged: boolean;
}

// 图表配置类型
interface ChartConfig {
  id: string;
  title: string;
  type: 'line' | 'bar' | 'pie';
  dataKey: string;
  visible: boolean;
  position: { x: number; y: number };
}

function DataDashboard() {
  // 核心数据状态
  const [metricsData, setMetricsData] = useState<MetricData[]>([]);
  const [historicalData, setHistoricalData] = useState<Record<string, number[]>>({});
  const [alerts, setAlerts] = useState<Alert[]>([]);
  
  // 界面控制状态
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // 秒
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 布局和展示状态
  const [chartConfigs, setChartConfigs] = useState<ChartConfig[]>([]);
  const [selectedTimeRange, setSelectedTimeRange] = useState('1h');
  const [showAlerts, setShowAlerts] = useState(true);
  const [fullScreenChart, setFullScreenChart] = useState<string | null>(null);
  
  // 用户交互状态
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMetrics, setSelectedMetrics] = useState<Set<string>>(new Set());
  const [draggedChart, setDraggedChart] = useState<string | null>(null);

  // 模拟API数据获取
  const fetchMetricsData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 生成模拟数据
      const mockData: MetricData[] = [
        {
          id: 'sales',
          name: '今日销售额',
          value: Math.floor(Math.random() * 1000000) + 500000,
          unit: '元',
          trend: Math.random() > 0.5 ? 'up' : 'down',
          change: (Math.random() - 0.5) * 20,
          threshold: { warning: 300000, critical: 200000 }
        },
        {
          id: 'orders',
          name: '订单数量',
          value: Math.floor(Math.random() * 5000) + 2000,
          unit: '笔',
          trend: Math.random() > 0.5 ? 'up' : 'down',
          change: (Math.random() - 0.5) * 15,
          threshold: { warning: 1000, critical: 500 }
        },
        {
          id: 'users',
          name: '在线用户',
          value: Math.floor(Math.random() * 10000) + 5000,
          unit: '人',
          trend: Math.random() > 0.5 ? 'up' : 'down',
          change: (Math.random() - 0.5) * 10,
          threshold: { warning: 3000, critical: 1000 }
        },
        {
          id: 'performance',
          name: '系统性能',
          value: Math.floor(Math.random() * 100) + 50,
          unit: '%',
          trend: Math.random() > 0.5 ? 'up' : 'down',
          change: (Math.random() - 0.5) * 5,
          threshold: { warning: 70, critical: 50 }
        }
      ];

      setMetricsData(mockData);
      setLastUpdateTime(new Date());
      
      // 更新历史数据
      setHistoricalData(prev => {
        const updated = { ...prev };
        mockData.forEach(metric => {
          if (!updated[metric.id]) {
            updated[metric.id] = [];
          }
          updated[metric.id] = [
            ...updated[metric.id].slice(-29), // 保留最近30个数据点
            metric.value
          ];
        });
        return updated;
      });

      // 检查告警
      checkAlerts(mockData);
      
    } catch (err) {
      setError('数据获取失败');
      console.error('Error fetching metrics:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 告警检查逻辑
  const checkAlerts = useCallback((data: MetricData[]) => {
    const newAlerts: Alert[] = [];
    
    data.forEach(metric => {
      if (metric.value <= metric.threshold.critical) {
        newAlerts.push({
          id: Math.random().toString(36).substr(2, 9),
          metric: metric.name,
          level: 'critical',
          message: metric.name + '已达到危险阈值：' + metric.value + metric.unit,
          timestamp: new Date(),
          acknowledged: false
        });
      } else if (metric.value <= metric.threshold.warning) {
        newAlerts.push({
          id: Math.random().toString(36).substr(2, 9),
          metric: metric.name,
          level: 'warning',
          message: metric.name + '已达到警告阈值：' + metric.value + metric.unit,
          timestamp: new Date(),
          acknowledged: false
        });
      }
    });

    if (newAlerts.length > 0) {
      setAlerts(prev => [...newAlerts, ...prev].slice(0, 50)); // 保留最近50条告警
    }
  }, []);

  // 自动刷新逻辑
  useEffect(() => {
    if (isAutoRefresh) {
      const interval = setInterval(fetchMetricsData, refreshInterval * 1000);
      return () => clearInterval(interval);
    }
  }, [isAutoRefresh, refreshInterval, fetchMetricsData]);

  // 初始化数据
  useEffect(() => {
    fetchMetricsData();
    
    // 初始化图表配置
    setChartConfigs([
      { id: 'sales-chart', title: '销售趋势', type: 'line', dataKey: 'sales', visible: true, position: { x: 0, y: 0 } },
      { id: 'orders-chart', title: '订单统计', type: 'bar', dataKey: 'orders', visible: true, position: { x: 1, y: 0 } },
      { id: 'users-chart', title: '用户分布', type: 'pie', dataKey: 'users', visible: true, position: { x: 0, y: 1 } },
      { id: 'performance-chart', title: '性能监控', type: 'line', dataKey: 'performance', visible: true, position: { x: 1, y: 1 } }
    ]);
  }, [fetchMetricsData]);

  // 筛选后的指标数据
  const filteredMetrics = useMemo(() => {
    if (!searchTerm) return metricsData;
    return metricsData.filter(metric => 
      metric.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [metricsData, searchTerm]);

  // 未确认的告警数量
  const unacknowledgedAlerts = useMemo(() => 
    alerts.filter(alert => !alert.acknowledged).length,
    [alerts]
  );

  // 确认告警
  const acknowledgeAlert = useCallback((alertId: string) => {
    setAlerts(prev => 
      prev.map(alert => 
        alert.id === alertId 
          ? { ...alert, acknowledged: true }
          : alert
      )
    );
  }, []);

  // 切换指标选择
  const toggleMetricSelection = useCallback((metricId: string) => {
    setSelectedMetrics(prev => {
      const updated = new Set(prev);
      if (updated.has(metricId)) {
        updated.delete(metricId);
      } else {
        updated.add(metricId);
      }
      return updated;
    });
  }, []);

  // 更新图表配置
  const updateChartConfig = useCallback((chartId: string, updates: Partial<ChartConfig>) => {
    setChartConfigs(prev =>
      prev.map(config =>
        config.id === chartId
          ? { ...config, ...updates }
          : config
      )
    );
  }, []);

  // 导出数据
  const exportData = useCallback(() => {
    const dataToExport = {
      metrics: metricsData,
      alerts: alerts,
      timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'dashboard-data-' + new Date().toISOString().split('T')[0] + '.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [metricsData, alerts]);

  return (
    <div className="data-dashboard">
      {/* 头部控制栏 */}
      <div className="dashboard-header">
        <div className="header-left">
          <h1>企业监控大屏</h1>
          <div className="last-update">
            最后更新: {lastUpdateTime.toLocaleTimeString()}
          </div>
        </div>
        
        <div className="header-controls">
          <div className="search-box">
            <input
              type="text"
              placeholder="搜索指标..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="auto-refresh">
            <label>
              <input
                type="checkbox"
                checked={isAutoRefresh}
                onChange={(e) => setIsAutoRefresh(e.target.checked)}
              />
              自动刷新
            </label>
            <select
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(Number(e.target.value))}
              disabled={!isAutoRefresh}
            >
              <option value={10}>10秒</option>
              <option value={30}>30秒</option>
              <option value={60}>1分钟</option>
              <option value={300}>5分钟</option>
            </select>
          </div>
          
          <button onClick={fetchMetricsData} disabled={loading}>
            {loading ? '刷新中...' : '手动刷新'}
          </button>
          
          <button onClick={exportData}>
            导出数据
          </button>
        </div>
      </div>

      {/* 告警通知栏 */}
      {showAlerts && unacknowledgedAlerts > 0 && (
        <div className="alerts-bar">
          <div className="alerts-summary">
            <span className="alert-count">{unacknowledgedAlerts}</span>
            条未处理告警
          </div>
          <div className="recent-alerts">
            {alerts.slice(0, 3).map(alert => (
              <div key={alert.id} className={'alert-item ' + alert.level}>
                <span>{alert.message}</span>
                <button onClick={() => acknowledgeAlert(alert.id)}>
                  确认
                </button>
              </div>
            ))}
          </div>
          <button onClick={() => setShowAlerts(false)}>
            隐藏
          </button>
        </div>
      )}

      {/* 主要内容区域 */}
      <div className="dashboard-content">
        {error && (
          <div className="error-message">
            {error}
            <button onClick={() => setError(null)}>关闭</button>
          </div>
        )}

        {/* 指标卡片网格 */}
        <div className="metrics-grid">
          {filteredMetrics.map(metric => (
            <div 
              key={metric.id} 
              className={'metric-card ' + metric.trend + (selectedMetrics.has(metric.id) ? ' selected' : '')}
              onClick={() => toggleMetricSelection(metric.id)}
            >
              <div className="metric-header">
                <h3>{metric.name}</h3>
                <div className={'trend-indicator ' + metric.trend}>
                  {metric.trend === 'up' ? '↗' : metric.trend === 'down' ? '↘' : '→'}
                </div>
              </div>
              
              <div className="metric-value">
                <span className="value">{metric.value.toLocaleString()}</span>
                <span className="unit">{metric.unit}</span>
              </div>
              
              <div className="metric-change">
                <span className={metric.change >= 0 ? 'positive' : 'negative'}>
                  {metric.change >= 0 ? '+' : ''}{metric.change.toFixed(1)}%
                </span>
                <span className="period">vs 昨日</span>
              </div>
              
              {/* 简化的历史趋势线 */}
              <div className="mini-chart">
                {historicalData[metric.id]?.slice(-10).map((value, index) => (
                  <div 
                    key={index}
                    className="chart-bar"
                    style={{ 
                      height: (value / Math.max(...(historicalData[metric.id] || [1])) * 100) + '%' 
                    }}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* 图表展示区域 */}
        <div className="charts-section">
          <div className="section-header">
            <h2>数据图表</h2>
            <div className="chart-controls">
              <select
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange(e.target.value)}
              >
                <option value="1h">最近1小时</option>
                <option value="24h">最近24小时</option>
                <option value="7d">最近7天</option>
                <option value="30d">最近30天</option>
              </select>
            </div>
          </div>
          
          <div className="charts-grid">
            {chartConfigs.filter(config => config.visible).map(config => (
              <div key={config.id} className="chart-container">
                <div className="chart-header">
                  <h4>{config.title}</h4>
                  <div className="chart-actions">
                    <button 
                      onClick={() => setFullScreenChart(config.id)}
                      title="全屏显示"
                    >
                      ⛶
                    </button>
                    <button 
                      onClick={() => updateChartConfig(config.id, { visible: false })}
                      title="隐藏图表"
                    >
                      ✕
                    </button>
                  </div>
                </div>
                
                {/* 这里应该是实际的图表组件，此处用占位符表示 */}
                <div className="chart-placeholder">
                  <div className="chart-type">{config.type.toUpperCase()} 图表</div>
                  <div className="chart-data">
                    数据: {historicalData[config.dataKey]?.slice(-5).join(', ') || '暂无数据'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 全屏图表模态框 */}
      {fullScreenChart && (
        <div className="fullscreen-modal">
          <div className="modal-header">
            <h2>
              {chartConfigs.find(c => c.id === fullScreenChart)?.title}
            </h2>
            <button onClick={() => setFullScreenChart(null)}>
              关闭
            </button>
          </div>
          <div className="modal-content">
            <div className="fullscreen-chart">
              全屏图表内容
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default DataDashboard;`,
    explanation: '这个企业级场景展示了useState在大型应用中的高级应用模式。通过合理的状态分层设计，分别管理数据状态、界面状态和用户交互状态。重点演示了性能优化技巧：使用useMemo缓存计算结果、useCallback防止不必要的重渲染、状态更新的不可变模式。同时展示了复杂的业务逻辑处理：实时数据刷新、告警机制、数据导出等功能。',
    benefits: [
      '完整的企业级监控系统，功能丰富且实用',
      '采用模块化状态设计，便于维护和扩展',
      '包含性能优化策略，支持大量数据处理',
      '提供完整的用户交互和错误处理机制',
      '支持数据导出和自定义配置功能'
    ],
    metrics: {
      performance: '通过合理的状态设计和memo优化，支持大量数据的高效渲染',
      userExperience: '实时更新、告警通知和交互反馈提供优秀的用户体验',
      technicalMetrics: '模块化架构，代码可维护性强，支持功能扩展和定制'
    },
    difficulty: 'hard',
    tags: ['企业应用', '实时监控', '大数据', '性能优化', '复杂交互']
  }
];

export default businessScenarios; 