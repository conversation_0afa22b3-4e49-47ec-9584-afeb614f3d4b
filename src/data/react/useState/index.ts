import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

/**
 * useState API 完整文档
 * 
 * 📋 基本信息：
 * - 框架：React
 * - 分类：Hooks
 * - 难度：Easy
 * - 版本：16.8+
 * 
 * 🎯 学习路径：
 * 1. 基本信息 - 了解语法和基础概念
 * 2. 业务场景 - 掌握实际应用方法
 * 3. 原理解析 - 理解底层实现机制
 * 4. 面试准备 - 准备技术面试问题
 * 5. 常见问题 - 解决开发中的困惑
 * 6. 知识考古 - 了解历史背景
 * 7. 性能优化 - 掌握性能调优技巧
 * 8. 调试技巧 - 学会问题排查方法
 * 9. 本质洞察 - 获得深层理解
 */

const useStateData: ApiItem = {
  // 基础配置
  id: 'use-state',
  title: 'useState()',
  category: 'React Hooks',
  description: 'useState是React Hooks体系中状态管理的基础组件，专门用于在函数组件中添加状态管理功能，解决了函数组件无法保存状态的根本问题，其核心优势是简单直观且性能高效。',
  difficulty: 'easy',
  version: 'React 16.8+',
  tags: ['状态管理', '函数组件', 'Hook', '响应式', '基础Hook', '类型安全'],
  
  // 语法和示例
  syntax: 'function useState<S>(initialState: S | (() => S)): [S, Dispatch<SetStateAction<S>>];',
  example: `function CounterExample() {
  // useState 返回状态值和更新函数
  const [count, setCount] = useState(0);

  return (
    <div>
      {/* 显示当前状态值 */}
      <p>当前计数: {count}</p>
      
      {/* 点击按钮更新状态 */}
      <button onClick={() => setCount(count + 1)}>
        增加
      </button>
      
      {/* 使用函数形式更新 */}
      <button onClick={() => setCount(prev => prev - 1)}>
        减少
      </button>
    </div>
  );
}`,
  notes: '状态更新是异步的，无法立即获取更新后的值',
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useStateData; 