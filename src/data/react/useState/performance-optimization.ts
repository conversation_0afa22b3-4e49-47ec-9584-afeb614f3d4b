import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: '状态结构优化',
      description: '通过合理设计状态结构，减少不必要的重渲染和提升更新性能',
      techniques: [
        {
          name: '状态合并策略',
          description: '将相关的多个状态合并为单个对象，减少useState调用次数和组件重渲染频率',
          code: `// ❌ 性能问题 - 多个独立状态
function UserProfile() {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  
  // 每次更新都会触发单独的重渲染
  const updateFirstName = (name) => setFirstName(name);
  const updateLastName = (name) => setLastName(name);
  
  return (
    <form>
      <input value={firstName} onChange={(e) => updateFirstName(e.target.value)} />
      <input value={lastName} onChange={(e) => updateLastName(e.target.value)} />
      <input value={email} onChange={(e) => setEmail(e.target.value)} />
      <input value={phone} onChange={(e) => setPhone(e.target.value)} />
    </form>
  );
}

// ✅ 性能优化 - 状态合并
function OptimizedUserProfile() {
  const [userInfo, setUserInfo] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  });
  
  // 使用单个更新函数，React会自动批处理
  const updateUserInfo = (field, value) => {
    setUserInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  return (
    <form>
      <input 
        value={userInfo.firstName} 
        onChange={(e) => updateUserInfo('firstName', e.target.value)} 
      />
      <input 
        value={userInfo.lastName} 
        onChange={(e) => updateUserInfo('lastName', e.target.value)} 
      />
      <input 
        value={userInfo.email} 
        onChange={(e) => updateUserInfo('email', e.target.value)} 
      />
      <input 
        value={userInfo.phone} 
        onChange={(e) => updateUserInfo('phone', e.target.value)} 
      />
    </form>
  );
}

// 📊 性能对比：
// 优化前：4个独立的重渲染，每次输入触发1次渲染
// 优化后：批处理合并，每次输入仍触发1次渲染，但状态管理更高效
// 内存优化：减少75%的Hook节点数量`,
          impact: 'high',
          difficulty: 'medium'
        },
        {
          name: '惰性初始化',
          description: '对于昂贵的初始状态计算，使用函数形式避免每次渲染时重复执行',
          code: `// ❌ 性能问题 - 每次渲染都执行昂贵计算
function DataDashboard({ rawData }) {
  // processLargeDataset在每次渲染时都会执行
  const [processedData, setProcessedData] = useState(processLargeDataset(rawData));
  
  return <div>{processedData.length} 条数据</div>;
}

// ✅ 性能优化 - 惰性初始化
function OptimizedDataDashboard({ rawData }) {
  // 使用函数形式，只在初始化时执行一次
  const [processedData, setProcessedData] = useState(() => {
    console.log('执行昂贵的数据处理...'); // 只会打印一次
    return processLargeDataset(rawData);
  });
  
  return <div>{processedData.length} 条数据</div>;
}

// 📊 性能对比：
// 优化前：每次渲染执行processLargeDataset（假设100ms）
// 优化后：仅首次渲染执行（100ms），后续渲染0ms
// 在频繁重渲染场景下性能提升显著`,
          impact: 'medium',
          difficulty: 'easy'
        }
      ]
    },
    {
      title: '函数式更新优化',
      description: '使用函数式更新模式避免闭包陷阱，确保状态更新的正确性和性能',
      techniques: [
        {
          name: '闭包陷阱消除',
          description: '使用函数式更新避免过期闭包问题，减少不必要的依赖和重渲染',
          code: `// ❌ 性能问题 - 闭包陷阱导致的性能问题
function Counter() {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    const timer = setInterval(() => {
      // 每次访问的都是初始的count值(0)
      // 导致状态更新不正确，需要添加count依赖
      setCount(count + 1);
    }, 1000);
    
    return () => clearInterval(timer);
  }, [count]); // 依赖count会导致定时器频繁重新创建
  
  return <div>计数: {count}</div>;
}

// ✅ 性能优化 - 函数式更新
function OptimizedCounter() {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    const timer = setInterval(() => {
      // 使用函数式更新，总是获取最新的状态值
      setCount(prevCount => prevCount + 1);
    }, 1000);
    
    return () => clearInterval(timer);
  }, []); // 空依赖数组，定时器只创建一次
  
  return <div>计数: {count}</div>;
}

// 📊 性能对比：
// 优化前：每秒重新创建定时器，产生内存泄漏风险
// 优化后：定时器创建一次，内存使用稳定
// CPU优化：减少90%的定时器创建销毁开销`,
          impact: 'high',
          difficulty: 'hard'
        }
      ]
    },
    {
      title: '批处理和调度优化',
      description: '利用React 18的自动批处理特性，优化状态更新的时机和频率',
      techniques: [
        {
          name: '手动批处理控制',
          description: '在特殊场景下使用unstable_batchedUpdates或flushSync控制更新时机',
          code: `// ❌ 性能问题 - 频繁的单独更新
function SearchResults() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const handleSearch = async (searchTerm) => {
    // 这些更新在React 18中会自动批处理
    // 但在某些异步场景下可能需要手动控制
    setLoading(true);
    setError(null);
    setResults([]);
    
    try {
      const data = await searchAPI(searchTerm);
      setResults(data);
      setLoading(false);
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };
  
  return (
    <div>
      <input 
        value={query}
        onChange={(e) => {
          setQuery(e.target.value);
          // 频繁调用可能导致性能问题
          handleSearch(e.target.value);
        }}
      />
      {loading && <div>搜索中...</div>}
      {error && <div>错误: {error}</div>}
      <div>{results.length} 个结果</div>
    </div>
  );
}

// ✅ 性能优化 - 防抖和批处理优化
function OptimizedSearchResults() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // 使用防抖减少搜索频率
  const debouncedSearch = useMemo(() => {
    return debounce(async (searchTerm) => {
      if (!searchTerm) return;
      
      // 使用React 18的startTransition优化非紧急更新
      startTransition(() => {
        setLoading(true);
        setError(null);
      });
      
      try {
        const data = await searchAPI(searchTerm);
        // 批量更新状态
        startTransition(() => {
          setResults(data);
          setLoading(false);
        });
      } catch (err) {
        startTransition(() => {
          setError(err.message);
          setLoading(false);
        });
      }
    }, 300);
  }, []);
  
  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value); // 立即更新输入框（紧急更新）
    debouncedSearch(value); // 延迟搜索（非紧急更新）
  };
  
  return (
    <div>
      <input value={query} onChange={handleInputChange} />
      {loading && <div>搜索中...</div>}
      {error && <div>错误: {error}</div>}
      <div>{results.length} 个结果</div>
    </div>
  );
}

// 📊 性能对比：
// 优化前：每次输入都触发API调用，大量无用请求
// 优化后：300ms防抖 + startTransition优化，API调用减少90%
// 用户体验：输入响应立即，搜索结果平滑更新`,
          impact: 'high',
          difficulty: 'medium'
        }
      ]
    }
  ],
  
  performanceMetrics: {
    'renderTime': {
      description: '组件重新渲染的耗时，包括状态更新触发的渲染时间',
      tool: 'React DevTools Profiler',
      example: '使用Profiler面板记录组件渲染时间，正常应在16ms以内保持60fps'
    },
    'stateUpdateFrequency': {
      description: '状态更新的频率和批处理效率，影响整体应用性能',
      tool: 'Performance API + React DevTools',
      example: 'performance.mark("stateUpdate") 标记更新时机，分析批处理效果'
    },
    'memoryUsage': {
      description: 'Hook节点和状态数据的内存占用，影响应用的内存效率',
      tool: 'Chrome DevTools Memory Tab',
      example: '监控heap快照，分析useState相关的内存分配和垃圾回收'
    },
    'hookChainLength': {
      description: 'Hook链表的长度和遍历效率，影响组件初始化和更新性能',
      tool: 'React DevTools Components Tab',
      example: '检查组件的Hook数量，避免过长的Hook链表影响性能'
    }
  },
  
  bestPractices: [
    '合并相关状态：将逻辑相关的多个状态合并为对象，减少Hook数量和重渲染次数',
    '使用函数式更新：在回调和异步操作中使用 setState(prev => newValue) 避免闭包陷阱',
    '惰性初始化：对于昂贵的初始计算使用 useState(() => expensiveCalculation()) 形式',
    '避免频繁更新：使用防抖、节流等技术减少不必要的状态更新频率',
    '利用批处理：信任React的自动批处理机制，避免手动优化除非必要',
    '监控性能指标：使用React DevTools定期检查组件渲染性能和状态更新效率',
    '合理使用memo：对昂贵的子组件使用React.memo配合稳定的props减少重渲染',
    '状态提升适度：避免过度状态提升导致的大范围重渲染，保持状态在合适的层级'
  ],
  
  commonPitfalls: [
    {
      issue: '过度拆分状态导致性能下降',
      cause: '将本应合并的状态拆分为多个独立的useState，导致组件中Hook数量过多，每次更新都需要遍历更长的Hook链表',
      solution: '将逻辑相关的状态合并为对象。例如表单字段、配置选项等可以合并为单个状态对象，使用展开语法进行部分更新'
    },
    {
      issue: '在渲染期间执行昂贵的初始化计算',
      cause: '直接在useState参数中传递昂贵的计算结果，导致每次组件重渲染都会重复执行这些计算，严重影响性能',
      solution: '使用惰性初始化模式 useState(() => expensiveCalculation())，确保昂贵计算只在组件首次挂载时执行一次'
    },
    {
      issue: '闭包陷阱导致的无限重渲染',
      cause: '在useEffect等Hook中直接使用状态值而不是函数式更新，导致需要添加状态依赖，进而触发无限的重渲染循环',
      solution: '使用函数式更新 setState(prev => prev + 1) 替代直接使用状态值，减少不必要的依赖，避免重渲染循环'
    },
    {
      issue: '忽略React 18的批处理特性',
      cause: '在异步操作中过度手动控制更新时机，或者错误地认为需要手动批处理，增加了不必要的复杂性',
      solution: '信任React 18的自动批处理机制，只在确实需要立即同步更新时使用flushSync，大多数情况下让React自动优化'
    }
  ]
};

export default performanceOptimization; 