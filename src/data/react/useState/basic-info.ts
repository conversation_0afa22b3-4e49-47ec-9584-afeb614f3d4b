import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "useState是React Hooks体系中状态管理的基础组件，专门用于在函数组件中添加状态管理功能，解决了函数组件无法保存状态的根本问题，其核心优势是简单直观且性能高效。",
  
  introduction: `useState是为了解决函数组件无状态的历史问题而在React 16.8中引入的核心Hook。它遵循声明式编程理念，在简洁性和功能性之间做出了完美权衡。主要用于组件状态管理、用户交互响应和数据临时存储。相比类组件的this.state，它的创新在于状态的独立性和组合性。在React生态中，它是Hooks系统的基石，常见于所有需要状态的场景，特别适合简单状态管理的需求。核心优势包括API简洁、类型友好，但也需要注意闭包陷阱的问题。`,
  
  syntax: `function useState<S>(initialState: S | (() => S)): [S, Dispatch<SetStateAction<S>>];
function useState<S = undefined>(): [S | undefined, Dispatch<SetStateAction<S | undefined>>];`,

  quickExample: `function CounterExample() {
  // useState 返回状态值和更新函数
  const [count, setCount] = useState(0);

  return (
    <div>
      {/* 显示当前状态值 */}
      <p>当前计数: {count}</p>
      
      {/* 点击按钮更新状态 */}
      <button onClick={() => setCount(count + 1)}>
        增加
      </button>
      
      {/* 使用函数形式更新 */}
      <button onClick={() => setCount(prev => prev - 1)}>
        减少
      </button>
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "useState在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用这个Hook",
      diagram: `graph LR
    A[useState核心场景] --> B[用户交互状态]
    A --> C[表单数据管理]
    A --> D[组件内部状态]

    B --> B1["🎯 计数器<br/>按钮点击计数"]
    B --> B2["🔄 开关状态<br/>模态框显示隐藏"]

    C --> C1["📝 输入框值<br/>实时输入验证"]
    C --> C2["📋 表单字段<br/>多字段状态管理"]

    D --> D1["📊 加载状态<br/>数据请求进度"]
    D --> D2["🎨 主题切换<br/>界面外观控制"]

    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "状态更新机制",
      description: "useState的状态更新机制，展示同步更新、异步批处理和函数式更新的工作原理",
      diagram: `graph TB
    A[useState状态更新] --> B[直接值更新]
    A --> C[函数式更新]
    A --> D[批处理机制]

    B --> B1["🔄 setCount(5)<br/>直接设置新值"]
    B --> B2["⚡ setName('张三')<br/>替换字符串值"]

    C --> C1["📈 setCount(prev => prev + 1)<br/>基于当前值计算"]
    C --> C2["🛡️ 避免闭包陷阱<br/>获取最新状态"]

    D --> D1["🚀 React 18自动批处理<br/>多个setState合并"]
    D --> D2["⏱️ 同步更新时机<br/>渲染后生效"]

    style A fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    style B fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    style C fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    style D fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px`
    },
    {
      title: "Hook生态集成",
      description: "useState在React Hook生态系统中的位置和与其他Hook的协作关系",
      diagram: `graph TD
    A[React Hook生态] --> B[状态管理Hook]
    A --> C[副作用Hook]
    A --> D[性能优化Hook]

    B --> B1["useState<br/>基础状态"]
    B --> B2["useReducer<br/>复杂状态"]
    B --> B3["useContext<br/>共享状态"]

    C --> C1["useEffect<br/>监听状态变化"]
    C --> C2["useLayoutEffect<br/>同步状态更新"]

    D --> D1["useMemo<br/>缓存计算结果"]
    D --> D2["useCallback<br/>缓存事件处理"]

    B1 -.-> C1
    B1 -.-> D1
    B1 -.-> D2

    style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
    style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style B1 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
    }
  ],
  
  parameters: [
    {
      name: "initialState",
      type: "T | (() => T)",
      required: true,
      description: "组件的初始状态值，可以是具体值或返回初始值的函数。支持函数形式是为了延迟计算昂贵的初始值，避免每次渲染都重新计算。",
      example: `// 简单值
const [count, setCount] = useState(0);

// 对象状态
const [user, setUser] = useState({ name: '', age: 0 });

// 函数形式（延迟初始化）
const [data, setData] = useState(() => expensiveCalculation());

// 从localStorage读取
const [theme, setTheme] = useState(() => 
  localStorage.getItem('theme') || 'light'
);`
    }
  ],
  
  returnValue: {
    type: "[T, Dispatch<SetStateAction<T>>]",
    description: "返回一个数组，包含当前状态值和状态更新函数。状态值的类型与初始值类型一致，更新函数支持直接值和函数形式的更新。",
    example: `const [state, setState] = useState(initialValue);

// state: 当前状态值
// setState: 状态更新函数，支持两种调用方式：
// 1. 直接传值: setState(newValue)
// 2. 函数更新: setState(prevState => newValue)`
  },
  
  keyFeatures: [
    {
      title: "简洁的API设计",
      description: "极简的调用方式，只需一行代码即可在函数组件中添加状态",
      benefit: "降低学习成本，提高开发效率，减少代码复杂度"
    },
    {
      title: "类型安全保障",
      description: "完整的TypeScript支持，自动类型推导和泛型约束",
      benefit: "编译时错误检查，减少运行时错误，提升代码质量"
    },
    {
      title: "函数式更新模式",
      description: "支持基于当前状态的函数式更新，避免闭包陷阱",
      benefit: "确保状态更新的准确性，避免并发更新问题"
    },
    {
      title: "惰性初始化支持",
      description: "支持函数形式的初始值，实现昂贵计算的延迟执行",
      benefit: "优化性能，避免不必要的重复计算"
    },
    {
      title: "批处理优化",
      description: "React 18中自动批处理多个状态更新，减少重渲染次数",
      benefit: "提升性能，减少不必要的DOM操作"
    }
  ],
  
  limitations: [
    "状态更新是异步的，无法立即获取更新后的值",
    "对象和数组的更新需要创建新的引用才能触发重渲染",
    "在循环或条件语句中调用会导致Hook顺序错误",
    "频繁的状态更新可能导致性能问题",
    "不支持状态的时间旅行和撤销重做功能"
  ],
  
  bestPractices: [
    "使用函数式更新避免闭包陷阱：setState(prev => prev + 1)",
    "对象状态使用展开语法更新：setState(prev => ({...prev, field: value}))",
    "数组状态创建新数组：setState(prev => [...prev, newItem])",
    "昂贵初始化使用函数形式：useState(() => expensiveCalculation())",
    "相关状态考虑合并为对象：useState({loading: false, data: null})",
    "避免在渲染期间调用setState",
    "使用useCallback缓存事件处理函数",
    "复杂状态逻辑考虑使用useReducer"
  ],
  
  warnings: [
    "不要在循环、条件或嵌套函数中调用useState",
    "状态更新后不会立即反映，需要等下次渲染",
    "直接修改对象或数组不会触发重渲染，必须创建新引用",
    "在useEffect依赖数组中使用state时要小心无限循环",
    "避免在useState中存储大量数据，考虑使用useMemo缓存"
  ]
};

export default basicInfo; 