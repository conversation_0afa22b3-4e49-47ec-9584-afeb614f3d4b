import { CommonQuestion } from '@/types/api';

const data: CommonQuestion[] = [
  {
    id: 1,
    question: "createPortal的基本用法是什么？",
    answer: `createPortal的基本用法如下：

\`\`\`jsx
import { createPortal } from 'react-dom';

function Modal({ children, isOpen }) {
  if (!isOpen) return null;
  
  return createPortal(
    React.createElement('div', { className: 'modal' }, children),
    document.getElementById('modal-root') // 目标DOM容器
  );
}
\`\`\`

createPortal接收两个参数：
1. **第一个参数**：任何可渲染的React子元素（如JSX、字符串、数字等）
2. **第二个参数**：一个DOM元素，作为Portal的容器

Portal常用于将内容渲染到DOM树中的其他位置，特别适合实现模态框、提示框、悬浮菜单等需要"逃离"父组件CSS限制的UI元素。`,
    category: "使用方法",
    tags: ["入门问题", "基础概念", "API用法"],
    completionStatus: '内容已完成',
  },
  {
    id: 2,
    question: "如何在Next.js或其他SSR环境中安全地使用createPortal？",
    answer: `在Next.js或其他SSR(服务器端渲染)环境中安全使用createPortal需要特别注意，因为服务器端没有DOM环境。以下是安全实现方式：

\`\`\`jsx
import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';

function Portal({ children }) {
  // 1. 创建状态跟踪客户端挂载
  const [mounted, setMounted] = useState(false);
  
  // 2. 仅在客户端渲染后执行
  useEffect(() => {
    setMounted(true);
    
    // 可选：创建容器元素
    const portalRoot = document.createElement('div');
    portalRoot.setAttribute('id', 'portal-root');
    document.body.appendChild(portalRoot);
    
    return () => {
      // 清理创建的元素
      portalRoot.remove();
    };
  }, []);
  
  // 3. 服务器端渲染时不返回Portal
  if (!mounted) return null;
  
  // 4. 客户端渲染时使用Portal
  return createPortal(
    children,
    document.getElementById('portal-root') || document.body
  );
}

// 使用方式
function Modal({ isOpen, onClose, children }) {
  if (!isOpen) return null;
  
  return (
    React.createElement(Portal, null,
      React.createElement('div', { className: 'modal-overlay', onClick: onClose },
        React.createElement('div', { className: 'modal-content', onClick: e => e.stopPropagation() },
          children
        )
      )
    )
  );
}
\`\`\`

关键点：
- 使用useState和useEffect来确保仅在客户端渲染
- 可以在页面HTML中预先包含容器元素，或在客户端动态创建
- 考虑使用Next.js的dynamic import的"ssr: false"选项导入使用Portal的组件
- 对于Next.js 13+的App Router，可以使用'use client'指令标记组件为客户端组件`,
    category: "框架集成",
    tags: ["SSR", "Next.js", "服务器端渲染"],
    completionStatus: '内容已完成',
  },
  {
    id: 3,
    question: "使用createPortal时，如何处理无障碍性(accessibility)问题？",
    answer: `使用createPortal时，处理无障碍性(accessibility)问题至关重要，特别是对于模态框、对话框等UI元素。以下是关键处理方法：

\`\`\`jsx
import React, { useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import FocusTrap from 'focus-trap-react'; // 推荐使用focus-trap库

function AccessibleModal({ isOpen, onClose, title, description, children }) {
  const previousFocusRef = useRef(null);
  
  useEffect(() => {
    if (isOpen) {
      // 1. 保存当前焦点元素
      previousFocusRef.current = document.activeElement;
      
      // 3. 处理Escape键
      const handleEscape = (e) => {
        if (e.key === 'Escape') onClose();
      };
      document.addEventListener('keydown', handleEscape);
      
      return () => {
        // 4. 清理事件监听
        document.removeEventListener('keydown', handleEscape);
        // 5. 恢复原来的焦点
        previousFocusRef.current?.focus();
      };
    }
  }, [isOpen, onClose]);
  
  if (!isOpen) return null;
  
  return createPortal(
    <FocusTrap focusTrapOptions={{ initialFocus: false }}>
      <div 
        role="dialog" 
        aria-modal="true"
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
        className="modal-overlay"
      >
        <div className="modal-content">
          {/* 2. 添加正确的ARIA属性 */}
          <h2 id="modal-title" tabIndex={-1}>{title}</h2>
          <p id="modal-description">{description}</p>
          
          {children}
          
          {/* 6. 提供清晰的关闭方式 */}
          <button 
            onClick={onClose}
            aria-label="关闭对话框"
          >
            关闭
          </button>
        </div>
      </div>
    </FocusTrap>,
    document.body
  );
}
\`\`\`

主要处理方法：

1. **ARIA角色和属性**
   - 使用'role="dialog"'或'role="alertdialog"'
   - 设置'aria-modal="true"'
   - 通过'aria-labelledby'和'aria-describedby'关联描述文本

2. **焦点管理**
   - 打开时自动聚焦到模态框内的第一个控件
   - 使用焦点陷阱(focus trap)限制焦点在模态框内循环
   - 关闭时恢复之前的焦点位置

3. **键盘交互**
   - 确保可以用Escape键关闭
   - 提供清晰的关闭按钮
   - 支持Tab键导航
   
4. **屏幕阅读器考虑**
   - 确保动态内容变更时通知用户
   - 正确设置内容的层次结构
   
5. **相关库**
   - focus-trap-react：管理焦点陷阱
   - react-aria：Adobe的无障碍工具集
   - @reach/dialog：已内置无障碍功能的对话框

正确处理无障碍性问题可以确保所有用户（包括依赖屏幕阅读器或键盘的用户）都能有效地使用您的应用。`,
    category: "最佳实践",
    tags: ["无障碍", "ARIA", "键盘操作"],
    completionStatus: '内容已完成',
  },
  {
    id: 4,
    question: "createPortal和React.createContext是否可以一起使用？如何确保Portal内的组件能访问上下文？",
    answer: `createPortal和React.createContext完全可以一起使用。Portal不会破坏React的上下文传递机制，它仅改变了DOM渲染位置，而不改变React组件树结构。

以下是确保Portal内的组件能够访问上下文的示例：

\`\`\`jsx
import React, { useContext, useState } from 'react';
import { createPortal } from 'react-dom';

// 1. 创建上下文
const ThemeContext = React.createContext('light');

// 2. 模态框组件使用createPortal
function Modal({ isOpen, onClose, children }) {
  if (!isOpen) return null;
  
  return createPortal(
    React.createElement('div', { className: 'modal-overlay' },
      React.createElement('div', { className: 'modal-content' },
        // 3. Portal内容可以访问外部上下文
        children,
        React.createElement('button', { onClick: onClose }, '关闭')
      )
    ),
    document.body
  );
}

// 4. 使用上下文的组件
function ThemedButton() {
  // 即使在Portal内部也能访问ThemeContext
  const theme = useContext(ThemeContext);
  
  return React.createElement('button', 
    { className: \`button-\${theme}\` },
    theme === 'light' ? '🌞' : '🌙',
    ' 切换主题'
  );
}

// 5. 应用入口
function App() {
  const [theme, setTheme] = useState('light');
  const [showModal, setShowModal] = useState(false);
  
  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };
  
  return React.createElement(ThemeContext.Provider, { value: theme },
    React.createElement('div', { className: \`app \${theme}\` },
      React.createElement('h1', null, 'Portal与Context示例'),
      React.createElement('button', { onClick: () => setShowModal(true) }, '打开模态框'),
      React.createElement('button', { onClick: toggleTheme }, '切换主题'),
      // 模态框内部可以访问ThemeContext
      React.createElement(Modal, {
        isOpen: showModal,
        onClose: () => setShowModal(false)
      },
        React.createElement('h2', null, '模态框标题'),
        React.createElement('p', null, \`当前主题：\${theme}\`),
        React.createElement(ThemedButton)
      )
    )
  );
}
\`\`\`

关键点：

1. **Context工作原理**：Context是基于React组件树结构工作的，而不是DOM树
2. **Portal的特性**：Portal只改变渲染的DOM位置，但在React内部组件树中仍保持原有的层级关系
3. **实际应用场景**：
   - 主题切换：Portal内容可以继承应用的主题设置
   - 国际化：Portal内文本可以使用应用的语言设置
   - 认证状态：Portal可以访问用户的认证信息
   - 状态管理：Redux、MobX等通过Context传递的状态在Portal中同样可用

这是React Portal的一个重要优势——它让你可以在DOM树中的任何位置渲染内容，同时保持与React应用其余部分的上下文连接。`,
    category: "概念理解",
    tags: ["Context API", "状态管理", "组件通信"],
    completionStatus: '内容已完成',
  },
  {
    id: 5,
    question: "在使用createPortal时，如何优化性能以避免不必要的重渲染？",
    answer: `在使用createPortal时优化性能，避免不必要的重渲染的关键策略如下：

\`\`\`jsx
import React, { useState, useCallback, memo } from 'react';
import { createPortal } from 'react-dom';

// 1. 使用React.memo缓存Portal内容组件
const ModalContent = memo(({ title, children, onClose }) => {
  console.log("Modal content rendering");
  return React.createElement('div', { className: 'modal-content' },
    React.createElement('h2', null, title),
    children,
    React.createElement('button', { onClick: onClose }, '关闭')
  );
});

// 2. 分离Portal容器和内容
function Modal({ isOpen, title, children, onClose }) {
  // 3. 使用useCallback缓存回调函数
  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);
  
  // 4. 有条件地创建Portal，而不是总是渲染后再隐藏
  if (!isOpen) return null;
  
  // 5. 渲染Portal
  return createPortal(
    React.createElement('div', { className: 'modal-overlay' },
      React.createElement(ModalContent, {
        title: title,
        onClose: handleClose
      }, children)
    ),
    document.body
  );
}

// 6. 提升Portal容器创建到应用初始化阶段
function setupPortalContainer() {
  if (typeof document !== 'undefined' && !document.getElementById('portal-root')) {
    const portalRoot = document.createElement('div');
    portalRoot.id = 'portal-root';
    document.body.appendChild(portalRoot);
  }
}

// 7. 使用组件
function App() {
  const [showModal, setShowModal] = useState(false);
  const [count, setCount] = useState(0);
  
  // 应用初始化时创建容器
  React.useEffect(() => {
    setupPortalContainer();
  }, []);
  
  // 模拟频繁更新的状态
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCount(c => c + 1);
    }, 1000);
    return () => clearInterval(timer);
  }, []);
  
  return React.createElement('div', { className: 'app' },
    React.createElement('h1', null, \`计数: \${count}\`),
    React.createElement('button', { onClick: () => setShowModal(true) }, '打开模态框'),
    React.createElement(Modal, {
      isOpen: showModal,
      title: '优化的模态框',
      onClose: () => setShowModal(false)
    },
      React.createElement('p', null, '这个模态框使用了性能优化措施')
      // 8. 避免传递不必要的props
      // 不要传入count，除非模态框真的需要
    )
  );
}

// 9. 共享Portal容器的优化方案
const PortalManager = {
  getContainer(id = 'default') {
    if (typeof document === 'undefined') return null;
    
    let container = document.getElementById(\`portal-\${id}\`);
    
    if (!container) {
      container = document.createElement('div');
      container.id = \`portal-\${id}\`;
      document.body.appendChild(container);
    }
    
    return container;
  }
};
\`\`\`

关键优化策略：

1. **使用memo**：对Portal内容组件使用React.memo减少重渲染
2. **缓存回调函数**：使用useCallback保证回调函数引用稳定
3. **懒加载内容**：只在需要时才创建Portal内容
4. **共享Portal容器**：多个Portal可以共享同一个容器，避免频繁创建DOM节点
5. **避免不必要的props**：只传递Portal真正需要的props
6. **正确的依赖管理**：确保useEffect等hooks有正确的依赖数组
7. **使用DevTools分析**：使用React DevTools Profiler检测不必要的重渲染
8. **分离状态逻辑**：避免Portal因父组件不相关状态变化而重渲染

性能问题的具体案例：
- 模态框闪烁：通常是因为不必要的重渲染导致
- 动画不流畅：可能是过多的计算导致帧率下降
- 表单输入延迟：Portal中表单元素响应缓慢

合理应用这些优化措施，可以确保使用createPortal的组件保持高性能，即使在复杂应用中也不会成为性能瓶颈。`,
    category: "性能优化",
    tags: ["性能", "重渲染", "优化技巧"],
    completionStatus: '内容已完成',
  }
];

export default data; 