import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  completionStatus: '内容已完成',
  
  definition: "createPortal是React中用于将子组件渲染到DOM树中不同位置的ReactDOM API，它实现了'传送门'机制。",
  
  introduction: `createPortal是ReactDOM提供的强大API，允许我们将React子元素渲染到位于组件树之外的DOM节点中。这打破了React组件常规的父子包含关系，使我们能够解决模态框、弹出菜单、提示框等需要"逃离"父组件CSS限制的场景。Portal保持了React的事件冒泡机制，使得事件可以从portal内部传播到React组件树中，提供了灵活性的同时保持了React应用的一致性。`,

  syntax: `import { createPortal } from 'react-dom';

createPortal(children, container);`,

  quickExample: `import { createPortal } from 'react-dom';
import { useState } from 'react';

function Modal({ isOpen, onClose, children }) {
  if (!isOpen) return null;
  
  // 将Modal内容渲染到body元素的末尾
  return createPortal(
    React.createElement('div', { className: 'modal-overlay' },
      React.createElement('div', { className: 'modal-content' },
        children,
        React.createElement('button', { onClick: onClose }, '关闭')
      )
    ),
    document.body
  );
}

function App() {
  const [showModal, setShowModal] = useState(false);
  
  return React.createElement('div', { className: 'app' },
    React.createElement('h1', null, 'Portal 示例'),
    React.createElement('button', 
      { onClick: () => setShowModal(true) },
      '打开模态框'
    ),
    React.createElement(Modal, {
      isOpen: showModal,
      onClose: () => setShowModal(false)
    },
      React.createElement('h2', null, '这是一个模态框'),
      React.createElement('p', null, '模态框内容会被渲染到body元素末尾')
    )
  );
}`,

  scenarioDiagram: `graph TD
    A[主要使用场景] --> B[模态框/对话框]
    A --> C[悬浮提示]
    A --> D[下拉菜单]
    A --> E[通知/toast]
    A --> F[CSS上下文逃逸]
    A --> G[悬浮组件]
    A --> H[第三方DOM容器集成]

    B --> B1[登录弹窗]
    B --> B2[确认对话框]
    B --> B3[图片预览]

    C --> C1[工具提示]
    C --> C2[帮助文本]

    D --> D1[导航菜单]
    D --> D2[选择器/下拉列表]

    E --> E1[操作结果通知]
    E --> E2[系统消息]

    F --> F1[overflow:hidden逃逸]
    F --> F2[z-index堆叠问题解决]

    G --> G1[悬浮视频播放器]
    G --> G2[全局悬浮按钮]

    H --> H1[地图组件集成]
    H --> H2[编辑器集成]`,
  
  parameters: [
    {
      name: "children",
      type: "ReactNode",
      required: true,
      description: "需要渲染的React元素、字符串或片段，将被渲染到container中",
      example: "<div>这是一个Portal内容</div>"
    },
    {
      name: "container",
      type: "DOM Element",
      description: "一个已经存在的DOM元素，children将被挂载到这个DOM元素中",
      example: "document.getElementById('modal-root')",
      required: true
    }
  ],
  
  returnValue: {
    type: "ReactPortal",
    description: "返回一个ReactPortal对象，可以像普通React元素一样使用和返回",
    example: "const portal = createPortal(children, container); return <div>{portal}</div>;"
  },
  
  keyFeatures: [
    {
      title: "突破DOM层级限制",
      description: "允许将内容渲染到任意DOM位置，不受组件层级结构限制",
      benefit: "解决了CSS上下文、overflow、z-index等样式问题"
    },
    {
      title: "保留React事件系统",
      description: "Portal中的事件会冒泡到React树中的父组件，即使DOM结构上不是父子关系",
      benefit: "维持了React统一的事件处理模型，代码逻辑更一致"
    },
    {
      title: "动态容器渲染",
      description: "可以在运行时动态选择或创建目标容器",
      benefit: "支持更灵活的渲染策略，适应不同场景需求"
    },
    {
      title: "服务器端渲染兼容",
      description: "支持SSR环境，在服务端渲染时会被正确处理",
      benefit: "可以无缝集成到现代React应用架构中"
    }
  ],
  
  limitations: [
    "DOM结构不直观，实际DOM与组件树不一致，可能导致调试困难",
    "必须确保container已经存在于DOM中，否则会抛出错误",
    "需要手动管理portal内容的生命周期和清理工作",
    "过度使用会导致应用结构混乱，增加维护难度"
  ],
  
  bestPractices: [
    "为Portal内容创建专门的DOM容器，如<div id=\"modal-root\"></div>",
    "使用React.memo或类组件的shouldComponentUpdate优化Portal渲染性能",
    "确保在组件卸载时清理相关DOM资源，避免内存泄漏",
    "仅在必要时使用Portal，大多数UI元素应遵循正常的组件树结构",
    "结合React.lazy和Suspense实现Portal内容的按需加载"
  ],
  
  warnings: [
    "在SSR环境中，确保container只在客户端渲染时使用，避免服务端渲染错误",
    "Portal改变了DOM结构但不改变组件树，事件冒泡会按React组件树传播而非DOM树",
    "使用Portal时要特别注意无障碍性(accessibility)问题，确保键盘导航和屏幕阅读器支持"
  ]
};

export default basicInfo; 