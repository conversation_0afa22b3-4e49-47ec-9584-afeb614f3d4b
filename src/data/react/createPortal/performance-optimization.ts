import { PerformanceOptimization } from '@/types/api';

const data: PerformanceOptimization = {
  completionStatus: '内容已完成',

  optimizationStrategies: [
    {
      strategy: "避免不必要的Portal重渲染",
      description: `Portal内容的重渲染可能发生得比预期更频繁，特别是当Portal所在的父组件频繁更新时。可以通过几种方式减少不必要的重渲染。`,
      code: `import React, { useState, memo, useCallback } from 'react';
import { createPortal } from 'react-dom';

// 使用React.memo减少不必要的重渲染
const MemoizedModalContent = memo(({ title, children, onClose }) => {
  console.log('Modal content rendering');
  return (
    <div className="modal-content">
      <h2>{title}</h2>
      <div>{children}</div>
      <button onClick={onClose}>关闭</button>
    </div>
  );
});

function Modal({ isOpen, title, children, onClose }) {
  // 使用useCallback保证回调函数引用的稳定性
  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);
  
  // 条件渲染Portal，避免不必要的创建和销毁
  if (!isOpen) return null;
  
  return createPortal(
    <div className="modal-overlay">
      <MemoizedModalContent
        title={title}
        onClose={handleClose}
      >
        {children}
      </MemoizedModalContent>
    </div>,
    document.body
  );
}

// 父组件
function App() {
  const [showModal, setShowModal] = useState(false);
  const [count, setCount] = useState(0);
  
  // 频繁更新的状态不会导致Modal内容重渲染
  React.useEffect(() => {
    const timer = setInterval(() => setCount(c => c + 1), 1000);
    return () => clearInterval(timer);
  }, []);
  
  return (
    <div>
      <h1>计数: {count}</h1>
      <button onClick={() => setShowModal(true)}>打开模态框</button>
      
      <Modal
        isOpen={showModal}
        title="优化的模态框"
        onClose={() => setShowModal(false)}
      >
        <p>这个模态框内容不会因为父组件更新而重渲染</p>
      </Modal>
    </div>
  );
}`
    },
    {
      strategy: "懒创建Portal容器",
      description: `在应用初始化时创建所有Portal容器会浪费资源。相反，可以实现懒创建机制，仅在需要时创建Portal容器，并适当地缓存它们以供重用。`,
      code: `import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';

// Portal容器管理器 - 单例模式
const PortalManager = {
  containers: {},
  
  getContainer(id) {
    // 如果容器已存在，则复用
    if (this.containers[id]) {
      return this.containers[id];
    }
    
    // 否则创建新容器
    const container = document.createElement('div');
    container.id = \`portal-container-\${id}\`;
    document.body.appendChild(container);
    
    // 缓存容器
    this.containers[id] = container;
    return container;
  },
  
  removeContainer(id) {
    if (this.containers[id]) {
      document.body.removeChild(this.containers[id]);
      delete this.containers[id];
    }
  }
};

function LazyPortal({ id, children, isOpen }) {
  const [container, setContainer] = useState(null);
  
  // 仅在需要显示Portal时创建/获取容器
  useEffect(() => {
    if (isOpen && !container) {
      setContainer(PortalManager.getContainer(id));
    }
    
    // 清理函数 - 组件卸载时考虑是否移除容器
    // 在实际应用中，可能需要更复杂的引用计数机制
    return () => {
      // 这里可以选择不立即移除，而是在合适的时机清理
      // PortalManager.removeContainer(id);
    };
  }, [isOpen, id]);
  
  // 没有容器或不需要显示时不渲染
  if (!isOpen || !container) return null;
  
  return createPortal(children, container);
}

function App() {
  const [showModal, setShowModal] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  
  return (
    <div>
      <button 
        onClick={() => setShowModal(true)}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        显示模态框
      </button>
      
      <LazyPortal id="modal" isOpen={showModal}>
        <div className="modal">
          <h2>模态框内容</h2>
          <button onClick={() => setShowModal(false)}>关闭</button>
        </div>
      </LazyPortal>
      
      <LazyPortal id="tooltip" isOpen={showTooltip}>
        <div className="tooltip">这是工具提示</div>
      </LazyPortal>
    </div>
  );
}`
    },
    {
      strategy: "避免过多的Portal实例",
      description: `大量的Portal实例可能会导致性能问题，因为每个Portal都代表一个独立的渲染入口。优化策略包括合并多个Portal内容到共享容器，或使用组件池来限制同时激活的Portal数量。`,
      code: `import React, { useState, useRef } from 'react';
import { createPortal } from 'react-dom';

// 基于容器类型的Portal组合器
function PortalGroup({ type, children }) {
  // 为每种Portal类型使用单一容器
  const [container] = useState(() => {
    let existingContainer = document.getElementById(\`portal-\${type}\`);
    
    if (!existingContainer) {
      existingContainer = document.createElement('div');
      existingContainer.id = \`portal-\${type}\`;
      existingContainer.className = \`portal-container \${type}-container\`;
      document.body.appendChild(existingContainer);
    }
    
    return existingContainer;
  });
  
  return createPortal(children, container);
}

// 通知消息系统 - 复用同一个Portal容器
function NotificationSystem() {
  const [notifications, setNotifications] = useState([]);
  const nextIdRef = useRef(1);
  
  const addNotification = (message, type = 'info') => {
    const id = nextIdRef.current++;
    setNotifications(prev => [...prev, { id, message, type }]);
    
    // 自动移除
    setTimeout(() => {
      removeNotification(id);
    }, 3000);
  };
  
  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(note => note.id !== id));
  };
  
  // 共享一个Portal容器，而不是为每个通知创建新Portal
  return (
    <>
      <button onClick={() => addNotification('信息通知', 'info')}>
        添加信息
      </button>
      <button onClick={() => addNotification('警告通知', 'warning')}>
        添加警告
      </button>
      
      {notifications.length > 0 && (
        <PortalGroup type="notifications">
          <div className="notifications-wrapper">
            {notifications.map(note => (
              <div 
                key={note.id} 
                className={\`notification \${note.type}\`}
              >
                {note.message}
                <button onClick={() => removeNotification(note.id)}>
                  ×
                </button>
              </div>
            ))}
          </div>
        </PortalGroup>
      )}
    </>
  );
}

// 固定数量的模态框池，而不是无限创建
function ModalPool() {
  const [activeModals, setActiveModals] = useState([]);
  const maxModals = 3; // 最多同时显示3个
  
  const openModal = (content, title) => {
    setActiveModals(prev => {
      // 如果已达到最大数量，替换最旧的
      if (prev.length >= maxModals) {
        return [...prev.slice(1), { id: Date.now(), content, title }];
      }
      return [...prev, { id: Date.now(), content, title }];
    });
  };
  
  const closeModal = (id) => {
    setActiveModals(prev => prev.filter(modal => modal.id !== id));
  };
  
  return (
    <div>
      <button onClick={() => openModal('模态框内容1', '标题1')}>
        打开模态框1
      </button>
      <button onClick={() => openModal('模态框内容2', '标题2')}>
        打开模态框2
      </button>
      
      <PortalGroup type="modals">
        {activeModals.map(modal => (
          <div key={modal.id} className="modal-wrapper">
            <div className="modal">
              <h3>{modal.title}</h3>
              <div>{modal.content}</div>
              <button onClick={() => closeModal(modal.id)}>关闭</button>
            </div>
          </div>
        ))}
      </PortalGroup>
    </div>
  );
}`
    },
    {
      strategy: "延迟加载Portal内容",
      description: `对于复杂的Portal内容，可以使用延迟加载策略，仅在Portal即将显示时才加载相关组件和数据。这可以通过React.lazy和Suspense，或自定义的加载机制实现。`,
      code: `import React, { useState, lazy, Suspense } from 'react';
import { createPortal } from 'react-dom';

// 延迟加载的复杂模态框内容
const HeavyModalContent = lazy(() => 
  // 模拟复杂组件加载
  new Promise(resolve => {
    setTimeout(() => {
      resolve(import('./HeavyModalContent'));
    }, 500);
  })
);

// 延迟加载的图表组件
const ChartComponent = lazy(() => 
  import('./ChartComponent')
);

function LazyLoadPortal({ children, isOpen }) {
  const [portalRoot] = useState(() => {
    const root = document.createElement('div');
    root.id = 'lazy-portal-root';
    document.body.appendChild(root);
    return root;
  });
  
  // 如果未打开，不渲染任何内容
  if (!isOpen) return null;
  
  return createPortal(
    <Suspense fallback={<div className="loading">加载中...</div>}>
      {children}
    </Suspense>,
    portalRoot
  );
}

// 数据加载器组件
function DataLoader({ loadData, children }) {
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  
  React.useEffect(() => {
    // 仅在组件挂载时加载数据
    // 对于真实应用，可能需要根据props变化决定是否重新加载
    setIsLoading(true);
    loadData()
      .then(result => {
        setData(result);
        setIsLoading(false);
      });
  }, [loadData]);
  
  if (isLoading) return <div>加载数据中...</div>;
  if (!data) return null;
  
  return children(data);
}

function App() {
  const [showModal, setShowModal] = useState(false);
  const [showChart, setShowChart] = useState(false);
  
  // 模拟数据加载函数
  const loadChartData = React.useCallback(() => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          labels: ['一月', '二月', '三月'],
          values: [10, 20, 30]
        });
      }, 1000);
    });
  }, []);
  
  return (
    <div>
      <button onClick={() => setShowModal(true)}>
        显示复杂模态框
      </button>
      <button onClick={() => setShowChart(true)}>
        显示图表模态框
      </button>
      
      {/* 复杂模态框，仅在显示时才加载内容 */}
      <LazyLoadPortal isOpen={showModal}>
        <HeavyModalContent onClose={() => setShowModal(false)} />
      </LazyLoadPortal>
      
      {/* 图表模态框，仅在显示时才加载图表组件和数据 */}
      <LazyLoadPortal isOpen={showChart}>
        <div className="chart-modal">
          <h2>数据图表</h2>
          <DataLoader loadData={loadChartData}>
            {(data) => (
              <ChartComponent data={data} />
            )}
          </DataLoader>
          <button onClick={() => setShowChart(false)}>关闭</button>
        </div>
      </LazyLoadPortal>
    </div>
  );
}`
    },
    {
      strategy: "优化Portal的生命周期管理",
      description: `Portal组件的挂载和卸载可能导致昂贵的DOM操作。通过保留Portal容器并控制其内容的可见性，可以减少这些开销。此外，应确保在组件卸载时正确清理Portal相关的资源。`,
      code: `import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

// 可重用的Portal组件，优化生命周期管理
function ReusablePortal({ children, show }) {
  const portalRef = useRef(null);
  const [mounted, setMounted] = useState(false);
  
  // 首次渲染时创建Portal容器
  useEffect(() => {
    const portalRoot = document.createElement('div');
    portalRoot.className = 'reusable-portal';
    document.body.appendChild(portalRoot);
    portalRef.current = portalRoot;
    setMounted(true);
    
    // 组件完全卸载时移除Portal容器
    return () => {
      if (portalRef.current) {
        document.body.removeChild(portalRef.current);
      }
    };
  }, []);
  
  // 控制Portal内容可见性，而不是挂载/卸载
  useEffect(() => {
    if (portalRef.current) {
      portalRef.current.style.display = show ? 'block' : 'none';
    }
  }, [show]);
  
  // 没有容器或组件未挂载时返回null
  if (!mounted || !portalRef.current) return null;
  
  // Portal内容始终挂载，但通过CSS控制可见性
  return createPortal(children, portalRef.current);
}

// 管理多个Portal的系统
function PortalSystem() {
  // 不同类型的Portal状态
  const [showModal, setShowModal] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  
  // 显示通知并自动隐藏
  const showTemporaryNotification = () => {
    setShowNotification(true);
    setTimeout(() => setShowNotification(false), 3000);
  };
  
  return (
    <div>
      <div className="controls">
        <button 
          onClick={() => setShowModal(!showModal)}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
        >
          {showModal ? '关闭' : '打开'}模态框
        </button>
        
        <button onClick={showTemporaryNotification}>
          显示通知
        </button>
      </div>
      
      {/* 模态框Portal - 始终挂载但控制可见性 */}
      <ReusablePortal show={showModal}>
        <div className="modal-overlay" 
             onClick={() => setShowModal(false)}>
          <div className="modal" 
               onClick={e => e.stopPropagation()}>
            <h3>可重用模态框</h3>
            <p>这个模态框组件始终保持挂载状态，仅通过CSS控制显示/隐藏</p>
          </div>
        </div>
      </ReusablePortal>
      
      {/* 工具提示Portal */}
      <ReusablePortal show={showTooltip}>
        <div className="tooltip">
          点击按钮切换模态框显示状态
        </div>
      </ReusablePortal>
      
      {/* 通知Portal */}
      <ReusablePortal show={showNotification}>
        <div className="notification">
          这是一条会自动消失的通知
        </div>
      </ReusablePortal>
    </div>
  );
}

// 应用入口
function App() {
  return (
    <div className="app">
      <h1>Portal生命周期优化示例</h1>
      <PortalSystem />
    </div>
  );
}`
    }
  ],

  bestPractices: [
    {
      practice: "使用React.memo优化Portal内容",
      description: "对Portal内部组件使用React.memo包装，防止父组件重渲染导致Portal内容不必要的重渲染",
      example: "对模态框内容组件使用memo，结合useCallback稳定回调函数"
    },
    {
      practice: "实现Portal容器重用",
      description: "使用Portal管理器统一管理容器创建和销毁，实现容器重用以减少DOM操作",
      example: "同类型Portal共享同一容器，如通知系统使用单一通知容器"
    },
    {
      practice: "限制Portal数量",
      description: "对同时活跃的Portal数量设置合理限制，防止过多Portal影响性能",
      example: "实现模态框池，限制同时显示的模态框数量不超过3个"
    },
    {
      practice: "使用延迟加载策略",
      description: "对复杂Portal内容使用React.lazy和Suspense，仅在显示时才加载相关组件",
      example: "大型表单或图表组件在Portal打开时才开始加载"
    },
    {
      practice: "优化Portal生命周期",
      description: "使用CSS控制Portal可见性而非频繁挂载/卸载，减少DOM操作开销",
      example: "保持Portal容器挂载，通过display属性控制显示/隐藏"
    }
  ]
};

export default data; 