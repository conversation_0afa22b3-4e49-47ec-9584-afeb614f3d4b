import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: "什么是createPortal，它的主要用途是什么？",
    difficulty: "easy",
    frequency: "high",
    category: "基础概念",
    answer: {
      brief: "createPortal是React DOM提供的API，允许将React元素渲染到位于组件树之外的DOM节点中，常用于模态框、弹出菜单等需要打破DOM层级的场景。",
      detailed: `createPortal是React DOM提供的API，允许将React元素渲染到位于组件树之外的DOM节点中。

**基本语法**：
\`\`\`jsx
import { createPortal } from 'react-dom';

createPortal(children, container);
\`\`\`

其中：
- children：任何可渲染的React元素
- container：DOM树中的一个DOM节点

**主要用途**：
1. **模态框/对话框**：将模态框渲染到body元素末尾，避免被父组件的CSS属性（如overflow:hidden）影响
2. **工具提示/弹出菜单**：将弹出内容渲染到更高层级的DOM位置，解决z-index堆叠上下文问题
3. **浮动UI元素**：如通知、toast提示等需要全局显示的UI元素
4. **第三方DOM容器集成**：将React组件渲染到React应用之外的DOM结构中

Portal的独特之处在于，虽然DOM结构上元素被"传送"到了另一个位置，但在React的组件树结构中，它仍然是原来父组件的子元素，因此可以正常接收context，并且事件会按照React组件树（而非DOM树）正常冒泡。`,
      code: `
// 基本的模态框示例
import React, { useState } from 'react';
import { createPortal } from 'react-dom';

function Modal({ isOpen, onClose, children }) {
  if (!isOpen) return null;
  
  return createPortal(
    <div className="modal-overlay" onClick={onClose}>
      <div 
        className="modal-content" 
        onClick={e => e.stopPropagation()}
      >
        {children}
        <button onClick={onClose}>关闭</button>
      </div>
    </div>,
    document.body // 渲染到body元素
  );
}

function App() {
  const [showModal, setShowModal] = useState(false);
  
  return (
    <div className="app">
      <h1>Portal演示</h1>
      <button onClick={() => setShowModal(true)}>
        打开模态框
      </button>
      
      <Modal 
        isOpen={showModal} 
        onClose={() => setShowModal(false)}
      >
        <h2>这是一个模态框</h2>
        <p>使用createPortal渲染到body中</p>
      </Modal>
    </div>
  );
}
`
    },
    tags: ['DOM操作', 'UI渲染', '模态框']
  },
  {
    id: 2,
    question: "createPortal与普通组件渲染有什么区别？事件处理机制有何不同？",
    difficulty: "medium",
    frequency: "medium",
    category: "原理解析",
    answer: {
      brief: "createPortal允许将内容渲染到DOM树的不同位置，但在React组件树中保持原有层级关系，因此事件冒泡遵循React组件树而非DOM树的结构。",
      detailed: `createPortal与普通React组件渲染有几个关键区别，特别是在DOM结构和事件处理方面：

### DOM结构差异

**普通组件渲染**：
\`\`\`jsx
function ParentComponent() {
  return (
    <div className="parent">
      <ChildComponent />
    </div>
  );
}
\`\`\`

在这种情况下，ChildComponent的DOM输出会嵌套在parent div内部，形成一个层级清晰的DOM树。

**使用Portal渲染**：
\`\`\`jsx
function ParentComponent() {
  return (
    <div className="parent">
      {createPortal(
        <ChildComponent />,
        document.getElementById('portal-root')
      )}
    </div>
  );
}
\`\`\`

在这种情况下，ChildComponent的DOM输出会被渲染到指定的portal-root元素中，而不是嵌套在parent div内部，打破了DOM的嵌套结构。

### 事件处理机制

createPortal的一个重要特性是：**事件冒泡仍然遵循React组件树，而不是DOM树**。

这意味着：
- 即使Portal的内容在DOM中被渲染到不同位置
- 事件仍然会从Portal内部的元素开始，向上冒泡到React组件树中的父组件

示例：
\`\`\`jsx
function ModalExample() {
  const [showModal, setShowModal] = useState(false);
  
  // 这个事件处理函数会捕获到从Modal内部冒泡的事件
  const handleClick = () => {
    console.log('Container clicked');
  };
  
  return (
    <div className="container" onClick={handleClick}>
      <button onClick={() => setShowModal(true)}>
        显示模态框
      </button>
      
      {showModal && createPortal(
        <div className="modal">
          <p>模态框内容</p>
          <button onClick={() => {
            console.log('Modal button clicked');
            // 这个事件会冒泡到container的onClick
            setShowModal(false);
          }}>
            关闭
          </button>
        </div>,
        document.body
      )}
    </div>
  );
}
\`\`\`

在上面的例子中：
1. 当点击模态框中的按钮时，首先会触发按钮自己的onClick
2. 然后事件会冒泡到React组件树中的container div，触发handleClick
3. 尽管在DOM树中，模态框已经被渲染到body中，不再是container的子元素

这种行为确保了事件处理的一致性，使得Portal可以无缝集成到React的事件系统中。

### 其他重要区别

1. **CSS上下文**：Portal内容不受父组件CSS上下文的限制（如overflow、z-index等）
2. **焦点管理**：Portal内容在DOM中的位置不同，可能影响Tab键导航顺序
3. **Context传递**：Portal内容仍然可以访问父组件提供的Context
4. **生命周期**：Portal内容的生命周期仍然与父组件绑定，父组件卸载时Portal也会卸载

理解这些区别对于正确使用Portal至关重要，特别是在构建复杂UI交互时。`,
      code: `
// 事件冒泡演示
import React, { useState } from 'react';
import { createPortal } from 'react-dom';

function EventBubblingDemo() {
  const [showPortal, setShowPortal] = useState(false);
  const [eventLog, setEventLog] = useState([]);
  
  const logEvent = (message) => {
    setEventLog(prev => [...prev, message]);
  };
  
  // 父组件事件处理
  const handleParentClick = () => {
    logEvent("3. 事件冒泡到父容器");
  };
  
  return (
    <div className="event-demo">
      <div 
        className="parent-container" 
        onClick={handleParentClick}
        style={{ padding: '20px', border: '2px solid blue' }}
      >
        <h3>父组件</h3>
        <button onClick={() => setShowPortal(true)}>
          显示Portal内容
        </button>
        
        {showPortal && createPortal(
          <div 
            className="portal-content" 
            style={{ 
              position: 'fixed', 
              top: '50%', 
              left: '50%',
              transform: 'translate(-50%, -50%)',
              padding: '20px',
              background: 'white',
              boxShadow: '0 0 10px rgba(0,0,0,0.3)',
              zIndex: 1000
            }}
          >
            <h4>Portal内容 (渲染到body)</h4>
            <button onClick={() => {
              logEvent("1. Portal按钮被点击");
              logEvent("2. Portal按钮事件开始冒泡");
              setShowPortal(false);
            }}>
              关闭Portal (点击将触发冒泡)
            </button>
          </div>,
          document.body
        )}
      </div>
      
      <div className="event-log">
        <h4>事件日志：</h4>
        <ul>
          {eventLog.map((log, i) => (
            <li key={i}>{log}</li>
          ))}
        </ul>
      </div>
    </div>
  );
}
`
    },
    tags: ['事件冒泡', 'DOM结构', '渲染机制']
  },
  {
    id: 3,
    question: "如何在SSR（服务器端渲染）环境中正确使用createPortal？",
    difficulty: "hard",
    frequency: "medium",
    category: "高级应用",
    answer: {
      brief: "SSR环境中应该使用客户端条件渲染Portal，因为服务器没有DOM。可以通过useEffect延迟创建Portal，或使用动态导入来保证Portal只在客户端渲染。",
      detailed: `在SSR（服务器端渲染）环境中正确使用createPortal需要特别注意，因为服务器端没有DOM环境。以下是完整的解决方案：

### 核心挑战

在SSR环境中使用createPortal面临的主要挑战：

1. **服务器端没有DOM**：服务器上不存在document或window对象
2. **hydration不匹配**：如果服务器渲染的HTML与客户端期望的不同，会导致hydration错误
3. **容器可用性**：Portal的目标容器可能在初始HTML中不存在

### 解决方案

**方法1：条件渲染 - 仅在客户端使用Portal**

\`\`\`jsx
import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';

function ClientOnlyPortal({ children, selector }) {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);
  
  return mounted
    ? createPortal(children, document.querySelector(selector))
    : null;
}

// 使用方式
function Modal({ isOpen, onClose, children }) {
  if (!isOpen) return null;
  
  return (
    <ClientOnlyPortal selector="#modal-root">
      <div className="modal-overlay">
        <div className="modal-content">
          {children}
          <button onClick={onClose}>关闭</button>
        </div>
      </div>
    </ClientOnlyPortal>
  );
}
\`\`\`

**方法2：使用Next.js的动态导入（推荐）**

\`\`\`jsx
// components/Modal.js
import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

function Modal({ children, isOpen, onClose }) {
  const [portalElement, setPortalElement] = useState(null);
  
  useEffect(() => {
    // 确保portal容器存在
    let element = document.getElementById('modal-root');
    if (!element) {
      element = document.createElement('div');
      element.id = 'modal-root';
      document.body.appendChild(element);
    }
    setPortalElement(element);
    
    return () => {
      // 可选的清理，如果需要的话
    };
  }, []);
  
  if (!isOpen || !portalElement) return null;
  
  return createPortal(
    <div className="modal-overlay">
      <div className="modal-content">
        {children}
        <button onClick={onClose}>关闭</button>
      </div>
    </div>,
    portalElement
  );
}

export default Modal;

// pages/index.js
import dynamic from 'next/dynamic';

// 禁用SSR
const DynamicModal = dynamic(
  () => import('../components/Modal'),
  { ssr: false }
);

function HomePage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  return (
    <div>
      <button onClick={() => setIsModalOpen(true)}>打开模态框</button>
      <DynamicModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)}
      >
        <h2>这是一个客户端渲染的模态框</h2>
      </DynamicModal>
    </div>
  );
}
\`\`\`

**方法3：使用Next.js 13+ App Router的客户端组件**

\`\`\`jsx
// components/Modal.jsx
'use client'; // 标记为客户端组件

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';

export default function Modal({ children, isOpen, onClose }) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  if (!isOpen || !isMounted) return null;
  
  return createPortal(
    <div className="modal-overlay">
      <div className="modal-content">
        {children}
        <button onClick={onClose}>关闭</button>
      </div>
    </div>,
    document.body
  );
}

// app/page.jsx (服务器组件)
import ModalClient from '../components/Modal';

export default function Page() {
  return (
    <div>
      <h1>SSR页面</h1>
      {/* 客户端组件将仅在客户端激活Portal */}
      <ModalClient />
    </div>
  );
}
\`\`\`

### 最佳实践

1. **总是使用客户端渲染Portal**：通过useEffect或客户端组件确保Portal只在浏览器中创建
2. **准备回退内容**：为不支持JS或初始渲染提供合理的回退UI
3. **确保容器存在**：动态创建Portal容器而不是依赖预先存在的DOM元素
4. **管理焦点**：在客户端激活后，确保为辅助技术提供适当的焦点管理
5. **避免hydration不匹配**：确保服务器渲染和客户端渲染的输出一致（Portal内容除外）`,
      code: `
// SSR友好的Portal系统
import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';

// 通用的SSR安全Portal组件
function SafePortal({ children, selector, enabled = true }) {
  const [portalNode, setPortalNode] = useState(null);
  const [mounted, setMounted] = useState(false);

  // 仅在客户端执行
  useEffect(() => {
    if (!enabled) return;
    
    setMounted(true);
    
    // 查找或创建Portal容器
    let targetNode = null;
    if (typeof selector === 'string') {
      targetNode = document.querySelector(selector);
      
      // 如果没找到目标节点，创建一个
      if (!targetNode) {
        targetNode = document.createElement('div');
        targetNode.id = selector.replace(/^[#.]/, '');
        document.body.appendChild(targetNode);
      }
    } else if (selector instanceof HTMLElement) {
      targetNode = selector;
    } else {
      // 默认容器
      targetNode = document.createElement('div');
      targetNode.classList.add('portal-container');
      document.body.appendChild(targetNode);
    }
    
    setPortalNode(targetNode);
    
    // 清理函数 - 如果我们创建了新节点，则移除它
    return () => {
      if (targetNode && targetNode.classList.contains('portal-container')) {
        document.body.removeChild(targetNode);
      }
      setMounted(false);
    };
  }, [selector, enabled]);
  
  // 服务器端或未挂载时返回null
  if (!mounted || !portalNode) return null;
  
  // 客户端且已挂载，创建portal
  return createPortal(children, portalNode);
}

// 使用示例
function SSRFriendlyModal({ isOpen, onClose, children }) {
  if (!isOpen) return null;
  
  // SSR安全的modal渲染
  return (
    <SafePortal selector="#modal-root">
      <div className="modal-backdrop" onClick={onClose}>
        <div 
          className="modal" 
          onClick={e => e.stopPropagation()}
          role="dialog"
          aria-modal="true"
        >
          {children}
          <button onClick={onClose}>关闭</button>
        </div>
      </div>
    </SafePortal>
  );
}
`
    },
    tags: ['SSR', '服务器端渲染', 'Next.js']
  }
];

export default interviewQuestions; 