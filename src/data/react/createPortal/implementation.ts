import { Implementation } from '@/types/api';

const implementation: Implementation = {
  completionStatus: '内容已完成',

  introduction: `createPortal是ReactDOM提供的一个强大API，允许开发者突破React组件树的限制，将子组件渲染到DOM树中的任意位置。本文深入探讨其实现机制、内部工作原理及核心设计思想。`,

  mechanism: `createPortal的核心机制基于React的渲染管道与DOM操作分离。当React处理渲染时，大部分组件都遵循组件树的层级关系进行渲染，而Portal则提供了一种"捷径"，使组件内容可以被渲染到DOM树中的其他位置。

实现上，createPortal接收两个参数：要渲染的React子元素和目标DOM容器。函数返回一个特殊的React元素类型（ReactPortal），这个元素会被React的协调算法特殊处理。

在React的调和过程中，当遇到ReactPortal类型的元素时，React不会将其子元素渲染到当前组件树的DOM位置，而是将这些子元素渲染到指定的DOM容器中。同时，React维护了一个内部映射，跟踪Portal元素与其渲染位置的关系。

关键的是，虽然DOM结构被改变，但React组件树的层次结构保持不变。这就是为什么Portal内部的事件可以正常冒泡到React组件树中的父组件 —— React的事件系统基于组件树而非DOM树实现。`,

  visualization: `graph TD
    A[React渲染流程] --> B[创建元素树]
    B --> C{元素类型?}
    C -->|普通元素| D[按组件层级渲染到DOM]
    C -->|Portal元素| E[特殊处理流程]
    
    E --> F[提取子元素和容器信息]
    F --> G[创建ReactPortal对象]
    G --> H[React内部标记为Portal类型]
    
    H --> I[调和阶段]
    I --> J{检测到Portal?}
    J -->|是| K[渲染子元素到指定容器]
    J -->|否| L[常规DOM更新]
    
    K --> M[维护内部映射关系]
    M --> N[保持事件冒泡路径完整]
    
    subgraph 组件树与DOM树分离
    O[React组件树] -.- P[父组件]
    P -.- Q[Portal组件]
    Q -.- R[子组件]
    
    S[DOM树] -.- T[父组件DOM]
    S -.- U[Portal目标容器]
    U -.- V[Portal内容DOM]
    
    Q -.事件冒泡.-> P
    end`,

  codeImplementation: `// 这是一个类似ReactDOM.createPortal的简化实现，展示其工作原理
function createPortalSimplified(children, container) {
  // 创建一个特殊类型的React元素
  return {
    // 特殊的Symbol标记这是一个Portal
    $$typeof: Symbol.for('react.portal'),
    
    // 保存子元素引用
    children: children,
    
    // 保存目标容器引用
    containerInfo: container,
    
    // 实现标准的React元素接口
    key: null,
    
    // Portal模式下没有实例
    stateNode: null
  };
}

// React调和器中处理Portal的简化逻辑
function reconcilePortalChildren(portalElement) {
  const { children, containerInfo } = portalElement;
  
  // 为Portal内容创建或更新DOM节点
  const childFibers = reconcileChildren(children);
  
  // 将生成的DOM节点挂载到指定容器，而非当前位置
  childFibers.forEach(fiber => {
    if (fiber.dom) {
      containerInfo.appendChild(fiber.dom);
    }
  });
  
  // 在内部映射中记录此Portal的组件树路径
  // 这对于正确处理事件冒泡至关重要
  trackPortalTreePath(portalElement);
}

// 事件系统中处理Portal事件冒泡的简化逻辑
function getEventTargetFiber(domEventTarget) {
  // 从事件目标DOM节点查找对应的Fiber
  const targetFiber = findFiberFromDOM(domEventTarget);
  
  // 检查目标是否在Portal内部
  if (isInsidePortal(targetFiber)) {
    // 获取Portal的React组件树路径，而非DOM树路径
    return getPortalTreePath(targetFiber);
  }
  
  // 正常情况下返回DOM路径对应的Fiber路径
  return targetFiber;
}`,

  workflowDiagram: `sequenceDiagram
    participant App as 应用组件
    participant React as React渲染器
    participant DOM as DOM树
    
    App->>React: 渲染包含createPortal的组件
    React->>React: 创建元素树
    React->>React: 检测到Portal元素
    React->>React: 正常处理组件树结构
    React->>DOM: 渲染非Portal组件到原始位置
    React->>DOM: 渲染Portal内容到指定容器
    
    Note over React,DOM: Portal渲染完成，DOM树与组件树结构不同
    
    DOM-->>App: 触发Portal内部事件
    React->>React: 计算事件路径（基于组件树）
    React->>App: 事件冒泡到父组件`,

  architectureDiagram: `graph TD
    A[React架构中的Portal] --> B[ReactDOM.createPortal]
    A --> C[React协调器]
    A --> D[React事件系统]
    
    B --> B1[创建特殊React元素]
    B --> B2[设置$$typeof标记]
    B --> B3[存储容器引用]
    
    C --> C1[识别Portal元素]
    C --> C2[特殊处理Portal渲染]
    C --> C3[管理Portal映射表]
    
    D --> D1[合成事件系统]
    D --> D2[事件传播路径计算]
    D --> D3[Portal事件桥接]
    
    subgraph 核心组件关系
    E[ReactDOM] --> F[渲染器]
    F --> G[协调器]
    G --> H[Fiber架构]
    H --> I[调度器]
    end`,

  dataFlowDiagram: `graph LR
    A[应用代码] -->|调用| B[createPortal API]
    B -->|创建| C[ReactPortal元素]
    C -->|进入| D[React更新队列]
    D -->|触发| E[协调过程]
    
    E -->|创建/更新| F[Fiber节点]
    F -->|标记特殊类型| G[Portal Fiber]
    G -->|提交阶段| H[DOM操作]
    
    H -->|1. 渲染组件| I[常规DOM更新]
    H -->|2. 处理Portal| J[目标容器更新]
    
    K[事件触发] -->|从DOM节点| L[合成事件系统]
    L -->|查找对应Fiber| M[事件处理映射]
    M -->|确定路径| N[按组件树传播]`,

  internalMechanism: `createPortal内部机制的核心在于React将"元素的逻辑位置"与"实际DOM渲染位置"解耦。这种解耦是通过几个关键步骤实现的：

1. **ReactPortal对象创建**
   创建特殊的ReactPortal对象，使用Symbol.for('react.portal')作为标识，并存储子元素和目标容器信息。

2. **协调阶段特殊处理**
   React在协调阶段识别Portal类型的元素，为其创建特殊的Fiber节点，并记录其原始位置和目标容器的映射关系。

3. **提交阶段DOM操作**
   当React将变更提交到DOM时，对于Portal节点，它不会将其追加到父Fiber对应的DOM节点，而是将其追加到指定的容器元素。

4. **事件系统桥接**
   React维护了一个从DOM节点到Fiber节点的映射，这使得即使Portal内容被渲染到DOM树中的其他位置，事件系统仍能找到对应的Fiber节点并按照React组件树的结构正确传播事件。

5. **生命周期和上下文保持**
   尽管DOM结构被改变，Portal组件仍然参与正常的React生命周期，能够访问父组件提供的Context，并受父组件的状态更新影响。

这种机制使得Portal成为React中实现特定UI模式（如模态框、提示框、悬浮菜单）的强大工具，同时保持了React编程模型的一致性。`,

  usageExample: `// 一个使用createPortal的完整例子，展示其工作机制

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';

// Modal组件，使用Portal将内容渲染到body
function Modal({ isOpen, onClose, title, children }) {
  const [modalRoot, setModalRoot] = useState(null);
  
  // 创建和清理模态框容器
  useEffect(() => {
    // 创建专门的DOM容器
    const modalRootElement = document.createElement('div');
    modalRootElement.className = 'modal-root';
    document.body.appendChild(modalRootElement);
    
    // 保存引用
    setModalRoot(modalRootElement);
    
    // 清理函数
    return () => {
      document.body.removeChild(modalRootElement);
    };
  }, []);
  
  // 事件处理：防止事件冒泡到父元素
  const handleContentClick = (e) => {
    // 阻止事件冒泡，防止触发backdrop的点击事件
    e.stopPropagation();
  };
  
  // 如果模态框未打开或容器不存在，则不渲染
  if (!isOpen || !modalRoot) return null;
  
  // 使用createPortal渲染模态框内容到body
  return createPortal(
    <div className="modal-backdrop" onClick={onClose}>
      <div className="modal-container" onClick={handleContentClick}>
        <div className="modal-header">
          <h3>{title}</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        <div className="modal-content">
          {children}
        </div>
        <div className="modal-footer">
          <button onClick={onClose}>关闭</button>
        </div>
      </div>
    </div>,
    modalRoot
  );
}

// 使用示例
function App() {
  const [showModal, setShowModal] = useState(false);
  const [count, setCount] = useState(0);
  
  // 演示事件冒泡：模态框内的按钮点击会导致计数增加
  const handleButtonClick = () => {
    setCount(prev => prev + 1);
    console.log('按钮被点击，计数增加');
  };
  
  return (
    <div className="app">
      <h1>Portal演示</h1>
      <p>当前计数: {count}</p>
      
      <button onClick={() => setShowModal(true)}>
        打开模态框
      </button>
      
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="Portal事件演示"
      >
        <p>这个模态框使用createPortal渲染到body元素中。</p>
        <p>尽管DOM结构上不是子元素，但React事件仍然按组件树冒泡。</p>
        
        {/* 这个按钮的点击事件会冒泡到App组件 */}
        <div onClick={handleButtonClick}>
          <button>
            点击这里增加计数（演示事件冒泡）
          </button>
        </div>
      </Modal>
    </div>
  );
}`,

  bestPractices: `基于createPortal内部实现机制的最佳实践：

1. **谨慎创建容器元素**
   - 推荐在应用初始化时创建专用的Portal容器元素，而不是动态创建和销毁
   - 对于多个Portal，考虑共用同一个容器元素，减少DOM操作

2. **正确管理Portal生命周期**
   - 在组件卸载时确保清理Portal相关的DOM元素，避免内存泄漏
   - 使用useEffect处理Portal容器的创建与清理

3. **合理处理事件传播**
   - 了解事件仍按React组件树冒泡的特性，谨慎使用stopPropagation()
   - 需要阻止事件冒泡时，在正确的层级调用stopPropagation()

4. **避免过度使用**
   - 仅在确实需要打破DOM层级限制时使用Portal
   - 过度使用会使应用结构变得难以理解和维护

5. **结合React Context**
   - Portal内的组件可以访问外部Context，利用这一特性传递主题、状态等
   - 避免通过props层层传递数据到Portal组件`
};

export default implementation; 