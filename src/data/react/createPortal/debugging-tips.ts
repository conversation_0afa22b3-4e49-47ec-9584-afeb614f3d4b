import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'ReactDOM.createPortal 在使用过程中会遇到一些常见问题，了解这些问题和解决方案能帮助开发者更有效地使用此API。',
        sections: [
          {
            title: '事件冒泡和上下文问题',
            description: '使用Portal时最常见的困惑是事件冒泡和React上下文传递问题',
            items: [
              {
                title: '事件冒泡不符合预期',
                description: '开发者经常困惑于Portal中的事件如何冒泡到React组件树而非DOM树',
                solution: '理解Portal的工作原理：Portal只改变DOM渲染位置，不改变React组件树结构。事件会沿React组件树冒泡，而非DOM树',
                prevention: '在设计使用Portal的组件时，提前考虑事件传播路径，并在文档中明确说明事件冒泡的行为',
                code: `// ❌ 错误理解：认为事件会沿DOM树冒泡
const ModalPortal = ({ children }) => {
  return ReactDOM.createPortal(
    React.createElement('div', { className: 'modal' }, children),
    document.body
  );
};

// 父组件
function App() {
  const handleClick = () => {
    console.log("这个事件处理器实际上会被触发！");
  };
  
  return React.createElement(
    'div',
    { onClick: handleClick },
    React.createElement(ModalPortal, null,
      React.createElement('button', null, '点击我') // 点击会触发App的handleClick
    )
  );
}

// ✅ 正确理解：在需要时阻止事件冒泡
function ModalWithEventHandling() {
  const handleModalClick = (e) => {
    // 阻止事件冒泡到React父组件
    e.stopPropagation();
    console.log("Modal内部点击，不会冒泡");
  };
  
  return ReactDOM.createPortal(
    React.createElement('div', { className: 'modal', onClick: handleModalClick },
      React.createElement('div', { className: 'modal-content' }, 'Modal内容')
    ),
    document.body
  );
}`
              },
              {
                title: 'Context未能正常工作',
                description: 'Portal中的组件无法访问父组件的Context',
                solution: '确认Context Provider包裹了Portal组件本身，而不仅是Portal渲染的目标位置',
                prevention: '在使用Portal时，总是确保Context Provider在组件树中的正确位置',
                code: `// ❌ 错误用法：Context Provider位置错误
function App() {
  return React.createElement('div', null,
    React.createElement(ThemeProvider, { value: 'dark' },
      React.createElement('button', null, '常规按钮')
    ),
    ReactDOM.createPortal(
      React.createElement('div', null,
        React.createElement(ThemedButton) // 无法访问ThemeContext
      ),
      document.body
    )
  );
}

// ✅ 正确用法：Context Provider包裹Portal组件
function App() {
  return React.createElement(ThemeProvider, { value: 'dark' },
    React.createElement('div', null,
      React.createElement('button', null, '常规按钮'),
      ReactDOM.createPortal(
        React.createElement('div', null,
          React.createElement(ThemedButton) // 可以正常访问ThemeContext
        ),
        document.body
      )
    )
  );
}`
              }
            ]
          },
          {
            title: '生命周期和清理问题',
            description: '使用Portal时容易忽视DOM节点的创建和清理，导致内存泄漏或意外行为',
            items: [
              {
                title: '容器节点未正确清理导致内存泄漏',
                description: '动态创建的Portal容器没有在组件卸载时被移除',
                solution: '使用useEffect的清理函数移除动态创建的DOM节点',
                prevention: '创建自定义Portal Hook封装创建和清理逻辑',
                code: `// ❌ 错误用法：没有清理DOM节点
function Modal({ isOpen }) {
  const portalNode = document.createElement('div');
  
  useEffect(() => {
    document.body.appendChild(portalNode);
    // 缺少清理函数
  }, []);
  
  if (!isOpen) return null;
  
  return ReactDOM.createPortal(
    React.createElement('div', null, 'Modal内容'),
    portalNode
  );
}

// ✅ 正确用法：正确创建和清理DOM节点
function Modal({ isOpen }) {
  const [portalNode] = useState(() => document.createElement('div'));
  
  useEffect(() => {
    document.body.appendChild(portalNode);
    
    // 组件卸载时清理DOM节点
    return () => {
      document.body.removeChild(portalNode);
    };
  }, [portalNode]);
  
  if (!isOpen) return null;
  
  return ReactDOM.createPortal(
    React.createElement('div', null, 'Modal内容'),
    portalNode
  );
}

// 🌟 最佳实践：封装自定义Hook
function usePortal(id) {
  const rootElemRef = useRef(null);
  
  useEffect(() => {
    const existingParent = document.querySelector('#' + id);
    const parentElem = existingParent || createPortalRoot(id);
    
    if (!existingParent) {
      document.body.appendChild(parentElem);
    }
    
    parentElem.appendChild(rootElemRef.current);
    
    return () => {
      rootElemRef.current.remove();
      if (!parentElem.childElementCount) {
        parentElem.remove();
      }
    };
  }, [id]);
  
  function createPortalRoot(id) {
    const root = document.createElement('div');
    root.id = id;
    return root;
  }
  
  function getRootElem() {
    if (!rootElemRef.current) {
      rootElemRef.current = document.createElement('div');
    }
    return rootElemRef.current;
  }
  
  return getRootElem();
}`
              },
              {
                title: 'SSR环境中的Portal不兼容',
                description: '在服务器渲染环境中使用Portal会导致错误，因为document对象不存在',
                solution: '使用条件渲染，仅在客户端环境中渲染Portal',
                prevention: '创建安全的SSR兼容Portal组件，检测运行环境',
                code: `// ❌ 错误用法：SSR环境直接使用Portal
function Modal({ children }) {
  // 在SSR环境会失败，因为document未定义
  return ReactDOM.createPortal(
    React.createElement('div', { className: 'modal' }, children),
    document.body
  );
}

// ✅ 正确用法：SSR安全的Portal组件
function SSRSafePortal({ children, containerSelector = 'body' }) {
  const [isMounted, setIsMounted] = useState(false);
  
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  // 服务端渲染时不执行Portal
  if (!isMounted) return null;
  
  const container = document.querySelector(containerSelector);
  if (!container) return null;
  
  return ReactDOM.createPortal(children, container);
}

// 使用方式
function App() {
  return React.createElement('div', null,
    React.createElement(SSRSafePortal, null,
      React.createElement('div', { className: 'modal' }, '模态框内容')
    )
  );
}`
              }
            ]
          },
          {
            title: 'Portal与状态管理',
            description: 'Portal组件的状态管理和更新可能变得复杂',
            items: [
              {
                title: 'Portal内外状态不同步',
                description: '当Portal内部和外部组件需要共享状态时，容易导致状态不同步问题',
                solution: '将状态提升到共同的父组件，或使用Context/Redux等全局状态管理',
                prevention: '设计清晰的状态流，确保Portal组件有明确的数据来源',
                code: `// ❌ 错误模式：Portal内外状态分离
function App() {
  const [count, setCount] = useState(0);
  
  return React.createElement('div', null,
    React.createElement('button', 
      { onClick: () => setCount(count + 1) },
      \`父组件中增加计数: \${count}\`
    ),
    ReactDOM.createPortal(
      React.createElement('div', null,
        React.createElement(Counter) // 有自己的独立状态
      ),
      document.body
    )
  );
}

function Counter() {
  // 独立状态，与App中的count不同步
  const [count, setCount] = useState(0);
  
  return React.createElement('button',
    { onClick: () => setCount(count + 1) },
    \`Portal内部计数: \${count}\`
  );
}

// ✅ 正确模式：状态提升或Context共享
function App() {
  const [count, setCount] = useState(0);
  
  return React.createElement(CountContext.Provider, 
    { value: { count, setCount } },
    React.createElement('div', null,
      React.createElement('button',
        { onClick: () => setCount(count + 1) },
        \`父组件中增加计数: \${count}\`
      ),
      ReactDOM.createPortal(
        React.createElement('div', null,
          React.createElement(ConnectedCounter) // 使用共享状态
        ),
        document.body
      )
    )
  );
}

function ConnectedCounter() {
  const { count, setCount } = useContext(CountContext);
  
  return React.createElement('button',
    { onClick: () => setCount(count + 1) },
    \`Portal内部计数: \${count}\`
  );
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🛠️ 调试工具',
      content: {
        introduction: '使用适当的调试工具可以帮助开发者更有效地解决createPortal相关问题。',
        sections: [
          {
            title: 'React DevTools与Portal',
            description: 'React DevTools对Portal组件的特殊处理和调试技巧',
            items: [
              {
                title: '使用React DevTools定位Portal组件',
                description: 'React DevTools可以显示Portal组件在React组件树中的实际位置',
                solution: '使用React DevTools查看Portal组件与其父组件的关系，验证组件树结构',
                tips: [
                  'Portal组件在React DevTools中会有特殊标记(⟿)',
                  '可以通过DevTools确认事件冒泡路径',
                  '检查Context提供者是否正确包含Portal组件'
                ]
              },
              {
                title: 'DevTools中检查Portal渲染目标',
                description: '验证Portal内容是否正确渲染到目标DOM节点',
                solution: '使用浏览器Elements面板与React DevTools结合，确认渲染位置',
                steps: [
                  '在React DevTools中找到Portal组件',
                  '查看组件的"rendered at"信息',
                  '在Elements面板中确认内容渲染位置',
                  '验证CSS样式是否按预期应用'
                ]
              }
            ]
          },
          {
            title: 'Portal专用调试技巧',
            description: '用于诊断和解决Portal特定问题的技术',
            items: [
              {
                title: '验证Portal事件冒泡',
                description: '确认Portal事件冒泡是否按预期工作',
                solution: '使用事件监听器和日志确认事件传播路径',
                code: `// 调试Portal事件冒泡
function DebugPortalEvents() {
  // 在各个层级添加事件监听，观察冒泡顺序
  const logEvent = (level) => (e) => {
    console.log('Event reached ' + level);
    // 不阻止冒泡，以观察完整传播路径
  };
  
  return React.createElement('div',
    { onClick: logEvent('Parent div'), style: { padding: 20 } },
    '外层容器',
    React.createElement('button',
      { onClick: logEvent('Parent button') },
      '父组件按钮'
    ),
    ReactDOM.createPortal(
      React.createElement('div',
        { onClick: logEvent('Portal outer div'), style: { padding: 20 } },
        'Portal容器',
        React.createElement('button',
          { onClick: logEvent('Portal button') },
          'Portal内按钮'
        )
      ),
      document.body
    )
  );
}

// 在控制台中观察点击Portal内按钮时的输出顺序：
// 1. "Event reached Portal button"
// 2. "Event reached Portal outer div"
// 3. "Event reached Parent div" (注意不是父DOM节点，而是React组件树中的父节点)`
              },
              {
                title: '动态检查Portal容器',
                description: '验证Portal容器元素是否正确创建和管理',
                solution: '实现辅助函数来监控Portal容器的生命周期',
                code: `// Portal容器调试助手
function debugPortalContainer(containerId) {
  const container = document.getElementById(containerId) || document.querySelector('#' + containerId);
  
  console.log('Portal容器状态:', {
    exists: !!container,
    childrenCount: container ? container.childElementCount : 0,
    content: container ? container.innerHTML.substring(0, 100) + '...' : 'N/A',
    styles: container ? getComputedStyle(container) : 'N/A'
  });
  
  if (container) {
    // 监控容器变化
    const observer = new MutationObserver((mutations) => {
      console.log('Portal容器变化:', 
        mutations.map(m => ({
          type: m.type,
          addedNodes: m.addedNodes.length,
          removedNodes: m.removedNodes.length
        }))
      );
    });
    
    observer.observe(container, { 
      childList: true, 
      subtree: true,
      attributes: true
    });
    
    console.log('已设置Portal容器监控');
    
    return () => observer.disconnect();
  }
  
  return () => console.log('无容器可监控');
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'troubleshooting',
      title: '🔍 排查指南',
      content: {
        introduction: '系统性的createPortal问题排查流程和常见症状诊断。',
        sections: [
          {
            title: '常见症状诊断',
            description: '根据常见现象快速定位createPortal问题',
            items: [
              {
                title: 'Portal内容没有显示',
                description: '内容完全没有渲染或不可见',
                solution: '系统性检查Portal渲染过程中的每个环节',
                steps: [
                  '确认目标容器存在且已挂载到DOM',
                  '检查条件渲染逻辑，确保Portal确实被渲染',
                  '验证CSS样式，确保内容可见（检查z-index、position、display等）',
                  '检查Portal内组件是否有错误导致渲染失败',
                  '使用React DevTools确认组件树结构'
                ],
                code: `// 排查Portal不显示的问题
function TroubleshootPortal() {
  const [container, setContainer] = useState(null);
  
  useEffect(() => {
    // 步骤1: 确认容器元素
    const portalContainer = document.getElementById('portal-root');
    console.log('Portal容器:', portalContainer);
    setContainer(portalContainer);
    
    if (!portalContainer) {
      console.error('Portal容器不存在，创建一个');
      const newContainer = document.createElement('div');
      newContainer.id = 'portal-root';
      document.body.appendChild(newContainer);
      setContainer(newContainer);
    }
  }, []);
  
  // 步骤2: 验证条件渲染逻辑
  if (!container) {
    console.log('容器不可用，Portal未渲染');
    return null;
  }
  
  // 步骤3: 添加可见性调试样式
  const debugStyles = {
    border: '2px solid red',
    padding: '20px',
    background: 'rgba(255,0,0,0.1)',
    position: 'fixed',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 9999
  };
  
  // 返回Portal以进行调试
  return ReactDOM.createPortal(
    React.createElement('div', { style: debugStyles },
      React.createElement('h2', null, 'Portal调试内容'),
      React.createElement('p', null, '如果看到此内容，Portal渲染成功')
    ),
    container
  );
}`
              },
              {
                title: 'Portal与第三方库冲突',
                description: 'Portal与其他库或框架的集成问题',
                solution: '隔离问题并创建最小复现示例',
                steps: [
                  '创建不使用Portal的简化版本，确认第三方库正常工作',
                  '逐步添加Portal相关代码，找出冲突点',
                  '检查DOM操作冲突（两者可能在操作同一DOM元素）',
                  '验证事件处理系统冲突（事件被其他库拦截）',
                  '考虑时序问题（Portal挂载时机与第三方库初始化顺序）'
                ],
                code: `// 与第三方库集成的Portal调试模式
function DebugThirdPartyIntegration() {
  const portalRef = useRef(null);
  const [debugMode, setDebugMode] = useState(0);
  
  useEffect(() => {
    // 模拟第三方库初始化
    const initThirdParty = () => {
      console.log('第三方库初始化');
      // 模拟第三方库可能影响Portal的操作
      const portalContainer = document.getElementById('modal-root');
      if (portalContainer) {
        console.log('第三方库检测到Portal容器');
      }
    };
    
    switch(debugMode) {
      case 0:
        console.log('模式0: 不初始化第三方库，不使用Portal');
        break;
      case 1:
        console.log('模式1: 先初始化第三方库，不使用Portal');
        initThirdParty();
        break;
      case 2:
        console.log('模式2: 不初始化第三方库，使用Portal');
        // Portal将在render中使用
        break;
      case 3:
        console.log('模式3: 先初始化第三方库，再使用Portal');
        initThirdParty();
        // Portal将在render中使用
        break;
      case 4:
        setTimeout(() => {
          console.log('模式4: Portal后初始化第三方库');
          initThirdParty();
        }, 100);
        break;
    }
  }, [debugMode]);
  
  // 提供UI切换不同调试模式
  const renderControls = () => React.createElement('div',
    { className: 'debug-controls' },
    React.createElement('h3', null, 'Portal与第三方库集成调试'),
    React.createElement('div', { className: 'buttons' },
      [
        '不用库不用Portal',
        '用库不用Portal',
        '不用库用Portal',
        '先用库后用Portal',
        '先用Portal后用库'
      ].map((label, index) => 
        React.createElement('button',
          {
            key: index,
            onClick: () => setDebugMode(index),
            style: {
              fontWeight: debugMode === index ? 'bold' : 'normal',
              background: debugMode === index ? '#e0e0ff' : ''
            }
          },
          \`模式\${index}: \${label}\`
        )
      )
    )
  );
  
  // 根据不同调试模式返回内容
  return React.createElement('div', null,
    renderControls(),
    React.createElement('div',
      { ref: portalRef, className: 'content-area' },
      '普通内容区域'
    ),
    // 根据模式决定是否渲染Portal
    debugMode >= 2 && ReactDOM.createPortal(
      React.createElement('div',
        { className: 'portal-content' },
        \`Portal内容 - 模式\${debugMode}\`
      ),
      document.getElementById('modal-root') || document.body
    )
  );
}`
              }
            ]
          },
          {
            title: '性能问题排查',
            description: '解决Portal使用中的性能问题',
            items: [
              {
                title: '多余的Portal重渲染',
                description: 'Portal组件频繁重渲染导致性能下降',
                solution: '优化Portal组件的渲染策略，减少不必要的重渲染',
                prevention: '使用React.memo、useMemo等优化手段，缓存Portal内容和容器引用',
                code: `// 优化Portal渲染性能
import { memo, useMemo } from 'react';

// 使用React.memo减少不必要的重渲染
const MemoizedModalContent = memo(function ModalContent({ title, content }) {
  console.log('ModalContent render');
  return React.createElement('div',
    { className: 'modal-content' },
    React.createElement('h2', null, title),
    React.createElement('p', null, content)
  );
});

function OptimizedPortalExample({ isOpen, title, content }) {
  // 缓存Portal容器引用
  const portalContainer = useMemo(() => {
    return document.getElementById('portal-root') || document.body;
  }, []);
  
  // 仅在必要内容变化时才重新创建Portal内容
  const portalContent = useMemo(() => {
    console.log('Recreating portal content');
    return React.createElement('div',
      { className: 'modal-wrapper' },
      React.createElement(MemoizedModalContent, { title, content }),
      React.createElement('button', null, '关闭')
    );
  }, [title, content]);
  
  // 仅在显示状态变化时才创建/销毁Portal
  if (!isOpen) return null;
  
  return ReactDOM.createPortal(portalContent, portalContainer);
}

// 使用React DevTools Profiler验证渲染优化效果`
              }
            ]
          }
        ]
      }
    }
  ],
  
  // 兼容旧版本的平铺结构
  troubleshooting: [
    {
      symptom: "Portal内容不显示或显示在错误位置",
      possibleCauses: [
        "目标容器元素不存在或未正确挂载到DOM",
        "容器元素CSS问题导致内容被隐藏",
        "Portal的条件渲染逻辑有误",
        "Portal内组件有错误导致渲染失败"
      ],
      solutions: [
        "确认目标容器存在并已挂载到DOM树中",
        "检查容器元素和Portal内容的CSS属性，特别是z-index、position、display和visibility",
        "使用React DevTools检查组件树和渲染情况",
        "添加错误边界捕获Portal内部可能的渲染错误"
      ]
    },
    {
      symptom: "Portal组件中的Context未能正常工作",
      possibleCauses: [
        "Context Provider没有包裹Portal组件本身",
        "错误地假设Context依赖于DOM层级而非React组件树",
        "多个Context实例导致引用不匹配"
      ],
      solutions: [
        "确保Context Provider在组件树中包裹创建Portal的组件",
        "检查Context是否使用了正确的Provider和Consumer对",
        "使用React DevTools查看Context的提供者和消费者关系"
      ]
    },
    {
      symptom: "Portal中的事件处理出现异常",
      possibleCauses: [
        "错误理解Portal的事件冒泡机制",
        "事件处理器中的this绑定问题",
        "忘记阻止事件冒泡导致意外行为"
      ],
      solutions: [
        "记住：事件沿React组件树冒泡，而非DOM树",
        "使用箭头函数或bind正确绑定事件处理器上下文",
        "适当使用e.stopPropagation()控制事件传播"
      ]
    }
  ]
};

export default debuggingTips; 