import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 1,
    title: "模态框/对话框实现",
    description: "使用createPortal创建模态框，将内容渲染到body末尾，解决z-index和定位问题",
    scenario: `在企业级应用中，模态框是最常见的UI元素之一。传统做法中，如果模态框组件在深层次的组件树内部，可能会受父组件CSS属性的影响（如overflow: hidden、transform等），导致显示异常。
    
使用createPortal可以将模态框内容直接渲染到body末尾，完全脱离原组件层级结构，避免样式干扰。同时，事件冒泡机制保持不变，可以正常向上传递到父组件中处理。

这种实现方式已成为React应用中模态框的标准模式，被广泛应用于企业管理系统、电商平台和各类Web应用中。`,
    businessValue: "提升用户体验，避免UI层叠错误，简化模态框实现逻辑，降低跨组件通信复杂度",
    code: `import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import './Modal.css';

const Modal = ({ isOpen, onClose, title, children }) => {
  // 如果模态框未打开，不渲染任何内容
  if (!isOpen) return null;
  
  // 创建Portal，渲染到body末尾
  return createPortal(
    React.createElement('div', { className: 'modal-backdrop' },
      React.createElement('div', { className: 'modal-container' },
        React.createElement('div', { className: 'modal-header' },
          React.createElement('h3', null, title),
          React.createElement('button', { className: 'close-button', onClick: onClose }, '×')
        ),
        React.createElement('div', { className: 'modal-content' }, children),
        React.createElement('div', { className: 'modal-footer' },
          React.createElement('button', { className: 'primary-button', onClick: onClose }, '确定'),
          React.createElement('button', { className: 'secondary-button', onClick: onClose }, '取消')
        )
      )
    ),
    document.body
  );
};

// 使用示例
function ProductPage() {
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  
  const handleDelete = () => {
    // 执行删除操作
    setShowDeleteModal(false);
  };
  
  return React.createElement('div', { className: 'product-page' },
    React.createElement('h1', null, '产品管理'),
    React.createElement('button', 
      { onClick: () => setShowDeleteModal(true) },
      '删除产品'
    ),
    React.createElement(Modal, {
      isOpen: showDeleteModal,
      onClose: () => setShowDeleteModal(false),
      title: '确认删除'
    },
      React.createElement('p', null, '您确定要删除这个产品吗？此操作无法撤销。')
    )
  );
}`,
    completionStatus: '内容已完成',
  },
  {
    id: 2,
    title: "全局通知/Toast系统",
    description: "使用createPortal构建全局通知系统，在应用任何位置触发消息提示",
    scenario: `企业应用中，操作反馈是提升用户体验的关键。用户执行关键操作（如保存、删除、提交）后，系统需要给予即时反馈。传统实现方式需要通过层层组件传递状态或使用全局状态管理，结构复杂。

通过createPortal实现的通知系统可以独立于组件树，在任何组件中都能轻松触发通知，不受嵌套层级限制。通知会渲染在统一的位置（通常是屏幕顶部或右上角），有统一的样式和行为管理。

这种模式特别适合大型企业应用，可以大大简化应用架构，降低组件间的耦合度。`,
    businessValue: "简化全局通知实现，统一用户操作反馈体验，提高开发效率和代码可维护性",
    code: `import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import './Toast.css';

// 创建一个单例Toast容器
let toastContainer;
if (typeof document !== 'undefined') {
  toastContainer = document.getElementById('toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    document.body.appendChild(toastContainer);
  }
}

// Toast组件
function Toast({ message, type = 'info', duration = 3000, onClose }) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, duration);
    
    return () => clearTimeout(timer);
  }, [duration, onClose]);
  
  return createPortal(
    React.createElement('div', { className: \`toast toast-\${type}\` },
      React.createElement('div', { className: 'toast-content' },
        type === 'success' && React.createElement('span', { className: 'icon' }, '✓'),
        type === 'error' && React.createElement('span', { className: 'icon' }, '✗'),
        type === 'info' && React.createElement('span', { className: 'icon' }, 'ℹ'),
        React.createElement('p', null, message)
      )
    ),
    toastContainer
  );
}

// Toast管理器
export const ToastManager = {
  toasts: [],
  listeners: [],
  
  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  },
  
  show(message, type, duration) {
    const id = Date.now();
    const toast = { id, message, type, duration };
    this.toasts.push(toast);
    this.listeners.forEach(listener => listener(this.toasts));
    
    return id;
  },
  
  hide(id) {
    this.toasts = this.toasts.filter(toast => toast.id !== id);
    this.listeners.forEach(listener => listener(this.toasts));
  },
  
  success(message, duration = 3000) {
    return this.show(message, 'success', duration);
  },
  
  error(message, duration = 3000) {
    return this.show(message, 'error', duration);
  },
  
  info(message, duration = 3000) {
    return this.show(message, 'info', duration);
  }
};

// Toast容器组件
export function ToastContainer() {
  const [toasts, setToasts] = useState([]);
  
  useEffect(() => {
    const unsubscribe = ToastManager.subscribe(setToasts);
    return unsubscribe;
  }, []);
  
  return toasts.map(toast => 
    React.createElement(Toast, {
      key: toast.id,
      message: toast.message,
      type: toast.type,
      duration: toast.duration,
      onClose: () => ToastManager.hide(toast.id)
    })
  );
}

// 使用示例
function App() {
  return React.createElement(React.Fragment, null,
    React.createElement('div', { className: 'app' },
      React.createElement('button', 
        { onClick: () => ToastManager.success('操作成功!') },
        '显示成功提示'
      ),
      React.createElement('button', 
        { onClick: () => ToastManager.error('操作失败!') },
        '显示错误提示'
      )
    ),
    React.createElement(ToastContainer)
  );
}`,
    completionStatus: '内容已完成',
  },
  {
    id: 3,
    title: "第三方库集成与内容注入",
    description: "使用createPortal将React组件渲染到第三方DOM容器中，实现与地图、编辑器等组件的无缝集成",
    scenario: `企业级应用通常需要集成各种第三方库和组件，如地图、图表、富文本编辑器等。这些库往往有自己的DOM渲染机制，需要React与它们进行交互，但直接渲染可能会导致冲突。

createPortal提供了一种优雅的解决方案，允许React组件被渲染到由第三方库创建和管理的DOM容器中。这保持了React组件的所有特性，同时避免了框架冲突。

例如，在GIS应用中，地图库（如Leaflet、MapboxGL）会创建自己的容器元素，我们可以使用createPortal将React组件（如信息窗口、控制面板）渲染到这些容器中，而不破坏地图库的功能。`,
    businessValue: "简化与第三方库的集成，减少代码冲突，提高开发效率，确保用户体验一致性",
    code: `import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { Map, TileLayer, Marker } from 'leaflet';
import 'leaflet/dist/leaflet.css';

// 自定义信息窗口组件
function InfoWindow({ map, position, onClose, children }) {
  const containerRef = useRef(null);
  
  // 首次渲染时创建信息窗口容器
  useEffect(() => {
    // 创建自定义的DOM元素作为信息窗口容器
    const container = document.createElement('div');
    container.className = 'custom-info-window';
    containerRef.current = container;
    
    // 使用Leaflet的DivIcon将DOM元素添加到地图上
    const icon = L.divIcon({
      html: container,
      className: 'custom-info-icon',
      iconSize: [280, 150]
    });
    
    // 创建标记并添加到地图
    const marker = L.marker(position, { icon }).addTo(map);
    
    return () => {
      // 清理，从地图中移除标记
      map.removeLayer(marker);
    };
  }, [map, position]);
  
  // 使用createPortal将React内容渲染到信息窗口容器中
  return containerRef.current 
    ? createPortal(
        React.createElement('div', { className: 'info-window-content' },
          React.createElement('button', { className: 'close-btn', onClick: onClose }, '×'),
          children
        ),
        containerRef.current
      )
    : null;
}

// 地图组件
function MapComponent() {
  const mapRef = useRef(null);
  const mapContainerRef = useRef(null);
  const [showInfo, setShowInfo] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState([51.505, -0.09]);
  
  // 初始化地图
  useEffect(() => {
    if (!mapRef.current) {
      const map = new Map(mapContainerRef.current).setView([51.505, -0.09], 13);
      new TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
      
      // 点击地图上的点，显示信息窗口
      map.on('click', (e) => {
        setSelectedLocation([e.latlng.lat, e.latlng.lng]);
        setShowInfo(true);
      });
      
      mapRef.current = map;
    }
  }, []);
  
  return React.createElement('div', { className: 'map-container' },
    React.createElement('div', {
      ref: mapContainerRef,
      style: { height: '600px', width: '100%' }
    }),
    // 使用createPortal渲染信息窗口
    showInfo && mapRef.current && React.createElement(InfoWindow, {
      map: mapRef.current,
      position: selectedLocation,
      onClose: () => setShowInfo(false)
    },
      React.createElement('h3', null, '位置信息'),
      React.createElement('p', null, \`纬度: \${selectedLocation[0].toFixed(4)}\`),
      React.createElement('p', null, \`经度: \${selectedLocation[1].toFixed(4)}\`),
      React.createElement('div', { className: 'actions' },
        React.createElement('button', null, '查看详情'),
        React.createElement('button', null, '添加标记')
      )
    )
  );
}

export default MapComponent;`,
    completionStatus: '内容已完成',
  }
];

export default businessScenarios; 