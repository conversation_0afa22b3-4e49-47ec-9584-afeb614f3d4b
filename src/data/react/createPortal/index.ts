import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const createPortalData: ApiItem = {
  id: 'createPortal',
  title: 'createPortal',
  description: 'ReactDOM API，用于将子组件渲染到DOM树中的任意位置，实现模态框等脱离普通文档流的UI元素。',
  category: 'ReactDOM APIs',
  difficulty: 'medium',
  
  syntax: `// 基础语法
createPortal(children, container)

// TypeScript接口
function createPortal(
  children: React.ReactNode,
  container: Element | DocumentFragment,
  key?: null | string
): React.ReactPortal;

// 使用示例
return createPortal(
  React.createElement('div', null, 'Modal content'),
  document.body
);`,

  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default createPortalData; 