import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 1,
    question: '什么是prefetchDNS？它与preconnect有什么区别？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'prefetchDNS是React DOM中用于DNS预解析的函数，只进行域名到IP地址的解析，而preconnect会建立完整的网络连接（DNS+TCP+TLS）。',
      detailed: `prefetchDNS是React 18.0+引入的ReactDOM网络优化API，专门用于DNS预解析。

**主要区别：**
1. **功能范围**：prefetchDNS只进行DNS解析，preconnect建立完整连接
2. **资源消耗**：prefetchDNS消耗更少，preconnect消耗更多
3. **性能提升**：prefetchDNS提升较小，preconnect提升更大
4. **使用场景**：prefetchDNS适合可能访问的域名，preconnect适合确定访问的域名

**使用时机：**
- 备用服务和可选资源：使用prefetchDNS
- 确定会访问的服务：使用preconnect
- 大量可能的域名：使用prefetchDNS
- 关键业务服务：使用preconnect

**核心优势：**
- 轻量级DNS优化
- 适合大规模使用
- 为故障切换做准备
- 资源消耗较少`,
      code: `// prefetchDNS vs preconnect 对比
function DNSOptimizationExample() {
  useEffect(() => {
    // ✅ prefetchDNS：可能访问的域名
    prefetchDNS('https://backup-api.example.com');
    prefetchDNS('https://fallback-cdn.example.com');

    // ✅ preconnect：确定访问的域名
    preconnect('https://api.example.com', {
      crossOrigin: 'anonymous'
    });

    // ✅ 组合使用：先预解析，再预连接
    prefetchDNS('https://optional-service.com');

    // 根据条件决定是否预连接
    if (userNeedsOptionalService) {
      preconnect('https://optional-service.com');
    }
  }, []);

  const makeAPICall = async () => {
    // preconnect的连接已建立，立即发送请求
    const response = await fetch('https://api.example.com/data');
    return response.json();
  };

  const useBackupService = async () => {
    // prefetchDNS的DNS已解析，连接建立更快
    const response = await fetch('https://backup-api.example.com/data');
    return response.json();
  };
}`
    },
    tags: ['基础概念', 'API对比']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 2,
    question: 'prefetchDNS在什么场景下最有效？如何设计DNS预解析策略？',
    difficulty: 'medium',
    frequency: 'high',
    category: '应用场景',
    answer: {
      brief: 'prefetchDNS在备用服务、可选资源、多CDN容灾、国际化应用等场景下最有效，需要根据服务重要性、访问概率和资源消耗设计分层预解析策略。',
      detailed: `prefetchDNS的最佳应用场景和策略设计原则：

**最有效的场景：**
1. **备用服务准备**：为故障切换预解析备用域名
2. **可选资源优化**：预解析可能使用的第三方服务
3. **多CDN容灾**：预解析所有CDN域名
4. **国际化应用**：预解析全球服务端点

**策略设计原则：**
1. **分层预解析**：按重要性和概率分层
2. **时机控制**：在合适的时间进行预解析
3. **数量控制**：避免过度预解析
4. **监控优化**：跟踪效果并持续优化

**实施策略：**
- 应用启动时：预解析核心备用服务
- 用户行为时：预解析相关可选服务
- 空闲时间：预解析低优先级域名
- 网络条件好时：增加预解析数量`,
      code: `// DNS预解析策略设计
class DNSPreloadStrategy {
  constructor() {
    this.preloadLayers = {
      critical: [], // 关键备用服务
      important: [], // 重要可选服务
      optional: [] // 可选第三方服务
    };
    this.preloadedDomains = new Set();
  }

  addDomain(url, layer, probability = 0.5) {
    this.preloadLayers[layer].push({ url, probability });
  }

  execute() {
    // 立即预解析关键备用服务
    this.preloadLayers.critical.forEach(({ url }) => {
      this.preloadDomain(url, 'critical');
    });

    // 延迟预解析重要服务
    setTimeout(() => {
      this.preloadLayers.important.forEach(({ url, probability }) => {
        if (probability > 0.3) {
          this.preloadDomain(url, 'important');
        }
      });
    }, 1000);

    // 空闲时预解析可选服务
    requestIdleCallback(() => {
      this.preloadLayers.optional.forEach(({ url, probability }) => {
        if (probability > 0.1) {
          this.preloadDomain(url, 'optional');
        }
      });
    });
  }

  preloadDomain(url, layer) {
    if (!this.preloadedDomains.has(url)) {
      prefetchDNS(url);
      this.preloadedDomains.add(url);
      console.log(\`DNS preloaded (\${layer}): \${url}\`);
    }
  }

  adaptToNetworkCondition() {
    if (navigator.connection) {
      const { effectiveType, saveData } = navigator.connection;

      if (saveData || effectiveType === 'slow-2g') {
        // 慢网络：只预解析关键服务
        this.preloadLayers.important = [];
        this.preloadLayers.optional = [];
      } else if (effectiveType === '4g') {
        // 快网络：可以预解析更多
        this.execute();
      }
    }
  }
}

// 使用示例
function StrategicDNSPreload() {
  const [strategy] = useState(() => new DNSPreloadStrategy());

  useEffect(() => {
    // 配置预解析策略
    strategy.addDomain('https://backup-api.example.com', 'critical', 0.8);
    strategy.addDomain('https://fallback-cdn.example.com', 'critical', 0.7);

    strategy.addDomain('https://analytics.example.com', 'important', 0.6);
    strategy.addDomain('https://support.example.com', 'important', 0.4);

    strategy.addDomain('https://social-login.example.com', 'optional', 0.3);
    strategy.addDomain('https://chat-widget.example.com', 'optional', 0.2);

    // 根据网络条件执行策略
    strategy.adaptToNetworkCondition();
  }, []);
}`
    },
    tags: ['应用场景', '策略设计']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 3,
    question: '在微服务架构中如何设计prefetchDNS的全面DNS优化方案？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    answer: {
      brief: '微服务架构的DNS优化需要考虑服务发现、依赖管理、故障切换、监控告警等多个维度，建立自动化的DNS预解析管理系统。',
      detailed: `微服务架构的全面DNS优化是一个复杂的系统工程，需要综合考虑多个因素。

**设计原则：**
1. **服务分类管理**：按服务类型和重要性分类
2. **依赖关系分析**：理解服务间的依赖关系
3. **故障切换准备**：为每个服务准备备用方案
4. **自动化管理**：建立自动化的DNS管理系统

**核心组件设计：**
1. **服务注册中心**：管理所有服务的DNS信息
2. **依赖分析器**：分析服务间的依赖关系
3. **DNS预解析调度器**：智能调度DNS预解析
4. **健康监控器**：监控服务健康状态

**实施策略：**
1. 启动时预解析核心依赖服务
2. 运行时根据调用模式动态预解析
3. 故障时快速切换到备用服务
4. 持续监控和优化预解析策略

**监控指标：**
- DNS解析命中率
- 服务调用延迟
- 故障切换时间
- 系统整体性能`,
      code: `// 微服务DNS优化架构方案
class MicroservicesDNSOptimizer {
  constructor() {
    this.serviceRegistry = new Map();
    this.dependencyGraph = new Map();
    this.dnsPreloadScheduler = new DNSPreloadScheduler();
    this.healthMonitor = new ServiceHealthMonitor();
  }

  async initialize() {
    // 注册所有服务
    await this.registerServices();

    // 分析依赖关系
    this.analyzeDependencies();

    // 启动DNS预解析
    this.startDNSPreloading();

    // 启动健康监控
    this.healthMonitor.start();
  }

  registerServices() {
    const services = {
      // 核心业务服务
      core: {
        'user-service': {
          primary: 'https://user-service.prod.com',
          backup: 'https://user-service.backup.com'
        },
        'order-service': {
          primary: 'https://order-service.prod.com',
          backup: 'https://order-service.backup.com'
        }
      },

      // 第三方服务
      external: {
        'payment-gateway': {
          stripe: 'https://api.stripe.com',
          paypal: 'https://api.paypal.com'
        },
        'notification': {
          twilio: 'https://api.twilio.com',
          sendgrid: 'https://api.sendgrid.com'
        }
      },

      // 基础设施服务
      infrastructure: {
        'monitoring': {
          datadog: 'https://api.datadoghq.com',
          newrelic: 'https://api.newrelic.com'
        }
      }
    };

    Object.entries(services).forEach(([category, categoryServices]) => {
      Object.entries(categoryServices).forEach(([service, endpoints]) => {
        this.serviceRegistry.set(service, {
          category,
          endpoints,
          dependencies: [],
          priority: this.calculatePriority(category, service)
        });
      });
    });
  }

  analyzeDependencies() {
    // 分析服务依赖关系
    this.serviceRegistry.forEach((service, serviceName) => {
      const dependencies = this.discoverDependencies(serviceName);
      service.dependencies = dependencies;
      this.dependencyGraph.set(serviceName, dependencies);
    });
  }

  startDNSPreloading() {
    // 按优先级预解析服务
    const sortedServices = Array.from(this.serviceRegistry.entries())
      .sort(([,a], [,b]) => b.priority - a.priority);

    sortedServices.forEach(([serviceName, service], index) => {
      setTimeout(() => {
        this.preloadServiceDNS(serviceName, service);
      }, index * 100); // 错开预解析时机
    });
  }

  preloadServiceDNS(serviceName, service) {
    Object.values(service.endpoints).forEach(endpoint => {
      if (typeof endpoint === 'string') {
        prefetchDNS(endpoint);
      } else {
        Object.values(endpoint).forEach(url => {
          prefetchDNS(url);
        });
      }
    });

    // 预解析依赖服务
    service.dependencies.forEach(depService => {
      const depServiceInfo = this.serviceRegistry.get(depService);
      if (depServiceInfo) {
        this.preloadServiceDNS(depService, depServiceInfo);
      }
    });
  }

  async callService(serviceName, endpoint, data) {
    const service = this.serviceRegistry.get(serviceName);
    if (!service) {
      throw new Error(\`Service not found: \${serviceName}\`);
    }

    const primaryUrl = service.endpoints.primary;

    try {
      // DNS已预解析，服务调用更快
      const response = await fetch(\`\${primaryUrl}\${endpoint}\`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      return response.json();
    } catch (error) {
      // 故障切换到备用服务
      return this.failoverToBackup(serviceName, endpoint, data);
    }
  }

  async failoverToBackup(serviceName, endpoint, data) {
    const service = this.serviceRegistry.get(serviceName);
    const backupUrl = service.endpoints.backup;

    if (backupUrl) {
      try {
        const response = await fetch(\`\${backupUrl}\${endpoint}\`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });

        console.log(\`Failover successful for \${serviceName}\`);
        return response.json();
      } catch (backupError) {
        throw new Error(\`Both primary and backup failed for \${serviceName}\`);
      }
    }

    throw new Error(\`No backup available for \${serviceName}\`);
  }

  calculatePriority(category, service) {
    const categoryPriority = {
      core: 10,
      external: 7,
      infrastructure: 5
    };

    return categoryPriority[category] || 1;
  }

  discoverDependencies(serviceName) {
    // 模拟依赖发现逻辑
    const dependencyMap = {
      'order-service': ['user-service', 'payment-gateway'],
      'user-service': ['notification'],
      'payment-gateway': ['monitoring']
    };

    return dependencyMap[serviceName] || [];
  }
}

// 使用示例
function MicroservicesApplication() {
  const [dnsOptimizer] = useState(() => new MicroservicesDNSOptimizer());

  useEffect(() => {
    dnsOptimizer.initialize();
  }, []);

  return <div>微服务应用</div>;
}`
    },
    tags: ['架构设计', '微服务']
  }
];

export default interviewQuestions;