import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: "prefetchDNS是React DOM中用于DNS预解析的函数，专门用于提前解析外部域名的IP地址，将域名转换为IP地址并缓存结果，为后续的网络连接建立做好准备，减少DNS查询延迟。",

  introduction: `prefetchDNS是React 18.0+引入的ReactDOM网络优化API，专门为现代Web应用的DNS性能优化设计的预解析函数。

它遵循现代Web性能优化的最佳实践，在DNS解析和网络连接之间做出了智能优化，允许开发者提前声明可能需要访问的外部域名，让浏览器在空闲时间进行DNS预解析。

主要用于可能访问的域名预解析、备用服务准备和网络性能优化。相比完整的preconnect，它的优势在于资源消耗更少，适合预解析大量可能访问的域名。

在React生态中，它是Resource Preloading APIs的基础组成部分，常见于需要优化大量外部域名访问的应用，特别适合需要准备多个备用服务或可选资源的场景。

核心优势包括减少DNS查询延迟、优化网络连接准备时间、提升用户体验，同时资源消耗较少，适合大规模使用。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react-dom/index.d.ts:32
 * - 实现文件：packages/react-dom/src/shared/ReactDOMResourceUtils.js:42
 * - 内部类型：packages/react-dom-bindings/src/shared/ReactDOMResourceValidation.js:18
 */

// 基础语法
function prefetchDNS(href: string): void;

// 使用示例
prefetchDNS('https://api.example.com');
prefetchDNS('https://backup-cdn.example.com');
prefetchDNS('https://fonts.googleapis.com');

/**
 * 参数约束：
 * - href 必须是有效的域名URL
 * - 只进行DNS解析，不建立TCP连接
 * - 同一个域名多次调用会被去重
 * - 适合预解析大量可能访问的域名
 */`,

  quickExample: `function PrefetchDNSExample() {
  useEffect(() => {
    // 预解析可能访问的API域名
    prefetchDNS('https://api.example.com');
    prefetchDNS('https://backup-api.example.com');

    // 预解析可能使用的CDN域名
    prefetchDNS('https://cdn.example.com');
    prefetchDNS('https://images.example.com');

    // 预解析第三方服务域名
    prefetchDNS('https://analytics.google.com');
    prefetchDNS('https://fonts.googleapis.com');

    // 预解析备用服务域名
    prefetchDNS('https://backup-cdn.example.com');
    prefetchDNS('https://fallback-api.example.com');
  }, []);

  const handleAPICall = async () => {
    // DNS已预解析，连接建立更快
    const response = await fetch('https://api.example.com/data');
    const data = await response.json();
    setData(data);
  };

  const loadBackupResource = async () => {
    // 备用域名DNS已预解析，故障切换更快
    try {
      const response = await fetch('https://api.example.com/resource');
      return response.json();
    } catch (error) {
      // 切换到备用API，DNS已预解析
      const backupResponse = await fetch('https://backup-api.example.com/resource');
      return backupResponse.json();
    }
  };

  return (
    <div>
      {/* DNS已预解析，网络请求响应更快 */}
      <button onClick={handleAPICall}>
        获取数据 (DNS已预解析)
      </button>
      <button onClick={loadBackupResource}>
        加载资源 (含备用方案)
      </button>
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "prefetchDNS在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用这个API来优化DNS解析性能",
      diagram: `graph LR
      A[prefetchDNS核心场景] --> B[备用服务准备]
      A --> C[可选资源预解析]
      A --> D[大规模域名优化]

      B --> B1["🔄 备用API<br/>故障切换准备"]
      B --> B2["🛡️ 容灾服务<br/>灾备域名预解析"]

      C --> C1["📦 可选CDN<br/>多CDN选择准备"]
      C --> C2["🎨 第三方服务<br/>可能使用的外部服务"]

      D --> D1["🌐 多区域部署<br/>全球域名预解析"]
      D --> D2["📊 分析服务<br/>多个分析工具准备"]

      style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
      style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
      style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "DNS解析优化流程",
      description: "prefetchDNS的DNS解析优化流程，展示其如何通过提前解析域名来减少后续连接的延迟",
      diagram: `graph TB
      A[prefetchDNS调用] --> B[域名验证]
      B --> C[DNS查询发起]
      C --> D[域名解析]
      D --> E[IP地址缓存]

      F[后续网络请求] --> G[DNS缓存查找]
      G --> H[直接使用IP]
      G --> I[跳过DNS解析]

      E -.-> G

      J[preconnect调用] --> K[使用已解析IP]
      K --> L[直接建立连接]

      E -.-> K

      style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
      style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
      style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px
      style E fill:#e8eaf6,stroke:#3f51b5,stroke-width:3px
      style F fill:#ffebee,stroke:#d32f2f,stroke-width:2px
      style G fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
      style H fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px`
    },
    {
      title: "Resource Preloading层次结构",
      description: "prefetchDNS在React Resource Preloading APIs层次结构中的位置和与其他优化技术的关系",
      diagram: `graph TD
      A[Resource Preloading APIs] --> B[DNS层]
      A --> C[连接层]
      A --> D[资源层]
      A --> E[初始化层]

      B --> B1["prefetchDNS<br/>DNS预解析"]

      C --> C1["preconnect<br/>完整连接建立"]

      D --> D1["preload<br/>通用资源预加载"]
      D --> D2["preloadModule<br/>ES模块预加载"]

      E --> E1["preinit<br/>通用资源预初始化"]
      E --> E2["preinitModule<br/>ES模块预初始化"]

      B1 -.-> C1
      C1 -.-> D1
      C1 -.-> D2
      D1 -.-> E1
      D2 -.-> E2

      style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
      style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
      style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style E fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
      style B1 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
    }
  ],
  
  parameters: [
    {
      name: "href",
      type: "string",
      required: true,
      description: "要进行DNS预解析的域名URL。必须是有效的域名地址，支持HTTP和HTTPS协议。",
      example: `prefetchDNS('https://api.example.com');
prefetchDNS('https://backup-cdn.example.com');`
    }
  ],

  returnValue: {
    type: "void",
    description: "prefetchDNS不返回任何值，它是一个纯粹的副作用函数，用于向浏览器发出DNS预解析的指令。",
    example: `// prefetchDNS没有返回值
prefetchDNS('https://api.example.com');
// 函数执行后，浏览器开始解析指定域名的IP地址`
  },

  keyFeatures: [
    {
      title: "DNS预解析优化",
      description: "提前解析域名的IP地址并缓存结果，为后续网络连接做好准备",
      benefit: "减少DNS查询延迟，加快网络连接建立速度"
    },
    {
      title: "轻量级网络优化",
      description: "相比preconnect，只进行DNS解析，资源消耗更少，适合大规模使用",
      benefit: "在资源消耗和性能提升之间找到最佳平衡点"
    },
    {
      title: "备用服务准备",
      description: "特别适合预解析备用服务和可选资源的域名，为故障切换做好准备",
      benefit: "提升应用的容错能力和故障恢复速度"
    },
    {
      title: "智能去重机制",
      description: "自动去重相同域名的预解析请求，避免重复操作",
      benefit: "优化网络资源使用，避免不必要的DNS查询"
    },
    {
      title: "React集成优化",
      description: "与React的资源管理系统深度集成，提供声明式的DNS优化方案",
      benefit: "简化DNS优化的实现复杂度，提升开发效率"
    },
    {
      title: "广泛浏览器支持",
      description: "基于成熟的DNS prefetch标准，在各种浏览器中都有良好支持",
      benefit: "确保DNS优化在不同环境中的一致性和可靠性"
    }
  ],

  limitations: [
    "只能在React 18.0+版本中使用，在旧版本中不可用",
    "只进行DNS解析，不建立TCP连接或TLS握手，性能提升相对有限",
    "在服务端渲染环境中，prefetchDNS调用会被忽略，只在客户端生效",
    "DNS解析结果的缓存时间由DNS服务器的TTL设置决定，无法控制缓存时长",
    "对于已经访问过的域名，DNS缓存可能已存在，预解析效果不明显"
  ],

  bestPractices: [
    "优先预解析可能访问但不确定的域名，如备用服务和可选资源",
    "在应用启动时预解析多个备用域名，为故障切换做好准备",
    "结合preconnect使用：先prefetchDNS预解析，再preconnect建立连接",
    "预解析第三方服务域名，如分析工具、广告服务、社交媒体插件等",
    "在移动网络环境下谨慎使用，避免不必要的网络请求",
    "建立DNS预解析的监控机制，跟踪预解析效果和命中率",
    "根据用户地理位置预解析相应区域的服务域名",
    "预解析多CDN域名，为CDN切换和负载均衡做好准备"
  ],

  warnings: [
    "不要预解析过多域名，虽然资源消耗较少，但仍会产生网络开销",
    "确保预解析的域名是真正可能访问的，无效域名会浪费资源但不会报错",
    "注意DNS解析的时效性，预解析后应在合理时间内使用",
    "在隐私敏感的环境中，DNS预解析可能泄露用户的潜在访问意图",
    "避免预解析恶意或不可信的域名，可能带来安全风险"
  ]
};

export default basicInfo;