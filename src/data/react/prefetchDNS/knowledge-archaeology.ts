import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  introduction: `prefetchDNS的诞生标志着Web DNS优化技术从"被动解析"向"主动预解析"的历史性转变。这不仅仅是一个API的添加，而是整个DNS性能优化理念的基础性演进。通过深入挖掘其历史背景、技术演进和设计理念，我们可以更好地理解现代Web DNS优化的精髓和发展方向。`,

  background: `prefetchDNS的出现源于Web应用对分布式服务依赖的日益增长和DNS解析延迟对用户体验的影响。在Web早期，应用主要访问单一域名，DNS解析相对简单。随着微服务架构和第三方服务集成的普及，应用需要访问大量不同的域名，DNS解析延迟成为新的性能瓶颈。`,

  evolution: `prefetchDNS的演进历程体现了Web平台对DNS性能优化的不断探索：从早期的手动DNS管理，到DNS prefetch标准的制定，再到React框架层面的智能DNS预解析API，反映了Web技术从手动到自动，从通用到专用的发展轨迹。`,

  timeline: [
    {
      year: '2009',
      event: 'DNS prefetch概念提出',
      description: 'Mozilla首次在Firefox中实现DNS prefetch功能',
      significance: '为现代DNS预解析技术奠定了基础'
    },
    {
      year: '2011',
      event: 'HTML5标准化DNS prefetch',
      description: 'DNS prefetch被纳入HTML5标准，成为Web标准的一部分',
      significance: '确立了DNS预解析的技术标准和实现规范'
    },
    {
      year: '2022',
      event: 'React 18.0引入prefetchDNS',
      description: 'prefetchDNS作为ReactDOM的DNS优化API首次出现',
      significance: '标志着框架开始承担DNS优化的责任'
    }
  ],

  keyFigures: [
    {
      name: 'Patrick McManus',
      role: 'Mozilla网络工程师',
      contribution: '在Firefox中首次实现DNS prefetch功能，推动了DNS预解析技术的发展',
      significance: '他将DNS优化理论转化为浏览器实现，为现代DNS预解析技术奠定了基础'
    }
  ],

  concepts: [
    {
      term: 'DNS预解析(DNS Prefetching)',
      definition: '提前解析域名的IP地址并缓存结果，为后续网络连接做好准备',
      evolution: '从手动DNS管理发展为声明式的DNS预解析优化',
      modernRelevance: '现代Web应用DNS性能优化的基础技术，特别是在多服务集成场景中'
    }
  ],

  designPhilosophy: `prefetchDNS的设计哲学体现了现代Web开发对DNS性能的深度思考：预测优于响应、轻量优于重载、智能优于盲目。`,

  impact: `prefetchDNS对Web开发生态系统产生了重要影响：推动了DNS prefetch标准的普及，提升了框架的DNS管理能力，改变了开发者的DNS优化思路。`,

  modernRelevance: `在当今的Web开发环境中，prefetchDNS的重要性日益凸显：微服务架构成为主流，第三方服务集成越来越多，DNS解析延迟对用户体验的影响越来越大。`
};

export default knowledgeArchaeology;