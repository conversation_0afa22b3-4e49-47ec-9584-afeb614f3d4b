import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-1',
    question: 'prefetchDNS预解析的域名会缓存多长时间？如何控制缓存时长？',
    answer: `prefetchDNS的DNS缓存时长由DNS服务器的TTL（Time To Live）设置决定，无法通过API直接控制。

**DNS缓存机制：**
1. **TTL控制**：DNS记录的TTL值决定缓存时长，通常为几分钟到几小时
2. **浏览器缓存**：浏览器会遵循DNS服务器的TTL设置
3. **系统缓存**：操作系统也有自己的DNS缓存机制
4. **ISP缓存**：网络服务提供商也会缓存DNS记录

**优化策略：**
- 合理设置DNS记录的TTL值
- 在TTL过期前重新调用prefetchDNS
- 监控DNS解析的有效性
- 根据业务需求调整预解析频率

**最佳实践：**
- 对于稳定的服务，可以设置较长的TTL
- 对于可能变化的服务，设置较短的TTL
- 定期重新预解析重要的域名`,
    code: `// DNS缓存管理策略
class DNSCacheManager {
  constructor() {
    this.preloadedDomains = new Map();
    this.defaultTTL = 300000; // 5分钟默认TTL
  }

  preloadWithTTL(domain, customTTL) {
    const ttl = customTTL || this.defaultTTL;

    prefetchDNS(domain);

    this.preloadedDomains.set(domain, {
      timestamp: Date.now(),
      ttl: ttl
    });

    // 在TTL过期前重新预解析
    setTimeout(() => {
      this.refreshDNS(domain);
    }, ttl * 0.8); // 在80%时间点刷新
  }

  refreshDNS(domain) {
    if (this.preloadedDomains.has(domain)) {
      console.log(\`Refreshing DNS for: \${domain}\`);
      prefetchDNS(domain);

      const entry = this.preloadedDomains.get(domain);
      entry.timestamp = Date.now();
    }
  }

  isValid(domain) {
    const entry = this.preloadedDomains.get(domain);
    if (!entry) return false;

    return (Date.now() - entry.timestamp) < entry.ttl;
  }
}

// 使用示例
function DNSCacheExample() {
  const [cacheManager] = useState(() => new DNSCacheManager());

  useEffect(() => {
    // 预解析不同TTL的域名
    cacheManager.preloadWithTTL('https://api.example.com', 600000); // 10分钟
    cacheManager.preloadWithTTL('https://backup-api.example.com', 300000); // 5分钟
  }, []);
}`,
    tags: ['DNS缓存', 'TTL管理'],
    relatedQuestions: ['如何监控DNS解析效果？', '如何处理DNS解析失败？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-2',
    question: 'prefetchDNS会影响用户隐私吗？在隐私敏感的应用中如何使用？',
    answer: `prefetchDNS确实可能涉及隐私问题，因为DNS查询可能泄露用户的潜在访问意图。

**隐私影响：**
1. **DNS查询记录**：ISP和DNS服务器会记录查询历史
2. **访问意图泄露**：预解析的域名可能暴露用户可能的行为
3. **第三方追踪**：某些DNS服务可能用于用户追踪
4. **网络监控**：网络管理员可能监控DNS查询

**隐私保护策略：**
- 只预解析确实需要的域名
- 避免预解析敏感或争议性的域名
- 使用加密DNS（DoH/DoT）
- 提供用户控制选项

**最佳实践：**
- 在隐私政策中说明DNS预解析的使用
- 提供禁用预解析的选项
- 定期审查预解析的域名列表
- 考虑用户的地理位置和法律要求`,
    code: `// 隐私友好的DNS预解析
class PrivacyAwareDNSPreloader {
  constructor() {
    this.userConsent = false;
    this.sensitivePatterns = [
      /adult/i,
      /gambling/i,
      /political/i
    ];
  }

  async requestUserConsent() {
    // 请求用户同意DNS预解析
    const consent = await this.showConsentDialog();
    this.userConsent = consent;
    return consent;
  }

  isSensitiveDomain(domain) {
    return this.sensitivePatterns.some(pattern =>
      pattern.test(domain)
    );
  }

  preloadWithPrivacyCheck(domain) {
    // 检查用户同意
    if (!this.userConsent) {
      console.log('DNS preload skipped: no user consent');
      return;
    }

    // 检查敏感域名
    if (this.isSensitiveDomain(domain)) {
      console.log('DNS preload skipped: sensitive domain');
      return;
    }

    // 检查隐私模式
    if (this.isPrivateMode()) {
      console.log('DNS preload skipped: private mode');
      return;
    }

    prefetchDNS(domain);
  }

  isPrivateMode() {
    // 检测隐私模式（简化实现）
    return window.navigator.doNotTrack === '1';
  }

  showConsentDialog() {
    return new Promise(resolve => {
      // 显示同意对话框
      const consent = confirm(
        '为了提升性能，我们希望预解析一些域名。这可能会向您的网络服务提供商泄露您可能访问的网站。您同意吗？'
      );
      resolve(consent);
    });
  }
}

// 使用示例
function PrivacyAwareExample() {
  const [preloader] = useState(() => new PrivacyAwareDNSPreloader());

  useEffect(() => {
    const initializePreloading = async () => {
      const hasConsent = await preloader.requestUserConsent();

      if (hasConsent) {
        // 只预解析必要的域名
        preloader.preloadWithPrivacyCheck('https://api.example.com');
        preloader.preloadWithPrivacyCheck('https://cdn.example.com');
      }
    };

    initializePreloading();
  }, []);
}`,
    tags: ['隐私保护', '用户同意'],
    relatedQuestions: ['如何检测隐私模式？', '如何实现用户控制选项？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-3',
    question: 'prefetchDNS在移动设备上的表现如何？需要特殊考虑吗？',
    answer: `prefetchDNS在移动设备上需要特别考虑网络条件、电池消耗和数据使用量。

**移动设备特殊考虑：**
1. **网络条件**：移动网络可能不稳定，DNS解析可能失败
2. **电池消耗**：频繁的网络操作会消耗电池
3. **数据使用**：DNS查询会消耗用户的数据流量
4. **网络切换**：WiFi和移动网络切换可能影响DNS缓存

**优化策略：**
- 检测网络类型和质量
- 在省电模式下减少预解析
- 考虑用户的数据节省设置
- 适应不同的网络条件

**最佳实践：**
- 在WiFi环境下更积极地预解析
- 在移动网络下只预解析关键域名
- 监控网络状态变化
- 提供用户控制选项`,
    code: `// 移动设备优化的DNS预解析
class MobileDNSPreloader {
  constructor() {
    this.networkInfo = null;
    this.isLowPowerMode = false;
  }

  initialize() {
    // 监听网络状态
    if ('connection' in navigator) {
      this.networkInfo = navigator.connection;
      this.networkInfo.addEventListener('change', () => {
        this.adaptToNetworkChange();
      });
    }

    // 监听电池状态
    if ('getBattery' in navigator) {
      navigator.getBattery().then(battery => {
        this.isLowPowerMode = battery.level < 0.2;
        battery.addEventListener('levelchange', () => {
          this.isLowPowerMode = battery.level < 0.2;
        });
      });
    }
  }

  shouldPreload(domain, priority = 'normal') {
    // 检查省电模式
    if (this.isLowPowerMode && priority !== 'critical') {
      return false;
    }

    // 检查数据节省模式
    if (this.networkInfo?.saveData) {
      return priority === 'critical';
    }

    // 检查网络类型
    if (this.networkInfo) {
      const { effectiveType, type } = this.networkInfo;

      if (type === 'cellular') {
        // 移动网络：只预解析高优先级
        return priority === 'critical' ||
               (priority === 'high' && effectiveType === '4g');
      }

      if (type === 'wifi') {
        // WiFi：可以预解析更多
        return true;
      }
    }

    return priority === 'critical';
  }

  preloadWithMobileOptimization(domain, priority = 'normal') {
    if (this.shouldPreload(domain, priority)) {
      prefetchDNS(domain);
      console.log(\`DNS preloaded (\${priority}): \${domain}\`);
    } else {
      console.log(\`DNS preload skipped (\${priority}): \${domain}\`);
    }
  }

  adaptToNetworkChange() {
    console.log('Network changed:', {
      type: this.networkInfo?.type,
      effectiveType: this.networkInfo?.effectiveType,
      saveData: this.networkInfo?.saveData
    });

    // 根据新的网络条件调整策略
    if (this.networkInfo?.type === 'wifi') {
      // 切换到WiFi，可以预解析更多域名
      this.preloadAdditionalDomains();
    }
  }

  preloadAdditionalDomains() {
    const additionalDomains = [
      { url: 'https://backup-api.example.com', priority: 'normal' },
      { url: 'https://analytics.example.com', priority: 'low' }
    ];

    additionalDomains.forEach(({ url, priority }) => {
      this.preloadWithMobileOptimization(url, priority);
    });
  }
}

// 使用示例
function MobileOptimizedExample() {
  const [mobilePreloader] = useState(() => new MobileDNSPreloader());

  useEffect(() => {
    mobilePreloader.initialize();

    // 预解析关键域名
    mobilePreloader.preloadWithMobileOptimization(
      'https://api.example.com',
      'critical'
    );

    // 预解析普通域名
    mobilePreloader.preloadWithMobileOptimization(
      'https://cdn.example.com',
      'normal'
    );
  }, []);
}`,
    tags: ['移动优化', '网络适配'],
    relatedQuestions: ['如何检测网络类型？', '如何监控电池状态？']
  }
];

export default commonQuestions;