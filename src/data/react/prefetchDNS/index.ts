import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const prefetchDNSData: ApiItem = {
  id: 'prefetchDNS',
  title: 'prefetchDNS',
  description: 'React DOM中用于DNS预解析的函数，专门用于提前解析外部域名的IP地址，将域名转换为IP地址并缓存结果，为后续的网络连接建立做好准备，减少DNS查询延迟。',
  category: 'ReactDOM Resource API',
  difficulty: 'easy',

  syntax: `prefetchDNS(href: string): void`,
  example: `prefetchDNS('https://api.example.com');
prefetchDNS('https://backup-cdn.example.com');`,
  notes: '专门用于DNS预解析，资源消耗较少，适合预解析大量可能访问的域名，是Resource Preloading APIs的基础组成部分。',

  version: 'React 18.0+',
  tags: ["ReactDOM", "Resource", "DNS", "Performance"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default prefetchDNSData;