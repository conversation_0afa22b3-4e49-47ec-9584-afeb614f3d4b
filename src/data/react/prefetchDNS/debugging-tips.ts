import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'prefetchDNS虽然API简单，但在实际使用中开发者经常遇到一些典型问题。本节提供完整的问题诊断和解决方案，帮助快速定位和修复prefetchDNS相关的DNS解析问题。',
        sections: [
          {
            title: 'DNS预解析无效果问题',
            description: '最常见的问题是prefetchDNS看起来没有任何效果，DNS解析时间没有改善。这通常涉及调用时机、域名有效性、浏览器支持等多个方面',
            items: [
              {
                title: 'DNS缓存已存在',
                description: '域名之前已经被访问过，DNS缓存已存在，预解析效果不明显',
                solution: '1. 测试时清除DNS缓存；2. 使用未访问过的域名进行测试；3. 监控DNS解析时间的变化',
                prevention: '在测试环境中使用专门的测试域名，避免使用已缓存的域名',
                code: `// 检测DNS缓存状态
function checkDNSCache(domain) {
  const start = performance.now();

  // 创建一个隐藏的图片元素触发DNS解析
  const img = new Image();
  img.onload = img.onerror = () => {
    const dnsTime = performance.now() - start;
    console.log(\`DNS resolution time for \${domain}: \${dnsTime}ms\`);

    if (dnsTime < 10) {
      console.log('DNS likely cached');
    } else {
      console.log('DNS resolution performed');
    }
  };

  img.src = \`\${domain}/favicon.ico?\${Date.now()}\`;
}

// 清除DNS缓存（仅开发环境）
function clearDNSCache() {
  if (process.env.NODE_ENV === 'development') {
    console.log('请手动清除浏览器DNS缓存：');
    console.log('Chrome: chrome://net-internals/#dns');
    console.log('Firefox: about:networking#dns');
  }
}`
              },
              {
                title: '域名无效或不可达',
                description: 'prefetchDNS的域名无效或网络不可达，导致DNS解析失败',
                solution: '1. 验证域名的有效性；2. 检查网络连接；3. 测试域名的可达性；4. 使用有效的测试域名',
                prevention: '在预解析前验证域名的有效性，建立域名健康检查机制',
                code: `// 域名有效性检查
async function validateDomain(domain) {
  try {
    const url = new URL(domain);

    // 检查域名格式
    if (!url.hostname) {
      throw new Error('Invalid domain format');
    }

    // 测试域名可达性
    const response = await fetch(\`\${domain}/\`, {
      method: 'HEAD',
      mode: 'no-cors',
      timeout: 5000
    });

    return true;
  } catch (error) {
    console.error(\`Domain validation failed for \${domain}:\`, error);
    return false;
  }
}

// 安全的DNS预解析
async function safePrefetchDNS(domain) {
  const isValid = await validateDomain(domain);

  if (isValid) {
    prefetchDNS(domain);
    console.log(\`DNS prefetch initiated for: \${domain}\`);
  } else {
    console.warn(\`Skipping DNS prefetch for invalid domain: \${domain}\`);
  }
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '有效的调试工具可以帮助开发者监控prefetchDNS的效果，诊断DNS问题，优化预解析策略。掌握这些工具是高效使用prefetchDNS的关键。',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '现代浏览器提供了强大的网络和DNS分析工具，可以直观地监控DNS预解析的效果',
            items: [
              {
                title: 'DNS解析监控',
                description: '使用浏览器开发者工具监控DNS解析的状态和时机',
                solution: '1. 打开开发者工具网络面板；2. 查看DNS解析时间；3. 分析预解析效果；4. 对比优化前后的差异',
                prevention: '定期检查DNS解析性能，确保预解析按预期工作',
                code: `// DNS解析性能监控
class DNSPerformanceMonitor {
  constructor() {
    this.measurements = new Map();
  }

  startMonitoring() {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name.startsWith('http')) {
          this.recordDNSMetrics(entry);
        }
      }
    });

    observer.observe({ entryTypes: ['resource'] });
  }

  recordDNSMetrics(entry) {
    const dnsTime = entry.domainLookupEnd - entry.domainLookupStart;
    const domain = new URL(entry.name).hostname;

    if (!this.measurements.has(domain)) {
      this.measurements.set(domain, []);
    }

    this.measurements.get(domain).push({
      timestamp: entry.startTime,
      dnsTime: dnsTime,
      cached: dnsTime === 0
    });

    console.log(\`DNS metrics for \${domain}:\`, {
      dnsTime: \`\${dnsTime}ms\`,
      cached: dnsTime === 0
    });
  }

  getDNSReport() {
    const report = {};

    this.measurements.forEach((metrics, domain) => {
      const avgDNSTime = metrics.reduce((sum, m) => sum + m.dnsTime, 0) / metrics.length;
      const cacheHitRate = metrics.filter(m => m.cached).length / metrics.length;

      report[domain] = {
        averageDNSTime: Math.round(avgDNSTime),
        cacheHitRate: Math.round(cacheHitRate * 100),
        totalRequests: metrics.length
      };
    });

    return report;
  }
}

// 使用示例
const dnsMonitor = new DNSPerformanceMonitor();
dnsMonitor.startMonitoring();

// 获取DNS性能报告
setTimeout(() => {
  console.log('DNS Performance Report:', dnsMonitor.getDNSReport());
}, 10000);`
              },
              {
                title: 'DNS预解析验证',
                description: '验证DNS预解析是否正确执行和生效',
                solution: '1. 检查DOM中的dns-prefetch标签；2. 监控DNS解析时间变化；3. 验证预解析的域名列表',
                prevention: '建立自动化的DNS预解析验证机制，确保预解析正确执行',
                code: `// DNS预解析验证工具
class DNSPrefetchValidator {
  constructor() {
    this.prefetchedDomains = new Set();
  }

  validatePrefetchTags() {
    const prefetchLinks = document.querySelectorAll('link[rel="dns-prefetch"]');
    const domains = Array.from(prefetchLinks).map(link => link.href);

    console.log('DNS prefetch tags found:', domains);
    return domains;
  }

  trackPrefetchCalls() {
    // 包装原始的prefetchDNS函数
    const originalPrefetchDNS = window.prefetchDNS;

    window.prefetchDNS = (href) => {
      this.prefetchedDomains.add(href);
      console.log(\`DNS prefetch called for: \${href}\`);
      return originalPrefetchDNS(href);
    };
  }

  validatePrefetchEffectiveness(domain) {
    return new Promise((resolve) => {
      const start = performance.now();

      fetch(\`\${domain}/\`, { method: 'HEAD', mode: 'no-cors' })
        .then(() => {
          const totalTime = performance.now() - start;
          resolve({
            domain,
            totalTime: Math.round(totalTime),
            effective: totalTime < 100 // 假设100ms以下为有效
          });
        })
        .catch(() => {
          resolve({
            domain,
            totalTime: null,
            effective: false
          });
        });
    });
  }

  async generateReport() {
    const prefetchedList = Array.from(this.prefetchedDomains);
    const domTags = this.validatePrefetchTags();

    const effectiveness = await Promise.all(
      prefetchedList.map(domain => this.validatePrefetchEffectiveness(domain))
    );

    return {
      prefetchedDomains: prefetchedList,
      domTags: domTags,
      effectiveness: effectiveness,
      summary: {
        totalPrefetched: prefetchedList.length,
        effectiveCount: effectiveness.filter(e => e.effective).length
      }
    };
  }
}

// 使用示例
const validator = new DNSPrefetchValidator();
validator.trackPrefetchCalls();

// 生成验证报告
setTimeout(async () => {
  const report = await validator.generateReport();
  console.log('DNS Prefetch Validation Report:', report);
}, 5000);`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;