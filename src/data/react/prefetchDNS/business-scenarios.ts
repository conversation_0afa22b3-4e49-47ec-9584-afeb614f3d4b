import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-1',
    title: '多CDN容灾备份系统DNS预解析',
    description: '在大型Web应用中，通常会配置多个CDN服务商作为备份，确保在主CDN出现问题时能够快速切换。使用prefetchDNS可以提前解析所有备用CDN的域名，为故障切换做好准备。',
    businessValue: '提升应用的可用性和容错能力，减少CDN故障对用户体验的影响，确保关键业务的连续性，特别适合对稳定性要求极高的企业级应用。',
    scenario: '用户访问应用时，主要使用主CDN加载资源。当主CDN出现故障或响应缓慢时，系统需要快速切换到备用CDN。传统方式会在切换时才开始DNS解析，导致额外的延迟。',
    code: `function MultiCDNDNSPreloader() {
  const [cdnStatus, setCdnStatus] = useState({});
  const [currentCDN, setCurrentCDN] = useState('primary');
  const [dnsPreloaded, setDnsPreloaded] = useState(new Set());

  // CDN配置
  const cdnConfig = {
    primary: 'https://cdn-primary.example.com',
    backup1: 'https://cdn-backup1.example.com',
    backup2: 'https://cdn-backup2.example.com',
    backup3: 'https://cdn-backup3.example.com',
    international: 'https://cdn-intl.example.com'
  };

  useEffect(() => {
    // 应用启动时预解析所有CDN域名
    Object.values(cdnConfig).forEach(cdnUrl => {
      prefetchDNS(cdnUrl);
      setDnsPreloaded(prev => new Set([...prev, cdnUrl]));
    });

    // 预解析其他可能的备用服务
    const additionalServices = [
      'https://backup-api.example.com',
      'https://fallback-images.example.com',
      'https://emergency-assets.example.com'
    ];

    additionalServices.forEach(service => {
      prefetchDNS(service);
      setDnsPreloaded(prev => new Set([...prev, service]));
    });
  }, []);

  useEffect(() => {
    // 监控CDN健康状态
    const monitorCDNHealth = async () => {
      const healthChecks = Object.entries(cdnConfig).map(async ([name, url]) => {
        try {
          const start = performance.now();
          await fetch(\`\${url}/health-check.txt\`, { method: 'HEAD' });
          const latency = performance.now() - start;

          return {
            name,
            url,
            status: 'healthy',
            latency: Math.round(latency)
          };
        } catch (error) {
          return {
            name,
            url,
            status: 'unhealthy',
            latency: null
          };
        }
      });

      const results = await Promise.all(healthChecks);
      const statusMap = {};
      results.forEach(result => {
        statusMap[result.name] = result;
      });

      setCdnStatus(statusMap);
    };

    monitorCDNHealth();
    const interval = setInterval(monitorCDNHealth, 30000); // 每30秒检查一次

    return () => clearInterval(interval);
  }, []);

  const selectOptimalCDN = () => {
    // 选择最优CDN
    const healthyCDNs = Object.entries(cdnStatus)
      .filter(([_, status]) => status.status === 'healthy')
      .sort((a, b) => a[1].latency - b[1].latency);

    if (healthyCDNs.length > 0) {
      return healthyCDNs[0][0];
    }

    return 'primary'; // 默认使用主CDN
  };

  const loadResourceWithFallback = async (resourcePath) => {
    const optimalCDN = selectOptimalCDN();
    const primaryUrl = \`\${cdnConfig[optimalCDN]}\${resourcePath}\`;

    try {
      // DNS已预解析，连接建立更快
      const response = await fetch(primaryUrl);
      if (response.ok) {
        return response;
      }
      throw new Error('Primary CDN failed');
    } catch (error) {
      // 故障切换到备用CDN，DNS已预解析
      for (const [name, url] of Object.entries(cdnConfig)) {
        if (name !== optimalCDN) {
          try {
            const fallbackResponse = await fetch(\`\${url}\${resourcePath}\`);
            if (fallbackResponse.ok) {
              console.log(\`Switched to backup CDN: \${name}\`);
              setCurrentCDN(name);
              return fallbackResponse;
            }
          } catch (fallbackError) {
            continue;
          }
        }
      }
      throw new Error('All CDNs failed');
    }
  };

  return (
    <div className="multi-cdn-system">
      <div className="dns-status">
        <h3>DNS预解析状态</h3>
        <p>已预解析域名: {dnsPreloaded.size}个</p>
        {Array.from(dnsPreloaded).map(domain => (
          <span key={domain} className="dns-badge">
            {domain.split('//')[1]} ✅
          </span>
        ))}
      </div>

      <div className="cdn-health">
        <h3>CDN健康状态</h3>
        {Object.entries(cdnStatus).map(([name, status]) => (
          <div key={name} className={\`cdn-item \${status.status}\`}>
            <span className="cdn-name">{name}</span>
            <span className="cdn-status">{status.status}</span>
            {status.latency && (
              <span className="cdn-latency">({status.latency}ms)</span>
            )}
          </div>
        ))}
      </div>

      <div className="current-cdn">
        <p>当前使用CDN: {currentCDN}</p>
        <button onClick={() => loadResourceWithFallback('/test-resource.js')}>
          测试资源加载 (含故障切换)
        </button>
      </div>
    </div>
  );
}`,
    explanation: '这个场景展示了如何在多CDN容灾系统中使用prefetchDNS预解析所有备用CDN域名。通过在应用启动时预解析所有可能的CDN域名，确保在主CDN出现故障时能够快速切换到备用CDN，显著减少故障切换的延迟时间。',
    benefits: [
      '显著减少CDN故障切换的延迟时间，提升应用可用性',
      '为多CDN容灾系统提供DNS层面的优化支持',
      '提升用户在网络故障情况下的使用体验',
      '支持智能CDN选择，根据健康状态和延迟选择最优CDN'
    ],
    metrics: {
      performance: 'CDN故障切换时间从2.1s减少到400ms，性能提升81%',
      userExperience: '应用可用性从99.5%提升到99.9%，用户满意度提升35%',
      technicalMetrics: 'DNS解析命中率达到95%，故障恢复时间减少75%'
    },
    difficulty: 'easy',
    tags: ['多CDN', '容灾备份', 'DNS优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-2',
    title: '国际化应用多区域服务DNS预解析',
    description: '在面向全球用户的国际化应用中，需要根据用户地理位置连接到不同区域的服务器。使用prefetchDNS可以预解析所有可能的区域服务域名，确保用户无论在哪个地区都能获得最佳的服务响应速度。',
    businessValue: '提升全球用户的使用体验，减少跨区域访问延迟，增强应用的国际竞争力，特别适合需要服务全球用户的SaaS平台和电商应用。',
    scenario: '用户可能来自世界各地，应用需要智能选择最近的服务器提供服务。同时还需要准备备用区域服务器以应对区域性网络故障。传统方式会在检测到用户位置后才开始DNS解析，增加了服务响应时间。',
    code: `function GlobalServiceDNSPreloader() {
  const [userRegion, setUserRegion] = useState('');
  const [serviceEndpoints, setServiceEndpoints] = useState({});
  const [dnsPreloadStatus, setDnsPreloadStatus] = useState({});

  // 全球服务端点配置
  const globalEndpoints = {
    'us-east': {
      api: 'https://api-us-east.example.com',
      cdn: 'https://cdn-us-east.example.com',
      websocket: 'wss://ws-us-east.example.com'
    },
    'us-west': {
      api: 'https://api-us-west.example.com',
      cdn: 'https://cdn-us-west.example.com',
      websocket: 'wss://ws-us-west.example.com'
    },
    'eu-central': {
      api: 'https://api-eu-central.example.com',
      cdn: 'https://cdn-eu-central.example.com',
      websocket: 'wss://ws-eu-central.example.com'
    },
    'asia-pacific': {
      api: 'https://api-ap-southeast.example.com',
      cdn: 'https://cdn-ap-southeast.example.com',
      websocket: 'wss://ws-ap-southeast.example.com'
    },
    'asia-east': {
      api: 'https://api-ap-east.example.com',
      cdn: 'https://cdn-ap-east.example.com',
      websocket: 'wss://ws-ap-east.example.com'
    }
  };

  useEffect(() => {
    // 应用启动时预解析所有区域服务域名
    Object.entries(globalEndpoints).forEach(([region, endpoints]) => {
      Object.values(endpoints).forEach(endpoint => {
        prefetchDNS(endpoint);
        setDnsPreloadStatus(prev => ({
          ...prev,
          [endpoint]: { region, status: 'preloaded', timestamp: Date.now() }
        }));
      });
    });

    // 预解析其他全球服务
    const globalServices = [
      'https://auth.example.com',
      'https://analytics.example.com',
      'https://support.example.com',
      'https://status.example.com'
    ];

    globalServices.forEach(service => {
      prefetchDNS(service);
      setDnsPreloadStatus(prev => ({
        ...prev,
        [service]: { region: 'global', status: 'preloaded', timestamp: Date.now() }
      }));
    });
  }, []);

  useEffect(() => {
    // 检测用户地理位置
    const detectUserRegion = async () => {
      try {
        // 使用多种方法检测用户位置
        const geoResponse = await fetch('/api/detect-location');
        const { region, country, timezone } = await geoResponse.json();

        setUserRegion(region);
        setServiceEndpoints(globalEndpoints[region] || globalEndpoints['us-east']);

        console.log('User detected in region:', region);
      } catch (error) {
        // 默认使用美东区域
        setUserRegion('us-east');
        setServiceEndpoints(globalEndpoints['us-east']);
      }
    };

    detectUserRegion();
  }, []);

  const selectOptimalEndpoint = async (serviceType) => {
    // 选择最优端点
    const candidates = Object.entries(globalEndpoints).map(([region, endpoints]) => ({
      region,
      endpoint: endpoints[serviceType]
    }));

    // 测试延迟并选择最优端点
    const latencyTests = candidates.map(async ({ region, endpoint }) => {
      try {
        const start = performance.now();
        await fetch(\`\${endpoint}/ping\`, { method: 'HEAD' });
        const latency = performance.now() - start;
        return { region, endpoint, latency };
      } catch (error) {
        return { region, endpoint, latency: Infinity };
      }
    });

    const results = await Promise.all(latencyTests);
    const optimal = results.reduce((best, current) =>
      current.latency < best.latency ? current : best
    );

    return optimal.endpoint;
  };

  const makeGlobalAPICall = async (path, data) => {
    // DNS已预解析，选择最优端点
    const optimalAPI = await selectOptimalEndpoint('api');

    try {
      const response = await fetch(\`\${optimalAPI}\${path}\`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      return response.json();
    } catch (error) {
      // 故障切换到备用区域
      const backupRegions = Object.keys(globalEndpoints).filter(r => r !== userRegion);

      for (const region of backupRegions) {
        try {
          const backupAPI = globalEndpoints[region].api;
          const response = await fetch(\`\${backupAPI}\${path}\`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          });

          console.log(\`Switched to backup region: \${region}\`);
          return response.json();
        } catch (backupError) {
          continue;
        }
      }

      throw new Error('All regions failed');
    }
  };

  return (
    <div className="global-service-system">
      <div className="region-info">
        <h3>用户区域信息</h3>
        <p>检测到的区域: {userRegion}</p>
        <p>当前API端点: {serviceEndpoints.api}</p>
      </div>

      <div className="dns-preload-status">
        <h3>DNS预解析状态</h3>
        <p>已预解析域名: {Object.keys(dnsPreloadStatus).length}个</p>
        <div className="endpoint-grid">
          {Object.entries(globalEndpoints).map(([region, endpoints]) => (
            <div key={region} className="region-group">
              <h4>{region}</h4>
              {Object.entries(endpoints).map(([service, endpoint]) => (
                <div key={endpoint} className="endpoint-item">
                  <span className="service-type">{service}</span>
                  <span className="endpoint-url">{endpoint.split('//')[1]}</span>
                  <span className="dns-status">
                    {dnsPreloadStatus[endpoint] ? '✅' : '⏳'}
                  </span>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>

      <div className="global-actions">
        <button onClick={() => makeGlobalAPICall('/user/profile', {})}>
          获取用户资料 (全球优化)
        </button>
        <button onClick={() => selectOptimalEndpoint('api')}>
          测试最优端点
        </button>
      </div>
    </div>
  );
}`,
    explanation: '这个场景展示了如何在国际化应用中使用prefetchDNS预解析全球服务域名。通过在应用启动时预解析所有区域的服务端点，确保用户无论在哪个地区都能快速连接到最优的服务器，同时为跨区域故障切换做好准备。',
    benefits: [
      '显著减少全球用户的服务访问延迟，提升国际化体验',
      '支持智能区域选择，根据网络延迟自动选择最优服务端点',
      '为跨区域故障切换提供DNS层面的优化支持',
      '提升应用的全球可用性和用户满意度'
    ],
    metrics: {
      performance: '跨区域服务访问时间从1.8s减少到350ms，性能提升81%',
      userExperience: '全球用户满意度提升48%，服务响应评分从7.2提升到9.1',
      technicalMetrics: '全球DNS解析命中率达到94%，区域故障切换时间减少70%'
    },
    difficulty: 'medium',
    tags: ['国际化', '多区域服务', '全球优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-3',
    title: '微服务架构第三方依赖DNS预解析',
    description: '在复杂的微服务架构中，应用依赖大量的第三方服务，如认证服务、支付网关、消息队列、监控系统等。使用prefetchDNS可以预解析所有可能的第三方服务域名，确保微服务间的通信和第三方集成都能获得最佳性能。',
    businessValue: '提升微服务架构的整体性能和稳定性，减少服务间通信延迟，增强系统的可靠性和响应速度，特别适合复杂的企业级微服务系统。',
    scenario: '微服务应用需要与多个第三方服务进行集成，包括身份认证、支付处理、数据分析、日志收集、监控告警等。这些服务可能来自不同的供应商，分布在不同的域名下。传统方式会在实际调用时才进行DNS解析，增加了服务调用的延迟。',
    code: `function MicroservicesDNSPreloader() {
  const [serviceRegistry, setServiceRegistry] = useState({});
  const [healthStatus, setHealthStatus] = useState({});
  const [dnsPreloadMap, setDnsPreloadMap] = useState(new Map());

  // 第三方服务配置
  const thirdPartyServices = {
    authentication: {
      primary: 'https://auth.okta.com',
      backup: 'https://auth.auth0.com'
    },
    payment: {
      stripe: 'https://api.stripe.com',
      paypal: 'https://api.paypal.com',
      square: 'https://connect.squareup.com'
    },
    messaging: {
      twilio: 'https://api.twilio.com',
      sendgrid: 'https://api.sendgrid.com',
      mailgun: 'https://api.mailgun.net'
    },
    analytics: {
      mixpanel: 'https://api.mixpanel.com',
      amplitude: 'https://api.amplitude.com',
      segment: 'https://api.segment.io'
    },
    monitoring: {
      datadog: 'https://api.datadoghq.com',
      newrelic: 'https://api.newrelic.com',
      sentry: 'https://sentry.io'
    },
    storage: {
      aws: 'https://s3.amazonaws.com',
      gcp: 'https://storage.googleapis.com',
      azure: 'https://blob.core.windows.net'
    }
  };

  // 内部微服务配置
  const internalServices = {
    'user-service': 'https://user-service.internal.com',
    'order-service': 'https://order-service.internal.com',
    'inventory-service': 'https://inventory-service.internal.com',
    'notification-service': 'https://notification-service.internal.com',
    'analytics-service': 'https://analytics-service.internal.com'
  };

  useEffect(() => {
    // 预解析所有第三方服务域名
    Object.entries(thirdPartyServices).forEach(([category, services]) => {
      Object.entries(services).forEach(([provider, url]) => {
        prefetchDNS(url);
        setDnsPreloadMap(prev => new Map([
          ...prev,
          [url, { category, provider, type: 'third-party', timestamp: Date.now() }]
        ]));
      });
    });

    // 预解析内部微服务域名
    Object.entries(internalServices).forEach(([service, url]) => {
      prefetchDNS(url);
      setDnsPreloadMap(prev => new Map([
        ...prev,
        [url, { service, type: 'internal', timestamp: Date.now() }]
      ]));
    });

    // 预解析其他可能的服务
    const additionalServices = [
      'https://api.github.com',
      'https://hooks.slack.com',
      'https://api.elasticsearch.com',
      'https://api.redis.com'
    ];

    additionalServices.forEach(url => {
      prefetchDNS(url);
      setDnsPreloadMap(prev => new Map([
        ...prev,
        [url, { type: 'additional', timestamp: Date.now() }]
      ]));
    });
  }, []);

  useEffect(() => {
    // 监控服务健康状态
    const monitorServices = async () => {
      const allServices = {
        ...Object.values(thirdPartyServices).reduce((acc, services) => ({ ...acc, ...services }), {}),
        ...internalServices
      };

      const healthChecks = Object.entries(allServices).map(async ([name, url]) => {
        try {
          const start = performance.now();
          const response = await fetch(\`\${url}/health\`, {
            method: 'HEAD',
            timeout: 5000
          });
          const latency = performance.now() - start;

          return {
            name,
            url,
            status: response.ok ? 'healthy' : 'degraded',
            latency: Math.round(latency)
          };
        } catch (error) {
          return {
            name,
            url,
            status: 'unhealthy',
            latency: null,
            error: error.message
          };
        }
      });

      const results = await Promise.all(healthChecks);
      const statusMap = {};
      results.forEach(result => {
        statusMap[result.url] = result;
      });

      setHealthStatus(statusMap);
    };

    monitorServices();
    const interval = setInterval(monitorServices, 60000); // 每分钟检查一次

    return () => clearInterval(interval);
  }, []);

  const makeServiceCall = async (serviceCategory, provider, endpoint, data) => {
    const serviceUrl = thirdPartyServices[serviceCategory]?.[provider];
    if (!serviceUrl) {
      throw new Error(\`Service not found: \${serviceCategory}.\${provider}\`);
    }

    try {
      // DNS已预解析，服务调用更快
      const response = await fetch(\`\${serviceUrl}\${endpoint}\`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': \`Bearer \${getServiceToken(serviceCategory, provider)}\`
        },
        body: JSON.stringify(data)
      });

      return response.json();
    } catch (error) {
      // 尝试备用服务
      const backupServices = Object.entries(thirdPartyServices[serviceCategory])
        .filter(([p]) => p !== provider);

      for (const [backupProvider, backupUrl] of backupServices) {
        try {
          const response = await fetch(\`\${backupUrl}\${endpoint}\`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': \`Bearer \${getServiceToken(serviceCategory, backupProvider)}\`
            },
            body: JSON.stringify(data)
          });

          console.log(\`Switched to backup service: \${backupProvider}\`);
          return response.json();
        } catch (backupError) {
          continue;
        }
      }

      throw new Error(\`All \${serviceCategory} services failed\`);
    }
  };

  const getServiceToken = (category, provider) => {
    // 模拟获取服务令牌
    return \`token-\${category}-\${provider}\`;
  };

  return (
    <div className="microservices-dns-system">
      <div className="dns-preload-summary">
        <h3>DNS预解析概览</h3>
        <p>已预解析域名: {dnsPreloadMap.size}个</p>
        <div className="service-categories">
          {Object.entries(thirdPartyServices).map(([category, services]) => (
            <div key={category} className="category-group">
              <h4>{category}</h4>
              <div className="service-list">
                {Object.entries(services).map(([provider, url]) => (
                  <div key={url} className="service-item">
                    <span className="provider">{provider}</span>
                    <span className="url">{url.split('//')[1]}</span>
                    <span className="dns-status">✅</span>
                    <span className="health-status">
                      {healthStatus[url]?.status || '⏳'}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="service-actions">
        <button onClick={() => makeServiceCall('payment', 'stripe', '/charges', {})}>
          调用支付服务 (DNS已预解析)
        </button>
        <button onClick={() => makeServiceCall('messaging', 'twilio', '/messages', {})}>
          发送消息 (DNS已预解析)
        </button>
        <button onClick={() => makeServiceCall('analytics', 'mixpanel', '/track', {})}>
          记录分析事件 (DNS已预解析)
        </button>
      </div>
    </div>
  );
}`,
    explanation: '这个场景展示了如何在微服务架构中使用prefetchDNS预解析所有第三方依赖服务的域名。通过在应用启动时预解析所有可能的第三方服务和内部微服务域名，确保服务间通信和第三方集成都能获得最佳性能，同时为服务故障切换做好准备。',
    benefits: [
      '显著减少微服务间通信和第三方服务调用的延迟',
      '提升复杂微服务架构的整体性能和响应速度',
      '为第三方服务故障切换提供DNS层面的优化支持',
      '增强系统的可靠性和容错能力',
      '支持大规模微服务部署的网络性能优化'
    ],
    metrics: {
      performance: '第三方服务调用时间从900ms减少到250ms，性能提升72%',
      userExperience: '系统响应速度提升58%，用户操作流畅度评分从7.5提升到9.3',
      technicalMetrics: '微服务DNS解析命中率达到96%，服务故障切换时间减少68%'
    },
    difficulty: 'hard',
    tags: ['微服务架构', '第三方集成', 'DNS优化', '企业级系统']
  }
];

export default businessScenarios;