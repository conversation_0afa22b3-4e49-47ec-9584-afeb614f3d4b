import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useDebugValueData: ApiItem = {
  id: 'useDebugValue',
  title: 'useDebugValue',
  description: 'useDebugValue是React开发者工具专用的调试Hook，用于在React DevTools中显示自定义Hook的调试信息，帮助开发者更好地理解和调试自定义Hook的内部状态。',
  category: 'React Hooks',
  difficulty: 'easy',

  syntax: `function useDebugValue<T>(value: T): void;
function useDebugValue<T>(value: T, format?: (value: T) => any): void;`,
  example: `function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);

  // 在React DevTools中显示当前计数值
  useDebugValue(count);

  return { count, increment, decrement };
}`,
  notes: '仅在开发环境有效，生产环境中完全被忽略',

  version: 'React 16.8.0+',
  tags: ["React", "Hook", "调试", "DevTools", "开发工具", "自定义Hook"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useDebugValueData;