import { DebuggingTips } from '../../../../types/api';

const debuggingTips: DebuggingTips = {
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: '社区工程师在使用useDebugValue时最常遇到的问题和解决方案。这些都是来自真实项目的经验总结，帮助你快速定位和解决调试问题。',
        sections: [
          {
            title: '环境配置问题',
            description: '最常见的问题类型，通常与开发环境配置相关',
            items: [
              {
                title: 'useDebugValue在React DevTools中不显示任何信息',
                description: '这是最常见的调试问题，通常由环境配置、使用位置或格式化函数错误导致',
                solution: '按照系统化的排查流程逐步定位问题根源',
                prevention: '建立标准的useDebugValue使用规范和环境检查清单',
                code: `// 🔍 系统化排查流程
function debugUseDebugValueIssue() {
  // 第一步：环境检查
  console.log('Environment checks:');
  console.log('- NODE_ENV:', process.env.NODE_ENV);
  console.log('- DevTools available:', !!window.__REACT_DEVTOOLS_GLOBAL_HOOK__);
  console.log('- React version:', React.version);

  // 第二步：最简测试
  function useSimpleTest() {
    const [count, setCount] = useState(0);

    // 最简单的useDebugValue测试
    useDebugValue(count);

    return { count, setCount };
  }

  // 第三步：格式化函数测试
  function useFormatterTest() {
    const [data, setData] = useState({ value: 'test' });

    useDebugValue(data, (data) => {
      try {
        console.log('Formatter called with:', data);
        return 'Formatted: ' + JSON.stringify(data);
      } catch (error) {
        console.error('Formatter error:', error);
        return 'Formatter Error';
      }
    });

    return { data, setData };
  }

  // 第四步：位置检查
  function usePositionTest() {
    const [state, setState] = useState(0);

    // ✅ 正确：在Hook顶层
    useDebugValue(state);

    // ❌ 错误示例（不要这样做）
    // if (state > 0) {
    //   useDebugValue(state); // 违反Hook规则
    // }

    return { state, setState };
  }
}`
              },
              {
                title: '生产环境中useDebugValue未被移除',
                description: '构建配置问题或使用了不支持代码消除的打包工具',
                solution: '检查和修复构建配置，验证生产包中的代码消除效果',
                prevention: '建立自动化检查流程，确保生产构建的正确性',
                code: `// 🔍 生产环境检查工具
function checkProductionBuild() {
  // 检查环境变量
  console.log('Environment checks:');
  console.log('- NODE_ENV:', process.env.NODE_ENV);
  console.log('- Production mode:', process.env.NODE_ENV === 'production');

  // 检查useDebugValue是否存在
  if (process.env.NODE_ENV === 'production') {
    // 在生产环境中，这个检查应该被移除
    console.error('❌ Production check failed: debug code not removed!');
  }
}

// 🛠️ 构建验证脚本
function validateBuild() {
  const buildStats = {
    environment: process.env.NODE_ENV,
    debugCodePresent: false,
    bundleSize: 'Check with bundler',
    recommendations: []
  };

  // 检查是否有调试代码残留
  try {
    // 这个函数在生产环境应该不存在
    if (typeof useDebugValue !== 'undefined') {
      buildStats.debugCodePresent = true;
      buildStats.recommendations.push('Check Babel configuration for useDebugValue removal');
    }
  } catch (error) {
    // 在生产环境中，useDebugValue应该是undefined
    buildStats.debugCodePresent = false;
  }

  // 输出验证结果
  console.log('Build Validation:', buildStats);

  return buildStats;
}`
              }
            ]
          },
          {
            title: '格式化函数问题',
            description: '格式化函数相关的常见错误和解决方案',
            items: [
              {
                title: '格式化函数抛出异常导致调试信息显示错误',
                description: '格式化函数中访问undefined属性、执行副作用或返回无法序列化的值',
                solution: '使用安全的格式化函数模式，添加错误处理和类型检查',
                prevention: '建立格式化函数的安全编程规范，使用工具函数简化开发',
                code: `// 🛡️ 安全的格式化函数模式
function createSafeFormatter(formatter) {
  return (value) => {
    try {
      // 基础类型检查
      if (value === null || value === undefined) {
        return 'null/undefined';
      }

      // 执行格式化
      const result = formatter(value);

      // 序列化检查
      JSON.stringify(result);

      return result;
    } catch (error) {
      // 开发环境显示详细错误
      if (process.env.NODE_ENV === 'development') {
        console.warn('Debug formatter error:', error.message);
        console.warn('Value that caused error:', value);
      }

      return 'Formatter Error: ' + error.message;
    }
  };
}

// 使用安全格式化函数
function useSafeDebugHook(complexData) {
  const [state, setState] = useState(complexData);

  const safeFormatter = createSafeFormatter((data) => {
    // 安全的属性访问
    const summary = {
      type: Array.isArray(data) ? 'array' : typeof data,
      hasData: !!data,
      size: data?.length || (typeof data === 'object' ? Object.keys(data || {}).length : 0)
    };

    return summary;
  });

  useDebugValue(state, safeFormatter);

  return { state, setState };
}`
              },
              {
                title: 'useDebugValue影响开发环境性能',
                description: '格式化函数执行昂贵计算或在Hook中过度使用useDebugValue',
                solution: '优化格式化函数性能，使用缓存机制，适度使用调试信息',
                prevention: '建立性能监控机制，制定useDebugValue使用规范',
                code: `// ⚡ 性能优化的调试策略
function usePerformanceOptimizedDebug(expensiveData) {
  const [state, setState] = useState(expensiveData);
  const performanceRef = useRef({ calls: 0, totalTime: 0 });

  useDebugValue(state, (state) => {
    const start = performance.now();

    try {
      // 简化的调试信息，避免昂贵计算
      const quickSummary = {
        hasData: !!state,
        type: Array.isArray(state) ? 'array' : typeof state,
        size: state?.length || 0
      };

      // 性能监控
      const duration = performance.now() - start;
      const perf = performanceRef.current;
      perf.calls++;
      perf.totalTime += duration;

      // 性能警告
      if (duration > 5) {
        console.warn('Slow debug formatter:', duration + 'ms');
      }

      // 定期性能报告
      if (perf.calls % 50 === 0) {
        console.log('Debug Performance:', {
          averageTime: (perf.totalTime / perf.calls).toFixed(2) + 'ms',
          totalCalls: perf.calls
        });
      }

      return quickSummary;
    } catch (error) {
      return 'Performance Error';
    }
  });

  return { state, setState };
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'devtools-usage',
      title: '🛠️ DevTools使用',
      content: {
        introduction: '掌握React DevTools的高级使用技巧，充分发挥useDebugValue的调试能力。这些技巧来自资深开发者的实战经验。',
        sections: [
          {
            title: 'React DevTools Components面板',
            description: '最常用的调试面板，学会高效查看和分析useDebugValue信息',
            items: [
              {
                title: '基础信息查看技巧',
                description: '快速定位和查看useDebugValue显示的调试信息',
                steps: [
                  '打开React DevTools扩展',
                  '切换到Components面板',
                  '在组件树中找到使用useDebugValue的组件',
                  '在右侧面板查看Hooks部分',
                  '找到DebugValue条目查看调试信息'
                ],
                tips: [
                  '使用搜索功能快速定位组件',
                  '可以实时编辑Props和State来测试调试信息',
                  '使用Profiler面板分析调试信息对性能的影响',
                  '在Settings中可以调整显示选项'
                ],
                code: `// 🔍 在React DevTools中查看useDebugValue信息的技巧

// 1. 基础查看方法
function useBasicDebugViewing() {
  const [user, setUser] = useState({ name: 'John', age: 30 });

  // 在DevTools中会显示为 "DebugValue: {name: 'John', age: 30}"
  useDebugValue(user);

  return { user, setUser };
}

// 2. 高级查看技巧
function useAdvancedDebugViewing() {
  const [state, setState] = useState({ loading: false, data: null, error: null });

  // 使用有意义的标签
  useDebugValue(state, (state) => {
    const status = state.loading ? '🔄 Loading' :
                  state.error ? '❌ Error' :
                  state.data ? '✅ Success' : '⭕ Idle';

    return {
      status,
      dataSize: state.data ? Object.keys(state.data).length : 0,
      errorType: state.error?.name || null
    };
  });

  return { state, setState };
}`
              },
              {
                title: 'Profiler性能分析',
                description: '使用React DevTools Profiler分析useDebugValue对性能的影响',
                steps: [
                  '开始录制前清除之前的性能数据',
                  '执行包含useDebugValue的操作',
                  '停止录制并分析结果',
                  '查看Commit阶段的耗时',
                  '识别异常长的渲染时间'
                ],
                tips: [
                  '对比有无useDebugValue的性能差异',
                  '关注格式化函数的执行频率',
                  '使用Profiler API标记关键操作',
                  '设置性能预算和警告阈值'
                ],
                code: `// 📊 使用Profiler分析useDebugValue性能影响

function useProfilerIntegratedDebug(data) {
  const [state, setState] = useState(data);

  // 集成性能分析的调试信息
  useDebugValue(state, (state) => {
    // 标记调试信息计算开始
    performance.mark('debug-format-start');

    const result = {
      stateType: typeof state,
      hasData: !!state,
      complexity: calculateComplexity(state)
    };

    // 标记调试信息计算结束
    performance.mark('debug-format-end');
    performance.measure('debug-format-duration', 'debug-format-start', 'debug-format-end');

    return result;
  });

  return { state, setState };
}`
              }
            ]
          },
          {
            title: '浏览器Performance面板',
            description: '使用浏览器原生Performance面板进行深度性能分析',
            items: [
              {
                title: '深度性能分析',
                description: '使用Performance面板分析useDebugValue的执行性能和内存使用',
                steps: [
                  '打开Chrome DevTools',
                  '切换到Performance面板',
                  '点击录制按钮开始性能分析',
                  '执行包含useDebugValue的操作',
                  '停止录制查看结果'
                ],
                tips: [
                  '关注Main线程活动时间',
                  '监控JavaScript执行时间',
                  '查看用户自定义标记的耗时',
                  '观察内存使用变化趋势'
                ],
                code: `// 🔬 使用Performance面板进行深度性能分析

function usePerformanceAnalysisDebug(complexData) {
  const [state, setState] = useState(complexData);

  useDebugValue(state, (state) => {
    // 添加性能标记用于Performance面板分析
    performance.mark('useDebugValue-start');

    // 模拟复杂的调试信息计算
    const analysis = {
      timestamp: Date.now(),
      dataStructure: analyzeDataStructure(state),
      memoryUsage: performance.memory ? {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      } : null,
      renderCount: getRenderCount()
    };

    performance.mark('useDebugValue-end');
    performance.measure('useDebugValue-duration', 'useDebugValue-start', 'useDebugValue-end');

    return analysis;
  });

  return { state, setState };
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'troubleshooting',
      title: '🔧 问题排查',
      content: {
        introduction: '系统化的问题排查方法，帮助你快速定位和解决useDebugValue相关的各种问题。这些方法经过大量实践验证。',
        sections: [
          {
            title: '显示问题排查',
            description: '当调试信息显示不正常时的排查方法',
            items: [
              {
                title: 'useDebugValue显示的信息不是最新的',
                description: '调试信息与实际状态不同步的问题排查',
                steps: [
                  '检查格式化函数是否只依赖传入的参数',
                  '刷新DevTools或重新选择组件',
                  '验证状态更新是否正确触发了重新渲染',
                  '使用useCallback确保格式化函数的正确依赖'
                ],
                code: `// 🔍 信息同步问题排查

// ❌ 问题：使用了过期的闭包变量
function useBuggyDebug() {
  const [count, setCount] = useState(0);
  const [multiplier, setMultiplier] = useState(2);

  // 错误：格式化函数捕获了过期的multiplier值
  useDebugValue(count, (count) => {
    return 'Result: ' + (count * multiplier); // multiplier可能是过期的
  });

  return { count, setCount, multiplier, setMultiplier };
}

// ✅ 解决：确保格式化函数只依赖传入参数
function useCorrectDebug() {
  const [count, setCount] = useState(0);
  const [multiplier, setMultiplier] = useState(2);

  // 正确：将所有需要的值作为调试值传入
  useDebugValue({ count, multiplier }, ({ count, multiplier }) => {
    return 'Result: ' + (count * multiplier);
  });

  return { count, setCount, multiplier, setMultiplier };
}`
              },
              {
                title: "调试信息显示'[object Object]'而不是具体内容",
                description: '对象序列化问题的排查和解决',
                steps: [
                  '添加格式化函数将对象转换为可读格式',
                  '使用JSON.stringify或自定义序列化方法',
                  '确保返回值是基础类型或简单对象',
                  '检查对象是否有循环引用'
                ],
                code: `// 🔍 对象显示问题排查

// ❌ 问题：复杂对象显示为[object Object]
function useBadObjectDebug() {
  const [user, setUser] = useState({
    profile: { name: 'John', settings: { theme: 'dark' } },
    permissions: new Set(['read', 'write']),
    lastLogin: new Date()
  });

  // 问题：复杂对象无法正确序列化
  useDebugValue(user);

  return { user, setUser };
}

// ✅ 解决：使用格式化函数处理复杂对象
function useGoodObjectDebug() {
  const [user, setUser] = useState({
    profile: { name: 'John', settings: { theme: 'dark' } },
    permissions: new Set(['read', 'write']),
    lastLogin: new Date()
  });

  useDebugValue(user, (user) => {
    return {
      name: user.profile?.name || 'Unknown',
      theme: user.profile?.settings?.theme || 'default',
      permissions: Array.from(user.permissions || []),
      lastLogin: user.lastLogin?.toISOString() || 'Never',
      hasProfile: !!user.profile
    };
  });

  return { user, setUser };
}`
              }
            ]
          },
          {
            title: '性能问题排查',
            description: '当useDebugValue影响性能时的排查方法',
            items: [
              {
                title: '格式化函数被频繁调用影响性能',
                description: '格式化函数性能问题的排查和优化',
                steps: [
                  '使用缓存机制避免重复计算',
                  '简化格式化函数的逻辑',
                  '优化组件的重新渲染频率',
                  '使用防抖或节流控制格式化函数的执行'
                ],
                code: `// ⚡ 性能问题排查和优化

// ❌ 问题：昂贵的格式化计算
function useSlowDebug(largeDataSet) {
  const [data, setData] = useState(largeDataSet);

  // 问题：每次都执行昂贵的计算
  useDebugValue(data, (data) => {
    // 昂贵的计算：遍历大数据集
    const summary = data.reduce((acc, item) => {
      // 复杂的聚合计算
      return {
        total: acc.total + item.value,
        categories: [...acc.categories, item.category],
        processed: acc.processed + 1
      };
    }, { total: 0, categories: [], processed: 0 });

    return summary;
  });

  return { data, setData };
}

// ✅ 解决：使用缓存和简化计算
function useFastDebug(largeDataSet) {
  const [data, setData] = useState(largeDataSet);
  const cacheRef = useRef(new Map());

  useDebugValue(data, (data) => {
    // 使用数据长度作为缓存键
    const cacheKey = data.length + '_' + (data[0]?.id || 'empty');

    if (cacheRef.current.has(cacheKey)) {
      return cacheRef.current.get(cacheKey);
    }

    // 简化的计算：只统计基本信息
    const summary = {
      count: data.length,
      hasData: data.length > 0,
      firstItem: data[0]?.category || 'none',
      lastItem: data[data.length - 1]?.category || 'none'
    };

    // 缓存结果（限制缓存大小）
    if (cacheRef.current.size > 10) {
      cacheRef.current.clear();
    }
    cacheRef.current.set(cacheKey, summary);

    return summary;
  });

  return { data, setData };
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
