import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'useDebugValue-not-showing',
    question: 'useDebugValue没有在React DevTools中显示任何信息，是什么原因？',
    answer: `这是useDebugValue最常见的问题，通常有以下几个原因：

**环境检查**：
1. 确保安装了React DevTools浏览器扩展
2. 确认当前运行在开发环境（process.env.NODE_ENV !== 'production'）
3. 检查React版本是否支持useDebugValue（需要16.8+）

**使用位置检查**：
1. useDebugValue必须在自定义Hook中调用，不能在普通组件中使用
2. 必须在Hook的顶层调用，不能在条件语句或循环中使用
3. 确保Hook调用顺序保持一致

**代码实现检查**：
1. 检查格式化函数是否抛出异常
2. 确认传递的值可以被正常序列化
3. 验证Hook是否正常执行

**快速排查步骤**：
1. 先移除格式化函数，只传递简单值测试
2. 在Hook中添加console.log确认执行
3. 检查浏览器控制台是否有错误信息
4. 尝试在不同的自定义Hook中测试`,
    code: `// ❌ 错误：在组件中使用
function MyComponent() {
  const [count, setCount] = useState(0);
  useDebugValue(count); // 错误位置
  return <div>{count}</div>;
}

// ✅ 正确：在自定义Hook中使用
function useCounter() {
  const [count, setCount] = useState(0);
  useDebugValue(count); // 正确位置
  return { count, setCount };
}

// ❌ 错误：条件调用
function useConditionalHook(enabled) {
  const [state, setState] = useState(0);

  if (enabled) {
    useDebugValue(state); // 违反Hook规则
  }

  return [state, setState];
}

// ✅ 正确：条件显示
function useConditionalHook(enabled) {
  const [state, setState] = useState(0);

  useDebugValue(enabled ? state : 'disabled');

  return [state, setState];
}

// 🔍 调试代码
function useDebugValueTroubleshooting() {
  const [count, setCount] = useState(0);

  // 检查环境
  console.log('Environment:', process.env.NODE_ENV);
  console.log('DevTools available:', !!window.__REACT_DEVTOOLS_GLOBAL_HOOK__);

  // 简单测试
  useDebugValue(count);

  // 格式化函数测试
  useDebugValue(count, (value) => {
    try {
      return 'Count: ' + value;
    } catch (error) {
      console.error('Formatter error:', error);
      return 'Error';
    }
  });

  return { count, setCount };
}`,
    tags: ['调试', '环境问题', '使用错误'],
    relatedQuestions: ['useDebugValue-production-removed', 'useDebugValue-formatter-error']
  },

  {
    id: 'useDebugValue-production-removed',
    question: '为什么useDebugValue在生产环境中被完全移除？这是如何实现的？',
    answer: `useDebugValue在生产环境中被移除是通过编译时优化和运行时检查的双重机制实现的：

**编译时优化**：
1. Babel插件在生产构建时识别useDebugValue调用
2. 类似于__DEV__条件编译，完全移除相关代码
3. 打包工具（Webpack、Rollup等）进行死代码消除（DCE）
4. 最终生产代码中不存在任何useDebugValue相关逻辑

**运行时保障**：
1. 即使编译优化失败，运行时也有环境检查
2. 检查process.env.NODE_ENV和DevTools状态
3. 在生产环境中直接返回，不执行任何逻辑

**性能保障**：
1. 零运行时开销：生产代码中完全不存在
2. 零包体积影响：不会增加最终打包大小
3. 零性能影响：不会影响应用运行性能

**实现原理**：
这种设计确保了开发者可以放心使用useDebugValue而不用担心生产环境的性能影响。React团队通过工具链的深度集成，实现了真正的"开发时可见、生产时透明"。`,
    code: `// 开发环境代码
function useCounter() {
  const [count, setCount] = useState(0);

  // 开发环境：正常执行
  useDebugValue(count, count => 'Count: ' + count);

  return { count, setCount };
}

// 生产环境编译后的代码（简化示例）
function useCounter() {
  const [count, setCount] = useState(0);

  // 生产环境：这行代码被完全移除
  // useDebugValue调用不存在

  return { count, setCount };
}

// Babel插件转换示例
// 输入代码：
useDebugValue(value, formatter);

// 开发环境输出：
if (process.env.NODE_ENV !== 'production') {
  useDebugValue(value, formatter);
}

// 生产环境输出：
// (代码被完全移除)

// 运行时检查机制（内部实现）
function useDebugValue(value, format) {
  // 生产环境快速返回
  if (process.env.NODE_ENV === 'production') {
    return;
  }

  // 开发环境逻辑
  const hook = getCurrentHook();
  hook.debugValue = value;
  hook.debugFormatter = format;
}`,
    tags: ['性能优化', '编译优化', '生产环境'],
    relatedQuestions: ['useDebugValue-not-showing', 'useDebugValue-performance-impact']
  },

  {
    id: 'useDebugValue-formatter-error',
    question: '格式化函数抛出错误导致useDebugValue不工作，如何避免和处理？',
    answer: `格式化函数错误是useDebugValue常见的问题，需要遵循安全编程原则：

**常见错误原因**：
1. 访问undefined或null对象的属性
2. 在格式化函数中执行副作用操作
3. 格式化函数返回无法序列化的值
4. 访问外部变量导致闭包问题

**安全编程原则**：
1. 始终进行空值检查
2. 使用try-catch包装复杂逻辑
3. 保持格式化函数的纯函数特性
4. 避免在格式化函数中执行异步操作

**最佳实践**：
1. 优先返回简单的字符串或数字
2. 对复杂对象进行安全的属性访问
3. 使用可选链操作符（?.）进行安全访问
4. 提供有意义的默认值

**调试技巧**：
1. 在格式化函数中添加错误处理
2. 使用console.log验证格式化函数的执行
3. 逐步简化格式化逻辑定位问题
4. 检查返回值是否可以被JSON.stringify序列化`,
    code: `// ❌ 危险的格式化函数
function useDangerousHook() {
  const [user, setUser] = useState(null);

  useDebugValue(user, (user) => {
    // 错误1：直接访问可能为null的对象
    return user.name + ' (' + user.email + ')';

    // 错误2：执行副作用
    console.log('Formatting user:', user);

    // 错误3：返回无法序列化的值
    return { user, timestamp: new Date() };
  });

  return { user, setUser };
}

// ✅ 安全的格式化函数
function useSafeHook() {
  const [user, setUser] = useState(null);

  useDebugValue(user, (user) => {
    // 安全检查
    if (!user) return 'No user';

    try {
      // 使用可选链和默认值
      const name = user?.name || 'Unknown';
      const email = user?.email || 'No email';
      return name + ' (' + email + ')';
    } catch (error) {
      // 错误处理
      return 'Error formatting user';
    }
  });

  return { user, setUser };
}

// 🚀 最佳实践示例
function useRobustHook() {
  const [data, setData] = useState({ items: [], status: 'idle' });

  useDebugValue(data, (data) => {
    // 多层安全检查
    if (!data) return 'No data';
    if (typeof data !== 'object') return 'Invalid data type';

    try {
      // 安全的属性访问
      const itemCount = Array.isArray(data.items) ? data.items.length : 0;
      const status = data.status || 'unknown';

      // 返回简单的字符串
      return status + ': ' + itemCount + ' items';
    } catch (error) {
      // 详细的错误信息（仅开发环境）
      if (process.env.NODE_ENV === 'development') {
        console.warn('useDebugValue formatter error:', error);
      }
      return 'Formatter error';
    }
  });

  return { data, setData };
}

// 通用的安全格式化工具
function createSafeFormatter(formatter) {
  return (value) => {
    try {
      const result = formatter(value);

      // 检查结果是否可序列化
      JSON.stringify(result);

      return result;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('Debug formatter error:', error);
      }
      return 'Format error';
    }
  };
}

// 使用安全格式化工具
function useWithSafeFormatter() {
  const [complexData, setComplexData] = useState({});

  const safeFormatter = createSafeFormatter((data) => {
    return {
      keys: Object.keys(data).length,
      hasData: Object.keys(data).length > 0
    };
  });

  useDebugValue(complexData, safeFormatter);

  return { complexData, setComplexData };
}`,
    tags: ['错误处理', '格式化函数', '安全编程'],
    relatedQuestions: ['useDebugValue-not-showing', 'useDebugValue-best-practices']
  },

  {
    id: 'useDebugValue-performance-impact',
    question: 'useDebugValue会影响开发环境的性能吗？如何优化？',
    answer: `useDebugValue在开发环境中确实有轻微的性能开销，但通过合理使用可以最小化影响：

**性能开销来源**：
1. Hook节点的debugValue字段存储
2. 格式化函数的执行开销
3. DevTools遍历和信息收集
4. 复杂对象的序列化处理

**优化策略**：
1. **延迟执行**：格式化函数仅在DevTools需要显示时执行
2. **简化数据**：避免在调试信息中包含大量数据
3. **缓存结果**：对昂贵的计算结果进行缓存
4. **条件显示**：根据开发需要动态控制调试信息

**最佳实践**：
1. 避免在格式化函数中执行昂贵计算
2. 优先显示关键状态信息，而非完整数据
3. 使用简单的数据类型作为调试值
4. 在复杂Hook中适度使用，不要过度调试

**性能监控**：
开发环境中useDebugValue的开销通常<1ms，对用户体验影响微乎其微。但在性能敏感的场景下，可以通过条件编译进一步优化。`,
    code: `// ❌ 性能不友好的做法
function useExpensiveHook() {
  const [data, setData] = useState([]);

  // 问题1：每次渲染都执行昂贵计算
  useDebugValue(data, (data) => {
    // 昂贵的计算
    const processed = data.map(item => ({
      ...item,
      computed: heavyComputation(item)
    }));
    return processed;
  });

  // 问题2：显示过多信息
  useDebugValue(data);
  useDebugValue(data.length);
  useDebugValue(data.map(item => item.id));

  return { data, setData };
}

// ✅ 性能友好的做法
function useOptimizedHook() {
  const [data, setData] = useState([]);

  // 优化1：显示关键摘要信息
  useDebugValue(data, (data) => {
    // 简单快速的计算
    return {
      count: data.length,
      hasData: data.length > 0,
      firstId: data[0]?.id || null
    };
  });

  return { data, setData };
}

// 🚀 高级优化技巧
function useAdvancedOptimization() {
  const [complexState, setComplexState] = useState({});
  const debugInfoRef = useRef(null);
  const lastStateRef = useRef(null);

  // 优化2：缓存昂贵的调试计算
  useDebugValue(complexState, (state) => {
    // 检查状态是否变化
    if (lastStateRef.current === state && debugInfoRef.current) {
      return debugInfoRef.current;
    }

    // 执行昂贵计算
    const debugInfo = {
      stateSize: JSON.stringify(state).length,
      keyCount: Object.keys(state).length,
      complexity: calculateComplexity(state)
    };

    // 缓存结果
    debugInfoRef.current = debugInfo;
    lastStateRef.current = state;

    return debugInfo;
  });

  return { complexState, setComplexState };
}

// 条件调试：根据需要启用
const DEBUG_ENABLED = process.env.REACT_APP_DEBUG_HOOKS === 'true';

function useConditionalDebug() {
  const [state, setState] = useState(null);

  // 优化3：条件性调试
  if (DEBUG_ENABLED) {
    useDebugValue(state, (state) => {
      return state ? 'loaded' : 'empty';
    });
  }

  return { state, setState };
}

// 性能监控工具
function usePerformanceAwareDebug() {
  const [data, setData] = useState(null);

  useDebugValue(data, (data) => {
    const start = performance.now();

    const result = {
      hasData: !!data,
      size: data ? Object.keys(data).length : 0
    };

    const duration = performance.now() - start;

    // 监控格式化函数性能
    if (duration > 1) {
      console.warn('Slow debug formatter:', duration + 'ms');
    }

    return result;
  });

  return { data, setData };
}`,
    tags: ['性能优化', '开发环境', '最佳实践'],
    relatedQuestions: ['useDebugValue-production-removed', 'useDebugValue-best-practices']
  },

  {
    id: 'useDebugValue-complex-objects',
    question: '如何在useDebugValue中有效显示复杂的对象和数组？',
    answer: `显示复杂对象是useDebugValue的常见需求，需要通过格式化函数进行合理的信息提取：

**核心原则**：
1. **信息精简**：只显示最关键的信息，避免信息过载
2. **结构清晰**：使用易于理解的格式组织信息
3. **性能友好**：避免深度遍历和复杂计算
4. **可读性强**：使用直观的标签和描述

**常见场景处理**：
1. **数组数据**：显示长度、类型分布、关键元素
2. **用户对象**：显示关键身份信息和状态
3. **API响应**：显示请求状态、数据大小、错误信息
4. **表单状态**：显示验证状态、字段数量、错误统计

**格式化策略**：
1. 使用对象摘要而非完整对象
2. 提取业务相关的关键指标
3. 使用字符串模板提高可读性
4. 为不同状态提供不同的显示格式

**调试技巧**：
1. 分层显示：从概览到详细
2. 状态标识：使用emoji或符号增强可读性
3. 动态格式：根据数据状态调整显示格式
4. 错误处理：优雅处理异常数据结构`,
    code: `// 复杂对象的有效显示策略

// 1. 数组数据处理
function useUserList() {
  const [users, setUsers] = useState([]);

  useDebugValue(users, (users) => {
    if (!Array.isArray(users)) return 'Invalid data';
    if (users.length === 0) return '📭 Empty list';

    // 提取关键统计信息
    const activeUsers = users.filter(u => u.isActive).length;
    const adminUsers = users.filter(u => u.role === 'admin').length;

    return {
      total: users.length,
      active: activeUsers,
      admins: adminUsers,
      sample: users[0]?.name || 'Unknown'
    };
  });

  return { users, setUsers };
}

// 2. API状态对象
function useApiData(url) {
  const [apiState, setApiState] = useState({
    data: null,
    loading: false,
    error: null,
    lastFetch: null,
    retryCount: 0
  });

  useDebugValue(apiState, (state) => {
    // 状态优先级显示
    if (state.loading) return '🔄 Loading...';
    if (state.error) return '❌ Error: ' + state.error.message;
    if (!state.data) return '⭕ No data';

    // 成功状态的详细信息
    const dataSize = JSON.stringify(state.data).length;
    const lastFetch = state.lastFetch ?
      Math.floor((Date.now() - state.lastFetch) / 1000) + 's ago' : 'never';

    return {
      status: '✅ Success',
      size: Math.round(dataSize / 1024) + 'KB',
      lastFetch,
      retries: state.retryCount
    };
  });

  return { apiState, setApiState };
}

// 3. 表单状态管理
function useFormState(initialValues) {
  const [formState, setFormState] = useState({
    values: initialValues,
    errors: {},
    touched: {},
    isSubmitting: false,
    submitCount: 0
  });

  useDebugValue(formState, (state) => {
    const fieldCount = Object.keys(state.values).length;
    const errorCount = Object.keys(state.errors).length;
    const touchedCount = Object.keys(state.touched).length;

    // 表单状态摘要
    const status = state.isSubmitting ? 'submitting' :
                  errorCount > 0 ? 'invalid' : 'valid';

    return {
      status: status.toUpperCase(),
      fields: fieldCount,
      errors: errorCount,
      touched: touchedCount + '/' + fieldCount,
      attempts: state.submitCount
    };
  });

  return { formState, setFormState };
}

// 4. 复杂业务对象
function useShoppingCart() {
  const [cart, setCart] = useState({
    items: [],
    discounts: [],
    shipping: null,
    total: 0,
    currency: 'USD'
  });

  useDebugValue(cart, (cart) => {
    if (!cart.items.length) return '🛒 Empty cart';

    // 业务指标提取
    const itemCount = cart.items.reduce((sum, item) => sum + item.quantity, 0);
    const uniqueItems = cart.items.length;
    const hasDiscounts = cart.discounts.length > 0;
    const hasShipping = !!cart.shipping;

    return {
      items: itemCount + ' items (' + uniqueItems + ' unique)',
      total: cart.currency + ' ' + cart.total.toFixed(2),
      discounts: hasDiscounts ? '💰 ' + cart.discounts.length : 'none',
      shipping: hasShipping ? '🚚 ' + cart.shipping.method : 'not set'
    };
  });

  return { cart, setCart };
}

// 5. 嵌套对象的安全访问
function useNestedData() {
  const [data, setData] = useState({});

  useDebugValue(data, (data) => {
    try {
      // 安全的深度访问
      const user = data?.user || {};
      const profile = user?.profile || {};
      const settings = user?.settings || {};

      return {
        user: user?.name || 'Anonymous',
        avatar: profile?.avatar ? '🖼️ Yes' : '❌ No',
        verified: profile?.verified ? '✅' : '❌',
        theme: settings?.theme || 'default',
        notifications: settings?.notifications ? '🔔' : '🔕'
      };
    } catch (error) {
      return 'Error parsing data';
    }
  });

  return { data, setData };
}

// 6. 动态格式化工具
function createObjectFormatter(config) {
  return (obj) => {
    if (!obj || typeof obj !== 'object') return 'Invalid object';

    const result = {};

    // 根据配置提取信息
    for (const [key, extractor] of Object.entries(config)) {
      try {
        result[key] = typeof extractor === 'function' ?
          extractor(obj) : obj[extractor];
      } catch (error) {
        result[key] = 'Error';
      }
    }

    return result;
  };
}

// 使用动态格式化工具
function useWithDynamicFormatter() {
  const [userData, setUserData] = useState({});

  const userFormatter = createObjectFormatter({
    id: 'id',
    name: 'name',
    status: (user) => user.isActive ? 'Active' : 'Inactive',
    posts: (user) => user.posts?.length || 0,
    lastSeen: (user) => user.lastLoginAt ?
      new Date(user.lastLoginAt).toLocaleDateString() : 'Never'
  });

  useDebugValue(userData, userFormatter);

  return { userData, setUserData };
}`,
    tags: ['复杂对象', '格式化函数', '数据显示'],
    relatedQuestions: ['useDebugValue-formatter-error', 'useDebugValue-performance-impact']
  }
];

export default commonQuestions;