import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: 'useDebugValue是什么？它解决了什么问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'useDebugValue是React的调试专用Hook，用于在React DevTools中显示自定义Hook的调试信息，解决了自定义Hook内部状态不可观测的问题。',
      detailed: `**核心概念**：useDebugValue是React 16.8引入的调试专用Hook，专门为React DevTools提供自定义Hook的调试信息展示功能。

**解决的问题**：
1. **状态不可见**：自定义Hook内部状态在DevTools中无法直接观察
2. **调试困难**：开发者需要添加console.log等临时调试代码
3. **信息混乱**：缺乏标准化的调试信息展示方式
4. **生产影响**：传统调试方法可能影响生产环境性能

**核心特性**：
1. **条件性执行**：仅在开发环境且DevTools打开时生效
2. **零生产开销**：生产构建时完全移除，不影响性能
3. **格式化支持**：支持自定义格式化函数，优化复杂数据显示
4. **DevTools集成**：与React DevTools深度集成，提供原生调试体验

**使用价值**：帮助开发者构建更易调试的自定义Hook，提升开发效率和代码质量，特别适合Hook库开发和团队协作场景。`,
      code: `// 基础使用示例
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);

  // 在React DevTools中显示当前计数值
  useDebugValue(count);

  const increment = () => setCount(prev => prev + 1);
  const decrement = () => setCount(prev => prev - 1);

  return { count, increment, decrement };
}

// 格式化函数示例
function useUserAuth() {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // 使用格式化函数显示用户状态
  useDebugValue({ user, isLoading }, (state) => {
    if (state.isLoading) return 'Loading...';
    return state.user ? 'Authenticated: ' + state.user.name : 'Not authenticated';
  });

  return { user, isLoading, login, logout };
}`
    }
  },

  {
    id: 2,
    question: 'useDebugValue的工作原理是什么？为什么它在生产环境中没有性能开销？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '实现原理',
    answer: {
      brief: 'useDebugValue通过Hook链表节点标记机制工作，生产环境零开销是因为编译时优化和条件性执行的双重保障。',
      detailed: `**工作原理**：
1. **Hook节点标记**：在Hook链表节点上添加debugValue字段存储调试信息
2. **DevTools遍历**：React DevTools遍历Fiber树时检查Hook节点的debugValue
3. **条件性处理**：仅在开发环境且DevTools打开时处理调试信息
4. **格式化执行**：需要显示时才调用格式化函数，避免不必要计算

**零生产开销的实现机制**：

**编译时优化**：
- Babel插件在生产构建时完全移除useDebugValue调用
- 类似于__DEV__条件编译，确保生产代码中不存在相关逻辑
- 打包工具（如Webpack）会进行死代码消除（DCE）

**运行时保障**：
- 即使编译优化失败，运行时也有条件检查
- 检查process.env.NODE_ENV和DevTools状态
- 双重保障确保生产环境绝对不执行调试逻辑

**性能优化策略**：
1. **延迟执行**：格式化函数仅在实际需要显示时执行
2. **浅层检查**：DevTools遍历时进行快速的存在性检查
3. **批量处理**：多个Hook的调试信息批量收集和处理`,
      code: `// 简化的内部实现原理
function useDebugValue(value, format) {
  // 生产环境检查
  if (process.env.NODE_ENV === 'production') {
    return; // 生产环境直接返回
  }

  // 获取当前Hook节点
  const hook = getCurrentHook();

  // 存储调试信息到Hook节点
  hook.debugValue = value;
  hook.debugFormatter = format;

  // DevTools检查时的处理逻辑
  if (isDevToolsActive()) {
    const displayValue = format ? format(value) : value;
    // 传递给DevTools显示
    notifyDevTools(hook, displayValue);
  }
}

// 编译时优化示例（Babel转换）
// 开发环境代码：
useDebugValue(count, count => 'Count: ' + count);

// 生产环境编译后：
// (代码被完全移除，不存在任何调用)`
    }
  },

  {
    id: 3,
    question: '在实际项目中如何正确使用useDebugValue？有哪些最佳实践？',
    difficulty: 'medium',
    frequency: 'high',
    category: '最佳实践',
    answer: {
      brief: '正确使用useDebugValue需要遵循适度原则、格式化优化、团队规范和性能考虑四个核心最佳实践。',
      detailed: `**核心最佳实践**：

**1. 适度使用原则**：
- 仅在自定义Hook中使用，不在普通组件中滥用
- 优先显示最有代表性的状态信息
- 避免为每个状态都添加useDebugValue

**2. 格式化函数优化**：
- 对复杂对象使用格式化函数简化显示
- 格式化函数保持纯函数特性，避免副作用
- 提取关键信息，避免显示过多细节

**3. 团队协作规范**：
- 建立统一的调试信息命名规范
- 在Hook库开发中标准化使用
- 为复杂Hook提供有意义的调试标签

**4. 性能考虑**：
- 避免在格式化函数中执行昂贵计算
- 不要依赖useDebugValue进行业务逻辑
- 格式化函数中避免访问外部状态

**实际应用场景**：
- Hook库开发：为开源Hook库提供调试支持
- 复杂状态管理：监控复杂业务逻辑的状态变化
- 团队协作：帮助团队成员理解自定义Hook的工作状态
- 问题排查：在开发过程中快速定位状态相关问题`,
      code: `// ✅ 最佳实践示例
function useApiData(url) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // ✅ 推荐：显示关键状态摘要
  useDebugValue({ data, loading, error }, (state) => {
    if (state.loading) return '🔄 Loading...';
    if (state.error) return '❌ Error: ' + state.error.message;
    if (state.data) return '✅ Data loaded (' + Object.keys(state.data).length + ' items)';
    return '⭕ No data';
  });

  // 业务逻辑...
  return { data, loading, error, refetch };
}

// ❌ 不推荐的做法
function useCounter() {
  const [count, setCount] = useState(0);

  // ❌ 不推荐：过度使用
  useDebugValue(count);
  useDebugValue(count > 10 ? 'high' : 'low');
  useDebugValue('Counter component state');

  // ❌ 不推荐：在格式化函数中执行副作用
  useDebugValue(count, (value) => {
    console.log('Debug value called'); // 副作用
    return 'Count: ' + value;
  });

  return { count, setCount };
}

// ✅ 团队协作规范示例
function useShoppingCart() {
  const [items, setItems] = useState([]);
  const [total, setTotal] = useState(0);

  // ✅ 推荐：统一的调试信息格式
  useDebugValue({ items, total }, (state) =>
    'Cart: ' + state.items.length + ' items, $' + state.total.toFixed(2)
  );

  return { items, total, addItem, removeItem };
}`
    }
  },

  {
    id: 4,
    question: '如果useDebugValue没有在React DevTools中显示信息，可能是什么原因？如何排查？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '问题排查',
    answer: {
      brief: '常见原因包括DevTools未安装、生产环境运行、Hook位置错误和格式化函数异常，需要逐步排查环境、位置和代码问题。',
      detailed: `**常见原因分析**：

**1. 环境问题**：
- React DevTools未安装或版本过旧
- 在生产环境中运行（useDebugValue被编译器移除）
- 浏览器开发者工具未打开
- React版本过低（需要16.8+）

**2. 使用位置错误**：
- 在普通组件中使用而非自定义Hook
- 在条件语句或循环中调用
- Hook调用顺序发生变化

**3. 代码实现问题**：
- 格式化函数抛出异常
- 传递了无法序列化的值
- Hook链表结构被破坏

**排查步骤**：

**第一步：环境检查**
- 确认React DevTools已安装且版本兼容
- 检查当前是否为开发环境
- 验证React版本是否支持useDebugValue

**第二步：位置验证**
- 确保useDebugValue在自定义Hook中调用
- 检查Hook调用是否在组件顶层
- 验证Hook调用顺序的一致性

**第三步：代码调试**
- 简化格式化函数或移除格式化参数
- 检查传递的值是否可以正常序列化
- 使用console.log验证Hook是否正常执行`,
      code: `// 排查问题的调试代码
function useDebugValueTroubleshooting() {
  const [count, setCount] = useState(0);

  // 第一步：基础检查
  console.log('Hook executing, count:', count);
  console.log('Environment:', process.env.NODE_ENV);
  console.log('DevTools available:', window.__REACT_DEVTOOLS_GLOBAL_HOOK__);

  // 第二步：简化测试
  useDebugValue(count); // 不使用格式化函数

  // 第三步：格式化函数调试
  useDebugValue(count, (value) => {
    try {
      console.log('Formatter called with:', value);
      return 'Count: ' + value;
    } catch (error) {
      console.error('Formatter error:', error);
      return 'Error in formatter';
    }
  });

  return { count, setCount };
}

// 常见错误示例和修复
// ❌ 错误：在组件中使用
function MyComponent() {
  const [state, setState] = useState(0);
  useDebugValue(state); // 错误位置
  return <div>{state}</div>;
}

// ✅ 正确：在自定义Hook中使用
function useMyState() {
  const [state, setState] = useState(0);
  useDebugValue(state); // 正确位置
  return [state, setState];
}

// ❌ 错误：条件调用
function useConditionalDebug(condition) {
  const [state, setState] = useState(0);

  if (condition) {
    useDebugValue(state); // 违反Hook规则
  }

  return [state, setState];
}

// ✅ 正确：条件显示
function useConditionalDebug(condition) {
  const [state, setState] = useState(0);

  useDebugValue(condition ? state : 'disabled');

  return [state, setState];
}`
    }
  },

  {
    id: 5,
    question: '在复杂的企业级应用中，如何设计useDebugValue的使用策略？',
    difficulty: 'hard',
    frequency: 'low',
    category: '架构设计',
    answer: {
      brief: '企业级应用需要建立分层调试策略、标准化规范、性能监控和团队协作机制的完整useDebugValue使用体系。',
      detailed: `**企业级使用策略**：

**1. 分层调试架构**：
- **基础层**：核心Hook库的标准化调试信息
- **业务层**：业务逻辑Hook的状态监控
- **应用层**：页面级Hook的集成调试
- **监控层**：性能和错误的调试追踪

**2. 标准化规范**：
- 建立统一的调试信息格式规范
- 定义不同复杂度Hook的调试策略
- 制定团队代码审查中的调试信息检查标准
- 建立调试信息的文档化要求

**3. 性能监控集成**：
- 结合性能监控工具追踪Hook性能
- 在调试信息中包含关键性能指标
- 建立调试信息的性能影响评估机制
- 实现调试信息的智能开关控制

**4. 团队协作机制**：
- 新人培训中包含useDebugValue使用规范
- 建立Hook库的调试信息维护流程
- 实现调试信息的版本控制和更新机制
- 建立跨团队的调试信息共享标准

**5. 工具链集成**：
- 与CI/CD流程集成，确保调试信息质量
- 开发自定义DevTools插件增强调试体验
- 建立调试信息的自动化测试机制
- 实现调试信息的文档自动生成`,
      code: `// 企业级useDebugValue策略示例

// 1. 基础Hook库标准化
function useApiRequest(config) {
  const [state, setState] = useState({
    data: null,
    loading: false,
    error: null,
    retryCount: 0
  });

  // 标准化的调试信息格式
  useDebugValue(state, (s) => ({
    status: s.loading ? 'loading' : s.error ? 'error' : 'success',
    endpoint: config.url,
    retries: s.retryCount,
    dataSize: s.data ? JSON.stringify(s.data).length : 0,
    timestamp: new Date().toISOString()
  }));

  return state;
}

// 2. 业务层Hook监控
function useUserManagement() {
  const users = useApiRequest('/api/users');
  const permissions = useApiRequest('/api/permissions');

  // 业务层聚合调试信息
  useDebugValue({ users, permissions }, (state) => {
    const health = calculateSystemHealth(state);
    return {
      module: 'UserManagement',
      health: health.score + '/100',
      users: state.users.data?.length || 0,
      permissions: state.permissions.data?.length || 0,
      issues: health.issues
    };
  });

  return { users, permissions };
}

// 3. 性能监控集成
function usePerformanceAwareHook(heavyComputation) {
  const [result, setResult] = useState(null);
  const [metrics, setMetrics] = useState({
    computeTime: 0,
    memoryUsage: 0,
    renderCount: 0
  });

  // 包含性能指标的调试信息
  useDebugValue({ result, metrics }, (state) => ({
    result: state.result ? 'computed' : 'pending',
    performance: {
      avgComputeTime: state.metrics.computeTime + 'ms',
      memoryImpact: state.metrics.memoryUsage + 'MB',
      renderEfficiency: state.metrics.renderCount + ' renders'
    },
    optimization: getOptimizationSuggestions(state.metrics)
  }));

  return { result, metrics };
}

// 4. 团队协作工具
const DEBUG_CATEGORIES = {
  API: 'api',
  STATE: 'state',
  PERFORMANCE: 'perf',
  BUSINESS: 'business'
};

function createTeamDebugValue(category, identifier) {
  return (value, formatter) => {
    const teamFormatter = (val) => {
      const baseInfo = formatter ? formatter(val) : val;
      return {
        category,
        identifier,
        team: 'frontend-team',
        version: process.env.REACT_APP_VERSION,
        data: baseInfo
      };
    };

    useDebugValue(value, teamFormatter);
  };
}

// 使用团队标准化调试
function useTeamStandardHook() {
  const [data, setData] = useState(null);
  const teamDebug = createTeamDebugValue(DEBUG_CATEGORIES.API, 'user-data');

  teamDebug(data, (d) => ({
    status: d ? 'loaded' : 'empty',
    size: d?.length || 0
  }));

  return { data, setData };
}`
    }
  }
];

export default interviewQuestions;