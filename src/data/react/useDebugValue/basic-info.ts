import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "useDebugValue是React开发者工具专用的调试Hook，用于在React DevTools中显示自定义Hook的调试信息，帮助开发者更好地理解和调试自定义Hook的内部状态。",

  introduction: `useDebugValue是React 16.8引入的调试专用Hook，专门为React DevTools提供自定义Hook的调试信息展示。

它遵循"开发时可见、生产时透明"的设计理念，在开发环境中为开发者提供有价值的调试信息，而在生产环境中完全不影响性能。

主要用于自定义Hook的内部状态展示、复杂逻辑的调试追踪和Hook开发过程中的状态监控。相比传统的console.log调试方式，它的创新在于与React DevTools的深度集成和零生产环境开销。

在React生态中，它是开发工具链的重要组成部分，常见于Hook库开发、复杂状态管理和团队协作开发，特别适合需要深度调试自定义Hook的场景。

核心优势包括DevTools集成、条件性执行和零生产开销，但需要注意它仅在开发环境有效且依赖React DevTools。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react/index.d.ts:1156
 * - 实现文件：packages/react/src/ReactHooks.js:378
 * - DevTools集成：packages/react-devtools-shared/src/backend/renderer.js:2890
 */

// 基础语法
function useDebugValue<T>(value: T): void;
function useDebugValue<T>(value: T, format?: (value: T) => any): void;

// 泛型约束分析
/**
 * 泛型参数 T 的约束：
 * - T 可以是任意类型，包括原始类型、对象、数组、函数等
 * - format 函数接收 T 类型参数，返回任意类型用于显示
 * - React DevTools 会自动序列化返回值进行展示
 */

// 类型推导示例
useDebugValue(count);                    // T 推导为 number
useDebugValue(user, user => user.name);  // T 推导为 User 类型
useDebugValue(items, items => items.length); // 显示数组长度而非完整数组`,

  quickExample: `function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);

  // 在React DevTools中显示当前计数值
  useDebugValue(count);

  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);

  const decrement = useCallback(() => {
    setCount(prev => prev - 1);
  }, []);

  return { count, increment, decrement };
}

function CounterExample() {
  // 在DevTools中可以看到useCounter的调试信息
  const { count, increment, decrement } = useCounter(10);

  return (
    <div>
      <p>计数: {count}</p>
      <button onClick={increment}>增加</button>
      <button onClick={decrement}>减少</button>
    </div>
  );
}`,

  parameters: [
    {
      name: "value",
      type: "T",
      required: true,
      description: "要在React DevTools中显示的调试值，可以是任意类型的数据",
      details: "支持任意类型是为了适应不同Hook的调试需求，React DevTools会自动处理序列化和展示。在生产环境中此参数会被完全忽略，不会产生任何性能开销。"
    },
    {
      name: "format",
      type: "(value: T) => any",
      required: false,
      description: "可选的格式化函数，用于将调试值转换为更适合显示的格式",
      details: "提供格式化函数是为了避免在DevTools中显示过于复杂的对象，同时支持延迟计算以优化性能。格式化函数仅在DevTools实际需要显示时才会执行。"
    }
  ],

  returnValue: {
    type: "void",
    description: "useDebugValue不返回任何值，它的作用是在React DevTools中显示调试信息"
  },

  keyFeatures: [
    {
      feature: "React DevTools深度集成",
      description: "与React DevTools原生集成，在组件树中直接显示自定义Hook的调试信息",
      importance: "high",
      details: "提供可视化的调试体验，无需在代码中添加console.log或其他调试代码"
    },
    {
      feature: "条件性执行机制",
      description: "仅在开发环境且React DevTools打开时才执行，生产环境完全忽略",
      importance: "critical",
      details: "零生产环境性能影响，开发者可以放心使用而不担心性能问题"
    },
    {
      feature: "灵活的格式化支持",
      description: "支持自定义格式化函数，可以将复杂数据转换为易于理解的调试信息",
      importance: "medium",
      details: "避免在DevTools中显示过于复杂的对象，提供更清晰的调试体验"
    }
  ],

  limitations: [
    "仅在开发环境有效，生产环境中完全被忽略",
    "依赖React DevTools，没有安装DevTools时不会显示任何信息",
    "不能用于生产环境的日志记录或监控",
    "格式化函数应避免副作用，因为执行时机不可预测",
    "过度使用可能会影响DevTools的性能和可读性"
  ],

  bestPractices: [
    "在自定义Hook中使用useDebugValue提供调试信息",
    "传递最有代表性的状态值，避免冗余信息",
    "使用格式化函数简化复杂对象的显示",
    "保持调试信息简洁明了，便于快速理解",
    "在Hook库开发中标准化使用useDebugValue",
    "避免在格式化函数中执行副作用或昂贵计算",
    "结合有意义的标签名称提高调试效率",
    "在团队开发中建立useDebugValue使用规范"
  ],

  warnings: [
    "不要依赖useDebugValue进行业务逻辑，它仅用于调试目的",
    "格式化函数中避免执行副作用，可能导致不可预测的行为",
    "过度使用会让DevTools变得混乱，影响调试效率",
    "不要在格式化函数中访问外部状态或执行异步操作",
    "生产环境中useDebugValue会被完全移除，不要依赖其执行"
  ],

  // scenarioDiagram: 暂时移除用于调试

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "useDebugValue在实际开发中的三大核心应用场景",
      diagram: `graph LR
    A[useDebugValue核心场景] --> B[自定义Hook调试]
    A --> C[状态监控追踪]
    A --> D[团队协作开发]

    B --> B1[Hook内部状态展示]
    B --> B2[复杂逻辑追踪]
    B --> B3[性能分析辅助]

    C --> C1[实时状态监控]
    C --> C2[状态变化追踪]
    C --> C3[异常状态检测]

    D --> D1[代码审查辅助]
    D --> D2[新人学习支持]
    D --> D3[调试信息标准化]

    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0`
    },
    {
      title: "技术实现架构",
      description: "useDebugValue的技术实现架构，展示其与React DevTools的集成机制",
      diagram: `graph TB
    A[useDebugValue技术架构] --> B[DevTools集成层]
    A --> C[条件执行层]
    A --> D[编译优化层]

    B --> B1[React DevTools协议]
    B --> B2[可视化展示引擎]
    B --> B3[实时数据同步]

    C --> C1[开发环境检测]
    C --> C2[DevTools状态检查]
    C --> C3[智能执行控制]

    D --> D1[Babel编译插件]
    D --> D2[打包工具集成]
    D --> D3[零运行时开销]

    style A fill:#e1f5fe
    style B fill:#f1f8e9
    style C fill:#fce4ec
    style D fill:#e8eaf6`
    }
  ],
};

export default basicInfo;