import { PerformanceOptimization } from '../../../../types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: "格式化函数性能优化",
      description: "优化useDebugValue的格式化函数执行效率，避免昂贵计算影响开发体验",
      techniques: [
        {
          name: "延迟计算优化",
          description: "利用格式化函数的延迟执行特性，避免不必要的计算开销",
          code: `// ❌ 性能问题：每次Hook执行都计算
function useExpensiveHook(data) {
  const [state, setState] = useState(data);

  // 问题：即使DevTools未打开也会执行昂贵计算
  const debugInfo = expensiveCalculation(state);
  useDebugValue(debugInfo);

  return { state, setState };
}

// ✅ 性能优化：利用格式化函数延迟执行
function useOptimizedHook(data) {
  const [state, setState] = useState(data);

  // 优化：只有在DevTools需要显示时才执行计算
  useDebugValue(state, (state) => {
    // 这里的计算只在DevTools实际显示时执行
    return expensiveCalculation(state);
  });

  return { state, setState };
}

// 📊 性能对比
// 优化前：每次Hook执行 +10ms
// 优化后：Hook执行 +0ms，DevTools显示时 +10ms
// 开发体验提升：显著`,
          impact: "high",
          difficulty: "easy"
        },
        {
          name: "计算结果缓存",
          description: "对昂贵的调试信息计算结果进行缓存，避免重复计算",
          code: `// ✅ 高级优化：缓存昂贵的调试计算
function useAdvancedOptimization(complexData) {
  const [state, setState] = useState(complexData);
  const debugCacheRef = useRef(new Map());
  const lastStateRef = useRef(null);

  useDebugValue(state, (currentState) => {
    // 检查状态是否变化
    if (lastStateRef.current === currentState) {
      const cached = debugCacheRef.current.get(currentState);
      if (cached) return cached;
    }

    // 执行昂贵计算
    const result = {
      complexity: analyzeComplexity(currentState),
      summary: generateSummary(currentState),
      metrics: calculateMetrics(currentState)
    };

    // 缓存结果（限制缓存大小）
    if (debugCacheRef.current.size > 10) {
      debugCacheRef.current.clear();
    }
    debugCacheRef.current.set(currentState, result);
    lastStateRef.current = currentState;

    return result;
  });

  return { state, setState };
}`,
          impact: "high",
          difficulty: "medium"
        },
        {
          name: "条件性调试信息",
          description: "根据开发需要动态控制调试信息的详细程度",
          code: `// ✅ 智能优化：根据环境和需要调整调试详细程度
const DEBUG_LEVEL = process.env.REACT_APP_DEBUG_LEVEL || 'basic';

function useSmartDebugHook(data) {
  const [state, setState] = useState(data);

  useDebugValue(state, (state) => {
    switch (DEBUG_LEVEL) {
      case 'minimal':
        return state ? 'has data' : 'no data';

      case 'basic':
        return {
          hasData: !!state,
          itemCount: Array.isArray(state) ? state.length : 0
        };

      case 'detailed':
        return {
          hasData: !!state,
          itemCount: Array.isArray(state) ? state.length : 0,
          dataSize: JSON.stringify(state).length,
          structure: analyzeStructure(state)
        };

      case 'verbose':
        return {
          hasData: !!state,
          itemCount: Array.isArray(state) ? state.length : 0,
          dataSize: JSON.stringify(state).length,
          structure: analyzeStructure(state),
          performance: measurePerformance(state),
          memory: estimateMemoryUsage(state)
        };

      default:
        return 'debug info';
    }
  });

  return { state, setState };
}`,
          impact: "medium",
          difficulty: "medium"
        }
      ]
    },
    {
      title: "开发环境性能监控",
      description: "监控useDebugValue对开发环境性能的影响，确保调试功能不影响开发体验",
      techniques: [
        {
          name: "调试性能监控",
          description: "监控格式化函数的执行时间，识别性能瓶颈",
          code: `// ✅ 调试性能监控工具
function createPerformanceAwareDebugValue() {
  const performanceMetrics = useRef({
    totalCalls: 0,
    totalTime: 0,
    slowCalls: 0
  });

  return function usePerformanceDebugValue(value, formatter) {
    useDebugValue(value, (val) => {
      if (!formatter) return val;

      const start = performance.now();
      let result;

      try {
        result = formatter(val);
      } catch (error) {
        console.warn('Debug formatter error:', error);
        return 'Formatter Error';
      }

      const duration = performance.now() - start;
      const metrics = performanceMetrics.current;

      // 更新性能指标
      metrics.totalCalls++;
      metrics.totalTime += duration;

      if (duration > 5) { // 超过5ms认为是慢调用
        metrics.slowCalls++;
        console.warn('Slow debug formatter:', duration + 'ms');
      }

      // 定期报告性能统计
      if (metrics.totalCalls % 100 === 0) {
        console.log('Debug Performance Stats:', {
          averageTime: metrics.totalTime / metrics.totalCalls,
          slowCallRate: metrics.slowCalls / metrics.totalCalls,
          totalCalls: metrics.totalCalls
        });
      }

      return result;
    });
  };
}

// 使用性能监控的调试Hook
function useMonitoredHook(data) {
  const performanceDebugValue = createPerformanceAwareDebugValue();
  const [state, setState] = useState(data);

  performanceDebugValue(state, (state) => {
    // 这里的计算会被性能监控
    return analyzeState(state);
  });

  return { state, setState };
}`,
          impact: "medium",
          difficulty: "hard"
        },
        {
          name: "内存使用优化",
          description: "优化调试信息的内存占用，避免内存泄漏",
          code: `// ✅ 内存优化：避免调试信息导致的内存泄漏
function useMemoryOptimizedDebug(largeData) {
  const [state, setState] = useState(largeData);

  useDebugValue(state, (state) => {
    // 避免在调试信息中保留大对象的引用
    if (!state) return 'No data';

    // 只提取关键信息，不保留完整对象引用
    const summary = {
      type: Array.isArray(state) ? 'array' : typeof state,
      size: Array.isArray(state) ? state.length :
            typeof state === 'object' ? Object.keys(state).length : 1,
      hasData: !!state,
      // 避免：直接返回 state 或 state 的子对象
      // 这样会在DevTools中保持对原对象的引用
    };

    // 对于大型数据，只显示摘要信息
    if (summary.size > 1000) {
      return 'Large dataset: ' + summary.size + ' items';
    }

    return summary;
  });

  return { state, setState };
}

// ✅ WeakMap缓存避免内存泄漏
const debugInfoCache = new WeakMap();

function useWeakMapCachedDebug(objectData) {
  const [state, setState] = useState(objectData);

  useDebugValue(state, (state) => {
    if (!state || typeof state !== 'object') {
      return state;
    }

    // 使用WeakMap缓存，对象被GC时缓存自动清理
    if (debugInfoCache.has(state)) {
      return debugInfoCache.get(state);
    }

    const debugInfo = generateDebugInfo(state);
    debugInfoCache.set(state, debugInfo);

    return debugInfo;
  });

  return { state, setState };
}`,
          impact: "medium",
          difficulty: "medium"
        }
      ]
    },
    {
      title: "生产环境优化保障",
      description: "确保useDebugValue在生产环境中完全不影响性能",
      techniques: [
        {
          name: "编译时优化验证",
          description: "验证生产构建中useDebugValue调用被完全移除",
          code: `// ✅ 生产环境优化验证工具
function createProductionSafeDebugValue() {
  // 开发环境：正常的useDebugValue包装
  if (process.env.NODE_ENV === 'development') {
    return function(value, formatter) {
      useDebugValue(value, formatter);
    };
  }

  // 生产环境：空函数，应该被编译器完全移除
  return function(value, formatter) {
    // 这个函数在生产构建中应该不存在
    if (process.env.NODE_ENV === 'production') {
      console.error('useDebugValue not removed in production!');
    }
  };
}

// 使用生产安全的调试Hook
function useProductionSafeHook(data) {
  const safeDebugValue = createProductionSafeDebugValue();
  const [state, setState] = useState(data);

  // 这个调用在生产环境中应该被完全移除
  safeDebugValue(state, (state) => {
    return 'Debug: ' + JSON.stringify(state);
  });

  return { state, setState };
}

// 📊 构建大小验证
// 开发构建：包含完整调试逻辑
// 生产构建：useDebugValue相关代码完全不存在
// 包体积影响：0 bytes`,
          impact: "high",
          difficulty: "easy"
        }
      ]
    }
  ],

  bestPractices: [
    "使用格式化函数进行延迟计算，避免不必要的性能开销",
    "对昂贵的调试信息计算结果进行缓存，特别是在复杂数据处理场景中",
    "根据开发需要动态调整调试信息的详细程度，避免过度计算",
    "监控格式化函数的执行时间，识别和优化性能瓶颈",
    "避免在调试信息中保留大对象的引用，防止内存泄漏",
    "使用WeakMap等弱引用机制进行调试信息缓存",
    "验证生产构建中useDebugValue调用被完全移除",
    "在团队中建立调试性能的监控和优化规范"
  ],

  commonPitfalls: [
    {
      issue: "在Hook主体中执行昂贵计算用于调试",
      cause: "直接在useDebugValue的第一个参数中传递计算结果，导致每次Hook执行都会计算",
      solution: "使用格式化函数进行延迟计算，只有在DevTools需要显示时才执行昂贵操作"
    },
    {
      issue: "格式化函数执行时间过长影响DevTools响应",
      cause: "格式化函数中包含复杂的数据处理逻辑，执行时间超过可接受范围",
      solution: "简化格式化逻辑，使用缓存机制，或者提供不同详细程度的调试信息选项"
    },
    {
      issue: "调试信息导致内存泄漏",
      cause: "在调试信息中保留了对大对象的引用，阻止垃圾回收",
      solution: "只提取和显示关键信息摘要，避免在调试信息中保留完整对象引用"
    },
    {
      issue: "生产环境中useDebugValue未被移除",
      cause: "构建配置问题或使用了不支持代码消除的打包工具",
      solution: "检查Babel配置，确保生产构建时正确移除useDebugValue调用，验证最终包体积"
    },
    {
      issue: "过度使用useDebugValue影响开发性能",
      cause: "在每个Hook中都添加详细的调试信息，累积影响开发环境性能",
      solution: "适度使用，优先在复杂Hook中使用，建立团队使用规范和性能监控"
    }
  ]
};

export default performanceOptimization;