import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'ReactNode是什么？它包含哪些类型？在实际开发中有什么使用场景？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'ReactNode是React中最宽泛的类型，包含所有可以被React渲染的内容类型，主要用于组件的children属性和函数组件的返回值。',
      detailed: `ReactNode是React TypeScript生态中最重要的联合类型之一，它定义了所有可以被React渲染的内容。

**包含的类型：**
1. **ReactElement**: JSX元素和React组件
2. **string**: 字符串文本
3. **number**: 数字内容
4. **boolean**: 布尔值（渲染时被忽略）
5. **null/undefined**: 空值（不渲染任何内容）
6. **ReactFragment**: React.Fragment
7. **ReactPortal**: Portal元素
8. **ReactNode[]**: ReactNode数组

**实际使用场景：**
- **组件children属性**: 最常见的使用场景，为组件提供最大的内容灵活性
- **函数组件返回值**: 当组件可能返回null或不同类型内容时
- **条件渲染**: 处理可能为null/undefined的渲染内容
- **动态内容生成**: 运行时动态创建的内容
- **高阶组件**: 包装其他组件时的类型定义

**优势：**
- 类型包容性最强，避免复杂的类型约束
- 完美支持React的条件渲染机制
- 简化组件API设计，提高可复用性`,
      code: `// ReactNode的基础使用示例

// 1. 组件children属性的典型使用
interface ContainerProps {
  children: ReactNode; // 接受任何可渲染内容
  className?: string;
}

const Container: React.FC<ContainerProps> = ({ children, className }) => {
  return (
    <div className={className}>
      {children} {/* 可以是任何ReactNode类型 */}
    </div>
  );
};

// 2. 函数组件返回值类型
const ConditionalComponent: React.FC<{ show: boolean }> = ({ show }): ReactNode => {
  // 可能返回JSX元素或null
  return show ? <div>显示内容</div> : null;
};

// 3. 实际使用演示 - 各种ReactNode类型
const ReactNodeDemo: React.FC = () => {
  return (
    <Container className="demo-container">
      {/* 1. 字符串 */}
      这是字符串内容
      
      {/* 2. 数字 */}
      {42}
      
      {/* 3. JSX元素 */}
      <p>这是JSX元素</p>
      
      {/* 4. 数组 */}
      {[
        <span key="1">数组项1</span>,
        <span key="2">数组项2</span>
      ]}
      
      {/* 5. 条件渲染（可能为null） */}
      {Math.random() > 0.5 && <div>随机显示</div>}
      
      {/* 6. 布尔值（不渲染） */}
      {true}
      {false}
      
      {/* 7. null/undefined（不渲染） */}
      {null}
      {undefined}
      
      {/* 8. Fragment */}
      <React.Fragment>
        <span>Fragment内容1</span>
        <span>Fragment内容2</span>
      </React.Fragment>
    </Container>
  );
};

// 4. 动态内容处理
const DynamicContent: React.FC<{ contentType: string; data: any }> = ({ 
  contentType, 
  data 
}) => {
  const renderContent = (): ReactNode => {
    switch (contentType) {
      case 'text':
        return <p>{data}</p>;
      case 'list':
        return (
          <ul>
            {data.map((item: string, index: number) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        );
      case 'component':
        return data; // 直接返回ReactElement
      default:
        return null; // 返回null是有效的ReactNode
    }
  };
  
  return (
    <div className="dynamic-content">
      {renderContent()}
    </div>
  );
};

// 5. 高阶组件中的ReactNode使用
function withLoading<P extends {}>(
  WrappedComponent: React.ComponentType<P>
): React.FC<P & { loading?: boolean }> {
  return ({ loading, ...props }) => {
    if (loading) {
      return <div>加载中...</div>; // ReactNode
    }
    return <WrappedComponent {...props as P} />; // ReactElement
  };
}

export { Container, ConditionalComponent, DynamicContent };`
    },
    tags: ['基础概念', '类型系统']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'ReactNode和ReactElement有什么区别？什么时候应该使用哪一个？如何在它们之间进行类型转换？',
    difficulty: 'medium',
    frequency: 'high',
    category: '类型对比',
    answer: {
      brief: 'ReactNode是更宽泛的联合类型，包含所有可渲染内容；ReactElement更具体，只包含JSX元素。children属性用ReactNode，明确需要JSX元素时用ReactElement。',
      detailed: `ReactNode和ReactElement的区别是React TypeScript开发中的重要概念：

**核心区别：**

1. **类型范围**：
   - ReactNode: 联合类型，包含所有可渲染内容
   - ReactElement: 具体类型，只包含JSX元素对象

2. **包含内容**：
   - ReactNode: ReactElement + string + number + boolean + null + undefined + 数组
   - ReactElement: 只有通过JSX或createElement创建的元素

3. **使用场景**：
   - ReactNode: children属性、可能为null的返回值、动态内容
   - ReactElement: 明确需要JSX元素、元素操作、组件包装

**选择原则：**
- 需要最大灵活性时选择ReactNode
- 需要操作JSX元素属性时选择ReactElement
- 不确定时优先选择ReactNode

**类型转换和验证：**
- 使用React.isValidElement检查是否为ReactElement
- 使用类型守卫进行安全转换
- 条件渲染处理不同类型`,
      code: `// ReactNode vs ReactElement 详细对比

// 1. 类型定义对比
interface ReactNodeProps {
  content: ReactNode; // 接受任何可渲染内容
}

interface ReactElementProps {
  element: ReactElement; // 只接受JSX元素
}

// 2. 使用场景对比
const ReactNodeComponent: React.FC<ReactNodeProps> = ({ content }) => {
  return (
    <div className="content-wrapper">
      {content} {/* 可以是任何ReactNode */}
    </div>
  );
};

const ReactElementComponent: React.FC<ReactElementProps> = ({ element }) => {
  // 可以安全地访问element的属性
  console.log('元素类型:', element.type);
  console.log('元素属性:', element.props);
  
  return (
    <div className="element-wrapper">
      {element} {/* 确保是JSX元素 */}
    </div>
  );
};

// 3. 实际使用示例
const TypeComparisonDemo: React.FC = () => {
  const textContent = "这是文本内容";
  const numberContent = 42;
  const elementContent = <span>这是JSX元素</span>;
  const nullContent = null;

  return (
    <div>
      {/* ✅ ReactNode可以接受所有这些类型 */}
      <ReactNodeComponent content={textContent} />
      <ReactNodeComponent content={numberContent} />
      <ReactNodeComponent content={elementContent} />
      <ReactNodeComponent content={nullContent} />
      
      {/* ✅ ReactElement只能接受JSX元素 */}
      <ReactElementComponent element={elementContent} />
      
      {/* ❌ 以下会产生类型错误 */}
      {/* <ReactElementComponent element={textContent} /> */}
      {/* <ReactElementComponent element={numberContent} /> */}
      {/* <ReactElementComponent element={nullContent} /> */}
    </div>
  );
};

// 4. 类型检查和转换
const TypeChecker = {
  // 检查是否为ReactElement
  isReactElement(node: ReactNode): node is ReactElement {
    return React.isValidElement(node);
  },
  
  // 安全地处理ReactNode
  processReactNode(node: ReactNode): {
    type: string;
    canManipulate: boolean;
    content: ReactNode;
  } {
    if (this.isReactElement(node)) {
      return {
        type: 'ReactElement',
        canManipulate: true,
        content: React.cloneElement(node, { 
          className: (node.props.className || '') + ' processed'
        })
      };
    }
    
    if (typeof node === 'string') {
      return {
        type: 'string',
        canManipulate: false,
        content: node.toUpperCase()
      };
    }
    
    if (typeof node === 'number') {
      return {
        type: 'number',
        canManipulate: false,
        content: node.toFixed(2)
      };
    }
    
    if (node === null || node === undefined) {
      return {
        type: 'null/undefined',
        canManipulate: false,
        content: '(无内容)'
      };
    }
    
    if (Array.isArray(node)) {
      return {
        type: 'array',
        canManipulate: true,
        content: node.map((item, index) => 
          React.isValidElement(item) 
            ? React.cloneElement(item, { key: index })
            : item
        )
      };
    }
    
    return {
      type: 'unknown',
      canManipulate: false,
      content: String(node)
    };
  }
};

// 5. 实用的类型转换工具
class ReactNodeProcessor {
  // 将ReactNode转换为ReactElement数组
  static extractElements(node: ReactNode): ReactElement[] {
    const elements: ReactElement[] = [];
    
    React.Children.forEach(node, child => {
      if (React.isValidElement(child)) {
        elements.push(child);
      }
    });
    
    return elements;
  }
  
  // 过滤掉非ReactElement的内容
  static filterElements(node: ReactNode): ReactNode {
    if (React.isValidElement(node)) {
      return node;
    }
    
    if (Array.isArray(node)) {
      return node.filter(React.isValidElement);
    }
    
    return null;
  }
  
  // 包装非ReactElement内容为ReactElement
  static wrapAsElement(node: ReactNode): ReactElement {
    if (React.isValidElement(node)) {
      return node;
    }
    
    if (typeof node === 'string' || typeof node === 'number') {
      return <span>{node}</span>;
    }
    
    if (Array.isArray(node)) {
      return (
        <div>
          {node.map((item, index) => (
            <div key={index}>{item}</div>
          ))}
        </div>
      );
    }
    
    return <div>无效内容</div>;
  }
}

// 6. 高级类型操作示例
const AdvancedTypeHandling: React.FC<{
  mixedContent: ReactNode[];
}> = ({ mixedContent }) => {
  const processedContent = mixedContent.map((content, index) => {
    const processed = TypeChecker.processReactNode(content);
    
    return (
      <div key={index} className="content-item">
        <div className="type-info">
          类型: {processed.type} | 
          可操作: {processed.canManipulate ? '是' : '否'}
        </div>
        <div className="content">
          {processed.content}
        </div>
      </div>
    );
  });
  
  // 提取所有ReactElement
  const elements = ReactNodeProcessor.extractElements(mixedContent);
  
  return (
    <div className="advanced-demo">
      <h3>混合内容处理</h3>
      <div className="processed-content">
        {processedContent}
      </div>
      
      <h4>提取的ReactElement ({elements.length}个)</h4>
      <div className="extracted-elements">
        {elements.map((element, index) => (
          <div key={index} className="extracted-item">
            元素类型: {element.type}
          </div>
        ))}
      </div>
    </div>
  );
};

export { TypeChecker, ReactNodeProcessor, AdvancedTypeHandling };`
    },
    tags: ['类型对比', 'TypeScript']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 3,
    question: '如何优化包含大量ReactNode的组件性能？请介绍ReactNode的高级操作技巧，如深度克隆、类型验证、以及在复杂应用中的最佳实践。',
    difficulty: 'hard',
    frequency: 'medium',
    category: '性能优化',
    answer: {
      brief: '通过React.memo缓存、useMemo优化渲染、合理的key设置、ReactNode深度克隆、类型验证、虚拟化等技术来优化大量ReactNode的性能，并建立完善的错误处理和监控机制。',
      detailed: `ReactNode性能优化是高级React开发的重要技能，涉及多个层面的优化策略：

**性能优化策略：**

1. **渲染优化**：
   - React.memo缓存组件渲染结果
   - useMemo缓存ReactNode创建过程
   - useCallback稳定事件处理函数

2. **结构优化**：
   - 合理设置key属性避免不必要的重新渲染
   - 控制组件嵌套深度
   - 使用Fragment减少DOM层级

3. **数据优化**：
   - 避免在render中创建新对象
   - 使用不可变数据结构
   - 合理的数据分片和懒加载

**高级操作技巧：**

1. **深度克隆**：安全复制复杂的ReactNode结构
2. **类型验证**：运行时检查ReactNode有效性
3. **性能监控**：分析ReactNode渲染性能
4. **错误处理**：优雅处理无效的ReactNode

**企业级最佳实践：**
- 建立ReactNode操作工具库
- 实现性能监控和预警机制
- 制定代码规范和Review标准
- 构建自动化测试覆盖`,
      code: `// ReactNode高级性能优化实践

import React, { memo, useMemo, useCallback, useRef, useEffect } from 'react';

// 1. ReactNode性能监控工具
class ReactNodePerformanceMonitor {
  private static instance: ReactNodePerformanceMonitor;
  private metrics: Map<string, PerformanceMetric> = new Map();
  
  static getInstance(): ReactNodePerformanceMonitor {
    if (!this.instance) {
      this.instance = new ReactNodePerformanceMonitor();
    }
    return this.instance;
  }
  
  // 测量ReactNode渲染性能
  measureRender<T extends ReactNode>(
    name: string,
    renderFn: () => T
  ): T {
    const start = performance.now();
    const result = renderFn();
    const duration = performance.now() - start;
    
    this.updateMetric(name, duration);
    
    if (duration > 16) { // 超过一帧时间
      console.warn(\`ReactNode渲染性能警告: \${name} 耗时 \${duration.toFixed(2)}ms\`);
    }
    
    return result;
  }
  
  private updateMetric(name: string, duration: number): void {
    const existing = this.metrics.get(name) || {
      count: 0,
      totalTime: 0,
      averageTime: 0,
      maxTime: 0
    };
    
    existing.count++;
    existing.totalTime += duration;
    existing.averageTime = existing.totalTime / existing.count;
    existing.maxTime = Math.max(existing.maxTime, duration);
    
    this.metrics.set(name, existing);
  }
  
  getReport(): Record<string, PerformanceMetric> {
    return Object.fromEntries(this.metrics);
  }
}

interface PerformanceMetric {
  count: number;
  totalTime: number;
  averageTime: number;
  maxTime: number;
}

// 2. ReactNode深度操作工具
class ReactNodeDeepProcessor {
  // 深度克隆ReactNode，保持引用关系
  static deepClone(node: ReactNode): ReactNode {
    if (React.isValidElement(node)) {
      const clonedProps = { ...node.props };
      
      // 递归克隆children
      if (clonedProps.children) {
        clonedProps.children = React.Children.map(
          clonedProps.children,
          child => this.deepClone(child)
        );
      }
      
      return React.cloneElement(node, {
        ...clonedProps,
        key: node.key || Math.random().toString(36)
      });
    }
    
    if (Array.isArray(node)) {
      return node.map(item => this.deepClone(item));
    }
    
    return node;
  }
  
  // 深度遍历ReactNode
  static traverse(
    node: ReactNode,
    visitor: (node: ReactNode, depth: number, path: string) => void,
    depth: number = 0,
    path: string = 'root'
  ): void {
    visitor(node, depth, path);
    
    if (React.isValidElement(node)) {
      React.Children.forEach(node.props.children, (child, index) => {
        this.traverse(child, visitor, depth + 1, \`\${path}[\${index}]\`);
      });
    }
    
    if (Array.isArray(node)) {
      node.forEach((child, index) => {
        this.traverse(child, visitor, depth + 1, \`\${path}[\${index}]\`);
      });
    }
  }
  
  // 类型安全的ReactNode变换
  static transform(
    node: ReactNode,
    transformer: (node: ReactNode) => ReactNode
  ): ReactNode {
    const transformed = transformer(node);
    
    if (React.isValidElement(transformed)) {
      const transformedChildren = React.Children.map(
        transformed.props.children,
        child => this.transform(child, transformer)
      );
      
      return React.cloneElement(transformed, {
        ...transformed.props,
        children: transformedChildren
      });
    }
    
    if (Array.isArray(transformed)) {
      return transformed.map(item => this.transform(item, transformer));
    }
    
    return transformed;
  }
}

// 3. 高性能ReactNode渲染组件
interface OptimizedRendererProps {
  nodes: ReactNode[];
  chunkSize?: number;
  virtualizeThreshold?: number;
  enablePerfMonitoring?: boolean;
}

const OptimizedReactNodeRenderer = memo<OptimizedRendererProps>(({
  nodes,
  chunkSize = 50,
  virtualizeThreshold = 100,
  enablePerfMonitoring = false
}) => {
  const performanceMonitor = useRef(ReactNodePerformanceMonitor.getInstance());
  const [visibleRange, setVisibleRange] = React.useState({ start: 0, end: chunkSize });
  
  // 虚拟化处理大量节点
  const shouldVirtualize = nodes.length > virtualizeThreshold;
  
  // 优化的渲染函数
  const renderNodes = useMemo(() => {
    const renderFn = () => {
      const nodesToRender = shouldVirtualize 
        ? nodes.slice(visibleRange.start, visibleRange.end)
        : nodes;
      
      return nodesToRender.map((node, index) => {
        const actualIndex = shouldVirtualize ? visibleRange.start + index : index;
        
        // 为每个节点添加性能监控
        if (enablePerfMonitoring) {
          return performanceMonitor.current.measureRender(
            \`node-\${actualIndex}\`,
            () => (
              <div key={actualIndex} className="optimized-node-wrapper">
                {node}
              </div>
            )
          );
        }
        
        return (
          <div key={actualIndex} className="optimized-node-wrapper">
            {node}
          </div>
        );
      });
    };
    
    return enablePerfMonitoring 
      ? performanceMonitor.current.measureRender('batch-render', renderFn)
      : renderFn();
  }, [nodes, visibleRange, shouldVirtualize, enablePerfMonitoring]);
  
  // 滚动处理（虚拟化）
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    if (!shouldVirtualize) return;
    
    const container = e.currentTarget;
    const scrollTop = container.scrollTop;
    const itemHeight = 100; // 假设每个项目高度
    
    const start = Math.floor(scrollTop / itemHeight);
    const end = start + chunkSize;
    
    setVisibleRange({ start, end });
  }, [shouldVirtualize, chunkSize]);
  
  // 性能报告
  useEffect(() => {
    if (enablePerfMonitoring) {
      const timer = setTimeout(() => {
        const report = performanceMonitor.current.getReport();
        console.log('ReactNode性能报告:', report);
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [enablePerfMonitoring]);
  
  const containerStyle: React.CSSProperties = shouldVirtualize ? {
    height: '400px',
    overflowY: 'auto',
    position: 'relative'
  } : {};
  
  return (
    <div 
      className="optimized-renderer"
      style={containerStyle}
      onScroll={handleScroll}
    >
      {shouldVirtualize && (
        <div 
          style={{ height: nodes.length * 100 }}
          className="virtual-spacer"
        />
      )}
      
      <div className="rendered-nodes">
        {renderNodes}
      </div>
      
      {enablePerfMonitoring && (
        <div className="performance-overlay">
          渲染节点: {renderNodes.length} / {nodes.length}
          {shouldVirtualize && \` (虚拟化: \${visibleRange.start}-\${visibleRange.end})\`}
        </div>
      )}
    </div>
  );
});

// 4. ReactNode验证和错误处理
class ReactNodeValidator {
  static validate(node: ReactNode): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let elementCount = 0;
    let depth = 0;
    
    ReactNodeDeepProcessor.traverse(node, (currentNode, currentDepth) => {
      depth = Math.max(depth, currentDepth);
      
      if (React.isValidElement(currentNode)) {
        elementCount++;
        
        // 检查key属性
        if (currentDepth > 0 && !currentNode.key) {
          warnings.push(\`第\${currentDepth}层元素缺少key属性\`);
        }
        
        // 检查过深嵌套
        if (currentDepth > 10) {
          warnings.push(\`嵌套层级过深: \${currentDepth}层\`);
        }
      }
      
      // 检查无效对象
      if (typeof currentNode === 'object' && 
          currentNode !== null && 
          !React.isValidElement(currentNode) && 
          !Array.isArray(currentNode)) {
        errors.push('包含无效对象，可能导致渲染错误');
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      stats: { elementCount, depth }
    };
  }
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  stats: {
    elementCount: number;
    depth: number;
  };
}

// 5. 企业级使用示例
const EnterpriseReactNodeManager: React.FC = () => {
  const [nodes, setNodes] = React.useState<ReactNode[]>([]);
  const [validationResult, setValidationResult] = React.useState<ValidationResult | null>(null);
  
  // 生成大量测试节点
  const generateTestNodes = useCallback((count: number) => {
    const newNodes: ReactNode[] = [];
    
    for (let i = 0; i < count; i++) {
      const nodeType = Math.floor(Math.random() * 4);
      
      switch (nodeType) {
        case 0:
          newNodes.push(\`文本节点 \${i}\`);
          break;
        case 1:
          newNodes.push(i);
          break;
        case 2:
          newNodes.push(
            <div key={i} className="test-element">
              <h4>元素 {i}</h4>
              <p>内容 {i}</p>
            </div>
          );
          break;
        case 3:
          newNodes.push([
            <span key={\`\${i}-1\`}>子项1</span>,
            <span key={\`\${i}-2\`}>子项2</span>
          ]);
          break;
      }
    }
    
    setNodes(newNodes);
    
    // 验证生成的节点
    const combined = React.createElement(React.Fragment, null, ...newNodes);
    setValidationResult(ReactNodeValidator.validate(combined));
  }, []);
  
  // 深度克隆所有节点
  const cloneAllNodes = useCallback(() => {
    const clonedNodes = nodes.map(node => 
      ReactNodeDeepProcessor.deepClone(node)
    );
    setNodes(clonedNodes);
  }, [nodes]);
  
  return (
    <div className="enterprise-manager">
      <div className="controls">
        <button onClick={() => generateTestNodes(50)}>
          生成50个节点
        </button>
        <button onClick={() => generateTestNodes(200)}>
          生成200个节点
        </button>
        <button onClick={cloneAllNodes}>
          深度克隆所有节点
        </button>
      </div>
      
      {validationResult && (
        <div className="validation-result">
          <h4>验证结果</h4>
          <p>有效性: {validationResult.isValid ? '✅' : '❌'}</p>
          <p>元素数量: {validationResult.stats.elementCount}</p>
          <p>最大深度: {validationResult.stats.depth}</p>
          
          {validationResult.errors.length > 0 && (
            <div className="errors">
              <h5>错误:</h5>
              <ul>
                {validationResult.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
          
          {validationResult.warnings.length > 0 && (
            <div className="warnings">
              <h5>警告:</h5>
              <ul>
                {validationResult.warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
      
      <OptimizedReactNodeRenderer
        nodes={nodes}
        chunkSize={20}
        virtualizeThreshold={50}
        enablePerfMonitoring={true}
      />
    </div>
  );
};

export { 
  ReactNodePerformanceMonitor, 
  ReactNodeDeepProcessor, 
  ReactNodeValidator,
  OptimizedReactNodeRenderer,
  EnterpriseReactNodeManager 
};`
    },
    tags: ['性能优化', '高级操作']
  }
];

// ReactNode面试问题内容已完成
export default interviewQuestions;