import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ReactNodeData: ApiItem = {
  id: 'ReactNode',
  title: 'ReactNode',
  description: '{API_BRIEF_DESCRIPTION}',
  category: 'React Types',
  difficulty: 'easy',
  
  syntax: `{API_SYNTAX}`,
  example: `{API_EXAMPLE}`,
  notes: '{API_NOTES}',
  
  version: 'React 16.0.0+',
  tags: ["React","TypeScript","ReactNode","Types"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ReactNodeData;