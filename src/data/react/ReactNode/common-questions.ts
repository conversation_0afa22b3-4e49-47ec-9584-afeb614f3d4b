import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '什么时候应该使用ReactNode而不是ReactElement？两者的使用场景有什么区别？',
    answer: `这是React TypeScript开发中最常遇到的问题之一。选择正确的类型对于构建健壮的应用至关重要：

**使用ReactNode的场景：**
1. **组件children属性**：当你需要最大的灵活性，允许传入任何可渲染内容时
2. **条件渲染返回值**：当组件可能返回null、undefined或不同类型内容时
3. **动态内容处理**：处理来自API或用户输入的不确定类型内容时
4. **通用组件库**：构建需要接受各种内容类型的可复用组件时

**使用ReactElement的场景：**
1. **需要操作JSX元素属性**：当你需要访问element.type、element.props等属性时
2. **元素克隆和修改**：使用React.cloneElement进行元素操作时
3. **严格的类型约束**：明确要求传入JSX元素，不接受其他类型时
4. **高阶组件**：包装和增强特定React组件时

**选择原则：**
- 不确定时优先选择ReactNode（更包容）
- 需要元素操作时选择ReactElement（更具体）
- API设计时考虑使用者的便利性
- 组件库倾向于使用ReactNode提供更好的开发体验`,
    code: `// ReactNode vs ReactElement 使用对比

// ✅ 使用ReactNode - 灵活的组件children
interface ContainerProps {
  children: ReactNode; // 接受任何可渲染内容
  className?: string;
}

const Container: React.FC<ContainerProps> = ({ children, className }) => {
  return (
    <div className={className}>
      {children} {/* 可以是文本、数字、JSX、数组、null等 */}
    </div>
  );
};

// ✅ 使用ReactElement - 需要元素操作
interface ElementWrapperProps {
  element: ReactElement; // 只接受JSX元素
  wrapperClass?: string;
}

const ElementWrapper: React.FC<ElementWrapperProps> = ({ 
  element, 
  wrapperClass 
}) => {
  // 可以安全地访问element属性
  console.log('元素类型:', element.type);
  console.log('元素属性:', element.props);
  
  // 克隆并修改元素
  const enhancedElement = React.cloneElement(element, {
    className: (element.props.className || '') + ' enhanced',
    'data-wrapper': true
  });
  
  return (
    <div className={wrapperClass}>
      {enhancedElement}
    </div>
  );
};

// 🎯 实际使用示例
const App: React.FC = () => {
  return (
    <div>
      {/* ReactNode用法 - 支持各种内容 */}
      <Container className="content">
        <h1>标题</h1>
        <p>段落内容</p>
        {true && <span>条件内容</span>}
        {[1, 2, 3].map(num => <div key={num}>{num}</div>)}
        文本内容
        {42}
        {null}
      </Container>
      
      {/* ReactElement用法 - 只接受JSX元素 */}
      <ElementWrapper 
        element={<button onClick={() => console.log('点击')}>按钮</button>}
        wrapperClass="button-wrapper"
      />
      
      {/* ❌ 这会导致类型错误 */}
      {/* <ElementWrapper element="文本" /> */}
      {/* <ElementWrapper element={42} /> */}
    </div>
  );
};

// 🔧 实用工具：类型检查和转换
const TypeUtils = {
  // 检查是否为ReactElement
  isReactElement: (node: ReactNode): node is ReactElement => {
    return React.isValidElement(node);
  },
  
  // 安全地将ReactNode转换为ReactElement
  toReactElement: (node: ReactNode): ReactElement => {
    if (React.isValidElement(node)) {
      return node;
    }
    
    // 包装非元素内容
    return <span>{node}</span>;
  },
  
  // 从ReactNode数组中提取ReactElement
  extractElements: (nodes: ReactNode[]): ReactElement[] => {
    return nodes.filter(React.isValidElement);
  }
};

export { Container, ElementWrapper, TypeUtils };`,
    tags: ['类型选择', 'TypeScript'],
    relatedQuestions: ['ReactElement的具体用法', 'children属性的类型定义']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '如何正确处理可能为null或undefined的ReactNode？条件渲染的最佳实践是什么？',
    answer: `条件渲染是React开发中的基础操作，但不正确的处理方式可能导致意外的渲染结果。以下是处理可能为空的ReactNode的最佳实践：

**常见问题：**
1. **假值显示问题**：\`{count && <div>{count}</div>}\`当count为0时会显示0
2. **undefined渲染警告**：直接渲染undefined可能导致React警告
3. **复杂条件逻辑**：多层嵌套的条件判断导致代码难以维护

**最佳实践原则：**
1. **明确的布尔转换**：使用Boolean()或!!进行明确的布尔转换
2. **三元运算符优于&&运算符**：提供明确的else分支
3. **提前返回空值**：在组件顶部处理空值情况
4. **使用可选链和空值合并**：?. 和 ?? 操作符简化空值处理
5. **创建通用的条件渲染组件**：封装常见的条件渲染逻辑

**性能考虑：**
- 避免在条件表达式中创建新对象
- 使用useMemo缓存复杂的条件渲染逻辑
- 考虑使用React.Suspense处理异步内容`,
    code: `// ReactNode条件渲染最佳实践

// ❌ 常见错误做法
const BadConditionalRender: React.FC<{
  count: number;
  user?: User;
  items: Item[];
}> = ({ count, user, items }) => {
  return (
    <div>
      {/* 问题1: count为0时会显示0 */}
      {count && <div>数量: {count}</div>}
      
      {/* 问题2: 可能显示false */}
      {user && user.name && <span>{user.name}</span>}
      
      {/* 问题3: 每次渲染创建新数组 */}
      {items.length && items.map(item => <Item key={item.id} item={item} />)}
    </div>
  );
};

// ✅ 正确的条件渲染做法
const GoodConditionalRender: React.FC<{
  count: number;
  user?: User;
  items: Item[];
}> = ({ count, user, items }) => {
  // 提前处理空值情况
  if (!user) {
    return <div className="no-user">请先登录</div>;
  }
  
  return (
    <div>
      {/* 正确1: 明确的布尔判断 */}
      {count > 0 ? <div>数量: {count}</div> : null}
      
      {/* 正确2: 使用可选链和空值合并 */}
      <span>{user?.name ?? '匿名用户'}</span>
      
      {/* 正确3: 明确的条件判断 */}
      {items.length > 0 ? (
        <ul>
          {items.map(item => (
            <Item key={item.id} item={item} />
          ))}
        </ul>
      ) : (
        <div className="empty">暂无数据</div>
      )}
    </div>
  );
};

// 🔧 通用条件渲染组件
interface ConditionalProps {
  condition: boolean;
  children: ReactNode;
  fallback?: ReactNode;
  wrapper?: React.ComponentType<{ children: ReactNode }>;
}

const Conditional: React.FC<ConditionalProps> = ({ 
  condition, 
  children, 
  fallback = null,
  wrapper: Wrapper 
}) => {
  const content = condition ? children : fallback;
  
  if (Wrapper && content) {
    return <Wrapper>{content}</Wrapper>;
  }
  
  return <>{content}</>;
};

// 📦 高级条件渲染Hook
const useConditionalRender = () => {
  // 安全的条件渲染函数
  const renderIf = useCallback((
    condition: boolean, 
    node: ReactNode,
    fallback: ReactNode = null
  ): ReactNode => {
    return condition ? node : fallback;
  }, []);
  
  // 渲染非空值
  const renderIfNotEmpty = useCallback((
    value: any,
    renderFn: (value: any) => ReactNode,
    fallback: ReactNode = null
  ): ReactNode => {
    if (value === null || value === undefined || value === '') {
      return fallback;
    }
    
    if (Array.isArray(value) && value.length === 0) {
      return fallback;
    }
    
    return renderFn(value);
  }, []);
  
  // 渲染加载状态
  const renderWithLoading = useCallback((
    isLoading: boolean,
    content: ReactNode,
    loadingNode: ReactNode = <div>加载中...</div>
  ): ReactNode => {
    return isLoading ? loadingNode : content;
  }, []);
  
  return {
    renderIf,
    renderIfNotEmpty,
    renderWithLoading
  };
};

// 🎯 实际使用示例
const ConditionalRenderExample: React.FC = () => {
  const [user, setUser] = React.useState<User | null>(null);
  const [items, setItems] = React.useState<Item[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [count, setCount] = React.useState(0);
  
  const { renderIf, renderIfNotEmpty, renderWithLoading } = useConditionalRender();
  
  // 缓存复杂的条件渲染逻辑
  const userSection = useMemo((): ReactNode => {
    return renderIfNotEmpty(
      user,
      (u) => (
        <div className="user-section">
          <h3>欢迎, {u.name}</h3>
          <p>邮箱: {u.email}</p>
          {renderIf(u.isVIP, <span className="vip-badge">VIP</span>)}
        </div>
      ),
      <div className="login-prompt">
        <p>请先登录以查看个人信息</p>
        <button onClick={() => console.log('登录')}>登录</button>
      </div>
    );
  }, [user, renderIf, renderIfNotEmpty]);
  
  const itemsList = useMemo((): ReactNode => {
    return renderIfNotEmpty(
      items,
      (itemArray) => (
        <div className="items-container">
          <h4>项目列表 ({itemArray.length})</h4>
          {itemArray.map(item => (
            <div key={item.id} className="item">
              {item.name}
            </div>
          ))}
        </div>
      ),
      <div className="empty-items">
        <p>暂无项目</p>
        <button onClick={() => setItems([{id: '1', name: '示例项目'}])}>
          添加示例项目
        </button>
      </div>
    );
  }, [items, renderIfNotEmpty]);
  
  return (
    <div className="conditional-example">
      {/* 使用通用条件组件 */}
      <Conditional
        condition={!loading}
        fallback={<div className="loading">页面加载中...</div>}
      >
        <div className="content">
          <h1>条件渲染示例</h1>
          
          {/* 数字条件渲染 */}
          <Conditional
            condition={count > 0}
            wrapper={({ children }) => <div className="count-section">{children}</div>}
          >
            <p>当前计数: {count}</p>
          </Conditional>
          
          {/* 用户信息条件渲染 */}
          {userSection}
          
          {/* 项目列表条件渲染 */}
          {renderWithLoading(loading, itemsList)}
          
          {/* 控制按钮 */}
          <div className="controls">
            <button onClick={() => setCount(c => c + 1)}>
              增加计数
            </button>
            <button onClick={() => setUser({ name: '测试用户', email: '<EMAIL>', isVIP: true })}>
              模拟登录
            </button>
            <button onClick={() => setLoading(!loading)}>
              切换加载状态
            </button>
          </div>
        </div>
      </Conditional>
    </div>
  );
};

export { Conditional, useConditionalRender, ConditionalRenderExample };`,
    tags: ['条件渲染', '最佳实践'],
    relatedQuestions: ['如何处理异步加载的ReactNode', 'React.Suspense的使用场景']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: '渲染大量ReactNode时遇到性能问题怎么办？有哪些有效的优化策略？',
    answer: `大量ReactNode渲染是现代Web应用常见的性能挑战。正确的优化策略可以将渲染时间从秒级降低到毫秒级：

**性能问题识别：**
1. **渲染时间过长**：单次渲染超过16ms影响流畅度
2. **内存占用过高**：大量DOM节点导致内存泄漏
3. **滚动卡顿**：长列表滚动不流畅
4. **交互延迟**：用户操作响应缓慢

**核心优化策略：**

**1. 虚拟化（最有效）**
- 只渲染可视区域的元素
- 大幅减少DOM节点数量
- 适用于长列表、表格、网格等场景

**2. 记忆化缓存**
- React.memo防止不必要的重渲染
- useMemo缓存昂贵的计算
- useCallback稳定函数引用

**3. 代码分割和懒加载**
- React.lazy延迟加载组件
- 动态import按需加载
- Suspense配合实现优雅的加载体验

**4. 批量更新**
- 合并多个状态更新
- 使用unstable_batchedUpdates
- 减少渲染次数

**5. 结构优化**
- 合理的key设置
- 避免深层嵌套
- 使用Fragment减少包装元素

**实施建议：**
- 建立性能监控基线
- 优先优化最耗时的组件
- 渐进式优化，测量每次改进的效果
- 平衡代码复杂度和性能收益`,
    code: `// ReactNode大量渲染性能优化实践

// 🚀 策略1: 虚拟化长列表
import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';

interface VirtualizedListProps {
  items: any[];
  renderItem: (item: any, index: number) => ReactNode;
  itemHeight: number;
  containerHeight: number;
  overscan?: number; // 预渲染项目数
}

const VirtualizedList: React.FC<VirtualizedListProps> = ({
  items,
  renderItem,
  itemHeight,
  containerHeight,
  overscan = 5
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // 计算可见范围
  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);
  
  // 可见项目渲染
  const visibleItems = useMemo(() => {
    return items
      .slice(visibleRange.startIndex, visibleRange.endIndex)
      .map((item, index) => {
        const actualIndex = visibleRange.startIndex + index;
        return (
          <div
            key={actualIndex}
            style={{
              position: 'absolute',
              top: actualIndex * itemHeight,
              height: itemHeight,
              width: '100%',
              left: 0
            }}
          >
            {renderItem(item, actualIndex)}
          </div>
        );
      });
  }, [items, visibleRange, renderItem, itemHeight]);
  
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);
  
  return (
    <div
      ref={containerRef}
      style={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }}
      onScroll={handleScroll}
    >
      {/* 占位空间 */}
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        {visibleItems}
      </div>
    </div>
  );
};

// 🎯 策略2: 智能记忆化组件
const OptimizedItem = React.memo<{
  item: {
    id: string;
    title: string;
    content: string;
    metadata?: any;
  };
  onAction: (id: string, action: string) => void;
}>(({ item, onAction }) => {
  // 稳定的事件处理函数
  const handleClick = useCallback(() => {
    onAction(item.id, 'click');
  }, [item.id, onAction]);
  
  const handleEdit = useCallback(() => {
    onAction(item.id, 'edit');
  }, [item.id, onAction]);
  
  return (
    <div className="optimized-item">
      <h3>{item.title}</h3>
      <p>{item.content}</p>
      <div className="actions">
        <button onClick={handleClick}>查看</button>
        <button onClick={handleEdit}>编辑</button>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // 自定义比较函数
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.item.title === nextProps.item.title &&
    prevProps.item.content === nextProps.item.content &&
    prevProps.onAction === nextProps.onAction
  );
});

// ⚡ 策略3: 批量更新和状态管理
const useBatchedUpdates = () => {
  const [pendingUpdates, setPendingUpdates] = useState<Array<() => void>>([]);
  
  const batchUpdate = useCallback((updateFn: () => void) => {
    setPendingUpdates(prev => [...prev, updateFn]);
  }, []);
  
  // 使用 setTimeout 批量执行更新
  useEffect(() => {
    if (pendingUpdates.length > 0) {
      const timer = setTimeout(() => {
        // 批量执行所有更新
        React.unstable_batchedUpdates(() => {
          pendingUpdates.forEach(update => update());
        });
        setPendingUpdates([]);
      }, 0);
      
      return () => clearTimeout(timer);
    }
  }, [pendingUpdates]);
  
  return { batchUpdate };
};

// 🏗️ 策略4: 智能懒加载容器
const LazyRenderContainer: React.FC<{
  children: ReactNode[];
  batchSize?: number;
  delay?: number;
}> = ({ children, batchSize = 10, delay = 100 }) => {
  const [renderedCount, setRenderedCount] = useState(batchSize);
  const [isLoading, setIsLoading] = useState(false);
  
  // 逐步渲染更多内容
  const loadMore = useCallback(async () => {
    if (renderedCount >= children.length || isLoading) return;
    
    setIsLoading(true);
    
    // 模拟异步延迟，避免阻塞主线程
    await new Promise(resolve => setTimeout(resolve, delay));
    
    setRenderedCount(prev => Math.min(prev + batchSize, children.length));
    setIsLoading(false);
  }, [renderedCount, children.length, isLoading, batchSize, delay]);
  
  // 交集观察器检测是否需要加载更多
  const sentinelRef = useCallback((node: HTMLDivElement | null) => {
    if (!node) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );
    
    observer.observe(node);
    return () => observer.disconnect();
  }, [loadMore]);
  
  const visibleChildren = useMemo(() => {
    return children.slice(0, renderedCount);
  }, [children, renderedCount]);
  
  return (
    <div className="lazy-render-container">
      {visibleChildren}
      
      {renderedCount < children.length && (
        <div ref={sentinelRef} className="load-more-sentinel">
          {isLoading ? (
            <div className="loading">加载中...</div>
          ) : (
            <div className="load-more-trigger">向下滚动加载更多</div>
          )}
        </div>
      )}
    </div>
  );
};

// 📊 策略5: 性能监控和分析
const usePerformanceMonitor = (componentName: string) => {
  const renderStart = useRef<number>(0);
  const renderCount = useRef<number>(0);
  
  // 记录渲染开始时间
  renderStart.current = performance.now();
  renderCount.current++;
  
  useEffect(() => {
    const renderDuration = performance.now() - renderStart.current;
    
    console.log(\`📊 性能监控 - \${componentName}:\`, {
      渲染次数: renderCount.current,
      渲染耗时: \`\${renderDuration.toFixed(2)}ms\`,
      是否超时: renderDuration > 16 ? '⚠️ 是' : '✅ 否'
    });
    
    // 性能警告
    if (renderDuration > 16) {
      console.warn(\`⚠️ 性能警告: \${componentName} 渲染耗时 \${renderDuration.toFixed(2)}ms\`);
    }
  });
  
  return {
    renderCount: renderCount.current,
    measureRender: (fn: () => ReactNode) => {
      const start = performance.now();
      const result = fn();
      const duration = performance.now() - start;
      
      console.log(\`🎯 渲染测量 - \${componentName}:\`, {
        耗时: \`\${duration.toFixed(2)}ms\`
      });
      
      return result;
    }
  };
};

// 🎯 综合示例：高性能大数据列表
const HighPerformanceList: React.FC<{
  data: Array<{ id: string; title: string; content: string }>;
}> = ({ data }) => {
  const { measureRender } = usePerformanceMonitor('HighPerformanceList');
  const { batchUpdate } = useBatchedUpdates();
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  
  // 稳定的事件处理函数
  const handleItemAction = useCallback((id: string, action: string) => {
    batchUpdate(() => {
      switch (action) {
        case 'select':
          setSelectedIds(prev => new Set(prev).add(id));
          break;
        case 'deselect':
          setSelectedIds(prev => {
            const next = new Set(prev);
            next.delete(id);
            return next;
          });
          break;
      }
    });
  }, [batchUpdate]);
  
  // 渲染项目的函数
  const renderItem = useCallback((item: any, index: number) => {
    return (
      <OptimizedItem
        key={item.id}
        item={item}
        onAction={handleItemAction}
      />
    );
  }, [handleItemAction]);
  
  return measureRender(() => (
    <div className="high-performance-list">
      <div className="list-header">
        <h2>高性能列表 ({data.length} 项)</h2>
        <p>已选择: {selectedIds.size} 项</p>
      </div>
      
      <VirtualizedList
        items={data}
        renderItem={renderItem}
        itemHeight={120}
        containerHeight={600}
        overscan={3}
      />
    </div>
  ));
};

export { 
  VirtualizedList, 
  OptimizedItem, 
  LazyRenderContainer, 
  usePerformanceMonitor,
  useBatchedUpdates,
  HighPerformanceList 
};`,
    tags: ['性能优化', '虚拟化'],
    relatedQuestions: ['React.memo的使用技巧', '如何分析React应用的性能瓶颈']
  }
];

// ReactNode常见问题内容已完成
export default commonQuestions;