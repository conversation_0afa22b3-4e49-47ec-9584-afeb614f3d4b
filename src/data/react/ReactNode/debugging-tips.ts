import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'ReactNode在开发过程中可能遇到的常见问题及其解决方案。这些问题通常涉及类型错误、渲染异常、性能问题等，掌握这些调试技巧可以快速定位和解决问题。',
        sections: [
          {
            title: 'ReactNode类型错误',
            description: '当传递给组件的children或返回值不符合ReactNode类型要求时产生的错误',
            items: [
              {
                title: '"Objects are not valid as a React child"错误',
                description: '最常见的ReactNode错误，通常是因为直接渲染了对象而不是有效的React元素',
                solution: '检查是否直接渲染了对象，使用JSON.stringify()或正确提取对象属性进行渲染',
                prevention: '使用TypeScript严格模式，为props添加正确的类型注解，使用React.isValidElement检查',
                code: `// ❌ 错误：直接渲染对象
const BadComponent: React.FC = () => {
  const user = { name: '张三', age: 25 };
  return (
    <div>
      {user} {/* 错误：不能直接渲染对象 */}
    </div>
  );
};

// ✅ 正确：渲染对象的属性
const GoodComponent: React.FC = () => {
  const user = { name: '张三', age: 25 };
  return (
    <div>
      {user.name} - {user.age}岁 {/* 正确：渲染具体属性 */}
    </div>
  );
};

// ✅ 调试工具：检查ReactNode有效性
const debugReactNode = (node: ReactNode): void => {
  console.log('ReactNode类型:', typeof node);
  console.log('是否为React元素:', React.isValidElement(node));
  
  if (typeof node === 'object' && node !== null && !React.isValidElement(node) && !Array.isArray(node)) {
    console.error('发现无效对象，可能导致渲染错误:', node);
    console.log('建议：使用 JSON.stringify() 或提取对象属性');
  }
};`
              },
              {
                title: 'ReactNode条件渲染问题',
                description: '条件渲染时ReactNode类型不一致或逻辑错误导致的渲染异常',
                solution: '确保条件渲染的所有分支都返回有效的ReactNode，使用null作为空渲染的默认值',
                prevention: '使用明确的条件判断，避免使用可能为undefined的变量直接进行条件渲染',
                code: `// ❌ 错误：可能返回undefined
const BadConditionalRender: React.FC<{ user?: User }> = ({ user }) => {
  return (
    <div>
      {user && user.name} {/* 如果user.name为空字符串，会显示false */}
    </div>
  );
};

// ✅ 正确：明确的条件判断
const GoodConditionalRender: React.FC<{ user?: User }> = ({ user }) => {
  return (
    <div>
      {user?.name ? user.name : '未知用户'} {/* 明确处理空值情况 */}
    </div>
  );
};

// ✅ 调试工具：条件渲染检查器
const ConditionalRenderChecker: React.FC<{
  condition: any;
  children: ReactNode;
  fallback?: ReactNode;
}> = ({ condition, children, fallback = null }) => {
  // 开发环境下的调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log('条件渲染调试:', {
      condition,
      conditionType: typeof condition,
      willRender: Boolean(condition),
      hasChildren: children !== null && children !== undefined
    });
  }
  
  return condition ? <>{children}</> : <>{fallback}</>;
};`
              },
              {
                title: 'ReactNode数组渲染key警告',
                description: '渲染ReactNode数组时缺少key属性导致的React警告和性能问题',
                solution: '为数组中的每个元素添加唯一且稳定的key属性，避免使用数组索引',
                prevention: '建立key属性的最佳实践规范，使用数据的唯一标识符作为key',
                code: `// ❌ 错误：缺少key或使用不稳定的key
const BadArrayRender: React.FC<{ items: string[] }> = ({ items }) => {
  return (
    <div>
      {items.map((item, index) => (
        <div key={index}> {/* 问题：使用数组索引作为key */}
          {item}
        </div>
      ))}
    </div>
  );
};

// ✅ 正确：使用稳定的key
const GoodArrayRender: React.FC<{ items: Array<{id: string, text: string}> }> = ({ items }) => {
  return (
    <div>
      {items.map(item => (
        <div key={item.id}> {/* 正确：使用唯一标识符 */}
          {item.text}
        </div>
      ))}
    </div>
  );
};

// ✅ 调试工具：key重复检测器
const detectDuplicateKeys = (nodes: ReactNode[]): void => {
  const keys = new Set<string>();
  const duplicates = new Set<string>();
  
  nodes.forEach(node => {
    if (React.isValidElement(node) && node.key) {
      const key = String(node.key);
      if (keys.has(key)) {
        duplicates.add(key);
      } else {
        keys.add(key);
      }
    }
  });
  
  if (duplicates.size > 0) {
    console.warn('发现重复的key:', Array.from(duplicates));
  }
  
  return {
    totalKeys: keys.size,
    duplicateKeys: Array.from(duplicates),
    hasIssues: duplicates.size > 0
  };
};`
              }
            ]
          },
          {
            title: 'ReactNode性能问题',
            description: '大量ReactNode渲染导致的性能问题识别和优化方案',
            items: [
              {
                title: '大量ReactNode渲染缓慢',
                description: '渲染大量ReactNode时出现卡顿、延迟或浏览器无响应',
                solution: '使用虚拟化技术、React.memo缓存、代码分割等优化策略',
                prevention: '建立性能监控机制，设置渲染数量阈值，实施懒加载策略',
                code: `// 性能问题诊断工具
const PerformanceDiagnostics = {
  // 测量ReactNode渲染性能
  measureRenderPerformance: (
    name: string, 
    renderFn: () => ReactNode
  ): ReactNode => {
    const start = performance.now();
    const result = renderFn();
    const duration = performance.now() - start;
    
    if (duration > 16) { // 超过一帧时间
      console.warn(\`⚠️ 性能警告: \${name} 渲染耗时 \${duration.toFixed(2)}ms\`);
    }
    
    return result;
  },
  
  // 分析ReactNode结构复杂度
  analyzeComplexity: (node: ReactNode): PerformanceAnalysis => {
    let elementCount = 0;
    let maxDepth = 0;
    let hasKeys = true;
    
    const traverse = (n: ReactNode, depth: number = 0): void => {
      maxDepth = Math.max(maxDepth, depth);
      
      if (React.isValidElement(n)) {
        elementCount++;
        if (!n.key && depth > 0) hasKeys = false;
        
        React.Children.forEach(n.props.children, child => 
          traverse(child, depth + 1)
        );
      }
      
      if (Array.isArray(n)) {
        n.forEach(item => traverse(item, depth));
      }
    };
    
    traverse(node);
    
    const analysis = {
      elementCount,
      maxDepth,
      hasKeys,
      complexity: elementCount * maxDepth,
      recommendations: []
    };
    
    if (elementCount > 100) {
      analysis.recommendations.push('考虑使用虚拟化减少同时渲染的元素数量');
    }
    
    if (maxDepth > 10) {
      analysis.recommendations.push('组件嵌套过深，考虑组件拆分');
    }
    
    if (!hasKeys) {
      analysis.recommendations.push('为数组元素添加key属性提升性能');
    }
    
    return analysis;
  }
};

// 使用示例
const DiagnosticComponent: React.FC<{ nodes: ReactNode[] }> = ({ nodes }) => {
  const renderWithDiagnostics = () => {
    return PerformanceDiagnostics.measureRenderPerformance(
      'NodeList',
      () => (
        <div>
          {nodes.map((node, index) => (
            <div key={index}>{node}</div>
          ))}
        </div>
      )
    );
  };
  
  // 性能分析
  React.useEffect(() => {
    const analysis = PerformanceDiagnostics.analyzeComplexity(nodes);
    console.log('ReactNode性能分析:', analysis);
  }, [nodes]);
  
  return <div>{renderWithDiagnostics()}</div>;
};`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '专门用于调试ReactNode相关问题的开发工具和技巧，包括React DevTools的高级用法、TypeScript配置优化、自定义调试工具等。',
        sections: [
          {
            title: 'React DevTools高级调试',
            description: '使用React DevTools深入分析ReactNode的渲染过程和性能瓶颈',
            items: [
              {
                title: 'React DevTools Components面板调试',
                description: '使用Components面板检查ReactNode的结构、props传递和组件状态',
                solution: '通过Components面板的搜索、筛选、props编辑功能进行精确调试',
                prevention: '定期检查组件结构，避免不必要的prop drilling和组件嵌套',
                code: `// React DevTools调试增强器
const DevToolsEnhancer = {
  // 为组件添加调试标识
  addDebugProps: <P extends {}>(Component: React.ComponentType<P>) => {
    const EnhancedComponent: React.FC<P> = (props) => {
      // 开发环境下添加调试信息
      if (process.env.NODE_ENV === 'development') {
        (Component as any).displayName = Component.name || 'Anonymous';
        
        // 添加调试属性
        const debugProps = {
          ...props,
          '__debug_props': JSON.stringify(props, null, 2),
          '__debug_render_time': new Date().toISOString()
        };
        
        return <Component {...debugProps as P} />;
      }
      
      return <Component {...props} />;
    };
    
    EnhancedComponent.displayName = \`DebugEnhanced(\${Component.displayName || Component.name})\`;
    return EnhancedComponent;
  },
  
  // ReactNode渲染追踪
  traceRender: (name: string, children: ReactNode): ReactNode => {
    if (process.env.NODE_ENV === 'development') {
      console.group(\`🔍 ReactNode渲染追踪: \${name}\`);
      console.log('Children类型:', typeof children);
      console.log('Children内容:', children);
      
      if (React.isValidElement(children)) {
        console.log('元素类型:', children.type);
        console.log('元素Props:', children.props);
      }
      
      console.groupEnd();
    }
    
    return children;
  }
};

// 使用示例
const DebuggedComponent = DevToolsEnhancer.addDebugProps<{
  title: string;
  children: ReactNode;
}>(({ title, children }) => {
  return (
    <div>
      <h1>{title}</h1>
      {DevToolsEnhancer.traceRender('ComponentChildren', children)}
    </div>
  );
});`
              },
              {
                title: 'React DevTools Profiler性能分析',
                description: '使用Profiler面板分析ReactNode渲染性能，识别性能瓶颈',
                solution: '通过火焰图、排名图、提交图分析组件渲染时间和频率',
                prevention: '建立性能基准，定期进行性能回归测试',
                code: `// Profiler集成调试工具
import { Profiler, ProfilerOnRenderCallback } from 'react';

const ReactNodeProfiler: React.FC<{
  id: string;
  children: ReactNode;
  onRender?: ProfilerOnRenderCallback;
}> = ({ id, children, onRender }) => {
  const defaultOnRender: ProfilerOnRenderCallback = (
    profileId,
    phase,
    actualDuration,
    baseDuration,
    startTime,
    commitTime
  ) => {
    const performanceData = {
      组件ID: profileId,
      渲染阶段: phase,
      实际耗时: \`\${actualDuration.toFixed(2)}ms\`,
      基准耗时: \`\${baseDuration.toFixed(2)}ms\`,
      开始时间: startTime,
      提交时间: commitTime,
      性能比: \`\${((actualDuration / baseDuration) * 100).toFixed(1)}%\`
    };
    
    console.table(performanceData);
    
    // 性能预警
    if (actualDuration > 16) {
      console.warn(\`⚠️ 性能警告: \${profileId} 渲染超时\`, performanceData);
    }
    
    // 调用自定义回调
    onRender?.(profileId, phase, actualDuration, baseDuration, startTime, commitTime);
  };
  
  return (
    <Profiler id={id} onRender={onRender || defaultOnRender}>
      {children}
    </Profiler>
  );
};

// 批量性能监控
const BatchProfiler: React.FC<{
  components: Array<{
    id: string;
    component: ReactNode;
  }>;
}> = ({ components }) => {
  const [performanceData, setPerformanceData] = React.useState<
    Record<string, PerformanceEntry[]>
  >({});
  
  const handleRender: ProfilerOnRenderCallback = (id, phase, actualDuration) => {
    setPerformanceData(prev => ({
      ...prev,
      [id]: [
        ...(prev[id] || []),
        {
          phase,
          duration: actualDuration,
          timestamp: Date.now()
        }
      ].slice(-10) // 只保留最近10次记录
    }));
  };
  
  return (
    <div>
      {components.map(({ id, component }) => (
        <ReactNodeProfiler key={id} id={id} onRender={handleRender}>
          {component}
        </ReactNodeProfiler>
      ))}
      
      {/* 性能数据展示 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="performance-dashboard">
          <h3>性能监控面板</h3>
          {Object.entries(performanceData).map(([id, entries]) => (
            <div key={id}>
              <h4>{id}</h4>
              <p>平均耗时: {(entries.reduce((sum, entry) => sum + entry.duration, 0) / entries.length).toFixed(2)}ms</p>
              <p>最近渲染: {entries[entries.length - 1]?.duration.toFixed(2)}ms</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};`
              },
              {
                title: 'TypeScript调试配置',
                description: 'TypeScript编译器和IDE配置优化，提升ReactNode类型检查和调试体验',
                items: [
                  {
                    title: 'TypeScript严格模式配置',
                    description: '配置TypeScript严格模式以提前发现ReactNode类型问题',
                    solution: '启用strict模式、noImplicitAny、strictNullChecks等配置项',
                    prevention: '建立TypeScript代码规范，使用ESLint配合TypeScript检查',
                    code: `// tsconfig.json 优化配置
{
  "compilerOptions": {
    "strict": true,                    // 启用所有严格检查
    "noImplicitAny": true,            // 禁止隐式any类型
    "strictNullChecks": true,         // 严格空值检查
    "noImplicitReturns": true,        // 检查函数返回值
    "noUnusedLocals": true,           // 检查未使用的局部变量
    "noUnusedParameters": true,       // 检查未使用的参数
    "exactOptionalPropertyTypes": true, // 精确可选属性类型
    
    // React相关配置
    "jsx": "react-jsx",               // 使用新的JSX转换
    "lib": ["dom", "dom.iterable", "es6"], // 包含必要的类型库
    
    // 调试相关
    "sourceMap": true,                // 生成source map
    "declaration": true,              // 生成类型声明文件
    "declarationMap": true            // 生成声明文件的source map
  },
  
  "include": [
    "src/**/*"
  ],
  
  "exclude": [
    "node_modules",
    "build",
    "dist"
  ]
}

// .eslintrc.js React + TypeScript配置
module.exports = {
  extends: [
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-  ],
  
  rules: {
    // ReactNode相关规则
    'react/prop-types': 'off', // TypeScript已提供类型检查
    'react/react-in-jsx-scope': 'off', // React 17+不需要导入React
    '@typescript-eslint/no-explicit-any': 'warn', // 警告any类型使用
    
    // 自定义ReactNode检查规则
    'no-restricted-syntax': [
      'error',
      {
        selector: 'TSTypeReference[typeName.name="ReactNode"] Property[key.name="children"][value.type="TSAnyKeyword"]',
        message: '避免在ReactNode children中使用any类型'
      }
    ]
  }
};`
                  },
                  {
                    title: '自定义ReactNode类型检查工具',
                    description: '开发自定义的TypeScript检查工具和VSCode插件配置',
                    solution: '创建类型守卫函数、自定义Hook、开发时类型检查工具',
                    prevention: '建立自动化类型检查流程，集成到CI/CD管道中',
                    code: `// 自定义ReactNode类型守卫和检查工具
export const ReactNodeTypeGuards = {
  // 检查是否为有效的ReactNode
  isValidReactNode: (value: unknown): value is ReactNode => {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') return true;
    if (React.isValidElement(value)) return true;
    if (Array.isArray(value)) {
      return value.every(item => ReactNodeTypeGuards.isValidReactNode(item));
    }
    return false;
  },
  
  // 检查是否为ReactElement
  isReactElement: (value: ReactNode): value is ReactElement => {
    return React.isValidElement(value);
  },
  
  // 检查是否为文本节点
  isTextNode: (value: ReactNode): value is string | number => {
    return typeof value === 'string' || typeof value === 'number';
  },
  
  // 检查是否为空节点
  isEmptyNode: (value: ReactNode): value is null | undefined | false => {
    return value === null || value === undefined || value === false;
  }
};

// 开发时ReactNode验证Hook
export const useReactNodeValidation = (
  node: ReactNode,
  name: string = 'unnamed'
): ValidationResult => {
  const [validationResult, setValidationResult] = React.useState<ValidationResult>({
    isValid: true,
    errors: [],
    warnings: []
  });
  
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const errors: string[] = [];
      const warnings: string[] = [];
      
      if (!ReactNodeTypeGuards.isValidReactNode(node)) {
        errors.push(\`\${name}: 包含无效的ReactNode类型\`);
      }
      
      // 检查对象类型（常见错误）
      if (typeof node === 'object' && 
          node !== null && 
          !React.isValidElement(node) && 
          !Array.isArray(node)) {
        errors.push(\`\${name}: 尝试渲染普通对象，使用JSON.stringify()或提取属性\`);
      }
      
      // 检查数组key
      if (Array.isArray(node)) {
        const hasElementsWithoutKeys = node.some(item => 
          React.isValidElement(item) && !item.key
        );
        
        if (hasElementsWithoutKeys) {
          warnings.push(\`\${name}: 数组中的React元素缺少key属性\`);
        }
      }
      
      const result = {
        isValid: errors.length === 0,
        errors,
        warnings
      };
      
      setValidationResult(result);
      
      // 控制台输出验证结果
      if (errors.length > 0) {
        console.error(\`ReactNode验证失败 (\${name}):\`, errors);
      }
      
      if (warnings.length > 0) {
        console.warn(\`ReactNode验证警告 (\${name}):\`, warnings);
      }
    }
  }, [node, name]);
  
  return validationResult;
};

// VSCode 设置建议 (settings.json)
const vscodeSettings = {
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.preferences.includeCompletionsForModuleExports": true,
  
  // React相关设置
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  },
  
  // 类型检查设置
  "typescript.validate.enable": true,
  "typescript.check.npmIsInstalled": true,
  
  // 调试相关设置
  "debug.showBreakpointsInOverviewRuler": true,
  "debug.inlineValues": true
};`
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  ]
};

// ReactNode调试技巧内容已完成
export default debuggingTips;