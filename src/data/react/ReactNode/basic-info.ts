import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: "ReactNode是React中用于表示所有可渲染内容的TypeScript联合类型，涵盖了React组件可以返回或接受的所有有效内容类型",
  
  introduction: `ReactNode是React TypeScript生态系统中最基础和最重要的类型之一，定义了所有可以被React渲染的内容。它是一个联合类型，包含ReactElement、字符串、数字、布尔值、null、undefined、数组等所有React能够处理的数据类型。

作为React组件children属性的标准类型，ReactNode为开发者提供了最大的灵活性，允许组件接受任何有效的React内容。它不仅简化了组件API设计，还确保了类型安全性，让开发者在编译期就能发现潜在的类型错误。

理解ReactNode对于掌握React TypeScript开发至关重要，它是构建灵活、可复用组件的基础，也是React声明式编程范式的重要体现。`,

  syntax: `// ReactNode类型定义
type ReactNode = 
  | ReactElement 
  | string 
  | number 
  | boolean 
  | null 
  | undefined 
  | ReactFragment 
  | ReactPortal 
  | ReactNode[];

// 在组件中使用
interface ComponentProps {
  children: ReactNode;
}

// 函数组件返回类型
const MyComponent: React.FC = (): ReactNode => {
  return <div>Hello World</div>;
};

// 条件渲染
function conditionalRender(condition: boolean): ReactNode {
  return condition ? <div>显示内容</div> : null;
}`,

  quickExample: `function ReactNodeExample() {
  // 演示ReactNode的各种使用方式
  const textNode: ReactNode = "纯文本内容";
  const numberNode: ReactNode = 42;
  const elementNode: ReactNode = <span>JSX元素</span>;
  const arrayNode: ReactNode = [
    <div key="1">数组项1</div>,
    <div key="2">数组项2</div>
  ];

  return (
    <div>
      {/* ReactNode可以接受任何可渲染内容 */}
      <Container>
        {textNode}
        {numberNode}
        {elementNode}
        {arrayNode}
        {null} {/* null和undefined都是有效的ReactNode */}
        {true && <div>条件渲染</div>}
      </Container>
    </div>
  );
}

// 灵活的容器组件
interface ContainerProps {
  children: ReactNode; // 接受任何可渲染内容
  className?: string;
}

const Container: React.FC<ContainerProps> = ({ children, className }) => {
  return (
    <div className={className || 'container'}>
      {children}
    </div>
  );
};`,

  scenarioDiagram: `graph TD
    A[ReactNode应用场景] --> B[组件children属性]
    A --> C[条件渲染内容]
    A --> D[动态内容渲染]

    B --> B1[文本和数字内容]
    B --> B2[JSX元素和组件]
    B --> B3[数组和Fragment]

    C --> C1[null/undefined处理]
    C --> C2[布尔值条件渲染]
    C --> C3[可选内容显示]

    D --> D1[列表渲染]
    D --> D2[模板内容生成]
    D --> D3[组合式内容构建]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "children",
      type: "ReactNode",
      required: false,
      description: "组件的子内容，可以是任何可渲染的React内容类型",
      example: "\"文本\" | 123 | <div>元素</div> | [<span key=\"1\">数组</span>] | null"
    },
    {
      name: "content",
      type: "ReactNode",
      required: false,
      description: "用于表示可变内容的通用参数",
      example: "{condition ? <Component /> : \"加载中...\"}"
    },
    {
      name: "fallback",
      type: "ReactNode",
      required: false,
      description: "当主要内容不可用时显示的备用内容",
      example: "<div>暂无数据</div> | \"加载失败\" | null"
    }
  ],
  
  returnValue: {
    type: "ReactNode",
    description: "函数组件或render方法返回的可渲染内容，可以是任何有效的ReactNode类型",
    example: "<div>Hello</div> | \"文本\" | 42 | null | [<span key=\"1\">item</span>]"
  },
  
  keyFeatures: [
    {
      title: "类型包容性",
      description: "包含所有React可以渲染的内容类型，提供最大的灵活性",
      benefit: "简化组件API设计，避免复杂的类型约束，提高开发效率"
    },
    {
      title: "null安全性",
      description: "明确支持null和undefined，符合React的条件渲染机制",
      benefit: "避免运行时错误，支持安全的条件渲染和可选内容显示"
    },
    {
      title: "数组支持",
      description: "原生支持ReactNode数组，便于列表渲染和内容组合",
      benefit: "简化列表渲染逻辑，支持动态内容生成和模板化开发"
    },
    {
      title: "递归结构",
      description: "支持嵌套的ReactNode数组，可以构建复杂的内容层次",
      benefit: "实现灵活的组件组合模式，支持深层嵌套和复杂布局"
    }
  ],
  
  limitations: [
    "不能直接渲染复杂对象（Object），需要转换为字符串或使用专门的渲染组件",
    "函数类型不是有效的ReactNode，需要调用函数获取返回值后再渲染",
    "ReactNode类型过于宽泛，在某些场景下可能需要更具体的类型约束",
    "数组类型的ReactNode需要正确的key属性以避免渲染警告",
    "某些TypeScript版本中，复杂的嵌套ReactNode类型推断可能不够准确"
  ],
  
  bestPractices: [
    "优先使用ReactNode作为children属性的类型，提供最大的组件使用灵活性",
    "在需要特定内容类型时，使用更具体的类型如ReactElement而不是ReactNode",
    "使用React.Children工具函数来安全地操作ReactNode内容",
    "为数组形式的ReactNode提供稳定且唯一的key属性",
    "使用条件渲染时合理利用ReactNode对null和undefined的支持",
    "在处理用户输入的动态内容时，注意XSS安全防护",
    "使用TypeScript的类型守卫来在运行时验证ReactNode的具体类型"
  ],
  
  warnings: [
    "不要尝试直接渲染复杂对象，这会导致React抛出错误",
    "避免在ReactNode中包含循环引用，可能导致渲染死循环",
    "注意ReactNode数组的性能影响，大量元素时考虑虚拟化技术"
  ]
};

// ReactNode基本信息内容已完成
export default basicInfo;