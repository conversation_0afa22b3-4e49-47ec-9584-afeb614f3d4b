import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useCallbackApi: ApiItem = {
  id: 'use-callback',
  title: 'useCallback()',
  description: '缓存函数引用，避免子组件不必要的重新渲染',
  category: 'React Hooks',
  difficulty: 'medium',
  syntax: 'const memoizedCallback = useCallback(callback, dependencies)',
  example: `import React, { useState, useCallback } from 'react';

function ParentComponent() {
  const [count, setCount] = useState(0);
  const [text, setText] = useState('');

  // 缓存回调函数，只在count改变时重新创建
  const handleIncrement = useCallback(() => {
    setCount(c => c + 1);
  }, []);

  // 这个函数在每次渲染时都会重新创建
  const handleTextChange = (e) => {
    setText(e.target.value);
  };

  return (
    <div>
      <input value={text} onChange={handleTextChange} />
      <ExpensiveChild onIncrement={handleIncrement} />
      <p>Count: {count}</p>
    </div>
  );
}

// 使用React.memo避免不必要的重新渲染
const ExpensiveChild = React.memo(({ onIncrement }) => {
  console.log('ExpensiveChild rendered');
  return <button onClick={onIncrement}>Increment</button>;
});`,
  notes: '注意：过度使用useCallback可能适得其反，只在确实需要优化时使用',
  isNew: false,
  version: 'React 16.8+',
  tags: ['性能优化', '函数缓存', 'Hook'],
  
  // 扩展内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useCallbackApi; 