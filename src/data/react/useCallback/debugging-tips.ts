import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  commonErrors: [
    {
      error: "闭包陷阱：函数中使用了过期的状态值",
      cause: "依赖数组不完整，导致函数捕获了旧的变量值",
      solution: "确保依赖数组包含所有使用的外部变量，或使用函数式更新",
      code: `// ❌ 错误示例：闭包陷阱
function Counter() {
  const [count, setCount] = useState(0);
  
  // 缺少count依赖，函数内的count永远是0
  const increment = useCallback(() => {
    console.log('Current count:', count); // 永远输出0
    setCount(count + 1); // 永远设置为1
  }, []); // 缺少count依赖！
  
  useEffect(() => {
    const timer = setInterval(increment, 1000);
    return () => clearInterval(timer);
  }, [increment]);
  
  return <div>Count: {count}</div>;
}

// ✅ 解决方案1：添加正确的依赖
const increment1 = useCallback(() => {
  console.log('Current count:', count);
  setCount(count + 1);
}, [count]); // 添加count依赖

// ✅ 解决方案2：使用函数式更新（推荐）
const increment2 = useCallback(() => {
  setCount(prevCount => {
    console.log('Current count:', prevCount);
    return prevCount + 1;
  });
}, []); // 不需要依赖count

// ✅ 解决方案3：使用useRef保存最新值
function CounterWithRef() {
  const [count, setCount] = useState(0);
  const countRef = useRef(count);
  
  useEffect(() => {
    countRef.current = count;
  }, [count]);
  
  const increment = useCallback(() => {
    console.log('Current count:', countRef.current);
    setCount(countRef.current + 1);
  }, []);
}`
    },
    {
      error: "过度使用useCallback导致性能下降",
      cause: "在不需要的地方使用useCallback，增加了额外开销",
      solution: "只在真正需要稳定引用的地方使用",
      code: `// ❌ 过度使用
function OverOptimized() {
  const [text, setText] = useState('');
  
  // 不必要的useCallback
  const handleChange = useCallback((e) => {
    setText(e.target.value);
  }, []); // 没有传给子组件，没必要缓存
  
  // 简单函数不需要缓存
  const simpleLog = useCallback(() => {
    console.log('clicked');
  }, []);
  
  return (
    <div>
      <input onChange={handleChange} />
      <button onClick={simpleLog}>Log</button>
    </div>
  );
}

// ✅ 合理使用
function ProperlyOptimized() {
  const [text, setText] = useState('');
  const [items, setItems] = useState([]);
  
  // 直接使用内联函数
  const handleChange = (e) => {
    setText(e.target.value);
  };
  
  // 传给memo组件的函数需要缓存
  const handleItemClick = useCallback((id) => {
    console.log('Item clicked:', id);
  }, []);
  
  return (
    <div>
      <input onChange={handleChange} />
      <ItemList items={items} onItemClick={handleItemClick} />
    </div>
  );
}`
    },
    {
      error: "依赖项对象/数组导致useCallback失效",
      cause: "依赖项是每次渲染都创建的新对象或数组",
      solution: "使用稳定的依赖项或分解为基本类型",
      code: `// ❌ 问题：依赖项总是新的
function BadDependencies() {
  const [items, setItems] = useState([]);
  
  // config每次渲染都是新对象
  const config = { sortBy: 'name', order: 'asc' };
  
  // 这个callback每次都会重新创建
  const sortItems = useCallback(() => {
    return items.sort((a, b) => {
      if (config.sortBy === 'name') {
        return config.order === 'asc' 
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      }
    });
  }, [items, config]); // config总是新的！
}

// ✅ 解决方案1：使用useState保持稳定
function GoodDependencies1() {
  const [items, setItems] = useState([]);
  const [config] = useState({ sortBy: 'name', order: 'asc' });
  
  const sortItems = useCallback(() => {
    // 现在config是稳定的
  }, [items, config]);
}

// ✅ 解决方案2：分解为基本类型
function GoodDependencies2() {
  const [items, setItems] = useState([]);
  const [sortBy, setSortBy] = useState('name');
  const [order, setOrder] = useState('asc');
  
  const sortItems = useCallback(() => {
    return items.sort((a, b) => {
      if (sortBy === 'name') {
        return order === 'asc' 
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      }
    });
  }, [items, sortBy, order]); // 基本类型依赖
}

// ✅ 解决方案3：使用useMemo稳定对象
function GoodDependencies3() {
  const [sortBy, setSortBy] = useState('name');
  const [order, setOrder] = useState('asc');
  
  const config = useMemo(() => ({
    sortBy, order
  }), [sortBy, order]);
  
  const sortItems = useCallback(() => {
    // 使用稳定的config
  }, [config]);
}`
    },
    {
      error: "在循环中错误使用useCallback",
      cause: "试图在循环或条件语句中调用useCallback",
      solution: "将useCallback移到顶层，使用参数化的回调",
      code: `// ❌ 错误：在循环中调用Hook
function BadLoop({ items }) {
  return items.map(item => {
    // 错误！不能在循环中调用Hook
    const handleClick = useCallback(() => {
      console.log(item.id);
    }, [item.id]);
    
    return <Item key={item.id} onClick={handleClick} />;
  });
}

// ❌ 错误：在条件语句中调用Hook
function BadCondition({ shouldOptimize }) {
  if (shouldOptimize) {
    // 错误！不能在条件语句中调用Hook
    const handleClick = useCallback(() => {
      console.log('optimized');
    }, []);
  }
}

// ✅ 正确：在顶层调用，使用参数
function GoodPattern({ items }) {
  // 在顶层创建通用的回调
  const handleItemClick = useCallback((itemId) => {
    console.log('Item clicked:', itemId);
  }, []);
  
  return items.map(item => (
    <Item 
      key={item.id} 
      onClick={() => handleItemClick(item.id)}
    />
  ));
}

// ✅ 或者将逻辑提取到子组件
function ItemWrapper({ item }) {
  const handleClick = useCallback(() => {
    console.log('Item clicked:', item.id);
  }, [item.id]);
  
  return <Item onClick={handleClick} {...item} />;
}

function GoodList({ items }) {
  return items.map(item => (
    <ItemWrapper key={item.id} item={item} />
  ));
}`
    }
  ],
  
  devToolsTips: [
    {
      tool: "React DevTools Profiler",
      technique: "分析useCallback的效果",
      example: `// 1. 启用Profiler记录
import { Profiler } from 'react';

function ProfiledComponent() {
  const [count, setCount] = useState(0);
  
  // 测试有无useCallback的差异
  const callbackWithMemo = useCallback(() => {
    console.log('memoized');
  }, []);
  
  const callbackWithoutMemo = () => {
    console.log('not memoized');
  };
  
  return (
    <Profiler
      id="callback-test"
      onRender={(id, phase, actualDuration) => {
        console.log(id + " (" + phase + ") took " + actualDuration + "ms");
      }}
    >
      <MemoChild name="with-callback" onClick={callbackWithMemo} />
      <MemoChild name="without-callback" onClick={callbackWithoutMemo} />
      <button onClick={() => setCount(count + 1)}>
        Re-render ({count})
      </button>
    </Profiler>
  );
}

// 2. 使用DevTools查看：
// - 打开React DevTools
// - 切换到Profiler标签
// - 点击录制按钮
// - 触发重渲染
// - 查看哪些组件重新渲染了`
    },
    {
      tool: "自定义调试Hook",
      technique: "创建调试工具来追踪useCallback行为",
      example: `// useCallbackDebug - 追踪函数引用变化
function useCallbackDebug(callback, deps, debugName) {
  const renderCount = useRef(0);
  const previousCallback = useRef();
  const previousDeps = useRef();
  
  renderCount.current++;
  
  // 检查函数引用是否变化
  if (previousCallback.current !== callback) {
    console.log(
      "[" + debugName + "] Callback reference changed at render #" + renderCount.current
    );
  }
  
  // 检查哪些依赖项变化了
  if (previousDeps.current && deps) {
    const changedDeps = deps.reduce((acc, dep, index) => {
      if (!Object.is(dep, previousDeps.current[index])) {
        acc.push({
          index,
          from: previousDeps.current[index],
          to: dep
        });
      }
      return acc;
    }, []);
    
    if (changedDeps.length > 0) {
      console.log("[" + debugName + "] Dependencies changed:", changedDeps);
    }
  }
  
  previousCallback.current = callback;
  previousDeps.current = deps;
  
  return useCallback(callback, deps);
}

// 使用示例
function MyComponent({ userId }) {
  const [data, setData] = useState(null);
  
  const fetchData = useCallbackDebug(
    async () => {
      const response = await api.getUser(userId);
      setData(response);
    },
    [userId],
    'fetchData'
  );
  
  useEffect(() => {
    fetchData();
  }, [fetchData]);
}`
    },
    {
      tool: "性能监控",
      technique: "测量useCallback的实际影响",
      example: `// 创建性能监控组件
function PerformanceMonitor({ children }) {
  const measurements = useRef({
    renders: 0,
    totalDuration: 0,
    callbacks: new Map()
  });
  
  const logPerformance = useCallback(() => {
    const { renders, totalDuration, callbacks } = measurements.current;
    console.group('Performance Summary');
    console.log('Total renders:', renders);
    console.log('Average render time:', (totalDuration / renders).toFixed(2), 'ms');
    console.log('Callback stability:');
    callbacks.forEach((info, name) => {
      console.log("  " + name + ": " + info.changes + " changes in " + renders + " renders");
    });
    console.groupEnd();
  }, []);
  
  useEffect(() => {
    // 定期输出性能报告
    const interval = setInterval(logPerformance, 5000);
    return () => clearInterval(interval);
  }, [logPerformance]);
  
  return (
    <PerformanceContext.Provider value={measurements}>
      {children}
    </PerformanceContext.Provider>
  );
}

// 追踪特定callback的Hook
function useTrackedCallback(callback, deps, name) {
  const measurements = useContext(PerformanceContext);
  const previousCallback = useRef(callback);
  
  useEffect(() => {
    if (!measurements.current.callbacks.has(name)) {
      measurements.current.callbacks.set(name, { changes: 0 });
    }
    
    if (previousCallback.current !== callback) {
      measurements.current.callbacks.get(name).changes++;
      previousCallback.current = callback;
    }
  });
  
  return useCallback(callback, deps);
}`
    }
  ],
  
  troubleshooting: [
    {
      symptom: "组件仍然频繁重渲染",
      possibleCauses: [
        "子组件没有使用React.memo",
        "useCallback的依赖项频繁变化",
        "父组件的状态更新导致整体重渲染"
      ],
      solutions: [
        "确保接收callback的子组件使用了React.memo",
        "检查并优化依赖项，使用更稳定的值",
        "将状态提升或下沉到合适的位置"
      ]
    },
    {
      symptom: "函数行为不符合预期",
      possibleCauses: [
        "闭包陷阱导致使用了旧值",
        "依赖项数组不完整",
        "异步操作中的竞态条件"
      ],
      solutions: [
        "使用函数式更新或useRef保存最新值",
        "确保依赖数组包含所有外部变量",
        "使用取消机制处理异步操作"
      ]
    },
    {
      symptom: "内存使用持续增长",
      possibleCauses: [
        "缓存了大量不再使用的函数",
        "闭包中捕获了大对象",
        "事件监听器没有正确清理"
      ],
      solutions: [
        "只在必要时使用useCallback",
        "避免在闭包中捕获大对象",
        "确保在cleanup函数中移除事件监听"
      ]
    }
  ]
};

export default debuggingTips; 