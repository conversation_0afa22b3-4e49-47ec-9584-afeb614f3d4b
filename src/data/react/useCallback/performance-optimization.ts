import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: "识别真正的性能瓶颈",
      description: "使用工具分析，而不是盲目优化",
      techniques: [
        {
          name: "React DevTools Profiler",
          description: "使用React专用的性能分析工具",
          code: `// 包装组件以收集性能数据
function ProfiledComponent() {
  return (
    <Profiler
      id="expensive-component"
      onRender={(id, phase, actualDuration) => {
        console.log('[性能监控]', {
          component: id,
          phase,
          渲染时间: actualDuration.toFixed(2) + 'ms'
        });
      }}
    >
      <ExpensiveComponent />
    </Profiler>
  );
}`,
          impact: 'high',
          difficulty: 'easy'
        },
        {
          name: "自定义性能监控Hook",
          description: "创建专门的性能监控组件",
          code: `const useRenderTracking = (componentName) => {
  const renderCount = useRef(0);
  const renderTimes = useRef([]);
  
  useEffect(() => {
    renderCount.current++;
    const renderTime = performance.now();
    renderTimes.current.push(renderTime);
    
    // 计算平均渲染间隔
    if (renderTimes.current.length > 1) {
      const intervals = [];
      for (let i = 1; i < renderTimes.current.length; i++) {
        intervals.push(renderTimes.current[i] - renderTimes.current[i-1]);
      }
      const avgInterval = intervals.reduce((a, b) => a + b) / intervals.length;
      
      console.log(\`[\${componentName}] 第\${renderCount.current}次渲染, 平均间隔: \${avgInterval.toFixed(2)}ms\`);
    }
  });
  
  return renderCount.current;
};`,
          impact: 'medium',
          difficulty: 'medium'
        }
      ]
    },
    {
      title: "优化列表渲染中的回调",
      description: "在大型列表中正确使用useCallback",
      techniques: [
        {
          name: "单一回调函数模式",
          description: "避免为每个列表项创建独特的回调",
          code: `// ❌ 反模式：为每个项创建回调
function BadList({ items }) {
  return items.map(item => (
    <ListItem
      key={item.id}
      item={item}
      // 每个项都有独特的函数，破坏了优化
      onSelect={() => handleSelect(item.id)}
    />
  ));
}

// ✅ 优化方案：单一回调函数
function OptimizedList({ items }) {
  const [selectedIds, setSelectedIds] = useState(new Set());
  
  const handleSelect = useCallback((id) => {
    setSelectedIds(prev => {
      const next = new Set(prev);
      next.has(id) ? next.delete(id) : next.add(id);
      return next;
    });
  }, []); // 无依赖，永远稳定
  
  return items.map(item => (
    <MemoListItem
      key={item.id}
      item={item}
      isSelected={selectedIds.has(item.id)}
      onSelect={handleSelect}
    />
  ));
}`,
          impact: 'high',
          difficulty: 'medium'
        },
        {
          name: "Context优化模式",
          description: "使用Context避免prop drilling",
          code: `const ListContext = createContext();

function OptimizedList({ items }) {
  const [selectedIds, setSelectedIds] = useState(new Set());
  
  const contextValue = useMemo(() => ({
    selectedIds,
    toggleSelect: (id) => {
      setSelectedIds(prev => {
        const next = new Set(prev);
        next.has(id) ? next.delete(id) : next.add(id);
        return next;
      });
    }
  }), [selectedIds]);
  
  return (
    <ListContext.Provider value={contextValue}>
      {items.map(item => <ListItem key={item.id} item={item} />)}
    </ListContext.Provider>
  );
}

const ListItem = memo(({ item }) => {
  const { selectedIds, toggleSelect } = useContext(ListContext);
  
  const handleClick = useCallback(() => {
    toggleSelect(item.id);
  }, [item.id, toggleSelect]);
  
  return (
    <div 
      className={selectedIds.has(item.id) ? 'selected' : ''}
      onClick={handleClick}
    >
      {item.name}
    </div>
  );
});`,
          impact: 'high',
          difficulty: 'hard'
        }
      ]
    },
    {
      title: "结合useReducer减少依赖",
      description: "使用useReducer获得稳定的dispatch，减少useCallback依赖",
      techniques: [
        {
          name: "Reducer状态管理",
          description: "用reducer简化复杂状态逻辑",
          code: `// ❌ 多个相关状态导致复杂的依赖
function ComplexComponent() {
  const [items, setItems] = useState([]);
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  
  // 许多依赖项，容易变化
  const handleFilter = useCallback((newFilter) => {
    setFilter(newFilter);
  }, [items, sortBy]); // 依赖项多
}

// ✅ 使用useReducer简化
const searchReducer = (state, action) => {
  switch (action.type) {
    case 'SET_FILTER':
      return { ...state, filter: action.payload };
    case 'SET_SORT':
      return { ...state, sortBy: action.payload };
    case 'RESET':
      return initialState;
    default:
      return state;
  }
};

function OptimizedComponent() {
  const [state, dispatch] = useReducer(searchReducer, initialState);
  
  // dispatch是稳定的，无需依赖
  const setFilter = useCallback((filter) => {
    dispatch({ type: 'SET_FILTER', payload: filter });
  }, []);
  
  return <FilterBar onFilterChange={setFilter} />;
}`,
          impact: 'high',
          difficulty: 'medium'
        }
      ]
    },
    {
      title: "智能的依赖管理",
      description: "优化依赖项以减少函数重建",
      techniques: [
        {
          name: "细粒度依赖",
          description: "只依赖需要的属性而不是整个对象",
          code: `function DependencyOptimization() {
  const [user, setUser] = useState({ id: 1, name: 'John', role: 'admin' });
  
  // ❌ 依赖整个对象
  const badCallback = useCallback(() => {
    console.log(user.name);
  }, [user]); // 对象引用经常变化
  
  // ✅ 只依赖需要的属性
  const { name } = user;
  const goodCallback = useCallback(() => {
    console.log(name);
  }, [name]); // 更细粒度的依赖
  
  return <Component onAction={goodCallback} />;
}`,
          impact: 'medium',
          difficulty: 'easy'
        },
        {
          name: "ref存储不影响渲染的值",
          description: "使用ref存储不需要触发重渲染的数据",
          code: `function useStableCallback() {
  const analyticsRef = useRef(null);
  
  useEffect(() => {
    analyticsRef.current = new Analytics();
  }, []);
  
  // 使用ref中的值，不需要作为依赖
  const trackEvent = useCallback((eventName, data) => {
    analyticsRef.current?.track(eventName, data);
  }, []); // 空依赖数组
  
  return trackEvent;
}`,
          impact: 'medium',
          difficulty: 'medium'
        }
      ]
    }
  ],

  bestPractices: [
    "只在传递给React.memo包装的组件时使用useCallback",
    "避免在useCallback中创建新的对象或数组",
    "使用ESLint规则自动检测遗漏的依赖项",
    "优先考虑组件结构优化而非过度使用useCallback",
    "在大型列表中特别注意回调函数的优化",
    "结合useReducer减少复杂的依赖关系",
    "使用React DevTools Profiler验证优化效果",
    "不要为了优化而优化，先测量后优化"
  ],

  commonPitfalls: [
    {
      issue: "过度使用useCallback",
      cause: "认为所有函数都需要缓存",
      solution: "只在有明确性能问题时使用，或传递给memo组件时使用"
    },
    {
      issue: "依赖数组不完整",
      cause: "为了保持函数稳定而故意省略依赖",
      solution: "使用eslint-plugin-react-hooks规则检查依赖，或重构组件结构"
    },
    {
      issue: "在useCallback中创建新对象",
      cause: "不理解闭包和引用相等性",
      solution: "将对象创建移到useCallback外部，或使用useMemo缓存对象"
    },
    {
      issue: "错误的性能期望",
      cause: "认为useCallback会让函数执行更快",
      solution: "理解useCallback只是缓存函数引用，不影响函数执行性能"
    }
  ]
};

export default performanceOptimization; 