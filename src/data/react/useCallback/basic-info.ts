import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "useCallback是React提供的性能优化Hook，用于缓存函数引用。它接收一个内联回调函数和依赖项数组，只有当依赖项发生变化时才返回新的函数引用。这对于防止子组件因父组件函数引用变化而不必要的重渲染非常有用。",
  
  syntax: `const memoizedCallback = useCallback(() => {
  doSomething(a, b);
}, [a, b]);`,

  overview: {
    description: "useCallback是React提供的性能优化Hook，用于缓存函数引用。它接收一个内联回调函数和依赖项数组，只有当依赖项发生变化时才返回新的函数引用。这对于防止子组件因父组件函数引用变化而不必要的重渲染非常有用。",
    
    keyFeatures: [
      "缓存函数引用，避免重新创建",
      "只在依赖项变化时返回新函数",
      "优化子组件渲染，配合React.memo使用",
      "保持函数引用稳定性",
      "减少不必要的重渲染",
      "适用于传递给子组件的回调函数"
    ]
  },
  
  basicUsage: {
    description: "useCallback的基本用法展示如何缓存函数引用并优化性能",
    examples: [
      {
        title: "基础用法 - 缓存事件处理函数",
        description: "防止因父组件重渲染导致的函数重新创建",
        code: `import { useCallback, useState } from 'react';

function TodoList() {
  const [todos, setTodos] = useState([]);
  const [input, setInput] = useState('');
  
  // ❌ 没有使用useCallback - 每次渲染都创建新函数
  const handleAddBad = () => {
    setTodos([...todos, { id: Date.now(), text: input }]);
    setInput('');
  };
  
  // ✅ 使用useCallback - 只在依赖项变化时创建新函数
  const handleAdd = useCallback(() => {
    setTodos([...todos, { id: Date.now(), text: input }]);
    setInput('');
  }, [todos, input]); // 依赖todos和input
  
  // 更好的方式：使用函数式更新减少依赖
  const handleAddOptimized = useCallback(() => {
    setTodos(prev => [...prev, { id: Date.now(), text: input }]);
    setInput('');
  }, [input]); // 只依赖input
  
  return (
    <div>
      <input 
        value={input} 
        onChange={(e) => setInput(e.target.value)} 
      />
      <button onClick={handleAddOptimized}>Add</button>
      <TodoItems todos={todos} />
    </div>
  );
}`
      },
      {
        title: "优化子组件渲染",
        description: "配合React.memo防止不必要的子组件重渲染",
        code: `import { useCallback, useState, memo } from 'react';

// 使用memo包装的子组件
const ExpensiveChild = memo(({ onClick, data }) => {
  console.log('ExpensiveChild rendered');
  
  return (
    <div>
      <h3>Data: {data}</h3>
      <button onClick={onClick}>Click me</button>
    </div>
  );
});

function Parent() {
  const [count, setCount] = useState(0);
  const [data, setData] = useState('initial');
  
  // ❌ 没有使用useCallback
  const handleClickBad = () => {
    console.log('Button clicked');
    setData('updated');
  };
  
  // ✅ 使用useCallback缓存函数
  const handleClick = useCallback(() => {
    console.log('Button clicked');
    setData('updated');
  }, []); // 空依赖数组，函数永不改变
  
  return (
    <div>
      <button onClick={() => setCount(count + 1)}>
        Parent Count: {count}
      </button>
      {/* 
        使用handleClickBad时，每次Parent渲染都会导致ExpensiveChild重渲染
        使用handleClick时，ExpensiveChild只在data变化时重渲染
      */}
      <ExpensiveChild onClick={handleClick} data={data} />
    </div>
  );
}`
      },
      {
        title: "处理依赖项变化",
        description: "正确管理useCallback的依赖项",
        code: `import { useCallback, useState, useEffect } from 'react';

function SearchComponent({ onSearch }) {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState({ type: 'all', status: 'active' });
  
  // 依赖外部props的回调
  const handleSearch = useCallback(() => {
    onSearch(query, filters);
  }, [query, filters, onSearch]); // 包含所有依赖
  
  // 使用防抖的搜索
  const debouncedSearch = useCallback((searchTerm) => {
    const timer = setTimeout(() => {
      onSearch(searchTerm, filters);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [filters, onSearch]); // 不包含searchTerm，因为它是参数
  
  // 创建稳定的事件处理器
  const handleInputChange = useCallback((e) => {
    const value = e.target.value;
    setQuery(value);
    
    // 清理之前的定时器
    const cleanup = debouncedSearch(value);
    return cleanup;
  }, [debouncedSearch]);
  
  return (
    <div>
      <input 
        value={query}
        onChange={handleInputChange}
        placeholder="Search..."
      />
      <button onClick={handleSearch}>Search Now</button>
    </div>
  );
}`
      }
    ]
  },
  
  parameters: {
    required: [
      {
        name: "callback",
        type: "T extends (...args: any[]) => any",
        description: "要缓存的函数。这个函数本身会被缓存，不是它的返回值"
      },
      {
        name: "dependencies",
        type: "DependencyList",
        description: "依赖项数组。只有当数组中的值发生变化时，才会返回新的函数引用。如果是空数组，函数引用永远不会改变"
      }
    ],
    optional: []
  },
  
  returnValue: {
    type: "T",
    description: "缓存的函数引用",
    details: [
      {
        name: "首次渲染",
        description: "返回传入的callback函数"
      },
      {
        name: "后续渲染",
        description: "如果依赖项没有变化，返回上次缓存的函数引用；如果依赖项变化，返回新的函数引用"
      }
    ]
  },
  
  historicalContext: {
    origin: "useCallback在React 16.8中与其他Hooks一起引入，是为了解决函数组件中的函数引用稳定性问题",
    evolution: [
      {
        version: "16.8",
        changes: "首次引入useCallback Hook"
      },
      {
        version: "18.0",
        changes: "在并发特性下，useCallback的行为更加稳定"
      }
    ],
    designPhilosophy: "useCallback的设计理念是提供一种声明式的方式来保持函数引用的稳定性，这对于性能优化和防止不必要的重渲染至关重要。它本质上是useMemo的特殊情况"
  }
};

export default basicInfo; 