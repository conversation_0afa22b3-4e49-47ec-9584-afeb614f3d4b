import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: "useCallback-performance",
    question: "useCallback真的能提升性能吗？什么时候应该使用？",
    answer: `useCallback并不总是能提升性能，它是一个优化工具而非性能万能药。实际上，不当使用可能会降低性能。

**关键原则**：
- useCallback本身有成本：需要比较依赖项、维护缓存
- 只有在避免的重渲染成本大于缓存成本时才有收益
- 主要用于优化向下传递的props，特别是传给React.memo组件
- 作为其他Hook的依赖项时使用，保持引用稳定
- 不要预先优化，先用React DevTools Profiler分析`,
    code: `// ✅ 好的使用场景
const GoodExample = () => {
  const [data, setData] = useState([]);
  const [filter, setFilter] = useState('');
  
  // 场景1：传递给memo化的子组件
  const handleItemClick = useCallback((id) => {
    console.log('Item clicked:', id);
  }, []);
  
  // 场景2：作为effect的依赖
  const fetchData = useCallback(async () => {
    const result = await api.getData(filter);
    setData(result);
  }, [filter]);
  
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  
  return <ExpensiveList items={data} onItemClick={handleItemClick} />;
};

// ❌ 不必要的使用
const BadExample = () => {
  const [count, setCount] = useState(0);
  
  // 没有传递给子组件，也不作为依赖项
  const increment = useCallback(() => {
    setCount(c => c + 1);
  }, []);
  
  return <button onClick={increment}>+</button>;
};`,
    tags: ['性能优化', '使用场景'],
    relatedQuestions: ['如何判断是否需要useCallback？', 'useCallback的性能开销有多大？']
  },
  {
    id: "useCallback-dependencies",
    question: "useCallback的依赖项数组应该如何正确设置？",
    answer: `依赖项数组是useCallback正确工作的关键。设置不当会导致闭包陷阱或性能问题。

**核心原则**：
- 包含函数体内使用的所有外部变量
- 使用ESLint的exhaustive-deps规则自动检查
- 空数组[]表示函数永不改变
- 省略数组会导致每次都返回新函数（相当于不用useCallback）
- 依赖项应该是稳定的，避免每次渲染都变化`,
    code: `function DependencyExample() {
  const [count, setCount] = useState(0);
  const [text, setText] = useState('');
  
  // ❌ 错误：遗漏依赖项
  const badCallback = useCallback(() => {
    console.log(count); // 使用了count但未声明依赖
  }, []); // ESLint会警告
  
  // ✅ 正确：包含所有使用的外部变量
  const goodCallback1 = useCallback(() => {
    console.log('Count: ' + count + ', Text: ' + text);
  }, [count, text]);
  
  // ✅ 正确：使用函数式更新减少依赖
  const goodCallback2 = useCallback(() => {
    setCount(c => c + 1);
    setText('Updated');
  }, []); // 不需要依赖
  
  return <div>...</div>;
}`,
    tags: ['依赖管理', '闭包陷阱'],
    relatedQuestions: ['为什么依赖项如此重要？', '如何避免依赖项频繁变化？']
  },
  {
    id: "useCallback-debugging",
    question: "如何调试useCallback相关的性能问题？",
    answer: `调试useCallback需要结合多种工具和技术，找出真正的性能瓶颈。

**调试方法**：
- 使用React DevTools Profiler分析组件渲染
- 添加console.log跟踪函数创建和调用
- 使用useWhyDidYouUpdate自定义Hook
- 检查依赖项变化频率
- 对比有无useCallback的性能差异`,
    code: `// 自定义Hook：追踪更新原因
function useWhyDidYouUpdate(name, props) {
  const previousProps = useRef();
  
  useEffect(() => {
    if (previousProps.current) {
      const allKeys = Object.keys({ ...previousProps.current, ...props });
      const changedProps = {};
      
      allKeys.forEach(key => {
        if (previousProps.current[key] !== props[key]) {
          changedProps[key] = {
            from: previousProps.current[key],
            to: props[key]
          };
        }
      });
      
      if (Object.keys(changedProps).length) {
        console.log('[why-did-you-update]', name, changedProps);
      }
    }
    
    previousProps.current = props;
  });
}

// 追踪函数引用变化
function useCallbackDebug(callback, deps, name) {
  const callbackRef = useRef(callback);
  const renderCount = useRef(0);
  
  renderCount.current++;
  
  useEffect(() => {
    if (callbackRef.current !== callback) {
      console.log("[" + name + "] callback changed at render #" + renderCount.current);
      callbackRef.current = callback;
    }
  });
  
  return useCallback(callback, deps);
}`,
    tags: ['调试技巧', '性能分析'],
    relatedQuestions: ['如何使用React DevTools Profiler？', '什么工具可以检测不必要的重渲染？']
  },
  {
    id: "useCallback-with-memo",
    question: "useCallback和React.memo如何配合使用？有什么注意事项？",
    answer: `useCallback和React.memo是性能优化的黄金搭档，但需要正确配合使用才能发挥效果。

**配合原理**：
- React.memo对props进行浅比较，决定是否重渲染
- 函数props每次都是新引用会破坏memo的优化
- useCallback保持函数引用稳定，让memo的比较有意义
- 不是所有组件都需要memo，要根据实际性能瓶颈决定`,
    code: `// 基础配合使用
const ChildComponent = React.memo(({ onClick, data }) => {
  console.log('ChildComponent rendered');
  return (
    <div onClick={onClick}>
      {data.map(item => <div key={item.id}>{item.name}</div>)}
    </div>
  );
});

function ParentComponent() {
  const [count, setCount] = useState(0);
  const [data] = useState([{ id: 1, name: 'Item 1' }]);
  
  // ❌ 没有useCallback，ChildComponent每次都重渲染
  const handleClickBad = () => {
    console.log('Clicked');
  };
  
  // ✅ 使用useCallback，ChildComponent只在必要时重渲染
  const handleClick = useCallback(() => {
    console.log('Clicked');
  }, []);
  
  return (
    <div>
      <button onClick={() => setCount(count + 1)}>
        Parent Count: {count}
      </button>
      <ChildComponent onClick={handleClick} data={data} />
    </div>
  );
}`,
    tags: ['React.memo', '性能优化组合'],
    relatedQuestions: ['什么时候应该使用React.memo？', '如何避免过度优化？']
  }
];

export default commonQuestions; 