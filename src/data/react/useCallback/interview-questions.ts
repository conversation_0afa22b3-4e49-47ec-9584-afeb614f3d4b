import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: "解释useCallback和useMemo的区别，以及各自的使用场景",
    answer: {
      brief: "useCallback缓存函数本身，useMemo缓存函数的返回值。useCallback专门用于优化函数引用稳定性，useMemo用于缓存昂贵计算结果。",
      detailed: `**主要区别**：
1. **useCallback** - 缓存函数本身，保持函数引用稳定
2. **useMemo** - 缓存函数的返回值，避免重复计算

**使用场景**：
- useCallback：传递给子组件的回调函数、作为其他Hook的依赖项
- useMemo：昂贵的计算结果、复杂对象的创建、组件props的优化

**本质关系**：useCallback(fn, deps) 等价于 useMemo(() => fn, deps)`,
      code: `// useCallback - 缓存函数
const handleClick = useCallback(() => {
  doSomething(a, b);
}, [a, b]);

// useMemo - 缓存计算结果  
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(a, b);
}, [a, b]);

// 实际应用场景
function Parent() {
  const [count, setCount] = useState(0);
  const [text, setText] = useState('');
  
  // useCallback优化传递给子组件的函数
  const handleTextChange = useCallback((newText) => {
    setText(newText);
  }, []);
  
  // useMemo缓存计算结果
  const statistics = useMemo(() => ({
    length: text.length,
    words: text.split(' ').filter(w => w).length,
    lines: text.split('\\n').length
  }), [text]);
  
  return (
    <>
      <button onClick={() => setCount(count + 1)}>Count: {count}</button>
      <TextEditor onChange={handleTextChange} />
      <Statistics data={statistics} />
    </>
  );
}`
    },
    difficulty: 'easy',
    frequency: 'high',
    category: 'comparison',
    tags: ['useCallback', 'useMemo', '比较', '缓存', '性能优化']
  },
  {
    id: 2,
    question: "什么情况下useCallback可能会降低性能？如何避免？",
    answer: {
      brief: "过度使用、依赖项频繁变化、简单函数缓存等情况会降低性能。应该只在传递给优化组件或作为Hook依赖时使用。",
      detailed: `**降低性能的情况**：
1. 过度使用导致额外内存开销
2. 依赖项频繁变化导致缓存失效
3. 简单函数的缓存成本高于收益
4. 错误的依赖项导致闭包陷阱

**正确使用原则**：
- 只在函数传递给使用React.memo的子组件时使用
- 只在函数作为其他Hook的依赖项时使用
- 使用React DevTools Profiler验证是否真的需要优化`,
      code: `// ❌ 反模式：过度使用
function BadExample() {
  const [count, setCount] = useState(0);
  
  // 简单函数不需要缓存
  const increment = useCallback(() => {
    setCount(count + 1);
  }, [count]); // count频繁变化，缓存失效
  
  return <button onClick={increment}>Click</button>;
}

// ✅ 正确使用
function GoodExample() {
  const [items, setItems] = useState([]);
  
  // 传递给优化过的子组件
  const handleItemClick = useCallback((id) => {
    setItems(prev => prev.map(item => 
      item.id === id ? { ...item, selected: !item.selected } : item
    ));
  }, []); // 使用函数式更新，无需依赖
  
  return <ItemList items={items} onItemClick={handleItemClick} />;
}`
    },
    difficulty: 'medium',
    frequency: 'medium',
    category: 'performance',
    tags: ['性能优化', '反模式', '最佳实践', '陷阱']
  },
  {
    id: 3,
    question: "如何处理useCallback中的闭包陷阱？",
    answer: {
      brief: "闭包陷阱通常由依赖项不完整导致。解决方案包括：添加完整依赖、使用函数式更新、使用useRef存储最新值、使用useReducer。",
      detailed: `**闭包陷阱原因**：函数捕获了过期的变量值，通常因为依赖项数组不完整

**解决方案**：
1. 添加正确的依赖项
2. 使用函数式更新避免依赖状态值
3. 使用useRef存储可变值
4. 使用useReducer替代复杂状态`,
      code: `// ❌ 闭包陷阱
function ClosureTrap() {
  const [count, setCount] = useState(0);
  
  const handleClick = useCallback(() => {
    console.log('Count:', count); // 永远是0
    setCount(count + 1); // 永远设置为1
  }, []); // 缺少count依赖
}

// ✅ 解决方案1：使用函数式更新
const handleClick = useCallback(() => {
  setCount(prev => {
    console.log('Count:', prev);
    return prev + 1;
  });
}, []); // 不需要count依赖

// ✅ 解决方案2：使用useRef
function NoClosureTrap() {
  const [count, setCount] = useState(0);
  const countRef = useRef(count);
  
  useEffect(() => {
    countRef.current = count;
  }, [count]);
  
  const handleClick = useCallback(() => {
    console.log('Count:', countRef.current);
    setCount(countRef.current + 1);
  }, []);
}`
    },
    difficulty: 'hard',
    frequency: 'medium',
    category: 'advanced',
    tags: ['闭包陷阱', '依赖项', 'useRef', '函数式更新']
  }
];

export default interviewQuestions; 