import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  background: `## useCallback 的诞生背景

useCallback的诞生源于React函数组件的性能优化需求。在Hooks出现之前，类组件可以通过方法绑定保持函数引用稳定，但函数组件每次渲染都会创建新的函数实例。

### 历史时间线

**2013-2015年：React早期版本**
- 类组件通过在constructor中绑定方法或使用箭头函数属性来保持方法引用稳定

**2016-2017年：无状态函数组件流行**
- 函数组件简洁但缺乏优化手段，每次渲染都创建新的事件处理函数

**2018年10月：React 16.6引入React.memo**
- 为函数组件提供了类似PureComponent的浅比较优化，但函数props仍是痛点

**2019年2月：React 16.8正式发布Hooks**
- useCallback作为核心Hook之一，专门解决函数引用稳定性问题

**2020-2021年：最佳实践形成**
- 社区逐渐形成useCallback的使用规范，避免过度优化

**2022年至今：React Compiler探索**
- React团队探索自动优化，未来可能不需要手动使用useCallback

### 关键人物

**Dan Abramov**：设计了Hooks API，强调useCallback应该是优化手段而非默认选择
> "useCallback is not a way to make functions faster. It's a way to make them the same."

**Sophie Alpert**：参与Hooks设计，推动了函数组件性能优化方案
> "Hooks让函数组件拥有了类组件的所有能力，包括性能优化"`,

  evolution: `## useCallback 的演进历程

### Hooks之前的函数稳定性解决方案

**类组件方法绑定**：
\`\`\`javascript
class Component extends React.Component {
  constructor(props) {
    super(props);
    // 方式1：构造函数中绑定
    this.handleClick = this.handleClick.bind(this);
  }
  
  // 方式2：箭头函数属性
  handleClick2 = () => {
    console.log('clicked');
  }
  
  render() {
    return <Child onClick={this.handleClick} />;
  }
}
\`\`\`

**高阶组件模式**：
\`\`\`javascript
function withStableCallback(Component) {
  return class extends React.Component {
    callbacks = new Map();
    
    getCallback(key, fn) {
      if (!this.callbacks.has(key)) {
        this.callbacks.set(key, fn.bind(this));
      }
      return this.callbacks.get(key);
    }
    
    render() {
      return <Component {...this.props} getCallback={this.getCallback} />;
    }
  };
}
\`\`\`

### Hooks带来的革命性变化

**Hooks之前：复杂的优化逻辑**
\`\`\`javascript
class OptimizedList extends PureComponent {
  constructor(props) {
    super(props);
    this.itemCallbacks = new Map();
  }
  
  getItemCallback(id) {
    if (!this.itemCallbacks.has(id)) {
      this.itemCallbacks.set(id, () => this.handleItemClick(id));
    }
    return this.itemCallbacks.get(id);
  }
  
  render() {
    return this.props.items.map(item => (
      <Item key={item.id} onClick={this.getItemCallback(item.id)} />
    ));
  }
}
\`\`\`

**Hooks之后：简洁的声明式API**
\`\`\`javascript
function OptimizedList({ items }) {
  const handleItemClick = useCallback((id) => {
    // 处理点击
  }, []);
  
  return items.map(item => (
    <Item key={item.id} onClick={() => handleItemClick(item.id)} />
  ));
}
\`\`\`

### 最佳实践的演进

- **2019年初**：对所有函数使用useCallback（过度谨慎）
- **2019年中**：只对传给memo组件的函数使用（理解优化价值）
- **2020年后**：基于实际性能瓶颈决定（工具成熟，可以准确测量）`,

  comparisons: `## 与其他方案的对比

### useCallback vs useMemo

\`\`\`javascript
// useCallback - 缓存函数本身
const memoizedCallback = useCallback(() => {
  doSomething(a, b);
}, [a, b]);

// useMemo - 缓存函数调用结果
const memoizedValue = useMemo(() => {
  return computeExpensiveValue(a, b);
}, [a, b]);

// 等价关系
const memoizedCallback = useMemo(() => () => {
  doSomething(a, b);
}, [a, b]);
\`\`\`

### 与类组件方法的对比

| 特性 | 类组件方法 | useCallback |
|-----|-----------|-------------|
| 函数稳定性 | 构造函数绑定稳定 | 依赖数组变化时更新 |
| 语法复杂度 | 需要bind或箭头函数 | 声明式，简洁明了 |
| 内存占用 | 方法绑定到实例 | 按需缓存 |
| 逻辑复用 | 难以跨组件复用 | 可封装为自定义Hook |

### 与其他框架的对比

**Vue的computed和methods**：
- Vue的响应式系统自动追踪依赖
- React需要手动声明依赖数组

**Angular的纯管道**：
- Angular使用纯管道优化模板中的函数调用
- React使用useCallback优化函数引用稳定性`,

  philosophy: `## 设计哲学

### 核心理念

**1. 引用相等性（Referential Equality）**

JavaScript中函数是对象，每次创建都有新的引用。React的浅比较依赖引用相等性判断是否需要更新：

\`\`\`javascript
// 引用相等性示例
const fn1 = () => console.log('hello');
const fn2 = () => console.log('hello');

console.log(fn1 === fn2); // false - 即使功能相同

const fn3 = fn1;
console.log(fn1 === fn3); // true - 同一个引用
\`\`\`

**2. 记忆化（Memoization）**

useCallback是函数记忆化的特殊形式，缓存函数本身而非结果：

\`\`\`javascript
// useCallback的概念模型
function conceptualUseCallback(fn, deps) {
  const cache = useRef();
  
  if (!cache.current || !shallowEqual(cache.current.deps, deps)) {
    cache.current = { fn, deps };
  }
  
  return cache.current.fn;
}
\`\`\`

**3. 性能权衡（Performance Trade-offs）**

优化总是有成本的，useCallback需要权衡缓存成本与避免重渲染的收益：

\`\`\`javascript
// 成本分析
const CostAnalysis = () => {
  // 成本：依赖比较 + 内存占用
  const memoizedFn = useCallback(() => {
    doSomething();
  }, [dep1, dep2, dep3]); // 每次渲染都要比较3个依赖
  
  // 收益：避免子组件重渲染
  return <ExpensiveChild onClick={memoizedFn} />;
  
  // 如果ExpensiveChild渲染成本 > useCallback成本，则值得优化
};
\`\`\`

### 设计原则

- **明确的优化意图**：不是默认选择，而是针对性优化
- **简单的API设计**：与useMemo保持一致的接口
- **可预测的行为**：基于依赖项的确定性更新
- **渐进式采用**：可以逐步添加到现有代码中

### 常见误区

- ❌ **误区**：useCallback让函数执行更快
- ✅ **现实**：useCallback只是缓存函数引用，不影响函数执行性能

- ❌ **误区**：使用useCallback总是好的
- ✅ **现实**：不当使用会增加内存占用和比较开销

- ❌ **误区**：依赖数组可以选择性添加
- ✅ **现实**：必须包含所有使用的外部变量，否则会有闭包问题`,

  presentValue: `## 现实应用价值

### 核心应用场景

**1. 表单和用户输入**
- 防止输入框因父组件渲染而失去焦点
- 优化复杂表单的交互性能
- 避免输入验证的重复触发

**2. 列表和表格优化**
- 大数据量列表的性能优化
- 虚拟滚动中的回调稳定性
- 选择和操作功能的优化

**3. 事件处理优化**
- Modal、Dropdown等组件的事件处理
- 防抖和节流函数的稳定引用
- 异步操作的取消和重试逻辑

### 生态系统影响

**对React生态的推动**：
- 促进了函数组件的全面采用
- 推动了性能优化最佳实践的标准化
- 影响了状态管理库的设计（如React Query、Recoil）

**开发体验提升**：
- 降低了性能优化的技术门槛
- 提供了声明式的优化方式
- 与TypeScript的完美集成

### 实际项目价值

**性能提升**：
- 在复杂应用中可以减少10-30%的不必要重渲染
- 特别适合数据密集型和交互频繁的应用
- 移动端性能优化的重要工具

**代码质量**：
- 促进了更好的组件设计
- 鼓励开发者思考渲染性能
- 提高了代码的可维护性

### 未来发展方向

**React Compiler (React Forget)**：
- 自动优化组件，可能不再需要手动使用useCallback
- 基于编译时分析自动插入优化代码

**更智能的默认行为**：
- React可能会自动检测哪些函数需要稳定引用
- 基于运行时分析自动决定是否缓存函数

**与新特性的结合**：
- 服务端组件和客户端组件的协作优化
- Suspense和并发特性的深度集成
- AI辅助的性能优化建议`
};

export default knowledgeArchaeology;
