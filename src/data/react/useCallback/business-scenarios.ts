import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: "form-input-handling",
    title: "表单输入处理与验证",
    description: "使用useCallback优化表单处理函数，避免输入组件的不必要重渲染",
    businessValue: "在复杂表单中减少50-70%的不必要重渲染，显著提升用户体验和应用性能",
    scenario: "用户注册表单包含多个输入字段，每个字段都有实时验证。不使用useCallback会导致每次输入都重新渲染所有字段组件。",
    code: `import { useCallback, useState, memo } from 'react';

// 输入组件 - 使用memo优化
const FormInput = memo(({ 
  name, 
  value, 
  onChange, 
  onBlur, 
  error, 
  placeholder 
}) => {
  console.log("FormInput " + name + " rendered");
  
  return (
    <div className="form-field">
      <input
        name={name}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        placeholder={placeholder}
        className={error ? 'error' : ''}
      />
      {error && <span className="error-message">{error}</span>}
    </div>
  );
});

function RegistrationForm() {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  
  // 验证规则
  const validators = {
    username: (value) => {
      if (!value) return '用户名必填';
      if (value.length < 3) return '用户名至少3个字符';
      if (!/^[a-zA-Z0-9_]+$/.test(value)) return '用户名只能包含字母数字和下划线';
      return null;
    },
    email: (value) => {
      if (!value) return '邮箱必填';
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return '邮箱格式不正确';
      return null;
    },
    password: (value) => {
      if (!value) return '密码必填';
      if (value.length < 8) return '密码至少8个字符';
      if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
        return '密码必须包含大小写字母和数字';
      }
      return null;
    },
    confirmPassword: (value, allValues) => {
      if (!value) return '请确认密码';
      if (value !== allValues.password) return '两次密码不一致';
      return null;
    }
  };
  
  // 通用的输入处理函数 - 使用useCallback缓存
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // 如果字段已被触碰，实时验证
    if (touched[name]) {
      const error = validators[name](value, { ...formData, [name]: value });
      setErrors(prev => ({
        ...prev,
        [name]: error
      }));
    }
  }, [formData, touched]); // 依赖formData和touched
  
  // 优化：为每个字段创建独立的处理函数
  const createFieldHandler = useCallback((fieldName) => {
    return (e) => {
      const { value } = e.target;
      setFormData(prev => ({
        ...prev,
        [fieldName]: value
      }));
      
      if (touched[fieldName]) {
        const error = validators[fieldName](
          value, 
          { ...formData, [fieldName]: value }
        );
        setErrors(prev => ({
          ...prev,
          [fieldName]: error
        }));
      }
    };
  }, [formData, touched]);
  
  // 为每个字段创建稳定的处理函数
  const handleUsernameChange = useCallback(createFieldHandler('username'), [createFieldHandler]);
  const handleEmailChange = useCallback(createFieldHandler('email'), [createFieldHandler]);
  const handlePasswordChange = useCallback(createFieldHandler('password'), [createFieldHandler]);
  const handleConfirmPasswordChange = useCallback(createFieldHandler('confirmPassword'), [createFieldHandler]);
  
  // 处理失焦事件
  const handleBlur = useCallback((e) => {
    const { name } = e.target;
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));
    
    // 验证当前字段
    const error = validators[name](formData[name], formData);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  }, [formData]);
  
  // 提交处理
  const handleSubmit = useCallback((e) => {
    e.preventDefault();
    
    // 标记所有字段为已触碰
    const allTouched = Object.keys(formData).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {});
    setTouched(allTouched);
    
    // 验证所有字段
    const allErrors = Object.keys(formData).reduce((acc, key) => {
      const error = validators[key](formData[key], formData);
      if (error) acc[key] = error;
      return acc;
    }, {});
    
    setErrors(allErrors);
    
    if (Object.keys(allErrors).length === 0) {
      console.log('表单提交:', formData);
      // 提交表单...
    }
  }, [formData]);
  
  return (
    <form onSubmit={handleSubmit}>
      <h2>用户注册</h2>
      
      <FormInput
        name="username"
        value={formData.username}
        onChange={handleUsernameChange}
        onBlur={handleBlur}
        error={errors.username}
        placeholder="用户名"
      />
      
      <FormInput
        name="email"
        value={formData.email}
        onChange={handleEmailChange}
        onBlur={handleBlur}
        error={errors.email}
        placeholder="邮箱"
      />
      
      <FormInput
        name="password"
        value={formData.password}
        onChange={handlePasswordChange}
        onBlur={handleBlur}
        error={errors.password}
        placeholder="密码"
        type="password"
      />
      
      <FormInput
        name="confirmPassword"
        value={formData.confirmPassword}
        onChange={handleConfirmPasswordChange}
        onBlur={handleBlur}
        error={errors.confirmPassword}
        placeholder="确认密码"
        type="password"
      />
      
      <button type="submit">注册</button>
    </form>
  );
}`,
    explanation: "通过useCallback缓存表单处理函数，配合React.memo优化子组件，避免不必要的重渲染"
  },
  {
    id: "list-operations",
    title: "列表项操作与批量处理",
    description: "在大型列表中使用useCallback优化项目操作，提升性能",
    businessValue: "在1000+项的列表中，将渲染性能提升80%，用户交互响应时间从500ms降至100ms",
    scenario: "待办事项管理应用需要支持大量任务的创建、编辑、删除和批量操作。每个列表项都有多个交互按钮。",
    code: `import { useCallback, useState, memo, useMemo } from 'react';

// 列表项组件
const TodoItem = memo(({ 
  todo, 
  onToggle, 
  onEdit, 
  onDelete, 
  isSelected,
  onSelect 
}) => {
  console.log('TodoItem ' + todo.id + ' rendered');
  
  return (
    <div className={isSelected ? 'todo-item selected' : 'todo-item'}>
      <input
        type="checkbox"
        checked={isSelected}
        onChange={() => onSelect(todo.id)}
      />
      <input
        type="checkbox"
        checked={todo.completed}
        onChange={() => onToggle(todo.id)}
        className="todo-checkbox"
      />
      <span 
        className={todo.completed ? 'completed' : ''}
        onDoubleClick={() => onEdit(todo.id)}
      >
        {todo.text}
      </span>
      <button onClick={() => onDelete(todo.id)}>删除</button>
    </div>
  );
});

function TodoApp() {
  const [todos, setTodos] = useState(() => 
    Array.from({ length: 1000 }, (_, i) => ({
      id: i + 1,
      text: '任务 ' + (i + 1),
      completed: false,
      category: ['work', 'personal', 'urgent'][i % 3]
    }))
  );
  
  const [selectedIds, setSelectedIds] = useState(new Set());
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  
  // 过滤后的todos
  const filteredTodos = useMemo(() => {
    return todos.filter(todo => {
      const matchesFilter = filter === 'all' || 
        (filter === 'active' && !todo.completed) ||
        (filter === 'completed' && todo.completed);
      
      const matchesSearch = todo.text.toLowerCase()
        .includes(searchTerm.toLowerCase());
      
      return matchesFilter && matchesSearch;
    });
  }, [todos, filter, searchTerm]);
  
  // 切换单个任务状态
  const handleToggle = useCallback((id) => {
    setTodos(prevTodos => 
      prevTodos.map(todo =>
        todo.id === id 
          ? { ...todo, completed: !todo.completed }
          : todo
      )
    );
  }, []); // 不依赖任何外部值
  
  // 编辑任务
  const handleEdit = useCallback((id) => {
    const todo = todos.find(t => t.id === id);
    if (!todo) return;
    
    const newText = prompt('编辑任务:', todo.text);
    if (newText && newText !== todo.text) {
      setTodos(prevTodos =>
        prevTodos.map(t =>
          t.id === id ? { ...t, text: newText } : t
        )
      );
    }
  }, [todos]); // 依赖todos来获取当前文本
  
  // 删除任务
  const handleDelete = useCallback((id) => {
    setTodos(prevTodos => prevTodos.filter(todo => todo.id !== id));
    setSelectedIds(prevSelected => {
      const newSelected = new Set(prevSelected);
      newSelected.delete(id);
      return newSelected;
    });
  }, []);
  
  // 选择/取消选择
  const handleSelect = useCallback((id) => {
    setSelectedIds(prevSelected => {
      const newSelected = new Set(prevSelected);
      if (newSelected.has(id)) {
        newSelected.delete(id);
      } else {
        newSelected.add(id);
      }
      return newSelected;
    });
  }, []);
  
  // 全选/取消全选
  const handleSelectAll = useCallback(() => {
    if (selectedIds.size === filteredTodos.length) {
      setSelectedIds(new Set());
    } else {
      setSelectedIds(new Set(filteredTodos.map(t => t.id)));
    }
  }, [filteredTodos, selectedIds.size]);
  
  // 批量操作
  const handleBatchComplete = useCallback(() => {
    if (selectedIds.size === 0) return;
    
    setTodos(prevTodos =>
      prevTodos.map(todo =>
        selectedIds.has(todo.id)
          ? { ...todo, completed: true }
          : todo
      )
    );
    setSelectedIds(new Set());
  }, [selectedIds]);
  
  const handleBatchDelete = useCallback(() => {
    if (selectedIds.size === 0) return;
    
    if (confirm('确定删除 ' + selectedIds.size + ' 个任务吗？')) {
      setTodos(prevTodos =>
        prevTodos.filter(todo => !selectedIds.has(todo.id))
      );
      setSelectedIds(new Set());
    }
  }, [selectedIds]);
  
  // 创建任务项的props生成器
  const createTodoItemProps = useCallback((todo) => ({
    todo,
    onToggle: handleToggle,
    onEdit: handleEdit,
    onDelete: handleDelete,
    isSelected: selectedIds.has(todo.id),
    onSelect: handleSelect
  }), [handleToggle, handleEdit, handleDelete, selectedIds, handleSelect]);
  
  return (
    <div className="todo-app">
      <div className="controls">
        <input
          type="text"
          placeholder="搜索任务..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        
        <div className="filters">
          <button 
            className={filter === 'all' ? 'active' : ''}
            onClick={() => setFilter('all')}
          >
            全部 ({todos.length})
          </button>
          <button 
            className={filter === 'active' ? 'active' : ''}
            onClick={() => setFilter('active')}
          >
            未完成 ({todos.filter(t => !t.completed).length})
          </button>
          <button 
            className={filter === 'completed' ? 'active' : ''}
            onClick={() => setFilter('completed')}
          >
            已完成 ({todos.filter(t => t.completed).length})
          </button>
        </div>
        
        <div className="batch-actions">
          <button onClick={handleSelectAll}>
            {selectedIds.size === filteredTodos.length ? '取消全选' : '全选'}
          </button>
          <button 
            onClick={handleBatchComplete}
            disabled={selectedIds.size === 0}
          >
            批量完成 ({selectedIds.size})
          </button>
          <button 
            onClick={handleBatchDelete}
            disabled={selectedIds.size === 0}
          >
            批量删除 ({selectedIds.size})
          </button>
        </div>
      </div>
      
      <div className="todo-list">
        {filteredTodos.map(todo => (
          <TodoItem key={todo.id} {...createTodoItemProps(todo)} />
        ))}
      </div>
    </div>
  );
}`,
    explanation: "使用useCallback配合函数式状态更新，确保列表项组件只在必要时重渲染"
  },
  {
    id: "real-time-search",
    title: "实时搜索与防抖节流",
    description: "使用useCallback实现高性能的实时搜索功能",
    businessValue: "将搜索请求减少90%，API调用成本降低，用户体验更流畅，服务器负载大幅减少",
    scenario: "电商网站的商品搜索功能，用户输入时需要实时显示搜索建议，但要避免过于频繁的API调用。",
    code: `import { useCallback, useState, useEffect, useRef, memo } from 'react';

// 搜索建议组件
const SearchSuggestions = memo(({ 
  suggestions, 
  onSelect, 
  highlightIndex,
  onHighlightChange 
}) => {
  console.log('SearchSuggestions rendered');
  
  return (
    <div className="suggestions">
      {suggestions.map((item, index) => (
        <div
          key={item.id}
          className={index === highlightIndex ? 'suggestion-item highlighted' : 'suggestion-item'}
          onClick={() => onSelect(item)}
          onMouseEnter={() => onHighlightChange(index)}
        >
          <strong>{item.title}</strong>
          <span>{item.category}</span>
        </div>
      ))}
    </div>
  );
});

function AutocompleteSearch({ onSearch, searchApi }) {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [highlightIndex, setHighlightIndex] = useState(-1);
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  const searchTimeoutRef = useRef(null);
  const abortControllerRef = useRef(null);
  const inputRef = useRef(null);
  
  // 防抖搜索函数
  const debouncedSearch = useCallback(async (searchTerm) => {
    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    if (!searchTerm.trim()) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }
    
    // 设置新的定时器
    searchTimeoutRef.current = setTimeout(async () => {
      setIsLoading(true);
      abortControllerRef.current = new AbortController();
      
      try {
        const results = await searchApi(searchTerm, {
          signal: abortControllerRef.current.signal
        });
        
        setSuggestions(results);
        setShowSuggestions(true);
        setHighlightIndex(-1);
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Search error:', error);
          setSuggestions([]);
        }
      } finally {
        setIsLoading(false);
      }
    }, 300); // 300ms 防抖延迟
  }, [searchApi]);
  
  // 输入处理
  const handleInputChange = useCallback((e) => {
    const value = e.target.value;
    setQuery(value);
    debouncedSearch(value);
  }, [debouncedSearch]);
  
  // 选择建议项
  const handleSelect = useCallback((item) => {
    setQuery(item.title);
    setShowSuggestions(false);
    setSuggestions([]);
    onSearch(item);
    
    // 聚焦输入框
    inputRef.current?.focus();
  }, [onSearch]);
  
  // 键盘导航
  const handleKeyDown = useCallback((e) => {
    if (!showSuggestions || suggestions.length === 0) return;
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
        
      case 'ArrowUp':
        e.preventDefault();
        setHighlightIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
        
      case 'Enter':
        e.preventDefault();
        if (highlightIndex >= 0) {
          handleSelect(suggestions[highlightIndex]);
        } else {
          onSearch({ title: query });
          setShowSuggestions(false);
        }
        break;
        
      case 'Escape':
        setShowSuggestions(false);
        setHighlightIndex(-1);
        break;
    }
  }, [showSuggestions, suggestions, highlightIndex, handleSelect, query, onSearch]);
  
  // 处理焦点
  const handleFocus = useCallback(() => {
    if (query && suggestions.length > 0) {
      setShowSuggestions(true);
    }
  }, [query, suggestions.length]);
  
  const handleBlur = useCallback(() => {
    // 延迟隐藏，允许点击建议项
    setTimeout(() => {
      setShowSuggestions(false);
    }, 200);
  }, []);
  
  // 高亮变化处理
  const handleHighlightChange = useCallback((index) => {
    setHighlightIndex(index);
  }, []);
  
  // 清理函数
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);
  
  return (
    <div className="autocomplete-search">
      <div className="search-input-wrapper">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder="搜索产品、文章、用户..."
          className="search-input"
        />
        {isLoading && <span className="loading-spinner">⟳</span>}
      </div>
      
      {showSuggestions && suggestions.length > 0 && (
        <SearchSuggestions
          suggestions={suggestions}
          onSelect={handleSelect}
          highlightIndex={highlightIndex}
          onHighlightChange={handleHighlightChange}
        />
      )}
    </div>
  );
}

// 使用示例
function App() {
  // 模拟搜索API
  const searchApi = useCallback(async (query, { signal }) => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 模拟搜索结果
    const allItems = [
      { id: 1, title: 'React Hooks 教程', category: '教程' },
      { id: 2, title: 'React 性能优化', category: '文章' },
      { id: 3, title: 'useCallback 详解', category: '文档' },
      // ... 更多数据
    ];
    
    return allItems.filter(item =>
      item.title.toLowerCase().includes(query.toLowerCase())
    );
  }, []);
  
  const handleSearch = useCallback((result) => {
    console.log('搜索结果:', result);
  }, []);
  
  return (
    <div className="app">
      <AutocompleteSearch
        onSearch={handleSearch}
        searchApi={searchApi}
      />
    </div>
  );
}`,
    explanation: "通过useCallback缓存防抖函数，避免每次渲染都创建新的定时器，实现真正的防抖效果"
  }
];

export default businessScenarios; 