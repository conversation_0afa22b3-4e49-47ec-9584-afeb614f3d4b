import { DebuggingTips } from '../../../types/api-types';

const debuggingTips: DebuggingTips = {
  title: "createRoot 调试指南",
  
  introduction: "createRoot引入的并发特性带来了新的调试挑战。本指南提供了诊断createRoot相关问题的实用技巧和工具。",

  troubleshooting: [
    {
      symptom: "页面加载后显示空白，控制台报错 'Target container is not a DOM element'",
      possibleCauses: [
        "DOM元素在createRoot调用时还未存在",
        "getElementById返回null",
        "脚本执行时机问题"
      ],
      solutions: [
        "确保DOM完全加载后再调用createRoot",
        "使用DOMContentLoaded事件",
        "检查元素ID是否正确",
        "验证脚本加载顺序"
      ],
      code: `// ❌ 错误：DOM可能还未准备好
const root = createRoot(document.getElementById('root'));

// ✅ 正确：等待DOM准备
document.addEventListener('DOMContentLoaded', () => {
  const container = document.getElementById('root');
  if (container) {
    const root = createRoot(container);
    root.render(<App />);
  } else {
    console.error('Root container not found');
  }
});`
    },

    {
      symptom: "应用在开发环境下渲染两次，组件的useEffect执行多次",
      possibleCauses: [
        "React 18开发模式的双重渲染",
        "StrictMode的副作用检测",
        "热重载导致的重复挂载"
      ],
      solutions: [
        "这是正常的开发模式行为，生产环境不会发生",
        "确保副作用有正确的清理函数",
        "使用useEffect的清理函数避免内存泄漏",
        "临时移除StrictMode验证行为"
      ],
      code: `// 正确处理双重渲染
function MyComponent() {
  useEffect(() => {
    console.log('Component mounted');
    
    // 设置订阅
    const subscription = api.subscribe(handleData);
    
    return () => {
      // 清理函数确保正确清理
      subscription.unsubscribe();
      console.log('Component cleaned up');
    };
  }, []);
}`
    },

    {
      symptom: "createRoot后某些状态更新不生效或延迟",
      possibleCauses: [
        "状态更新被包装在startTransition中",
        "并发模式下的优先级调度",
        "组件在渲染过程中被中断"
      ],
      solutions: [
        "检查是否使用了startTransition",
        "使用flushSync强制同步更新",
        "验证状态依赖关系",
        "使用React DevTools Profiler分析"
      ],
      code: `// 调试状态更新问题
import { flushSync } from 'react-dom';

function MyComponent() {
  const [count, setCount] = useState(0);
  
  const handleUrgentUpdate = () => {
    // 强制同步更新
    flushSync(() => {
      setCount(c => c + 1);
    });
    console.log('Count immediately updated:', count + 1);
  };
  
  const handleNormalUpdate = () => {
    startTransition(() => {
      setCount(c => c + 1); // 可能被延迟
    });
  };
}`
    },

    {
      symptom: "错误处理器没有捕获到预期的错误",
      possibleCauses: [
        "错误处理器配置不正确",
        "错误被其他Error Boundary捕获",
        "异步错误未正确处理"
      ],
      solutions: [
        "验证错误处理器配置",
        "检查Error Boundary的层次结构",
        "使用try-catch处理异步错误",
        "添加详细的错误日志"
      ],
      code: `// 调试错误处理
const root = createRoot(container, {
  onUncaughtError: (error, errorInfo) => {
    console.group('🚨 Uncaught Error Debug');
    console.error('Error:', error);
    console.error('Component Stack:', errorInfo.componentStack);
    console.error('Error Stack:', error.stack);
    console.groupEnd();
  },
  
  onCaughtError: (error, errorInfo) => {
    console.group('⚠️ Caught Error Debug');
    console.error('Error caught by Error Boundary:', error);
    console.error('Component:', errorInfo.componentStack);
    console.groupEnd();
  }
});`
    }
  ],

  debuggingTools: [
    {
      tool: "React DevTools",
      description: "最重要的React调试工具，支持createRoot的新特性",
      usage: `
1. **Profiler标签**：
   - 记录组件渲染性能
   - 查看并发特性的影响
   - 分析状态更新的优先级

2. **Components标签**：
   - 检查组件状态和props
   - 查看Hook的值和依赖
   - 调试组件树结构

3. **设置调试模式**：
\`\`\`javascript
// 启用更详细的调试信息
if (process.env.NODE_ENV === 'development') {
  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.settings.hideConsoleLogsInStrictMode = false;
}
\`\`\``,
      benefits: ["可视化组件树", "性能分析", "状态检查", "并发特性调试"]
    },

    {
      tool: "浏览器开发者工具",
      description: "利用浏览器原生工具调试createRoot应用",
      usage: `
1. **Performance标签**：
   - 记录页面性能
   - 查看长任务和阻塞
   - 分析时间切片效果

2. **Console调试**：
\`\`\`javascript
// 添加调试断点
function debugRender() {
  console.log('Component rendering...');
  debugger; // 暂停执行
}

// 监控状态变化
const [state, setState] = useState(initialValue);
console.log('State changed:', state);
\`\`\`

3. **Memory标签**：
   - 检测内存泄漏
   - 分析根节点清理效果
   - 监控垃圾回收`,
      benefits: ["性能分析", "内存监控", "断点调试", "网络分析"]
    },

    {
      tool: "自定义调试Hook",
      description: "创建专门的调试Hook来监控createRoot应用",
      usage: `
\`\`\`javascript
// 调试Hook示例
function useDebugRender(componentName) {
  const renderCount = useRef(0);
  const prevProps = useRef();
  
  useEffect(() => {
    renderCount.current++;
    console.log('[' + componentName + '] Render #' + renderCount.current);
  });
  
  const logProps = useCallback((props) => {
    if (prevProps.current) {
      const changedProps = Object.keys(props).filter(
        key => props[key] !== prevProps.current[key]
      );
      if (changedProps.length > 0) {
        console.log('[' + componentName + '] Props changed:', changedProps);
      }
    }
    prevProps.current = props;
  }, [componentName]);
  
  return { renderCount: renderCount.current, logProps };
}

// 使用调试Hook
function MyComponent(props) {
  const debug = useDebugRender('MyComponent');
  debug.logProps(props);
  
  return <div>Render count: {debug.renderCount}</div>;
}
\`\`\``,
      benefits: ["自定义监控", "渲染追踪", "Props变化检测", "性能分析"]
    }
  ],

  commonDebuggingScenarios: [
    {
      scenario: "并发特性调试",
      description: "调试并发渲染和优先级相关问题",
      steps: [
        "使用React DevTools Profiler记录渲染",
        "查看Lanes和优先级信息",
        "分析时间切片的效果",
        "检查startTransition的使用"
      ],
      code: `// 并发特性调试代码
function ConcurrentDebugger() {
  const [urgent, setUrgent] = useState(0);
  const [deferred, setDeferred] = useState(0);
  
  const handleUpdate = () => {
    // 紧急更新
    setUrgent(u => u + 1);
    console.log('Urgent update triggered');
    
    // 非紧急更新
    startTransition(() => {
      setDeferred(d => d + 1);
      console.log('Deferred update triggered');
    });
  };
  
  // 监控渲染
  useEffect(() => {
    console.log('Component rendered with:', { urgent, deferred });
  });
}`
    },

    {
      scenario: "内存泄漏调试",
      description: "检测和修复createRoot应用的内存泄漏",
      steps: [
        "使用浏览器Memory标签录制堆快照",
        "对比unmount前后的内存使用",
        "检查事件监听器是否正确清理",
        "验证定时器和订阅的清理"
      ],
      code: `// 内存泄漏调试工具
class MemoryDebugger {
  static trackComponent(name) {
    const startMemory = performance.memory?.usedJSHeapSize;
    console.log('[Memory] ' + name + ' mounted, heap:', startMemory);
    
    return () => {
      const endMemory = performance.memory?.usedJSHeapSize;
      console.log('[Memory] ' + name + ' unmounted, heap:', endMemory);
      
      if (endMemory > startMemory + 1024 * 1024) { // 1MB增长
        console.warn('[Memory] Potential leak in', name);
      }
    };
  }
}

// 使用内存调试器
function MyComponent() {
  useEffect(() => {
    const cleanup = MemoryDebugger.trackComponent('MyComponent');
    return cleanup;
  }, []);
}`
    }
  ],

  performanceDebugging: {
    title: "性能调试策略",
    techniques: [
      {
        name: "渲染性能分析",
        description: "使用React DevTools Profiler分析渲染性能",
        implementation: `// 性能标记
function withPerformanceMarks(WrappedComponent) {
  return function PerformanceWrapper(props) {
    useLayoutEffect(() => {
      performance.mark('component-render-start');
    });
    
    useEffect(() => {
      performance.mark('component-render-end');
      performance.measure(
        'component-render-duration',
        'component-render-start',
        'component-render-end'
      );
    });
    
    return <WrappedComponent {...props} />;
  };
}`
      },
      {
        name: "状态更新追踪",
        description: "追踪状态更新的触发和完成",
        implementation: `// 状态更新追踪
function useTrackedState(initialValue, name) {
  const [state, setState] = useState(initialValue);
  
  const trackedSetState = useCallback((newValue) => {
    console.log('[State]', name, 'updating from', state, 'to', newValue);
    setState(newValue);
  }, [state, name]);
  
  useEffect(() => {
    console.log('[State]', name, 'updated to', state);
  }, [state, name]);
  
  return [state, trackedSetState];
}`
      }
    ]
  }
};

export default debuggingTips; 