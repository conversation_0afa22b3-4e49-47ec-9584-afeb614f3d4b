import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 'q1',
    question: 'createRoot与ReactDOM.render有什么区别？为什么React 18要引入createRoot？',
    difficulty: 'medium',
    category: 'concept',
    answer: `主要区别：
1. **并发特性支持**：createRoot启用React 18的并发渲染，ReactDOM.render不支持
2. **自动批处理**：createRoot默认启用自动批处理，提升性能
3. **错误处理**：createRoot提供更细粒度的错误处理配置
4. **API设计**：createRoot返回根对象，ReactDOM.render直接渲染

引入原因：
- 启用并发特性（Suspense、Transitions等）
- 提供更好的性能优化
- 为未来React特性做准备
- 改善开发者体验和错误处理`,
    tags: ['concept', 'React 18', 'performance'],
    followUp: '如何在现有项目中从ReactDOM.render迁移到createRoot？',
    relatedQuestions: ['q2', 'q3']
  },

  {
    id: 'q2',
    question: 'createRoot如何启用并发特性？并发渲染的优势是什么？',
    difficulty: 'hard',
    category: 'implementation',
    answer: `并发特性启用机制：
\`\`\`javascript
// createRoot自动启用并发模式
const root = createRoot(container);
// 现在可以使用并发特性
root.render(
  <Suspense fallback={<Loading />}>
    <App />
  </Suspense>
);
\`\`\`

并发渲染优势：
1. **可中断渲染**：长时间渲染可以被高优先级更新中断
2. **时间切片**：避免阻塞主线程，保持页面响应
3. **优先级调度**：紧急更新优先处理，提升用户体验
4. **自动批处理**：减少不必要的重渲染

技术原理：
- Fiber架构支持任务中断和恢复
- Scheduler调度器管理任务优先级
- lanes模型实现细粒度更新管理`,
    tags: ['concurrent', 'performance', 'fiber'],
    followUp: '如何在并发模式下处理状态更新的优先级？',
    relatedQuestions: ['q1', 'q4']
  },

  {
    id: 'q3',
    question: 'createRoot的错误处理配置有哪些？如何在生产环境中配置错误监控？',
    difficulty: 'medium',
    category: 'practical',
    answer: `错误处理配置：
\`\`\`javascript
const root = createRoot(container, {
  // 未被Error Boundary捕获的错误
  onUncaughtError: (error, errorInfo) => {
    console.error('Uncaught error:', error);
    reportToSentry(error, {
      componentStack: errorInfo.componentStack
    });
  },
  
  // Error Boundary捕获的错误
  onCaughtError: (error, errorInfo) => {
    console.warn('Caught error:', error);
    analytics.track('react_error_boundary', {
      error: error.message,
      componentStack: errorInfo.componentStack
    });
  },
  
  // 可恢复的错误（如hydration不匹配）
  onRecoverableError: (error, errorInfo) => {
    console.warn('Recoverable error:', error);
    monitoring.recordWarning('hydration_mismatch', error);
  }
});
\`\`\`

生产环境最佳实践：
- 配置错误报告服务集成
- 区分错误类型和严重程度
- 记录用户行为和组件堆栈
- 设置错误告警阈值`,
    tags: ['error-handling', 'production', 'monitoring'],
    followUp: '如何区分处理不同类型的React错误？',
    relatedQuestions: ['q1', 'q5']
  },

  {
    id: 'q4',
    question: '在微前端架构中如何正确使用createRoot？需要注意哪些问题？',
    difficulty: 'hard',
    category: 'architecture',
    answer: `微前端中的createRoot使用：
\`\`\`javascript
class MicroFrontendManager {
  private roots = new Map();

  mountApp(appId, containerId, Component) {
    const container = document.getElementById(containerId);
    
    const root = createRoot(container, {
      // 避免ID冲突
      identifierPrefix: appId + '-',
      // 错误隔离
      onUncaughtError: (error) => {
        this.handleAppError(appId, error);
      }
    });

    root.render(<Component />);
    this.roots.set(appId, root);
  }

  unmountApp(appId) {
    const root = this.roots.get(appId);
    if (root) {
      root.unmount();
      this.roots.delete(appId);
    }
  }
}
\`\`\`

关键注意事项：
1. **ID冲突避免**：使用identifierPrefix
2. **错误隔离**：配置独立的错误处理
3. **内存泄漏**：正确调用unmount清理
4. **样式隔离**：避免全局CSS冲突
5. **事件冲突**：注意事件委托边界`,
    tags: ['microfrontend', 'architecture', 'isolation'],
    followUp: '如何实现微前端之间的通信和状态共享？',
    relatedQuestions: ['q3', 'q6']
  },

  {
    id: 'q5',
    question: 'createRoot与服务端渲染(SSR)的关系是什么？什么时候使用hydrateRoot？',
    difficulty: 'medium',
    category: 'ssr',
    answer: `createRoot与SSR的关系：
- **createRoot**：纯客户端渲染，清空容器重新渲染
- **hydrateRoot**：SSR场景，复用服务端HTML，绑定事件

使用场景：
\`\`\`javascript
// 检测SSR环境
if (typeof window !== 'undefined' && window.__INITIAL_DATA__) {
  // SSR场景使用hydrateRoot
  hydrateRoot(container, <App initialData={window.__INITIAL_DATA__} />);
} else {
  // 纯客户端使用createRoot
  const root = createRoot(container);
  root.render(<App />);
}
\`\`\`

区别对比：
| 方面 | createRoot | hydrateRoot |
|------|-----------|-------------|
| 使用场景 | 客户端渲染 | 服务端渲染 |
| DOM处理 | 清空重建 | 复用现有HTML |
| 性能 | 首屏较慢 | 首屏快速 |
| SEO | 不友好 | 友好 |

注意事项：
- hydrateRoot要求服务端客户端渲染一致
- 需要处理hydration不匹配问题`,
    tags: ['ssr', 'hydration', 'performance'],
    followUp: '如何处理SSR中的hydration不匹配问题？',
    relatedQuestions: ['q1', 'q3']
  },

  {
    id: 'q6',
    question: 'createRoot的内存管理策略是什么？如何避免内存泄漏？',
    difficulty: 'medium',
    category: 'performance',
    answer: `内存管理策略：
1. **根节点清理**：
\`\`\`javascript
const root = createRoot(container);
// 使用完毕后必须清理
root.unmount(); // 清理Fiber树和事件监听器
\`\`\`

2. **事件监听器清理**：
\`\`\`javascript
// createRoot会自动设置事件监听器
// unmount时自动清理，避免手动管理
\`\`\`

3. **引用释放**：
\`\`\`javascript
// 避免持有根节点引用
let root = createRoot(container);
// ... 使用
root.unmount();
root = null; // 释放引用
\`\`\`

常见内存泄漏场景：
- 忘记调用unmount方法
- 组件内定时器未清理
- 事件监听器未移除
- 异步操作未取消

最佳实践：
- 在组件卸载时清理资源
- 使用useEffect的清理函数
- 避免在已卸载组件中更新状态`,
    tags: ['memory', 'performance', 'cleanup'],
    followUp: 'React如何检测和调试内存泄漏问题？',
    relatedQuestions: ['q4', 'q7']
  },

  {
    id: 'q7',
    question: 'createRoot如何支持React 18的新特性（如Suspense、Transitions）？',
    difficulty: 'hard',
    category: 'features',
    answer: `createRoot对新特性的支持：

1. **Suspense增强**：
\`\`\`javascript
const root = createRoot(container);
root.render(
  <Suspense fallback={<Loading />}>
    <LazyComponent />
  </Suspense>
);
// 支持并发渲染下的Suspense
\`\`\`

2. **Transitions支持**：
\`\`\`javascript
function App() {
  const [isPending, startTransition] = useTransition();
  
  const handleClick = () => {
    startTransition(() => {
      // 低优先级更新，不会阻塞紧急更新
      setData(newData);
    });
  };
}
\`\`\`

3. **自动批处理**：
\`\`\`javascript
// 在createRoot下，这些更新会被自动批处理
setTimeout(() => {
  setState1(value1);
  setState2(value2); // 只触发一次重渲染
}, 1000);
\`\`\`

4. **useDeferredValue**：
\`\`\`javascript
function SearchResults({ query }) {
  const deferredQuery = useDeferredValue(query);
  // 延迟更新，避免阻塞输入
  return <Results query={deferredQuery} />;
}
\`\`\`

技术原理：
- Fiber架构支持任务中断
- 调度器管理优先级
- lanes模型实现精确更新`,
    tags: ['react-18', 'suspense', 'transitions'],
    followUp: '如何在实际项目中最佳实践使用这些新特性？',
    relatedQuestions: ['q2', 'q5']
  }
];

export default interviewQuestions; 