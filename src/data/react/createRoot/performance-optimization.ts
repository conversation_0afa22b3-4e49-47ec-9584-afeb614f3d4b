import { PerformanceOptimization } from '../../../types/api-types';

const performanceOptimization: PerformanceOptimization = {
  title: "createRoot 性能优化策略",
  
  bestPractices: [
    {
      practice: "正确配置并发特性",
      description: "充分利用createRoot启用的并发特性来提升应用性能",
      example: `// 利用并发特性优化大列表渲染
import { createRoot } from 'react-dom/client';
import { startTransition } from 'react';

const root = createRoot(container);

function LargeList({ items }) {
  const [filter, setFilter] = useState('');
  const [filteredItems, setFilteredItems] = useState(items);

  const handleFilterChange = (newFilter) => {
    // 紧急更新：立即更新输入框
    setFilter(newFilter);
    
    // 非紧急更新：延迟过滤大量数据
    startTransition(() => {
      setFilteredItems(items.filter(item => 
        item.name.includes(newFilter)
      ));
    });
  };

  return (
    <div>
      <input 
        value={filter}
        onChange={e => handleFilterChange(e.target.value)}
        placeholder="搜索..."
      />
      <List items={filteredItems} />
    </div>
  );
}`,
      benefits: ["保持输入响应性", "避免UI阻塞", "提升用户体验"]
    },

    {
      practice: "优化根节点错误处理",
      description: "配置高效的错误处理，避免性能损耗和不必要的错误报告",
      example: `const root = createRoot(container, {
  onUncaughtError: (error, errorInfo) => {
    // 节流错误报告，避免重复发送
    throttledErrorReport(error, errorInfo);
  },
  
  onCaughtError: (error, errorInfo) => {
    // 只在开发环境详细记录
    if (process.env.NODE_ENV === 'development') {
      console.group('React Error Boundary');
      console.error('Error:', error);
      console.error('Component Stack:', errorInfo.componentStack);
      console.groupEnd();
    }
  },
  
  onRecoverableError: (error, errorInfo) => {
    // 批量处理可恢复错误
    batchedWarningCollector.add(error, errorInfo);
  }
});

// 节流错误报告
const throttledErrorReport = throttle((error, errorInfo) => {
  errorReporting.send({
    message: error.message,
    stack: error.stack,
    componentStack: errorInfo.componentStack,
    url: window.location.href,
    timestamp: Date.now()
  });
}, 5000); // 5秒内最多发送一次`,
      benefits: ["减少错误报告开销", "避免错误风暴", "提升监控效率"]
    },

    {
      practice: "内存优化和清理策略",
      description: "实施严格的内存管理，防止内存泄漏影响性能",
      example: `class ComponentManager {
  private roots = new Map();
  private timers = new Set();
  private subscriptions = new Set();

  createComponent(containerId, Component, props) {
    const container = document.getElementById(containerId);
    const root = createRoot(container);
    
    // 包装组件以添加清理逻辑
    const WrappedComponent = () => {
      useEffect(() => {
        // 注册清理回调
        return () => {
          this.cleanup(containerId);
        };
      }, []);
      
      return <Component {...props} />;
    };

    root.render(<WrappedComponent />);
    this.roots.set(containerId, root);
  }

  cleanup(containerId) {
    // 清理根节点
    const root = this.roots.get(containerId);
    if (root) {
      root.unmount();
      this.roots.delete(containerId);
    }

    // 清理定时器
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();

    // 清理订阅
    this.subscriptions.forEach(unsub => unsub());
    this.subscriptions.clear();
  }

  // 全局清理
  destroy() {
    this.roots.forEach(root => root.unmount());
    this.roots.clear();
    this.cleanup();
  }
}`,
      benefits: ["防止内存泄漏", "提升长期运行稳定性", "减少GC压力"]
    }
  ],

  commonPitfalls: [
    {
      pitfall: "重复创建根节点",
      description: "在同一个容器上多次调用createRoot会导致错误和性能问题",
      problematicCode: `// ❌ 错误做法
const container = document.getElementById('root');
const root1 = createRoot(container);
const root2 = createRoot(container); // 错误！

// ❌ 每次渲染都创建新根节点
function App() {
  useEffect(() => {
    const root = createRoot(modalContainer); // 错误！
    root.render(<Modal />);
  }, [isOpen]);
}`,
      optimizedCode: `// ✅ 正确做法
const container = document.getElementById('root');
let root = null;

function getOrCreateRoot() {
  if (!root) {
    root = createRoot(container);
  }
  return root;
}

// ✅ 复用根节点
function App() {
  const [modalRoot, setModalRoot] = useState(null);
  
  useEffect(() => {
    if (!modalRoot) {
      setModalRoot(createRoot(modalContainer));
    }
  }, []);

  useEffect(() => {
    if (modalRoot && isOpen) {
      modalRoot.render(<Modal />);
    }
  }, [modalRoot, isOpen]);
}`,
      impact: "避免错误和不必要的性能开销"
    },

    {
      pitfall: "忘记清理根节点",
      description: "未调用unmount导致内存泄漏和事件监听器残留",
      problematicCode: `// ❌ 忘记清理
function ComponentWithModal() {
  useEffect(() => {
    const modalRoot = createRoot(modalContainer);
    modalRoot.render(<Modal />);
    // 缺少清理逻辑
  }, []);
}`,
      optimizedCode: `// ✅ 正确清理
function ComponentWithModal() {
  useEffect(() => {
    const modalRoot = createRoot(modalContainer);
    modalRoot.render(<Modal />);
    
    return () => {
      modalRoot.unmount(); // 清理根节点
    };
  }, []);
}

// ✅ 使用自定义Hook管理
function useModalRoot(container) {
  const [root, setRoot] = useState(null);
  
  useEffect(() => {
    if (container) {
      const modalRoot = createRoot(container);
      setRoot(modalRoot);
      
      return () => {
        modalRoot.unmount();
      };
    }
  }, [container]);
  
  return root;
}`,
      impact: "防止内存泄漏，提升应用稳定性"
    }
  ],

  optimizationTechniques: [
    {
      technique: "预加载和懒加载结合",
      description: "结合createRoot的并发特性实现智能资源加载",
      implementation: `// 智能预加载策略
function SmartPreloader() {
  const [isVisible, setIsVisible] = useState(false);
  const [Component, setComponent] = useState(null);

  useEffect(() => {
    let timeoutId;
    
    if (isVisible) {
      // 立即加载
      import('./HeavyComponent').then(module => {
        setComponent(() => module.default);
      });
    } else {
      // 延迟预加载
      timeoutId = setTimeout(() => {
        startTransition(() => {
          import('./HeavyComponent').then(module => {
            setComponent(() => module.default);
          });
        });
      }, 2000);
    }

    return () => clearTimeout(timeoutId);
  }, [isVisible]);

  return Component ? <Component /> : <Placeholder />;
}`,
      benefits: ["减少首屏加载时间", "提升后续交互响应", "优化资源利用"]
    },

    {
      technique: "批处理优化",
      description: "利用createRoot的自动批处理特性优化状态更新",
      implementation: `// 批处理状态更新
function OptimizedCounter() {
  const [count, setCount] = useState(0);
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleComplexUpdate = useCallback(() => {
    // 在createRoot下，这些更新会被自动批处理
    setIsLoading(true);
    setMessage('Processing...');
    setCount(prev => prev + 1);
    
    // 即使在setTimeout中也会批处理
    setTimeout(() => {
      setIsLoading(false);
      setMessage('Complete!');
    }, 1000);
  }, []);

  // 如果需要同步更新，使用flushSync
  const handleSyncUpdate = useCallback(() => {
    flushSync(() => {
      setCount(prev => prev + 1);
    });
    // DOM已经更新
    console.log('Count updated:', count + 1);
  }, [count]);

  return (
    <div>
      <span>Count: {count}</span>
      <span>Status: {message}</span>
      {isLoading && <Spinner />}
    </div>
  );
}`,
      benefits: ["减少渲染次数", "提升性能", "改善用户体验"]
    }
  ],

  measurementTools: [
    {
      tool: "React DevTools Profiler",
      usage: "测量createRoot应用的渲染性能",
      commands: "在DevTools中开启Profiler标签，记录和分析组件渲染"
    },
    {
      tool: "Performance.mark API",
      usage: "自定义性能标记",
      commands: `performance.mark('createRoot-start');
const root = createRoot(container);
performance.mark('createRoot-end');
performance.measure('createRoot-time', 'createRoot-start', 'createRoot-end');`
    },
    {
      tool: "Web Vitals",
      usage: "监控关键性能指标",
      commands: `import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);`
    }
  ]
};

export default performanceOptimization; 