import { BasicInfo } from '../../../types/api-types';

const basicInfo: BasicInfo = {
  definition: "createRoot 创建React根节点，用于在浏览器DOM元素中渲染React组件。这是React 18引入的新API，替代了传统的ReactDOM.render方法，启用了并发特性和现代React功能。",
  
  syntax: `import { createRoot } from 'react-dom/client';

const root = createRoot(domNode, options?);
root.render(<App />);`,

  commonUseCases: [
    {
      title: "基础应用根节点创建",
      code: `import { createRoot } from 'react-dom/client';
import App from './App';

const container = document.getElementById('root');
const root = createRoot(container);
root.render(<App />);`,
      explanation: "最常见的用法，为整个React应用创建根节点。"
    },
    {
      title: "带错误处理的根节点",
      code: `import { createRoot } from 'react-dom/client';

const container = document.getElementById('root');
const root = createRoot(container, {
  onUncaughtError: (error, errorInfo) => {
    console.error('Uncaught error:', error);
    reportError(error);
  },
  onCaughtError: (error, errorInfo) => {
    console.error('Caught error:', error);
  }
});
root.render(<App />);`,
      explanation: "配置错误处理回调，实现生产环境的错误监控。"
    },
    {
      title: "多根节点应用",
      code: `import { createRoot } from 'react-dom/client';

// 导航栏根节点
const navRoot = createRoot(document.getElementById('nav'));
navRoot.render(<Navigation />);

// 主内容根节点  
const mainRoot = createRoot(document.getElementById('main'));
mainRoot.render(<MainContent />);`,
      explanation: "在部分使用React的应用中创建多个独立的根节点。"
    }
  ],

  parameters: [
    {
      name: "domNode",
      type: "Element",
      description: "要作为React根节点的DOM元素",
      required: true
    },
    {
      name: "options",
      type: "RootOptions",
      description: "根节点配置选项",
      required: false,
      subParameters: [
        {
          name: "onUncaughtError",
          type: "(error: Error, errorInfo: ErrorInfo) => void",
          description: "未捕获错误的回调函数"
        },
        {
          name: "onCaughtError", 
          type: "(error: Error, errorInfo: ErrorInfo) => void",
          description: "Error Boundary捕获的错误回调"
        },
        {
          name: "onRecoverableError",
          type: "(error: Error, errorInfo: ErrorInfo) => void", 
          description: "可恢复错误的回调函数"
        },
        {
          name: "identifierPrefix",
          type: "string",
          description: "useId生成的ID前缀，避免多根节点冲突"
        }
      ]
    }
  ],

  returns: {
    type: "Root",
    description: "包含render和unmount方法的根节点对象"
  },

  limitations: [
    "createRoot只能用于客户端渲染，服务端渲染需要使用hydrateRoot",
    "DOM节点必须是有效的DOM元素，不能是文档片段或文本节点",
    "每个DOM节点只能创建一个根节点，重复创建会报错",
    "根节点创建后会清空DOM节点的现有内容"
  ],

  keyFeatures: [
    "启用React 18的并发特性和自动批处理",
    "支持Suspense和错误边界的现代行为",
    "提供细粒度的错误处理配置",
    "支持多根节点架构",
    "替代ReactDOM.render的现代API"
  ],

  relatedConcepts: [
    {
      name: "hydrateRoot",
      description: "用于服务端渲染的根节点创建",
      relationship: "createRoot的SSR版本"
    },
    {
      name: "root.render",
      description: "在根节点中渲染React组件",
      relationship: "createRoot返回对象的核心方法"
    },
    {
      name: "ReactDOM.render",
      description: "React 17及以前的渲染API",
      relationship: "被createRoot替代的传统API"
    },
    {
      name: "Concurrent Features",
      description: "React 18的并发特性",
      relationship: "createRoot启用的核心功能"
    }
  ],

  scenarios: [
    {
      title: "现代React应用入口",
      context: "将传统React应用升级到React 18",
      challenge: "启用并发特性和现代React功能",
      solution: "使用createRoot替代ReactDOM.render"
    },
    {
      title: "渐进式React集成",
      context: "在传统Web应用中局部使用React",
      challenge: "在不同页面区域独立渲染React组件",
      solution: "为每个React区域创建独立的根节点"
    }
  ]
}; 

export default basicInfo; 