import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import knowledgeArchaeology from './knowledge-archaeology';
import essenceInsights from './essence-insights';

/**
 * createRoot API 完整文档
 * 
 * 📋 基本信息：
 * - 框架：ReactDOM
 * - 分类：Client APIs
 * - 难度：Medium
 * - 版本：React 18.0+
 * 
 * 🎯 学习路径：
 * 1. 基本信息 - 了解语法和并发特性
 * 2. 业务场景 - 掌握现代化升级方法
 * 3. 原理解析 - 理解Fiber和调度机制
 * 4. 面试准备 - 准备并发渲染面试题
 * 5. 常见问题 - 解决迁移中的困惑
 * 6. 知识考古 - 了解React渲染进化史
 * 7. 性能优化 - 掌握并发优化技巧
 * 8. 调试技巧 - 学会并发问题排查
 * 9. 本质洞察 - 获得渲染哲学深层理解
 */

const createRootData: ApiItem = {
  // 基础配置
  id: 'createRoot',
  title: 'createRoot()',
  category: 'ReactDOM',
  description: 'React 18 新API，用于创建现代React应用的根节点，启用并发特性和自动批处理功能。',
  difficulty: 'medium',
  version: 'React 18.0+',
  tags: ['ReactDOM', 'Client', 'React 18', 'Root', 'Concurrent'],
  
  // 语法和示例
  syntax: 'createRoot(domNode, options?)',
  example: 'const root = createRoot(document.getElementById("root")); root.render(<App />);',
  notes: '替代ReactDOM.render，支持并发特性，仅用于客户端渲染',
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  performanceOptimization,
  debuggingTips,
  knowledgeArchaeology,
  essenceInsights
};

export default createRootData; 