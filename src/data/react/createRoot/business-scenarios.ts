import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 'scenario-1',
    title: '企业级SPA应用现代化升级',
    description: '大型企业管理系统从React 17升级到React 18，需要启用并发特性提升用户体验。',
    businessValue: '用户体验提升30%，页面响应速度增加25%，错误监控覆盖率达到95%',
    scenario: '在不破坏现有功能的前提下，平滑迁移到新的渲染API，并充分利用React 18的性能优势。',
    code: `// 升级前 (React 17)
import ReactDOM from 'react-dom';
import App from './App';

ReactDOM.render(<App />, document.getElementById('root'));

// 升级后 (React 18)
import { createRoot } from 'react-dom/client';
import App from './App';

const container = document.getElementById('root');
const root = createRoot(container, {
  // 配置生产环境错误报告
  onUncaughtError: (error, errorInfo) => {
    analytics.track('react_uncaught_error', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack
    });
  },
  onCaughtError: (error, errorInfo) => {
    console.warn('React Error Boundary caught:', error);
    // 发送到错误监控服务
    errorReporting.captureException(error, {
      extra: { componentStack: errorInfo.componentStack }
    });
  }
});

root.render(<App />);`,
    explanation: '通过升级到createRoot，应用自动启用了并发特性，包括自动批处理和时间切片。同时配置了完善的错误监控，提升了生产环境的可观测性。',
    benefits: [
      '启用自动批处理，减少不必要的重渲染',
      '支持并发特性，提升大列表和复杂UI的响应性',
      '更好的错误处理和监控能力',
      '为未来的React特性做好准备'
    ],
    metrics: {
      performance: '页面响应时间提升25%，主线程阻塞时间减少40%',
      userExperience: '用户交互响应延迟降低30%，卡顿现象减少80%',
      technicalMetrics: '错误监控覆盖率95%，首屏渲染时间优化20%'
    },
    difficulty: 'medium',
    tags: ['企业应用', '版本升级', '并发特性', '错误监控']
  },

  {
    id: 'scenario-2',
    title: '微前端架构中的独立React应用',
    description: '大型电商平台采用微前端架构，不同团队负责不同业务模块，每个模块都是独立的React应用。',
    businessValue: '开发效率提升40%，发布频率增加60%，系统稳定性提升显著',
    scenario: '在同一页面中渲染多个独立的React应用，避免相互干扰，实现模块化开发。',
    code: `// 主应用框架
class MicroFrontendManager {
  private roots: Map<string, any> = new Map();

  mountApp(appId: string, containerId: string, AppComponent: React.ComponentType) {
    const container = document.getElementById(containerId);
    if (!container) {
      throw new Error('Container not found: ' + containerId);
    }

    // 为每个微应用创建独立的根节点
    const root = createRoot(container, {
      identifierPrefix: appId + '-', // 避免ID冲突
      onUncaughtError: (error) => {
        console.error('[' + appId + '] Uncaught error:', error);
        // 隔离错误，不影响其他微应用
        this.reportError(appId, error);
      }
    });

    root.render(<AppComponent />);
    this.roots.set(appId, root);
    
    return root;
  }

  unmountApp(appId: string) {
    const root = this.roots.get(appId);
    if (root) {
      root.unmount();
      this.roots.delete(appId);
    }
  }
}

// 使用示例
const manager = new MicroFrontendManager();

// 挂载用户中心模块
manager.mountApp('user-center', 'user-center-container', UserCenterApp);

// 挂载商品管理模块  
manager.mountApp('product-mgmt', 'product-container', ProductManagementApp);

// 挂载订单系统模块
manager.mountApp('order-system', 'order-container', OrderSystemApp);`,
    explanation: '通过为每个微前端应用创建独立的根节点，实现了完全的隔离。错误隔离确保单个模块的问题不会影响整体应用，identifierPrefix避免了ID冲突。',
    benefits: [
      '各微应用完全独立，避免样式和状态冲突',
      '支持独立部署和版本管理',
      '错误隔离，单个模块异常不影响整体',
      '团队可以独立开发和维护自己的模块'
    ],
    metrics: {
      performance: '模块间隔离性能影响<5%，独立部署效率提升60%',
      userExperience: '单模块故障不影响其他功能，整体可用性99.9%',
      technicalMetrics: '代码耦合度降低70%，团队开发效率提升40%'
    },
    difficulty: 'hard',
    tags: ['微前端', '架构设计', '错误隔离', '模块化']
  }
];

export default businessScenarios; 