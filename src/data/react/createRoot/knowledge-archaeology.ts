import { KnowledgeArchaeology } from '../../../types/api-types';

const knowledgeArchaeology: KnowledgeArchaeology = {
  title: "createRoot 知识考古 - 现代React渲染的进化史",
  
  introduction: "createRoot的诞生标志着React从同步渲染到并发渲染的历史性跃迁。这一变革历经多年酝酿，承载着React团队对前端性能极限的不懈追求。",

  timeline: [
    {
      year: "2016",
      event: "Fiber架构概念诞生",
      significance: "React团队开始重写协调算法，为并发渲染奠定基础",
      description: "Sebastian Markbåge在React团队内部提出Fiber架构概念，旨在解决大型应用的性能瓶颈。这是createRoot诞生的技术前提。"
    },
    
    {
      year: "2017", 
      event: "Fiber架构正式发布（React 16）",
      significance: "新的协调算法上线，具备了中断和恢复渲染的基础能力",
      description: "React 16.0发布，内置Fiber架构，虽然还是同步渲染，但已经具备了增量渲染的基础设施。Error Boundaries和Portal等特性也在此版本引入。"
    },

    {
      year: "2018",
      event: "Concurrent Mode概念公布", 
      significance: "React首次公开展示时间切片和可中断渲染",
      description: "在JSConf Iceland 2018上，Dan Abramov演示了Concurrent Mode的早期原型，展示了时间切片如何保持应用的响应性。这是createRoot理念的首次公开亮相。"
    },

    {
      year: "2019",
      event: "实验性Concurrent Mode发布",
      significance: "开发者可以开始实验并发特性", 
      description: "React 16.8引入Hooks的同时，也发布了实验性的Concurrent Mode。虽然API还不稳定，但开发者已经可以通过unstable_createRoot体验并发特性。"
    },

    {
      year: "2020",
      event: "Scheduler独立包发布",
      significance: "时间切片调度器从React内核中分离，成为独立的包",
      description: "为了让调度逻辑更加清晰和可复用，React团队将Scheduler抽离为独立包。这为createRoot的最终实现提供了核心调度能力。"
    },

    {
      year: "2021", 
      event: "React 18 Alpha发布",
      significance: "createRoot API首次以稳定形式出现",
      description: "React 18 Alpha版本发布，createRoot替代ReactDOM.render成为推荐的渲染API。自动批处理、Suspense改进等特性开始稳定。"
    },

    {
      year: "2022",
      event: "React 18正式发布", 
      significance: "createRoot成为现代React应用的标准入口",
      description: "2022年3月，React 18正式发布。createRoot成为官方推荐的渲染API，并发特性正式可用于生产环境。这标志着React进入了并发渲染的新时代。"
    }
  ],

  keyFigures: [
    {
      name: "Sebastian Markbåge",
      role: "Fiber架构和并发模式的主要设计师", 
      contribution: "设计了Fiber数据结构和协调算法，为createRoot的实现奠定了理论基础。他的设计让React具备了时间切片的能力。",
      significance: "被誉为React并发特性的奠基人，他的设计理念影响了整个前端生态系统"
    },

    {
      name: "Andrew Clark",
      role: "并发特性和Scheduler的核心开发者",
      contribution: "实现了React的调度器系统，这是createRoot能够实现优先级调度的核心技术。他将复杂的调度逻辑转化为可工程化的代码。",
      significance: "调度器技术的实现者，让并发渲染从理论变为现实"
    },

    {
      name: "Dan Abramov", 
      role: "并发特性的布道者和API设计参与者",
      contribution: "通过技术分享和文档让开发者理解并发特性的价值，参与了createRoot等API的设计讨论。",
      significance: "架起了复杂技术与开发者理解之间的桥梁"
    },

    {
      name: "Brian Vaughn",
      role: "React DevTools和性能工具的开发者", 
      contribution: "开发了支持并发特性调试的开发者工具，让createRoot的复杂行为变得可观测和可调试。",
      significance: "让复杂的并发特性变得可见和可理解"
    }
  ],

  evolutionAnalysis: {
    technicalEvolution: [
      {
        stage: "同步渲染时代（React 15及以前）",
        characteristics: [
          "递归遍历虚拟DOM树",
          "渲染过程不可中断",
          "所有更新同等优先级"
        ],
        limitations: [
          "大型应用渲染阻塞主线程",
          "用户交互可能无响应",
          "无法实现智能的性能优化"
        ]
      },
      {
        stage: "Fiber基础设施时代（React 16-17）",
        characteristics: [
          "链表结构替代递归",
          "工作可以分解为小单元",
          "具备中断和恢复的能力"
        ],
        improvements: [
          "为并发特性准备了基础架构",
          "错误边界和Portal等新特性",
          "更好的错误处理机制"
        ]
      },
      {
        stage: "并发渲染时代（React 18+）",
        characteristics: [
          "时间切片和优先级调度",
          "自动批处理和智能优化",
          "可中断的并发渲染"
        ],
        breakthroughs: [
          "用户交互始终保持响应",
          "大型应用性能质的提升",
          "为未来特性奠定了基础"
        ]
      }
    ],

    philosophicalEvolution: [
      {
        era: "React早期：简单性优先",
        mindset: "让开发者能够快速上手，API简单直观",
        tradeoffs: "性能问题留给开发者自己解决"
      },
      {
        era: "React中期：性能与简单性平衡",
        mindset: "在保持API简单的同时，内部实现更复杂的优化",
        tradeoffs: "引入了一些高级概念，但核心使用方式保持简单"
      },
      {
        era: "React现代：智能化优先",
        mindset: "框架应该足够智能，能够自动优化大部分场景",
        tradeoffs: "底层复杂度增加，但用户体验显著提升"
      }
    ]
  },

  designPhilosophy: "createRoot体现了React团队'渐进式复杂度暴露'的设计哲学：让简单的事情保持简单（基础使用只需一行代码），让复杂的事情成为可能（高级用户可配置错误处理、优先级等）。同时践行'性能默认化'理念，将性能优化从开发者责任转移到框架本身，通过自动批处理、时间切片等技术默认启用性能优化。最终实现'向后兼容的革命'，在保持API兼容性的同时完成底层架构的根本性变革。",

  impact: "createRoot的发布标志着前端框架从'优化应用性能是开发者的责任'转向'框架应该智能地处理性能问题'的范式转变。它推动了其他框架研究时间切片技术，使并发渲染成为现代前端框架的标配特性，提高了用户对Web应用响应性的期望标准，让原生应用级别的流畅度成为Web应用的目标。",

  modernRelevance: "在现代Web开发中，createRoot不仅是React应用的入口，更是通往高性能用户体验的钥匙。它为React Server Components提供了客户端渲染基础设施，为Web Workers渲染创造了可能性，并为基于机器学习的智能调度奠定了基础。随着Web应用复杂度的持续增长，createRoot的并发特性将变得更加重要。"
};

export default knowledgeArchaeology; 