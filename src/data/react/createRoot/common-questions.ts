import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'cq1',
    question: '为什么使用createRoot后我的应用变慢了？',
    answer: `这通常是由以下原因引起的：

1. **开发模式的双重渲染**：React 18在开发模式下会故意双重渲染组件来检测副作用
2. **Strict Mode的额外检查**：如果应用包含了StrictMode，会进行额外的检查
3. **错误的性能测量**：在并发模式下测量性能需要不同的方法

解决方案：
\`\`\`javascript
// 1. 确保在生产环境测试性能
if (process.env.NODE_ENV === 'production') {
  // 性能测试代码
}

// 2. 临时移除StrictMode进行对比
// <React.StrictMode>  // 注释掉
  <App />
// </React.StrictMode>

// 3. 使用React DevTools Profiler测量真实性能
\`\`\``,
    category: 'performance',
    difficulty: 'medium',
    tags: ['performance', 'debugging', 'development'],
    relatedTopics: ['React DevTools', 'StrictMode', 'Performance']
  },

  {
    id: 'cq2',
    question: 'createRoot和ReactDOM.render可以在同一个应用中混用吗？',
    answer: `不建议混用，但技术上是可能的：

**不建议的原因：**
- 不同的渲染模式可能导致不一致的行为
- 错误处理机制不同
- 批处理行为差异
- 维护复杂度增加

**如果必须混用：**
\`\`\`javascript
// 新部分使用createRoot
const newRoot = createRoot(document.getElementById('new-section'));
newRoot.render(<NewFeature />);

// 旧部分继续使用ReactDOM.render（会有警告）
ReactDOM.render(<LegacyComponent />, document.getElementById('legacy-section'));
\`\`\`

**推荐的迁移策略：**
1. 创建迁移计划
2. 逐步替换各个页面/组件
3. 统一错误处理策略
4. 全面测试`,
    category: 'migration',
    difficulty: 'medium',
    tags: ['migration', 'compatibility', 'best-practices'],
    relatedTopics: ['Migration', 'ReactDOM.render', 'Version Upgrade']
  },

  {
    id: 'cq3',
    question: 'createRoot创建的根节点会自动清理吗？',
    answer: `不会自动清理，需要手动调用unmount：

\`\`\`javascript
// ❌ 错误：没有清理
const root = createRoot(container);
root.render(<App />);
// 页面刷新或组件卸载时，根节点没有被清理

// ✅ 正确：手动清理
const root = createRoot(container);
root.render(<App />);

// 在适当的时机清理
window.addEventListener('beforeunload', () => {
  root.unmount();
});

// 或在React组件中管理
function ComponentManager() {
  useEffect(() => {
    const root = createRoot(externalContainer);
    root.render(<ExternalComponent />);
    
    return () => {
      root.unmount(); // 组件卸载时清理
    };
  }, []);
}
\`\`\`

**清理的重要性：**
- 防止内存泄漏
- 移除事件监听器
- 清理定时器和订阅
- 释放DOM引用`,
    category: 'memory-management',
    difficulty: 'easy',
    tags: ['memory', 'cleanup', 'lifecycle'],
    relatedTopics: ['Memory Management', 'Cleanup', 'useEffect']
  },

  {
    id: 'cq4',
    question: 'createRoot的错误处理配置是必需的吗？',
    answer: `不是必需的，但强烈推荐配置：

**默认行为：**
\`\`\`javascript
// 最简单的使用方式
const root = createRoot(container);
root.render(<App />);
// React会使用默认的错误处理
\`\`\`

**推荐配置：**
\`\`\`javascript
const root = createRoot(container, {
  onUncaughtError: (error, errorInfo) => {
    // 生产环境必备：未捕获错误处理
    if (process.env.NODE_ENV === 'production') {
      reportError(error, errorInfo);
    } else {
      console.error('Uncaught error:', error);
    }
  },
  
  onCaughtError: (error, errorInfo) => {
    // 可选：Error Boundary捕获的错误
    logError('Error Boundary caught:', error);
  },
  
  onRecoverableError: (error, errorInfo) => {
    // 可选：可恢复错误（如hydration不匹配）
    logWarning('Recoverable error:', error);
  }
});
\`\`\`

**配置建议：**
- 生产环境必须配置onUncaughtError
- 开发环境可以配置更详细的日志
- 根据错误类型制定不同策略`,
    category: 'error-handling',
    difficulty: 'medium',
    tags: ['error-handling', 'configuration', 'production'],
    relatedTopics: ['Error Handling', 'Production Setup', 'Error Boundaries']
  },

  {
    id: 'cq5',
    question: 'createRoot支持服务端渲染吗？',
    answer: `createRoot本身不直接支持SSR，需要使用hydrateRoot：

**客户端渲染（CSR）：**
\`\`\`javascript
// 纯客户端应用
import { createRoot } from 'react-dom/client';

const root = createRoot(document.getElementById('root'));
root.render(<App />);
\`\`\`

**服务端渲染（SSR）：**
\`\`\`javascript
// 服务端渲染应用
import { hydrateRoot } from 'react-dom/client';

// 客户端激活服务端HTML
hydrateRoot(document.getElementById('root'), <App />);
\`\`\`

**通用解决方案：**
\`\`\`javascript
// 根据环境选择合适的API
import { createRoot, hydrateRoot } from 'react-dom/client';

const container = document.getElementById('root');

if (container.hasChildNodes()) {
  // 有服务端渲染的HTML，使用hydrateRoot
  hydrateRoot(container, <App />);
} else {
  // 空容器，使用createRoot
  const root = createRoot(container);
  root.render(<App />);
}
\`\`\`

**关键区别：**
- createRoot会清空容器内容
- hydrateRoot会复用现有HTML并绑定事件`,
    category: 'ssr',
    difficulty: 'medium',
    tags: ['ssr', 'hydration', 'client-server'],
    relatedTopics: ['SSR', 'hydrateRoot', 'Hydration']
  },

  {
    id: 'cq6',
    question: '如何在微前端架构中使用createRoot？',
    answer: `微前端中使用createRoot需要注意隔离和管理：

**基础实现：**
\`\`\`javascript
class MicroAppManager {
  private apps = new Map();

  mount(appId, container, Component, props = {}) {
    // 创建独立的根节点
    const root = createRoot(container, {
      identifierPrefix: appId + '-', // 避免ID冲突
      onUncaughtError: (error) => {
        console.error('[' + appId + '] Error:', error);
        this.handleAppError(appId, error);
      }
    });

    root.render(<Component {...props} />);
    this.apps.set(appId, { root, container });
  }

  unmount(appId) {
    const app = this.apps.get(appId);
    if (app) {
      app.root.unmount();
      this.apps.delete(appId);
    }
  }

  unmountAll() {
    this.apps.forEach((app, appId) => {
      this.unmount(appId);
    });
  }
}
\`\`\`

**注意事项：**
1. **ID冲突**：使用identifierPrefix避免
2. **错误隔离**：配置独立的错误处理
3. **样式隔离**：使用CSS-in-JS或Shadow DOM
4. **内存管理**：及时清理不用的应用
5. **通信机制**：设计应用间通信协议`,
    category: 'architecture',
    difficulty: 'hard',
    tags: ['microfrontend', 'architecture', 'isolation'],
    relatedTopics: ['Micro Frontend', 'Architecture', 'App Isolation']
  },

  {
    id: 'cq7',
    question: 'createRoot的并发特性什么时候会生效？',
    answer: `并发特性在以下情况下生效：

**自动启用的特性：**
\`\`\`javascript
const root = createRoot(container);
// 以下特性自动启用：

// 1. 自动批处理
setTimeout(() => {
  setState1(value1);
  setState2(value2); // 自动批处理，只渲染一次
}, 100);

// 2. 可中断渲染
// 长时间渲染会自动被高优先级更新中断
\`\`\`

**需要明确使用的特性：**
\`\`\`javascript
// 1. Transitions
import { startTransition } from 'react';

startTransition(() => {
  // 低优先级更新
  setLargeList(newData);
});

// 2. useDeferredValue
function SearchResults({ query }) {
  const deferredQuery = useDeferredValue(query);
  return <Results query={deferredQuery} />;
}

// 3. Suspense with concurrent features
<Suspense fallback={<Loading />}>
  <LazyComponent />
</Suspense>
\`\`\`

**判断并发特性是否生效：**
- 使用React DevTools Profiler查看渲染行为
- 观察用户交互的响应性
- 检查自动批处理是否工作`,
    category: 'concurrent-features',
    difficulty: 'medium',
    tags: ['concurrent', 'features', 'performance'],
    relatedTopics: ['Concurrent Mode', 'Transitions', 'Suspense']
  },

  {
    id: 'cq8',
    question: 'createRoot和旧版React兼容吗？',
    answer: `createRoot是React 18+的特性，兼容性情况：

**版本要求：**
- React 18.0.0+ ✅
- React 17.x ❌
- React 16.x ❌

**向后兼容策略：**
\`\`\`javascript
// 条件使用createRoot
function renderApp() {
  const container = document.getElementById('root');
  
  if (typeof createRoot === 'function') {
    // React 18+
    const root = createRoot(container);
    root.render(<App />);
  } else {
    // React 17及以下
    ReactDOM.render(<App />, container);
  }
}

// 或使用动态导入
async function renderWithFallback() {
  try {
    const { createRoot } = await import('react-dom/client');
    const root = createRoot(container);
    root.render(<App />);
  } catch {
    // 降级到旧API
    const ReactDOM = await import('react-dom');
    ReactDOM.render(<App />, container);
  }
}
\`\`\`

**迁移建议：**
1. 升级到React 18
2. 渐进式替换ReactDOM.render
3. 测试新特性的兼容性
4. 更新TypeScript类型定义`,
    category: 'compatibility',
    difficulty: 'medium',
    tags: ['compatibility', 'migration', 'version'],
    relatedTopics: ['React 18', 'Migration', 'Backward Compatibility']
  }
];

export default commonQuestions; 