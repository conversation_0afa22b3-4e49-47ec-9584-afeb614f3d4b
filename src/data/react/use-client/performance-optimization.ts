import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '内容已完成',
  
  optimizationStrategies: [
    {
      strategy: '推迟客户端边界',
      description: '将use client指令推到组件树的叶子节点，减少客户端组件数量',
      implementation: `// ✅ 好：只有交互部分是客户端组件
export default function ProductPage() {
  return (
    <div>
      <Header />
      <ProductInfo />
      <InteractiveCounter /> {/* 只有这个是客户端组件 */}
    </div>
  );
}`,
      impact: '显著减少JavaScript bundle大小，提升首屏加载速度'
    }
  ],

  benchmarks: [
    {
      scenario: '电商产品页面',
      description: '对比全客户端组件vs混合架构的性能表现',
      metrics: {
        'First Contentful Paint': '1.2s vs 0.8s',
        'JavaScript Bundle Size': '150KB vs 80KB'
      },
      conclusion: '混合架构显著提升首屏加载性能'
    }
  ],

  monitoring: {
    tools: [
      {
        name: '{TOOL_1_NAME}',
        description: '{TOOL_1_DESCRIPTION}',
        usage: `{TOOL_1_USAGE}`
      }
    ],
    
    metrics: [
      {
        metric: '{METRIC_1}',
        description: '{METRIC_1_DESCRIPTION}',
        target: '{METRIC_1_TARGET}',
        measurement: '{METRIC_1_MEASUREMENT}'
      }
    ]
  },

  bestPractices: [
    {
      practice: '{PRACTICE_1}',
      description: '{PRACTICE_1_DESCRIPTION}',
      example: `{PRACTICE_1_EXAMPLE}`
    }
  ]
};

// 占位内容，具体内容请参考 @7-性能优化.mdc
export default performanceOptimization;