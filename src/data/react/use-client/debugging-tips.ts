import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: '调试use client组件时的常见问题和解决方案',
        sections: [
          {
            title: '运行时错误',
            description: '客户端组件运行时常见的错误类型',
            items: [
              {
                title: '忘记添加use client指令',
                description: '在需要客户端功能的组件中忘记添加指令',
                solution: '在文件顶部添加use client指令',
                prevention: '使用ESLint规则检查客户端功能使用',
                code: `// ❌ 错误
import { useState } from 'react';
export default function Counter() {
  const [count, setCount] = useState(0); // 错误！
  return <button>Count: {count}</button>;
}

// ✅ 正确
'use client';
import { useState } from 'react';
export default function Counter() {
  const [count, setCount] = useState(0);
  return <button>Count: {count}</button>;
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '使用开发工具调试use client组件的技巧和方法',
        sections: [
          {
            title: 'React DevTools',
            description: '使用React开发者工具调试客户端组件',
            items: [
              {
                title: '组件边界识别',
                description: '在DevTools中识别哪些组件是客户端组件',
                solution: '查看组件名称旁的标识符',
                prevention: '定期检查组件树结构',
                code: `// 在React DevTools中
// 客户端组件会有特殊标识
// 可以看到组件的渲染环境信息`
              }
            ]
          }
        ]
      }
    }
  ]
};

// 占位内容，具体内容请参考 @8-调试技巧.mdc
export default debuggingTips;