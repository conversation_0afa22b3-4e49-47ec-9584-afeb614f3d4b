import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 1,
    question: "'use client'指令的作用是什么？什么时候需要使用它？",
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: "'use client'用于标记客户端边界，告诉React这个组件需要在客户端运行，支持交互功能和浏览器API",
      detailed: `'use client'是React Server Components架构中的关键指令，用于明确区分服务端和客户端组件。

**主要作用：**
1. **标记客户端边界**：告诉React这个文件及其导出的组件应该在客户端运行
2. **启用交互功能**：允许使用useState、useEffect等需要客户端JavaScript的Hook
3. **浏览器API访问**：可以访问window、document、localStorage等浏览器专有API
4. **第三方库支持**：支持使用需要客户端环境的第三方库

**使用场景：**
- 需要用户交互的组件（按钮点击、表单输入等）
- 需要访问浏览器API的组件（地理位置、摄像头等）
- 使用客户端状态管理的组件
- 集成第三方UI库或动画库的组件

**注意事项：**
- 必须放在文件的最顶部
- 会增加客户端JavaScript包的大小
- 子组件会自动继承客户端边界`,
      code: `// ❌ 错误：服务端组件无法使用交互功能
export default function ServerComponent() {
  const [count, setCount] = useState(0); // 错误！
  return <button onClick={() => setCount(count + 1)}>Click</button>;
}

// ✅ 正确：使用'use client'标记
'use client';

import { useState } from 'react';

export default function ClientComponent() {
  const [count, setCount] = useState(0); // 正确！

  return (
    <button onClick={() => setCount(count + 1)}>
      Count: {count}
    </button>
  );
}

// ✅ 混合使用：服务端组件包含客户端组件
// ServerPage.tsx (服务端组件)
export default async function ServerPage() {
  const data = await fetchData(); // 服务端数据获取

  return (
    <div>
      <h1>{data.title}</h1>
      <ClientComponent /> {/* 客户端交互组件 */}
    </div>
  );
}`
    },
    tags: ['基础概念', 'RSC']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 2,
    question: "'use client'和传统React组件有什么区别？如何优化客户端组件的使用？",
    difficulty: 'medium',
    frequency: 'high',
    category: '架构设计',
    answer: {
      brief: "'use client'组件在RSC架构中运行在客户端，而传统React组件默认在客户端。优化策略包括减少客户端边界、合理拆分组件、避免过度使用",
      detailed: `在React Server Components架构中，'use client'组件与传统React组件的主要区别：

**架构差异：**
1. **执行环境**：'use client'组件在客户端运行，服务端组件在服务器运行
2. **包大小影响**：客户端组件会增加JavaScript包大小
3. **数据获取**：服务端组件可以直接访问数据库，客户端组件需要通过API

**优化策略：**
1. **推迟客户端边界**：将'use client'推到组件树的叶子节点
2. **合理拆分**：将交互逻辑和静态内容分离
3. **减少客户端组件数量**：优先使用服务端组件
4. **避免过度嵌套**：防止整个组件树变成客户端组件`,
      code: `// ❌ 不好：整个页面都是客户端组件
'use client';

export default function ProductPage({ productId }) {
  const [product, setProduct] = useState(null);

  useEffect(() => {
    fetchProduct(productId).then(setProduct);
  }, [productId]);

  return (
    <div>
      <ProductInfo product={product} />
      <ProductReviews productId={productId} />
      <AddToCartButton product={product} />
    </div>
  );
}

// ✅ 好：只有交互部分是客户端组件
// ProductPage.tsx (服务端组件)
export default async function ProductPage({ productId }) {
  const product = await fetchProduct(productId);
  const reviews = await fetchReviews(productId);

  return (
    <div>
      <ProductInfo product={product} />
      <ProductReviews reviews={reviews} />
      <AddToCartButton product={product} /> {/* 客户端组件 */}
    </div>
  );
}

// AddToCartButton.tsx (客户端组件)
'use client';

export default function AddToCartButton({ product }) {
  const [isAdding, setIsAdding] = useState(false);

  const handleAdd = async () => {
    setIsAdding(true);
    await addToCart(product);
    setIsAdding(false);
  };

  return (
    <button onClick={handleAdd} disabled={isAdding}>
      {isAdding ? 'Adding...' : 'Add to Cart'}
    </button>
  );
}`
    },
    tags: ['架构设计', '性能优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 3,
    question: "如何在'use client'组件中处理服务端数据？有哪些最佳实践？",
    difficulty: 'hard',
    frequency: 'medium',
    category: '数据管理',
    answer: {
      brief: "通过props传递服务端数据，使用SWR/React Query管理客户端状态，避免在客户端组件中直接获取服务端数据",
      detailed: `在'use client'组件中处理服务端数据的策略：

**数据传递模式：**
1. **Props传递**：从服务端组件通过props传递初始数据
2. **Context共享**：使用React Context在组件树中共享数据
3. **状态管理**：使用Zustand、Redux等管理全局状态

**最佳实践：**
1. **初始数据服务端获取**：在服务端组件中获取初始数据
2. **客户端增量更新**：使用SWR、React Query等库管理数据同步
3. **避免重复获取**：合理使用缓存策略
4. **错误边界处理**：为数据获取添加错误处理`,
      code: `// ✅ 最佳实践：服务端获取初始数据，客户端管理状态
// UserProfile.tsx (服务端组件)
export default async function UserProfile({ userId }) {
  const initialUser = await fetchUser(userId);

  return (
    <div>
      <UserInfo user={initialUser} />
      <UserSettings initialUser={initialUser} />
    </div>
  );
}

// UserSettings.tsx (客户端组件)
'use client';

import useSWR from 'swr';

export default function UserSettings({ initialUser }) {
  // 使用SWR管理数据，初始数据来自服务端
  const { data: user, mutate } = useSWR(
    \`/api/users/\${initialUser.id}\`,
    fetcher,
    { fallbackData: initialUser }
  );

  const updateUser = async (updates) => {
    // 乐观更新
    mutate({ ...user, ...updates }, false);

    try {
      const updated = await fetch(\`/api/users/\${user.id}\`, {
        method: 'PATCH',
        body: JSON.stringify(updates)
      }).then(res => res.json());

      // 确认更新
      mutate(updated);
    } catch (error) {
      // 回滚
      mutate(user);
      throw error;
    }
  };

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      const formData = new FormData(e.target);
      updateUser(Object.fromEntries(formData));
    }}>
      <input name="name" defaultValue={user.name} />
      <button type="submit">Update</button>
    </form>
  );
}`
    },
    tags: ['数据管理', '状态同步']
  }
];

// 占位内容，具体内容请参考 @4-面试准备.mdc
export default interviewQuestions;