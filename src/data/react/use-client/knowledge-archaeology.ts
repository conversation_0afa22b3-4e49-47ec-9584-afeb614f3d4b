import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '内容已完成',
  
  introduction: `探索'use client'指令的历史背景和技术演进，理解其在React生态系统中的重要地位。`,

  background: `React Server Components于2020年首次提出，'use client'指令是RSC架构的核心组成部分，用于标记客户端边界。`,

  evolution: `从传统的客户端渲染(CSR)到服务端渲染(SSR)，再到React Server Components，'use client'代表了Web开发范式的重要转变。`,

  timeline: [
    {
      year: '2020',
      event: 'React Server Components RFC发布',
      description: 'React团队首次提出Server Components概念',
      significance: '标志着React架构的重大转变'
    },
    {
      year: '2022',
      event: 'Next.js 13 App Router发布',
      description: '首个生产级RSC实现，包含use client指令',
      significance: '使RSC从概念变为现实'
    }
  ],

  keyFigures: [
    {
      name: '{NAME_1}',
      role: '{ROLE_1}',
      contribution: '{CONTRIBUTION_1}',
      significance: '{SIGNIFICANCE_1}'
    }
  ],

  concepts: [
    {
      term: '{TERM_1}',
      definition: '{DEFINITION_1}',
      evolution: '{EVOLUTION_1}',
      modernRelevance: '{MODERN_RELEVANCE_1}'
    }
  ],

  designPhilosophy: `'use client'体现了React团队对性能和用户体验的深度思考，代表了从"默认客户端"到"默认服务端"的范式转变。`,

  impact: `'use client'指令重新定义了现代Web应用的架构模式，影响了整个React生态系统的发展方向。`,

  modernRelevance: `在当今注重性能和SEO的Web环境中，'use client'提供了在服务端渲染和客户端交互之间的完美平衡。`
};

// 占位内容，具体内容请参考 @6-知识考古.mdc
export default knowledgeArchaeology;