import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-1',
    title: '电商产品页面交互组件',
    description: '在电商网站的产品详情页中，使用"use client"创建交互式的购买组件，包括数量选择、规格选择、购物车操作等功能',
    businessValue: '提升用户购买体验，增加转化率，减少购买流程中的摩擦，提供即时反馈和动态价格计算',
    scenario: '用户浏览产品页面时，需要选择商品规格、调整购买数量、查看实时价格变化，并能够快速添加到购物车。这些交互功能需要客户端JavaScript支持。',
    code: `// ProductPage.tsx (服务端组件)
import ProductInfo from './ProductInfo'; // 服务端组件
import ProductInteraction from './ProductInteraction'; // 客户端组件

export default async function ProductPage({ productId }: { productId: string }) {
  // 在服务端获取产品数据
  const product = await fetchProduct(productId);
  const reviews = await fetchReviews(productId);

  return (
    <div className="product-page">
      {/* 服务端渲染的静态内容 */}
      <ProductInfo product={product} />

      {/* 客户端交互组件 */}
      <ProductInteraction product={product} />

      {/* 服务端渲染的评论列表 */}
      <ReviewList reviews={reviews} />
    </div>
  );
}

// ProductInteraction.tsx (客户端组件)
'use client';

import { useState, useEffect } from 'react';
import { useCart } from '@/hooks/useCart';

interface Product {
  id: string;
  name: string;
  price: number;
  variants: Array<{
    id: string;
    name: string;
    price: number;
    stock: number;
  }>;
}

export default function ProductInteraction({ product }: { product: Product }) {
  const [selectedVariant, setSelectedVariant] = useState(product.variants[0]);
  const [quantity, setQuantity] = useState(1);
  const [isAdding, setIsAdding] = useState(false);
  const { addToCart, cartCount } = useCart();

  // 计算总价
  const totalPrice = selectedVariant.price * quantity;

  // 添加到购物车
  const handleAddToCart = async () => {
    setIsAdding(true);
    try {
      await addToCart({
        productId: product.id,
        variantId: selectedVariant.id,
        quantity
      });

      // 显示成功提示
      showToast('商品已添加到购物车！');
    } catch (error) {
      showToast('添加失败，请重试', 'error');
    } finally {
      setIsAdding(false);
    }
  };

  // 库存检查
  const isOutOfStock = selectedVariant.stock === 0;
  const maxQuantity = Math.min(selectedVariant.stock, 10);

  return (
    <div className="product-interaction">
      {/* 规格选择 */}
      <div className="variant-selector">
        <h3>选择规格</h3>
        <div className="variant-options">
          {product.variants.map(variant => (
            <button
              key={variant.id}
              className={\`variant-option \${
                selectedVariant.id === variant.id ? 'selected' : ''
              } \${variant.stock === 0 ? 'out-of-stock' : ''}\`}
              onClick={() => setSelectedVariant(variant)}
              disabled={variant.stock === 0}
            >
              {variant.name}
              {variant.stock === 0 && <span className="stock-label">缺货</span>}
            </button>
          ))}
        </div>
      </div>

      {/* 数量选择 */}
      <div className="quantity-selector">
        <h3>购买数量</h3>
        <div className="quantity-controls">
          <button
            onClick={() => setQuantity(Math.max(1, quantity - 1))}
            disabled={quantity <= 1}
          >
            -
          </button>
          <input
            type="number"
            value={quantity}
            onChange={(e) => {
              const value = parseInt(e.target.value) || 1;
              setQuantity(Math.min(maxQuantity, Math.max(1, value)));
            }}
            min={1}
            max={maxQuantity}
          />
          <button
            onClick={() => setQuantity(Math.min(maxQuantity, quantity + 1))}
            disabled={quantity >= maxQuantity}
          >
            +
          </button>
        </div>
        <p className="stock-info">库存: {selectedVariant.stock} 件</p>
      </div>

      {/* 价格显示 */}
      <div className="price-display">
        <div className="unit-price">
          单价: ¥{selectedVariant.price.toFixed(2)}
        </div>
        <div className="total-price">
          总价: ¥{totalPrice.toFixed(2)}
        </div>
      </div>

      {/* 购买按钮 */}
      <div className="purchase-actions">
        <button
          className="add-to-cart-btn"
          onClick={handleAddToCart}
          disabled={isOutOfStock || isAdding}
        >
          {isAdding ? '添加中...' : isOutOfStock ? '暂时缺货' : '加入购物车'}
        </button>

        <button
          className="buy-now-btn"
          disabled={isOutOfStock}
          onClick={() => {
            // 立即购买逻辑
            window.location.href = \`/checkout?product=\${product.id}&variant=\${selectedVariant.id}&quantity=\${quantity}\`;
          }}
        >
          立即购买
        </button>
      </div>

      {/* 购物车状态 */}
      <div className="cart-status">
        购物车中有 {cartCount} 件商品
      </div>
    </div>
  );
}

// useCart.ts (自定义Hook)
'use client';

import { useState, useEffect } from 'react';

export function useCart() {
  const [cartCount, setCartCount] = useState(0);

  useEffect(() => {
    // 从localStorage读取购物车数据
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      const cart = JSON.parse(savedCart);
      setCartCount(cart.items?.length || 0);
    }
  }, []);

  const addToCart = async (item: any) => {
    // 添加到购物车的逻辑
    const savedCart = localStorage.getItem('cart');
    const cart = savedCart ? JSON.parse(savedCart) : { items: [] };

    cart.items.push(item);
    localStorage.setItem('cart', JSON.stringify(cart));
    setCartCount(cart.items.length);

    // 发送到服务端同步
    await fetch('/api/cart', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(item)
    });
  };

  return { cartCount, addToCart };
}`,
    explanation: '通过将交互功能封装在"use client"组件中，实现了服务端渲染的性能优势和客户端交互的用户体验。服务端组件负责数据获取和SEO内容，客户端组件处理用户交互和状态管理。',
    benefits: [
      'SEO优化：产品信息在服务端渲染，搜索引擎可以完整抓取',
      '性能提升：静态内容快速加载，交互功能按需激活',
      '用户体验：实时价格计算、库存检查、购物车状态更新'
    ],
    metrics: {
      performance: '首屏加载时间减少40%，交互响应时间<100ms',
      userExperience: '转化率提升25%，购物车放弃率降低15%',
      technicalMetrics: 'JavaScript包大小减少30%，SEO评分提升20%'
    },
    difficulty: 'easy',
    tags: ['电商', '交互组件', 'RSC']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-2',
    title: '实时数据仪表板',
    description: '构建企业级实时数据仪表板，使用"use client"处理WebSocket连接、图表渲染和用户交互',
    businessValue: '提供实时业务洞察，支持快速决策，提升运营效率，降低响应时间',
    scenario: '企业管理者需要实时监控销售数据、用户活跃度、系统性能等关键指标，要求数据实时更新、支持交互式筛选和钻取分析。',
    code: `// Dashboard.tsx (服务端组件)
export default async function Dashboard() {
  const initialData = await fetchDashboardData();

  return (
    <div className="dashboard">
      <DashboardHeader />
      <RealTimeCharts initialData={initialData} />
      <StaticReports />
    </div>
  );
}

// RealTimeCharts.tsx (客户端组件)
'use client';

import { useState, useEffect } from 'react';
import { LineChart, BarChart } from 'recharts';

export default function RealTimeCharts({ initialData }) {
  const [data, setData] = useState(initialData);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const ws = new WebSocket('wss://api.example.com/realtime');

    ws.onopen = () => setIsConnected(true);
    ws.onmessage = (event) => {
      const newData = JSON.parse(event.data);
      setData(prev => ({ ...prev, ...newData }));
    };

    return () => ws.close();
  }, []);

  return (
    <div className="charts-container">
      <div className="connection-status">
        {isConnected ? '🟢 实时连接' : '🔴 连接中断'}
      </div>

      <LineChart data={data.timeSeries} width={800} height={400}>
        {/* 图表配置 */}
      </LineChart>

      <BarChart data={data.categories} width={600} height={300}>
        {/* 图表配置 */}
      </BarChart>
    </div>
  );
}`,
    explanation: '通过"use client"标记实时图表组件，使其能够建立WebSocket连接和处理动态数据更新，同时保持服务端组件的SEO和性能优势。',
    benefits: [
      '实时数据更新：WebSocket连接提供毫秒级数据同步',
      '交互式分析：支持图表缩放、筛选、钻取等操作',
      '性能优化：静态内容服务端渲染，动态内容客户端处理',
      '用户体验：连接状态提示，数据加载状态管理'
    ],
    metrics: {
      performance: '数据更新延迟<500ms，图表渲染时间<200ms',
      userExperience: '用户停留时间增加60%，决策效率提升40%',
      technicalMetrics: 'WebSocket连接稳定性99.9%，内存使用优化30%'
    },
    difficulty: 'medium',
    tags: ['实时数据', 'WebSocket', '仪表板']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-3',
    title: '多媒体内容编辑器',
    description: '构建富文本编辑器，集成摄像头、麦克风、文件上传等浏览器API，支持实时协作和媒体处理',
    businessValue: '提升内容创作效率，支持多媒体内容制作，增强用户参与度，降低内容制作门槛',
    scenario: '内容创作平台需要提供强大的编辑器，支持文本、图片、视频、音频等多种媒体类型，用户可以直接在浏览器中录制、编辑和发布内容。',
    code: `// ContentEditor.tsx (服务端组件)
export default async function ContentEditor({ documentId }) {
  const document = await fetchDocument(documentId);

  return (
    <div className="editor-container">
      <EditorToolbar />
      <MediaEditor initialContent={document.content} />
      <CollaborationPanel documentId={documentId} />
    </div>
  );
}

// MediaEditor.tsx (客户端组件)
'use client';

import { useState, useRef, useEffect } from 'react';

export default function MediaEditor({ initialContent }) {
  const [content, setContent] = useState(initialContent);
  const [isRecording, setIsRecording] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);

  // 摄像头访问
  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (error) {
      console.error('摄像头访问失败:', error);
    }
  };

  // 录制功能
  const startRecording = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({
      video: true,
      audio: true
    });

    const mediaRecorder = new MediaRecorder(stream);
    mediaRecorderRef.current = mediaRecorder;

    const chunks: BlobPart[] = [];
    mediaRecorder.ondataavailable = (event) => {
      chunks.push(event.data);
    };

    mediaRecorder.onstop = () => {
      const blob = new Blob(chunks, { type: 'video/webm' });
      const url = URL.createObjectURL(blob);
      insertMedia(url, 'video');
    };

    mediaRecorder.start();
    setIsRecording(true);
  };

  // 文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach(file => {
        const url = URL.createObjectURL(file);
        const type = file.type.startsWith('image/') ? 'image' :
                    file.type.startsWith('video/') ? 'video' : 'file';
        insertMedia(url, type);
      });
    }
  };

  // 插入媒体内容
  const insertMedia = (url: string, type: string) => {
    const mediaElement = {
      id: Date.now().toString(),
      type,
      url,
      timestamp: new Date().toISOString()
    };

    setContent(prev => ({
      ...prev,
      elements: [...prev.elements, mediaElement]
    }));
  };

  return (
    <div className="media-editor">
      {/* 工具栏 */}
      <div className="toolbar">
        <button onClick={startCamera}>📷 开启摄像头</button>
        <button
          onClick={isRecording ? stopRecording : startRecording}
          className={isRecording ? 'recording' : ''}
        >
          {isRecording ? '⏹️ 停止录制' : '🎥 开始录制'}
        </button>

        <label className="file-upload">
          📁 上传文件
          <input
            type="file"
            multiple
            accept="image/*,video/*,audio/*"
            onChange={handleFileUpload}
            style={{ display: 'none' }}
          />
        </label>
      </div>

      {/* 预览区域 */}
      <div className="preview-area">
        <video
          ref={videoRef}
          autoPlay
          muted
          style={{ display: isRecording ? 'block' : 'none' }}
        />
      </div>

      {/* 内容编辑区 */}
      <div className="content-area">
        {content.elements.map(element => (
          <MediaElement
            key={element.id}
            element={element}
            onUpdate={(updated) => updateElement(element.id, updated)}
            onDelete={() => deleteElement(element.id)}
          />
        ))}
      </div>
    </div>
  );
}`,
    explanation: '通过"use client"标记媒体编辑器组件，使其能够访问摄像头、麦克风等浏览器API，同时保持服务端组件处理文档数据和协作功能。',
    benefits: [
      '浏览器API访问：直接使用摄像头、麦克风、文件系统API',
      '实时媒体处理：支持录制、预览、编辑等功能',
      '用户体验优化：无需插件，原生浏览器支持',
      '性能优化：媒体处理在客户端，减少服务器负载',
      '功能丰富：支持多种媒体格式和编辑操作'
    ],
    metrics: {
      performance: '媒体录制延迟<100ms，文件上传速度提升50%',
      userExperience: '内容创作效率提升70%，用户满意度提升45%',
      technicalMetrics: '浏览器兼容性95%，媒体处理成功率99%'
    },
    difficulty: 'hard',
    tags: ['媒体编辑', '浏览器API', '实时协作', '文件处理']
  }
];

export default businessScenarios;