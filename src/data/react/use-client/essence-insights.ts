import { EssenceInsights } from '@/types/api';

const essenceInsights: EssenceInsights = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  coreQuestion: `如何在保持服务端渲染优势的同时，实现必要的客户端交互？`,

  designPhilosophy: {
    worldview: `React Server Components代表了Web开发的范式转变：从"默认客户端"回归到"默认服务端"`,
    methodology: `渐进增强：从服务端组件开始，只在必要时添加客户端功能`,
    tradeoffs: `在SEO、首屏加载速度和交互性之间找到最佳平衡点`,
    evolution: `从传统SPA到RSC，体现了对性能和用户体验的更深层理解`
  },

  hiddenTruth: {
    surfaceProblem: `开发者认为'use client'只是一个简单的指令`,
    realProblem: `'use client'实际上定义了应用的架构边界和性能特征`,
    hiddenCost: `过度使用客户端组件会抵消RSC的所有优势`,
    deeperValue: `正确使用'use client'能够实现最佳的用户体验和开发体验`
  },

  deeperQuestions: [],

  paradigmShift: {
    oldParadigm: {
      assumption: `所有React组件都应该在客户端运行`,
      limitation: `导致大量JavaScript下载和执行，影响性能`,
      worldview: `客户端渲染是现代Web应用的标准`
    },
    newParadigm: {
      breakthrough: `服务端组件为默认，客户端组件按需使用`,
      possibility: `实现更好的性能、SEO和用户体验`,
      cost: `需要重新思考组件架构和数据流`
    },
    transition: {
      resistance: `开发者习惯了客户端思维模式`,
      catalyst: `性能问题和SEO需求的压力`,
      tippingPoint: `Next.js App Router的广泛采用`
    }
  },

  universalPrinciples: [
    '边界清晰：明确区分服务端和客户端职责',
    '渐进增强：从服务端开始，按需添加客户端功能',
    '性能优先：优化用户体验而不是开发便利性'
  ]
};

// 占位内容，具体内容请参考 @9-本质洞察.mdc
export default essenceInsights;