import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: "'use client'是React Server Components中用于标记客户端边界的指令",

  introduction: `'use client'是React 18引入的指令，主要用于React Server Components架构中标记客户端边界、启用交互功能和访问浏览器API。它采用文件级指令的设计模式，提供了服务端和客户端代码的清晰分离。`,

  syntax: `'use client';

// 文件顶部声明，标记整个文件为客户端组件
import React from 'react';

export default function ClientComponent() {
  // 可以使用客户端特性
}`,

  quickExample: `'use client';

import { useState } from 'react';

export default function InteractiveButton() {
  // 使用客户端状态 - 只有在'use client'文件中才能使用
  const [count, setCount] = useState(0);

  return (
    <div>
      {/* 交互式组件 - 需要客户端JavaScript */}
      <button onClick={() => setCount(count + 1)}>
        点击次数: {count}
      </button>
      <p>这是一个客户端组件，可以处理用户交互</p>
    </div>
  );
}`,

  scenarioDiagram: `graph TD
    A['use client'应用场景] --> B[交互式组件]
    A --> C[浏览器API访问]
    A --> D[第三方库集成]

    B --> B1[表单处理]
    B --> B2[状态管理]
    B --> B3[事件处理]

    C --> C1[localStorage访问]
    C --> C2[地理位置API]
    C --> C3[摄像头/麦克风]

    D --> D1[图表库集成]
    D --> D2[动画库使用]
    D --> D3[支付SDK集成]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0`,
  
  parameters: [
    {
      name: "无参数",
      type: "指令",
      required: true,
      description: "'use client'是一个文件级指令，不接受任何参数，必须放在文件的最顶部",
      details: "作为字符串字面量出现在文件开头，告诉React这个文件及其导出的组件应该在客户端运行"
    }
  ],

  returnValue: {
    type: "void",
    description: "'use client'不返回任何值，它是一个编译时指令，用于标记文件的执行环境"
  },

  keyFeatures: [
    {
      feature: "客户端边界标记",
      description: "明确标记哪些组件需要在客户端运行",
      importance: "high" as const,
      details: "为React Server Components提供清晰的服务端/客户端分界线"
    },
    {
      feature: "交互功能启用",
      description: "允许使用useState、useEffect等客户端Hook",
      importance: "high" as const,
      details: "只有在'use client'文件中才能使用需要客户端JavaScript的功能"
    },
    {
      feature: "浏览器API访问",
      description: "可以访问window、document、localStorage等浏览器专有API",
      importance: "medium" as const,
      details: "服务端组件无法访问浏览器API，需要'use client'标记"
    },
    {
      feature: "第三方库兼容",
      description: "支持使用需要客户端环境的第三方库",
      importance: "medium" as const,
      details: "许多UI库、动画库需要在客户端环境中运行"
    }
  ],

  limitations: [
    "必须放在文件的最顶部，在所有import语句之前",
    "会增加客户端JavaScript包的大小",
    "标记的组件无法访问服务端专有功能（如数据库、文件系统）",
    "子组件会自动继承客户端边界，无法在其中使用服务端组件"
  ],
  
  bestPractices: [
    "尽量减少'use client'组件的数量，优先使用服务端组件",
    "将'use client'边界推到组件树的叶子节点",
    "避免在'use client'组件中进行大量数据获取",
    "合理拆分客户端组件，避免单个组件过于庞大",
    "使用TypeScript确保客户端/服务端边界的类型安全"
  ],

  warnings: [
    "不要忘记在文件顶部添加'use client'指令",
    "客户端组件会增加JavaScript包大小，影响首屏加载",
    "避免在客户端组件中直接访问服务端资源（数据库、文件系统等）"
  ]
};

export default basicInfo;