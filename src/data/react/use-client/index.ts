import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useClientData: ApiItem = {
  id: 'use-client',
  title: '"use client"',
  description: 'React Server Components指令，用于标记客户端边界，启用交互功能和浏览器API访问',
  category: 'React Directives',
  difficulty: 'medium',

  syntax: `'use client';

export default function ClientComponent() {
  // 可以使用客户端特性
}`,
  example: `'use client';

import { useState } from 'react';

export default function Counter() {
  const [count, setCount] = useState(0);

  return (
    <button onClick={() => setCount(count + 1)}>
      Count: {count}
    </button>
  );
}`,
  notes: '必须放在文件顶部，标记整个文件为客户端组件，支持交互和浏览器API',

  version: 'React 18.0.0+',
  tags: ["React", "RSC", "Directive", "Client"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useClientData;