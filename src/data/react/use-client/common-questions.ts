import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-1',
    question: "忘记添加'use client'指令会发生什么？",
    answer: `如果在需要客户端功能的组件中忘记添加'use client'指令，React会抛出错误。常见错误包括：
- 使用useState、useEffect等Hook时报错
- 访问window、document等浏览器API时报错
- 事件处理器无法正常工作

解决方案是在文件顶部添加'use client'指令。`,
    code: `// ❌ 错误：忘记添加'use client'
import { useState } from 'react';

export default function Counter() {
  const [count, setCount] = useState(0); // 错误！
  return <button onClick={() => setCount(count + 1)}>Count: {count}</button>;
}

// ✅ 正确：添加'use client'指令
'use client';

import { useState } from 'react';

export default function Counter() {
  const [count, setCount] = useState(0); // 正确！
  return <button onClick={() => setCount(count + 1)}>Count: {count}</button>;
}`,
    tags: ['错误处理', '调试'],
    relatedQuestions: ['如何调试RSC问题？', '什么时候需要use client？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-2',
    question: "如何在'use client'组件中优化性能？",
    answer: `优化'use client'组件性能的关键策略：

1. **减少客户端组件数量**：只在必要时使用'use client'
2. **代码分割**：使用动态导入延迟加载客户端组件
3. **状态管理优化**：避免不必要的重渲染
4. **缓存策略**：合理使用React.memo、useMemo、useCallback

最重要的是将'use client'边界推到组件树的叶子节点。`,
    code: `// ❌ 不好：整个页面都是客户端组件
'use client';
export default function Page() {
  const [count, setCount] = useState(0);
  return (
    <div>
      <Header />
      <StaticContent />
      <Counter count={count} setCount={setCount} />
    </div>
  );
}

// ✅ 好：只有交互部分是客户端组件
export default function Page() {
  return (
    <div>
      <Header />
      <StaticContent />
      <InteractiveCounter /> {/* 只有这个是客户端组件 */}
    </div>
  );
}

// InteractiveCounter.tsx
'use client';
export default function InteractiveCounter() {
  const [count, setCount] = useState(0);
  return <button onClick={() => setCount(count + 1)}>Count: {count}</button>;
}`,
    tags: ['性能优化', '最佳实践'],
    relatedQuestions: ['什么时候需要use client？', '如何调试RSC问题？']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',

    id: 'question-3',
    question: "'use client'组件可以导入服务端组件吗？",
    answer: `不可以。这是React Server Components的重要限制：

**规则**：
- 服务端组件可以导入和渲染客户端组件
- 客户端组件不能直接导入服务端组件
- 客户端组件只能通过props接收服务端组件作为children

**解决方案**：
1. 通过props传递数据而不是组件
2. 使用children模式传递服务端组件
3. 重新设计组件架构`,
    code: `// ❌ 错误：客户端组件不能导入服务端组件
'use client';
import ServerComponent from './ServerComponent'; // 错误！

export default function ClientComponent() {
  return <ServerComponent />; // 这会报错
}

// ✅ 正确：通过children传递服务端组件
'use client';
export default function ClientComponent({ children }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div>
      <button onClick={() => setIsOpen(!isOpen)}>
        Toggle
      </button>
      {isOpen && children} {/* 服务端组件作为children */}
    </div>
  );
}

// 在父组件中使用
export default function ParentPage() {
  return (
    <ClientComponent>
      <ServerComponent /> {/* 服务端组件 */}
    </ClientComponent>
  );
}`,
    tags: ['组件架构', '限制规则'],
    relatedQuestions: ['如何在客户端组件中使用服务端数据？', 'RSC的边界规则是什么？']
  }
];

// 占位内容，具体内容请参考 @5-常见问题.mdc
export default commonQuestions;