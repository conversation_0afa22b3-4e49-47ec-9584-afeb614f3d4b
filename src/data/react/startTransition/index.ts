import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const startTransitionData: ApiItem = {
  id: 'startTransition',
  title: 'startTransition',
  description: 'startTransition是React 18中用于标记非紧急状态更新的函数，专门用于优化用户体验，解决了大量状态更新阻塞用户交互的问题，其核心优势是让紧急更新优先执行。',
  category: 'React APIs',
  difficulty: 'medium',

  syntax: `function startTransition(callback: () => void): void;`,
  example: `function SearchExample() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);

  const handleSearch = (value) => {
    setQuery(value);
    startTransition(() => {
      const filteredResults = performExpensiveSearch(value);
      setResults(filteredResults);
    });
  };

  return (
    <div>
      <input value={query} onChange={(e) => handleSearch(e.target.value)} />
      <div>{results.map(item => <div key={item.id}>{item.name}</div>)}</div>
    </div>
  );
}`,
  notes: '只能在React 18+版本中使用并发特性，在旧版本中会降级为同步更新',

  version: 'React 18.0.0+',
  tags: ["并发特性", "性能优化", "用户体验", "优先级调度", "过渡更新"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default startTransitionData;