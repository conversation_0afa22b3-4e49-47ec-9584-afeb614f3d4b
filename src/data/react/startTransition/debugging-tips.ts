import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'startTransition虽然强大，但在实际使用中开发者经常遇到一些典型问题。本节提供完整的问题诊断和解决方案，帮助快速定位和修复startTransition相关的技术问题。',
        sections: [
          {
            title: 'startTransition无效果问题',
            description: '最常见的问题是startTransition看起来没有任何效果，界面依然卡顿。这通常涉及版本、配置、使用方式等多个方面的问题',
            items: [
              {
                title: 'React版本不支持并发特性',
                description: 'startTransition需要React 18+版本才能发挥作用，在旧版本中会被忽略',
                solution: '1. 检查React版本是否为18+；2. 升级到React 18或更高版本；3. 确保使用createRoot而不是ReactDOM.render',
                prevention: '在项目开始时就确定React版本，建立版本检查机制',
                code: `// 检查React版本
import React from 'react';
console.log('React version:', React.version);

// 正确的根节点配置
import { createRoot } from 'react-dom/client';

// ✅ 正确：使用createRoot启用并发特性
const root = createRoot(document.getElementById('root'));
root.render(<App />);

// ❌ 错误：使用旧的render方法
// ReactDOM.render(<App />, document.getElementById('root'));

// 版本检查工具函数
function checkConcurrentSupport() {
  const version = React.version;
  const majorVersion = parseInt(version.split('.')[0]);

  if (majorVersion < 18) {
    console.warn('startTransition需要React 18+版本才能发挥作用');
    return false;
  }

  // 检查是否使用了createRoot
  const rootElement = document.getElementById('root');
  const hasReactRoot = rootElement._reactRootContainer === undefined;

  if (!hasReactRoot) {
    console.warn('请使用createRoot而不是ReactDOM.render来启用并发特性');
    return false;
  }

  return true;
}`
              },
              {
                title: '任务太简单无法体现效果',
                description: '如果包装在startTransition中的操作本身很快，优化效果不明显',
                solution: '1. 只在真正耗时的操作中使用startTransition；2. 使用性能分析工具验证效果；3. 考虑是否需要人工增加复杂度来测试',
                prevention: '建立性能基准测试，明确哪些操作需要优化',
                code: `// 性能测试工具
function measurePerformance(name, fn) {
  const start = performance.now();
  fn();
  const end = performance.now();
  console.log(name + ' took ' + (end - start) + ' milliseconds.');
}

// ❌ 不需要startTransition的简单操作
measurePerformance('Simple update', () => {
  setCount(count + 1); // 通常 < 1ms，不需要优化
});

// ✅ 需要startTransition的复杂操作
measurePerformance('Complex update', () => {
  startTransition(() => {
    // 复杂数据处理，通常 > 50ms
    const processedData = largeDataSet.map(item => ({
      ...item,
      computed: expensiveCalculation(item)
    }));
    setData(processedData);
  });
});

// 自动判断是否需要startTransition
function smartUpdate(updateFn) {
  const start = performance.now();

  // 先测试一次执行时间
  const testResult = updateFn();
  const duration = performance.now() - start;

  if (duration > 50) {
    console.log('检测到耗时操作，建议使用startTransition');
    startTransition(updateFn);
  } else {
    console.log('操作较快，直接执行');
    updateFn();
  }
}`
              }
            ]
          },
          {
            title: '异步操作相关问题',
            description: 'startTransition与异步操作的配合使用经常出现问题，需要正确理解其工作机制',
            items: [
              {
                title: '在startTransition中使用异步函数',
                description: 'startTransition的回调必须是同步的，不能包含async/await或Promise',
                solution: '1. 将异步操作移到startTransition外部；2. 在异步操作完成后再调用startTransition；3. 使用useEffect处理异步逻辑',
                prevention: '建立代码审查规则，检查startTransition中的异步操作',
                code: `// ❌ 错误：异步回调
startTransition(async () => {
  const data = await fetchData(); // 这样不行
  setData(data);
});

// ✅ 正确：异步操作在外部
const handleAsyncUpdate = async () => {
  try {
    const data = await fetchData();

    // 异步操作完成后使用startTransition
    startTransition(() => {
      setData(data);
      setLoading(false);
    });
  } catch (error) {
    setError(error);
  }
};

// ✅ 正确：使用useEffect处理异步
useEffect(() => {
  let cancelled = false;

  const fetchAndUpdate = async () => {
    try {
      const data = await fetchData();

      if (!cancelled) {
        startTransition(() => {
          setData(data);
        });
      }
    } catch (error) {
      if (!cancelled) {
        setError(error);
      }
    }
  };

  fetchAndUpdate();

  return () => {
    cancelled = true;
  };
}, [dependency]);`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: 'React提供了强大的开发工具来帮助调试和优化startTransition的使用。掌握这些工具可以大大提高开发效率和问题解决能力。',
        sections: [
          {
            title: 'React DevTools Profiler',
            description: 'React官方的性能分析工具，可以直观地看到startTransition的效果和组件渲染性能',
            items: [
              {
                title: '使用Profiler监控过渡更新',
                description: 'React DevTools Profiler可以显示哪些更新被标记为过渡更新，以及它们的执行时间',
                solution: '1. 安装React DevTools浏览器扩展；2. 在Profiler标签页中录制性能；3. 查看过渡更新的标记和时间',
                prevention: '定期使用Profiler检查应用性能，建立性能监控习惯',
                code: `// 在代码中添加Profiler组件进行监控
import { Profiler } from 'react';

function App() {
  const onRenderCallback = (id, phase, actualDuration, baseDuration, startTime, commitTime, interactions) => {
    console.log('Profiler数据:', {
      id,                    // Profiler树的id
      phase,                 // "mount" 或 "update"
      actualDuration,        // 本次更新花费的时间
      baseDuration,          // 不使用memoization的情况下渲染整棵子树需要的时间
      startTime,             // 本次更新开始时间
      commitTime,            // 本次更新提交时间
      interactions           // 本次更新的交互集合
    });

    // 检测是否为过渡更新
    if (interactions.size > 0) {
      interactions.forEach(interaction => {
        if (interaction.name.includes('transition')) {
          console.log('检测到过渡更新:', interaction);
        }
      });
    }
  };

  return (
    <Profiler id="App" onRender={onRenderCallback}>
      <YourComponent />
    </Profiler>
  );
}

// 自定义Hook监控startTransition性能
function useTransitionProfiler() {
  const [metrics, setMetrics] = useState({
    transitionCount: 0,
    averageDuration: 0,
    lastTransitionTime: null
  });

  const profiledStartTransition = useCallback((callback) => {
    const startTime = performance.now();

    startTransition(() => {
      callback();

      // 异步测量完成时间
      setTimeout(() => {
        const endTime = performance.now();
        const duration = endTime - startTime;

        setMetrics(prev => ({
          transitionCount: prev.transitionCount + 1,
          averageDuration: (prev.averageDuration * prev.transitionCount + duration) / (prev.transitionCount + 1),
          lastTransitionTime: duration
        }));
      }, 0);
    });
  }, []);

  return { metrics, startTransition: profiledStartTransition };
}`
              },
              {
                title: '使用Console调试过渡状态',
                description: '通过控制台输出来跟踪startTransition的执行状态和效果',
                solution: '1. 添加console.log跟踪执行流程；2. 使用console.time测量执行时间；3. 监控状态变化和渲染次数',
                prevention: '建立统一的调试日志格式，便于问题排查',
                code: `// 调试工具函数
function debugStartTransition(name, callback) {
  console.group('🔄 StartTransition Debug:', name);
  console.time('Transition Duration');

  const startTime = performance.now();
  console.log('开始时间:', new Date().toISOString());

  startTransition(() => {
    console.log('执行过渡更新...');

    try {
      callback();
      console.log('✅ 过渡更新成功');
    } catch (error) {
      console.error('❌ 过渡更新失败:', error);
    }

    // 异步记录完成时间
    setTimeout(() => {
      const endTime = performance.now();
      console.log('完成时间:', new Date().toISOString());
      console.log('总耗时:', endTime - startTime, 'ms');
      console.timeEnd('Transition Duration');
      console.groupEnd();
    }, 0);
  });
}

// 使用示例
const handleSearch = (query) => {
  setSearchQuery(query); // 立即更新

  debugStartTransition('搜索过滤', () => {
    const results = performSearch(query);
    setSearchResults(results);
  });
};

// 状态变化监控
function useStateChangeMonitor(stateName, state) {
  const prevState = useRef(state);

  useEffect(() => {
    if (prevState.current !== state) {
      console.log('状态变化 [' + stateName + ']:', {
        from: prevState.current,
        to: state,
        timestamp: new Date().toISOString()
      });
      prevState.current = state;
    }
  }, [state, stateName]);
}

// 在组件中使用
function SearchComponent() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);

  // 监控状态变化
  useStateChangeMonitor('searchQuery', query);
  useStateChangeMonitor('searchResults', results);

  return (
    // ... 组件内容
  );
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;