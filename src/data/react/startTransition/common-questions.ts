import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-1',
    question: 'startTransition中的回调函数可以是异步的吗？',
    answer: `不可以。startTransition的回调函数必须是同步的，不能包含async/await、Promise等异步操作。

**原因：**
1. React需要在同步执行期间收集所有的状态更新
2. 异步操作会导致状态更新无法被正确标记为过渡更新
3. 异步操作的时机无法被React的调度器控制

**正确做法：**
如果需要异步操作，应该在startTransition外部处理异步逻辑，然后在回调中只进行状态更新。`,
    code: `// ❌ 错误：异步回调
startTransition(async () => {
  const data = await fetchData(); // 这样不行
  setData(data);
});

// ✅ 正确：异步操作在外部
const handleUpdate = async () => {
  const data = await fetchData(); // 异步操作在外部

  startTransition(() => {
    setData(data); // 只在回调中进行状态更新
  });
};

// ✅ 正确：使用useEffect处理异步
useEffect(() => {
  const fetchAndUpdate = async () => {
    const data = await fetchData();

    startTransition(() => {
      setData(data);
    });
  };

  fetchAndUpdate();
}, [dependency]);`,
    tags: ['异步处理', '常见错误'],
    relatedQuestions: ['如何在startTransition中处理Promise？', 'startTransition和useEffect的配合使用']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-2',
    question: '为什么我的startTransition没有效果，界面还是卡顿？',
    answer: `startTransition没有效果通常有以下几个原因：

**1. React版本问题：**
- 需要React 18+版本才支持并发特性
- 在旧版本中会降级为同步更新

**2. 根节点配置问题：**
- 必须使用createRoot而不是ReactDOM.render
- 只有并发模式下startTransition才有效果

**3. 更新类型问题：**
- 只有状态更新才会被标记为过渡更新
- DOM操作、副作用等不会被优化

**4. 任务太小：**
- 如果任务本身很快，优化效果不明显
- 需要真正耗时的操作才能看到效果`,
    code: `// ✅ 确保使用正确的根节点配置
import { createRoot } from 'react-dom/client';

const root = createRoot(document.getElementById('root'));
root.render(<App />);

// ✅ 确保任务足够复杂才使用startTransition
const handleComplexUpdate = (data) => {
  // 简单更新不需要startTransition
  setSimpleValue(data.simple);

  // 复杂更新使用startTransition
  startTransition(() => {
    // 处理大量数据
    const processedData = data.items.map(item => ({
      ...item,
      computed: expensiveComputation(item)
    }));
    setComplexData(processedData);
  });
};

// ❌ 避免在过于简单的场景使用
startTransition(() => {
  setCount(count + 1); // 太简单，没必要
});`,
    tags: ['故障排除', '配置问题'],
    relatedQuestions: ['如何检查React版本是否支持并发特性？', 'createRoot和ReactDOM.render的区别']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-3',
    question: 'startTransition会影响SEO或服务端渲染吗？',
    answer: `startTransition对SEO和SSR的影响很小，但需要注意一些细节：

**服务端渲染(SSR)：**
1. 在服务端，startTransition会被忽略，所有更新都是同步的
2. 不会影响服务端生成的HTML内容
3. 客户端激活(hydration)时才会启用并发特性

**SEO影响：**
1. 初始页面内容不受影响，搜索引擎能正常抓取
2. 动态内容更新可能被延迟，但不影响已渲染的内容
3. 关键内容应该在初始渲染时就存在

**最佳实践：**
1. 确保重要内容在初始渲染时就可见
2. 使用startTransition优化用户交互，而不是初始加载
3. 考虑使用Suspense配合处理异步内容`,
    code: `// ✅ SSR友好的使用方式
function ProductList({ initialProducts }) {
  const [products, setProducts] = useState(initialProducts); // 初始数据来自SSR
  const [filter, setFilter] = useState('');

  const handleFilter = (filterValue) => {
    setFilter(filterValue); // 立即更新筛选器UI

    // 筛选结果可以延迟更新
    startTransition(() => {
      const filtered = initialProducts.filter(p =>
        p.name.includes(filterValue)
      );
      setProducts(filtered);
    });
  };

  return (
    <div>
      {/* 筛选器立即响应 */}
      <input
        value={filter}
        onChange={(e) => handleFilter(e.target.value)}
      />

      {/* 产品列表，初始内容对SEO友好 */}
      <div>
        {products.map(product => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
}

// 服务端渲染时提供初始数据
export async function getServerSideProps() {
  const initialProducts = await fetchProducts();

  return {
    props: {
      initialProducts
    }
  };
}`,
    tags: ['SSR', 'SEO'],
    relatedQuestions: ['如何在Next.js中使用startTransition？', 'Suspense和startTransition的配合使用']
  }
];

export default commonQuestions;