import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: "startTransition是React 18中用于标记非紧急状态更新的函数，专门用于优化用户体验，解决了大量状态更新阻塞用户交互的问题，其核心优势是让紧急更新优先执行。",

  introduction: `startTransition是为了解决React应用中大量状态更新阻塞用户交互而在React 18中引入的并发特性函数。

它遵循用户体验优先的设计理念，在性能和响应性之间做出了智能权衡选择，允许React中断非紧急更新来优先处理用户交互。

主要用于搜索过滤、数据可视化更新和复杂列表渲染等场景。相比传统的同步更新，它的创新在于引入了更新优先级概念，让React能够智能调度渲染任务。

在React生态中，它是并发特性的核心组件，常见于需要处理大量数据的企业级应用，特别适合需要保持界面响应性的高频更新场景。

核心优势包括提升用户交互响应性、避免界面卡顿，但也需要注意它会延迟非紧急更新的执行时机。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react/index.d.ts:1089
 * - 实现文件：packages/react/src/ReactStartTransition.js:15
 * - 内部类型：packages/react-reconciler/src/ReactFiberLane.js:144
 */

// 基础语法
function startTransition(callback: () => void): void;

// 使用示例
startTransition(() => {
  // 标记为过渡更新的状态变更
  setSearchResults(filteredData);
  setFilteredItems(processedItems);
});

/**
 * 参数约束：
 * - callback 必须是同步函数，不能是异步函数
 * - callback 内部的状态更新会被标记为过渡更新
 * - 过渡更新可以被紧急更新中断
 */`,

  quickExample: `function SearchExample() {
  // 搜索关键词 - 紧急更新，立即响应用户输入
  const [query, setQuery] = useState('');
  // 搜索结果 - 非紧急更新，可以被延迟
  const [results, setResults] = useState([]);

  const handleSearch = (value) => {
    // 紧急更新：立即更新输入框显示
    setQuery(value);

    // 非紧急更新：使用startTransition包装
    startTransition(() => {
      // 这个更新可以被中断，不会阻塞用户继续输入
      const filteredResults = performExpensiveSearch(value);
      setResults(filteredResults);
    });
  };

  return (
    <div>
      {/* 输入框响应性保持最高优先级 */}
      <input
        value={query}
        onChange={(e) => handleSearch(e.target.value)}
        placeholder="搜索..."
      />
      {/* 搜索结果更新可以被延迟 */}
      <div>
        {results.map(item => (
          <div key={item.id}>{item.name}</div>
        ))}
      </div>
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "startTransition在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用这个API来优化用户体验",
      diagram: `graph LR
      A[startTransition核心场景] --> B[搜索过滤]
      A --> C[数据可视化]
      A --> D[列表渲染]

      B --> B1["🔍 实时搜索<br/>用户输入时过滤大量数据"]
      B --> B2["📊 筛选器<br/>多条件组合筛选"]

      C --> C1["📈 图表更新<br/>大数据集可视化渲染"]
      C --> C2["🗺️ 地图渲染<br/>地理数据动态加载"]

      D --> D1["📋 虚拟列表<br/>大量条目的滚动渲染"]
      D --> D2["🎓 分页加载<br/>无限滚动数据加载"]

      style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
      style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
      style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "技术实现架构",
      description: "startTransition的技术实现架构，展示其核心机制和与React并发特性的集成关系",
      diagram: `graph TB
      A[startTransition技术架构] --> B[优先级调度层]
      A --> C[并发渲染层]
      A --> D[用户交互层]

      B --> B1["🎯 Lane优先级<br/>更新任务优先级分配"]
      B --> B2["⏰ 时间切片<br/>可中断的渲染调度"]

      C --> C1["🔄 Fiber架构<br/>可中断的组件树遍历"]
      C --> C2["🛠️ Scheduler<br/>任务调度和时间分配"]

      D --> D1["⚡ 紧急更新<br/>用户输入立即响应"]
      D --> D2["📦 过渡更新<br/>后台数据处理"]

      style A fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
      style B fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
      style C fill:#fce4ec,stroke:#c2185b,stroke-width:2px
      style D fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px`
    },
    {
      title: "生态系统集成",
      description: "startTransition在React并发特性生态系统中的位置和与其他API的协作关系",
      diagram: `graph TD
      A[React并发特性生态] --> B[核心APIs]
      A --> C[开发工具链]
      A --> D[社区生态]

      B --> B1["useTransition<br/>Hook版本"]
      B --> B2["useDeferredValue<br/>值延迟更新"]
      B --> B3["startTransition<br/>函数版本"]
      B --> B4["Suspense<br/>异步边界"]

      C --> C1["React DevTools<br/>并发调试支持"]
      C --> C2["Profiler<br/>性能分析工具"]

      D --> D1["React Query<br/>数据获取优化"]
      D --> D2["Zustand<br/>状态管理集成"]

      B3 -.-> B1
      B3 -.-> B2
      B3 -.-> C1
      B3 -.-> D1

      style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
      style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
      style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style B3 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
    }
  ],

  parameters: [
    {
      name: "callback",
      type: "() => void",
      required: true,
      description: "包含需要标记为过渡更新的状态变更的回调函数。函数必须是同步的，内部的所有状态更新都会被标记为过渡更新。",
      example: `startTransition(() => {
  setSearchResults(newResults);
  setFilteredData(processedData);
});`
    }
  ],

  returnValue: {
    type: "void",
    description: "startTransition不返回任何值，它是一个纯粹的副作用函数，用于标记状态更新的优先级。",
    example: `// startTransition没有返回值
startTransition(() => {
  // 这些更新会被标记为过渡更新
  setState(newState);
});`
  },

  keyFeatures: [
    {
      title: "优先级调度",
      description: "自动将回调函数内的状态更新标记为低优先级的过渡更新，允许被紧急更新中断",
      benefit: "确保用户交互（如输入、点击）始终保持最高响应性，避免界面卡顿"
    },
    {
      title: "可中断渲染",
      description: "过渡更新可以被React的并发渲染机制中断和恢复，实现时间切片渲染",
      benefit: "大量数据处理不会阻塞主线程，保持应用的流畅性"
    },
    {
      title: "智能批处理",
      description: "自动将多个过渡更新合并为一个批次处理，减少不必要的重新渲染",
      benefit: "提升性能，减少渲染开销，特别适合频繁更新的场景"
    },
    {
      title: "无额外状态",
      description: "与useTransition不同，startTransition不提供pending状态，更适合不需要加载指示器的场景",
      benefit: "代码更简洁，适合后台数据处理和不需要用户感知的更新"
    },
    {
      title: "向后兼容",
      description: "在不支持并发特性的React版本中会降级为同步更新，保证功能正常",
      benefit: "可以安全地在现有项目中使用，无需担心兼容性问题"
    }
  ],

  limitations: [
    "只能在React 18+版本中使用并发特性，在旧版本中会降级为同步更新",
    "callback函数必须是同步的，不能包含异步操作（async/await、Promise等）",
    "过渡更新可能被延迟执行，不适合需要立即反馈的关键业务逻辑",
    "在服务端渲染(SSR)环境中，startTransition会被忽略，所有更新都是同步的",
    "不提供pending状态，如果需要加载指示器应该使用useTransition Hook"
  ],

  bestPractices: [
    "将耗时的数据处理和大量DOM更新包装在startTransition中，保持用户交互响应性",
    "结合防抖(debounce)使用，避免过于频繁的过渡更新触发",
    "在搜索、筛选、排序等场景中使用，让用户输入保持最高优先级",
    "避免在startTransition中执行副作用操作，专注于状态更新",
    "对于需要加载状态的场景，优先考虑useTransition Hook",
    "在复杂数据可视化场景中使用，避免图表更新阻塞用户操作",
    "配合React.memo和useMemo使用，进一步优化渲染性能",
    "在开发环境中使用React DevTools Profiler监控过渡更新的效果"
  ],

  warnings: [
    "不要在startTransition的callback中执行异步操作，这会导致状态更新无法被正确标记",
    "避免过度使用startTransition，只有真正耗时的更新才需要标记为过渡更新",
    "注意过渡更新可能被延迟，不要依赖其立即执行来处理关键业务逻辑",
    "在严格模式(StrictMode)下，callback可能被执行两次，确保函数是纯函数",
    "过渡更新期间组件可能显示过时的数据，需要合理设计UI来处理这种情况"
  ]
};

export default basicInfo;