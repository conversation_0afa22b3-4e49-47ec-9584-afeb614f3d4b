import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 1,
    question: 'startTransition是什么？它解决了什么问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'startTransition是React 18引入的函数，用于标记非紧急状态更新，解决大量状态更新阻塞用户交互的问题。',
      detailed: `startTransition是React 18并发特性的核心API之一，主要解决以下问题：

**解决的核心问题：**
1. **界面卡顿**：大量数据处理导致的UI阻塞
2. **用户体验差**：输入延迟、点击无响应
3. **性能瓶颈**：同步更新阻塞主线程

**工作原理：**
- 将回调函数中的状态更新标记为"过渡更新"
- 过渡更新具有较低优先级，可以被紧急更新中断
- 利用React的时间切片机制，避免长时间阻塞主线程

**使用场景：**
- 搜索结果过滤
- 大数据列表渲染
- 复杂图表更新
- 数据可视化场景`,
      code: `// 基础使用示例
function SearchComponent() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);

  const handleSearch = (value) => {
    // 紧急更新：立即更新输入框
    setQuery(value);

    // 非紧急更新：使用startTransition包装
    startTransition(() => {
      const filteredResults = performExpensiveSearch(value);
      setResults(filteredResults);
    });
  };

  return (
    <div>
      <input
        value={query}
        onChange={(e) => handleSearch(e.target.value)}
      />
      <ResultsList results={results} />
    </div>
  );
}`
    },
    tags: ['基础概念', '并发特性']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 2,
    question: 'startTransition和useTransition有什么区别？什么时候使用哪个？',
    difficulty: 'medium',
    frequency: 'high',
    category: 'API对比',
    answer: {
      brief: 'startTransition是函数版本，useTransition是Hook版本并提供pending状态。根据是否需要加载指示器来选择。',
      detailed: `startTransition和useTransition都用于标记过渡更新，但有重要区别：

**startTransition特点：**
- 纯函数，可以在任何地方调用
- 不提供pending状态
- 代码更简洁
- 适合不需要加载指示器的场景

**useTransition特点：**
- React Hook，只能在组件内使用
- 提供isPending状态和startTransition函数
- 可以显示加载状态
- 适合需要用户反馈的场景

**选择原则：**
1. 需要加载指示器 → useTransition
2. 后台数据处理 → startTransition
3. 组件外调用 → startTransition
4. 简单场景 → startTransition`,
      code: `// useTransition - 需要加载状态
function SearchWithLoading() {
  const [isPending, startTransition] = useTransition();
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);

  const handleSearch = (value) => {
    setQuery(value);
    startTransition(() => {
      setResults(performSearch(value));
    });
  };

  return (
    <div>
      <input onChange={(e) => handleSearch(e.target.value)} />
      {isPending && <div>搜索中...</div>}
      <ResultsList results={results} />
    </div>
  );
}

// startTransition - 不需要加载状态
function SimpleSearch() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);

  const handleSearch = (value) => {
    setQuery(value);
    startTransition(() => {
      setResults(performSearch(value));
    });
  };

  return (
    <div>
      <input onChange={(e) => handleSearch(e.target.value)} />
      <ResultsList results={results} />
    </div>
  );
}`
    },
    tags: ['API对比', 'Hook使用']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 3,
    question: 'startTransition的内部实现原理是什么？它是如何实现优先级调度的？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '内部原理',
    answer: {
      brief: 'startTransition基于React的Lane优先级系统和Fiber架构，通过标记TransitionLane实现可中断的低优先级更新。',
      detailed: `startTransition的内部实现涉及React的核心架构：

**Lane优先级系统：**
1. **Lane标记**：将更新标记为TransitionLane（低优先级）
2. **优先级排序**：SyncLane > InputContinuousLane > TransitionLane
3. **调度决策**：高优先级任务可以中断低优先级任务

**Fiber架构支持：**
1. **可中断渲染**：Fiber节点可以暂停和恢复
2. **时间切片**：每5ms检查一次是否有更高优先级任务
3. **工作循环**：workLoopConcurrent支持中断机制

**Scheduler调度器：**
1. **任务队列**：不同优先级的任务分别排队
2. **时间分片**：使用MessageChannel或setTimeout
3. **让出控制权**：在浏览器空闲时执行

**实现流程：**
1. startTransition调用requestUpdateLane
2. 获取TransitionLane标记
3. 执行callback，收集状态更新
4. 将更新加入Scheduler队列
5. 在时间片中执行，可被中断`,
      code: `// React内部实现简化版本
function startTransition(callback) {
  // 1. 获取当前的Lane上下文
  const prevTransition = ReactCurrentBatchConfig.transition;

  // 2. 设置过渡上下文
  ReactCurrentBatchConfig.transition = {
    _callbacks: new Set(),
    _pendingTransitions: null
  };

  try {
    // 3. 执行回调，此时的状态更新会被标记为TransitionLane
    callback();
  } finally {
    // 4. 恢复之前的上下文
    ReactCurrentBatchConfig.transition = prevTransition;
  }
}

// Lane优先级定义（简化）
const SyncLane = 0b0000000000000000000000000000001;
const InputContinuousLane = 0b0000000000000000000000000000100;
const TransitionLane = 0b0000000000000000000001000000000;

// 调度逻辑（简化）
function scheduleUpdateOnFiber(fiber, lane, eventTime) {
  if (lane === SyncLane) {
    // 同步更新，立即执行
    performSyncWorkOnRoot(root);
  } else {
    // 异步更新，加入调度队列
    ensureRootIsScheduled(root, eventTime);
  }
}

// 可中断的工作循环
function workLoopConcurrent() {
  while (workInProgress !== null && !shouldYield()) {
    performUnitOfWork(workInProgress);
  }
}

// 检查是否应该让出控制权
function shouldYield() {
  return getCurrentTime() >= deadline;
}`
    },
    tags: ['内部原理', '性能优化']
  }
];

export default interviewQuestions;