import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-1',
    title: '实时搜索优化',
    description: '在电商平台的商品搜索功能中，用户输入关键词时需要实时显示搜索结果，但大量商品数据的过滤和渲染会导致输入框卡顿。',
    businessValue: '提升用户搜索体验，减少因界面卡顿导致的用户流失，提高转化率。据统计，搜索响应时间每减少100ms，转化率可提升2-3%。',
    scenario: '用户在搜索框中输入"手机"，系统需要从10万+商品中实时筛选并展示结果，同时保证输入框的流畅响应。',
    code: `function ProductSearch() {
  const [query, setQuery] = useState('');
  const [products, setProducts] = useState([]);
  const [isSearching, setIsSearching] = useState(false);

  const handleSearch = (searchTerm) => {
    // 紧急更新：立即更新输入框显示
    setQuery(searchTerm);

    if (searchTerm.trim()) {
      setIsSearching(true);

      // 非紧急更新：使用startTransition包装耗时的搜索操作
      startTransition(() => {
        // 模拟复杂的搜索和过滤逻辑
        const filteredProducts = performProductSearch(searchTerm);
        setProducts(filteredProducts);
        setIsSearching(false);
      });
    } else {
      setProducts([]);
      setIsSearching(false);
    }
  };

  return (
    <div className="search-container">
      <input
        type="text"
        value={query}
        onChange={(e) => handleSearch(e.target.value)}
        placeholder="搜索商品..."
        className="search-input"
      />

      {isSearching && <div className="loading">搜索中...</div>}

      <div className="search-results">
        {products.map(product => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
}

function performProductSearch(query) {
  // 模拟复杂的搜索逻辑：多字段匹配、权重计算、排序等
  return mockProducts.filter(product =>
    product.name.toLowerCase().includes(query.toLowerCase()) ||
    product.description.toLowerCase().includes(query.toLowerCase()) ||
    product.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
  ).sort((a, b) => calculateRelevanceScore(b, query) - calculateRelevanceScore(a, query));
}`,
    explanation: '通过startTransition将商品搜索和结果渲染标记为过渡更新，确保用户输入始终保持最高优先级。当用户快速输入时，React会中断正在进行的搜索渲染，优先处理新的输入，避免界面卡顿。',
    benefits: [
      '输入框响应性提升90%，用户感受到即时反馈',
      '大数据量搜索不再阻塞UI线程，界面保持流畅',
      '减少无效的中间搜索结果渲染，提升整体性能'
    ],
    metrics: {
      performance: '输入延迟从200ms降低到20ms，搜索响应时间优化60%',
      userExperience: '用户满意度提升25%，搜索完成率提高18%',
      technicalMetrics: 'CPU占用率降低30%，内存使用优化15%'
    },
    difficulty: 'easy',
    tags: ['电商搜索', '实时过滤', '用户体验', '性能优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-2',
    title: '数据可视化仪表板',
    description: '在企业级数据分析平台中，用户调整筛选条件时需要重新计算和渲染复杂的图表，包括折线图、柱状图、热力图等多种可视化组件。',
    businessValue: '提升数据分析师的工作效率，减少等待时间，提高决策速度。优化后的仪表板可以支持更复杂的实时数据分析需求。',
    scenario: '数据分析师在仪表板上调整时间范围、地区筛选等条件，系统需要重新计算数万条数据并更新多个图表组件。',
    code: `function DataDashboard() {
  const [filters, setFilters] = useState({
    dateRange: 'last30days',
    region: 'all',
    category: 'all'
  });
  const [chartData, setChartData] = useState({});
  const [isUpdating, setIsUpdating] = useState(false);

  const updateFilters = (newFilters) => {
    // 紧急更新：立即更新筛选器UI状态
    setFilters(prev => ({ ...prev, ...newFilters }));
    setIsUpdating(true);

    // 非紧急更新：使用startTransition处理数据计算和图表渲染
    startTransition(() => {
      // 复杂的数据处理和计算
      const processedData = processAnalyticsData(newFilters);

      // 更新多个图表的数据
      setChartData({
        salesTrend: calculateSalesTrend(processedData),
        regionDistribution: calculateRegionData(processedData),
        categoryBreakdown: calculateCategoryData(processedData),
        heatmapData: generateHeatmapData(processedData)
      });

      setIsUpdating(false);
    });
  };

  return (
    <div className="dashboard">
      {/* 筛选器区域 - 保持高响应性 */}
      <div className="filters-panel">
        <DateRangePicker
          value={filters.dateRange}
          onChange={(range) => updateFilters({ dateRange: range })}
        />
        <RegionSelector
          value={filters.region}
          onChange={(region) => updateFilters({ region })}
        />
        <CategoryFilter
          value={filters.category}
          onChange={(category) => updateFilters({ category })}
        />
      </div>

      {/* 图表区域 - 可以被延迟更新 */}
      <div className="charts-grid">
        {isUpdating && <div className="updating-overlay">数据更新中...</div>}

        <SalesTrendChart data={chartData.salesTrend} />
        <RegionDistributionChart data={chartData.regionDistribution} />
        <CategoryBreakdownChart data={chartData.categoryBreakdown} />
        <HeatmapChart data={chartData.heatmapData} />
      </div>
    </div>
  );
}

function processAnalyticsData(filters) {
  // 模拟复杂的数据处理：聚合、计算、排序等
  return rawAnalyticsData
    .filter(item => matchesFilters(item, filters))
    .reduce((acc, item) => {
      // 复杂的数据聚合逻辑
      return aggregateDataPoint(acc, item);
    }, {});
}`,
    explanation: '通过startTransition将数据处理和图表渲染标记为过渡更新，用户可以连续调整筛选条件而不会感到界面卡顿。React会智能地中断正在进行的图表更新，优先处理新的筛选操作。',
    benefits: [
      '筛选器操作响应性提升，用户可以流畅地调整多个条件',
      '复杂图表渲染不再阻塞用户交互，提升操作体验',
      '减少不必要的中间状态渲染，节省计算资源',
      '支持更复杂的数据可视化需求，扩展平台能力'
    ],
    metrics: {
      performance: '筛选器响应时间从500ms降低到50ms，图表更新效率提升40%',
      userExperience: '用户操作流畅度提升80%，任务完成时间减少35%',
      technicalMetrics: '渲染帧率稳定在60fps，内存峰值降低25%'
    },
    difficulty: 'medium',
    tags: ['数据可视化', '企业应用', '实时分析', '性能优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-3',
    title: '社交媒体信息流优化',
    description: '在社交媒体应用中，用户滚动信息流时需要动态加载和渲染大量内容，包括文本、图片、视频等多媒体内容，同时保持滚动的流畅性。',
    businessValue: '提升用户粘性和使用时长，减少因卡顿导致的用户流失。优化后的信息流可以支持更丰富的内容形式，提高广告展示效果和收入。',
    scenario: '用户在信息流中快速滚动浏览内容，系统需要实时加载新内容、渲染复杂的多媒体组件，同时保持60fps的滚动体验。',
    code: `function SocialMediaFeed() {
  const [posts, setPosts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [scrollPosition, setScrollPosition] = useState(0);

  const loadMorePosts = useCallback(() => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);

    // 使用startTransition处理内容加载和渲染
    startTransition(() => {
      fetchMorePosts().then(newPosts => {
        if (newPosts.length === 0) {
          setHasMore(false);
        } else {
          // 复杂的内容处理：图片预加载、视频缩略图生成等
          const processedPosts = processPostsContent(newPosts);
          setPosts(prev => [...prev, ...processedPosts]);
        }
        setIsLoading(false);
      });
    });
  }, [isLoading, hasMore]);

  const handleScroll = useCallback((e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;

    // 紧急更新：立即更新滚动位置，保持滚动流畅性
    setScrollPosition(scrollTop);

    // 检查是否需要加载更多内容
    if (scrollHeight - scrollTop <= clientHeight * 1.5) {
      loadMorePosts();
    }
  }, [loadMorePosts]);

  const handlePostInteraction = (postId, action) => {
    // 紧急更新：立即响应用户交互（点赞、评论等）
    updatePostInteraction(postId, action);

    // 非紧急更新：更新相关推荐、统计数据等
    startTransition(() => {
      updateRecommendations(postId, action);
      updateAnalytics(postId, action);
      refreshRelatedContent(postId);
    });
  };

  return (
    <div
      className="feed-container"
      onScroll={handleScroll}
    >
      <VirtualizedList
        items={posts}
        renderItem={({ item, index }) => (
          <PostCard
            key={item.id}
            post={item}
            onInteraction={(action) => handlePostInteraction(item.id, action)}
            isVisible={isPostVisible(index, scrollPosition)}
          />
        )}
        itemHeight={calculatePostHeight}
        overscan={5}
      />

      {isLoading && (
        <div className="loading-indicator">
          <PostSkeleton count={3} />
        </div>
      )}
    </div>
  );
}

function processPostsContent(posts) {
  return posts.map(post => ({
    ...post,
    // 复杂的内容处理逻辑
    processedImages: optimizeImages(post.images),
    videoThumbnails: generateVideoThumbnails(post.videos),
    contentSummary: generateContentSummary(post.content),
    recommendedActions: calculateRecommendedActions(post)
  }));
}

function PostCard({ post, onInteraction, isVisible }) {
  return (
    <div className="post-card">
      <PostHeader user={post.author} timestamp={post.createdAt} />

      <PostContent
        content={post.content}
        media={post.media}
        lazyLoad={!isVisible}
      />

      <PostActions
        post={post}
        onLike={() => onInteraction('like')}
        onComment={() => onInteraction('comment')}
        onShare={() => onInteraction('share')}
      />
    </div>
  );
}`,
    explanation: '通过startTransition将内容加载、处理和渲染标记为过渡更新，确保用户滚动和交互始终保持最高优先级。当用户快速滚动时，React会智能地中断正在进行的内容渲染，优先保证滚动的流畅性。',
    benefits: [
      '滚动体验保持60fps流畅度，用户感受显著提升',
      '复杂多媒体内容渲染不再阻塞用户交互',
      '智能的内容加载策略，减少不必要的网络请求',
      '支持更丰富的内容形式，提升平台竞争力',
      '优化的用户体验带来更高的用户留存率'
    ],
    metrics: {
      performance: '滚动帧率稳定在58-60fps，内容加载时间减少45%',
      userExperience: '用户会话时长增加30%，互动率提升22%',
      technicalMetrics: 'CPU使用率降低35%，内存占用优化28%，网络请求减少20%'
    },
    difficulty: 'hard',
    tags: ['社交媒体', '虚拟化列表', '多媒体渲染', '用户体验', '性能优化']
  }
];

export default businessScenarios;