import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',
    
    id: 'scenario-1',
    title: '电商网站关键资源预加载',
    description: '在电商网站中预加载关键字体、产品图片和样式文件，提升首屏加载速度和用户购买体验。',
    businessValue: '减少页面加载时间20-30%，提升转化率15%，降低跳出率25%，增强用户购买意愿',
    scenario: '用户访问电商首页时，需要快速看到产品列表、价格信息和品牌Logo。通过预加载关键资源，确保核心内容立即可见，避免字体闪烁和图片延迟加载影响用户体验。',
    code: `import { preload } from 'react-dom';
import { useEffect } from 'react';

// 电商首页组件
function EcommerceHomePage() {
  useEffect(() => {
    // 预加载品牌字体
    preload('/fonts/brand-font.woff2', {
      as: 'font',
      type: 'font/woff2',
      crossOrigin: 'anonymous'
    });

    // 预加载关键CSS
    preload('/styles/product-grid.css', {
      as: 'style'
    });

    // 预加载首屏产品图片
    preload('/images/featured-product-1.jpg', {
      as: 'image',
      fetchPriority: 'high'
    });
    
    preload('/images/featured-product-2.jpg', {
      as: 'image',
      fetchPriority: 'high'
    });

    // 预加载品牌Logo
    preload('/images/brand-logo.svg', {
      as: 'image',
      fetchPriority: 'high'
    });
  }, []);

  return (
    <div className="ecommerce-homepage">
      <header className="brand-header">
        <img src="/images/brand-logo.svg" alt="品牌Logo" />
        <h1 className="brand-title">时尚购物</h1>
      </header>
      
      <section className="featured-products">
        <h2>精选商品</h2>
        <div className="product-grid">
          <ProductCard 
            image="/images/featured-product-1.jpg"
            title="经典连衣裙"
            price="¥299"
          />
          <ProductCard 
            image="/images/featured-product-2.jpg"
            title="时尚手包"
            price="¥199"
          />
        </div>
      </section>
    </div>
  );
}

function ProductCard({ image, title, price }) {
  return (
    <div className="product-card">
      <img src={image} alt={title} loading="eager" />
      <h3>{title}</h3>
      <p className="price">{price}</p>
      <button className="add-to-cart">加入购物车</button>
    </div>
  );
}`,
    explanation: '通过在组件挂载时预加载关键资源，确保用户看到的首屏内容立即可用。品牌字体使用crossOrigin属性确保正确加载，关键图片设置fetchPriority="high"提升加载优先级。这种策略特别适合电商场景，因为快速的视觉呈现直接影响购买决策。',
    benefits: [
      '页面首屏渲染时间减少25-30%',
      '字体闪烁问题完全消除，品牌形象更一致',
      '产品图片加载速度提升40%，用户浏览体验更流畅'
    ],
    metrics: {
      performance: 'LCP时间从3.2s降至2.1s，FCP时间从1.8s降至1.2s',
      userExperience: '用户停留时间增加35%，页面跳出率降低25%',
      technicalMetrics: '关键资源平均加载时间减少600ms，缓存命中率提升至85%'
    },
    difficulty: 'easy',
    tags: ['电商', '性能优化', '用户体验', '首屏加载']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',
    
    id: 'scenario-2',
    title: '多媒体网站智能预加载策略',
    description: '在视频/图片分享网站中实现基于用户行为的智能预加载，包括鼠标悬停预加载和视口预加载机制。',
    businessValue: '提升用户浏览体验，减少等待时间，增加页面浏览深度30%，提升用户活跃度',
    scenario: '用户在浏览视频/图片列表时，系统需要智能预测用户可能点击的内容。当用户鼠标悬停在缩略图上时，预加载对应的高清图片或视频；当内容即将进入视口时，提前加载相关资源。',
    code: `import { preload } from 'react-dom';
import { useState, useEffect, useRef, useCallback } from 'react';

// 智能预加载Hook
function useSmartPreload() {
  const preloadedResources = useRef(new Set());
  
  const smartPreload = useCallback((url, options) => {
    if (preloadedResources.current.has(url)) {
      return; // 避免重复预加载
    }
    
    preloadedResources.current.add(url);
    preload(url, options);
  }, []);
  
  return smartPreload;
}

// 视口预加载Hook
function useViewportPreload(callback, options = {}) {
  const elementRef = useRef(null);
  
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            callback();
          }
        });
      },
      { threshold: 0.1, rootMargin: '50px', ...options }
    );
    
    observer.observe(element);
    return () => observer.disconnect();
  }, [callback]);
  
  return elementRef;
}

// 多媒体内容卡片
function MediaCard({ mediaItem }) {
  const smartPreload = useSmartPreload();
  const [isHovered, setIsHovered] = useState(false);
  
  // 鼠标悬停预加载
  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
    
    // 预加载高清图片
    if (mediaItem.highResImage) {
      smartPreload(mediaItem.highResImage, {
        as: 'image',
        fetchPriority: 'high'
      });
    }
    
    // 预加载视频封面
    if (mediaItem.videoPoster) {
      smartPreload(mediaItem.videoPoster, {
        as: 'image'
      });
    }
    
    // 预加载相关CSS
    smartPreload('/styles/media-detail.css', {
      as: 'style'
    });
  }, [mediaItem, smartPreload]);
  
  // 视口预加载回调
  const viewportPreloadCallback = useCallback(() => {
    // 预加载缩略图的下一级图片
    if (mediaItem.mediumResImage) {
      smartPreload(mediaItem.mediumResImage, {
        as: 'image',
        fetchPriority: 'low'
      });
    }
  }, [mediaItem, smartPreload]);
  
  const viewportRef = useViewportPreload(viewportPreloadCallback);
  
  return (
    <div 
      ref={viewportRef}
      className="media-card"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="thumbnail-container">
        <img 
          src={mediaItem.thumbnail} 
          alt={mediaItem.title}
          className={isHovered ? 'hovered' : ''}
        />
        {mediaItem.type === 'video' && (
          <div className="play-button">▶</div>
        )}
      </div>
      
      <div className="media-info">
        <h3>{mediaItem.title}</h3>
        <p className="creator">{mediaItem.creator}</p>
        <div className="stats">
          <span>{mediaItem.views} 次观看</span>
          <span>{mediaItem.likes} 个赞</span>
        </div>
      </div>
    </div>
  );
}

// 多媒体网站主页
function MediaHomePage() {
  const [mediaList, setMediaList] = useState([]);
  const smartPreload = useSmartPreload();
  
  useEffect(() => {
    // 预加载关键资源
    smartPreload('/fonts/media-ui-font.woff2', {
      as: 'font',
      type: 'font/woff2',
      crossOrigin: 'anonymous'
    });
    
    smartPreload('/styles/media-grid.css', {
      as: 'style'
    });
    
    // 加载媒体列表数据
    fetchMediaList().then(setMediaList);
  }, [smartPreload]);
  
  return (
    <div className="media-homepage">
      <header className="site-header">
        <h1>创意分享平台</h1>
        <nav className="main-nav">
          <a href="/trending">热门</a>
          <a href="/latest">最新</a>
          <a href="/following">关注</a>
        </nav>
      </header>
      
      <main className="media-grid">
        {mediaList.map(item => (
          <MediaCard key={item.id} mediaItem={item} />
        ))}
      </main>
    </div>
  );
}

// 模拟数据获取
async function fetchMediaList() {
  // 模拟API调用
  return [
    {
      id: 1,
      type: 'image',
      title: '城市夜景摄影',
      creator: '摄影师小王',
      thumbnail: '/images/thumbnails/city-night-thumb.jpg',
      mediumResImage: '/images/medium/city-night-medium.jpg',
      highResImage: '/images/high/city-night-high.jpg',
      views: '12.5k',
      likes: '892'
    },
    // ... 更多媒体项
  ];
}`,
    explanation: '这个场景展示了多层次的智能预加载策略：1) 基于Intersection Observer的视口预加载，当内容即将可见时开始预加载；2) 基于鼠标悬停的交互式预加载，预测用户可能的点击行为；3) 资源去重机制，避免重复加载相同资源。这种策略能够在不浪费带宽的前提下，显著提升用户体验。',
    benefits: [
      '用户点击响应时间减少70%，从原来的2秒降至0.6秒',
      '带宽利用效率提升45%，避免无效预加载',
      '用户浏览深度增加30%，页面停留时间延长',
      '移动端体验优化，智能控制预加载数量'
    ],
    metrics: {
      performance: '交互响应时间减少70%，资源加载成功率提升至95%',
      userExperience: '用户满意度提升40%，内容浏览量增加35%',
      technicalMetrics: '预加载准确率达到85%，缓存利用率提升至92%'
    },
    difficulty: 'medium',
    tags: ['多媒体', '智能预加载', '用户交互', '性能优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',
    
    id: 'scenario-3',
    title: '大型SaaS应用的性能优化预加载策略',
    description: '在企业级SaaS应用中实现基于路由的预加载、代码分割配合预加载、以及基于用户角色的资源优化策略。',
    businessValue: '提升企业用户工作效率，减少模块切换等待时间，提高系统响应性，增强用户满意度和续费率',
    scenario: '企业用户在SaaS应用中频繁切换不同功能模块（仪表板、报表、设置等）。系统需要根据用户角色和使用习惯，智能预加载相关模块的资源，包括路由级别的代码分割、主题样式、数据图表库等。',
    code: `import { preload } from 'react-dom';
import { useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/router';

// 用户角色和权限管理
interface UserProfile {
  role: 'admin' | 'manager' | 'employee';
  permissions: string[];
  frequentModules: string[];
  preferredTheme: 'light' | 'dark';
}

// 资源预加载管理器
class ResourcePreloadManager {
  private preloadedRoutes = new Set<string>();
  private preloadedAssets = new Set<string>();
  
  // 预加载路由相关资源
  preloadRoute(route: string, priority: 'high' | 'medium' | 'low' = 'medium') {
    if (this.preloadedRoutes.has(route)) return;
    
    this.preloadedRoutes.add(route);
    
    // 预加载路由对应的JS chunk
    const chunkPath = '/chunks/' + route.replace('/', '') + '.js';
    preload(chunkPath, {
      as: 'script',
      fetchPriority: priority
    });
    
    // 预加载路由对应的CSS
    const stylePath = '/styles/pages/' + route.replace('/', '') + '.css';
    preload(stylePath, {
      as: 'style'
    });
  }
  
  // 预加载主题资源
  preloadThemeAssets(theme: string) {
    const themeKey = 'theme-' + theme;
    if (this.preloadedAssets.has(themeKey)) return;
    
    this.preloadedAssets.add(themeKey);
    
    preload('/styles/themes/' + theme + '.css', {
      as: 'style',
      fetchPriority: 'low'
    });
    
    preload('/images/themes/' + theme + '-icons.svg', {
      as: 'image'
    });
  }
  
  // 预加载图表库
  preloadChartLibraries(chartTypes: string[]) {
    chartTypes.forEach(type => {
      if (this.preloadedAssets.has('chart-' + type)) return;
      
      this.preloadedAssets.add('chart-' + type);
      preload('/libs/charts/' + type + '.js', {
        as: 'script',
        fetchPriority: 'low'
      });
    });
  }
  
  // 基于用户角色的智能预加载
  preloadByUserRole(userProfile: UserProfile) {
    const roleBasedModules = this.getRoleBasedModules(userProfile.role);
    
    // 预加载角色相关模块
    roleBasedModules.high.forEach(module => {
      this.preloadRoute(module, 'high');
    });
    
    roleBasedModules.medium.forEach(module => {
      this.preloadRoute(module, 'medium');
    });
    
    // 预加载用户主题
    this.preloadThemeAssets(userProfile.preferredTheme);
    
    // 预加载常用图表类型
    if (userProfile.permissions.includes('analytics')) {
      this.preloadChartLibraries(['line', 'bar', 'pie']);
    }
  }
  
  private getRoleBasedModules(role: string) {
    const moduleMap = {
      admin: {
        high: ['/dashboard', '/settings', '/users'],
        medium: ['/reports', '/analytics', '/billing']
      },
      manager: {
        high: ['/dashboard', '/reports', '/team'],
        medium: ['/analytics', '/projects', '/settings']
      },
      employee: {
        high: ['/dashboard', '/tasks'],
        medium: ['/projects', '/profile']
      }
    };
    
    return moduleMap[role] || { high: [], medium: [] };
  }
}

// 导航预加载Hook
function useNavigationPreload(userProfile: UserProfile) {
  const router = useRouter();
  const preloadManager = useMemo(() => new ResourcePreloadManager(), []);
  
  // 基于用户角色预加载
  useEffect(() => {
    preloadManager.preloadByUserRole(userProfile);
  }, [userProfile, preloadManager]);
  
  // 导航链接悬停预加载
  const handleLinkHover = useCallback((route: string) => {
    preloadManager.preloadRoute(route, 'high');
  }, [preloadManager]);
  
  // 基于访问历史的预测性预加载
  useEffect(() => {
    const currentRoute = router.pathname;
    const predictedRoutes = predictNextRoutes(currentRoute, userProfile);
    
    predictedRoutes.forEach(route => {
      setTimeout(() => {
        preloadManager.preloadRoute(route, 'low');
      }, 2000); // 2秒后开始预加载预测路由
    });
  }, [router.pathname, userProfile, preloadManager]);
  
  return { handleLinkHover, preloadManager };
}

// SaaS应用主布局
function SaaSLayout({ children, userProfile }: { 
  children: React.ReactNode;
  userProfile: UserProfile;
}) {
  const { handleLinkHover } = useNavigationPreload(userProfile);
  
  useEffect(() => {
    // 预加载全局资源
    preload('/fonts/saas-ui-font.woff2', {
      as: 'font',
      type: 'font/woff2',
      crossOrigin: 'anonymous'
    });
    
    preload('/styles/global-components.css', {
      as: 'style',
      fetchPriority: 'high'
    });
    
    // 预加载用户头像
    if (userProfile.avatar) {
      preload(userProfile.avatar, {
        as: 'image',
        fetchPriority: 'low'
      });
    }
  }, [userProfile]);
  
  return (
    <div className={'saas-layout theme-' + userProfile.preferredTheme}>
      <Sidebar userProfile={userProfile} onLinkHover={handleLinkHover} />
      <MainContent>{children}</MainContent>
    </div>
  );
}

// 智能侧边栏
function Sidebar({ userProfile, onLinkHover }: {
  userProfile: UserProfile;
  onLinkHover: (route: string) => void;
}) {
  const navigationItems = getNavigationByRole(userProfile.role);
  
  return (
    <nav className="sidebar">
      <div className="user-info">
        <img 
          src={userProfile.avatar} 
          alt="用户头像"
          className="avatar"
        />
        <span className="username">{userProfile.name}</span>
      </div>
      
      <ul className="nav-list">
        {navigationItems.map(item => (
          <li key={item.route}>
            <a 
              href={item.route}
              onMouseEnter={() => onLinkHover(item.route)}
              className="nav-link"
            >
              <span className="icon">{item.icon}</span>
              <span className="label">{item.label}</span>
            </a>
          </li>
        ))}
      </ul>
    </nav>
  );
}

// 预测下一个可能访问的路由
function predictNextRoutes(currentRoute: string, userProfile: UserProfile): string[] {
  const routePatterns = {
    '/dashboard': ['/reports', '/analytics'],
    '/reports': ['/analytics', '/dashboard'],
    '/analytics': ['/reports', '/settings'],
    '/settings': ['/users', '/billing'],
    '/users': ['/settings', '/dashboard']
  };
  
  const predicted = routePatterns[currentRoute] || [];
  
  // 基于用户常用模块调整预测
  return predicted.filter(route => 
    userProfile.frequentModules.includes(route)
  );
}

// 根据角色获取导航项
function getNavigationByRole(role: string) {
  const allItems = [
    { route: '/dashboard', label: '仪表板', icon: '📊' },
    { route: '/reports', label: '报表', icon: '📈' },
    { route: '/analytics', label: '分析', icon: '🔍' },
    { route: '/settings', label: '设置', icon: '⚙️' },
    { route: '/users', label: '用户管理', icon: '👥' },
    { route: '/billing', label: '账单', icon: '💳' }
  ];
  
  const rolePermissions = {
    admin: ['dashboard', 'reports', 'analytics', 'settings', 'users', 'billing'],
    manager: ['dashboard', 'reports', 'analytics', 'settings'],
    employee: ['dashboard', 'reports']
  };
  
  const allowedRoutes = rolePermissions[role] || [];
  return allItems.filter(item => 
    allowedRoutes.some(route => item.route.includes(route))
  );
}`,
    explanation: '这个高级场景展示了企业级应用的综合预加载策略：1) 基于用户角色的权限预加载，只加载用户有权访问的模块；2) 路由级代码分割配合预加载，按需加载不同功能模块；3) 预测性预加载，基于用户行为模式预测可能访问的页面；4) 主题和资源的动态预加载；5) 性能监控和资源管理。这种策略在大型SaaS应用中能显著提升用户工作效率。',
    benefits: [
      '模块切换时间从3-5秒降至0.5-1秒，提升工作效率',
      '基于角色的精准预加载，减少不必要的资源消耗',
      '智能预测算法，预加载准确率达到80%以上',
      '支持多主题切换的无缝体验',
      '企业用户满意度提升，系统响应性增强'
    ],
    metrics: {
      performance: '路由切换时间减少80%，资源加载效率提升60%',
      userExperience: '用户任务完成效率提升45%，系统使用满意度提升至4.8/5',
      technicalMetrics: '缓存命中率提升至95%，带宽使用优化30%，服务器响应压力减轻'
    },
    difficulty: 'hard',
    tags: ['SaaS应用', '企业级', '路由预加载', '角色权限', '智能预测']
  }
];

// ReactDOM Resource API - 实际业务场景中的性能优化实践
export default businessScenarios;