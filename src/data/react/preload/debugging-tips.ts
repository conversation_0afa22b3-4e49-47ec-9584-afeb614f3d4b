import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'preload API虽然强大，但在实际使用中开发者经常遇到一些典型问题。本节提供完整的问题诊断和解决方案，帮助快速定位和修复preload相关的技术问题。',
        sections: [
          {
            title: '字体预加载问题',
            description: '字体预加载是最常见也是最容易出错的场景，涉及CORS、格式匹配、时序等多个方面的问题',
            items: [
              {
                title: '字体闪烁(FOUT/FOIT)仍然出现',
                description: '即使设置了preload，页面仍然出现字体闪烁或不可见文字闪烁现象',
                solution: '1. 检查crossOrigin属性是否正确设置；2. 确认type属性与字体格式匹配；3. 验证CSS中font-family名称一致性；4. 使用font-display: swap配合预加载',
                prevention: '建立字体预加载检查清单，在开发时期就确保所有字体配置正确',
                code: `// 问题诊断脚本
function diagnoseFontPreload() {
  const preloadLinks = document.querySelectorAll('link[rel="preload"][as="font"]');
  
  preloadLinks.forEach(link => {
    const issues = [];
    
    // 检查crossOrigin属性
    if (!link.crossOrigin) {
      issues.push('缺少crossOrigin属性');
    }
    
    // 检查type属性
    if (!link.type) {
      issues.push('缺少type属性');
    }
    
    // 检查URL可访问性
    fetch(link.href, { method: 'HEAD' })
      .then(response => {
        if (!response.ok) {
          issues.push('字体文件无法访问: ' + response.status);
        }
      })
      .catch(() => issues.push('网络错误'));
    
    console.log('字体预加载诊断:', link.href, issues);
  });
}

// 正确的字体预加载配置
preload('/fonts/roboto.woff2', {
  as: 'font',
  type: 'font/woff2',
  crossOrigin: 'anonymous'  // 必需！
});`
              },
              {
                title: '预加载资源未被使用',
                description: '浏览器控制台显示"The resource was preloaded using link preload but not used within a few seconds"警告',
                solution: '1. 检查预加载资源是否真的被页面使用；2. 调整预加载时机，避免过早预加载；3. 确认资源URL在预加载和实际使用时完全一致',
                prevention: '建立预加载使用率监控，定期清理未使用的预加载配置',
                code: `// 预加载使用率监控
class PreloadUsageMonitor {
  constructor() {
    this.preloadedResources = new Map();
    this.usedResources = new Set();
    this.monitoringEnabled = true;
  }

  trackPreload(url, type) {
    this.preloadedResources.set(url, {
      type,
      timestamp: Date.now(),
      used: false
    });
  }

  trackUsage(url) {
    this.usedResources.add(url);
    if (this.preloadedResources.has(url)) {
      this.preloadedResources.get(url).used = true;
    }
  }

  generateReport() {
    const report = {
      total: this.preloadedResources.size,
      used: 0,
      unused: [],
      efficiency: 0
    };

    this.preloadedResources.forEach((info, url) => {
      if (info.used) {
        report.used++;
      } else {
        report.unused.push({
          url,
          type: info.type,
          ageMs: Date.now() - info.timestamp
        });
      }
    });

    report.efficiency = (report.used / report.total) * 100;
    return report;
  }
}`
              }
            ]
          },
          {
            title: '网络和性能问题',
            description: '预加载相关的网络请求、缓存和性能问题的诊断与解决',
            items: [
              {
                title: '预加载阻塞关键渲染路径',
                description: '过度的预加载导致页面加载变慢，影响首屏渲染性能',
                solution: '1. 建立资源优先级分层；2. 使用fetchPriority控制加载优先级；3. 延迟非关键资源的预加载时机；4. 监控关键性能指标',
                prevention: '制定预加载策略规范，定期评估预加载效果',
                code: `// 性能影响分析工具
class PreloadPerformanceAnalyzer {
  constructor() {
    this.metrics = {
      beforePreload: {},
      afterPreload: {},
      preloadResources: []
    };
    this.startTime = performance.now();
  }

  analyzePreloadImpact() {
    const resources = performance.getEntriesByType('resource');
    const preloadResources = resources.filter(r => 
      r.initiatorType === 'link' && r.name.includes('preload')
    );

    const analysis = {
      totalPreloadTime: 0,
      criticalPathBlocked: false,
      recommendations: []
    };

    preloadResources.forEach(resource => {
      analysis.totalPreloadTime += resource.duration;
      
      // 检查是否阻塞关键路径
      if (resource.startTime < 1000 && resource.duration > 500) {
        analysis.criticalPathBlocked = true;
        analysis.recommendations.push(
          '资源 ' + resource.name + ' 在关键时期预加载，建议延迟或降低优先级'
        );
      }
    });

    return analysis;
  }

  // 智能预加载调度器
  smartSchedulePreload(resources) {
    // 按优先级分组
    const groups = {
      critical: resources.filter(r => r.priority === 'high'),
      important: resources.filter(r => r.priority === 'medium'),
      optional: resources.filter(r => r.priority === 'low')
    };

    // 立即加载关键资源
    groups.critical.forEach(resource => {
      preload(resource.url, {
        as: resource.type,
        fetchPriority: 'high'
      });
    });
  }
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '有效的调试工具和技术能帮助开发者快速定位preload相关问题，提高开发效率和代码质量。',
        sections: [
          {
            title: 'Chrome DevTools调试技巧',
            description: '充分利用Chrome DevTools的各项功能来调试和优化preload性能',
            items: [
              {
                title: '网络面板预加载分析',
                description: '使用Chrome DevTools Network面板深入分析预加载资源的加载时序和性能',
                solution: '1. 使用Network面板的Initiator列查看预加载触发源；2. 利用Waterfall视图分析加载时序；3. 检查资源大小和传输时间；4. 验证缓存状态',
                prevention: '建立标准的网络性能检查流程，定期审查预加载策略',
                code: `// Chrome DevTools Console调试脚本

// 1. 预加载资源概览
console.group('🔍 预加载资源分析');
const preloadLinks = document.querySelectorAll('link[rel="preload"]');
console.log('总预加载资源数:', preloadLinks.length);

preloadLinks.forEach((link, index) => {
  console.log((index + 1) + '. ' + link.href);
  console.log('   类型:', link.as);
  console.log('   MIME:', link.type);
  console.log('   跨域:', link.crossOrigin);
  console.log('   优先级:', link.fetchPriority || 'auto');
});
console.groupEnd();

// 2. 性能时序分析
console.group('⏱️ 性能时序分析');
const entries = performance.getEntriesByType('resource');
const preloadEntries = entries.filter(entry => 
  entry.initiatorType === 'link'
);

preloadEntries.forEach(entry => {
  const timing = {
    name: entry.name,
    总时间: Math.round(entry.duration),
    DNS查询: Math.round(entry.domainLookupEnd - entry.domainLookupStart),
    TCP连接: Math.round(entry.connectEnd - entry.connectStart),
    传输大小: entry.transferSize,
    已缓存: entry.transferSize === 0 ? '是' : '否'
  };
  console.table(timing);
});
console.groupEnd();`
              },
              {
                title: 'Lighthouse性能审计',
                description: '使用Lighthouse自动化检测preload相关的性能问题和优化建议',
                solution: '1. 运行Lighthouse性能审计；2. 检查"Preload key requests"建议；3. 分析"Remove unused CSS"和"Eliminate render-blocking resources"；4. 设置CI/CD自动化检测',
                prevention: '将Lighthouse检测集成到开发流程中，确保每次部署都经过性能审计',
                code: `// Lighthouse CI配置示例

// lighthouse.config.js
module.exports = {
  ci: {
    collect: {
      numberOfRuns: 3,
      settings: {
        chromeFlags: '--no-sandbox',
        emulatedFormFactor: 'desktop',
        onlyCategories: ['performance']
      }
    },
    assert: {
      assertions: {
        // 预加载相关断言
        'largest-contentful-paint': ['error', {maxNumericValue: 2500}],
        'first-contentful-paint': ['error', {maxNumericValue: 1500}],
        'speed-index': ['error', {maxNumericValue: 3000}],
        'total-blocking-time': ['error', {maxNumericValue: 300}],
        
        // 预加载最佳实践
        'preload-lcp-image': ['error'],
        'unused-css-rules': ['warn', {maxLength: 2}],
        'render-blocking-resources': ['warn', {maxLength: 1}]
      }
    }
  }
};`
              }
            ]
          },
          {
            title: '自动化测试和监控',
            description: '建立自动化的preload测试和监控体系，确保预加载策略的持续有效性',
            items: [
              {
                title: 'Jest单元测试框架',
                description: '为preload功能编写单元测试，确保预加载逻辑的正确性',
                solution: '1. 模拟preload API调用；2. 测试预加载条件逻辑；3. 验证资源去重机制；4. 测试错误处理逻辑',
                prevention: '将preload测试纳入CI/CD流程，确保代码质量',
                code: `// Jest测试示例

// preload.test.js
import { preload } from 'react-dom';

// Mock preload API
jest.mock('react-dom', () => ({
  preload: jest.fn()
}));

describe('智能预加载管理器', () => {
  let preloadManager;
  
  beforeEach(() => {
    jest.clearAllMocks();
    preloadManager = new SmartPreloadManager();
  });

  test('应该避免重复预加载相同资源', () => {
    const url = '/fonts/test.woff2';
    const options = { as: 'font', type: 'font/woff2' };
    
    // 首次预加载
    preloadManager.preload(url, options);
    expect(preload).toHaveBeenCalledTimes(1);
    
    // 重复预加载应该被忽略
    preloadManager.preload(url, options);
    expect(preload).toHaveBeenCalledTimes(1);
  });

  test('应该根据网络条件调整预加载策略', () => {
    // Mock慢网络
    Object.defineProperty(navigator, 'connection', {
      writable: true,
      value: { effectiveType: '3g', downlink: 1.5 }
    });

    const resources = [
      { url: '/critical.css', type: 'style', priority: 'high' },
      { url: '/optional.jpg', type: 'image', priority: 'low' }
    ];

    preloadManager.adaptivePreload(resources);

    // 应该只预加载关键资源
    expect(preload).toHaveBeenCalledWith('/critical.css', 
      expect.objectContaining({ as: 'style' })
    );
    expect(preload).not.toHaveBeenCalledWith('/optional.jpg', 
      expect.any(Object)
    );
  });
});`
              }
            ]
          }
        ]
      }
    }
  ]
};

// ReactDOM Resource API - 全面的调试技巧和开发工具指南
export default debuggingTips;