import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'React中的preload API是什么？如何使用它预加载关键资源？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'preload是ReactDOM提供的Resource API，用于提前加载关键资源如字体、样式表、图片等，提升页面性能',
      detailed: `preload API是React提供的资源预加载功能，它允许开发者声明式地指示浏览器提前下载可能需要的资源。

**核心特点：**
1. **提前加载**：在资源实际需要之前就开始下载
2. **类型安全**：提供完整的TypeScript类型定义
3. **SSR兼容**：服务端渲染时自动转换为HTML link标签
4. **重复检测**：自动避免重复预加载相同资源

**主要用途：**
- 关键字体文件预加载（防止字体闪烁）
- 首屏图片预加载（提升LCP指标）
- 关键CSS样式表预加载
- 重要脚本模块预加载

**工作原理：**
preload调用会在浏览器中生成<link rel="preload">标签，浏览器根据资源类型和优先级进行下载调度。React内部维护资源状态，确保不会重复预加载相同资源。`,
      code: `import { preload } from 'react-dom';
import { useEffect } from 'react';

function HomePage() {
  useEffect(() => {
    // 预加载关键字体（必须设置crossOrigin）
    preload('/fonts/brand-font.woff2', {
      as: 'font',
      type: 'font/woff2',
      crossOrigin: 'anonymous'
    });

    // 预加载首屏关键图片
    preload('/images/hero-banner.jpg', {
      as: 'image',
      fetchPriority: 'high'
    });

    // 预加载关键CSS文件
    preload('/styles/critical.css', {
      as: 'style'
    });

    // 预加载脚本模块
    preload('/js/analytics.js', {
      as: 'script',
      fetchPriority: 'low'
    });
  }, []);

  return (
    <div>
      <h1 style={{ fontFamily: 'BrandFont' }}>欢迎访问</h1>
      <img src="/images/hero-banner.jpg" alt="首页横幅" />
    </div>
  );
}

// 也可以在组件外部调用
preload('/fonts/global-font.woff2', {
  as: 'font',
  type: 'font/woff2',
  crossOrigin: 'anonymous'
});`
    },
    tags: ['基础API', 'Resource Hints']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'preload与prefetch有什么区别？在什么场景下使用preload？',
    difficulty: 'medium',
    frequency: 'high',
    category: '性能优化',
    answer: {
      brief: 'preload用于当前页面即将使用的关键资源，优先级高；prefetch用于将来可能使用的资源，优先级低',
      detailed: `preload和prefetch都是浏览器资源提示(Resource Hints)，但用途和优先级不同：

**preload特点：**
- **用途**：当前页面即将使用的关键资源
- **优先级**：高优先级，立即开始下载
- **时机**：页面加载过程中就需要的资源
- **影响**：直接影响页面性能指标（LCP、FCP等）
- **缓存**：下载后立即可用，不会被垃圾回收

**prefetch特点：**
- **用途**：将来页面可能使用的资源
- **优先级**：低优先级，在空闲时下载
- **时机**：用户可能导航到的页面资源
- **影响**：提升后续页面的加载速度
- **缓存**：可能被垃圾回收，需要重新验证

**技术实现差异：**
1. **网络优先级**：preload为高优先级，prefetch为最低优先级
2. **下载时机**：preload立即下载，prefetch在浏览器空闲时下载
3. **错误处理**：preload失败会影响页面加载，prefetch失败不影响
4. **资源验证**：prefetch的资源在使用时需要验证新鲜度

**使用场景对比：**
- preload：关键字体、首屏图片、重要CSS
- prefetch：下一页面的资源、可能用到的组件`,
      code: `// ✅ preload - 当前页面关键资源
preload('/fonts/logo-font.woff2', {
  as: 'font',
  type: 'font/woff2',
  crossOrigin: 'anonymous'  // 当前页面会立即使用
});

preload('/images/hero.jpg', {
  as: 'image',
  fetchPriority: 'high'     // 首屏关键图片
});

// ✅ prefetch - 将来可能需要的资源
// 注意：React没有直接的prefetch API，但可以这样实现
function prefetchResource(href, options) {
  const link = document.createElement('link');
  link.rel = 'prefetch';
  link.href = href;
  if (options.as) link.as = options.as;
  document.head.appendChild(link);
}

// 用户悬停在导航链接上时预取下一页资源
function NavigationLink({ href, children }) {
  const handleMouseEnter = () => {
    prefetchResource(href + '/style.css', { as: 'style' });
    prefetchResource(href + '/data.json', { as: 'fetch' });
  };

  return (
    <a href={href} onMouseEnter={handleMouseEnter}>
      {children}
    </a>
  );
}

// ❌ 常见错误 - 混淆使用场景
// 不要用preload预加载非关键资源
preload('/images/footer-logo.png', {  // ❌ 页脚图片不是关键资源
  as: 'image'
});

// 不要用prefetch预加载当前页面必需的资源
prefetchResource('/fonts/main-font.woff2', {  // ❌ 主字体是关键资源
  as: 'font'
});`
    },
    tags: ['性能优化', 'Resource Hints', '浏览器机制']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',
    
    id: 3,
    question: '在大型单页应用中，如何设计一个智能的preload策略来优化用户体验？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    answer: {
      brief: '需要结合用户行为分析、路由预测、资源优先级管理和性能监控，设计多层次的智能预加载系统',
      detailed: `设计大型SPA的智能preload策略需要考虑多个维度：

**1. 用户行为分析驱动的预加载**
- **交互预测**：基于鼠标悬停、焦点事件预测用户意图
- **访问模式学习**：分析用户历史访问路径，预测下一步操作
- **时间模式**：根据访问时间段预加载不同优先级的资源

**2. 路由级智能预加载**
- **关键路径识别**：标识应用中的关键业务流程
- **代码分割协调**：与webpack的代码分割策略协调
- **渐进式加载**：按优先级分批预加载路由资源

**3. 资源优先级管理系统**
- **静态优先级**：基于资源类型的固定优先级
- **动态优先级**：基于用户角色和上下文的动态调整
- **网络感知**：根据网络条件调整预加载策略

**4. 性能监控与反馈优化**
- **预加载效果评估**：监控预加载命中率和性能提升
- **资源浪费监控**：识别无效预加载，优化策略
- **A/B测试框架**：持续优化预加载策略

**5. 内存和带宽管理**
- **资源生命周期管理**：合理清理不再需要的预加载资源
- **移动端优化**：在移动网络环境下的保守策略
- **并发控制**：限制同时进行的预加载数量`,
      code: `// 智能预加载管理系统
class IntelligentPreloadManager {
  constructor() {
    this.preloadCache = new Map();
    this.userBehaviorTracker = new UserBehaviorTracker();
    this.networkMonitor = new NetworkMonitor();
    this.performanceAnalyzer = new PerformanceAnalyzer();
  }

  // 基于用户行为的智能预加载
  async smartPreload(context) {
    const strategy = await this.calculatePreloadStrategy(context);
    
    // 1. 关键资源立即预加载
    await this.preloadCriticalResources(strategy.critical);
    
    // 2. 重要资源延迟预加载
    setTimeout(() => {
      this.preloadImportantResources(strategy.important);
    }, strategy.delayMs);
    
    // 3. 可选资源按需预加载
    this.setupLazyPreload(strategy.optional);
  }

  async calculatePreloadStrategy(context) {
    const userProfile = await this.userBehaviorTracker.getProfile();
    const networkCondition = this.networkMonitor.getCurrentCondition();
    const deviceCapability = this.getDeviceCapability();
    
    return {
      critical: this.identifyCriticalResources(context, userProfile),
      important: this.identifyImportantResources(context, userProfile),
      optional: this.identifyOptionalResources(context, userProfile),
      delayMs: this.calculateOptimalDelay(networkCondition, deviceCapability)
    };
  }

  identifyCriticalResources(context, userProfile) {
    const critical = [];
    
    // 基于用户角色的关键资源
    if (userProfile.role === 'admin') {
      critical.push({
        url: '/chunks/admin-dashboard.js',
        type: 'script',
        priority: 'high'
      });
    }
    
    // 基于当前路由的关键资源
    if (context.route === '/reports') {
      critical.push({
        url: '/libs/charts.js',
        type: 'script',
        priority: 'high'
      });
    }
    
    return critical;
  }

  // 路由预测引擎
  predictNextRoutes(currentRoute, userProfile) {
    const routePatterns = this.userBehaviorTracker.getRoutePatterns();
    const predictions = [];
    
    // 基于历史模式预测
    const historicalNext = routePatterns[currentRoute] || [];
    predictions.push(...historicalNext);
    
    // 基于用户角色预测
    if (userProfile.frequentRoutes) {
      predictions.push(...userProfile.frequentRoutes);
    }
    
    // 基于时间模式预测
    const timeBasedRoutes = this.getTimeBasedRoutes();
    predictions.push(...timeBasedRoutes);
    
    return this.deduplicate(predictions);
  }

  // 网络感知预加载
  networkAwarePreload(resources) {
    const condition = this.networkMonitor.getCurrentCondition();
    
    switch (condition.type) {
      case 'wifi':
        return this.aggressivePreload(resources);
      case '4g':
        return this.moderatePreload(resources);
      case '3g':
      case 'slow-2g':
        return this.conservativePreload(resources);
      default:
        return this.minimumPreload(resources);
    }
  }

  aggressivePreload(resources) {
    // WiFi环境：预加载所有预测资源
    resources.forEach(resource => {
      preload(resource.url, {
        as: resource.type,
        fetchPriority: resource.priority
      });
    });
  }

  conservativePreload(resources) {
    // 慢网络：只预加载最关键的资源
    const critical = resources.filter(r => r.priority === 'high');
    critical.slice(0, 2).forEach(resource => {
      preload(resource.url, {
        as: resource.type,
        fetchPriority: 'high'
      });
    });
  }

  // 性能监控和优化
  monitorPreloadEffectiveness() {
    return {
      hitRate: this.performanceAnalyzer.getHitRate(),
      wastedBytes: this.performanceAnalyzer.getWastedBytes(),
      performanceGain: this.performanceAnalyzer.getPerformanceGain(),
      recommendations: this.generateOptimizationRecommendations()
    };
  }
}

// 使用示例
const preloadManager = new IntelligentPreloadManager();

function App() {
  const router = useRouter();
  const userProfile = useUserProfile();
  
  useEffect(() => {
    const context = {
      route: router.pathname,
      user: userProfile,
      timestamp: Date.now()
    };
    
    preloadManager.smartPreload(context);
  }, [router.pathname, userProfile]);

  return <AppContent />;
}`
    },
    tags: ['架构设计', '性能优化', '用户体验', '智能算法']
  }
];

// ReactDOM Resource API - 面试中的深度技术考察
export default interviewQuestions;