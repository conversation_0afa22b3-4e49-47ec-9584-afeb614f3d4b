import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么我预加载了字体，但页面还是会出现字体闪烁(FOUT/FOIT)？',
    answer: `字体闪烁问题通常是因为预加载配置不正确或时机不当。preload字体时必须正确设置crossOrigin属性，否则浏览器会当作不同的资源重新下载。

**常见原因及解决方案：**

1. **缺少crossOrigin属性**：即使是同域字体，也必须设置crossOrigin="anonymous"
2. **字体格式不匹配**：确保type属性与实际字体文件格式一致
3. **预加载时机太晚**：应该在组件外部或useEffect的第一时间预加载
4. **CSS中的字体声明不匹配**：确保CSS中的字体名称与预加载的字体一致

**完整解决方案：**
- 使用正确的crossOrigin设置
- 优先预加载WOFF2格式字体
- 配合font-display: swap使用
- 在HTML head中静态预加载关键字体`,
    code: `// ❌ 错误的字体预加载方式
preload('/fonts/brand-font.woff2', {
  as: 'font',
  type: 'font/woff2'
  // 缺少 crossOrigin 属性！
});

// ❌ 类型不匹配
preload('/fonts/brand-font.woff2', {
  as: 'font',
  type: 'font/woff',  // 类型错误，应该是 font/woff2
  crossOrigin: 'anonymous'
});

// ✅ 正确的字体预加载方式
import { preload } from 'react-dom';

// 在应用启动时立即预加载关键字体
preload('/fonts/brand-font.woff2', {
  as: 'font',
  type: 'font/woff2',
  crossOrigin: 'anonymous'
});

function App() {
  useEffect(() => {
    // 预加载额外字体
    preload('/fonts/secondary-font.woff2', {
      as: 'font',
      type: 'font/woff2',
      crossOrigin: 'anonymous'
    });
  }, []);

  return <div>应用内容</div>;
}

/* CSS配置 */
@font-face {
  font-family: 'BrandFont';
  src: url('/fonts/brand-font.woff2') format('woff2');
  font-display: swap; /* 配合预加载使用 */
  font-weight: normal;
  font-style: normal;
}

/* 也可以在HTML中静态预加载 */
// 在index.html的<head>中添加：
// <link rel="preload" href="/fonts/brand-font.woff2" 
//       as="font" type="font/woff2" crossorigin="anonymous">`,
    tags: ['字体预加载', '性能优化'],
    relatedQuestions: ['如何选择最优的字体格式', '字体加载策略最佳实践']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '预加载了很多资源后，页面反而变慢了，如何优化？',
    answer: `过度预加载确实会适得其反，消耗带宽、阻塞关键资源。需要建立智能的预加载策略，区分资源优先级，并根据网络条件动态调整。

**问题原因分析：**
1. **无差别预加载**：把所有资源都设为高优先级
2. **预加载时机不当**：在页面加载关键阶段预加载非关键资源
3. **没有考虑网络环境**：在慢网络下也执行激进的预加载策略
4. **缺乏预加载管理**：重复预加载相同资源

**优化策略：**
- 建立资源优先级分层系统
- 使用网络感知进行自适应预加载
- 实施渐进式预加载时机
- 监控预加载效果并动态调整`,
    code: `// ❌ 错误的无差别预加载
useEffect(() => {
  // 一次性预加载所有资源，会阻塞关键渲染
  preload('/images/hero.jpg', { as: 'image', fetchPriority: 'high' });
  preload('/images/gallery1.jpg', { as: 'image', fetchPriority: 'high' });
  preload('/images/gallery2.jpg', { as: 'image', fetchPriority: 'high' });
  preload('/images/footer.jpg', { as: 'image', fetchPriority: 'high' });
  preload('/js/analytics.js', { as: 'script', fetchPriority: 'high' });
  preload('/css/non-critical.css', { as: 'style', fetchPriority: 'high' });
}, []);

// ✅ 正确的分层预加载策略
class SmartPreloadManager {
  constructor() {
    this.preloadQueue = {
      critical: [],
      important: [],
      optional: []
    };
    this.networkCondition = this.getNetworkCondition();
  }

  // 智能预加载调度
  schedulePreload() {
    // 1. 立即预加载关键资源
    this.preloadCritical();
    
    // 2. 页面加载完成后预加载重要资源
    window.addEventListener('load', () => {
      this.preloadImportant();
    });
    
    // 3. 空闲时预加载可选资源
    requestIdleCallback(() => {
      this.preloadOptional();
    });
  }

  preloadCritical() {
    // 只预加载真正关键的资源
    preload('/fonts/main.woff2', {
      as: 'font',
      type: 'font/woff2',
      crossOrigin: 'anonymous'
    });
    
    preload('/images/hero.webp', {
      as: 'image',
      fetchPriority: 'high'
    });
  }

  preloadImportant() {
    if (this.networkCondition.effectiveType === '4g') {
      preload('/images/product-showcase.webp', {
        as: 'image',
        fetchPriority: 'low'
      });
    }
  }

  preloadOptional() {
    // 只在网络良好时预加载可选资源
    if (this.networkCondition.downlink > 5) {
      preload('/images/background.webp', {
        as: 'image',
        fetchPriority: 'low'
      });
    }
  }

  getNetworkCondition() {
    return navigator.connection || { effectiveType: '4g', downlink: 10 };
  }
}

// 使用智能预加载管理器
function App() {
  useEffect(() => {
    const preloadManager = new SmartPreloadManager();
    preloadManager.schedulePreload();
  }, []);

  return <div>应用内容</div>;
}

// 预加载效果监控
function monitorPreloadEffectiveness() {
  const observer = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    const preloadEntries = entries.filter(entry => 
      entry.initiatorType === 'link'
    );
    
    console.log('预加载性能分析:', {
      total: preloadEntries.length,
      avgLoadTime: preloadEntries.reduce((sum, entry) => 
        sum + (entry.responseEnd - entry.startTime), 0
      ) / preloadEntries.length,
      totalBytes: preloadEntries.reduce((sum, entry) => 
        sum + (entry.transferSize || 0), 0
      )
    });
  });
  
  observer.observe({ entryTypes: ['resource'] });
}`,
    tags: ['性能优化', '资源管理'],
    relatedQuestions: ['如何监控预加载效果', '网络感知预加载策略']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: '在开发环境中preload工作正常，但在生产环境中不生效，是什么原因？',
    answer: `开发环境和生产环境的差异可能导致preload行为不一致，主要涉及路径解析、构建工具处理、CDN配置和缓存策略等问题。

**常见环境差异问题：**

1. **路径解析差异**：开发环境使用相对路径，生产环境可能需要绝对路径或CDN路径
2. **构建工具影响**：Webpack、Vite等构建工具可能改变资源路径或添加hash
3. **服务器配置**：生产环境的缓存头、CORS设置可能不同
4. **CDN配置问题**：CDN的preload头部设置或资源路径问题

**解决方案：**
- 使用环境变量管理资源路径
- 配置构建工具正确处理preload
- 检查生产环境的服务器配置
- 验证CDN的CORS和缓存设置`,
    code: `// 问题分析和解决方案

// ❌ 硬编码路径，在不同环境下可能失效
preload('/static/fonts/main.woff2', {
  as: 'font',
  type: 'font/woff2',
  crossOrigin: 'anonymous'
});

// ✅ 环境感知的资源路径管理
class EnvironmentAwarePreloader {
  constructor() {
    this.baseURL = this.getBaseURL();
    this.isDevelopment = process.env.NODE_ENV === 'development';
  }

  getBaseURL() {
    // 根据环境返回正确的基础URL
    if (typeof window !== 'undefined') {
      return window.location.origin;
    }
    
    // 服务端渲染环境
    return process.env.REACT_APP_BASE_URL || 'https://your-domain.com';
  }

  preloadWithEnvironment(path, options) {
    let resourceURL;
    
    if (this.isDevelopment) {
      // 开发环境：直接使用相对路径
      resourceURL = path;
    } else {
      // 生产环境：使用完整URL
      resourceURL = path.startsWith('http') ? path : this.baseURL + path;
    }

    console.log('预加载资源:', resourceURL);
    
    preload(resourceURL, {
      ...options,
      // 生产环境强制设置crossOrigin
      crossOrigin: options.crossOrigin || 'anonymous'
    });
  }
}

// Webpack配置优化（webpack.config.js）
module.exports = {
  output: {
    publicPath: process.env.NODE_ENV === 'production' 
      ? 'https://cdn.yoursite.com/assets/' 
      : '/'
  },
  plugins: [
    // 确保preload资源正确处理
    new HtmlWebpackPlugin({
      template: 'public/index.html',
      // 在HTML中注入preload链接
      preloadLinks: [
        {
          href: '/fonts/main.woff2',
          as: 'font',
          type: 'font/woff2',
          crossorigin: 'anonymous'
        }
      ]
    })
  ]
};

// Next.js环境下的配置
// next.config.js
module.exports = {
  images: {
    domains: ['cdn.yoursite.com']
  },
  async headers() {
    return [
      {
        source: '/fonts/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*'
          }
        ]
      }
    ];
  }
};

// React组件中的使用
function App() {
  const preloader = useMemo(() => new EnvironmentAwarePreloader(), []);

  useEffect(() => {
    // 环境感知预加载
    preloader.preloadWithEnvironment('/fonts/main.woff2', {
      as: 'font',
      type: 'font/woff2'
    });

    // 检查预加载是否生效的调试代码
    if (process.env.NODE_ENV === 'development') {
      setTimeout(() => {
        const preloadLinks = document.querySelectorAll('link[rel="preload"]');
        console.log('当前预加载链接:', preloadLinks);
        
        preloadLinks.forEach(link => {
          link.addEventListener('load', () => {
            console.log('预加载成功:', link.href);
          });
          
          link.addEventListener('error', () => {
            console.error('预加载失败:', link.href);
          });
        });
      }, 100);
    }
  }, [preloader]);

  return <div>应用内容</div>;
}

// 生产环境调试脚本
function debugProductionPreload() {
  // 检查所有预加载资源的状态
  const preloadLinks = document.querySelectorAll('link[rel="preload"]');
  
  preloadLinks.forEach(link => {
    fetch(link.href, { method: 'HEAD' })
      .then(response => {
        console.log('预加载资源可访问:', link.href, response.status);
      })
      .catch(error => {
        console.error('预加载资源不可访问:', link.href, error);
      });
  });
}`,
    tags: ['环境配置', '部署问题'],
    relatedQuestions: ['构建工具与preload的集成', 'CDN配置最佳实践']
  }
];

// ReactDOM Resource API - 开发中的常见问题与解决方案
export default commonQuestions;