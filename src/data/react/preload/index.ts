import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const preloadData: ApiItem = {
  id: 'preload',
  title: 'preload',
  description: 'preload是ReactDOM中用于资源预加载的Resource API',
  category: 'ReactDOM APIs',
  difficulty: 'medium',
  
  syntax: 'preload(href, options?)',
  example: `import { preload } from 'react-dom';

// 预加载关键字体
preload('/fonts/main-font.woff2', {
  as: 'font',
  type: 'font/woff2',
  crossOrigin: 'anonymous'
});

// 预加载关键CSS
preload('/styles/critical.css', {
  as: 'style'
});

// 预加载关键图片
preload('/images/hero-banner.jpg', {
  as: 'image',
  fetchPriority: 'high'
});`,
  notes: '只能在浏览器环境中使用，过度使用可能消耗带宽，为字体资源设置crossOrigin属性确保正确加载',
  
  version: 'React 18.0.0+',
  tags: ["ReactDOM", "Performance", "Resource", "Preload"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default preloadData;