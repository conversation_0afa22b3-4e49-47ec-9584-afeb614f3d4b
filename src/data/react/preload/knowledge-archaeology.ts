import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '内容已完成',
  
  introduction: `Web资源预加载的概念并非一蹴而就，而是经历了从简单的浏览器缓存优化到现代智能预测加载的漫长演进过程。preload API作为现代Web标准的一部分，体现了Web性能优化领域数十年来的技术积累和设计智慧。通过追溯其技术谱系和设计理念的演进，我们可以更深入地理解现代Web性能优化的本质。`,
  
  background: `在互联网发展早期，网页加载速度问题就已经困扰着开发者和用户。随着Web内容日益丰富，从简单的HTML文档发展到包含大量CSS、JavaScript、图片和字体的复杂应用，资源加载成为了影响用户体验的关键瓶颈。传统的按需加载模式在面对现代Web应用的复杂性时显得力不从心，催生了预加载技术的诞生和发展。preload API的出现标志着Web标准化组织对性能优化问题的重视，以及对开发者工具需求的深度理解。`,

  evolution: `preload API的演进体现了Web技术从"反应式"到"预测式"的重大转变。早期的优化手段主要是响应已经发生的性能问题，而现代的预加载技术则试图在问题发生之前就进行干预。这种思维方式的转变不仅影响了技术实现，更重要的是改变了开发者对性能优化的理解。从最初的简单标签到现在的智能API，预加载技术经历了从工具到平台，从被动到主动的深刻变革。`,

  timeline: [
    {
      year: '1995-2000',
      event: '早期浏览器缓存机制建立',
      description: 'Netscape和IE等早期浏览器开始实现基础的HTTP缓存机制，这是资源预加载概念的雏形',
      significance: '奠定了Web资源重用的技术基础，为后续预加载技术提供了理论支撑'
    },
    {
      year: '2008',
      event: 'DNS预解析技术标准化',
      description: 'HTML5工作组开始讨论dns-prefetch技术，允许浏览器提前解析域名',
      significance: '首次将"预测性优化"的概念引入Web标准，开启了主动性能优化的先河'
    },
    {
      year: '2011',
      event: 'Resource Hints规范起草',
      description: 'W3C开始制定Resource Hints规范，包括dns-prefetch、preconnect、prefetch等指令',
      significance: '建立了系统性的资源提示框架，为现代预加载技术奠定了标准基础'
    },
    {
      year: '2015',
      event: 'preload属性首次提出',
      description: 'Ilya Grigorik在WebPerf工作组提出preload link关系，用于高优先级资源预加载',
      significance: '明确区分了"可能用到"的prefetch和"肯定用到"的preload，提升了优化精度'
    },
    {
      year: '2016',
      event: 'Chrome 50实现preload支持',
      description: 'Chrome浏览器率先实现了完整的preload功能，包括as属性和优先级控制',
      significance: '将理论标准转化为实际可用的开发工具，推动了Web性能优化的实用化'
    },
    {
      year: '2018',
      event: 'Priority Hints规范发布',
      description: 'fetchPriority属性标准化，允许开发者精确控制资源加载优先级',
      significance: '从简单的预加载进化到智能的优先级管理，体现了性能优化的精细化趋势'
    },
    {
      year: '2021',
      event: 'React集成preload API',
      description: 'React 18引入了内置的preload函数，将浏览器原生能力与框架深度集成',
      significance: '标志着预加载技术从底层优化工具升级为应用开发的标准组件'
    },
    {
      year: '2023',
      event: 'AI驱动的预测性预加载',
      description: '机器学习开始被应用于预测用户行为，实现更智能的资源预加载',
      significance: '代表了预加载技术向智能化方向的重大突破，开启了性能优化的新纪元'
    }
  ],

  keyFigures: [
    {
      name: 'Ilya Grigorik',
      role: 'Google Web性能工程师，Resource Hints规范作者',
      contribution: '主导制定了现代Web预加载技术的核心标准，包括preload、preconnect等关键规范',
      significance: '被誉为"Web性能优化之父"，其技术理念深刻影响了现代Web开发实践'
    },
    {
      name: 'Steve Souders',
      role: 'Web性能专家，《高性能网站建设指南》作者',
      contribution: '系统性地阐述了Web性能优化的理论基础，推动了性能优化从经验到科学的转变',
      significance: '奠定了现代Web性能优化的理论基础，影响了一代开发者的性能观念'
    },
    {
      name: 'Dan Abramov',
      role: 'React核心团队成员',
      contribution: '将现代预加载技术与React框架深度集成，推动了声明式预加载的发展',
      significance: '架起了底层浏览器API与高级应用框架之间的桥梁，提升了预加载技术的可用性'
    }
  ],

  concepts: [
    {
      term: '资源提示(Resource Hints)',
      definition: '一组HTML属性，用于向浏览器提供关于后续可能需要的资源的信息，包括dns-prefetch、preconnect、prefetch、preload等',
      evolution: '从简单的DNS预解析发展到复杂的资源优先级管理，体现了从被动缓存到主动预测的技术进步',
      modernRelevance: '现代Web应用的核心性能优化技术，直接影响用户体验和商业指标'
    },
    {
      term: '关键渲染路径(Critical Rendering Path)',
      definition: '浏览器从接收HTML到首次渲染页面所必须完成的最小资源集合和处理步骤',
      evolution: '从关注单一资源加载速度到优化整个渲染流水线，反映了性能优化思维的系统化发展',
      modernRelevance: 'Core Web Vitals等现代性能指标的理论基础，指导预加载策略的制定'
    },
    {
      term: '预测性加载(Predictive Loading)',
      definition: '基于用户行为模式、机器学习等技术，预测用户可能访问的资源并提前加载',
      evolution: '从简单的链接预加载发展到基于AI的智能预测，代表了性能优化的未来方向',
      modernRelevance: '现代大型Web应用性能优化的前沿技术，正在改变传统的资源加载模式'
    },
    {
      term: '优先级控制(Priority Control)',
      definition: '通过fetchPriority、as等属性精确控制资源加载的优先级和时机',
      evolution: '从粗粒度的预加载到精细化的优先级管理，体现了性能优化技术的成熟化',
      modernRelevance: '复杂Web应用性能调优的关键技术，直接影响资源分配效率'
    }
  ],

  designPhilosophy: `preload API的设计哲学体现了现代Web标准制定的几个核心原则：**性能优先主义** - 将性能视为用户体验的基础，任何功能都不应以牺牲性能为代价；**开发者友好** - 提供简洁而强大的API，降低性能优化的技术门槛；**渐进增强** - 确保在不支持的环境中优雅降级，不影响基本功能；**标准化优先** - 通过W3C等标准组织确保跨浏览器兼容性；**面向未来** - 设计足够灵活的架构，为未来的性能优化需求留出扩展空间。这种设计哲学反映了Web技术发展的成熟度，以及对开发者需求的深度理解。`,

  impact: `preload API的引入产生了深远的技术和商业影响。在技术层面，它重新定义了Web性能优化的边界，将性能控制权从浏览器引擎转移到开发者手中，催生了新一代的性能优化工具和最佳实践。在商业层面，预加载技术直接影响了用户留存率、转化率等核心商业指标，使性能优化从技术问题升级为商业策略。更重要的是，它推动了整个Web生态系统的性能意识觉醒，从CDN服务商到应用框架，都在积极集成和优化预加载功能。`,

  modernRelevance: `在当今的Web开发环境中，preload API已经从可选的优化手段演变为必需的基础设施。随着Single Page Applications的普及、Progressive Web Apps的发展，以及移动互联网对性能的严苛要求，预加载技术变得比以往任何时候都更加重要。现代前端框架如React、Vue、Angular都在深度集成预加载能力，而Webpack、Vite等构建工具也在自动化预加载策略的生成。更进一步，机器学习和边缘计算的结合正在开启智能预加载的新时代，预示着Web性能优化即将进入一个全新的发展阶段。`
};

// ReactDOM Resource API - 深度挖掘预加载技术的历史脉络和设计智慧
export default knowledgeArchaeology;