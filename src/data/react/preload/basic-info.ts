import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '内容已完成',
  
  definition: "preload是ReactDOM中用于资源预加载的Resource API",
  
  introduction: `preload是ReactDOM引入的Resource API，主要用于关键资源预加载、性能优化和用户体验提升。它采用声明式的设计模式，提供了与浏览器原生preload机制的深度集成。

preload允许你告诉浏览器尽早开始下载资源，即使这些资源可能稍后才会被实际使用。这对于提升Core Web Vitals指标，特别是LCP(Largest Contentful Paint)具有重要作用。

与传统的<link rel="preload">不同，React的preload API提供了更智能的资源管理、重复检测和SSR兼容性。`,

  syntax: `preload(href, options?)`,

  quickExample: `import { preload } from 'react-dom';

function App() {
  // 预加载关键字体
  preload('/fonts/main-font.woff2', {
    as: 'font',
    type: 'font/woff2',
    crossOrigin: 'anonymous'
  });

  // 预加载关键CSS
  preload('/styles/critical.css', {
    as: 'style'
  });

  // 预加载关键图片
  preload('/images/hero-banner.jpg', {
    as: 'image',
    fetchPriority: 'high'
  });

  return (
    <div>
      <h1>高性能应用</h1>
      <img src="/images/hero-banner.jpg" alt="英雄横幅" />
    </div>
  );
}`,

  scenarioDiagram: `graph TD
    A[资源预加载场景] --> B[字体预加载]
    A --> C[样式表预加载]
    A --> D[图片预加载]
    A --> E[脚本预加载]

    B --> B1[关键字体文件]
    B --> B2[图标字体]
    B --> B3[自定义字体族]

    C --> C1[关键CSS]
    C --> C2[组件样式表]
    C --> C3[第三方样式库]

    D --> D1[首屏图片]
    D --> D2[背景图片]
    D --> D3[用户头像]

    E --> E1[关键脚本模块]
    E --> E2[第三方库]
    E --> E3[Worker脚本]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`,
  
  parameters: [
    {
      name: "href",
      type: "string",
      required: true,
      description: "要预加载的资源URL，可以是相对路径或绝对URL",
      example: "'/fonts/roboto.woff2' | 'https://cdn.example.com/style.css'"
    },
    {
      name: "options",
      type: "PreloadOptions",
      required: false,
      description: "预加载选项对象，用于配置资源类型、优先级、跨域等",
      example: "{ as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' }"
    },
    {
      name: "options.as",
      type: "'font' | 'image' | 'script' | 'style' | 'fetch' | 'audio' | 'video' | 'document' | 'embed' | 'object' | 'track' | 'worker'",
      required: false,
      description: "资源类型标识，帮助浏览器正确处理资源",
      example: "'font' | 'image' | 'style'"
    },
    {
      name: "options.crossOrigin",
      type: "'anonymous' | 'use-credentials'",
      required: false,
      description: "跨域资源共享设置，对字体和某些资源必需",
      example: "'anonymous'"
    },
    {
      name: "options.fetchPriority",
      type: "'high' | 'low' | 'auto'",
      required: false,
      description: "资源获取优先级，影响浏览器的调度决策",
      example: "'high'"
    },
    {
      name: "options.integrity",
      type: "string",
      required: false,
      description: "子资源完整性(SRI)哈希值，用于安全验证",
      example: "'sha384-abc123...'"
    },
    {
      name: "options.type",
      type: "string",
      required: false,
      description: "资源的MIME类型，帮助浏览器决定是否下载",
      example: "'font/woff2' | 'text/css'"
    },
    {
      name: "options.nonce",
      type: "string",
      required: false,
      description: "Content Security Policy随机数",
      example: "'xyz789'"
    }
  ],
  
  returnValue: {
    type: "void",
    description: "preload函数不返回任何值，它的作用是指示浏览器开始资源预加载",
    example: "// 无返回值，调用后浏览器开始预加载"
  },
  
  keyFeatures: [
    {
      title: "智能重复检测",
      description: "React会自动检测重复的preload调用，避免不必要的网络请求",
      benefit: "防止资源浪费，优化网络性能"
    },
    {
      title: "SSR兼容性",
      description: "在服务端渲染时，preload调用会被转换为HTML <link rel=\"preload\">标签",
      benefit: "确保资源在页面HTML中就开始预加载"
    },
    {
      title: "优先级管理",
      description: "支持fetchPriority设置，允许精确控制资源加载优先级",
      benefit: "关键资源优先加载，提升Core Web Vitals"
    },
    {
      title: "类型安全",
      description: "提供完整的TypeScript类型定义，确保参数正确性",
      benefit: "开发时期错误检测，提高代码质量"
    },
    {
      title: "跨域支持",
      description: "内置跨域资源共享(CORS)支持，处理第三方资源",
      benefit: "安全地预加载跨域资源，如字体和CDN资源"
    }
  ],
  
  limitations: [
    "只能在浏览器环境中使用，Node.js环境中会被忽略",
    "过度使用可能消耗带宽，特别是在移动网络环境中",
    "某些资源类型可能不被所有浏览器支持",
    "预加载的资源如果未使用，会造成网络资源浪费",
    "不能替代适当的缓存策略和资源优化"
  ],
  
  bestPractices: [
    "只预加载真正关键的资源，避免预加载所有资源",
    "为字体资源设置crossOrigin='anonymous'，确保正确加载",
    "使用fetchPriority='high'标记最关键的资源",
    "结合performance API监控预加载效果",
    "在组件挂载早期调用preload，给资源足够的加载时间",
    "配合Intersection Observer实现智能预加载",
    "使用SRI(Subresource Integrity)确保资源安全性"
  ],
  
  warnings: [
    "避免预加载大型资源文件，可能影响用户的数据消耗",
    "注意资源的缓存策略，确保预加载的资源能被有效利用",
    "不要在每次渲染时重复调用preload，应该在组件生命周期早期调用",
    "跨域字体资源必须设置正确的CORS头和crossOrigin属性"
  ]
};

// React DOM Resource API - 用于性能优化的资源预加载
export default basicInfo;