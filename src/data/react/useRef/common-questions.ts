import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'ref-current-null',
    question: '为什么 ref.current 始终是 null？如何正确处理初始化问题？',
    answer: `## 常见原因和解决方案

### 1. 在组件挂载前访问 ref

\`\`\`tsx
// ❌ 错误示例：在渲染过程中访问ref
function BadExample() {
  const inputRef = useRef<HTMLInputElement>(null);
  
  // ❌ 此时DOM还未挂载，ref.current为null
  console.log(inputRef.current); // null
  
  // ❌ 直接在渲染中调用DOM方法会报错
  inputRef.current?.focus(); // 可能在DOM挂载前执行
  
  return <input ref={inputRef} />;
}

// ✅ 正确示例：在合适的时机访问ref
function GoodExample() {
  const inputRef = useRef<HTMLInputElement>(null);
  
  useEffect(() => {
    // ✅ 在useEffect中访问，确保DOM已挂载
    console.log(inputRef.current); // HTMLInputElement
    inputRef.current?.focus();
  }, []);
  
  const handleClick = () => {
    // ✅ 在事件处理器中访问也是安全的
    inputRef.current?.focus();
  };
  
  return (
    <div>
      <input ref={inputRef} />
      <button onClick={handleClick}>聚焦输入框</button>
    </div>
  );
}
\`\`\`

### 2. 条件渲染导致的 ref 问题

\`\`\`tsx
// ❌ 错误示例：条件渲染时ref可能为null
function ConditionalRenderBad() {
  const [show, setShow] = useState(false);
  const divRef = useRef<HTMLDivElement>(null);
  
  const handleShow = () => {
    setShow(true);
    // ❌ 此时DOM还未更新，ref.current仍为null
    divRef.current?.scrollIntoView();
  };
  
  return (
    <div>
      <button onClick={handleShow}>显示内容</button>
      {show && <div ref={divRef}>我是内容</div>}
    </div>
  );
}

// ✅ 正确示例：使用useEffect处理条件渲染
function ConditionalRenderGood() {
  const [show, setShow] = useState(false);
  const divRef = useRef<HTMLDivElement>(null);
  
  // ✅ 监听show状态变化，在DOM更新后执行
  useEffect(() => {
    if (show && divRef.current) {
      divRef.current.scrollIntoView();
    }
  }, [show]);
  
  return (
    <div>
      <button onClick={() => setShow(true)}>显示内容</button>
      {show && <div ref={divRef}>我是内容</div>}
    </div>
  );
}
\`\`\`

### 3. 在循环中使用 ref

\`\`\`tsx
// ❌ 错误示例：在循环中错误使用ref
function LoopRefBad({ items }: { items: string[] }) {
  const itemRef = useRef<HTMLDivElement>(null);
  
  return (
    <div>
      {items.map((item, index) => (
        // ❌ 所有元素都使用同一个ref，只有最后一个生效
        <div key={index} ref={itemRef}>
          {item}
        </div>
      ))}
    </div>
  );
}

// ✅ 正确示例：使用ref数组或ref回调
function LoopRefGood({ items }: { items: string[] }) {
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);
  
  // 方法1：使用ref数组
  const setItemRef = (index: number) => (el: HTMLDivElement | null) => {
    itemRefs.current[index] = el;
  };
  
  const scrollToItem = (index: number) => {
    itemRefs.current[index]?.scrollIntoView();
  };
  
  return (
    <div>
      {items.map((item, index) => (
        <div key={index} ref={setItemRef(index)}>
          {item}
          <button onClick={() => scrollToItem(index)}>
            滚动到此项
          </button>
        </div>
      ))}
    </div>
  );
}

// 方法2：使用Map存储refs
function LoopRefWithMap({ items }: { items: { id: string; text: string }[] }) {
  const itemRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  
  const setItemRef = (id: string) => (el: HTMLDivElement | null) => {
    if (el) {
      itemRefs.current.set(id, el);
    } else {
      itemRefs.current.delete(id);
    }
  };
  
  const scrollToItem = (id: string) => {
    const element = itemRefs.current.get(id);
    element?.scrollIntoView();
  };
  
  return (
    <div>
      {items.map((item) => (
        <div key={item.id} ref={setItemRef(item.id)}>
          {item.text}
          <button onClick={() => scrollToItem(item.id)}>
            滚动到此项
          </button>
        </div>
      ))}
    </div>
  );
}
\`\`\`

### 4. 使用 ref 回调函数处理动态情况

\`\`\`tsx
function RefCallbackExample() {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  
  // ✅ 使用ref回调函数，在元素挂载时立即获取尺寸
  const measuredRef = useCallback((node: HTMLDivElement | null) => {
    if (node !== null) {
      const { width, height } = node.getBoundingClientRect();
      setDimensions({ width, height });
    }
  }, []);
  
  return (
    <div>
      <div
        ref={measuredRef}
        style={{
          width: '50%',
          height: '200px',
          backgroundColor: 'lightblue'
        }}
      >
        测量我的尺寸
      </div>
      <p>宽度: {dimensions.width}px</p>
      <p>高度: {dimensions.height}px</p>
    </div>
  );
}
\`\`\`

## 防御性检查模式

\`\`\`tsx
function SafeRefAccess() {
  const inputRef = useRef<HTMLInputElement>(null);
  
  // ✅ 创建安全的ref访问函数
  const safelyAccessRef = <T>(
    ref: React.RefObject<T>,
    callback: (element: T) => void
  ) => {
    if (ref.current) {
      callback(ref.current);
    } else {
      console.warn('尝试访问未挂载的ref');
    }
  };
  
  const handleFocus = () => {
    safelyAccessRef(inputRef, (element) => {
      element.focus();
    });
  };
  
  const getValue = () => {
    safelyAccessRef(inputRef, (element) => {
      console.log('输入值:', element.value);
    });
  };
  
  return (
    <div>
      <input ref={inputRef} placeholder="安全的ref访问" />
      <button onClick={handleFocus}>聚焦</button>
      <button onClick={getValue}>获取值</button>
    </div>
  );
}
\`\`\``,
    tags: ['ref.current', 'null检查', '条件渲染', '循环ref'],
    difficulty: 'beginner'
  },
  
  {
    id: 'dom-update-timing',
    question: 'ref 什么时候被赋值？如何确保在 DOM 更新后访问 ref？',
    answer: `## React 渲染和 ref 赋值时机

### React 渲染流程中的 ref 赋值

\`\`\`tsx
function RefTimingExample() {
  const [count, setCount] = useState(0);
  const divRef = useRef<HTMLDivElement>(null);
  
  console.log('1. 渲染阶段 - ref.current:', divRef.current);
  
  useLayoutEffect(() => {
    // ✅ DOM更新后，浏览器绘制前 - ref已经被赋值
    console.log('2. useLayoutEffect - ref.current:', divRef.current);
  });
  
  useEffect(() => {
    // ✅ DOM更新后，浏览器绘制后 - ref已经被赋值
    console.log('3. useEffect - ref.current:', divRef.current);
  });
  
  const handleClick = () => {
    console.log('4. 点击时 - ref.current:', divRef.current);
    setCount(prev => prev + 1);
    
    // ❌ 状态更新后立即访问，DOM可能还未更新
    console.log('5. 状态更新后立即访问:', divRef.current?.textContent);
  };
  
  return (
    <div>
      <div ref={divRef}>计数: {count}</div>
      <button onClick={handleClick}>增加计数</button>
    </div>
  );
}
\`\`\`

### 确保 DOM 更新后访问 ref 的方法

\`\`\`tsx
// 方法1：使用 useLayoutEffect（同步执行）
function UseLayoutEffectExample() {
  const [height, setHeight] = useState(0);
  const elementRef = useRef<HTMLDivElement>(null);
  
  useLayoutEffect(() => {
    // ✅ DOM更新后立即执行，可以同步读取DOM属性
    if (elementRef.current) {
      const newHeight = elementRef.current.offsetHeight;
      setHeight(newHeight);
    }
  });
  
  return (
    <div>
      <div 
        ref={elementRef}
        style={{ 
          height: \`\${Math.random() * 200 + 100}px\`,
          backgroundColor: 'lightblue' 
        }}
      >
        动态高度内容
      </div>
      <p>当前高度: {height}px</p>
    </div>
  );
}

// 方法2：使用 useEffect（异步执行）
function UseEffectExample() {
  const [text, setText] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  
  useEffect(() => {
    // ✅ DOM更新后异步执行，适合大多数场景
    if (inputRef.current && text) {
      inputRef.current.scrollIntoView();
    }
  }, [text]);
  
  return (
    <div>
      <input
        ref={inputRef}
        value={text}
        onChange={(e) => setText(e.target.value)}
        placeholder="输入文本会自动滚动到视图"
      />
    </div>
  );
}

// 方法3：使用 flushSync（React 18+）
function FlushSyncExample() {
  const [items, setItems] = useState<string[]>(['item1']);
  const lastItemRef = useRef<HTMLDivElement>(null);
  
  const addItem = () => {
    // 使用flushSync确保DOM同步更新
    flushSync(() => {
      setItems(prev => [...prev, \`item\${prev.length + 1}\`]);
    });
    
    // ✅ 此时DOM已经更新，可以安全访问新添加的元素
    lastItemRef.current?.scrollIntoView();
  };
  
  return (
    <div style={{ height: '200px', overflow: 'auto' }}>
      {items.map((item, index) => (
        <div 
          key={item}
          ref={index === items.length - 1 ? lastItemRef : null}
          style={{ height: '50px', border: '1px solid #ccc', margin: '5px' }}
        >
          {item}
        </div>
      ))}
      <button onClick={addItem}>添加项目（自动滚动）</button>
    </div>
  );
}

// 方法4：使用 ref 回调函数
function RefCallbackExample() {
  const [show, setShow] = useState(false);
  
  // ✅ ref回调在元素挂载时立即执行
  const handleRefCallback = useCallback((node: HTMLDivElement | null) => {
    if (node) {
      // 元素刚刚挂载到DOM，可以立即操作
      node.style.backgroundColor = 'yellow';
      node.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);
  
  return (
    <div>
      <button onClick={() => setShow(!show)}>
        {show ? '隐藏' : '显示'}内容
      </button>
      {show && (
        <div ref={handleRefCallback} style={{ marginTop: '1000px' }}>
          我会自动滚动到视图并高亮显示
        </div>
      )}
    </div>
  );
}
\`\`\`

### 处理异步状态更新的复杂情况

\`\`\`tsx
function AsyncStateUpdateExample() {
  const [data, setData] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const fetchData = async () => {
    setLoading(true);
    
    // 模拟异步数据获取
    await new Promise(resolve => setTimeout(resolve, 1000));
    const newData = Array.from({ length: 10 }, (_, i) => \`项目 \${i + 1}\`);
    
    setData(newData);
    setLoading(false);
  };
  
  // ✅ 监听数据变化，在DOM更新后执行滚动
  useEffect(() => {
    if (data.length > 0 && containerRef.current) {
      // 滚动到容器底部
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [data]);
  
  return (
    <div>
      <button onClick={fetchData} disabled={loading}>
        {loading ? '加载中...' : '获取数据'}
      </button>
      
      <div
        ref={containerRef}
        style={{
          height: '200px',
          overflow: 'auto',
          border: '1px solid #ccc',
          marginTop: '10px'
        }}
      >
        {data.map((item, index) => (
          <div key={index} style={{ padding: '10px', borderBottom: '1px solid #eee' }}>
            {item}
          </div>
        ))}
      </div>
    </div>
  );
}
\`\`\`

## 调试 ref 时机的工具

\`\`\`tsx
// 自定义Hook用于调试ref时机
function useRefDebugger<T>(name: string) {
  const ref = useRef<T>(null);
  
  // 监控ref的变化
  useEffect(() => {
    console.log(\`[RefDebugger] \${name} ref changed:, ref.current);
  });
  
  // 在每次渲染时检查ref状态
  console.log(\`[RefDebugger] \${name} render time:, ref.current);
  
  return ref;
}

// 使用调试工具
function DebugExample() {
  const debugRef = useRefDebugger<HTMLDivElement>('myDiv');
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <div ref={debugRef}>内容 {count}</div>
      <button onClick={() => setCount(c => c + 1)}>更新</button>
    </div>
  );
}
\`\`\`

## 最佳实践总结

1. **在 useEffect/useLayoutEffect 中访问 ref**：确保DOM已更新
2. **使用 ref 回调函数**：处理动态挂载的情况
3. **避免在渲染过程中访问 ref**：可能导致 null 错误
4. **使用 flushSync**：需要立即同步更新DOM时
5. **防御性编程**：始终检查 ref.current 是否存在`,
    tags: ['DOM更新', 'useLayoutEffect', 'useEffect', 'flushSync'],
    difficulty: 'intermediate'
  },
  
  {
    id: 'useref-vs-createref-performance',
    question: 'useRef 和 createRef 在性能上有什么区别？什么情况下选择哪个？',
    answer: `## 性能对比分析

### 内存分配差异

\`\`\`tsx
// createRef - 每次调用都创建新对象
function CreateRefExample() {
  // ❌ 每次组件重新渲染都会创建新的ref对象
  const ref1 = createRef<HTMLInputElement>();
  const ref2 = createRef<HTMLInputElement>();
  
  console.log('ref1 === ref2:', ref1 === ref2); // false
  console.log('ref1:', ref1); // 每次都是新对象
  
  return <input ref={ref1} />;
}

// useRef - 返回稳定的引用
function UseRefExample() {
  // ✅ 整个组件生命周期中返回同一个对象
  const ref1 = useRef<HTMLInputElement>(null);
  const ref2 = useRef<HTMLInputElement>(null);
  
  console.log('每次渲染ref1地址:', ref1); // 地址保持不变
  
  return <input ref={ref1} />;
}
\`\`\`

### 渲染性能影响

\`\`\`tsx
// 性能测试组件
function PerformanceComparison() {
  const [count, setCount] = useState(0);
  
  // 模拟大量ref的使用场景
  const useRefRefs = Array.from({ length: 1000 }, () => useRef<HTMLDivElement>(null));
  
  // ❌ 错误做法：在组件内部使用createRef
  const createRefRefs = Array.from({ length: 1000 }, () => createRef<HTMLDivElement>());
  
  // 性能监控
  const renderStartTime = performance.now();
  
  useEffect(() => {
    const renderEndTime = performance.now();
    console.log(\`渲染耗时: \${renderEndTime - renderStartTime}ms\`);
  });
  
  return (
    <div>
      <button onClick={() => setCount(c => c + 1)}>
        重新渲染 (count: {count})
      </button>
      
      {/* useRef版本 - 性能更好 */}
      <div>
        {useRefRefs.slice(0, 5).map((ref, index) => (
          <div key={\`useref-\${index}\`} ref={ref}>
            useRef {index}
          </div>
        ))}
      </div>
      
      {/* createRef版本 - 性能较差 */}
      <div>
        {createRefRefs.slice(0, 5).map((ref, index) => (
          <div key={\`createref-\${index}\`} ref={ref}>
            createRef {index}
          </div>
        ))}
      </div>
    </div>
  );
}
\`\`\`

### 内存使用对比

\`\`\`tsx
// 内存使用测试
function MemoryUsageExample() {
  const [show, setShow] = useState(true);
  
  if (!show) return null;
  
  // useRef - 内存效率高
  const UseRefComponent = () => {
    const refs = Array.from({ length: 10000 }, () => useRef<HTMLDivElement>(null));
    
    useEffect(() => {
      console.log('UseRef组件挂载，创建了10000个useRef');
      
      return () => {
        console.log('UseRef组件卸载，内存自动释放');
      };
    }, []);
    
    return <div>useRef组件</div>;
  };
  
  // createRef - 内存效率低
  const CreateRefComponent = () => {
    // ❌ 每次渲染都创建10000个新对象
    const refs = Array.from({ length: 10000 }, () => createRef<HTMLDivElement>());
    
    useEffect(() => {
      console.log('CreateRef组件挂载，每次渲染创建10000个新对象');
    });
    
    return <div>createRef组件</div>;
  };
  
  return (
    <div>
      <button onClick={() => setShow(!show)}>
        切换显示 (当前: {show ? '显示' : '隐藏'})
      </button>
      
      {show && (
        <div>
          <UseRefComponent />
          <CreateRefComponent />
        </div>
      )}
    </div>
  );
}
\`\`\`

### 实际应用场景对比

\`\`\`tsx
// 场景1：表单组件 - useRef更适合
function FormWithUseRef() {
  const inputRefs = {
    username: useRef<HTMLInputElement>(null),
    email: useRef<HTMLInputElement>(null),
    password: useRef<HTMLInputElement>(null)
  };
  
  const focusField = (field: keyof typeof inputRefs) => {
    inputRefs[field].current?.focus();
  };
  
  // ✅ useRef提供稳定的引用，适合表单验证
  const validateForm = () => {
    const values = {
      username: inputRefs.username.current?.value || '',
      email: inputRefs.email.current?.value || '',
      password: inputRefs.password.current?.value || ''
    };
    
    // 验证逻辑...
    return values;
  };
  
  return (
    <form>
      <input ref={inputRefs.username} placeholder="用户名" />
      <input ref={inputRefs.email} placeholder="邮箱" type="email" />
      <input ref={inputRefs.password} placeholder="密码" type="password" />
      
      <button type="button" onClick={() => focusField('username')}>
        聚焦用户名
      </button>
    </form>
  );
}

// 场景2：类组件迁移 - createRef的历史用法
class LegacyComponent extends React.Component {
  // ✅ 在类组件中，createRef是正确的选择
  private inputRef = createRef<HTMLInputElement>();
  
  componentDidMount() {
    this.inputRef.current?.focus();
  }
  
  render() {
    return <input ref={this.inputRef} />;
  }
}

// 场景3：工厂函数 - createRef适用
function createInputRef() {
  // ✅ 在工厂函数中创建独立的ref
  return createRef<HTMLInputElement>();
}

function MultipleInputs() {
  // 每个输入框都需要独立的ref
  const [inputRefs] = useState(() => 
    Array.from({ length: 5 }, () => createInputRef())
  );
  
  return (
    <div>
      {inputRefs.map((ref, index) => (
        <input
          key={index}
          ref={ref}
          placeholder={\`输入框 \${index + 1}\`}
        />
      ))}
    </div>
  );
}
\`\`\`

### 性能基准测试

\`\`\`tsx
// 性能测试工具
function PerformanceBenchmark() {
  const [results, setResults] = useState<{
    useRefTime: number;
    createRefTime: number;
  } | null>(null);
  
  const runBenchmark = () => {
    const iterations = 100000;
    
    // 测试useRef性能
    const useRefStart = performance.now();
    for (let i = 0; i < iterations; i++) {
      // 模拟useRef的使用（实际上useRef只在组件内使用）
      const ref = { current: null };
    }
    const useRefEnd = performance.now();
    
    // 测试createRef性能
    const createRefStart = performance.now();
    for (let i = 0; i < iterations; i++) {
      const ref = createRef();
    }
    const createRefEnd = performance.now();
    
    setResults({
      useRefTime: useRefEnd - useRefStart,
      createRefTime: createRefEnd - createRefStart
    });
  };
  
  return (
    <div>
      <button onClick={runBenchmark}>运行性能测试</button>
      
      {results && (
        <div>
          <p>useRef耗时: {results.useRefTime.toFixed(2)}ms</p>
          <p>createRef耗时: {results.createRefTime.toFixed(2)}ms</p>
          <p>
            createRef比useRef慢: 
            {((results.createRefTime / results.useRefTime - 1) * 100).toFixed(1)}%
          </p>
        </div>
      )}
    </div>
  );
}
\`\`\`

## 选择指南

### 使用 useRef 的场景：
- ✅ 函数组件中需要持久化引用
- ✅ 需要跨渲染周期保持稳定引用
- ✅ 性能敏感的应用
- ✅ 大量ref对象的场景
- ✅ 与React生命周期紧密集成

### 使用 createRef 的场景：
- ✅ 类组件中创建ref
- ✅ 在组件外部创建独立的ref
- ✅ 工厂函数或工具函数中
- ✅ 需要动态创建多个独立ref
- ✅ 与第三方库集成时

## 性能优化建议

1. **函数组件优先使用useRef**：避免不必要的对象创建
2. **避免在渲染中调用createRef**：会导致性能问题
3. **使用对象池**：对于大量临时ref需求
4. **监控内存使用**：在大型应用中关注ref的内存占用
5. **合理设计ref结构**：避免过度嵌套和复杂的ref管理`,
    tags: ['性能对比', 'createRef', '内存优化', '基准测试'],
    difficulty: 'intermediate'
  },
  
  {
    id: 'ref-with-typescript',
    question: '在 TypeScript 中如何正确定义和使用 ref 的类型？常见的类型错误有哪些？',
    answer: `## TypeScript 中 ref 的类型定义

### 基础类型定义

\`\`\`tsx
// 1. DOM元素ref的类型定义
function BasicRefTypes() {
  // ✅ 常见DOM元素的ref类型
  const inputRef = useRef<HTMLInputElement>(null);
  const divRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  
  // ✅ 泛型ref类型
  const genericRef = useRef<HTMLElement>(null);
  
  return (
    <div ref={divRef}>
      <input ref={inputRef} />
      <button ref={buttonRef}>点击</button>
      <canvas ref={canvasRef} />
      <video ref={videoRef} />
    </div>
  );
}

// 2. 自定义类型的ref
interface CustomData {
  id: string;
  value: number;
}

function CustomTypeRef() {
  // ✅ 存储自定义数据的ref
  const dataRef = useRef<CustomData | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const callbackRef = useRef<(() => void) | null>(null);
  
  return <div>自定义类型ref示例</div>;
}
\`\`\`

### 组件ref类型定义

\`\`\`tsx
// 子组件暴露的方法接口
interface ChildComponentRef {
  focus: () => void;
  getValue: () => string;
  reset: () => void;
}

// ✅ 正确的forwardRef类型定义
const ChildComponent = React.forwardRef<ChildComponentRef, {
  defaultValue?: string;
  placeholder?: string;
}>((props, ref) => {
  const [value, setValue] = useState(props.defaultValue || '');
  const inputRef = useRef<HTMLInputElement>(null);
  
  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
    getValue: () => value,
    reset: () => {
      setValue('');
      inputRef.current?.focus();
    }
  }));
  
  return (
    <input
      ref={inputRef}
      value={value}
      onChange={(e) => setValue(e.target.value)}
      placeholder={props.placeholder}
    />
  );
});

// 父组件中使用
function ParentComponent() {
  // ✅ 正确的组件ref类型
  const childRef = useRef<ChildComponentRef>(null);
  
  const handleOperations = () => {
    childRef.current?.focus();
    console.log('值:', childRef.current?.getValue());
    childRef.current?.reset();
  };
  
  return (
    <div>
      <ChildComponent ref={childRef} placeholder="请输入" />
      <button onClick={handleOperations}>操作子组件</button>
    </div>
  );
}
\`\`\`

### 常见的类型错误和解决方案

\`\`\`tsx
// ❌ 错误1：类型不匹配
function TypeMismatchError() {
  // ❌ 错误：类型不匹配
  const ref = useRef<HTMLInputElement>(null);
  
  return (
    // ❌ 错误：试图将input ref分配给div
    <div ref={ref}>内容</div>
  );
}

// ✅ 修复：使用正确的类型
function TypeMismatchFixed() {
  const inputRef = useRef<HTMLInputElement>(null);
  const divRef = useRef<HTMLDivElement>(null);
  
  return (
    <div ref={divRef}>
      <input ref={inputRef} />
    </div>
  );
}

// ❌ 错误2：忘记null检查
function NullCheckError() {
  const inputRef = useRef<HTMLInputElement>(null);
  
  const handleClick = () => {
    // ❌ 错误：可能为null，TypeScript会报错
    inputRef.current.focus();
  };
  
  return (
    <div>
      <input ref={inputRef} />
      <button onClick={handleClick}>聚焦</button>
    </div>
  );
}

// ✅ 修复：添加null检查
function NullCheckFixed() {
  const inputRef = useRef<HTMLInputElement>(null);
  
  const handleClick = () => {
    // ✅ 正确：使用可选链或条件检查
    inputRef.current?.focus();
    
    // 或者使用条件检查
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };
  
  return (
    <div>
      <input ref={inputRef} />
      <button onClick={handleClick}>聚焦</button>
    </div>
  );
}

// ❌ 错误3：ref回调函数类型错误
function RefCallbackError() {
  // ❌ 错误：参数类型不明确
  const refCallback = (element) => {
    if (element) {
      element.focus();
    }
  };
  
  return <input ref={refCallback} />;
}

// ✅ 修复：明确指定回调函数类型
function RefCallbackFixed() {
  // ✅ 正确：明确指定参数类型
  const refCallback = (element: HTMLInputElement | null) => {
    if (element) {
      element.focus();
    }
  };
  
  // 或者使用useCallback with type
  const typedRefCallback = useCallback((element: HTMLInputElement | null) => {
    if (element) {
      element.focus();
    }
  }, []);
  
  return <input ref={typedRefCallback} />;
}
\`\`\`

### 高级类型模式

\`\`\`tsx
// 1. 联合类型ref
function UnionTypeRef() {
  // ✅ 支持多种元素类型
  const elementRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  
  const handleFocus = () => {
    // TypeScript会提供两种类型共有的方法
    elementRef.current?.focus();
  };
  
  const [inputType, setInputType] = useState<'input' | 'textarea'>('input');
  
  return (
    <div>
      <button onClick={() => setInputType(inputType === 'input' ? 'textarea' : 'input')}>
        切换类型
      </button>
      
      {inputType === 'input' ? (
        <input ref={elementRef as React.RefObject<HTMLInputElement>} />
      ) : (
        <textarea ref={elementRef as React.RefObject<HTMLTextAreaElement>} />
      )}
      
      <button onClick={handleFocus}>聚焦</button>
    </div>
  );
}

// 2. 泛型ref组件
interface GenericRefComponentProps<T extends HTMLElement> {
  elementType: React.ElementType;
  onElementReady?: (element: T) => void;
}

function GenericRefComponent<T extends HTMLElement>({
  elementType: Element,
  onElementReady
}: GenericRefComponentProps<T>) {
  const elementRef = useRef<T>(null);
  
  useEffect(() => {
    if (elementRef.current && onElementReady) {
      onElementReady(elementRef.current);
    }
  }, [onElementReady]);
  
  return <Element ref={elementRef} />;
}

// 使用泛型ref组件
function GenericRefUsage() {
  return (
    <div>
      <GenericRefComponent<HTMLInputElement>
        elementType="input"
        onElementReady={(element) => {
          // element的类型被正确推断为HTMLInputElement
          element.focus();
        }}
      />
      
      <GenericRefComponent<HTMLDivElement>
        elementType="div"
        onElementReady={(element) => {
          // element的类型被正确推断为HTMLDivElement
          element.style.backgroundColor = 'lightblue';
        }}
      />
    </div>
  );
}

// 3. ref类型工具函数
type RefType<T> = React.RefObject<T>;
type RefCallback<T> = (instance: T | null) => void;
type RefProp<T> = RefType<T> | RefCallback<T>;

// 创建类型安全的ref处理函数
function createRefHandler<T>() {
  return {
    create: (): RefType<T> => useRef<T>(null),
    
    callback: (handler: (element: T) => void): RefCallback<T> => {
      return (element: T | null) => {
        if (element) {
          handler(element);
        }
      };
    },
    
    isRefObject: (ref: RefProp<T>): ref is RefType<T> => {
      return typeof ref === 'object' && ref !== null && 'current' in ref;
    }
  };
}

// 使用ref工具函数
function RefHandlerExample() {
  const inputHandler = createRefHandler<HTMLInputElement>();
  const inputRef = inputHandler.create();
  
  const inputCallback = inputHandler.callback((element) => {
    element.focus();
    element.select();
  });
  
  return (
    <div>
      <input ref={inputRef} placeholder="使用ref对象" />
      <input ref={inputCallback} placeholder="使用ref回调" />
    </div>
  );
}
\`\`\`

### 类型声明文件扩展

\`\`\`tsx
// 扩展全局类型
declare global {
  namespace React {
    interface RefAttributes<T> {
      // 自定义ref属性
      customRef?: React.Ref<T>;
    }
  }
}

// 自定义ref Hook类型
interface UseRefWithCallbackOptions<T> {
  onMount?: (element: T) => void;
  onUnmount?: (element: T) => void;
}

function useRefWithCallback<T>(
  options: UseRefWithCallbackOptions<T> = {}
): React.RefCallback<T> {
  const { onMount, onUnmount } = options;
  
  return useCallback((element: T | null) => {
    if (element && onMount) {
      onMount(element);
    } else if (!element && onUnmount) {
      // Note: 这里需要保存之前的element引用
    }
  }, [onMount, onUnmount]);
}

// 使用自定义ref Hook
function CustomRefHookExample() {
  const inputRef = useRefWithCallback<HTMLInputElement>({
    onMount: (element) => {
      element.focus();
      console.log('输入框已挂载');
    }
  });
  
  return <input ref={inputRef} />;
}
\`\`\`

## 最佳实践总结

### 1. 类型定义原则
- ✅ 始终明确指定ref的泛型类型
- ✅ 使用具体的DOM元素类型而非通用的Element
- ✅ 为null值做好类型保护
- ✅ 在组件接口中明确定义ref类型

### 2. 错误预防
- ✅ 使用可选链操作符（?.）
- ✅ 在访问ref前进行null检查
- ✅ 为ref回调函数明确指定类型
- ✅ 避免类型断言，优先使用类型守卫

### 3. 工具支持
- ✅ 配置ESLint规则检查ref使用
- ✅ 使用TypeScript strict模式
- ✅ 利用编辑器的类型提示
- ✅ 编写类型测试确保类型正确性`,
    tags: ['TypeScript', '类型定义', '类型错误', '类型安全'],
    difficulty: 'advanced'
  }
];

export default commonQuestions; 