import { Implementation } from '@/types/api';

const implementation: Implementation = {
  mechanism: `useRef 通过 React 的 Hook 机制实现持久化引用存储。在首次渲染时创建包含 current 属性的对象，后续渲染返回同一个对象。

## 🔧 核心实现机制

### 1. Hook链表存储
useRef 在 React Fiber 架构中作为 Hook 链表的一个节点存储：

\`\`\`typescript
// React 内部 useRef 简化实现
function useRef<T>(initialValue: T): MutableRefObject<T> {
  // 在首次渲染时创建ref对象
  const currentHook = mountWorkInProgressHook();
  
  if (currentHook.memoizedState === null) {
    // 创建包含 current 属性的对象
    const ref = { current: initialValue };
    currentHook.memoizedState = ref;
    return ref;
  }
  
  // 后续渲染直接返回已存在的ref对象
  return currentHook.memoizedState;
}
\`\`\`

### 2. 引用稳定性保证
React 保证 useRef 返回的对象在组件生命周期内始终是同一个引用：

\`\`\`typescript
// Fiber 节点中的Hook链表结构
interface Hook {
  memoizedState: any;    // 存储当前Hook的状态
  next: Hook | null;     // 指向下一个Hook
  baseState: any;        // 基础状态
  baseQueue: Update<any> | null;
  queue: UpdateQueue<any> | null;
}

function FiberHookExample() {
  const [count, setCount] = useState(0);     // Hook链表的第1个节点
  const inputRef = useRef<HTMLInputElement>(null); // Hook链表的第2个节点
  const timerRef = useRef<number | null>(null);    // Hook链表的第3个节点
  
  // 在Fiber节点中的Hook链表结构：
  // Hook1 (useState) -> Hook2 (useRef) -> Hook3 (useRef) -> null
}
\`\`\`

### 3. 不触发重新渲染的机制
ref.current 的修改绕过了 React 的调度系统：

\`\`\`typescript
// useState 的调度更新机制
function dispatchAction<S, A>(fiber: Fiber, queue: UpdateQueue<S, A>, action: A) {
  const update = createUpdate(action);
  enqueueUpdate(fiber, queue, update);
  scheduleUpdateOnFiber(fiber); // 触发重新渲染
}

// useRef 的直接赋值机制
function updateRefValue<T>(ref: MutableRefObject<T>, value: T) {
  ref.current = value; // 直接修改对象属性，不经过React调度系统
  // 没有调用 scheduleUpdateOnFiber，所以不会触发重新渲染
}
\`\`\`

### 4. ref回调函数机制
React 在 DOM 节点挂载/卸载时执行 ref 回调：

\`\`\`typescript
// React 内部ref回调的执行流程
function commitAttachRef(finishedWork: Fiber) {
  const ref = finishedWork.ref;
  if (ref !== null) {
    const instance = finishedWork.stateNode;
    
    if (typeof ref === 'function') {
      ref(instance); // 执行ref回调函数
    } else {
      ref.current = instance; // 设置ref对象的current属性
    }
  }
}

function commitDetachRef(current: Fiber) {
  const currentRef = current.ref;
  if (currentRef !== null) {
    if (typeof currentRef === 'function') {
      currentRef(null); // 调用ref回调函数，传入null
    } else {
      currentRef.current = null; // 清空ref对象的current属性
    }
  }
}
\`\`\``,

  visualization: `graph TD
    A[组件首次渲染] --> B[调用useRef]
    B --> C[创建Hook节点]
    C --> D["创建ref对象 {current: initialValue}"]
    D --> E[存储到Hook.memoizedState]
    E --> F[返回ref对象]
    
    G[组件重新渲染] --> H[调用useRef]
    H --> I[获取现有Hook节点]
    I --> J[返回已存在的ref对象]
    
    K[修改ref.current] --> L[直接属性赋值]
    L --> M[不触发调度更新]
    M --> N[组件不重新渲染]
    
    O[DOM元素挂载] --> P[执行ref回调或设置ref.current]
    P --> Q[ref指向DOM节点]
    
    R[DOM元素卸载] --> S[执行ref回调传入null]
    S --> T[清空ref.current]`,

  plainExplanation: `想象 useRef 就像是你房间里的一个储物盒。

🏠 **储物盒比喻**：
- 房间 = React组件
- 储物盒 = useRef返回的对象
- 盒子里的东西 = ref.current的值

📦 **储物盒的特点**：
1. **持久存在**：即使你重新装修房间（组件重新渲染），储物盒还是那个储物盒
2. **静默存储**：往盒子里放东西或取东西不会惊动别人（不触发重新渲染）
3. **随时访问**：你可以随时打开盒子查看或更换里面的东西
4. **标签系统**：你可以给盒子贴标签（ref回调）,当盒子被放置或移走时会通知你

这就是为什么 useRef 特别适合存储那些"需要记住但不需要立即响应"的东西，比如 DOM 元素的引用、定时器ID、或者上一次的值。`,

  designConsiderations: [
    "useRef 设计为不触发重新渲染，适合存储不影响UI的数据",
    "返回的对象引用在整个组件生命周期内保持稳定，避免不必要的依赖变化",
    "支持泛型类型推断，提供类型安全的访问方式",
    "与 createRef 相比，useRef 在函数组件中更高效，避免重复创建对象",
    "ref 回调机制允许在 DOM 节点生命周期事件中执行自定义逻辑"
  ],

  relatedConcepts: [
    "useState - 状态管理Hook，修改会触发重新渲染",
    "useCallback - 记忆化回调函数，常与useRef配合使用",
    "useEffect - 生命周期Hook，常用于操作ref.current",
    "React.createRef - 类组件中的ref创建方法",
    "React.forwardRef - 转发ref到子组件",
    "MutableRefObject - useRef返回的类型接口"
  ]
};

export default implementation; 