import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 'useref-vs-usestate',
    question: '什么时候使用useRef而不是useState？请详细解释两者的区别和使用场景。',
    difficulty: 'beginner',
    category: '基础概念',
    answer: `## 核心区别

### useState 特点
- **触发重新渲染**：状态改变时组件会重新渲染
- **用于UI状态**：影响组件显示的数据
- **异步更新**：状态更新可能是异步的
- **函数式更新**：支持函数式更新模式

### useRef 特点  
- **不触发重新渲染**：ref值改变时组件不会重新渲染
- **用于非UI数据**：存储不影响渲染的数据
- **同步更新**：ref.current的修改是同步的
- **跨渲染持久化**：在组件重新渲染时保持值的引用

## 使用场景对比

\`\`\`tsx
function StateVsRefExample() {
  // 使用useState - 需要在UI中显示的数据
  const [count, setCount] = useState(0);
  const [userName, setUserName] = useState('');
  
  // 使用useRef - 不需要在UI中显示的数据
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const renderCountRef = useRef(0);
  const previousValueRef = useRef<string>('');
  
  // 每次渲染时增加计数（不触发重新渲染）
  renderCountRef.current += 1;
  
  useEffect(() => {
    // 保存上一个值
    previousValueRef.current = userName;
  });
  
  const startTimer = () => {
    timerRef.current = setInterval(() => {
      setCount(prev => prev + 1); // 这会触发重新渲染
    }, 1000);
  };
  
  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };
  
  return (
    <div>
      <p>Count: {count}</p>
      <p>渲染次数: {renderCountRef.current}</p>
      <p>上一个用户名: {previousValueRef.current}</p>
      
      <input 
        value={userName}
        onChange={e => setUserName(e.target.value)}
        placeholder="输入用户名"
      />
      
      <button onClick={startTimer}>开始计时</button>
      <button onClick={stopTimer}>停止计时</button>
    </div>
  );
}
\`\`\`

## 选择原则

**使用useState的场景：**
- 数据变化需要更新UI
- 表单输入、用户交互状态
- 组件的可见性控制
- 动态样式或类名

**使用useRef的场景：**
- DOM元素引用
- 定时器ID存储
- 上一次的值缓存
- 第三方库实例存储
- 性能优化（避免不必要的渲染）`,
    tags: ['useState', 'useRef', '状态管理', '性能优化'],
    relatedTopics: ['React状态管理', '性能优化', 'DOM操作']
  },
  
  {
    id: 'ref-forwarding',
    question: 'React.forwardRef的作用是什么？如何结合useRef使用？请提供实际应用场景。',
    difficulty: 'intermediate',
    category: 'ref转发',
    answer: `## forwardRef的作用

React.forwardRef允许组件向子组件"转发"ref，使父组件能够直接访问子组件的DOM元素或组件实例。

## 基础用法

\`\`\`tsx
// 子组件 - 使用forwardRef包装
const CustomInput = React.forwardRef<HTMLInputElement, {
  placeholder?: string;
  error?: string;
}>((props, ref) => {
  return (
    <div className="input-wrapper">
      <input 
        ref={ref}
        type="text"
        placeholder={props.placeholder}
        className={props.error ? 'error' : ''}
      />
      {props.error && <span className="error-text">{props.error}</span>}
    </div>
  );
});

// 父组件 - 使用useRef获取子组件的input引用
function ParentComponent() {
  const inputRef = useRef<HTMLInputElement>(null);
  
  const focusInput = () => {
    inputRef.current?.focus();
  };
  
  const getInputValue = () => {
    console.log(inputRef.current?.value);
  };
  
  return (
    <div>
      <CustomInput 
        ref={inputRef}
        placeholder="请输入内容"
      />
      <button onClick={focusInput}>聚焦输入框</button>
      <button onClick={getInputValue}>获取输入值</button>
    </div>
  );
}
\`\`\`

## 高级应用场景

### 1. 表单组件库
\`\`\`tsx
interface FormFieldProps {
  label: string;
  error?: string;
  required?: boolean;
}

const FormField = React.forwardRef<HTMLInputElement, FormFieldProps>(
  ({ label, error, required, ...props }, ref) => {
    return (
      <div className="form-field">
        <label>
          {label}
          {required && <span className="required">*</span>}
        </label>
        <input ref={ref} {...props} />
        {error && <div className="error">{error}</div>}
      </div>
    );
  }
);

// 使用表单组件
function RegistrationForm() {
  const emailRef = useRef<HTMLInputElement>(null);
  const passwordRef = useRef<HTMLInputElement>(null);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 验证邮箱
    if (!emailRef.current?.value) {
      emailRef.current?.focus();
      return;
    }
    
    // 验证密码
    if (!passwordRef.current?.value) {
      passwordRef.current?.focus();
      return;
    }
    
    // 提交表单...
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <FormField
        ref={emailRef}
        label="邮箱地址"
        type="email"
        required
      />
      <FormField
        ref={passwordRef}
        label="密码"
        type="password"
        required
      />
      <button type="submit">注册</button>
    </form>
  );
}
\`\`\`

### 2. 复合组件模式
\`\`\`tsx
// 模态框组件，需要暴露内部方法
interface ModalRef {
  open: () => void;
  close: () => void;
  toggle: () => void;
}

const Modal = React.forwardRef<ModalRef, {
  title: string;
  children: ReactNode;
}>((props, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  
  // 使用useImperativeHandle暴露方法
  useImperativeHandle(ref, () => ({
    open: () => setIsOpen(true),
    close: () => setIsOpen(false),
    toggle: () => setIsOpen(prev => !prev)
  }));
  
  if (!isOpen) return null;
  
  return (
    <div className="modal-overlay">
      <div className="modal">
        <div className="modal-header">
          <h2>{props.title}</h2>
          <button onClick={() => setIsOpen(false)}>×</button>
        </div>
        <div className="modal-body">
          {props.children}
        </div>
      </div>
    </div>
  );
});

// 使用模态框
function App() {
  const modalRef = useRef<ModalRef>(null);
  
  return (
    <div>
      <button onClick={() => modalRef.current?.open()}>
        打开模态框
      </button>
      
      <Modal ref={modalRef} title="确认操作">
        <p>确定要执行此操作吗？</p>
        <button onClick={() => modalRef.current?.close()}>
          确定
        </button>
      </Modal>
    </div>
  );
}
\`\`\`

## TypeScript类型定义

\`\`\`tsx
// 定义ref类型
type InputRef = HTMLInputElement;

// 组件props类型
interface CustomInputProps {
  placeholder?: string;
  defaultValue?: string;
}

// 正确的forwardRef类型定义
const CustomInput = React.forwardRef<InputRef, CustomInputProps>(
  (props, ref) => {
    return <input ref={ref} {...props} />;
  }
);

// 使用时的类型安全
function Parent() {
  const inputRef = useRef<InputRef>(null);
  // TypeScript会正确推断inputRef.current的类型
  return <CustomInput ref={inputRef} />;
}
\`\`\``,
    tags: ['forwardRef', 'ref转发', 'TypeScript', '组件设计'],
    relatedTopics: ['useImperativeHandle', '组件通信', 'TypeScript']
  },
  
  {
    id: 'useref-performance',
    question: '如何使用useRef进行性能优化？请说明在什么情况下使用ref比state更好。',
    difficulty: 'intermediate',
    category: '性能优化',
    answer: `## useRef性能优化的核心原理

useRef不触发重新渲染的特性使其成为性能优化的重要工具，特别是在需要存储频繁变化但不影响UI的数据时。

## 优化场景1：避免昂贵的重新计算

\`\`\`tsx
function ExpensiveComponent({ data }: { data: any[] }) {
  // ❌ 每次渲染都会重新计算（性能差）
  const expensiveValue = data.reduce((acc, item) => {
    // 模拟昂贵的计算
    return acc + complexCalculation(item);
  }, 0);
  
  // ✅ 使用useRef缓存计算结果（性能好）
  const expensiveValueRef = useRef<number>();
  const dataHashRef = useRef<string>();
  
  // 计算数据的哈希值来判断是否需要重新计算
  const currentDataHash = JSON.stringify(data);
  
  if (dataHashRef.current !== currentDataHash) {
    // 只有当数据真正变化时才重新计算
    expensiveValueRef.current = data.reduce((acc, item) => {
      return acc + complexCalculation(item);
    }, 0);
    dataHashRef.current = currentDataHash;
  }
  
  return <div>结果: {expensiveValueRef.current}</div>;
}
\`\`\`

## 优化场景2：防止事件处理器的频繁创建

\`\`\`tsx
function SearchComponent({ onSearch }: { onSearch: (query: string) => void }) {
  const [query, setQuery] = useState('');
  const debounceTimerRef = useRef<NodeJS.Timeout>();
  
  // ❌ 每次渲染都创建新的防抖函数
  const handleSearchBad = useMemo(() => {
    return debounce(onSearch, 300);
  }, [onSearch]);
  
  // ✅ 使用useRef创建稳定的防抖处理
  const handleSearchGood = useCallback((searchQuery: string) => {
    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    // 设置新的定时器
    debounceTimerRef.current = setTimeout(() => {
      onSearch(searchQuery);
    }, 300);
  }, [onSearch]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    handleSearchGood(value);
  };
  
  // 清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);
  
  return (
    <input
      value={query}
      onChange={handleInputChange}
      placeholder="搜索..."
    />
  );
}
\`\`\`

## 优化场景3：跨渲染的状态同步

\`\`\`tsx
function AnimationComponent() {
  const [isPlaying, setIsPlaying] = useState(false);
  const animationFrameRef = useRef<number>();
  const startTimeRef = useRef<number>();
  const progressRef = useRef<number>(0);
  
  // 动画循环函数
  const animate = useCallback((currentTime: number) => {
    if (!startTimeRef.current) {
      startTimeRef.current = currentTime;
    }
    
    const elapsed = currentTime - startTimeRef.current;
    const duration = 2000; // 2秒动画
    
    // 更新进度（不触发重新渲染）
    progressRef.current = Math.min(elapsed / duration, 1);
    
    // 更新DOM（直接操作，避免React重新渲染）
    const element = document.getElementById('animated-element');
    if (element) {
      element.style.transform = \`translateX(\${progressRef.current * 200}px)\`;
    }
    
    if (progressRef.current < 1) {
      // 继续动画
      animationFrameRef.current = requestAnimationFrame(animate);
    } else {
      // 动画完成
      setIsPlaying(false);
      startTimeRef.current = undefined;
    }
  }, []);
  
  const startAnimation = () => {
    if (!isPlaying) {
      setIsPlaying(true);
      progressRef.current = 0;
      animationFrameRef.current = requestAnimationFrame(animate);
    }
  };
  
  const stopAnimation = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      setIsPlaying(false);
      startTimeRef.current = undefined;
    }
  };
  
  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);
  
  return (
    <div>
      <div
        id="animated-element"
        style={{
          width: 50,
          height: 50,
          backgroundColor: 'blue',
          transition: 'none' // 禁用CSS过渡，使用JS动画
        }}
      />
      <button onClick={startAnimation} disabled={isPlaying}>
        开始动画
      </button>
      <button onClick={stopAnimation} disabled={!isPlaying}>
        停止动画
      </button>
    </div>
  );
}
\`\`\`

## 优化场景4：大列表性能优化

\`\`\`tsx
function VirtualList({ items }: { items: string[] }) {
  const containerRef = useRef<HTMLDivElement>(null);
  const itemHeight = 50;
  const visibleCount = 10;
  
  // 使用ref存储滚动相关的状态，避免频繁重新渲染
  const scrollTopRef = useRef(0);
  const startIndexRef = useRef(0);
  const endIndexRef = useRef(visibleCount);
  
  const [, forceUpdate] = useState({});
  
  const handleScroll = useCallback(() => {
    if (!containerRef.current) return;
    
    const scrollTop = containerRef.current.scrollTop;
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(startIndex + visibleCount, items.length);
    
    // 更新ref值（不触发重新渲染）
    scrollTopRef.current = scrollTop;
    startIndexRef.current = startIndex;
    endIndexRef.current = endIndex;
    
    // 只在必要时强制更新
    if (startIndex !== startIndexRef.current) {
      forceUpdate({});
    }
  }, [items.length, itemHeight, visibleCount]);
  
  const visibleItems = items.slice(startIndexRef.current, endIndexRef.current);
  
  return (
    <div
      ref={containerRef}
      style={{ height: visibleCount * itemHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        {visibleItems.map((item, index) => (
          <div
            key={startIndexRef.current + index}
            style={{
              position: 'absolute',
              top: (startIndexRef.current + index) * itemHeight,
              height: itemHeight,
              width: '100%'
            }}
          >
            {item}
          </div>
        ))}
      </div>
    </div>
  );
}
\`\`\`

## 性能对比总结

| 场景 | useState | useRef | 性能差异 |
|------|----------|--------|----------|
| 频繁变化的值 | 每次变化都重新渲染 | 不触发重新渲染 | useRef性能更好 |
| DOM直接操作 | 需要通过state→render流程 | 直接操作 | useRef更直接高效 |
| 定时器/动画 | 可能导致不必要的渲染 | 不影响渲染性能 | useRef避免性能损耗 |
| 缓存计算结果 | 每次渲染重新计算 | 可以智能缓存 | useRef减少计算开销 |`,
    tags: ['性能优化', '防抖', '动画', '虚拟列表'],
    relatedTopics: ['React性能优化', 'useMemo', 'useCallback']
  },
  
  {
    id: 'useref-vs-useimperativehandle',
    question: 'useRef和useImperativeHandle有什么区别？什么时候使用useImperativeHandle？',
    difficulty: 'advanced',
    category: 'Hook关系',
    answer: `## 两者的作用对比

### useRef
- **用途**：创建可变的ref对象，存储DOM引用或任意值
- **作用域**：在组件内部使用
- **访问方式**：通过 \`.current\` 属性访问

### useImperativeHandle
- **用途**：自定义通过ref暴露给父组件的实例值
- **作用域**：在子组件中定义，供父组件调用
- **访问方式**：父组件通过ref直接调用暴露的方法

## 基础对比示例

\`\`\`tsx
// 使用useRef的常规方式
function RegularComponent() {
  const inputRef = useRef<HTMLInputElement>(null);
  
  const focusInput = () => {
    inputRef.current?.focus();
  };
  
  return (
    <div>
      <input ref={inputRef} />
      <button onClick={focusInput}>聚焦</button>
    </div>
  );
}

// 使用useImperativeHandle暴露方法
interface CustomInputRef {
  focus: () => void;
  clear: () => void;
  getValue: () => string;
}

const CustomInput = React.forwardRef<CustomInputRef, {
  placeholder?: string;
}>((props, ref) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [value, setValue] = useState('');
  
  // 使用useImperativeHandle定义暴露给父组件的接口
  useImperativeHandle(ref, () => ({
    focus: () => {
      inputRef.current?.focus();
    },
    clear: () => {
      setValue('');
      inputRef.current?.focus();
    },
    getValue: () => {
      return value;
    }
  }));
  
  return (
    <input
      ref={inputRef}
      value={value}
      onChange={(e) => setValue(e.target.value)}
      placeholder={props.placeholder}
    />
  );
});

// 父组件使用
function ParentComponent() {
  const customInputRef = useRef<CustomInputRef>(null);
  
  const handleOperations = () => {
    // 直接调用子组件暴露的方法
    customInputRef.current?.focus();
    
    setTimeout(() => {
      const value = customInputRef.current?.getValue();
      console.log('输入值:', value);
      
      if (!value) {
        customInputRef.current?.clear();
      }
    }, 1000);
  };
  
  return (
    <div>
      <CustomInput ref={customInputRef} placeholder="请输入内容" />
      <button onClick={handleOperations}>执行操作</button>
    </div>
  );
}
\`\`\`

## 复杂应用场景

### 1. 表单组件封装
\`\`\`tsx
interface FormRef {
  validate: () => boolean;
  reset: () => void;
  getValues: () => Record<string, any>;
  setValues: (values: Record<string, any>) => void;
  focusFirstError: () => void;
}

const Form = React.forwardRef<FormRef, {
  children: ReactNode;
  onSubmit?: (values: Record<string, any>) => void;
}>((props, ref) => {
  const [values, setValues] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const fieldRefs = useRef<Record<string, HTMLInputElement>>({});
  
  useImperativeHandle(ref, () => ({
    validate: () => {
      const newErrors: Record<string, string> = {};
      
      // 验证逻辑
      Object.keys(values).forEach(key => {
        if (!values[key]) {
          newErrors[key] = \`\${key}不能为空\`;
        }
      });
      
      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    },
    
    reset: () => {
      setValues({});
      setErrors({});
    },
    
    getValues: () => values,
    
    setValues: (newValues) => {
      setValues(newValues);
    },
    
    focusFirstError: () => {
      const firstErrorField = Object.keys(errors)[0];
      if (firstErrorField && fieldRefs.current[firstErrorField]) {
        fieldRefs.current[firstErrorField].focus();
      }
    }
  }));
  
  const registerField = (name: string, element: HTMLInputElement) => {
    fieldRefs.current[name] = element;
  };
  
  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      if (ref && typeof ref === 'object' && ref.current) {
        if (ref.current.validate()) {
          props.onSubmit?.(values);
        } else {
          ref.current.focusFirstError();
        }
      }
    }}>
      {/* 渲染表单字段 */}
      {props.children}
    </form>
  );
});
\`\`\`

### 2. 媒体播放器组件
\`\`\`tsx
interface VideoPlayerRef {
  play: () => Promise<void>;
  pause: () => void;
  seek: (time: number) => void;
  getCurrentTime: () => number;
  getDuration: () => number;
  setVolume: (volume: number) => void;
}

const VideoPlayer = React.forwardRef<VideoPlayerRef, {
  src: string;
  poster?: string;
}>((props, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  
  useImperativeHandle(ref, () => ({
    play: async () => {
      if (videoRef.current) {
        await videoRef.current.play();
        setIsPlaying(true);
      }
    },
    
    pause: () => {
      if (videoRef.current) {
        videoRef.current.pause();
        setIsPlaying(false);
      }
    },
    
    seek: (time: number) => {
      if (videoRef.current) {
        videoRef.current.currentTime = time;
        setCurrentTime(time);
      }
    },
    
    getCurrentTime: () => {
      return videoRef.current?.currentTime || 0;
    },
    
    getDuration: () => {
      return videoRef.current?.duration || 0;
    },
    
    setVolume: (volume: number) => {
      if (videoRef.current) {
        videoRef.current.volume = Math.max(0, Math.min(1, volume));
      }
    }
  }));
  
  return (
    <video
      ref={videoRef}
      src={props.src}
      poster={props.poster}
      onTimeUpdate={() => setCurrentTime(videoRef.current?.currentTime || 0)}
      onLoadedMetadata={() => setDuration(videoRef.current?.duration || 0)}
      onPlay={() => setIsPlaying(true)}
      onPause={() => setIsPlaying(false)}
    />
  );
});

// 使用播放器
function VideoApp() {
  const playerRef = useRef<VideoPlayerRef>(null);
  
  const handlePlayPause = () => {
    if (playerRef.current) {
      const currentTime = playerRef.current.getCurrentTime();
      const duration = playerRef.current.getDuration();
      
      if (currentTime < duration) {
        playerRef.current.play();
      } else {
        playerRef.current.pause();
      }
    }
  };
  
  const handleSeek = (position: number) => {
    playerRef.current?.seek(position);
  };
  
  return (
    <div>
      <VideoPlayer
        ref={playerRef}
        src="/video.mp4"
        poster="/poster.jpg"
      />
      <button onClick={handlePlayPause}>播放/暂停</button>
      <button onClick={() => handleSeek(30)}>跳转到30秒</button>
    </div>
  );
}
\`\`\`

## 使用时机总结

### 使用useRef的情况：
- 访问DOM元素
- 存储定时器ID
- 缓存计算结果
- 保存上一次的值
- 与第三方库集成

### 使用useImperativeHandle的情况：
- 创建可复用的组件库
- 需要暴露特定方法给父组件
- 封装复杂的交互逻辑
- 提供命令式API
- 组件间的解耦通信

## 最佳实践

1. **优先使用声明式**：尽量使用props和state，避免过度使用命令式API
2. **接口设计**：useImperativeHandle暴露的接口应该简洁、语义化
3. **类型安全**：使用TypeScript定义清晰的ref接口
4. **文档说明**：为暴露的方法提供清晰的文档和使用示例`,
    tags: ['useImperativeHandle', 'forwardRef', '组件设计', 'API设计'],
    relatedTopics: ['React.forwardRef', '组件通信', 'TypeScript']
  },
  
  {
    id: 'useref-memory-leaks',
    question: '使用useRef时如何避免内存泄漏？请举例说明常见的陷阱和解决方案。',
    difficulty: 'advanced',
    category: '内存管理',
    answer: `## 常见的内存泄漏场景

### 1. 事件监听器未清理

\`\`\`tsx
// ❌ 错误示例：事件监听器未清理
function BadEventExample() {
  const buttonRef = useRef<HTMLButtonElement>(null);
  
  useEffect(() => {
    const button = buttonRef.current;
    if (button) {
      const handleClick = () => console.log('clicked');
      button.addEventListener('click', handleClick);
      
      // ❌ 忘记清理事件监听器
    }
  }, []);
  
  return <button ref={buttonRef}>点击我</button>;
}

// ✅ 正确示例：清理事件监听器
function GoodEventExample() {
  const buttonRef = useRef<HTMLButtonElement>(null);
  
  useEffect(() => {
    const button = buttonRef.current;
    if (button) {
      const handleClick = () => console.log('clicked');
      button.addEventListener('click', handleClick);
      
      // ✅ 清理事件监听器
      return () => {
        button.removeEventListener('click', handleClick);
      };
    }
  }, []);
  
  return <button ref={buttonRef}>点击我</button>;
}
\`\`\`

### 2. 定时器和动画帧未清理

\`\`\`tsx
// ❌ 错误示例：定时器泄漏
function BadTimerExample() {
  const [count, setCount] = useState(0);
  const timerRef = useRef<NodeJS.Timeout>();
  
  const startTimer = () => {
    // ❌ 没有清理之前的定时器就创建新的
    timerRef.current = setInterval(() => {
      setCount(prev => prev + 1);
    }, 1000);
  };
  
  // ❌ 组件卸载时没有清理定时器
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={startTimer}>开始计时</button>
    </div>
  );
}

// ✅ 正确示例：完整的定时器管理
function GoodTimerExample() {
  const [count, setCount] = useState(0);
  const timerRef = useRef<NodeJS.Timeout>();
  
  const startTimer = () => {
    // ✅ 先清理之前的定时器
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    
    timerRef.current = setInterval(() => {
      setCount(prev => prev + 1);
    }, 1000);
  };
  
  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = undefined;
    }
  };
  
  // ✅ 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      stopTimer();
    };
  }, []);
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={startTimer}>开始计时</button>
      <button onClick={stopTimer}>停止计时</button>
    </div>
  );
}
\`\`\`

### 3. DOM观察器未清理

\`\`\`tsx
// ❌ 错误示例：观察器泄漏
function BadObserverExample() {
  const elementRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver>();
  
  useEffect(() => {
    if (elementRef.current) {
      observerRef.current = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          console.log('可见性变化:', entry.isIntersecting);
        });
      });
      
      observerRef.current.observe(elementRef.current);
      
      // ❌ 没有清理观察器
    }
  }, []);
  
  return <div ref={elementRef}>观察我</div>;
}

// ✅ 正确示例：清理观察器
function GoodObserverExample() {
  const elementRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver>();
  
  useEffect(() => {
    const element = elementRef.current;
    if (element) {
      observerRef.current = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          console.log('可见性变化:', entry.isIntersecting);
        });
      });
      
      observerRef.current.observe(element);
      
      // ✅ 清理观察器
      return () => {
        if (observerRef.current) {
          observerRef.current.unobserve(element);
          observerRef.current.disconnect();
        }
      };
    }
  }, []);
  
  return <div ref={elementRef}>观察我</div>;
}
\`\`\`

### 4. 第三方库实例未清理

\`\`\`tsx
// ❌ 错误示例：图表库实例泄漏
function BadChartExample() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const chartRef = useRef<any>();
  
  useEffect(() => {
    if (canvasRef.current) {
      // 假设使用Chart.js
      chartRef.current = new Chart(canvasRef.current, {
        type: 'bar',
        data: { /* 数据 */ }
      });
      
      // ❌ 没有销毁图表实例
    }
  }, []);
  
  return <canvas ref={canvasRef} />;
}

// ✅ 正确示例：清理图表实例
function GoodChartExample() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const chartRef = useRef<any>();
  
  useEffect(() => {
    if (canvasRef.current) {
      chartRef.current = new Chart(canvasRef.current, {
        type: 'bar',
        data: { /* 数据 */ }
      });
      
      // ✅ 销毁图表实例
      return () => {
        if (chartRef.current) {
          chartRef.current.destroy();
          chartRef.current = null;
        }
      };
    }
  }, []);
  
  return <canvas ref={canvasRef} />;
}
\`\`\`

### 5. 闭包捕获过期的ref

\`\`\`tsx
// ❌ 错误示例：闭包捕获过期引用
function BadClosureExample() {
  const [items, setItems] = useState<string[]>([]);
  const callbackRef = useRef<() => void>();
  
  // ❌ 闭包捕获了过期的items值
  callbackRef.current = () => {
    console.log('当前项目数量:', items.length);
  };
  
  useEffect(() => {
    const timer = setInterval(() => {
      // 这里调用的callback可能使用过期的items值
      callbackRef.current?.();
    }, 1000);
    
    return () => clearInterval(timer);
  }, []); // 空依赖数组导致闭包问题
  
  return (
    <div>
      <button onClick={() => setItems(prev => [...prev, \`item-\${Date.now()}\`])}>
        添加项目
      </button>
      <p>项目数量: {items.length}</p>
    </div>
  );
}

// ✅ 正确示例：避免闭包陷阱
function GoodClosureExample() {
  const [items, setItems] = useState<string[]>([]);
  const itemsRef = useRef<string[]>([]);
  
  // ✅ 使用ref保持最新值
  useEffect(() => {
    itemsRef.current = items;
  }, [items]);
  
  useEffect(() => {
    const timer = setInterval(() => {
      // 使用ref获取最新值
      console.log('当前项目数量:', itemsRef.current.length);
    }, 1000);
    
    return () => clearInterval(timer);
  }, []); // 可以安全使用空依赖数组
  
  return (
    <div>
      <button onClick={() => setItems(prev => [...prev, \`item-\${Date.now()}\`])}>
        添加项目
      </button>
      <p>项目数量: {items.length}</p>
    </div>
  );
}
\`\`\`

## 内存泄漏检测工具

\`\`\`tsx
// 自定义Hook用于检测内存泄漏
function useMemoryLeakDetector(name: string) {
  const mountTimeRef = useRef<number>();
  const cleanupFunctionsRef = useRef<(() => void)[]>([]);
  
  useEffect(() => {
    mountTimeRef.current = Date.now();
    console.log(\`[MemoryLeakDetector] \${name} 组件挂载\`);
    
    return () => {
      const unmountTime = Date.now();
      const lifeTime = unmountTime - (mountTimeRef.current || 0);
      
      console.log(\`[MemoryLeakDetector] \${name} 组件卸载，生命周期: \${lifeTime}ms\`);
      
      // 执行所有清理函数
      cleanupFunctionsRef.current.forEach(cleanup => cleanup());
      cleanupFunctionsRef.current = [];
    };
  }, [name]);
  
  const addCleanup = useCallback((cleanup: () => void) => {
    cleanupFunctionsRef.current.push(cleanup);
  }, []);
  
  return { addCleanup };
}

// 使用示例
function MemoryLeakAwareComponent() {
  const { addCleanup } = useMemoryLeakDetector('MemoryLeakAwareComponent');
  const intervalRef = useRef<NodeJS.Timeout>();
  
  useEffect(() => {
    // 设置定时器
    intervalRef.current = setInterval(() => {
      console.log('定时器执行');
    }, 1000);
    
    // 注册清理函数
    addCleanup(() => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        console.log('定时器已清理');
      }
    });
  }, [addCleanup]);
  
  return <div>内存泄漏检测组件</div>;
}
\`\`\`

## 最佳实践总结

### 1. 清理检查清单
- [ ] 事件监听器已移除
- [ ] 定时器已清理
- [ ] 动画帧已取消
- [ ] 观察器已断开
- [ ] 第三方库实例已销毁
- [ ] WebSocket连接已关闭
- [ ] 订阅已取消

### 2. 防御性编程
\`\`\`tsx
// 创建安全的清理函数
function createSafeCleanup(cleanup: () => void) {
  return () => {
    try {
      cleanup();
    } catch (error) {
      console.error('清理函数执行失败:', error);
    }
  };
}

// 使用示例
const cleanup = createSafeCleanup(() => {
  if (timerRef.current) {
    clearInterval(timerRef.current);
  }
});
\`\`\`

### 3. 开发工具集成
- 使用React DevTools的Profiler检测内存使用
- 使用浏览器内存工具监控内存增长
- 设置自动化测试检测内存泄漏`,
    tags: ['内存泄漏', '清理函数', '事件监听', '定时器'],
    relatedTopics: ['useEffect', '组件生命周期', 'React DevTools']
  }
];

export default interviewQuestions; 