import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  background: `## useRef 的历史演进

### React 早期的 ref 系统（2013-2017）

在 React 的早期版本中，ref 系统经历了多次重大变化，从字符串 ref 到回调 ref，再到现在的 useRef 和 createRef。

#### 字符串 ref 时代（React 0.13-15.x）
\`\`\`jsx
// 早期的字符串ref（已废弃）
class OldComponent extends React.Component {
  componentDidMount() {
    // 通过 this.refs 访问
    this.refs.myInput.focus();
  }
  
  render() {
    return <input ref="myInput" />;
  }
}
\`\`\`

字符串 ref 的问题：
- **性能问题**：React 需要维护一个全局的 refs 映射
- **组合问题**：无法在高阶组件中传递 ref
- **静态分析困难**：工具无法分析 ref 的使用

#### 回调 ref 时代（React 0.14+）
\`\`\`jsx
class CallbackRefComponent extends React.Component {
  componentDidMount() {
    // 直接访问实例变量
    this.myInput.focus();
  }
  
  render() {
    return (
      <input 
        ref={(element) => { this.myInput = element; }}
      />
    );
  }
}
\`\`\`

### React 16.3：ref 系统的重大革新（2018年3月）

React 16.3 引入了 \`createRef\` API，标志着 ref 系统的现代化。

\`\`\`jsx
class ModernRefComponent extends React.Component {
  constructor(props) {
    super(props);
    this.myRef = React.createRef();
  }
  
  componentDidMount() {
    this.myRef.current.focus();
  }
  
  render() {
    return <input ref={this.myRef} />;
  }
}
\`\`\`

**createRef 的设计理念：**
- **明确性**：ref 对象有明确的生命周期
- **类型安全**：更好的 TypeScript 支持
- **性能优化**：避免字符串 ref 的性能开销`,

  evolution: `## ref 系统的技术演进

### 第一代：字符串 ref（2013-2016）
\`\`\`jsx
// React 0.13 时代
var MyComponent = React.createClass({
  componentDidMount: function() {
    this.refs.myInput.getDOMNode().focus();
  },
  render: function() {
    return React.DOM.input({ref: 'myInput'});
  }
});
\`\`\`

**技术特点：**
- 简单直观的 API
- 内部使用字符串到组件实例的映射
- 存在内存泄漏风险

### 第二代：回调 ref（2015-2018）
\`\`\`jsx
class ComponentWithCallbackRef extends React.Component {
  setTextInputRef = (element) => {
    this.textInput = element;
  }
  
  focusTextInput = () => {
    if (this.textInput) this.textInput.focus();
  }
  
  render() {
    return (
      <div>
        <input type="text" ref={this.setTextInputRef} />
        <input
          type="button"
          value="Focus the text input"
          onClick={this.focusTextInput}
        />
      </div>
    );
  }
}
\`\`\`

### 第三代：createRef 对象 ref（2018-至今）
\`\`\`jsx
class ComponentWithObjectRef extends React.Component {
  constructor(props) {
    super(props);
    this.textInput = React.createRef();
  }
  
  focusTextInput = () => {
    this.textInput.current.focus();
  }
  
  render() {
    return (
      <div>
        <input type="text" ref={this.textInput} />
        <input
          type="button"
          value="Focus the text input"
          onClick={this.focusTextInput}
        />
      </div>
    );
  }
}
\`\`\`

### 第四代：useRef Hook（2019-至今）
\`\`\`jsx
function ComponentWithHookRef() {
  const textInput = useRef(null);
  
  const focusTextInput = () => {
    textInput.current.focus();
  };
  
  return (
    <div>
      <input type="text" ref={textInput} />
      <input
        type="button"
        value="Focus the text input"
        onClick={focusTextInput}
      />
    </div>
  );
}
\`\`\``,

  comparisons: `## 跨框架 ref 概念对比

### React vs Vue：响应式引用对比

#### React useRef
\`\`\`jsx
function ReactRef() {
  const countRef = useRef(0);
  
  const increment = () => {
    countRef.current += 1;
    // 不会触发重新渲染
  };
  
  return <button onClick={increment}>Count: {countRef.current}</button>;
}
\`\`\`

#### Vue 3 ref
\`\`\`vue
<template>
  <button @click="increment">Count: {{ count }}</button>
</template>

<script setup>
import { ref } from 'vue'

const count = ref(0)

const increment = () => {
  count.value += 1  // 会触发重新渲染
}
</script>
\`\`\`

**关键差异：**
- **React useRef**：不触发重新渲染，主要用于 DOM 访问和值存储
- **Vue ref**：响应式引用，变化时触发更新`,

  philosophy: `## useRef 的设计哲学

### 1. 可变性与不可变性的平衡

React 的核心理念是数据不可变，但 \`useRef\` 提供了一个"逃生舱"，允许在特定场景下使用可变状态。

\`\`\`jsx
function ImmutableVsMutable() {
  // 不可变状态 - 触发重新渲染
  const [count, setCount] = useState(0);
  
  // 可变引用 - 不触发重新渲染
  const renderCount = useRef(0);
  
  // 每次渲染都增加计数，但不会导致无限循环
  renderCount.current += 1;
  
  return (
    <div>
      <p>状态计数: {count}</p>
      <p>渲染计数: {renderCount.current}</p>
      <button onClick={() => setCount(c => c + 1)}>
        更新状态
      </button>
    </div>
  );
}
\`\`\``,

  presentValue: `## useRef 在现代 React 开发中的价值

### 1. 微前端架构中的作用

在微前端架构中，useRef 提供了跨应用通信的桥梁。

\`\`\`jsx
function MicrofrontendContainer() {
  const containerRef = useRef(null);
  const microAppRef = useRef(null);
  
  useEffect(() => {
    // 动态加载微前端应用
    import('./micro-app').then(({ MicroApp }) => {
      const app = new MicroApp({
        container: containerRef.current,
        props: { /* 传递给微应用的属性 */ }
      });
      
      microAppRef.current = app;
      app.mount();
    });
    
    return () => {
      // 清理微应用
      microAppRef.current?.unmount();
    };
  }, []);
  
  return <div ref={containerRef} id="micro-app-container" />;
}
\`\`\`

### 2. 性能监控和调试

useRef 在现代性能监控中扮演重要角色。

\`\`\`jsx
function PerformanceMonitoredComponent() {
  const renderTimesRef = useRef([]);
  const componentMountTimeRef = useRef(null);
  
  // 记录组件挂载时间
  useEffect(() => {
    componentMountTimeRef.current = performance.now();
    
    return () => {
      const mountTime = componentMountTimeRef.current;
      const unmountTime = performance.now();
      console.log(\`组件生命周期: \${unmountTime - mountTime}ms\`);
    };
  }, []);
  
  return <div>组件内容</div>;
}
\`\`\``
};

export default knowledgeArchaeology; 