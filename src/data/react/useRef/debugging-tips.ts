import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  overview: `useRef虽然概念简单，但在实际使用中容易出现DOM引用为null、时机错误、内存泄漏等调试难题。这些问题往往表现为运行时错误、性能问题或不一致的行为，需要系统的调试方法来快速定位和解决。

本指南提供4种核心调试策略、6个实用调试工具，以及5个预防技巧，帮助开发者掌握useRef的调试精髓，提升开发效率和代码质量。`,
  
  troubleshooting: [
    {
      symptom: 'ref.current为null导致运行时错误',
      possibleCauses: [
        '在组件挂载前访问ref.current',
        '条件渲染导致DOM元素未挂载',
        'ref回调函数中的时机问题'
      ],
      solutions: [
        '在useEffect或事件处理器中访问ref，确保DOM已挂载',
        '添加空值检查：ref.current?.method()',
        '使用ref回调函数处理动态挂载的元素'
      ],
      codeExample: `// 调试ref为null的问题
function DebugRefNull() {
  const inputRef = useRef<HTMLInputElement>(null);
  const [show, setShow] = useState(false);
  
  // ❌ 错误：在渲染期间访问ref
  console.log('渲染时ref:', inputRef.current); // 可能为null
  
  // ❌ 错误：在条件渲染前访问ref
  const handleFocus = () => {
    inputRef.current.focus(); // 如果show为false会报错
  };
  
  // ✅ 正确：在useEffect中访问ref
  useEffect(() => {
    if (show && inputRef.current) {
      console.log('DOM已挂载:', inputRef.current);
      inputRef.current.focus();
    }
  }, [show]);
  
  // ✅ 正确：使用可选链操作符
  const handleFocusSafe = () => {
    inputRef.current?.focus();
  };
  
  // ✅ 正确：使用ref回调处理动态挂载
  const refCallback = useCallback((element: HTMLInputElement | null) => {
    if (element) {
      console.log('元素已挂载:', element);
      element.focus();
    }
  }, []);
  
  return (
    <div>
      {show && <input ref={inputRef} />}
      {show && <input ref={refCallback} />}
      <button onClick={() => setShow(!show)}>切换显示</button>
      <button onClick={handleFocusSafe}>安全聚焦</button>
    </div>
  );
}`,
      severity: 'high'
    },
    {
      symptom: 'ref值更新但UI没有响应',
      possibleCauses: [
        '误以为修改ref.current会触发重渲染',
        '在渲染期间读取ref.current的值',
        '依赖ref.current的计算没有正确更新'
      ],
      solutions: [
        '理解ref不会触发重渲染，需要配合state使用',
        '使用useEffect监听ref变化并更新state',
        '在事件处理器中同时更新ref和state'
      ],
      codeExample: `// 调试ref更新不触发渲染的问题
function DebugRefUpdate() {
  const countRef = useRef(0);
  const [displayCount, setDisplayCount] = useState(0);
  
  // ❌ 错误：只更新ref，UI不会更新
  const incrementBad = () => {
    countRef.current += 1;
    console.log('ref更新了:', countRef.current);
    // UI仍然显示旧值，因为没有触发重渲染
  };
  
  // ✅ 正确：同时更新ref和state
  const incrementGood = () => {
    countRef.current += 1;
    setDisplayCount(countRef.current);
    console.log('ref和state都更新了:', countRef.current);
  };
  
  // 调试工具：监控ref变化
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('定时检查ref值:', countRef.current);
      console.log('当前显示值:', displayCount);
    }, 1000);
    
    return () => clearInterval(interval);
  }, [displayCount]);
  
  return (
    <div>
      <p>显示值: {displayCount}</p>
      <p>ref值: {countRef.current}</p> {/* 这里总是显示初始值 */}
      <button onClick={incrementBad}>只更新ref(错误)</button>
      <button onClick={incrementGood}>正确更新</button>
    </div>
  );
}`,
      severity: 'medium'
    },
    {
      symptom: 'ref导致的内存泄漏',
      possibleCauses: [
        'ref持有大对象的引用未及时清理',
        '事件监听器通过ref引用组件实例',
        '定时器或订阅通过ref持有闭包引用'
      ],
      solutions: [
        '在组件卸载时清理ref引用',
        '使用WeakMap或WeakSet存储临时引用',
        '及时移除事件监听器和清理定时器'
      ],
      codeExample: `// 调试ref内存泄漏问题
function DebugMemoryLeak() {
  const dataRef = useRef<any[]>([]);
  const timerRef = useRef<number>();
  const listenerRef = useRef<(() => void) | null>(null);
  
  useEffect(() => {
    // ❌ 潜在内存泄漏：大数据对象持续增长
    const loadData = () => {
      const newData = Array.from({ length: 10000 }, (_, i) => ({
        id: i,
        data: new Array(1000).fill(Math.random())
      }));
      dataRef.current.push(...newData); // 数据不断累积
    };
    
    // ❌ 潜在内存泄漏：定时器持有ref引用
    timerRef.current = window.setInterval(() => {
      loadData();
      console.log('数据量:', dataRef.current.length);
    }, 1000);
    
    // ❌ 潜在内存泄漏：事件监听器持有ref
    const handleResize = () => {
      console.log('窗口大小变化，数据量:', dataRef.current.length);
    };
    listenerRef.current = handleResize;
    window.addEventListener('resize', handleResize);
    
    // ✅ 正确：清理函数
    return () => {
      // 清理定时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      
      // 清理事件监听器
      if (listenerRef.current) {
        window.removeEventListener('resize', listenerRef.current);
      }
      
      // 清理大数据引用
      dataRef.current = [];
      
      console.log('组件卸载，已清理所有引用');
    };
  }, []);
  
  // 内存使用监控工具
  const checkMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      console.log('内存使用情况:', {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + 'MB',
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
      });
    }
  };
  
  return (
    <div>
      <p>数据量: {dataRef.current.length}</p>
      <button onClick={checkMemoryUsage}>检查内存使用</button>
    </div>
  );
}`,
      severity: 'high'
    }
  ],
  
  tools: [
    {
      name: 'React DevTools',
      description: 'React官方开发工具，用于检查组件状态和ref引用',
      usage: '在Components面板中查看组件的ref属性和引用状态',
      category: '浏览器扩展',
      documentation: 'https://react.dev/learn/react-developer-tools'
    },
    {
      name: 'Chrome DevTools Console',
      description: '浏览器控制台，用于运行调试脚本和监控ref状态',
      usage: '使用console.log记录ref变化，设置断点调试ref访问时机',
      category: '浏览器工具',
      documentation: 'https://developer.chrome.com/docs/devtools/console/'
    },
    {
      name: 'Memory Profiler',
      description: 'Chrome内存分析工具，用于检测ref相关的内存泄漏',
      usage: '对比组件挂载前后的内存快照，查找ref持有的大对象引用',
      category: '性能工具',
      documentation: 'https://developer.chrome.com/docs/devtools/memory/'
    }
  ],
  
  bestPractices: [
    '在useEffect或事件处理器中访问ref，避免在渲染期间访问',
    '始终进行空值检查，使用可选链操作符(?.)访问ref.current',
    '使用ref回调函数处理动态挂载的元素',
    '在组件卸载时清理ref持有的大对象引用',
    '建立ref状态监控机制，及时发现异常情况'
  ],
  
  commonMistakes: [
    {
      mistake: '在渲染期间访问ref.current',
      consequence: '可能获取到null值或过期值，导致运行时错误',
      solution: '将ref访问移到useEffect或事件处理器中',
      prevention: '建立代码审查规则，检查ref.current的访问时机'
    },
    {
      mistake: '期望修改ref.current会触发重渲染',
      consequence: 'UI不会更新，导致显示与实际状态不一致',
      solution: '理解ref的特性，需要时配合state使用',
      prevention: '在团队中明确ref与state的区别和使用场景'
    },
    {
      mistake: '忘记清理ref持有的资源',
      consequence: '导致内存泄漏，影响应用性能',
      solution: '在useEffect的清理函数中释放ref引用',
      prevention: '建立内存监控机制，定期检查内存使用情况'
    }
  ]
};

export default debuggingTips;
