import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: 'DOM操作性能优化',
      description: '通过useRef优化DOM操作，减少不必要的查询和重复操作，提升DOM交互性能',
      techniques: [
        {
          name: '批量DOM操作优化',
          description: '使用useRef缓存DOM引用，避免重复查询，实现高效的批量DOM操作',
          code: `// ❌ 性能问题 - 重复DOM查询
function SlowListComponent({ items }) {
  const handleBatchUpdate = () => {
    items.forEach((item, index) => {
      // 每次都重新查询DOM元素
      const element = document.getElementById(\`item-\${index}\`);
      if (element) {
        element.style.backgroundColor = item.color;
        element.style.transform = \`translateX(\${item.offset}px)\`;
      }
    });
  };

  return (
    <div>
      {items.map((item, index) => (
        <div key={index} id={\`item-\${index}\`}>
          {item.content}
        </div>
      ))}
      <button onClick={handleBatchUpdate}>更新样式</button>
    </div>
  );
}

// ✅ 性能优化 - 使用useRef缓存DOM引用
function FastListComponent({ items }) {
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);
  
  // 确保refs数组长度与items匹配
  useEffect(() => {
    itemRefs.current = itemRefs.current.slice(0, items.length);
  }, [items.length]);

  const handleBatchUpdate = () => {
    // 使用requestAnimationFrame优化动画性能
    requestAnimationFrame(() => {
      itemRefs.current.forEach((element, index) => {
        if (element && items[index]) {
          // 直接操作缓存的DOM引用，无需查询
          element.style.backgroundColor = items[index].color;
          element.style.transform = \`translateX(\${items[index].offset}px)\`;
        }
      });
    });
  };

  return (
    <div>
      {items.map((item, index) => (
        <div
          key={index}
          ref={el => itemRefs.current[index] = el}
        >
          {item.content}
        </div>
      ))}
      <button onClick={handleBatchUpdate}>更新样式</button>
    </div>
  );
}`,
          impact: 'high',
          difficulty: 'medium'
        },
        {
          name: '滚动性能优化',
          description: '使用useRef优化滚动事件处理，避免频繁的状态更新和重渲染',
          code: `// ❌ 性能问题 - 滚动时频繁更新状态
function SlowScrollComponent() {
  const [scrollY, setScrollY] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY); // 每次滚动都触发重渲染
      setIsScrolling(true);
      
      // 防抖设置scrolling状态
      clearTimeout(scrollTimer);
      const scrollTimer = setTimeout(() => {
        setIsScrolling(false);
      }, 150);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return <div>滚动位置: {scrollY}</div>;
}

// ✅ 性能优化 - 使用useRef避免频繁重渲染
function FastScrollComponent() {
  const scrollYRef = useRef(0);
  const isScrollingRef = useRef(false);
  const scrollTimerRef = useRef<number>();
  const displayRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      scrollYRef.current = window.scrollY;
      isScrollingRef.current = true;
      
      // 直接更新DOM，避免React重渲染
      if (displayRef.current) {
        displayRef.current.textContent = \`滚动位置: \${scrollYRef.current}\`;
      }
      
      // 清除之前的定时器
      if (scrollTimerRef.current) {
        clearTimeout(scrollTimerRef.current);
      }
      
      scrollTimerRef.current = window.setTimeout(() => {
        isScrollingRef.current = false;
      }, 150);
    };

    // 使用passive监听器提升性能
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimerRef.current) {
        clearTimeout(scrollTimerRef.current);
      }
    };
  }, []);

  return <div ref={displayRef}>滚动位置: 0</div>;
}`,
          impact: 'high',
          difficulty: 'medium'
        }
      ]
    },
    {
      title: '值存储性能优化',
      description: '利用useRef的不触发重渲染特性，优化频繁变化但不影响UI的数据存储',
      techniques: [
        {
          name: '避免闭包陷阱',
          description: '使用useRef存储最新值，避免事件处理器中的闭包陷阱',
          code: `// ❌ 性能问题 - 闭包陷阱导致内存泄漏
function ProblematicComponent() {
  const [count, setCount] = useState(0);
  const [data, setData] = useState([]);

  useEffect(() => {
    const interval = setInterval(() => {
      // 闭包捕获了初始的count和data值
      console.log('当前count:', count); // 始终是0
      console.log('当前data长度:', data.length); // 始终是0
      
      // 这里无法获取到最新的状态值
      if (count > 10) {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []); // 空依赖数组导致闭包陷阱

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(c => c + 1)}>增加</button>
    </div>
  );
}

// ✅ 性能优化 - 使用useRef避免闭包陷阱
function OptimizedComponent() {
  const [count, setCount] = useState(0);
  const [data, setData] = useState([]);
  
  // 使用ref存储最新值
  const countRef = useRef(count);
  const dataRef = useRef(data);
  
  // 保持ref与state同步
  useEffect(() => {
    countRef.current = count;
  }, [count]);
  
  useEffect(() => {
    dataRef.current = data;
  }, [data]);

  useEffect(() => {
    const interval = setInterval(() => {
      // 通过ref访问最新值，无闭包陷阱
      console.log('当前count:', countRef.current);
      console.log('当前data长度:', dataRef.current.length);
      
      // 可以正确获取最新状态
      if (countRef.current > 10) {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []); // 安全的空依赖数组

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(c => c + 1)}>增加</button>
    </div>
  );
}`,
          impact: 'high',
          difficulty: 'easy'
        },
        {
          name: '缓存昂贵计算结果',
          description: '使用useRef缓存计算结果，避免重复的昂贵计算',
          code: `// ❌ 性能问题 - 每次渲染都重新计算
function ExpensiveComponent({ data, threshold }) {
  // 每次渲染都执行昂贵的计算
  const expensiveResult = data.filter(item => 
    complexCalculation(item) > threshold
  ).map(item => 
    anotherExpensiveOperation(item)
  );

  return <div>结果数量: {expensiveResult.length}</div>;
}

// ✅ 性能优化 - 使用useRef缓存计算结果
function OptimizedExpensiveComponent({ data, threshold }) {
  const cacheRef = useRef<{
    data: any[];
    threshold: number;
    result: any[];
  } | null>(null);

  // 检查是否需要重新计算
  const needsRecalculation = !cacheRef.current || 
    cacheRef.current.data !== data || 
    cacheRef.current.threshold !== threshold;

  if (needsRecalculation) {
    // 只在必要时重新计算
    const result = data.filter(item => 
      complexCalculation(item) > threshold
    ).map(item => 
      anotherExpensiveOperation(item)
    );
    
    cacheRef.current = { data, threshold, result };
  }

  return <div>结果数量: {cacheRef.current.result.length}</div>;
}

// 更高级的缓存策略
function useExpensiveCalculation(data: any[], threshold: number) {
  const cacheRef = useRef(new Map());
  
  return useMemo(() => {
    const cacheKey = \`\${JSON.stringify(data)}-\${threshold}\`;
    
    if (cacheRef.current.has(cacheKey)) {
      return cacheRef.current.get(cacheKey);
    }
    
    const result = data.filter(item => 
      complexCalculation(item) > threshold
    ).map(item => 
      anotherExpensiveOperation(item)
    );
    
    // 限制缓存大小，避免内存泄漏
    if (cacheRef.current.size > 10) {
      const firstKey = cacheRef.current.keys().next().value;
      cacheRef.current.delete(firstKey);
    }
    
    cacheRef.current.set(cacheKey, result);
    return result;
  }, [data, threshold]);
}`,
          impact: 'high',
          difficulty: 'hard'
        }
      ]
    }
  ],

  performanceMetrics: {
    domQueryTime: {
      description: '测量DOM查询操作的时间开销，useRef可以显著减少查询次数',
      tool: 'Performance API',
      example: 'performance.mark("dom-start"); element = document.getElementById("test"); performance.mark("dom-end");'
    },
    renderCount: {
      description: '监控组件重渲染次数，useRef修改不会触发重渲染',
      tool: 'React DevTools Profiler',
      example: '在Profiler中查看使用useRef前后的渲染次数对比'
    },
    memoryUsage: {
      description: '监控ref对象的内存占用和缓存数据的内存使用',
      tool: 'Chrome DevTools Memory Profiler',
      example: '对比使用useRef缓存前后的内存快照，关注对象引用的变化'
    }
  },

  bestPractices: [
    '使用useRef缓存DOM引用，避免重复的DOM查询操作',
    '在事件处理器中使用ref访问最新状态，避免闭包陷阱',
    '合理使用ref缓存计算结果，但要注意内存使用',
    '在滚动、拖拽等高频事件中使用ref避免频繁重渲染',
    '使用ref存储定时器ID、订阅对象等不影响渲染的数据',
    '配合requestAnimationFrame优化动画性能',
    '在大列表中使用ref缓存元素引用，提升批量操作性能',
    '注意ref的生命周期，及时清理不需要的引用避免内存泄漏'
  ],

  commonPitfalls: [
    {
      issue: '在渲染过程中访问ref.current导致不一致',
      cause: 'ref.current在渲染期间可能为null或过期值，导致错误或不一致的行为',
      solution: '在useEffect或事件处理器中访问ref.current，确保DOM已经更新'
    },
    {
      issue: 'ref缓存过多数据导致内存泄漏',
      cause: '无限制地在ref中缓存数据，特别是大对象或数组，导致内存持续增长',
      solution: '设置缓存大小限制，定期清理不需要的缓存数据，使用WeakMap等弱引用'
    },
    {
      issue: '过度使用ref避免重渲染导致状态不同步',
      cause: '为了性能而过度使用ref存储状态，导致UI与实际数据不同步',
      solution: '只在确实不需要触发重渲染的场景使用ref，保持UI状态的一致性'
    }
  ]
};

export default performanceOptimization;
