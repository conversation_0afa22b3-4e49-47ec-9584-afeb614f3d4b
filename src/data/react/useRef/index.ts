import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useRefApi: ApiItem = {
  id: 'useRef',
  title: 'useRef',
  subtitle: 'React Hook，用于创建可变的ref对象，可以持久化存储值而不触发组件重新渲染',
  description: 'useRef 是 React 提供的核心 Hook，主要用于两种场景：DOM 元素引用和跨渲染周期的值存储。与 state 不同，ref 的改变不会触发组件重新渲染，使其成为性能优化的重要工具。',
  
  // 必需的基础字段
  syntax: 'const refContainer = useRef(initialValue)',
  example: `import React, { useRef, useEffect } from 'react';

function TextInput() {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // 组件挂载后自动聚焦
    inputRef.current?.focus();
  }, []);

  const handleClick = () => {
    // 通过ref访问DOM元素
    inputRef.current?.focus();
  };

  return (
    <div>
      <input 
        ref={inputRef} 
        type="text" 
        placeholder="自动聚焦的输入框" 
      />
      <button onClick={handleClick}>聚焦输入框</button>
    </div>
  );
}

export default TextInput;`,
  
  // 基础信息
  basicInfo,
  
  // 业务场景（3个不同复杂度）
  businessScenarios,
  
  // 实现原理
  implementation,
  
  // 面试题目（5个高质量题目）
  interviewQuestions,
  
  // 常见问题（4个实际问题）
  commonQuestions,
  
  // 知识考古
  knowledgeArchaeology,

  // 性能优化
  performanceOptimization,

  // 调试技巧
  debuggingTips,

  // 本质洞察
  essenceInsights,

  // 元数据
  category: 'React Hooks',
  tags: ['Hook', 'DOM操作', '引用存储', '性能优化', 'ref转发'],
  difficulty: 'beginner',
  
  // 统计信息
  readTime: '15分钟',
  practiceTime: '30分钟',
  
  // 相关主题
  relatedAPIs: ['createRef', 'forwardRef', 'useImperativeHandle', 'useCallback', 'useEffect'],
  
  // 版本信息
  version: 'React 16.8+',
  
  // 更新信息
  lastUpdated: '2024-12-27',
  
  // 完成状态
  isCompleted: true,
  completedTabs: [
    'basicInfo',
    'businessScenarios',
    'implementation',
    'interviewQuestions',
    'commonQuestions',
    'knowledgeArchaeology',
    'performanceOptimization',
    'debuggingTips',
    'essenceInsights'
  ]
};

export default useRefApi; 