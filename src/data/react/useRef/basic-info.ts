import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "useRef是React提供的Hook，用于创建可变的ref对象。它返回一个包含current属性的可变对象，该对象在组件的整个生命周期内保持不变。与state不同，修改ref.current不会触发组件重新渲染，使其成为访问DOM元素和存储实例值的完美工具。",
  
  introduction: `useRef 是 React 提供的核心 Hook，主要用于两种场景：DOM 元素引用和跨渲染周期的值存储。与 state 不同，ref 的改变不会触发组件重新渲染，使其成为性能优化的重要工具。

## 🎯 核心特性

- **持久存储**：跨渲染周期保持值的引用
- **不触发渲染**：修改 ref.current 不会导致组件重新渲染  
- **DOM访问**：直接操作 DOM 元素的最佳方式
- **实例存储**：保存定时器ID、上一个值等实例数据
- **第三方集成**：与非React库集成的桥梁`,

  syntax: `const refContainer = useRef(initialValue);
// refContainer.current - 访问或修改存储的值

// 常用模式：
const inputRef = useRef<HTMLInputElement>(null);
const valueRef = useRef<number>(0);
const timerRef = useRef<number | null>(null);`,

  quickExample: `import React, { useRef, useEffect } from 'react';

function TextInput() {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // 组件挂载后自动聚焦
    inputRef.current?.focus();
  }, []);

  return <input ref={inputRef} type="text" placeholder="自动聚焦的输入框" />;
}`,

  parameters: [
    {
      name: "initialValue",
      type: "T",
      description: "ref对象的初始值，通常传入null用于DOM引用，或传入具体值用于存储数据",
      required: false,
      default: "undefined",
      details: "可以是任何类型的值，包括null、基本类型、对象或函数"
    }
  ],

  returnValue: {
    type: "MutableRefObject<T>",
    description: "返回一个包含current属性的可变对象，其中T是initialValue的类型"
  },

  keyFeatures: [
    {
      feature: "持久引用存储",
      description: "在组件重新渲染时保持相同的对象引用",
      importance: "critical",
      details: "ref对象在组件的整个生命周期内保持不变，是存储跨渲染数据的理想选择"
    },
    {
      feature: "不触发重新渲染",
      description: "修改ref.current不会导致组件重新渲染",
      importance: "critical",
      details: "这是useRef与useState的关键区别，适用于不需要响应式更新的数据"
    },
    {
      feature: "DOM元素访问",
      description: "直接访问和操作DOM元素的首选方法",
      importance: "high",
      details: "通过ref可以直接调用DOM方法，如focus()、scrollTo()等"
    },
    {
      feature: "定时器和订阅管理",
      description: "存储定时器ID、事件监听器等需要清理的资源",
      importance: "high",
      details: "在useEffect中创建的定时器或订阅可以存储在ref中，便于清理"
    }
  ],

  commonUseCases: [
    {
      title: "DOM元素引用",
      description: "获取DOM元素的引用，进行直接操作",
      code: `import { useRef, useEffect } from 'react';

function AutoFocusInput() {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // 组件挂载后自动聚焦
    inputRef.current?.focus();
  }, []);

  const handleClear = () => {
    if (inputRef.current) {
      inputRef.current.value = '';
      inputRef.current.focus();
    }
  };

  return (
    <div>
      <input ref={inputRef} type="text" placeholder="自动聚焦" />
      <button onClick={handleClear}>清空并聚焦</button>
    </div>
  );
}`
    },
    {
      title: "存储前一个值",
      description: "跨渲染周期保存上一次的状态值",
      code: `import { useRef, useEffect, useState } from 'react';

function usePrevious<T>(value: T): T | undefined {
  const ref = useRef<T>();
  
  useEffect(() => {
    ref.current = value;
  });
  
  return ref.current;
}

function Counter() {
  const [count, setCount] = useState(0);
  const prevCount = usePrevious(count);

  return (
    <div>
      <p>当前值: {count}</p>
      <p>前一个值: {prevCount}</p>
      <button onClick={() => setCount(c => c + 1)}>增加</button>
    </div>
  );
}`
    },
    {
      title: "定时器管理",
      description: "存储定时器ID，便于清理和控制",
      code: `import { useRef, useState, useCallback } from 'react';

function Timer() {
  const [count, setCount] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const timerRef = useRef<number | null>(null);

  const startTimer = useCallback(() => {
    if (!isRunning) {
      timerRef.current = window.setInterval(() => {
        setCount(c => c + 1);
      }, 1000);
      setIsRunning(true);
    }
  }, [isRunning]);

  const stopTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      setIsRunning(false);
    }
  }, []);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  return (
    <div>
      <p>计时: {count}秒</p>
      <button onClick={startTimer} disabled={isRunning}>
        开始
      </button>
      <button onClick={stopTimer} disabled={!isRunning}>
        停止
      </button>
    </div>
  );
}`
    }
  ],

  warnings: [
    "不要在渲染期间读取或写入ref.current，这可能导致不一致的行为",
    "初始渲染时DOM ref.current可能为null，需要进行空值检查",
    "ref的修改是同步的，但不会触发重新渲染，可能导致UI与数据不同步"
  ],

  notes: [
    "useRef在组件的整个生命周期内返回相同的ref对象",
    "可以存储任何可变值，不仅仅是DOM引用",
    "与createRef不同，useRef不会在每次渲染时创建新对象",
    "ref回调函数在DOM节点挂载和卸载时被调用"
  ],

  limitations: [
    "修改ref.current不会触发重新渲染，不适用于需要响应式更新的数据",
    "在服务端渲染(SSR)中，ref.current始终为初始值",
    "不能直接在JSX中使用ref.current，因为它可能在渲染时发生变化"
  ]
};

export default basicInfo; 