import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 'dom-focus-management',
    title: '表单焦点管理',
    description: '在复杂表单中管理输入框焦点，提升用户体验',
    difficulty: 'beginner',
    tags: ['DOM操作', '表单', '用户体验'],
    estimatedTime: '10分钟',
    code: `import React, { useRef, useState } from 'react';

interface FormData {
  username: string;
  email: string;
  password: string;
}

function RegistrationForm() {
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState<Partial<FormData>>({});
  
  // 创建输入框引用
  const usernameRef = useRef<HTMLInputElement>(null);
  const emailRef = useRef<HTMLInputElement>(null);
  const passwordRef = useRef<HTMLInputElement>(null);

  const validateField = (field: keyof FormData): boolean => {
    const value = formData[field];
    let error = '';

    switch (field) {
      case 'username':
        if (!value.trim()) error = '用户名不能为空';
        else if (value.length < 3) error = '用户名至少3个字符';
        break;
      case 'email':
        if (!value.trim()) error = '邮箱不能为空';
        else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)) error = '邮箱格式不正确';
        break;
      case 'password':
        if (!value.trim()) error = '密码不能为空';
        else if (value.length < 6) error = '密码至少6个字符';
        break;
    }

    setErrors(prev => ({ ...prev, [field]: error }));
    return !error;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 验证所有字段
    const isUsernameValid = validateField('username');
    const isEmailValid = validateField('email');
    const isPasswordValid = validateField('password');

    if (!isUsernameValid) {
      // 聚焦到第一个错误字段
      usernameRef.current?.focus();
      return;
    }
    
    if (!isEmailValid) {
      emailRef.current?.focus();
      return;
    }
    
    if (!isPasswordValid) {
      passwordRef.current?.focus();
      return;
    }

    alert('注册成功！');
  };

  const handleFieldChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除错误信息
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="max-w-md mx-auto p-6 bg-white rounded-lg shadow">
      <h2 className="text-2xl font-bold mb-6 text-center">用户注册</h2>
      
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">用户名</label>
        <input
          ref={usernameRef}
          type="text"
          value={formData.username}
          onChange={(e) => handleFieldChange('username', e.target.value)}
          onBlur={() => validateField('username')}
          className={\`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 \${
            errors.username ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'
          }\`}
          placeholder="请输入用户名"
        />
        {errors.username && <p className="mt-1 text-sm text-red-500">{errors.username}</p>}
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">邮箱</label>
        <input
          ref={emailRef}
          type="email"
          value={formData.email}
          onChange={(e) => handleFieldChange('email', e.target.value)}
          onBlur={() => validateField('email')}
          className={\`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 \${
            errors.email ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'
          }\`}
          placeholder="请输入邮箱"
        />
        {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">密码</label>
        <input
          ref={passwordRef}
          type="password"
          value={formData.password}
          onChange={(e) => handleFieldChange('password', e.target.value)}
          onBlur={() => validateField('password')}
          className={\`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 \${
            errors.password ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'
          }\`}
          placeholder="请输入密码"
        />
        {errors.password && <p className="mt-1 text-sm text-red-500">{errors.password}</p>}
      </div>

      <button 
        type="submit"
        className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        注册
      </button>
    </form>
  );
}

export default RegistrationForm;`,
    explanation: '这个示例展示了如何使用 useRef 来管理表单中的焦点。当表单验证失败时，自动聚焦到第一个错误的输入框，大大提升了用户体验。'
  },
  {
    id: 'timer-management',
    title: '定时器和状态管理',
    description: '使用useRef存储定时器ID和跨渲染的值，避免闭包陷阱',
    difficulty: 'intermediate',
    tags: ['定时器', '状态管理', '性能优化'],
    estimatedTime: '15分钟',
    code: `import React, { useState, useRef, useEffect, useCallback } from 'react';

interface TimerState {
  isRunning: boolean;
  elapsedTime: number;
  laps: number[];
}

function StopwatchTimer() {
  const [state, setState] = useState<TimerState>({
    isRunning: false,
    elapsedTime: 0,
    laps: []
  });

  // 使用 useRef 存储定时器ID，避免在依赖数组中包含
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  // 存储开始时间，用于计算精确的经过时间
  const startTimeRef = useRef<number>(0);
  
  // 存储上次暂停时的累计时间
  const pausedTimeRef = useRef<number>(0);

  // 更新显示时间的函数
  const updateTimer = useCallback(() => {
    if (startTimeRef.current) {
      const now = Date.now();
      const elapsed = now - startTimeRef.current + pausedTimeRef.current;
      setState(prev => ({ ...prev, elapsedTime: elapsed }));
    }
  }, []);

  // 开始计时器
  const startTimer = useCallback(() => {
    if (!state.isRunning) {
      startTimeRef.current = Date.now();
      setState(prev => ({ ...prev, isRunning: true }));
      
      // 设置定时器
      timerRef.current = setInterval(updateTimer, 10); // 10ms 精度
    }
  }, [state.isRunning, updateTimer]);

  // 暂停计时器
  const pauseTimer = useCallback(() => {
    if (state.isRunning && timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      
      // 保存累计时间
      pausedTimeRef.current = state.elapsedTime;
      setState(prev => ({ ...prev, isRunning: false }));
    }
  }, [state.isRunning, state.elapsedTime]);

  // 重置计时器
  const resetTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    
    startTimeRef.current = 0;
    pausedTimeRef.current = 0;
    setState({
      isRunning: false,
      elapsedTime: 0,
      laps: []
    });
  }, []);

  // 记录圈数
  const recordLap = useCallback(() => {
    if (state.isRunning) {
      setState(prev => ({
        ...prev,
        laps: [prev.elapsedTime, ...prev.laps]
      }));
    }
  }, [state.isRunning]);

  // 格式化时间显示
  const formatTime = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    const ms = Math.floor((milliseconds % 1000) / 10);
    
    return \`\${minutes.toString().padStart(2, '0')}:\${seconds.toString().padStart(2, '0')}.\${ms.toString().padStart(2, '0')}\`;
  };

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  return (
    <div className="max-w-md mx-auto p-6 bg-gray-100 rounded-lg shadow">
      <h2 className="text-3xl font-bold text-center mb-6">秒表计时器</h2>
      
      {/* 主要时间显示 */}
      <div className="text-center mb-6">
        <div className="text-6xl font-mono font-bold text-blue-600">
          {formatTime(state.elapsedTime)}
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="flex justify-center space-x-4 mb-6">
        {!state.isRunning ? (
          <button
            onClick={startTimer}
            className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 font-semibold"
          >
            开始
          </button>
        ) : (
          <button
            onClick={pauseTimer}
            className="px-6 py-3 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 font-semibold"
          >
            暂停
          </button>
        )}
        
        <button
          onClick={resetTimer}
          className="px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 font-semibold"
        >
          重置
        </button>
        
        {state.isRunning && (
          <button
            onClick={recordLap}
            className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 font-semibold"
          >
            记圈
          </button>
        )}
      </div>

      {/* 圈数记录 */}
      {state.laps.length > 0 && (
        <div className="bg-white rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">圈数记录</h3>
          <div className="max-h-40 overflow-y-auto">
            {state.laps.map((lapTime, index) => (
              <div key={index} className="flex justify-between py-2 border-b border-gray-200 last:border-b-0">
                <span className="font-medium">第 {state.laps.length - index} 圈</span>
                <span className="font-mono">{formatTime(lapTime)}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default StopwatchTimer;`,
    explanation: '这个秒表示例展示了 useRef 在定时器管理中的重要作用。通过 ref 存储定时器ID和时间戳，避免了闭包陷阱，确保定时器的准确性和性能。'
  },
  {
    id: 'third-party-integration',
    title: '第三方图表库集成',
    description: '与Chart.js等第三方库集成，使用useRef管理DOM实例',
    difficulty: 'advanced',
    tags: ['第三方库', 'DOM操作', '图表'],
    estimatedTime: '20分钟',
    code: `import React, { useRef, useEffect, useState } from 'react';

// 模拟 Chart.js 的简化接口
interface ChartConfig {
  type: 'line' | 'bar' | 'pie';
  data: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      backgroundColor?: string;
      borderColor?: string;
    }>;
  };
  options?: {
    responsive?: boolean;
    plugins?: {
      title?: {
        display: boolean;
        text: string;
      };
    };
  };
}

// 模拟的 Chart 类
class MockChart {
  private canvas: HTMLCanvasElement;
  private config: ChartConfig;
  
  constructor(canvas: HTMLCanvasElement, config: ChartConfig) {
    this.canvas = canvas;
    this.config = config;
    this.render();
  }
  
  update(config: ChartConfig) {
    this.config = config;
    this.render();
  }
  
  destroy() {
    const ctx = this.canvas.getContext('2d');
    if (ctx) {
      ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
  }
  
  private render() {
    const ctx = this.canvas.getContext('2d');
    if (!ctx) return;
    
    // 清空画布
    ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // 绘制简单的柱状图示例
    const { data } = this.config;
    const maxValue = Math.max(...data.datasets[0].data);
    const barWidth = this.canvas.width / data.labels.length;
    const barMaxHeight = this.canvas.height - 40;
    
    data.datasets[0].data.forEach((value, index) => {
      const barHeight = (value / maxValue) * barMaxHeight;
      const x = index * barWidth + 10;
      const y = this.canvas.height - barHeight - 20;
      
      ctx.fillStyle = data.datasets[0].backgroundColor || '#3B82F6';
      ctx.fillRect(x, y, barWidth - 20, barHeight);
      
      // 绘制标签
      ctx.fillStyle = '#000';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(data.labels[index], x + (barWidth - 20) / 2, this.canvas.height - 5);
      ctx.fillText(value.toString(), x + (barWidth - 20) / 2, y - 5);
    });
  }
}

interface SalesData {
  month: string;
  sales: number;
  profit: number;
}

function SalesChart() {
  const [salesData, setSalesData] = useState<SalesData[]>([
    { month: '1月', sales: 120, profit: 20 },
    { month: '2月', sales: 190, profit: 35 },
    { month: '3月', sales: 300, profit: 45 },
    { month: '4月', sales: 500, profit: 80 },
    { month: '5月', sales: 200, profit: 30 },
    { month: '6月', sales: 300, profit: 50 }
  ]);
  
  const [chartType, setChartType] = useState<'sales' | 'profit'>('sales');
  
  // 使用 useRef 存储 canvas 元素和 chart 实例
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const chartInstanceRef = useRef<MockChart | null>(null);

  // 生成随机数据
  const generateRandomData = () => {
    const months = ['1月', '2月', '3月', '4月', '5月', '6月'];
    const newData = months.map(month => ({
      month,
      sales: Math.floor(Math.random() * 400) + 100,
      profit: Math.floor(Math.random() * 60) + 20
    }));
    setSalesData(newData);
  };

  // 创建图表配置
  const createChartConfig = (): ChartConfig => {
    const dataToShow = chartType === 'sales' ? 'sales' : 'profit';
    const label = chartType === 'sales' ? '销售额' : '利润';
    const color = chartType === 'sales' ? '#3B82F6' : '#10B981';
    
    return {
      type: 'bar',
      data: {
        labels: salesData.map(item => item.month),
        datasets: [{
          label,
          data: salesData.map(item => item[dataToShow]),
          backgroundColor: color,
          borderColor: color
        }]
      },
      options: {
        responsive: true,
        plugins: {
          title: {
            display: true,
            text: \`2024年\${label}统计\`
          }
        }
      }
    };
  };

  // 初始化和更新图表
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // 销毁现有图表实例
    if (chartInstanceRef.current) {
      chartInstanceRef.current.destroy();
    }

    // 创建新的图表实例
    const config = createChartConfig();
    chartInstanceRef.current = new MockChart(canvas, config);

    // 组件卸载时清理图表实例
    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
        chartInstanceRef.current = null;
      }
    };
  }, [salesData, chartType]);

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow">
      <h2 className="text-2xl font-bold mb-6 text-center">销售数据分析</h2>
      
      {/* 控制面板 */}
      <div className="flex justify-between items-center mb-6">
        <div className="space-x-4">
          <button
            onClick={() => setChartType('sales')}
            className={\`px-4 py-2 rounded-lg font-medium \${
              chartType === 'sales' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }\`}
          >
            销售额
          </button>
          <button
            onClick={() => setChartType('profit')}
            className={\`px-4 py-2 rounded-lg font-medium \${
              chartType === 'profit' 
                ? 'bg-green-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }\`}
          >
            利润
          </button>
        </div>
        
        <button
          onClick={generateRandomData}
          className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 font-medium"
        >
          生成新数据
        </button>
      </div>

      {/* 图表容器 */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <canvas
          ref={canvasRef}
          width={800}
          height={400}
          className="w-full h-auto border border-gray-200 rounded"
        />
      </div>

      {/* 数据表格 */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-gray-300">
          <thead className="bg-gray-100">
            <tr>
              <th className="border border-gray-300 px-4 py-2 text-left">月份</th>
              <th className="border border-gray-300 px-4 py-2 text-right">销售额</th>
              <th className="border border-gray-300 px-4 py-2 text-right">利润</th>
            </tr>
          </thead>
          <tbody>
            {salesData.map((item, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="border border-gray-300 px-4 py-2">{item.month}</td>
                <td className="border border-gray-300 px-4 py-2 text-right">¥{item.sales}万</td>
                <td className="border border-gray-300 px-4 py-2 text-right">¥{item.profit}万</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default SalesChart;`,
    explanation: '这个示例展示了如何使用 useRef 与第三方图表库集成。通过 ref 管理 canvas 元素和图表实例，确保在组件更新时正确地创建、更新和销毁图表，避免内存泄漏。'
  }
];

export default businessScenarios; 