import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'combine-reducers',
    question: "如何组合多个reducer来管理复杂状态？",
    answer: `可以通过组合多个reducer来管理不同部分的状态，类似Redux的combineReducers。

当应用状态变得复杂时，可以将状态和reducer拆分成多个部分，然后组合使用。

**手动组合reducer：**
可以创建一个组合函数，将多个reducer合并成一个根reducer。

**使用多个useReducer：**
在组件中使用多个useReducer Hook，分别管理不同的状态部分，然后通过统一的dispatch函数分发action。`,
    code: `function combineReducers(reducers) {
  return (state, action) => {
    const newState = {};
    for (const key in reducers) {
      newState[key] = reducers[key](state[key], action);
    }
    return newState;
  };
}

const rootReducer = combineReducers({
  user: userReducer,
  todos: todosReducer
});

// 使用多个useReducer
function App() {
  const [user, userDispatch] = useReducer(userReducer, null);
  const [todos, todosDispatch] = useReducer(todosReducer, []);
  
  const dispatch = (action) => {
    switch (action.type) {
      case 'SET_USER':
        userDispatch(action);
        break;
      case 'ADD_TODO':
        todosDispatch(action);
        break;
    }
  };
}`,
    tags: ['组合reducer', '状态管理', '架构设计'],
    relatedQuestions: ['reducer-pattern', 'state-normalization']
  }
];

export default commonQuestions; 