import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useReducerApi: ApiItem = {
  id: 'use-reducer',
  title: 'useReducer()',
  description: '管理复杂的组件状态逻辑，是useState的替代方案',
  category: 'React Hooks',
  difficulty: 'medium',
  syntax: 'const [state, dispatch] = useReducer(reducer, initialState, init?)',
  example: `import React, { useReducer } from 'react';

// 定义reducer函数
function reducer(state, action) {
  switch (action.type) {
    case 'increment':
      return { count: state.count + 1 };
    case 'decrement':
      return { count: state.count - 1 };
    case 'reset':
      return { count: 0 };
    default:
      throw new Error('Unknown action type');
  }
}

function Counter() {
  // 使用useReducer
  const [state, dispatch] = useReducer(reducer, { count: 0 });

  return (
    <div>
      <p>Count: {state.count}</p>
      <button onClick={() => dispatch({ type: 'increment' })}>
        +
      </button>
      <button onClick={() => dispatch({ type: 'decrement' })}>
        -
      </button>
      <button onClick={() => dispatch({ type: 'reset' })}>
        Reset
      </button>
    </div>
  );
}

export default Counter;`,
  notes: '⚠️ reducer必须是纯函数，不能包含副作用。dispatch函数的引用是稳定的，不会在重渲染时改变。',
  isNew: false,
  version: 'React 16.8+',
  tags: ['状态管理', 'reducer', '复杂状态', 'dispatch'],
  
  // 标准9个Tab
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useReducerApi; 