import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  commonErrors: [
    {
      error: "在reducer中执行副作用导致行为不可预测",
      cause: "Reducer不是纯函数，包含了异步操作、DOM操作或外部状态修改",
      solution: "将副作用移到组件中，reducer只负责计算新状态",
      code: `// ❌ 错误：在reducer中执行副作用
function badReducer(state, action) {
  switch (action.type) {
    case 'SAVE_DATA':
      // 不应该在reducer中执行API调用
      fetch('/api/save', {
        method: 'POST',
        body: JSON.stringify(state)
      });
      return state;
      
    case 'UPDATE_DOM':
      // 不应该直接操作DOM
      document.title = action.payload;
      return { ...state, title: action.payload };
      
    case 'SET_RANDOM':
      // 不应该有随机性
      return { ...state, value: Math.random() };
  }
}

// ✅ 正确：纯函数reducer + 副作用在组件中
function pureReducer(state, action) {
  switch (action.type) {
    case 'SET_DATA':
      return { ...state, data: action.payload };
      
    case 'SET_TITLE':
      return { ...state, title: action.payload };
      
    case 'SET_VALUE':
      return { ...state, value: action.payload };
  }
}

function Component() {
  const [state, dispatch] = useReducer(pureReducer, initialState);
  
  // 副作用在组件中处理
  const saveData = async () => {
    const response = await fetch('/api/save', {
      method: 'POST',
      body: JSON.stringify(state)
    });
    dispatch({ type: 'SET_DATA', payload: await response.json() });
  };
  
  // DOM操作通过useEffect
  useEffect(() => {
    document.title = state.title;
  }, [state.title]);
  
  // 生成随机数在dispatch前
  const setRandomValue = () => {
    dispatch({ type: 'SET_VALUE', payload: Math.random() });
  };
}`
    },
    {
      error: "直接修改state对象导致组件不更新",
      cause: "JavaScript对象是引用类型，直接修改不会触发React的重渲染机制",
      solution: "始终创建新的对象，使用展开运算符或不可变更新方法",
      code: `// ❌ 错误：直接修改state
function mutatingReducer(state, action) {
  switch (action.type) {
    case 'ADD_ITEM':
      // 直接修改数组
      state.items.push(action.payload);
      return state; // React认为是同一个对象，不会重渲染
      
    case 'UPDATE_USER':
      // 直接修改嵌套对象
      state.user.name = action.payload;
      return state;
      
    case 'DELETE_PROP':
      // 直接删除属性
      delete state[action.key];
      return state;
  }
}

// ✅ 正确：创建新对象
function immutableReducer(state, action) {
  switch (action.type) {
    case 'ADD_ITEM':
      // 创建新数组
      return {
        ...state,
        items: [...state.items, action.payload]
      };
      
    case 'UPDATE_USER':
      // 创建新的嵌套对象
      return {
        ...state,
        user: {
          ...state.user,
          name: action.payload
        }
      };
      
    case 'DELETE_PROP':
      // 创建新对象并排除属性
      const { [action.key]: deleted, ...rest } = state;
      return rest;
  }
}

// 使用immer简化不可变更新
import { produce } from 'immer';

const immerReducer = produce((draft, action) => {
  switch (action.type) {
    case 'ADD_ITEM':
      draft.items.push(action.payload); // 看起来像修改，实际创建新对象
      break;
    case 'UPDATE_USER':
      draft.user.name = action.payload;
      break;
    case 'DELETE_PROP':
      delete draft[action.key];
      break;
  }
});`
    },
    {
      error: "Action type拼写错误或不匹配",
      cause: "字符串常量容易拼错，没有类型检查",
      solution: "使用常量或TypeScript枚举，添加default分支处理",
      code: `// ❌ 容易出错的方式
function reducer(state, action) {
  switch (action.type) {
    case 'INCREMETN': // 拼写错误！
      return { count: state.count + 1 };
  }
}

dispatch({ type: 'INCREMENT' }); // 不会生效，也不会报错

// ✅ 使用常量
const ActionTypes = {
  INCREMENT: 'INCREMENT',
  DECREMENT: 'DECREMENT',
  RESET: 'RESET'
} as const;

function reducer(state, action) {
  switch (action.type) {
    case ActionTypes.INCREMENT:
      return { count: state.count + 1 };
    case ActionTypes.DECREMENT:
      return { count: state.count - 1 };
    case ActionTypes.RESET:
      return { count: 0 };
    default:
      // 开发环境警告未处理的action
      if (process.env.NODE_ENV === 'development') {
        console.warn('Unknown action type:', action.type);
      }
      return state;
  }
}

// ✅ TypeScript方式
type ActionType = 
  | { type: 'INCREMENT' }
  | { type: 'DECREMENT' }
  | { type: 'RESET' }
  | { type: 'SET'; payload: number };

function typedReducer(state: State, action: ActionType): State {
  switch (action.type) {
    case 'INCREMENT':
      return { count: state.count + 1 };
    case 'SET':
      return { count: action.payload }; // TypeScript知道有payload
    // TypeScript会检查是否处理了所有case
  }
}`
    },
    {
      error: "异步操作处理不当导致状态不同步",
      cause: "在异步回调中使用了过时的state值",
      solution: "使用函数式更新或在effect中获取最新状态",
      code: `// ❌ 错误：闭包陷阱
function AsyncComponent() {
  const [state, dispatch] = useReducer(reducer, { count: 0, loading: false });
  
  const fetchAndUpdate = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    
    const data = await fetchData();
    
    // 这里的state.count可能是旧值
    dispatch({ 
      type: 'SET_DATA', 
      payload: { ...data, count: state.count + 1 } 
    });
  };
}

// ✅ 正确方式1：让reducer处理计算
function AsyncComponent() {
  const [state, dispatch] = useReducer(reducer, { count: 0, loading: false });
  
  const fetchAndUpdate = async () => {
    dispatch({ type: 'FETCH_START' });
    
    try {
      const data = await fetchData();
      // 让reducer基于当前state计算新值
      dispatch({ type: 'FETCH_SUCCESS', payload: data });
    } catch (error) {
      dispatch({ type: 'FETCH_ERROR', payload: error });
    }
  };
}

function reducer(state, action) {
  switch (action.type) {
    case 'FETCH_SUCCESS':
      return {
        ...state,
        data: action.payload,
        count: state.count + 1, // reducer中使用当前state
        loading: false
      };
  }
}

// ✅ 正确方式2：使用ref保存最新值
function AsyncComponent() {
  const [state, dispatch] = useReducer(reducer, initialState);
  const stateRef = useRef(state);
  
  // 保持ref同步
  useEffect(() => {
    stateRef.current = state;
  }, [state]);
  
  const fetchAndUpdate = async () => {
    const data = await fetchData();
    // 使用ref中的最新值
    const currentState = stateRef.current;
    dispatch({ 
      type: 'SET_DATA', 
      payload: { ...data, count: currentState.count + 1 } 
    });
  };
}`
    }
  ],
  
  devToolsTips: [
    {
      tool: "React DevTools",
      technique: "使用React DevTools查看useReducer的状态",
      example: `// 1. 给reducer添加displayName便于调试
const [state, dispatch] = useReducer(
  todoReducer,
  initialState
);

// 2. 使用useDebugValue显示自定义信息
function useAppState() {
  const [state, dispatch] = useReducer(reducer, initialState);
  
  // 在DevTools中显示状态摘要
  useDebugValue('Todos: ' + state.todos.length + ', Filter: ' + state.filter);
  
  return [state, dispatch];
}

// 3. 创建调试wrapper
function useDebugReducer(reducer, initialState, name = 'Reducer') {
  const [state, dispatch] = useReducer(reducer, initialState);
  
  // 在DevTools中显示当前状态
  useDebugValue({
    name,
    state,
    lastAction: useRef(null)
  });
  
  // 包装dispatch以记录action
  const debugDispatch = useCallback((action) => {
    console.log('[' + name + '] Dispatching:', action);
    dispatch(action);
  }, [name]);
  
  return [state, debugDispatch];
}`
    },
    {
      tool: "Redux DevTools",
      technique: "将Redux DevTools连接到useReducer",
      example: `// 创建支持Redux DevTools的useReducer
function useReducerWithDevTools(reducer, initialState, name = 'App') {
  const [state, dispatch] = useReducer(reducer, initialState);
  
  const devTools = useMemo(() => {
    if (typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION__) {
      return window.__REDUX_DEVTOOLS_EXTENSION__.connect({
        name,
        features: {
          pause: true,
          lock: true,
          persist: true,
          export: true,
          import: 'custom',
          jump: true,
          skip: true,
          reorder: true,
          dispatch: true,
          test: true
        }
      });
    }
    return null;
  }, [name]);
  
  // 初始化DevTools
  useEffect(() => {
    if (devTools) {
      devTools.init(state);
    }
    
    return () => {
      if (devTools) {
        devTools.disconnect();
      }
    };
  }, [devTools]);
  
  // 包装dispatch
  const enhancedDispatch = useCallback((action) => {
    dispatch(action);
    
    if (devTools) {
      devTools.send(action, state);
    }
  }, [state, devTools]);
  
  // 监听DevTools的时间旅行
  useEffect(() => {
    if (!devTools) return;
    
    const unsubscribe = devTools.subscribe((message) => {
      if (message.type === 'DISPATCH' && message.state) {
        console.log('Time travel to:', message.state);
        // 这里需要特殊处理来恢复状态
      }
    });
    
    return unsubscribe;
  }, [devTools]);
  
  return [state, enhancedDispatch];
}

// 使用
function App() {
  const [state, dispatch] = useReducerWithDevTools(
    rootReducer,
    initialState,
    'MyApp'
  );
  
  // 现在可以在Redux DevTools中查看状态变化了
}`
    },
    {
      tool: "自定义日志",
      technique: "创建详细的日志系统用于开发调试",
      example: `// 创建日志增强的reducer
function createLoggedReducer(reducer, options = {}) {
  const {
    collapsed = true,
    duration = true,
    diff = true,
    colors = {
      title: '#4CAF50',
      prev: '#9E9E9E',
      action: '#03A9F4',
      next: '#4CAF50',
      error: '#F44336'
    }
  } = options;
  
  return (state, action) => {
    const start = performance.now();
    const prevState = state;
    
    try {
      const nextState = reducer(state, action);
      const end = performance.now();
      
      // 日志组
      const groupMethod = collapsed ? 'groupCollapsed' : 'group';
      console[groupMethod](
        '%c' + action.type + (duration ? ' @ ' + new Date().toLocaleTimeString() : ''),
        'color: ' + colors.title + '; font-weight: bold;'
      );
      
      console.log('%cprev state', 'color: ' + colors.prev + '; font-weight: bold;', prevState);
      console.log('%caction', 'color: ' + colors.action + '; font-weight: bold;', action);
      console.log('%cnext state', 'color: ' + colors.next + '; font-weight: bold;', nextState);
      
      if (duration) {
        console.log('%cduration: ' + (end - start).toFixed(2) + 'ms', 'color: gray; font-style: italic;');
      }
      
      if (diff && prevState !== nextState) {
        console.log('%cstate diff', 'color: #FF6B6B; font-weight: bold;', {
          removed: getObjectDiff(prevState, nextState).removed,
          added: getObjectDiff(prevState, nextState).added,
          updated: getObjectDiff(prevState, nextState).updated
        });
      }
      
      console.groupEnd();
      
      return nextState;
    } catch (error) {
      console.error('%cError in reducer for action ' + action.type, 'color: ' + colors.error + ';', error);
      return state;
    }
  };
}

// 使用日志reducer
const [state, dispatch] = useReducer(
  process.env.NODE_ENV === 'development'
    ? createLoggedReducer(rootReducer, { collapsed: false })
    : rootReducer,
  initialState
);

// ... existing code ...
      }),
);`
    }
  ],
  
  troubleshooting: [
    {
      symptom: "状态更新了但组件没有重渲染",
      possibleCauses: [
        "直接修改了状态对象而不是创建新对象",
        "返回了相同的对象引用",
        "组件被React.memo包装但比较函数有问题"
      ],
      solutions: [
        "确保reducer总是返回新对象",
        "使用React DevTools检查组件是否接收到新props",
        "添加日志确认reducer是否被调用"
      ]
    },
    {
      symptom: "dispatch后state没有立即更新",
      possibleCauses: [
        "误解了React的异步更新机制",
        "在dispatch后立即读取state",
        "闭包中使用了旧的state值"
      ],
      solutions: [
        "理解state更新是异步的，会在下次渲染时生效",
        "使用useEffect监听state变化",
        "在reducer中处理依赖当前状态的逻辑"
      ]
    },
    {
      symptom: "reducer被调用多次",
      possibleCauses: [
        "StrictMode导致的双重调用",
        "父组件重渲染导致reducer重新创建",
        "错误地在循环或条件中使用useReducer"
      ],
      solutions: [
        "理解StrictMode的行为是正常的",
        "将reducer定义在组件外部",
        "确保遵循Hooks的使用规则"
      ]
    }
  ]
};

export default debuggingTips; 