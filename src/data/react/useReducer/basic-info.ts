import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "useReducer是React提供的状态管理Hook，它接受一个reducer函数和初始状态，返回当前状态和dispatch函数。useReducer是useState的替代方案，特别适合管理包含多个子值的复杂状态逻辑，或者当下一个状态依赖于之前的状态时。",
  
  syntax: `const [state, dispatch] = useReducer(reducer, initialState);
// 或带有惰性初始化
const [state, dispatch] = useReducer(reducer, initialArg, init);`,

  overview: {
    description: "useReducer是React提供的状态管理Hook，它接受一个reducer函数和初始状态，返回当前状态和dispatch函数。useReducer是useState的替代方案，特别适合管理包含多个子值的复杂状态逻辑，或者当下一个状态依赖于之前的状态时。",
    
    keyFeatures: [
      "接受reducer函数处理状态更新逻辑",
      "通过dispatch派发action来更新状态",
      "支持惰性初始化状态",
      "状态更新逻辑与组件分离，便于测试",
      "适合管理复杂的状态逻辑",
      "可以避免多个useState带来的状态不同步问题"
    ]
  },
  
  basicUsage: {
    description: "useReducer的基本用法展示如何定义reducer函数、初始化状态和派发action",
    examples: [
      {
        title: "基础计数器示例",
        description: "使用useReducer实现一个简单的计数器",
        code: `import { useReducer } from 'react';

// 定义reducer函数
function counterReducer(state, action) {
  switch (action.type) {
    case 'increment':
      return { count: state.count + 1 };
    case 'decrement':
      return { count: state.count - 1 };
    case 'reset':
      return { count: 0 };
    default:
      throw new Error("Unknown action: " + action.type);
  }
}

function Counter() {
  // 使用useReducer
  const [state, dispatch] = useReducer(counterReducer, { count: 0 });
  
  return (
    <div>
      <p>Count: {state.count}</p>
      <button onClick={() => dispatch({ type: 'increment' })}>+</button>
      <button onClick={() => dispatch({ type: 'decrement' })}>-</button>
      <button onClick={() => dispatch({ type: 'reset' })}>Reset</button>
    </div>
  );
}`
      },
      {
        title: "带payload的action",
        description: "action可以携带额外的数据",
        code: `function todoReducer(state, action) {
  switch (action.type) {
    case 'add':
      return {
        todos: [...state.todos, {
          id: Date.now(),
          text: action.payload,
          completed: false
        }]
      };
    case 'toggle':
      return {
        todos: state.todos.map(todo =>
          todo.id === action.payload
            ? { ...todo, completed: !todo.completed }
            : todo
        )
      };
    case 'remove':
      return {
        todos: state.todos.filter(todo => todo.id !== action.payload)
      };
    default:
      return state;
  }
}

function TodoList() {
  const [state, dispatch] = useReducer(todoReducer, { todos: [] });
  const [input, setInput] = useState('');
  
  const handleAdd = () => {
    if (input.trim()) {
      dispatch({ type: 'add', payload: input });
      setInput('');
    }
  };
  
  return (
    <div>
      <input 
        value={input} 
        onChange={(e) => setInput(e.target.value)}
        onKeyPress={(e) => e.key === 'Enter' && handleAdd()}
      />
      <button onClick={handleAdd}>Add Todo</button>
      
      <ul>
        {state.todos.map(todo => (
          <li key={todo.id}>
            <span 
              style={{ textDecoration: todo.completed ? 'line-through' : 'none' }}
              onClick={() => dispatch({ type: 'toggle', payload: todo.id })}
            >
              {todo.text}
            </span>
            <button onClick={() => dispatch({ type: 'remove', payload: todo.id })}>
              Delete
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
}`
      }
    ]
  },
  
  parameters: {
    required: [
      {
        name: "reducer",
        type: "(state, action) => newState",
        description: "纯函数，接收当前state和action，返回新的state。不应有副作用，相同输入应返回相同输出"
      },
      {
        name: "initialState",
        type: "any",
        description: "初始状态值，可以是任何类型。如果提供了init函数，则作为init函数的参数"
      }
    ],
    optional: [
      {
        name: "init",
        type: "(initialArg) => initialState",
        description: "惰性初始化函数，接收initialState作为参数，返回初始状态。用于计算成本较高的初始状态"
      }
    ]
  },
  
  returnValue: {
    type: "[state, dispatch]",
    description: "返回一个包含两个元素的数组",
    details: [
      {
        name: "state",
        description: "当前状态值，初始值为initialState或init(initialState)的返回值"
      },
      {
        name: "dispatch",
        description: "派发函数，用于触发状态更新。接收action作为参数，通常是包含type字段的对象"
      }
    ]
  },
  
  historicalContext: {
    origin: "useReducer在React 16.8中与其他Hooks一起引入，灵感来自Redux的reducer模式",
    evolution: [
      {
        version: "16.8",
        changes: "首次引入useReducer Hook"
      },
      {
        version: "18.0",
        changes: "自动批处理优化了多个dispatch调用的性能"
      }
    ],
    designPhilosophy: "useReducer的设计理念是将状态更新逻辑从组件中抽离，使其更易于测试和理解。它借鉴了Redux的核心思想，但提供了更轻量级的实现，不需要额外的库依赖"
  }
};

export default basicInfo; 