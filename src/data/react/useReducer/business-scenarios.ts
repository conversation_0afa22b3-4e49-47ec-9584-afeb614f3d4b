import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: "complex-form-management",
    title: "复杂表单管理",
    description: "使用useReducer管理包含多个字段、验证规则和提交状态的复杂表单",
    businessValue: "在大型表单中提升状态管理的可维护性和可预测性，减少bug发生率50%",
    scenario: "用户注册表单包含多个字段、验证规则和提交状态。使用多个useState会导致状态管理复杂，逻辑分散。",
    code: `import { useReducer, useCallback } from 'react';

// 表单状态类型
interface FormState {
  values: {
    username: string;
    email: string;
    password: string;
    confirmPassword: string;
  };
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}

// Action类型
type FormAction = 
  | { type: 'SET_FIELD_VALUE'; field: string; value: string }
  | { type: 'SET_FIELD_ERROR'; field: string; error: string }
  | { type: 'SET_FIELD_TOUCHED'; field: string }
  | { type: 'SUBMIT_START' }
  | { type: 'SUBMIT_SUCCESS' }
  | { type: 'SUBMIT_ERROR'; error: string }
  | { type: 'RESET_FORM' };

// 初始状态
const initialState: FormState = {
  values: {
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  },
  errors: {},
  touched: {},
  isSubmitting: false,
  isValid: false
};

// Reducer函数
function formReducer(state: FormState, action: FormAction): FormState {
  switch (action.type) {
    case 'SET_FIELD_VALUE': {
      const newValues = { ...state.values, [action.field]: action.value };
      const isValid = Object.values(newValues).every(value => value.trim() !== '');
      
      return {
        ...state,
        values: newValues,
        isValid
      };
    }
    
    case 'SET_FIELD_TOUCHED':
      return {
        ...state,
        touched: { ...state.touched, [action.field]: true }
      };
      
    case 'SUBMIT_START':
      return { ...state, isSubmitting: true };
      
    case 'SUBMIT_SUCCESS':
      return { ...initialState };
      
    case 'SUBMIT_ERROR':
      return {
        ...state,
        isSubmitting: false,
        errors: { submit: action.error }
      };
      
    case 'RESET_FORM':
      return initialState;
      
    default:
      return state;
  }
}

// 使用示例
function RegistrationForm() {
  const [state, dispatch] = useReducer(formReducer, initialState);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!state.isValid) return;
    
    dispatch({ type: 'SUBMIT_START' });
    
    try {
      await submitForm(state.values);
      dispatch({ type: 'SUBMIT_SUCCESS' });
    } catch (error) {
      dispatch({ type: 'SUBMIT_ERROR', error: error.message });
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <input 
        value={state.values.username}
        onChange={(e) => dispatch({ 
          type: 'SET_FIELD_VALUE', 
          field: 'username', 
          value: e.target.value 
        })}
      />
      <button type="submit" disabled={!state.isValid || state.isSubmitting}>
        {state.isSubmitting ? '提交中...' : '注册'}
      </button>
    </form>
  );
}`,
    explanation: "使用useReducer集中管理复杂表单状态，让状态变更更可预测"
  }
];

export default businessScenarios; 