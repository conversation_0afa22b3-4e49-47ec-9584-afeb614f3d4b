import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: "利用Bailout优化",
      description: "通过返回相同的状态引用来避免不必要的重渲染",
      techniques: [
        {
          name: "条件更新状态",
          description: "只在真正需要时才创建新的状态对象",
          impact: "high",
          difficulty: "easy",
          code: `// ❌ 总是创建新对象
function inefficientReducer(state, action) {
  switch (action.type) {
    case 'UPDATE_NAME':
      // 即使名字相同也会创建新对象
      return { ...state, name: action.payload };
  }
}

// ✅ 条件创建新对象
function efficientReducer(state, action) {
  switch (action.type) {
    case 'UPDATE_NAME':
      // 先检查是否真的需要更新
      if (state.name === action.payload) {
        return state; // 返回相同引用，触发bailout
      }
      return { ...state, name: action.payload };
      
    case 'UPDATE_FILTERS':
      const newFilters = action.payload;
      // 深度比较
      if (JSON.stringify(state.filters) === JSON.stringify(newFilters)) {
        return state;
      }
      return { ...state, filters: newFilters };
  }
}`
        },
        {
          name: "使用不可变更新工具",
          description: "使用immer等库来简化不可变更新并自动优化",
          impact: "medium",
          difficulty: "easy",
          code: `import { produce } from 'immer';

// 使用immer的reducer
const immerReducer = produce((draft, action) => {
  switch (action.type) {
    case 'ADD_TODO':
      draft.todos.push(action.payload);
      break;
      
    case 'UPDATE_TODO':
      const todo = draft.todos.find(t => t.id === action.id);
      if (todo) {
        Object.assign(todo, action.payload);
      }
      break;
      
    case 'TOGGLE_ALL':
      draft.todos.forEach(todo => {
        todo.completed = action.payload;
      });
      break;
  }
});

// immer会自动处理：
// 1. 如果没有修改，返回原对象
// 2. 只创建被修改部分的新对象
// 3. 结构共享未修改的部分`
        }
      ]
    },
    {
      title: "分离State和Dispatch Context",
      description: "避免因状态变化导致只需要dispatch的组件重渲染",
      techniques: [
        {
          name: "双Context模式",
          description: "将状态和dispatch分别放在不同的Context中",
          impact: "high",
          difficulty: "medium",
          code: `// 创建两个独立的Context
const StateContext = createContext();
const DispatchContext = createContext();

function StateProvider({ children }) {
  const [state, dispatch] = useReducer(reducer, initialState);
  
  // dispatch永远不变，不会触发DispatchContext的重渲染
  return (
    <StateContext.Provider value={state}>
      <DispatchContext.Provider value={dispatch}>
        {children}
      </DispatchContext.Provider>
    </StateContext.Provider>
  );
}

// 只需要dispatch的组件
function ActionButton() {
  // 这个组件不会因为state变化而重渲染
  const dispatch = useContext(DispatchContext);
  
  return (
    <button onClick={() => dispatch({ type: 'ACTION' })}>
      Trigger Action
    </button>
  );
}

// 需要state的组件
function StateDisplay() {
  const state = useContext(StateContext);
  return <div>{state.value}</div>;
}

// 自定义Hooks
function useAppState() {
  const state = useContext(StateContext);
  if (!state) throw new Error('Missing StateProvider');
  return state;
}

function useAppDispatch() {
  const dispatch = useContext(DispatchContext);
  if (!dispatch) throw new Error('Missing StateProvider');
  return dispatch;
}`
        }
      ]
    },
    {
      title: "Reducer组合与懒加载",
      description: "按需加载和组合reducer，减少初始加载和计算成本",
      techniques: [
        {
          name: "动态Reducer注入",
          description: "根据需要动态添加reducer，适合大型应用",
          impact: "medium",
          difficulty: "hard",
          code: `// 动态reducer管理
function createReducerManager(initialReducers) {
  const reducers = { ...initialReducers };
  let combinedReducer = combineReducers(reducers);
  let keysToRemove = [];
  
  return {
    getReducerMap: () => reducers,
    
    reduce: (state, action) => {
      if (keysToRemove.length > 0) {
        state = { ...state };
        keysToRemove.forEach(key => delete state[key]);
        keysToRemove = [];
      }
      return combinedReducer(state, action);
    },
    
    add: (key, reducer) => {
      if (!key || reducers[key]) return;
      reducers[key] = reducer;
      combinedReducer = combineReducers(reducers);
    },
    
    remove: (key) => {
      if (!key || !reducers[key]) return;
      delete reducers[key];
      keysToRemove.push(key);
      combinedReducer = combineReducers(reducers);
    }
  };
}

// 使用动态reducer
function DynamicStateProvider({ children }) {
  const reducerManager = useMemo(
    () => createReducerManager({ core: coreReducer }),
    []
  );
  
  const [state, dispatch] = useReducer(
    reducerManager.reduce,
    initialState
  );
  
  // 提供注入reducer的方法
  const injectReducer = useCallback((key, reducer) => {
    reducerManager.add(key, reducer);
    dispatch({ type: '@@REDUCER_INJECTED', key });
  }, [reducerManager]);
  
  return (
    <StateContext.Provider value={{ state, dispatch, injectReducer }}>
      {children}
    </StateContext.Provider>
  );
}

// 在组件中动态注入reducer
function FeatureComponent() {
  const { injectReducer } = useContext(StateContext);
  
  useEffect(() => {
    // 懒加载feature的reducer
    import('./featureReducer').then(module => {
      injectReducer('feature', module.default);
    });
  }, [injectReducer]);
  
  return <div>Feature Component</div>;
}`
        },
        {
          name: "Memoized Selectors",
          description: "使用记忆化选择器避免重复计算",
          impact: "high",
          difficulty: "medium",
          code: `// 创建记忆化的选择器
const createSelector = (selector, equalityFn = Object.is) => {
  let lastArgs;
  let lastResult;
  
  return (...args) => {
    if (lastArgs && args.every((arg, i) => equalityFn(arg, lastArgs[i]))) {
      return lastResult;
    }
    
    lastArgs = args;
    lastResult = selector(...args);
    return lastResult;
  };
};

// 使用选择器
const selectTodos = state => state.todos;
const selectFilter = state => state.filter;

const selectFilteredTodos = createSelector(
  (todos, filter) => {
    console.log('Computing filtered todos');
    switch (filter) {
      case 'active':
        return todos.filter(todo => !todo.completed);
      case 'completed':
        return todos.filter(todo => todo.completed);
      default:
        return todos;
    }
  }
);

// 在组件中使用
function TodoList() {
  const { state } = useContext(StateContext);
  
  // 只有todos或filter变化时才重新计算
  const filteredTodos = selectFilteredTodos(
    selectTodos(state),
    selectFilter(state)
  );
  
  return (
    <ul>
      {filteredTodos.map(todo => (
        <TodoItem key={todo.id} todo={todo} />
      ))}
    </ul>
  );
}`
        }
      ]
    },
    {
      title: "批量更新优化",
      description: "合并多个状态更新，减少渲染次数",
      techniques: [
        {
          name: "Action批处理",
          description: "将多个action合并为一个批量action",
          impact: "medium",
          difficulty: "medium",
          code: `// 批量action reducer
function batchReducer(state, action) {
  switch (action.type) {
    case 'BATCH_ACTIONS':
      // 依次应用多个action
      return action.payload.reduce(
        (prevState, action) => baseReducer(prevState, action),
        state
      );
    default:
      return baseReducer(state, action);
  }
}

// 批量dispatch helper
function useBatchDispatch() {
  const dispatch = useContext(DispatchContext);
  const pending = useRef([]);
  const rafId = useRef();
  
  const batchDispatch = useCallback((action) => {
    pending.current.push(action);
    
    if (rafId.current) {
      cancelAnimationFrame(rafId.current);
    }
    
    rafId.current = requestAnimationFrame(() => {
      const actions = pending.current;
      pending.current = [];
      
      if (actions.length === 1) {
        dispatch(actions[0]);
      } else if (actions.length > 1) {
        dispatch({
          type: 'BATCH_ACTIONS',
          payload: actions
        });
      }
    });
  }, [dispatch]);
  
  return batchDispatch;
}

// 使用批量更新
function BulkOperations() {
  const batchDispatch = useBatchDispatch();
  
  const handleBulkUpdate = () => {
    // 这些更新会被批处理成一次渲染
    items.forEach(item => {
      batchDispatch({ type: 'UPDATE_ITEM', payload: item });
    });
  };
  
  return <button onClick={handleBulkUpdate}>Update All</button>;
}`
        }
      ]
    }
  ],
  
  performanceMetrics: {
    renderCount: {
      tool: "React DevTools Profiler",
      description: "监控组件因状态更新导致的渲染次数",
      example: "使用Profiler记录不同优化策略的效果"
    },
    stateUpdateTime: {
      tool: "Performance API",
      description: "测量reducer执行时间",
      example: "在开发环境包装reducer，记录执行耗时"
    },
    memoryUsage: {
      tool: "Chrome DevTools Memory Profiler",
      description: "监控状态对象的内存占用",
      example: "检查是否有内存泄漏或过度的对象创建"
    }
  },
  
  bestPractices: [
    "始终检查是否需要创建新状态对象",
    "使用dispatch的稳定性优化子组件",
    "将状态正规化，避免深层嵌套",
    "合理拆分reducer，避免单个reducer过于复杂",
    "使用TypeScript获得更好的类型推断和错误检查",
    "在开发环境添加性能监控"
  ],
  
  commonPitfalls: [
    {
      issue: "每次都创建新的状态对象",
      cause: "没有检查新旧值是否相同",
      solution: "添加相等性检查，必要时才创建新对象"
    },
    {
      issue: "Context导致过度渲染",
      cause: "将state和dispatch放在同一个Context中",
      solution: "分离StateContext和DispatchContext"
    },
    {
      issue: "复杂计算重复执行",
      cause: "在render中直接进行派生状态计算",
      solution: "使用useMemo或选择器模式"
    }
  ]
};

export default performanceOptimization; 