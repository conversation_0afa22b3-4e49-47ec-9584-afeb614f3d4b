import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: "解释useReducer和useState的区别，以及什么时候使用useReducer更合适？",
    answer: {
      brief: "useReducer适用于复杂状态逻辑，提供更可预测的状态管理，而useState适用于简单状态。",
      detailed: `useReducer和useState的主要区别：

**useReducer优势：**
- 适合复杂状态逻辑，多个状态相互依赖
- 状态更新逻辑集中在reducer中，更容易测试
- 避免多个useState可能导致的状态不同步
- 类似Redux的模式，便于状态管理
- 支持复杂的状态更新逻辑

**何时使用useReducer：**
- 状态逻辑复杂，包含多个子值
- 下个状态依赖于前一个状态
- 想要集中管理状态更新逻辑
- 需要向下传递dispatch而不是多个setter`,
      code: `// useState - 简单状态
const [count, setCount] = useState(0);

// useReducer - 复杂状态
const [state, dispatch] = useReducer(reducer, initialState);

// 适合useReducer的场景：复杂表单
const formReducer = (state, action) => {
  switch (action.type) {
    case 'SET_FIELD':
      return { ...state, [action.field]: action.value };
    case 'VALIDATE':
      return { ...state, errors: validateForm(state) };
    case 'SUBMIT':
      return { ...state, isSubmitting: true };
    default:
      return state;
  }
};`
    },
    difficulty: 'easy',
    frequency: 'high',
    category: 'comparison'
  },
  {
    id: 2,
    question: "什么时候应该使用useReducer而不是useState？",
    answer: {
      brief: "选择useReducer还是useState主要取决于状态的复杂度和更新逻辑",
      detailed: `选择useReducer的关键指标：

**适合useReducer的场景：**
- 状态结构复杂，包含多个子值时使用useReducer
- 状态更新逻辑复杂，依赖之前的状态时使用useReducer
- 需要在多个组件间共享状态更新逻辑时使用useReducer
- 简单的独立状态使用useState更直观

**实际决策标准：**
- 如果状态只是简单的布尔值或字符串，用useState
- 如果状态更新涉及复杂逻辑，用useReducer
- 如果多个状态需要同时更新，用useReducer`,
      code: `// 适合useState的场景
function SimpleToggle() {
  const [isOpen, setIsOpen] = useState(false);
  return <button onClick={() => setIsOpen(!isOpen)}>Toggle</button>;
}

// 适合useReducer的场景
function ComplexForm() {
  const [state, dispatch] = useReducer(formReducer, {
    values: {},
    errors: {},
    touched: {},
    isSubmitting: false
  });
  
  // 多个相关状态需要同时更新
  const handleSubmit = () => {
    dispatch({ type: 'SUBMIT_START' });
    // ...
  };
}`
    },
    difficulty: 'medium',
    frequency: 'high',
    category: 'comparison'
  },
  {
    id: 3,
    question: "为什么说dispatch函数的引用是稳定的？这有什么好处？",
    answer: {
      brief: "dispatch函数在组件的整个生命周期内保持相同的引用，不会因为重渲染而改变",
      detailed: `dispatch引用稳定性的意义：

**React保证dispatch函数的引用稳定性：**
- React保证dispatch函数的引用稳定性
- 可以安全地省略useEffect和useCallback的依赖
- 有助于性能优化，减少子组件不必要的重渲染
- 适合在Context中传递，不会触发Consumer重渲染

**性能优化价值：**
- 避免因dispatch引用变化导致的不必要重渲染
- 在Context Provider中传递dispatch不会造成性能问题
- 可以作为useCallback的依赖而不用担心引用变化`,
      code: `// dispatch稳定性的应用
function Parent() {
  const [state, dispatch] = useReducer(reducer, initialState);
  
  // ✅ dispatch不需要作为依赖
  const handleUpdate = useCallback((data) => {
    dispatch({ type: 'UPDATE', payload: data });
  }, []); // 空依赖数组是安全的
  
  // ✅ 传递给子组件不会导致重渲染
  return <MemoizedChild onUpdate={dispatch} />;
}

// Context优化模式
const DispatchContext = createContext();

function Provider({ children }) {
  const [state, dispatch] = useReducer(reducer, initialState);
  
  // dispatch永不改变，Provider不会因此重渲染
  return (
    <DispatchContext.Provider value={dispatch}>
      {children}
    </DispatchContext.Provider>
  );
}`
    },
    difficulty: 'hard',
    frequency: 'medium',
    category: 'performance'
  }
];

export default interviewQuestions; 