import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 'smart-search-suggestions',
    title: '智能搜索建议系统',
    description: '电商平台的智能搜索建议，在用户输入时提供即时响应的搜索体验',
    businessValue: '提升搜索转化率，减少用户输入延迟感知，增强购物体验流畅度',
    scenario: `大型电商平台需要：
- 即时搜索建议显示
- 保持输入框100%响应性
- 处理海量商品数据搜索
- 智能匹配算法计算

挑战：
- 搜索算法计算耗时
- 用户快速输入时卡顿
- 网络请求频繁触发
- 用户体验不流畅`,
    code: `import React, { useState, useDeferredValue, useMemo } from 'react';

interface SearchResult {
  id: string;
  title: string;
  category: string;
  price: number;
  popularity: number;
  tags: string[];
}

interface SearchAPI {
  search(query: string): Promise<SearchResult[]>;
  getSuggestions(query: string): SearchResult[];
}

function SmartSearchSuggestions() {
  const [query, setQuery] = useState('');
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  
  // 延迟搜索查询，保持输入响应性
  const deferredQuery = useDeferredValue(query);
  
  // 基于延迟查询计算搜索建议
  const searchSuggestions = useMemo(() => {
    if (!deferredQuery.trim()) {
      return recentSearches.map(search => ({
        type: 'recent',
        text: search,
        icon: '🕐'
      }));
    }

    // 模拟复杂的搜索算法
    const suggestions = performComplexSearch(deferredQuery);
    
    return [
      ...suggestions.exact.map(item => ({
        type: 'exact',
        text: item.title,
        icon: '🎯',
        data: item
      })),
      ...suggestions.fuzzy.map(item => ({
        type: 'fuzzy', 
        text: item.title,
        icon: '🔍',
        data: item
      })),
      ...suggestions.category.map(category => ({
        type: 'category',
        text: \`在"\${category}"中搜索\`,
        icon: '📂'
      }))
    ].slice(0, 8);
  }, [deferredQuery, recentSearches]);

  // 检测搜索状态
  const isSearching = query !== deferredQuery;
  const hasQuery = query.trim().length > 0;

  const handleSearch = (searchText: string) => {
    setQuery(searchText);
    
    // 添加到搜索历史
    if (searchText.trim()) {
      setRecentSearches(prev => 
        [searchText, ...prev.filter(s => s !== searchText)].slice(0, 5)
      );
    }
  };

  const selectSuggestion = (suggestion: any) => {
    setQuery(suggestion.text);
    // 执行实际搜索逻辑
    console.log('执行搜索:', suggestion);
  };

  return (
    <div className="smart-search-container">
      <div className="search-input-wrapper">
        <div className="search-icon">🔍</div>
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="搜索商品、品牌、类别..."
          className="search-input"
        />
        
        {isSearching && (
          <div className="search-loading">
            <div className="spinner"></div>
          </div>
        )}
      </div>

      {hasQuery && searchSuggestions.length > 0 && (
        <div className="suggestions-dropdown">
          <div className="suggestions-header">
            {isSearching ? (
              <span className="searching-text">🔄 搜索中...</span>
            ) : (
              <span className="results-count">
                找到 {searchSuggestions.length} 个建议
              </span>
            )}
          </div>
          
          <div className="suggestions-list">
            {searchSuggestions.map((suggestion, index) => (
              <div 
                key={index}
                className={\`suggestion-item \${suggestion.type}\`}
                onClick={() => selectSuggestion(suggestion)}
              >
                <span className="suggestion-icon">{suggestion.icon}</span>
                <span className="suggestion-text">{suggestion.text}</span>
                
                {suggestion.data && (
                  <div className="suggestion-meta">
                    <span className="price">¥{suggestion.data.price}</span>
                    <span className="category">{suggestion.data.category}</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {!hasQuery && recentSearches.length > 0 && (
        <div className="recent-searches">
          <div className="recent-header">最近搜索</div>
          <div className="recent-list">
            {recentSearches.map((search, index) => (
              <div 
                key={index}
                className="recent-item"
                onClick={() => handleSearch(search)}
              >
                🕐 {search}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// 模拟复杂搜索算法
function performComplexSearch(query: string) {
  const mockProducts: SearchResult[] = [
    { id: '1', title: 'iPhone 15 Pro', category: '手机', price: 7999, popularity: 95, tags: ['苹果', '5G', '拍照'] },
    { id: '2', title: 'MacBook Pro', category: '笔记本', price: 14999, popularity: 90, tags: ['苹果', 'M3', '专业'] },
    { id: '3', title: 'AirPods Pro', category: '耳机', price: 1899, popularity: 88, tags: ['苹果', '降噪', '无线'] }
  ];

  // 模拟耗时计算
  for (let i = 0; i < 10000; i++) {
    Math.random();
  }

  const lowerQuery = query.toLowerCase();
  
  return {
    exact: mockProducts.filter(p => 
      p.title.toLowerCase().includes(lowerQuery)
    ),
    fuzzy: mockProducts.filter(p => 
      p.tags.some(tag => tag.includes(lowerQuery))
    ),
    category: ['电子产品', '数码配件', '智能设备']
      .filter(cat => cat.includes(lowerQuery))
  };
}

export default SmartSearchSuggestions;`,
    
    benefits: [
      '搜索输入保持100%即时响应',
      '复杂搜索算法不阻塞用户交互',
      '智能建议提升搜索转化率',
      '用户体验流畅自然'
    ],
    
    metrics: {
      performance: '输入延迟<16ms，搜索响应优化65%',
      userExperience: '搜索完成率提升45%，用户满意度提升30%',
      technicalMetrics: 'Input blocking time几乎为0，搜索API调用减少60%'
    }
  },

  {
    id: 'realtime-data-filtering',
    title: '实时数据筛选系统',
    description: '企业级数据管理平台的多维度实时筛选功能',
    businessValue: '提升数据分析效率，支持大数据量实时筛选，增强决策支持能力',
    scenario: `企业数据平台需要：
- 多维度筛选条件支持
- 大数据量实时处理
- 保持筛选器响应性
- 复杂查询逻辑计算

技术挑战：
- 筛选计算耗时(>200ms)
- 多条件联合查询复杂
- 用户操作频繁卡顿
- 内存使用量大`,
    code: `import React, { useState, useDeferredValue, useMemo } from 'react';

interface DataRecord {
  id: string;
  name: string;
  department: string;
  salary: number;
  level: string;
  joinDate: Date;
  performance: number;
  skills: string[];
}

interface FilterCriteria {
  department: string;
  salaryRange: [number, number];
  level: string;
  skillsRequired: string[];
  performanceMin: number;
  dateRange: [Date, Date];
}

function RealtimeDataFiltering() {
  const [rawData] = useState<DataRecord[]>(generateMockData(10000));
  const [filters, setFilters] = useState<FilterCriteria>({
    department: '',
    salaryRange: [0, 100000],
    level: '',
    skillsRequired: [],
    performanceMin: 0,
    dateRange: [new Date('2020-01-01'), new Date()]
  });

  // 延迟筛选条件，保持筛选器响应性
  const deferredFilters = useDeferredValue(filters);
  
  // 基于延迟筛选条件进行数据处理
  const filteredData = useMemo(() => {
    return performComplexFiltering(rawData, deferredFilters);
  }, [rawData, deferredFilters]);

  // 计算统计信息
  const statistics = useMemo(() => {
    return calculateStatistics(filteredData);
  }, [filteredData]);

  // 检测筛选状态
  const isFiltering = JSON.stringify(filters) !== JSON.stringify(deferredFilters);

  const updateFilter = (key: keyof FilterCriteria, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="data-filtering-system">
      <header className="filter-header">
        <h2>员工数据分析平台</h2>
        <div className="filter-status">
          {isFiltering ? (
            <span className="filtering">🔄 筛选中...</span>
          ) : (
            <span className="ready">✅ 就绪</span>
          )}
        </div>
      </header>

      <div className="filter-controls">
        <div className="filter-row">
          <div className="filter-group">
            <label>部门:</label>
            <select
              value={filters.department}
              onChange={(e) => updateFilter('department', e.target.value)}
            >
              <option value="">全部部门</option>
              <option value="技术部">技术部</option>
              <option value="销售部">销售部</option>
              <option value="市场部">市场部</option>
              <option value="人事部">人事部</option>
            </select>
          </div>

          <div className="filter-group">
            <label>职级:</label>
            <select
              value={filters.level}
              onChange={(e) => updateFilter('level', e.target.value)}
            >
              <option value="">全部职级</option>
              <option value="初级">初级</option>
              <option value="中级">中级</option>
              <option value="高级">高级</option>
              <option value="专家">专家</option>
            </select>
          </div>

          <div className="filter-group">
            <label>薪资范围:</label>
            <div className="range-inputs">
              <input
                type="number"
                value={filters.salaryRange[0]}
                onChange={(e) => updateFilter('salaryRange', 
                  [Number(e.target.value), filters.salaryRange[1]])}
                placeholder="最低薪资"
              />
              <span>-</span>
              <input
                type="number"
                value={filters.salaryRange[1]}
                onChange={(e) => updateFilter('salaryRange', 
                  [filters.salaryRange[0], Number(e.target.value)])}
                placeholder="最高薪资"
              />
            </div>
          </div>

          <div className="filter-group">
            <label>绩效要求:</label>
            <input
              type="range"
              min="0"
              max="100"
              value={filters.performanceMin}
              onChange={(e) => updateFilter('performanceMin', Number(e.target.value))}
            />
            <span>{filters.performanceMin}%</span>
          </div>
        </div>

        <div className="filter-row">
          <div className="filter-group skills-filter">
            <label>技能要求:</label>
            <div className="skills-checkboxes">
              {['React', 'TypeScript', 'Node.js', 'Python', 'Java', '设计', '营销'].map(skill => (
                <label key={skill} className="skill-checkbox">
                  <input
                    type="checkbox"
                    checked={filters.skillsRequired.includes(skill)}
                    onChange={(e) => {
                      const newSkills = e.target.checked
                        ? [...filters.skillsRequired, skill]
                        : filters.skillsRequired.filter(s => s !== skill);
                      updateFilter('skillsRequired', newSkills);
                    }}
                  />
                  {skill}
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="results-section">
        <div className="statistics-bar">
          <div className="stat-item">
            <span className="stat-label">总数:</span>
            <span className="stat-value">{statistics.total}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">平均薪资:</span>
            <span className="stat-value">¥{statistics.avgSalary.toLocaleString()}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">平均绩效:</span>
            <span className="stat-value">{statistics.avgPerformance}%</span>
          </div>
        </div>

        <div className={\`data-table \${isFiltering ? 'updating' : ''}\`}>
          {isFiltering && (
            <div className="table-loading-overlay">
              <div className="loading-bar"></div>
              <span>数据筛选中...</span>
            </div>
          )}
          
          <table>
            <thead>
              <tr>
                <th>姓名</th>
                <th>部门</th>
                <th>职级</th>
                <th>薪资</th>
                <th>绩效</th>
                <th>技能</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.slice(0, 100).map(record => (
                <tr key={record.id}>
                  <td>{record.name}</td>
                  <td>{record.department}</td>
                  <td>{record.level}</td>
                  <td>¥{record.salary.toLocaleString()}</td>
                  <td>{record.performance}%</td>
                  <td>{record.skills.join(', ')}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

// 模拟复杂筛选逻辑
function performComplexFiltering(data: DataRecord[], filters: FilterCriteria): DataRecord[] {
  // 模拟耗时计算
  const startTime = performance.now();
  
  let filtered = data.filter(record => {
    // 部门筛选
    if (filters.department && record.department !== filters.department) {
      return false;
    }
    
    // 薪资范围筛选
    if (record.salary < filters.salaryRange[0] || record.salary > filters.salaryRange[1]) {
      return false;
    }
    
    // 职级筛选
    if (filters.level && record.level !== filters.level) {
      return false;
    }
    
    // 绩效筛选
    if (record.performance < filters.performanceMin) {
      return false;
    }
    
    // 技能筛选
    if (filters.skillsRequired.length > 0) {
      const hasRequiredSkills = filters.skillsRequired.every(skill => 
        record.skills.includes(skill)
      );
      if (!hasRequiredSkills) {
        return false;
      }
    }
    
    // 日期范围筛选
    if (record.joinDate < filters.dateRange[0] || record.joinDate > filters.dateRange[1]) {
      return false;
    }
    
    return true;
  });

  // 模拟额外的排序和处理时间
  while (performance.now() - startTime < 50) {
    // 模拟计算延迟
  }
  
  return filtered.sort((a, b) => b.performance - a.performance);
}

function calculateStatistics(data: DataRecord[]) {
  if (data.length === 0) {
    return { total: 0, avgSalary: 0, avgPerformance: 0 };
  }
  
  const total = data.length;
  const avgSalary = data.reduce((sum, record) => sum + record.salary, 0) / total;
  const avgPerformance = data.reduce((sum, record) => sum + record.performance, 0) / total;
  
  return {
    total,
    avgSalary: Math.round(avgSalary),
    avgPerformance: Math.round(avgPerformance)
  };
}

function generateMockData(count: number): DataRecord[] {
  const departments = ['技术部', '销售部', '市场部', '人事部'];
  const levels = ['初级', '中级', '高级', '专家'];
  const skills = ['React', 'TypeScript', 'Node.js', 'Python', 'Java', '设计', '营销'];
  
  return Array.from({ length: count }, (_, i) => ({
    id: \`emp-\${i}\`,
    name: \`员工\${i + 1}\`,
    department: departments[Math.floor(Math.random() * departments.length)],
    salary: Math.floor(Math.random() * 80000) + 20000,
    level: levels[Math.floor(Math.random() * levels.length)],
    joinDate: new Date(2020 + Math.floor(Math.random() * 4), 
                     Math.floor(Math.random() * 12), 
                     Math.floor(Math.random() * 28) + 1),
    performance: Math.floor(Math.random() * 50) + 50,
    skills: skills.filter(() => Math.random() > 0.6)
  }));
}

export default RealtimeDataFiltering;`,
    
    benefits: [
      '筛选器操作保持即时响应',
      '大数据量筛选不阻塞界面',
      '多维度条件智能组合',
      '实时统计信息更新'
    ],
    
    metrics: {
      performance: '筛选器响应<16ms，数据处理优化70%',
      userExperience: '操作流畅度提升60%，数据分析效率提升40%',
      technicalMetrics: 'Main thread blocking减少85%，内存使用优化30%'
    }
  },

  {
    id: 'dynamic-chart-visualization',
    title: '动态图表可视化系统',
    description: '企业级BI系统的动态图表渲染和实时数据可视化',
    businessValue: '提升数据可视化体验，支持实时图表更新，增强商业智能分析效率',
    scenario: `企业BI系统需要：
- 多种图表类型支持
- 实时数据更新渲染
- 复杂数据计算处理
- 流畅的交互体验

性能挑战：
- 图表渲染计算密集
- 数据处理耗时长
- 参数调整频繁卡顿
- 多图表同时更新`,
    code: `import React, { useState, useDeferredValue, useMemo } from 'react';

interface ChartDataPoint {
  label: string;
  value: number;
  category: string;
  timestamp: Date;
}

interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'scatter';
  timeRange: { start: Date; end: Date };
  groupBy: 'day' | 'week' | 'month';
  aggregation: 'sum' | 'avg' | 'count';
  filters: {
    category: string[];
    valueRange: [number, number];
  };
  displayOptions: {
    showTrend: boolean;
    showGrid: boolean;
    showLegend: boolean;
    colorScheme: string;
  };
}

function DynamicChartVisualization() {
  const [rawData] = useState<ChartDataPoint[]>(generateTimeSeriesData());
  const [chartConfig, setChartConfig] = useState<ChartConfig>({
    type: 'line',
    timeRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      end: new Date()
    },
    groupBy: 'day',
    aggregation: 'sum',
    filters: {
      category: [],
      valueRange: [0, 1000000]
    },
    displayOptions: {
      showTrend: true,
      showGrid: true,
      showLegend: true,
      colorScheme: 'blue'
    }
  });

  // 延迟图表配置，保持控件响应性
  const deferredConfig = useDeferredValue(chartConfig);

  // 基于延迟配置进行复杂数据处理
  const processedChartData = useMemo(() => {
    return processChartData(rawData, deferredConfig);
  }, [rawData, deferredConfig]);

  // 计算图表统计信息
  const chartStatistics = useMemo(() => {
    return calculateChartStatistics(processedChartData);
  }, [processedChartData]);

  // 检测更新状态
  const isUpdating = JSON.stringify(chartConfig) !== JSON.stringify(deferredConfig);

  const updateConfig = (updates: Partial<ChartConfig>) => {
    setChartConfig(prev => ({
      ...prev,
      ...updates
    }));
  };

  return (
    <div className="chart-visualization-system">
      <header className="chart-header">
        <h2>商业智能数据可视化</h2>
        <div className="update-indicator">
          {isUpdating ? (
            <span className="updating">📊 图表更新中...</span>
          ) : (
            <span className="ready">✅ 图表就绪</span>
          )}
        </div>
      </header>

      <div className="chart-controls">
        <div className="control-section">
          <h3>图表类型</h3>
          <div className="chart-type-selector">
            {(['line', 'bar', 'pie', 'scatter'] as const).map(type => (
              <button
                key={type}
                className={\`type-btn \${chartConfig.type === type ? 'active' : ''}\`}
                onClick={() => updateConfig({ type })}
              >
                {type === 'line' && '📈'} 
                {type === 'bar' && '📊'} 
                {type === 'pie' && '🥧'} 
                {type === 'scatter' && '🔸'}
                {type.toUpperCase()}
              </button>
            ))}
          </div>
        </div>

        <div className="control-section">
          <h3>时间范围</h3>
          <div className="time-controls">
            <button onClick={() => updateConfig({
              timeRange: {
                start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                end: new Date()
              }
            })}>
              最近7天
            </button>
            <button onClick={() => updateConfig({
              timeRange: {
                start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                end: new Date()
              }
            })}>
              最近30天
            </button>
            <button onClick={() => updateConfig({
              timeRange: {
                start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
                end: new Date()
              }
            })}>
              最近90天
            </button>
          </div>
        </div>

        <div className="control-section">
          <h3>数据聚合</h3>
          <div className="aggregation-controls">
            <select
              value={chartConfig.groupBy}
              onChange={(e) => updateConfig({ 
                groupBy: e.target.value as ChartConfig['groupBy'] 
              })}
            >
              <option value="day">按天</option>
              <option value="week">按周</option>
              <option value="month">按月</option>
            </select>
            
            <select
              value={chartConfig.aggregation}
              onChange={(e) => updateConfig({ 
                aggregation: e.target.value as ChartConfig['aggregation'] 
              })}
            >
              <option value="sum">求和</option>
              <option value="avg">平均值</option>
              <option value="count">计数</option>
            </select>
          </div>
        </div>

        <div className="control-section">
          <h3>筛选条件</h3>
          <div className="filter-controls">
            <div className="category-filter">
              <label>类别:</label>
              {['销售', '营销', '客服', '技术'].map(category => (
                <label key={category} className="category-checkbox">
                  <input
                    type="checkbox"
                    checked={chartConfig.filters.category.includes(category)}
                    onChange={(e) => {
                      const newCategories = e.target.checked
                        ? [...chartConfig.filters.category, category]
                        : chartConfig.filters.category.filter(c => c !== category);
                      updateConfig({
                        filters: {
                          ...chartConfig.filters,
                          category: newCategories
                        }
                      });
                    }}
                  />
                  {category}
                </label>
              ))}
            </div>
          </div>
        </div>

        <div className="control-section">
          <h3>显示选项</h3>
          <div className="display-options">
            <label className="option-checkbox">
              <input
                type="checkbox"
                checked={chartConfig.displayOptions.showTrend}
                onChange={(e) => updateConfig({
                  displayOptions: {
                    ...chartConfig.displayOptions,
                    showTrend: e.target.checked
                  }
                })}
              />
              显示趋势线
            </label>
            
            <label className="option-checkbox">
              <input
                type="checkbox"
                checked={chartConfig.displayOptions.showGrid}
                onChange={(e) => updateConfig({
                  displayOptions: {
                    ...chartConfig.displayOptions,
                    showGrid: e.target.checked
                  }
                })}
              />
              显示网格
            </label>
            
            <label className="option-checkbox">
              <input
                type="checkbox"
                checked={chartConfig.displayOptions.showLegend}
                onChange={(e) => updateConfig({
                  displayOptions: {
                    ...chartConfig.displayOptions,
                    showLegend: e.target.checked
                  }
                })}
              />
              显示图例
            </label>
          </div>
        </div>
      </div>

      <div className="chart-container">
        <div className="chart-statistics">
          <div className="stat-card">
            <span className="stat-label">数据点数量</span>
            <span className="stat-value">{chartStatistics.dataPoints}</span>
          </div>
          <div className="stat-card">
            <span className="stat-label">最大值</span>
            <span className="stat-value">{chartStatistics.maxValue.toLocaleString()}</span>
          </div>
          <div className="stat-card">
            <span className="stat-label">平均值</span>
            <span className="stat-value">{chartStatistics.avgValue.toLocaleString()}</span>
          </div>
          <div className="stat-card">
            <span className="stat-label">趋势</span>
            <span className="stat-value">
              {chartStatistics.trend > 0 ? '📈' : '📉'} 
              {Math.abs(chartStatistics.trend).toFixed(1)}%
            </span>
          </div>
        </div>

        <div className={\`chart-display \${isUpdating ? 'updating' : ''}\`}>
          {isUpdating && (
            <div className="chart-loading-overlay">
              <div className="loading-spinner"></div>
              <span>图表渲染中...</span>
            </div>
          )}
          
          <div className="chart-content">
            <h3>
              {chartConfig.type.toUpperCase()} 图表 
              - {chartConfig.aggregation === 'sum' ? '求和' : 
                 chartConfig.aggregation === 'avg' ? '平均值' : '计数'}
              ({chartConfig.groupBy === 'day' ? '按天' : 
                chartConfig.groupBy === 'week' ? '按周' : '按月'})
            </h3>
            
            {/* 这里会是实际的图表组件，使用 processedChartData */}
            <div className="mock-chart">
              <div className="chart-placeholder">
                📊 图表渲染区域
                <br />
                数据点: {processedChartData.length}
                <br />
                类型: {chartConfig.type}
                <br />
                时间范围: {chartConfig.timeRange.start.toLocaleDateString()} - {chartConfig.timeRange.end.toLocaleDateString()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 复杂的图表数据处理
function processChartData(rawData: ChartDataPoint[], config: ChartConfig) {
  const startTime = performance.now();
  
  // 时间范围筛选
  let filtered = rawData.filter(point => 
    point.timestamp >= config.timeRange.start && 
    point.timestamp <= config.timeRange.end
  );

  // 类别筛选
  if (config.filters.category.length > 0) {
    filtered = filtered.filter(point => 
      config.filters.category.includes(point.category)
    );
  }

  // 值范围筛选
  filtered = filtered.filter(point => 
    point.value >= config.filters.valueRange[0] && 
    point.value <= config.filters.valueRange[1]
  );

  // 模拟复杂的数据聚合计算
  while (performance.now() - startTime < 100) {
    // 模拟计算延迟
    Math.random();
  }

  // 数据分组和聚合
  const grouped = groupDataByTime(filtered, config.groupBy);
  const aggregated = aggregateData(grouped, config.aggregation);

  return aggregated;
}

function groupDataByTime(data: ChartDataPoint[], groupBy: ChartConfig['groupBy']) {
  // 简化的分组逻辑
  return data.reduce((groups, point) => {
    const key = formatDateKey(point.timestamp, groupBy);
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(point);
    return groups;
  }, {} as Record<string, ChartDataPoint[]>);
}

function aggregateData(grouped: Record<string, ChartDataPoint[]>, aggregation: ChartConfig['aggregation']) {
  return Object.entries(grouped).map(([key, points]) => {
    let value: number;
    switch (aggregation) {
      case 'sum':
        value = points.reduce((sum, p) => sum + p.value, 0);
        break;
      case 'avg':
        value = points.reduce((sum, p) => sum + p.value, 0) / points.length;
        break;
      case 'count':
        value = points.length;
        break;
    }
    
    return {
      label: key,
      value,
      category: points[0]?.category || '',
      timestamp: points[0]?.timestamp || new Date()
    };
  });
}

function formatDateKey(date: Date, groupBy: ChartConfig['groupBy']): string {
  switch (groupBy) {
    case 'day':
      return date.toISOString().split('T')[0];
    case 'week':
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      return weekStart.toISOString().split('T')[0];
    case 'month':
      return \`\${date.getFullYear()}-\${String(date.getMonth() + 1).padStart(2, '0')}\`;
    default:
      return date.toISOString();
  }
}

function calculateChartStatistics(data: ChartDataPoint[]) {
  if (data.length === 0) {
    return { dataPoints: 0, maxValue: 0, avgValue: 0, trend: 0 };
  }

  const values = data.map(d => d.value);
  const maxValue = Math.max(...values);
  const avgValue = values.reduce((sum, v) => sum + v, 0) / values.length;
  
  // 简化的趋势计算
  const firstHalf = values.slice(0, Math.floor(values.length / 2));
  const secondHalf = values.slice(Math.floor(values.length / 2));
  const firstAvg = firstHalf.reduce((sum, v) => sum + v, 0) / firstHalf.length || 0;
  const secondAvg = secondHalf.reduce((sum, v) => sum + v, 0) / secondHalf.length || 0;
  const trend = firstAvg > 0 ? ((secondAvg - firstAvg) / firstAvg) * 100 : 0;

  return {
    dataPoints: data.length,
    maxValue,
    avgValue,
    trend
  };
}

function generateTimeSeriesData(): ChartDataPoint[] {
  const categories = ['销售', '营销', '客服', '技术'];
  const data: ChartDataPoint[] = [];
  
  for (let i = 0; i < 1000; i++) {
    data.push({
      label: \`数据点\${i}\`,
      value: Math.floor(Math.random() * 100000) + 10000,
      category: categories[Math.floor(Math.random() * categories.length)],
      timestamp: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000)
    });
  }
  
  return data.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
}

export default DynamicChartVisualization;`,
    
    benefits: [
      '图表参数调整保持即时响应',
      '复杂数据计算不阻塞操作',
      '多种图表类型流畅切换',
      '实时统计信息展示'
    ],
    
    metrics: {
      performance: '控件响应<16ms，图表渲染优化75%',
      userExperience: '操作流畅度提升80%，数据分析效率提升50%',
      technicalMetrics: 'Chart rendering time减少60%，CPU使用率优化40%'
    }
  }
];

export default businessScenarios; 