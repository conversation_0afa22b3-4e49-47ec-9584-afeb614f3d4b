import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: 'useDeferredValue和useTransition有什么区别？什么时候使用哪个？',
    difficulty: 'medium',
    category: 'concept',
    answer: {
      brief: 'useDeferredValue用于延迟值的更新，useTransition用于标记状态更新为非紧急。前者是被动延迟，后者是主动控制。',
      detailed: `**核心区别**：

**useDeferredValue（被动延迟）**：
- 对已有的值进行延迟处理
- 不需要修改状态更新逻辑
- 适用于处理来自props或外部的值
- 延迟时机由React自动决定

**useTransition（主动控制）**：
- 主动将状态更新标记为非紧急
- 需要用startTransition包装状态更新
- 提供isPending状态指示
- 开发者主动决定哪些更新需要延迟

**使用场景对比**：

| 场景 | useDeferredValue | useTransition |
|------|------------------|---------------|
| **搜索建议** | ✅ 延迟搜索结果 | ❌ 不需要控制更新 |
| **大列表筛选** | ✅ 延迟筛选结果 | ✅ 延迟筛选操作 |
| **路由切换** | ❌ 不处理值延迟 | ✅ 延迟页面更新 |
| **表单输入** | ✅ 延迟验证结果 | ❌ 验证是外部逻辑 |`,
      code: `// useDeferredValue示例：延迟值处理
function SearchSuggestions({ searchTerm }) {
  // 延迟搜索词，保持输入响应性
  const deferredSearchTerm = useDeferredValue(searchTerm);
  
  const suggestions = useMemo(() => {
    return performSearch(deferredSearchTerm);
  }, [deferredSearchTerm]);
  
  return <SuggestionList items={suggestions} />;
}

// useTransition示例：控制状态更新
function FilterableList() {
  const [filter, setFilter] = useState('');
  const [items, setItems] = useState(allItems);
  const [isPending, startTransition] = useTransition();
  
  const handleFilterChange = (newFilter) => {
    setFilter(newFilter); // 立即更新输入框
    
    // 延迟过滤操作
    startTransition(() => {
      const filtered = allItems.filter(item => 
        item.name.includes(newFilter)
      );
      setItems(filtered);
    });
  };
  
  return (
    <div>
      <input onChange={(e) => handleFilterChange(e.target.value)} />
      {isPending && <div>筛选中...</div>}
      <ItemList items={items} />
    </div>
  );
}`
    }
  },

  {
    id: 2,
    question: 'useDeferredValue如何实现性能优化？与传统的防抖节流相比有什么优势？',
    difficulty: 'medium',
    category: 'performance',
    answer: {
      brief: 'useDeferredValue基于React优先级调度实现智能延迟，比传统防抖节流更精准，能根据系统负载动态调整延迟时间。',
      detailed: `**useDeferredValue性能优化机制**：

## 1. 智能调度
- 基于React并发渲染的优先级系统
- 自动识别高优先级任务（用户输入）
- 动态调整延迟时间，不是固定延迟

## 2. 非阻塞更新
- 延迟值更新不会阻塞用户交互
- 利用浏览器空闲时间进行更新
- 可被更高优先级任务中断

## 3. 与传统方案对比

**传统防抖（debounce）**：
- ❌ 固定延迟时间，无法适应不同场景
- ❌ 可能在系统繁忙时仍然执行
- ❌ 无法与React渲染周期协调
- ✅ 简单易懂，兼容性好

**传统节流（throttle）**：
- ❌ 固定执行频率，浪费资源
- ❌ 可能在不合适的时机执行
- ❌ 无法感知系统负载

**useDeferredValue优势**：
- ✅ 智能调度，根据优先级自动延迟
- ✅ 与React渲染周期完美配合
- ✅ 系统负载感知，性能最优
- ✅ 支持中断和恢复`,
      code: `// 传统防抖实现
function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value); // 固定延迟，无法适应系统负载
    }, delay);
    
    return () => clearTimeout(timer);
  }, [value, delay]);
  
  return debouncedValue;
}

// useDeferredValue智能延迟
function SmartDelayExample() {
  const [query, setQuery] = useState('');
  
  // 智能延迟：根据系统负载自动调整
  const deferredQuery = useDeferredValue(query);
  
  // 性能对比
  const debouncedQuery = useDebounce(query, 300); // 固定300ms
  
  return (
    <div>
      <input onChange={(e) => setQuery(e.target.value)} />
      
      {/* 传统防抖：固定延迟 */}
      <SearchResults query={debouncedQuery} />
      
      {/* 智能延迟：动态调整 */}
      <SmartSearchResults query={deferredQuery} />
    </div>
  );
}

// 性能监控对比
function PerformanceComparison() {
  const startTime = performance.now();
  
  // 传统方案：无法感知系统状态
  const debouncedValue = useDebounce(value, 300);
  
  // 智能方案：系统负载感知
  const deferredValue = useDeferredValue(value);
  
  useEffect(() => {
    const endTime = performance.now();
    console.log('更新耗时:', endTime - startTime);
    
    // useDeferredValue会在系统空闲时更新，耗时更少
  }, [deferredValue]);
}`
    }
  },

  {
    id: 3,
    question: '在什么情况下不应该使用useDeferredValue？请举例说明。',
    difficulty: 'medium',
    category: 'best-practices',
    answer: {
      brief: '不应该在关键UI更新、简单状态、实时性要求高的场景中使用useDeferredValue，会导致用户体验问题。',
      detailed: `**不适用场景及原因**：

## 1. 关键UI控制
- **按钮状态**：loading、disabled等状态必须立即响应
- **表单验证**：错误提示需要即时显示
- **导航状态**：菜单展开/收起等交互状态

## 2. 实时性要求高的场景
- **聊天消息**：新消息必须立即显示
- **系统警告**：安全提示、错误信息
- **计时器显示**：倒计时、时钟等

## 3. 简单快速的更新
- **计数器**：简单的数字递增
- **开关状态**：toggle开关
- **颜色主题**：深色/浅色模式切换

## 4. 依赖关系复杂的状态
- **多个状态相互依赖**：延迟会导致状态不一致
- **副作用链**：一个更新触发多个副作用

## 5. 开发调试阶段
- **快速迭代**：延迟更新影响调试效率
- **状态追踪**：增加状态追踪复杂度`,
      code: `// ❌ 错误用法：关键UI控制
function BadButtonExample() {
  const [isLoading, setIsLoading] = useState(false);
  
  // 错误：按钮状态不应延迟
  const deferredLoading = useDeferredValue(isLoading);
  
  return (
    <button disabled={deferredLoading}>
      {deferredLoading ? '加载中...' : '提交'}
    </button>
  );
}

// ✅ 正确用法：非关键内容延迟
function GoodButtonExample() {
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  
  // 正确：搜索结果可以延迟
  const deferredResults = useDeferredValue(searchResults);
  
  return (
    <div>
      <button disabled={isLoading}> {/* 立即响应 */}
        {isLoading ? '加载中...' : '搜索'}
      </button>
      
      <SearchResults data={deferredResults} /> {/* 延迟更新 */}
    </div>
  );
}

// ❌ 错误用法：实时消息
function BadChatExample() {
  const [messages, setMessages] = useState([]);
  
  // 错误：消息必须立即显示
  const deferredMessages = useDeferredValue(messages);
  
  return <MessageList messages={deferredMessages} />;
}

// ✅ 正确用法：消息立即显示，统计延迟
function GoodChatExample() {
  const [messages, setMessages] = useState([]);
  const [statistics, setStatistics] = useState({});
  
  // 正确：消息立即显示
  // 统计信息可以延迟
  const deferredStats = useDeferredValue(statistics);
  
  return (
    <div>
      <MessageList messages={messages} /> {/* 立即显示 */}
      <ChatStatistics stats={deferredStats} /> {/* 延迟更新 */}
    </div>
  );
}

// ❌ 错误用法：简单状态
function BadCounterExample() {
  const [count, setCount] = useState(0);
  
  // 错误：简单计数不需要延迟
  const deferredCount = useDeferredValue(count);
  
  return (
    <div>
      <span>{deferredCount}</span>
      <button onClick={() => setCount(c => c + 1)}>+</button>
    </div>
  );
}

// ✅ 正确用法：复杂计算延迟
function GoodCounterExample() {
  const [count, setCount] = useState(0);
  const [complexData, setComplexData] = useState([]);
  
  // 正确：复杂数据处理可以延迟
  const deferredData = useDeferredValue(complexData);
  
  const processedData = useMemo(() => {
    return expensiveCalculation(deferredData);
  }, [deferredData]);
  
  return (
    <div>
      <span>{count}</span> {/* 立即更新 */}
      <button onClick={() => setCount(c => c + 1)}>+</button>
      
      <DataVisualization data={processedData} /> {/* 延迟处理 */}
    </div>
  );
}`
    }
  },

  {
    id: 4,
    question: '如何检测useDeferredValue的延迟状态？如何为用户提供合适的反馈？',
    difficulty: 'easy',
    category: 'implementation',
    answer: {
      brief: '通过比较原值和延迟值（value !== deferredValue）检测延迟状态，为用户提供loading指示器、skeleton屏幕等视觉反馈。',
      detailed: `**延迟状态检测方法**：

## 1. 基础检测
通过比较原值和延迟值来检测是否处于延迟状态：
\`\`\`javascript
const isStale = value !== deferredValue;
\`\`\`

## 2. 类型安全检测
对于对象类型，确保使用正确的比较方式：
\`\`\`javascript
const isStale = !Object.is(value, deferredValue);
\`\`\`

## 3. 用户反馈策略

**视觉反馈类型**：
- **Loading指示器**：显示正在处理状态
- **Skeleton屏幕**：保持界面结构的占位符
- **Progress条**：显示处理进度（如果可估算）
- **半透明遮罩**：保持内容可见但标识为过期

**反馈设计原则**：
- 保持内容可访问性
- 避免突兀的闪烁
- 提供明确的状态指示
- 考虑不同设备的性能差异`,
      code: `// 基础延迟状态检测
function BasicStaleDetection() {
  const [query, setQuery] = useState('');
  const deferredQuery = useDeferredValue(query);
  
  // 检测延迟状态
  const isSearching = query !== deferredQuery;
  
  return (
    <div>
      <input 
        value={query}
        onChange={(e) => setQuery(e.target.value)}
      />
      
      {isSearching && (
        <div className="loading-indicator">
          🔄 搜索中...
        </div>
      )}
      
      <SearchResults query={deferredQuery} />
    </div>
  );
}

// 高级状态管理和反馈
function AdvancedStateDetection() {
  const [data, setData] = useState(initialData);
  const deferredData = useDeferredValue(data);
  
  // 多层状态检测
  const isStale = !Object.is(data, deferredData);
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now());
  
  useEffect(() => {
    if (!isStale) {
      setLastUpdateTime(Date.now());
    }
  }, [isStale]);
  
  // 计算延迟时长
  const staleDuration = isStale ? Date.now() - lastUpdateTime : 0;
  
  return (
    <div className="data-container">
      <div className="status-bar">
        {isStale ? (
          <div className="stale-indicator">
            📊 数据更新中... ({staleDuration}ms)
          </div>
        ) : (
          <div className="fresh-indicator">
            ✅ 数据已更新
          </div>
        )}
      </div>
      
      <div className={\`content \${isStale ? 'stale' : 'fresh'}\`}>
        <DataVisualization data={deferredData} />
      </div>
    </div>
  );
}

// Skeleton屏幕实现
function SkeletonFeedback() {
  const [content, setContent] = useState('');
  const deferredContent = useDeferredValue(content);
  const isLoading = content !== deferredContent;
  
  return (
    <div className="content-area">
      {isLoading ? (
        <div className="skeleton-container">
          <div className="skeleton-header"></div>
          <div className="skeleton-text"></div>
          <div className="skeleton-text"></div>
          <div className="loading-overlay">
            <span>内容更新中...</span>
          </div>
        </div>
      ) : (
        <div className="actual-content">
          <ProcessedContent data={deferredContent} />
        </div>
      )}
    </div>
  );
}

// 渐进式加载反馈
function ProgressiveFeedback() {
  const [rawData, setRawData] = useState([]);
  const deferredData = useDeferredValue(rawData);
  
  const isProcessing = rawData !== deferredData;
  const processingProgress = isProcessing ? 
    Math.min((deferredData.length / rawData.length) * 100, 95) : 100;
  
  return (
    <div>
      {isProcessing && (
        <div className="progress-container">
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: \`\${processingProgress}%\` }}
            />
          </div>
          <span>处理进度: {processingProgress.toFixed(0)}%</span>
        </div>
      )}
      
      <DataTable 
        data={deferredData}
        className={isProcessing ? 'processing' : 'ready'}
      />
    </div>
  );
}

// 自定义Hook：延迟状态管理
function useDeferredState(initialValue) {
  const [value, setValue] = useState(initialValue);
  const deferredValue = useDeferredValue(value);
  
  const isStale = !Object.is(value, deferredValue);
  const [staleStartTime, setStaleStartTime] = useState(null);
  
  useEffect(() => {
    if (isStale && !staleStartTime) {
      setStaleStartTime(Date.now());
    } else if (!isStale && staleStartTime) {
      setStaleStartTime(null);
    }
  }, [isStale, staleStartTime]);
  
  const staleDuration = isStale && staleStartTime ? 
    Date.now() - staleStartTime : 0;
  
  return {
    value,
    deferredValue,
    setValue,
    isStale,
    staleDuration
  };
}`
    }
  },

  {
    id: 5,
    question: 'useDeferredValue在SSR（服务端渲染）环境中有什么注意事项？如何处理hydration不一致？',
    difficulty: 'hard',
    category: 'advanced',
    answer: {
      brief: 'SSR时延迟值始终等于初始值，需要在客户端hydration后才进行延迟处理，避免hydration不一致问题。',
      detailed: `**SSR环境中的挑战**：

## 1. 服务端限制
- 服务端没有用户交互，无法产生优先级差异
- 延迟值在SSR时始终等于传入值
- 无法使用React并发特性

## 2. Hydration一致性问题
- 服务端和客户端初始状态可能不同
- 延迟状态检测在hydration后才有效
- 可能导致首次渲染闪烁

## 3. 解决方案

**策略1：延迟激活**
- 在客户端hydration完成后才启用延迟逻辑
- 使用\`useEffect\`检测客户端环境

**策略2：同构渲染**
- 确保服务端和客户端使用相同的初始值
- 避免在首次渲染时显示延迟状态

**策略3：渐进增强**
- 先渲染基础内容，再逐步添加延迟优化
- 使用\`useLayoutEffect\`进行无闪烁升级`,
      code: `// 检测客户端环境
function useIsClient() {
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  return isClient;
}

// SSR安全的useDeferredValue
function useSSRSafeDeferredValue(value) {
  const isClient = useIsClient();
  const deferredValue = useDeferredValue(value);
  
  // 服务端和首次渲染时返回原值
  return isClient ? deferredValue : value;
}

// 使用示例
function SSRSafeComponent({ initialData }) {
  const [data, setData] = useState(initialData);
  const safeDeferredData = useSSRSafeDeferredValue(data);
  const isClient = useIsClient();
  
  // 延迟状态检测只在客户端有效
  const isStale = isClient && data !== safeDeferredData;
  
  return (
    <div>
      <DataControls onChange={setData} />
      
      {/* 只在客户端显示延迟状态 */}
      {isClient && isStale && (
        <div className="loading-indicator">
          数据更新中...
        </div>
      )}
      
      <DataDisplay data={safeDeferredData} />
    </div>
  );
}

// Next.js专用解决方案
function NextJSSafeComponent() {
  const [mounted, setMounted] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  // 只在客户端挂载后使用延迟值
  const deferredQuery = mounted ? 
    useDeferredValue(searchQuery) : searchQuery;
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // 避免hydration不一致
  if (!mounted) {
    return (
      <div>
        <SearchInput value={searchQuery} onChange={setSearchQuery} />
        <SearchPlaceholder />
      </div>
    );
  }
  
  return (
    <div>
      <SearchInput value={searchQuery} onChange={setSearchQuery} />
      <SearchResults query={deferredQuery} />
    </div>
  );
}

// 高级：无闪烁SSR方案
function NoFlickerSSRComponent({ serverData }) {
  const [data, setData] = useState(serverData);
  const [isHydrated, setIsHydrated] = useState(false);
  
  // 使用useLayoutEffect避免闪烁
  useLayoutEffect(() => {
    setIsHydrated(true);
  }, []);
  
  const deferredData = useDeferredValue(data);
  
  // 智能状态检测
  const shouldShowStale = isHydrated && data !== deferredData;
  
  return (
    <div className={\`container \${shouldShowStale ? 'updating' : ''}\`}>
      <Controls onChange={setData} />
      
      {/* 使用CSS transition实现平滑过渡 */}
      <div className="content-wrapper">
        {shouldShowStale && (
          <div className="update-overlay">
            <span>更新中...</span>
          </div>
        )}
        
        <Content data={isHydrated ? deferredData : serverData} />
      </div>
    </div>
  );
}

// 通用SSR Hook
function useSSRDeferredValue(value, options = {}) {
  const { 
    enableOnClient = true,
    fallbackValue = value,
    ssrSafe = true 
  } = options;
  
  const [isClient, setIsClient] = useState(false);
  const deferredValue = useDeferredValue(value);
  
  useEffect(() => {
    if (enableOnClient) {
      setIsClient(true);
    }
  }, [enableOnClient]);
  
  // SSR安全模式
  if (ssrSafe && !isClient) {
    return {
      value: fallbackValue,
      deferredValue: fallbackValue,
      isStale: false,
      isSSR: true
    };
  }
  
  return {
    value,
    deferredValue,
    isStale: value !== deferredValue,
    isSSR: false
  };
}`
    }
  }
];

export default interviewQuestions; 