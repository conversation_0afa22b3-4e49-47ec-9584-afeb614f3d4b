import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'when-to-use',
    question: '什么时候应该使用useDeferredValue？',
    answer: `**使用useDeferredValue的典型场景**：

## ✅ 适合的场景

**1. 搜索和筛选**
- 搜索建议列表
- 数据表格筛选
- 实时搜索结果

**2. 数据可视化**
- 图表参数调整
- 复杂数据处理
- 实时数据更新

**3. 大量内容渲染**
- 长列表显示
- 复杂组件树
- 重计算密集型组件

**4. 非关键UI更新**
- 统计信息显示
- 次要内容区域
- 背景数据同步

## ❌ 不适合的场景

**1. 关键交互**
- 按钮状态（loading/disabled）
- 表单验证错误
- 导航状态

**2. 实时通信**
- 聊天消息
- 通知提醒
- 系统警告

**3. 简单快速更新**
- 计数器
- 开关状态
- 主题切换`,
    tags: ['使用场景', '最佳实践', '选择指南'],
    relatedQuestions: ['performance-boundaries', 'vs-debounce']
  },

  {
    id: 'performance-boundaries',
    question: 'useDeferredValue的延迟时间是多少？如何控制延迟程度？',
    answer: `**延迟时间特性**：

## 🕐 延迟时间不固定
useDeferredValue的延迟时间**不是固定的**，而是由React调度器根据当前系统状态动态决定：

- **系统空闲时**：几乎没有延迟（<16ms）
- **有用户交互时**：延迟直到交互完成
- **系统繁忙时**：自动推迟更新

## ⚙️ 影响延迟的因素

**1. 优先级队列状态**
\`\`\`
高优先级任务多 → 延迟时间长
系统相对空闲 → 延迟时间短
\`\`\`

**2. 设备性能**
\`\`\`
高性能设备 → 延迟感知较少
低性能设备 → 延迟更明显
\`\`\`

**3. 应用复杂度**
\`\`\`
简单应用 → 延迟时间短
复杂应用 → 可能延迟较长
\`\`\`

## 🎛️ 无法直接控制但可优化

虽然不能直接控制延迟时间，但可以通过以下方式优化：

**1. 合理划分优先级**
\`\`\`javascript
// 关键更新立即执行
setInputValue(value);

// 非关键更新使用延迟
const deferredValue = useDeferredValue(expensiveData);
\`\`\`

**2. 结合其他优化技术**
\`\`\`javascript
// 配合useMemo减少计算量
const processedData = useMemo(() => {
  return heavyCalculation(deferredValue);
}, [deferredValue]);

// 配合debounce处理高频更新
const debouncedInput = useDebounce(input, 200);
const deferredDebounced = useDeferredValue(debouncedInput);
\`\`\`

**3. 监控和反馈**
\`\`\`javascript
const isStale = value !== deferredValue;
const [delayStart, setDelayStart] = useState(null);

useEffect(() => {
  if (isStale && !delayStart) {
    setDelayStart(Date.now());
  } else if (!isStale && delayStart) {
    console.log('延迟时长:', Date.now() - delayStart);
    setDelayStart(null);
  }
}, [isStale, delayStart]);
\`\`\``,
    tags: ['性能优化', '延迟控制', '监控'],
    relatedQuestions: ['when-to-use', 'vs-debounce']
  },

  {
    id: 'vs-debounce',
    question: 'useDeferredValue和传统防抖（debounce）有什么区别？',
    answer: `**核心差异对比**：

## 🔄 传统防抖（Debounce）

**特点**：
- ✅ 固定延迟时间，可预测
- ✅ 简单易懂，兼容性好
- ✅ 可以取消和重置
- ❌ 无法感知系统负载
- ❌ 可能在不合适时机执行
- ❌ 不与React渲染周期协调

**适用场景**：
- API请求防抖
- 用户输入处理
- 事件监听优化

## ⚡ useDeferredValue

**特点**：
- ✅ 智能调度，系统负载感知
- ✅ 与React渲染周期完美配合
- ✅ 支持中断和恢复
- ✅ 自动优先级管理
- ❌ 延迟时间不可预测
- ❌ 只能在React 18+使用
- ❌ 调试相对复杂

**适用场景**：
- React组件内状态延迟
- UI渲染性能优化
- 大数据量处理

## 📊 实际对比示例

**传统防抖实现**：
\`\`\`javascript
function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay); // 固定延迟
    
    return () => clearTimeout(timer);
  }, [value, delay]);
  
  return debouncedValue;
}

// 使用
const debouncedQuery = useDebounce(query, 300); // 固定300ms
\`\`\`

**useDeferredValue实现**：
\`\`\`javascript
// 直接使用，无需自定义逻辑
const deferredQuery = useDeferredValue(query); // 智能延迟
\`\`\`

## 🤝 组合使用策略

在某些场景下，两者可以**配合使用**：

\`\`\`javascript
function OptimalSearchExample() {
  const [query, setQuery] = useState('');
  
  // 第一层：防抖减少高频更新
  const debouncedQuery = useDebounce(query, 200);
  
  // 第二层：智能延迟优化渲染
  const deferredQuery = useDeferredValue(debouncedQuery);
  
  return (
    <div>
      <input 
        value={query}
        onChange={(e) => setQuery(e.target.value)}
      />
      <SearchResults query={deferredQuery} />
    </div>
  );
}
\`\`\`

## 📈 性能对比

| 指标 | 防抖 | useDeferredValue | 组合使用 |
|------|------|------------------|----------|
| **CPU利用率** | 中等 | 优秀 | 最优 |
| **用户体验** | 好 | 很好 | 优秀 |
| **系统负载感知** | 无 | 优秀 | 优秀 |
| **可预测性** | 高 | 低 | 中等 |
| **实现复杂度** | 低 | 极低 | 中等 |`,
    tags: ['性能对比', '防抖节流', '组合策略'],
    relatedQuestions: ['when-to-use', 'performance-boundaries']
  },

  {
    id: 'value-consistency',
    question: '如何处理延迟值和原值不一致导致的UI状态问题？',
    answer: `**值不一致的常见问题及解决方案**：

## 🐛 常见问题场景

**1. 条件渲染问题**
\`\`\`javascript
// ❌ 可能导致内容闪烁
function ProblematicComponent() {
  const [data, setData] = useState([]);
  const deferredData = useDeferredValue(data);
  
  // 问题：data和deferredData不一致时显示什么？
  if (deferredData.length === 0) {
    return <EmptyState />; // 可能闪烁
  }
  
  return <DataList items={deferredData} />;
}
\`\`\`

**2. 依赖计算问题**
\`\`\`javascript
// ❌ 计算结果可能不匹配
function DependentCalculation() {
  const [count, setCount] = useState(0);
  const deferredCount = useDeferredValue(count);
  
  // 问题：total可能与显示的count不匹配
  const total = count * 2; // 基于最新值
  const display = deferredCount; // 延迟值
  
  return <div>{display} × 2 = {total}</div>; // 不一致！
}
\`\`\`

## ✅ 解决方案

**1. 智能状态检测**
\`\`\`javascript
function SmartStateHandling() {
  const [data, setData] = useState([]);
  const deferredData = useDeferredValue(data);
  
  const isStale = data !== deferredData;
  const displayData = deferredData.length === 0 && !isStale ? [] : deferredData;
  
  return (
    <div>
      {isStale && <div className="updating">数据更新中...</div>}
      
      {displayData.length === 0 ? (
        <EmptyState />
      ) : (
        <DataList items={displayData} />
      )}
    </div>
  );
}
\`\`\`

**2. 一致性计算**
\`\`\`javascript
function ConsistentCalculation() {
  const [count, setCount] = useState(0);
  const deferredCount = useDeferredValue(count);
  
  // 确保计算基于相同的值
  const calculatedTotal = useMemo(() => {
    return deferredCount * 2;
  }, [deferredCount]);
  
  const isStale = count !== deferredCount;
  
  return (
    <div>
      <div className={isStale ? 'stale' : 'fresh'}>
        {deferredCount} × 2 = {calculatedTotal}
      </div>
      {isStale && <span className="indicator">计算中...</span>}
    </div>
  );
}
\`\`\`

**3. 状态分层管理**
\`\`\`javascript
function LayeredStateManagement() {
  const [userInput, setUserInput] = useState('');
  const [processedData, setProcessedData] = useState([]);
  
  // 输入立即响应
  const inputDisplay = userInput;
  
  // 处理结果延迟更新
  const deferredProcessed = useDeferredValue(processedData);
  
  const isProcessing = processedData !== deferredProcessed;
  
  return (
    <div>
      {/* 输入区域：立即响应 */}
      <input 
        value={inputDisplay}
        onChange={(e) => setUserInput(e.target.value)}
      />
      
      {/* 结果区域：延迟更新 */}
      <div className={isProcessing ? 'processing' : 'ready'}>
        <ProcessedResults data={deferredProcessed} />
        {isProcessing && <ProcessingIndicator />}
      </div>
    </div>
  );
}
\`\`\`

**4. 自定义Hook封装**
\`\`\`javascript
function useConsistentDeferredValue(value) {
  const deferredValue = useDeferredValue(value);
  const [lastStableValue, setLastStableValue] = useState(value);
  
  // 记录最后稳定的值
  useEffect(() => {
    if (value === deferredValue) {
      setLastStableValue(value);
    }
  }, [value, deferredValue]);
  
  const isStale = value !== deferredValue;
  
  return {
    value: deferredValue,
    originalValue: value,
    lastStableValue,
    isStale,
    isReady: !isStale
  };
}

// 使用示例
function ConsistentComponent() {
  const [data, setData] = useState(initialData);
  const {
    value: deferredData,
    isStale,
    lastStableValue
  } = useConsistentDeferredValue(data);
  
  return (
    <div>
      <Controls onChange={setData} />
      
      {isStale ? (
        <div>
          <div className="stale-content">
            <DataDisplay data={lastStableValue} />
          </div>
          <div className="updating-overlay">
            <span>更新中...</span>
          </div>
        </div>
      ) : (
        <DataDisplay data={deferredData} />
      )}
    </div>
  );
}
\`\`\`

## 🎯 最佳实践总结

1. **明确状态边界**：区分关键状态和非关键状态
2. **一致性计算**：确保相关计算基于相同数据源
3. **适当的视觉反馈**：为用户提供清晰的状态指示
4. **渐进式增强**：先保证基础功能，再添加优化
5. **测试边界情况**：特别测试快速连续更新的场景`,
    tags: ['状态一致性', 'UI稳定性', '最佳实践'],
    relatedQuestions: ['when-to-use', 'ssr-compatibility']
  },

  {
    id: 'ssr-compatibility',
    question: 'useDeferredValue在SSR项目中如何正确使用？',
    answer: `**SSR环境中的使用指南**：

## 🚨 SSR环境的限制

**1. 服务端行为**
- 服务端没有用户交互，无优先级差异
- 延迟值始终等于传入值
- 无法利用并发特性

**2. Hydration风险**
- 服务端和客户端初始状态可能不同
- 可能导致内容不匹配警告
- 首次渲染可能出现闪烁

## ✅ 安全使用策略

**1. 基础SSR安全封装**
\`\`\`javascript
function useSSRSafeDeferredValue(value) {
  const [isClient, setIsClient] = useState(false);
  const deferredValue = useDeferredValue(value);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // SSR时返回原值，客户端返回延迟值
  return isClient ? deferredValue : value;
}
\`\`\`

**2. Next.js专用方案**
\`\`\`javascript
import { useEffect, useState } from 'react';

function NextJSComponent() {
  const [mounted, setMounted] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // 只在客户端挂载后使用延迟值
  const deferredQuery = mounted ? 
    useDeferredValue(searchQuery) : searchQuery;
  
  // 避免hydration不匹配
  if (!mounted) {
    return (
      <div>
        <SearchInput 
          value={searchQuery} 
          onChange={setSearchQuery} 
        />
        <SearchPlaceholder />
      </div>
    );
  }
  
  return (
    <div>
      <SearchInput 
        value={searchQuery} 
        onChange={setSearchQuery} 
      />
      <SearchResults query={deferredQuery} />
    </div>
  );
}
\`\`\`

**3. 渐进增强方案**
\`\`\`javascript
function ProgressiveEnhancement({ initialData }) {
  const [data, setData] = useState(initialData);
  const [enhancementEnabled, setEnhancementEnabled] = useState(false);
  
  // 延迟启用优化特性
  useLayoutEffect(() => {
    // 使用 requestIdleCallback 确保不影响初始渲染
    if (window.requestIdleCallback) {
      window.requestIdleCallback(() => {
        setEnhancementEnabled(true);
      });
    } else {
      // 降级处理
      setTimeout(() => setEnhancementEnabled(true), 100);
    }
  }, []);
  
  const displayData = enhancementEnabled ? 
    useDeferredValue(data) : data;
  
  return (
    <div>
      <DataControls onChange={setData} />
      <DataDisplay 
        data={displayData}
        enhanced={enhancementEnabled}
      />
    </div>
  );
}
\`\`\`

**4. 通用SSR Hook**
\`\`\`javascript
function useEnhancedDeferredValue(value, options = {}) {
  const {
    ssrSafe = true,
    fallbackValue = value,
    enableDelay = 100
  } = options;
  
  const [isClient, setIsClient] = useState(false);
  const [enhancementReady, setEnhancementReady] = useState(!ssrSafe);
  
  const deferredValue = useDeferredValue(value);
  
  useEffect(() => {
    setIsClient(true);
    
    if (ssrSafe) {
      const timer = setTimeout(() => {
        setEnhancementReady(true);
      }, enableDelay);
      
      return () => clearTimeout(timer);
    }
  }, [ssrSafe, enableDelay]);
  
  return {
    value: enhancementReady ? deferredValue : fallbackValue,
    isStale: enhancementReady && value !== deferredValue,
    isEnhanced: enhancementReady,
    isSSR: !isClient
  };
}

// 使用示例
function SSROptimizedComponent() {
  const [query, setQuery] = useState('');
  const {
    value: displayQuery,
    isStale,
    isEnhanced
  } = useEnhancedDeferredValue(query);
  
  return (
    <div>
      <SearchInput value={query} onChange={setQuery} />
      
      {isEnhanced && isStale && (
        <div className="search-indicator">搜索中...</div>
      )}
      
      <SearchResults 
        query={displayQuery}
        optimized={isEnhanced}
      />
    </div>
  );
}
\`\`\`

## 🔧 框架特定解决方案

**Next.js 13+ App Router**
\`\`\`javascript
'use client'; // 标记为客户端组件

import { useDeferredValue } from 'react';

export default function ClientOnlyDeferredComponent() {
  const [data, setData] = useState(initialData);
  const deferredData = useDeferredValue(data);
  
  // 在客户端组件中可以安全使用
  return <OptimizedComponent data={deferredData} />;
}
\`\`\`

**Nuxt.js + React**
\`\`\`javascript
function NuxtReactComponent() {
  const [isMounted, setIsMounted] = useState(false);
  
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  if (!isMounted) {
    return <ServerSafeComponent />;
  }
  
  return <ClientOptimizedComponent />;
}
\`\`\`

## ⚠️ 注意事项

1. **不要在服务端使用延迟状态检测**
2. **确保首次渲染的一致性**
3. **提供合适的加载状态**
4. **测试hydration过程**
5. **考虑SEO影响**`,
    tags: ['SSR', 'Next.js', 'Hydration'],
    relatedQuestions: ['value-consistency', 'when-to-use']
  },

  {
    id: 'debugging-tips',
    question: '如何调试useDeferredValue相关的性能问题？',
    answer: `**调试策略和工具**：

## 🔧 React DevTools调试

**1. Profiler使用**
\`\`\`javascript
// 启用并发特性调试
import { unstable_trace as trace } from 'scheduler/tracing';

function DebuggableComponent() {
  const [data, setData] = useState([]);
  const deferredData = useDeferredValue(data);
  
  // 标记延迟更新
  useEffect(() => {
    if (data !== deferredData) {
      trace('deferred-update', performance.now(), () => {
        console.log('延迟更新开始');
      });
    }
  }, [data, deferredData]);
  
  return <DataDisplay data={deferredData} />;
}
\`\`\`

**2. 性能监控Hook**
\`\`\`javascript
function usePerformanceMonitor(name) {
  const startTimeRef = useRef(null);
  
  const start = useCallback(() => {
    startTimeRef.current = performance.now();
    performance.mark(\`\${name}-start\`);
  }, [name]);
  
  const end = useCallback(() => {
    if (startTimeRef.current) {
      const duration = performance.now() - startTimeRef.current;
      performance.mark(\`\${name}-end\`);
      performance.measure(name, \`\${name}-start\`, \`\${name}-end\`);
      
      console.log(\`\${name} 耗时: \${duration.toFixed(2)}ms\`);
      startTimeRef.current = null;
      
      return duration;
    }
  }, [name]);
  
  return { start, end };
}

// 使用示例
function MonitoredComponent() {
  const [query, setQuery] = useState('');
  const deferredQuery = useDeferredValue(query);
  const monitor = usePerformanceMonitor('search-deferred');
  
  useEffect(() => {
    if (query !== deferredQuery) {
      monitor.start();
    } else {
      monitor.end();
    }
  }, [query, deferredQuery, monitor]);
  
  return <SearchResults query={deferredQuery} />;
}
\`\`\`

## 📊 性能指标收集

**1. 延迟统计**
\`\`\`javascript
function useDeferredMetrics(value) {
  const deferredValue = useDeferredValue(value);
  const [metrics, setMetrics] = useState({
    totalDelays: 0,
    averageDelay: 0,
    maxDelay: 0,
    delayHistory: []
  });
  
  useEffect(() => {
    const isStale = value !== deferredValue;
    
    if (isStale) {
      // 开始延迟
      const startTime = performance.now();
      
      const checkResolved = () => {
        if (value === deferredValue) {
          const delay = performance.now() - startTime;
          
          setMetrics(prev => {
            const newHistory = [...prev.delayHistory, delay].slice(-10);
            const newTotal = prev.totalDelays + 1;
            const newAverage = newHistory.reduce((a, b) => a + b, 0) / newHistory.length;
            
            return {
              totalDelays: newTotal,
              averageDelay: newAverage,
              maxDelay: Math.max(prev.maxDelay, delay),
              delayHistory: newHistory
            };
          });
        } else {
          requestAnimationFrame(checkResolved);
        }
      };
      
      requestAnimationFrame(checkResolved);
    }
  }, [value, deferredValue]);
  
  return { deferredValue, metrics };
}
\`\`\`

**2. 系统负载监控**
\`\`\`javascript
function useSystemLoad() {
  const [loadMetrics, setLoadMetrics] = useState({
    fps: 60,
    taskQueueLength: 0,
    isHighLoad: false
  });
  
  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let requestId;
    
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        const fps = frameCount;
        const isHighLoad = fps < 30;
        
        setLoadMetrics(prev => ({
          ...prev,
          fps,
          isHighLoad
        }));
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestId = requestAnimationFrame(measureFPS);
    };
    
    measureFPS();
    
    return () => {
      if (requestId) {
        cancelAnimationFrame(requestId);
      }
    };
  }, []);
  
  return loadMetrics;
}
\`\`\`

## 🐛 常见问题诊断

**1. 延迟时间过长**
\`\`\`javascript
function DiagnoseLongDelays() {
  const [data, setData] = useState([]);
  const deferredData = useDeferredValue(data);
  const systemLoad = useSystemLoad();
  
  useEffect(() => {
    const isStale = data !== deferredData;
    
    if (isStale && systemLoad.isHighLoad) {
      console.warn('⚠️ 系统负载过高，延迟时间可能较长', {
        fps: systemLoad.fps,
        dataSize: data.length,
        deferredSize: deferredData.length
      });
    }
  }, [data, deferredData, systemLoad]);
  
  // 提供优化建议
  const optimizationTips = useMemo(() => {
    if (systemLoad.fps < 30) {
      return [
        '考虑减少数据量',
        '优化渲染组件',
        '使用虚拟滚动'
      ];
    }
    return [];
  }, [systemLoad.fps]);
  
  return (
    <div>
      <DataDisplay data={deferredData} />
      {optimizationTips.length > 0 && (
        <div className="optimization-tips">
          <h3>性能优化建议：</h3>
          <ul>
            {optimizationTips.map((tip, index) => (
              <li key={index}>{tip}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
\`\`\`

**2. 内存泄漏检测**
\`\`\`javascript
function useMemoryLeakDetection(componentName) {
  const mountTime = useRef(Date.now());
  const [memoryUsage, setMemoryUsage] = useState(0);
  
  useEffect(() => {
    const checkMemory = () => {
      if (performance.memory) {
        const usage = performance.memory.usedJSHeapSize / 1024 / 1024;
        setMemoryUsage(usage);
        
        // 检测异常内存增长
        if (usage > 50) { // 50MB阈值
          console.warn(\`⚠️ \${componentName} 内存使用较高: \${usage.toFixed(2)}MB\`);
        }
      }
    };
    
    const interval = setInterval(checkMemory, 5000);
    return () => clearInterval(interval);
  }, [componentName]);
  
  useEffect(() => {
    return () => {
      const lifetime = Date.now() - mountTime.current;
      console.log(\`📊 \${componentName} 生命周期: \${lifetime}ms, 最终内存: \${memoryUsage.toFixed(2)}MB\`);
    };
  }, [componentName, memoryUsage]);
  
  return memoryUsage;
}
\`\`\`

## 🎯 调试最佳实践

1. **使用React DevTools Profiler**
2. **添加性能标记和测量**
3. **监控系统负载指标**
4. **记录延迟统计数据**
5. **检测内存使用情况**
6. **提供可视化的调试信息**
7. **在开发环境中启用详细日志**
8. **制定性能基准线**`,
    tags: ['调试技巧', '性能监控', 'React DevTools'],
    relatedQuestions: ['performance-boundaries', 'value-consistency']
  }
];

export default commonQuestions; 