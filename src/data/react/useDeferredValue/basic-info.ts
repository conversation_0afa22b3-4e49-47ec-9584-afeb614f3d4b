import { BasicInfo } from '../../../types/api';

const basicInfo: BasicInfo = {
  definition: 'useDeferredValue是React 18引入的并发特性Hook，用于延迟非关键值的更新。当React处理更紧急的任务（如用户输入）时，延迟值会保持之前的状态，直到有空闲时间再更新到最新值。这种机制可以避免非关键内容的渲染阻塞用户交互，显著提升应用的响应性和用户体验。',
  
  introduction: 'useDeferredValue解决了一个经典的用户体验问题：如何在处理大量数据更新时保持界面的响应性。传统的解决方案是防抖和节流，但它们使用固定的延迟时间，无法根据系统负载动态调整。useDeferredValue基于React 18的并发渲染能力，智能地根据当前任务优先级决定何时更新延迟值，实现了更精确和高效的性能优化。',

  syntax: `const deferredValue = useDeferredValue(value)

// 完整类型定义
function useDeferredValue<T>(value: T): T

// 使用模式
const [query, setQuery] = useState('');
const deferredQuery = useDeferredValue(query);

// 检测值是否过期
const isStale = query !== deferredQuery;

// 基于延迟值进行昂贵计算
const expensiveResult = useMemo(() => {
  return computeExpensiveValue(deferredQuery);
}, [deferredQuery]);`,

  parameters: [
    {
      name: 'value',
      type: 'T (泛型)',
      description: '需要延迟更新的值',
      required: true,
      details: `value参数的特征：
- 可以是任何类型的值：string、number、object、array等
- 当value频繁变化时，deferredValue会智能延迟更新
- React会根据当前任务优先级决定何时同步deferredValue
- 不会改变原值，只是提供一个延迟更新的副本`
    }
  ],

  returnValue: {
    type: 'T (与输入值相同类型)',
    description: `useDeferredValue返回输入值的延迟版本：

**返回值行为**：
\`\`\`typescript
const value = 'hello';
const deferredValue = useDeferredValue(value);

// 场景1：正常情况
// deferredValue === value (立即同步)

// 场景2：有紧急更新时
// deferredValue !== value (暂时保持旧值)

// 场景3：紧急更新完成后
// deferredValue === value (最终同步到最新值)
\`\`\`

**实际应用模式**：
| 更新类型 | 原值更新 | 延迟值更新 | 用途 |
|---------|---------|-----------|------|
| **用户输入** | 立即 | 立即 | 保持输入响应性 |
| **搜索结果** | 立即 | 延迟 | 避免阻塞输入 |
| **图表数据** | 立即 | 延迟 | 保持交互流畅 |`
  },

  keyFeatures: [
    {
      feature: '智能延迟机制',
      description: '基于React优先级系统的动态延迟',
      importance: 'critical',
      details: '不是固定时间延迟，而是根据当前任务优先级智能决定延迟时机，比传统防抖更精准'
    },
    {
      feature: '非阻塞更新',
      description: '延迟值更新不会阻塞紧急任务',
      importance: 'high',
      details: '当用户进行输入、点击等高优先级操作时，延迟值的更新会被推迟，确保用户交互流畅'
    },
    {
      feature: '过期状态检测',
      description: '可以检测延迟值是否过期',
      importance: 'high',
      details: '通过比较原值和延迟值，可以检测当前是否处于延迟状态，用于显示加载指示器'
    },
    {
      feature: '类型安全',
      description: '完整的TypeScript类型支持',
      importance: 'medium',
      details: '延迟值与原值保持完全相同的类型，确保类型安全和IDE支持'
    }
  ],

  commonUseCases: [
    {
      title: '搜索建议延迟',
      description: '在搜索输入时延迟搜索结果的更新，保持输入框响应性',
      code: `const [query, setQuery] = useState('');
const deferredQuery = useDeferredValue(query);

const suggestions = useMemo(() => {
  if (!deferredQuery) return [];
  return searchAPI(deferredQuery);
}, [deferredQuery]);

const isSearching = query !== deferredQuery;

return (
  <div>
    <input 
      value={query}
      onChange={(e) => setQuery(e.target.value)} // 立即响应
    />
    {isSearching && <div>搜索中...</div>}
    <SuggestionList items={suggestions} />
  </div>
);`
    },
    {
      title: '实时筛选优化',
      description: '在数据筛选时延迟结果更新，避免频繁重新渲染',
      code: `const [filter, setFilter] = useState('');
const deferredFilter = useDeferredValue(filter);

const filteredData = useMemo(() => {
  return largeDataSet.filter(item => 
    item.name.includes(deferredFilter)
  );
}, [deferredFilter]);

return (
  <div>
    <FilterInput 
      value={filter}
      onChange={setFilter} // 输入保持即时
    />
    <DataTable 
      data={filteredData} // 结果延迟更新
      isStale={filter !== deferredFilter}
    />
  </div>
);`
    },
    {
      title: '图表数据更新',
      description: '延迟图表数据的重新计算和渲染',
      code: `const [chartParams, setChartParams] = useState(defaultParams);
const deferredParams = useDeferredValue(chartParams);

const chartData = useMemo(() => {
  return generateChartData(deferredParams);
}, [deferredParams]);

return (
  <div>
    <ChartControls 
      params={chartParams}
      onChange={setChartParams} // 控件立即响应
    />
    <Chart 
      data={chartData} // 图表延迟更新
      isUpdating={chartParams !== deferredParams}
    />
  </div>
);`
    }
  ],

  bestPractices: [
    '🎯 **明确延迟目标**：只对非关键、计算昂贵的值使用延迟',
    '⚡ **保持输入响应**：用户直接交互的元素不应使用延迟值',
    '🔍 **利用过期检测**：使用 value !== deferredValue 检测延迟状态',
    '🔄 **结合useMemo**：延迟值通常与useMemo配合进行昂贵计算',
    '📱 **考虑移动端**：在性能较弱的设备上，延迟效果更明显',
    '🧪 **渐进式采用**：可以逐步将耗时更新迁移到延迟值',
    '⚖️ **平衡延迟**：避免过度使用导致界面更新不及时',
    '🎨 **视觉反馈**：为延迟状态提供适当的视觉指示'
  ],

  warnings: [
    '🚨 **React 18专属**：只能在React 18+的并发模式下使用',
    '⚠️ **不是防抖**：延迟时间不固定，由React根据优先级决定',
    '🔄 **值引用相等性**：延迟值与原值可能暂时不相等',
    '📈 **内存考虑**：延迟值会保持旧值的引用，注意内存使用',
    '🎭 **开发vs生产**：开发模式下延迟效果可能不明显',
    '🔍 **调试复杂性**：延迟更新增加了状态追踪的复杂度'
  ],

  notes: [
    'useDeferredValue是React 18并发特性的重要组成部分，与useTransition配合使用效果更佳',
    '相比传统的防抖节流，useDeferredValue提供了更智能的延迟机制',
    '延迟值的更新时机完全由React调度器控制，开发者无需关心具体时间',
    '在大型应用中，合理使用useDeferredValue可以显著提升用户体验'
  ]
};

export default basicInfo; 