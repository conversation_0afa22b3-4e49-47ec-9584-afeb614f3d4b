import { ApiItem } from '../../../types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import commonQuestions from './common-questions';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

// 特色可选Tab
import react18ConcurrentComparison from './react18-concurrent-comparison';

const useDeferredValueData: ApiItem = {
  id: 'useDeferredValue',
  title: 'useDeferredValue',
  description: 'React 18并发特性核心Hook，用于延迟非关键值的更新，优化用户体验感知',
  content: `useDeferredValue让你延迟值的更新，当有更紧急的更新需要处理时，延迟值会保持之前的状态。
它是React 18并发特性的重要组成部分，通过推迟非关键数据的渲染，让用户交互保持流畅响应，特别适用于搜索建议、实时筛选等场景。`,
  
  syntax: `const deferredValue = useDeferredValue(value)

// 完整类型定义
function useDeferredValue<T>(value: T): T`,

  example: `import React, { useState, useDeferredValue, useMemo } from 'react';

// 搜索建议示例：延迟搜索结果更新
function SearchSuggestions() {
  const [query, setQuery] = useState('');
  const deferredQuery = useDeferredValue(query);

  // 基于延迟值进行昂贵计算
  const suggestions = useMemo(() => {
    if (!deferredQuery) return [];
    
    // 模拟昂贵的搜索计算
    return searchDatabase(deferredQuery).slice(0, 10);
  }, [deferredQuery]);

  const isStale = query !== deferredQuery;

  return (
    <div>
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="输入搜索关键词..."
      />
      
      <div className={isStale ? 'suggestions stale' : 'suggestions'}>
        {suggestions.map(item => (
          <div key={item.id} className="suggestion-item">
            {item.title}
          </div>
        ))}
      </div>
      
      {isStale && <div className="loading">搜索中...</div>}
    </div>
  );
}

// 搜索输入保持即时响应，搜索结果延迟更新
function searchDatabase(query) {
  // 模拟数据库搜索
  return mockData.filter(item => 
    item.title.toLowerCase().includes(query.toLowerCase())
  );
}`,

  parameters: [
    {
      name: 'value',
      type: 'T',
      description: '需要延迟更新的值，可以是任何类型'
    }
  ],

  returnValue: {
    type: 'T',
    description: `useDeferredValue返回传入值的延迟版本：

**返回值特征**：
- 在初始渲染时，返回与传入值相同的值
- 当有紧急更新时，先返回之前的值，然后在后台更新
- 当没有更紧急的任务时，最终返回最新的值

**与原值的关系**：
\`\`\`typescript
const value = 'hello';
const deferredValue = useDeferredValue(value);

// 正常情况：deferredValue === value
// 有紧急更新时：deferredValue可能暂时不等于value
// 检测是否过期：value !== deferredValue
\`\`\``
  },

  notes: '注意：useDeferredValue是React 18的新特性，需要在并发渲染环境中使用。延迟值在紧急更新期间会暂时保持旧值',

  commonMistakes: [
    {
      title: '误解延迟机制',
      description: 'useDeferredValue不是防抖，而是基于React优先级的智能延迟',
      wrongCode: `// ❌ 错误理解：认为是防抖
const deferredValue = useDeferredValue(value);
// 期望固定延迟时间`,
      correctCode: `// ✅ 正确理解：基于优先级的智能延迟
const deferredValue = useDeferredValue(value);
const isStale = value !== deferredValue;
// 延迟时间由React根据优先级动态决定`
    },
    {
      title: '在错误场景使用',
      description: '用于不需要延迟的关键UI更新',
      wrongCode: `// ❌ 错误：关键按钮状态不应延迟
const deferredIsLoading = useDeferredValue(isLoading);
return <button disabled={deferredIsLoading}>提交</button>`,
      correctCode: `// ✅ 正确：关键状态直接使用，非关键内容延迟
return (
  <div>
    <button disabled={isLoading}>提交</button>
    <Results data={useDeferredValue(searchResults)} />
  </div>
);`
    }
  ],

  relatedHooks: ['useTransition', 'useMemo', 'useState'],
  category: 'react18',

  // 9个必选Tab的完整数据结构
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  knowledgeArchaeology,
  commonQuestions,
  performanceOptimization,
  debuggingTips,
  essenceInsights,

  // 1个特色可选Tab
  react18ConcurrentComparison
};

export default useDeferredValueData; 