import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  overview: `useDeferredValue作为React 18并发特性的核心Hook，在调试时容易出现延迟不生效、优先级混乱、状态不一致等问题。这些问题往往表现为性能优化失效、用户体验异常或并发渲染错误，需要专门的调试方法来快速定位和解决。

本指南提供4种核心调试策略、6个实用调试工具，以及5个预防技巧，帮助开发者掌握useDeferredValue的调试精髓，避免常见陷阱，充分发挥并发渲染的优势。`,
  
  troubleshooting: [
    {
      symptom: 'useDeferredValue延迟效果不明显或不生效',
      possibleCauses: [
        '应用没有启用并发渲染模式',
        '延迟的值变化不够频繁或计算不够昂贵',
        '没有配合useMemo等优化Hook使用'
      ],
      solutions: [
        '确保使用createRoot启用并发模式',
        '检查延迟值的使用场景是否合适',
        '配合useMemo优化计算逻辑'
      ],
      codeExample: `// 调试useDeferredValue延迟效果
import React, { useState, useDeferredValue, useMemo, useEffect } from 'react';
import { createRoot } from 'react-dom/client';

// 调试工具：延迟效果可视化
function DeferredValueDebugger() {
  const [value, setValue] = useState('');
  const deferredValue = useDeferredValue(value);
  const [renderCount, setRenderCount] = useState(0);
  const [deferredRenderCount, setDeferredRenderCount] = useState(0);
  
  // 跟踪渲染次数
  useEffect(() => {
    setRenderCount(prev => prev + 1);
  });
  
  useEffect(() => {
    setDeferredRenderCount(prev => prev + 1);
  }, [deferredValue]);
  
  // 模拟昂贵计算
  const expensiveResult = useMemo(() => {
    console.log('🔄 执行昂贵计算，延迟值:', deferredValue);
    const start = performance.now();
    
    // 模拟计算延迟
    let result = '';
    for (let i = 0; i < 100000; i++) {
      result += deferredValue;
    }
    
    const duration = performance.now() - start;
    console.log(\`⏱️ 计算耗时: \${duration.toFixed(2)}ms\`);
    
    return {
      result: result.slice(0, 100),
      computeTime: duration,
      timestamp: Date.now()
    };
  }, [deferredValue]);
  
  const isStale = value !== deferredValue;
  
  return (
    <div style={{ padding: '20px' }}>
      <h3>useDeferredValue调试器</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <label>输入值: </label>
        <input
          value={value}
          onChange={(e) => setValue(e.target.value)}
          placeholder="快速输入测试延迟效果..."
          style={{ width: '300px' }}
        />
      </div>
      
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
        <div style={{ border: '1px solid #ccc', padding: '10px' }}>
          <h4>实时值状态</h4>
          <p>当前值: "{value}"</p>
          <p>渲染次数: {renderCount}</p>
          <p style={{ color: isStale ? 'orange' : 'green' }}>
            状态: {isStale ? '有延迟更新' : '同步'}
          </p>
        </div>
        
        <div style={{ border: '1px solid #ccc', padding: '10px' }}>
          <h4>延迟值状态</h4>
          <p>延迟值: "{deferredValue}"</p>
          <p>延迟渲染次数: {deferredRenderCount}</p>
          <p>计算耗时: {expensiveResult.computeTime.toFixed(2)}ms</p>
        </div>
      </div>
      
      <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        <p>💡 调试提示:</p>
        <ul>
          <li>快速输入时，实时值会立即更新，延迟值会稍后更新</li>
          <li>如果延迟效果不明显，检查是否启用了并发模式</li>
          <li>观察控制台的计算日志，了解延迟值的更新时机</li>
        </ul>
      </div>
    </div>
  );
}

// 并发模式检查器
function ConcurrentModeChecker() {
  const [isConcurrent, setIsConcurrent] = useState(false);
  
  useEffect(() => {
    // 检查是否启用并发模式
    const checkConcurrentMode = () => {
      // React 18并发模式的特征检测
      const hasConcurrentFeatures = typeof React.startTransition === 'function';
      const hasCreateRoot = typeof createRoot === 'function';
      
      setIsConcurrent(hasConcurrentFeatures && hasCreateRoot);
    };
    
    checkConcurrentMode();
  }, []);
  
  return (
    <div style={{ 
      padding: '10px', 
      backgroundColor: isConcurrent ? '#d4edda' : '#f8d7da',
      border: \`1px solid \${isConcurrent ? '#c3e6cb' : '#f5c6cb'}\`,
      borderRadius: '4px',
      marginBottom: '20px'
    }}>
      <strong>并发模式状态: </strong>
      {isConcurrent ? (
        <span style={{ color: '#155724' }}>✅ 已启用</span>
      ) : (
        <span style={{ color: '#721c24' }}>❌ 未启用</span>
      )}
      
      {!isConcurrent && (
        <div style={{ marginTop: '10px', fontSize: '14px' }}>
          <p>要启用并发模式，请确保:</p>
          <ul>
            <li>使用React 18或更高版本</li>
            <li>使用createRoot而不是ReactDOM.render</li>
          </ul>
        </div>
      )}
    </div>
  );
}

// 正确的应用启动方式
function setupConcurrentApp() {
  // ❌ 错误：使用旧的render方法
  // ReactDOM.render(<App />, document.getElementById('root'));
  
  // ✅ 正确：使用createRoot启用并发模式
  const container = document.getElementById('root');
  const root = createRoot(container);
  root.render(<App />);
}`,
      severity: 'high'
    },
    {
      symptom: '延迟值状态不一致，导致UI显示错误',
      possibleCauses: [
        '没有正确检测延迟状态',
        '在延迟期间进行了不当的状态更新',
        '多个延迟值之间的同步问题'
      ],
      solutions: [
        '使用isStale模式检测延迟状态',
        '避免在延迟期间进行状态变更',
        '合理设计多个延迟值的协调机制'
      ],
      codeExample: `// 调试状态一致性问题
function StateConsistencyDebugger() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({ category: 'all' });
  
  const deferredSearchTerm = useDeferredValue(searchTerm);
  const deferredFilters = useDeferredValue(filters);
  
  // 状态一致性检查
  const searchStale = searchTerm !== deferredSearchTerm;
  const filtersStale = filters !== deferredFilters;
  const anyStale = searchStale || filtersStale;
  
  // 调试信息收集
  const debugInfo = {
    searchTerm,
    deferredSearchTerm,
    searchStale,
    filters: JSON.stringify(filters),
    deferredFilters: JSON.stringify(deferredFilters),
    filtersStale,
    anyStale,
    timestamp: Date.now()
  };
  
  // 记录状态变化
  useEffect(() => {
    console.log('🔍 状态变化调试:', debugInfo);
  }, [searchTerm, deferredSearchTerm, filters, deferredFilters]);
  
  const results = useMemo(() => {
    if (!deferredSearchTerm) return [];
    
    console.log('🔄 计算搜索结果:', {
      searchTerm: deferredSearchTerm,
      filters: deferredFilters
    });
    
    return performSearch(deferredSearchTerm, deferredFilters);
  }, [deferredSearchTerm, deferredFilters]);
  
  return (
    <div>
      <div style={{ marginBottom: '20px' }}>
        <input
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="搜索..."
        />
        
        <select
          value={filters.category}
          onChange={(e) => setFilters({ category: e.target.value })}
        >
          <option value="all">全部</option>
          <option value="tech">技术</option>
          <option value="design">设计</option>
        </select>
      </div>
      
      {/* 状态指示器 */}
      <div style={{ 
        padding: '10px', 
        backgroundColor: anyStale ? '#fff3cd' : '#d1ecf1',
        border: \`1px solid \${anyStale ? '#ffeaa7' : '#bee5eb'}\`,
        marginBottom: '20px'
      }}>
        <strong>状态调试信息:</strong>
        <ul style={{ margin: '5px 0', fontSize: '12px' }}>
          <li>搜索词: {searchStale ? '🔄 更新中' : '✅ 同步'}</li>
          <li>过滤器: {filtersStale ? '🔄 更新中' : '✅ 同步'}</li>
          <li>整体状态: {anyStale ? '⏳ 有延迟更新' : '✅ 完全同步'}</li>
        </ul>
      </div>
      
      {/* 结果显示 */}
      <div className={anyStale ? 'results stale' : 'results'}>
        <h4>搜索结果 ({results.length})</h4>
        {results.map(item => (
          <div key={item.id} style={{ padding: '5px', border: '1px solid #eee' }}>
            {item.title}
          </div>
        ))}
      </div>
    </div>
  );
}

// 高级调试：状态变化时间线
function StateTimelineDebugger() {
  const [value, setValue] = useState('');
  const deferredValue = useDeferredValue(value);
  const [timeline, setTimeline] = useState([]);
  
  // 记录状态变化时间线
  useEffect(() => {
    setTimeline(prev => [...prev, {
      type: 'value-change',
      value,
      timestamp: Date.now(),
      time: new Date().toLocaleTimeString()
    }]);
  }, [value]);
  
  useEffect(() => {
    setTimeline(prev => [...prev, {
      type: 'deferred-change',
      value: deferredValue,
      timestamp: Date.now(),
      time: new Date().toLocaleTimeString()
    }]);
  }, [deferredValue]);
  
  // 清理过多的时间线记录
  useEffect(() => {
    if (timeline.length > 50) {
      setTimeline(prev => prev.slice(-25));
    }
  }, [timeline.length]);
  
  return (
    <div>
      <input
        value={value}
        onChange={(e) => setValue(e.target.value)}
        placeholder="输入以查看状态变化时间线..."
      />
      
      <div style={{ 
        maxHeight: '300px', 
        overflow: 'auto', 
        border: '1px solid #ccc',
        marginTop: '10px',
        padding: '10px'
      }}>
        <h4>状态变化时间线:</h4>
        {timeline.slice(-20).map((entry, index) => (
          <div key={index} style={{
            fontSize: '12px',
            color: entry.type === 'value-change' ? 'blue' : 'green',
            marginBottom: '2px'
          }}>
            [{entry.time}] {entry.type}: "{entry.value}"
          </div>
        ))}
      </div>
    </div>
  );
}`,
      severity: 'medium'
    },
    {
      symptom: '性能优化效果不明显或反而变差',
      possibleCauses: [
        '使用场景不适合useDeferredValue',
        '延迟值的计算逻辑过于简单',
        '没有配合其他性能优化技术'
      ],
      solutions: [
        '评估是否真的需要延迟优化',
        '确保延迟值用于昂贵的计算',
        '配合useMemo、React.memo等优化'
      ],
      codeExample: `// 调试性能优化效果
function PerformanceDebugger() {
  const [query, setQuery] = useState('');
  const [useDeferred, setUseDeferred] = useState(true);
  
  const deferredQuery = useDeferredValue(query);
  const actualQuery = useDeferred ? deferredQuery : query;
  
  const [metrics, setMetrics] = useState({
    renderCount: 0,
    computeTime: 0,
    lastUpdate: Date.now()
  });
  
  // 性能监控
  const results = useMemo(() => {
    const start = performance.now();
    
    // 模拟昂贵计算
    const searchResults = performExpensiveSearch(actualQuery);
    
    const duration = performance.now() - start;
    
    setMetrics(prev => ({
      renderCount: prev.renderCount + 1,
      computeTime: duration,
      lastUpdate: Date.now()
    }));
    
    console.log(\`🔄 搜索计算耗时: \${duration.toFixed(2)}ms\`);
    
    return searchResults;
  }, [actualQuery]);
  
  const isStale = useDeferred && query !== deferredQuery;
  
  return (
    <div>
      <div style={{ marginBottom: '20px' }}>
        <label>
          <input
            type="checkbox"
            checked={useDeferred}
            onChange={(e) => setUseDeferred(e.target.checked)}
          />
          使用useDeferredValue
        </label>
      </div>
      
      <input
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="输入搜索词测试性能..."
      />
      
      <div style={{ 
        marginTop: '20px',
        padding: '10px',
        backgroundColor: '#f8f9fa',
        border: '1px solid #dee2e6'
      }}>
        <h4>性能指标:</h4>
        <ul>
          <li>渲染次数: {metrics.renderCount}</li>
          <li>最后计算耗时: {metrics.computeTime.toFixed(2)}ms</li>
          <li>延迟状态: {isStale ? '🔄 延迟中' : '✅ 同步'}</li>
          <li>优化模式: {useDeferred ? '启用' : '禁用'}</li>
        </ul>
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <h4>搜索结果 ({results.length})</h4>
        <div className={isStale ? 'results stale' : 'results'}>
          {results.slice(0, 10).map(item => (
            <div key={item.id}>{item.title}</div>
          ))}
        </div>
      </div>
    </div>
  );
}

function performExpensiveSearch(query) {
  // 模拟昂贵的搜索操作
  const items = Array.from({ length: 10000 }, (_, i) => ({
    id: i,
    title: \`Item \${i} - \${query}\`
  }));
  
  return items.filter(item => 
    item.title.toLowerCase().includes(query.toLowerCase())
  );
}`,
      severity: 'medium'
    }
  ],
  
  tools: [
    {
      name: 'React DevTools Profiler',
      description: 'React官方性能分析工具，用于监控useDeferredValue的渲染性能',
      usage: '在Profiler面板中记录组件渲染，分析延迟值对性能的影响',
      category: '浏览器扩展',
      documentation: 'https://react.dev/blog/2018/09/10/introducing-the-react-profiler'
    },
    {
      name: 'Chrome DevTools Performance',
      description: '浏览器性能分析工具，用于监控并发渲染的执行情况',
      usage: '在Performance面板中记录页面性能，查看任务调度和渲染时间线',
      category: '浏览器工具',
      documentation: 'https://developer.chrome.com/docs/devtools/performance/'
    },
    {
      name: 'React Concurrent Features DevTools',
      description: 'React 18并发特性的专用调试工具',
      usage: '监控Suspense、Transition和DeferredValue的工作状态',
      category: '开发工具',
      documentation: 'https://react.dev/blog/2022/03/29/react-v18#new-feature-concurrent-rendering'
    }
  ],
  
  bestPractices: [
    '使用React DevTools Profiler监控延迟值对渲染性能的影响',
    '建立状态一致性检查机制，及时发现延迟状态问题',
    '在开发环境中添加性能监控，对比优化前后的效果',
    '使用时间线调试工具，了解状态变化的时序关系',
    '定期检查并发模式的启用状态，确保延迟机制正常工作'
  ],
  
  commonMistakes: [
    {
      mistake: '在非并发模式下使用useDeferredValue',
      consequence: '延迟效果不生效，无法获得性能优化',
      solution: '确保使用createRoot启用并发模式',
      prevention: '建立并发模式检查机制，在开发环境中提醒'
    },
    {
      mistake: '对简单计算使用延迟值导致过度工程化',
      consequence: '增加了复杂性但没有性能收益',
      solution: '只在确实需要优化的昂贵计算中使用',
      prevention: '建立性能基准测试，验证优化效果'
    },
    {
      mistake: '忽略延迟状态的用户反馈',
      consequence: '用户不知道系统正在处理，体验不佳',
      solution: '使用isStale状态提供适当的用户反馈',
      prevention: '建立延迟状态的UI设计规范'
    }
  ]
};

export default debuggingTips;
