import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: '并发渲染优化',
      description: '利用useDeferredValue的并发特性，优化渲染优先级和用户体验',
      techniques: [
        {
          name: '智能优先级分离',
          description: '将关键UI更新与非关键内容更新分离，确保用户交互的即时响应',
          code: `// ❌ 性能问题 - 所有更新都是同步的，阻塞用户交互
function BadSearchComponent() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  
  useEffect(() => {
    // 同步执行昂贵的搜索，阻塞UI
    const searchResults = performExpensiveSearch(query);
    setResults(searchResults);
  }, [query]);
  
  return (
    <div>
      <input 
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="搜索..."
      />
      <ExpensiveResultsList results={results} />
    </div>
  );
}

// ✅ 性能优化 - 使用useDeferredValue分离优先级
function GoodSearchComponent() {
  const [query, setQuery] = useState('');
  const deferredQuery = useDeferredValue(query);
  
  // 基于延迟值计算结果，不阻塞输入响应
  const results = useMemo(() => {
    if (!deferredQuery) return [];
    return performExpensiveSearch(deferredQuery);
  }, [deferredQuery]);
  
  const isStale = query !== deferredQuery;
  
  return (
    <div>
      {/* 输入框保持即时响应 */}
      <input 
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="搜索..."
      />
      
      {/* 结果列表延迟更新，不阻塞输入 */}
      <div className={isStale ? 'results stale' : 'results'}>
        <ExpensiveResultsList results={results} />
        {isStale && <div className="loading-indicator">搜索中...</div>}
      </div>
    </div>
  );
}

// 高级优化：多层级延迟
function AdvancedSearchComponent() {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState({});
  
  // 不同优先级的延迟值
  const deferredQuery = useDeferredValue(query);
  const deferredFilters = useDeferredValue(filters);
  
  // 基础搜索结果（较高优先级）
  const basicResults = useMemo(() => {
    return performBasicSearch(deferredQuery);
  }, [deferredQuery]);
  
  // 过滤后的结果（较低优先级）
  const filteredResults = useMemo(() => {
    return applyFilters(basicResults, deferredFilters);
  }, [basicResults, deferredFilters]);
  
  const isQueryStale = query !== deferredQuery;
  const isFiltersStale = filters !== deferredFilters;
  
  return (
    <div>
      <input 
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="搜索..."
      />
      
      <FilterPanel 
        filters={filters}
        onChange={setFilters}
      />
      
      <div className="results">
        {isQueryStale && <div>正在搜索...</div>}
        {isFiltersStale && <div>正在过滤...</div>}
        <ResultsList results={filteredResults} />
      </div>
    </div>
  );
}`,
          impact: 'high',
          difficulty: 'medium'
        },
        {
          name: '渲染中断与恢复优化',
          description: '利用并发特性实现可中断的渲染，提升大数据集的处理性能',
          code: `// ❌ 性能问题 - 大列表渲染阻塞UI
function BadLargeList({ items, searchTerm }) {
  const filteredItems = items.filter(item => 
    item.title.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  return (
    <div>
      {/* 渲染大量项目时会阻塞UI */}
      {filteredItems.map(item => (
        <ExpensiveListItem key={item.id} item={item} />
      ))}
    </div>
  );
}

// ✅ 性能优化 - 使用useDeferredValue实现可中断渲染
function GoodLargeList({ items, searchTerm }) {
  const deferredSearchTerm = useDeferredValue(searchTerm);
  
  // 基于延迟值进行过滤，允许中断
  const filteredItems = useMemo(() => {
    return items.filter(item => 
      item.title.toLowerCase().includes(deferredSearchTerm.toLowerCase())
    );
  }, [items, deferredSearchTerm]);
  
  const isStale = searchTerm !== deferredSearchTerm;
  
  return (
    <div>
      {/* 显示过期状态，提供用户反馈 */}
      {isStale && (
        <div className="stale-indicator">
          正在更新结果...
        </div>
      )}
      
      {/* 使用虚拟化进一步优化大列表 */}
      <VirtualizedList 
        items={filteredItems}
        renderItem={({ item }) => (
          <ExpensiveListItem key={item.id} item={item} />
        )}
        className={isStale ? 'list-stale' : 'list-fresh'}
      />
    </div>
  );
}

// 高级优化：分片渲染
function ChunkedRenderingList({ items, searchTerm }) {
  const deferredSearchTerm = useDeferredValue(searchTerm);
  const [visibleCount, setVisibleCount] = useState(50);
  
  const filteredItems = useMemo(() => {
    return items.filter(item => 
      item.title.toLowerCase().includes(deferredSearchTerm.toLowerCase())
    );
  }, [items, deferredSearchTerm]);
  
  const visibleItems = useMemo(() => {
    return filteredItems.slice(0, visibleCount);
  }, [filteredItems, visibleCount]);
  
  const hasMore = filteredItems.length > visibleCount;
  const isStale = searchTerm !== deferredSearchTerm;
  
  const loadMore = useCallback(() => {
    setVisibleCount(prev => prev + 50);
  }, []);
  
  return (
    <div>
      {isStale && <div className="loading">更新中...</div>}
      
      {visibleItems.map(item => (
        <ListItem key={item.id} item={item} />
      ))}
      
      {hasMore && !isStale && (
        <button onClick={loadMore}>加载更多</button>
      )}
    </div>
  );
}`,
          impact: 'high',
          difficulty: 'hard'
        }
      ]
    },
    {
      title: '内存和计算优化',
      description: '优化useDeferredValue的内存使用和计算效率',
      techniques: [
        {
          name: '缓存策略优化',
          description: '结合缓存机制，避免重复计算延迟值',
          code: `// ❌ 性能问题 - 重复计算相同的延迟值
function BadCachedSearch({ query, options }) {
  const deferredQuery = useDeferredValue(query);
  
  // 每次都重新计算，即使是相同的查询
  const results = useMemo(() => {
    return performExpensiveSearch(deferredQuery, options);
  }, [deferredQuery, options]);
  
  return <SearchResults results={results} />;
}

// ✅ 性能优化 - 使用缓存避免重复计算
function GoodCachedSearch({ query, options }) {
  const deferredQuery = useDeferredValue(query);
  const cacheRef = useRef(new Map());
  
  const results = useMemo(() => {
    const cacheKey = \`\${deferredQuery}-\${JSON.stringify(options)}\`;
    
    if (cacheRef.current.has(cacheKey)) {
      return cacheRef.current.get(cacheKey);
    }
    
    const searchResults = performExpensiveSearch(deferredQuery, options);
    
    // 限制缓存大小，避免内存泄漏
    if (cacheRef.current.size > 100) {
      const firstKey = cacheRef.current.keys().next().value;
      cacheRef.current.delete(firstKey);
    }
    
    cacheRef.current.set(cacheKey, searchResults);
    return searchResults;
  }, [deferredQuery, options]);
  
  return <SearchResults results={results} />;
}

// 高级缓存：LRU缓存实现
class LRUCache {
  constructor(capacity) {
    this.capacity = capacity;
    this.cache = new Map();
  }
  
  get(key) {
    if (this.cache.has(key)) {
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return null;
  }
  
  set(key, value) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.capacity) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }
}

function AdvancedCachedSearch({ query, options }) {
  const deferredQuery = useDeferredValue(query);
  const cacheRef = useRef(new LRUCache(50));
  
  const results = useMemo(() => {
    const cacheKey = \`\${deferredQuery}-\${JSON.stringify(options)}\`;
    
    let searchResults = cacheRef.current.get(cacheKey);
    if (!searchResults) {
      searchResults = performExpensiveSearch(deferredQuery, options);
      cacheRef.current.set(cacheKey, searchResults);
    }
    
    return searchResults;
  }, [deferredQuery, options]);
  
  return <SearchResults results={results} />;
}`,
          impact: 'medium',
          difficulty: 'medium'
        }
      ]
    }
  ],

  performanceMetrics: {
    renderingTime: {
      description: '测量延迟渲染对整体渲染时间的影响',
      tool: 'React DevTools Profiler',
      example: '在Profiler中对比使用useDeferredValue前后的渲染时间'
    },
    interactionLatency: {
      description: '监控用户交互的响应延迟',
      tool: 'Performance API',
      example: 'performance.mark("interaction-start"); /* user interaction */ performance.mark("interaction-end");'
    },
    memoryUsage: {
      description: '监控延迟值和缓存的内存占用',
      tool: 'Chrome DevTools Memory',
      example: '对比使用useDeferredValue前后的内存快照'
    }
  },

  bestPractices: [
    '只对非关键UI更新使用useDeferredValue，保持关键交互的即时响应',
    '结合useMemo使用，避免在每次渲染时重新计算延迟值',
    '使用isStale状态提供用户反馈，改善感知性能',
    '合理设计缓存策略，避免重复计算相同的延迟值',
    '在大数据集场景中结合虚拟化技术使用',
    '监控渲染性能，确保延迟机制确实带来了性能提升',
    '避免过度使用，只在确实需要优化的场景中使用',
    '配合Suspense使用，提供更好的加载体验'
  ],

  commonPitfalls: [
    {
      issue: '对关键UI状态使用useDeferredValue导致用户体验下降',
      cause: '误将需要即时响应的状态（如按钮禁用状态）进行延迟',
      solution: '只对非关键的内容更新使用延迟，保持关键交互的即时性'
    },
    {
      issue: '过度使用导致系统复杂性增加',
      cause: '在不需要优化的简单场景中也使用useDeferredValue',
      solution: '只在确实存在性能问题的复杂场景中使用'
    },
    {
      issue: '缓存策略不当导致内存泄漏',
      cause: '无限制地缓存延迟值的计算结果',
      solution: '实现合理的缓存清理机制，限制缓存大小'
    }
  ]
};

export default performanceOptimization;
