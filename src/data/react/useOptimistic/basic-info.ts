import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "useOptimistic是React 19中用于乐观更新的Hook，允许在异步操作进行时立即显示预期结果",
  
  introduction: `useOptimistic是React 19引入的革命性Hook，专为优化用户体验而设计。它允许开发者在异步操作（如API请求）进行时立即更新UI，显示预期的结果，而不需要等待操作完成。这种"乐观更新"模式极大地提升了应用的响应性和用户满意度，特别适用于社交媒体点赞、表单提交、购物车操作等场景。`,

  syntax: `const [optimisticState, addOptimistic] = useOptimistic(
  state, 
  updateFn
);`,

  quickExample: `function LikeButton({ postId, initialLikes }) {
  const [optimisticLikes, addOptimisticLike] = useOptimistic(
    initialLikes,
    (currentLikes, amount) => currentLikes + amount
  );

  return (
    <button
      onClick={async () => {
        // 立即显示乐观更新结果
        addOptimisticLike(1);
        
        // 实际发送API请求
        try {
          await likePost(postId);
        } catch (error) {
          // 如果失败，React会自动回滚到原始状态
          console.error('点赞失败:', error);
        }
      }}
    >
      ❤️ {optimisticLikes} 个赞
    </button>
  );
}`,

  scenarioDiagram: `graph TD
    A[用户操作] --> B[立即UI更新]
    A --> C[异步API请求]
    
    B --> B1[显示预期结果]
    B --> B2[用户继续操作]
    B --> B3[无等待感]
    
    C --> C1[成功响应]
    C --> C2[失败处理]
    C --> C3[网络延迟]
    
    C1 --> D1[确认更新状态]
    C2 --> D2[自动回滚状态]
    C3 --> D3[保持乐观状态]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "state",
      type: "T",
      required: true,
      description: "当前的真实状态值，通常来自props或其他state",
      example: "{ likes: 42, comments: 5 }"
    },
    {
      name: "updateFn",
      type: "(currentState: T, optimisticValue: U) => T",
      required: true,
      description: "乐观更新函数，定义如何基于操作更新状态",
      example: "(current, action) => ({ ...current, likes: current.likes + action.delta })"
    }
  ],
  
  returnValue: {
    type: "[T, (optimisticValue: U) => void]",
    description: "返回包含乐观状态和触发乐观更新函数的数组",
    example: "const [optimisticData, addOptimistic] = useOptimistic(...)"
  },
  
  keyFeatures: [
    {
      title: "即时响应",
      description: "用户操作立即反映在UI上，无需等待网络请求完成",
      benefit: "极大提升用户体验，消除操作延迟感"
    },
    {
      title: "自动回滚",
      description: "当异步操作失败时，自动恢复到操作前的状态",
      benefit: "无需手动处理失败状态的UI回滚逻辑"
    },
    {
      title: "并发安全",
      description: "支持多个并发的乐观操作，React自动管理状态冲突",
      benefit: "简化复杂交互场景的状态管理"
    },
    {
      title: "类型安全",
      description: "完整的TypeScript支持，编译时检查状态更新逻辑",
      benefit: "减少运行时错误，提升代码可靠性"
    }
  ],
  
  limitations: [
    "仅在React 19+版本中可用，不向后兼容",
    "不适合需要服务器确认的关键操作（如支付处理）",
    "过度使用可能导致UI状态与服务器状态不一致",
    "需要合理的错误处理机制来处理乐观更新失败的情况"
  ],
  
  bestPractices: [
    "为乐观操作提供明确的视觉反馈（如loading状态或禁用按钮）",
    "在关键操作失败时，向用户显示清晰的错误信息",
    "合理设置乐观更新的范围，避免影响到不相关的UI部分",
    "结合React.Suspense使用，优化异步数据加载体验",
    "在开发环境中测试网络延迟和失败场景"
  ],
  
  warnings: [
    "不要在乐观更新中执行不可逆的本地操作",
    "避免在updateFn中执行副作用，保持函数纯净性",
    "注意乐观状态可能与实际服务器状态暂时不一致"
  ]
};

export default basicInfo;