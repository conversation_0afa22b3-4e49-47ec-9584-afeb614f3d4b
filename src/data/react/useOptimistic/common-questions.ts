import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'optimistic-state-not-updating',
    question: 'useOptimistic的乐观状态没有立即更新，界面仍然显示旧数据？',
    answer: '这通常是由于updateFn函数有问题、状态引用未改变、或组件重新渲染被阻止导致的。检查updateFn是否返回新对象，确保状态不可变性。',
    code: `// ❌ 错误：直接修改原状态对象
const [optimisticData, addOptimistic] = useOptimistic(
  initialData,
  (state, action) => {
    // 错误：直接修改state对象
    state.items.push(action.newItem);
    return state; // React无法检测到变化
  }
);

// ✅ 正确：返回新的状态对象
const [optimisticData, addOptimistic] = useOptimistic(
  initialData,
  (state, action) => {
    return {
      ...state,
      items: [...state.items, action.newItem], // 创建新数组
      total: state.total + action.newItem.price
    };
  }
);

// ✅ 对于复杂状态，使用immer辅助
import { produce } from 'immer';

const [optimisticData, addOptimistic] = useOptimistic(
  initialData,
  (state, action) => produce(state, draft => {
    draft.items.push(action.newItem);
    draft.total += action.newItem.price;
  })
);`,
    tags: ['状态更新', '调试'],
    relatedQuestions: ['乐观更新回滚失败', '状态不一致问题']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'rollback-not-working',
    question: '异步操作失败后，乐观更新没有自动回滚，如何解决？',
    answer: 'useOptimistic的自动回滚依赖于React的状态管理机制。确保异步操作失败时没有手动更新状态，让React自动处理回滚。避免在catch块中调用其他状态更新函数。',
    code: `// ❌ 错误：手动状态更新干扰自动回滚
function BadExample() {
  const [likes, setLikes] = useState(initialLikes);
  const [optimisticLikes, addOptimisticLike] = useOptimistic(
    likes,
    (current, amount) => current + amount
  );

  const handleLike = async () => {
    addOptimisticLike(1);
    
    try {
      const result = await likePost(postId);
      setLikes(result.likes); // ❌ 错误：手动更新干扰回滚
    } catch (error) {
      setLikes(initialLikes); // ❌ 错误：阻止自动回滚
    }
  };
}

// ✅ 正确：让React自动处理回滚
function GoodExample() {
  const [optimisticLikes, addOptimisticLike] = useOptimistic(
    serverLikes, // 来自props或context的服务器状态
    (current, amount) => current + amount
  );

  const handleLike = async () => {
    addOptimisticLike(1);
    
    try {
      await likePost(postId);
      // 成功后，父组件会重新获取数据，自动更新serverLikes
    } catch (error) {
      // 什么都不做，React会自动回滚到serverLikes
      toast.error('点赞失败，请重试');
    }
  };
}

// ✅ 最佳实践：配合SWR使用
function BestPracticeExample() {
  const { data: serverLikes, mutate } = useSWR(\`/api/posts/\${postId}/likes\`);
  const [optimisticLikes, addOptimisticLike] = useOptimistic(
    serverLikes || 0,
    (current, amount) => current + amount
  );

  const handleLike = async () => {
    addOptimisticLike(1);
    
    try {
      const result = await likePost(postId);
      mutate(result.likes, false); // 更新SWR缓存
    } catch (error) {
      mutate(); // 重新获取服务器数据，触发自动回滚
      toast.error('点赞失败');
    }
  };
}`,
    tags: ['回滚机制', '错误处理'],
    relatedQuestions: ['状态管理最佳实践', 'SWR集成问题']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'multiple-optimistic-updates',
    question: '多个并发的乐观更新导致状态混乱，如何处理？',
    answer: '多个并发更新需要合理设计updateFn函数，确保操作的幂等性和可组合性。可以为每个操作添加唯一标识，并在updateFn中处理冲突。',
    code: `// ❌ 问题：简单累加导致重复计算
const [optimisticCart, updateOptimisticCart] = useOptimistic(
  initialCart,
  (state, action) => {
    // 如果同时有多个"添加商品"操作，会重复累加
    return {
      ...state,
      total: state.total + action.price // ❌ 可能重复计算
    };
  }
);

// ✅ 解决方案1：基于操作类型设计
const [optimisticCart, updateOptimisticCart] = useOptimistic(
  initialCart,
  (state, action) => {
    switch (action.type) {
      case 'ADD_ITEM':
        const existingItem = state.items.find(item => item.id === action.item.id);
        if (existingItem) {
          return {
            ...state,
            items: state.items.map(item =>
              item.id === action.item.id
                ? { ...item, quantity: item.quantity + action.quantity }
                : item
            )
          };
        }
        return {
          ...state,
          items: [...state.items, { ...action.item, quantity: action.quantity }]
        };
      
      case 'REMOVE_ITEM':
        return {
          ...state,
          items: state.items.filter(item => item.id !== action.itemId)
        };
      
      default:
        return state;
    }
  }
);

// ✅ 解决方案2：操作去重机制
const [optimisticState, addOptimistic] = useOptimistic(
  initialState,
  (state, action) => {
    // 检查是否已经应用过相同的操作
    if (state.appliedOperations?.includes(action.operationId)) {
      return state; // 跳过重复操作
    }
    
    const newState = applyOperation(state, action);
    return {
      ...newState,
      appliedOperations: [...(state.appliedOperations || []), action.operationId]
    };
  }
);

// 使用时生成唯一ID
const handleAddItem = async (item) => {
  const operationId = generateUniqueId();
  
  addOptimistic({
    type: 'ADD_ITEM',
    item,
    operationId,
    timestamp: Date.now()
  });
  
  try {
    await addItemToCart(item, operationId);
  } catch (error) {
    // 处理错误
  }
};

// ✅ 解决方案3：使用Reducer模式
const cartReducer = (state, action) => {
  switch (action.type) {
    case 'ADD_ITEM':
      return addItem(state, action.payload);
    case 'UPDATE_QUANTITY':
      return updateQuantity(state, action.payload);
    case 'APPLY_COUPON':
      return applyCoupon(state, action.payload);
    default:
      return state;
  }
};

const [optimisticCart, dispatch] = useOptimistic(
  initialCart,
  cartReducer // 复用reducer逻辑，确保一致性
);`,
    tags: ['并发处理', '状态管理'],
    relatedQuestions: ['竞态条件处理', '操作幂等性设计']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'performance-issues',
    question: 'useOptimistic导致组件频繁重新渲染，影响性能？',
    answer: '性能问题通常由于updateFn复杂度过高、状态结构不合理、或缺少必要的优化导致。可以通过memoization、状态拆分、批量更新等方式解决。',
    code: `// ❌ 性能问题：复杂的updateFn导致每次都重新计算
const [optimisticData, addOptimistic] = useOptimistic(
  hugeDataSet,
  (state, action) => {
    // ❌ 每次更新都遍历整个大数组
    return {
      ...state,
      items: state.items.map(item => {
        if (item.category === action.category) {
          return { ...item, processed: true };
        }
        return item;
      }),
      // ❌ 复杂计算没有缓存
      statistics: calculateComplexStatistics(state.items)
    };
  }
);

// ✅ 性能优化1：状态拆分
// 将大状态拆分成多个小状态
const [optimisticItems, updateItems] = useOptimistic(items, itemsReducer);
const [optimisticStats, updateStats] = useOptimistic(stats, statsReducer);

// ✅ 性能优化2：memoization
const memoizedUpdateFn = useCallback((state, action) => {
  switch (action.type) {
    case 'UPDATE_ITEM':
      const newItems = updateSingleItem(state.items, action);
      return {
        ...state,
        items: newItems,
        // 只有items变化时才重新计算
        statistics: newItems !== state.items 
          ? calculateStats(newItems)
          : state.statistics
      };
    default:
      return state;
  }
}, []);

const [optimisticData, addOptimistic] = useOptimistic(
  initialData,
  memoizedUpdateFn
);

// ✅ 性能优化3：使用React.memo避免子组件重渲染
const OptimizedItem = React.memo(({ item, onUpdate }) => {
  return (
    <div>
      {item.name} - {item.value}
      <button onClick={() => onUpdate(item.id)}>Update</button>
    </div>
  );
});

// ✅ 性能优化4：批量更新
const [optimisticData, addOptimistic] = useOptimistic(
  initialData,
  (state, actions) => {
    // 支持批量操作
    if (Array.isArray(actions)) {
      return actions.reduce((currentState, action) => 
        applyAction(currentState, action), state
      );
    }
    return applyAction(state, actions);
  }
);

// 批量提交多个操作
const handleBatchUpdate = () => {
  const batchActions = [
    { type: 'UPDATE_A', payload: dataA },
    { type: 'UPDATE_B', payload: dataB },
    { type: 'UPDATE_C', payload: dataC }
  ];
  
  addOptimistic(batchActions);
};

// ✅ 性能优化5：虚拟化长列表
function VirtualizedOptimisticList() {
  const [optimisticItems, updateItems] = useOptimistic(
    largeItemList,
    listReducer
  );

  // 只渲染可见部分
  const visibleItems = useMemo(() => {
    return optimisticItems.slice(startIndex, endIndex);
  }, [optimisticItems, startIndex, endIndex]);

  return (
    <VirtualList
      items={visibleItems}
      renderItem={({ item }) => <OptimizedItem item={item} />}
    />
  );
}`,
    tags: ['性能优化', '重新渲染'],
    relatedQuestions: ['状态结构设计', 'React.memo使用']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'testing-optimistic-updates',
    question: '如何测试使用useOptimistic的组件？异步行为难以验证？',
    answer: '测试乐观更新需要模拟异步操作的成功和失败场景，验证UI的即时响应和最终状态。可以使用Testing Library的async utilities和MSW模拟网络请求。',
    code: `// ✅ 测试用例：验证乐观更新和回滚
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { rest } from 'msw';
import { setupServer } from 'msw/node';

// 设置MSW服务器模拟API
const server = setupServer(
  rest.post('/api/like', (req, res, ctx) => {
    const { shouldFail } = req.body;
    
    if (shouldFail) {
      return res(ctx.status(500), ctx.json({ error: 'Server error' }));
    }
    
    return res(ctx.json({ likes: 42 }));
  })
);

describe('OptimisticLikeButton', () => {
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  test('立即显示乐观更新结果', async () => {
    render(<OptimisticLikeButton initialLikes={10} postId="123" />);
    
    const button = screen.getByRole('button');
    expect(button).toHaveTextContent('❤️ 10');
    
    // 点击后立即检查UI变化
    fireEvent.click(button);
    expect(button).toHaveTextContent('❤️ 11'); // 立即更新
  });

  test('成功请求后保持乐观状态', async () => {
    render(<OptimisticLikeButton initialLikes={10} postId="123" />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    // 等待异步操作完成
    await waitFor(() => {
      expect(button).toHaveTextContent('❤️ 11'); // 保持乐观状态
    });
  });

  test('失败请求自动回滚', async () => {
    // 模拟API失败
    server.use(
      rest.post('/api/like', (req, res, ctx) => {
        return res(ctx.status(500));
      })
    );

    render(<OptimisticLikeButton initialLikes={10} postId="123" />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    // 立即显示乐观更新
    expect(button).toHaveTextContent('❤️ 11');
    
    // 等待失败和回滚
    await waitFor(() => {
      expect(button).toHaveTextContent('❤️ 10'); // 自动回滚
    });
    
    // 验证错误提示
    expect(screen.getByText('点赞失败，请重试')).toBeInTheDocument();
  });

  test('多个并发操作的处理', async () => {
    render(<OptimisticLikeButton initialLikes={10} postId="123" />);
    
    const button = screen.getByRole('button');
    
    // 快速连续点击
    fireEvent.click(button);
    fireEvent.click(button);
    fireEvent.click(button);
    
    // 验证最终状态
    expect(button).toHaveTextContent('❤️ 13');
    
    // 等待所有请求完成
    await waitFor(() => {
      expect(button).toHaveTextContent('❤️ 13'); // 应该保持正确状态
    });
  });
});

// ✅ 自定义Hook测试
import { renderHook, act } from '@testing-library/react';

describe('useOptimistic', () => {
  test('基本乐观更新功能', () => {
    const { result } = renderHook(() => 
      useOptimistic(0, (state, increment) => state + increment)
    );

    const [optimisticValue, addOptimistic] = result.current;
    expect(optimisticValue).toBe(0);

    act(() => {
      addOptimistic(1);
    });

    const [newValue] = result.current;
    expect(newValue).toBe(1);
  });

  test('复杂状态更新', () => {
    const initialState = { count: 0, items: [] };
    
    const { result } = renderHook(() => 
      useOptimistic(initialState, (state, action) => {
        switch (action.type) {
          case 'INCREMENT':
            return { ...state, count: state.count + 1 };
          case 'ADD_ITEM':
            return { 
              ...state, 
              items: [...state.items, action.item] 
            };
          default:
            return state;
        }
      })
    );

    act(() => {
      result.current[1]({ type: 'INCREMENT' });
    });

    expect(result.current[0].count).toBe(1);

    act(() => {
      result.current[1]({ type: 'ADD_ITEM', item: 'test' });
    });

    expect(result.current[0].items).toEqual(['test']);
  });
});

// ✅ 集成测试：完整用户流程
test('购物车乐观更新完整流程', async () => {
  render(<ShoppingCartApp />);
  
  // 1. 添加商品到购物车
  const addButton = screen.getByTestId('add-to-cart-123');
  fireEvent.click(addButton);
  
  // 2. 验证立即更新
  expect(screen.getByTestId('cart-count')).toHaveTextContent('1');
  
  // 3. 更新商品数量
  const increaseButton = screen.getByTestId('increase-quantity');
  fireEvent.click(increaseButton);
  
  // 4. 验证乐观更新
  expect(screen.getByTestId('item-quantity')).toHaveTextContent('2');
  
  // 5. 等待所有异步操作完成
  await waitFor(() => {
    expect(screen.getByTestId('cart-total')).toHaveTextContent('$39.98');
  });
  
  // 6. 验证最终状态正确
  expect(screen.getByTestId('cart-count')).toHaveTextContent('1');
  expect(screen.getByTestId('item-quantity')).toHaveTextContent('2');
});`,
    tags: ['测试', '异步验证'],
    relatedQuestions: ['MSW集成', 'Testing Library使用']
  }
];

export default commonQuestions;