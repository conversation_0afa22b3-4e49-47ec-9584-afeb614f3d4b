import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'React 19的useOptimistic Hook是什么？它解决了什么问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'useOptimistic是React 19新增的Hook，用于实现乐观更新，让用户操作立即反映在UI上，无需等待异步操作完成。',
      detailed: `useOptimistic是React 19引入的一个专门用于优化用户交互体验的Hook。它主要解决以下问题：

**1. 传统异步操作的用户体验问题**：
- 用户点击按钮后需要等待loading，体验不流畅
- 网络延迟导致的操作反馈滞后
- 用户可能重复点击或产生操作困惑

**2. 状态管理复杂性**：
- 需要手动管理loading、success、error三种状态
- 失败时的状态回滚逻辑复杂
- 多个并发操作的状态冲突处理困难

**3. 乐观更新的实现难点**：
- 手动实现乐观更新容易出错
- 需要考虑各种边界情况和错误处理
- 状态同步的复杂性

**useOptimistic的价值**：
- 提供原生的乐观更新支持
- 自动处理状态回滚和错误恢复
- 与React并发特性深度集成
- 简化开发者的使用复杂度`,
      code: `// 传统方式：需要手动管理多种状态
function TraditionalLikeButton({ postId, initialLikes }) {
  const [likes, setLikes] = useState(initialLikes);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleLike = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await likePost(postId);
      setLikes(result.likes);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button disabled={isLoading} onClick={handleLike}>
      {isLoading ? 'Loading...' : \`❤️ \${likes}\`}
      {error && <span>Error: {error}</span>}
    </button>
  );
}

// useOptimistic方式：乐观更新，简洁优雅
function OptimisticLikeButton({ postId, initialLikes }) {
  const [optimisticLikes, addOptimisticLike] = useOptimistic(
    initialLikes,
    (currentLikes, amount) => currentLikes + amount
  );

  const handleLike = async () => {
    // 立即更新UI
    addOptimisticLike(1);
    
    try {
      await likePost(postId);
    } catch (error) {
      // 失败时自动回滚，可选择显示错误提示
      toast.error('点赞失败，请重试');
    }
  };

  return (
    <button onClick={handleLike}>
      ❤️ {optimisticLikes}
    </button>
  );
}`
    },
    tags: ['基础概念', '用户体验', '状态管理']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'useOptimistic的内部实现原理是什么？它是如何实现自动回滚的？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: 'useOptimistic基于React 18的Transition机制，维护双重状态（真实状态和乐观状态），通过并发渲染实现自动回滚。',
      detailed: `useOptimistic的内部实现基于React 18的并发特性，主要包含以下几个核心机制：

**1. 双重状态管理**：
- baseState：真实的、经过服务器确认的状态
- optimisticState：包含乐观更新的临时状态
- 当没有待处理的乐观更新时，两者保持一致

**2. Transition机制集成**：
- 乐观更新被标记为可中断的Transition
- 利用React的时间分片和优先级调度
- 当真实数据返回时，高优先级更新会中断乐观更新

**3. 自动回滚原理**：
- React维护更新的版本号和依赖关系
- 当异步操作失败时，React会重新调度渲染
- 回滚到最后一个确认的baseState

**4. 并发安全机制**：
- 每个乐观更新都有唯一的标识符
- React会正确处理多个并发乐观操作
- 确保最终状态的一致性

**5. 生命周期管理**：
- 组件卸载时自动清理待处理的乐观更新
- 避免内存泄漏和状态污染`,
      code: `// 简化的内部实现原理（概念性代码）
function useOptimistic(baseState, updateFn) {
  // 获取当前的Fiber节点和Hook状态
  const hook = updateWorkInProgressHook();
  
  // 维护基础状态和乐观状态
  const [current] = useState(() => ({
    baseState,
    optimisticUpdates: [], // 待处理的乐观更新队列
    pendingTransitions: new Set() // 待处理的transition
  }));
  
  // 计算当前的乐观状态
  const optimisticState = useMemo(() => {
    return current.optimisticUpdates.reduce(
      (state, update) => updateFn(state, update.action),
      current.baseState
    );
  }, [current.baseState, current.optimisticUpdates, updateFn]);
  
  // 添加乐观更新的函数
  const addOptimistic = useCallback((action) => {
    const transitionId = generateTransitionId();
    
    // 标记为Transition更新
    startTransition(() => {
      // 添加到乐观更新队列
      current.optimisticUpdates.push({
        id: transitionId,
        action,
        timestamp: Date.now()
      });
      
      // 触发重新渲染
      forceUpdate();
    });
    
    current.pendingTransitions.add(transitionId);
  }, [current]);
  
  // 当组件接收到新的baseState时，清理对应的乐观更新
  useEffect(() => {
    current.baseState = baseState;
    // 清理已确认的乐观更新
    current.optimisticUpdates = current.optimisticUpdates.filter(
      update => current.pendingTransitions.has(update.id)
    );
  }, [baseState, current]);
  
  return [optimisticState, addOptimistic];
}

// 关键的状态同步逻辑
function syncOptimisticState(newBaseState, optimisticUpdates) {
  // 当真实状态更新时，React会：
  // 1. 检查是否有待处理的乐观更新
  // 2. 重新应用这些更新到新的基础状态
  // 3. 如果异步操作失败，则丢弃对应的乐观更新
  
  return optimisticUpdates.reduce((state, update) => {
    if (update.isValid) {
      return updateFn(state, update.action);
    }
    return state;
  }, newBaseState);
}`
    },
    tags: ['实现原理', '并发特性', '状态管理']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 3,
    question: '在实际项目中，你会如何设计一个支持乐观更新的购物车系统？需要考虑哪些关键因素？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '实战应用',
    answer: {
      brief: '设计乐观更新购物车需要考虑状态结构、错误处理、用户反馈、数据同步等多个方面，确保良好的用户体验和数据一致性。',
      detailed: `设计支持乐观更新的购物车系统需要从多个维度进行考虑：

**1. 状态结构设计**：
- 购物车状态应该包含足够的信息支持各种操作
- 设计合理的updateFn来处理不同类型的更新
- 考虑商品属性、价格、库存等复杂情况

**2. 错误处理策略**：
- 区分不同类型的错误（网络错误、业务错误、验证错误）
- 设计合适的错误反馈机制
- 考虑部分失败的情况（如部分商品添加成功）

**3. 用户体验优化**：
- 提供清晰的视觉反馈（loading状态、确认状态）
- 避免用户在乐观更新期间的困惑
- 合理的防抖和节流策略

**4. 数据同步机制**：
- 定期同步服务器状态
- 处理价格变动、库存变化等实时数据
- 考虑离线场景的数据存储

**5. 性能考虑**：
- 避免过度的重新渲染
- 合理的状态更新粒度
- 考虑大量商品时的性能表现`,
      code: `// 完整的购物车乐观更新设计
interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  maxQuantity?: number;
  isOptimistic?: boolean; // 标记乐观更新的商品
}

interface CartState {
  items: CartItem[];
  total: number;
  itemCount: number;
  appliedCoupons: string[];
  errors: Record<string, string>; // 错误状态
}

function ShoppingCartSystem({ userId }: { userId: string }) {
  // 从服务器获取初始状态
  const { data: serverCart, mutate } = useSWR(
    \`/api/cart/\${userId}\`,
    fetchCart
  );
  
  const [optimisticCart, updateOptimisticCart] = useOptimistic(
    serverCart || { items: [], total: 0, itemCount: 0, appliedCoupons: [], errors: {} },
    (currentCart, action) => {
      switch (action.type) {
        case 'ADD_ITEM':
          return addItemToCart(currentCart, action);
        case 'UPDATE_QUANTITY':
          return updateItemQuantity(currentCart, action);
        case 'REMOVE_ITEM':
          return removeItemFromCart(currentCart, action);
        case 'APPLY_COUPON':
          return applyCouponToCart(currentCart, action);
        case 'SET_ERROR':
          return { ...currentCart, errors: { ...currentCart.errors, [action.itemId]: action.error } };
        case 'CLEAR_ERROR':
          const { [action.itemId]: _, ...restErrors } = currentCart.errors;
          return { ...currentCart, errors: restErrors };
        default:
          return currentCart;
      }
    }
  );

  // 添加商品到购物车
  const addItem = async (product: Product, quantity: number = 1) => {
    // 乐观更新
    updateOptimisticCart({ 
      type: 'ADD_ITEM', 
      item: { ...product, quantity, isOptimistic: true }
    });

    try {
      const result = await cartAPI.addItem(userId, product.id, quantity);
      
      // 成功后同步服务器状态
      mutate(result, false);
      
      // 显示成功反馈
      toast.success(\`\${product.name} 已添加到购物车\`);
      
    } catch (error) {
      // 处理不同类型的错误
      if (error.code === 'INSUFFICIENT_STOCK') {
        updateOptimisticCart({ 
          type: 'SET_ERROR', 
          itemId: product.id, 
          error: '库存不足' 
        });
      } else if (error.code === 'PRODUCT_UNAVAILABLE') {
        toast.error('商品已下架');
      } else {
        toast.error('添加失败，请重试');
      }
    }
  };

  // 更新商品数量
  const updateQuantity = async (itemId: string, newQuantity: number) => {
    const item = optimisticCart.items.find(item => item.id === itemId);
    if (!item) return;

    // 验证数量范围
    if (newQuantity < 0 || (item.maxQuantity && newQuantity > item.maxQuantity)) {
      toast.warning('数量超出限制');
      return;
    }

    if (newQuantity === 0) {
      return removeItem(itemId);
    }

    // 乐观更新
    updateOptimisticCart({ 
      type: 'UPDATE_QUANTITY', 
      itemId, 
      newQuantity 
    });

    try {
      const result = await cartAPI.updateQuantity(userId, itemId, newQuantity);
      mutate(result, false);
      
    } catch (error) {
      updateOptimisticCart({ 
        type: 'SET_ERROR', 
        itemId, 
        error: '更新失败' 
      });
    }
  };

  // 应用优惠券
  const applyCoupon = async (couponCode: string) => {
    try {
      // 先验证优惠券
      const couponInfo = await cartAPI.validateCoupon(couponCode);
      
      // 验证成功后才进行乐观更新
      updateOptimisticCart({ 
        type: 'APPLY_COUPON', 
        coupon: couponInfo 
      });
      
      const result = await cartAPI.applyCoupon(userId, couponCode);
      mutate(result, false);
      
      toast.success(\`优惠券 \${couponCode} 已应用\`);
      
    } catch (error) {
      toast.error('优惠券无效或已过期');
    }
  };

  // 定期同步服务器状态
  useEffect(() => {
    const interval = setInterval(() => {
      mutate(); // 重新获取服务器状态
    }, 30000); // 每30秒同步一次

    return () => clearInterval(interval);
  }, [mutate]);

  return (
    <CartContext.Provider value={{ 
      cart: optimisticCart, 
      addItem, 
      updateQuantity, 
      removeItem,
      applyCoupon 
    }}>
      <CartComponent />
    </CartContext.Provider>
  );
}

// 工具函数：处理添加商品的逻辑
function addItemToCart(cart: CartState, action: AddItemAction): CartState {
  const existingItem = cart.items.find(item => item.id === action.item.id);
  
  if (existingItem) {
    return {
      ...cart,
      items: cart.items.map(item =>
        item.id === action.item.id
          ? { ...item, quantity: item.quantity + action.item.quantity }
          : item
      ),
      total: cart.total + (action.item.price * action.item.quantity),
      itemCount: cart.itemCount + action.item.quantity
    };
  }
  
  return {
    ...cart,
    items: [...cart.items, action.item],
    total: cart.total + (action.item.price * action.item.quantity),
    itemCount: cart.itemCount + action.item.quantity
  };
}`
    },
    tags: ['实战应用', '购物车', '系统设计']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 4,
    question: 'useOptimistic在高并发场景下可能遇到哪些问题？如何设计解决方案？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '高级应用',
    answer: {
      brief: '高并发场景下useOptimistic面临状态冲突、竞态条件、内存溢出等问题，需要通过版本控制、冲突解决策略、资源管理等手段解决。',
      detailed: `高并发场景下useOptimistic面临的主要挑战和解决方案：

**1. 状态冲突问题**：
- **问题**：多个用户同时操作同一资源，乐观更新产生冲突
- **解决方案**：实现三路合并算法，类似Git的冲突解决机制
- **策略**：最后写入胜出、操作类型优先级、用户权限优先级

**2. 竞态条件（Race Condition）**：
- **问题**：网络请求返回顺序与发送顺序不一致
- **解决方案**：为每个请求分配版本号，忽略过期的响应
- **策略**：使用AbortController取消过期请求

**3. 内存泄漏风险**：
- **问题**：大量未完成的乐观更新占用内存
- **解决方案**：设置更新队列大小限制，清理过期更新
- **策略**：LRU缓存策略、定期垃圾回收

**4. 性能问题**：
- **问题**：频繁的状态更新导致性能下降
- **解决方案**：防抖、节流、批量更新
- **策略**：使用unstable_batchedUpdates合并更新

**5. 数据一致性保障**：
- **问题**：客户端状态与服务器状态不一致
- **解决方案**：定期校验、冲突检测、自动同步机制
- **策略**：CRDTs（无冲突复制数据类型）算法`,
      code: `// 高并发场景下的useOptimistic增强版本
interface OptimisticUpdate<T, A> {
  id: string;
  timestamp: number;
  version: number;
  action: A;
  status: 'pending' | 'confirmed' | 'failed' | 'expired';
}

function useEnhancedOptimistic<T, A>(
  baseState: T,
  updateFn: (state: T, action: A) => T,
  options: {
    maxUpdates?: number;
    expireTime?: number;
    conflictResolver?: (conflicts: OptimisticUpdate<T, A>[]) => A[];
  } = {}
) {
  const { maxUpdates = 50, expireTime = 30000, conflictResolver } = options;
  
  const [state, setState] = useState(() => ({
    baseState,
    updates: new Map<string, OptimisticUpdate<T, A>>(),
    version: 0,
    conflicts: []
  }));

  // 计算当前乐观状态
  const optimisticState = useMemo(() => {
    const validUpdates = Array.from(state.updates.values())
      .filter(update => 
        update.status === 'pending' && 
        Date.now() - update.timestamp < expireTime
      )
      .sort((a, b) => a.timestamp - b.timestamp);

    return validUpdates.reduce(
      (currentState, update) => updateFn(currentState, update.action),
      state.baseState
    );
  }, [state, updateFn, expireTime]);

  // 清理过期的更新
  const cleanupExpiredUpdates = useCallback(() => {
    const now = Date.now();
    const newUpdates = new Map(state.updates);
    
    for (const [id, update] of newUpdates) {
      if (now - update.timestamp > expireTime || update.status !== 'pending') {
        newUpdates.delete(id);
      }
    }
    
    setState(prev => ({ ...prev, updates: newUpdates }));
  }, [state.updates, expireTime]);

  // 定期清理
  useEffect(() => {
    const interval = setInterval(cleanupExpiredUpdates, 5000);
    return () => clearInterval(interval);
  }, [cleanupExpiredUpdates]);

  // 添加乐观更新
  const addOptimistic = useCallback((action: A) => {
    const updateId = generateUniqueId();
    const version = state.version + 1;
    
    // 检查是否超出最大更新数量
    if (state.updates.size >= maxUpdates) {
      console.warn('Too many pending optimistic updates');
      return;
    }

    const update: OptimisticUpdate<T, A> = {
      id: updateId,
      timestamp: Date.now(),
      version,
      action,
      status: 'pending'
    };

    setState(prev => ({
      ...prev,
      version,
      updates: new Map(prev.updates).set(updateId, update)
    }));

    return updateId;
  }, [state.updates.size, state.version, maxUpdates]);

  // 确认更新
  const confirmUpdate = useCallback((updateId: string) => {
    setState(prev => {
      const newUpdates = new Map(prev.updates);
      const update = newUpdates.get(updateId);
      
      if (update) {
        newUpdates.set(updateId, { ...update, status: 'confirmed' });
      }
      
      return { ...prev, updates: newUpdates };
    });
  }, []);

  // 处理冲突
  const resolveConflict = useCallback((serverState: T) => {
    if (!conflictResolver) {
      // 简单策略：服务器状态优先
      setState(prev => ({
        ...prev,
        baseState: serverState,
        updates: new Map() // 清空所有乐观更新
      }));
      return;
    }

    const conflicts = Array.from(state.updates.values())
      .filter(update => update.status === 'pending');
    
    const resolvedActions = conflictResolver(conflicts);
    
    // 应用解决后的动作
    const newState = resolvedActions.reduce(
      (currentState, action) => updateFn(currentState, action),
      serverState
    );

    setState(prev => ({
      ...prev,
      baseState: newState,
      updates: new Map(),
      conflicts: []
    }));
  }, [conflictResolver, state.updates, updateFn]);

  return {
    optimisticState,
    addOptimistic,
    confirmUpdate,
    resolveConflict,
    pendingCount: state.updates.size,
    conflicts: state.conflicts
  };
}

// 使用示例：协作编辑场景
function CollaborativeEditor({ documentId }) {
  const {
    optimisticState: document,
    addOptimistic,
    confirmUpdate,
    resolveConflict
  } = useEnhancedOptimistic(
    { content: '', cursors: {} },
    (state, action) => {
      switch (action.type) {
        case 'INSERT_TEXT':
          return {
            ...state,
            content: insertText(state.content, action.position, action.text)
          };
        case 'DELETE_TEXT':
          return {
            ...state,
            content: deleteText(state.content, action.start, action.end)
          };
        default:
          return state;
      }
    },
    {
      maxUpdates: 100,
      expireTime: 60000,
      conflictResolver: (conflicts) => {
        // 实现操作变换算法（Operational Transformation）
        return resolveTextEditingConflicts(conflicts);
      }
    }
  );

  const handleTextChange = async (position: number, text: string) => {
    const updateId = addOptimistic({
      type: 'INSERT_TEXT',
      position,
      text,
      timestamp: Date.now()
    });

    try {
      const result = await editorAPI.insertText(documentId, position, text);
      confirmUpdate(updateId);
    } catch (error) {
      if (error.code === 'CONFLICT') {
        resolveConflict(error.serverState);
      }
    }
  };

  return (
    <EditorComponent 
      document={document}
      onChange={handleTextChange}
    />
  );
}`
    },
    tags: ['高级应用', '并发处理', '性能优化']
  }
];

export default interviewQuestions;