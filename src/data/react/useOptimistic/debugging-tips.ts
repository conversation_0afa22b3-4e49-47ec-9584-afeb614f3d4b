import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
  completionStatus: '内容已完成',
  
  troubleshooting: [
    {
      symptom: '乐观状态没有立即更新',
      description: '用户操作后UI没有立即响应，仍显示旧状态',
      steps: [
        '1. 检查updateFn是否返回新的对象引用',
        '2. 确认没有直接修改原状态对象',
        '3. 验证React.StrictMode下的双重调用行为',
        '4. 使用React DevTools检查状态变化'
      ],
      code: `// 🔍 调试技巧：添加日志跟踪状态变化
const [optimisticState, addOptimistic] = useOptimistic(
  initialState,
  (state, action) => {
    console.log('🔄 updateFn called:', { state, action });
    
    // 确保返回新对象
    const newState = { ...state, [action.field]: action.value };
    
    console.log('✅ newState:', newState);
    console.log('🆔 Reference changed:', newState !== state);
    
    return newState;
  }
);

// 🔍 调试技巧：使用useEffect监控状态变化
useEffect(() => {
  console.log('📊 Optimistic state changed:', optimisticState);
}, [optimisticState]);

// 🔍 调试技巧：验证updateFn的纯净性
const debugUpdateFn = (state, action) => {
  const originalState = JSON.stringify(state);
  const result = updateFn(state, action);
  
  if (JSON.stringify(state) !== originalState) {
    console.error('⚠️ updateFn modified original state!');
  }
  
  return result;
};`
    },
    {
      symptom: '乐观更新没有自动回滚',
      description: '异步操作失败后，UI仍显示乐观状态而没有回滚到原始状态',
      steps: [
        '1. 确认基础状态来源是否正确',
        '2. 检查是否在错误处理中手动修改了状态',
        '3. 验证异步操作的错误处理逻辑',
        '4. 使用React DevTools观察状态树变化'
      ],
      code: `// 🔍 调试技巧：追踪回滚过程
function DebuggableOptimisticComponent({ serverState }) {
  console.log('🌐 Server state:', serverState);
  
  const [optimisticState, addOptimistic] = useOptimistic(
    serverState,
    (state, action) => {
      console.log('🔄 Optimistic update:', { state, action });
      return { ...state, value: action.value };
    }
  );

  const handleUpdate = async (newValue) => {
    console.log('🚀 Starting optimistic update:', newValue);
    
    addOptimistic({ value: newValue });
    
    try {
      await updateServerValue(newValue);
      console.log('✅ Server update successful');
    } catch (error) {
      console.log('❌ Server update failed:', error);
      console.log('🔄 React will auto-rollback to:', serverState);
      
      // ⚠️ 不要在这里手动设置状态！
      // setState(serverState); // 这会阻止自动回滚
    }
  };

  // 🔍 监控状态变化
  useEffect(() => {
    console.log('📊 Component state:', {
      serverState,
      optimisticState,
      isOptimistic: optimisticState !== serverState
    });
  }, [serverState, optimisticState]);
}`
    },
    {
      symptom: '多个并发更新导致状态混乱',
      description: '快速连续的操作导致状态不一致或意外的结果',
      steps: [
        '1. 使用唯一ID标识每个操作',
        '2. 在updateFn中添加操作去重逻辑',
        '3. 监控操作队列的长度和处理状态',
        '4. 使用时间戳调试操作顺序'
      ],
      code: `// 🔍 调试技巧：操作追踪和去重
function ConcurrentUpdatesDebugger() {
  const [operationLog, setOperationLog] = useState([]);
  
  const [optimisticState, addOptimistic] = useOptimistic(
    initialState,
    (state, action) => {
      const timestamp = Date.now();
      const operationId = action.id || \`op_\${timestamp}\`;
      
      console.log('🔄 Processing operation:', {
        id: operationId,
        type: action.type,
        timestamp: new Date(timestamp).toISOString(),
        currentState: state
      });

      // 检查重复操作
      if (state.processedOperations?.includes(operationId)) {
        console.warn('⚠️ Duplicate operation detected:', operationId);
        return state;
      }

      const newState = {
        ...state,
        data: applyUpdate(state.data, action),
        processedOperations: [
          ...(state.processedOperations || []), 
          operationId
        ],
        lastUpdated: timestamp
      };

      // 记录操作日志
      setOperationLog(prev => [...prev, {
        id: operationId,
        type: action.type,
        timestamp,
        success: true
      }]);

      return newState;
    }
  );

  const debuggedAddOptimistic = useCallback((action) => {
    const operationId = \`op_\${Date.now()}_\${Math.random().toString(36).substr(2, 9)}\`;
    console.log('🚀 Queuing operation:', operationId);
    
    addOptimistic({ ...action, id: operationId });
  }, [addOptimistic]);

  // 🔍 操作统计面板
  const operationStats = useMemo(() => {
    const total = operationLog.length;
    const successful = operationLog.filter(op => op.success).length;
    const failed = total - successful;
    
    return { total, successful, failed };
  }, [operationLog]);

  return (
    <div>
      <div>状态: {JSON.stringify(optimisticState.data)}</div>
      <div>操作统计: {operationStats.total} 总计, {operationStats.successful} 成功, {operationStats.failed} 失败</div>
      
      {/* 操作日志 */}
      <details>
        <summary>操作日志 ({operationLog.length})</summary>
        {operationLog.map(op => (
          <div key={op.id}>
            {new Date(op.timestamp).toLocaleTimeString()} - {op.type} ({op.id})
          </div>
        ))}
      </details>
    </div>
  );
}`
    }
  ],
  
  commonErrors: [
    {
      error: 'TypeError: Cannot read properties of undefined',
      cause: '基础状态为undefined或null，或者在updateFn中访问了不存在的属性',
      solution: '添加防御性检查和默认值处理',
      prevention: '在组件渲染前确保初始状态的完整性',
      code: `// ❌ 错误：没有防御性检查
const [optimisticUser, updateUser] = useOptimistic(
  user, // 可能为undefined
  (state, action) => ({
    ...state,
    profile: {
      ...state.profile, // state.profile可能不存在
      name: action.name
    }
  })
);

// ✅ 正确：添加防御性检查
const [optimisticUser, updateUser] = useOptimistic(
  user || { profile: { name: '', email: '' } }, // 提供默认值
  (state, action) => ({
    ...state,
    profile: {
      ...(state.profile || {}), // 安全解构
      name: action.name
    }
  })
);

// 🔍 调试技巧：运行时类型检查
const safeUpdateFn = (state, action) => {
  if (!state || typeof state !== 'object') {
    console.error('❌ Invalid state:', state);
    return state || {};
  }
  
  if (!action || typeof action !== 'object') {
    console.error('❌ Invalid action:', action);
    return state;
  }
  
  return actualUpdateFn(state, action);
};`
    },
    {
      error: 'Maximum update depth exceeded',
      cause: 'updateFn中触发了无限循环的状态更新',
      solution: '检查updateFn的逻辑，确保没有递归调用或依赖循环',
      prevention: '保持updateFn纯净，不要在其中调用其他状态更新函数',
      code: `// ❌ 错误：在updateFn中触发其他状态更新
function ProblematicComponent() {
  const [count, setCount] = useState(0);
  const [optimisticData, addOptimistic] = useOptimistic(
    data,
    (state, action) => {
      setCount(prev => prev + 1); // ❌ 触发其他状态更新
      return { ...state, value: action.value };
    }
  );
}

// ✅ 正确：在effect中处理副作用
function CorrectComponent() {
  const [count, setCount] = useState(0);
  const [optimisticData, addOptimistic] = useOptimistic(
    data,
    (state, action) => {
      return { ...state, value: action.value, updatedAt: Date.now() };
    }
  );

  // 在effect中处理计数更新
  useEffect(() => {
    if (optimisticData.updatedAt !== data.updatedAt) {
      setCount(prev => prev + 1);
    }
  }, [optimisticData.updatedAt, data.updatedAt]);
}

// 🔍 调试技巧：updateFn调用追踪
const trackedUpdateFn = (state, action) => {
  const callStack = new Error().stack;
  console.log('📍 updateFn called from:', callStack);
  
  // 检测可能的循环调用
  if (window.updateFnCallCount > 100) {
    console.error('🚨 Possible infinite loop detected!');
    return state;
  }
  
  window.updateFnCallCount = (window.updateFnCallCount || 0) + 1;
  
  const result = originalUpdateFn(state, action);
  
  setTimeout(() => {
    window.updateFnCallCount = 0;
  }, 1000);
  
  return result;
};`
    },
    {
      error: 'Memory leak: Component unmounted but state still updating',
      cause: '组件卸载后异步操作仍在尝试更新状态',
      solution: '使用cleanup函数取消未完成的异步操作',
      prevention: '在useEffect的cleanup中取消所有pending请求',
      code: `// ❌ 错误：没有清理异步操作
function LeakyComponent() {
  const [optimisticData, addOptimistic] = useOptimistic(data, updateFn);

  const handleUpdate = async (newValue) => {
    addOptimistic({ value: newValue });
    
    try {
      await updateAPI(newValue); // 可能在组件卸载后完成
    } catch (error) {
      // 可能在组件卸载后执行
    }
  };
}

// ✅ 正确：使用AbortController清理
function SafeComponent() {
  const [optimisticData, addOptimistic] = useOptimistic(data, updateFn);
  const abortControllerRef = useRef();

  const handleUpdate = async (newValue) => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();
    
    addOptimistic({ value: newValue });
    
    try {
      await updateAPI(newValue, {
        signal: abortControllerRef.current.signal
      });
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Update failed:', error);
      }
    }
  };

  useEffect(() => {
    return () => {
      // 组件卸载时取消所有pending请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);
}

// 🔍 调试技巧：内存泄漏检测
function MemoryLeakDetector() {
  const mountedRef = useRef(true);
  
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const safeSetState = useCallback((updater) => {
    if (mountedRef.current) {
      setState(updater);
    } else {
      console.warn('🚨 Attempted to update state on unmounted component');
    }
  }, []);
}`
    }
  ],
  
  devToolsTips: [
    {
      tool: 'React DevTools Profiler',
      technique: '使用Profiler分析useOptimistic的性能影响',
      example: '1. 打开React DevTools > Profiler\n2. 点击录制按钮\n3. 触发乐观更新操作\n4. 停止录制查看组件渲染时间\n5. 查找性能瓶颈和不必要的重新渲染'
    },
    {
      tool: 'React DevTools Components',
      technique: '检查useOptimistic的Hook状态',
      example: '1. 选择使用useOptimistic的组件\n2. 在右侧面板查看Hooks状态\n3. 观察optimistic状态的变化\n4. 对比基础状态和乐观状态的差异'
    },
    {
      tool: 'Chrome DevTools Console',
      technique: '使用console.trace追踪函数调用栈',
      example: `// 在updateFn中添加调用栈追踪
const debugUpdateFn = (state, action) => {
  console.group('🔄 useOptimistic Update');
  console.log('State:', state);
  console.log('Action:', action);
  console.trace('Call stack');
  
  const result = actualUpdateFn(state, action);
  
  console.log('Result:', result);
  console.groupEnd();
  
  return result;
};`
    },
    {
      tool: 'Network Tab',
      technique: '监控异步请求和乐观更新的关联',
      example: '1. 打开Chrome DevTools > Network\n2. 触发乐观更新操作\n3. 观察对应的网络请求\n4. 检查请求时序和响应状态\n5. 验证失败请求是否正确触发回滚'
    }
  ]
};

export default debuggingTips;