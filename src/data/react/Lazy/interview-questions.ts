import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: 'React.lazy的基本原理是什么？它是如何实现组件的懒加载的？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'React.lazy通过动态导入和Suspense机制实现组件懒加载，将组件包装为LazyComponent对象，在渲染时才执行import()函数加载真正的组件。',
      detailed: `React.lazy的实现原理包含以下几个核心步骤：

**1. 组件包装过程**
React.lazy接收一个返回动态导入Promise的函数，创建一个特殊的LazyComponent对象：
- 设置$$typeof标记为REACT_LAZY_TYPE
- 将factory函数存储在_payload属性中
- 返回一个ExoticComponent类型的组件

**2. 延迟加载机制**
当React渲染这个懒加载组件时：
- 首次渲染时调用factory函数
- factory函数执行import()动态导入
- 返回的Promise会被"抛出"给最近的Suspense边界
- Suspense捕获Promise并显示fallback UI

**3. Promise状态管理**
React内部跟踪Promise的三种状态：
- pending：显示loading状态
- fulfilled：缓存组件并正常渲染
- rejected：触发Error Boundary

**4. 缓存优化**
组件成功加载后会被缓存，后续渲染直接使用缓存的组件，避免重复加载。

这种设计的核心优势是将代码分割与组件系统无缝集成，开发者只需要关心何时使用lazy，而不用处理复杂的异步加载逻辑。`,
      code: `// React.lazy的简化实现原理
function lazy(factory) {
  const lazyComponent = {
    $$typeof: REACT_LAZY_TYPE,
    _payload: factory,
    _init: function(payload) {
      // 在渲染时才执行
      if (payload._status === undefined) {
        payload._status = 'pending';
        const promise = payload._result = payload._factory();
        
        promise.then(
          moduleObject => {
            payload._status = 'resolved';
            payload._result = moduleObject.default;
          },
          error => {
            payload._status = 'rejected';
            payload._result = error;
          }
        );
      }
      
      if (payload._status === 'resolved') {
        return payload._result;
      } else {
        throw payload._result; // 抛出Promise或Error
      }
    }
  };
  
  return lazyComponent;
}

// 使用示例
const LazyComponent = React.lazy(() => import('./MyComponent'));

function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LazyComponent />
    </Suspense>
  );
}`
    },
    tags: ['React.lazy', '动态导入', 'Suspense', '代码分割']
  },

  {
    id: 2,
    question: 'React.lazy必须与Suspense配合使用，请解释这种设计的原因和内在机制',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: '因为懒加载组件在首次渲染时会抛出Promise，必须有Suspense边界来捕获这个Promise并管理异步加载状态，这是React异步渲染架构的核心设计。',
      detailed: `React.lazy与Suspense的耦合设计体现了React异步渲染的核心理念：

**1. Promise抛出机制**
当懒加载组件首次渲染时：
- React内部会调用lazy组件的_init方法
- 如果组件尚未加载完成，_init会抛出Promise而不是返回组件
- 这个Promise需要被某个上层组件捕获和处理

**2. Suspense的捕获职责**
Suspense组件的核心职责是：
- 捕获子组件树中抛出的Promise
- 在Promise pending期间显示fallback UI
- Promise resolve后重新触发子组件渲染
- 提供错误边界功能处理加载失败

**3. 架构设计优势**
这种设计带来了几个重要优势：
- **统一的异步处理模式**：所有异步组件(懒加载、数据获取等)都使用相同的模式
- **声明式异步UI**：开发者只需声明loading状态，不需要手动管理加载逻辑
- **并发渲染基础**：为React 18的并发特性奠定架构基础
- **错误边界集成**：Suspense可以与Error Boundary配合处理加载失败

**4. 内部协调机制**
React内部的协调过程：
- Render阶段：遇到lazy组件时检查加载状态
- Suspend阶段：如果未加载完成则中断渲染，找到最近的Suspense
- Resume阶段：Promise resolve后从Suspense位置恢复渲染

**5. 为什么不能单独使用**
如果没有Suspense边界：
- 抛出的Promise无法被捕获
- 会导致React渲染器崩溃
- 无法提供合适的loading状态反馈

这种设计虽然增加了使用复杂度，但提供了更加强大和统一的异步组件管理能力。`,
      code: `// Suspense捕获Promise的内部机制简化版
function Suspense({ children, fallback }) {
  try {
    // 尝试渲染子组件
    return renderChildren(children);
  } catch (promise) {
    if (typeof promise.then === 'function') {
      // 捕获到Promise，显示fallback
      promise.then(() => {
        // Promise resolve后重新渲染
        forceUpdate();
      });
      return fallback;
    } else {
      // 普通错误，继续抛出
      throw promise;
    }
  }
}

// 错误示例：没有Suspense边界
function BadExample() {
  const LazyComponent = React.lazy(() => import('./Component'));
  
  return (
    <div>
      {/* ❌ 这会导致错误：Suspense边界缺失 */}
      <LazyComponent />
    </div>
  );
}

// 正确示例：使用Suspense包装
function GoodExample() {
  const LazyComponent = React.lazy(() => import('./Component'));
  
  return (
    <Suspense fallback={<div>Loading...</div>}>
      {/* ✅ 正确：有Suspense边界保护 */}
      <LazyComponent />
    </Suspense>
  );
}`
    },
    tags: ['Suspense', '异步渲染', 'Promise', '错误处理']
  },

  {
    id: 3,
    question: '在实际项目中如何合理使用React.lazy进行性能优化？有哪些最佳实践？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '实战应用',
    answer: {
      brief: '合理使用React.lazy需要考虑代码分割粒度、预加载策略、错误处理和用户体验，关键是在性能优化和用户体验之间找到平衡点。',
      detailed: `React.lazy的实战应用需要考虑多个维度的优化策略：

**1. 代码分割粒度策略**
- **页面级分割**：适用于SPA的路由懒加载，效果最明显
- **功能模块分割**：按业务功能分割，如管理面板、报表模块
- **第三方库分割**：将大型第三方库(图表、编辑器等)单独分割
- **避免过度分割**：不要对小组件使用lazy，会增加网络请求开销

**2. 预加载优化策略**
- **鼠标悬停预加载**：用户悬停在链接上时预加载目标页面
- **视区预加载**：当用户滚动到某个区域时预加载相关组件
- **空闲时间预加载**：在浏览器空闲时预加载可能需要的组件
- **用户行为分析**：基于用户使用模式智能预加载

**3. 错误处理和降级策略**
- **Error Boundary配合**：处理懒加载失败的情况
- **重试机制**：提供重新加载失败组件的能力
- **降级方案**：准备轻量级的替代组件
- **离线支持**：缓存关键组件以支持离线访问

**4. 用户体验优化**
- **有意义的Loading状态**：提供具体的加载提示而非通用loading
- **骨架屏**：使用骨架屏减少加载时的视觉跳跃
- **渐进式加载**：优先加载关键内容，次要内容延后加载
- **加载进度提示**：对于大型组件提供加载进度反馈

**5. 性能监控和优化**
- **Bundle分析**：定期分析打包体积和分割效果
- **加载时间监控**：监控各个lazy组件的加载时间
- **缓存策略**：配置合适的浏览器缓存策略
- **CDN优化**：将分割后的chunk部署到CDN

**6. 开发和维护最佳实践**
- **组件命名规范**：为lazy组件建立清晰的命名规范
- **文档记录**：记录分割策略和加载依赖关系
- **自动化测试**：确保lazy组件的加载和渲染正确性
- **性能基准**：建立性能基准线，定期回归测试`,
      code: `// 实战中的React.lazy最佳实践示例

// 1. 路由级别的懒加载 - 最常见且效果最好
const HomePage = React.lazy(() => import('./pages/HomePage'));
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const UserProfile = React.lazy(() => import('./pages/UserProfile'));

// 2. 预加载策略实现
const ChartComponent = React.lazy(() => 
  // 在鼠标悬停时预加载
  import(/* webpackChunkName: "charts" */ './components/Charts')
);

function PreloadOnHover({ children, componentImport }) {
  const [isPreloaded, setIsPreloaded] = useState(false);
  
  const handleMouseEnter = () => {
    if (!isPreloaded) {
      componentImport();
      setIsPreloaded(true);
    }
  };
  
  return (
    <div onMouseEnter={handleMouseEnter}>
      {children}
    </div>
  );
}

// 3. 错误处理和重试机制
function LazyComponentWrapper({ children }) {
  const [retryCount, setRetryCount] = useState(0);
  
  const handleRetry = () => {
    setRetryCount(count => count + 1);
  };
  
  return (
    <ErrorBoundary
      onError={(error) => {
        console.error('Lazy component failed to load:', error);
      }}
      fallback={
        <div className="error-fallback">
          <p>组件加载失败</p>
          <button onClick={handleRetry}>
            重试 ({retryCount})
          </button>
        </div>
      }
      key={retryCount} // 重试时重新创建ErrorBoundary
    >
      <Suspense fallback={<ComponentSkeleton />}>
        {children}
      </Suspense>
    </ErrorBoundary>
  );
}

// 4. 智能预加载Hook
function useIntelligentPreload() {
  const [loadedComponents, setLoadedComponents] = useState(new Set());
  
  const preloadComponent = useCallback((importFn, componentName) => {
    if (!loadedComponents.has(componentName)) {
      importFn().then(() => {
        setLoadedComponents(prev => new Set([...prev, componentName]));
      });
    }
  }, [loadedComponents]);
  
  // 基于用户行为模式的智能预加载
  useEffect(() => {
    const userBehaviorData = getUserBehaviorAnalytics();
    const recommendedComponents = predictNextComponents(userBehaviorData);
    
    recommendedComponents.forEach(({ importFn, name }) => {
      preloadComponent(importFn, name);
    });
  }, [preloadComponent]);
  
  return { preloadComponent, loadedComponents };
}

// 5. 性能监控集成
function withLazyPerformanceTracking(LazyComponent, componentName) {
  return React.lazy(async () => {
    const startTime = performance.now();
    
    try {
      const module = await LazyComponent;
      const loadTime = performance.now() - startTime;
      
      // 发送性能数据到监控系统
      sendPerformanceMetric({
        componentName,
        loadTime,
        status: 'success'
      });
      
      return module;
    } catch (error) {
      const loadTime = performance.now() - startTime;
      
      sendPerformanceMetric({
        componentName,
        loadTime,
        status: 'error',
        error: error.message
      });
      
      throw error;
    }
  });
}`
    },
    tags: ['性能优化', '代码分割', '预加载', '错误处理', '用户体验']
  },

  {
    id: 4,
    question: 'React.lazy只支持default导出，如何处理命名导出的组件？有哪些解决方案？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '实战应用',
    answer: {
      brief: '可以通过中间模块重新导出、动态导入后解构、或创建包装组件等方式来处理命名导出，每种方案都有各自的适用场景和权衡考虑。',
      detailed: `React.lazy的默认导出限制是一个设计决策，但实际开发中经常需要处理命名导出的情况：

**1. 问题背景**
React.lazy只能处理默认导出(default export)，因为：
- 简化了内部实现逻辑，避免了复杂的模块解析
- 保持了API的简洁性和一致性
- 减少了类型推导的复杂度

但实际项目中经常遇到命名导出的情况：
- 第三方组件库的命名导出
- 团队代码规范要求使用命名导出
- 一个模块导出多个组件

**2. 解决方案对比**

**方案一：中间模块重新导出(推荐)**
优势：清晰明确，性能好，类型安全
劣势：需要额外的文件

**方案二：动态导入后解构**
优势：代码简洁，不需要额外文件
劣势：可能影响tree-shaking，类型推导复杂

**方案三：创建包装组件**
优势：灵活性高，可以添加额外逻辑
劣势：增加了组件层级，可能影响性能

**方案四：修改原始模块的导出方式**
优势：从根本上解决问题
劣势：不适用于第三方库，可能影响其他地方

**3. 最佳实践建议**
- **团队项目**：统一使用默认导出，从源头避免问题
- **第三方库**：使用中间模块重新导出
- **复杂场景**：创建专门的lazy wrapper utility
- **类型安全**：确保所有解决方案都保持TypeScript类型安全

**4. 性能考虑**
- 中间模块方案对打包体积影响最小
- 动态解构可能影响静态分析和tree-shaking
- 包装组件会增加运行时开销
- 选择方案时要权衡开发体验和性能影响

**5. 未来发展**
React团队可能在未来版本中支持命名导出，但目前还需要使用这些变通方案。`,
      code: `// 各种处理命名导出的解决方案

// 原始模块 - utils.js (命名导出)
export const UtilComponent = () => <div>Utility Component</div>;
export const HelperComponent = () => <div>Helper Component</div>;

// 方案一：中间模块重新导出 (推荐)
// utils-lazy.js
export { UtilComponent as default } from './utils';

// 使用
const LazyUtilComponent = React.lazy(() => import('./utils-lazy'));

// 方案二：动态导入后解构
const LazyUtilComponent = React.lazy(async () => {
  const module = await import('./utils');
  return { default: module.UtilComponent };
});

// 方案三：创建包装组件
const LazyUtilComponent = React.lazy(async () => {
  const { UtilComponent } = await import('./utils');
  
  // 可以在这里添加额外逻辑
  const WrappedComponent = (props) => {
    console.log('Lazy component loaded');
    return <UtilComponent {...props} />;
  };
  
  return { default: WrappedComponent };
});

// 方案四：通用的命名导出处理工具
function createLazyNamedExport(importFn, exportName) {
  return React.lazy(async () => {
    const module = await importFn();
    const Component = module[exportName];
    
    if (!Component) {
      throw new Error('Export "' + exportName + '" not found');
    }
    
    return { default: Component };
  });
}

// 使用通用工具
const LazyUtilComponent = createLazyNamedExport(
  () => import('./utils'),
  'UtilComponent'
);

// 方案五：批量处理多个命名导出
function createLazyComponents(importFn, componentNames) {
  const lazyComponents = {};
  
  componentNames.forEach(name => {
    lazyComponents[name] = React.lazy(async () => {
      const module = await importFn();
      return { default: module[name] };
    });
  });
  
  return lazyComponents;
}

// 使用批量处理
const LazyComponents = createLazyComponents(
  () => import('./utils'),
  ['UtilComponent', 'HelperComponent']
);

// 方案六：TypeScript类型安全的解决方案
type NamedExportLoader<T> = () => Promise<{ [K in keyof T]: React.ComponentType<any> }>;

function createTypedLazyComponent<T, K extends keyof T>(
  loader: NamedExportLoader<T>,
  exportName: K
): React.LazyExoticComponent<T[K] extends React.ComponentType<infer P> ? React.ComponentType<P> : never> {
  return React.lazy(async () => {
    const module = await loader();
    const Component = module[exportName];
    return { default: Component as any };
  });
}

// TypeScript使用示例
interface UtilModule {
  UtilComponent: React.ComponentType<{ title: string }>;
  HelperComponent: React.ComponentType<{ count: number }>;
}

const TypedLazyUtil = createTypedLazyComponent<UtilModule, 'UtilComponent'>(
  () => import('./utils'),
  'UtilComponent'
);

// 方案七：结合错误处理的完整解决方案
function createRobustLazyComponent(importFn, exportName, fallbackComponent = null) {
  return React.lazy(async () => {
    try {
      const module = await importFn();
      const Component = module[exportName] || module.default;
      
      if (!Component) {
        console.warn('Component "' + exportName + '" not found, using fallback');
        return { default: fallbackComponent || (() => <div>Component not found</div>) };
      }
      
      return { default: Component };
    } catch (error) {
      console.error('Failed to load component:', error);
      throw error;
    }
  });
}`
    },
    tags: ['命名导出', '默认导出', '模块系统', 'TypeScript', '代码组织']
  },

  {
    id: 5,
    question: '在生产环境中遇到懒加载组件加载失败的问题，应该如何排查和解决？',
    difficulty: 'hard',
    frequency: 'low',
    category: '问题排查',
    answer: {
      brief: '懒加载失败通常涉及网络问题、构建配置、缓存策略和错误处理等多个方面，需要系统性的排查方法和完善的监控体系来定位和解决问题。',
      detailed: `生产环境中的懒加载失败问题需要系统性的排查和解决策略：

**1. 常见失败原因分析**

**网络层面问题**：
- CDN节点故障或延迟过高
- 用户网络环境不稳定(弱网、高延迟)
- DNS解析问题导致资源无法访问
- 防火墙或代理服务器阻止了chunk文件下载

**构建和部署问题**：
- Webpack配置错误导致chunk路径不正确
- 静态资源服务器配置问题
- 版本更新时的缓存问题(旧版本引用新chunk)
- publicPath配置不匹配部署环境

**浏览器和兼容性问题**：
- 旧版本浏览器不支持动态导入
- 浏览器缓存策略冲突
- 内存不足导致模块加载失败
- JavaScript执行错误阻止了模块初始化

**2. 系统性排查方法**

**第一步：错误信息收集**
- 收集详细的错误堆栈信息
- 记录用户的网络环境和浏览器信息
- 分析错误发生的时间模式和频率
- 检查服务器日志中的404或500错误

**第二步：本地环境复现**
- 模拟不同的网络条件(慢网、断网)
- 测试不同浏览器和版本的兼容性
- 验证构建产物的完整性
- 检查chunk文件的可访问性

**第三步：生产环境验证**
- 检查CDN和静态资源服务器状态
- 验证缓存策略的正确性
- 分析用户地理分布和错误分布的关联性
- 检查负载均衡和容错机制

**3. 解决方案和防护措施**

**错误恢复机制**：
- 实现重试逻辑，支持指数退避
- 提供降级组件作为备选方案
- 建立错误边界捕获和恢复机制
- 实现优雅的用户提示和引导

**网络优化策略**：
- 配置多个CDN节点做容灾
- 实现预加载和缓存策略
- 优化chunk大小和分割策略
- 添加网络状态检测和适配

**监控和告警系统**：
- 建立实时的错误监控和告警
- 跟踪关键指标(加载成功率、加载时间等)
- 实现用户行为和错误关联分析
- 定期进行性能基准测试

**4. 长期优化策略**

**架构层面改进**：
- 考虑Service Worker缓存策略
- 实现模块联邦(Module Federation)
- 建立微前端架构减少单点故障
- 优化构建流程和部署策略

**用户体验优化**：
- 提供离线模式支持
- 实现渐进式加载策略
- 优化loading状态和错误提示
- 建立用户反馈和问题报告机制

**5. 预防性措施**

**开发阶段**：
- 建立完善的测试覆盖率
- 实现自动化的构建验证
- 建立性能基准和回归测试
- 制定代码审查标准

**部署阶段**：
- 实现蓝绿部署减少风险
- 建立回滚机制和应急预案
- 实现灰度发布验证新版本
- 监控关键指标确保稳定性`,
      code: `// 生产环境懒加载失败的完整解决方案

// 1. 带重试机制的懒加载工具
function createResilientLazyComponent(importFn, options = {}) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    fallbackComponent = null,
    onError = null
  } = options;
  
  return React.lazy(async () => {
    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const module = await importFn();
        
        // 记录成功加载
        console.log('Component loaded successfully on attempt:', attempt + 1);
        return module;
        
      } catch (error) {
        lastError = error;
        
        // 记录失败信息
        const errorInfo = {
          attempt: attempt + 1,
          maxRetries: maxRetries + 1,
          error: error.message,
          stack: error.stack,
          userAgent: navigator.userAgent,
          connection: navigator.connection?.effectiveType || 'unknown',
          timestamp: new Date().toISOString()
        };
        
        console.error('Component load failed:', errorInfo);
        
        // 发送错误监控数据
        if (onError) {
          onError(errorInfo);
        }
        
        // 如果不是最后一次尝试，等待后重试
        if (attempt < maxRetries) {
          await new Promise(resolve => 
            setTimeout(resolve, retryDelay * Math.pow(2, attempt))
          );
        }
      }
    }
    
    // 所有重试都失败，使用降级方案
    if (fallbackComponent) {
      console.warn('Using fallback component after all retries failed');
      return { default: fallbackComponent };
    }
    
    throw lastError;
  });
}

// 2. 网络状态感知的加载策略
function useNetworkAwareLazyLoading() {
  const [networkState, setNetworkState] = useState({
    online: navigator.onLine,
    effectiveType: navigator.connection?.effectiveType || 'unknown',
    downlink: navigator.connection?.downlink || 0
  });
  
  useEffect(() => {
    const updateNetworkState = () => {
      setNetworkState({
        online: navigator.onLine,
        effectiveType: navigator.connection?.effectiveType || 'unknown',
        downlink: navigator.connection?.downlink || 0
      });
    };
    
    window.addEventListener('online', updateNetworkState);
    window.addEventListener('offline', updateNetworkState);
    
    if (navigator.connection) {
      navigator.connection.addEventListener('change', updateNetworkState);
    }
    
    return () => {
      window.removeEventListener('online', updateNetworkState);
      window.removeEventListener('offline', updateNetworkState);
      if (navigator.connection) {
        navigator.connection.removeEventListener('change', updateNetworkState);
      }
    };
  }, []);
  
  const getLoadingStrategy = useCallback(() => {
    if (!networkState.online) {
      return { timeout: 0, retries: 0 }; // 离线时不加载
    }
    
    switch (networkState.effectiveType) {
      case 'slow-2g':
      case '2g':
        return { timeout: 30000, retries: 5 };
      case '3g':
        return { timeout: 15000, retries: 3 };
      case '4g':
      default:
        return { timeout: 10000, retries: 2 };
    }
  }, [networkState]);
  
  return { networkState, getLoadingStrategy };
}

// 3. 错误监控和数据收集
class LazyLoadingMonitor {
  constructor(config = {}) {
    this.config = {
      endpoint: '/api/errors',
      batchSize: 10,
      flushInterval: 5000,
      ...config
    };
    
    this.errorQueue = [];
    this.setupPeriodicFlush();
  }
  
  reportError(errorInfo) {
    this.errorQueue.push({
      ...errorInfo,
      url: window.location.href,
      timestamp: Date.now(),
      sessionId: this.getSessionId()
    });
    
    if (this.errorQueue.length >= this.config.batchSize) {
      this.flush();
    }
  }
  
  flush() {
    if (this.errorQueue.length === 0) return;
    
    const errors = this.errorQueue.splice(0);
    
    fetch(this.config.endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ errors })
    }).catch(error => {
      console.error('Failed to send error reports:', error);
      // 错误发送失败时重新加入队列
      this.errorQueue.unshift(...errors);
    });
  }
  
  setupPeriodicFlush() {
    setInterval(() => this.flush(), this.config.flushInterval);
    
    // 页面卸载时发送剩余错误
    window.addEventListener('beforeunload', () => this.flush());
  }
  
  getSessionId() {
    if (!this.sessionId) {
      this.sessionId = Math.random().toString(36).substr(2, 9);
    }
    return this.sessionId;
  }
}

// 4. 使用示例
const monitor = new LazyLoadingMonitor({
  endpoint: 'https://api.example.com/lazy-loading-errors'
});

const LazyDashboard = createResilientLazyComponent(
  () => import('./Dashboard'),
  {
    maxRetries: 3,
    retryDelay: 1000,
    onError: (errorInfo) => monitor.reportError(errorInfo),
    fallbackComponent: () => (
      <div className="error-fallback">
        <h3>暂时无法加载仪表板</h3>
        <p>请检查网络连接或稍后重试</p>
        <button onClick={() => window.location.reload()}>
          刷新页面
        </button>
      </div>
    )
  }
);

// 5. 生产环境使用
function ProductionApp() {
  const { networkState, getLoadingStrategy } = useNetworkAwareLazyLoading();
  const strategy = getLoadingStrategy();
  
  return (
    <div>
      {!networkState.online && (
        <div className="offline-banner">
          您当前处于离线状态，部分功能可能无法使用
        </div>
      )}
      
      <ErrorBoundary
        fallback={({ error, retry }) => (
          <div className="global-error-fallback">
            <h2>应用出现了问题</h2>
            <details>
              <summary>错误详情</summary>
              <pre>{error.message}</pre>
            </details>
            <button onClick={retry}>重试</button>
          </div>
        )}
      >
        <Suspense 
          fallback={
            <div className="loading-indicator">
              <span>加载中...</span>
              {networkState.effectiveType === '2g' && (
                <small>网络较慢，请耐心等待</small>
              )}
            </div>
          }
        >
          <LazyDashboard />
        </Suspense>
      </ErrorBoundary>
    </div>
  );
}`
    },
    tags: ['问题排查', '错误处理', '网络优化', '监控', '生产环境']
  }
];

export default interviewQuestions;