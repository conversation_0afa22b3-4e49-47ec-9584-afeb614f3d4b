import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "React.lazy是React中用于代码分割和懒加载组件的API，支持动态导入组件以优化应用性能",

  introduction: `React.lazy是React 16.6版本引入的代码分割解决方案，用于实现组件级别的懒加载和动态导入。它采用ES6动态导入和Promise机制，提供了声明式的代码分割能力。

在React生态中，它是性能优化层的核心工具，常见于大型应用、路由懒加载和按需组件加载，特别适合需要减少初始包体积和优化首屏加载时间的场景。

核心优势包括减少打包体积、提升首屏性能和支持按需加载，但也需要注意必须与Suspense配合使用和处理加载状态的复杂性。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react/index.d.ts:1020
 * - 实现文件：packages/react/src/Lazy.js
 */

// 基础语法
function lazy<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>
): LazyExoticComponent<T>;

// 完整用法
const LazyComponent = React.lazy(() => import('./MyComponent'));

// 与Suspense配合使用
function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LazyComponent />
    </Suspense>
  );
}

// TypeScript完整语法
interface LazyExoticComponent<T extends ComponentType<any>>
  extends ExoticComponent<ComponentPropsWithRef<T>> {
  readonly _result: T;
}`,

  quickExample: `function ReactLazyExample() {
  // 创建懒加载组件 - 只有在需要时才会加载
  const LazyDialog = React.lazy(() => import('./Dialog'));
  const [showDialog, setShowDialog] = useState(false);

  return (
    <div>
      <h2>React.lazy 懒加载示例</h2>
      <button onClick={() => setShowDialog(true)}>
        打开对话框
      </button>
      
      {showDialog && (
        <Suspense fallback={<div>正在加载对话框...</div>}>
          {/* 只有点击按钮时才会真正加载Dialog组件 */}
          <LazyDialog 
            onClose={() => setShowDialog(false)}
            title="懒加载的对话框"
          />
        </Suspense>
      )}
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "React.lazy在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用代码分割",
      diagram: `graph LR
        A[React.lazy核心场景] --> B[路由懒加载]
        A --> C[条件组件加载]
        A --> D[大型组件优化]

        B --> B1["🌐 页面级路由<br/>减少初始包体积"]
        B --> B2["📱 Tab切换<br/>按需加载内容"]

        C --> C1["🎭 弹窗组件<br/>点击时才加载"]
        C --> C2["🔧 管理面板<br/>权限控制加载"]

        D --> D1["📊 图表库<br/>数据可视化组件"]
        D --> D2["📝 富文本编辑器<br/>编辑功能组件"]

        style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
        style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
        style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
        style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "技术实现架构",
      description: "React.lazy的技术实现架构，展示其核心机制和与其他技术的集成关系",
      diagram: `graph TB
        A[React.lazy技术架构] --> B[动态导入层]
        A --> C[Promise处理层]
        A --> D[Suspense集成层]

        B --> B1["📦 ES6 import()<br/>动态模块加载"]
        B --> B2["🔄 Webpack代码分割<br/>自动bundle分离"]

        C --> C1["⏳ Promise管理<br/>加载状态追踪"]
        C --> C2["❌ 错误边界<br/>加载失败处理"]

        D --> D1["🎪 Suspense边界<br/>fallback UI显示"]
        D --> D2["🔄 组件状态管理<br/>加载完成渲染"]

        style A fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
        style B fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
        style C fill:#fce4ec,stroke:#c2185b,stroke-width:2px
        style D fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px`
    },
    {
      title: "性能优化生态",
      description: "React.lazy在性能优化技术生态系统中的位置和与其他优化技术的协作关系",
      diagram: `graph TD
        A[性能优化生态系统] --> B[打包优化]
        A --> C[运行时优化]
        A --> D[用户体验优化]

        B --> B1["Webpack<br/>代码分割"]
        B --> B2["Tree Shaking<br/>无用代码消除"]
        B --> B3["React.lazy<br/>组件懒加载"]
        B --> B4["动态导入<br/>模块按需加载"]

        C --> C1["React.memo<br/>组件缓存"]
        C --> C2["Suspense<br/>异步边界"]

        D --> D1["Loading UI<br/>用户感知优化"]
        D --> D2["Progressive Loading<br/>渐进式加载"]

        B3 -.-> B1
        B3 -.-> C2
        B3 -.-> D1
        B3 -.-> D2

        style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
        style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
        style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
        style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
        style B3 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
    }
  ],
  
  parameters: [
    {
      name: "factory",
      type: "() => Promise<{ default: T }>",
      required: true,
      description: "返回动态导入Promise的工厂函数，该Promise必须resolve为包含default导出的模块",
      example: "() => import('./MyComponent')"
    }
  ],
  
  returnValue: {
    type: "LazyExoticComponent<T>",
    description: "返回懒加载的React组件，具有特殊的类型标记，必须在Suspense边界内使用",
    example: "const LazyComponent = React.lazy(() => import('./Component'))"
  },
  
  keyFeatures: [
    {
      title: "代码自动分割",
      description: "与现代打包工具（Webpack、Vite等）深度集成，自动将懒加载组件分离为独立bundle",
      benefit: "显著减少初始包体积，提升首屏加载速度"
    },
    {
      title: "声明式懒加载", 
      description: "提供简洁的API，将复杂的动态导入和加载状态管理封装为声明式语法",
      benefit: "简化代码分割实现，提高开发效率"
    },
    {
      title: "Suspense深度集成",
      description: "与React.Suspense无缝配合，提供统一的异步组件加载机制",
      benefit: "统一的加载状态管理和错误边界处理"
    },
    {
      title: "TypeScript完美支持",
      description: "提供完整的类型推导和类型检查，保持组件props的类型安全",
      benefit: "开发时类型安全和智能提示支持"
    }
  ],
  
  limitations: [
    "只能用于默认导出的组件，不支持命名导出",
    "必须与React.Suspense配合使用，增加组件层级",
    "网络加载失败时需要额外的错误处理机制",
    "初次加载时会有网络延迟，影响用户体验",
    "不适合频繁切换显示的小组件"
  ],
  
  bestPractices: [
    "为懒加载组件提供有意义的loading状态和错误边界",
    "合理拆分组件粒度，避免过度懒加载影响用户体验",
    "使用React.memo包装懒加载组件避免不必要的重新加载",
    "在用户操作前预加载关键组件（如鼠标悬停时预加载）",
    "为懒加载失败提供重试机制和降级方案",
    "配合路由使用时注意路由切换的用户体验",
    "在开发环境中监控代码分割效果和包大小变化",
    "避免在组件内部创建lazy组件，应在模块顶层定义"
  ],
  
  warnings: [
    "factory函数每次调用都会创建新的Promise，避免在渲染函数内调用",
    "动态导入的模块必须包含default导出，否则会加载失败",
    "在SSR环境中需要特殊处理，避免服务端渲染错误",
    "过度使用可能导致用户频繁看到loading状态，影响体验",
    "网络环境不佳时要考虑加载失败的用户体验"
  ]
};

export default basicInfo;