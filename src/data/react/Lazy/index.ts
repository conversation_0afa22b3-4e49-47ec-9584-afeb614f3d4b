import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ReactLazyData: ApiItem = {
  id: 'Lazy',
  title: 'React.lazy',
  description: 'React.lazy是React中用于代码分割和懒加载组件的API，支持动态导入组件以优化应用性能',
  category: 'React APIs',
  difficulty: 'medium',
  
  syntax: `function lazy<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>
): LazyExoticComponent<T>;

const LazyComponent = React.lazy(() => import('./MyComponent'));`,
  example: `function ReactLazyExample() {
  // 创建懒加载组件 - 只有在需要时才会加载
  const LazyDialog = React.lazy(() => import('./Dialog'));
  const [showDialog, setShowDialog] = useState(false);

  return (
    <div>
      <button onClick={() => setShowDialog(true)}>
        打开对话框
      </button>
      
      {showDialog && (
        <Suspense fallback={<div>正在加载对话框...</div>}>
          <LazyDialog 
            onClose={() => setShowDialog(false)}
            title="懒加载的对话框"
          />
        </Suspense>
      )}
    </div>
  );
}`,
  notes: '只能用于默认导出的组件，不支持命名导出',
  
  version: 'React 16.6.0+',
  tags: ["React", "API", "懒加载", "代码分割", "性能优化"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ReactLazyData;