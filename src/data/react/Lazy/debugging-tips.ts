import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'React.lazy在实际开发中可能遇到的常见问题和解决方案，帮助开发者快速定位和解决懒加载相关的错误。',
        sections: [
          {
            title: 'Suspense边界相关问题',
            description: '最常见的React.lazy错误都与Suspense边界的设置和使用有关',
            items: [
              {
                title: 'Suspense边界缺失错误',
                description: '懒加载组件没有被Suspense包装，导致Promise无法被正确捕获',
                solution: '确保所有懒加载组件都在Suspense边界内，检查组件树结构',
                prevention: '建立组件创建规范，使用ESLint规则检查Suspense使用',
                code: `// ❌ 错误：缺少Suspense边界
const LazyComponent = React.lazy(() => import('./Component'));
function App() {
  return <LazyComponent />; // 抛出错误
}

// ✅ 正确：添加Suspense边界
function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LazyComponent />
    </Suspense>
  );
}`
              },
              {
                title: '嵌套Suspense边界混乱',
                description: '多层嵌套的Suspense边界导致fallback显示不正确或加载状态混乱',
                solution: '合理规划Suspense边界的层级，避免不必要的嵌套',
                prevention: '绘制组件树和Suspense边界的关系图，明确每个边界的职责',
                code: `// 问题：过度嵌套的Suspense
function App() {
  return (
    <Suspense fallback={<div>App Loading...</div>}>
      <Header />
      <Suspense fallback={<div>Content Loading...</div>}>
        <LazyContent />
        <Suspense fallback={<div>Sidebar Loading...</div>}>
          <LazySidebar />
        </Suspense>
      </Suspense>
    </Suspense>
  );
}

// 优化：合理的Suspense层级
function App() {
  return (
    <div>
      <Header />
      <Suspense fallback={<PageLoadingSkeleton />}>
        <MainContent />
      </Suspense>
    </div>
  );
}`
              }
            ]
          },
          {
            title: '模块导入和路径问题',
            description: '动态导入的模块路径、导出方式等问题是常见的错误来源',
            items: [
              {
                title: '模块路径解析失败',
                description: '动态导入的路径不正确，导致模块加载失败',
                solution: '检查文件路径、webpack配置和模块解析规则',
                prevention: '使用TypeScript和绝对路径导入，配置路径映射',
                code: `// 检查模块是否正确导出
console.log(import('./Component')); // 检查Promise是否正确解析

// 添加错误处理
const LazyComponent = React.lazy(() => 
  import('./Component').catch(error => {
    console.error('Component import failed:', error);
    throw error;
  })
);`
              },
              {
                title: '命名导出vs默认导出',
                description: 'React.lazy只支持默认导出，使用命名导出会导致加载失败',
                solution: '确保目标组件使用默认导出，或创建包装模块',
                prevention: '团队统一使用默认导出，或建立命名导出的处理规范',
                code: `// ❌ 错误：使用命名导出
export const MyComponent = () => <div>Hello</div>;

// ✅ 正确：使用默认导出
const MyComponent = () => <div>Hello</div>;
export default MyComponent;

// 或者创建包装模块
// MyComponent.lazy.js
export { MyComponent as default } from './MyComponent';`
              }
            ]
          },
          {
            title: '网络和加载相关问题',
            description: '网络环境、CDN配置等外部因素导致的加载问题',
            items: [
              {
                title: '网络请求失败',
                description: '弱网环境或服务器问题导致chunk文件加载失败',
                solution: '实现重试机制和错误降级，检查网络配置',
                prevention: '配置CDN、实现离线缓存、监控加载成功率',
                code: `// 带重试的懒加载
function createRetryableLazy(importFn, retries = 3) {
  return React.lazy(async () => {
    for (let i = 0; i < retries; i++) {
      try {
        return await importFn();
      } catch (error) {
        if (i === retries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  });
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '使用各种开发工具来调试和监控React.lazy的行为，提高开发效率和问题排查能力。',
        sections: [
          {
            title: 'React DevTools调试',
            description: '使用React DevTools来观察和调试懒加载组件的行为',
            items: [
              {
                title: 'Profiler性能分析',
                description: '使用React DevTools的Profiler来分析懒加载组件的加载性能',
                solution: '启用Profiler，记录加载过程，分析性能瓶颈',
                prevention: '定期进行性能基准测试，建立性能监控体系',
                code: `// 在组件中添加性能标记
function App() {
  return (
    <Profiler id="LazyComponent" onRender={onRenderCallback}>
      <Suspense fallback={<div>Loading...</div>}>
        <LazyComponent />
      </Suspense>
    </Profiler>
  );
}

function onRenderCallback(id, phase, actualDuration) {
  console.log('Component:', id, 'Phase:', phase, 'Duration:', actualDuration);
}`
              },
              {
                title: 'Suspense状态观察',
                description: '在DevTools中观察Suspense组件的状态变化和fallback渲染',
                solution: '使用Components面板查看Suspense组件的props和state',
                prevention: '为Suspense组件添加有意义的displayName便于识别',
                code: `// 为调试添加标识
const DebugSuspense = ({ children, fallback, name }) => {
  const SuspenseWithName = React.Suspense;
  SuspenseWithName.displayName = 'DebugSuspense' + (name ? '(' + name + ')' : '');
  
  return (
    <SuspenseWithName fallback={fallback}>
      {children}
    </SuspenseWithName>
  );
};`
              }
            ]
          },
          {
            title: '浏览器开发者工具',
            description: '使用浏览器内置的开发者工具来调试网络请求和JavaScript执行',
            items: [
              {
                title: 'Network面板监控',
                description: '使用Network面板监控chunk文件的加载情况',
                solution: '查看chunk文件的加载时间、大小和失败原因',
                prevention: '定期检查chunk文件的大小和加载性能',
                code: `// 添加网络监控
window.addEventListener('online', () => {
  console.log('Network back online');
});

window.addEventListener('offline', () => {
  console.log('Network went offline');
});

// 监控chunk加载
if ('connection' in navigator) {
  console.log('Connection type:', navigator.connection.effectiveType);
  console.log('Downlink speed:', navigator.connection.downlink);
}`
              },
              {
                title: 'Console错误调试',
                description: '使用Console面板来查看和分析懒加载相关的错误信息',
                solution: '设置断点、查看错误堆栈、分析错误上下文',
                prevention: '实现全局错误处理和日志记录系统',
                code: `// 全局错误处理
window.addEventListener('error', (event) => {
  if (event.filename.includes('chunk')) {
    console.error('Chunk loading error:', event.error);
  }
});

// Promise错误处理
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});`
              }
            ]
          },
          {
            title: '自定义调试工具',
            description: '创建专门的调试工具来监控和分析懒加载行为',
            items: [
              {
                title: '懒加载监控器',
                description: '创建自定义的监控工具来跟踪所有懒加载组件的状态',
                solution: '实现组件加载状态的集中监控和可视化',
                prevention: '集成到CI/CD流程中，自动化性能回归测试',
                code: `// 懒加载监控器
class LazyLoadMonitor {
  constructor() {
    this.components = new Map();
    this.startTime = performance.now();
  }
  
  registerComponent(name, promise) {
    const startTime = performance.now();
    this.components.set(name, { startTime, status: 'loading' });
    
    promise
      .then(() => {
        const endTime = performance.now();
        this.components.set(name, {
          startTime,
          endTime,
          duration: endTime - startTime,
          status: 'loaded'
        });
        console.log('Component ' + name + ' loaded in ' + (endTime - startTime) + 'ms');
      })
      .catch(error => {
        this.components.set(name, {
          startTime,
          error,
          status: 'failed'
        });
        console.error('Component ' + name + ' failed to load:', error);
      });
  }
  
  getStats() {
    const stats = {
      total: this.components.size,
      loaded: 0,
      failed: 0,
      loading: 0,
      averageLoadTime: 0
    };
    
    let totalLoadTime = 0;
    let loadedCount = 0;
    
    for (const [name, data] of this.components) {
      stats[data.status]++;
      if (data.status === 'loaded') {
        totalLoadTime += data.duration;
        loadedCount++;
      }
    }
    
    stats.averageLoadTime = loadedCount > 0 ? totalLoadTime / loadedCount : 0;
    
    return stats;
  }
}

const monitor = new LazyLoadMonitor();

// 使用监控器
const LazyComponent = React.lazy(() => {
  const promise = import('./Component');
  monitor.registerComponent('MyComponent', promise);
  return promise;
});`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;