import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 'basic-routing',
    title: '基础路由懒加载',
    description: '在单页应用中实现页面级组件的懒加载，减少初始包体积，提升首屏加载速度',
    businessValue: '将应用打包体积减少60%，首屏加载时间提升40%，用户体验显著改善',
    scenario: `某电商网站有首页、商品列表、商品详情、用户中心等多个页面。初始加载时只需要首页内容，其他页面可以按需加载，避免一次性加载所有代码导致首屏缓慢。

技术需求：使用React Router配合React.lazy实现页面级代码分割，每个路由对应一个独立的bundle，用户访问时才加载对应页面代码。`,
    code: `import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

// 使用React.lazy创建懒加载页面组件
const HomePage = React.lazy(() => import('./pages/HomePage'));
const ProductList = React.lazy(() => import('./pages/ProductList'));
const ProductDetail = React.lazy(() => import('./pages/ProductDetail'));
const UserCenter = React.lazy(() => import('./pages/UserCenter'));

// 全局Loading组件
const PageLoading = () => (
  <div className="flex justify-center items-center h-64">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    <span className="ml-3 text-gray-600">页面加载中...</span>
  </div>
);

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        {/* 导航栏 - 始终可见 */}
        <nav className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <h1 className="text-xl font-semibold">电商网站</h1>
              </div>
            </div>
          </div>
        </nav>

        {/* 页面内容区域 - 使用Suspense包装懒加载路由 */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <Suspense fallback={<PageLoading />}>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/products" element={<ProductList />} />
              <Route path="/products/:id" element={<ProductDetail />} />
              <Route path="/user" element={<UserCenter />} />
            </Routes>
          </Suspense>
        </main>
      </div>
    </Router>
  );
}

export default App;`,
    explanation: `这个示例展示了React.lazy在路由懒加载中的典型应用。通过将每个页面组件包装为懒加载组件，实现了按需加载：

1. **代码分割**：每个页面组件被打包到独立的chunk中
2. **按需加载**：只有用户访问对应路由时才加载页面代码
3. **加载状态**：使用Suspense提供统一的加载状态反馈
4. **用户体验**：首屏只加载必要代码，提升加载速度

打包工具会自动将懒加载的组件分离成独立文件，如HomePage.chunk.js、ProductList.chunk.js等。`,
    benefits: [
      '初始包体积减少60-80%，显著提升首屏加载速度',
      '用户按需获取页面内容，减少不必要的网络传输',
      '浏览器缓存更高效，页面更新时只需重新下载变更的chunk',
      '开发和部署更灵活，可以独立更新单个页面'
    ],
    metrics: {
      performance: '首屏加载时间从3.2s降低到1.8s，FCP提升44%',
      userExperience: '页面切换响应时间<200ms，用户满意度提升35%',
      technicalMetrics: '初始bundle大小从800KB降低到320KB，代码分割率75%'
    },
    difficulty: 'easy',
    tags: ['路由', '代码分割', 'SPA', '性能优化']
  },

  {
    id: 'conditional-loading',
    title: '条件组件懒加载系统',
    description: '基于用户权限和操作行为的智能组件加载系统，实现精准的按需加载和资源优化',
    businessValue: '管理后台系统中，根据用户权限动态加载功能模块，减少40%的无效资源加载，提升系统响应速度',
    scenario: `企业管理后台包含多个功能模块：数据统计、用户管理、财务报表、系统设置等。不同角色用户只能访问特定模块，且某些重型组件（如图表、编辑器）只在用户主动触发时才需要加载。

技术挑战：需要结合权限控制、用户交互和组件懒加载，实现智能的资源管理系统。既要保证权限安全，又要优化加载性能。`,
    code: `import React, { Suspense, useState, useEffect } from 'react';

import { hasPermission } from './utils/permissions';

// 懒加载的功能模块组件
const DataDashboard = React.lazy(() => import('./modules/DataDashboard'));
const UserManagement = React.lazy(() => import('./modules/UserManagement'));
const FinanceReports = React.lazy(() => import('./modules/FinanceReports'));
const SystemSettings = React.lazy(() => import('./modules/SystemSettings'));

// 重型组件懒加载
const ChartEditor = React.lazy(() => import('./components/ChartEditor'));
const RichTextEditor = React.lazy(() => import('./components/RichTextEditor'));

// 组件加载状态管理
const ModuleLoading = ({ moduleName }) => (
  <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
    <div className="animate-pulse flex flex-col items-center">
      <div className="w-16 h-16 bg-gray-300 rounded-full mb-4"></div>
      <div className="h-4 bg-gray-300 rounded w-32 mb-2"></div>
      <div className="h-3 bg-gray-200 rounded w-24"></div>
    </div>
    <p className="mt-4 text-sm text-gray-600">正在加载{moduleName}模块...</p>
  </div>
);

function AdminDashboard() {
  const { user, loading } = useUser();
  const [activeModule, setActiveModule] = useState('dashboard');
  const [showChartEditor, setShowChartEditor] = useState(false);
  const [showRichEditor, setShowRichEditor] = useState(false);

  // 权限验证和模块配置
  const moduleConfig = [
    {
      id: 'dashboard',
      name: '数据统计',
      component: DataDashboard,
      permission: 'view_dashboard',
      icon: '📊'
    },
    {
      id: 'users',
      name: '用户管理',
      component: UserManagement,
      permission: 'manage_users',
      icon: '👥'
    },
    {
      id: 'finance',
      name: '财务报表',
      component: FinanceReports,
      permission: 'view_finance',
      icon: '💰'
    },
    {
      id: 'settings',
      name: '系统设置',
      component: SystemSettings,
      permission: 'system_admin',
      icon: '⚙️'
    }
  ];

  // 获取用户有权限的模块
  const availableModules = moduleConfig.filter(module =>
    hasPermission(user, module.permission)
  );

  // 预加载即将访问的模块
  const preloadModule = (moduleId) => {
    const module = moduleConfig.find(m => m.id === moduleId);
    if (module && hasPermission(user, module.permission)) {
      // 触发模块预加载
      import('./modules/' + module.component.name);
    }
  };

  if (loading) {
    return <div className="flex justify-center items-center h-screen">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
    </div>;
  }

  const ActiveComponent = availableModules.find(m => m.id === activeModule)?.component;

  return (
    <div className="flex h-screen bg-gray-100">
      {/* 侧边导航 */}
      <aside className="w-64 bg-white shadow-md">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-gray-800">管理后台</h2>
        </div>
        <nav className="mt-6">
          {availableModules.map((module) => (
            <button
              key={module.id}
              onClick={() => setActiveModule(module.id)}
              onMouseEnter={() => preloadModule(module.id)}
              className={'w-full flex items-center px-6 py-3 text-left hover:bg-gray-50 ' +
                (activeModule === module.id ? 'bg-blue-50 border-r-2 border-blue-500' : '')}
            >
              <span className="mr-3">{module.icon}</span>
              <span className={activeModule === module.id ? 'text-blue-600 font-medium' : 'text-gray-700'}>
                {module.name}
              </span>
            </button>
          ))}
        </nav>
      </aside>

      {/* 主内容区域 */}
      <main className="flex-1 overflow-auto">
        <div className="p-8">
          {/* 工具栏 */}
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">
              {availableModules.find(m => m.id === activeModule)?.name}
            </h1>
            <div className="flex space-x-3">
              {hasPermission(user, 'create_charts') && (
                <button
                  onClick={() => setShowChartEditor(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  📈 创建图表
                </button>
              )}
              {hasPermission(user, 'edit_content') && (
                <button
                  onClick={() => setShowRichEditor(true)}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  📝 富文本编辑
                </button>
              )}
            </div>
          </div>

          {/* 模块内容 */}
          <div className="bg-white rounded-lg shadow">
            <Suspense fallback={<ModuleLoading moduleName={availableModules.find(m => m.id === activeModule)?.name} />}>
              {ActiveComponent && <ActiveComponent />}
            </Suspense>
          </div>

          {/* 条件加载的重型组件 */}
          {showChartEditor && (
            <Suspense fallback={<ModuleLoading moduleName="图表编辑器" />}>
              <ChartEditor onClose={() => setShowChartEditor(false)} />
            </Suspense>
          )}

          {showRichEditor && (
            <Suspense fallback={<ModuleLoading moduleName="富文本编辑器" />}>
              <RichTextEditor onClose={() => setShowRichEditor(false)} />
            </Suspense>
          )}
        </div>
      </main>
    </div>
  );
}

export default AdminDashboard;`,
    explanation: `这个高级示例展示了React.lazy在复杂业务场景中的应用，结合了权限控制、智能预加载和条件渲染：

**核心技术特性：**
1. **权限驱动加载**：根据用户角色动态确定可加载的模块
2. **智能预加载**：鼠标悬停时预加载模块，提升用户体验
3. **条件组件加载**：重型组件只在用户主动触发时加载
4. **优雅降级**：提供详细的加载状态和错误处理

**业务价值体现：**
- 不同权限用户只加载必要模块，节省带宽和提升安全性
- 重型组件按需加载，避免影响主界面响应速度
- 预加载策略平衡了性能和用户体验`,
    benefits: [
      '根据用户权限精准加载，减少40%的无效资源请求',
      '重型组件延迟加载，主界面响应速度提升60%',
      '智能预加载机制，模块切换延迟降低到100ms以内',
      '权限和性能双重优化，系统安全性和用户体验并重'
    ],
    metrics: {
      performance: '模块切换时间从800ms降低到150ms，响应速度提升81%',
      userExperience: '页面交互流畅度评分从7.2提升到9.1（满分10分）',
      technicalMetrics: '权限验证准确率100%，无效资源加载减少43%'
    },
    difficulty: 'medium',
    tags: ['权限控制', '条件加载', '预加载', '企业应用']
  },

  {
    id: 'enterprise-optimization',
    title: '大型企业应用性能优化系统',
    description: '面向大型企业应用的全面性能优化方案，涵盖智能分割、缓存策略、错误恢复和监控体系',
    businessValue: '支撑10万+用户的企业级应用，页面加载速度提升70%，系统稳定性达99.9%，运维成本降低50%',
    scenario: `某大型企业的统一办公平台，包含100+个功能模块，服务10万+员工。系统面临的挑战：
- 初始包体积过大（15MB+），首屏加载缓慢
- 功能模块众多，不同部门使用不同模块组合
- 网络环境复杂，需要支持弱网和离线场景
- 需要实时监控和自动错误恢复机制

技术目标：构建智能化的代码分割和加载系统，支持预测性加载、渐进式更新、错误自恢复等企业级特性。`,
    code: `import React, { Suspense, useState, useEffect, useCallback } from 'react';
    import { ErrorBoundary } from 'react-error-boundary';
    
    
    
    import { moduleRegistry } from './utils/moduleRegistry';
    import { cacheManager } from './utils/cacheManager';

    // 动态模块加载器
    class ModuleLoader {
      static cache = new Map();
      static loadingPromises = new Map();
      static retryCount = new Map();

      static async loadModule(moduleName, options = {}) {
        const { maxRetries = 3, timeout = 10000, fallback = null } = options;
        const cacheKey = moduleName + JSON.stringify(options);

        // 检查缓存
        if (this.cache.has(cacheKey)) {
          return this.cache.get(cacheKey);
        }

        // 检查是否正在加载
        if (this.loadingPromises.has(cacheKey)) {
          return this.loadingPromises.get(cacheKey);
        }

        // 创建加载Promise
        const loadPromise = this.attemptLoad(moduleName, maxRetries, timeout, fallback);
        this.loadingPromises.set(cacheKey, loadPromise);

        try {
          const module = await loadPromise;
          this.cache.set(cacheKey, module);
          this.loadingPromises.delete(cacheKey);
          this.retryCount.delete(moduleName);
          return module;
        } catch (error) {
          this.loadingPromises.delete(cacheKey);
          throw error;
        }
      }

      static async attemptLoad(moduleName, maxRetries, timeout, fallback) {
        let lastError;
        const currentRetries = this.retryCount.get(moduleName) || 0;

        for (let attempt = currentRetries; attempt < maxRetries; attempt++) {
          try {
            this.retryCount.set(moduleName, attempt + 1);
            
            // 设置超时
            const timeoutPromise = new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Module load timeout')), timeout)
            );

            const loadPromise = import('./modules/' + moduleName);
            const module = await Promise.race([loadPromise, timeoutPromise]);
            
            return module;
          } catch (error) {
            lastError = error;
            console.warn('Module load attempt ' + (attempt + 1) + ' failed:', error.message);
            
            // 指数退避
            if (attempt < maxRetries - 1) {
              await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
            }
          }
        }

        // 所有重试失败，使用降级方案
        if (fallback) {
          console.warn('Using fallback for module: ' + moduleName);
          return fallback;
        }

        throw lastError;
      }
    }

    // 智能模块加载组件
    const SmartModuleLoader = ({ moduleName, fallbackComponent = null, children, ...props }) => {
      const [error, setError] = useState(null);
      const [isLoading, setIsLoading] = useState(false);
      const { isOnline, connectionSpeed } = useNetworkStatus();
      const { trackModuleLoad } = usePerformanceMonitor();

      // 根据网络状况调整加载策略
      const getLoadOptions = useCallback(() => {
        const baseOptions = {
          maxRetries: isOnline ? 3 : 1,
          timeout: connectionSpeed === 'slow' ? 30000 : 10000,
          fallback: fallbackComponent
        };

        return baseOptions;
      }, [isOnline, connectionSpeed, fallbackComponent]);

      const LazyComponent = React.lazy(() => {
        setIsLoading(true);
        const startTime = performance.now();
        
        return ModuleLoader.loadModule(moduleName, getLoadOptions())
          .then(module => {
            const loadTime = performance.now() - startTime;
            trackModuleLoad(moduleName, loadTime, 'success');
            setIsLoading(false);
            return module;
          })
          .catch(error => {
            const loadTime = performance.now() - startTime;
            trackModuleLoad(moduleName, loadTime, 'error', error.message);
            setIsLoading(false);
            setError(error);
            throw error;
          });
      });

      if (error && fallbackComponent) {
        return React.createElement(fallbackComponent, { error, ...props });
      }

      return (
        <ErrorBoundary
          fallback={({ error, resetErrorBoundary }) => (
            <ModuleErrorFallback
              error={error}
              moduleName={moduleName}
              onRetry={resetErrorBoundary}
              fallbackComponent={fallbackComponent}
            />
          )}
          onError={(error) => setError(error)}
        >
          <Suspense fallback={<SmartLoadingIndicator moduleName={moduleName} isLoading={isLoading} />}>
            <LazyComponent {...props}>
              {children}
            </LazyComponent>
          </Suspense>
        </ErrorBoundary>
      );
    };

    // 智能加载指示器
    const SmartLoadingIndicator = ({ moduleName, isLoading }) => {
      const [progress, setProgress] = useState(0);
      const [estimatedTime, setEstimatedTime] = useState(null);
      const { getModuleStats } = usePerformanceMonitor();

      useEffect(() => {
        if (!isLoading) return;

        const stats = getModuleStats(moduleName);
        const avgLoadTime = stats?.averageLoadTime || 3000;
        setEstimatedTime(Math.ceil(avgLoadTime / 1000));

        const interval = setInterval(() => {
          setProgress(prev => {
            if (prev >= 90) return prev;
            return prev + Math.random() * 10;
          });
        }, 200);

        return () => clearInterval(interval);
      }, [isLoading, moduleName, getModuleStats]);

      return (
        <div className="flex flex-col items-center justify-center h-64 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg">
          <div className="relative w-32 h-32 mb-6">
            <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="#e5e7eb"
                strokeWidth="8"
                fill="none"
              />
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="#3b82f6"
                strokeWidth="8"
                fill="none"
                strokeDasharray={'251.2 251.2'}
                strokeDashoffset={251.2 - (progress / 100) * 251.2}
                className="transition-all duration-500 ease-out"
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-2xl font-bold text-blue-600">{Math.round(progress)}%</span>
            </div>
          </div>
          
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">加载{moduleName}模块</h3>
            <p className="text-sm text-gray-600">
              {estimatedTime && '预计还需 ' + Math.max(1, estimatedTime - Math.floor(progress / 20)) + ' 秒'}
            </p>
            <div className="mt-4 flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            </div>
          </div>
        </div>
      );
    };

    // 模块错误降级组件
    const ModuleErrorFallback = ({ error, moduleName, onRetry, fallbackComponent }) => {
      const [retrying, setRetrying] = useState(false);
      const { isOnline } = useNetworkStatus();

      const handleRetry = async () => {
        setRetrying(true);
        try {
          // 清除缓存
          ModuleLoader.cache.clear();
          await new Promise(resolve => setTimeout(resolve, 1000));
          onRetry();
        } finally {
          setRetrying(false);
        }
      };

      if (fallbackComponent) {
        return React.createElement(fallbackComponent, { error });
      }

      return (
        <div className="flex flex-col items-center justify-center h-64 bg-red-50 border-2 border-red-200 rounded-lg">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">模块加载失败</h3>
          <p className="text-sm text-red-600 text-center mb-4">
            无法加载 {moduleName} 模块<br />
            {!isOnline && '当前网络连接不可用'}
            {isOnline && error.message}
          </p>
          <div className="flex space-x-3">
            <button
              onClick={handleRetry}
              disabled={retrying}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              {retrying ? '重试中...' : '重试加载'}
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              刷新页面
            </button>
          </div>
        </div>
      );
    };

    // 主应用组件
    function EnterpriseApp() {
      const [activeModules, setActiveModules] = useState(['dashboard']);
      const { preloadModules, getPreloadSuggestions } = useModulePreloader();
      const { trackUserBehavior } = usePerformanceMonitor();

      // 用户行为分析和预加载
      useEffect(() => {
        const suggestions = getPreloadSuggestions();
        preloadModules(suggestions);
      }, [getPreloadSuggestions, preloadModules]);

      const handleModuleActivation = useCallback((moduleName) => {
        trackUserBehavior('module_activate', { module: moduleName });
        setActiveModules(prev => [...prev, moduleName]);
      }, [trackUserBehavior]);

      return (
        <div className="min-h-screen bg-gray-100">
          <header className="bg-white shadow-sm border-b">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between h-16">
                <div className="flex items-center">
                  <h1 className="text-xl font-semibold">企业办公平台</h1>
                </div>
              </div>
            </div>
          </header>

          <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {activeModules.map(moduleName => (
                <div key={moduleName} className="bg-white rounded-lg shadow">
                  <SmartModuleLoader
                    moduleName={moduleName}
                    fallbackComponent={({ error }) => (
                      <div className="p-4 text-center text-gray-500">
                        模块暂时不可用
                      </div>
                    )}
                  />
                </div>
              ))}
            </div>
          </main>
        </div>
      );
    }

    export default EnterpriseApp;`,
    explanation: `这个企业级示例展示了React.lazy在大型应用中的完整解决方案，包含：

**核心架构特性：**
1. **智能加载器**：支持重试、超时、降级的模块加载机制
2. **性能监控**：实时跟踪加载性能和用户行为
3. **网络适配**：根据网络状况调整加载策略
4. **错误恢复**：完善的错误处理和自动恢复机制
5. **预测性加载**：基于用户行为分析的智能预加载

**企业级特性：**
- 支持弱网和离线场景
- 模块加载失败时的优雅降级
- 实时性能监控和告警
- 可配置的加载策略
- 完整的错误日志和恢复机制`,
    benefits: [
      '支持10万+并发用户，系统稳定性达到99.9%',
      '模块加载失败率降低到0.1%，用户体验显著提升',
      '智能预加载机制使模块响应时间降低到50ms以内',
      '完善的监控体系实现问题的快速定位和自动恢复',
      '网络适配能力使弱网用户的使用体验提升80%'
    ],
    metrics: {
      performance: '整体页面加载速度提升70%，首屏时间从5.2s降低到1.6s',
      userExperience: '用户满意度从78%提升到94%，系统可用性达99.9%',
      technicalMetrics: '代码分割效率90%，缓存命中率85%，错误恢复成功率98%'
    },
    difficulty: 'hard',
    tags: ['企业级', '性能监控', '错误恢复', '智能加载', '大规模应用']
  }
];

export default businessScenarios;