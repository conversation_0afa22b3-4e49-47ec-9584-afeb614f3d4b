import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'suspense-boundary-missing',
    question: '使用React.lazy时遇到"A React component suspended while rendering, but no fallback UI was specified"错误',
    answer: '这个错误表示懒加载组件没有被Suspense边界包装。React.lazy组件在首次加载时会抛出Promise，必须有Suspense组件来捕获这个Promise并显示fallback UI。解决方法是用Suspense组件包装所有懒加载组件。',
    code: `// ❌ 错误：缺少Suspense边界
const LazyComponent = React.lazy(() => import('./Component'));

function App() {
  return <LazyComponent />; // 会导致错误
}

// ✅ 正确：使用Suspense包装
function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LazyComponent />
    </Suspense>
  );
}`,
    tags: ['Suspense', '错误处理', '边界'],
    relatedQuestions: ['如何处理懒加载失败', '多个懒加载组件如何管理']
  },

  {
    id: 'named-export-issue',
    question: '第三方组件库使用命名导出，React.lazy无法直接使用怎么办？',
    answer: 'React.lazy只支持默认导出(default export)。对于命名导出的组件，需要创建中间模块重新导出，或者使用动态导入后解构的方式来处理。推荐使用中间模块方案，性能更好且类型安全。',
    code: `// 原始组件库 - components.js
export const Button = () => <button>Click me</button>;
export const Input = () => <input />;

// 方案一：创建中间模块 - Button.lazy.js
export { Button as default } from './components';

// 使用
const LazyButton = React.lazy(() => import('./Button.lazy'));

// 方案二：动态导入解构
const LazyButton = React.lazy(async () => {
  const { Button } = await import('./components');
  return { default: Button };
});`,
    tags: ['命名导出', '模块系统', '第三方库'],
    relatedQuestions: ['如何处理TypeScript类型', '打包工具配置问题']
  },

  {
    id: 'loading-performance',
    question: '懒加载组件首次加载时间过长，用户体验不好',
    answer: '可以通过多种策略优化加载性能：1) 实现预加载机制；2) 优化chunk大小；3) 使用CDN加速；4) 提供更好的loading状态；5) 实现渐进式加载。关键是平衡性能和用户体验。',
    code: `// 预加载策略
const LazyComponent = React.lazy(() => import('./Component'));

// 鼠标悬停时预加载
function PreloadLink({ to, children }) {
  const [isHovered, setIsHovered] = useState(false);
  
  useEffect(() => {
    if (isHovered) {
      // 预加载组件
      import('./Component').catch(console.error);
    }
  }, [isHovered]);
  
  return (
    <Link 
      to={to}
      onMouseEnter={() => setIsHovered(true)}
    >
      {children}
    </Link>
  );
}

// 更好的loading状态
function SmartSuspense({ children }) {
  const [showDelay, setShowDelay] = useState(false);
  
  useEffect(() => {
    const timer = setTimeout(() => setShowDelay(true), 300);
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <Suspense 
      fallback={
        showDelay ? (
          <div className="loading-skeleton">
            <div className="skeleton-header"></div>
            <div className="skeleton-content"></div>
          </div>
        ) : null
      }
    >
      {children}
    </Suspense>
  );
}`,
    tags: ['性能优化', '预加载', '用户体验'],
    relatedQuestions: ['如何监控加载性能', 'CDN配置最佳实践']
  },

  {
    id: 'build-configuration',
    question: '在生产环境中懒加载组件加载失败，本地开发环境正常',
    answer: '通常是构建配置或部署配置问题。检查：1) publicPath配置是否正确；2) 静态资源服务器配置；3) CDN配置和缓存策略；4) chunk文件命名和路径；5) 网络环境和防火墙设置。',
    code: `// webpack.config.js 配置检查
module.exports = {
  output: {
    // 确保publicPath配置正确
    publicPath: process.env.NODE_ENV === 'production' 
      ? 'https://cdn.example.com/assets/' 
      : '/',
    // chunk文件命名
    chunkFilename: '[name].[contenthash].chunk.js'
  },
  
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    },
  }
};

// 添加错误处理和重试
const LazyComponent = React.lazy(() => 
  import('./Component').catch(error => {
    console.error('Component load failed:', error);
    // 可以返回错误组件或重试
    return { default: () => <div>加载失败，请刷新重试</div> };
  })
);`,
    tags: ['构建配置', 'webpack', '部署'],
    relatedQuestions: ['如何配置CDN', '缓存策略最佳实践']
  },

  {
    id: 'debugging-difficulty',
    question: '懒加载组件的错误难以调试，DevTools中信息不够详细',
    answer: '可以通过多种方式改善调试体验：1) 添加详细的错误日志；2) 使用React DevTools Profiler；3) 实现自定义错误边界；4) 添加加载状态监控；5) 使用source map定位问题。',
    code: `// 调试友好的懒加载包装器
function createDebuggableLazy(importFn, componentName) {
  return React.lazy(async () => {
    console.log('[DEBUG] Loading component:', componentName);
    const startTime = performance.now();
    
    try {
      const module = await importFn();
      const loadTime = performance.now() - startTime;
      console.log('[DEBUG] Component loaded:', componentName, 'in', loadTime + 'ms');
      
      // 添加displayName便于调试
      if (module.default) {
        module.default.displayName = componentName;
      }
      
      return module;
    } catch (error) {
      console.error('[DEBUG] Component load failed:', componentName, error);
      throw error;
    }
  });
}

// 使用
const LazyDashboard = createDebuggableLazy(
  () => import('./Dashboard'),
  'Dashboard'
);

// 自定义错误边界
class LazyComponentErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Lazy component error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      props: this.props
    });
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h3>组件加载出错</h3>
          <details>
            <summary>错误详情</summary>
            <pre>{this.state.error?.stack}</pre>
          </details>
          <button onClick={() => window.location.reload()}>
            刷新页面
          </button>
        </div>
      );
    }
    
    return this.props.children;
  }
}`,
    tags: ['调试', 'DevTools', '错误处理'],
    relatedQuestions: ['如何使用React DevTools', '生产环境错误监控']
  }
];

export default commonQuestions;