import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  introduction: `useInsertionEffect的诞生是CSS-in-JS技术演进的必然结果，它承载着前端样式管理从静态到动态、从分离到集成的历史变迁。这个看似简单的Hook背后，隐藏着十多年来前端工程化的智慧结晶和技术博弈。`,
  
  background: `## 🏛️ 历史背景：样式管理的演进之路

### 早期的样式困境（2000-2010）
在Web开发的早期，样式管理面临着根本性的挑战：
- **全局命名空间污染**：CSS的全局性质导致样式冲突频发
- **维护性差**：大型项目中CSS文件难以维护和重构
- **缺乏模块化**：无法实现真正的样式封装和复用

### CSS预处理器时代（2010-2015）
Sass、Less等预处理器的出现带来了变量、嵌套、混入等特性：
- **变量系统**：解决了样式值的复用问题
- **嵌套语法**：提供了更好的代码组织方式
- **函数和混入**：实现了样式逻辑的抽象

但本质问题依然存在：样式与组件逻辑分离，缺乏真正的封装。

### CSS Modules革命（2015-2017）
CSS Modules提出了局部作用域的概念：
- **自动类名生成**：解决了命名冲突问题
- **依赖关系明确**：样式文件与组件文件建立了明确的依赖
- **构建时处理**：通过工具链实现样式的模块化

这为后来的CSS-in-JS奠定了理论基础。`,

  evolution: `## 🌱 CSS-in-JS的演进历程

### 第一代：内联样式复兴（2013-2015）
React的出现重新点燃了内联样式的讨论：

\`\`\`javascript
// React早期的内联样式
const buttonStyle = {
  backgroundColor: '#007bff',
  color: 'white',
  padding: '8px 16px',
  border: 'none',
  borderRadius: '4px'
};

function Button() {
  return <button style={buttonStyle}>Click me</button>;
}
\`\`\`

**优势**：
- 完全的组件封装
- 动态样式支持
- 无全局污染

**局限**：
- 无法使用CSS的高级特性（伪类、媒体查询等）
- 性能问题（每次渲染都创建新对象）
- 缺乏样式复用机制

### 第二代：运行时CSS-in-JS（2016-2018）
以styled-components、emotion为代表的库解决了第一代的问题：

**styled-components的革命性语法：**
- 使用模板字符串语法，保持CSS的原生体验
- 支持动态样式：根据props动态生成样式
- 完整CSS特性：伪类、媒体查询、嵌套等
- 自动供应商前缀和样式隔离

**创新点**：
- 模板字符串语法，保持CSS的原生体验
- 支持完整的CSS特性
- 主题系统和动态样式
- 自动供应商前缀

**挑战**：
- 运行时性能开销
- 样式注入时机问题
- 服务端渲染复杂性

### 第三代：编译时优化（2019-2021）
零运行时的CSS-in-JS方案开始兴起：

**编译时CSS-in-JS的特点：**
- 零运行时开销：样式在构建时提取
- 静态分析：支持更好的优化
- 类型安全：提供完整的TypeScript支持
- 性能优异：避免运行时样式计算

**优势**：
- 零运行时开销
- 构建时优化
- 更好的性能表现

**权衡**：
- 动态样式支持有限
- 构建复杂度增加

### 第四代：精确时机控制（2022-至今）
useInsertionEffect的引入标志着CSS-in-JS进入新阶段：

**精确时机控制的突破：**
- 专用Hook：专门为样式注入设计
- 时机精确：在DOM变更后、布局前执行
- 性能优化：避免样式闪烁问题
- 官方支持：React团队的正式认可

**突破**：
- 精确的样式注入时机
- 避免样式闪烁问题
- 更好的性能表现
- 与React并发特性的完美配合`,

  timeline: [
    {
      year: '2013',
      event: 'React发布，重新定义组件化',
      description: 'Facebook发布React，提出了组件化的前端开发理念，为CSS-in-JS奠定了基础',
      significance: 'React的组件化思想启发了样式管理的新思路，开始有人思考样式与组件的紧密结合'
    },
    {
      year: '2014',
      event: 'Christopher Chedeau的CSS-in-JS演讲',
      description: '在NationJS大会上，Christopher Chedeau发表了著名的"CSS in JS"演讲，正式提出了CSS-in-JS的概念',
      significance: '这次演讲被认为是CSS-in-JS运动的起点，引发了前端社区对样式管理的重新思考'
    },
    {
      year: '2015',
      event: 'CSS Modules发布',
      description: 'Glen Maddern和Mark Dalgleish发布CSS Modules，提供了局部作用域的CSS解决方案',
      significance: '证明了样式模块化的可行性，为CSS-in-JS的发展提供了理论支撑'
    },
    {
      year: '2016',
      event: 'styled-components诞生',
      description: 'Glen Maddern和Max Stoiber创建了styled-components，将CSS-in-JS推向主流',
      significance: '第一个真正成功的CSS-in-JS库，确立了模板字符串语法的标准'
    },
    {
      year: '2017',
      event: 'emotion发布',
      description: 'Kye Hohenberger发布emotion，提供了更高性能的CSS-in-JS实现',
      significance: '推动了CSS-in-JS的性能优化，引入了更多创新特性'
    },
    {
      year: '2018',
      event: 'React Hooks发布',
      description: 'React 16.8引入Hooks，为CSS-in-JS提供了新的实现方式',
      significance: 'Hooks的出现让CSS-in-JS库能够更好地集成到React生态中'
    },
    {
      year: '2020',
      event: '零运行时CSS-in-JS兴起',
      description: 'linaria、compiled等零运行时方案开始受到关注',
      significance: '解决了运行时性能问题，但在动态样式支持上做出了权衡'
    },
    {
      year: '2022',
      event: 'useInsertionEffect发布',
      description: 'React 18引入useInsertionEffect，专门为CSS-in-JS优化',
      significance: '标志着React官方对CSS-in-JS的正式支持，解决了样式注入时机的根本问题'
    }
  ],

  keyFigures: [
    {
      name: 'Christopher Chedeau (vjeux)',
      role: 'CSS-in-JS概念提出者',
      contribution: '2014年在NationJS大会上发表了开创性的"CSS in JS"演讲，正式提出了CSS-in-JS的概念和理念',
      significance: '被誉为CSS-in-JS之父，他的演讲启发了整个前端社区对样式管理的重新思考'
    },
    {
      name: 'Glen Maddern',
      role: 'CSS Modules和styled-components创始人',
      contribution: '创建了CSS Modules，后来又参与创建了styled-components，推动了样式模块化的发展',
      significance: '在样式模块化领域做出了开创性贡献，影响了整个CSS-in-JS生态的发展方向'
    },
    {
      name: 'Max Stoiber',
      role: 'styled-components联合创始人',
      contribution: '与Glen Maddern共同创建了styled-components，并持续推动其发展和社区建设',
      significance: '通过styled-components的成功，证明了CSS-in-JS的商业价值和技术可行性'
    },
    {
      name: 'Kye Hohenberger',
      role: 'emotion创始人',
      contribution: '创建了emotion库，在性能和功能上都有重要创新，推动了CSS-in-JS的技术进步',
      significance: '通过emotion的创新，推动了整个CSS-in-JS生态的性能优化和功能完善'
    }
  ],

  concepts: [
    {
      term: 'CSS-in-JS',
      definition: '一种将CSS样式直接写在JavaScript代码中的技术方案，实现样式与组件的紧密耦合',
      evolution: '从简单的内联样式，发展到支持完整CSS特性的库，再到编译时优化和精确时机控制',
      modernRelevance: '现代前端开发中实现组件化样式管理的主流方案，特别适合React等组件化框架'
    },
    {
      term: '样式注入时机',
      definition: '指CSS规则被插入到DOM中的具体时间点，直接影响样式的生效和页面的渲染表现',
      evolution: '从随意的异步注入，到useLayoutEffect的同步注入，再到useInsertionEffect的精确时机控制',
      modernRelevance: 'useInsertionEffect的核心价值所在，解决了CSS-in-JS长期存在的样式闪烁问题'
    },
    {
      term: '运行时vs编译时',
      definition: '两种不同的CSS-in-JS实现策略：运行时在浏览器中动态生成样式，编译时在构建阶段提取样式',
      evolution: '从纯运行时方案，到运行时+编译时混合，再到纯编译时方案，现在趋向于精确的运行时控制',
      modernRelevance: '现代CSS-in-JS需要在动态性和性能之间找到平衡，useInsertionEffect提供了新的可能性'
    }
  ],

  designPhilosophy: `## 🎯 设计哲学：精确时机控制的艺术

useInsertionEffect的设计体现了React团队对CSS-in-JS生态的深度理解和技术洞察：

### 专业化原则
不同于useEffect和useLayoutEffect的通用性，useInsertionEffect专门为样式注入而生。这种专业化设计体现了"工具应该专注于特定问题"的哲学。

### 时机精确性
在DOM变更后、布局计算前的精确时机执行，确保样式能够影响首次布局计算。这种精确控制体现了对浏览器渲染流水线的深度理解。

### 生态友好性
专门为CSS-in-JS库设计，体现了React对社区生态的重视和支持。这不仅是技术决策，更是生态战略。

### 向后兼容性
在不影响现有Hook行为的前提下，提供新的能力。这体现了React团队对稳定性和渐进式升级的重视。`,

  impact: `## 🌍 技术影响：重塑CSS-in-JS生态

useInsertionEffect的引入对整个前端生态产生了深远影响：

### 对CSS-in-JS库的影响
- **性能提升**：解决了长期存在的样式闪烁问题
- **实现简化**：库作者不再需要复杂的时机控制逻辑
- **标准化**：提供了统一的样式注入标准

### 对开发者体验的影响
- **更好的视觉体验**：用户不再看到样式闪烁
- **更简单的API**：开发者可以更直接地控制样式注入
- **更好的性能**：减少了不必要的重排重绘

### 对前端架构的影响
- **组件化样式**：进一步推动了样式的组件化管理
- **运行时优化**：为运行时CSS-in-JS提供了新的优化空间
- **生态整合**：促进了React生态的进一步整合`,

  modernRelevance: `## 🚀 现代意义：面向未来的样式管理

在当今的前端开发环境中，useInsertionEffect具有重要的现代意义：

### 响应现代开发需求
- **组件化开发**：完美契合现代组件化开发模式
- **动态样式**：支持基于状态的动态样式生成
- **性能优化**：在保持灵活性的同时优化性能

### 适应技术趋势
- **并发渲染**：与React 18的并发特性完美配合
- **服务端渲染**：为SSR场景提供了更好的支持
- **微前端**：在微前端架构中提供样式隔离

### 推动标准化
- **最佳实践**：确立了CSS-in-JS的最佳实践标准
- **生态统一**：推动了整个生态的标准化和统一
- **技术演进**：为未来的样式管理技术指明了方向

useInsertionEffect不仅解决了当前的技术问题，更为未来的前端开发奠定了坚实的基础。它代表了React团队对前端技术发展趋势的准确判断，也体现了技术演进中"专业化"和"精确化"的重要价值。`
};

export default knowledgeArchaeology;
