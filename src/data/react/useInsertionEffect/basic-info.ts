import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "useInsertionEffect是React 18中专门为CSS-in-JS库设计的Hook，在DOM变更后、useLayoutEffect执行前同步触发，确保样式注入的时机精确性",

  introduction: `useInsertionEffect是为了解决CSS-in-JS库样式注入时机问题而在React 18中引入的专用Hook。

它遵循"时机精确控制"的设计理念，在性能和样式一致性之间做出了精确的权衡选择。

主要用于CSS-in-JS样式注入、第三方样式库集成和动态样式管理。相比useEffect和useLayoutEffect，它的创新在于提供了更早的执行时机，专门优化样式相关操作。

在React生态中，它是样式管理层的核心工具，常见于现代前端应用，特别适合需要动态样式注入的场景。

核心优势包括样式注入时机精确、避免样式闪烁，但也需要注意仅限于样式相关操作的限制。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react/index.d.ts:1156
 * - 实现文件：packages/react/src/ReactHooks.js:95
 * - 内部类型：packages/react-reconciler/src/ReactFiberHooks.js:1890
 */

// 基础语法
useInsertionEffect(effect: EffectCallback, deps?: DependencyList): void;

// 完整类型定义
type EffectCallback = () => (void | (() => void | undefined));
type DependencyList = ReadonlyArray<any>;

function useInsertionEffect(
  effect: () => void | (() => void),
  deps?: React.DependencyList
): void;

// 使用示例
useInsertionEffect(() => {
  // 样式注入逻辑
  const style = document.createElement('style');
  style.textContent = '.my-class { color: red; }';
  document.head.appendChild(style);
  
  // 清理函数
  return () => {
    document.head.removeChild(style);
  };
}, []);`,

  // 快速示例 - 完整且简单的基础用法
  quickExample: `function StyleInjectionExample() {
  // 使用useInsertionEffect注入CSS样式
  useInsertionEffect(() => {
    // 创建样式元素并注入到文档头部
    const style = document.createElement('style');
    style.textContent = '.dynamic-button { background: linear-gradient(45deg, #ff6b6b, #4ecdc4); border: none; padding: 12px 24px; border-radius: 8px; color: white; font-weight: bold; cursor: pointer; transition: transform 0.2s ease; } .dynamic-button:hover { transform: scale(1.05); }';
    document.head.appendChild(style);

    // 清理函数：组件卸载时移除样式
    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []); // 空依赖数组，只在组件挂载时执行一次

  return (
    <div>
      {/* 使用动态注入的样式 */}
      <button className="dynamic-button">
        动态样式按钮
      </button>
      <p>这个按钮的样式是通过useInsertionEffect动态注入的</p>
    </div>
  );
}`,

  // 业务场景图表 - 展示使用场景和相关API
  scenarioDiagram: `graph TD
    A[CSS-in-JS应用场景] --> B[样式注入]
    A --> C[主题切换]
    A --> D[第三方库集成]

    B --> B1[动态CSS规则]
    B --> B2[组件样式隔离]
    B --> B3[条件样式应用]

    C --> C1[深色/浅色主题]
    C --> C2[用户自定义主题]
    C --> C3[响应式主题适配]

    D --> D1[Styled-components集成]
    D --> D2[Emotion库支持]
    D --> D3[Material-UI定制]

    E[技术特性] --> F[精确时机控制]
    E --> G[同步执行]
    E --> H[样式优先级]

    F --> F1[DOM变更后立即执行]
    F --> F2[早于useLayoutEffect]
    F --> F3[避免样式闪烁]

    G --> G1[阻塞渲染直到完成]
    G --> G2[确保样式一致性]
    G --> G3[防止布局抖动]

    H --> H1[样式注入顺序可控]
    H --> H2[CSS优先级管理]
    H --> H3[样式覆盖策略]

    I[相关API] --> J[useLayoutEffect]
    I --> K[useEffect]
    I --> L[useMemo]

    J --> J1[DOM测量场景]
    K --> K1[异步副作用场景]
    L --> L1[样式计算缓存]

    style A fill:#e1f5fe
    style E fill:#f3e5f5
    style I fill:#e8f5e8`,

  parameters: [
    {
      name: "effect",
      type: "() => void | (() => void)",
      required: true,
      description: "要执行的副作用函数，可以返回一个清理函数。专门用于样式相关操作，在DOM变更后、useLayoutEffect前同步执行",
      example: `useInsertionEffect(() => {
  // 注入样式
  const style = document.createElement('style');
  style.textContent = '.my-class { color: blue; }';
  document.head.appendChild(style);

  // 返回清理函数
  return () => {
    document.head.removeChild(style);
  };
}, []);`
    },
    {
      name: "deps",
      type: "React.DependencyList | undefined",
      required: false,
      description: "依赖数组，控制effect的重新执行。当依赖项发生变化时，会先执行清理函数，然后重新执行effect",
      example: `// 依赖主题变化重新注入样式
useInsertionEffect(() => {
  const style = document.createElement('style');
  style.textContent = '.themed-element { background-color: ' + theme.backgroundColor + '; color: ' + theme.textColor + '; }';
  document.head.appendChild(style);

  return () => document.head.removeChild(style);
}, [theme]); // 依赖theme对象`
    }
  ],

  returnValue: {
    type: "void",
    description: "useInsertionEffect不返回任何值，它是一个纯副作用Hook，专门用于执行样式注入等DOM操作"
  },

  keyFeatures: [
    {
      feature: "精确的执行时机",
      description: "在DOM变更后、useLayoutEffect执行前的特定时机同步执行，专为样式注入优化",
      importance: "critical" as const,
      details: "这个时机确保样式能在布局计算前生效，避免样式闪烁和布局抖动"
    },
    {
      feature: "CSS-in-JS专用优化",
      description: "专门为CSS-in-JS库设计，解决样式注入时机和性能问题",
      importance: "high" as const,
      details: "相比useEffect和useLayoutEffect，提供了更适合样式操作的执行环境"
    },
    {
      feature: "同步执行保证",
      description: "同步执行确保样式立即生效，不会出现异步延迟导致的视觉问题",
      importance: "high" as const,
      details: "阻塞渲染直到样式注入完成，保证用户看到的是最终样式效果"
    },
    {
      feature: "清理机制完善",
      description: "支持返回清理函数，确保组件卸载时正确清理注入的样式",
      importance: "medium" as const,
      details: "防止样式泄漏和内存泄漏，维护应用的性能和稳定性"
    }
  ],

  limitations: [
    "仅适用于样式相关操作，不应用于其他类型的副作用，因为执行时机过早可能导致问题",
    "同步执行会阻塞渲染，过重的计算会影响性能，应避免在其中执行复杂逻辑",
    "不能访问DOM布局信息，因为此时布局计算尚未完成，DOM几何属性不可用",
    "依赖数组的比较使用Object.is，对象和数组需要注意引用稳定性",
    "在服务端渲染(SSR)中不会执行，需要考虑样式的服务端处理方案"
  ],

  bestPractices: [
    "仅用于样式注入：专门用于CSS规则注入，不要用于其他副作用操作",
    "保持操作轻量：避免在useInsertionEffect中执行复杂计算或大量DOM操作",
    "正确管理样式生命周期：始终提供清理函数来移除注入的样式，防止样式泄漏",
    "使用稳定的依赖：确保依赖数组中的对象引用稳定，避免不必要的重新执行",
    "配合CSS-in-JS库使用：与styled-components、emotion等库结合使用时效果最佳",
    "考虑样式优先级：注意CSS规则的插入顺序，合理管理样式优先级",
    "性能监控：监控样式注入的性能影响，确保不会阻塞渲染过久",
    "SSR兼容性：为服务端渲染场景提供备用的样式处理方案"
  ],

  warnings: [
    "不要在useInsertionEffect中执行非样式相关的副作用，这会违背其设计初衷并可能导致性能问题",
    "避免在其中执行异步操作或状态更新，因为执行时机特殊，可能导致不可预期的行为",
    "注意样式注入的性能影响，大量或复杂的CSS规则可能会阻塞渲染，影响用户体验",
    "确保提供清理函数，否则组件卸载后样式仍会保留，可能影响其他组件或页面",
    "在开发环境中React可能会双重调用effect，确保代码具有幂等性，重复执行不会产生副作用"
  ]
};

export default basicInfo;
