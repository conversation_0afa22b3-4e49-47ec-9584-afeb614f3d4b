import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: 'useInsertionEffect是什么？它解决了什么问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'useInsertionEffect是React 18中专门为CSS-in-JS库设计的Hook，在DOM变更后、useLayoutEffect执行前同步触发，解决样式注入时机问题。',
      detailed: `useInsertionEffect是React 18引入的一个专用Hook，主要解决CSS-in-JS库的样式注入时机问题。

## 🎯 核心功能
useInsertionEffect专门用于在DOM变更后、useLayoutEffect执行前的特定时机同步执行副作用，确保样式能在布局计算前生效。

## 🔧 解决的问题
1. **样式闪烁**：传统useEffect异步执行导致的样式延迟应用
2. **布局抖动**：样式注入时机不当造成的视觉跳动
3. **性能问题**：多次重排重绘带来的性能开销
4. **时机控制**：CSS-in-JS库需要精确的样式注入时机

## 💡 核心价值
- ✅ **时机精确**：在最佳时机注入样式，避免视觉问题
- ✅ **性能优化**：减少重排重绘次数
- ✅ **专业化**：专门为样式操作设计，不适用于其他副作用
- ✅ **同步执行**：确保样式立即生效`,
      code: `
// 基础使用示例
import { useInsertionEffect } from 'react';

function StyledComponent() {
  useInsertionEffect(() => {
    // 注入CSS样式
    const style = document.createElement('style');
    style.textContent = '.my-component { background: linear-gradient(45deg, #ff6b6b, #4ecdc4); padding: 16px; border-radius: 8px; }';
    document.head.appendChild(style);

    // 清理函数
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return <div className="my-component">样式化组件</div>;
}
      `
    },
    tags: ['基础概念', 'CSS-in-JS', '样式注入']
  },

  {
    id: 2,
    question: 'useInsertionEffect与useEffect、useLayoutEffect有什么区别？执行顺序是怎样的？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: '三者执行时机不同：useInsertionEffect在DOM变更后立即同步执行，useLayoutEffect在布局阶段同步执行，useEffect在渲染完成后异步执行。',
      detailed: `三个Effect Hook的执行时机和用途各不相同：

## ⏰ 执行时机对比

**执行顺序：**
1. DOM变更完成
2. useInsertionEffect (同步) ← 样式注入
3. useLayoutEffect (同步) ← DOM测量/布局调整
4. 浏览器绘制
5. useEffect (异步) ← 一般副作用

## 🎯 用途区别

### useInsertionEffect
- **用途**：专门用于CSS样式注入
- **时机**：DOM变更后，布局计算前
- **特点**：同步执行，阻塞渲染
- **场景**：CSS-in-JS库，动态样式注入

### useLayoutEffect  
- **用途**：DOM测量和同步布局调整
- **时机**：DOM变更后，浏览器绘制前
- **特点**：同步执行，可以读取DOM布局
- **场景**：测量DOM尺寸，同步DOM操作

### useEffect
- **用途**：一般副作用操作
- **时机**：浏览器绘制后
- **特点**：异步执行，不阻塞渲染
- **场景**：数据获取，事件监听，订阅

## 🚨 选择原则
- 样式注入 → useInsertionEffect
- DOM测量 → useLayoutEffect  
- 其他副作用 → useEffect`,
      code: `
// 执行顺序演示
function ExecutionOrderDemo() {
  console.log('1. 组件渲染');
  
  useInsertionEffect(() => {
    console.log('2. useInsertionEffect执行 - 注入样式');
    const style = document.createElement('style');
    style.textContent = '.demo { color: red; }';
    document.head.appendChild(style);
    
    return () => {
      console.log('useInsertionEffect清理');
      document.head.removeChild(style);
    };
  }, []);
  
  useLayoutEffect(() => {
    console.log('3. useLayoutEffect执行 - 测量DOM');
    const element = document.querySelector('.demo');
    if (element) {
      console.log('元素尺寸:', element.getBoundingClientRect());
    }
  }, []);
  
  useEffect(() => {
    console.log('4. useEffect执行 - 异步副作用');
    // 数据获取、事件监听等
  }, []);
  
  return <div className="demo">执行顺序演示</div>;
}
      `
    },
    tags: ['执行时机', '对比分析', '实现原理']
  },

  {
    id: 3,
    question: '在实际项目中，如何使用useInsertionEffect优化CSS-in-JS库的性能？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '实战应用',
    answer: {
      brief: '通过useInsertionEffect在正确时机注入样式，配合样式缓存、批量更新、依赖优化等策略，可以显著提升CSS-in-JS的性能。',
      detailed: `在实际项目中优化CSS-in-JS性能需要综合考虑多个方面：

## 🚀 性能优化策略

### 1. 样式缓存机制
- 缓存已生成的CSS规则，避免重复计算
- 使用WeakMap存储组件样式映射
- 实现样式去重和复用

### 2. 批量样式更新
- 收集多个组件的样式变更
- 一次性批量注入，减少DOM操作
- 使用DocumentFragment优化插入性能

### 3. 依赖数组优化
- 精确控制样式重新生成的时机
- 使用useMemo缓存样式计算结果
- 避免不必要的样式重新注入

### 4. 样式作用域管理
- 为每个组件生成唯一的CSS类名
- 实现样式隔离，避免全局污染
- 支持样式的按需加载和卸载

## 💡 最佳实践
- 将样式逻辑抽取为自定义Hook
- 实现样式的懒加载和预加载
- 监控样式注入的性能指标
- 提供开发环境的调试工具`,
      code: `
// 高性能CSS-in-JS实现
import { useInsertionEffect, useMemo, useRef } from 'react';

// 样式缓存
const styleCache = new Map();
const componentStyleMap = new WeakMap();

function useOptimizedStyles(styleConfig, dependencies = []) {
  const componentRef = useRef();
  
  // 生成样式缓存键
  const cacheKey = useMemo(() => {
    return JSON.stringify(styleConfig) + dependencies.join(',');
  }, [styleConfig, ...dependencies]);
  
  // 缓存样式计算结果
  const computedStyles = useMemo(() => {
    if (styleCache.has(cacheKey)) {
      return styleCache.get(cacheKey);
    }
    
    const styles = generateStyles(styleConfig);
    styleCache.set(cacheKey, styles);
    return styles;
  }, [cacheKey]);
  
  useInsertionEffect(() => {
    const componentId = \`comp-\${Math.random().toString(36).substr(2, 9)}\`;
    const styleId = \`styles-\${componentId}\`;
    
    // 批量样式注入
    const injectStyles = () => {
      let style = document.getElementById(styleId);
      if (!style) {
        style = document.createElement('style');
        style.id = styleId;
        document.head.appendChild(style);
      }
      
      style.textContent = computedStyles;
    };
    
    // 使用requestAnimationFrame优化性能
    const rafId = requestAnimationFrame(injectStyles);
    
    // 记录组件样式映射
    componentStyleMap.set(componentRef.current, styleId);
    
    return () => {
      cancelAnimationFrame(rafId);
      const existingStyle = document.getElementById(styleId);
      if (existingStyle) {
        document.head.removeChild(existingStyle);
      }
      componentStyleMap.delete(componentRef.current);
    };
  }, [computedStyles]);
  
  return { componentRef, className: 'comp-' + cacheKey.slice(0, 8) };
}

// 样式生成函数
function generateStyles(config) {
  const { colors, spacing, typography } = config;
  
  return \`
    .comp-\${config.id} {
      background: \${colors.background};
      color: \${colors.text};
      padding: \${spacing.padding}px;
      font-size: \${typography.fontSize}px;
      border-radius: \${spacing.borderRadius}px;
      transition: all 0.2s ease;
    }
    
    .comp-\${config.id}:hover {
      background: \${colors.hover};
      transform: translateY(-2px);
    }
  \`;
}

// 使用示例
function OptimizedStyledComponent({ theme, size }) {
  const styleConfig = {
    id: 'button',
    colors: {
      background: theme.primary,
      text: theme.text,
      hover: theme.primaryHover
    },
    spacing: {
      padding: size === 'large' ? 16 : 12,
      borderRadius: 8
    },
    typography: {
      fontSize: size === 'large' ? 16 : 14
    }
  };
  
  const { componentRef, className } = useOptimizedStyles(
    styleConfig, 
    [theme.primary, size]
  );
  
  return (
    <button ref={componentRef} className={className}>
      优化的样式化按钮
    </button>
  );
}
      `
    },
    tags: ['性能优化', '实战应用', 'CSS-in-JS']
  },

  {
    id: 4,
    question: 'useInsertionEffect在服务端渲染(SSR)中有什么注意事项？如何处理样式的服务端渲染？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '高级应用',
    answer: {
      brief: 'useInsertionEffect在SSR中不会执行，需要配合服务端样式提取、客户端样式注水、样式优先级管理等技术实现完整的SSR支持。',
      detailed: `SSR环境下的useInsertionEffect需要特殊处理：

## 🚨 SSR挑战

### 1. 执行环境差异
- 服务端没有DOM，useInsertionEffect不会执行
- 客户端需要重新注入样式，可能导致闪烁
- 样式的服务端和客户端需要保持一致

### 2. 样式注水问题
- 服务端渲染的HTML包含样式类名
- 客户端需要及时注入对应的CSS规则
- 避免样式丢失和视觉跳动

## 🛠️ 解决方案

### 1. 服务端样式提取
- 在服务端收集所有组件的样式
- 将样式内联到HTML或生成CSS文件
- 确保首屏渲染时样式完整

### 2. 客户端样式注水
- 客户端启动时立即注入样式
- 使用useInsertionEffect确保时机正确
- 实现样式的增量更新

### 3. 样式优先级管理
- 确保服务端和客户端样式优先级一致
- 处理样式覆盖和冲突问题
- 支持动态样式的平滑切换`,
      code: `
// SSR样式管理解决方案
import { useInsertionEffect, useEffect, useState } from 'react';

// 服务端样式收集器
class ServerStyleCollector {
  constructor() {
    this.styles = new Set();
    this.componentStyles = new Map();
  }
  
  collectStyles(componentId, styles) {
    this.componentStyles.set(componentId, styles);
    this.styles.add(styles);
  }
  
  getCollectedStyles() {
    return Array.from(this.styles).join('\\n');
  }
  
  getStylesHTML() {
    return \`<style id="ssr-styles">\${this.getCollectedStyles()}</style>\`;
  }
}

// 全局样式收集器实例
let styleCollector = null;

// SSR兼容的样式Hook
function useSSRCompatibleStyles(styleConfig, dependencies = []) {
  const [isClient, setIsClient] = useState(false);
  const componentId = useMemo(() =>
    'comp-' + Math.random().toString(36).substr(2, 9), []
  );
  
  // 检测客户端环境
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // 生成样式
  const styles = useMemo(() => {
    return generateComponentStyles(componentId, styleConfig);
  }, [componentId, styleConfig, ...dependencies]);
  
  // 服务端样式收集
  if (typeof window === 'undefined' && styleCollector) {
    styleCollector.collectStyles(componentId, styles);
  }
  
  // 客户端样式注入
  useInsertionEffect(() => {
    if (!isClient) return;
    
    const styleId = \`ssr-style-\${componentId}\`;
    
    // 检查是否已有SSR样式
    const ssrStyles = document.getElementById('ssr-styles');
    const hasSSRStyle = ssrStyles && ssrStyles.textContent.includes(styles);
    
    if (!hasSSRStyle) {
      // 注入客户端样式
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = styles;
      document.head.appendChild(style);
      
      return () => {
        const existingStyle = document.getElementById(styleId);
        if (existingStyle) {
          document.head.removeChild(existingStyle);
        }
      };
    }
  }, [isClient, styles]);
  
  return { componentId, className: \`comp-\${componentId}\` };
}

// 服务端渲染入口
function renderToStringWithStyles(App) {
  styleCollector = new ServerStyleCollector();
  
  const html = renderToString(<App />);
  const stylesHTML = styleCollector.getStylesHTML();
  
  const fullHTML = \`
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>SSR App</title>
        \${stylesHTML}
      </head>
      <body>
        <div id="root">\${html}</div>
        <script>
          // 标记SSR样式，便于客户端识别
          window.__SSR_STYLES__ = true;
        </script>
      </body>
    </html>
  \`;
  
  styleCollector = null;
  return fullHTML;
}

// 客户端注水
function hydrateWithStyles(App, container) {
  // 客户端启动时清理SSR样式
  useEffect(() => {
    const ssrStyles = document.getElementById('ssr-styles');
    if (ssrStyles && window.__SSR_STYLES__) {
      // 延迟清理，确保客户端样式已注入
      setTimeout(() => {
        ssrStyles.remove();
        delete window.__SSR_STYLES__;
      }, 100);
    }
  }, []);
  
  hydrateRoot(container, <App />);
}

// 使用示例
function SSRStyledComponent({ theme }) {
  const styleConfig = {
    background: theme.background,
    color: theme.text,
    padding: '16px',
    borderRadius: '8px'
  };
  
  const { className } = useSSRCompatibleStyles(styleConfig, [theme]);
  
  return (
    <div className={className}>
      SSR兼容的样式化组件
    </div>
  );
}
      `
    },
    tags: ['SSR', '服务端渲染', '样式注水', '高级应用']
  },

  {
    id: 5,
    question: '如何设计一个基于useInsertionEffect的CSS-in-JS库？需要考虑哪些核心功能和性能优化？',
    difficulty: 'hard',
    frequency: 'low',
    category: '架构设计',
    answer: {
      brief: '设计CSS-in-JS库需要考虑样式生成、缓存机制、作用域隔离、性能优化、SSR支持、开发工具等核心功能，以及完整的生态系统集成。',
      detailed: `设计一个完整的CSS-in-JS库是一个复杂的系统工程：

## 🏗️ 核心架构设计

### 1. 样式生成引擎
- 支持多种样式语法（对象、模板字符串、函数）
- 实现样式计算和优化算法
- 支持主题系统和变量插值
- 提供样式组合和继承机制

### 2. 缓存和优化系统
- 多层缓存策略（内存、持久化）
- 样式去重和压缩算法
- 按需加载和代码分割
- 运行时性能监控

### 3. 作用域和隔离
- 自动生成唯一类名
- 支持CSS Modules模式
- 实现样式封装和隔离
- 处理全局样式和重置

### 4. 开发者体验
- TypeScript类型支持
- 开发工具和调试器
- 热重载和样式预览
- 错误提示和性能警告

## 🚀 性能优化策略
- 样式批量更新和合并
- 虚拟样式表和diff算法
- 关键路径样式优先加载
- 运行时和构建时优化`,
      code: `
// CSS-in-JS库核心实现
class StyleEngine {
  constructor(options = {}) {
    this.cache = new Map();
    this.styleSheet = null;
    this.componentCounter = 0;
    this.options = {
      prefix: 'css',
      enableCache: true,
      enableSSR: false,
      ...options
    };
    
    this.initializeStyleSheet();
  }
  
  initializeStyleSheet() {
    if (typeof document !== 'undefined') {
      this.styleSheet = document.createElement('style');
      this.styleSheet.id = 'css-in-js-styles';
      document.head.appendChild(this.styleSheet);
    }
  }
  
  // 样式生成核心方法
  createStyles(styleDefinition, dependencies = []) {
    const cacheKey = this.generateCacheKey(styleDefinition, dependencies);
    
    if (this.options.enableCache && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    const className = this.generateClassName();
    const cssText = this.compileToCSSText(className, styleDefinition);
    
    const styleResult = {
      className,
      cssText,
      inject: () => this.injectStyles(cssText),
      remove: () => this.removeStyles(className)
    };
    
    if (this.options.enableCache) {
      this.cache.set(cacheKey, styleResult);
    }
    
    return styleResult;
  }
  
  // 样式编译器
  compileToCSSText(className, styles) {
    const cssRules = [];
    
    const processStyles = (obj, selector = \`.\${className}\`) => {
      Object.entries(obj).forEach(([key, value]) => {
        if (typeof value === 'object' && value !== null) {
          // 处理嵌套选择器
          const nestedSelector = key.startsWith('&') 
            ? selector + key.slice(1)
            : \`\${selector} \${key}\`;
          processStyles(value, nestedSelector);
        } else {
          // 处理CSS属性
          const cssProperty = this.camelToKebab(key);
          cssRules.push(\`\${selector} { \${cssProperty}: \${value}; }\`);
        }
      });
    };
    
    processStyles(styles);
    return cssRules.join('\\n');
  }
  
  // 样式注入
  injectStyles(cssText) {
    if (this.styleSheet) {
      this.styleSheet.textContent += cssText + '\\n';
    }
  }
  
  // 工具方法
  generateClassName() {
    return \`\${this.options.prefix}-\${++this.componentCounter}-\${Math.random().toString(36).substr(2, 5)}\`;
  }
  
  generateCacheKey(styles, deps) {
    return JSON.stringify({ styles, deps });
  }
  
  camelToKebab(str) {
    return str.replace(/[A-Z]/g, letter => \`-\${letter.toLowerCase()}\`);
  }
}

// React Hook封装
const styleEngine = new StyleEngine();

function useStyles(styleDefinition, dependencies = []) {
  const [styleResult, setStyleResult] = useState(null);
  
  useInsertionEffect(() => {
    const result = styleEngine.createStyles(styleDefinition, dependencies);
    setStyleResult(result);
    
    // 注入样式
    result.inject();
    
    return () => {
      // 清理样式
      result.remove();
    };
  }, [styleDefinition, ...dependencies]);
  
  return styleResult?.className || '';
}

// 高级API：样式化组件
function styled(Component) {
  return function StyledComponent(styleDefinition) {
    return function WrappedComponent(props) {
      const className = useStyles(styleDefinition, [props.theme]);
      
      return (
        <Component 
          {...props} 
          className={[className, props.className].filter(Boolean).join(' ')}
        />
      );
    };
  };
}

// 使用示例
const StyledButton = styled('button')({
  background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
  border: 'none',
  padding: '12px 24px',
  borderRadius: '8px',
  color: 'white',
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 12px rgba(0,0,0,0.2)'
  },
  
  '&:active': {
    transform: 'translateY(0)'
  }
});

// 主题支持
const ThemeProvider = ({ theme, children }) => {
  useInsertionEffect(() => {
    // 注入主题变量
    const themeVars = Object.entries(theme)
      .map(([key, value]) => \`--theme-\${key}: \${value};\`)
      .join(' ');
    
    const style = document.createElement('style');
    style.textContent = \`:root { \${themeVars} }\`;
    document.head.appendChild(style);
    
    return () => document.head.removeChild(style);
  }, [theme]);
  
  return children;
};
      `
    },
    tags: ['架构设计', '库开发', '性能优化', '生态系统']
  }
];

export default interviewQuestions;
