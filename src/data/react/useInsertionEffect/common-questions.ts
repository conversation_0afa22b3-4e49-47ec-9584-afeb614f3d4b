import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'usage-error',
    question: '为什么我在useInsertionEffect中执行非样式操作时会出现问题？',
    answer: `useInsertionEffect专门为样式注入设计，在其中执行非样式操作可能导致多种问题：

## 🚨 常见问题表现
- **时机过早**：DOM可能尚未完全准备好，访问某些DOM属性会失败
- **性能影响**：同步执行会阻塞渲染，复杂操作会影响用户体验
- **行为异常**：某些API在此时机调用可能产生不可预期的结果

## ✅ 正确做法
- **仅用于样式**：只在useInsertionEffect中注入CSS规则
- **其他操作分离**：将非样式操作移到useLayoutEffect或useEffect中
- **保持轻量**：确保操作简单快速，避免复杂计算

## 💡 最佳实践
使用不同的Hook处理不同类型的副作用，让每个Hook专注于其设计目标。`,
    code: `// ❌ 错误用法 - 在useInsertionEffect中执行非样式操作
function BadExample() {
  useInsertionEffect(() => {
    // 错误：数据获取
    fetch('/api/data').then(setData);
    
    // 错误：事件监听
    window.addEventListener('resize', handleResize);
    
    // 错误：复杂计算
    const result = heavyComputation();
    
    // 错误：状态更新
    setState(newValue);
  }, []);
}

// ✅ 正确用法 - 分离不同类型的副作用
function GoodExample() {
  // 样式注入 - 使用useInsertionEffect
  useInsertionEffect(() => {
    const style = document.createElement('style');
    style.textContent = '.my-class { color: red; }';
    document.head.appendChild(style);
    
    return () => document.head.removeChild(style);
  }, []);
  
  // DOM操作 - 使用useLayoutEffect
  useLayoutEffect(() => {
    const element = document.querySelector('.my-class');
    if (element) {
      const rect = element.getBoundingClientRect();
      console.log('元素尺寸:', rect);
    }
  }, []);
  
  // 数据获取 - 使用useEffect
  useEffect(() => {
    fetch('/api/data').then(response => response.json()).then(setData);
  }, []);
}`,
    tags: ['使用错误', '最佳实践'],
    relatedQuestions: ['什么时候应该使用useInsertionEffect？', '如何选择合适的Effect Hook？']
  },

  {
    id: 'performance-issue',
    question: '使用useInsertionEffect后页面渲染变慢了，如何优化性能？',
    answer: `useInsertionEffect的同步执行特性可能影响渲染性能，需要采用多种优化策略：

## 🔍 性能问题诊断
- **样式复杂度**：检查注入的CSS规则是否过于复杂
- **执行频率**：确认是否因依赖变化导致频繁重新执行
- **DOM操作量**：评估样式注入的DOM操作开销
- **浏览器兼容**：某些浏览器对大量样式处理较慢

## 🚀 优化策略

### 1. 样式缓存
使用缓存避免重复生成相同样式

### 2. 批量操作
将多个样式变更合并为一次DOM操作

### 3. 依赖优化
精确控制依赖数组，避免不必要的重新执行

### 4. 样式分割
将大型样式拆分为多个小块，按需加载`,
    code: `// 🚀 性能优化示例
import { useInsertionEffect, useMemo, useRef } from 'react';

// 样式缓存
const styleCache = new Map();

function useOptimizedStyles(styleConfig, deps = []) {
  const styleRef = useRef();
  
  // 1. 缓存样式计算
  const cachedStyles = useMemo(() => {
    const cacheKey = JSON.stringify({ styleConfig, deps });
    
    if (styleCache.has(cacheKey)) {
      return styleCache.get(cacheKey);
    }
    
    const styles = generateStyles(styleConfig);
    styleCache.set(cacheKey, styles);
    return styles;
  }, [styleConfig, ...deps]);
  
  useInsertionEffect(() => {
    // 2. 批量DOM操作
    const fragment = document.createDocumentFragment();
    const style = document.createElement('style');
    style.textContent = cachedStyles;
    fragment.appendChild(style);
    
    // 3. 一次性插入
    document.head.appendChild(fragment);
    styleRef.current = style;
    
    return () => {
      if (styleRef.current && document.head.contains(styleRef.current)) {
        document.head.removeChild(styleRef.current);
      }
    };
  }, [cachedStyles]);
}

// 4. 性能监控
function useStylePerformanceMonitor() {
  useInsertionEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (duration > 5) { // 超过5ms警告
        console.warn('样式注入耗时: ' + duration.toFixed(2) + 'ms');
      }
    };
  });
}`,
    tags: ['性能问题', '优化策略'],
    relatedQuestions: ['如何监控useInsertionEffect的性能？', '样式注入的最佳实践是什么？']
  },

  {
    id: 'compatibility-issue',
    question: 'useInsertionEffect在旧版本React或某些环境中不可用，如何处理兼容性？',
    answer: `useInsertionEffect是React 18的新特性，在旧版本或特殊环境中需要兼容性处理：

## 🔧 兼容性策略

### 1. 版本检测
检测React版本，提供降级方案

### 2. Polyfill实现
为旧版本提供类似功能的实现

### 3. 条件使用
根据环境动态选择合适的Hook

### 4. 渐进增强
确保核心功能在所有环境中可用

## 🛡️ 降级方案
- React 18: 使用useInsertionEffect
- React 17: 使用useLayoutEffect
- React 16: 使用useEffect + 同步处理`,
    code: `// 兼容性处理方案
import React, { useLayoutEffect, useEffect } from 'react';

// 1. 版本检测和Polyfill
const useInsertionEffect = (() => {
  // 检测是否支持useInsertionEffect
  if (typeof React.useInsertionEffect === 'function') {
    return React.useInsertionEffect;
  }
  
  // React 17降级到useLayoutEffect
  if (typeof useLayoutEffect === 'function') {
    return useLayoutEffect;
  }
  
  // React 16降级到useEffect
  return useEffect;
})();

// 2. 兼容性Hook封装
function useCompatibleStyleInjection(styleContent, deps = []) {
  const isModernReact = typeof React.useInsertionEffect === 'function';
  
  if (isModernReact) {
    // React 18: 使用原生useInsertionEffect
    React.useInsertionEffect(() => {
      const style = document.createElement('style');
      style.textContent = styleContent;
      document.head.appendChild(style);
      
      return () => document.head.removeChild(style);
    }, deps);
  } else {
    // 旧版本: 使用同步方式模拟
    useLayoutEffect(() => {
      // 立即同步执行，模拟useInsertionEffect的时机
      const style = document.createElement('style');
      style.textContent = styleContent;
      
      // 使用insertBefore确保在其他样式之前
      const firstStyle = document.head.querySelector('style');
      if (firstStyle) {
        document.head.insertBefore(style, firstStyle);
      } else {
        document.head.appendChild(style);
      }
      
      return () => {
        if (document.head.contains(style)) {
          document.head.removeChild(style);
        }
      };
    }, deps);
  }
}

// 3. 特性检测和降级
function useStyleInjectionWithFallback(styles, deps = []) {
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // 服务端渲染兼容
  if (!isClient) {
    return;
  }
  
  // 特性检测
  if (supportsInsertionEffect()) {
    React.useInsertionEffect(() => {
      injectStyles(styles);
    }, deps);
  } else {
    // 降级方案
    useLayoutEffect(() => {
      // 尽可能早地注入样式
      const injectImmediately = () => {
        injectStyles(styles);
      };
      
      // 使用setTimeout(0)确保在下一个事件循环中执行
      const timeoutId = setTimeout(injectImmediately, 0);
      
      return () => {
        clearTimeout(timeoutId);
        removeStyles(styles);
      };
    }, deps);
  }
}

// 使用示例
function CompatibleStyledComponent() {
  const styles = '.compatible-component { background: linear-gradient(45deg, #ff6b6b, #4ecdc4); padding: 16px; border-radius: 8px; }';
  
  useCompatibleStyleInjection(styles);
  
  return (
    <div className="compatible-component">
      兼容所有React版本的样式化组件
    </div>
  );
}`,
    tags: ['兼容性问题', '版本降级'],
    relatedQuestions: ['如何检测React版本？', '旧版本React如何实现类似功能？']
  },

  {
    id: 'debugging-difficulty',
    question: '如何调试useInsertionEffect中的样式问题？样式没有生效或出现冲突怎么办？',
    answer: `调试useInsertionEffect的样式问题需要系统性的方法和工具：

## 🔍 调试策略

### 1. 样式注入检查
- 确认样式是否成功注入到DOM
- 检查样式标签的内容和位置
- 验证CSS语法的正确性

### 2. 优先级分析
- 检查CSS选择器的优先级
- 确认样式的覆盖关系
- 分析样式的加载顺序

### 3. 时机验证
- 确认useInsertionEffect的执行时机
- 检查依赖数组的变化
- 验证清理函数的执行

### 4. 开发工具使用
- 利用浏览器开发者工具
- 使用React DevTools
- 自定义调试工具`,
    code: `// 调试工具和方法
import { useInsertionEffect, useRef, useEffect } from 'react';

// 1. 样式调试Hook
function useStyleDebugger(styles, deps = [], debugName = 'unnamed') {
  const debugRef = useRef({
    injectionCount: 0,
    lastInjectedStyles: null,
    styleElement: null
  });
  
  useInsertionEffect(() => {
    const debug = debugRef.current;
    debug.injectionCount++;
    debug.lastInjectedStyles = styles;
    
    console.group('🎨 样式注入调试 - ' + debugName);
    console.log('注入次数:', debug.injectionCount);
    console.log('依赖变化:', deps);
    console.log('样式内容:', styles);
    
    // 创建带调试信息的样式元素
    const style = document.createElement('style');
    style.setAttribute('data-debug-name', debugName);
    style.setAttribute('data-injection-count', debug.injectionCount);
    style.textContent = '/* Debug: ' + debugName + ' - Injection #' + debug.injectionCount + ' */ ' + styles;
    
    document.head.appendChild(style);
    debug.styleElement = style;
    
    console.log('样式元素:', style);
    console.groupEnd();
    
    return () => {
      console.log('🧹 清理样式 - ' + debugName);
      if (style && document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, deps);
  
  // 提供调试信息
  return {
    getDebugInfo: () => debugRef.current,
    logCurrentStyles: () => {
      console.log('当前样式状态 - ' + debugName + ':', debugRef.current);
    }
  };
}

// 2. 样式注入监控器
function useStyleInjectionMonitor() {
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeName === 'STYLE') {
              console.log('🎨 检测到样式注入:', {
                content: node.textContent?.slice(0, 100) + '...',
                timestamp: new Date().toISOString()
              });
            }
          });
          
          mutation.removedNodes.forEach((node) => {
            if (node.nodeName === 'STYLE') {
              console.log('🧹 检测到样式移除:', {
                content: node.textContent?.slice(0, 100) + '...',
                timestamp: new Date().toISOString()
              });
            }
          });
        }
      });
    });
    
    observer.observe(document.head, {
      childList: true,
      subtree: true
    });
    
    return () => observer.disconnect();
  }, []);
}

// 使用示例
function DebuggableStyledComponent() {
  const styles = '.debuggable-component { background: linear-gradient(45deg, #ff6b6b, #4ecdc4); padding: 16px; border-radius: 8px; color: white; }';
  
  // 启用调试
  const { logCurrentStyles } = useStyleDebugger(styles, [], 'DebuggableComponent');
  
  // 监控样式注入
  useStyleInjectionMonitor();
  
  return (
    <div className="debuggable-component">
      <p>可调试的样式化组件</p>
      <button onClick={logCurrentStyles}>
        输出调试信息
      </button>
    </div>
  );
}`,
    tags: ['调试困难', '开发工具'],
    relatedQuestions: ['如何检查样式是否正确注入？', '样式优先级冲突如何解决？']
  },

  {
    id: 'best-practice',
    question: '使用useInsertionEffect的最佳实践有哪些？如何避免常见陷阱？',
    answer: `遵循最佳实践可以确保useInsertionEffect的正确和高效使用：

## ✅ 核心最佳实践

### 1. 专用性原则
- 仅用于样式注入，不处理其他副作用
- 保持操作轻量，避免复杂计算
- 确保同步执行不阻塞渲染

### 2. 生命周期管理
- 始终提供清理函数
- 正确处理组件卸载
- 避免样式泄漏

### 3. 性能优化
- 使用依赖数组控制重新执行
- 实现样式缓存机制
- 批量处理样式更新

### 4. 开发体验
- 添加调试信息和错误处理
- 提供清晰的API设计
- 支持TypeScript类型检查`,
    code: `// 最佳实践示例
import { useInsertionEffect, useMemo, useCallback, useRef } from 'react';

// 1. 样式管理最佳实践
function useStyledComponent(styleConfig, dependencies = []) {
  const styleRef = useRef();
  const componentId = useRef('styled-' + Math.random().toString(36).substr(2, 9));
  
  // 缓存样式计算
  const computedStyles = useMemo(() => {
    return generateOptimizedStyles(componentId.current, styleConfig);
  }, [styleConfig, ...dependencies]);
  
  useInsertionEffect(() => {
    // 检查是否已存在样式
    const existingStyle = document.getElementById(componentId.current);
    if (existingStyle) {
      existingStyle.textContent = computedStyles;
      return;
    }
    
    // 创建新样式元素
    const style = document.createElement('style');
    style.id = componentId.current;
    style.textContent = computedStyles;
    
    // 添加调试信息
    if (process.env.NODE_ENV === 'development') {
      style.setAttribute('data-component', 'styled-component');
      style.setAttribute('data-created', new Date().toISOString());
    }
    
    document.head.appendChild(style);
    styleRef.current = style;
    
    // 清理函数
    return () => {
      if (styleRef.current && document.head.contains(styleRef.current)) {
        document.head.removeChild(styleRef.current);
      }
    };
  }, [computedStyles]);
  
  return {
    className: componentId.current,
    updateStyles: useCallback((newConfig) => {
      // 提供动态更新样式的方法
      if (styleRef.current) {
        const newStyles = generateOptimizedStyles(componentId.current, newConfig);
        styleRef.current.textContent = newStyles;
      }
    }, [])
  };
}

// 2. 错误处理和边界情况
function useSafeStyleInjection(styles, deps = []) {
  useInsertionEffect(() => {
    try {
      // 验证样式内容
      if (!styles || typeof styles !== 'string') {
        console.warn('Invalid styles provided to useInsertionEffect');
        return;
      }
      
      // 创建样式元素
      const style = document.createElement('style');
      style.textContent = styles;
      document.head.appendChild(style);
      
      return () => {
        try {
          if (document.head.contains(style)) {
            document.head.removeChild(style);
          }
        } catch (error) {
          console.error('Error removing style element:', error);
        }
      };
    } catch (error) {
      console.error('Error in useInsertionEffect:', error);
    }
  }, deps);
}

// 工具函数
function generateOptimizedStyles(id, config) {
  // 样式生成逻辑
  return '.' + id + ' { /* generated styles */ }';
}`,
    tags: ['最佳实践', '性能优化'],
    relatedQuestions: ['如何设计可复用的样式Hook？', '样式注入的性能如何优化？']
  }
];

export default commonQuestions;
