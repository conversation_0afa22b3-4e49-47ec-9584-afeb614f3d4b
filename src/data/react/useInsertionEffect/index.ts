import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useInsertionEffectData: ApiItem = {
  id: 'use-insertion-effect',
  title: 'useInsertionEffect',
  description: 'useInsertionEffect是React 18中专门为CSS-in-JS库设计的Hook，在DOM变更后、useLayoutEffect执行前同步触发，确保样式注入的时机精确性',
  category: 'React Hooks',
  difficulty: 'medium',
  
  syntax: `useInsertionEffect(effect: EffectCallback, deps?: DependencyList): void;

// 完整类型定义
type EffectCallback = () => (void | (() => void | undefined));
type DependencyList = ReadonlyArray<any>;

function useInsertionEffect(
  effect: () => void | (() => void),
  deps?: React.DependencyList
): void;`,

  example: `function StyleInjectionExample() {
  // 使用useInsertionEffect注入CSS样式
  useInsertionEffect(() => {
    // 创建样式元素并注入到文档头部
    const style = document.createElement('style');
    style.textContent = \`
      .dynamic-button {
        background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        cursor: pointer;
        transition: transform 0.2s ease;
      }
      .dynamic-button:hover {
        transform: scale(1.05);
      }
    \`;
    document.head.appendChild(style);

    // 清理函数：组件卸载时移除样式
    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []); // 空依赖数组，只在组件挂载时执行一次

  return (
    <div>
      {/* 使用动态注入的样式 */}
      <button className="dynamic-button">
        动态样式按钮
      </button>
      <p>这个按钮的样式是通过useInsertionEffect动态注入的</p>
    </div>
  );
}`,

  notes: '仅适用于样式相关操作，不应用于其他类型的副作用，因为执行时机过早可能导致问题',
  
  version: 'React 18.0.0+',
  tags: ['CSS-in-JS', '样式注入', '同步执行', '性能优化', 'React 18'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useInsertionEffectData;
