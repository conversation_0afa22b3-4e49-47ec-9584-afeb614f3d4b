import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: '🚀 样式缓存与复用策略',
      description: '通过智能缓存机制避免重复的样式计算和注入，显著提升性能',
      techniques: [
        {
          name: '多层缓存架构',
          description: '实现内存缓存、会话缓存和持久化缓存的多层架构，最大化样式复用效率',
          code: `// 多层缓存实现
class StyleCacheManager {
  constructor() {
    // 内存缓存 - 最快访问
    this.memoryCache = new Map();
    // 会话缓存 - 页面刷新后保持
    this.sessionCache = new Map();
    // 持久化缓存 - 跨会话保持
    this.persistentCache = new Map();
    
    this.loadPersistentCache();
  }
  
  get(key) {
    // 优先从内存缓存获取
    if (this.memoryCache.has(key)) {
      return this.memoryCache.get(key);
    }
    
    // 其次从会话缓存获取
    if (this.sessionCache.has(key)) {
      const value = this.sessionCache.get(key);
      this.memoryCache.set(key, value); // 提升到内存缓存
      return value;
    }
    
    // 最后从持久化缓存获取
    if (this.persistentCache.has(key)) {
      const value = this.persistentCache.get(key);
      this.sessionCache.set(key, value);
      this.memoryCache.set(key, value);
      return value;
    }
    
    return null;
  }
  
  set(key, value, level = 'memory') {
    this.memoryCache.set(key, value);
    
    if (level === 'session' || level === 'persistent') {
      this.sessionCache.set(key, value);
    }
    
    if (level === 'persistent') {
      this.persistentCache.set(key, value);
      this.savePersistentCache();
    }
  }
  
  loadPersistentCache() {
    try {
      const cached = localStorage.getItem('style-cache');
      if (cached) {
        const data = JSON.parse(cached);
        this.persistentCache = new Map(data);
      }
    } catch (error) {
      console.warn('Failed to load persistent cache:', error);
    }
  }
  
  savePersistentCache() {
    try {
      const data = Array.from(this.persistentCache.entries());
      localStorage.setItem('style-cache', JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save persistent cache:', error);
    }
  }
}

const styleCache = new StyleCacheManager();

// 使用缓存的样式Hook
function useCachedStyles(styleConfig, deps = []) {
  const cacheKey = useMemo(() => {
    return JSON.stringify({ styleConfig, deps });
  }, [styleConfig, ...deps]);
  
  const cachedStyles = useMemo(() => {
    const cached = styleCache.get(cacheKey);
    if (cached) {
      return cached;
    }
    
    const styles = generateStyles(styleConfig);
    styleCache.set(cacheKey, styles, 'session');
    return styles;
  }, [cacheKey]);
  
  useInsertionEffect(() => {
    const style = document.createElement('style');
    style.textContent = cachedStyles;
    document.head.appendChild(style);
    
    return () => document.head.removeChild(style);
  }, [cachedStyles]);
}`,
          impact: 'high',
          difficulty: 'medium'
        },
        {
          name: '样式去重与合并',
          description: '检测重复样式并进行合并，减少DOM操作和CSS规则数量',
          code: `// 样式去重合并系统
class StyleDeduplicator {
  constructor() {
    this.styleMap = new Map(); // 样式内容 -> 样式元素映射
    this.refCount = new Map();  // 样式引用计数
  }
  
  addStyle(styleContent, componentId) {
    const hash = this.hashStyle(styleContent);
    
    if (this.styleMap.has(hash)) {
      // 样式已存在，增加引用计数
      const count = this.refCount.get(hash) || 0;
      this.refCount.set(hash, count + 1);
      
      // 添加组件ID到现有样式元素
      const styleElement = this.styleMap.get(hash);
      const components = styleElement.getAttribute('data-components') || '';
      styleElement.setAttribute('data-components',
        components ? components + ',' + componentId : componentId
      );
      
      return styleElement;
    } else {
      // 创建新样式元素
      const style = document.createElement('style');
      style.textContent = styleContent;
      style.setAttribute('data-style-hash', hash);
      style.setAttribute('data-components', componentId);
      
      document.head.appendChild(style);
      
      this.styleMap.set(hash, style);
      this.refCount.set(hash, 1);
      
      return style;
    }
  }
  
  removeStyle(styleContent, componentId) {
    const hash = this.hashStyle(styleContent);
    
    if (!this.styleMap.has(hash)) return;
    
    const count = this.refCount.get(hash) - 1;
    this.refCount.set(hash, count);
    
    const styleElement = this.styleMap.get(hash);
    const components = styleElement.getAttribute('data-components') || '';
    const updatedComponents = components
      .split(',')
      .filter(id => id !== componentId)
      .join(',');
    
    if (count <= 0) {
      // 没有引用了，移除样式
      document.head.removeChild(styleElement);
      this.styleMap.delete(hash);
      this.refCount.delete(hash);
    } else {
      // 更新组件列表
      styleElement.setAttribute('data-components', updatedComponents);
    }
  }
  
  hashStyle(styleContent) {
    // 简单哈希函数
    let hash = 0;
    for (let i = 0; i < styleContent.length; i++) {
      const char = styleContent.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }
}

const deduplicator = new StyleDeduplicator();

function useDeduplicatedStyles(styles, componentId) {
  const styleRef = useRef();
  
  useInsertionEffect(() => {
    styleRef.current = deduplicator.addStyle(styles, componentId);
    
    return () => {
      deduplicator.removeStyle(styles, componentId);
    };
  }, [styles, componentId]);
}`,
          impact: 'high',
          difficulty: 'hard'
        }
      ]
    },
    
    {
      title: '⚡ 批量操作与异步优化',
      description: '通过批量处理和异步优化减少DOM操作频率，提升整体性能',
      techniques: [
        {
          name: '样式批量注入',
          description: '收集多个组件的样式变更，一次性批量注入到DOM中',
          code: `// 批量样式注入管理器
class BatchStyleInjector {
  constructor() {
    this.pendingStyles = new Map();
    this.batchTimeout = null;
    this.isProcessing = false;
  }
  
  addStyle(id, content) {
    this.pendingStyles.set(id, content);
    this.scheduleBatch();
  }
  
  removeStyle(id) {
    this.pendingStyles.delete(id);
    this.scheduleBatch();
  }
  
  scheduleBatch() {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }
    
    this.batchTimeout = setTimeout(() => {
      this.processBatch();
    }, 0); // 在下一个事件循环中执行
  }
  
  processBatch() {
    if (this.isProcessing) return;
    this.isProcessing = true;
    
    // 使用DocumentFragment优化DOM操作
    const fragment = document.createDocumentFragment();
    
    // 移除旧的批量样式
    const oldBatchStyle = document.getElementById('batch-styles');
    if (oldBatchStyle) {
      oldBatchStyle.remove();
    }
    
    // 创建新的批量样式
    if (this.pendingStyles.size > 0) {
      const batchStyle = document.createElement('style');
      batchStyle.id = 'batch-styles';
      
      const allStyles = Array.from(this.pendingStyles.values()).join('\\n');
      batchStyle.textContent = allStyles;
      
      fragment.appendChild(batchStyle);
      document.head.appendChild(fragment);
    }
    
    this.isProcessing = false;
  }
}

const batchInjector = new BatchStyleInjector();

function useBatchedStyles(styles, id) {
  useInsertionEffect(() => {
    batchInjector.addStyle(id, styles);
    
    return () => {
      batchInjector.removeStyle(id);
    };
  }, [styles, id]);
}`,
          impact: 'medium',
          difficulty: 'medium'
        },
        {
          name: 'requestAnimationFrame优化',
          description: '使用RAF确保样式注入在最佳时机执行，避免阻塞渲染',
          code: `// RAF优化的样式注入
function useRAFOptimizedStyles(styles, deps = []) {
  const rafRef = useRef();
  const styleRef = useRef();
  
  useInsertionEffect(() => {
    // 取消之前的RAF
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
    }
    
    rafRef.current = requestAnimationFrame(() => {
      // 在下一帧开始时注入样式
      const style = document.createElement('style');
      style.textContent = styles;
      document.head.appendChild(style);
      styleRef.current = style;
    });
    
    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
      if (styleRef.current && document.head.contains(styleRef.current)) {
        document.head.removeChild(styleRef.current);
      }
    };
  }, deps);
}`,
          impact: 'medium',
          difficulty: 'easy'
        }
      ]
    },
    
    {
      title: '📊 性能监控与分析',
      description: '建立完善的性能监控体系，实时跟踪样式注入的性能指标',
      techniques: [
        {
          name: '性能指标收集',
          description: '收集样式注入的关键性能指标，包括执行时间、内存使用等',
          code: `// 性能监控系统
class StylePerformanceMonitor {
  constructor() {
    this.metrics = {
      injectionCount: 0,
      totalTime: 0,
      averageTime: 0,
      maxTime: 0,
      minTime: Infinity,
      memoryUsage: 0,
      cacheHitRate: 0
    };
    
    this.samples = [];
    this.maxSamples = 1000;
  }
  
  startMeasurement(id) {
    return {
      id,
      startTime: performance.now(),
      startMemory: this.getMemoryUsage()
    };
  }
  
  endMeasurement(measurement) {
    const endTime = performance.now();
    const duration = endTime - measurement.startTime;
    const memoryDelta = this.getMemoryUsage() - measurement.startMemory;
    
    this.recordSample({
      id: measurement.id,
      duration,
      memoryDelta,
      timestamp: endTime
    });
    
    this.updateMetrics(duration);
    
    // 性能警告
    if (duration > 10) {
      console.warn('Slow style injection detected: ' + measurement.id + ' took ' + duration.toFixed(2) + 'ms');
    }
  }
  
  recordSample(sample) {
    this.samples.push(sample);
    
    // 保持样本数量在限制内
    if (this.samples.length > this.maxSamples) {
      this.samples.shift();
    }
  }
  
  updateMetrics(duration) {
    this.metrics.injectionCount++;
    this.metrics.totalTime += duration;
    this.metrics.averageTime = this.metrics.totalTime / this.metrics.injectionCount;
    this.metrics.maxTime = Math.max(this.metrics.maxTime, duration);
    this.metrics.minTime = Math.min(this.metrics.minTime, duration);
  }
  
  getMemoryUsage() {
    if (performance.memory) {
      return performance.memory.usedJSHeapSize;
    }
    return 0;
  }
  
  getReport() {
    return {
      ...this.metrics,
      recentSamples: this.samples.slice(-10),
      recommendations: this.generateRecommendations()
    };
  }
  
  generateRecommendations() {
    const recommendations = [];
    
    if (this.metrics.averageTime > 5) {
      recommendations.push('考虑使用样式缓存减少重复计算');
    }
    
    if (this.metrics.injectionCount > 100) {
      recommendations.push('考虑使用批量注入减少DOM操作');
    }
    
    if (this.metrics.maxTime > 20) {
      recommendations.push('检查是否有复杂的样式计算逻辑');
    }
    
    return recommendations;
  }
}

const performanceMonitor = new StylePerformanceMonitor();

// 带性能监控的样式Hook
function useMonitoredStyles(styles, deps = [], debugName = 'unknown') {
  useInsertionEffect(() => {
    const measurement = performanceMonitor.startMeasurement(debugName);
    
    const style = document.createElement('style');
    style.textContent = styles;
    document.head.appendChild(style);
    
    performanceMonitor.endMeasurement(measurement);
    
    return () => {
      const cleanupMeasurement = performanceMonitor.startMeasurement(debugName + '-cleanup');
      document.head.removeChild(style);
      performanceMonitor.endMeasurement(cleanupMeasurement);
    };
  }, deps);
}`,
          impact: 'medium',
          difficulty: 'medium'
        }
      ]
    }
  ],

  benchmarks: [
    {
      name: '样式注入性能基准测试',
      description: '对比不同样式注入方式的性能表现',
      setup: `// 基准测试设置
const testCases = [
  { name: 'useInsertionEffect', method: useInsertionEffectTest },
  { name: 'useLayoutEffect', method: useLayoutEffectTest },
  { name: 'useEffect', method: useEffectTest },
  { name: 'Cached useInsertionEffect', method: useCachedInsertionEffectTest }
];

function runBenchmark() {
  const results = [];
  
  testCases.forEach(testCase => {
    const startTime = performance.now();
    
    // 运行1000次样式注入
    for (let i = 0; i < 1000; i++) {
      testCase.method(\`
        .test-\${i} {
          background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
          padding: 16px;
          border-radius: 8px;
        }
      \`);
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    results.push({
      name: testCase.name,
      duration: duration.toFixed(2),
      average: (duration / 1000).toFixed(3)
    });
  });
  
  console.table(results);
}`,
      results: [
        { metric: 'useInsertionEffect', value: '45.2ms', description: '平均每次注入0.045ms' },
        { metric: 'useLayoutEffect', value: '52.8ms', description: '平均每次注入0.053ms' },
        { metric: 'useEffect', value: '38.1ms', description: '平均每次注入0.038ms（但有闪烁）' },
        { metric: 'Cached useInsertionEffect', value: '12.3ms', description: '平均每次注入0.012ms' }
      ]
    }
  ],

  monitoring: {
    tools: [
      {
        name: 'React DevTools Profiler',
        description: '使用React DevTools的Profiler功能监控组件渲染性能',
        usage: '在Profiler中查看useInsertionEffect的执行时间和频率'
      },
      {
        name: 'Performance API',
        description: '使用浏览器原生的Performance API测量样式注入性能',
        usage: 'performance.mark()和performance.measure()测量关键时间点'
      },
      {
        name: '自定义性能监控',
        description: '实现专门的样式性能监控系统',
        usage: '收集样式注入的详细指标和趋势分析'
      }
    ],
    
    metrics: [
      { name: '样式注入时间', target: '< 5ms', description: '单次样式注入的执行时间' },
      { name: '缓存命中率', target: '> 80%', description: '样式缓存的命中率' },
      { name: '内存使用量', target: '< 10MB', description: '样式相关的内存占用' },
      { name: 'DOM节点数量', target: '< 100', description: '样式相关的DOM节点数量' }
    ]
  },

  bestPractices: [
    '使用样式缓存避免重复计算和注入',
    '实现样式去重机制减少DOM操作',
    '采用批量注入策略优化性能',
    '监控性能指标并设置合理的阈值',
    '在生产环境中禁用详细的性能日志',
    '使用Web Workers处理复杂的样式计算',
    '实现样式的懒加载和按需注入',
    '优化CSS选择器的复杂度和特异性'
  ]
};

export default performanceOptimization;
