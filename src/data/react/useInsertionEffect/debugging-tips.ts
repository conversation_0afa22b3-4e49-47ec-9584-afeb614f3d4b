import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  commonIssues: [
    {
      issue: '样式没有生效或被覆盖',
      symptoms: [
        '组件渲染后样式不显示',
        '样式在开发者工具中可见但未应用',
        '样式被其他CSS规则覆盖'
      ],
      causes: [
        'CSS选择器优先级不足',
        '样式注入时机过晚',
        '样式语法错误',
        '样式被其他样式覆盖'
      ],
      solutions: [
        '检查CSS选择器的特异性和优先级',
        '确认useInsertionEffect的执行时机',
        '验证CSS语法的正确性',
        '使用!important或提高选择器优先级'
      ],
      code: `// 调试样式优先级问题
function debugStylePriority(selector) {
  const elements = document.querySelectorAll(selector);
  
  elements.forEach((element, index) => {
    console.group(\`🎯 样式调试 - 元素 \${index + 1}\`);
    
    const computedStyles = window.getComputedStyle(element);
    const appliedRules = [];
    
    // 获取所有应用的CSS规则
    for (let sheet of document.styleSheets) {
      try {
        for (let rule of sheet.cssRules) {
          if (rule.selectorText && element.matches(rule.selectorText)) {
            appliedRules.push({
              selector: rule.selectorText,
              specificity: calculateSpecificity(rule.selectorText),
              styles: rule.style.cssText,
              source: sheet.href || 'inline'
            });
          }
        }
      } catch (e) {
        console.warn('无法访问样式表:', sheet.href);
      }
    }
    
    // 按优先级排序
    appliedRules.sort((a, b) => b.specificity - a.specificity);
    
    console.log('应用的样式规则（按优先级排序）:');
    console.table(appliedRules);
    
    console.log('最终计算样式:');
    console.log({
      background: computedStyles.background,
      color: computedStyles.color,
      padding: computedStyles.padding,
      margin: computedStyles.margin
    });
    
    console.groupEnd();
  });
}

// 计算CSS选择器优先级
function calculateSpecificity(selector) {
  const idCount = (selector.match(/#/g) || []).length;
  const classCount = (selector.match(/\\./g) || []).length;
  const elementCount = (selector.match(/[a-zA-Z]/g) || []).length;
  
  return idCount * 100 + classCount * 10 + elementCount;
}`
    },
    
    {
      issue: 'useInsertionEffect执行时机异常',
      symptoms: [
        '样式注入过早或过晚',
        '与useLayoutEffect执行顺序混乱',
        '在某些条件下不执行'
      ],
      causes: [
        '依赖数组配置错误',
        'React版本不支持useInsertionEffect',
        '组件渲染时机问题',
        '条件渲染导致Hook跳过'
      ],
      solutions: [
        '检查依赖数组的正确性',
        '确认React版本 >= 18',
        '验证组件的渲染流程',
        '避免条件性调用Hook'
      ],
      code: `// 调试执行时机问题
function useInsertionEffectDebugger(effect, deps, debugName = 'unknown') {
  const renderCount = useRef(0);
  const executionCount = useRef(0);
  
  renderCount.current++;
  
  useInsertionEffect(() => {
    executionCount.current++;
    
    console.group(\`🔍 useInsertionEffect调试 - \${debugName}\`);
    console.log('渲染次数:', renderCount.current);
    console.log('执行次数:', executionCount.current);
    console.log('依赖项:', deps);
    console.log('执行时间:', new Date().toISOString());
    
    // 检查React版本
    console.log('React版本:', React.version);
    
    // 检查是否支持useInsertionEffect
    console.log('支持useInsertionEffect:', typeof React.useInsertionEffect === 'function');
    
    const result = effect();
    
    console.log('Effect执行完成');
    console.groupEnd();
    
    return () => {
      console.log(\`🧹 清理函数执行 - \${debugName}\`);
      if (typeof result === 'function') {
        result();
      }
    };
  }, deps);
}`
    }
  ],

  debuggingTools: [
    {
      name: 'React DevTools',
      description: 'React官方开发者工具，用于检查组件状态和Hook执行',
      usage: `// 在组件中添加调试信息
function DebuggableComponent() {
  // 使用React DevTools的useDebugValue
  useDebugValue('样式注入组件');
  
  const [styleState, setStyleState] = useState('initial');
  
  useInsertionEffect(() => {
    // 在DevTools中可以看到这个Hook的执行
    setStyleState('injected');
    
    const style = document.createElement('style');
    style.textContent = '.debug-component { color: red; }';
    document.head.appendChild(style);
    
    return () => {
      setStyleState('cleaned');
      document.head.removeChild(style);
    };
  }, []);
  
  return <div className="debug-component">调试组件</div>;
}`,
      tips: [
        '在Profiler中查看Hook的执行时间',
        '使用Components面板检查Hook状态',
        '观察Hook的执行顺序和依赖变化'
      ]
    },
    
    {
      name: '浏览器开发者工具',
      description: '使用浏览器内置的开发者工具调试样式问题',
      usage: `// 样式注入监控器
function createStyleMonitor() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeName === 'STYLE') {
            console.log('🎨 样式添加:', {
              content: node.textContent?.slice(0, 100) + '...',
              timestamp: new Date().toISOString(),
              attributes: Array.from(node.attributes).map(attr => 
                \`\${attr.name}="\${attr.value}"\`
              )
            });
          }
        });
        
        mutation.removedNodes.forEach((node) => {
          if (node.nodeName === 'STYLE') {
            console.log('🗑️ 样式移除:', {
              content: node.textContent?.slice(0, 100) + '...',
              timestamp: new Date().toISOString()
            });
          }
        });
      }
    });
  });
  
  observer.observe(document.head, {
    childList: true,
    subtree: true
  });
  
  return () => observer.disconnect();
}

// 在应用启动时启用监控
if (process.env.NODE_ENV === 'development') {
  createStyleMonitor();
}`,
      tips: [
        '使用Elements面板检查样式元素',
        '在Console中运行调试脚本',
        '使用Performance面板分析渲染性能',
        '利用Network面板检查样式资源加载'
      ]
    },
    
    {
      name: '自定义调试Hook',
      description: '创建专门的调试Hook来跟踪样式注入过程',
      usage: `// 全功能样式调试Hook
function useStyleDebugger(styles, deps = [], options = {}) {
  const {
    enableLogging = true,
    enablePerformanceTracking = true,
    enableErrorBoundary = true,
    debugName = 'unnamed-style'
  } = options;
  
  const debugInfo = useRef({
    renderCount: 0,
    injectionCount: 0,
    errors: [],
    performanceData: []
  });
  
  debugInfo.current.renderCount++;
  
  useInsertionEffect(() => {
    if (!enableLogging && !enablePerformanceTracking) {
      // 生产模式，直接注入样式
      const style = document.createElement('style');
      style.textContent = styles;
      document.head.appendChild(style);
      return () => document.head.removeChild(style);
    }
    
    const startTime = performance.now();
    debugInfo.current.injectionCount++;
    
    try {
      if (enableLogging) {
        console.group(\`🎨 样式调试 - \${debugName}\`);
        console.log('渲染次数:', debugInfo.current.renderCount);
        console.log('注入次数:', debugInfo.current.injectionCount);
        console.log('依赖项:', deps);
        console.log('样式内容长度:', styles.length);
      }
      
      // 验证样式语法
      if (enableErrorBoundary && !isValidCSS(styles)) {
        throw new Error('Invalid CSS syntax detected');
      }
      
      const style = document.createElement('style');
      style.setAttribute('data-debug-name', debugName);
      style.setAttribute('data-injection-count', debugInfo.current.injectionCount);
      style.textContent = styles;
      
      document.head.appendChild(style);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (enablePerformanceTracking) {
        debugInfo.current.performanceData.push({
          timestamp: endTime,
          duration,
          stylesLength: styles.length
        });
        
        if (enableLogging) {
          console.log(\`⏱️ 注入耗时: \${duration.toFixed(2)}ms\`);
          
          if (duration > 5) {
            console.warn('⚠️ 样式注入耗时过长');
          }
        }
      }
      
      if (enableLogging) {
        console.log('✅ 样式注入成功');
        console.groupEnd();
      }
      
      return () => {
        if (enableLogging) {
          console.log(\`🧹 清理样式 - \${debugName}\`);
        }
        
        if (document.head.contains(style)) {
          document.head.removeChild(style);
        }
      };
      
    } catch (error) {
      debugInfo.current.errors.push({
        timestamp: Date.now(),
        error: error.message,
        styles: styles.slice(0, 200)
      });
      
      if (enableLogging) {
        console.error('❌ 样式注入失败 - ' + debugName + ':', error);
        console.groupEnd();
      }
      
      if (!enableErrorBoundary) {
        throw error;
      }
    }
  }, deps);
  
  // 返回调试信息
  return {
    getDebugInfo: () => debugInfo.current,
    logPerformanceReport: () => {
      if (debugInfo.current.performanceData.length > 0) {
        const avgDuration = debugInfo.current.performanceData
          .reduce((sum, data) => sum + data.duration, 0) / 
          debugInfo.current.performanceData.length;
        
        console.table({
          '组件名称': debugName,
          '总注入次数': debugInfo.current.injectionCount,
          '平均耗时': avgDuration.toFixed(2) + 'ms',
          '错误次数': debugInfo.current.errors.length
        });
      }
    }
  };
}

// CSS语法验证函数
function isValidCSS(css) {
  try {
    const style = document.createElement('style');
    style.textContent = css;
    return true;
  } catch {
    return false;
  }
}`,
      tips: [
        '在开发环境启用详细日志',
        '生产环境关闭调试功能',
        '定期检查性能报告',
        '监控错误率和异常情况'
      ]
    }
  ],

  troubleshooting: [
    {
      problem: '样式闪烁或延迟显示',
      diagnosis: [
        '检查是否使用了useEffect而非useInsertionEffect',
        '确认样式注入的时机是否正确',
        '验证CSS规则的复杂度'
      ],
      solution: `// 解决样式闪烁问题
function useFlickerFreeStyles(styles, deps = []) {
  // 使用useInsertionEffect确保在布局前注入
  useInsertionEffect(() => {
    const style = document.createElement('style');
    
    // 添加样式标识，便于调试
    style.setAttribute('data-flicker-free', 'true');
    style.textContent = styles;
    
    // 确保样式在其他样式之前
    const firstStyle = document.head.querySelector('style');
    if (firstStyle) {
      document.head.insertBefore(style, firstStyle);
    } else {
      document.head.appendChild(style);
    }
    
    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, deps);
}`,
      prevention: [
        '始终使用useInsertionEffect进行样式注入',
        '避免在样式中使用复杂的计算',
        '预加载关键样式',
        '使用CSS变量减少动态计算'
      ]
    },
    
    {
      problem: '内存泄漏和性能问题',
      diagnosis: [
        '检查是否正确清理样式元素',
        '确认没有重复注入相同样式',
        '验证依赖数组的正确性'
      ],
      solution: `// 防止内存泄漏的样式管理
function useMemoryEfficientStyles(styles, deps = []) {
  const styleRef = useRef();
  const cleanupRef = useRef();
  
  useInsertionEffect(() => {
    // 清理之前的样式
    if (cleanupRef.current) {
      cleanupRef.current();
    }
    
    const style = document.createElement('style');
    style.textContent = styles;
    document.head.appendChild(style);
    
    styleRef.current = style;
    
    // 设置清理函数
    cleanupRef.current = () => {
      if (style && document.head.contains(style)) {
        document.head.removeChild(style);
      }
      styleRef.current = null;
    };
    
    return cleanupRef.current;
  }, deps);
  
  // 组件卸载时确保清理
  useEffect(() => {
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
      }
    };
  }, []);
}`,
      prevention: [
        '始终提供清理函数',
        '使用WeakMap存储样式引用',
        '实现样式去重机制',
        '定期检查DOM中的样式元素数量'
      ]
    }
  ],

  bestPractices: [
    '在开发环境启用详细的调试日志',
    '使用有意义的调试名称标识样式',
    '实现样式注入的性能监控',
    '建立样式问题的自动化测试',
    '使用TypeScript提供类型安全',
    '创建样式调试的开发者工具',
    '定期审查和清理无用的样式代码',
    '建立样式问题的错误报告机制'
  ]
};

export default debuggingTips;
