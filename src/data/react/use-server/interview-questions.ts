import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',
    
    id: 1,
    question: "什么是'use server'指令？它解决了什么问题？",
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: "'use server'是React Server Components中的指令，用于标记服务器端执行的函数",
      detailed: `'use server'是React Server Components架构中的核心指令，主要解决以下问题：

1. **安全性问题**：将敏感逻辑（如数据库操作、API密钥使用）限制在服务器端执行
2. **性能问题**：减少客户端JavaScript包大小，提升首屏加载速度
3. **开发体验**：提供类型安全的全栈开发体验，无需手动创建API端点
4. **数据一致性**：确保数据操作在服务器端进行，避免客户端数据不一致

它通过编译时转换，将标记的函数转换为可从客户端调用的服务器端点。`,
      code: `'use server';

export async function createUser(formData: FormData) {
  // 这个函数在服务器端执行
  const name = formData.get('name') as string;

  // 可以安全地访问服务器资源
  const user = await db.user.create({
    data: { name }
  });

  return { success: true, user };
}

// 客户端可以直接调用
// <form action={createUser}>...</form>`
    },
    tags: ['基础概念', 'RSC']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',

    id: 2,
    question: "如何在'use server'函数中处理错误和验证？",
    difficulty: 'medium',
    frequency: 'high',
    category: '错误处理',
    answer: {
      brief: "使用try-catch捕获异常，返回统一的错误格式，避免敏感信息泄露",
      detailed: `在'use server'函数中处理错误需要考虑多个层面：

**1. 输入验证**：
- 验证FormData或参数的有效性
- 使用类型检查和业务规则验证

**2. 异常处理**：
- 使用try-catch捕获数据库和网络错误
- 记录详细错误信息用于调试
- 返回用户友好的错误消息

**3. 安全考虑**：
- 不要在错误消息中暴露敏感信息
- 使用统一的错误响应格式
- 实现适当的日志记录

**4. 类型安全**：
- 定义明确的返回类型
- 使用TypeScript确保错误处理的一致性`,
      code: `'use server';

type ActionResult<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: string;
  code?: string;
};

export async function createUser(
  formData: FormData
): Promise<ActionResult<User>> {
  try {
    // 输入验证
    const email = formData.get('email') as string;
    if (!email || !email.includes('@')) {
      return {
        success: false,
        error: '请输入有效的邮箱地址',
        code: 'INVALID_EMAIL'
      };
    }

    // 业务逻辑
    const user = await db.user.create({
      data: { email }
    });

    return { success: true, data: user };
  } catch (error) {
    // 记录详细错误但不暴露给客户端
    console.error('创建用户失败:', error);

    return {
      success: false,
      error: '创建用户失败，请稍后重试',
      code: 'CREATE_FAILED'
    };
  }
}`
    },
    tags: ['错误处理', '类型安全']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',

    id: 3,
    question: "如何优化'use server'函数的性能？",
    difficulty: 'hard',
    frequency: 'medium',
    category: '性能优化',
    answer: {
      brief: "通过缓存策略、批量操作、连接池和智能数据获取来优化性能",
      detailed: `优化'use server'函数性能的关键策略：

**1. 缓存策略**：
- 使用Next.js的缓存API（revalidateTag, revalidatePath）
- 实现Redis或内存缓存
- 合理设置缓存过期时间

**2. 数据库优化**：
- 使用连接池减少连接开销
- 实现批量操作减少数据库查询次数
- 优化SQL查询和索引

**3. 网络优化**：
- 减少函数调用频率
- 实现请求去重和防抖
- 使用流式响应处理大数据

**4. 代码优化**：
- 避免在服务器函数中执行重计算
- 使用异步操作和并行处理
- 实现适当的错误恢复机制`,
      code: `'use server';

import { unstable_cache } from 'next/cache';
import { redis } from '@/lib/redis';

// 缓存包装器
const getCachedUser = unstable_cache(
  async (id: string) => {
    return await db.user.findUnique({ where: { id } });
  },
  ['user'],
  { revalidate: 3600 } // 1小时缓存
);

// 批量操作优化
export async function batchUpdateUsers(updates: UserUpdate[]) {
  try {
    // 使用事务批量处理
    const results = await db.$transaction(
      updates.map(update =>
        db.user.update({
          where: { id: update.id },
          data: update.data
        })
      )
    );

    // 批量清除缓存
    const userIds = updates.map(u => u.id);
    await redis.del(...userIds.map(id => \`user:\${id}\`));

    return { success: true, results };
  } catch (error) {
    return { success: false, error: '批量更新失败' };
  }
}

// 流式数据处理
export async function* streamLargeDataset(query: string) {
  const batchSize = 100;
  let offset = 0;

  while (true) {
    const batch = await db.data.findMany({
      where: { query },
      take: batchSize,
      skip: offset
    });

    if (batch.length === 0) break;

    yield batch;
    offset += batchSize;
  }
}`
    },
    tags: ['性能优化', '缓存策略']
  }
];

// 'use server' API 面试准备完成
export default interviewQuestions;