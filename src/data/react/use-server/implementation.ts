import { Implementation } from '@/types/api';

const implementation: Implementation = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '内容已完成',

  mechanism: `'use server'指令的实现机制基于React Server Components架构：

1. **编译时处理**：构建工具识别'use server'指令，将标记的函数提取为服务器端点
2. **函数序列化**：服务器函数被转换为可调用的网络端点，生成唯一标识符
3. **客户端代理**：在客户端生成代理函数，封装网络请求逻辑
4. **数据序列化**：自动处理参数和返回值的序列化/反序列化
5. **安全边界**：确保服务器函数只能在服务器环境中执行`,

  visualization: `graph TD
    A["客户端调用"] --> B["代理函数"]
    B --> C["网络请求"]
    C --> D["服务器端点"]
    D --> E["执行服务器函数"]
    E --> F["返回结果"]
    F --> G["序列化响应"]
    G --> H["客户端接收"]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#f1f8e9
    style G fill:#fff8e1
    style H fill:#e8eaf6`,

  plainExplanation: `想象'use server'就像是在客户端和服务器之间建立了一个"魔法传送门"。当你在客户端调用一个标记了'use server'的函数时，这个调用会被自动"传送"到服务器上执行，然后结果再"传送"回来。整个过程对开发者来说是透明的，就像调用本地函数一样简单，但实际上是在远程服务器上运行的。`,

  designConsiderations: [
    '安全性：服务器函数暴露给客户端，需要严格的输入验证和权限检查',
    '序列化限制：只能传递和返回可序列化的数据，不支持函数、DOM元素等',
    '性能考虑：每次调用都是网络请求，需要合理设计函数粒度',
    '错误处理：需要优雅处理网络错误和服务器异常',
    '类型安全：利用TypeScript确保客户端和服务器端的类型一致性'
  ],

  relatedConcepts: [
    'React Server Components：\'use server\'是RSC架构的核心组成部分',
    'Server Actions：Next.js中\'use server\'函数的具体实现形式',
    'Form Actions：HTML表单与服务器函数的原生集成',
    'Streaming：服务器函数支持流式响应和渐进式加载'
  ]
};

// 'use server' API 实现原理完成
export default implementation;