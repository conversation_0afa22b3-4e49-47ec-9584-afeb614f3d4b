import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '内容已完成',
  
  introduction: `探索'use server'指令的历史背景和技术演进，理解其在全栈Web开发中的革命性意义。`,

  background: `'use server'的诞生源于React团队对现代Web应用架构的深度思考。传统的客户端-服务器分离模式虽然清晰，但在开发体验和性能优化方面存在诸多挑战。`,

  evolution: `从传统的API端点到React Server Components，再到'use server'指令，代表了Web开发从"分离式"向"统一式"架构的重大转变。`,

  timeline: [
    {
      year: '2020',
      event: 'React Server Components RFC发布',
      description: 'React团队首次提出服务器组件概念，为\'use server\'奠定理论基础',
      significance: '标志着React架构思维的重大转变，从纯客户端向全栈发展'
    },
    {
      year: '2022',
      event: 'Next.js 13 App Router发布',
      description: '首次在生产环境中实现\'use server\'指令和Server Actions',
      significance: '将理论概念转化为可用的开发工具，推动全栈React发展'
    }
  ],

  keyFigures: [
    {
      name: '{NAME_1}',
      role: '{ROLE_1}',
      contribution: '{CONTRIBUTION_1}',
      significance: '{SIGNIFICANCE_1}'
    }
  ],

  concepts: [
    {
      term: '{TERM_1}',
      definition: '{DEFINITION_1}',
      evolution: '{EVOLUTION_1}',
      modernRelevance: '{MODERN_RELEVANCE_1}'
    }
  ],

  designPhilosophy: `{DESIGN_PHILOSOPHY}`,

  impact: `{IMPACT}`,

  modernRelevance: `{MODERN_RELEVANCE}`
};

// 占位内容，具体内容请参考 @6-知识考古.mdc
export default knowledgeArchaeology;