import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '内容已完成',

  definition: "'use server'是React Server Components中用于标记服务器函数的指令",

  introduction: `'use server'是React Server Components引入的指令，主要用于标记服务器端函数、实现全栈开发和无缝的客户端-服务器通信。它采用指令式标记的设计模式，提供了类型安全的服务器函数调用机制。`,

  syntax: `'use server';

// 文件级别使用
'use server';
export async function serverAction(formData: FormData) {
  // 服务器端逻辑
}

// 函数级别使用
export async function myAction() {
  'use server';
  // 服务器端逻辑
}`,

  quickExample: `// actions.ts
'use server';

export async function createUser(formData: FormData) {
  const name = formData.get('name') as string;
  const email = formData.get('email') as string;

  // 服务器端数据库操作
  const user = await db.user.create({
    data: { name, email }
  });

  return { success: true, user };
}

// UserForm.tsx
'use client';
import { createUser } from './actions';

export default function UserForm() {
  return (
    <form action={createUser}>
      <input name="name" placeholder="姓名" required />
      <input name="email" type="email" placeholder="邮箱" required />
      <button type="submit">创建用户</button>
    </form>
  );
}`,

  scenarioDiagram: `graph TD
    A[use server 应用场景] --> B[表单处理]
    A --> C[API端点]
    A --> D[数据操作]

    B --> B1[用户注册]
    B --> B2[文件上传]
    B --> B3[数据提交]

    C --> C1[RESTful API]
    C --> C2[GraphQL端点]
    C --> C3[Webhook处理]

    D --> D1[数据库操作]
    D --> D2[文件系统]
    D --> D3[外部服务调用]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0`,
  
  parameters: [
    {
      name: "无参数",
      type: "指令",
      required: true,
      description: "'use server'是一个指令，不接受参数，用于标记函数或文件为服务器端执行",
      example: "'use server';"
    }
  ],

  returnValue: {
    type: "void",
    description: "'use server'指令本身不返回值，它标记的函数可以返回任何类型的数据",
    example: "// 标记的函数可以返回Promise<any>或任何序列化数据"
  },
  
  keyFeatures: [
    {
      title: "服务器端执行",
      description: "标记的函数在服务器端执行，可以访问服务器资源如数据库、文件系统等",
      benefit: "提供安全的服务器端逻辑执行环境"
    },
    {
      title: "类型安全",
      description: "与TypeScript完美集成，提供端到端的类型安全保障",
      benefit: "减少运行时错误，提升开发体验"
    },
    {
      title: "自动序列化",
      description: "自动处理客户端和服务器端之间的数据序列化和反序列化",
      benefit: "简化数据传输，无需手动处理JSON转换"
    }
  ],

  limitations: [
    "只能在支持React Server Components的框架中使用（如Next.js App Router）",
    "标记的函数必须是异步函数，返回Promise",
    "传递的参数和返回值必须是可序列化的数据"
  ],

  bestPractices: [
    "将'use server'放在文件或函数的第一行",
    "保持服务器函数的纯净性，避免副作用",
    "使用TypeScript确保类型安全",
    "合理处理错误和异常情况",
    "避免在服务器函数中返回敏感信息"
  ],

  warnings: [
    "服务器函数会暴露给客户端，注意安全性",
    "避免在服务器函数中执行长时间运行的操作",
    "确保服务器函数的参数和返回值可序列化"
  ]
};

// 'use server' API 基本信息完成
export default basicInfo;