import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useserverData: ApiItem = {
  id: 'use-server',
  title: "'use server'",
  description: 'React Server Components指令，用于标记服务器函数，实现安全的服务器端逻辑执行',
  category: 'React Directives',
  difficulty: 'medium',

  syntax: `'use server';`,
  example: `'use server';
export async function createUser(formData: FormData) {
  // 服务器端逻辑
  const user = await db.user.create({
    data: { name: formData.get('name') }
  });
  return { success: true, user };
}`,
  notes: '只能在支持React Server Components的框架中使用，如Next.js App Router',

  version: 'React Server Components',
  tags: ["React", "Server Components", "Full Stack"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useserverData;