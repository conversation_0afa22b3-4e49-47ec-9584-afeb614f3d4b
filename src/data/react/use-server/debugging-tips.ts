import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: '调试use server函数时的常见问题和解决方案',
        sections: [
          {
            title: '序列化错误',
            description: '服务器函数参数和返回值的序列化问题',
            items: [
              {
                title: '函数参数无法序列化',
                description: '尝试传递函数、DOM元素或其他不可序列化的数据',
                solution: '只传递可序列化的数据类型（字符串、数字、对象等）',
                prevention: '使用TypeScript定义明确的参数类型',
                code: `// ❌ 错误：传递函数
const callback = () => console.log('test');
await serverAction(callback);

// ✅ 正确：传递可序列化数据
await serverAction({
  type: 'callback',
  data: 'test'
});`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '{INTRODUCTION}',
        sections: [
          {
            title: '{SECTION_2_TITLE}',
            description: '{SECTION_2_DESCRIPTION}',
            items: [
              {
                title: '{ITEM_2_TITLE}',
                description: '{ITEM_2_DESCRIPTION}',
                solution: '{ITEM_2_SOLUTION}',
                prevention: '{ITEM_2_PREVENTION}',
                code: `{ITEM_2_CODE}`
              }
            ]
          }
        ]
      }
    }
  ]
};

// 占位内容，具体内容请参考 @8-调试技巧.mdc
export default debuggingTips;