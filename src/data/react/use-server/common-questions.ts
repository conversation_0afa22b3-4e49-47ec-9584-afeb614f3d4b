import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',

    id: 'question-1',
    question: "'use server'和'use client'有什么区别？",
    answer: `'use server'和'use client'是React Server Components中的两个互补指令：

**'use server'**：
- 标记函数在服务器端执行
- 可以访问服务器资源（数据库、文件系统等）
- 用于处理敏感逻辑和数据操作
- 函数会被序列化并可从客户端调用

**'use client'**：
- 标记组件在客户端执行
- 可以使用浏览器API和React Hooks
- 用于处理用户交互和客户端状态
- 创建客户端边界

两者配合使用可以实现最佳的性能和安全性。`,
    code: `// 服务器函数
'use server';
export async function saveData(data: FormData) {
  // 在服务器端执行
  await db.save(data);
}

// 客户端组件
'use client';
import { saveData } from './actions';
export default function Form() {
  // 在客户端执行
  return <form action={saveData}>...</form>;
}`,
    tags: ['指令对比', '架构设计'],
    relatedQuestions: ['如何选择使用哪个指令？', 'RSC架构的最佳实践是什么？']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',

    id: 'question-2',
    question: "'use server'函数可以返回什么类型的数据？",
    answer: `'use server'函数可以返回任何可序列化的数据类型：

**支持的类型**：
- 基本类型：string, number, boolean, null, undefined
- 对象和数组（包含可序列化属性）
- Date对象（会被序列化为字符串）
- Promise（异步函数的返回值）

**不支持的类型**：
- 函数
- Symbol
- 包含循环引用的对象
- DOM元素
- 类实例（除非实现了序列化方法）

建议返回简单的数据结构，避免复杂的对象关系。`,
    code: `'use server';

// ✅ 支持的返回类型
export async function getData() {
  return {
    user: { id: 1, name: 'John' },
    posts: [{ id: 1, title: 'Hello' }],
    timestamp: new Date(),
    success: true
  };
}

// ❌ 不支持的返回类型
export async function getBadData() {
  return {
    callback: () => {}, // 函数不可序列化
    element: document.body, // DOM元素不可序列化
    symbol: Symbol('test') // Symbol不可序列化
  };
}`,
    tags: ['数据类型', '序列化'],
    relatedQuestions: ['如何处理复杂对象的序列化？', '如何传递函数给服务器？']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '内容已完成',

    id: 'question-3',
    question: "如何在'use server'函数中处理错误？",
    answer: `在'use server'函数中处理错误需要考虑多个层面：

**1. 服务器端错误处理**：
- 使用try-catch捕获异常
- 返回错误状态而不是抛出异常
- 记录错误日志用于调试

**2. 客户端错误处理**：
- 检查返回值中的错误状态
- 提供用户友好的错误信息
- 实现重试机制

**3. 类型安全**：
- 使用TypeScript定义错误类型
- 统一错误响应格式

建议使用统一的错误处理模式，确保错误信息不会泄露敏感数据。`,
    code: `'use server';

// 统一的响应类型
type ActionResult<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: string;
  code?: string;
};

export async function createUser(formData: FormData): Promise<ActionResult<User>> {
  try {
    const email = formData.get('email') as string;

    // 验证输入
    if (!email || !email.includes('@')) {
      return {
        success: false,
        error: '请输入有效的邮箱地址',
        code: 'INVALID_EMAIL'
      };
    }

    // 数据库操作
    const user = await db.user.create({
      data: { email }
    });

    return {
      success: true,
      data: user
    };
  } catch (error) {
    // 记录错误但不暴露给客户端
    console.error('创建用户失败:', error);

    return {
      success: false,
      error: '创建用户失败，请稍后重试',
      code: 'CREATE_FAILED'
    };
  }
}

// 客户端使用
async function handleSubmit(formData: FormData) {
  const result = await createUser(formData);

  if (result.success) {
    console.log('用户创建成功:', result.data);
  } else {
    console.error('错误:', result.error);
    // 根据错误代码显示不同的提示
    if (result.code === 'INVALID_EMAIL') {
      setEmailError(result.error);
    }
  }
}`,
    tags: ['错误处理', '类型安全'],
    relatedQuestions: ['如何实现全局错误处理？', '如何避免错误信息泄露？']
  }
];

// 'use server' API 常见问题完成
export default commonQuestions;