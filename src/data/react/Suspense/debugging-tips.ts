import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'React.Suspense在开发过程中经常遇到的问题和解决方案，帮助开发者快速定位和修复问题。',
        sections: [
          {
            title: 'Suspense边界问题',
            description: 'Suspense边界设置不当导致的渲染和性能问题',
            items: [
              {
                title: 'fallback UI一直显示不消失',
                description: 'Promise没有正确resolve，导致Suspense永远处于pending状态',
                solution: '检查异步操作的Promise状态，确保正确的resolve/reject处理',
                prevention: '使用Promise状态监控工具，添加超时机制',
                code: `// 问题代码
const BrokenComponent = lazy(() => {
  return new Promise(() => {
    // 忘记resolve，Promise永远pending
  });
});

// 解决方案：添加状态监控和超时
const SafeComponent = lazy(() => {
  const loadPromise = import('./MyComponent');
  
  // 添加超时保护
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Component load timeout')), 10000);
  });
  
  return Promise.race([loadPromise, timeoutPromise]);
});

// 调试技巧：添加Promise状态日志
const DebuggableComponent = lazy(() => {
  console.log('开始加载组件...');
  return import('./MyComponent')
    .then(module => {
      console.log('组件加载成功:', module);
      return module;
    })
    .catch(error => {
      console.error('组件加载失败:', error);
      throw error;
    });
});`
              },
              {
                title: 'Suspense边界嵌套导致的渲染闪烁',
                description: '多层Suspense嵌套时，不同层级的fallback UI同时显示造成视觉混乱',
                solution: '合理设计边界层次，使用delay策略避免不必要的fallback显示',
                prevention: '制定边界设计规范，使用可视化工具验证边界效果',
                code: `// 问题：嵌套边界导致闪烁
<Suspense fallback={<PageLoading />}>
  <Suspense fallback={<SectionLoading />}>
    <Suspense fallback={<ComponentLoading />}>
      <MyComponent />
    </Suspense>
  </Suspense>
</Suspense>

// 解决方案：延迟fallback显示
function DelayedFallback({ children, delay = 200 }) {
  const [show, setShow] = useState(false);
  
  useEffect(() => {
    const timer = setTimeout(() => setShow(true), delay);
    return () => clearTimeout(timer);
  }, [delay]);
  
  return show ? children : null;
}

// 优化后的嵌套结构
<Suspense fallback={<PageLoading />}>
  <MainContent>
    <Suspense fallback={
      <DelayedFallback delay={300}>
        <SectionLoading />
      </DelayedFallback>
    }>
      <SectionContent />
    </Suspense>
  </MainContent>
</Suspense>`
              }
            ]
          },
          {
            title: 'Promise处理问题',
            description: 'Promise状态管理和错误处理相关的常见问题',
            items: [
              {
                title: 'Promise reject导致白屏',
                description: 'Promise被reject但没有Error Boundary捕获，导致组件崩溃',
                solution: '始终配合Error Boundary使用，处理加载失败情况',
                prevention: '建立完整的错误处理体系，包括重试机制',
                code: `// 完整的错误处理方案
class SuspenseErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Suspense loading error:', error, errorInfo);
    // 上报错误到监控系统
    errorReporting.captureException(error, {
      tags: { component: 'suspense' },
      extra: errorInfo
    });
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h3>加载失败</h3>
          <p>{this.state.error?.message}</p>
          <button onClick={() => this.setState({ hasError: false, error: null })}>
            重试
          </button>
        </div>
      );
    }
    
    return this.props.children;
  }
}

// 使用方式
<SuspenseErrorBoundary>
  <Suspense fallback={<LoadingSpinner />}>
    <LazyComponent />
  </Suspense>
</SuspenseErrorBoundary>`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '利用React DevTools和其他工具进行Suspense调试和性能分析',
        sections: [
          {
            title: 'React DevTools使用技巧',
            description: '如何使用React DevTools有效调试Suspense相关问题',
            items: [
              {
                title: 'Profiler分析Suspense性能',
                description: '使用React DevTools Profiler分析Suspense的渲染性能',
                solution: '正确配置Profiler，分析组件加载时机和渲染耗时',
                prevention: '建立性能监控基线，定期检查Suspense性能指标',
                code: `// 在应用中集成Profiler进行Suspense调试
import { Profiler } from 'react';

function SuspenseProfiler({ children, id }) {
  const onRenderCallback = (id, phase, actualDuration, baseDuration, startTime, commitTime) => {
    // 记录Suspense相关的渲染性能数据
    console.log('Suspense Render Info:', {
      componentId: id,
      phase, // 'mount' | 'update'
      actualDuration, // 实际渲染耗时
      baseDuration, // 估计渲染耗时
      startTime, // 开始渲染时间
      commitTime, // 提交时间
    });
    
    // 检测Suspense导致的性能问题
    if (actualDuration > 100) {
      console.warn(\`Suspense component \${id} took \${actualDuration}ms to render\`);
    }
  };
  
  return (
    <Profiler id={id} onRender={onRenderCallback}>
      {children}
    </Profiler>
  );
}

// 使用示例
<SuspenseProfiler id="product-list">
  <Suspense fallback={<ProductListSkeleton />}>
    <ProductList />
  </Suspense>
</SuspenseProfiler>`
              },
              {
                title: 'Components标签页调试技巧',
                description: '在Components标签页中观察Suspense状态和Props变化',
                solution: '学会识别Suspense的状态指示，追踪fallback触发原因',
                prevention: '掌握DevTools的高级过滤和搜索功能',
                code: `// DevTools调试技巧
// 1. 在Components标签页搜索"Suspense"
// 2. 观察Suspense组件的Props：
//    - fallback: 当前的fallback UI
//    - children: 被包装的子组件
// 3. 查看组件状态：
//    - Suspended: 是否处于挂起状态
//    - Fallback: 是否显示fallback
// 4. 使用"高亮更新"功能观察重新渲染

// 添加调试友好的组件名称
const LazyProductList = lazy(() => import('./ProductList'));
LazyProductList.displayName = 'LazyProductList';

// 为调试添加额外的Props
function DebuggableSuspense({ children, debugId, ...props }) {
  useEffect(() => {
    console.log(\`Suspense[\${debugId}] mounted\`);
    return () => console.log(\`Suspense[\${debugId}] unmounted\`);
  }, [debugId]);
  
  return <Suspense {...props}>{children}</Suspense>;
}`
              }
            ]
          },
          {
            title: '浏览器开发者工具',
            description: '使用浏览器原生开发者工具调试网络请求和性能问题',
            items: [
              {
                title: 'Network面板监控资源加载',
                description: '监控Suspense触发的动态import请求，分析加载时间',
                solution: '正确解读Network面板中的chunk加载信息',
                prevention: '建立网络请求监控和告警机制',
                code: `// 网络请求调试配置
// 在webpack配置中添加chunk命名
module.exports = {
  output: {
    chunkFilename: '[name].[contenthash].chunk.js',
    // 为调试添加有意义的chunk名称
  },
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\\\/]node_modules[\\\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true
        }
      }
    }
  }
};

// 添加动态import的调试信息
const LazyComponent = lazy(() => {
  const startTime = performance.now();
  
  return import(/* webpackChunkName: "product-detail" */ './ProductDetail')
    .then(module => {
      const loadTime = performance.now() - startTime;
      console.log(\`ProductDetail loaded in \${loadTime.toFixed(2)}ms\`);
      return module;
    });
});`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'debugging-strategies',
      title: '🎯 调试策略',
      content: {
        introduction: '系统性的Suspense调试方法和策略，帮助快速定位复杂问题',
        sections: [
          {
            title: '问题诊断流程',
            description: '遇到Suspense问题时的系统性诊断方法',
            items: [
              {
                title: '性能问题诊断checklist',
                description: '系统性排查Suspense性能问题的检查清单',
                solution: '按照checklist逐项检查，快速定位性能瓶颈',
                prevention: '建立定期性能审计机制',
                code: `// Suspense性能诊断工具
class SuspensePerformanceDiagnostic {
  constructor() {
    this.metrics = new Map();
    this.issues = [];
  }
  
  diagnose(componentId) {
    console.group(\`🔍 Suspense Performance Diagnostic: \${componentId}\`);
    
    // 1. 检查加载时间
    this.checkLoadingTime(componentId);
    
    // 2. 检查fallback显示率
    this.checkFallbackDisplayRate(componentId);
    
    // 3. 检查缓存命中率
    this.checkCacheHitRate(componentId);
    
    // 4. 检查网络条件影响
    this.checkNetworkImpact(componentId);
    
    // 5. 生成诊断报告
    this.generateReport(componentId);
    
    console.groupEnd();
  }
  
  checkLoadingTime(componentId) {
    const metric = this.metrics.get(componentId);
    if (!metric) return;
    
    if (metric.averageLoadTime > 1000) {
      this.issues.push({
        type: 'performance',
        severity: 'high',
        message: \`Component \${componentId} average load time is \${metric.averageLoadTime}ms\`,
        suggestion: 'Consider code splitting optimization or preloading'
      });
    }
  }
  
  generateReport(componentId) {
    const report = {
      componentId,
      timestamp: new Date().toISOString(),
      issues: this.issues.filter(issue => issue.componentId === componentId),
      metrics: this.metrics.get(componentId),
      recommendations: this.generateRecommendations(componentId)
    };
    
    console.table(report.issues);
    console.log('📊 Full Report:', report);
    return report;
  }
}`
              },
              {
                title: '错误追踪和日志记录',
                description: '建立完整的Suspense错误追踪和日志记录系统',
                solution: '使用结构化日志记录，便于问题复现和分析',
                prevention: '建立错误告警和自动恢复机制',
                code: `// Suspense错误追踪系统
class SuspenseErrorTracker {
  constructor() {
    this.errorLog = [];
    this.setupGlobalErrorHandling();
  }
  
  setupGlobalErrorHandling() {
    // 监听未处理的Promise rejection
    window.addEventListener('unhandledrejection', event => {
      if (this.isSuspenseRelated(event.reason)) {
        this.logError({
          type: 'unhandled_promise_rejection',
          error: event.reason,
          timestamp: Date.now(),
          stack: event.reason?.stack,
          userAgent: navigator.userAgent,
          url: window.location.href
        });
      }
    });
  }
  
  logError(errorInfo) {
    this.errorLog.push(errorInfo);
    
    // 发送到错误监控服务
    this.sendToErrorService(errorInfo);
    
    // 本地存储用于调试
    localStorage.setItem('suspense_errors', JSON.stringify(this.errorLog));
  }
  
  getSuspenseErrors() {
    return this.errorLog.filter(error => 
      error.type.includes('suspense') || 
      error.error?.message?.includes('lazy')
    );
  }
  
  isSuspenseRelated(error) {
    const suspenseIndicators = [
      'dynamic import',
      'lazy component',
      'chunk load',
      'loading chunk'
    ];
    
    const errorMessage = error?.message?.toLowerCase() || '';
    return suspenseIndicators.some(indicator => 
      errorMessage.includes(indicator)
    );
  }
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;