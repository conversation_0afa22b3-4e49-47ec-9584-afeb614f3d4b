import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: "React.Suspense是React中用于处理异步操作的组件边界，允许组件在等待异步资源时显示备用UI",
  
  introduction: `React.Suspense是React 16.6.0引入的组件，主要用于代码分割、数据获取和异步渲染。它采用声明式的设计模式，提供了优雅的异步状态管理能力。当包装的组件需要等待某些异步操作（如动态导入、数据获取）时，Suspense会自动显示fallback UI，直到操作完成。`,

  syntax: `<Suspense fallback={<Loading />}>
  <AsyncComponent />
</Suspense>`,

  quickExample: `function SuspenseExample() {
  // 使用Suspense包装异步组件
  const LazyComponent = React.lazy(() => import('./LazyComponent'));

  return (
    <div>
      {/* 异步组件加载时显示fallback */}
      <Suspense fallback={<div>加载中...</div>}>
        <LazyComponent />
      </Suspense>
    </div>
  );
}`,

  scenarioDiagram: `graph TD
    A[用户访问页面] --> B[代码分割场景]
    A --> C[数据获取场景]
    A --> D[嵌套Suspense场景]

    B --> B1[路由级代码分割]
    B --> B2[组件级懒加载]
    B --> B3[第三方库动态导入]

    C --> C1[React Query + Suspense]
    C --> C2[SWR + Suspense]
    C --> C3[原生fetch + Suspense]

    D --> D1[页面级Suspense]
    D --> D2[组件级Suspense]
    D --> D3[多层异步边界]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "fallback",
      type: "ReactNode",
      required: true,
      description: "异步组件加载时显示的备用UI",
      example: "<div>Loading...</div>"
    },
    {
      name: "children",
      type: "ReactNode",
      required: true,
      description: "可能挂起的组件树",
      example: "<LazyComponent />"
    }
  ],
  
  returnValue: {
    type: "JSX.Element",
    description: "渲染fallback UI或children，取决于异步状态",
    example: "// 异步加载中: <div>Loading...</div>\n// 加载完成: <LazyComponent />"
  },
  
  keyFeatures: [
    {
      title: "异步边界处理",
      description: "自动捕获组件树中的异步操作，提供统一的加载状态管理",
      benefit: "简化异步状态管理，提升用户体验"
    },
    {
      title: "代码分割支持",
      description: "与React.lazy()完美配合，实现组件级代码分割",
      benefit: "减少初始包大小，提升页面加载速度"
    },
    {
      title: "嵌套Suspense边界",
      description: "支持多层嵌套，实现细粒度的异步控制",
      benefit: "灵活的异步加载策略，优化用户感知性能"
    },
    {
      title: "并发渲染集成",
      description: "与React 18并发特性深度集成，支持时间分片",
      benefit: "更流畅的用户交互，避免阻塞主线程"
    }
  ],
  
  limitations: [
    "只能捕获组件树中的Promise抛出，不支持传统的异步模式",
    "fallback UI一旦显示，就会完全替换children，无法部分更新",
    "在SSR环境中需要特殊处理，避免水合不匹配"
  ],
  
  bestPractices: [
    "为不同层级的异步操作设置合适的Suspense边界",
    "使用有意义的fallback UI，避免空白页面或闪烁",
    "结合Error Boundary处理异步操作的错误情况",
    "在路由级别使用Suspense，实现页面级的加载状态",
    "避免过深的Suspense嵌套，保持组件结构清晰"
  ],
  
  warnings: [
    "不要在Suspense内部使用useEffect处理异步操作，这不会触发Suspense",
    "避免在fallback中执行复杂的逻辑或副作用",
    "注意Suspense边界的粒度，过多边界可能导致用户体验分散"
  ]
};

export default basicInfo;