import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'React.Suspense是什么？它解决了什么问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'Suspense是React用于处理异步操作的组件边界，允许组件在等待异步资源时显示备用UI',
      detailed: `React.Suspense是React 16.6引入的组件，主要解决以下问题：

1. **异步加载的优雅处理**：传统方式需要手动管理loading状态，Suspense自动处理
2. **代码分割的用户体验**：与React.lazy()配合，实现组件级代码分割
3. **数据获取的统一模式**：提供统一的异步数据获取模式（未来特性）

核心优势：
- 声明式的异步状态管理
- 自动的loading状态处理  
- 更好的用户体验
- 简化的组件代码`,
      code: `// 传统方式
function ProductList() {
  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState([]);
  
  useEffect(() => {
    loadProducts().then(data => {
      setProducts(data);
      setLoading(false);
    });
  }, []);
  
  if (loading) return <div>加载中...</div>;
  return <div>{products.map(...)}</div>;
}

// Suspense方式
function App() {
  return (
    <Suspense fallback={<div>加载中...</div>}>
      <ProductList />
    </Suspense>
  );
}`
    },
    tags: ['基础概念', '异步处理']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'Suspense的工作原理是什么？它是如何捕获异步操作的？',
    difficulty: 'medium',
    frequency: 'high',
    category: '工作原理',
    answer: {
      brief: 'Suspense通过捕获组件抛出的Promise来检测异步操作，然后渲染fallback UI直到Promise解决',
      detailed: `Suspense的工作原理基于JavaScript的异常处理机制：

**核心机制**：
1. 组件在渲染过程中遇到异步操作时，抛出一个Promise
2. Suspense边界捕获这个Promise（类似try-catch）
3. Suspense暂停当前组件树的渲染，显示fallback UI
4. 后台继续处理异步操作
5. Promise解决后，React重新渲染组件树
6. 显示实际的组件内容

**为什么能工作**：
- React的渲染是同步的，但可以被Promise"中断"
- Suspense利用了JavaScript的异常传播机制
- React会重新尝试渲染直到所有Promise都解决`,
      code: `// React.lazy内部实现原理（简化版）
function lazy(importFunc) {
  let status = 'pending';
  let result;
  let promise = importFunc().then(
    module => {
      status = 'resolved';
      result = module.default;
    },
    error => {
      status = 'rejected';
      result = error;
    }
  );
  
  return function LazyComponent() {
    if (status === 'resolved') {
      return result;
    }
    if (status === 'rejected') {
      throw result;
    }
    // 关键：抛出Promise让Suspense捕获
    throw promise;
  };
}`
    },
    tags: ['工作原理', 'Promise机制']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 3,
    question: '如何处理Suspense中的错误情况？嵌套Suspense的最佳实践是什么？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '高级应用',
    answer: {
      brief: '使用Error Boundary配合Suspense处理错误，合理设计嵌套边界粒度',
      detailed: `**错误处理策略**：

1. **Error Boundary集成**：
   - Suspense只处理loading状态，不处理错误
   - 需要Error Boundary包装处理加载失败
   
2. **嵌套Suspense设计**：
   - 粗粒度：页面级边界，统一loading
   - 细粒度：组件级边界，独立loading
   - 混合模式：根据业务需求灵活组合

**最佳实践原则**：
- 避免过深嵌套，保持清晰的边界层次
- 为不同优先级的内容设置不同边界
- 考虑用户感知，避免loading闪烁`,
      code: `// 完整的错误处理 + 嵌套Suspense
function App() {
  return (
    <ErrorBoundary fallback={<AppErrorFallback />}>
      {/* 页面级Suspense */}
      <Suspense fallback={<PageSkeleton />}>
        <Header />
        <Main />
      </Suspense>
    </ErrorBoundary>
  );
}

function Main() {
  return (
    <div>
      <PrimaryContent />
      
      {/* 次要内容独立边界 */}
      <ErrorBoundary fallback={<SecondaryError />}>
        <Suspense fallback={<SecondarySkeleton />}>
          <SecondaryContent />
        </Suspense>
      </ErrorBoundary>
    </div>
  );
}

// 错误边界组件
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true };
  }
  
  componentDidCatch(error, errorInfo) {
    console.log('Suspense loading error:', error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    return this.props.children;
  }
}`
    },
    tags: ['错误处理', '嵌套边界', '最佳实践']
  }
];

export default interviewQuestions;