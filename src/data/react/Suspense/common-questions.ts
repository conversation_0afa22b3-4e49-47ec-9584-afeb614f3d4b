import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么我的Suspense fallback一直在显示，组件从未加载？',
    answer: `这通常是因为Promise没有正确解决或组件没有正确抛出Promise。检查以下几点：

1. **确认异步操作正确实现**：
   - React.lazy()的import()是否正确
   - 数据获取是否按Suspense模式实现

2. **检查网络和缓存**：
   - 组件文件是否存在构建错误
   - 网络请求是否被阻塞

3. **验证Promise状态**：
   - Promise是否正确resolve
   - 是否有未捕获的错误导致Promise reject`,
    code: `// 错误示例：Promise永远不resolve
const BrokenComponent = lazy(() => {
  return new Promise(() => {
    // 忘记resolve，Promise永远pending
  });
});

// 正确示例：确保Promise正确resolve
const WorkingComponent = lazy(() => 
  import('./MyComponent').then(module => ({
    default: module.MyComponent
  }))
);

// 调试技巧：添加日志
const DebuggableComponent = lazy(() => {
  console.log('开始加载组件...');
  return import('./MyComponent')
    .then(module => {
      console.log('组件加载成功:', module);
      return module;
    })
    .catch(error => {
      console.error('组件加载失败:', error);
      throw error;
    });
});`,
    tags: ['调试', 'Promise', '加载失败'],
    relatedQuestions: ['Promise状态如何调试', 'lazy loading最佳实践']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: 'Suspense可以用于数据获取吗？如何实现？',
    answer: `Suspense可以用于数据获取，但需要特定的实现方式。目前主要通过第三方库实现：

1. **使用支持Suspense的数据获取库**：
   - SWR（版本2.0+）
   - React Query/TanStack Query
   - Relay

2. **自定义Suspense数据获取**：
   - 创建自定义Hook抛出Promise
   - 缓存已解决的数据

3. **React未来计划**：
   - 官方数据获取API仍在开发中
   - use() Hook是未来的标准方案`,
    code: `// 使用SWR的Suspense模式
import useSWR from 'swr';

function Profile({ userId }) {
  // SWR自动支持Suspense
  const { data } = useSWR(\`/api/users/\${userId}\`, fetcher, {
    suspense: true
  });
  
  return <div>用户：{data.name}</div>;
}

// 自定义Suspense数据获取Hook
function createSuspenseResource(promise) {
  let status = 'pending';
  let result;
  
  const resource = promise.then(
    (data) => {
      status = 'success';
      result = data;
    },
    (error) => {
      status = 'error';
      result = error;
    }
  );
  
  return {
    read() {
      if (status === 'pending') throw resource;
      if (status === 'error') throw result;
      return result;
    }
  };
}

// 使用自定义资源
const userResource = createSuspenseResource(
  fetch('/api/user').then(r => r.json())
);

function UserProfile() {
  const user = userResource.read(); // 抛出Promise或返回数据
  return <div>{user.name}</div>;
}`,
    tags: ['数据获取', 'SWR', '自定义Hook'],
    relatedQuestions: ['SWR配置', 'React Query使用', '数据缓存策略']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: 'Suspense fallback一闪而过怎么办？如何避免闪烁？',
    answer: `fallback闪烁是常见的用户体验问题，有多种解决方案：

1. **延迟显示fallback**：
   - 设置最小显示时间
   - 只在加载时间超过阈值时显示

2. **预加载策略**：
   - 路由预加载
   - 用户交互预加载

3. **骨架屏替代loading**：
   - 使用内容骨架而非简单loading
   - 保持页面布局稳定

4. **缓存策略**：
   - 组件缓存避免重复加载
   - 数据缓存减少获取时间`,
    code: `// 防闪烁的fallback组件
function DelayedFallback({ delay = 200, children }) {
  const [show, setShow] = useState(false);
  
  useEffect(() => {
    const timer = setTimeout(() => setShow(true), delay);
    return () => clearTimeout(timer);
  }, [delay]);
  
  return show ? children : null;
}

// 使用延迟fallback
<Suspense fallback={
  <DelayedFallback>
    <div>加载中...</div>
  </DelayedFallback>
}>
  <LazyComponent />
</Suspense>

// 路由预加载策略
function NavigationLink({ to, children }) {
  const handleMouseEnter = () => {
    // 预加载目标路由组件
    import(\`./pages/\${to}\`);
  };
  
  return (
    <Link to={to} onMouseEnter={handleMouseEnter}>
      {children}
    </Link>
  );
}

// 骨架屏fallback
function ProductListSkeleton() {
  return (
    <div className="product-grid">
      {Array.from({ length: 8 }).map((_, i) => (
        <div key={i} className="product-skeleton">
          <div className="skeleton-image" />
          <div className="skeleton-title" />
          <div className="skeleton-price" />
        </div>
      ))}
    </div>
  );
}`,
    tags: ['用户体验', '闪烁问题', '骨架屏'],
    relatedQuestions: ['预加载策略', '骨架屏设计', '缓存优化']
  }
];

export default commonQuestions;