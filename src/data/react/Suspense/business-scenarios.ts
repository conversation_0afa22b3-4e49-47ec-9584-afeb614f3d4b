import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'scenario-1',
    title: '电商平台路由级代码分割',
    description: '大型电商平台使用Suspense实现页面级代码分割，提升首页加载速度',
    businessValue: '将首页包大小从2.5MB减少到800KB，首屏加载时间缩短60%，用户转化率提升15%',
    scenario: '用户访问电商首页时，只加载必要的核心组件，商品详情、购物车、用户中心等页面采用懒加载',
    code: `import { Suspense, lazy } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';

// 路由级代码分割
const ProductDetail = lazy(() => import('./pages/ProductDetail'));
const ShoppingCart = lazy(() => import('./pages/ShoppingCart'));
const UserCenter = lazy(() => import('./pages/UserCenter'));

function EcommerceApp() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route 
          path="/product/:id" 
          element={
            <Suspense fallback={<ProductDetailSkeleton />}>
              <ProductDetail />
            </Suspense>
          } 
        />
        <Route 
          path="/cart" 
          element={
            <Suspense fallback={<CartLoadingSkeleton />}>
              <ShoppingCart />
            </Suspense>
          } 
        />
        <Route 
          path="/user/*" 
          element={
            <Suspense fallback={<UserCenterSkeleton />}>
              <UserCenter />
            </Suspense>
          } 
        />
      </Routes>
    </BrowserRouter>
  );
}`,
    explanation: '通过路由级Suspense，电商平台实现了按需加载，用户只在访问特定页面时才下载相关代码，大幅提升了首页性能',
    benefits: [
      '首页包大小减少70%，加载速度显著提升',
      '用户体验优化，减少白屏时间',
      '带宽成本降低，特别是移动端用户受益明显'
    ],
    metrics: {
      performance: 'LCP从4.2s降低到1.6s，FCP提升65%',
      userExperience: '页面跳出率从35%降低到22%，用户停留时间增加40%',
      technicalMetrics: 'Bundle splitting效率提升80%，缓存命中率提升至85%'
    },
    difficulty: 'easy',
    tags: ['路由分割', '性能优化', '用户体验']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'scenario-2',
    title: '数据可视化图表懒加载',
    description: '企业级数据分析平台使用Suspense优化重量级图表组件的加载体验',
    businessValue: '图表页面初始加载时间从8秒减少到2秒，用户操作响应性提升300%',
    scenario: '企业数据分析师访问包含多个复杂图表的仪表板时，使用Suspense实现图表的渐进式加载',
    code: `import { Suspense, lazy, useState } from 'react';

// 按需加载重量级图表组件
const AdvancedChart = lazy(() => import('./charts/AdvancedChart'));
const HeatMapChart = lazy(() => import('./charts/HeatMapChart'));
const GeoChart = lazy(() => import('./charts/GeoChart'));

function DataDashboard() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="dashboard">
      <div className="tab-navigation">
        <button onClick={() => setActiveTab('overview')}>概览</button>
        <button onClick={() => setActiveTab('advanced')}>高级分析</button>
        <button onClick={() => setActiveTab('heatmap')}>热力图</button>
        <button onClick={() => setActiveTab('geo')}>地理分布</button>
      </div>

      <div className="dashboard-content">
        {activeTab === 'overview' && <OverviewCharts />}
        
        {activeTab === 'advanced' && (
          <Suspense fallback={<ChartLoadingSkeleton type="advanced" />}>
            <AdvancedChart data={analysisData} />
          </Suspense>
        )}
        
        {activeTab === 'heatmap' && (
          <Suspense fallback={<ChartLoadingSkeleton type="heatmap" />}>
            <HeatMapChart data={heatmapData} />
          </Suspense>
        )}
        
        {activeTab === 'geo' && (
          <Suspense fallback={<ChartLoadingSkeleton type="geo" />}>
            <GeoChart data={geoData} />
          </Suspense>
        )}
      </div>
    </div>
  );
}`,
    explanation: '通过Tab切换触发的条件式Suspense，数据分析平台实现了图表组件的按需加载，避免了一次性加载所有重量级图表库',
    benefits: [
      '页面初始加载时间缩短75%，用户可以快速看到基础内容',
      '内存使用优化，避免同时渲染多个复杂图表',
      '网络带宽节省，只在需要时下载图表库',
      '用户体验提升，有意义的加载动画替代白屏'
    ],
    metrics: {
      performance: '页面TTI从8.5s降低到2.1s，主线程阻塞时间减少80%',
      userExperience: '用户交互响应时间从1200ms降低到300ms',
      technicalMetrics: 'JS bundle大小优化65%，内存峰值使用降低40%'
    },
    difficulty: 'medium',
    tags: ['数据可视化', '按需加载', '性能优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'scenario-3',
    title: '微前端架构中的模块边界',
    description: '大型企业应用使用Suspense管理微前端模块的加载边界和错误处理',
    businessValue: '支持50+子应用的独立部署，开发效率提升200%，故障隔离能力增强',
    scenario: '企业级应用包含多个业务模块，每个模块独立开发部署，使用Suspense作为模块间的加载边界',
    code: `import { Suspense, lazy, ErrorBoundary } from 'react';

// 微前端模块懒加载
const CRMModule = lazy(() => import('crm/CRMApp'));
const HRModule = lazy(() => import('hr/HRApp'));
const FinanceModule = lazy(() => import('finance/FinanceApp'));

function MicroFrontendContainer({ currentModule }) {
  return (
    <div className="enterprise-app">
      <NavigationBar />
      
      <main className="app-content">
        <ErrorBoundary fallback={<ModuleErrorFallback />}>
          <Suspense fallback={<ModuleLoadingSkeleton />}>
            {currentModule === 'crm' && (
              <CRMModule 
                apiKey={crmConfig.apiKey}
                onNavigate={handleNavigation}
              />
            )}
            
            {currentModule === 'hr' && (
              <Suspense fallback={<HRLoadingSkeleton />}>
                <HRModule 
                  permissions={userPermissions}
                  theme={appTheme}
                />
              </Suspense>
            )}
            
            {currentModule === 'finance' && (
              <Suspense fallback={<FinanceLoadingSkeleton />}>
                <FinanceModule 
                  companyId={companyId}
                  fiscalYear={currentYear}
                />
              </Suspense>
            )}
          </Suspense>
        </ErrorBoundary>
      </main>
    </div>
  );
}

// 嵌套Suspense实现细粒度控制
function CRMApp() {
  return (
    <div className="crm-app">
      <CRMSidebar />
      
      <div className="crm-content">
        <Suspense fallback={<CustomerListSkeleton />}>
          <CustomerManagement />
        </Suspense>
        
        <Suspense fallback={<ReportsSkeleton />}>
          <CRMReports />
        </Suspense>
      </div>
    </div>
  );
}`,
    explanation: '通过多层嵌套的Suspense边界，微前端架构实现了模块级的加载管理和故障隔离，每个业务模块可以独立加载和错误处理',
    benefits: [
      '模块独立部署，支持团队并行开发',
      '故障隔离，单个模块失败不影响整体应用',
      '按需加载，用户只下载当前使用的业务模块',
      '开发体验优化，支持模块级热更新',
      '维护成本降低，模块可以独立测试和版本管理'
    ],
    metrics: {
      performance: '应用启动时间从15s降低到3s，模块切换响应时间<500ms',
      userExperience: '用户可用功能加载时间缩短80%，界面响应性显著提升',
      technicalMetrics: '代码复用率提升60%，构建时间优化70%，部署频率提升300%'
    },
    difficulty: 'hard',
    tags: ['微前端', '模块化', '企业级', '故障隔离']
  }
];

export default businessScenarios;