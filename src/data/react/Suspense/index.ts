import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ReactSuspenseData: ApiItem = {
  id: 'Suspense',
  title: 'React.Suspense',
  description: 'React中用于处理异步操作的组件边界，允许组件在等待异步资源时显示备用UI',
  category: 'React Components',
  difficulty: 'medium',
  
  syntax: `<Suspense fallback={<Loading />}>
  <AsyncComponent />
</Suspense>`,
  example: `function SuspenseExample() {
  // 使用Suspense包装异步组件
  const LazyComponent = React.lazy(() => import('./LazyComponent'));

  return (
    <Suspense fallback={<div>加载中...</div>}>
      <LazyComponent />
    </Suspense>
  );
}`,
  notes: '只能捕获组件树中的Promise抛出，不支持传统的异步模式',
  
  version: 'React 16.6.0+',
  tags: ['React', 'Component', '异步', 'Suspense', '代码分割'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ReactSuspenseData;