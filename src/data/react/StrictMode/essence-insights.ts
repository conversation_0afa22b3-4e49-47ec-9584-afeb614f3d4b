import { EssenceInsights } from '@/types/api';

const essenceInsights: EssenceInsights = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  coreQuestion: `StrictMode的本质是什么？它为什么要"故意"让开发变得困难？`,

  designPhilosophy: {
    worldview: `StrictMode体现了"早期发现问题"的设计哲学。它认为开发时的"痛苦"是为了避免生产环境的"灾难"。这种设计理念认为，开发者应该在安全的开发环境中面对所有潜在问题，而不是在用户面前暴露缺陷。`,
    methodology: `通过故意重复执行、激活额外检查、模拟边界条件等方式，StrictMode将隐藏的副作用和不安全模式暴露出来。它使用"对抗性测试"的方法论，故意创造困难条件来考验代码的健壮性。`,
    tradeoffs: `StrictMode牺牲了开发时的简洁性和性能，换取了代码质量的保证。它增加了开发复杂度，但降低了维护成本。这种权衡体现了"前期投资，后期收益"的工程思维。`,
    evolution: `从PropTypes到StrictMode的演进，反映了React生态对代码质量要求的不断提升。从静态类型检查到运行时行为检测，这一演进体现了工具链向"全方位质量保障"的发展趋势。`
  },

  hiddenTruth: {
    surfaceProblem: `开发者常常因为StrictMode产生大量警告而选择禁用它，认为它增加了开发负担。`,
    realProblem: `真正的问题不是StrictMode太严格，而是我们的代码存在潜在的副作用和不安全模式。StrictMode只是暴露了本来就存在的问题。`,
    hiddenCost: `禁用StrictMode看似解决了问题，实际上是将技术债务推迟到了生产环境，可能导致难以调试的运行时错误和性能问题。`,
    deeperValue: `StrictMode的真正价值在于它强制开发者思考代码的纯净性和可预测性，培养编写高质量、可维护代码的习惯。它是一个编程思维的训练工具。`
  },

  deeperQuestions: [
    "为什么现代软件开发需要如此多的安全网和检查机制？",
    "故意增加开发难度是否真的能提升软件质量？",
    "开发体验和代码质量之间的平衡点在哪里？",
    "如果开发者无法容忍开发时的警告，他们如何保证生产代码的质量？"
  ],

  paradigmShift: {
    oldParadigm: {
      assumption: `代码能运行就是好代码，开发工具应该让开发变得更容易。`,
      limitation: `这种思维忽略了代码的长期维护性和健壮性，容易产生技术债务。`,
      worldview: `以开发效率为中心的线性思维，认为工具的价值在于减少摩擦。`
    },
    newParadigm: {
      breakthrough: `质量优先的开发理念：好的工具应该帮助开发者写出更好的代码，而不仅仅是更快的代码。`,
      possibility: `通过在开发时暴露问题，可以构建更健壮、更可预测的应用，减少生产环境的意外情况。`,
      cost: `需要开发者投入更多时间理解和修复警告，改变习惯性的编程模式。`
    },
    transition: {
      resistance: `开发者的惯性思维和对警告的排斥心理，认为额外的检查是对开发流程的干扰。`,
      catalyst: `生产环境中难以调试的bug和性能问题，让开发者意识到开发时质量检查的重要性。`,
      tippingPoint: `当团队经历过因为忽略开发时警告而导致的重大生产问题后，会自然转向拥抱StrictMode的哲学。`
    }
  },

  universalPrinciples: [
    "早期发现问题比后期修复更经济高效",
    "开发时的摩擦往往预示着设计上的缺陷",
    "工具的价值不在于消除所有困难，而在于暴露有意义的困难",
    "质量是设计出来的，不是测试出来的"
  ]
};

export default essenceInsights;