import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
  completionStatus: '内容已完成',
  
  optimizationStrategies: [
    {
      title: 'StrictMode开发环境性能优化',
      description: '在开发环境中平衡StrictMode检测功能与开发体验的性能优化策略',
      techniques: [
        {
          name: '条件性StrictMode启用',
          description: '根据开发阶段和需求动态启用或禁用StrictMode',
          code: `// 智能StrictMode控制策略
function App() {
  const isDev = process.env.NODE_ENV === 'development';
  const isLocalhost = window.location.hostname === 'localhost';
  const strictModeEnabled = localStorage.getItem('strict-mode-enabled') !== 'false';
  
  // 多层条件控制StrictMode启用
  const shouldUseStrictMode = 
    isDev && 
    isLocalhost && 
    strictModeEnabled &&
    !window.location.search.includes('debug=fast'); // 快速调试时禁用
  
  const AppContent = (
    <Router>
      <ErrorBoundary>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/profile" element={<Profile />} />
        </Routes>
      </ErrorBoundary>
    </Router>
  );
  
  return shouldUseStrictMode ? (
    <React.StrictMode>
      {AppContent}
    </React.StrictMode>
  ) : AppContent;
}

// 开发环境性能控制面板
function DevPerformanceControls() {
  const [settings, setSettings] = useState({
    strictMode: localStorage.getItem('dev-strict-mode') !== 'false',
    verboseLogging: localStorage.getItem('dev-verbose') === 'true',
    performanceMonitoring: localStorage.getItem('dev-perf') === 'true'
  });
  
  const updateSetting = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    localStorage.setItem(\`dev-\${key.toLowerCase()}\`, value.toString());
    
    if (key === 'strictMode') {
      alert('StrictMode设置已更改，请刷新页面生效');
    }
  };
  
  if (process.env.NODE_ENV !== 'development') return null;
  
  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '4px',
      fontSize: '12px',
      zIndex: 9999
    }}>
      <h4>开发性能控制</h4>
      <label>
        <input
          type="checkbox"
          checked={settings.strictMode}
          onChange={(e) => updateSetting('strictMode', e.target.checked)}
        />
        StrictMode检测
      </label>
      <br />
      <label>
        <input
          type="checkbox"
          checked={settings.verboseLogging}
          onChange={(e) => updateSetting('verboseLogging', e.target.checked)}
        />
        详细日志
      </label>
      <br />
      <label>
        <input
          type="checkbox"
          checked={settings.performanceMonitoring}
          onChange={(e) => updateSetting('performanceMonitoring', e.target.checked)}
        />
        性能监控
      </label>
    </div>
  );
}`,
          impact: 'high',
          difficulty: 'easy'
        },
        {
          name: '渐进式StrictMode应用',
          description: '在大型应用中分模块、分路由逐步应用StrictMode',
          code: `// 分模块应用StrictMode策略
function AppWithProgressiveStrictMode() {
  const location = useLocation();
  const [strictModeRoutes] = useState([
    '/new-features',
    '/dashboard',
    '/profile'
  ]);
  
  // 检查当前路由是否需要StrictMode
  const needsStrictMode = strictModeRoutes.some(route => 
    location.pathname.startsWith(route)
  );
  
  return (
    <Router>
      <Routes>
        {/* 新功能强制使用StrictMode */}
        <Route path="/new-features/*" element={
          <React.StrictMode>
            <NewFeaturesModule />
          </React.StrictMode>
        } />
        
        {/* 已优化的核心模块 */}
        <Route path="/dashboard/*" element={
          <React.StrictMode>
            <DashboardModule />
          </React.StrictMode>
        } />
        
        {/* 遗留模块暂不使用StrictMode */}
        <Route path="/legacy/*" element={<LegacyModule />} />
        
        {/* 条件性应用StrictMode */}
        <Route path="/profile/*" element={
          process.env.REACT_APP_PROFILE_STRICT_MODE === 'true' ? (
            <React.StrictMode>
              <ProfileModule />
            </React.StrictMode>
          ) : (
            <ProfileModule />
          )
        } />
      </Routes>
    </Router>
  );
}

// 模块级StrictMode包装器
function ModuleStrictModeWrapper({ 
  children, 
  moduleName, 
  enabledByDefault = false 
}) {
  const [isEnabled, setIsEnabled] = useState(() => {
    const stored = localStorage.getItem(\`strict-mode-\${moduleName}\`);
    return stored ? stored === 'true' : enabledByDefault;
  });
  
  const toggleStrictMode = () => {
    const newValue = !isEnabled;
    setIsEnabled(newValue);
    localStorage.setItem(\`strict-mode-\${moduleName}\`, newValue.toString());
    window.location.reload(); // 刷新页面应用新设置
  };
  
  return (
    <div>
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          background: '#f0f0f0',
          padding: '4px 8px',
          fontSize: '11px',
          borderBottom: '1px solid #ddd'
        }}>
          模块: {moduleName} | StrictMode: {isEnabled ? '启用' : '禁用'}
          <button onClick={toggleStrictMode} style={{ marginLeft: '8px' }}>
            切换
          </button>
        </div>
      )}
      
      {isEnabled ? (
        <React.StrictMode>
          {children}
        </React.StrictMode>
      ) : children}
    </div>
  );
}

// 使用示例
function ProfilePage() {
  return (
    <ModuleStrictModeWrapper moduleName="profile" enabledByDefault={true}>
      <ProfileContent />
    </ModuleStrictModeWrapper>
  );
}`,
          impact: 'medium',
          difficulty: 'medium'
        },
        {
          name: '性能监控和预警系统',
          description: '监控StrictMode对开发环境性能的影响，及时调整',
          code: `// 开发环境性能监控组件
function StrictModePerformanceMonitor({ children }) {
  const [metrics, setMetrics] = useState({
    renderCount: 0,
    avgRenderTime: 0,
    maxRenderTime: 0,
    memoryUsage: 0,
    warnings: []
  });
  
  const startTimeRef = useRef();
  const renderCountRef = useRef(0);
  const renderTimesRef = useRef([]);
  
  // 渲染开始时间记录
  useLayoutEffect(() => {
    startTimeRef.current = performance.now();
  });
  
  // 渲染结束时间计算
  useEffect(() => {
    if (startTimeRef.current) {
      const renderTime = performance.now() - startTimeRef.current;
      renderCountRef.current += 1;
      renderTimesRef.current.push(renderTime);
      
      // 保持最近50次渲染记录
      if (renderTimesRef.current.length > 50) {
        renderTimesRef.current.shift();
      }
      
      const avgTime = renderTimesRef.current.reduce((a, b) => a + b, 0) / 
                      renderTimesRef.current.length;
      const maxTime = Math.max(...renderTimesRef.current);
      
      setMetrics(prev => ({
        ...prev,
        renderCount: renderCountRef.current,
        avgRenderTime: avgTime,
        maxRenderTime: maxTime
      }));
      
      // 性能警告
      if (renderTime > 50) {
        setMetrics(prev => ({
          ...prev,
          warnings: [...prev.warnings.slice(-4), {
            type: 'slow-render',
            time: renderTime,
            timestamp: Date.now()
          }]
        }));
      }
    }
  });
  
  // 内存使用监控
  useEffect(() => {
    const checkMemory = () => {
      if (performance.memory) {
        setMetrics(prev => ({
          ...prev,
          memoryUsage: performance.memory.usedJSHeapSize / 1024 / 1024 // MB
        }));
      }
    };
    
    const interval = setInterval(checkMemory, 2000);
    return () => clearInterval(interval);
  }, []);
  
  // 性能建议
  const getPerformanceSuggestions = () => {
    const suggestions = [];
    
    if (metrics.avgRenderTime > 16) {
      suggestions.push('平均渲染时间超过16ms，考虑优化组件或暂时禁用StrictMode');
    }
    
    if (metrics.memoryUsage > 50) {
      suggestions.push('内存使用较高，检查是否有内存泄漏');
    }
    
    if (metrics.warnings.length > 3) {
      suggestions.push('频繁出现慢渲染，建议检查组件性能');
    }
    
    return suggestions;
  };
  
  if (process.env.NODE_ENV !== 'development') {
    return children;
  }
  
  const suggestions = getPerformanceSuggestions();
  
  return (
    <>
      {children}
      
      {/* 性能监控面板 */}
      <div style={{
        position: 'fixed',
        bottom: 10,
        right: 10,
        background: 'rgba(0,0,0,0.9)',
        color: 'white',
        padding: '8px',
        borderRadius: '4px',
        fontSize: '10px',
        maxWidth: '250px',
        zIndex: 10000
      }}>
        <div><strong>性能监控</strong></div>
        <div>渲染次数: {metrics.renderCount}</div>
        <div>平均渲染: {metrics.avgRenderTime.toFixed(1)}ms</div>
        <div>最大渲染: {metrics.maxRenderTime.toFixed(1)}ms</div>
        <div>内存使用: {metrics.memoryUsage.toFixed(1)}MB</div>
        
        {suggestions.length > 0 && (
          <div style={{ marginTop: '4px', color: '#ffcc00' }}>
            <strong>建议:</strong>
            {suggestions.map((suggestion, index) => (
              <div key={index} style={{ fontSize: '9px' }}>
                • {suggestion}
              </div>
            ))}
          </div>
        )}
        
        {metrics.warnings.length > 0 && (
          <div style={{ marginTop: '4px', color: '#ff6666' }}>
            <strong>警告:</strong>
            {metrics.warnings.slice(-2).map((warning, index) => (
              <div key={index} style={{ fontSize: '9px' }}>
                • 慢渲染: {warning.time.toFixed(1)}ms
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
}

// 性能优化的StrictMode包装器
function OptimizedStrictMode({ children, performanceThreshold = 16 }) {
  const [isEnabled, setIsEnabled] = useState(true);
  const performanceRef = useRef({ renderTimes: [], slowRenders: 0 });
  
  const checkPerformance = useCallback((renderTime) => {
    const perf = performanceRef.current;
    perf.renderTimes.push(renderTime);
    
    // 保持最近20次渲染记录
    if (perf.renderTimes.length > 20) {
      perf.renderTimes.shift();
    }
    
    // 检查慢渲染
    if (renderTime > performanceThreshold) {
      perf.slowRenders += 1;
      
      // 如果连续出现慢渲染，自动禁用StrictMode
      if (perf.slowRenders > 5) {
        console.warn('检测到频繁慢渲染，自动禁用StrictMode以提升开发体验');
        setIsEnabled(false);
        perf.slowRenders = 0;
      }
    } else {
      perf.slowRenders = Math.max(0, perf.slowRenders - 1);
    }
  }, [performanceThreshold]);
  
  const PerformanceWrapper = ({ children }) => {
    const startTime = useRef();
    
    useLayoutEffect(() => {
      startTime.current = performance.now();
    });
    
    useEffect(() => {
      if (startTime.current) {
        const renderTime = performance.now() - startTime.current;
        checkPerformance(renderTime);
      }
    });
    
    return children;
  };
  
  return isEnabled ? (
    <React.StrictMode>
      <PerformanceWrapper>
        {children}
      </PerformanceWrapper>
    </React.StrictMode>
  ) : (
    <PerformanceWrapper>
      {children}
    </PerformanceWrapper>
  );
}`,
          impact: 'high',
          difficulty: 'hard'
        }
      ]
    }
  ],

  performanceMetrics: {
    'Development Build Time': {
      description: '开发环境构建时间，StrictMode不影响构建性能',
      tool: 'Webpack Bundle Analyzer, Vite Build Analysis',
      example: 'StrictMode对构建时间无影响，仅影响运行时检测'
    },
    'Runtime Performance': {
      description: '运行时性能影响，主要体现在开发环境的重复渲染',
      tool: 'React DevTools Profiler, Chrome Performance Tab',
      example: '开发环境下某些组件渲染时间可能增加30-50%，生产环境无影响'
    },
    'Memory Usage': {
      description: '内存使用情况，StrictMode可能暴露内存泄漏问题',
      tool: 'Chrome DevTools Memory Tab, Performance.memory API',
      example: 'StrictMode帮助发现订阅未清理导致的内存泄漏问题'
    },
    'Developer Experience': {
      description: '开发者体验指标，包括调试效率和问题发现速度',
      tool: '开发时间统计, Bug发现率统计',
      example: '使用StrictMode后，90%的潜在问题在开发阶段被发现'
    },
    'CI/CD Performance': {
      description: '持续集成/部署性能，StrictMode对生产构建无影响',
      tool: 'GitHub Actions, Jenkins, CircleCI监控',
      example: '生产构建时间与性能完全不受StrictMode影响'
    }
  },

  bestPractices: [
    '⚡ **分阶段启用**：在大型项目中分模块、分路由逐步启用StrictMode，降低性能影响',
    '🎯 **条件性使用**：根据开发阶段和性能需求动态控制StrictMode启用状态',
    '📊 **性能监控**：建立开发环境性能监控机制，及时发现和解决性能问题',
    '🔧 **智能优化**：当检测到频繁慢渲染时，自动调整StrictMode设置',
    '🌊 **批量处理**：将多个相关的开发工具（StrictMode、DevTools等）统一管理',
    '💾 **状态持久化**：保存开发者的StrictMode偏好设置，提升开发体验',
    '📈 **渐进式采用**：从新功能开始使用StrictMode，逐步扩展到整个应用',
    '🧪 **环境分离**：确保开发、测试、生产环境的StrictMode配置合理',
    '🔍 **问题优先级**：优先修复StrictMode检测到的高影响问题',
    '⚖️ **平衡取舍**：在开发效率和代码质量之间找到合适的平衡点'
  ],

  commonPitfalls: [
    {
      issue: '开发环境性能下降严重影响开发体验',
      cause: '在复杂组件中启用StrictMode导致渲染时间过长',
      solution: '使用条件性StrictMode启用，或者优化组件性能后再启用StrictMode'
    },
    {
      issue: '控制台日志过多干扰正常调试',
      cause: 'StrictMode的重复执行导致大量重复日志输出',
      solution: '使用条件性日志输出，或者配置开发工具过滤重复日志'
    },
    {
      issue: '团队成员频繁禁用StrictMode逃避问题',
      cause: '修复StrictMode检测到的问题需要时间，团队选择临时禁用',
      solution: '建立代码审查机制，制定StrictMode问题修复的优先级和时间表'
    },
    {
      issue: '新功能开发时StrictMode检测过于严格',
      cause: '在快速原型开发阶段，StrictMode的检测影响开发速度',
      solution: '为原型开发阶段提供快速开发模式，暂时禁用某些检测'
    },
    {
      issue: '生产环境意外包含了StrictMode代码',
      cause: '构建配置错误导致开发时代码进入生产环境',
      solution: '建立生产构建验证机制，确保开发时代码被正确移除'
    }
  ]
};

export default performanceOptimization;