import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'StrictMode在开发过程中可能遇到的常见问题及解决方案。',
        sections: [
          {
            title: '双重渲染问题',
            description: 'StrictMode会故意重复调用某些函数来检测副作用，导致意外的双重渲染。',
            items: [
              {
                title: 'useEffect执行两次',
                description: 'StrictMode会故意重复调用useEffect来检测副作用，导致控制台日志重复。',
                solution: '确保Effect函数是纯净的，或使用cleanup函数清理副作用。',
                prevention: '编写可重复执行的Effect函数，避免依赖副作用。',
                code: `// ❌ 问题代码
useEffect(() => {
  console.log('这会被调用两次');
  document.title = count; // 副作用
}, [count]);

// ✅ 正确代码  
useEffect(() => {
  console.log('这会被调用两次，但没问题');
  document.title = count;
  
  // cleanup函数确保没有副作用残留
  return () => {
    document.title = '默认标题';
  };
}, [count]);`
              },
              {
                title: '状态初始化函数重复调用',
                description: 'useState的初始化函数在StrictMode下会被调用多次。',
                solution: '使用lazy初始化避免昂贵计算的重复执行。',
                prevention: '始终使用函数形式的lazy初始化来处理复杂计算。',
                code: `// ❌ 问题代码
const [data, setData] = useState(expensiveCalculation());

// ✅ 正确代码
const [data, setData] = useState(() => expensiveCalculation());`
              }
            ]
          },
          {
            title: '控制台警告处理',
            description: 'StrictMode会产生大量开发时警告，需要正确理解和处理。',
            items: [
              {
                title: '过时生命周期警告',
                description: 'StrictMode会警告使用已弃用的生命周期方法。',
                solution: '将过时的生命周期方法迁移到新的Hook API。',
                prevention: '始终使用最新的React API模式。',
                code: `// ❌ 过时方法
componentWillMount() {
  this.fetchData();
}

// ✅ 现代方法
useEffect(() => {
  fetchData();
}, []);`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '使用开发工具有效调试StrictMode相关问题。',
        sections: [
          {
            title: 'React DevTools',
            description: '利用React DevTools分析StrictMode的影响和警告。',
            items: [
              {
                title: 'Profiler面板使用',
                description: '使用Profiler查看StrictMode对组件渲染性能的影响。',
                solution: '在Profiler中对比启用/禁用StrictMode的性能差异。',
                prevention: '定期使用Profiler监控应用性能表现。',
                code: `// React DevTools Profiler使用步骤
// 1. 打开React DevTools
// 2. 切换到Profiler标签
// 3. 开始录制
// 4. 执行操作
// 5. 停止录制查看结果`
              },
              {
                title: '控制台过滤技巧',
                description: '有效过滤和分析StrictMode产生的控制台信息。',
                solution: '使用浏览器控制台的过滤功能专注于重要警告。',
                prevention: '建立系统的警告处理流程，逐步清理所有警告。',
                code: `// 控制台过滤示例
// 过滤StrictMode相关警告
console: "Warning: componentWillMount"
console: "Warning: findDOMNode"

// 使用自定义日志标记
console.log('%c[StrictMode Debug]', 'color: orange', data);`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;