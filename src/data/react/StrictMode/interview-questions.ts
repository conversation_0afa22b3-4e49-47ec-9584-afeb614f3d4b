import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'React.StrictMode是什么？它在开发中起到什么作用？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'React.StrictMode是一个用于启用额外开发时检查的组件，帮助识别潜在问题和不安全的代码模式。',
      detailed: `React.StrictMode是React 16.3引入的开发工具组件，主要作用包括：

**核心功能**：
1. **副作用检测**：通过重复调用某些函数来检测不纯的副作用
2. **不安全API警告**：警告使用已弃用的生命周期方法
3. **并发模式准备**：检测与未来并发特性不兼容的代码

**工作原理**：
- 仅在开发模式下工作，生产环境完全移除
- 故意重复调用构造函数、render方法、state更新函数
- 重复执行useEffect的setup和cleanup函数
- 检测直接的DOM操作和状态突变

**重要特点**：
- 零生产环境影响
- 帮助发现隐藏的代码质量问题
- 为React未来版本升级做准备
- 与React DevTools集成显示详细警告信息`,
      code: `// 基本使用方式
function App() {
  return (
    <React.StrictMode>
      <Header />
      <MainContent />
      <Footer />
    </React.StrictMode>
  );
}

// StrictMode会检测这些常见问题：

// 1. 副作用检测
function ProblematicComponent() {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    // StrictMode会调用这个effect两次来检测副作用
    console.log('Effect called'); // 这会被打印两次
    document.title = \`Count: \${count}\`;
  }, [count]);
}

// 2. 不安全的生命周期方法
class LegacyComponent extends Component {
  // ⚠️ StrictMode会警告这个方法已弃用
  UNSAFE_componentWillMount() {
    this.loadData();
  }
}

// 3. 状态突变检测
function MutationComponent() {
  const [items, setItems] = useState([]);
  
  const addItem = (newItem) => {
    // ❌ StrictMode可能检测到这种突变
    items.push(newItem);
    setItems(items);
    
    // ✅ 正确的不可变更新
    setItems(prev => [...prev, newItem]);
  };
}`
    },
    tags: ['基础概念', '开发工具', '代码质量']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'StrictMode如何检测副作用？为什么要重复调用某些函数？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: 'StrictMode通过故意重复调用函数来检测不纯的副作用，确保组件在重复渲染时行为一致。',
      detailed: `StrictMode的副作用检测机制基于"纯函数"的概念：

**检测原理**：
1. **重复调用机制**：StrictMode会故意执行某些函数两次
2. **纯度验证**：纯函数重复调用应该产生相同结果
3. **副作用暴露**：如果函数有副作用，重复调用会暴露问题

**被重复调用的函数**：
- 组件构造函数
- render方法
- state更新函数（setState的参数）
- useState的初始化函数
- useMemo、useCallback的计算函数

**检测的副作用类型**：
- 状态突变
- DOM直接操作
- 全局变量修改
- 网络请求（应该在useEffect中）
- 定时器设置

**为什么重复调用有效**：
- 纯函数重复调用结果一致
- 有副作用的函数重复调用会产生意外结果
- 模拟React并发模式下的重复渲染情况`,
      code: `// StrictMode检测副作用的示例

// ❌ 有副作用的组件（StrictMode会检测到）
function ProblematicComponent() {
  const [count, setCount] = useState(() => {
    // 这会被调用两次，导致副作用
    console.log('初始化状态'); // 会打印两次
    window.globalCounter = (window.globalCounter || 0) + 1; // 副作用！
    return 0;
  });
  
  // render函数也会被调用两次
  console.log('渲染组件'); // 会打印两次
  
  const increment = () => {
    setCount(prevCount => {
      // setState的参数函数会被调用两次
      console.log('更新状态'); // 会打印两次
      return prevCount + 1;
    });
  };
  
  return <button onClick={increment}>Count: {count}</button>;
}

// ✅ 纯净的组件（StrictMode兼容）
function CleanComponent() {
  const [count, setCount] = useState(() => {
    // 纯净的初始化，重复调用没问题
    return 0;
  });
  
  // 纯净的render函数
  const increment = useCallback(() => {
    setCount(prevCount => prevCount + 1); // 纯净的更新
  }, []);
  
  // 副作用放在useEffect中
  useEffect(() => {
    console.log('副作用在这里是安全的');
    document.title = \`Count: \${count}\`;
  }, [count]);
  
  return <button onClick={increment}>Count: {count}</button>;
}

// StrictMode如何处理useEffect
function EffectComponent() {
  useEffect(() => {
    console.log('Setup'); // 开发模式下会被调用两次
    
    return () => {
      console.log('Cleanup'); // 也会被调用两次
    };
  }, []);
  
  // StrictMode会执行：Setup -> Cleanup -> Setup
  // 确保cleanup函数能正确清理资源
}`
    },
    tags: ['实现原理', '副作用检测', '函数纯度']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 3,
    question: '在大型项目中如何逐步引入StrictMode？有哪些最佳实践？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '实践应用',
    answer: {
      brief: '在大型项目中应该分阶段、分模块逐步引入StrictMode，配合代码审查和团队培训确保顺利迁移。',
      detailed: `大型项目引入StrictMode需要系统性的策略：

**分阶段引入策略**：
1. **评估阶段**：在开发环境启用StrictMode，收集警告信息
2. **修复阶段**：逐个修复检测到的问题
3. **验证阶段**：确保修复后的代码稳定运行
4. **推广阶段**：在CI/CD中强制启用StrictMode

**分模块迁移**：
- 从新功能开始，所有新代码必须StrictMode兼容
- 按业务模块逐个迁移现有代码
- 优先迁移核心模块和公共组件

**团队协作**：
- 建立StrictMode编码规范
- 代码审查中检查StrictMode兼容性
- 培训团队成员理解StrictMode原理

**工具支持**：
- ESLint规则强制StrictMode使用
- CI/CD检查StrictMode警告
- 自动化测试覆盖StrictMode场景`,
      code: `// 1. 分阶段引入策略
function App() {
  const isDev = process.env.NODE_ENV === 'development';
  const enableStrictMode = process.env.REACT_APP_STRICT_MODE === 'true';
  
  const AppContent = (
    <Router>
      <ErrorBoundary>
        <Routes>
          {/* 新功能强制使用StrictMode */}
          <Route path="/new-feature/*" element={
            <React.StrictMode>
              <NewFeatureModule />
            </React.StrictMode>
          } />
          
          {/* 已迁移的模块 */}
          <Route path="/migrated/*" element={
            <React.StrictMode>
              <MigratedModule />
            </React.StrictMode>
          } />
          
          {/* 未迁移的遗留代码 */}
          <Route path="/legacy/*" element={<LegacyModule />} />
        </Routes>
      </ErrorBoundary>
    </Router>
  );
  
  // 开发环境可选择性启用
  return (isDev && enableStrictMode) ? (
    <React.StrictMode>{AppContent}</React.StrictMode>
  ) : AppContent;
}

// 2. ESLint配置强制StrictMode
// .eslintrc.js
module.exports = {
  rules: {
    // 自定义规则：新组件必须在StrictMode下开发
    'custom/require-strict-mode-for-new-components': 'error',
    'react-    'react-  },
  overrides: [
    {
      files: ['src/components/new/**/*.js', 'src/features/**/*.js'],
      rules: {
        'custom/require-strict-mode': 'error'
      }
    }
  ]
};

// 3. 迁移检查工具
function StrictModeChecker() {
  const [migrationStatus, setMigrationStatus] = useState({
    total: 0,
    migrated: 0,
    remaining: 0,
    issues: []
  });
  
  useEffect(() => {
    const checkMigrationStatus = async () => {
      const status = await migrationService.checkStatus();
      setMigrationStatus(status);
    };
    
    checkMigrationStatus();
  }, []);
  
  return (
    <div className="migration-dashboard">
      <h2>StrictMode迁移进度</h2>
      <div className="progress-bar">
        <div 
          className="progress-fill"
          style={{ width: \`\${(migrationStatus.migrated / migrationStatus.total) * 100}%\` }}
        />
      </div>
      <div className="stats">
        <span>已迁移: {migrationStatus.migrated}</span>
        <span>剩余: {migrationStatus.remaining}</span>
      </div>
      
      {migrationStatus.issues.length > 0 && (
        <div className="issues">
          <h3>待修复问题</h3>
          {migrationStatus.issues.map(issue => (
            <div key={issue.id} className="issue-item">
              <span className="severity">{issue.severity}</span>
              <span className="file">{issue.file}</span>
              <span className="description">{issue.description}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// 4. CI/CD集成
// .github/workflows/strict-mode-check.yml
/*
name: StrictMode Compliance Check
on: [push, pull_request]
jobs:
  strict-mode-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run StrictMode compliance check
        run: npm run strict-mode-check
      - name: Check for StrictMode warnings
        run: npm run test:strict-mode
*/`
    },
    tags: ['项目管理', '团队协作', '迁移策略']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 4,
    question: 'StrictMode检测到的常见问题有哪些？如何逐一解决？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '问题排查',
    answer: {
      brief: 'StrictMode常见问题包括副作用泄露、不安全生命周期、状态突变等，每类问题都有对应的解决策略。',
      detailed: `StrictMode检测到的常见问题及解决方案：

**1. 副作用泄露问题**：
- 症状：useEffect没有清理函数，导致内存泄漏
- 解决：添加适当的cleanup函数

**2. 不安全的生命周期方法**：
- 症状：使用UNSAFE_componentWillMount等方法
- 解决：迁移到现代Hook或安全的生命周期方法

**3. 状态突变问题**：
- 症状：直接修改state对象或props
- 解决：使用不可变更新模式

**4. 重复渲染问题**：
- 症状：组件函数内有副作用导致重复调用出错
- 解决：将副作用移到useEffect中

**5. 并发不安全问题**：
- 症状：竞态条件、资源竞争
- 解决：使用AbortController、isCancelled标记等`,
      code: `// 1. 副作用泄露问题及解决
// ❌ 问题：没有清理订阅
function ProblematicSubscription() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    const subscription = dataService.subscribe(setData);
    // 没有清理函数！
  }, []);
  
  return <div>{data}</div>;
}

// ✅ 解决：添加清理函数
function FixedSubscription() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    const subscription = dataService.subscribe(setData);
    
    return () => {
      subscription.unsubscribe(); // 清理订阅
    };
  }, []);
  
  return <div>{data}</div>;
}

// 2. 不安全生命周期方法问题
// ❌ 问题：使用不安全的生命周期
class LegacyComponent extends Component {
  UNSAFE_componentWillMount() {
    this.setState({ loading: true });
    this.fetchData();
  }
  
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.id !== this.props.id) {
      this.fetchData(nextProps.id);
    }
  }
}

// ✅ 解决：迁移到现代Hook
function ModernComponent({ id }) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    setLoading(true);
    fetchData(id).then(result => {
      setData(result);
      setLoading(false);
    });
  }, [id]); // 依赖id变化时重新执行
  
  if (loading) return <div>Loading...</div>;
  return <div>{data}</div>;
}

// 3. 状态突变问题
// ❌ 问题：直接修改状态
function MutatingComponent() {
  const [items, setItems] = useState([]);
  
  const addItem = (newItem) => {
    items.push(newItem); // 直接修改状态！
    setItems(items);
  };
  
  const updateItem = (index, newValue) => {
    items[index] = newValue; // 直接修改状态！
    setItems(items);
  };
}

// ✅ 解决：不可变更新
function ImmutableComponent() {
  const [items, setItems] = useState([]);
  
  const addItem = useCallback((newItem) => {
    setItems(prevItems => [...prevItems, newItem]); // 不可变添加
  }, []);
  
  const updateItem = useCallback((index, newValue) => {
    setItems(prevItems => 
      prevItems.map((item, i) => i === index ? newValue : item)
    ); // 不可变更新
  }, []);
  
  const removeItem = useCallback((index) => {
    setItems(prevItems => 
      prevItems.filter((_, i) => i !== index)
    ); // 不可变删除
  }, []);
}

// 4. 重复渲染问题
// ❌ 问题：render函数中有副作用
function ProblematicRender() {
  const [count, setCount] = useState(0);
  
  // 副作用在render中！
  console.log('Rendering'); // StrictMode会调用两次
  document.title = \`Count: \${count}\`; // 副作用！
  
  return <div>{count}</div>;
}

// ✅ 解决：副作用移到useEffect
function CleanRender() {
  const [count, setCount] = useState(0);
  
  // 副作用在useEffect中
  useEffect(() => {
    console.log('Effect running');
    document.title = \`Count: \${count}\`;
  }, [count]);
  
  return <div>{count}</div>;
}

// 5. 并发安全问题
// ❌ 问题：竞态条件
function RaceConditionComponent({ userId }) {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    fetchUser(userId).then(setUser); // 可能的竞态条件
  }, [userId]);
}

// ✅ 解决：处理竞态条件
function SafeComponent({ userId }) {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    let isCancelled = false;
    
    const fetchUserData = async () => {
      try {
        const userData = await fetchUser(userId);
        if (!isCancelled) {
          setUser(userData);
        }
      } catch (error) {
        if (!isCancelled) {
          console.error('Failed to fetch user:', error);
        }
      }
    };
    
    fetchUserData();
    
    return () => {
      isCancelled = true; // 取消标记
    };
  }, [userId]);
}`
    },
    tags: ['问题排查', '代码修复', '最佳实践']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 5,
    question: 'StrictMode与React并发模式的关系是什么？如何为并发模式做准备？',
    difficulty: 'hard',
    frequency: 'low',
    category: '高级概念',
    answer: {
      brief: 'StrictMode帮助检测与React并发模式不兼容的代码模式，确保应用能够平滑升级到并发渲染。',
      detailed: `StrictMode与React并发模式的关系及准备策略：

**并发模式的挑战**：
1. **可中断渲染**：渲染过程可能被中断和重启
2. **时间分片**：长任务被分解为小片段执行
3. **优先级调度**：高优先级更新可能打断低优先级更新
4. **重复执行**：某些函数可能被重复调用

**StrictMode的预备作用**：
- 通过重复调用模拟并发模式的行为
- 检测不纯的函数和副作用
- 暴露潜在的竞态条件
- 验证组件的幂等性

**并发安全的代码特征**：
- 纯函数组件（render函数无副作用）
- 正确的依赖数组
- 适当的状态管理
- 合理的资源清理

**迁移准备步骤**：
1. 启用StrictMode并修复所有警告
2. 使用Transition API标记非紧急更新
3. 采用Suspense处理异步加载
4. 优化状态结构减少不必要的重渲染`,
      code: `// 1. 并发模式下的组件设计
function ConcurrentSafeComponent({ data, onUpdate }) {
  const [localState, setLocalState] = useState(data);
  const [isPending, startTransition] = useTransition();
  
  // 纯净的render函数，可以安全地重复调用
  const handleUpdate = useCallback((newValue) => {
    // 标记为非紧急更新，可以被中断
    startTransition(() => {
      setLocalState(newValue);
      onUpdate?.(newValue);
    });
  }, [onUpdate]);
  
  // 使用useMemo避免不必要的计算
  const expensiveValue = useMemo(() => {
    return computeExpensiveValue(localState);
  }, [localState]);
  
  return (
    <div>
      <div>Value: {expensiveValue}</div>
      <button 
        onClick={() => handleUpdate(localState + 1)}
        disabled={isPending}
      >
        {isPending ? 'Updating...' : 'Update'}
      </button>
    </div>
  );
}

// 2. Suspense集成准备
function DataFetcher({ id }) {
  // 使用Suspense边界处理异步数据
  const data = useSuspenseQuery({
    queryKey: ['data', id],
    queryFn: () => fetchData(id)
  });
  
  return <DataDisplay data={data} />;
}

function App() {
  return (
    <React.StrictMode>
      <Suspense fallback={<Loading />}>
        <ErrorBoundary>
          <DataFetcher id="123" />
        </ErrorBoundary>
      </Suspense>
    </React.StrictMode>
  );
}

// 3. 状态更新的并发安全性
function ConcurrentStateManager() {
  const [items, setItems] = useState([]);
  const [filter, setFilter] = useState('');
  const [isPending, startTransition] = useTransition();
  
  // 紧急更新：用户输入
  const handleFilterChange = (newFilter) => {
    setFilter(newFilter); // 立即更新，不可被中断
  };
  
  // 非紧急更新：数据处理
  const handleDataUpdate = (newData) => {
    startTransition(() => {
      setItems(newData); // 可以被中断的更新
    });
  };
  
  // 使用useDeferredValue延迟计算
  const deferredFilter = useDeferredValue(filter);
  const filteredItems = useMemo(() => {
    return items.filter(item => 
      item.name.toLowerCase().includes(deferredFilter.toLowerCase())
    );
  }, [items, deferredFilter]);
  
  return (
    <div>
      <input 
        value={filter}
        onChange={(e) => handleFilterChange(e.target.value)}
        placeholder="Filter items..."
      />
      {isPending && <div>Updating...</div>}
      <ItemList items={filteredItems} />
    </div>
  );
}

// 4. 资源管理的并发安全性
function ResourceManager() {
  const [resource, setResource] = useState(null);
  const abortControllerRef = useRef();
  
  useEffect(() => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;
    
    const loadResource = async () => {
      try {
        const data = await fetch('/api/data', { signal });
        const result = await data.json();
        
        // 检查是否仍然有效
        if (!signal.aborted) {
          setResource(result);
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Failed to load resource:', error);
        }
      }
    };
    
    loadResource();
    
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);
  
  return resource ? <ResourceDisplay data={resource} /> : <Loading />;
}

// 5. 并发模式检查清单组件
function ConcurrencyChecklist() {
  const [checks, setChecks] = useState({
    strictModeEnabled: false,
    noSideEffectsInRender: false,
    properDependencies: false,
    transitionUsage: false,
    suspenseIntegration: false,
    resourceCleanup: false
  });
  
  const runConcurrencyChecks = async () => {
    const results = await Promise.all([
      checkStrictMode(),
      analyzeSideEffects(),
      validateDependencies(),
      checkTransitionUsage(),
      validateSuspense(),
      checkResourceCleanup()
    ]);
    
    setChecks({
      strictModeEnabled: results[0],
      noSideEffectsInRender: results[1],
      properDependencies: results[2],
      transitionUsage: results[3],
      suspenseIntegration: results[4],
      resourceCleanup: results[5]
    });
  };
  
  return (
    <div className="concurrency-checklist">
      <h3>并发模式准备检查</h3>
      <CheckItem label="StrictMode已启用" checked={checks.strictModeEnabled} />
      <CheckItem label="Render函数无副作用" checked={checks.noSideEffectsInRender} />
      <CheckItem label="正确的依赖数组" checked={checks.properDependencies} />
      <CheckItem label="使用Transition API" checked={checks.transitionUsage} />
      <CheckItem label="Suspense集成" checked={checks.suspenseIntegration} />
      <CheckItem label="资源正确清理" checked={checks.resourceCleanup} />
      
      <button onClick={runConcurrencyChecks}>
        运行并发检查
      </button>
    </div>
  );
}`
    },
    tags: ['并发模式', '高级概念', '架构设计']
  }
];

export default interviewQuestions;