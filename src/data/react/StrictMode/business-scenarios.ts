import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'legacy-app-modernization',
    title: '遗留应用现代化升级',
    description: '在大型遗留React应用中逐步引入StrictMode，识别和修复潜在问题',
    businessValue: '降低80%的生产bug率，提升代码质量评分至A级，减少60%的维护成本',
    scenario: '某电商公司的React应用已运行3年，包含大量遗留代码和过时的API使用。随着React版本升级和团队扩大，需要系统性地识别和修复代码质量问题，为未来的并发模式做准备。',
    code: `// 第一阶段：在开发环境启用StrictMode
function App() {
  return (
    <React.StrictMode>
      <Router>
        <Layout>
          <Routes>
            <Route path="/products" element={<ProductList />} />
            <Route path="/cart" element={<ShoppingCart />} />
            <Route path="/user" element={<UserProfile />} />
          </Routes>
        </Layout>
      </Router>
    </React.StrictMode>
  );
}

// 发现的问题1：不安全的生命周期方法
class LegacyProductCard extends Component {
  // ⚠️ StrictMode警告：UNSAFE_componentWillMount已弃用
  UNSAFE_componentWillMount() {
    this.loadProductData(this.props.productId);
  }
  
  // ✅ 修复：使用现代Hook重写
}

// 修复后的现代版本
function ModernProductCard({ productId }) {
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const loadProduct = async () => {
      setLoading(true);
      try {
        const data = await fetchProduct(productId);
        setProduct(data);
      } catch (error) {
        console.error('加载商品失败:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadProduct();
  }, [productId]);
  
  if (loading) return <ProductSkeleton />;
  return <ProductDisplay product={product} />;
}

// 发现的问题2：副作用泄漏
function ProblematicShoppingCart() {
  const [items, setItems] = useState([]);
  
  useEffect(() => {
    // ⚠️ StrictMode检测：订阅没有清理
    const subscription = cartService.subscribe((newItems) => {
      setItems(newItems);
    });
    
    // 缺少清理函数导致内存泄漏
  }, []);
  
  return <CartItems items={items} />;
}

// ✅ 修复：正确的订阅清理
function FixedShoppingCart() {
  const [items, setItems] = useState([]);
  
  useEffect(() => {
    const subscription = cartService.subscribe((newItems) => {
      setItems(newItems);
    });
    
    // 正确的清理函数
    return () => {
      subscription.unsubscribe();
    };
  }, []);
  
  return <CartItems items={items} />;
}

// 发现的问题3：状态突变
function ProblematicUserProfile() {
  const [user, setUser] = useState({ name: '', preferences: {} });
  
  const updatePreference = (key, value) => {
    // ⚠️ StrictMode检测：直接修改状态对象
    user.preferences[key] = value;
    setUser(user); // React无法检测到变化
  };
  
  return <UserSettings onUpdate={updatePreference} />;
}

// ✅ 修复：immutable状态更新
function FixedUserProfile() {
  const [user, setUser] = useState({ name: '', preferences: {} });
  
  const updatePreference = useCallback((key, value) => {
    setUser(prevUser => ({
      ...prevUser,
      preferences: {
        ...prevUser.preferences,
        [key]: value
      }
    }));
  }, []);
  
  return <UserSettings onUpdate={updatePreference} />;
}`,
    explanation: 'StrictMode在遗留应用现代化中发挥关键作用，通过暴露隐藏的代码质量问题，帮助团队系统性地升级和改善代码库。这种渐进式改进策略既保证了业务连续性，又为未来的React版本升级做好准备。',
    benefits: [
      '早期发现生产环境潜在bug，降低线上故障率',
      '系统性识别技术债务，制定优化路线图',
      '为React并发模式升级做好代码准备',
      '提升团队代码质量意识和最佳实践采用',
      '通过自动化检测减少人工代码审查工作量'
    ],
    metrics: {
      performance: '应用启动时间优化15%，渲染性能提升20%',
      userExperience: '用户投诉减少65%，页面错误率从0.8%降至0.2%',
      technicalMetrics: '代码质量评分从C提升至A，技术债务减少40%'
    },
    difficulty: 'medium',
    tags: ['遗留系统', '代码质量', '渐进升级']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'new-feature-development',
    title: '新功能开发质量保证',
    description: '在新功能开发中使用StrictMode确保代码质量，建立开发最佳实践',
    businessValue: '新功能缺陷率降低90%，开发周期缩短30%，代码审查效率提升50%',
    scenario: '某金融科技公司正在开发新的投资组合管理功能，需要确保代码质量符合金融级别的可靠性要求。团队决定在开发阶段严格使用StrictMode来确保代码健壮性。',
    code: `// 新功能开发：投资组合管理器
function PortfolioManager() {
  return (
    <React.StrictMode>
      <ErrorBoundary fallback={<ErrorFallback />}>
        <Suspense fallback={<PortfolioSkeleton />}>
          <PortfolioProvider>
            <PortfolioDashboard />
            <AssetAllocation />
            <RiskAnalysis />
            <TransactionHistory />
          </PortfolioProvider>
        </Suspense>
      </ErrorBoundary>
    </React.StrictMode>
  );
}

// StrictMode帮助发现的并发安全问题
function AssetPriceTracker({ symbols }) {
  const [prices, setPrices] = useState({});
  const [isSubscribed, setIsSubscribed] = useState(false);
  
  useEffect(() => {
    let websocket;
    let reconnectTimer;
    
    const connect = () => {
      websocket = new WebSocket('wss://api.prices.com/realtime');
      
      websocket.onopen = () => {
        console.log('价格订阅已连接');
        setIsSubscribed(true);
        // 订阅指定资产价格
        symbols.forEach(symbol => {
          websocket.send(JSON.stringify({ action: 'subscribe', symbol }));
        });
      };
      
      websocket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        // StrictMode会检测这里的状态更新逻辑
        setPrices(prevPrices => ({
          ...prevPrices,
          [data.symbol]: {
            price: data.price,
            change: data.change,
            timestamp: data.timestamp
          }
        }));
      };
      
      websocket.onclose = () => {
        setIsSubscribed(false);
        // 自动重连机制
        reconnectTimer = setTimeout(connect, 5000);
      };
      
      websocket.onerror = (error) => {
        console.error('WebSocket错误:', error);
        setIsSubscribed(false);
      };
    };
    
    connect();
    
    // StrictMode确保清理函数正确执行
    return () => {
      if (websocket) {
        websocket.close();
      }
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
      }
      setIsSubscribed(false);
    };
  }, [symbols]);
  
  return (
    <div className="price-tracker">
      <div className="connection-status">
        状态: {isSubscribed ? '已连接' : '连接中...'}
      </div>
      <div className="price-grid">
        {symbols.map(symbol => (
          <PriceCard
            key={symbol}
            symbol={symbol}
            data={prices[symbol]}
          />
        ))}
      </div>
    </div>
  );
}

// StrictMode检测到的状态管理问题
function RiskCalculator({ portfolio }) {
  const [riskMetrics, setRiskMetrics] = useState(null);
  const [calculating, setCalculating] = useState(false);
  
  // 复杂的风险计算逻辑
  const calculateRisk = useCallback(async (portfolioData) => {
    setCalculating(true);
    
    try {
      // 模拟复杂的风险计算
      const result = await riskAnalysisService.calculate({
        assets: portfolioData.assets,
        timeframe: portfolioData.timeframe,
        riskModel: 'VaR' // Value at Risk
      });
      
      // StrictMode会验证这个状态更新的纯净性
      setRiskMetrics({
        var95: result.var95,
        var99: result.var99,
        sharpeRatio: result.sharpeRatio,
        maxDrawdown: result.maxDrawdown,
        beta: result.beta
      });
    } catch (error) {
      console.error('风险计算失败:', error);
      setRiskMetrics(null);
    } finally {
      setCalculating(false);
    }
  }, []);
  
  useEffect(() => {
    if (portfolio && portfolio.assets.length > 0) {
      calculateRisk(portfolio);
    }
  }, [portfolio, calculateRisk]);
  
  if (calculating) {
    return <div>正在计算风险指标...</div>;
  }
  
  if (!riskMetrics) {
    return <div>暂无风险数据</div>;
  }
  
  return (
    <div className="risk-metrics">
      <h3>风险分析</h3>
      <div className="metrics-grid">
        <MetricCard title="VaR (95%)" value={riskMetrics.var95} format="percentage" />
        <MetricCard title="VaR (99%)" value={riskMetrics.var99} format="percentage" />
        <MetricCard title="夏普比率" value={riskMetrics.sharpeRatio} format="decimal" />
        <MetricCard title="最大回撤" value={riskMetrics.maxDrawdown} format="percentage" />
        <MetricCard title="Beta系数" value={riskMetrics.beta} format="decimal" />
      </div>
    </div>
  );
}

// StrictMode验证的自定义Hook
function usePortfolioOptimization(portfolio, constraints) {
  const [optimizedWeights, setOptimizedWeights] = useState([]);
  const [optimizing, setOptimizing] = useState(false);
  
  useEffect(() => {
    let isCancelled = false;
    
    const optimize = async () => {
      if (!portfolio || portfolio.assets.length === 0) return;
      
      setOptimizing(true);
      
      try {
        const result = await optimizationService.optimize({
          assets: portfolio.assets,
          expectedReturns: portfolio.expectedReturns,
          covarianceMatrix: portfolio.covarianceMatrix,
          constraints: constraints
        });
        
        // StrictMode确保这个检查有效防止竞态条件
        if (!isCancelled) {
          setOptimizedWeights(result.weights);
        }
      } catch (error) {
        if (!isCancelled) {
          console.error('组合优化失败:', error);
          setOptimizedWeights([]);
        }
      } finally {
        if (!isCancelled) {
          setOptimizing(false);
        }
      }
    };
    
    optimize();
    
    return () => {
      isCancelled = true;
    };
  }, [portfolio, constraints]);
  
  return { optimizedWeights, optimizing };
}`,
    explanation: '在新功能开发中，StrictMode作为质量守护者，确保每一行代码都符合React最佳实践。特别是在金融等对可靠性要求极高的领域，StrictMode能够在开发阶段就发现潜在的竞态条件、内存泄漏等问题。',
    benefits: [
      '在开发阶段即发现并发安全问题，避免生产环境数据不一致',
      '确保WebSocket连接等异步资源得到正确清理',
      '验证复杂状态计算的纯净性和可预测性',
      '提升金融级应用的可靠性和稳定性',
      '建立团队代码质量标准和开发规范'
    ],
    metrics: {
      performance: '实时数据更新延迟降低45%，计算准确性提升到99.9%',
      userExperience: '功能稳定性达到金融级标准，用户投诉为零',
      technicalMetrics: '代码覆盖率95%，自动化测试通过率99.8%'
    },
    difficulty: 'hard',
    tags: ['金融科技', '质量保证', '并发安全']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'team-development-standards',
    title: '团队开发标准化建设',
    description: '在团队开发流程中集成StrictMode，建立代码质量标准和最佳实践',
    businessValue: '团队效率提升40%，代码审查时间减少60%，新人上手时间缩短50%',
    scenario: '某创业公司技术团队快速扩张，从5人增长到20人。为了保持代码质量一致性，避免技术债务累积，需要建立标准化的开发流程和质量保证机制。',
    code: `// 项目模板：强制启用StrictMode
// create-react-app模板定制
function App() {
  // 在所有环境都启用StrictMode
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const AppContent = (
    <Router>
      <ErrorBoundary>
        <AuthProvider>
          <ThemeProvider>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/projects" element={<ProjectList />} />
              <Route path="/team" element={<TeamManagement />} />
            </Routes>
          </ThemeProvider>
        </AuthProvider>
      </ErrorBoundary>
    </Router>
  );
  
  // 开发环境强制使用StrictMode
  return isDevelopment ? (
    <React.StrictMode>
      {AppContent}
    </React.StrictMode>
  ) : AppContent;
}

// ESLint规则：检测StrictMode使用
// .eslintrc.js
module.exports = {
  rules: {
    // 自定义规则：确保根组件使用StrictMode
    'custom/require-strict-mode': 'error',
    // React Hooks相关规则，配合StrictMode使用
    'react-    'react-  }
};

// 代码审查检查列表组件
function CodeReviewChecklist() {
  const [checks, setChecks] = useState({
    strictModeEnabled: false,
    noUnsafeLifecycles: false,
    properCleanup: false,
    immutableUpdates: false,
    hookDependencies: false
  });
  
  // StrictMode确保这个effect的纯净性
  useEffect(() => {
    // 模拟代码质量检查
    const runQualityChecks = async () => {
      const results = await codeQualityService.analyze();
      setChecks({
        strictModeEnabled: results.hasStrictMode,
        noUnsafeLifecycles: results.noDeprecatedAPIs,
        properCleanup: results.hasProperCleanup,
        immutableUpdates: results.immutableStateUpdates,
        hookDependencies: results.correctDependencies
      });
    };
    
    runQualityChecks();
  }, []);
  
  return (
    <div className="code-review-checklist">
      <h3>代码质量检查清单</h3>
      <CheckItem 
        label="StrictMode已启用" 
        checked={checks.strictModeEnabled}
        severity="error"
      />
      <CheckItem 
        label="无不安全的生命周期方法" 
        checked={checks.noUnsafeLifecycles}
        severity="warning"
      />
      <CheckItem 
        label="正确的资源清理" 
        checked={checks.properCleanup}
        severity="error"
      />
      <CheckItem 
        label="不可变状态更新" 
        checked={checks.immutableUpdates}
        severity="warning"
      />
      <CheckItem 
        label="Hook依赖项正确" 
        checked={checks.hookDependencies}
        severity="info"
      />
    </div>
  );
}

// 新员工培训：StrictMode教学组件
function StrictModeTraining() {
  const [currentStep, setCurrentStep] = useState(0);
  const [practiceResults, setPracticeResults] = useState([]);
  
  const trainingSteps = [
    {
      title: 'StrictMode基础概念',
      content: '了解StrictMode的作用和重要性',
      practice: <BasicConceptPractice />
    },
    {
      title: '副作用检测',
      content: '学习如何处理重复调用检测出的问题',
      practice: <SideEffectPractice />
    },
    {
      title: '生命周期方法',
      content: '掌握现代React模式替代不安全的API',
      practice: <LifecyclePractice />
    },
    {
      title: '状态管理最佳实践',
      content: '编写StrictMode兼容的状态更新逻辑',
      practice: <StateManagementPractice />
    }
  ];
  
  const handlePracticeComplete = useCallback((stepIndex, result) => {
    setPracticeResults(prev => [
      ...prev.filter(r => r.step !== stepIndex),
      { step: stepIndex, result, timestamp: Date.now() }
    ]);
  }, []);
  
  return (
    <React.StrictMode>
      <div className="training-module">
        <h2>React StrictMode 培训</h2>
        
        <ProgressBar 
          current={currentStep} 
          total={trainingSteps.length}
        />
        
        <div className="training-content">
          <h3>{trainingSteps[currentStep].title}</h3>
          <p>{trainingSteps[currentStep].content}</p>
          
          <div className="practice-area">
            {React.cloneElement(
              trainingSteps[currentStep].practice,
              { 
                onComplete: (result) => handlePracticeComplete(currentStep, result)
              }
            )}
          </div>
        </div>
        
        <div className="training-navigation">
          <button 
            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
            disabled={currentStep === 0}
          >
            上一步
          </button>
          <button 
            onClick={() => setCurrentStep(Math.min(trainingSteps.length - 1, currentStep + 1))}
            disabled={currentStep === trainingSteps.length - 1}
          >
            下一步
          </button>
        </div>
        
        <div className="progress-summary">
          <h4>学习进度</h4>
          {practiceResults.map(result => (
            <div key={result.step} className="progress-item">
              步骤 {result.step + 1}: {result.result.score}/100 分
            </div>
          ))}
        </div>
      </div>
    </React.StrictMode>
  );
}

// CI/CD流水线集成：自动质量检查
function QualityGateComponent() {
  const [qualityReport, setQualityReport] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  
  useEffect(() => {
    // 模拟CI/CD质量门检查
    const runQualityGate = async () => {
      setIsAnalyzing(true);
      
      try {
        const report = await qualityGateService.analyze({
          strictModeCompliance: true,
          codeQualityThreshold: 80,
          performanceThreshold: 90
        });
        
        setQualityReport(report);
      } catch (error) {
        console.error('质量门检查失败:', error);
      } finally {
        setIsAnalyzing(false);
      }
    };
    
    runQualityGate();
  }, []);
  
  if (isAnalyzing) {
    return <div>正在进行质量分析...</div>;
  }
  
  return (
    <div className="quality-gate">
      <h3>代码质量门禁报告</h3>
      {qualityReport && (
        <div className="quality-metrics">
          <QualityMetric 
            name="StrictMode合规性" 
            value={qualityReport.strictModeCompliance}
            threshold={100}
          />
          <QualityMetric 
            name="代码质量评分" 
            value={qualityReport.codeQuality}
            threshold={80}
          />
          <QualityMetric 
            name="性能评分" 
            value={qualityReport.performance}
            threshold={90}
          />
        </div>
      )}
    </div>
  );
}`,
    explanation: 'StrictMode在团队标准化建设中扮演了质量基石的角色。通过将其集成到开发流程、代码审查、培训体系和CI/CD流水线中，确保整个团队都能编写高质量、符合最佳实践的React代码。',
    benefits: [
      '建立统一的代码质量标准，减少团队间的分歧',
      '新员工快速掌握React最佳实践，缩短学习曲线',
      '自动化质量检查减少人工审查工作量',
      '提前发现问题降低维护成本',
      '培养团队的质量意识和技术素养'
    ],
    metrics: {
      performance: '代码审查效率提升60%，构建失败率降低75%',
      userExperience: '团队协作效率提升40%，项目交付质量提升85%',
      technicalMetrics: '代码质量评分提升至A级，技术债务控制在5%以内'
    },
    difficulty: 'medium',
    tags: ['团队协作', '标准化', '质量保证']
  }
];

export default businessScenarios;