import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'strict-mode-console-logs',
    question: '为什么启用StrictMode后控制台有很多重复的日志输出？',
    answer: 'StrictMode会故意重复调用某些函数来检测副作用，导致console.log等调试语句被执行多次。这是正常行为，用于帮助发现不纯的代码。',
    code: `// 问题：StrictMode导致重复日志
function Component() {
  const [count, setCount] = useState(0);
  
  // 这个console.log会被调用两次
  console.log('组件渲染中...');
  
  useEffect(() => {
    // 这个effect也会被重复执行
    console.log('Effect执行');
    
    return () => {
      console.log('Effect清理');
    };
  }, []);
  
  return <div>Count: {count}</div>;
}

// 解决方案1：条件性调试
function BetterComponent() {
  const [count, setCount] = useState(0);
  
  // 开发时调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log('组件渲染中...', { count });
  }
  
  useEffect(() => {
    console.log('Effect执行 - 这在StrictMode下是正常的');
    
    return () => {
      console.log('Effect清理');
    };
  }, []);
  
  return <div>Count: {count}</div>;
}

// 解决方案2：使用React DevTools Profiler
function ProfiledComponent() {
  const [count, setCount] = useState(0);
  
  // 使用React DevTools而不是console.log
  useEffect(() => {
    // 实际的副作用逻辑
    document.title = \`Count: \${count}\`;
  }, [count]);
  
  return <div>Count: {count}</div>;
}`,
    tags: ['调试', '控制台输出'],
    relatedQuestions: ['effect重复执行', 'StrictMode行为']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'effect-cleanup-issues',
    question: 'useEffect在StrictMode下执行了两次，导致订阅或定时器出现问题？',
    answer: '这表明effect缺少适当的清理函数。StrictMode故意重复执行effect来检测资源泄漏，正确的effect应该包含清理逻辑。',
    code: `// ❌ 问题：没有清理函数的effect
function ProblematicComponent() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    // 每次都创建新的订阅，但没有清理旧的
    const subscription = eventBus.subscribe('data-update', setData);
    
    // 设置定时器但没有清理
    const timer = setInterval(() => {
      console.log('定时任务执行');
    }, 1000);
    
    // 添加事件监听器但没有清理
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    
    // 缺少清理函数！
  }, []);
  
  return <div>{data}</div>;
}

// ✅ 解决方案：正确的清理函数
function FixedComponent() {
  const [data, setData] = useState(null);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  
  useEffect(() => {
    console.log('Effect setup'); // StrictMode下会执行两次
    
    // 创建订阅
    const subscription = eventBus.subscribe('data-update', setData);
    
    // 设置定时器
    const timer = setInterval(() => {
      console.log('定时任务执行');
    }, 1000);
    
    // 添加事件监听器
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    
    // 重要：返回清理函数
    return () => {
      console.log('Effect cleanup'); // StrictMode下也会执行两次
      
      // 清理订阅
      subscription.unsubscribe();
      
      // 清理定时器
      clearInterval(timer);
      
      // 清理事件监听器
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  return <div>{data} - Window: {windowWidth}px</div>;
}

// 高级模式：自定义Hook处理复杂清理
function useWebSocketConnection(url) {
  const [socket, setSocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  
  useEffect(() => {
    let ws = null;
    let reconnectTimer = null;
    
    const connect = () => {
      ws = new WebSocket(url);
      setSocket(ws);
      
      ws.onopen = () => setConnectionStatus('connected');
      ws.onclose = () => {
        setConnectionStatus('disconnected');
        // 自动重连
        reconnectTimer = setTimeout(connect, 3000);
      };
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('error');
      };
    };
    
    connect();
    
    // 完整的清理逻辑
    return () => {
      if (ws) {
        ws.close();
      }
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
      }
      setSocket(null);
      setConnectionStatus('disconnected');
    };
  }, [url]);
  
  return { socket, connectionStatus };
}`,
    tags: ['useEffect', '资源清理', '内存泄漏'],
    relatedQuestions: ['订阅管理', '定时器清理']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'unsafe-lifecycle-warnings',
    question: 'StrictMode警告使用了不安全的生命周期方法，如何迁移？',
    answer: '需要将UNSAFE_componentWillMount、UNSAFE_componentWillUpdate等方法迁移到现代的Hook或安全的生命周期方法。',
    code: `// ❌ 老旧的Class组件使用不安全生命周期
class LegacyComponent extends Component {
  constructor(props) {
    super(props);
    this.state = { data: null, loading: false };
  }
  
  // ⚠️ StrictMode警告：这个方法已经不安全
  UNSAFE_componentWillMount() {
    this.setState({ loading: true });
    this.fetchData(this.props.id);
  }
  
  // ⚠️ StrictMode警告：这个方法也不安全
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.id !== this.props.id) {
      this.setState({ loading: true });
      this.fetchData(nextProps.id);
    }
  }
  
  // ⚠️ StrictMode警告：这个方法同样不安全
  UNSAFE_componentWillUpdate() {
    console.log('组件即将更新');
  }
  
  async fetchData(id) {
    try {
      const response = await fetch(\`/api/data/\${id}\`);
      const data = await response.json();
      this.setState({ data, loading: false });
    } catch (error) {
      this.setState({ loading: false });
      console.error('数据加载失败:', error);
    }
  }
  
  render() {
    const { data, loading } = this.state;
    
    if (loading) return <div>Loading...</div>;
    return <div>{data ? data.name : 'No data'}</div>;
  }
}

// ✅ 迁移方案1：转换为函数组件 + Hooks
function ModernFunctionComponent({ id }) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  // 替代UNSAFE_componentWillMount和UNSAFE_componentWillReceiveProps
  useEffect(() => {
    let isCancelled = false;
    
    const fetchData = async () => {
      setLoading(true);
      
      try {
        const response = await fetch(\`/api/data/\${id}\`);
        const result = await response.json();
        
        if (!isCancelled) {
          setData(result);
          setLoading(false);
        }
      } catch (error) {
        if (!isCancelled) {
          setLoading(false);
          console.error('数据加载失败:', error);
        }
      }
    };
    
    fetchData();
    
    return () => {
      isCancelled = true; // 防止竞态条件
    };
  }, [id]); // 依赖id变化时重新执行
  
  if (loading) return <div>Loading...</div>;
  return <div>{data ? data.name : 'No data'}</div>;
}

// ✅ 迁移方案2：如果必须保持Class组件，使用安全的生命周期
class ModernClassComponent extends Component {
  constructor(props) {
    super(props);
    this.state = { data: null, loading: false };
  }
  
  // 使用componentDidMount替代UNSAFE_componentWillMount
  componentDidMount() {
    this.setState({ loading: true });
    this.fetchData(this.props.id);
  }
  
  // 使用componentDidUpdate替代UNSAFE_componentWillReceiveProps
  componentDidUpdate(prevProps) {
    if (prevProps.id !== this.props.id) {
      this.setState({ loading: true });
      this.fetchData(this.props.id);
    }
  }
  
  // 使用getSnapshotBeforeUpdate替代UNSAFE_componentWillUpdate
  getSnapshotBeforeUpdate(prevProps, prevState) {
    console.log('组件即将更新');
    return null;
  }
  
  componentDidUpdate(prevProps, prevState, snapshot) {
    // 处理snapshot数据
  }
  
  async fetchData(id) {
    try {
      const response = await fetch(\`/api/data/\${id}\`);
      const data = await response.json();
      this.setState({ data, loading: false });
    } catch (error) {
      this.setState({ loading: false });
      console.error('数据加载失败:', error);
    }
  }
  
  render() {
    const { data, loading } = this.state;
    
    if (loading) return <div>Loading...</div>;
    return <div>{data ? data.name : 'No data'}</div>;
  }
}

// ✅ 高级模式：自定义Hook封装数据获取逻辑
function useDataFetcher(id) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    let isCancelled = false;
    
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch(\`/api/data/\${id}\`);
        if (!response.ok) {
          throw new Error(\`HTTP error! status: \${response.status}\`);
        }
        const result = await response.json();
        
        if (!isCancelled) {
          setData(result);
        }
      } catch (err) {
        if (!isCancelled) {
          setError(err.message);
        }
      } finally {
        if (!isCancelled) {
          setLoading(false);
        }
      }
    };
    
    if (id) {
      fetchData();
    }
    
    return () => {
      isCancelled = true;
    };
  }, [id]);
  
  return { data, loading, error };
}

// 使用自定义Hook的组件
function ComponentWithCustomHook({ id }) {
  const { data, loading, error } = useDataFetcher(id);
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  return <div>{data ? data.name : 'No data'}</div>;
}`,
    tags: ['生命周期', '组件迁移', 'Class组件'],
    relatedQuestions: ['Hook迁移', 'componentDidMount替代']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'state-mutation-detection',
    question: 'StrictMode下组件状态更新有时不生效，是什么原因？',
    answer: '这通常是因为直接修改了state对象而不是创建新的对象。StrictMode通过重复调用来检测这种状态突变问题。',
    code: `// ❌ 问题：直接修改state对象
function ProblematicComponent() {
  const [user, setUser] = useState({
    name: 'John',
    preferences: { theme: 'light', language: 'en' },
    items: []
  });
  
  const updateTheme = (newTheme) => {
    // ❌ 直接修改state对象
    user.preferences.theme = newTheme;
    setUser(user); // React可能检测不到变化
  };
  
  const addItem = (newItem) => {
    // ❌ 直接修改数组
    user.items.push(newItem);
    setUser(user); // React可能检测不到变化
  };
  
  const updateName = (newName) => {
    // ❌ 直接修改对象属性
    user.name = newName;
    setUser(user); // React可能检测不到变化
  };
  
  return (
    <div>
      <p>用户: {user.name}</p>
      <p>主题: {user.preferences.theme}</p>
      <p>项目数量: {user.items.length}</p>
      
      <button onClick={() => updateTheme('dark')}>
        切换主题
      </button>
      <button onClick={() => addItem('新项目')}>
        添加项目
      </button>
      <button onClick={() => updateName('Jane')}>
        更改姓名
      </button>
    </div>
  );
}

// ✅ 解决方案：不可变更新模式
function FixedComponent() {
  const [user, setUser] = useState({
    name: 'John',
    preferences: { theme: 'light', language: 'en' },
    items: []
  });
  
  const updateTheme = useCallback((newTheme) => {
    setUser(prevUser => ({
      ...prevUser,
      preferences: {
        ...prevUser.preferences,
        theme: newTheme
      }
    }));
  }, []);
  
  const addItem = useCallback((newItem) => {
    setUser(prevUser => ({
      ...prevUser,
      items: [...prevUser.items, newItem]
    }));
  }, []);
  
  const updateName = useCallback((newName) => {
    setUser(prevUser => ({
      ...prevUser,
      name: newName
    }));
  }, []);
  
  return (
    <div>
      <p>用户: {user.name}</p>
      <p>主题: {user.preferences.theme}</p>
      <p>项目数量: {user.items.length}</p>
      
      <button onClick={() => updateTheme('dark')}>
        切换主题
      </button>
      <button onClick={() => addItem('新项目')}>
        添加项目
      </button>
      <button onClick={() => updateName('Jane')}>
        更改姓名
      </button>
    </div>
  );
}

// ✅ 高级解决方案：使用Immer简化不可变更新
import { produce } from 'immer';

function ImmerComponent() {
  const [user, setUser] = useState({
    name: 'John',
    preferences: { theme: 'light', language: 'en' },
    items: [],
    profile: {
      bio: '',
      avatar: null,
      socialLinks: []
    }
  });
  
  const updateUser = useCallback((updater) => {
    setUser(produce(updater));
  }, []);
  
  const updateTheme = useCallback((newTheme) => {
    updateUser(draft => {
      draft.preferences.theme = newTheme;
    });
  }, [updateUser]);
  
  const addItem = useCallback((newItem) => {
    updateUser(draft => {
      draft.items.push(newItem);
    });
  }, [updateUser]);
  
  const updateProfile = useCallback((field, value) => {
    updateUser(draft => {
      draft.profile[field] = value;
    });
  }, [updateUser]);
  
  const addSocialLink = useCallback((link) => {
    updateUser(draft => {
      draft.profile.socialLinks.push(link);
    });
  }, [updateUser]);
  
  return (
    <div>
      <p>用户: {user.name}</p>
      <p>主题: {user.preferences.theme}</p>
      <p>项目数量: {user.items.length}</p>
      <p>社交链接: {user.profile.socialLinks.length}</p>
      
      <button onClick={() => updateTheme('dark')}>切换主题</button>
      <button onClick={() => addItem('新项目')}>添加项目</button>
      <button onClick={() => updateProfile('bio', '新的个人简介')}>
        更新简介
      </button>
      <button onClick={() => addSocialLink('https://twitter.com/user')}>
        添加社交链接
      </button>
    </div>
  );
}

// 检测状态突变的工具函数
function useStateMutationDetector(state, stateName = 'state') {
  const previousStateRef = useRef();
  
  useEffect(() => {
    if (previousStateRef.current) {
      // 检查是否发生了状态突变
      if (previousStateRef.current === state) {
        console.warn(
          \`⚠️ 检测到可能的状态突变：\${stateName}\`,
          '同一个对象引用被重复使用，可能是直接修改了state'
        );
      }
    }
    
    // 深拷贝当前状态作为参考
    previousStateRef.current = JSON.parse(JSON.stringify(state));
  }, [state, stateName]);
}

// 使用状态突变检测器
function ComponentWithMutationDetector() {
  const [data, setData] = useState({ count: 0, items: [] });
  
  // 开发环境下检测状态突变
  if (process.env.NODE_ENV === 'development') {
    useStateMutationDetector(data, 'data');
  }
  
  const updateCount = () => {
    setData(prev => ({ ...prev, count: prev.count + 1 }));
  };
  
  return (
    <div>
      <p>Count: {data.count}</p>
      <button onClick={updateCount}>Increment</button>
    </div>
  );
}`,
    tags: ['状态管理', '不可变更新', 'Immer'],
    relatedQuestions: ['对象引用相等', '状态更新检测']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
    completionStatus: '内容已完成',
    
    id: 'strict-mode-performance-impact',
    question: 'StrictMode会影响应用性能吗？如何在开发中平衡调试和性能？',
    answer: 'StrictMode仅在开发模式下工作，生产环境完全移除，不会影响最终用户性能。开发时可以通过配置和工具优化来平衡调试需求和开发体验。',
    code: `// 1. 条件性启用StrictMode
function App() {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const enableStrictMode = localStorage.getItem('enable-strict-mode') !== 'false';
  
  const AppContent = (
    <Router>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/profile" element={<Profile />} />
      </Routes>
    </Router>
  );
  
  // 可以通过环境变量或本地存储控制
  return (isDevelopment && enableStrictMode) ? (
    <React.StrictMode>
      {AppContent}
    </React.StrictMode>
  ) : AppContent;
}

// 2. 开发时性能监控工具
function PerformanceMonitor({ children }) {
  const [renderTimes, setRenderTimes] = useState([]);
  const renderStartTime = useRef();
  
  useLayoutEffect(() => {
    renderStartTime.current = performance.now();
  });
  
  useEffect(() => {
    if (renderStartTime.current) {
      const renderTime = performance.now() - renderStartTime.current;
      setRenderTimes(prev => [...prev.slice(-9), renderTime]);
      
      // 警告性能问题
      if (renderTime > 16) { // 超过一帧时间
        console.warn(\`⚠️ 慢渲染检测：\${renderTime.toFixed(2)}ms\`);
      }
    }
  });
  
  // 开发环境显示性能指标
  const avgRenderTime = renderTimes.length > 0 
    ? renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length 
    : 0;
  
  return (
    <>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          position: 'fixed',
          top: 0,
          right: 0,
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '8px',
          fontSize: '12px',
          zIndex: 9999
        }}>
          渲染时间: {avgRenderTime.toFixed(2)}ms
        </div>
      )}
    </>
  );
}

// 3. 智能调试组件
function SmartDebugger({ enabled = true, children }) {
  const [debugInfo, setDebugInfo] = useState({
    renderCount: 0,
    lastRenderTime: 0,
    propsChanges: [],
    stateChanges: []
  });
  
  const prevPropsRef = useRef();
  
  useEffect(() => {
    if (!enabled || process.env.NODE_ENV !== 'development') return;
    
    setDebugInfo(prev => ({
      ...prev,
      renderCount: prev.renderCount + 1,
      lastRenderTime: Date.now()
    }));
  });
  
  // 只在需要时显示调试信息
  const showDebugInfo = debugInfo.renderCount > 10 || 
    (debugInfo.renderCount > 5 && performance.now() - debugInfo.lastRenderTime < 100);
  
  if (process.env.NODE_ENV !== 'development' || !enabled || !showDebugInfo) {
    return children;
  }
  
  return (
    <div style={{ border: '2px solid orange', padding: '4px' }}>
      <div style={{ fontSize: '10px', color: 'orange' }}>
        DEBUG: 渲染次数 {debugInfo.renderCount}
      </div>
      {children}
    </div>
  );
}

// 4. 开发环境配置工具
function DevelopmentConfig() {
  const [config, setConfig] = useState({
    strictMode: localStorage.getItem('dev-strict-mode') !== 'false',
    performanceMonitor: localStorage.getItem('dev-perf-monitor') === 'true',
    debugComponents: localStorage.getItem('dev-debug-components') === 'true',
    verboseLogging: localStorage.getItem('dev-verbose-logging') === 'true'
  });
  
  const updateConfig = (key, value) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    localStorage.setItem(\`dev-\${key.replace(/([A-Z])/g, '-$1').toLowerCase()}\`, value);
    
    // 提示需要刷新页面
    if (key === 'strictMode') {
      alert('StrictMode设置已更改，请刷新页面生效');
    }
  };
  
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }
  
  return (
    <div style={{
      position: 'fixed',
      bottom: 20,
      left: 20,
      background: 'white',
      border: '1px solid #ccc',
      padding: '12px',
      borderRadius: '4px',
      fontSize: '12px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      zIndex: 1000
    }}>
      <h4>开发配置</h4>
      <label>
        <input
          type="checkbox"
          checked={config.strictMode}
          onChange={(e) => updateConfig('strictMode', e.target.checked)}
        />
        启用 StrictMode
      </label>
      <br />
      <label>
        <input
          type="checkbox"
          checked={config.performanceMonitor}
          onChange={(e) => updateConfig('performanceMonitor', e.target.checked)}
        />
        性能监控
      </label>
      <br />
      <label>
        <input
          type="checkbox"
          checked={config.debugComponents}
          onChange={(e) => updateConfig('debugComponents', e.target.checked)}
        />
        组件调试
      </label>
      <br />
      <label>
        <input
          type="checkbox"
          checked={config.verboseLogging}
          onChange={(e) => updateConfig('verboseLogging', e.target.checked)}
        />
        详细日志
      </label>
    </div>
  );
}

// 5. 生产环境构建验证
function ProductionBuildChecker() {
  useEffect(() => {
    if (process.env.NODE_ENV === 'production') {
      // 验证StrictMode相关代码是否被正确移除
      const hasStrictModeTrace = document.documentElement.outerHTML.includes('StrictMode');
      
      if (hasStrictModeTrace) {
        console.warn('⚠️ 生产环境检测到StrictMode痕迹，请检查构建配置');
      }
      
      // 验证开发专用代码是否被移除
      const hasDevOnlyCode = window.__DEV__ || window.__REACT_DEVTOOLS_GLOBAL_HOOK__;
      
      if (hasDevOnlyCode) {
        console.warn('⚠️ 生产环境检测到开发工具痕迹');
      }
    }
  }, []);
  
  return null;
}

// 使用示例
function OptimizedApp() {
  return (
    <div>
      <PerformanceMonitor>
        <SmartDebugger enabled={process.env.NODE_ENV === 'development'}>
          <Router>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/about" element={<About />} />
            </Routes>
          </Router>
        </SmartDebugger>
      </PerformanceMonitor>
      
      <DevelopmentConfig />
      <ProductionBuildChecker />
    </div>
  );
}`,
    tags: ['性能优化', '开发配置', '构建优化'],
    relatedQuestions: ['开发环境配置', '生产构建']
  }
];

export default commonQuestions;