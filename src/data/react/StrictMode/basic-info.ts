import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容完成后改为"内容已完成"
  completionStatus: '内容已完成',
  
  definition: "React.StrictMode是React中用于启用额外开发时检查的组件包装器",
  
  introduction: `React.StrictMode是React 16.3引入的开发模式组件，专门用于帮助开发者发现应用中的潜在问题。它通过故意重复调用某些函数、激活额外的检查和警告来暴露副作用和不安全的生命周期方法。StrictMode只在开发模式下工作，不会影响生产构建，是提升React应用质量的重要工具。`,

  syntax: `import React from 'react';

function MyApp() {
  return (
    <React.StrictMode>
      <ComponentTree />
    </React.StrictMode>
  );
}`,

  quickExample: `function App() {
  return (
    <React.StrictMode>
      {/* StrictMode会对这些组件进行额外的开发时检查 */}
      <Header />
      <MainContent />
      <Footer />
    </React.StrictMode>
  );
}

// StrictMode会检测这个组件中的潜在问题
function ProblematicComponent() {
  const [count, setCount] = useState(0);
  
  // StrictMode会故意重复调用这个effect来检测副作用
  useEffect(() => {
    console.log('这个effect会被调用两次');
    document.title = \`Count: \${count}\`;
  }, [count]);
  
  return <div>Count: {count}</div>;
}`,

  scenarioDiagram: `graph TD
    A[React.StrictMode包装] --> B[开发时检查]
    A --> C[生产环境无影响]
    A --> D[问题早期发现]

    B --> B1[重复函数调用检测]
    B --> B2[不安全生命周期警告]
    B --> B3[副作用检测]

    C --> C1[零性能开销]
    C --> C2[自动移除检查]
    C --> C3[正常渲染行为]

    D --> D1[状态管理问题]
    D --> D2[内存泄漏风险]
    D --> D3[并发模式兼容性]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "children",
      type: "ReactNode",
      required: true,
      description: "需要进行严格模式检查的组件树",
      example: "<App /> 或任何React组件"
    }
  ],
  
  returnValue: {
    type: "JSX.Element",
    description: "返回包装后的组件树，在开发模式下启用额外检查",
    example: "在开发模式下，子组件会接受额外的检查和验证"
  },
  
  keyFeatures: [
    {
      title: "副作用检测",
      description: "通过重复调用函数来检测不纯的副作用和状态突变",
      benefit: "帮助开发者编写更可靠、可预测的组件代码"
    },
    {
      title: "过时API警告",
      description: "警告使用已弃用的生命周期方法和不安全的模式",
      benefit: "促进代码现代化，提前适配React未来版本"
    },
    {
      title: "并发模式准备",
      description: "检测与React并发特性不兼容的代码模式",
      benefit: "确保应用能够平滑升级到React的并发渲染"
    },
    {
      title: "零生产影响",
      description: "所有检查仅在开发模式运行，生产构建完全移除",
      benefit: "获得开发时的安全保障而不影响用户体验"
    }
  ],
  
  limitations: [
    "仅在开发模式下工作，生产环境无任何效果",
    "会导致某些函数被重复调用，可能影响开发时性能",
    "可能产生大量控制台警告，需要逐个处理",
    "对于大型现有项目，可能暴露出大量历史遗留问题"
  ],
  
  bestPractices: [
    "在应用的根组件处使用StrictMode包装整个应用",
    "逐步修复StrictMode警告，而不是关闭StrictMode",
    "在团队开发中确保所有开发者都启用StrictMode",
    "将StrictMode修复作为代码质量的重要指标",
    "使用StrictMode来验证自定义Hook的纯净性"
  ],
  
  warnings: [
    "不要因为控制台警告多就禁用StrictMode",
    "不要在生产代码中依赖StrictMode的行为",
    "注意StrictMode会使某些副作用执行两次"
  ]
};

export default basicInfo;