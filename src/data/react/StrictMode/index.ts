import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const StrictModeData: ApiItem = {
  id: 'StrictMode',
  title: 'StrictMode',
  description: 'React.StrictMode是React中用于启用额外开发时检查的组件包装器',
  category: 'React Components',
  difficulty: 'medium',
  
  syntax: `import React from 'react';

function MyApp() {
  return (
    <React.StrictMode>
      <ComponentTree />
    </React.StrictMode>
  );
}`,
  example: `function App() {
  return (
    <React.StrictMode>
      {/* StrictMode会对这些组件进行额外的开发时检查 */}
      <Header />
      <MainContent />
      <Footer />
    </React.StrictMode>
  );
}`,
  notes: '仅在开发模式下工作，生产环境无任何效果',
  
  version: 'React 16.3.0+',
  tags: ["React","Component","Development","StrictMode"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default StrictModeData;