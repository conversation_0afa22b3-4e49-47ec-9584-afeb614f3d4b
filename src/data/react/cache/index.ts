import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const cacheData: ApiItem = {
  id: 'cache',
  title: 'cache',
  description: 'React中用于函数缓存的API，专门用于缓存函数的执行结果，避免重复计算，提升应用性能。通过记忆化技术，将函数的输入参数和输出结果建立映射关系，实现智能的函数级缓存优化。',
  category: 'React API',
  difficulty: 'medium',

  syntax: `cache<Args, Return>(fn: (...args: Args) => Return): (...args: Args) => Return`,
  example: `const cachedFetch = cache(async (url: string) => {
  const response = await fetch(url);
  return response.json();
});`,
  notes: '专门用于函数缓存和性能优化，支持同步和异步函数，自动管理缓存生命周期，是React性能优化的重要工具。',

  version: 'React 18.0+',
  tags: ["React", "Cache", "Performance", "Memoization"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default cacheData;