import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  introduction: `cache的诞生标志着React从"无状态计算"向"记忆化计算"的历史性转变。这不仅仅是一个API的添加，而是整个函数式编程和性能优化理念的基础性演进。通过深入挖掘其历史背景、技术演进和设计理念，我们可以更好地理解现代React性能优化的精髓和发展方向。`,

  background: `cache的出现源于现代Web应用对计算性能的日益增长需求和函数式编程范式的普及。在React早期，性能优化主要依赖组件级的优化技术。随着应用复杂度的增长和函数式编程的深入应用，函数级的缓存优化成为新的性能瓶颈解决方案。`,

  evolution: `cache的演进历程体现了React对性能优化的不断探索：从早期的手动缓存实现，到useMemo和useCallback的组件级优化，再到cache的函数级缓存，反映了React技术从局部到全局，从组件到函数的发展轨迹。`,

  timeline: [
    {
      year: '2013',
      event: 'React发布',
      description: 'Facebook发布React，引入虚拟DOM和组件化思想',
      significance: '为现代前端性能优化奠定了基础'
    },
    {
      year: '2018',
      event: 'React Hooks发布',
      description: 'React 16.8引入Hooks，包括useMemo和useCallback',
      significance: '为函数组件提供了记忆化优化能力'
    },
    {
      year: '2022',
      event: 'React 18发布',
      description: 'React 18引入并发特性和新的性能优化API',
      significance: '为现代React应用提供了更强大的性能优化工具'
    },
    {
      year: '2023',
      event: 'cache API引入',
      description: 'React引入cache函数，提供函数级缓存能力',
      significance: '标志着React性能优化从组件级扩展到函数级'
    }
  ],

  keyFigures: [
    {
      name: 'Dan Abramov',
      role: 'React核心团队成员',
      contribution: '推动了React Hooks的设计和实现，为cache等性能优化API奠定了理论基础',
      significance: '他的函数式编程理念深刻影响了React的性能优化方向'
    }
  ],

  concepts: [
    {
      term: 'Memoization（记忆化）',
      definition: '一种优化技术，通过缓存函数的计算结果来避免重复计算',
      evolution: '从手动实现发展为框架内置的标准化API',
      modernRelevance: '现代React性能优化的核心技术，cache是其在函数级的具体实现'
    }
  ],

  designPhilosophy: `cache的设计哲学体现了现代React对性能优化的深度思考：简单易用、自动管理、类型安全。它将复杂的缓存逻辑封装在简洁的API背后，让开发者能够专注于业务逻辑而不是缓存管理。`,

  impact: `cache对React生态系统产生了重要影响：推动了函数式编程的普及，提升了React应用的性能优化能力，改变了开发者的性能优化思路，为现代Web应用的性能优化提供了新的解决方案。`,

  modernRelevance: `在当今的Web开发环境中，cache的重要性日益凸显：应用复杂度不断增长，用户对性能的要求越来越高，函数式编程成为主流，性能优化从可选项变为必需品。cache作为React性能优化工具箱中的重要工具，为开发者提供了强大而易用的函数级缓存能力。`
};

export default knowledgeArchaeology;