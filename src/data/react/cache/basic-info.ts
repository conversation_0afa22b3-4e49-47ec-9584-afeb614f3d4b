import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: "cache是React中用于函数缓存的API，专门用于缓存函数的执行结果，避免重复计算，提升应用性能。它通过记忆化技术，将函数的输入参数和输出结果建立映射关系，实现智能的函数级缓存优化。",

  introduction: `cache是React 18.0+引入的函数缓存API，专门为现代React应用的性能优化设计的记忆化函数。

它遵循现代函数式编程的最佳实践，在函数执行和结果缓存之间做出了智能优化，允许开发者将昂贵的计算函数转换为缓存版本，避免重复执行相同的计算。

主要用于数据获取函数缓存、计算密集型函数优化和API调用去重。相比传统的手动缓存实现，它的优势在于自动管理缓存生命周期，提供更好的内存管理和缓存失效策略。

在React生态中，它是性能优化的重要工具，常见于需要优化重复计算或网络请求的场景，特别适合服务端渲染和数据获取优化。

核心优势包括自动缓存管理、避免重复计算、提升应用响应速度，同时提供了简洁的API设计，易于集成到现有代码中。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react/index.d.ts:2890
 * - 实现文件：packages/react/src/ReactCache.js:15
 * - 内部类型：packages/react/src/ReactCacheTypes.js:12
 */

// 基础语法
function cache<Args extends Array<any>, Return>(
  fn: (...args: Args) => Return
): (...args: Args) => Return;

// 使用示例
const cachedFunction = cache(expensiveFunction);

// 数据获取函数缓存
const fetchUser = cache(async (userId: string) => {
  const response = await fetch(\`/api/users/\${userId}\`);
  return response.json();
});

// 计算函数缓存
const fibonacci = cache((n: number): number => {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
});

/**
 * 参数约束：
 * - fn 必须是一个函数
 * - 缓存基于参数的引用相等性
 * - 支持异步函数缓存
 * - 自动管理缓存生命周期
 */`,

  quickExample: `function CacheExample() {
  // 缓存数据获取函数
  const fetchUserData = cache(async (userId: string) => {
    console.log('Fetching user data for:', userId);
    const response = await fetch(\`/api/users/\${userId}\`);
    return response.json();
  });

  // 缓存计算函数
  const calculateExpensiveValue = cache((input: number) => {
    console.log('Computing expensive value for:', input);
    // 模拟复杂计算
    let result = 0;
    for (let i = 0; i < input * 1000000; i++) {
      result += Math.sqrt(i);
    }
    return result;
  });

  const [userId, setUserId] = useState('user1');
  const [inputValue, setInputValue] = useState(100);

  const handleFetchUser = async () => {
    // 第一次调用会执行网络请求
    // 后续相同参数的调用会返回缓存结果
    const userData = await fetchUserData(userId);
    console.log('User data:', userData);
  };

  const handleCalculate = () => {
    // 第一次调用会执行计算
    // 后续相同参数的调用会返回缓存结果
    const result = calculateExpensiveValue(inputValue);
    console.log('Calculation result:', result);
  };

  return (
    <div>
      {/* 缓存的数据获取 */}
      <div>
        <input
          value={userId}
          onChange={(e) => setUserId(e.target.value)}
          placeholder="User ID"
        />
        <button onClick={handleFetchUser}>
          获取用户数据 (缓存优化)
        </button>
      </div>

      {/* 缓存的计算函数 */}
      <div>
        <input
          type="number"
          value={inputValue}
          onChange={(e) => setInputValue(Number(e.target.value))}
          placeholder="计算输入值"
        />
        <button onClick={handleCalculate}>
          执行复杂计算 (缓存优化)
        </button>
      </div>
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "cache在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用这个API来优化函数执行性能",
      diagram: `graph LR
      A[cache核心场景] --> B[数据获取优化]
      A --> C[计算密集型优化]
      A --> D[API调用去重]

      B --> B1["🌐 用户数据获取<br/>缓存用户信息查询"]
      B --> B2["📊 报表数据获取<br/>缓存复杂查询结果"]

      C --> C1["🧮 数学计算<br/>缓存斐波那契等递归计算"]
      C --> C2["🎨 图像处理<br/>缓存图像变换结果"]

      D --> D1["🔄 重复API调用<br/>避免相同请求重复执行"]
      D --> D2["📡 第三方服务<br/>缓存外部API响应"]

      style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
      style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
      style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "缓存机制流程",
      description: "cache的缓存机制流程，展示其如何通过记忆化技术避免重复计算",
      diagram: `graph TB
      A[函数调用] --> B[参数检查]
      B --> C{缓存中存在?}
      C -->|是| D[返回缓存结果]
      C -->|否| E[执行原函数]
      E --> F[存储结果到缓存]
      F --> G[返回计算结果]

      H[后续相同参数调用] --> I[参数检查]
      I --> J[缓存命中]
      J --> K[直接返回缓存]

      style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
      style C fill:#fff3e0,stroke:#f57c00,stroke-width:2px
      style D fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style E fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style F fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
      style G fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style K fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px`
    },
    {
      title: "React性能优化生态",
      description: "cache在React性能优化生态系统中的位置和与其他优化技术的协作关系",
      diagram: `graph TD
      A[React性能优化] --> B[组件层优化]
      A --> C[函数层优化]
      A --> D[数据层优化]

      B --> B1["React.memo<br/>组件记忆化"]
      B --> B2["useMemo<br/>值记忆化"]

      C --> C1["cache<br/>函数缓存"]
      C --> C2["useCallback<br/>回调记忆化"]

      D --> D1["Suspense<br/>数据获取优化"]
      D --> D2["startTransition<br/>优先级控制"]

      C1 -.-> B1
      C1 -.-> D1
      C2 -.-> B1
      B2 -.-> C1

      style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
      style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
      style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style C1 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
    }
  ],
  
  parameters: [
    {
      name: "fn",
      type: "(...args: Args) => Return",
      required: true,
      description: "要进行缓存的函数。可以是同步函数或异步函数，支持任意数量和类型的参数。",
      example: `// 同步函数缓存
const cachedCalculation = cache((x: number, y: number) => {
  return x * y + Math.sqrt(x);
});

// 异步函数缓存
const cachedFetch = cache(async (url: string) => {
  const response = await fetch(url);
  return response.json();
});`
    }
  ],

  returnValue: {
    type: "(...args: Args) => Return",
    description: "返回一个与原函数具有相同签名的缓存版本函数。当使用相同参数调用时，会返回缓存的结果而不是重新执行函数。",
    example: `const originalFunction = (x: number) => x * 2;
const cachedFunction = cache(originalFunction);

// 第一次调用：执行计算并缓存结果
const result1 = cachedFunction(5); // 计算并返回 10

// 第二次调用：直接返回缓存结果
const result2 = cachedFunction(5); // 直接返回 10，无需重新计算`
  },

  keyFeatures: [
    {
      title: "自动记忆化",
      description: "自动缓存函数的执行结果，基于参数的引用相等性进行缓存匹配",
      benefit: "避免重复计算，显著提升应用性能，特别是对于计算密集型函数"
    },
    {
      title: "异步函数支持",
      description: "完全支持异步函数的缓存，包括Promise和async/await模式",
      benefit: "优化数据获取和API调用，避免重复的网络请求"
    },
    {
      title: "类型安全",
      description: "提供完整的TypeScript类型支持，保持原函数的类型签名",
      benefit: "确保类型安全，提供良好的开发体验和IDE支持"
    },
    {
      title: "自动缓存管理",
      description: "React自动管理缓存的生命周期，无需手动清理缓存",
      benefit: "简化缓存管理，避免内存泄漏和缓存失效问题"
    },
    {
      title: "参数敏感缓存",
      description: "基于函数参数的引用相等性进行精确的缓存匹配",
      benefit: "确保缓存的准确性，避免错误的缓存命中"
    },
    {
      title: "零配置使用",
      description: "无需复杂的配置，简单包装函数即可获得缓存能力",
      benefit: "降低使用门槛，易于集成到现有代码中"
    }
  ],

  limitations: [
    "只能在React 18.0+版本中使用，在旧版本中不可用",
    "缓存基于参数的引用相等性，对象参数需要保持引用稳定性",
    "缓存的生命周期由React管理，无法手动控制缓存失效时机",
    "不适合有副作用的函数，只应该用于纯函数或幂等函数",
    "缓存存储在内存中，大量缓存可能影响内存使用"
  ],

  bestPractices: [
    "优先缓存计算密集型和数据获取函数，避免缓存简单的计算",
    "确保被缓存的函数是纯函数或幂等函数，避免副作用",
    "对于对象参数，使用稳定的引用或考虑参数序列化",
    "合理评估缓存的收益，避免过度缓存导致内存浪费",
    "结合React.memo和useMemo等其他优化技术使用",
    "在数据获取场景中，考虑与Suspense和错误边界结合使用",
    "定期监控缓存的命中率和内存使用情况",
    "在开发环境中测试缓存的正确性和性能提升效果"
  ],

  warnings: [
    "不要缓存有副作用的函数，这可能导致意外的行为",
    "注意对象参数的引用稳定性，频繁变化的引用会导致缓存失效",
    "避免缓存过于简单的函数，缓存开销可能超过计算开销",
    "在服务端渲染环境中使用时，注意缓存的一致性问题",
    "大量使用缓存时，监控内存使用情况，避免内存泄漏"
  ]
};

export default basicInfo;