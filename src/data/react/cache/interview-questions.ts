import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 1,
    question: '什么是React的cache函数？它与useMemo有什么区别？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'cache是React中用于函数缓存的API，它缓存整个函数的执行结果，而useMemo缓存计算值。cache适用于函数级缓存，useMemo适用于组件内的值缓存。',
      detailed: `cache和useMemo都是React中的记忆化技术，但用途和作用域不同：

**cache的特点：**
1. **函数级缓存**：缓存整个函数的执行结果
2. **全局作用域**：可以在组件外部使用
3. **参数敏感**：基于参数的引用相等性进行缓存
4. **异步支持**：完全支持异步函数缓存

**useMemo的特点：**
1. **值级缓存**：缓存计算结果的值
2. **组件作用域**：只能在组件内部使用
3. **依赖数组**：基于依赖数组进行缓存控制
4. **同步计算**：主要用于同步计算的优化

**使用场景对比：**
- cache：数据获取函数、复杂算法、API调用
- useMemo：组件内的计算值、派生状态、渲染优化

**生命周期管理：**
- cache：由React自动管理，跨组件共享
- useMemo：与组件生命周期绑定，组件卸载时清理`,
      code: `// cache 示例
const fetchUserData = cache(async (userId: string) => {
  const response = await fetch(\`/api/users/\${userId}\`);
  return response.json();
});

// 可以在任何地方使用
function UserProfile({ userId }) {
  const [userData, setUserData] = useState(null);

  useEffect(() => {
    fetchUserData(userId).then(setUserData);
  }, [userId]);

  return <div>{userData?.name}</div>;
}

// useMemo 示例
function ExpensiveComponent({ items, filter }) {
  // 只在组件内部使用，依赖数组控制缓存
  const filteredItems = useMemo(() => {
    return items.filter(item => item.category === filter);
  }, [items, filter]);

  const expensiveCalculation = useMemo(() => {
    return filteredItems.reduce((sum, item) => sum + item.value, 0);
  }, [filteredItems]);

  return <div>Total: {expensiveCalculation}</div>;
}

// 组合使用示例
const processData = cache((data: any[]) => {
  // 复杂的数据处理逻辑
  return data.map(item => ({
    ...item,
    processed: true,
    score: calculateComplexScore(item)
  }));
});

function DataVisualization({ rawData }) {
  // cache处理数据
  const processedData = processData(rawData);

  // useMemo计算图表配置
  const chartConfig = useMemo(() => {
    return {
      data: processedData,
      options: generateChartOptions(processedData)
    };
  }, [processedData]);

  return <Chart config={chartConfig} />;
}`
    },
    tags: ['基础概念', 'API对比']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 2,
    question: 'cache函数的缓存机制是如何工作的？如何确保缓存的正确性？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: 'cache基于参数的引用相等性进行缓存匹配，使用内部映射表存储结果。确保正确性需要使用纯函数、稳定的参数引用，并理解缓存的生命周期管理。',
      detailed: `cache的缓存机制基于记忆化技术和React的内部管理系统：

**缓存键生成机制：**
1. **参数序列化**：将函数参数转换为缓存键
2. **引用相等性**：使用Object.is进行参数比较
3. **参数顺序敏感**：参数顺序影响缓存匹配
4. **类型敏感**：不同类型的参数不会匹配

**缓存存储和查找：**
1. **内部映射表**：React维护函数到缓存的映射
2. **快速查找**：O(1)时间复杂度的缓存查找
3. **内存管理**：自动清理过期或不再使用的缓存
4. **并发安全**：支持并发访问和更新

**确保缓存正确性的策略：**
1. **使用纯函数**：确保相同输入产生相同输出
2. **稳定参数引用**：避免频繁创建新对象作为参数
3. **避免副作用**：不要在缓存函数中执行副作用
4. **合理的参数设计**：选择合适的参数粒度

**异步函数的特殊处理：**
- 缓存Promise对象而不是最终结果
- 确保并发调用共享同一个Promise
- 处理Promise的reject情况`,
      code: `// 缓存机制演示
class CacheDemo {
  constructor() {
    this.callCount = 0;
  }

  // 演示缓存键生成
  demonstrateCacheKeys() {
    const expensiveFunction = cache((a: number, b: string, c: object) => {
      this.callCount++;
      console.log(\`Function called \${this.callCount} times\`);
      return \`Result: \${a}-\${b}-\${JSON.stringify(c)}\`;
    });

    // 相同参数 - 缓存命中
    const obj1 = { id: 1 };
    console.log(expensiveFunction(1, 'test', obj1)); // 第1次调用
    console.log(expensiveFunction(1, 'test', obj1)); // 缓存命中

    // 不同参数 - 缓存未命中
    console.log(expensiveFunction(2, 'test', obj1)); // 第2次调用
    console.log(expensiveFunction(1, 'different', obj1)); // 第3次调用

    // 相同值但不同引用 - 缓存未命中
    const obj2 = { id: 1 }; // 不同的对象引用
    console.log(expensiveFunction(1, 'test', obj2)); // 第4次调用
  }

  // 演示异步函数缓存
  demonstrateAsyncCache() {
    const asyncFunction = cache(async (id: string) => {
      console.log(\`Fetching data for \${id}\`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { id, data: \`Data for \${id}\` };
    });

    // 并发调用会共享同一个Promise
    Promise.all([
      asyncFunction('user1'),
      asyncFunction('user1'), // 共享Promise
      asyncFunction('user1')  // 共享Promise
    ]).then(results => {
      console.log('All results:', results);
      // 只会看到一次"Fetching data for user1"
    });
  }

  // 演示参数稳定性的重要性
  demonstrateParameterStability() {
    const processData = cache((config: any) => {
      console.log('Processing with config:', config);
      return \`Processed with \${config.type}\`;
    });

    // 错误的使用方式 - 每次都创建新对象
    function badUsage() {
      return processData({ type: 'default', options: {} }); // 每次新对象
    }

    // 正确的使用方式 - 稳定的引用
    const stableConfig = { type: 'default', options: {} };
    function goodUsage() {
      return processData(stableConfig); // 稳定引用
    }

    // 测试效果
    console.log('Bad usage:');
    badUsage(); // 第1次调用
    badUsage(); // 第2次调用（应该缓存但没有）

    console.log('Good usage:');
    goodUsage(); // 第3次调用
    goodUsage(); // 缓存命中
  }
}

// 实际应用中的最佳实践
const DataProcessor = {
  // 使用稳定的配置对象
  defaultConfig: {
    algorithm: 'fast',
    precision: 0.01
  },

  // 缓存的数据处理函数
  processData: cache((data: any[], config = DataProcessor.defaultConfig) => {
    console.log('Processing data with config:', config);
    return data.map(item => ({
      ...item,
      processed: true,
      score: item.value * config.precision
    }));
  }),

  // 缓存的API调用
  fetchUserData: cache(async (userId: string, includeDetails = false) => {
    const url = \`/api/users/\${userId}\${includeDetails ? '?details=true' : ''}\`;
    const response = await fetch(url);
    return response.json();
  })
};

// 使用示例
function useDataProcessor(rawData: any[]) {
  // 使用稳定的配置确保缓存命中
  const processedData = DataProcessor.processData(rawData);

  return processedData;
}`
    },
    tags: ['实现原理', '缓存机制']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 3,
    question: '在大型应用中如何设计和管理cache的使用策略？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    answer: {
      brief: '大型应用的cache策略需要考虑缓存粒度、内存管理、性能监控、错误处理等多个维度，建立分层的缓存架构和完善的管理机制。',
      detailed: `大型应用中的cache使用策略是一个复杂的系统工程，需要综合考虑多个因素：

**缓存架构设计原则：**
1. **分层缓存策略**：按功能和重要性分层管理
2. **粒度控制**：选择合适的缓存粒度
3. **生命周期管理**：合理控制缓存的生存时间
4. **内存优化**：避免内存泄漏和过度使用

**缓存分类和管理：**
1. **数据获取层**：API调用、数据库查询缓存
2. **计算处理层**：算法计算、数据处理缓存
3. **业务逻辑层**：业务规则、验证逻辑缓存
4. **UI渲染层**：组件渲染、样式计算缓存

**性能监控和优化：**
1. **缓存命中率监控**：跟踪缓存效果
2. **内存使用监控**：防止内存泄漏
3. **性能基准测试**：量化缓存收益
4. **动态调优**：根据使用模式调整策略

**错误处理和容错：**
1. **缓存失效处理**：优雅降级机制
2. **异常恢复**：缓存错误时的恢复策略
3. **数据一致性**：确保缓存数据的准确性
4. **监控告警**：及时发现和处理问题`,
      code: `// 大型应用的cache管理架构
class EnterpriseCache {
  private static instance: EnterpriseCache;
  private cacheMetrics: Map<string, any> = new Map();
  private cacheConfig: any = {};

  static getInstance() {
    if (!EnterpriseCache.instance) {
      EnterpriseCache.instance = new EnterpriseCache();
    }
    return EnterpriseCache.instance;
  }

  // 分层缓存管理
  createLayeredCache() {
    return {
      // 数据获取层
      dataLayer: {
        fetchUser: cache(async (userId: string) => {
          this.recordCacheCall('dataLayer.fetchUser', userId);
          const response = await fetch(\`/api/users/\${userId}\`);
          return response.json();
        }),

        fetchUserPermissions: cache(async (userId: string) => {
          this.recordCacheCall('dataLayer.fetchUserPermissions', userId);
          const response = await fetch(\`/api/users/\${userId}/permissions\`);
          return response.json();
        })
      },

      // 计算处理层
      computationLayer: {
        calculateUserScore: cache((userData: any, metrics: any[]) => {
          this.recordCacheCall('computationLayer.calculateUserScore');
          return metrics.reduce((score, metric) => {
            return score + (userData[metric.field] || 0) * metric.weight;
          }, 0);
        }),

        processAnalytics: cache((events: any[], timeRange: any) => {
          this.recordCacheCall('computationLayer.processAnalytics');
          return events
            .filter(event => this.isInTimeRange(event.timestamp, timeRange))
            .reduce((analytics, event) => {
              analytics[event.type] = (analytics[event.type] || 0) + 1;
              return analytics;
            }, {});
        })
      },

      // 业务逻辑层
      businessLayer: {
        validateBusinessRules: cache((entity: any, rules: any[]) => {
          this.recordCacheCall('businessLayer.validateBusinessRules');
          return rules.every(rule => this.evaluateRule(entity, rule));
        }),

        calculatePricing: cache((product: any, user: any, promotions: any[]) => {
          this.recordCacheCall('businessLayer.calculatePricing');
          let price = product.basePrice;

          // 应用用户折扣
          if (user.membershipLevel === 'premium') {
            price *= 0.9;
          }

          // 应用促销
          promotions.forEach(promo => {
            if (this.isPromotionApplicable(product, user, promo)) {
              price *= (1 - promo.discount);
            }
          });

          return Math.round(price * 100) / 100;
        })
      }
    };
  }

  // 缓存性能监控
  recordCacheCall(functionName: string, ...args: any[]) {
    const key = \`\${functionName}(\${args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(', ')})\`;

    if (!this.cacheMetrics.has(key)) {
      this.cacheMetrics.set(key, {
        calls: 0,
        hits: 0,
        misses: 0,
        lastAccess: Date.now()
      });
    }

    const metrics = this.cacheMetrics.get(key);
    metrics.calls++;
    metrics.lastAccess = Date.now();
  }

  // 缓存健康检查
  performHealthCheck() {
    const report = {
      totalFunctions: this.cacheMetrics.size,
      totalCalls: 0,
      averageHitRate: 0,
      memoryUsage: this.estimateMemoryUsage(),
      recommendations: []
    };

    let totalHits = 0;
    let totalCalls = 0;

    this.cacheMetrics.forEach((metrics, key) => {
      totalCalls += metrics.calls;
      totalHits += metrics.hits;

      const hitRate = metrics.hits / metrics.calls;
      if (hitRate < 0.3) {
        report.recommendations.push(\`Low hit rate for \${key}: \${(hitRate * 100).toFixed(1)}%\`);
      }
    });

    report.totalCalls = totalCalls;
    report.averageHitRate = totalCalls > 0 ? totalHits / totalCalls : 0;

    return report;
  }

  // 内存使用估算
  estimateMemoryUsage() {
    // 简化的内存使用估算
    return {
      estimatedSize: this.cacheMetrics.size * 1024, // 假设每个缓存项1KB
      itemCount: this.cacheMetrics.size,
      recommendation: this.cacheMetrics.size > 1000 ? 'Consider cache cleanup' : 'Memory usage normal'
    };
  }

  // 缓存配置管理
  configureCachePolicy(config: any) {
    this.cacheConfig = {
      maxCacheSize: config.maxCacheSize || 10000,
      defaultTTL: config.defaultTTL || 300000, // 5分钟
      cleanupInterval: config.cleanupInterval || 60000, // 1分钟
      ...config
    };
  }

  // 辅助方法
  private isInTimeRange(timestamp: number, timeRange: any): boolean {
    return timestamp >= timeRange.start && timestamp <= timeRange.end;
  }

  private evaluateRule(entity: any, rule: any): boolean {
    // 简化的规则评估
    return entity[rule.field] === rule.expectedValue;
  }

  private isPromotionApplicable(product: any, user: any, promotion: any): boolean {
    // 简化的促销适用性检查
    return promotion.applicableProducts.includes(product.id) &&
           promotion.applicableUserTypes.includes(user.type);
  }
}

// 使用示例
function useEnterpriseCache() {
  const cacheManager = EnterpriseCache.getInstance();
  const cache = cacheManager.createLayeredCache();

  // 配置缓存策略
  cacheManager.configureCachePolicy({
    maxCacheSize: 5000,
    defaultTTL: 600000, // 10分钟
    cleanupInterval: 120000 // 2分钟
  });

  return {
    // 数据获取
    async getUserData(userId: string) {
      const [user, permissions] = await Promise.all([
        cache.dataLayer.fetchUser(userId),
        cache.dataLayer.fetchUserPermissions(userId)
      ]);
      return { user, permissions };
    },

    // 业务计算
    calculateUserMetrics(userData: any, events: any[]) {
      const score = cache.computationLayer.calculateUserScore(userData, [
        { field: 'loginCount', weight: 0.3 },
        { field: 'purchaseAmount', weight: 0.7 }
      ]);

      const analytics = cache.computationLayer.processAnalytics(events, {
        start: Date.now() - 86400000, // 24小时前
        end: Date.now()
      });

      return { score, analytics };
    },

    // 健康监控
    getCacheHealth() {
      return cacheManager.performHealthCheck();
    }
  };
}`
    },
    tags: ['架构设计', '企业应用']
  }
];

export default interviewQuestions;