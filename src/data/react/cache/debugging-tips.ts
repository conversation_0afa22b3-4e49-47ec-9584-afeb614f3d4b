import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'cache虽然API简单，但在实际使用中开发者经常遇到一些典型问题。本节提供完整的问题诊断和解决方案，帮助快速定位和修复cache相关的缓存问题。',
        sections: [
          {
            title: '缓存未命中问题',
            description: '最常见的问题是cache看起来没有任何效果，函数仍然重复执行。这通常涉及参数引用、函数纯度、调用时机等多个方面',
            items: [
              {
                title: '参数引用不稳定',
                description: '每次调用时传入新的对象引用，导致缓存无法命中',
                solution: '1. 使用稳定的对象引用；2. 考虑参数序列化；3. 重新设计参数结构；4. 使用useMemo稳定参数',
                prevention: '在设计缓存函数时，优先使用原始类型参数，避免频繁创建新对象',
                code: `// 问题：参数引用不稳定
const badUsage = () => {
  // 每次都创建新对象，缓存无法命中
  return cachedFunction({ type: 'user', options: {} });
};

// 解决方案：稳定的参数引用
const stableConfig = { type: 'user', options: {} };
const goodUsage = () => {
  return cachedFunction(stableConfig);
};

// 或者使用useMemo稳定参数
function Component({ userType }) {
  const config = useMemo(() => ({
    type: userType,
    options: {}
  }), [userType]);

  return cachedFunction(config);
}`
              },
              {
                title: '函数包含副作用',
                description: '缓存的函数包含副作用，导致缓存命中时副作用不执行',
                solution: '1. 将副作用从函数中分离；2. 使用纯函数进行缓存；3. 在缓存函数外部处理副作用；4. 重新设计函数结构',
                prevention: '只缓存纯函数，将所有副作用移到缓存函数外部处理',
                code: `// 问题：函数包含副作用
const badCachedFunction = cache((data) => {
  console.log('Processing data...'); // 副作用
  updateGlobalState(data); // 副作用
  return processData(data);
});

// 解决方案：分离副作用
const pureCachedFunction = cache((data) => {
  return processData(data); // 只有纯计算
});

function useData(data) {
  const result = pureCachedFunction(data);

  // 副作用在外部处理
  useEffect(() => {
    console.log('Processing data...');
    updateGlobalState(data);
  }, [data]);

  return result;
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '有效的调试工具可以帮助开发者监控cache的效果，诊断缓存问题，优化缓存策略。掌握这些工具是高效使用cache的关键。',
        sections: [
          {
            title: '缓存性能监控',
            description: '监控cache的命中率、执行时间和内存使用情况',
            items: [
              {
                title: '缓存命中率监控',
                description: '跟踪缓存函数的调用次数和命中次数，计算命中率',
                solution: '1. 包装cache函数添加监控；2. 记录调用和命中统计；3. 定期生成性能报告；4. 设置命中率告警',
                prevention: '定期监控缓存性能，及时发现和解决缓存效率问题',
                code: `// 缓存性能监控工具
class CacheMonitor {
  private stats = new Map();

  wrapCache<T extends (...args: any[]) => any>(name: string, fn: T): T {
    const cachedFn = cache(fn);

    return ((...args: any[]) => {
      const key = JSON.stringify(args);
      const startTime = performance.now();

      // 检查是否为缓存命中
      const isCacheHit = this.checkCacheHit(name, key);

      const result = cachedFn(...args);
      const endTime = performance.now();

      this.recordCall(name, key, endTime - startTime, isCacheHit);

      return result;
    }) as T;
  }

  private recordCall(name: string, key: string, duration: number, hit: boolean) {
    if (!this.stats.has(name)) {
      this.stats.set(name, { calls: 0, hits: 0, totalTime: 0 });
    }

    const stat = this.stats.get(name);
    stat.calls++;
    stat.totalTime += duration;
    if (hit) stat.hits++;
  }

  getReport() {
    const report = {};
    this.stats.forEach((stat, name) => {
      report[name] = {
        hitRate: (stat.hits / stat.calls * 100).toFixed(1) + '%',
        avgTime: (stat.totalTime / stat.calls).toFixed(2) + 'ms',
        totalCalls: stat.calls
      };
    });
    return report;
  }
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;