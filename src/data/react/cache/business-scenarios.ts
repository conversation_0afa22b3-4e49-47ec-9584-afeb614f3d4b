import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-1',
    title: '电商平台商品推荐算法缓存优化',
    description: '在电商平台中，商品推荐算法需要基于用户行为、商品属性、销售数据等进行复杂计算。使用cache可以缓存推荐算法的计算结果，避免重复计算，显著提升推荐系统的响应速度。',
    businessValue: '提升用户购物体验，减少推荐系统的计算延迟，增加用户停留时间和转化率，特别适合需要实时推荐的电商场景。',
    scenario: '用户浏览商品时，系统需要实时计算个性化推荐。传统方式每次都会重新计算，即使用户参数相同。这导致推荐延迟，影响用户体验和购买决策。',
    code: `function EcommerceRecommendationCache() {
  const [userId, setUserId] = useState('user123');
  const [category, setCategory] = useState('electronics');
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(false);

  // 缓存复杂的推荐算法
  const calculateRecommendations = cache(async (userId: string, category: string, userBehavior: any) => {
    console.log('Computing recommendations for:', { userId, category });

    // 模拟复杂的推荐算法计算
    const userProfile = await fetchUserProfile(userId);
    const categoryProducts = await fetchCategoryProducts(category);
    const behaviorData = await analyzeBehaviorData(userBehavior);

    // 复杂的推荐算法
    const recommendations = categoryProducts
      .map(product => ({
        ...product,
        score: calculateRecommendationScore(product, userProfile, behaviorData)
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 10);

    return recommendations;
  });

  // 缓存用户行为分析
  const analyzeBehaviorData = cache(async (userBehavior: any) => {
    console.log('Analyzing user behavior...');

    // 模拟行为分析计算
    const clickPatterns = analyzeClickPatterns(userBehavior.clicks);
    const purchaseHistory = analyzePurchaseHistory(userBehavior.purchases);
    const browsingPreferences = analyzeBrowsingPreferences(userBehavior.views);

    return {
      clickPatterns,
      purchaseHistory,
      browsingPreferences,
      computedAt: Date.now()
    };
  });

  // 缓存商品相似度计算
  const calculateProductSimilarity = cache((product1: any, product2: any) => {
    console.log('Calculating similarity between products...');

    // 复杂的相似度算法
    const attributeSimilarity = compareAttributes(product1.attributes, product2.attributes);
    const categorySimilarity = compareCategories(product1.category, product2.category);
    const priceSimilarity = comparePrices(product1.price, product2.price);

    return (attributeSimilarity * 0.5 + categorySimilarity * 0.3 + priceSimilarity * 0.2);
  });

  const handleGetRecommendations = async () => {
    setLoading(true);
    try {
      const userBehavior = {
        clicks: await fetchUserClicks(userId),
        purchases: await fetchUserPurchases(userId),
        views: await fetchUserViews(userId)
      };

      // 第一次调用会执行完整计算
      // 后续相同参数的调用会返回缓存结果
      const result = await calculateRecommendations(userId, category, userBehavior);
      setRecommendations(result);
    } catch (error) {
      console.error('Failed to get recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSimilarProducts = async (productId: string) => {
    const allProducts = await fetchCategoryProducts(category);
    const targetProduct = allProducts.find(p => p.id === productId);

    if (targetProduct) {
      // 使用缓存的相似度计算
      const similarProducts = allProducts
        .filter(p => p.id !== productId)
        .map(product => ({
          ...product,
          similarity: calculateProductSimilarity(targetProduct, product)
        }))
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, 5);

      console.log('Similar products:', similarProducts);
    }
  };

  return (
    <div className="recommendation-system">
      <div className="user-controls">
        <h3>推荐系统控制面板</h3>
        <div>
          <label>用户ID: </label>
          <input
            value={userId}
            onChange={(e) => setUserId(e.target.value)}
          />
        </div>
        <div>
          <label>商品类别: </label>
          <select
            value={category}
            onChange={(e) => setCategory(e.target.value)}
          >
            <option value="electronics">电子产品</option>
            <option value="clothing">服装</option>
            <option value="books">图书</option>
          </select>
        </div>
        <button
          onClick={handleGetRecommendations}
          disabled={loading}
        >
          {loading ? '计算中...' : '获取推荐 (缓存优化)'}
        </button>
      </div>

      <div className="recommendations">
        <h4>推荐结果 ({recommendations.length}个)</h4>
        {recommendations.map(item => (
          <div key={item.id} className="recommendation-item">
            <span>{item.name}</span>
            <span>评分: {item.score.toFixed(2)}</span>
            <button onClick={() => handleSimilarProducts(item.id)}>
              查找相似商品
            </button>
          </div>
        ))}
      </div>

      <div className="cache-info">
        <p>💡 缓存优化说明：</p>
        <ul>
          <li>推荐算法结果已缓存，相同参数的计算会立即返回</li>
          <li>用户行为分析结果已缓存，避免重复分析</li>
          <li>商品相似度计算已缓存，提升相似商品查找速度</li>
        </ul>
      </div>
    </div>
  );
}

// 辅助函数（模拟）
async function fetchUserProfile(userId: string) {
  return { id: userId, preferences: ['tech', 'books'], age: 25 };
}

async function fetchCategoryProducts(category: string) {
  return Array.from({ length: 50 }, (_, i) => ({
    id: \`\${category}-\${i}\`,
    name: \`Product \${i}\`,
    category,
    price: Math.random() * 1000,
    attributes: { rating: Math.random() * 5, reviews: Math.floor(Math.random() * 1000) }
  }));
}

function calculateRecommendationScore(product: any, userProfile: any, behaviorData: any) {
  // 模拟复杂的评分算法
  return Math.random() * 100;
}`,
    explanation: '这个场景展示了如何在电商推荐系统中使用cache优化复杂的算法计算。通过缓存推荐算法、用户行为分析和商品相似度计算的结果，避免重复的复杂计算，显著提升推荐系统的响应速度和用户体验。',
    benefits: [
      '显著减少推荐算法的计算时间，提升用户体验',
      '避免重复的用户行为分析，优化系统资源使用',
      '提升商品相似度计算的效率，支持实时推荐',
      '减少服务器计算负载，提升系统整体性能'
    ],
    metrics: {
      performance: '推荐算法响应时间从2.5s减少到200ms，性能提升92%',
      userExperience: '用户停留时间增加45%，推荐点击率提升38%',
      technicalMetrics: '服务器CPU使用率降低60%，缓存命中率达到85%'
    },
    difficulty: 'easy',
    tags: ['电商推荐', '算法缓存', '性能优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-2',
    title: '金融风控系统实时风险评估缓存',
    description: '在金融风控系统中，需要对每笔交易进行实时风险评估，涉及复杂的规则引擎、机器学习模型和历史数据分析。使用cache可以缓存风险评估的计算结果，避免重复计算，提升风控系统的响应速度和处理能力。',
    businessValue: '提升风控系统的实时性和准确性，减少交易延迟，增强用户体验，同时降低系统计算成本，特别适合高频交易和大规模金融业务场景。',
    scenario: '每笔交易都需要经过多层风险评估，包括用户信用评估、交易模式分析、反欺诈检测等。传统方式每次都重新计算，即使是相似的交易参数，导致风控延迟和系统负载过高。',
    code: `function FinancialRiskAssessmentCache() {
  const [transaction, setTransaction] = useState({
    userId: 'user123',
    amount: 1000,
    merchantId: 'merchant456',
    location: 'Beijing'
  });
  const [riskResult, setRiskResult] = useState(null);
  const [loading, setLoading] = useState(false);

  // 缓存用户信用评估
  const assessUserCredit = cache(async (userId: string, transactionHistory: any[]) => {
    console.log('Computing user credit score for:', userId);

    // 模拟复杂的信用评估算法
    const creditHistory = await fetchCreditHistory(userId);
    const paymentBehavior = analyzePaymentBehavior(transactionHistory);
    const defaultRisk = calculateDefaultRisk(creditHistory, paymentBehavior);

    const creditScore = {
      score: Math.max(0, Math.min(1000, 750 - defaultRisk * 100)),
      level: defaultRisk < 0.1 ? 'low' : defaultRisk < 0.3 ? 'medium' : 'high',
      factors: {
        paymentHistory: paymentBehavior.onTimeRate,
        creditUtilization: creditHistory.utilizationRate,
        accountAge: creditHistory.accountAgeMonths
      }
    };

    return creditScore;
  });

  // 缓存交易模式分析
  const analyzeTransactionPattern = cache(async (userId: string, amount: number, merchantId: string) => {
    console.log('Analyzing transaction pattern...');

    const userTransactions = await fetchUserTransactions(userId);
    const merchantTransactions = await fetchMerchantTransactions(merchantId);

    // 分析交易模式
    const amountPattern = analyzeAmountPattern(userTransactions, amount);
    const frequencyPattern = analyzeFrequencyPattern(userTransactions);
    const merchantPattern = analyzeMerchantPattern(userTransactions, merchantId);

    const riskScore = calculatePatternRisk(amountPattern, frequencyPattern, merchantPattern);

    return {
      amountRisk: amountPattern.riskLevel,
      frequencyRisk: frequencyPattern.riskLevel,
      merchantRisk: merchantPattern.riskLevel,
      overallRisk: riskScore,
      confidence: 0.85
    };
  });

  // 缓存反欺诈检测
  const detectFraud = cache(async (transaction: any, deviceInfo: any) => {
    console.log('Running fraud detection...');

    // 设备指纹分析
    const deviceRisk = await analyzeDeviceFingerprint(deviceInfo);

    // 地理位置风险
    const locationRisk = await analyzeLocationRisk(transaction.location, transaction.userId);

    // 行为模式分析
    const behaviorRisk = await analyzeBehaviorPattern(transaction);

    // 机器学习模型预测
    const mlPrediction = await runFraudMLModel({
      ...transaction,
      deviceRisk,
      locationRisk,
      behaviorRisk
    });

    return {
      fraudProbability: mlPrediction.probability,
      riskFactors: mlPrediction.factors,
      recommendation: mlPrediction.probability > 0.7 ? 'block' :
                     mlPrediction.probability > 0.3 ? 'review' : 'approve',
      confidence: mlPrediction.confidence
    };
  });

  // 缓存综合风险评估
  const comprehensiveRiskAssessment = cache(async (transaction: any) => {
    console.log('Performing comprehensive risk assessment...');

    const transactionHistory = await fetchUserTransactions(transaction.userId);
    const deviceInfo = await getDeviceInfo();

    // 并行执行各项风险评估（利用缓存优化）
    const [creditAssessment, patternAnalysis, fraudDetection] = await Promise.all([
      assessUserCredit(transaction.userId, transactionHistory),
      analyzeTransactionPattern(transaction.userId, transaction.amount, transaction.merchantId),
      detectFraud(transaction, deviceInfo)
    ]);

    // 综合风险评分
    const overallRisk = calculateOverallRisk(creditAssessment, patternAnalysis, fraudDetection);

    return {
      overallRiskScore: overallRisk.score,
      riskLevel: overallRisk.level,
      decision: overallRisk.decision,
      components: {
        credit: creditAssessment,
        pattern: patternAnalysis,
        fraud: fraudDetection
      },
      processingTime: Date.now(),
      cacheHits: {
        credit: 'cached',
        pattern: 'cached',
        fraud: 'cached'
      }
    };
  });

  const handleRiskAssessment = async () => {
    setLoading(true);
    try {
      // 第一次调用会执行完整计算
      // 后续相同参数的调用会返回缓存结果
      const result = await comprehensiveRiskAssessment(transaction);
      setRiskResult(result);
    } catch (error) {
      console.error('Risk assessment failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleQuickTest = async () => {
    // 测试缓存效果
    const testTransactions = [
      { ...transaction, amount: 1000 },
      { ...transaction, amount: 1000 }, // 相同参数，应该命中缓存
      { ...transaction, amount: 2000 }, // 不同参数，需要重新计算
    ];

    for (const testTx of testTransactions) {
      const start = performance.now();
      await comprehensiveRiskAssessment(testTx);
      const end = performance.now();
      console.log(\`Assessment for amount \${testTx.amount}: \${end - start}ms\`);
    }
  };

  return (
    <div className="risk-assessment-system">
      <div className="transaction-input">
        <h3>交易风险评估系统</h3>
        <div className="input-group">
          <label>用户ID:</label>
          <input
            value={transaction.userId}
            onChange={(e) => setTransaction({...transaction, userId: e.target.value})}
          />
        </div>
        <div className="input-group">
          <label>交易金额:</label>
          <input
            type="number"
            value={transaction.amount}
            onChange={(e) => setTransaction({...transaction, amount: Number(e.target.value)})}
          />
        </div>
        <div className="input-group">
          <label>商户ID:</label>
          <input
            value={transaction.merchantId}
            onChange={(e) => setTransaction({...transaction, merchantId: e.target.value})}
          />
        </div>
        <div className="input-group">
          <label>交易地点:</label>
          <input
            value={transaction.location}
            onChange={(e) => setTransaction({...transaction, location: e.target.value})}
          />
        </div>

        <div className="action-buttons">
          <button onClick={handleRiskAssessment} disabled={loading}>
            {loading ? '评估中...' : '执行风险评估 (缓存优化)'}
          </button>
          <button onClick={handleQuickTest}>
            测试缓存效果
          </button>
        </div>
      </div>

      {riskResult && (
        <div className="risk-result">
          <h4>风险评估结果</h4>
          <div className="risk-summary">
            <div className={\`risk-level \${riskResult.riskLevel}\`}>
              风险等级: {riskResult.riskLevel.toUpperCase()}
            </div>
            <div className="risk-score">
              风险评分: {riskResult.overallRiskScore}/100
            </div>
            <div className="decision">
              处理建议: {riskResult.decision}
            </div>
          </div>

          <div className="risk-components">
            <div className="component">
              <h5>信用评估</h5>
              <p>信用评分: {riskResult.components.credit.score}</p>
              <p>风险等级: {riskResult.components.credit.level}</p>
            </div>
            <div className="component">
              <h5>交易模式</h5>
              <p>整体风险: {riskResult.components.pattern.overallRisk}</p>
              <p>置信度: {riskResult.components.pattern.confidence}</p>
            </div>
            <div className="component">
              <h5>反欺诈检测</h5>
              <p>欺诈概率: {(riskResult.components.fraud.fraudProbability * 100).toFixed(1)}%</p>
              <p>建议: {riskResult.components.fraud.recommendation}</p>
            </div>
          </div>
        </div>
      )}

      <div className="cache-info">
        <p>💡 缓存优化说明：</p>
        <ul>
          <li>用户信用评估结果已缓存，相同用户的评估会立即返回</li>
          <li>交易模式分析已缓存，相同模式的分析会复用结果</li>
          <li>反欺诈检测已缓存，相同交易特征的检测会加速</li>
          <li>综合风险评估已缓存，整体评估性能显著提升</li>
        </ul>
      </div>
    </div>
  );
}

// 辅助函数（模拟）
async function fetchCreditHistory(userId: string) {
  return { utilizationRate: 0.3, accountAgeMonths: 24 };
}

async function fetchUserTransactions(userId: string) {
  return Array.from({ length: 100 }, (_, i) => ({
    id: i,
    amount: Math.random() * 5000,
    timestamp: Date.now() - i * ********
  }));
}

function calculateOverallRisk(credit: any, pattern: any, fraud: any) {
  const score = (credit.score / 10) + (pattern.overallRisk * 30) + (fraud.fraudProbability * 60);
  return {
    score: Math.round(score),
    level: score < 30 ? 'low' : score < 60 ? 'medium' : 'high',
    decision: score < 30 ? 'approve' : score < 60 ? 'review' : 'block'
  };
}`,
    explanation: '这个场景展示了如何在金融风控系统中使用cache优化复杂的风险评估计算。通过缓存用户信用评估、交易模式分析、反欺诈检测和综合风险评估的结果，避免重复的复杂计算，显著提升风控系统的响应速度和处理能力。',
    benefits: [
      '显著减少风险评估的计算时间，提升交易处理速度',
      '避免重复的机器学习模型推理，优化系统资源使用',
      '提升风控系统的实时性，增强用户交易体验',
      '降低系统计算成本，支持更大规模的交易处理'
    ],
    metrics: {
      performance: '风险评估响应时间从1.8s减少到150ms，性能提升92%',
      userExperience: '交易处理速度提升85%，用户满意度评分从7.8提升到9.2',
      technicalMetrics: '系统CPU使用率降低70%，缓存命中率达到88%'
    },
    difficulty: 'medium',
    tags: ['金融风控', '风险评估', '机器学习缓存']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-3',
    title: '科学计算平台复杂算法缓存优化',
    description: '在科学计算平台中，需要处理大量的数学建模、数据分析和仿真计算任务。这些计算往往非常耗时且资源密集。使用cache可以缓存复杂算法的计算结果，避免重复计算，显著提升科学计算平台的效率和用户体验。',
    businessValue: '提升科学研究效率，减少计算等待时间，降低计算资源成本，支持更多并发用户，特别适合需要大量重复计算的科研和工程场景。',
    scenario: '科研人员经常需要运行相同参数的复杂算法，如数值积分、矩阵运算、机器学习训练等。传统方式每次都重新计算，即使参数完全相同，导致大量计算资源浪费和用户等待时间过长。',
    code: `function ScientificComputingCache() {
  const [computationParams, setComputationParams] = useState({
    algorithm: 'monte-carlo',
    iterations: 1000000,
    precision: 0.001,
    dimensions: 3
  });
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [computationHistory, setComputationHistory] = useState([]);

  // 缓存蒙特卡洛积分计算
  const monteCarloIntegration = cache(async (func: string, bounds: any, iterations: number) => {
    console.log('Computing Monte Carlo integration...', { func, bounds, iterations });

    // 模拟复杂的蒙特卡洛积分计算
    let sum = 0;
    const volume = (bounds.xMax - bounds.xMin) * (bounds.yMax - bounds.yMin) * (bounds.zMax - bounds.zMin);

    for (let i = 0; i < iterations; i++) {
      const x = Math.random() * (bounds.xMax - bounds.xMin) + bounds.xMin;
      const y = Math.random() * (bounds.yMax - bounds.yMin) + bounds.yMin;
      const z = Math.random() * (bounds.zMax - bounds.zMin) + bounds.zMin;

      // 评估函数值（这里简化为示例函数）
      const value = evaluateFunction(func, x, y, z);
      sum += value;
    }

    const result = (sum / iterations) * volume;
    const error = Math.abs(result - getAnalyticalSolution(func)) / getAnalyticalSolution(func);

    return {
      result,
      error,
      iterations,
      convergence: error < 0.01,
      computationTime: Date.now()
    };
  });

  // 缓存矩阵运算
  const matrixOperations = cache(async (operation: string, matrixA: number[][], matrixB: number[][]) => {
    console.log('Computing matrix operation:', operation);

    const start = performance.now();
    let result: number[][];

    switch (operation) {
      case 'multiply':
        result = multiplyMatrices(matrixA, matrixB);
        break;
      case 'inverse':
        result = invertMatrix(matrixA);
        break;
      case 'eigenvalues':
        result = computeEigenvalues(matrixA);
        break;
      default:
        throw new Error('Unsupported operation');
    }

    const computationTime = performance.now() - start;

    return {
      result,
      operation,
      dimensions: [matrixA.length, matrixA[0]?.length],
      computationTime,
      flops: estimateFlops(operation, matrixA.length)
    };
  });

  // 缓存机器学习模型训练
  const trainMLModel = cache(async (algorithm: string, dataset: any[], hyperparams: any) => {
    console.log('Training ML model...', { algorithm, datasetSize: dataset.length, hyperparams });

    // 模拟机器学习模型训练过程
    const epochs = hyperparams.epochs || 100;
    const learningRate = hyperparams.learningRate || 0.01;

    let model = initializeModel(algorithm, hyperparams);
    const trainingHistory = [];

    for (let epoch = 0; epoch < epochs; epoch++) {
      // 模拟训练过程
      const loss = simulateTrainingEpoch(model, dataset, learningRate);
      const accuracy = simulateValidation(model, dataset);

      trainingHistory.push({ epoch, loss, accuracy });

      // 早停条件
      if (accuracy > 0.95) break;
    }

    return {
      model,
      trainingHistory,
      finalAccuracy: trainingHistory[trainingHistory.length - 1].accuracy,
      finalLoss: trainingHistory[trainingHistory.length - 1].loss,
      epochs: trainingHistory.length,
      hyperparams
    };
  });

  // 缓存数值优化算法
  const numericalOptimization = cache(async (objective: string, constraints: any[], method: string) => {
    console.log('Running numerical optimization...', { objective, method });

    // 模拟数值优化过程
    let currentSolution = generateInitialSolution(constraints);
    const optimizationHistory = [];
    const maxIterations = 1000;

    for (let iteration = 0; iteration < maxIterations; iteration++) {
      const objectiveValue = evaluateObjective(objective, currentSolution);
      const gradient = computeGradient(objective, currentSolution);

      optimizationHistory.push({
        iteration,
        solution: [...currentSolution],
        objectiveValue,
        gradientNorm: computeNorm(gradient)
      });

      // 更新解
      currentSolution = updateSolution(currentSolution, gradient, method);

      // 收敛检查
      if (computeNorm(gradient) < 1e-6) break;
    }

    return {
      optimalSolution: currentSolution,
      optimalValue: optimizationHistory[optimizationHistory.length - 1].objectiveValue,
      iterations: optimizationHistory.length,
      converged: optimizationHistory[optimizationHistory.length - 1].gradientNorm < 1e-6,
      history: optimizationHistory
    };
  });

  const handleComputation = async () => {
    setLoading(true);
    const startTime = performance.now();

    try {
      let result;

      switch (computationParams.algorithm) {
        case 'monte-carlo':
          result = await monteCarloIntegration(
            'x^2 + y^2 + z^2',
            { xMin: -1, xMax: 1, yMin: -1, yMax: 1, zMin: -1, zMax: 1 },
            computationParams.iterations
          );
          break;

        case 'matrix-ops':
          const matrixA = generateRandomMatrix(50, 50);
          const matrixB = generateRandomMatrix(50, 50);
          result = await matrixOperations('multiply', matrixA, matrixB);
          break;

        case 'ml-training':
          const dataset = generateSyntheticDataset(1000);
          result = await trainMLModel('neural-network', dataset, {
            epochs: 50,
            learningRate: 0.01,
            hiddenLayers: [64, 32]
          });
          break;

        case 'optimization':
          result = await numericalOptimization(
            'x^2 + y^2 - 2*x*y',
            [{ type: 'bounds', xMin: -10, xMax: 10, yMin: -10, yMax: 10 }],
            'gradient-descent'
          );
          break;

        default:
          throw new Error('Unknown algorithm');
      }

      const totalTime = performance.now() - startTime;

      setResults({
        ...result,
        totalComputationTime: totalTime,
        algorithm: computationParams.algorithm,
        cached: totalTime < 100 // 如果计算时间很短，可能是缓存命中
      });

      // 添加到计算历史
      setComputationHistory(prev => [...prev, {
        timestamp: Date.now(),
        algorithm: computationParams.algorithm,
        params: { ...computationParams },
        computationTime: totalTime,
        cached: totalTime < 100
      }]);

    } catch (error) {
      console.error('Computation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBenchmark = async () => {
    // 测试缓存效果
    console.log('Running cache benchmark...');

    const testParams = [
      { algorithm: 'monte-carlo', iterations: 100000 },
      { algorithm: 'monte-carlo', iterations: 100000 }, // 相同参数，应该命中缓存
      { algorithm: 'monte-carlo', iterations: 200000 }, // 不同参数，需要重新计算
    ];

    for (const params of testParams) {
      const start = performance.now();
      await monteCarloIntegration(
        'x^2 + y^2 + z^2',
        { xMin: -1, xMax: 1, yMin: -1, yMax: 1, zMin: -1, zMax: 1 },
        params.iterations
      );
      const end = performance.now();
      console.log(\`Monte Carlo (\${params.iterations} iterations): \${end - start}ms\`);
    }
  };

  return (
    <div className="scientific-computing-platform">
      <div className="computation-controls">
        <h3>科学计算平台</h3>

        <div className="algorithm-selector">
          <label>算法类型:</label>
          <select
            value={computationParams.algorithm}
            onChange={(e) => setComputationParams({...computationParams, algorithm: e.target.value})}
          >
            <option value="monte-carlo">蒙特卡洛积分</option>
            <option value="matrix-ops">矩阵运算</option>
            <option value="ml-training">机器学习训练</option>
            <option value="optimization">数值优化</option>
          </select>
        </div>

        <div className="parameter-inputs">
          <div>
            <label>迭代次数:</label>
            <input
              type="number"
              value={computationParams.iterations}
              onChange={(e) => setComputationParams({...computationParams, iterations: Number(e.target.value)})}
            />
          </div>
          <div>
            <label>精度要求:</label>
            <input
              type="number"
              step="0.001"
              value={computationParams.precision}
              onChange={(e) => setComputationParams({...computationParams, precision: Number(e.target.value)})}
            />
          </div>
        </div>

        <div className="action-buttons">
          <button onClick={handleComputation} disabled={loading}>
            {loading ? '计算中...' : '开始计算 (缓存优化)'}
          </button>
          <button onClick={handleBenchmark}>
            缓存性能测试
          </button>
        </div>
      </div>

      {results && (
        <div className="computation-results">
          <h4>计算结果</h4>
          <div className="result-summary">
            <div className="result-item">
              <span>算法: {results.algorithm}</span>
            </div>
            <div className="result-item">
              <span>计算时间: {results.totalComputationTime.toFixed(2)}ms</span>
              {results.cached && <span className="cache-indicator">⚡ 缓存命中</span>}
            </div>
            {results.result !== undefined && (
              <div className="result-item">
                <span>结果: {typeof results.result === 'number' ? results.result.toFixed(6) : '复杂结果'}</span>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="computation-history">
        <h4>计算历史 ({computationHistory.length}次)</h4>
        <div className="history-list">
          {computationHistory.slice(-5).map((entry, index) => (
            <div key={index} className="history-item">
              <span>{entry.algorithm}</span>
              <span>{entry.computationTime.toFixed(2)}ms</span>
              {entry.cached && <span className="cache-badge">缓存</span>}
            </div>
          ))}
        </div>
      </div>

      <div className="cache-info">
        <p>💡 缓存优化说明：</p>
        <ul>
          <li>蒙特卡洛积分结果已缓存，相同参数的计算会立即返回</li>
          <li>矩阵运算结果已缓存，相同矩阵的运算会复用结果</li>
          <li>机器学习模型训练已缓存，相同配置的训练会跳过</li>
          <li>数值优化结果已缓存，相同问题的优化会直接返回</li>
        </ul>
      </div>
    </div>
  );
}

// 辅助函数（模拟）
function evaluateFunction(func: string, x: number, y: number, z: number): number {
  // 简化的函数评估
  return x * x + y * y + z * z;
}

function getAnalyticalSolution(func: string): number {
  // 返回解析解（简化）
  return 8; // 对于 x^2 + y^2 + z^2 在 [-1,1]^3 上的积分
}

function generateRandomMatrix(rows: number, cols: number): number[][] {
  return Array.from({ length: rows }, () =>
    Array.from({ length: cols }, () => Math.random())
  );
}

function multiplyMatrices(a: number[][], b: number[][]): number[][] {
  // 简化的矩阵乘法
  return a.map(row => b[0].map((_, i) => row.reduce((sum, cell, j) => sum + cell * b[j][i], 0)));
}`,
    explanation: '这个场景展示了如何在科学计算平台中使用cache优化复杂的算法计算。通过缓存蒙特卡洛积分、矩阵运算、机器学习训练和数值优化的结果，避免重复的耗时计算，显著提升科学计算平台的效率和用户体验。',
    benefits: [
      '显著减少复杂算法的计算时间，提升科研效率',
      '避免重复的机器学习训练，节省计算资源',
      '提升数值计算的响应速度，改善用户体验',
      '降低计算平台的资源成本，支持更多并发用户',
      '为科研人员提供更流畅的计算体验，促进创新'
    ],
    metrics: {
      performance: '复杂算法计算时间从45s减少到200ms，性能提升99.6%',
      userExperience: '用户等待时间减少95%，计算任务完成率提升78%',
      technicalMetrics: '计算资源利用率提升85%，缓存命中率达到92%'
    },
    difficulty: 'hard',
    tags: ['科学计算', '算法缓存', '机器学习', '数值计算']
  }
];

export default businessScenarios;