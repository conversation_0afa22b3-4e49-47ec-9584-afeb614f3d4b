import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-1',
    question: 'cache缓存的函数结果会保存多长时间？如何控制缓存的生命周期？',
    answer: `cache的缓存生命周期由React自动管理，无法手动控制具体的过期时间。

**缓存生命周期机制：**
1. **自动管理**：React根据内存使用情况和访问模式自动清理缓存
2. **组件无关**：缓存不与特定组件绑定，可以跨组件共享
3. **内存优化**：当内存压力增大时，React会清理较少使用的缓存
4. **应用重启**：应用重启时所有缓存都会清空

**影响缓存生命周期的因素：**
- 内存使用情况
- 缓存访问频率
- 系统内存压力
- React的内部清理策略

**最佳实践：**
- 不要依赖缓存的持久性
- 确保缓存的函数是幂等的
- 合理设计缓存的粒度
- 监控缓存的内存使用情况`,
    code: `// 缓存生命周期演示
function CacheLifecycleDemo() {
  const [cacheStats, setCacheStats] = useState({});

  // 创建一个带有时间戳的缓存函数
  const timestampedFunction = cache((input: string) => {
    const timestamp = Date.now();
    console.log(\`Function executed at: \${new Date(timestamp).toISOString()}\`);
    return {
      input,
      timestamp,
      result: \`Processed: \${input}\`
    };
  });

  // 测试缓存持久性
  const testCachePersistence = async () => {
    console.log('Testing cache persistence...');

    // 第一次调用
    const result1 = timestampedFunction('test-data');
    console.log('First call:', result1);

    // 立即第二次调用（应该命中缓存）
    const result2 = timestampedFunction('test-data');
    console.log('Second call:', result2);
    console.log('Cache hit:', result1.timestamp === result2.timestamp);

    // 等待一段时间后再次调用
    setTimeout(() => {
      const result3 = timestampedFunction('test-data');
      console.log('After delay:', result3);
      console.log('Still cached:', result1.timestamp === result3.timestamp);
    }, 5000);
  };

  // 监控缓存状态
  const monitorCacheUsage = () => {
    // 模拟缓存使用监控
    const stats = {
      memoryUsage: performance.memory?.usedJSHeapSize || 'N/A',
      cacheHits: 0,
      cacheMisses: 0,
      lastAccess: Date.now()
    };

    setCacheStats(stats);
  };

  return (
    <div>
      <h3>Cache生命周期测试</h3>
      <button onClick={testCachePersistence}>
        测试缓存持久性
      </button>
      <button onClick={monitorCacheUsage}>
        监控缓存状态
      </button>

      <div>
        <h4>缓存统计</h4>
        <pre>{JSON.stringify(cacheStats, null, 2)}</pre>
      </div>
    </div>
  );
}

// 缓存管理最佳实践
class CacheManager {
  private cacheRegistry = new Map();

  // 注册缓存函数以便监控
  registerCache(name: string, cachedFunction: any) {
    this.cacheRegistry.set(name, {
      function: cachedFunction,
      createdAt: Date.now(),
      lastAccessed: Date.now(),
      callCount: 0
    });
  }

  // 获取缓存统计
  getCacheStats() {
    const stats = {};
    this.cacheRegistry.forEach((info, name) => {
      stats[name] = {
        age: Date.now() - info.createdAt,
        lastAccessed: Date.now() - info.lastAccessed,
        callCount: info.callCount
      };
    });
    return stats;
  }

  // 清理建议
  getCleanupRecommendations() {
    const recommendations = [];
    this.cacheRegistry.forEach((info, name) => {
      const age = Date.now() - info.lastAccessed;
      if (age > 300000) { // 5分钟未访问
        recommendations.push(\`Consider if \${name} cache is still needed\`);
      }
    });
    return recommendations;
  }
}`,
    tags: ['缓存生命周期', '内存管理'],
    relatedQuestions: ['如何监控缓存的内存使用？', '如何优化缓存的性能？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-2',
    question: 'cache可以缓存有副作用的函数吗？应该如何处理副作用？',
    answer: `cache不应该用于有副作用的函数，这可能导致意外的行为和难以调试的问题。

**为什么不能缓存有副作用的函数：**
1. **副作用丢失**：缓存命中时副作用不会执行
2. **行为不一致**：相同参数可能产生不同的副作用
3. **调试困难**：副作用的执行变得不可预测
4. **状态污染**：可能导致应用状态不一致

**副作用的类型：**
- DOM操作
- 网络请求的副作用（如日志记录）
- 全局状态修改
- 文件系统操作
- 数据库写入

**正确的处理方式：**
- 将副作用从纯计算中分离
- 使用纯函数进行缓存
- 在缓存函数外部处理副作用
- 考虑使用useEffect处理副作用`,
    code: `// 错误的使用方式 - 不要这样做
const badCachedFunction = cache((userId: string) => {
  // ❌ 副作用：DOM操作
  document.title = \`User: \${userId}\`;

  // ❌ 副作用：全局状态修改
  window.currentUser = userId;

  // ❌ 副作用：日志记录
  console.log(\`Fetching user: \${userId}\`);

  // 这个函数有副作用，不应该被缓存
  return \`User data for \${userId}\`;
});

// 正确的使用方式 - 分离副作用
const pureFetchUser = cache(async (userId: string) => {
  // ✅ 纯函数：只进行数据获取，无副作用
  const response = await fetch(\`/api/users/\${userId}\`);
  return response.json();
});

function UserComponent({ userId }: { userId: string }) {
  const [userData, setUserData] = useState(null);

  useEffect(() => {
    // 使用缓存的纯函数获取数据
    pureFetchUser(userId).then(data => {
      setUserData(data);

      // ✅ 副作用在缓存函数外部处理
      document.title = \`User: \${data.name}\`;
      console.log(\`Loaded user: \${data.name}\`);

      // ✅ 状态更新也在外部处理
      updateGlobalUserState(data);
    });
  }, [userId]);

  return <div>{userData?.name}</div>;
}

// 更好的模式：分离数据获取和副作用
class UserService {
  // 纯函数：可以安全缓存
  static fetchUserData = cache(async (userId: string) => {
    const response = await fetch(\`/api/users/\${userId}\`);
    return response.json();
  });

  // 带副作用的函数：不缓存
  static async loadUser(userId: string) {
    // 使用缓存的纯函数获取数据
    const userData = await this.fetchUserData(userId);

    // 处理副作用
    this.logUserAccess(userId);
    this.updateUserSession(userData);
    this.trackAnalytics('user_loaded', { userId });

    return userData;
  }

  private static logUserAccess(userId: string) {
    console.log(\`User \${userId} accessed at \${new Date().toISOString()}\`);
  }

  private static updateUserSession(userData: any) {
    sessionStorage.setItem('currentUser', JSON.stringify(userData));
  }

  private static trackAnalytics(event: string, data: any) {
    // 发送分析数据
    fetch('/api/analytics', {
      method: 'POST',
      body: JSON.stringify({ event, data, timestamp: Date.now() })
    });
  }
}

// 处理副作用的Hook模式
function useUserWithSideEffects(userId: string) {
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!userId) return;

    setLoading(true);

    // 使用缓存的纯函数
    UserService.fetchUserData(userId)
      .then(data => {
        setUserData(data);

        // 处理副作用
        document.title = \`User: \${data.name}\`;

        // 记录访问日志
        fetch('/api/logs', {
          method: 'POST',
          body: JSON.stringify({
            action: 'user_viewed',
            userId,
            timestamp: Date.now()
          })
        });
      })
      .finally(() => setLoading(false));
  }, [userId]);

  return { userData, loading };
}

// 副作用检测工具
function detectSideEffects(fn: Function): string[] {
  const sideEffects = [];
  const originalConsole = console.log;
  const originalFetch = window.fetch;

  // 检测console调用
  console.log = (...args) => {
    sideEffects.push('Console output detected');
    originalConsole(...args);
  };

  // 检测网络请求
  window.fetch = (...args) => {
    sideEffects.push('Network request detected');
    return originalFetch(...args);
  };

  try {
    fn();
  } finally {
    // 恢复原始函数
    console.log = originalConsole;
    window.fetch = originalFetch;
  }

  return sideEffects;
}`,
    tags: ['副作用处理', '纯函数'],
    relatedQuestions: ['如何设计纯函数？', '如何分离副作用和计算逻辑？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-3',
    question: 'cache在服务端渲染(SSR)环境中如何工作？有什么注意事项？',
    answer: `cache在SSR环境中需要特别注意缓存的一致性和服务端客户端的同步问题。

**SSR环境中的挑战：**
1. **服务端客户端不一致**：服务端和客户端可能有不同的缓存状态
2. **缓存污染**：不同请求之间可能共享缓存
3. **内存泄漏**：服务端缓存可能无限增长
4. **数据新鲜度**：服务端缓存可能包含过期数据

**最佳实践：**
- 在SSR中谨慎使用cache
- 考虑请求级别的缓存隔离
- 定期清理服务端缓存
- 确保缓存数据的一致性
- 使用适当的缓存策略

**解决方案：**
- 使用请求上下文隔离缓存
- 实现缓存的TTL机制
- 在适当时机清理缓存
- 考虑使用专门的SSR缓存方案`,
    code: `// SSR环境中的cache使用注意事项

// ❌ 问题：全局缓存可能导致数据泄漏
const globalCachedFunction = cache(async (userId: string) => {
  // 在SSR中，这个缓存可能在不同请求间共享
  const response = await fetch(\`/api/users/\${userId}\`);
  return response.json();
});

// ✅ 解决方案：请求级别的缓存隔离
class SSRCacheManager {
  private requestCaches = new Map();

  // 为每个请求创建独立的缓存空间
  createRequestCache(requestId: string) {
    const requestCache = new Map();
    this.requestCaches.set(requestId, requestCache);

    return {
      cache: <T extends (...args: any[]) => any>(fn: T): T => {
        return ((...args: any[]) => {
          const key = JSON.stringify(args);

          if (requestCache.has(key)) {
            return requestCache.get(key);
          }

          const result = fn(...args);
          requestCache.set(key, result);
          return result;
        }) as T;
      },

      cleanup: () => {
        this.requestCaches.delete(requestId);
      }
    };
  }

  // 清理过期的请求缓存
  cleanupExpiredCaches(maxAge: number = 300000) { // 5分钟
    const now = Date.now();
    this.requestCaches.forEach((cache, requestId) => {
      if (now - cache.createdAt > maxAge) {
        this.requestCaches.delete(requestId);
      }
    });
  }
}

// SSR安全的缓存Hook
function useSSRSafeCache() {
  const [cacheManager] = useState(() => new SSRCacheManager());

  // 在服务端为每个请求创建独立缓存
  const createSafeCache = useCallback((requestId?: string) => {
    if (typeof window === 'undefined') {
      // 服务端：使用请求级缓存
      const id = requestId || \`req-\${Date.now()}-\${Math.random()}\`;
      return cacheManager.createRequestCache(id);
    } else {
      // 客户端：可以使用全局cache
      return {
        cache: cache,
        cleanup: () => {} // 客户端不需要手动清理
      };
    }
  }, [cacheManager]);

  return createSafeCache;
}

// SSR组件示例
function SSRUserProfile({ userId, requestId }: { userId: string, requestId?: string }) {
  const createSafeCache = useSSRSafeCache();
  const [userData, setUserData] = useState(null);

  useEffect(() => {
    const { cache: safeCache, cleanup } = createSafeCache(requestId);

    // 创建请求级别的缓存函数
    const fetchUser = safeCache(async (id: string) => {
      console.log(\`Fetching user \${id} for request \${requestId}\`);
      const response = await fetch(\`/api/users/\${id}\`);
      return response.json();
    });

    fetchUser(userId).then(setUserData);

    // 清理函数
    return cleanup;
  }, [userId, requestId, createSafeCache]);

  return <div>{userData?.name || 'Loading...'}</div>;
}

// Next.js SSR示例
export async function getServerSideProps(context: any) {
  const requestId = \`req-\${Date.now()}-\${Math.random()}\`;
  const cacheManager = new SSRCacheManager();
  const { cache: requestCache, cleanup } = cacheManager.createRequestCache(requestId);

  // 在服务端使用请求级缓存
  const fetchUserData = requestCache(async (userId: string) => {
    const response = await fetch(\`\${process.env.API_BASE_URL}/users/\${userId}\`);
    return response.json();
  });

  try {
    const userData = await fetchUserData(context.params.userId);

    return {
      props: {
        userData,
        requestId
      }
    };
  } finally {
    // 清理请求缓存
    cleanup();
  }
}

// 服务端缓存清理策略
class ServerCacheCleanup {
  private cleanupInterval: NodeJS.Timeout | null = null;

  startCleanup(cacheManager: SSRCacheManager, interval: number = 60000) {
    this.cleanupInterval = setInterval(() => {
      cacheManager.cleanupExpiredCaches();

      // 监控内存使用
      if (process.memoryUsage().heapUsed > 500 * 1024 * 1024) { // 500MB
        console.warn('High memory usage detected, forcing cache cleanup');
        cacheManager.cleanupExpiredCaches(0); // 清理所有缓存
      }
    }, interval);
  }

  stopCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
}

// 环境检测和适配
function createEnvironmentAwareCache() {
  if (typeof window === 'undefined') {
    // 服务端环境
    console.log('Using SSR-safe cache implementation');
    return new SSRCacheManager();
  } else {
    // 客户端环境
    console.log('Using standard cache implementation');
    return {
      cache: cache,
      createRequestCache: () => ({ cache: cache, cleanup: () => {} })
    };
  }
}`,
    tags: ['SSR', '服务端渲染'],
    relatedQuestions: ['如何在Next.js中使用cache？', '如何处理SSR的缓存一致性？']
  }
];

export default commonQuestions;