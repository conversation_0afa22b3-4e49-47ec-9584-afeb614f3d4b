import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const FragmentData: ApiItem = {
  id: 'Fragment',
  title: 'React.Fragment',
  description: 'React.Fragment是React中用于返回多个子元素而不添加额外DOM节点的组件，解决JSX必须有单一根元素的限制',
  category: 'React Components',
  difficulty: 'easy',
  
  syntax: `// 完整语法
<React.Fragment>
  {children}
</React.Fragment>

// 简写语法
<>
  {children}
</>

// 带key属性（只能使用完整语法）
<React.Fragment key={item.id}>
  <dt>{item.term}</dt>
  <dd>{item.description}</dd>
</React.Fragment>

// TypeScript接口
interface FragmentProps {
  children?: ReactNode;
  key?: React.Key;
}`,
  example: `function UserCard({ user }) {
  return (
    <>
      {/* 多个元素无需额外包装 */}
      <h2>{user.name}</h2>
      <p>{user.email}</p>
      <p>{user.role}</p>
    </>
  );
}

// 在列表中使用key
function DescriptionList({ items }) {
  return (
    <dl>
      {items.map(item => (
        <React.Fragment key={item.id}>
          <dt>{item.term}</dt>
          <dd>{item.description}</dd>
        </React.Fragment>
      ))}
    </dl>
  );
}

// 条件渲染多个元素
function ConditionalContent({ showDetails, user }) {
  return (
    <div>
      <h1>{user.name}</h1>
      {showDetails && (
        <>
          <p>Email: {user.email}</p>
          <p>Phone: {user.phone}</p>
          <p>Address: {user.address}</p>
        </>
      )}
    </div>
  );
}`,
  notes: '简写语法<></>不支持key和其他attributes，需要使用完整语法',
  
  version: 'React 16.0.0+',
  tags: ["React", "Component", "Fragment", "JSX", "DOM优化"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default FragmentData;