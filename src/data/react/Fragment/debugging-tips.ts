import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'Fragment虽然简单，但在实际使用中仍可能遇到一些问题。以下是最常见的问题和解决方案。',
        sections: [
          {
            title: 'Fragment语法错误',
            description: 'Fragment的不同语法形式容易混淆，导致编译错误或运行时问题',
            items: [
              {
                title: '短语法不支持key属性',
                description: '在列表渲染中使用短语法<>时无法添加key属性，导致React警告',
                solution: '在需要key的场景使用完整的<Fragment>语法',
                prevention: '了解不同语法的限制，根据需求选择合适的形式',
                code: `// ❌ 错误：短语法无法使用key
function ItemList({ items }) {
  return (
    <div>
      {items.map(item => (
        <> {/* 无法添加key属性 */}
          <h3>{item.title}</h3>
          <p>{item.description}</p>
        </>
      ))}
    </div>
  );
}

// ✅ 正确：使用完整语法添加key
function ItemList({ items }) {
  return (
    <div>
      {items.map(item => (
        <Fragment key={item.id}>
          <h3>{item.title}</h3>
          <p>{item.description}</p>
        </Fragment>
      ))}
    </div>
  );
}`
              },
              {
                title: 'Fragment嵌套过深',
                description: '不必要的Fragment嵌套会影响代码可读性',
                solution: '避免不必要的Fragment嵌套，保持代码结构清晰',
                prevention: '只在确实需要避免DOM包装时使用Fragment',
                code: `// ❌ 错误：不必要的Fragment嵌套
function OverNestedComponent() {
  return (
    <>
      <div>
        <>
          <h1>标题</h1>
          <>
            <p>内容</p>
          </>
        </>
      </div>
    </>
  );
}

// ✅ 正确：简化结构
function CleanComponent() {
  return (
    <div>
      <h1>标题</h1>
      <p>内容</p>
    </div>
  );
}`
              }
            ]
          },
          {
            title: 'Fragment使用场景错误',
            description: '在不合适的场景使用Fragment，或者应该使用Fragment时使用了div',
            items: [
              {
                title: '破坏CSS布局',
                description: '在CSS Grid或Flexbox中使用额外的div包装破坏了布局',
                solution: '在布局敏感的场景使用Fragment保持结构完整性',
                prevention: '了解CSS布局模型，在设计组件时考虑DOM结构影响',
                code: `/* CSS */
.flex-container {
  display: flex;
  justify-content: space-between;
}

// ❌ 错误：额外的div破坏flex布局
function FlexItems() {
  return (
    <div className="flex-container">
      <div> {/* 这个div会成为flex item */}
        <span>项目1</span>
        <span>项目2</span>
      </div>
    </div>
  );
}

// ✅ 正确：使用Fragment保持布局
function FlexItems() {
  return (
    <div className="flex-container">
      <>
        <span>项目1</span> {/* 这些span是直接的flex items */}
        <span>项目2</span>
      </>
    </div>
  );
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '使用正确的开发工具可以帮助你更好地理解和调试Fragment相关问题。',
        sections: [
          {
            title: 'React DevTools',
            description: '使用React DevTools检查Fragment在组件树中的表现',
            items: [
              {
                title: 'Fragment在组件树中的显示',
                description: 'Fragment在React DevTools中不会显示为节点，只显示其子元素',
                solution: '通过观察组件树结构验证Fragment是否正确工作',
                prevention: '定期使用DevTools检查组件结构是否符合预期',
                code: `// Fragment在DevTools中的表现
function FragmentExample() {
  return (
    <>
      <h1>标题</h1>
      <p>内容</p>
    </>
  );
}

// DevTools中显示为：
// FragmentExample
//   ├── h1 "标题"
//   └── p "内容"
// 
// 而不是：
// FragmentExample
//   └── Fragment
//       ├── h1 "标题"
//       └── p "内容"`
              },
              {
                title: '性能分析Fragment影响',
                description: '使用Profiler分析Fragment对渲染性能的影响',
                solution: '对比Fragment和div包装的性能数据',
                prevention: '在性能敏感的应用中定期进行性能分析',
                code: `import { Profiler } from 'react';

function ProfiledFragmentComponent() {
  function onRenderCallback(id, phase, actualDuration, baseDuration, startTime, commitTime) {
    console.log('Fragment组件渲染信息:', {
      id,
      phase,
      actualDuration,
      baseDuration,
      startTime,
      commitTime
    });
    
    // 性能分析
    if (actualDuration > 16) {
      console.warn('渲染时间过长，考虑优化');
    }
  }

  return (
    <Profiler id="FragmentList" onRender={onRenderCallback}>
      <FragmentBasedList />
    </Profiler>
  );
}`
              }
            ]
          },
          {
            title: '浏览器开发者工具',
            description: '使用浏览器DevTools检查DOM结构和性能',
            items: [
              {
                title: 'DOM结构检查',
                description: '在Elements面板验证Fragment确实没有创建额外的DOM节点',
                solution: '对比使用Fragment前后的DOM结构差异',
                prevention: '养成检查最终DOM结构的习惯',
                code: `// 检查DOM结构的方法
function checkDOMStructure() {
  // 在控制台中运行
  console.log('DOM节点数量:', document.querySelectorAll('*').length);
  
  // 查看特定容器的结构
  const container = document.querySelector('.my-container');
  console.log('容器子节点:', container.children.length);
  
  // 打印DOM结构
  console.log(container.innerHTML);
}

// 性能监控
function monitorPerformance() {
  // 标记开始
  performance.mark('render-start');
  
  // 渲染组件
  ReactDOM.render(<FragmentComponent />, container);
  
  // 标记结束
  performance.mark('render-end');
  
  // 测量时间
  performance.measure('render-time', 'render-start', 'render-end');
  
  // 获取结果
  const measures = performance.getEntriesByName('render-time');
  console.log('渲染时间:', measures[0].duration);
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;