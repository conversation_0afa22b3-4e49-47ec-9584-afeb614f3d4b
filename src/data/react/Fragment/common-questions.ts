import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'question-1',
    question: 'Fragment和div有什么区别？什么时候使用Fragment？',
    answer: `Fragment和div的主要区别在于DOM结构和语义：

**Fragment的优势：**
- **不创建额外DOM节点**：Fragment在渲染后不会出现在DOM中，避免不必要的包装元素
- **保持语义结构**：不会破坏HTML语义，如表格、列表等结构化元素
- **更好的性能**：减少DOM节点数量，降低内存占用和渲染开销
- **CSS友好**：不会影响CSS选择器和布局

**使用场景：**
1. 组件需要返回多个同级元素
2. 表格行、列表项等结构化内容
3. 避免破坏CSS Grid或Flexbox布局
4. 减少DOM嵌套层级`,
    code: `// ❌ 使用div会创建额外的DOM节点
function UserProfile() {
  return (
    <div> {/* 不必要的包装div */}
      <h2>用户信息</h2>
      <p>姓名：张三</p>
      <p>邮箱：<EMAIL></p>
    </div>
  );
}

// ✅ 使用Fragment避免额外的DOM节点
function UserProfile() {
  return (
    <Fragment>
      <h2>用户信息</h2>
      <p>姓名：张三</p>
      <p>邮箱：<EMAIL></p>
    </Fragment>
  );
}

// ✅ 使用短语法更简洁
function UserProfile() {
  return (
    <>
      <h2>用户信息</h2>
      <p>姓名：张三</p>
      <p>邮箱：<EMAIL></p>
    </>
  );
}

// 表格场景：Fragment保持语义结构
function TableRows() {
  return (
    <>
      <tr><td>行1列1</td><td>行1列2</td></tr>
      <tr><td>行2列1</td><td>行2列2</td></tr>
    </>
  );
}`,
    tags: ['语法对比', 'DOM结构'],
    relatedQuestions: ['Fragment语法有哪几种写法', 'Fragment对性能有什么影响']
  },
  {
    id: 'question-2',
    question: 'Fragment的三种语法有什么区别？',
    answer: `React Fragment提供了三种不同的语法形式，各有优缺点：

**1. 完整语法：<Fragment>**
- 需要导入Fragment
- 支持key属性（用于列表渲染）
- 最明确和完整的写法

**2. 短语法：<>**
- 不需要导入
- 更简洁的写法
- 不支持key属性
- 某些工具可能不支持

**3. 数组语法：[]**
- 最原始的方式
- 每个元素都需要key
- 不推荐在新项目中使用

**选择建议：**
- 一般情况使用短语法<>
- 需要key时使用完整语法<Fragment>
- 避免使用数组语法`,
    code: `import React, { Fragment } from 'react';

// 1. 完整语法 - 支持key属性
function ItemList({ items }) {
  return (
    <div>
      {items.map(item => (
        <Fragment key={item.id}>
          <h3>{item.title}</h3>
          <p>{item.description}</p>
        </Fragment>
      ))}
    </div>
  );
}

// 2. 短语法 - 简洁但不支持key
function UserInfo() {
  return (
    <>
      <h2>个人信息</h2>
      <p>这是用户详情</p>
    </>
  );
}

// 3. 数组语法 - 不推荐
function OldStyle() {
  return [
    <h2 key="title">标题</h2>,
    <p key="content">内容</p>
  ];
}

// ❌ 错误：短语法不能使用key
function WrongUsage({ items }) {
  return (
    <div>
      {items.map(item => (
        <> {/* 无法添加key属性 */}
          <h3>{item.title}</h3>
          <p>{item.description}</p>
        </>
      ))}
    </div>
  );
}`,
    tags: ['语法形式', 'key属性'],
    relatedQuestions: ['Fragment什么时候需要key', '为什么短语法不支持属性']
  },
  {
    id: 'question-3',
    question: 'Fragment对性能有什么影响？',
    answer: `Fragment对性能的影响主要体现在DOM操作优化和内存使用方面：

**性能优势：**
1. **减少DOM节点**：避免创建不必要的包装元素，减少DOM树的大小
2. **更快的渲染**：更少的DOM节点意味着更快的渲染和更新
3. **内存优化**：减少DOM元素的内存占用
4. **CSS性能**：更少的DOM层级，CSS选择器匹配更快

**具体影响：**
- 大型列表渲染时效果更明显
- 移动端性能提升更显著
- 复杂布局中避免不必要的重排重绘

**注意事项：**
- Fragment本身的开销几乎可以忽略
- 主要优势在于避免额外的DOM元素
- 在简单场景下差异可能不明显`,
    code: `// 性能对比示例
function ProductList({ products }) {
  // ❌ 每个产品都有额外的div包装
  return (
    <div className="product-list">
      {products.map(product => (
        <div key={product.id}> {/* 额外的DOM节点 */}
          <img src={product.image} alt={product.name} />
          <h3>{product.name}</h3>
          <p>{product.price}</p>
        </div>
      ))}
    </div>
  );
}

// ✅ 使用Fragment避免额外包装
function ProductList({ products }) {
  return (
    <div className="product-list">
      {products.map(product => (
        <Fragment key={product.id}>
          <img src={product.image} alt={product.name} />
          <h3>{product.name}</h3>
          <p>{product.price}</p>
        </Fragment>
      ))}
    </div>
  );
}

// 性能测试：大量元素渲染
function PerformanceTest() {
  const items = Array.from({ length: 1000 }, (_, i) => i);
  
  return (
    <div>
      {items.map(item => (
        <Fragment key={item}>
          <span>Item {item}</span>
          <br />
        </Fragment>
      ))}
    </div>
  );
}

// 使用React DevTools Profiler测量性能差异
// Fragment版本通常比div包装版本快5-15%`,
    tags: ['性能优化', 'DOM节点'],
    relatedQuestions: ['大型列表如何优化渲染', '移动端React性能最佳实践']
  }
];

export default commonQuestions;