import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "React.Fragment是React中用于返回多个子元素而不添加额外DOM节点的组件，解决JSX必须有单一根元素的限制",

  introduction: `React.Fragment是React 16.0版本引入的内置组件，解决了JSX语法中每个组件必须返回单一根元素的限制。在Fragment出现之前，开发者经常需要使用多余的div元素来包装多个兄弟元素，这会造成不必要的DOM嵌套和样式问题。

Fragment提供了一种"透明"的包装方式，让你可以返回多个子元素而不在DOM中产生额外的节点。它有两种语法：完整语法<React.Fragment>和简写语法<>，简写语法是React生态中使用最广泛的语法糖之一。

在现代React开发中，Fragment已成为日常开发的基础工具，特别是在组件返回列表项、表格行、条件渲染多个元素等场景。它是实现干净DOM结构的重要工具，也是性能优化的一个重要方面。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react/index.d.ts:195
 * - 实现文件：packages/react/src/ReactFragment.js
 */

// 完整语法
<React.Fragment>
  {children}
</React.Fragment>

// 简写语法（最常用）
<>
  {children}
</>

// 带key属性（必须使用完整语法）
<React.Fragment key={uniqueKey}>
  {children}
</React.Fragment>

// TypeScript接口定义
interface FragmentProps {
  children?: ReactNode;
  key?: React.Key;
}

// 在函数组件中的使用
function Component() {
  return (
    <>
      <Header />
      <Main />
      <Footer />
    </>
  );
}

// 在列表渲染中使用
{items.map(item => (
  <React.Fragment key={item.id}>
    <ListItem item={item} />
    <Divider />
  </React.Fragment>
))}`,

  quickExample: `function BlogPost({ post, showMetadata }) {
  return (
    <>
      {/* 主要内容 */}
      <h1>{post.title}</h1>
      <div className="content">
        {post.content}
      </div>
      
      {/* 条件渲染元数据 */}
      {showMetadata && (
        <>
          <div className="author">作者: {post.author}</div>
          <div className="date">发布时间: {post.publishDate}</div>
          <div className="tags">
            标签: {post.tags.join(', ')}
          </div>
        </>
      )}
    </>
  );
}

// 在表格中使用Fragment（需要key）
function DataTable({ rows }) {
  return (
    <table>
      <tbody>
        {rows.map(row => (
          <React.Fragment key={row.id}>
            <tr>
              <td>{row.name}</td>
              <td>{row.value}</td>
            </tr>
            {row.hasDetails && (
              <tr className="details">
                <td colSpan={2}>{row.details}</td>
              </tr>
            )}
          </React.Fragment>
        ))}
      </tbody>
    </table>
  );
}`,

  scenarioDiagram: [
    {
      title: "Fragment vs 传统div包装对比",
      description: "展示Fragment如何避免不必要的DOM节点，保持更干净的DOM结构",
      diagram: `graph TB
        subgraph "传统div包装"
          A1[父组件] --> B1[div包装器]
          B1 --> C1[实际内容1]
          B1 --> D1[实际内容2]
          B1 --> E1[实际内容3]
        end

        subgraph "使用Fragment"
          A2[父组件] --> C2[实际内容1]
          A2 --> D2[实际内容2]
          A2 --> E2[实际内容3]
        end

        style B1 fill:#ffebee,stroke:#d32f2f,stroke-width:2px
        style A2 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
        style C2 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
        style D2 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
        style E2 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px`
    },
    {
      title: "Fragment在列表渲染中的使用",
      description: "Fragment在map渲染中的key使用和DOM结构优化",
      diagram: `graph TD
        A[数组数据] --> B[map函数]
        B --> C{需要key?}
        
        C -->|是| D[React.Fragment key=id]
        C -->|否| E[简写语法 <>]
        
        D --> F[多个子元素]
        E --> G[多个子元素]
        
        F --> H[DOM: 无额外节点]
        G --> I[DOM: 无额外节点]

        style A fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
        style D fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
        style E fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
        style H fill:#fff3e0,stroke:#f57c00,stroke-width:2px
        style I fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "Fragment语法选择流程",
      description: "如何选择Fragment的完整语法或简写语法",
      diagram: `graph TB
        A[需要使用Fragment] --> B{需要key属性?}
        
        B -->|是| C[使用完整语法]
        B -->|否| D{需要其他属性?}
        
        D -->|是| C
        D -->|否| E[使用简写语法]
        
        C --> F[React.Fragment key=xx]
        E --> G[空标签 <>]
        
        F --> H[适用于列表渲染]
        G --> I[适用于一般场景]

        style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
        style C fill:#fff3e0,stroke:#f57c00,stroke-width:2px
        style E fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
        style F fill:#fff3e0,stroke:#f57c00,stroke-width:2px
        style G fill:#e8f5e8,stroke:#388e3c,stroke-width:2px`
    }
  ],
  
  parameters: [
    {
      name: "children",
      type: "ReactNode",
      required: false,
      description: "Fragment内部的子元素，可以是任意数量的React节点",
      example: "<div>Hello</div>, text, {variable}"
    },
    {
      name: "key",
      type: "React.Key",
      required: false,
      description: "当在列表中使用Fragment时，用于React的reconciliation算法。只能在完整语法中使用",
      example: "item.id, index, 'unique-key'"
    }
  ],
  
  returnValue: {
    type: "ReactElement",
    description: "返回一个Fragment元素，在DOM中不会产生任何额外的节点，直接渲染其子元素",
    example: "渲染时children会直接插入到父元素中"
  },
  
  keyFeatures: [
    {
      title: "零DOM占用",
      description: "Fragment不会在DOM中创建任何额外的节点，完全透明地包装子元素",
      benefit: "减少DOM嵌套，提升性能，避免样式冲突"
    },
    {
      title: "简写语法支持",
      description: "提供<></>简写语法，是React中最简洁的语法糖之一",
      benefit: "代码更加简洁易读，减少噪音字符"
    },
    {
      title: "列表渲染优化",
      description: "支持key属性，完美适配React列表渲染的需求",
      benefit: "在列表中使用时提供更好的性能和正确的更新行为"
    },
    {
      title: "完全类型安全",
      description: "完整的TypeScript支持，与其他React组件无差别使用",
      benefit: "开发时有完整的类型检查和智能提示"
    }
  ],
  
  limitations: [
    "简写语法<></>不支持key属性和其他props",
    "在某些旧版本的语法高亮和格式化工具中可能不被识别",
    "不能在Fragment上添加事件监听器或其他DOM属性",
    "在React DevTools中不会显示为独立组件",
    "某些CSS选择器（如>直接子选择器）可能会受到影响"
  ],
  
  bestPractices: [
    "优先使用简写语法<></>，除非需要key属性",
    "在列表渲染返回多个元素时使用Fragment而不是额外div",
    "使用Fragment避免破坏CSS Grid和Flexbox布局",
    "在条件渲染多个元素时使用Fragment保持DOM结构简洁",
    "在表格相关元素（tr、td、li等）中使用Fragment避免无效HTML",
    "配合条件渲染使用，避免不必要的包装元素",
    "在组件返回多个顶级元素时使用Fragment",
    "使用Fragment时确保不会破坏现有的CSS样式规则"
  ],
  
  warnings: [
    "在需要key的列表渲染中必须使用完整语法，不能用简写",
    "注意Fragment对CSS选择器的影响，特别是直接子选择器",
    "不要在Fragment上设置className或其他DOM属性",
    "避免过度嵌套Fragment，虽然技术上可行但会降低可读性",
    "确保开发工具和构建工具支持JSX Fragment语法"
  ]
};

export default basicInfo;