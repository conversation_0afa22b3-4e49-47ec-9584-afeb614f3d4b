import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-1',
    question: 'renderToPipeableStream什么时候开始传输HTML？如何控制传输时机？',
    answer: `renderToPipeableStream的传输时机由onShellReady回调控制，当页面的"Shell"（同步内容）准备就绪时触发。

**传输时机机制：**
1. **Shell渲染阶段**：React首先渲染所有同步内容和Suspense的fallback
2. **onShellReady触发**：Shell准备完成时调用此回调
3. **开始传输**：在回调中调用pipe(response)开始HTML传输
4. **异步内容替换**：后续异步内容准备好时自动替换fallback

**控制策略：**
- 在onShellReady中设置响应头和状态码
- 可以根据条件决定是否立即开始传输
- 通过Suspense边界控制哪些内容属于Shell
- 使用超时机制避免无限等待

**最佳实践：**
- 确保Shell包含页面的核心结构和重要内容
- 避免在Shell中包含耗时的异步操作
- 合理设置Suspense边界，平衡首屏速度和完整性`,
    code: `// 传输时机控制示例
function createControlledSSRHandler() {
  return (req, res) => {
    const startTime = Date.now();
    let shellTransmitted = false;

    const { pipe, abort } = renderToPipeableStream(<App />, {
      onShellReady() {
        const shellTime = Date.now() - startTime;

        // 检查Shell渲染时间
        if (shellTime > 1000) {
          console.warn('Shell render took too long:', shellTime);
        }

        // 条件控制传输
        if (shouldStartTransmission(req, shellTime)) {
          shellTransmitted = true;

          // 设置响应头
          res.statusCode = 200;
          res.setHeader('Content-Type', 'text/html; charset=utf-8');
          res.setHeader('X-Shell-Time', shellTime.toString());

          // 开始传输
          pipe(res);
          console.log('Started HTML transmission after', shellTime, 'ms');
        } else {
          // 延迟传输或降级处理
          handleDelayedTransmission(req, res, pipe);
        }
      },

      onShellError(error) {
        if (!shellTransmitted) {
          console.error('Shell error, providing fallback:', error);
          res.statusCode = 500;
          res.send('<h1>页面加载失败</h1>');
        }
      }
    });

    // 超时控制
    setTimeout(() => {
      if (!shellTransmitted) {
        console.log('Shell timeout, aborting');
        abort('Shell timeout');
      }
    }, 3000);
  };
}

// 传输条件判断
function shouldStartTransmission(req, shellTime) {
  // 基于多个因素决定是否开始传输
  const factors = {
    shellTime: shellTime < 2000,           // Shell时间合理
    userAgent: !isCrawler(req),            // 非爬虫用户
    networkHint: isGoodNetwork(req),       // 网络条件良好
    serverLoad: getServerLoad() < 0.8      // 服务器负载正常
  };

  return Object.values(factors).every(Boolean);
}

// Suspense边界设计
function App() {
  return (
    <html>
      <head>
        <title>我的应用</title>
      </head>
      <body>
        {/* Shell内容 - 立即渲染 */}
        <header>
          <nav>导航菜单</nav>
        </header>

        <main>
          {/* 核心内容 - 属于Shell */}
          <h1>页面标题</h1>
          <div className="important-content">
            重要的静态内容
          </div>

          {/* 异步内容 - 不属于Shell */}
          <Suspense fallback={<div>加载用户数据中...</div>}>
            <UserProfile />
          </Suspense>

          <Suspense fallback={<div>加载文章列表中...</div>}>
            <ArticleList />
          </Suspense>
        </main>

        {/* Footer - 属于Shell */}
        <footer>
          版权信息
        </footer>
      </body>
    </html>
  );
}`,
    tags: ['传输时机', 'Shell控制'],
    relatedQuestions: ['如何优化Shell的渲染速度？', '如何处理Shell渲染错误？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-2',
    question: 'renderToPipeableStream如何处理SEO和搜索引擎爬虫？',
    answer: `renderToPipeableStream对SEO友好，但需要特殊处理搜索引擎爬虫以确保完整内容被索引。

**SEO优化策略：**
1. **爬虫检测**：识别搜索引擎爬虫，提供完整内容
2. **onAllReady使用**：对爬虫等待所有内容加载完成
3. **结构化数据**：在Shell中包含重要的meta信息
4. **关键内容优先**：确保SEO关键内容在Shell中

**爬虫处理方式：**
- 检测User-Agent识别爬虫
- 对爬虫使用onAllReady而非onShellReady
- 提供完整的HTML内容给爬虫
- 确保所有重要内容都能被索引

**注意事项：**
- 避免重要内容完全依赖异步加载
- 在Shell中包含页面的核心信息
- 使用适当的HTTP状态码
- 提供合适的缓存策略`,
    code: `// SEO优化的SSR处理
function createSEOOptimizedHandler() {
  return (req, res) => {
    const isBot = detectSearchBot(req);
    const startTime = Date.now();

    const { pipe, abort } = renderToPipeableStream(
      <SEOOptimizedApp />,
      {
        onShellReady() {
          // 对普通用户立即开始传输
          if (!isBot) {
            res.statusCode = 200;
            res.setHeader('Content-Type', 'text/html; charset=utf-8');

            // SEO相关头部
            res.setHeader('X-Robots-Tag', 'index, follow');

            pipe(res);
          }
        },

        onAllReady() {
          // 对搜索引擎爬虫等待完整内容
          if (isBot) {
            res.statusCode = 200;
            res.setHeader('Content-Type', 'text/html; charset=utf-8');

            // 爬虫专用头部
            res.setHeader('X-Rendered-For', 'search-bot');
            res.setHeader('X-Render-Time', (Date.now() - startTime).toString());

            pipe(res);
          }
        },

        onShellError(error) {
          console.error('SEO shell error:', error);
          res.statusCode = 500;
          res.send(generateSEOErrorPage());
        }
      }
    );

    // 爬虫超时时间更长
    const timeout = isBot ? 15000 : 5000;
    setTimeout(() => abort('Timeout'), timeout);
  };
}

// 搜索引擎爬虫检测
function detectSearchBot(req) {
  const userAgent = req.headers['user-agent'] || '';
  const botPatterns = [
    /googlebot/i,
    /bingbot/i,
    /slurp/i,
    /duckduckbot/i,
    /baiduspider/i,
    /yandexbot/i,
    /facebookexternalhit/i,
    /twitterbot/i,
    /linkedinbot/i
  ];

  return botPatterns.some(pattern => pattern.test(userAgent));
}

// SEO优化的应用组件
function SEOOptimizedApp() {
  return (
    <html lang="zh-CN">
      <head>
        {/* 核心SEO信息 - 在Shell中 */}
        <title>页面标题 - 网站名称</title>
        <meta name="description" content="页面描述，包含关键词" />
        <meta name="keywords" content="关键词1, 关键词2, 关键词3" />

        {/* Open Graph标签 */}
        <meta property="og:title" content="页面标题" />
        <meta property="og:description" content="页面描述" />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://example.com/page" />

        {/* 结构化数据 */}
        <script type="application/ld+json">
          {\`{
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "页面标题",
            "description": "页面描述",
            "url": "https://example.com/page"
          }\`}
        </script>

        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="canonical" href="https://example.com/page" />
      </head>
      <body>
        {/* 主要内容 - 在Shell中确保SEO可见 */}
        <header>
          <h1>主要标题</h1>
          <nav>
            <a href="/">首页</a>
            <a href="/about">关于我们</a>
            <a href="/contact">联系我们</a>
          </nav>
        </header>

        <main>
          {/* 核心内容 - SEO关键 */}
          <article>
            <h2>文章标题</h2>
            <p>重要的文本内容，包含关键词...</p>
          </article>

          {/* 次要内容 - 可以异步加载 */}
          <aside>
            <Suspense fallback={<div>加载相关文章...</div>}>
              <RelatedArticles />
            </Suspense>

            <Suspense fallback={<div>加载评论...</div>}>
              <Comments />
            </Suspense>
          </aside>
        </main>

        <footer>
          <p>&copy; 2024 网站名称. 保留所有权利.</p>
        </footer>
      </body>
    </html>
  );
}

// SEO错误页面
function generateSEOErrorPage() {
  return \`
    <!DOCTYPE html>
    <html lang="zh-CN">
      <head>
        <title>页面暂时不可用</title>
        <meta name="robots" content="noindex, nofollow">
      </head>
      <body>
        <h1>页面暂时不可用</h1>
        <p>请稍后重试</p>
      </body>
    </html>
  \`;
}`,
    tags: ['SEO优化', '搜索引擎'],
    relatedQuestions: ['如何优化爬虫的渲染性能？', '如何处理动态内容的SEO？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-3',
    question: 'renderToPipeableStream在生产环境中需要注意哪些性能和安全问题？',
    answer: `生产环境中使用renderToPipeableStream需要考虑性能优化、安全防护、监控告警等多个方面。

**性能优化要点：**
1. **缓存策略**：实现多层缓存，减少重复渲染
2. **负载均衡**：合理分配服务器资源
3. **内存管理**：监控内存使用，防止内存泄漏
4. **超时控制**：设置合理的超时时间

**安全防护措施：**
1. **XSS防护**：严格的内容转义和CSP策略
2. **CSRF防护**：实现CSRF令牌验证
3. **DDoS防护**：限流和请求频率控制
4. **输入验证**：严格验证所有用户输入

**监控和告警：**
1. **性能监控**：渲染时间、内存使用、错误率
2. **错误追踪**：详细的错误日志和堆栈跟踪
3. **业务监控**：用户体验指标和转化率
4. **自动告警**：异常情况的及时通知

**部署考虑：**
- 使用进程管理器（如PM2）
- 配置健康检查
- 实现优雅关闭
- 设置适当的环境变量`,
    code: `// 生产环境优化配置
class ProductionSSRServer {
  constructor() {
    this.cache = new ProductionCache();
    this.security = new SecurityManager();
    this.monitor = new MonitoringSystem();
    this.rateLimiter = new RateLimiter();
  }

  createProductionHandler() {
    return async (req, res) => {
      const requestId = generateRequestId();
      const startTime = Date.now();

      try {
        // 1. 安全检查
        await this.security.validateRequest(req);

        // 2. 限流检查
        await this.rateLimiter.checkLimit(req);

        // 3. 缓存检查
        const cached = await this.cache.get(req);
        if (cached) {
          return this.serveCached(res, cached);
        }

        // 4. 渲染处理
        await this.renderWithSafety(req, res, requestId);

      } catch (error) {
        this.handleProductionError(error, req, res, requestId);
      } finally {
        this.recordMetrics(requestId, startTime, req);
      }
    };
  }

  async renderWithSafety(req, res, requestId) {
    const { pipe, abort } = renderToPipeableStream(
      <ProductionApp />,
      {
        // 生产环境优化的脚本加载
        bootstrapScripts: this.getOptimizedScripts(),

        onShellReady: () => {
          // 安全头部设置
          this.security.setSecurityHeaders(res);

          // 性能头部
          res.setHeader('X-Request-ID', requestId);
          res.setHeader('X-Server-Instance', process.env.INSTANCE_ID);

          res.statusCode = 200;
          res.setHeader('Content-Type', 'text/html; charset=utf-8');

          pipe(res);
        },

        onShellError: (error) => {
          this.monitor.recordError('shell-error', error, requestId);
          this.renderErrorPage(res, 500);
        },

        onAllReady: () => {
          this.monitor.recordSuccess(requestId);
          // 缓存完整响应
          this.cache.set(req, res);
        },

        onError: (error) => {
          this.monitor.recordRenderError(error, requestId);
        }
      }
    );

    // 生产环境超时控制
    const timeout = this.calculateProductionTimeout(req);
    setTimeout(() => {
      this.monitor.recordTimeout(requestId);
      abort('Production timeout');
    }, timeout);
  }

  // 安全管理器
  class SecurityManager {
    setSecurityHeaders(res) {
      // XSS防护
      res.setHeader('X-XSS-Protection', '1; mode=block');
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');

      // CSP策略
      res.setHeader('Content-Security-Policy',
        "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
      );

      // HSTS
      res.setHeader('Strict-Transport-Security',
        'max-age=31536000; includeSubDomains'
      );
    }

    async validateRequest(req) {
      // 输入验证
      this.validateInput(req.query);
      this.validateInput(req.body);

      // 请求大小限制
      if (req.headers['content-length'] > 1024 * 1024) {
        throw new Error('Request too large');
      }

      // User-Agent验证
      if (!req.headers['user-agent']) {
        throw new Error('Missing user agent');
      }
    }

    validateInput(input) {
      if (!input) return;

      for (const [key, value] of Object.entries(input)) {
        if (typeof value === 'string') {
          // XSS检查
          if (this.containsXSS(value)) {
            throw new Error(\`XSS detected in \${key}\`);
          }

          // SQL注入检查
          if (this.containsSQLInjection(value)) {
            throw new Error(\`SQL injection detected in \${key}\`);
          }
        }
      }
    }
  }

  // 监控系统
  class MonitoringSystem {
    recordError(type, error, requestId) {
      const errorData = {
        type,
        message: error.message,
        stack: error.stack,
        requestId,
        timestamp: new Date().toISOString(),
        serverInstance: process.env.INSTANCE_ID
      };

      // 发送到错误监控服务
      this.sendToErrorService(errorData);

      // 记录到日志
      console.error('Production SSR Error:', errorData);
    }

    recordMetrics(requestId, duration, memoryUsage) {
      const metrics = {
        requestId,
        duration,
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
        timestamp: Date.now()
      };

      // 发送到监控服务
      this.sendToMetricsService(metrics);

      // 检查告警条件
      this.checkAlerts(metrics);
    }

    checkAlerts(metrics) {
      // 响应时间告警
      if (metrics.duration > 5000) {
        this.sendAlert('slow-response', metrics);
      }

      // 内存使用告警
      if (metrics.memoryUsage.heapUsed > 500 * 1024 * 1024) {
        this.sendAlert('high-memory', metrics);
      }
    }
  }

  // 缓存系统
  class ProductionCache {
    constructor() {
      this.redis = createRedisCluster();
      this.localCache = new Map();
    }

    async get(req) {
      const key = this.generateCacheKey(req);

      // 本地缓存
      const local = this.localCache.get(key);
      if (local && local.expiry > Date.now()) {
        return local.data;
      }

      // Redis缓存
      try {
        const cached = await this.redis.get(key);
        if (cached) {
          const data = JSON.parse(cached);
          // 回填本地缓存
          this.localCache.set(key, {
            data,
            expiry: Date.now() + 60000 // 1分钟
          });
          return data;
        }
      } catch (error) {
        console.error('Cache get error:', error);
      }

      return null;
    }
  }
}

// 使用示例
const productionServer = new ProductionSSRServer();
const app = express();

// 生产环境中间件
app.use(helmet()); // 安全头部
app.use(compression()); // 响应压缩
app.use(morgan('combined')); // 访问日志

app.use(productionServer.createProductionHandler());

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    instance: process.env.INSTANCE_ID
  });
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully');
  server.close(() => {
    process.exit(0);
  });
});`,
    tags: ['生产环境', '性能安全'],
    relatedQuestions: ['如何监控SSR的性能指标？', '如何处理高并发SSR请求？']
  }
];

export default commonQuestions;