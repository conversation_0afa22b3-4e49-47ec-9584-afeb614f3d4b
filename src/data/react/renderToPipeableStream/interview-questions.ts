import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 1,
    question: '什么是renderToPipeableStream？它与renderToString有什么区别？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'renderToPipeableStream是React 18中用于Node.js流式SSR的API，支持Suspense和渐进式HTML传输，而renderToString是同步渲染为字符串的传统方法。',
      detailed: `renderToPipeableStream和renderToString都是React的服务端渲染API，但在设计理念和性能表现上有显著差异：

**renderToPipeableStream的特点：**
1. **流式传输**：支持HTML内容的分块传输
2. **Suspense支持**：原生支持异步组件和数据获取
3. **渐进式渲染**：Shell优先，异步内容后续加载
4. **并发特性**：支持React 18的并发特性

**renderToString的特点：**
1. **同步渲染**：一次性渲染完整HTML字符串
2. **阻塞式**：需要等待所有内容准备完成
3. **简单直接**：API简单，易于理解和使用
4. **兼容性好**：支持所有React版本

**性能对比：**
- renderToPipeableStream：首屏更快，支持大型页面
- renderToString：简单场景下开销更小

**使用场景：**
- renderToPipeableStream：现代应用，需要高性能SSR
- renderToString：简单应用，兼容性要求高`,
      code: `// renderToPipeableStream 示例
import { renderToPipeableStream } from 'react-dom/server';

app.get('/', (req, res) => {
  const { pipe, abort } = renderToPipeableStream(<App />, {
    onShellReady() {
      res.statusCode = 200;
      res.setHeader('Content-Type', 'text/html');
      pipe(res); // 开始流式传输
    },
    onShellError(error) {
      res.statusCode = 500;
      res.send('<h1>Error</h1>');
    }
  });
});

// renderToString 示例
import { renderToString } from 'react-dom/server';

app.get('/', (req, res) => {
  try {
    const html = renderToString(<App />); // 同步渲染
    res.send(\`
      <!DOCTYPE html>
      <html>
        <body>
          <div id="root">\${html}</div>
        </body>
      </html>
    \`);
  } catch (error) {
    res.status(500).send('<h1>Error</h1>');
  }
});

// 带Suspense的组件对比
function App() {
  return (
    <div>
      <Header />
      <Suspense fallback={<div>Loading...</div>}>
        <AsyncContent />
      </Suspense>
    </div>
  );
}

// renderToPipeableStream: 支持Suspense，Header立即显示
// renderToString: 等待AsyncContent完成才返回HTML`
    },
    tags: ['基础概念', 'API对比']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 2,
    question: 'renderToPipeableStream的生命周期回调有哪些？如何正确使用？',
    difficulty: 'medium',
    frequency: 'high',
    category: '生命周期管理',
    answer: {
      brief: 'renderToPipeableStream提供onShellReady、onShellError、onAllReady、onError四个主要回调，分别处理Shell准备、Shell错误、全部完成和渲染错误等不同阶段。',
      detailed: `renderToPipeableStream的生命周期回调提供了精确的渲染控制能力：

**主要回调函数：**

1. **onShellReady()**
   - 触发时机：页面Shell（同步内容）准备就绪
   - 用途：开始HTML传输，设置响应头
   - 重要性：决定首屏显示时机

2. **onShellError(error)**
   - 触发时机：Shell渲染过程中发生错误
   - 用途：错误处理，返回错误页面
   - 重要性：确保用户看到友好的错误信息

3. **onAllReady()**
   - 触发时机：所有内容（包括异步）都准备完成
   - 用途：完成状态记录，清理资源
   - 重要性：标志渲染过程完全结束

4. **onError(error)**
   - 触发时机：渲染过程中的任何错误
   - 用途：错误日志记录，监控告警
   - 重要性：不中断渲染，用于错误追踪

**使用策略：**
- 在onShellReady中开始传输，确保快速首屏
- 在onShellError中提供降级方案
- 在onError中记录错误但不中断渲染
- 合理设置超时机制`,
      code: `// 生命周期回调的完整使用示例
function createSSRHandler() {
  return (req, res) => {
    const startTime = Date.now();
    let shellReady = false;

    const { pipe, abort } = renderToPipeableStream(<App />, {
      // Shell准备就绪 - 开始传输
      onShellReady() {
        shellReady = true;
        const shellTime = Date.now() - startTime;

        console.log(\`Shell ready in \${shellTime}ms\`);

        // 设置响应头
        res.statusCode = 200;
        res.setHeader('Content-Type', 'text/html; charset=utf-8');
        res.setHeader('X-Shell-Time', shellTime.toString());

        // 开始流式传输
        pipe(res);
      },

      // Shell渲染错误 - 提供降级方案
      onShellError(error) {
        console.error('Shell render error:', error);

        if (!shellReady) {
          res.statusCode = 500;
          res.setHeader('Content-Type', 'text/html');
          res.send(\`
            <!DOCTYPE html>
            <html>
              <head><title>服务暂时不可用</title></head>
              <body>
                <h1>页面加载失败</h1>
                <p>请刷新页面重试，或稍后再访问</p>
                <script>
                  setTimeout(() => {
                    window.location.reload();
                  }, 5000);
                </script>
              </body>
            </html>
          \`);
        }
      },

      // 所有内容完成 - 记录完成状态
      onAllReady() {
        const totalTime = Date.now() - startTime;
        console.log(\`Full render completed in \${totalTime}ms\`);

        // 记录性能指标
        recordMetrics({
          renderTime: totalTime,
          url: req.url,
          userAgent: req.headers['user-agent']
        });
      },

      // 渲染错误 - 记录但不中断
      onError(error) {
        console.error('Render error:', {
          error: error.message,
          stack: error.stack,
          url: req.url,
          timestamp: new Date().toISOString()
        });

        // 发送错误监控
        sendErrorToMonitoring({
          type: 'ssr-render-error',
          error: error.message,
          url: req.url,
          userAgent: req.headers['user-agent']
        });

        // 不中断渲染，让其他内容继续加载
      }
    });

    // 超时处理
    const timeout = setTimeout(() => {
      console.log('SSR timeout, aborting render');
      abort('Request timeout');
    }, 10000);

    // 请求结束清理
    res.on('close', () => {
      clearTimeout(timeout);
    });

    res.on('error', (error) => {
      console.error('Response error:', error);
      clearTimeout(timeout);
    });
  };
}

// 错误处理策略
class SSRErrorHandler {
  static handleShellError(error, res) {
    // 根据错误类型提供不同的降级方案
    if (error.name === 'ChunkLoadError') {
      return this.renderChunkErrorPage(res);
    }

    if (error.name === 'NetworkError') {
      return this.renderNetworkErrorPage(res);
    }

    return this.renderGenericErrorPage(res);
  }

  static renderGenericErrorPage(res) {
    res.status(500).send(\`
      <!DOCTYPE html>
      <html>
        <head>
          <title>服务暂时不可用</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error-container { max-width: 500px; margin: 0 auto; }
          </style>
        </head>
        <body>
          <div class="error-container">
            <h1>服务暂时不可用</h1>
            <p>我们正在努力修复问题，请稍后重试</p>
            <button onclick="window.location.reload()">重新加载</button>
          </div>
        </body>
      </html>
    \`);
  }
}

// 性能监控
function recordMetrics(metrics) {
  // 记录到监控系统
  console.log('SSR Metrics:', metrics);
}

function sendErrorToMonitoring(errorData) {
  // 发送到错误监控服务
  console.log('Error reported:', errorData);
}`
    },
    tags: ['生命周期', '错误处理']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 3,
    question: '如何设计一个高性能的renderToPipeableStream SSR架构？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    answer: {
      brief: '高性能SSR架构需要考虑缓存策略、负载均衡、错误处理、监控告警等多个维度，建立分层的SSR服务架构和完善的性能优化机制。',
      detailed: `高性能renderToPipeableStream SSR架构是一个复杂的系统工程：

**架构设计原则：**
1. **分层缓存策略**：页面级、组件级、数据级多层缓存
2. **负载均衡优化**：智能路由和服务器资源分配
3. **错误隔离机制**：防止单点故障影响整体服务
4. **性能监控体系**：实时监控和自动优化

**核心组件设计：**
1. **SSR服务集群**：多实例部署，支持水平扩展
2. **缓存层**：Redis集群，支持分布式缓存
3. **负载均衡器**：智能路由，健康检查
4. **监控系统**：性能指标收集和告警

**优化策略：**
1. **预渲染优化**：静态内容预生成
2. **流式优化**：合理的Suspense边界设计
3. **资源优化**：CDN加速，资源压缩
4. **数据库优化**：查询优化，连接池管理

**扩展性考虑：**
- 支持多租户部署
- 支持A/B测试
- 支持灰度发布
- 支持动态配置`,
      code: `// 高性能SSR架构实现
class HighPerformanceSSRArchitecture {
  constructor() {
    this.cacheManager = new DistributedCacheManager();
    this.loadBalancer = new IntelligentLoadBalancer();
    this.monitoringSystem = new SSRMonitoringSystem();
    this.errorHandler = new AdvancedErrorHandler();
  }

  // 主SSR处理器
  createSSRHandler() {
    return async (req, res) => {
      const requestId = generateRequestId();
      const startTime = Date.now();

      try {
        // 1. 缓存检查
        const cachedResponse = await this.checkCache(req);
        if (cachedResponse) {
          return this.serveCachedResponse(res, cachedResponse);
        }

        // 2. 负载均衡
        const serverInstance = this.loadBalancer.selectServer(req);

        // 3. 预加载数据
        const preloadedData = await this.preloadData(req);

        // 4. 流式渲染
        await this.renderWithStreaming(req, res, preloadedData, requestId);

      } catch (error) {
        this.errorHandler.handleSSRError(error, req, res);
      } finally {
        this.recordMetrics(requestId, startTime, req);
      }
    };
  }

  // 分布式缓存管理
  async checkCache(req) {
    const cacheKey = this.generateCacheKey(req);

    // 多层缓存检查
    const memoryCache = await this.cacheManager.getFromMemory(cacheKey);
    if (memoryCache) return memoryCache;

    const redisCache = await this.cacheManager.getFromRedis(cacheKey);
    if (redisCache) {
      // 回填内存缓存
      this.cacheManager.setToMemory(cacheKey, redisCache);
      return redisCache;
    }

    return null;
  }

  // 智能流式渲染
  async renderWithStreaming(req, res, preloadedData, requestId) {
    const { pipe, abort } = renderToPipeableStream(
      <App preloadedData={preloadedData} />,
      {
        // 引导脚本优化
        bootstrapScripts: this.getOptimizedScripts(req),

        onShellReady: () => {
          const shellTime = Date.now() - req.startTime;

          // 性能头部
          res.setHeader('X-Shell-Time', shellTime);
          res.setHeader('X-Request-ID', requestId);
          res.setHeader('X-Server-Instance', process.env.INSTANCE_ID);

          // 缓存控制
          const cacheControl = this.calculateCacheControl(req);
          res.setHeader('Cache-Control', cacheControl);

          res.statusCode = 200;
          res.setHeader('Content-Type', 'text/html; charset=utf-8');

          pipe(res);
        },

        onShellError: (error) => {
          this.errorHandler.handleShellError(error, req, res);
        },

        onAllReady: () => {
          const totalTime = Date.now() - req.startTime;
          this.monitoringSystem.recordRenderComplete(requestId, totalTime);

          // 缓存完整响应
          this.cacheCompleteResponse(req, res);
        },

        onError: (error) => {
          this.monitoringSystem.recordRenderError(requestId, error);
        }
      }
    );

    // 智能超时控制
    const timeout = this.calculateTimeout(req);
    setTimeout(() => abort('Timeout'), timeout);
  }

  // 数据预加载优化
  async preloadData(req) {
    const dataRequirements = this.analyzeDataRequirements(req);

    // 并行数据获取
    const dataPromises = dataRequirements.map(requirement =>
      this.fetchDataWithCache(requirement)
    );

    const results = await Promise.allSettled(dataPromises);

    return this.processDataResults(results);
  }

  // 智能负载均衡
  selectOptimalServer(req) {
    const servers = this.loadBalancer.getHealthyServers();

    // 基于多个因素选择服务器
    const factors = {
      cpuUsage: 0.3,
      memoryUsage: 0.3,
      activeConnections: 0.2,
      responseTime: 0.2
    };

    return this.loadBalancer.selectByWeightedFactors(servers, factors);
  }

  // 性能监控
  recordMetrics(requestId, startTime, req) {
    const metrics = {
      requestId,
      duration: Date.now() - startTime,
      url: req.url,
      userAgent: req.headers['user-agent'],
      serverInstance: process.env.INSTANCE_ID,
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    };

    this.monitoringSystem.record(metrics);
  }
}

// 分布式缓存管理器
class DistributedCacheManager {
  constructor() {
    this.memoryCache = new Map();
    this.redisClient = createRedisClient();
  }

  async getFromMemory(key) {
    const cached = this.memoryCache.get(key);
    if (cached && cached.expiry > Date.now()) {
      return cached.data;
    }
    return null;
  }

  async getFromRedis(key) {
    try {
      const cached = await this.redisClient.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }

  async setToMemory(key, data, ttl = 300000) {
    this.memoryCache.set(key, {
      data,
      expiry: Date.now() + ttl
    });
  }

  async setToRedis(key, data, ttl = 600) {
    try {
      await this.redisClient.setex(key, ttl, JSON.stringify(data));
    } catch (error) {
      console.error('Redis set error:', error);
    }
  }
}

// 监控系统
class SSRMonitoringSystem {
  constructor() {
    this.metrics = new Map();
    this.alerts = new AlertManager();
  }

  record(metrics) {
    // 记录指标
    this.metrics.set(metrics.requestId, metrics);

    // 实时告警检查
    this.checkAlerts(metrics);

    // 发送到监控服务
    this.sendToMonitoringService(metrics);
  }

  checkAlerts(metrics) {
    if (metrics.duration > 5000) {
      this.alerts.trigger('slow-ssr', metrics);
    }

    if (metrics.memoryUsage.heapUsed > 500 * 1024 * 1024) {
      this.alerts.trigger('high-memory', metrics);
    }
  }
}

// 使用示例
const ssrArchitecture = new HighPerformanceSSRArchitecture();
const app = express();

app.use(ssrArchitecture.createSSRHandler());

app.listen(3000, () => {
  console.log('High-performance SSR server running on port 3000');
});`
    },
    tags: ['架构设计', '高性能SSR']
  }
];

export default interviewQuestions;