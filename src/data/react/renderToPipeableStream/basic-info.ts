import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: "renderToPipeableStream是React中用于Node.js流式服务端渲染的API，专门为Node.js环境设计，支持Suspense、流式传输和渐进式HTML生成，实现高性能的服务端渲染解决方案。",

  introduction: `renderToPipeableStream是React 18.0+引入的ReactDOM Server API，专门为现代Node.js服务端渲染设计的流式渲染函数。

它遵循现代Web标准的流式传输原则，在服务端渲染和客户端水合之间做出了智能优化，允许开发者将React组件渲染为可管道传输的Node.js流，实现渐进式的HTML传输。

主要用于Node.js SSR应用、流式HTML生成和Suspense支持的服务端渲染。相比传统的同步渲染方式，它的优势在于支持流式传输，提供更好的用户体验和服务器性能。

在React SSR生态中，它是现代服务端渲染的核心工具，常见于需要高性能SSR的应用，特别适合大型应用和需要快速首屏渲染的场景。

核心优势包括流式HTML传输、Suspense原生支持、渐进式渲染，同时提供了完整的错误处理和生命周期控制，易于集成到现有的Node.js服务器中。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react-dom/server.d.ts:45
 * - 实现文件：packages/react-dom/src/server/ReactDOMFizzServerNode.js:28
 * - 流处理：packages/react-dom/src/server/ReactDOMServerStreaming.js:15
 */

// 基础语法
function renderToPipeableStream(
  children: ReactNode,
  options?: {
    identifierPrefix?: string;
    namespaceURI?: string;
    nonce?: string;
    bootstrapScriptContent?: string;
    bootstrapScripts?: string[];
    bootstrapModules?: string[];
    progressiveChunkSize?: number;
    onShellReady?: () => void;
    onShellError?: (error: unknown) => void;
    onAllReady?: () => void;
    onError?: (error: unknown) => void;
  }
): {
  pipe: (destination: NodeJS.WritableStream) => void;
  abort: (reason?: any) => void;
}

// 使用示例
const { pipe, abort } = renderToPipeableStream(<App />, {
  bootstrapScripts: ['/static/js/main.js'],
  onShellReady() {
    response.statusCode = 200;
    response.setHeader('Content-Type', 'text/html');
    pipe(response);
  },
  onShellError(error) {
    response.statusCode = 500;
    response.setHeader('Content-Type', 'text/html');
    response.send('<h1>Server Error</h1>');
  },
  onError(error) {
    console.error('SSR Error:', error);
  }
});

/**
 * 参数约束：
 * - children 必须是有效的React节点
 * - destination 必须是Node.js可写流
 * - 支持Suspense和并发特性
 * - 自动处理客户端水合
 */`,

  quickExample: `// Node.js Express服务器示例
import express from 'express';
import { renderToPipeableStream } from 'react-dom/server';
import App from './App';

const server = express();

server.get('/', (request, response) => {
  // 渲染React应用为流式HTML
  const { pipe, abort } = renderToPipeableStream(<App />, {
    // 客户端JavaScript文件
    bootstrapScripts: ['/static/js/main.js'],

    // Shell准备就绪时开始传输
    onShellReady() {
      response.statusCode = 200;
      response.setHeader('Content-Type', 'text/html');
      response.setHeader('Cache-Control', 'no-cache');

      // 开始流式传输HTML
      pipe(response);
    },

    // Shell渲染错误处理
    onShellError(error) {
      console.error('Shell render error:', error);
      response.statusCode = 500;
      response.setHeader('Content-Type', 'text/html');
      response.send('<h1>服务器错误</h1>');
    },

    // 所有内容准备就绪
    onAllReady() {
      console.log('All content ready');
    },

    // 渲染过程中的错误
    onError(error) {
      console.error('Render error:', error);
    }
  });

  // 请求超时处理
  setTimeout(() => {
    abort('Request timeout');
  }, 10000);
});

// 带Suspense的组件示例
function App() {
  return (
    <html>
      <head>
        <title>流式SSR应用</title>
        <meta charSet="utf-8" />
      </head>
      <body>
        <div id="root">
          <Header />
          <Suspense fallback={<div>加载主要内容中...</div>}>
            <MainContent />
          </Suspense>
          <Suspense fallback={<div>加载侧边栏中...</div>}>
            <Sidebar />
          </Suspense>
          <Footer />
        </div>
      </body>
    </html>
  );
}

server.listen(3000, () => {
  console.log('SSR服务器运行在 http://localhost:3000');
});`,

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "renderToPipeableStream在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用这个API来实现高性能的流式SSR",
      diagram: `graph LR
      A[renderToPipeableStream核心场景] --> B[Node.js SSR应用]
      A --> C[流式HTML传输]
      A --> D[Suspense SSR支持]

      B --> B1["🌐 Express服务器<br/>构建SSR Web应用"]
      B --> B2["🚀 Next.js自定义服务器<br/>高性能SSR方案"]

      C --> C1["📡 渐进式HTML传输<br/>提升首屏加载速度"]
      C --> C2["🔄 分块内容传输<br/>优化大页面渲染"]

      D --> D1["⏳ 异步组件渲染<br/>支持数据获取组件"]
      D --> D2["🎯 选择性水合<br/>优化客户端性能"]

      style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
      style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
      style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "流式渲染流程",
      description: "renderToPipeableStream的流式渲染机制，展示其如何通过分块传输实现渐进式HTML生成",
      diagram: `graph TB
      A[开始渲染] --> B[Shell准备]
      B --> C[onShellReady触发]
      C --> D[开始流式传输]
      D --> E[渲染同步内容]
      E --> F[遇到Suspense边界]
      F --> G[发送Fallback内容]
      G --> H[异步加载数据]
      H --> I[替换Fallback内容]
      I --> J[继续渲染]
      J --> K[onAllReady触发]
      K --> L[传输完成]

      style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
      style C fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px
      style F fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style I fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
      style K fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style L fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px`
    },
    {
      title: "React SSR生态系统",
      description: "renderToPipeableStream在React SSR生态系统中的位置和与其他渲染方法的关系",
      diagram: `graph TD
      A[React SSR生态] --> B[Node.js环境]
      A --> C[Web标准环境]
      A --> D[静态生成]

      B --> B1["renderToPipeableStream<br/>流式Node.js渲染"]
      B --> B2["renderToString<br/>同步字符串渲染"]

      C --> C1["renderToReadableStream<br/>Web Streams渲染"]
      C --> C2["renderToStaticMarkup<br/>静态HTML生成"]

      D --> D1["Static Generation<br/>构建时预渲染"]
      D --> D2["ISR<br/>增量静态再生"]

      B1 -.-> C1
      B2 -.-> C2
      B1 -.-> D1

      style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
      style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
      style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style B1 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
    }
  ],
  
  parameters: [
    {
      name: "children",
      type: "ReactNode",
      required: true,
      description: "要渲染的React组件树。可以是任何有效的React节点，包括组件、元素、字符串等。",
      example: `// 简单组件
<App />

// 完整HTML结构
<html>
  <head><title>My App</title></head>
  <body><div id="root"><App /></div></body>
</html>`
    },
    {
      name: "options",
      type: "RenderToPipeableStreamOptions",
      required: false,
      description: "渲染选项配置对象，包含各种回调函数和配置参数。",
      example: `{
  bootstrapScripts: ['/static/js/main.js'],
  onShellReady() { pipe(response); },
  onShellError(error) { handleError(error); },
  onAllReady() { console.log('完成'); },
  onError(error) { logError(error); }
}`
    }
  ],

  returnValue: {
    type: "{ pipe: (destination: NodeJS.WritableStream) => void; abort: (reason?: any) => void }",
    description: "返回一个包含pipe和abort方法的对象。pipe方法用于将渲染结果传输到目标流，abort方法用于中止渲染过程。",
    example: `const { pipe, abort } = renderToPipeableStream(<App />, {
  onShellReady() {
    response.statusCode = 200;
    response.setHeader('Content-Type', 'text/html');
    pipe(response); // 开始流式传输
  },
  onShellError() {
    abort('Shell render failed'); // 中止渲染
  }
});`
  },

  keyFeatures: [
    {
      title: "流式HTML传输",
      description: "支持将HTML内容分块传输，无需等待整个页面渲染完成即可开始发送内容",
      benefit: "显著提升首屏加载速度，改善用户体验，特别是对于大型页面"
    },
    {
      title: "Suspense原生支持",
      description: "完全支持React Suspense，可以处理异步组件和数据获取",
      benefit: "实现渐进式渲染，允许页面部分内容先显示，异步内容后续加载"
    },
    {
      title: "Node.js流集成",
      description: "与Node.js流系统深度集成，支持背压控制和流式处理",
      benefit: "高效的内存使用，支持大型页面渲染，避免内存溢出"
    },
    {
      title: "生命周期回调",
      description: "提供丰富的生命周期回调函数，精确控制渲染过程的各个阶段",
      benefit: "灵活的错误处理和状态管理，支持复杂的SSR场景"
    },
    {
      title: "客户端水合优化",
      description: "自动生成客户端水合所需的标记和脚本，确保SSR和CSR的一致性",
      benefit: "无缝的服务端到客户端过渡，避免水合不匹配问题"
    },
    {
      title: "并发特性支持",
      description: "支持React 18的并发特性，包括时间切片和优先级调度",
      benefit: "更好的服务器性能，支持高并发SSR请求"
    }
  ],

  limitations: [
    "只能在Node.js环境中使用，不支持浏览器或其他JavaScript运行时",
    "需要React 18.0+版本，在旧版本中不可用",
    "流式传输需要客户端支持，某些代理或CDN可能缓冲响应",
    "Suspense边界的错误处理比同步渲染更复杂",
    "调试流式渲染比传统SSR更困难，需要专门的工具和技术"
  ],

  bestPractices: [
    "合理设置onShellReady和onAllReady回调，确保适当的响应时机",
    "使用Suspense边界包装可能失败的异步组件，提供优雅的降级",
    "设置合适的超时机制，避免长时间等待异步内容",
    "在生产环境中配置适当的错误监控和日志记录",
    "优化Suspense边界的粒度，平衡首屏速度和用户体验",
    "使用bootstrapScripts确保客户端JavaScript正确加载",
    "在开发环境中测试各种网络条件下的流式渲染效果",
    "配置适当的HTTP头部，确保流式传输正常工作"
  ],

  warnings: [
    "确保服务器和客户端的React版本一致，避免水合不匹配",
    "注意Suspense边界的错误边界设置，防止整个页面崩溃",
    "避免在流式传输过程中修改HTTP状态码和头部",
    "谨慎处理用户代理和SEO爬虫的兼容性问题",
    "在高并发场景下监控服务器内存和CPU使用情况"
  ]
};

export default basicInfo;