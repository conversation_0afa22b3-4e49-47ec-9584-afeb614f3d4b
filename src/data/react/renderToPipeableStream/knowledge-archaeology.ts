import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  introduction: `renderToPipeableStream的诞生标志着React从"批量渲染"向"流式渲染"的历史性转变。这不仅仅是一个API的添加，而是整个Web服务端渲染理念的基础性演进。通过深入挖掘其历史背景、技术演进和设计理念，我们可以更好地理解现代React SSR的精髓和发展方向。`,

  background: `renderToPipeableStream的出现源于现代Web应用对首屏性能的日益增长需求和Node.js流技术的成熟。在React早期，SSR主要依赖renderToString的同步渲染方式。随着应用复杂度的增长和用户体验要求的提升，传统的"等待完成再交付"模式已无法满足现代Web应用的性能需求。`,

  evolution: `renderToPipeableStream的演进历程体现了React对SSR性能优化的不断探索：从早期的同步字符串渲染，到流式HTML传输，再到与Suspense的深度集成，反映了React技术从简单到复杂，从同步到异步的发展轨迹。`,

  timeline: [
    {
      year: '2013',
      event: 'React SSR诞生',
      description: 'React引入renderToString，开启服务端渲染时代',
      significance: '为现代React SSR奠定了基础'
    },
    {
      year: '2016',
      event: 'Node.js流技术成熟',
      description: 'Node.js流API稳定，为流式渲染提供技术基础',
      significance: '确立了流式传输的技术标准'
    },
    {
      year: '2021',
      event: 'React 18 Suspense SSR',
      description: 'React 18引入Suspense的SSR支持，为流式渲染铺路',
      significance: '标志着React开始支持异步SSR'
    },
    {
      year: '2022',
      event: 'renderToPipeableStream发布',
      description: 'React 18正式发布renderToPipeableStream API',
      significance: '标志着React SSR进入流式渲染时代'
    }
  ],

  keyFigures: [
    {
      name: 'Sebastian Markbåge',
      role: 'React核心团队成员',
      contribution: '推动了React Suspense和并发特性的设计，为renderToPipeableStream的实现奠定了理论基础',
      significance: '他的并发渲染理念深刻影响了React SSR的发展方向'
    }
  ],

  concepts: [
    {
      term: 'Streaming SSR（流式服务端渲染）',
      definition: '一种服务端渲染技术，允许HTML内容分块传输，无需等待完整页面渲染完成',
      evolution: '从同步批量渲染发展为异步流式传输的现代SSR技术',
      modernRelevance: '现代React SSR的核心技术，renderToPipeableStream是其在Node.js环境的具体实现'
    }
  ],

  designPhilosophy: `renderToPipeableStream的设计哲学体现了现代React对用户体验的深度思考：渐进优于完整、流动优于静止、体验优于技术。它将复杂的流式渲染逻辑封装在简洁的API背后，让开发者能够专注于用户体验而不是技术实现。`,

  impact: `renderToPipeableStream对React生态系统产生了重要影响：推动了流式渲染的普及，提升了React SSR的性能表现，改变了开发者的SSR思维模式，为现代Web应用的性能优化提供了新的解决方案。`,

  modernRelevance: `在当今的Web开发环境中，renderToPipeableStream的重要性日益凸显：用户对首屏性能的要求越来越高，应用复杂度不断增长，移动设备成为主流，网络条件多样化。renderToPipeableStream作为React SSR工具箱中的重要工具，为开发者提供了强大而易用的流式渲染能力。`
};

export default knowledgeArchaeology;