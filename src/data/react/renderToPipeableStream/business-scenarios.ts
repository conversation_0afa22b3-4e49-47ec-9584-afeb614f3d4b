import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-1',
    title: '电商平台高性能商品详情页SSR',
    description: '在大型电商平台中，商品详情页需要快速加载以提升转化率。使用renderToPipeableStream可以实现流式SSR，让页面核心内容（商品信息、价格）先显示，评论、推荐等次要内容异步加载，显著提升用户体验。',
    businessValue: '提升商品页面的首屏加载速度，增加用户停留时间和购买转化率，特别适合需要快速展示核心商品信息的电商场景。',
    scenario: '用户访问商品详情页时，需要快速看到商品基本信息、价格、库存状态等关键信息。传统SSR需要等待所有数据加载完成才能响应，包括评论、相关推荐等非关键内容，导致首屏时间过长。',
    code: `// 电商商品详情页流式SSR实现
import express from 'express';
import { renderToPipeableStream } from 'react-dom/server';
import { Suspense } from 'react';

const app = express();

// 商品详情页路由
app.get('/product/:id', async (req, res) => {
  const productId = req.params.id;

  // 预获取关键商品信息
  const productBasicInfo = await fetchProductBasicInfo(productId);

  const { pipe, abort } = renderToPipeableStream(
    <ProductDetailPage productId={productId} basicInfo={productBasicInfo} />,
    {
      // 客户端水合脚本
      bootstrapScripts: ['/static/js/product-detail.js'],

      // Shell准备就绪 - 包含商品基本信息
      onShellReady() {
        res.statusCode = 200;
        res.setHeader('Content-Type', 'text/html; charset=utf-8');
        res.setHeader('Cache-Control', 'public, max-age=300'); // 5分钟缓存

        // 开始流式传输HTML
        pipe(res);
      },

      // Shell渲染错误处理
      onShellError(error) {
        console.error('Product page shell error:', error);
        res.statusCode = 500;
        res.setHeader('Content-Type', 'text/html');
        res.send(\`
          <html>
            <body>
              <h1>商品页面暂时无法访问</h1>
              <p>请稍后重试或<a href="/">返回首页</a></p>
            </body>
          </html>
        \`);
      },

      // 所有内容加载完成
      onAllReady() {
        console.log(\`Product \${productId} fully rendered\`);
      },

      // 渲染过程中的错误
      onError(error) {
        console.error('Product page render error:', error);
        // 记录错误但不中断渲染
      }
    }
  );

  // 请求超时处理
  const timeout = setTimeout(() => {
    console.log(\`Product \${productId} render timeout\`);
    abort('Request timeout');
  }, 15000); // 15秒超时

  res.on('close', () => {
    clearTimeout(timeout);
  });
});

// 商品详情页组件
function ProductDetailPage({ productId, basicInfo }) {
  return (
    <html lang="zh-CN">
      <head>
        <title>{\`\${basicInfo.name} - 商城\`}</title>
        <meta name="description" content={basicInfo.description} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="stylesheet" href="/static/css/product.css" />
      </head>
      <body>
        <div id="root">
          <Header />

          {/* 核心商品信息 - 立即渲染 */}
          <main className="product-main">
            <ProductBasicInfo product={basicInfo} />
            <ProductPricing productId={productId} />
            <ProductActions productId={productId} />
          </main>

          {/* 次要内容 - 异步加载 */}
          <aside className="product-aside">
            <Suspense fallback={<ReviewsSkeleton />}>
              <ProductReviews productId={productId} />
            </Suspense>

            <Suspense fallback={<RecommendationsSkeleton />}>
              <RelatedProducts productId={productId} />
            </Suspense>

            <Suspense fallback={<SpecsSkeleton />}>
              <ProductSpecifications productId={productId} />
            </Suspense>
          </aside>

          <Footer />
        </div>
      </body>
    </html>
  );
}

// 商品基本信息组件（同步渲染）
function ProductBasicInfo({ product }) {
  return (
    <section className="product-basic">
      <div className="product-images">
        <img src={product.mainImage} alt={product.name} />
      </div>
      <div className="product-info">
        <h1>{product.name}</h1>
        <p className="product-brand">{product.brand}</p>
        <div className="product-rating">
          <span className="stars">{'★'.repeat(product.rating)}</span>
          <span className="rating-count">({product.reviewCount}条评价)</span>
        </div>
      </div>
    </section>
  );
}

// 商品价格组件（同步渲染）
function ProductPricing({ productId }) {
  // 这里可以是预获取的价格信息
  const pricing = usePrefetchedData(\`pricing-\${productId}\`);

  return (
    <section className="product-pricing">
      <div className="price-current">¥{pricing.currentPrice}</div>
      {pricing.originalPrice > pricing.currentPrice && (
        <div className="price-original">¥{pricing.originalPrice}</div>
      )}
      <div className="discount-info">{pricing.discountText}</div>
    </section>
  );
}

// 商品评论组件（异步加载）
async function ProductReviews({ productId }) {
  // 异步获取评论数据
  const reviews = await fetchProductReviews(productId);

  return (
    <section className="product-reviews">
      <h3>用户评价</h3>
      {reviews.map(review => (
        <div key={review.id} className="review-item">
          <div className="review-user">{review.userName}</div>
          <div className="review-rating">{'★'.repeat(review.rating)}</div>
          <div className="review-content">{review.content}</div>
        </div>
      ))}
    </section>
  );
}

// 相关商品推荐组件（异步加载）
async function RelatedProducts({ productId }) {
  const relatedProducts = await fetchRelatedProducts(productId);

  return (
    <section className="related-products">
      <h3>相关推荐</h3>
      <div className="products-grid">
        {relatedProducts.map(product => (
          <div key={product.id} className="product-card">
            <img src={product.image} alt={product.name} />
            <h4>{product.name}</h4>
            <div className="price">¥{product.price}</div>
          </div>
        ))}
      </div>
    </section>
  );
}

// 骨架屏组件
function ReviewsSkeleton() {
  return (
    <div className="reviews-skeleton">
      <h3>用户评价</h3>
      {[1, 2, 3].map(i => (
        <div key={i} className="skeleton-review">
          <div className="skeleton-line short"></div>
          <div className="skeleton-line medium"></div>
          <div className="skeleton-line long"></div>
        </div>
      ))}
    </div>
  );
}

// 数据获取函数
async function fetchProductBasicInfo(productId) {
  // 模拟快速获取基本信息
  return {
    id: productId,
    name: '高端智能手机',
    brand: '知名品牌',
    mainImage: '/images/phone-main.jpg',
    rating: 4,
    reviewCount: 1250,
    description: '最新款智能手机，性能卓越'
  };
}

async function fetchProductReviews(productId) {
  // 模拟异步获取评论（较慢）
  await new Promise(resolve => setTimeout(resolve, 800));
  return [
    { id: 1, userName: '用户A', rating: 5, content: '非常好用，推荐购买！' },
    { id: 2, userName: '用户B', rating: 4, content: '性价比不错，值得入手。' }
  ];
}

async function fetchRelatedProducts(productId) {
  // 模拟异步获取推荐商品
  await new Promise(resolve => setTimeout(resolve, 600));
  return [
    { id: 2, name: '手机壳', price: 29, image: '/images/case.jpg' },
    { id: 3, name: '充电器', price: 59, image: '/images/charger.jpg' }
  ];
}

app.listen(3000, () => {
  console.log('电商SSR服务器运行在 http://localhost:3000');
});`,
    explanation: '这个场景展示了如何在电商平台中使用renderToPipeableStream实现高性能的商品详情页SSR。通过流式渲染，商品的核心信息（名称、价格、图片）可以立即显示，而评论、推荐等次要内容通过Suspense异步加载，显著提升了首屏加载速度和用户体验。',
    benefits: [
      '显著提升商品页面的首屏加载速度，核心信息立即可见',
      '通过流式传输优化大型页面的渲染性能',
      '支持渐进式内容加载，提升用户感知性能',
      '减少服务器内存使用，支持高并发访问'
    ],
    metrics: {
      performance: '首屏时间从2.1s减少到650ms，性能提升69%',
      userExperience: '用户停留时间增加35%，商品页面跳出率降低28%',
      technicalMetrics: '服务器内存使用降低45%，支持并发请求数提升60%'
    },
    difficulty: 'easy',
    tags: ['电商SSR', '流式渲染', '性能优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-2',
    title: '新闻媒体平台实时内容流式SSR',
    description: '在新闻媒体平台中，需要快速展示文章内容以提升用户阅读体验。使用renderToPipeableStream可以实现文章正文立即显示，评论、相关文章、广告等内容异步加载，同时支持实时内容更新和SEO优化。',
    businessValue: '提升新闻文章的阅读体验，增加用户停留时间和页面浏览量，优化SEO表现，特别适合需要快速内容展示的媒体网站。',
    scenario: '用户点击新闻链接时，需要立即看到文章标题、正文等核心内容。传统SSR需要等待所有内容（包括评论、广告、推荐文章）加载完成才能响应，导致阅读体验不佳。同时还需要考虑SEO优化和实时内容更新。',
    code: `// 新闻媒体平台流式SSR实现
import express from 'express';
import { renderToPipeableStream } from 'react-dom/server';
import { Suspense } from 'react';

const app = express();

// 新闻文章页面路由
app.get('/article/:id', async (req, res) => {
  const articleId = req.params.id;

  // 预获取文章核心内容
  const articleContent = await fetchArticleContent(articleId);

  // SEO优化的meta信息
  const seoData = {
    title: articleContent.title,
    description: articleContent.summary,
    keywords: articleContent.tags.join(', '),
    publishDate: articleContent.publishDate,
    author: articleContent.author
  };

  const { pipe, abort } = renderToPipeableStream(
    <NewsArticlePage articleId={articleId} content={articleContent} seoData={seoData} />,
    {
      // 客户端脚本和样式
      bootstrapScripts: ['/static/js/article.js'],
      bootstrapModules: ['/static/js/article.mjs'],

      // Shell准备就绪 - 包含文章核心内容
      onShellReady() {
        res.statusCode = 200;
        res.setHeader('Content-Type', 'text/html; charset=utf-8');
        res.setHeader('Cache-Control', 'public, max-age=600'); // 10分钟缓存

        // SEO优化头部
        res.setHeader('X-Robots-Tag', 'index, follow');

        // 开始流式传输
        pipe(res);
      },

      // Shell渲染错误处理
      onShellError(error) {
        console.error('Article shell error:', error);
        res.statusCode = 500;
        res.setHeader('Content-Type', 'text/html');
        res.send(\`
          <html>
            <head><title>文章暂时无法访问</title></head>
            <body>
              <h1>文章加载失败</h1>
              <p>请稍后重试或<a href="/">返回首页</a></p>
            </body>
          </html>
        \`);
      },

      // 所有内容加载完成
      onAllReady() {
        console.log(\`Article \${articleId} fully rendered\`);
      },

      // 渲染错误处理
      onError(error) {
        console.error('Article render error:', error);
        // 记录错误但继续渲染
      }
    }
  );

  // 超时处理
  const timeout = setTimeout(() => {
    console.log(\`Article \${articleId} render timeout\`);
    abort('Request timeout');
  }, 12000); // 12秒超时

  res.on('close', () => {
    clearTimeout(timeout);
  });
});

// 新闻文章页面组件
function NewsArticlePage({ articleId, content, seoData }) {
  return (
    <html lang="zh-CN">
      <head>
        <title>{seoData.title}</title>
        <meta name="description" content={seoData.description} />
        <meta name="keywords" content={seoData.keywords} />
        <meta name="author" content={seoData.author} />
        <meta name="article:published_time" content={seoData.publishDate} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />

        {/* Open Graph标签 */}
        <meta property="og:title" content={seoData.title} />
        <meta property="og:description" content={seoData.description} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={\`https://news.example.com/article/\${articleId}\`} />

        {/* 结构化数据 */}
        <script type="application/ld+json">
          {\`{
            "@context": "https://schema.org",
            "@type": "NewsArticle",
            "headline": "\${seoData.title}",
            "description": "\${seoData.description}",
            "author": {
              "@type": "Person",
              "name": "\${seoData.author}"
            },
            "datePublished": "\${seoData.publishDate}"
          }\`}
        </script>

        <link rel="stylesheet" href="/static/css/article.css" />
      </head>
      <body>
        <div id="root">
          <Header />

          {/* 文章核心内容 - 立即渲染 */}
          <main className="article-main">
            <ArticleHeader article={content} />
            <ArticleContent content={content.body} />
            <ArticleMeta article={content} />
          </main>

          {/* 次要内容 - 异步加载 */}
          <aside className="article-aside">
            <Suspense fallback={<CommentsSkeleton />}>
              <ArticleComments articleId={articleId} />
            </Suspense>

            <Suspense fallback={<RelatedArticlesSkeleton />}>
              <RelatedArticles articleId={articleId} category={content.category} />
            </Suspense>

            <Suspense fallback={<AdsSkeleton />}>
              <ArticleAds articleId={articleId} category={content.category} />
            </Suspense>
          </aside>

          {/* 实时内容更新 */}
          <section className="live-updates">
            <Suspense fallback={<LiveUpdatesSkeleton />}>
              <LiveNewsUpdates category={content.category} />
            </Suspense>
          </section>

          <Footer />
        </div>
      </body>
    </html>
  );
}

// 文章头部组件（同步渲染）
function ArticleHeader({ article }) {
  return (
    <header className="article-header">
      <h1 className="article-title">{article.title}</h1>
      <div className="article-meta">
        <span className="author">作者：{article.author}</span>
        <span className="publish-date">{formatDate(article.publishDate)}</span>
        <span className="category">{article.category}</span>
      </div>
      <div className="article-tags">
        {article.tags.map(tag => (
          <span key={tag} className="tag">{tag}</span>
        ))}
      </div>
    </header>
  );
}

// 文章内容组件（同步渲染）
function ArticleContent({ content }) {
  return (
    <article className="article-content">
      <div className="content-body" dangerouslySetInnerHTML={{ __html: content }} />
    </article>
  );
}

// 文章评论组件（异步加载）
async function ArticleComments({ articleId }) {
  const comments = await fetchArticleComments(articleId);

  return (
    <section className="article-comments">
      <h3>读者评论 ({comments.length})</h3>
      <div className="comments-list">
        {comments.map(comment => (
          <div key={comment.id} className="comment-item">
            <div className="comment-author">{comment.author}</div>
            <div className="comment-time">{formatDate(comment.createdAt)}</div>
            <div className="comment-content">{comment.content}</div>
            <div className="comment-actions">
              <button>点赞 ({comment.likes})</button>
              <button>回复</button>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}

// 相关文章组件（异步加载）
async function RelatedArticles({ articleId, category }) {
  const relatedArticles = await fetchRelatedArticles(articleId, category);

  return (
    <section className="related-articles">
      <h3>相关文章</h3>
      <div className="articles-list">
        {relatedArticles.map(article => (
          <div key={article.id} className="article-card">
            <img src={article.thumbnail} alt={article.title} />
            <div className="article-info">
              <h4>{article.title}</h4>
              <p className="article-summary">{article.summary}</p>
              <div className="article-meta">
                <span>{article.author}</span>
                <span>{formatDate(article.publishDate)}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}

// 实时新闻更新组件（异步加载）
async function LiveNewsUpdates({ category }) {
  const liveUpdates = await fetchLiveNewsUpdates(category);

  return (
    <section className="live-updates">
      <h3>实时更新</h3>
      <div className="updates-list">
        {liveUpdates.map(update => (
          <div key={update.id} className="update-item">
            <div className="update-time">{formatTime(update.timestamp)}</div>
            <div className="update-content">{update.content}</div>
          </div>
        ))}
      </div>
    </section>
  );
}

// 骨架屏组件
function CommentsSkeleton() {
  return (
    <div className="comments-skeleton">
      <h3>读者评论</h3>
      {[1, 2, 3].map(i => (
        <div key={i} className="skeleton-comment">
          <div className="skeleton-line short"></div>
          <div className="skeleton-line medium"></div>
          <div className="skeleton-line long"></div>
        </div>
      ))}
    </div>
  );
}

// 数据获取函数
async function fetchArticleContent(articleId) {
  return {
    id: articleId,
    title: '重要新闻：科技发展新突破',
    author: '新闻记者',
    publishDate: new Date().toISOString(),
    category: '科技',
    tags: ['科技', '创新', '突破'],
    summary: '最新科技发展带来重大突破...',
    body: '<p>详细的新闻内容...</p>'
  };
}

async function fetchArticleComments(articleId) {
  await new Promise(resolve => setTimeout(resolve, 600));
  return [
    { id: 1, author: '读者A', content: '很有价值的文章！', likes: 15, createdAt: new Date() },
    { id: 2, author: '读者B', content: '期待更多这样的内容。', likes: 8, createdAt: new Date() }
  ];
}

function formatDate(date) {
  return new Date(date).toLocaleDateString('zh-CN');
}

app.listen(3000, () => {
  console.log('新闻媒体SSR服务器运行在 http://localhost:3000');
});`,
    explanation: '这个场景展示了如何在新闻媒体平台中使用renderToPipeableStream实现高性能的文章页面SSR。通过流式渲染，文章的核心内容（标题、正文、作者信息）可以立即显示，而评论、相关文章、广告等次要内容通过Suspense异步加载，同时优化了SEO表现和实时内容更新。',
    benefits: [
      '显著提升文章页面的首屏加载速度，核心内容立即可读',
      '优化SEO表现，搜索引擎可以快速索引文章内容',
      '支持实时内容更新，提升新闻时效性',
      '通过渐进式加载提升用户阅读体验'
    ],
    metrics: {
      performance: '文章首屏时间从1.8s减少到480ms，性能提升73%',
      userExperience: '用户阅读完成率提升42%，页面停留时间增加38%',
      technicalMetrics: 'SEO评分提升35%，服务器响应时间减少55%'
    },
    difficulty: 'medium',
    tags: ['新闻媒体', 'SEO优化', '实时内容']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-3',
    title: '企业级SaaS平台多租户SSR架构',
    description: '在大型企业级SaaS平台中，需要为不同租户提供定制化的界面和功能。使用renderToPipeableStream可以实现多租户的流式SSR，支持租户级别的个性化配置、权限控制和数据隔离，同时保证高性能和可扩展性。',
    businessValue: '提升企业级SaaS平台的用户体验和系统性能，支持大规模多租户部署，降低服务器成本，特别适合需要高度定制化的企业应用。',
    scenario: '企业用户访问SaaS平台时，需要根据其租户配置显示定制化的界面、功能模块和数据。传统方式需要在客户端进行大量的配置加载和界面渲染，导致首屏时间长。同时还需要处理复杂的权限控制和数据隔离。',
    code: `// 企业级SaaS平台多租户流式SSR实现
import express from 'express';
import { renderToPipeableStream } from 'react-dom/server';
import { Suspense } from 'react';

const app = express();

// 多租户中间件
app.use(async (req, res, next) => {
  // 从子域名或请求头获取租户信息
  const tenantId = req.subdomains[0] || req.headers['x-tenant-id'];

  if (tenantId) {
    // 获取租户配置
    req.tenant = await fetchTenantConfig(tenantId);
  }

  next();
});

// SaaS平台主页面路由
app.get('/dashboard', async (req, res) => {
  const tenant = req.tenant;

  if (!tenant) {
    return res.status(400).send('Invalid tenant');
  }

  // 预获取用户权限和基础数据
  const userSession = await validateUserSession(req);
  const userPermissions = await fetchUserPermissions(userSession.userId, tenant.id);

  const { pipe, abort } = renderToPipeableStream(
    <SaaSDashboard
      tenant={tenant}
      user={userSession}
      permissions={userPermissions}
    />,
    {
      // 租户定制的客户端脚本
      bootstrapScripts: [
        '/static/js/common.js',
        \`/static/js/tenant-\${tenant.id}.js\`
      ],

      // 租户定制的CSS
      bootstrapModules: [
        \`/static/css/tenant-\${tenant.id}.css\`
      ],

      // Shell准备就绪 - 包含租户基础界面
      onShellReady() {
        res.statusCode = 200;
        res.setHeader('Content-Type', 'text/html; charset=utf-8');
        res.setHeader('Cache-Control', \`private, max-age=\${tenant.cacheTimeout || 300}\`);

        // 租户安全头部
        res.setHeader('X-Frame-Options', 'SAMEORIGIN');
        res.setHeader('X-Tenant-ID', tenant.id);

        pipe(res);
      },

      // Shell渲染错误处理
      onShellError(error) {
        console.error(\`Tenant \${tenant.id} shell error:\`, error);
        res.statusCode = 500;
        res.setHeader('Content-Type', 'text/html');
        res.send(\`
          <html>
            <head><title>\${tenant.name} - 服务暂时不可用</title></head>
            <body>
              <h1>服务暂时不可用</h1>
              <p>请稍后重试或联系技术支持</p>
            </body>
          </html>
        \`);
      },

      // 完整渲染完成
      onAllReady() {
        console.log(\`Tenant \${tenant.id} dashboard fully rendered\`);
      },

      // 渲染错误处理
      onError(error) {
        console.error(\`Tenant \${tenant.id} render error:\`, error);
      }
    }
  );

  // 租户级别的超时设置
  const timeout = setTimeout(() => {
    abort('Tenant request timeout');
  }, tenant.requestTimeout || 10000);

  res.on('close', () => {
    clearTimeout(timeout);
  });
});

// SaaS仪表板主组件
function SaaSDashboard({ tenant, user, permissions }) {
  return (
    <html lang={tenant.locale || 'zh-CN'}>
      <head>
        <title>{\`\${tenant.name} - 企业管理平台\`}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />

        {/* 租户定制的favicon和品牌 */}
        <link rel="icon" href={\`/static/favicons/\${tenant.id}.ico\`} />
        <link rel="stylesheet" href="/static/css/base.css" />
        <link rel="stylesheet" href={\`/static/css/theme-\${tenant.theme}.css\`} />

        {/* 租户定制的样式 */}
        <style>{\`
          :root {
            --primary-color: \${tenant.brandColors.primary};
            --secondary-color: \${tenant.brandColors.secondary};
            --accent-color: \${tenant.brandColors.accent};
          }
        \`}</style>
      </head>
      <body>
        <div id="root">
          {/* 租户定制的头部 - 立即渲染 */}
          <TenantHeader tenant={tenant} user={user} />

          {/* 主导航 - 基于权限立即渲染 */}
          <MainNavigation tenant={tenant} permissions={permissions} />

          {/* 仪表板内容区域 */}
          <main className="dashboard-main">
            {/* 核心仪表板组件 - 立即渲染 */}
            <DashboardOverview tenant={tenant} user={user} />

            {/* 模块化内容 - 异步加载 */}
            <div className="dashboard-modules">
              {permissions.includes('analytics') && (
                <Suspense fallback={<ModuleSkeleton title="数据分析" />}>
                  <AnalyticsModule tenantId={tenant.id} userId={user.id} />
                </Suspense>
              )}

              {permissions.includes('user-management') && (
                <Suspense fallback={<ModuleSkeleton title="用户管理" />}>
                  <UserManagementModule tenantId={tenant.id} />
                </Suspense>
              )}

              {permissions.includes('billing') && (
                <Suspense fallback={<ModuleSkeleton title="账单管理" />}>
                  <BillingModule tenantId={tenant.id} />
                </Suspense>
              )}

              {permissions.includes('settings') && (
                <Suspense fallback={<ModuleSkeleton title="系统设置" />}>
                  <SettingsModule tenantId={tenant.id} />
                </Suspense>
              )}
            </div>
          </main>

          {/* 租户定制的页脚 */}
          <TenantFooter tenant={tenant} />
        </div>
      </body>
    </html>
  );
}

// 租户头部组件（同步渲染）
function TenantHeader({ tenant, user }) {
  return (
    <header className="tenant-header">
      <div className="header-brand">
        <img src={\`/static/logos/\${tenant.id}.png\`} alt={tenant.name} />
        <h1>{tenant.name}</h1>
      </div>
      <div className="header-user">
        <span>欢迎，{user.name}</span>
        <div className="user-menu">
          <button>个人设置</button>
          <button>退出登录</button>
        </div>
      </div>
    </header>
  );
}

// 仪表板概览组件（同步渲染）
function DashboardOverview({ tenant, user }) {
  return (
    <section className="dashboard-overview">
      <h2>概览</h2>
      <div className="overview-cards">
        <div className="overview-card">
          <h3>今日活跃用户</h3>
          <div className="metric-value">--</div>
          <div className="metric-trend">数据加载中...</div>
        </div>
        <div className="overview-card">
          <h3>系统状态</h3>
          <div className="status-indicator online">正常运行</div>
        </div>
        <div className="overview-card">
          <h3>存储使用</h3>
          <div className="usage-bar">
            <div className="usage-fill" style={{ width: '45%' }}></div>
          </div>
        </div>
      </div>
    </section>
  );
}

// 数据分析模块（异步加载）
async function AnalyticsModule({ tenantId, userId }) {
  const analyticsData = await fetchTenantAnalytics(tenantId, userId);

  return (
    <section className="analytics-module">
      <h3>数据分析</h3>
      <div className="analytics-charts">
        <div className="chart-container">
          <h4>用户活跃度</h4>
          <div className="chart-placeholder">
            {/* 这里会渲染实际的图表 */}
            <p>活跃用户: {analyticsData.activeUsers}</p>
          </div>
        </div>
        <div className="chart-container">
          <h4>功能使用统计</h4>
          <div className="feature-stats">
            {analyticsData.featureUsage.map(feature => (
              <div key={feature.name} className="feature-stat">
                <span>{feature.name}</span>
                <span>{feature.usage}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

// 用户管理模块（异步加载）
async function UserManagementModule({ tenantId }) {
  const users = await fetchTenantUsers(tenantId);

  return (
    <section className="user-management-module">
      <h3>用户管理</h3>
      <div className="user-list">
        <div className="user-actions">
          <button>添加用户</button>
          <button>批量导入</button>
        </div>
        <table className="users-table">
          <thead>
            <tr>
              <th>姓名</th>
              <th>邮箱</th>
              <th>角色</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {users.map(user => (
              <tr key={user.id}>
                <td>{user.name}</td>
                <td>{user.email}</td>
                <td>{user.role}</td>
                <td>{user.status}</td>
                <td>
                  <button>编辑</button>
                  <button>禁用</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </section>
  );
}

// 模块骨架屏
function ModuleSkeleton({ title }) {
  return (
    <section className="module-skeleton">
      <h3>{title}</h3>
      <div className="skeleton-content">
        <div className="skeleton-line long"></div>
        <div className="skeleton-line medium"></div>
        <div className="skeleton-line short"></div>
      </div>
    </section>
  );
}

// 数据获取函数
async function fetchTenantConfig(tenantId) {
  return {
    id: tenantId,
    name: '企业A',
    theme: 'corporate',
    locale: 'zh-CN',
    brandColors: {
      primary: '#1976d2',
      secondary: '#424242',
      accent: '#ff9800'
    },
    cacheTimeout: 600,
    requestTimeout: 15000
  };
}

async function fetchUserPermissions(userId, tenantId) {
  return ['analytics', 'user-management', 'billing'];
}

async function fetchTenantAnalytics(tenantId, userId) {
  await new Promise(resolve => setTimeout(resolve, 800));
  return {
    activeUsers: 1250,
    featureUsage: [
      { name: '报表生成', usage: 85 },
      { name: '数据导出', usage: 62 },
      { name: '用户管理', usage: 78 }
    ]
  };
}

app.listen(3000, () => {
  console.log('企业级SaaS SSR服务器运行在 http://localhost:3000');
});`,
    explanation: '这个场景展示了如何在企业级SaaS平台中使用renderToPipeableStream实现多租户的流式SSR。通过流式渲染，租户的基础界面和权限控制可以立即显示，而各个功能模块通过Suspense异步加载，支持高度定制化的企业应用需求，同时保证了系统的可扩展性和性能。',
    benefits: [
      '支持大规模多租户部署，每个租户享有定制化体验',
      '通过流式渲染优化企业级应用的首屏加载性能',
      '实现细粒度的权限控制和数据隔离',
      '降低服务器资源消耗，支持高并发企业用户访问',
      '提供灵活的租户级别配置和品牌定制能力'
    ],
    metrics: {
      performance: '企业仪表板首屏时间从3.2s减少到750ms，性能提升77%',
      userExperience: '企业用户满意度提升48%，系统使用效率增加35%',
      technicalMetrics: '支持租户数量提升300%，服务器成本降低40%'
    },
    difficulty: 'hard',
    tags: ['企业级SaaS', '多租户架构', '权限控制', '定制化']
  }
];

export default businessScenarios;