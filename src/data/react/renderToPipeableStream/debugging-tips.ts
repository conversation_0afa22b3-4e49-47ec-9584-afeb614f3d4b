import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'renderToPipeableStream虽然功能强大，但在实际使用中开发者经常遇到一些典型问题。本节提供完整的问题诊断和解决方案，帮助快速定位和修复流式SSR相关的渲染问题。',
        sections: [
          {
            title: 'Shell渲染问题',
            description: '最常见的问题是Shell渲染失败或渲染时间过长，导致首屏显示延迟。这通常涉及Suspense边界设计、异步组件处理、错误边界等多个方面',
            items: [
              {
                title: 'onShellReady不触发',
                description: 'Shell渲染过程中遇到错误或无限等待，导致onShellReady回调永远不执行',
                solution: '1. 检查Shell中是否包含异步组件；2. 添加错误边界捕获渲染错误；3. 设置合理的超时机制；4. 使用onShellError处理Shell错误',
                prevention: '在Shell设计时避免包含异步操作，将所有异步内容放在Suspense边界内',
                code: `// 问题：Shell中包含异步组件
function ProblematicApp() {
  return (
    <html>
      <body>
        <Header />
        <AsyncUserProfile /> {/* 这会阻塞Shell */}
        <Footer />
      </body>
    </html>
  );
}

// 解决方案：将异步组件放入Suspense
function FixedApp() {
  return (
    <html>
      <body>
        <Header />
        <Suspense fallback={<UserProfileSkeleton />}>
          <AsyncUserProfile />
        </Suspense>
        <Footer />
      </body>
    </html>
  );
}

// 添加错误边界和超时处理
const { pipe, abort } = renderToPipeableStream(<FixedApp />, {
  onShellReady() {
    pipe(response);
  },
  onShellError(error) {
    console.error('Shell error:', error);
    response.status(500).send('<h1>页面加载失败</h1>');
  }
});

// 超时保护
setTimeout(() => abort('Shell timeout'), 5000);`
              },
              {
                title: '流式传输中断',
                description: '在流式传输过程中连接意外中断，导致页面显示不完整',
                solution: '1. 监听response的close和error事件；2. 实现优雅的错误处理；3. 添加客户端重试机制；4. 使用适当的缓存策略',
                prevention: '实现完善的错误处理和监控机制，及时发现和处理传输问题',
                code: `// 流式传输错误处理
function createRobustSSRHandler() {
  return (req, res) => {
    const { pipe, abort } = renderToPipeableStream(<App />, {
      onShellReady() {
        pipe(res);
      },
      onError(error) {
        console.error('Stream error:', error);
      }
    });

    // 监听连接事件
    res.on('close', () => {
      console.log('Client disconnected');
      abort('Client disconnected');
    });

    res.on('error', (error) => {
      console.error('Response error:', error);
      abort('Response error');
    });

    // 超时处理
    const timeout = setTimeout(() => {
      abort('Request timeout');
    }, 10000);

    res.on('finish', () => {
      clearTimeout(timeout);
    });
  };
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '有效的调试工具可以帮助开发者监控renderToPipeableStream的执行过程，诊断性能问题，优化流式渲染策略。掌握这些工具是高效使用流式SSR的关键。',
        sections: [
          {
            title: '流式渲染监控',
            description: '监控renderToPipeableStream的执行过程，包括Shell渲染时间、流式传输效率和错误情况',
            items: [
              {
                title: 'SSR性能监控工具',
                description: '跟踪流式SSR的各个阶段，记录性能指标和错误信息',
                solution: '1. 包装renderToPipeableStream添加监控；2. 记录各阶段时间戳；3. 监控内存和CPU使用；4. 生成性能报告',
                prevention: '定期监控SSR性能，及时发现和解决性能瓶颈',
                code: `// SSR性能监控工具
class SSRPerformanceMonitor {
  constructor() {
    this.metrics = new Map();
  }

  wrapRenderToPipeableStream(children, options = {}) {
    const requestId = generateRequestId();
    const startTime = Date.now();

    const enhancedOptions = {
      ...options,

      onShellReady: () => {
        const shellTime = Date.now() - startTime;
        this.recordMetric(requestId, 'shellTime', shellTime);

        console.log(\`Shell ready in \${shellTime}ms\`);

        if (options.onShellReady) {
          options.onShellReady();
        }
      },

      onAllReady: () => {
        const totalTime = Date.now() - startTime;
        this.recordMetric(requestId, 'totalTime', totalTime);

        console.log(\`Full render in \${totalTime}ms\`);

        if (options.onAllReady) {
          options.onAllReady();
        }
      },

      onError: (error) => {
        this.recordError(requestId, error);

        if (options.onError) {
          options.onError(error);
        }
      }
    };

    return renderToPipeableStream(children, enhancedOptions);
  }

  recordMetric(requestId, metric, value) {
    if (!this.metrics.has(requestId)) {
      this.metrics.set(requestId, {});
    }

    this.metrics.get(requestId)[metric] = value;
  }

  getPerformanceReport() {
    const allMetrics = Array.from(this.metrics.values());

    return {
      totalRequests: allMetrics.length,
      averageShellTime: this.calculateAverage(allMetrics, 'shellTime'),
      averageTotalTime: this.calculateAverage(allMetrics, 'totalTime'),
      errorRate: this.calculateErrorRate(allMetrics)
    };
  }
}

// 使用示例
const monitor = new SSRPerformanceMonitor();

app.get('/', (req, res) => {
  const { pipe } = monitor.wrapRenderToPipeableStream(<App />, {
    onShellReady() {
      res.statusCode = 200;
      res.setHeader('Content-Type', 'text/html');
      pipe(res);
    }
  });
});`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;