import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const renderToPipeableStreamData: ApiItem = {
  id: 'renderToPipeableStream',
  title: 'renderToPipeableStream',
  description: 'React中用于Node.js流式服务端渲染的API，专门为Node.js环境设计，支持Suspense、流式传输和渐进式HTML生成，实现高性能的服务端渲染解决方案。',
  category: 'ReactDOM Server API',
  difficulty: 'hard',

  syntax: `renderToPipeableStream(children: ReactNode, options?: RenderToPipeableStreamOptions)`,
  example: `const { pipe, abort } = renderToPipeableStream(<App />, {
  onShellReady() { pipe(response); },
  onError(error) { console.error(error); }
});`,
  notes: '专门用于Node.js环境的流式SSR，支持Suspense和渐进式HTML传输，是现代React服务端渲染的核心工具。',

  version: 'React 18.0+',
  tags: ["ReactDOM", "Server", "SSR", "Streaming", "Node.js"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default renderToPipeableStreamData;