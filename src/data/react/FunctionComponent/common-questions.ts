import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '什么时候应该使用React.FC，什么时候使用普通函数组件？两者有什么具体区别？',
    answer: `这是React TypeScript开发中的常见选择问题。选择依据主要考虑以下几个方面：

**使用React.FC的场景：**
1. **需要children属性**：组件设计上需要包装其他组件或内容
2. **团队规范要求**：团队统一使用React.FC作为函数组件标准
3. **静态属性需求**：需要设置displayName、defaultProps等静态属性
4. **类型一致性**：希望所有函数组件有统一的类型约束

**使用普通函数组件的场景：**
1. **不需要children**：组件是叶子节点，不包装其他内容
2. **更精确的类型控制**：希望完全控制Props接口，避免自动children
3. **性能考虑**：减少不必要的类型开销（虽然影响很小）
4. **避免混淆**：防止开发者误以为组件支持children但实际不使用

**主要区别：**
- **children属性**：React.FC自动添加children?: ReactNode，普通函数组件需要手动定义
- **静态属性类型**：React.FC提供更好的静态属性类型支持
- **泛型支持**：两者都支持泛型，但React.FC的泛型约束更严格
- **推断能力**：React.FC在某些复杂场景下类型推断更准确`,
    code: `// React.FC使用示例
interface CardProps {
  title: string;
  subtitle?: string;
}

// 使用React.FC - 适合需要children的组件
const Card: React.FC<CardProps> = ({ title, subtitle, children }) => {
  return (
    <div className="card">
      <div className="card-header">
        <h3>{title}</h3>
        {subtitle && <p>{subtitle}</p>}
      </div>
      <div className="card-content">
        {children} {/* 自动可用，无需在Props中定义 */}
      </div>
    </div>
  );
};

// 使用示例
const App = () => (
  <Card title="用户信息" subtitle="基本资料">
    <p>姓名: 张三</p>
    <p>邮箱: <EMAIL></p>
  </Card>
);

// 普通函数组件 - 适合不需要children的组件
interface ButtonProps {
  label: string;
  variant: 'primary' | 'secondary';
  onClick: () => void;
  disabled?: boolean;
}

const Button = ({ label, variant, onClick, disabled = false }: ButtonProps) => {
  return (
    <button 
      className={'btn btn-' + variant}
      onClick={onClick}
      disabled={disabled}
    >
      {label}
    </button>
  );
};

// 对比：如果Button使用React.FC
const ButtonWithFC: React.FC<ButtonProps> = ({ label, variant, onClick, disabled, children }) => {
  // children存在但不会被使用，可能导致混淆
  return (
    <button className={'btn btn-' + variant} onClick={onClick} disabled={disabled}>
      {label}
      {/* children被忽略，但类型上存在 */}
    </button>
  );
};

// 实际应用中的选择策略
// 1. 容器组件使用React.FC
const Layout: React.FC<{ sidebar?: boolean }> = ({ sidebar = true, children }) => (
  <div className="layout">
    {sidebar && <aside>侧边栏</aside>}
    <main>{children}</main>
  </div>
);

// 2. 叶子组件使用普通函数
interface AvatarProps {
  src: string;
  alt: string;
  size: 'small' | 'medium' | 'large';
}

const Avatar = ({ src, alt, size }: AvatarProps) => (
  <img 
    src={src} 
    alt={alt} 
    className={'avatar avatar-' + size}
  />
);

// 3. 需要泛型的组件
interface ListProps<T> {
  items: T[];
  renderItem: (item: T) => ReactNode;
  onItemClick?: (item: T) => void;
}

// 泛型函数组件（两种方式都可以）
function List<T>({ items, renderItem, onItemClick }: ListProps<T>) {
  return (
    <ul className="list">
      {items.map((item, index) => (
        <li 
          key={index} 
          onClick={() => onItemClick?.(item)}
          className="list-item"
        >
          {renderItem(item)}
        </li>
      ))}
    </ul>
  );
}

// 或者使用React.FC（如果需要children）
function ListWithFC<T>(): React.FC<ListProps<T> & { children?: ReactNode }> {
  return ({ items, renderItem, onItemClick, children }) => (
    <div className="list-container">
      <ul className="list">
        {items.map((item, index) => (
          <li key={index} onClick={() => onItemClick?.(item)}>
            {renderItem(item)}
          </li>
        ))}
      </ul>
      {children}
    </div>
  );
}`,
    tags: ['React.FC', '函数组件'],
    relatedQuestions: ['children属性如何正确处理？', '泛型函数组件如何定义类型？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: 'React.FC的children属性总是undefined或者类型不匹配，如何正确处理children的类型和验证？',
    answer: `children属性的类型问题是React.FC使用中的常见困扰。问题通常出现在以下几个方面：

**常见问题：**
1. **children未传入**：使用了React.FC但没有传入children，导致children为undefined
2. **children类型不匹配**：期望特定类型的children但实际传入了其他类型
3. **children数量问题**：期望单个child但传入了多个，或者相反
4. **children结构验证**：需要验证children是否符合预期的组件结构

**解决策略：**
1. **明确children使用意图**：组件设计时明确是否真的需要children
2. **使用更精确的children类型**：根据实际需求定义children类型约束
3. **运行时验证**：使用React.Children API进行children验证
4. **提供fallback**：当children不符合预期时提供默认内容`,
    code: `// 常见问题示例
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// ❌ 问题：children可能为undefined
const Modal: React.FC<ModalProps> = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;
  
  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content">
        {/* children可能是undefined，没有内容显示 */}
        {children}
        <button onClick={onClose}>关闭</button>
      </div>
    </div>
  );
};

// ✅ 解决方案1：children验证和fallback
const Modal: React.FC<ModalProps> = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;
  
  const hasValidChildren = React.Children.count(children) > 0;
  
  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content">
        {hasValidChildren ? children : <div>暂无内容</div>}
        <button onClick={onClose}>关闭</button>
      </div>
    </div>
  );
};

// ✅ 解决方案2：更精确的children类型定义
interface StrictModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode; // 明确要求children
}

const StrictModal: React.FC<StrictModalProps> = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;
  
  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content">
        {children}
        <button onClick={onClose}>关闭</button>
      </div>
    </div>
  );
};

// 高级：特定children类型约束
interface TabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  children: ReactElement<TabProps>[]; // 只接受Tab组件数组
}

interface TabProps {
  id: string;
  title: string;
  children: ReactNode;
}

const Tab: React.FC<TabProps> = ({ children }) => <div>{children}</div>;

const Tabs: React.FC<TabsProps> = ({ activeTab, onTabChange, children }) => {
  // 验证children类型
  const validTabs = React.Children.toArray(children).filter(
    (child): child is ReactElement<TabProps> => 
      React.isValidElement(child) && child.type === Tab
  );

  if (validTabs.length === 0) {
    console.warn('Tabs组件需要至少一个Tab子组件');
    return <div>没有可显示的标签页</div>;
  }

  return (
    <div className="tabs">
      <div className="tab-headers">
        {validTabs.map((tab) => (
          <button
            key={tab.props.id}
            onClick={() => onTabChange(tab.props.id)}
            className={activeTab === tab.props.id ? 'active' : ''}
          >
            {tab.props.title}
          </button>
        ))}
      </div>
      <div className="tab-content">
        {validTabs.find(tab => tab.props.id === activeTab)}
      </div>
    </div>
  );
};

// 使用示例
const App = () => {
  const [activeTab, setActiveTab] = React.useState('tab1');
  
  return (
    <Tabs activeTab={activeTab} onTabChange={setActiveTab}>
      <Tab id="tab1" title="用户信息">
        <div>用户信息内容</div>
      </Tab>
      <Tab id="tab2" title="设置">
        <div>设置内容</div>
      </Tab>
    </Tabs>
  );
};

// 实用工具函数：children处理
const useChildrenValidation = (
  children: ReactNode,
  expectedType?: React.ComponentType
) => {
  return React.useMemo(() => {
    const childArray = React.Children.toArray(children);
    
    return {
      count: childArray.length,
      hasChildren: childArray.length > 0,
      validChildren: expectedType 
        ? childArray.filter(child => 
            React.isValidElement(child) && child.type === expectedType
          )
        : childArray,
      firstChild: childArray[0] || null,
      lastChild: childArray[childArray.length - 1] || null
    };
  }, [children, expectedType]);
};

// 使用工具函数的组件
const SmartContainer: React.FC<{ maxChildren?: number }> = ({ 
  maxChildren = 5, 
  children 
}) => {
  const { count, hasChildren, validChildren } = useChildrenValidation(children);
  
  if (!hasChildren) {
    return <div className="empty-container">暂无内容</div>;
  }
  
  if (count > maxChildren) {
    console.warn('容器子元素数量超过限制:' + count + ' > ' + maxChildren);
  }
  
  return (
    <div className="smart-container">
      <div className="container-header">
        共 {count} 个子元素
      </div>
      <div className="container-content">
        {validChildren.slice(0, maxChildren)}
      </div>
      {count > maxChildren && (
        <div className="container-footer">
          还有 {count - maxChildren} 个元素未显示
        </div>
      )}
    </div>
  );
};

// 错误边界结合children验证
interface ErrorBoundaryProps {
  fallback?: React.ComponentType<{ error: Error }>;
  children: ReactNode;
}

const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({ fallback: Fallback, children }) => {
  const [error, setError] = React.useState<Error | null>(null);
  
  React.useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      setError(new Error(error.message));
    };
    
    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);
  
  if (error) {
    if (Fallback) {
      return <Fallback error={error} />;
    }
    return (
      <div className="error-boundary">
        <h3>出现错误</h3>
        <p>{error.message}</p>
      </div>
    );
  }
  
  // 验证children是否存在
  if (!React.Children.count(children)) {
    return <div>错误边界: 没有需要保护的内容</div>;
  }
  
  return <>{children}</>;
};`,
    tags: ['Children处理', '类型验证'],
    relatedQuestions: ['如何验证children的组件类型？', 'React.Children API有哪些常用方法？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: 'React.FC组件渲染性能很差，组件频繁重新渲染，如何进行性能优化？',
    answer: `React.FC组件的性能问题通常源于以下几个方面，需要针对性地进行优化：

**常见性能问题：**
1. **不必要的重新渲染**：props变化时组件过度渲染
2. **函数重创建**：每次渲染都创建新的函数引用
3. **复杂计算重复执行**：没有缓存计算结果
4. **子组件连锁更新**：父组件更新导致所有子组件重新渲染

**优化策略：**
1. **使用React.memo**：避免不必要的重新渲染
2. **useCallback优化**：缓存函数引用，防止子组件不必要更新
3. **useMemo优化**：缓存复杂计算结果
4. **合理拆分组件**：将经常变化的部分独立出来
5. **状态提升优化**：避免不必要的状态传递

**性能监控：**
使用React DevTools Profiler和useDebugValue进行性能分析`,
    code: `// ❌ 性能问题示例
interface UserListProps {
  users: User[];
  searchTerm: string;
  sortBy: 'name' | 'email' | 'role';
  onUserClick: (user: User) => void;
}

const UserList: React.FC<UserListProps> = ({ users, searchTerm, sortBy, onUserClick }) => {
  // 问题1：每次渲染都重新计算，即使dependencies没变
  const filteredUsers = users.filter(user => 
    user.name.includes(searchTerm) || user.email.includes(searchTerm)
  ).sort((a, b) => {
    switch (sortBy) {
      case 'name': return a.name.localeCompare(b.name);
      case 'email': return a.email.localeCompare(b.email);
      case 'role': return a.role.localeCompare(b.role);
      default: return 0;
    }
  });

  // 问题2：每次渲染都创建新函数，导致UserCard重新渲染
  const handleUserClick = (user: User) => {
    console.log('User clicked:', user.name);
    onUserClick(user);
  };

  return (
    <div className="user-list">
      {filteredUsers.map(user => (
        <UserCard 
          key={user.id} 
          user={user} 
          onClick={handleUserClick} // 每次都是新函数引用
        />
      ))}
    </div>
  );
};

// UserCard也没有优化，每次都重新渲染
const UserCard: React.FC<{ user: User; onClick: (user: User) => void }> = ({ user, onClick }) => {
  return (
    <div className="user-card" onClick={() => onClick(user)}>
      <img src={user.avatar} alt={user.name} />
      <div>
        <h4>{user.name}</h4>
        <p>{user.email}</p>
        <span>{user.role}</span>
      </div>
    </div>
  );
};

// ✅ 性能优化后的版本
const UserList: React.FC<UserListProps> = React.memo(({ 
  users, 
  searchTerm, 
  sortBy, 
  onUserClick 
}) => {
  // 优化1：使用useMemo缓存计算结果
  const filteredUsers = React.useMemo(() => {
    console.log('重新计算filtered users'); // 监控计算频率
    
    const filtered = users.filter(user => 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name': return a.name.localeCompare(b.name);
        case 'email': return a.email.localeCompare(b.email);
        case 'role': return a.role.localeCompare(b.role);
        default: return 0;
      }
    });
  }, [users, searchTerm, sortBy]);

  // 优化2：使用useCallback缓存函数引用
  const handleUserClick = React.useCallback((user: User) => {
    console.log('User clicked:', user.name);
    onUserClick(user);
  }, [onUserClick]);

  // 性能监控：渲染计数
  const renderCount = React.useRef(0);
  React.useEffect(() => {
    renderCount.current += 1;
    console.log('UserList rendered ' + renderCount.current + ' times');
  });

  return (
    <div className="user-list">
      <div className="list-info">
        显示 {filteredUsers.length} / {users.length} 个用户
      </div>
      {filteredUsers.map(user => (
        <UserCard 
          key={user.id} 
          user={user} 
          onClick={handleUserClick}
        />
      ))}
    </div>
  );
});

// 优化后的UserCard
const UserCard: React.FC<{ 
  user: User; 
  onClick: (user: User) => void; 
}> = React.memo(({ user, onClick }) => {
  // 缓存点击处理器
  const handleClick = React.useCallback(() => {
    onClick(user);
  }, [user, onClick]);

  // 缓存格式化的角色显示
  const roleDisplay = React.useMemo(() => {
    return user.role.charAt(0).toUpperCase() + user.role.slice(1);
  }, [user.role]);

  return (
    <div className="user-card" onClick={handleClick}>
      <img 
        src={user.avatar} 
        alt={user.name}
        loading="lazy" // 图片懒加载
      />
      <div className="user-info">
        <h4>{user.name}</h4>
        <p>{user.email}</p>
        <span className={'role role-' + user.role}>
          {roleDisplay}
        </span>
      </div>
    </div>
  );
});

// 自定义比较函数进行更精细的控制
const UserCardWithCustomMemo: React.FC<{ 
  user: User; 
  onClick: (user: User) => void; 
}> = React.memo(
  ({ user, onClick }) => {
    // 组件实现...
    return <div>User: {user.name}</div>;
  },
  (prevProps, nextProps) => {
    // 自定义比较逻辑：只在特定字段变化时重新渲染
    return (
      prevProps.user.id === nextProps.user.id &&
      prevProps.user.name === nextProps.user.name &&
      prevProps.user.email === nextProps.user.email &&
      prevProps.user.role === nextProps.user.role &&
      prevProps.onClick === nextProps.onClick
    );
  }
);

// 进一步优化：组件拆分
const UserAvatar: React.FC<{ src: string; alt: string }> = React.memo(({ src, alt }) => (
  <img src={src} alt={alt} loading="lazy" className="user-avatar" />
));

const UserInfo: React.FC<{ name: string; email: string; role: string }> = React.memo(({ 
  name, 
  email, 
  role 
}) => {
  const roleDisplay = React.useMemo(() => 
    role.charAt(0).toUpperCase() + role.slice(1), 
    [role]
  );

  return (
    <div className="user-info">
      <h4>{name}</h4>
      <p>{email}</p>
      <span className={'role role-' + role}>{roleDisplay}</span>
    </div>
  );
});

const OptimizedUserCard: React.FC<{ 
  user: User; 
  onClick: (user: User) => void; 
}> = React.memo(({ user, onClick }) => {
  const handleClick = React.useCallback(() => onClick(user), [user, onClick]);

  return (
    <div className="user-card" onClick={handleClick}>
      <UserAvatar src={user.avatar} alt={user.name} />
      <UserInfo name={user.name} email={user.email} role={user.role} />
    </div>
  );
});

// 性能监控Hook
function usePerformanceMonitor(componentName: string) {
  const renderCount = React.useRef(0);
  const lastRenderTime = React.useRef(performance.now());
  
  React.useEffect(() => {
    renderCount.current += 1;
    const currentTime = performance.now();
    const timeSinceLastRender = currentTime - lastRenderTime.current;
    
    console.log(componentName + ' performance:', {
      renderCount: renderCount.current,
      timeSinceLastRender: timeSinceLastRender.toFixed(2) + 'ms',
      timestamp: new Date().toISOString()
    });
    
    lastRenderTime.current = currentTime;
  });
  
  React.useDebugValue(componentName + ' renders: ' + renderCount.current);
}

// 使用性能监控的组件
const MonitoredUserList: React.FC<UserListProps> = (props) => {
  usePerformanceMonitor('UserList');
  return <UserList {...props} />;
};

// 虚拟化大列表（处理大量数据）
import { FixedSizeList as List } from 'react-window';

const VirtualizedUserList: React.FC<{ users: User[]; height: number }> = ({ 
  users, 
  height 
}) => {
  const Row = React.memo(({ index, style }: { index: number; style: React.CSSProperties }) => {
    const user = users[index];
    return (
      <div style={style} className="virtual-row">
        <UserCard user={user} onClick={() => console.log('Clicked:', user.name)} />
      </div>
    );
  });

  return (
    <List
      height={height}
      itemCount={users.length}
      itemSize={80}
      width="100%"
    >
      {Row}
    </List>
  );
};`,
    tags: ['性能优化', 'React.memo'],
    relatedQuestions: ['useCallback和useMemo有什么区别？', '如何使用React DevTools分析性能？']
  }
];

// FunctionComponent常见问题内容已完成
export default commonQuestions;