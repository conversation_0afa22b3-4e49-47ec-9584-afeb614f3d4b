import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ReactFunctionComponentData: ApiItem = {
  id: 'FunctionComponent',
  title: 'FunctionComponent',
  description: 'FunctionComponent（简称FC）是React中用于表示函数组件类型的TypeScript接口，为函数组件提供完整的类型约束和Props类型推断',
  category: 'React Types',
  difficulty: 'easy',
  
  syntax: `// 基本类型定义
interface FunctionComponent<P = {}> {
  (props: P & { children?: ReactNode }, context?: any): ReactElement<any, any> | null;
  propTypes?: WeakValidationMap<P>;
  contextTypes?: ValidationMap<any>;
  defaultProps?: Partial<P>;
  displayName?: string;
}

// 简写类型别名
type FC<P = {}> = FunctionComponent<P>;

// 使用示例
const MyComponent: React.FC<Props> = ({ title, children }) => {
  return <div>{title}{children}</div>;
};`,
  example: `// 定义Props接口
interface UserProps {
  name: string;
  email: string;
  isActive?: boolean;
}

// 使用FC类型约束函数组件
const UserProfile: React.FC<UserProps> = ({ name, email, isActive = false, children }) => {
  return (
    <div className="user-profile">
      <h2>{name}</h2>
      <p>邮箱: {email}</p>
      <p>状态: {isActive ? '活跃' : '非活跃'}</p>
      {children}
    </div>
  );
};`,
  notes: '仅适用于TypeScript环境，JavaScript项目无法使用',
  
  version: 'React 16.8.0+',
  tags: ["React","TypeScript","FunctionComponent","Types"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ReactFunctionComponentData;