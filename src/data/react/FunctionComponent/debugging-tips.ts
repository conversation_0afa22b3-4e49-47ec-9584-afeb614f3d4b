import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'React.FC在开发过程中的常见问题及其解决方案，涵盖类型错误、Props传递、children处理、性能优化等核心问题域。',
        sections: [
          {
            title: 'TypeScript类型相关问题',
            description: 'React.FC与TypeScript集成时的常见类型错误和解决策略',
            items: [
              {
                title: 'Props类型错误：Property does not exist on type',
                description: '当Props接口定义与实际使用不匹配时出现的类型错误',
                solution: '确保Props接口定义完整，使用正确的可选属性语法，检查导入和导出',
                prevention: '使用严格的TypeScript配置，建立Props接口规范，定期进行类型检查',
                code: `// ❌ 错误示例
interface UserProps {
  name: string;
  // 缺少age属性定义
}

const UserCard: React.FC<UserProps> = ({ name, age }) => {
  // TypeScript错误: Property 'age' does not exist on type 'UserProps'
  return <div>{name} - {age}</div>;
};

// ✅ 正确解决方案
interface UserProps {
  name: string;
  age?: number; // 添加缺失的属性定义
  email?: string;
}

const UserCard: React.FC<UserProps> = ({ name, age, email }) => {
  return (
    <div className="user-card">
      <h3>{name}</h3>
      {age && <p>年龄: {age}</p>}
      {email && <p>邮箱: {email}</p>}
    </div>
  );
};

// 🔧 调试技巧：使用类型断言进行临时调试
const DebugUserCard: React.FC<any> = (props) => {
  console.log('Props received:', props);
  console.log('Props type:', typeof props);
  console.log('Props keys:', Object.keys(props));
  return <UserCard {...(props as UserProps)} />;
};`
              },
              {
                title: 'Children类型冲突问题',
                description: 'React.FC自动添加children属性导致的类型冲突和混淆',
                solution: '明确children的使用意图，使用正确的children类型定义，考虑不使用React.FC',
                prevention: '设计组件时明确是否需要children，使用精确的children类型约束',
                code: `// ❌ 问题示例：不需要children但使用了React.FC
const Button: React.FC<{ label: string; onClick: () => void }> = ({ 
  label, 
  onClick, 
  children // children存在但不会被使用
}) => {
  return <button onClick={onClick}>{label}</button>;
  // children被忽略，容易产生困惑
};

// ✅ 解决方案1：不使用React.FC
interface ButtonProps {
  label: string;
  onClick: () => void;
}

const Button = ({ label, onClick }: ButtonProps) => {
  return <button onClick={onClick}>{label}</button>;
};

// ✅ 解决方案2：明确使用children
const Container: React.FC<{ className?: string }> = ({ className, children }) => {
  if (!children) {
    console.warn('Container组件没有children内容');
    return null;
  }
  
  return (
    <div className={className}>
      {children}
    </div>
  );
};

// 🔧 调试技巧：children内容验证
const DebuggingContainer: React.FC<{ className?: string }> = ({ className, children }) => {
  React.useEffect(() => {
    console.log('Children count:', React.Children.count(children));
    console.log('Children type:', typeof children);
    console.log('Children content:', children);
  }, [children]);

  return <Container className={className}>{children}</Container>;
};`
              },
              {
                title: '泛型约束问题',
                description: '使用泛型React.FC时的类型约束和推断问题',
                solution: '正确设置泛型约束，使用适当的默认类型参数，明确类型边界',
                prevention: '建立泛型组件开发规范，使用类型测试验证泛型正确性',
                code: `// ❌ 问题示例：泛型约束不足
function GenericList<T>(): React.FC<{ items: T[] }> {
  return ({ items }) => {
    return (
      <ul>
        {items.map((item, index) => (
          // 错误：无法访问item的属性，因为T没有约束
          <li key={index}>{item.toString()}</li>
        ))}
      </ul>
    );
  };
}

// ✅ 解决方案：添加适当的泛型约束
interface Identifiable {
  id: string | number;
}

interface Displayable {
  toString(): string;
}

function GenericList<T extends Identifiable & Displayable>(): React.FC<{
  items: T[];
  onItemClick?: (item: T) => void;
}> {
  return ({ items, onItemClick }) => {
    return (
      <ul className="generic-list">
        {items.map((item) => (
          <li 
            key={item.id}
            onClick={() => onItemClick?.(item)}
            className="list-item"
          >
            {item.toString()}
          </li>
        ))}
      </ul>
    );
  };
}

// 🔧 调试技巧：类型验证函数
function debugGenericType<T>(items: T[], componentName: string) {
  console.group('Generic Type Debug: ' + componentName);
  console.log('Items length:', items.length);
  console.log('First item type:', typeof items[0]);
  console.log('First item structure:', items[0]);
  console.log('Type constraints check:');
  items.slice(0, 3).forEach((item, index) => {
    console.log('Item ' + index + ':', {
      hasId: 'id' in (item as any),
      hasToString: typeof (item as any).toString === 'function',
      value: item
    });
  });
  console.groupEnd();
}`
              }
            ]
          },
          {
            title: 'Runtime行为问题',
            description: 'React.FC组件在运行时的常见问题和调试方法',
            items: [
              {
                title: '组件不更新或过度更新',
                description: 'React.FC组件的渲染优化问题，包括不必要的重新渲染',
                solution: '使用React.memo、useCallback、useMemo进行优化，检查依赖数组',
                prevention: '建立组件性能监控，使用React DevTools分析渲染行为',
                code: `// ❌ 问题示例：组件过度更新
const UserList: React.FC<{ users: User[]; onUserClick: (user: User) => void }> = ({ 
  users, 
  onUserClick 
}) => {
  // 每次渲染都创建新函数，导致子组件过度更新
  const handleUserClick = (user: User) => {
    console.log('User clicked:', user.name);
    onUserClick(user);
  };

  return (
    <div>
      {users.map(user => (
        <UserCard 
          key={user.id} 
          user={user} 
          onClick={handleUserClick} // 每次都是新函数引用
        />
      ))}
    </div>
  );
};

// ✅ 解决方案：正确的优化方式
const UserList: React.FC<{ 
  users: User[]; 
  onUserClick: (user: User) => void;
}> = React.memo(({ users, onUserClick }) => {
  // 使用useCallback缓存函数
  const handleUserClick = React.useCallback((user: User) => {
    console.log('User clicked:', user.name);
    onUserClick(user);
  }, [onUserClick]);

  return (
    <div className="user-list">
      {users.map(user => (
        <UserCard 
          key={user.id} 
          user={user} 
          onClick={handleUserClick}
        />
      ))}
    </div>
  );
});

// 子组件也需要优化
const UserCard: React.FC<{ 
  user: User; 
  onClick: (user: User) => void; 
}> = React.memo(({ user, onClick }) => {
  const handleClick = React.useCallback(() => {
    onClick(user);
  }, [user, onClick]);

  return (
    <div className="user-card" onClick={handleClick}>
      <h4>{user.name}</h4>
      <p>{user.email}</p>
    </div>
  );
});

// 🔧 调试技巧：渲染计数器
function useRenderCounter(componentName: string) {
  const renderCount = React.useRef(0);
  React.useEffect(() => {
    renderCount.current += 1;
    console.log(componentName + ' rendered ' + renderCount.current + ' times');
  });
}

const DebuggingUserCard: React.FC<{ user: User; onClick: (user: User) => void }> = (props) => {
  useRenderCounter('UserCard');
  return <UserCard {...props} />;
};`
              },
              {
                title: 'State和Effect Hook问题',
                description: 'React.FC中使用Hooks时的常见问题和调试方法',
                solution: '正确设置依赖数组，理解闭包陷阱，使用useRef解决引用问题',
                prevention: '建立Hook使用规范，使用ESLint Hook规则，定期Review Hook使用',
                code: `// ❌ 问题示例：闭包陷阱和过时状态
const Counter: React.FC = () => {
  const [count, setCount] = React.useState(0);

  React.useEffect(() => {
    const timer = setInterval(() => {
      // 闭包陷阱：count始终是0
      setCount(count + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []); // 空依赖数组导致count永远是初始值

  return <div>Count: {count}</div>;
};

// ✅ 解决方案：正确处理状态更新
const Counter: React.FC = () => {
  const [count, setCount] = React.useState(0);

  React.useEffect(() => {
    const timer = setInterval(() => {
      // 使用函数式更新避免闭包陷阱
      setCount(prevCount => prevCount + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []); // 空依赖数组是安全的

  return <div>Count: {count}</div>;
};

// 🔧 调试技巧：Hook状态监控
function useDebugValue<T>(value: T, formatter?: (value: T) => any) {
  React.useDebugValue(value, formatter);
  
  React.useEffect(() => {
    console.log('Value changed:', value);
  }, [value]);
  
  return value;
}

const DebuggingCounter: React.FC = () => {
  const [count, setCount] = React.useState(0);
  const debugCount = useDebugValue(count, count => 'Count: ' + count);

  React.useEffect(() => {
    console.log('Counter effect triggered with count:', count);
    
    const timer = setInterval(() => {
      setCount(prevCount => {
        console.log('Updating count from ' + prevCount + ' to ' + (prevCount + 1));
        return prevCount + 1;
      });
    }, 1000);

    return () => {
      console.log('Counter effect cleanup');
      clearInterval(timer);
    };
  }, [count]); // 故意加入count依赖来观察行为

  return <div>Count: {debugCount}</div>;
};`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: 'React.FC组件开发和调试的专业工具配置，包括IDE设置、浏览器扩展、调试技巧和性能分析工具。',
        sections: [
          {
            title: 'IDE集成和配置',
            description: 'VS Code、WebStorm等IDE中React.FC开发的最佳配置',
            items: [
              {
                title: 'VS Code TypeScript配置优化',
                description: '配置VS Code以获得最佳的React.FC开发体验',
                solution: '安装必要扩展，配置TypeScript设置，启用智能提示和错误检查',
                prevention: '定期更新扩展和配置，建立团队统一的开发环境',
                code: `// .vscode/settings.json
{
  "typescript.preferences.autoImportFileExcludePatterns": [
    "react"
  ],
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll.eslint": true
  },
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode"
}

// .vscode/extensions.json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}

// tsconfig.json 调试配置
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    // 调试相关配置
    "sourceMap": true,
    "declaration": true,
    "declarationMap": true,
    "removeComments": false
  },
  "include": [
    "src",
    "src/**/*"
  ]
}`
              },
              {
                title: 'React DevTools集成调试',
                description: '使用React DevTools深度调试React.FC组件',
                solution: '安装React DevTools，配置Profiler，使用Components面板调试',
                prevention: '定期使用DevTools检查组件性能，建立性能监控流程',
                code: `// React DevTools调试增强
// 在组件中添加调试信息
const UserProfile: React.FC<{ userId: string }> = ({ userId }) => {
  // 添加useDebugValue为DevTools提供调试信息
  React.useDebugValue(userId, id => 'User ID: ' + id);
  
  const [user, setUser] = React.useState<User | null>(null);
  const [loading, setLoading] = React.useState(true);

  // 调试Effect执行
  React.useEffect(() => {
    console.group('UserProfile Effect');
    console.log('userId changed to:', userId);
    
    const fetchUser = async () => {
      setLoading(true);
      try {
        const userData = await getUserById(userId);
        console.log('User data fetched:', userData);
        setUser(userData);
      } catch (error) {
        console.error('Failed to fetch user:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
    console.groupEnd();
  }, [userId]);

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div className="user-profile">
      <h2>{user.name}</h2>
      <p>{user.email}</p>
    </div>
  );
};

// 开发环境下的调试增强
if (process.env.NODE_ENV === 'development') {
  // 为所有React.FC组件添加displayName
  UserProfile.displayName = 'UserProfile';
  
  // 添加性能监控
  const OriginalUserProfile = UserProfile;
  UserProfile = React.forwardRef((props, ref) => {
    const renderStart = performance.now();
    
    React.useEffect(() => {
      const renderEnd = performance.now();
      console.log('UserProfile render time:', renderEnd - renderStart, 'ms');
    });
    
    return <OriginalUserProfile {...props} ref={ref} />;
  });
  UserProfile.displayName = 'UserProfile(Debug)';
}

// Profiler包装器用于性能分析
const ProfiledUserProfile: React.FC<{ userId: string }> = (props) => {
  return (
    <React.Profiler
      id="UserProfile"
      onRender={(id, phase, actualDuration, baseDuration) => {
        console.log('Profiler data:', {
          id,
          phase,
          actualDuration,
          baseDuration
        });
        
        // 发送到分析服务
        if (actualDuration > 16) {
          analytics.track('slow_component_render', {
            component: id,
            phase,
            duration: actualDuration
          });
        }
      }}
    >
      <UserProfile {...props} />
    </React.Profiler>
  );
};`
              },
              {
                title: 'Chrome DevTools调试技巧',
                description: '使用Chrome DevTools调试React.FC组件的JavaScript和性能问题',
                solution: '配置Source Maps，使用断点调试，分析性能瓶颈',
                prevention: '定期使用Performance面板分析，建立性能基准测试',
                code: `// Chrome DevTools调试辅助函数
window.debugReactFC = {
  // 全局组件状态检查
  checkComponentState: (componentName: string) => {
    const fiber = window.__REACT_DEVTOOLS_GLOBAL_HOOK__?.getFiberRoots?.();
    if (!fiber) {
      console.warn('React DevTools not found');
      return;
    }
    
    console.log('Searching for component:', componentName);
    // 搜索组件实例的逻辑
  },
  
  // 性能监控
  startPerformanceMonitoring: () => {
    performance.mark('react-app-start');
    
    window.addEventListener('load', () => {
      performance.mark('react-app-loaded');
      performance.measure('react-app-load-time', 'react-app-start', 'react-app-loaded');
      
      const measure = performance.getEntriesByName('react-app-load-time')[0];
      console.log('React app load time:', measure.duration, 'ms');
    });
  },
  
  // 内存泄漏检测
  checkMemoryLeaks: () => {
    const initialMemory = performance.memory?.usedJSHeapSize || 0;
    
    setTimeout(() => {
      const currentMemory = performance.memory?.usedJSHeapSize || 0;
      const diff = currentMemory - initialMemory;
      
      console.log('Memory usage change:', {
        initial: initialMemory,
        current: currentMemory,
        difference: diff,
        percentageIncrease: ((diff / initialMemory) * 100).toFixed(2) + '%'
      });
    }, 5000);
  }
};

// 开发环境下自动启动调试工具
if (process.env.NODE_ENV === 'development') {
  window.debugReactFC.startPerformanceMonitoring();
  
  // 添加全局错误监控
  window.addEventListener('error', (event) => {
    console.group('🚨 JavaScript Error Detected');
    console.error('Error:', event.error);
    console.log('Source:', event.filename + ':' + event.lineno + ':' + event.colno);
    console.log('Message:', event.message);
    console.groupEnd();
  });
  
  // React错误边界监控
  window.addEventListener('unhandledrejection', (event) => {
    console.group('🚨 Unhandled Promise Rejection');
    console.error('Reason:', event.reason);
    console.log('Promise:', event.promise);
    console.groupEnd();
  });
}

// Source Map调试辅助
// webpack.config.js (开发环境)
module.exports = {
  mode: 'development',
  devtool: 'eval-source-map', // 最佳调试体验
  optimization: {
    minimize: false // 保持代码可读性
  },
  devServer: {
    hot: true,
    overlay: {
      warnings: true,
      errors: true
    }
  }
};

// 断点调试最佳实践
const DebuggableComponent: React.FC<{ data: any }> = ({ data }) => {
  // 使用debugger语句进行断点调试
  if (process.env.NODE_ENV === 'development' && !data) {
    debugger; // 当data为空时触发断点
  }
  
  React.useEffect(() => {
    // 条件断点：只在特定条件下触发
    if (data?.id === 'debug-target') {
      debugger;
    }
    
    console.log('Component mounted with data:', data);
  }, [data]);
  
  return <div>Component content</div>;
};`
              },
              {
                title: '测试和验证工具',
                description: 'React.FC组件的单元测试、集成测试和端到端测试工具配置',
                items: [
                  {
                    title: 'Jest + Testing Library配置',
                    description: '配置Jest和React Testing Library进行React.FC组件测试',
                    solution: '设置测试环境，编写组件测试，配置覆盖率报告',
                    prevention: '建立测试驱动开发流程，确保组件质量和稳定性',
                    code: `// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.tsx'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};

// setupTests.ts
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';

afterEach(cleanup);

// 模拟window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// React.FC组件测试示例
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

const UserProfile: React.FC<{ userId: string }> = ({ userId }) => {
  const [user, setUser] = React.useState(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const fetchUser = async () => {
      setLoading(true);
      try {
        const userData = await getUserById(userId);
        setUser(userData);
      } catch (error) {
        console.error('Failed to fetch user:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  if (loading) return <div data-testid="loading">Loading...</div>;
  if (!user) return <div data-testid="not-found">User not found</div>;

  return (
    <div data-testid="user-profile">
      <h2>{user.name}</h2>
      <p>{user.email}</p>
    </div>
  );
};

// 测试文件: UserProfile.test.tsx
describe('UserProfile', () => {
  const mockGetUserById = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('displays loading state initially', () => {
    mockGetUserById.mockImplementation(() => new Promise(() => {})); // 永不resolve
    
    render(<UserProfile userId="123" />);
    
    expect(screen.getByTestId('loading')).toBeInTheDocument();
  });

  test('displays user data when loaded', async () => {
    const mockUser = { id: '123', name: 'John Doe', email: '<EMAIL>' };
    mockGetUserById.mockResolvedValue(mockUser);
    
    render(<UserProfile userId="123" />);
    
    await waitFor(() => {
      expect(screen.getByTestId('user-profile')).toBeInTheDocument();
    });
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  test('displays not found when user does not exist', async () => {
    mockGetUserById.mockRejectedValue(new Error('User not found'));
    
    render(<UserProfile userId="999" />);
    
    await waitFor(() => {
      expect(screen.getByTestId('not-found')).toBeInTheDocument();
    });
  });

  test('refetches user when userId changes', async () => {
    const { rerender } = render(<UserProfile userId="123" />);
    
    await waitFor(() => {
      expect(mockGetUserById).toHaveBeenCalledWith('123');
    });
    
    rerender(<UserProfile userId="456" />);
    
    await waitFor(() => {
      expect(mockGetUserById).toHaveBeenCalledWith('456');
    });
    
    expect(mockGetUserById).toHaveBeenCalledTimes(2);
  });
});`
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  ]
};

// FunctionComponent调试技巧内容已完成
export default debuggingTips;