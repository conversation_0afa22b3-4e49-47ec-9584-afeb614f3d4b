import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: "FunctionComponent（简称FC）是React中用于表示函数组件类型的TypeScript接口，为函数组件提供完整的类型约束和Props类型推断",
  
  introduction: `FunctionComponent是React TypeScript类型定义中的核心接口，专门用于为函数组件提供类型约束。它代表了现代React开发的主流模式，相比传统类组件更加简洁、性能更优。FunctionComponent接口不仅确保函数组件的类型安全，还提供了children等通用属性的自动类型推断。

随着React Hooks的引入，函数组件获得了状态管理和生命周期能力，FunctionComponent类型也成为了TypeScript React开发的事实标准。它简化了组件定义，提升了开发效率，是现代React应用的基础构建块。`,

  syntax: `// 基本类型定义
interface FunctionComponent<P = {}> {
  (props: P & { children?: ReactNode }, context?: any): ReactElement<any, any> | null;
  propTypes?: WeakValidationMap<P>;
  contextTypes?: ValidationMap<any>;
  defaultProps?: Partial<P>;
  displayName?: string;
}

// 简写类型别名
type FC<P = {}> = FunctionComponent<P>;

// 使用示例
const MyComponent: React.FC<Props> = ({ title, children }) => {
  return <div>{title}{children}</div>;
};

// 泛型约束使用
const GenericComponent: React.FC<{ data: T }> = ({ data }) => {
  return <div>{JSON.stringify(data)}</div>;
};`,

  quickExample: `// 定义Props接口
interface UserProps {
  name: string;
  email: string;
  isActive?: boolean;
}

// 使用FC类型约束函数组件
const UserProfile: React.FC<UserProps> = ({ name, email, isActive = false, children }) => {
  return (
    <div className="user-profile">
      <h2>{name}</h2>
      <p>邮箱: {email}</p>
      <p>状态: {isActive ? '活跃' : '非活跃'}</p>
      {children}
    </div>
  );
};

// 带默认Props的组件
const Button: React.FC<{ label: string; onClick?: () => void }> = ({ 
  label, 
  onClick = () => {}, 
  children 
}) => {
  return (
    <button onClick={onClick}>
      {label}
      {children}
    </button>
  );
};`,

  scenarioDiagram: `graph TD
    A[FunctionComponent使用场景] --> B[基础组件类型约束]
    A --> C[Props类型推断]
    A --> D[组件库开发]

    B --> B1[函数组件类型定义]
    B --> B2[Children属性自动推断]
    B --> B3[TypeScript智能提示]

    C --> C1[Props接口定义]
    C --> C2[可选属性处理]
    C --> C3[默认值类型安全]

    D --> D1[可复用组件设计]
    D --> D2[泛型组件构建]
    D --> D3[第三方库集成]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "P (Props)",
      type: "object",
      required: false,
      description: "组件的props类型，默认为空对象类型，包含组件接受的所有属性",
      example: "interface MyProps { title: string; count?: number; }"
    }
  ],
  
  returnValue: {
    type: "FunctionComponent<P>",
    description: "返回符合FunctionComponent接口的函数组件类型定义，包含Props约束和children支持",
    example: "React.FC<UserProps>"
  },
  
  keyFeatures: [
    {
      title: "自动children支持",
      description: "FunctionComponent自动为组件添加children属性，无需手动定义",
      benefit: "简化Props接口定义，自动获得children的完整类型支持"
    },
    {
      title: "Props类型推断",
      description: "提供完整的TypeScript类型推断和智能提示功能",
      benefit: "编译时发现类型错误，IDE提供精确的代码补全"
    },
    {
      title: "简洁语法支持",
      description: "支持FC简写语法，代码更加简洁易读",
      benefit: "提升开发效率，减少重复的类型声明代码"
    },
    {
      title: "静态属性约束",
      description: "支持defaultProps、displayName等静态属性的类型定义",
      benefit: "完整的组件元信息类型安全保障"
    }
  ],
  
  limitations: [
    "仅适用于TypeScript环境，JavaScript项目无法使用",
    "对于极其简单的组件可能显得过于冗长",
    "children属性的自动注入可能与某些特定设计不符",
    "泛型约束相比普通函数可能增加学习成本",
    "在某些复杂类型推断场景下可能需要显式类型注解"
  ],
  
  bestPractices: [
    "为Props定义明确的TypeScript接口，提供完整的属性说明",
    "使用FC简写语法保持代码简洁性",
    "合理使用可选属性和默认值，提供良好的API设计",
    "为组件添加displayName便于调试和开发工具识别",
    "结合React.memo使用获得性能优化和类型安全的双重收益"
  ],
  
  warnings: [
    "FC类型会自动添加children属性，如果组件不需要children可能造成类型混淆",
    "过度使用泛型可能导致类型定义过于复杂，影响可读性",
    "在某些版本的React类型定义中FC和FunctionComponent可能有细微差异"
  ]
};

// FunctionComponent基本信息内容已完成
export default basicInfo;