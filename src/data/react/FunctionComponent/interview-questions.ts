import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'React.FC（FunctionComponent）是什么？它与普通函数组件有什么区别？为什么要使用React.FC？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'React.FC是TypeScript中用于定义函数组件类型的接口，它自动为组件添加children属性，提供完整的类型约束和智能提示。',
      detailed: `React.FC（FunctionComponent的简写）是React TypeScript类型定义中的核心接口，专门用于为函数组件提供类型约束：

**主要作用：**
1. **类型约束**：为函数组件提供完整的TypeScript类型检查
2. **自动children支持**：自动为组件添加children?: ReactNode属性
3. **Props类型推断**：提供精确的Props类型推断和智能提示
4. **静态属性支持**：支持displayName、defaultProps等静态属性的类型定义

**与普通函数组件的区别：**
- 普通函数组件是具体实现，React.FC是类型约束
- React.FC自动添加children属性，普通函数组件需要手动定义
- React.FC提供更完整的TypeScript开发体验

**使用React.FC的优势：**
- 编译时类型检查，减少运行时错误
- IDE智能提示和自动补全
- 统一的组件接口规范
- 更好的代码可读性和维护性`,
      code: `// React.FC的基本使用
interface UserProps {
  name: string;
  age: number;
  email?: string;
}

// 使用React.FC类型约束
const UserCard: React.FC<UserProps> = ({ name, age, email, children }) => {
  return (
    <div className="user-card">
      <h3>{name}</h3>
      <p>年龄: {age}</p>
      {email && <p>邮箱: {email}</p>}
      {/* children自动可用，无需在Props中定义 */}
      {children}
    </div>
  );
};

// 对比：普通函数组件需要手动定义children
interface UserPropsWithChildren extends UserProps {
  children?: ReactNode; // 需要手动添加
}

const UserCardNormal = ({ name, age, email, children }: UserPropsWithChildren) => {
  return (
    <div className="user-card">
      <h3>{name}</h3>
      <p>年龄: {age}</p>
      {email && <p>邮箱: {email}</p>}
      {children}
    </div>
  );
};

// 使用示例
function App() {
  return (
    <UserCard name="张三" age={25} email="<EMAIL>">
      <button>编辑用户</button>
      <button>删除用户</button>
    </UserCard>
  );
}

// 类型推断验证
type UserCardProps = React.ComponentProps<typeof UserCard>; 
// 结果：{ name: string; age: number; email?: string; children?: ReactNode }`
    },
    tags: ['TypeScript', '函数组件']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'React.FC自动添加的children属性有什么优缺点？在什么情况下应该避免使用React.FC？如何正确处理children的类型问题？',
    difficulty: 'medium',
    frequency: 'high',
    category: 'Props设计',
    answer: {
      brief: 'React.FC自动添加children属性简化了组件定义，但可能导致类型混淆。对于不需要children的组件应避免使用，可以使用普通函数组件或显式类型定义。',
      detailed: `React.FC的children属性处理是一个重要的设计考量：

**自动children的优点：**
1. **简化定义**：无需在Props接口中手动添加children
2. **统一接口**：所有React.FC组件都自动支持children
3. **类型安全**：children被正确定义为ReactNode类型
4. **开发体验**：IDE提供完整的children属性提示

**自动children的缺点：**
1. **类型混淆**：不需要children的组件也会有children属性
2. **设计不明确**：组件API不够明确表达是否支持children
3. **运行时错误**：可能传入children但组件不渲染，容易困惑

**应该避免React.FC的情况：**
- 组件设计上不应该接受children
- 需要精确控制Props接口
- 组件是纯展示型，不需要包装其他内容
- 对类型定义有严格要求的场景

**children类型处理最佳实践：**
- 明确组件是否需要children
- 对children进行运行时验证
- 使用更精确的children类型定义
- 提供fallback和错误处理`,
      code: `// ❌ 不推荐：不需要children但使用了React.FC
const SimpleButton: React.FC<{ onClick: () => void; label: string }> = ({ onClick, label, children }) => {
  // children被忽略，但类型上存在，容易混淆
  return <button onClick={onClick}>{label}</button>;
};

// ✅ 推荐：明确不需要children
interface ButtonProps {
  onClick: () => void;
  label: string;
}

const SimpleButton = ({ onClick, label }: ButtonProps) => {
  return <button onClick={onClick}>{label}</button>;
};

// ✅ 推荐：明确需要children时使用React.FC
const Container: React.FC<{ className?: string }> = ({ className, children }) => {
  return (
    <div className={className}>
      {children}
    </div>
  );
};

// 高级：自定义children类型
interface TabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  children: ReactElement<TabProps>[]; // 限制children类型
}

const Tabs: React.FC<TabsProps> = ({ activeTab, onTabChange, children }) => {
  // 验证children类型
  const validChildren = React.Children.toArray(children).filter(
    (child): child is ReactElement<TabProps> => 
      React.isValidElement(child) && child.type === Tab
  );

  return (
    <div className="tabs">
      <div className="tab-headers">
        {validChildren.map((child) => (
          <button
            key={child.props.id}
            onClick={() => onTabChange(child.props.id)}
            className={activeTab === child.props.id ? 'active' : ''}
          >
            {child.props.title}
          </button>
        ))}
      </div>
      <div className="tab-content">
        {validChildren.find(child => child.props.id === activeTab)}
      </div>
    </div>
  );
};

// 更精确的children处理
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, children }) => {
  // children验证和处理
  const hasValidChildren = React.Children.count(children) > 0;
  
  if (!isOpen) return null;
  
  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        {hasValidChildren ? children : <div>暂无内容</div>}
        <button className="modal-close" onClick={onClose}>×</button>
      </div>
    </div>
  );
};

// 类型安全的children操作
function processChildren(children: ReactNode): ReactElement[] {
  return React.Children.toArray(children).filter(
    (child): child is ReactElement => React.isValidElement(child)
  );
}

// 使用示例
function App() {
  return (
    <Modal isOpen={true} onClose={() => {}}>
      <h2>模态框标题</h2>
      <p>这是模态框内容</p>
      <div>
        <button>确认</button>
        <button>取消</button>
      </div>
    </Modal>
  );
}`
    },
    tags: ['Children处理', 'Props设计']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 3,
    question: '如何设计一个支持泛型的React.FC组件工厂，实现类型安全的组件复用和高阶组件模式？请展示企业级应用中的实际应用场景。',
    difficulty: 'hard',
    frequency: 'medium',
    category: '高级应用',
    answer: {
      brief: '通过泛型约束和高阶组件模式，可以创建类型安全的组件工厂，实现企业级的组件复用、状态管理和功能增强。',
      detailed: `React.FC的高级应用涉及泛型约束、HOC模式和企业级架构设计：

**泛型组件工厂的设计原则：**
1. **类型安全**：确保所有泛型参数都有适当的约束
2. **灵活性**：支持不同数据类型和组件变体
3. **可复用性**：通过工厂模式实现组件的批量创建
4. **可扩展性**：支持功能的动态增强和配置

**高阶组件模式的应用场景：**
- 权限控制和身份验证
- 性能监控和错误追踪
- 数据获取和状态管理
- 主题和样式注入
- 多语言和国际化支持

**企业级实现策略：**
- 建立组件元数据系统
- 实现配置驱动的组件生成
- 建立组件质量和性能监控
- 提供完整的TypeScript类型支持
- 支持插件化的功能扩展`,
      code: `// 企业级泛型组件工厂设计
interface ComponentMetadata {
  name: string;
  version: string;
  team: string;
  permissions?: string[];
}

// 基础约束类型
interface BaseProps {
  id?: string;
  className?: string;
  testId?: string;
}

// 泛型组件工厂类型
type GenericComponentFactory<TData, TProps extends BaseProps = BaseProps> = React.FC<
  TProps & {
    data: TData;
    onDataChange?: (data: TData) => void;
    loading?: boolean;
    error?: string | null;
  }
>;

// 数据表格工厂
function createDataTable<TItem extends Record<string, any>>(): GenericComponentFactory<
  TItem[],
  {
    columns: Array<{
      key: keyof TItem;
      title: string;
      render?: (value: TItem[keyof TItem], item: TItem) => ReactNode;
      sortable?: boolean;
    }>;
    onRowClick?: (item: TItem) => void;
    onSort?: (key: keyof TItem, direction: 'asc' | 'desc') => void;
  }
> {
  const DataTable: React.FC<any> = ({ 
    data, 
    columns, 
    onRowClick, 
    onSort, 
    loading, 
    error, 
    className, 
    testId 
  }) => {
    const [sortKey, setSortKey] = React.useState<keyof TItem | null>(null);
    const [sortDirection, setSortDirection] = React.useState<'asc' | 'desc'>('asc');

    const handleSort = (key: keyof TItem) => {
      const newDirection = sortKey === key && sortDirection === 'asc' ? 'desc' : 'asc';
      setSortKey(key);
      setSortDirection(newDirection);
      onSort?.(key, newDirection);
    };

    if (loading) return <div className="loading">加载中...</div>;
    if (error) return <div className="error">错误: {error}</div>;
    if (!data || data.length === 0) return <div className="empty">暂无数据</div>;

    return (
      <table className={'data-table ' + (className || '')} data-testid={testId}>
        <thead>
          <tr>
            {columns.map((column) => (
              <th 
                key={String(column.key)}
                onClick={column.sortable ? () => handleSort(column.key) : undefined}
                className={column.sortable ? 'sortable' : ''}
              >
                {column.title}
                {sortKey === column.key && (
                  <span className="sort-indicator">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => (
            <tr 
              key={item.id || index}
              onClick={() => onRowClick?.(item)}
              className={onRowClick ? 'clickable' : ''}
            >
              {columns.map((column) => (
                <td key={String(column.key)}>
                  {column.render 
                    ? column.render(item[column.key], item)
                    : String(item[column.key])
                  }
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    );
  };

  return DataTable;
}

// 高阶组件：添加企业级功能
function withEnterpriseFeatures<TProps extends BaseProps>(
  Component: React.FC<TProps>,
  metadata: ComponentMetadata
): React.FC<TProps & {
  onAnalytics?: (event: string, data: any) => void;
  permissions?: string[];
}> {
  return function EnhancedComponent({ onAnalytics, permissions, ...props }) {
    // 权限检查
    const hasPermission = usePermissionCheck(permissions);
    
    // 性能监控
    const { startTrace, endTrace } = usePerformanceTrace();
    
    // 错误边界
    const [error, setError] = React.useState<Error | null>(null);
    
    React.useEffect(() => {
      startTrace(metadata.name);
      return () => endTrace(metadata.name);
    }, []);

    React.useEffect(() => {
      // 组件使用分析
      onAnalytics?.('component_mounted', {
        component: metadata.name,
        team: metadata.team,
        version: metadata.version
      });
    }, []);

    if (!hasPermission) {
      return <div className="permission-denied">权限不足</div>;
    }

    if (error) {
      return (
        <div className="component-error">
          <h4>组件错误</h4>
          <p>{error.message}</p>
          <button onClick={() => setError(null)}>重试</button>
        </div>
      );
    }

    try {
      return <Component {...(props as TProps)} />;
    } catch (err) {
      setError(err as Error);
      return null;
    }
  };
}

// 实际使用示例
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  lastLogin: string;
}

// 创建用户表格组件
const UserTable = createDataTable<User>();
const EnhancedUserTable = withEnterpriseFeatures(UserTable, {
  name: 'UserTable',
  version: '2.1.0',
  team: 'UserManagement',
  permissions: ['user.read']
});

// 表单工厂
function createForm<TFormData extends Record<string, any>>(): GenericComponentFactory<
  TFormData,
  {
    fields: Array<{
      key: keyof TFormData;
      label: string;
      type: 'text' | 'email' | 'password' | 'select' | 'textarea';
      required?: boolean;
      options?: Array<{ value: any; label: string }>;
      validation?: (value: any) => string | null;
    }>;
    onSubmit?: (data: TFormData) => void | Promise<void>;
    submitLabel?: string;
  }
> {
  const Form: React.FC<any> = ({ 
    data, 
    fields, 
    onDataChange, 
    onSubmit, 
    submitLabel = '提交',
    loading,
    error,
    className,
    testId 
  }) => {
    const [errors, setErrors] = React.useState<Record<string, string>>({});
    const [submitting, setSubmitting] = React.useState(false);

    const handleFieldChange = (key: keyof TFormData, value: any) => {
      const newData = { ...data, [key]: value };
      onDataChange?.(newData);
      
      // 清除字段错误
      if (errors[key as string]) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[key as string];
          return newErrors;
        });
      }
    };

    const validateForm = (): boolean => {
      const newErrors: Record<string, string> = {};
      
      fields.forEach(field => {
        const value = data[field.key];
        
        if (field.required && (!value || value === '')) {
          newErrors[field.key as string] = field.label + '是必填项';
        }
        
        if (value && field.validation) {
          const validationError = field.validation(value);
          if (validationError) {
            newErrors[field.key as string] = validationError;
          }
        }
      });
      
      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      
      if (!validateForm()) return;
      
      setSubmitting(true);
      try {
        await onSubmit?.(data);
      } catch (err) {
        console.error('表单提交失败:', err);
      } finally {
        setSubmitting(false);
      }
    };

    return (
      <form 
        className={'dynamic-form ' + (className || '')}
        onSubmit={handleSubmit}
        data-testid={testId}
      >
        {error && <div className="form-error">{error}</div>}
        
        {fields.map((field) => (
          <div key={String(field.key)} className="form-field">
            <label>
              {field.label}
              {field.required && <span className="required">*</span>}
            </label>
            
            {field.type === 'select' ? (
              <select
                value={data[field.key] || ''}
                onChange={(e) => handleFieldChange(field.key, e.target.value)}
              >
                <option value="">请选择</option>
                {field.options?.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            ) : field.type === 'textarea' ? (
              <textarea
                value={data[field.key] || ''}
                onChange={(e) => handleFieldChange(field.key, e.target.value)}
              />
            ) : (
              <input
                type={field.type}
                value={data[field.key] || ''}
                onChange={(e) => handleFieldChange(field.key, e.target.value)}
              />
            )}
            
            {errors[field.key as string] && (
              <span className="field-error">{errors[field.key as string]}</span>
            )}
          </div>
        ))}
        
        <button 
          type="submit" 
          disabled={submitting || loading}
          className="submit-button"
        >
          {submitting ? '提交中...' : submitLabel}
        </button>
      </form>
    );
  };

  return Form;
}

// 企业级应用示例
const UserForm = createForm<{
  name: string;
  email: string;
  role: string;
  department: string;
}>();

const EnhancedUserForm = withEnterpriseFeatures(UserForm, {
  name: 'UserForm',
  version: '1.5.0',
  team: 'UserManagement',
  permissions: ['user.create', 'user.edit']
});

// 使用示例
function UserManagementPage() {
  const [users, setUsers] = React.useState<User[]>([]);
  const [formData, setFormData] = React.useState({
    name: '',
    email: '',
    role: '',
    department: ''
  });

  return (
    <div className="user-management">
      <EnhancedUserTable
        data={users}
        columns={[
          { key: 'name', title: '姓名', sortable: true },
          { key: 'email', title: '邮箱', sortable: true },
          { key: 'role', title: '角色' },
          { key: 'department', title: '部门' },
          { 
            key: 'lastLogin', 
            title: '最后登录',
            render: (value) => new Date(value).toLocaleDateString()
          }
        ]}
        onRowClick={(user) => console.log('选择用户:', user)}
        onSort={(key, direction) => console.log('排序:', key, direction)}
        permissions={['user.read']}
        onAnalytics={(event, data) => console.log('分析事件:', event, data)}
      />
      
      <EnhancedUserForm
        data={formData}
        onDataChange={setFormData}
        fields={[
          { key: 'name', label: '姓名', type: 'text', required: true },
          { key: 'email', label: '邮箱', type: 'email', required: true },
          { 
            key: 'role', 
            label: '角色', 
            type: 'select', 
            required: true,
            options: [
              { value: 'user', label: '普通用户' },
              { value: 'admin', label: '管理员' }
            ]
          },
          { key: 'department', label: '部门', type: 'text', required: true }
        ]}
        onSubmit={(data) => console.log('提交表单:', data)}
        permissions={['user.create']}
        onAnalytics={(event, data) => console.log('表单分析:', event, data)}
      />
    </div>
  );
}`
    },
    tags: ['泛型约束', '高阶组件']
  }
];

// FunctionComponent面试问题内容已完成
export default interviewQuestions;