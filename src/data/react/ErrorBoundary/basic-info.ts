import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: "ErrorBoundary是React中用于捕获和处理组件树中JavaScript错误的错误边界类组件",
  
  introduction: `ErrorBoundary是React 16引入的错误处理机制，主要用于捕获子组件树中的JavaScript错误、记录错误信息和显示降级UI。它采用类组件的设计模式，提供了优雅的错误恢复能力，避免整个应用因单个组件错误而崩溃。`,

  syntax: `class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('ErrorBoundary captured an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <h1>Something went wrong.</h1>;
    }
    return this.props.children;
  }
}`,

  quickExample: `function ErrorBoundaryExample() {
  // 错误边界组件，捕获子组件错误
  const [hasError, setHasError] = useState(false);

  return (
    <div>
      {/* 错误边界包裹可能出错的组件 */}
      <ErrorBoundary>
        <ProblematicComponent 
          data={complexData}
          onError={() => setHasError(true)}
        >
          这里是可能出错的子组件内容
        </ProblematicComponent>
      </ErrorBoundary>
    </div>
  );
}`,

  scenarioDiagram: `graph TD
    A[ErrorBoundary使用场景] --> B[组件错误捕获]
    A --> C[第三方库集成]
    A --> D[数据处理异常]

    B --> B1[子组件渲染错误]
    B --> B2[生命周期错误]
    B --> B3[事件处理错误]

    C --> C1[图表库错误]
    C --> C2[地图组件错误]
    C --> C3[富文本编辑器错误]

    D --> D1[API数据异常]
    D --> D2[JSON解析错误]
    D --> D3[类型转换错误]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "children",
      type: "ReactNode",
      required: true,
      description: "被错误边界保护的子组件",
      example: "<MyComponent />"
    },
    {
      name: "fallback",
      type: "React.ComponentType<ErrorInfo>",
      required: false,
      description: "发生错误时显示的降级组件",
      example: "<ErrorFallback error={error} />"
    }
  ],
  
  returnValue: {
    type: "ReactElement",
    description: "正常情况返回children，错误时返回降级UI",
    example: "<div>Error occurred</div>"
  },
  
  keyFeatures: [
    {
      title: "错误捕获能力",
      description: "自动捕获子组件树中的JavaScript错误",
      benefit: "防止单个组件错误导致整个应用崩溃"
    },
    {
      title: "降级UI显示", 
      description: "在错误发生时显示用户友好的错误界面",
      benefit: "提供良好的用户体验，避免白屏现象"
    },
    {
      title: "错误信息记录",
      description: "记录错误详情和组件堆栈信息",
      benefit: "帮助开发者快速定位和修复问题"
    },
    {
      title: "组件树隔离",
      description: "将错误影响范围限制在特定组件树内",
      benefit: "提高应用的整体稳定性和可靠性"
    }
  ],
  
  limitations: [
    "只能捕获子组件的错误，无法捕获自身错误",
    "无法捕获事件处理器中的错误",
    "无法捕获异步代码错误（如setTimeout、Promise）",
    "无法捕获服务端渲染期间的错误",
    "无法捕获开发模式下的运行时警告"
  ],
  
  bestPractices: [
    "在应用的顶层和关键功能模块设置错误边界",
    "为不同类型的错误提供专门的降级UI",
    "记录错误信息到监控系统以便分析",
    "避免在错误边界内部抛出新的错误",
    "配合Suspense使用，处理异步加载错误"
  ],
  
  warnings: [
    "错误边界本身的错误无法自己捕获",
    "不要在错误边界的降级UI中执行可能出错的操作",
    "开发环境下React会显示错误详情覆盖降级UI"
  ]
};

// ErrorBoundary基本信息内容已完成
export default basicInfo;