import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: 'ErrorBoundary为什么不能捕获事件处理器中的错误？如何处理这类错误？',
    answer: `ErrorBoundary无法捕获事件处理器错误的原因是React的设计哲学。事件处理器不在React的渲染过程中执行，而是在浏览器的事件循环中异步执行。ErrorBoundary只能捕获React组件树中同步渲染阶段的错误。

解决方案：
1. 使用传统的try-catch语句包装事件处理器
2. 使用全局错误处理器监听unhandledrejection事件
3. 创建自定义Hook统一处理异步错误`,
    code: `// 方案1：try-catch包装
function MyComponent() {
  const handleClick = () => {
    try {
      // 可能出错的事件处理逻辑
      riskyOperation();
    } catch (error) {
      console.error('Event handler error:', error);
      // 手动处理错误或上报
    }
  };

  return <button onClick={handleClick}>Click me</button>;
}

// 方案2：自定义错误处理Hook
function useErrorHandler() {
  const handleError = useCallback((error) => {
    console.error('Async error:', error);
    // 发送到错误监控系统
    errorReporting.report(error);
  }, []);

  const safeAsyncCall = useCallback(async (asyncFn) => {
    try {
      return await asyncFn();
    } catch (error) {
      handleError(error);
      throw error; // 重新抛出让调用者决定如何处理
    }
  }, [handleError]);

  return { handleError, safeAsyncCall };
}`,
    tags: ['事件处理', '错误捕获'],
    relatedQuestions: ['如何处理异步代码错误？', 'ErrorBoundary的局限性有哪些？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '函数组件能否成为ErrorBoundary？如何在函数组件中使用错误边界功能？',
    answer: `函数组件无法直接成为ErrorBoundary，因为ErrorBoundary依赖于类组件的生命周期方法（getDerivedStateFromError和componentDidCatch）。但可以通过以下方式在函数组件中使用错误边界：

1. 使用第三方库如react-error-boundary
2. 创建高阶组件(HOC)包装
3. 使用自定义Hook处理错误状态
4. 将ErrorBoundary作为包装组件使用`,
    code: `// 方案1：使用react-error-boundary库
import { ErrorBoundary } from 'react-error-boundary';

function ErrorFallback({ error, resetErrorBoundary }) {
  return (
    <div role="alert">
      <h2>Something went wrong:</h2>
      <pre>{error.message}</pre>
      <button onClick={resetErrorBoundary}>Try again</button>
    </div>
  );
}

function MyFunctionComponent() {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Caught error:', error, errorInfo);
      }}
    >
      <RiskyComponent />
    </ErrorBoundary>
  );
}

// 方案2：自定义Hook处理错误状态
function useErrorBoundary() {
  const [error, setError] = useState(null);
  
  const resetError = () => setError(null);
  
  const captureError = useCallback((error) => {
    setError(error);
  }, []);

  useEffect(() => {
    if (error) {
      throw error; // 让上层ErrorBoundary捕获
    }
  }, [error]);

  return { captureError, resetError };
}

// 使用示例
function ComponentWithErrorHandling() {
  const { captureError } = useErrorBoundary();
  
  const handleRiskyOperation = async () => {
    try {
      await riskyAsyncOperation();
    } catch (error) {
      captureError(error); // 触发ErrorBoundary
    }
  };

  return <button onClick={handleRiskyOperation}>Do risky thing</button>;
}`,
    tags: ['函数组件', '错误处理'],
    relatedQuestions: ['如何在函数组件中模拟生命周期？', 'react-error-boundary库如何使用？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: 'ErrorBoundary和try-catch的区别是什么？什么时候应该使用哪一个？',
    answer: `ErrorBoundary和try-catch都是错误处理机制，但适用场景和工作原理不同：

**ErrorBoundary**：
- 用于React组件渲染阶段的声明式错误处理
- 自动捕获子组件树中的渲染错误
- 提供组件级别的错误隔离和降级UI
- 无法捕获异步代码、事件处理器等错误

**try-catch**：
- 用于命令式的错误处理，适用于任何JavaScript代码
- 需要手动包装可能出错的代码
- 可以捕获同步和异步代码错误
- 提供更精细的错误控制

**使用场景选择**：
- 组件渲染错误 → ErrorBoundary
- 事件处理器错误 → try-catch
- 异步操作错误 → try-catch + async/await
- API调用错误 → try-catch
- 第三方库错误 → ErrorBoundary包装 + try-catch处理`,
    code: `// ErrorBoundary - 处理组件渲染错误
class ComponentErrorBoundary extends React.Component {
  // ... ErrorBoundary实现
}

function App() {
  return (
    <ComponentErrorBoundary>
      <ThirdPartyChart data={complexData} />
    </ComponentErrorBoundary>
  );
}

// try-catch - 处理事件和异步错误
function DataFetcher() {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);

  const fetchData = async () => {
    try {
      // 异步操作用try-catch
      const response = await fetch('/api/data');
      const result = await response.json();
      setData(result);
    } catch (error) {
      setError(error.message);
      console.error('API调用失败:', error);
    }
  };

  const handleButtonClick = () => {
    try {
      // 事件处理器用try-catch
      processUserInput();
      fetchData();
    } catch (error) {
      console.error('事件处理错误:', error);
    }
  };

  return (
    <div>
      <button onClick={handleButtonClick}>Fetch Data</button>
      {error && <div>Error: {error}</div>}
    </div>
  );
}

// 组合使用 - ErrorBoundary + try-catch
function RobustComponent() {
  return (
    <ComponentErrorBoundary>
      <DataFetcher /> {/* 内部使用try-catch处理异步错误 */}
    </ComponentErrorBoundary>
  );
}`,
    tags: ['错误处理', '最佳实践'],
    relatedQuestions: ['如何设计完整的错误处理策略？', '异步错误如何与ErrorBoundary配合？']
  }
];

// ErrorBoundary常见问题内容已完成
export default commonQuestions;