import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'React ErrorBoundary是什么？它解决了什么问题？请简述其基本用法。',
    difficulty: 'easy',
    frequency: 'high',
    category: '基本概念',
    answer: {
      brief: 'ErrorBoundary是React 16引入的错误处理机制，用于捕获子组件树中的JavaScript错误并显示降级UI，防止整个应用崩溃。',
      detailed: `ErrorBoundary（错误边界）是React中专门用于处理组件树中JavaScript错误的特殊组件。

**解决的核心问题：**
1. **应用稳定性**：防止单个组件错误导致整个React应用崩溃和白屏
2. **用户体验**：在错误发生时显示友好的降级UI而不是空白页面
3. **错误隔离**：将错误影响范围限制在特定的组件子树内
4. **错误监控**：提供统一的错误捕获和上报机制

**基本实现原理：**
ErrorBoundary是一个类组件，通过定义两个生命周期方法来工作：
- \`getDerivedStateFromError()\`：更新state以渲染降级UI
- \`componentDidCatch()\`：记录错误信息和执行副作用

**使用限制：**
- 只能捕获子组件的错误，不能捕获自身错误
- 无法捕获事件处理器、异步代码、服务端渲染等场景的错误`,
      code: `// 基本ErrorBoundary实现
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    // 更新state以显示降级UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // 记录错误信息
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // 降级UI
      return <h1>Something went wrong.</h1>;
    }

    return this.props.children;
  }
}

// 使用方式
function App() {
  return (
    <ErrorBoundary>
      <Header />
      <Main />
      <Footer />
    </ErrorBoundary>
  );
}`
    },
    tags: ['基础概念', '错误处理']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'getDerivedStateFromError和componentDidCatch有什么区别？为什么需要两个生命周期方法？请实现一个带重试功能的ErrorBoundary。',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现细节',
    answer: {
      brief: 'getDerivedStateFromError用于状态更新（render阶段），componentDidCatch用于副作用操作（commit阶段）。前者更新state显示降级UI，后者记录错误信息。',
      detailed: `**两个方法的核心区别：**

**getDerivedStateFromError(error)**
- **执行阶段**：render阶段（协调阶段）
- **主要作用**：根据错误更新组件state，触发降级UI渲染
- **限制**：静态方法，不能执行副作用操作（如API调用、日志记录）
- **返回值**：必须返回新的state对象或null
- **调用时机**：在子组件抛出错误后立即调用

**componentDidCatch(error, errorInfo)**
- **执行阶段**：commit阶段
- **主要作用**：执行副作用操作，如错误日志记录、错误上报
- **限制**：可以执行任何副作用操作，但不应该直接修改state
- **参数**：错误对象和包含组件堆栈的errorInfo
- **调用时机**：在错误边界组件完成状态更新后调用

**为什么需要两个方法？**
这种设计符合React Fiber架构的两阶段渲染模型：
1. **Render阶段**：计算状态变化，必须是纯函数，可中断
2. **Commit阶段**：执行副作用操作，不可中断

分离职责确保了状态更新的纯净性和副作用操作的时机正确性。`,
      code: `// 带重试功能的高级ErrorBoundary
class RetryErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // 生成错误ID用于跟踪
    const errorId = Date.now() + Math.random().toString(36);
    
    return {
      hasError: true,
      error: error,
      errorId: errorId
    };
  }

  componentDidCatch(error, errorInfo) {
    // 记录详细错误信息
    this.setState({ errorInfo });
    
    // 发送错误报告到监控系统
    this.reportError(error, errorInfo);
  }

  reportError = (error, errorInfo) => {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      retryCount: this.state.retryCount,
      errorId: this.state.errorId,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // 发送到错误监控服务
    if (typeof this.props.onError === 'function') {
      this.props.onError(errorReport);
    }
  };

  handleRetry = () => {
    const maxRetries = this.props.maxRetries || 3;
    
    if (this.state.retryCount < maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1
      }));
    }
  };

  render() {
    if (this.state.hasError) {
      const maxRetries = this.props.maxRetries || 3;
      const canRetry = this.state.retryCount < maxRetries;

      return (
        <div className="error-boundary">
          <h2>出现了一个错误</h2>
          <details style={{ marginBottom: '16px' }}>
            <summary>错误详情</summary>
            <pre>{this.state.error && this.state.error.toString()}</pre>
          </details>
          
          <div className="error-actions">
            {canRetry && (
              <button onClick={this.handleRetry}>
                重试 ({maxRetries - this.state.retryCount} 次剩余)
              </button>
            )}
            <button onClick={() => window.location.reload()}>
              刷新页面
            </button>
          </div>
          
          <p>错误ID: {this.state.errorId}</p>
        </div>
      );
    }

    return this.props.children;
  }
}

// 使用示例
<RetryErrorBoundary 
  maxRetries={3}
  onError={(errorReport) => {
    // 发送到监控系统
    analytics.track('component_error', errorReport);
  }}
>
  <ComplexComponent />
</RetryErrorBoundary>`
    },
    tags: ['生命周期', '错误处理']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 3,
    question: '在大型React应用中如何设计多层ErrorBoundary架构？如何处理ErrorBoundary无法捕获的错误类型？请设计一个完整的错误处理策略。',
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    answer: {
      brief: '采用分层ErrorBoundary架构，根据组件重要性设置不同级别的错误边界。结合全局错误处理器、Promise rejection监听等处理ErrorBoundary无法捕获的错误。',
      detailed: `**多层ErrorBoundary架构设计：**

**1. 架构层级划分**
- **应用级边界**：最顶层，捕获整个应用的致命错误
- **页面级边界**：路由页面级别，防止页面间错误传播
- **功能模块边界**：重要功能模块，如购物车、支付等
- **组件级边界**：细粒度组件，如图表、列表项等

**2. 错误严重性分级**
- **Critical**：影响核心业务流程，需要立即处理
- **High**：影响重要功能，但不阻塞主流程
- **Medium**：影响用户体验，可延后处理
- **Low**：仅影响非核心功能

**3. ErrorBoundary无法捕获的错误类型及处理策略**
- **事件处理器错误**：使用try-catch包装
- **异步代码错误**：Promise.catch()和async/await错误处理
- **定时器错误**：setTimeout/setInterval的try-catch
- **全局错误**：window.onerror和unhandledrejection事件

**4. 完整错误处理策略核心原则**
- **优雅降级**：不同级别的错误提供不同的降级方案
- **错误隔离**：防止错误在组件树中传播
- **监控告警**：实时错误监控和告警机制
- **恢复机制**：自动重试和手动恢复选项`,
      code: `// 企业级多层ErrorBoundary架构实现
import React from 'react';

// 错误严重性枚举
const ErrorSeverity = {
  CRITICAL: 'critical',
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
};

// 通用ErrorBoundary基类
class BaseErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({ errorInfo });
    this.handleError(error, errorInfo);
  }

  handleError = (error, errorInfo) => {
    const errorReport = {
      level: this.props.level || ErrorSeverity.MEDIUM,
      component: this.props.componentName || 'Unknown',
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userId: this.props.userId,
      sessionId: this.props.sessionId
    };

    // 发送到错误监控系统
    ErrorMonitor.report(errorReport);

    // 根据严重性触发不同处理
    this.handleBySeverity(errorReport);
  };

  handleBySeverity = (errorReport) => {
    switch (errorReport.level) {
      case ErrorSeverity.CRITICAL:
        // 关键错误：立即告警，可能需要回滚
        AlertSystem.sendCritical(errorReport);
        break;
      case ErrorSeverity.HIGH:
        // 高级错误：快速响应
        AlertSystem.sendHigh(errorReport);
        break;
      default:
        // 记录到日志系统
        Logger.error(errorReport);
    }
  };

  render() {
    if (this.state.hasError) {
      return this.props.fallback || this.renderDefaultFallback();
    }
    return this.props.children;
  }

  renderDefaultFallback() {
    return (
      <div className={\`error-boundary error-\${this.props.level}\`}>
        <h3>抱歉，出现了问题</h3>
        <button onClick={() => this.setState({ hasError: false })}>
          重试
        </button>
      </div>
    );
  }
}

// 应用级ErrorBoundary
class AppErrorBoundary extends BaseErrorBoundary {
  renderDefaultFallback() {
    return (
      <div className="app-error">
        <h1>应用遇到问题</h1>
        <p>请刷新页面或联系技术支持</p>
        <button onClick={() => window.location.reload()}>
          刷新页面
        </button>
      </div>
    );
  }
}

// 页面级ErrorBoundary
class PageErrorBoundary extends BaseErrorBoundary {
  renderDefaultFallback() {
    return (
      <div className="page-error">
        <h2>页面加载失败</h2>
        <button onClick={() => this.props.onRetry?.()}>
          重新加载
        </button>
      </div>
    );
  }
}

// 全局错误处理器
class GlobalErrorHandler {
  static init() {
    // 处理ErrorBoundary无法捕获的错误
    
    // 1. 全局JavaScript错误
    window.onerror = (message, filename, lineno, colno, error) => {
      this.handleGlobalError({
        type: 'javascript',
        message,
        filename,
        lineno,
        colno,
        error
      });
    };

    // 2. Promise rejection错误
    window.addEventListener('unhandledrejection', (event) => {
      this.handleGlobalError({
        type: 'promise',
        reason: event.reason,
        promise: event.promise
      });
    });

    // 3. 资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.handleGlobalError({
          type: 'resource',
          element: event.target,
          source: event.target.src || event.target.href
        });
      }
    }, true);
  }

  static handleGlobalError(errorInfo) {
    ErrorMonitor.report({
      ...errorInfo,
      level: ErrorSeverity.HIGH,
      timestamp: new Date().toISOString()
    });
  }
}

// 使用示例：多层架构
function App() {
  return (
    <AppErrorBoundary level={ErrorSeverity.CRITICAL} componentName="App">
      <Router>
        <Routes>
          <Route path="/dashboard" element={
            <PageErrorBoundary level={ErrorSeverity.HIGH} componentName="Dashboard">
              <Dashboard />
            </PageErrorBoundary>
          } />
          <Route path="/profile" element={
            <PageErrorBoundary level={ErrorSeverity.MEDIUM} componentName="Profile">
              <Profile />
            </PageErrorBoundary>
          } />
        </Routes>
      </Router>
    </AppErrorBoundary>
  );
}

// 初始化全局错误处理
GlobalErrorHandler.init();`
    },
    tags: ['架构设计', '错误策略']
  }
];

// ErrorBoundary面试问题内容已完成
export default interviewQuestions;