import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'scenario-1',
    title: '电商平台商品详情页错误恢复机制',
    description: '在电商平台的商品详情页，当产品图片、评论或推荐组件出现JavaScript错误时，使用ErrorBoundary提供降级UI',
    businessValue: '确保商品页面核心功能正常，避免单个组件错误影响整个购买流程，提升用户购买转化率',
    scenario: '用户浏览商品详情时，图片预览组件因数据格式问题崩溃，ErrorBoundary捕获错误并显示默认图片，保证购买按钮和基本信息正常显示',
    code: `// 电商商品详情页ErrorBoundary实现
class ProductErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, errorType: null };
  }

  static getDerivedStateFromError(error) {
    return { 
      hasError: true, 
      errorType: error.name || 'Unknown'
    };
  }

  componentDidCatch(error, errorInfo) {
    // 记录错误到监控系统
    analytics.track('product_component_error', {
      error: error.message,
      componentStack: errorInfo.componentStack,
      productId: this.props.productId
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <div className="error-icon">⚠️</div>
          <p>商品信息暂时无法显示</p>
          <button 
            onClick={() => this.setState({ hasError: false })}
            className="retry-button"
          >
            重新加载
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// 商品详情页使用ErrorBoundary
function ProductDetailPage({ productId }) {
  return (
    <div className="product-page">
      <ProductErrorBoundary productId={productId}>
        <ProductImageGallery productId={productId} />
      </ProductErrorBoundary>
      
      <ProductErrorBoundary productId={productId}>
        <ProductReviews productId={productId} />
      </ProductErrorBoundary>
      
      <ProductErrorBoundary productId={productId}>
        <RecommendedProducts productId={productId} />
      </ProductErrorBoundary>
      
      {/* 核心购买功能不包在ErrorBoundary内，确保始终可用 */}
      <ProductPurchaseSection productId={productId} />
    </div>
  );
}`,
    explanation: 'ErrorBoundary将可能出错的非核心组件隔离，确保购买功能始终可用。同时提供重试机制和错误监控，帮助快速恢复和问题定位。',
    benefits: [
      '商品页面可用性提升95%，避免因组件错误导致的白屏',
      '购买转化率保持稳定，核心功能不受非核心组件影响',
      '错误监控数据帮助快速定位和修复问题，降低故障恢复时间50%'
    ],
    metrics: {
      performance: '页面可用性从85%提升到95%，错误恢复时间<2秒',
      userExperience: '用户流失率降低30%，购买完成率保持98%以上',
      technicalMetrics: '错误影响范围限制在单个组件，系统稳定性提升40%'
    },
    difficulty: 'easy',
    tags: ['电商平台', '错误恢复', '降级UI', '用户体验']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'scenario-2',
    title: '企业级数据仪表板图表组件错误隔离',
    description: '在企业数据仪表板中，当第三方图表库或数据可视化组件出现错误时，使用ErrorBoundary确保其他图表和关键数据正常显示',
    businessValue: '保证企业决策数据的连续性，避免单个图表错误影响整个仪表板，确保关键业务指标监控不中断',
    scenario: '企业管理者查看实时业务仪表板时，某个复杂的数据可视化图表因数据格式异常崩溃，ErrorBoundary显示错误提示，其他图表继续正常工作',
    code: `// 企业仪表板图表ErrorBoundary
class ChartErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      errorDetails: null,
      retryCount: 0 
    };
  }

  static getDerivedStateFromError(error) {
    return { 
      hasError: true, 
      errorDetails: {
        message: error.message,
        timestamp: new Date().toISOString()
      }
    };
  }

  componentDidCatch(error, errorInfo) {
    // 发送错误到企业监控系统
    enterpriseMonitoring.logError({
      component: 'ChartComponent',
      chartType: this.props.chartType,
      error: error.message,
      stack: error.stack,
      props: this.props,
      userId: getCurrentUser().id,
      timestamp: new Date().toISOString()
    });
  }

  handleRetry = () => {
    if (this.state.retryCount < 3) {
      this.setState({ 
        hasError: false, 
        retryCount: this.state.retryCount + 1 
      });
    }
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="chart-error-container">
          <div className="error-header">
            <h4>{this.props.title}</h4>
            <span className="error-badge">数据暂不可用</span>
          </div>
          <div className="error-content">
            <div className="error-icon">📊</div>
            <p>图表加载失败，请稍后重试</p>
            <div className="error-actions">
              {this.state.retryCount < 3 && (
                <button onClick={this.handleRetry} className="retry-btn">
                  重试 ({3 - this.state.retryCount} 次剩余)
                </button>
              )}
              <button 
                onClick={() => downloadChartData(this.props.dataSource)}
                className="download-btn"
              >
                下载原始数据
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// 企业仪表板使用
function EnterpriseDashboard({ dashboardConfig }) {
  return (
    <div className="dashboard-grid">
      {dashboardConfig.charts.map(chart => (
        <ChartErrorBoundary
          key={chart.id}
          title={chart.title}
          chartType={chart.type}
          dataSource={chart.dataSource}
        >
          <DynamicChart config={chart} />
        </ChartErrorBoundary>
      ))}
      
      {/* 关键指标始终可见 */}
      <div className="critical-metrics">
        <KeyMetricsDisplay />
      </div>
    </div>
  );
}`,
    explanation: 'ChartErrorBoundary为每个图表提供独立的错误处理，包含重试机制和数据下载功能。错误信息发送到企业监控系统，帮助IT团队快速响应。',
    benefits: [
      '仪表板整体可用性提升80%，单个图表错误不影响其他组件',
      '错误恢复机制减少用户等待时间，重试成功率达到85%',
      '企业决策连续性得到保障，关键指标监控不中断',
      '详细错误日志帮助快速定位问题，运维响应时间缩短60%'
    ],
    metrics: {
      performance: '仪表板加载成功率从70%提升到95%，图表渲染错误率降低75%',
      userExperience: '用户满意度提升45%，数据访问中断时间减少80%',
      technicalMetrics: '系统稳定性提升50%，错误隔离成功率99%'
    },
    difficulty: 'medium',
    tags: ['企业应用', '数据可视化', '错误隔离', '监控系统']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'scenario-3',
    title: '金融交易平台关键组件错误风险控制',
    description: '在金融交易平台中，使用多层ErrorBoundary对交易执行、风险监控、资金管理等关键组件进行错误隔离和风险控制',
    businessValue: '确保金融交易系统的高可用性和安全性，防止单个组件错误导致交易中断或资金损失，满足金融监管要求',
    scenario: '交易员执行大额交易时，市场数据组件因API异常崩溃，ErrorBoundary立即隔离错误并启用备用数据源，确保交易决策和风控系统正常运行',
    code: `// 金融交易平台多层ErrorBoundary
class CriticalErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      errorSeverity: 'low',
      emergencyMode: false 
    };
  }

  static getDerivedStateFromError(error) {
    // 根据错误类型判断严重程度
    const severity = error.name === 'SecurityError' ? 'critical' : 
                    error.name === 'NetworkError' ? 'high' : 'medium';
    
    return { 
      hasError: true, 
      errorSeverity: severity,
      emergencyMode: severity === 'critical'
    };
  }

  componentDidCatch(error, errorInfo) {
    const errorReport = {
      timestamp: new Date().toISOString(),
      component: this.props.componentName,
      severity: this.state.errorSeverity,
      error: error.message,
      stack: error.stack,
      userSession: getCurrentTraderSession(),
      tradingContext: getCurrentTradingContext(),
      riskLevel: this.props.riskLevel || 'unknown'
    };

    // 立即上报到风控系统
    riskManagement.reportComponentError(errorReport);
    
    // 关键组件错误触发应急流程
    if (this.state.errorSeverity === 'critical') {
      emergencyProtocols.activate({
        component: this.props.componentName,
        trader: getCurrentTrader(),
        error: errorReport
      });
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.state.emergencyMode) {
        return (
          <div className="emergency-error">
            <div className="critical-alert">
              <h2>🚨 系统安全警告</h2>
              <p>检测到安全相关错误，交易已暂停</p>
              <button onClick={() => contactSupport()}>
                联系技术支持
              </button>
            </div>
          </div>
        );
      }

      return (
        <div className="trading-error-fallback">
          <div className="error-indicator">
            <span className="severity-badge severity-{this.state.errorSeverity}">
              {this.state.errorSeverity}
            </span>
            <h4>{this.props.componentName} 暂不可用</h4>
          </div>
          <div className="fallback-actions">
            {this.props.fallbackComponent && (
              <div className="backup-system">
                <p>正在使用备用系统...</p>
                {this.props.fallbackComponent}
              </div>
            )}
            <button 
              onClick={() => this.setState({ hasError: false })}
              className="system-retry"
            >
              重新连接
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// 交易平台架构
function TradingPlatform() {
  return (
    <div className="trading-workspace">
      {/* 最高级别：整个交易系统 */}
      <CriticalErrorBoundary 
        componentName="TradingSystem"
        riskLevel="critical"
      >
        {/* 二级：关键功能模块 */}
        <div className="trading-modules">
          <CriticalErrorBoundary 
            componentName="OrderExecution"
            riskLevel="critical"
            fallbackComponent={<ManualOrderEntry />}
          >
            <AutomatedTradingEngine />
          </CriticalErrorBoundary>

          <CriticalErrorBoundary 
            componentName="RiskMonitoring"
            riskLevel="critical"
            fallbackComponent={<BasicRiskDisplay />}
          >
            <RealTimeRiskAnalysis />
          </CriticalErrorBoundary>

          <CriticalErrorBoundary 
            componentName="MarketData"
            riskLevel="high"
            fallbackComponent={<BackupDataFeed />}
          >
            <LiveMarketDataStream />
          </CriticalErrorBoundary>
        </div>

        {/* 三级：辅助功能 */}
        <div className="auxiliary-modules">
          <CriticalErrorBoundary componentName="Charts" riskLevel="low">
            <TradingCharts />
          </CriticalErrorBoundary>
          
          <CriticalErrorBoundary componentName="News" riskLevel="low">
            <MarketNewsFeeds />
          </CriticalErrorBoundary>
        </div>
      </CriticalErrorBoundary>
    </div>
  );
}`,
    explanation: '多层ErrorBoundary架构确保关键交易功能的高可用性。根据组件重要性设置不同的风险级别和降级策略，critical级别错误触发应急流程，high级别启用备用系统。',
    benefits: [
      '交易系统可用性达到99.9%，满足金融监管要求',
      '错误隔离机制防止单点故障导致系统瘫痪，风险控制能力提升90%',
      '多级降级策略确保关键功能在异常情况下仍可使用',
      '实时错误监控和应急响应机制，故障恢复时间缩短至秒级',
      '备用系统自动切换，交易中断时间减少95%'
    ],
    metrics: {
      performance: '系统可用性99.9%，交易执行成功率99.95%，故障恢复时间<30秒',
      userExperience: '交易员操作连续性提升98%，系统响应时间保持<100ms',
      technicalMetrics: '错误隔离成功率100%，备用系统切换时间<5秒，风险事件响应时间<10秒'
    },
    difficulty: 'hard',
    tags: ['金融交易', '风险控制', '高可用性', '安全监控', '应急响应']
  }
];

// ErrorBoundary业务场景内容已完成
export default businessScenarios;