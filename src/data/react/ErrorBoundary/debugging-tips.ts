import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: "ErrorBoundary调试需要理解React的错误传播机制。本指南提供系统性的故障排除方法，帮助快速定位和解决ErrorBoundary相关问题。",

  troubleshooting: [
    {
      symptom: "ErrorBoundary没有捕获到预期的错误",
      possibleCauses: [
        "错误发生在事件处理器中，ErrorBoundary无法捕获",
        "错误是异步错误（Promise、setTimeout等）",
        "ErrorBoundary本身出现了错误",
        "错误发生在ErrorBoundary的同级或父级组件中"
      ],
      solutions: [
        "使用try-catch包装事件处理器",
        "添加window.onerror和unhandledrejection监听器",
        "检查ErrorBoundary实现是否正确",
        "调整ErrorBoundary的层级位置"
      ]
    },
    {
      symptom: "ErrorBoundary捕获错误后页面仍然白屏",
      possibleCauses: [
        "降级UI组件本身有渲染错误",
        "getDerivedStateFromError返回了无效的state",
        "componentDidCatch中执行了会抛出错误的代码"
      ],
      solutions: [
        "简化降级UI，使用最基本的HTML元素",
        "确保getDerivedStateFromError返回有效state对象",
        "将componentDidCatch中的操作包装在try-catch中"
      ]
    },
    {
      symptom: "开发环境下看不到ErrorBoundary的效果",
      possibleCauses: [
        "React开发模式会显示错误覆盖层",
        "热重载干扰了ErrorBoundary状态",
        "StrictMode导致的双重渲染问题"
      ],
      solutions: [
        "点击错误覆盖层的关闭按钮查看降级UI",
        "在生产构建中测试ErrorBoundary",
        "临时移除StrictMode进行测试"
      ]
    }
  ],

  debuggingTools: [
    {
      name: "React DevTools",
      description: "查看ErrorBoundary的state变化和组件树结构",
      usage: "在Components面板中定位ErrorBoundary组件，观察hasError状态变化"
    },
    {
      name: "Chrome DevTools Console",
      description: "查看componentDidCatch记录的错误信息",
      usage: "检查console.error输出，包含完整的错误堆栈和组件堆栈"
    },
    {
      name: "Error Boundary测试工具",
      description: "专门用于测试ErrorBoundary的开发工具",
      usage: "使用react-error-boundary库的useErrorHandler Hook进行受控错误测试"
    }
  ],

  commonMistakes: [
    {
      mistake: "在getDerivedStateFromError中执行副作用操作",
      explanation: "getDerivedStateFromError是静态方法，在render阶段执行，不允许副作用",
      correctApproach: "将副作用操作移至componentDidCatch方法中"
    },
    {
      mistake: "ErrorBoundary包装层级过深或过浅",
      explanation: "层级不当会导致错误捕获范围不合理或性能问题",
      correctApproach: "在功能模块级别设置ErrorBoundary，平衡错误隔离和性能"
    },
    {
      mistake: "忘记在生产环境中测试ErrorBoundary",
      explanation: "开发环境的错误覆盖层会掩盖ErrorBoundary的真实表现",
      correctApproach: "使用生产构建测试ErrorBoundary的实际效果"
    }
  ],

  performanceAnalysis: [
    {
      metric: "错误处理延迟",
      description: "从错误发生到显示降级UI的时间",
      tools: ["Performance.now()", "React DevTools Profiler"],
      interpretation: "正常应在100ms内完成，超过则需优化componentDidCatch逻辑"
    },
    {
      metric: "内存泄漏检测",
      description: "检查ErrorBoundary是否导致内存泄漏",
      tools: ["Chrome DevTools Memory", "React DevTools Profiler"],
      interpretation: "重复触发错误后内存应回归正常，持续增长表明有泄漏"
    }
  ]
};

// ErrorBoundary调试技巧内容已完成
export default debuggingTips;