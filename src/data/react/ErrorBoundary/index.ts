import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ErrorBoundaryData: ApiItem = {
  id: 'ErrorBoundary',
  title: 'Error Boundaries',
  description: 'ErrorBoundary是React中用于捕获和处理组件树中JavaScript错误的错误边界类组件',
  category: 'React Concepts',
  difficulty: 'medium',
  
  syntax: `class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('ErrorBoundary captured an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <h1>Something went wrong.</h1>;
    }
    return this.props.children;
  }
}`,
  example: `function ErrorBoundaryExample() {
  // 错误边界组件，捕获子组件错误
  const [hasError, setHasError] = useState(false);

  return (
    <div>
      {/* 错误边界包裹可能出错的组件 */}
      <ErrorBoundary>
        <ProblematicComponent 
          data={complexData}
          onError={() => setHasError(true)}
        >
          这里是可能出错的子组件内容
        </ProblematicComponent>
      </ErrorBoundary>
    </div>
  );
}`,
  notes: '只能捕获子组件的错误，无法捕获自身错误、事件处理器错误或异步代码错误',
  
  version: 'React 16.0.0+',
  tags: ["React","ErrorBoundary","ErrorHandling","Concepts"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ErrorBoundaryData;