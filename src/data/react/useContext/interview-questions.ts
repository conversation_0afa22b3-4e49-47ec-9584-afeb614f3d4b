import { InterviewQuestion } from "@/types/api";

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: "为什么使用 Context 时所有消费组件都会重新渲染？如何优化？",
    answer: `Context 的重新渲染机制和优化方案：

**重新渲染原因**：
1. Context 没有内置的细粒度更新机制
2. Provider 的 value 变化时，React 会标记所有消费该 Context 的组件
3. 即使组件只使用了 Context 中的部分数据，也会重新渲染
4. Context 更新会绕过 React.memo 的优化

**优化方案**：
1. **拆分 Context**：将不同更新频率的数据分到不同的 Context
2. **使用 useMemo**：缓存 Provider 的 value 对象
3. **组件拆分**：将消费 Context 的部分抽取为独立组件
4. **使用状态管理库**：对于复杂状态，考虑使用 Redux、Zustand 等`,
    
    code: `// ❌ 问题：整个对象作为 value，任何属性变化都会导致重新渲染
function BadProvider({ children }) {
  const [user, setUser] = useState(null);
  const [theme, setTheme] = useState('light');
  const [lang, setLang] = useState('zh');
  
  // 每次渲染都创建新对象
  const value = { user, theme, lang, setUser, setTheme, setLang };
  
  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

// ✅ 优化方案1：拆分 Context
const UserContext = createContext();
const ThemeContext = createContext();
const LangContext = createContext();

function OptimizedProviders({ children }) {
  const [user, setUser] = useState(null);
  const [theme, setTheme] = useState('light');
  const [lang, setLang] = useState('zh');
  
  // 使用 useMemo 缓存 value
  const userValue = useMemo(() => ({ user, setUser }), [user]);
  const themeValue = useMemo(() => ({ theme, setTheme }), [theme]);
  const langValue = useMemo(() => ({ lang, setLang }), [lang]);
  
  return (
    <UserContext.Provider value={userValue}>
      <ThemeContext.Provider value={themeValue}>
        <LangContext.Provider value={langValue}>
          {children}
        </LangContext.Provider>
      </ThemeContext.Provider>
    </UserContext.Provider>
  );
}

// ✅ 优化方案2：组件拆分 + React.memo
const UserInfo = React.memo(() => {
  const { user } = useContext(UserContext);
  console.log('UserInfo render');
  return <div>{user?.name}</div>;
});

const ThemeToggle = React.memo(() => {
  const { theme, setTheme } = useContext(ThemeContext);
  console.log('ThemeToggle render');
  return (
    <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
      {theme}
    </button>
  );
});

// ✅ 优化方案3：使用订阅模式的自定义 Hook
function createSubscribableContext<T>() {
  const Context = createContext<{
    subscribe: (listener: () => void) => () => void;
    getState: () => T;
    setState: (value: T | ((prev: T) => T)) => void;
  } | null>(null);
  
  function Provider({ children, initialValue }: { children: ReactNode; initialValue: T }) {
    const [state, setState] = useState(initialValue);
    const listeners = useRef(new Set<() => void>());
    
    const contextValue = useMemo(() => ({
      subscribe: (listener: () => void) => {
        listeners.current.add(listener);
        return () => listeners.current.delete(listener);
      },
      getState: () => state,
      setState: (value: T | ((prev: T) => T)) => {
        setState(value);
        listeners.current.forEach(listener => listener());
      }
    }), []);
    
    return <Context.Provider value={contextValue}>{children}</Context.Provider>;
  }
  
  function useSelector<S>(selector: (state: T) => S) {
    const context = useContext(Context);
    if (!context) throw new Error('Missing Provider');
    
    const [selectedState, setSelectedState] = useState(() => 
      selector(context.getState())
    );
    
    useEffect(() => {
      const updateState = () => {
        const newState = selector(context.getState());
        setSelectedState(prev => 
          Object.is(prev, newState) ? prev : newState
        );
      };
      
      return context.subscribe(updateState);
    }, [context, selector]);
    
    return selectedState;
  }
  
  return { Provider, useSelector };
}`,
    
    difficulty: "hard",
    frequency: "high",
    category: "性能优化"
  },
  {
    id: 2,
    question: "如何处理多个 Context 的组合使用？有什么最佳实践？",
    answer: `多个 Context 的组合使用策略：

**常见场景**：
1. 不同类型的全局状态（用户、主题、语言等）
2. 功能模块的状态隔离
3. 避免单一大 Context 的性能问题

**最佳实践**：
1. **按功能拆分**：每个 Context 负责单一职责
2. **创建组合 Provider**：封装多个 Provider 的嵌套
3. **自定义 Hook 封装**：提供统一的使用接口
4. **避免过度嵌套**：使用组合模式减少嵌套层级`,
    
    code: `// 1. 定义多个独立的 Context
const AuthContext = createContext();
const ThemeContext = createContext();
const ConfigContext = createContext();

// 2. 创建独立的 Provider
function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const login = useCallback(async (credentials) => {
    const user = await authService.login(credentials);
    setUser(user);
  }, []);
  
  const value = useMemo(() => ({ user, login }), [user, login]);
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// 3. 组合 Provider（方式一：手动嵌套）
function AppProviders({ children }) {
  return (
    <AuthProvider>
      <ThemeProvider>
        <ConfigProvider>
          {children}
        </ConfigProvider>
      </ThemeProvider>
    </AuthProvider>
  );
}

// 4. 组合 Provider（方式二：reduce 模式）
function combineProviders(...providers) {
  return ({ children }) => 
    providers.reduceRight(
      (acc, Provider) => <Provider>{acc}</Provider>,
      children
    );
}

const AppProvider = combineProviders(
  AuthProvider,
  ThemeProvider,
  ConfigProvider
);

// 5. 创建 Hook 集合
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error('useAuth must be used within AuthProvider');
  return context;
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) throw new Error('useTheme must be used within ThemeProvider');
  return context;
};

// 6. 高级：创建 Context 工厂
function createContextWithHook<T>(name: string) {
  const Context = createContext<T | undefined>(undefined);
  
  function Provider({ children, value }: { children: ReactNode; value: T }) {
    return <Context.Provider value={value}>{children}</Context.Provider>;
  }
  
  function useContextHook() {
    const context = useContext(Context);
    if (context === undefined) {
      throw new Error("use" + name + " must be used within " + name + "Provider");
    }
    return context;
  }
  
  return { Provider, useContext: useContextHook };
}

// 使用工厂创建
const { Provider: UserProvider, useContext: useUser } = createContextWithHook<UserContextType>('User');
const { Provider: CartProvider, useContext: useCart } = createContextWithHook<CartContextType>('Cart');

// 7. 跨 Context 通信
function CrossContextComponent() {
  const { user } = useAuth();
  const { theme } = useTheme();
  const { updateConfig } = useConfig();
  
  // 根据用户偏好自动设置主题
  useEffect(() => {
    if (user?.preferences?.theme) {
      updateConfig({ theme: user.preferences.theme });
    }
  }, [user?.preferences?.theme, updateConfig]);
  
  return <div>Cross-context communication example</div>;
}`,
    
    difficulty: "medium",
    frequency: "high",
    category: "架构设计"
  },
  {
    id: 3,
    question: "Context 和 Redux 等状态管理库相比有什么优缺点？什么时候用哪个？",
    answer: `Context vs Redux 的对比分析：

**Context 优点**：
1. React 内置，无需额外依赖
2. API 简单，学习成本低
3. 适合中小型应用的状态管理
4. 与 React 生态深度集成

**Context 缺点**：
1. 性能优化困难，容易过度渲染
2. 缺少中间件机制
3. 没有时间旅行调试
4. 状态更新不够灵活

**Redux 优点**：
1. 可预测的状态管理
2. 强大的开发工具
3. 中间件生态丰富
4. 细粒度的性能优化

**Redux 缺点**：
1. 样板代码多
2. 学习曲线陡峭
3. 需要额外依赖

**选择建议**：
- **使用 Context**：简单的全局状态、主题切换、用户认证、小型应用
- **使用 Redux**：复杂状态逻辑、需要中间件、大型应用、团队协作`,
    
    code: `// Context 实现简单计数器
const CountContext = createContext();

function CountProvider({ children }) {
  const [count, setCount] = useState(0);
  const increment = () => setCount(c => c + 1);
  const decrement = () => setCount(c => c - 1);
  
  return (
    <CountContext.Provider value={{ count, increment, decrement }}>
      {children}
    </CountContext.Provider>
  );
}

// Redux 实现相同功能
// 1. Actions
const INCREMENT = 'INCREMENT';
const DECREMENT = 'DECREMENT';

// 2. Reducer
function counterReducer(state = { count: 0 }, action) {
  switch (action.type) {
    case INCREMENT:
      return { count: state.count + 1 };
    case DECREMENT:
      return { count: state.count - 1 };
    default:
      return state;
  }
}

// 3. Store
const store = createStore(counterReducer);

// 复杂场景对比：异步操作 + 错误处理
// Context 版本
function DataProvider({ children }) {
  const [state, setState] = useState({
    data: null,
    loading: false,
    error: null
  });
  
  const fetchData = async (id) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    try {
      const data = await api.getData(id);
      setState(prev => ({ ...prev, data, loading: false }));
    } catch (error) {
      setState(prev => ({ ...prev, error, loading: false }));
    }
  };
  
  return (
    <DataContext.Provider value={{ ...state, fetchData }}>
      {children}
    </DataContext.Provider>
  );
}

// Redux + Redux Toolkit 版本
const dataSlice = createSlice({
  name: 'data',
  initialState: {
    data: null,
    loading: false,
    error: null
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchData.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      });
  }
});

const fetchData = createAsyncThunk('data/fetch', async (id) => {
  const response = await api.getData(id);
  return response;
});

// 性能优化对比
// Context：需要手动优化
const OptimizedContext = () => {
  const [state, setState] = useState();
  
  // 拆分 Context
  const stateValue = useMemo(() => state, [state]);
  const actionsValue = useMemo(() => ({ setState }), []);
  
  return (
    <StateContext.Provider value={stateValue}>
      <ActionsContext.Provider value={actionsValue}>
        {children}
      </ActionsContext.Provider>
    </StateContext.Provider>
  );
};

// Redux：使用 useSelector 自动优化
function ReduxComponent() {
  // 只有 count 变化时才重新渲染
  const count = useSelector(state => state.counter.count);
  const dispatch = useDispatch();
  
  return <div>{count}</div>;
}`,
    
    difficulty: "medium",
    frequency: "high",
    category: "状态管理"
  }
];

export default interviewQuestions; 