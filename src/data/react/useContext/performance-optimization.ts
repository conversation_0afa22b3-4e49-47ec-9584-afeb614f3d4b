import { PerformanceOptimization } from '@/types/api';

export const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: "避免不必要的重渲染",
      description: "Context值变化会导致所有消费组件重渲染，需要合理优化",
      techniques: [
        {
          name: "拆分Context",
          description: "将频繁变化和不常变化的数据分离到不同的Context中",
          impact: "high",
          difficulty: "easy",
          code: `// ❌ 错误：所有数据在一个Context中
const AppContext = createContext({
  user: null,
  theme: 'light',
  notifications: [],
  settings: {}
});

// 任何数据变化都会导致所有消费者重渲染

// ✅ 正确：拆分Context
const UserContext = createContext(null);
const ThemeContext = createContext('light');
const NotificationContext = createContext([]);
const SettingsContext = createContext({});

// 组件只订阅需要的Context
function Header() {
  const user = useContext(UserContext);
  const theme = useContext(ThemeContext);
  // 不会因为notifications变化而重渲染
}`
        },
        {
          name: "使用memo优化消费组件",
          description: "使用React.memo包装Context消费组件，配合useMemo优化",
          impact: "high",
          difficulty: "medium",
          code: `// ❌ 没有优化的组件
function ExpensiveChild() {
  const { value } = useContext(MyContext);
  
  // 复杂的渲染逻辑
  return <ComplexComponent data={value} />;
}

// ✅ 使用memo优化
const ExpensiveChild = React.memo(() => {
  const { value } = useContext(MyContext);
  
  // 即使Context其他值变化，只要value不变就不重渲染
  return <ComplexComponent data={value} />;
});

// ✅ 更精细的控制
const OptimizedChild = React.memo(
  ({ data }) => <ComplexComponent data={data} />,
  (prevProps, nextProps) => {
    // 自定义比较逻辑
    return prevProps.data.id === nextProps.data.id;
  }
);`
        },
        {
          name: "Provider值的稳定性",
          description: "确保Provider的value引用稳定，避免不必要的重渲染",
          impact: "high",
          difficulty: "easy",
          code: `// ❌ 错误：每次渲染都创建新对象
function App() {
  const [user, setUser] = useState(null);
  
  return (
    <UserContext.Provider value={{ user, setUser }}>
      {children}
    </UserContext.Provider>
  );
  // value对象每次都是新的，导致所有消费者重渲染
}

// ✅ 正确：使用useMemo保持引用稳定
function App() {
  const [user, setUser] = useState(null);
  
  const contextValue = useMemo(
    () => ({ user, setUser }),
    [user] // 只有user变化时才创建新对象
  );
  
  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
}`
        }
      ]
    },
    {
      title: "Context选择器模式",
      description: "实现类似Redux的selector模式，只订阅需要的部分数据",
      techniques: [
        {
          name: "自定义Context选择器Hook",
          description: "创建选择器Hook，只在选中的数据变化时重渲染",
          impact: "high",
          difficulty: "hard",
          code: `// 实现一个Context选择器
function createContextSelector(context) {
  return function useContextSelector(selector) {
    const value = useContext(context);
    const selectedRef = useRef();
    const [, forceRender] = useReducer(x => x + 1, 0);
    
    const selected = selector(value);
    
    useLayoutEffect(() => {
      if (!Object.is(selectedRef.current, selected)) {
        selectedRef.current = selected;
        forceRender();
      }
    });
    
    return selected;
  };
}

// 使用示例
const StateContext = createContext({ count: 0, text: '' });
const useStateSelector = createContextSelector(StateContext);

function Counter() {
  // 只在count变化时重渲染，text变化不影响
  const count = useStateSelector(state => state.count);
  
  return <div>Count: {count}</div>;
}

// 或使用第三方库：use-context-selector
import { createContext, useContextSelector } from 'use-context-selector';

const context = createContext({ a: 1, b: 2 });

function Component() {
  const a = useContextSelector(context, v => v.a);
  // 只有a变化时才重渲染
}`
        },
        {
          name: "组合多个Context",
          description: "创建高阶组件或自定义Hook组合多个Context",
          impact: "medium",
          difficulty: "medium",
          code: `// 创建一个组合多个Context的Hook
function useAppContext() {
  const user = useContext(UserContext);
  const theme = useContext(ThemeContext);
  const settings = useContext(SettingsContext);
  
  return useMemo(
    () => ({ user, theme, settings }),
    [user, theme, settings]
  );
}

// 或者创建一个选择性订阅的Hook
function useSelectiveContext(selectors) {
  const contexts = {
    user: useContext(UserContext),
    theme: useContext(ThemeContext),
    settings: useContext(SettingsContext)
  };
  
  return useMemo(() => {
    const selected = {};
    selectors.forEach(key => {
      if (key in contexts) {
        selected[key] = contexts[key];
      }
    });
    return selected;
  }, selectors.map(key => contexts[key]));
}

// 使用
function Component() {
  // 只订阅需要的Context
  const { user, theme } = useSelectiveContext(['user', 'theme']);
}`
        }
      ]
    },
    {
      title: "Context与状态管理库的结合",
      description: "将Context与其他状态管理方案结合使用",
      techniques: [
        {
          name: "Context + useReducer模式",
          description: "使用useReducer管理复杂状态，Context传递dispatch",
          impact: "high",
          difficulty: "medium",
          code: `// 创建一个高性能的状态管理方案
const StateContext = createContext();
const DispatchContext = createContext();

function StateProvider({ children }) {
  const [state, dispatch] = useReducer(reducer, initialState);
  
  // dispatch函数引用稳定，不会导致重渲染
  return (
    <StateContext.Provider value={state}>
      <DispatchContext.Provider value={dispatch}>
        {children}
      </DispatchContext.Provider>
    </StateContext.Provider>
  );
}

// 自定义Hooks
function useAppState() {
  const context = useContext(StateContext);
  if (!context) {
    throw new Error('useAppState must be used within StateProvider');
  }
  return context;
}

function useAppDispatch() {
  const context = useContext(DispatchContext);
  if (!context) {
    throw new Error('useAppDispatch must be used within StateProvider');
  }
  return context;
}

// 使用
function Component() {
  const dispatch = useAppDispatch(); // 不会因状态变化重渲染
  const { user } = useAppState(); // 只在需要时订阅状态
}`
        },
        {
          name: "懒加载Context值",
          description: "对于大型数据，实现按需加载",
          impact: "medium",
          difficulty: "hard",
          code: `// 实现懒加载的Context
function LazyDataProvider({ children }) {
  const [cache, setCache] = useState({});
  
  const loadData = useCallback(async (key) => {
    if (cache[key]) return cache[key];
    
    const data = await fetchData(key);
    setCache(prev => ({ ...prev, [key]: data }));
    return data;
  }, [cache]);
  
  const contextValue = useMemo(
    () => ({ cache, loadData }),
    [cache, loadData]
  );
  
  return (
    <LazyDataContext.Provider value={contextValue}>
      {children}
    </LazyDataContext.Provider>
  );
}

// 使用懒加载
function DataConsumer({ dataKey }) {
  const { cache, loadData } = useContext(LazyDataContext);
  const [data, setData] = useState(cache[dataKey]);
  
  useEffect(() => {
    if (!cache[dataKey]) {
      loadData(dataKey).then(setData);
    }
  }, [dataKey, cache, loadData]);
  
  if (!data) return <Loading />;
  return <DataDisplay data={data} />;
}`
        }
      ]
    }
  ],
  
  performanceMetrics: {
    renderCount: {
      tool: "React DevTools Profiler",
      description: "监控Context变化导致的渲染次数",
      example: "使用Profiler追踪Context Provider和Consumer的渲染频率"
    },
    contextSize: {
      tool: "Chrome DevTools Memory",
      description: "监控Context存储的数据大小",
      example: "检查Context value的内存占用，避免存储过大对象"
    }
  },
  
  bestPractices: [
    "将变化频率不同的数据拆分到不同的Context",
    "使用useMemo确保Provider value的引用稳定性",
    "配合React.memo优化消费组件",
    "考虑使用Context选择器模式或第三方库",
    "dispatch函数和state分离，减少不必要的渲染",
    "避免在Context中存储派生状态",
    "合理使用多个Context而不是一个大Context"
  ],
  
  commonPitfalls: [
    {
      issue: "Provider value对象每次都是新的",
      cause: "在render中直接创建对象作为value",
      solution: "使用useMemo包装value对象"
    },
    {
      issue: "所有消费者都重渲染",
      cause: "Context值包含太多不相关的数据",
      solution: "拆分Context，使用多个专门的Context"
    },
    {
      issue: "Context嵌套过深",
      cause: "过度使用Context导致Provider嵌套",
      solution: "考虑组合Provider或使用状态管理库"
    }
  ]
};

export default performanceOptimization; 