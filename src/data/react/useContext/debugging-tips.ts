import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  overview: `useContext作为React的状态共享机制，在实际使用中容易出现Provider缺失、性能问题、Context值不更新等调试难题。这些问题往往表现为组件无法获取Context值、不必要的重渲染或状态同步异常，需要系统的调试方法来快速定位和解决。

本指南提供4种核心调试策略、6个实用调试工具，以及5个预防技巧，帮助开发者掌握useContext的调试精髓，避免常见陷阱，构建高效的状态共享架构。`,
  
  troubleshooting: [
    {
      symptom: 'useContext返回undefined或默认值，无法获取Provider提供的值',
      possibleCauses: [
        '组件不在对应的Provider包裹范围内',
        'Context对象引用错误，使用了不同的Context实例',
        'Provider的value属性未正确设置'
      ],
      solutions: [
        '检查组件树结构，确保消费者组件在Provider内部',
        '验证Context对象的导入和引用是否正确',
        '使用React DevTools检查Provider的value值'
      ],
      codeExample: `// 调试useContext获取不到值的问题
import React, { createContext, useContext, useState } from 'react';

// ❌ 常见错误：多个Context实例
// 文件A: contexts/theme.js
export const ThemeContext = createContext('light');

// 文件B: contexts/theme.js (重复创建)
export const ThemeContext = createContext('light'); // 不同的实例！

// ✅ 正确：统一的Context实例
// contexts/ThemeContext.js
export const ThemeContext = createContext('light');

// 调试工具：Context值检查器
function ContextDebugger({ children }) {
  const theme = useContext(ThemeContext);
  
  // 调试信息
  console.log('🔍 Context调试信息:', {
    contextValue: theme,
    isDefaultValue: theme === 'light', // 检查是否为默认值
    providerExists: theme !== undefined,
    timestamp: new Date().toISOString()
  });
  
  // 可视化调试信息
  return (
    <div>
      <div style={{ 
        position: 'fixed', 
        top: 0, 
        right: 0, 
        background: 'yellow', 
        padding: '10px',
        fontSize: '12px',
        zIndex: 9999 
      }}>
        Context值: {JSON.stringify(theme)}
        {theme === 'light' && <div style={{color: 'red'}}>⚠️ 可能使用了默认值</div>}
      </div>
      {children}
    </div>
  );
}

// 使用示例
function App() {
  const [theme, setTheme] = useState('dark');
  
  return (
    <ThemeContext.Provider value={theme}>
      <ContextDebugger>
        <Header />
      </ContextDebugger>
    </ThemeContext.Provider>
  );
}

function Header() {
  const theme = useContext(ThemeContext);
  
  // ❌ 错误检查：如果获取到默认值，说明Provider有问题
  if (theme === 'light') {
    console.error('🚨 Context错误：获取到默认值，检查Provider设置');
  }
  
  return <h1>当前主题: {theme}</h1>;
}

// 高级调试：Context Provider检查器
function useContextWithDebug(context, contextName = 'Unknown') {
  const value = useContext(context);
  const defaultValue = context._defaultValue || context._currentValue;
  
  useEffect(() => {
    if (value === defaultValue) {
      console.warn(\`⚠️ Context "\${contextName}" 可能未被Provider包裹\`);
      console.trace('调用栈追踪');
    }
  }, [value, defaultValue, contextName]);
  
  return value;
}

// 使用调试版本的useContext
function DebuggableComponent() {
  const theme = useContextWithDebug(ThemeContext, 'ThemeContext');
  return <div>主题: {theme}</div>;
}`,
      severity: 'high'
    },
    {
      symptom: 'Context值更新但组件没有重新渲染',
      possibleCauses: [
        'Provider的value引用没有变化，使用了相同的对象引用',
        '组件被React.memo包裹但没有正确的依赖',
        'Context值的嵌套属性变化但对象引用相同'
      ],
      solutions: [
        '确保Provider的value在数据变化时创建新的对象引用',
        '检查React.memo的比较函数是否正确',
        '使用不可变更新模式，避免直接修改对象'
      ],
      codeExample: `// 调试Context值更新问题
function ContextUpdateDebugger() {
  const [user, setUser] = useState({ name: 'John', age: 25 });
  const [renderCount, setRenderCount] = useState(0);
  
  // ❌ 错误：直接修改对象，引用不变
  const updateUserBad = () => {
    user.age += 1; // 直接修改
    setUser(user); // 相同引用，Context不会更新
  };
  
  // ✅ 正确：创建新对象引用
  const updateUserGood = () => {
    setUser(prev => ({ ...prev, age: prev.age + 1 }));
  };
  
  // 渲染计数器
  useEffect(() => {
    setRenderCount(prev => prev + 1);
  });
  
  // Context值变化监控
  const contextValue = useMemo(() => {
    console.log('🔄 Context值重新创建:', user);
    return {
      user,
      updateUser: updateUserGood,
      metadata: {
        lastUpdated: Date.now(),
        renderCount
      }
    };
  }, [user, renderCount]);
  
  return (
    <UserContext.Provider value={contextValue}>
      <div>
        <h3>Provider组件 (渲染次数: {renderCount})</h3>
        <button onClick={updateUserBad}>❌ 错误更新</button>
        <button onClick={updateUserGood}>✅ 正确更新</button>
        <UserDisplay />
      </div>
    </UserContext.Provider>
  );
}

// Context值变化监控Hook
function useContextChangeMonitor(context, contextName) {
  const value = useContext(context);
  const prevValueRef = useRef();
  const renderCountRef = useRef(0);
  
  useEffect(() => {
    renderCountRef.current += 1;
    
    if (prevValueRef.current !== value) {
      console.log(\`📊 Context "\${contextName}" 值变化:\`, {
        previous: prevValueRef.current,
        current: value,
        renderCount: renderCountRef.current,
        referenceChanged: prevValueRef.current !== value,
        timestamp: new Date().toISOString()
      });
      
      prevValueRef.current = value;
    }
  });
  
  return value;
}

function UserDisplay() {
  const contextValue = useContextChangeMonitor(UserContext, 'UserContext');
  const { user, updateUser } = contextValue;
  
  return (
    <div>
      <p>用户: {user.name}, 年龄: {user.age}</p>
      <button onClick={updateUser}>增加年龄</button>
    </div>
  );
}`,
      severity: 'high'
    },
    {
      symptom: 'Context导致的性能问题，组件频繁重渲染',
      possibleCauses: [
        'Context值包含过多数据，任何变化都导致所有消费者重渲染',
        'Provider的value在每次渲染时都创建新对象',
        '没有使用React.memo优化消费者组件'
      ],
      solutions: [
        '拆分Context，将频繁变化和稳定的数据分开',
        '使用useMemo稳定Context value的引用',
        '使用React.memo包裹消费者组件'
      ],
      codeExample: `// 调试Context性能问题
function PerformanceDebugger() {
  const [user, setUser] = useState({ name: 'John', age: 25 });
  const [theme, setTheme] = useState('light');
  const [notifications, setNotifications] = useState([]);
  
  // ❌ 性能问题：所有数据放在一个Context中
  const badContextValue = {
    user,
    theme,
    notifications,
    updateUser: (newUser) => setUser(newUser),
    updateTheme: (newTheme) => setTheme(newTheme),
    addNotification: (notification) => setNotifications(prev => [...prev, notification])
  };
  
  // ✅ 性能优化：拆分Context
  const userContextValue = useMemo(() => ({
    user,
    updateUser: (newUser) => setUser(newUser)
  }), [user]);
  
  const themeContextValue = useMemo(() => ({
    theme,
    updateTheme: (newTheme) => setTheme(newTheme)
  }), [theme]);
  
  const notificationContextValue = useMemo(() => ({
    notifications,
    addNotification: (notification) => setNotifications(prev => [...prev, notification])
  }), [notifications]);
  
  return (
    <UserContext.Provider value={userContextValue}>
      <ThemeContext.Provider value={themeContextValue}>
        <NotificationContext.Provider value={notificationContextValue}>
          <PerformanceMonitor />
          <UserProfile />
          <ThemeSelector />
          <NotificationList />
        </NotificationContext.Provider>
      </ThemeContext.Provider>
    </UserContext.Provider>
  );
}

// 性能监控组件
function PerformanceMonitor() {
  const [renderStats, setRenderStats] = useState({});
  
  const updateRenderStats = useCallback((componentName) => {
    setRenderStats(prev => ({
      ...prev,
      [componentName]: (prev[componentName] || 0) + 1
    }));
  }, []);
  
  return (
    <div style={{ position: 'fixed', bottom: 0, left: 0, background: 'lightblue', padding: '10px' }}>
      <h4>渲染统计:</h4>
      {Object.entries(renderStats).map(([component, count]) => (
        <div key={component}>{component}: {count}次</div>
      ))}
    </div>
  );
}

// 使用React.memo优化的组件
const UserProfile = React.memo(() => {
  const { user } = useContext(UserContext);
  
  useEffect(() => {
    console.log('👤 UserProfile 重新渲染');
  });
  
  return <div>用户: {user.name}</div>;
});

const ThemeSelector = React.memo(() => {
  const { theme, updateTheme } = useContext(ThemeContext);
  
  useEffect(() => {
    console.log('🎨 ThemeSelector 重新渲染');
  });
  
  return (
    <button onClick={() => updateTheme(theme === 'light' ? 'dark' : 'light')}>
      切换主题: {theme}
    </button>
  );
});`,
      severity: 'medium'
    }
  ],
  
  tools: [
    {
      name: 'React DevTools',
      description: 'React官方开发工具，用于检查Context Provider和Consumer的状态',
      usage: '在Components面板中查看Context.Provider的value值，观察Context值的变化',
      category: '浏览器扩展',
      documentation: 'https://react.dev/learn/react-developer-tools'
    },
    {
      name: 'React DevTools Profiler',
      description: 'React性能分析工具，用于监控Context导致的重渲染',
      usage: '在Profiler面板中记录组件渲染，分析Context变化对性能的影响',
      category: '浏览器扩展',
      documentation: 'https://react.dev/blog/2018/09/10/introducing-the-react-profiler'
    },
    {
      name: 'Chrome DevTools Console',
      description: '浏览器控制台，用于输出Context调试信息',
      usage: '使用console.log记录Context值变化，设置断点调试Context逻辑',
      category: '浏览器工具',
      documentation: 'https://developer.chrome.com/docs/devtools/console/'
    }
  ],
  
  bestPractices: [
    '使用React DevTools检查Context Provider的层级结构和value值',
    '添加Context值变化监控，及时发现异常的更新模式',
    '使用TypeScript定义Context类型，避免类型相关的错误',
    '建立Context调试工具，快速定位Provider和Consumer的问题',
    '定期检查Context的性能影响，避免不必要的重渲染'
  ],
  
  commonMistakes: [
    {
      mistake: '在组件外部使用useContext，导致运行时错误',
      consequence: '抛出"useContext must be used within a component"错误',
      solution: '确保useContext只在函数组件或自定义Hook内部使用',
      prevention: '使用ESLint的React Hooks规则检查Hook的使用位置'
    },
    {
      mistake: '忘记提供Context Provider，组件获取到默认值',
      consequence: '组件无法获取到预期的Context值，功能异常',
      solution: '检查组件树结构，确保所有消费者都在Provider包裹范围内',
      prevention: '建立Context使用检查机制，在开发环境中警告未包裹的组件'
    },
    {
      mistake: '在Provider的value中直接传递函数，导致每次渲染都创建新函数',
      consequence: '所有消费者组件都会重新渲染，性能下降',
      solution: '使用useCallback包裹函数，或将函数定义在组件外部',
      prevention: '使用useMemo包裹Context value，确保引用稳定性'
    }
  ]
};

export default debuggingTips;
