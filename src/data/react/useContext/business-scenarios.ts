import { BusinessScenario } from "@/types/api";

const businessScenarios: BusinessScenario[] = [
  {
    title: "主题切换系统",
    difficulty: "easy",
    description: "使用 useContext 实现全局主题切换功能，支持亮色/暗色模式切换，并持久化到 localStorage",
    code: `import React, { createContext, useContext, useState, useEffect } from 'react';

// 1. 定义主题类型
interface Theme {
  mode: 'light' | 'dark';
  colors: {
    primary: string;
    background: string;
    text: string;
    border: string;
  };
}

// 2. 定义主题配置
const themes: Record<string, Theme> = {
  light: {
    mode: 'light',
    colors: {
      primary: '#3b82f6',
      background: '#ffffff',
      text: '#1f2937',
      border: '#e5e7eb'
    }
  },
  dark: {
    mode: 'dark',
    colors: {
      primary: '#60a5fa',
      background: '#1f2937',
      text: '#f3f4f6',
      border: '#374151'
    }
  }
};

// 3. 创建 Context
interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// 4. Provider 组件
export function ThemeProvider({ children }: { children: ReactNode }) {
  // 从 localStorage 读取初始主题
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>(() => {
    const saved = localStorage.getItem('theme');
    return (saved as 'light' | 'dark') || 'light';
  });

  // 主题切换函数
  const toggleTheme = () => {
    setCurrentTheme(prev => {
      const next = prev === 'light' ? 'dark' : 'light';
      localStorage.setItem('theme', next);
      return next;
    });
  };

  // 应用主题到 document
  useEffect(() => {
    const root = document.documentElement;
    const theme = themes[currentTheme];
    
    // 设置 CSS 变量
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty("--color-" + key, value);
    });
    
    // 设置 data 属性供 CSS 使用
    root.setAttribute('data-theme', currentTheme);
  }, [currentTheme]);

  const value: ThemeContextType = {
    theme: themes[currentTheme],
    toggleTheme
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

// 5. 自定义 Hook
export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within ThemeProvider');
  }
  return context;
}

// 6. 使用示例
function App() {
  return (
    <ThemeProvider>
      <Layout />
    </ThemeProvider>
  );
}

function Layout() {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <div style={{
      backgroundColor: theme.colors.background,
      color: theme.colors.text,
      minHeight: '100vh',
      padding: '20px'
    }}>
      <header style={{
        borderBottom: "1px solid " + theme.colors.border,
        paddingBottom: '20px'
      }}>
        <h1>当前主题: {theme.mode}</h1>
        <button
          onClick={toggleTheme}
          style={{
            backgroundColor: theme.colors.primary,
            color: '#ffffff',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          切换主题
        </button>
      </header>
      
      <main style={{ marginTop: '20px' }}>
        <Card />
      </main>
    </div>
  );
}

function Card() {
  const { theme } = useTheme();
  
  return (
    <div style={{
      border: "1px solid " + theme.colors.border,
      borderRadius: '8px',
      padding: '20px',
      backgroundColor: theme.mode === 'light' ? '#f9fafb' : '#111827'
    }}>
      <h2>主题感知组件</h2>
      <p>这个卡片会根据主题自动调整样式</p>
    </div>
  );
}`,
    explanation: `这个示例展示了 useContext 在主题系统中的应用：

1. **Context 设计**：定义清晰的类型接口，包含主题数据和操作方法
2. **Provider 封装**：在 Provider 中处理主题逻辑，包括持久化和 CSS 变量设置
3. **自定义 Hook**：封装 useContext，提供类型安全和错误处理
4. **全局样式应用**：通过 CSS 变量和 data 属性实现主题切换
5. **组件响应**：所有子组件都能通过 useTheme 获取主题信息`
  },
  {
    title: "用户认证状态管理",
    difficulty: "medium",
    description: "实现完整的用户认证系统，包括登录、登出、权限控制和路由保护",
    code: `import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useNavigate, Navigate } from 'react-router-dom';

// 1. 定义用户和认证类型
interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user' | 'guest';
  permissions: string[];
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  checkPermission: (permission: string) => boolean;
  updateUser: (updates: Partial<User>) => void;
}

// 2. 创建 Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 3. AuthProvider 组件
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // 初始化：检查 token 和获取用户信息
  useEffect(() => {
    async function initAuth() {
      try {
        const token = localStorage.getItem('authToken');
        if (!token) {
          setLoading(false);
          return;
        }

        // 验证 token 并获取用户信息
        const response = await fetch('/api/auth/verify', {
          headers: {
            'Authorization': 'Bearer ' + token
          }
        });

        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
        } else {
          // Token 无效，清除
          localStorage.removeItem('authToken');
        }
      } catch (err) {
        console.error('Auth initialization error:', err);
      } finally {
        setLoading(false);
      }
    }

    initAuth();
  }, []);

  // 登录函数
  const login = useCallback(async (email: string, password: string) => {
    setError(null);
    setLoading(true);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '登录失败');
      }

      const { token, user: userData } = await response.json();
      
      // 保存 token
      localStorage.setItem('authToken', token);
      
      // 设置用户信息
      setUser(userData);
      
      // 根据角色跳转
      if (userData.role === 'admin') {
        navigate('/admin');
      } else {
        navigate('/dashboard');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [navigate]);

  // 登出函数
  const logout = useCallback(async () => {
    try {
      // 调用登出 API
      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + localStorage.getItem('authToken')
        }
      });
    } catch (err) {
      console.error('Logout error:', err);
    } finally {
      // 清除本地状态
      localStorage.removeItem('authToken');
      setUser(null);
      navigate('/login');
    }
  }, [navigate]);

  // 权限检查函数
  const checkPermission = useCallback((permission: string) => {
    if (!user) return false;
    if (user.role === 'admin') return true; // 管理员拥有所有权限
    return user.permissions.includes(permission);
  }, [user]);

  // 更新用户信息
  const updateUser = useCallback((updates: Partial<User>) => {
    setUser(prev => prev ? { ...prev, ...updates } : null);
  }, []);

  const value: AuthContextType = {
    user,
    loading,
    error,
    login,
    logout,
    checkPermission,
    updateUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// 4. useAuth Hook
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
}

// 5. 路由保护组件
interface ProtectedRouteProps {
  children: ReactNode;
  requiredPermission?: string;
  fallback?: string;
}

export function ProtectedRoute({ 
  children, 
  requiredPermission,
  fallback = '/login'
}: ProtectedRouteProps) {
  const { user, loading, checkPermission } = useAuth();

  if (loading) {
    return <div>加载中...</div>;
  }

  if (!user) {
    return <Navigate to={fallback} replace />;
  }

  if (requiredPermission && !checkPermission(requiredPermission)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
}

// 6. 使用示例
function App() {
  return (
    <AuthProvider>
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } />
        <Route path="/admin" element={
          <ProtectedRoute requiredPermission="admin.access">
            <AdminPanel />
          </ProtectedRoute>
        } />
      </Routes>
    </AuthProvider>
  );
}

function LoginPage() {
  const { login, error, loading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await login(email, password);
    } catch (err) {
      // 错误已经在 context 中处理
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <h2>登录</h2>
      {error && <div className="error">{error}</div>}
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="邮箱"
        required
      />
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        placeholder="密码"
        required
      />
      <button type="submit" disabled={loading}>
        {loading ? '登录中...' : '登录'}
      </button>
    </form>
  );
}

function Dashboard() {
  const { user, logout } = useAuth();

  return (
    <div>
      <h1>欢迎, {user?.name}!</h1>
      <p>角色: {user?.role}</p>
      <button onClick={logout}>登出</button>
    </div>
  );
}`,
    explanation: `这个中级示例展示了 useContext 在认证系统中的复杂应用：

1. **状态管理**：集中管理用户信息、加载状态和错误信息
2. **副作用处理**：在 Provider 中处理 API 调用和 localStorage 操作
3. **路由集成**：与 React Router 配合实现路由保护
4. **权限控制**：实现基于角色和权限的访问控制
5. **错误处理**：统一的错误处理机制
6. **TypeScript 支持**：完整的类型定义确保类型安全`
  },
  {
    title: "多语言国际化系统",
    difficulty: "hard",
    description: "实现高性能的国际化系统，支持动态语言切换、懒加载、复数处理和格式化",
    code: `import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';

// 1. 定义类型
interface TranslationData {
  [key: string]: string | TranslationData;
}

interface Locale {
  code: string;
  name: string;
  direction: 'ltr' | 'rtl';
  dateFormat: string;
  numberFormat: {
    decimal: string;
    thousand: string;
    currency: string;
  };
}

interface I18nContextType {
  locale: Locale;
  translations: TranslationData;
  loading: boolean;
  t: (key: string, params?: Record<string, any>) => string;
  tn: (key: string, count: number, params?: Record<string, any>) => string;
  setLocale: (localeCode: string) => Promise<void>;
  formatNumber: (num: number, options?: Intl.NumberFormatOptions) => string;
  formatDate: (date: Date, options?: Intl.DateTimeFormatOptions) => string;
  formatCurrency: (amount: number, currency?: string) => string;
}

// 2. 支持的语言配置
const SUPPORTED_LOCALES: Record<string, Locale> = {
  'zh-CN': {
    code: 'zh-CN',
    name: '简体中文',
    direction: 'ltr',
    dateFormat: 'YYYY年MM月DD日',
    numberFormat: {
      decimal: '.',
      thousand: ',',
      currency: '¥'
    }
  },
  'en-US': {
    code: 'en-US',
    name: 'English',
    direction: 'ltr',
    dateFormat: 'MM/DD/YYYY',
    numberFormat: {
      decimal: '.',
      thousand: ',',
      currency: '$'
    }
  },
  'ar-SA': {
    code: 'ar-SA',
    name: 'العربية',
    direction: 'rtl',
    dateFormat: 'DD/MM/YYYY',
    numberFormat: {
      decimal: '٫',
      thousand: '٬',
      currency: 'ر.س'
    }
  }
};

// 3. 创建 Context
const I18nContext = createContext<I18nContextType | undefined>(undefined);

// 4. 翻译缓存
const translationCache = new Map<string, TranslationData>();

// 5. I18nProvider
export function I18nProvider({ children }: { children: ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>(() => {
    // 从 localStorage 或浏览器语言获取初始语言
    const saved = localStorage.getItem('locale');
    const browserLang = navigator.language;
    const defaultLocale = saved || browserLang || 'en-US';
    return SUPPORTED_LOCALES[defaultLocale] || SUPPORTED_LOCALES['en-US'];
  });

  const [translations, setTranslations] = useState<TranslationData>({});
  const [loading, setLoading] = useState(true);

  // 加载翻译文件
  const loadTranslations = useCallback(async (localeCode: string) => {
    // 检查缓存
    if (translationCache.has(localeCode)) {
      setTranslations(translationCache.get(localeCode)!);
      return;
    }

    setLoading(true);
    try {
      // 动态导入翻译文件
      const module = await import("./locales/" + localeCode + ".json");
      const data = module.default;
      
      // 缓存翻译
      translationCache.set(localeCode, data);
      setTranslations(data);
    } catch (error) {
      console.error("Failed to load translations for " + localeCode + ":", error);
      // 回退到英文
      if (localeCode !== 'en-US') {
        await loadTranslations('en-US');
      }
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始加载
  useEffect(() => {
    loadTranslations(locale.code);
  }, [locale.code, loadTranslations]);

  // 设置文档方向
  useEffect(() => {
    document.documentElement.dir = locale.direction;
    document.documentElement.lang = locale.code;
  }, [locale.direction, locale.code]);

  // 切换语言
  const setLocale = useCallback(async (localeCode: string) => {
    const newLocale = SUPPORTED_LOCALES[localeCode];
    if (!newLocale) {
      console.error("Unsupported locale: " + localeCode);
      return;
    }

    localStorage.setItem('locale', localeCode);
    setLocaleState(newLocale);
    await loadTranslations(localeCode);
  }, [loadTranslations]);

  // 获取嵌套的翻译值
  const getNestedTranslation = useCallback((obj: any, path: string): string => {
    const keys = path.split('.');
    let result = obj;
    
    for (const key of keys) {
      if (result && typeof result === 'object' && key in result) {
        result = result[key];
      } else {
        return path; // 返回原始 key 作为 fallback
      }
    }
    
    return typeof result === 'string' ? result : path;
  }, []);

  // 翻译函数
  const t = useCallback((key: string, params?: Record<string, any>): string => {
    let translation = getNestedTranslation(translations, key);
    
    // 参数替换
    if (params) {
      Object.entries(params).forEach(([param, value]) => {
        translation = translation.replace(
          new RegExp("{{\\s*" + param + "\\s*}}", 'g'),
          String(value)
        );
      });
    }
    
    return translation;
  }, [translations, getNestedTranslation]);

  // 复数翻译函数
  const tn = useCallback((key: string, count: number, params?: Record<string, any>): string => {
    const pluralKey = count === 0 ? key + ".zero" :
                     count === 1 ? key + ".one" :
                     key + ".other";
    
    return t(pluralKey, { count, ...params });
  }, [t]);

  // 格式化函数
  const formatters = useMemo(() => ({
    formatNumber: (num: number, options?: Intl.NumberFormatOptions) => {
      return new Intl.NumberFormat(locale.code, options).format(num);
    },
    
    formatDate: (date: Date, options?: Intl.DateTimeFormatOptions) => {
      return new Intl.DateTimeFormat(locale.code, options).format(date);
    },
    
    formatCurrency: (amount: number, currency = 'USD') => {
      return new Intl.NumberFormat(locale.code, {
        style: 'currency',
        currency: currency
      }).format(amount);
    }
  }), [locale.code]);

  const value: I18nContextType = {
    locale,
    translations,
    loading,
    t,
    tn,
    setLocale,
    ...formatters
  };

  return (
    <I18nContext.Provider value={value}>
      {children}
    </I18nContext.Provider>
  );
}

// 6. useI18n Hook
export function useI18n() {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useI18n must be used within I18nProvider');
  }
  return context;
}

// 7. 使用示例
function App() {
  return (
    <I18nProvider>
      <Layout />
    </I18nProvider>
  );
}

function Layout() {
  const { locale, setLocale, loading } = useI18n();
  
  if (loading) {
    return <div>Loading translations...</div>;
  }
  
  return (
    <div>
      <Header />
      <LanguageSelector />
      <MainContent />
    </div>
  );
}

function LanguageSelector() {
  const { locale, setLocale } = useI18n();
  
  return (
    <select 
      value={locale.code} 
      onChange={(e) => setLocale(e.target.value)}
    >
      {Object.values(SUPPORTED_LOCALES).map(loc => (
        <option key={loc.code} value={loc.code}>
          {loc.name}
        </option>
      ))}
    </select>
  );
}

function MainContent() {
  const { t, tn, formatNumber, formatDate, formatCurrency } = useI18n();
  const [items, setItems] = useState(5);
  
  return (
    <div>
      <h1>{t('welcome.title')}</h1>
      <p>{t('welcome.message', { name: '张三' })}</p>
      
      <div>
        <h2>{t('shopping.cart')}</h2>
        <p>{tn('shopping.items', items, { count: items })}</p>
        <button onClick={() => setItems(items + 1)}>
          {t('shopping.addItem')}
        </button>
      </div>
      
      <div>
        <h3>{t('formats.title')}</h3>
        <p>{t('formats.number')}: {formatNumber(1234567.89)}</p>
        <p>{t('formats.date')}: {formatDate(new Date())}</p>
        <p>{t('formats.currency')}: {formatCurrency(99.99)}</p>
      </div>
    </div>
  );
}

// 8. 示例翻译文件 (locales/zh-CN.json)
const zhCN = {
  "welcome": {
    "title": "欢迎",
    "message": "你好，{{name}}！欢迎使用我们的应用。"
  },
  "shopping": {
    "cart": "购物车",
    "items": {
      "zero": "购物车是空的",
      "one": "购物车中有 {{count}} 件商品",
      "other": "购物车中有 {{count}} 件商品"
    },
    "addItem": "添加商品"
  },
  "formats": {
    "title": "格式化示例",
    "number": "数字",
    "date": "日期",
    "currency": "货币"
  }
};`,
    explanation: `这个高级示例展示了 useContext 在国际化系统中的复杂应用：

1. **动态加载**：使用动态 import 按需加载翻译文件，减少初始包体积
2. **缓存机制**：使用 Map 缓存已加载的翻译，避免重复加载
3. **复数处理**：实现了 tn 函数处理不同语言的复数规则
4. **格式化支持**：集成 Intl API 提供数字、日期、货币格式化
5. **性能优化**：使用 useMemo 缓存格式化函数，避免重复创建
6. **RTL 支持**：自动设置文档方向，支持阿拉伯语等 RTL 语言
7. **类型安全**：完整的 TypeScript 类型定义
8. **错误处理**：翻译文件加载失败时的回退机制`
  }
];

export default businessScenarios; 