import { ApiItem } from "@/types/api";
import basicInfo from "./basic-info";
import businessScenarios from "./business-scenarios";
import implementation from "./implementation";
import interviewQuestions from "./interview-questions";
import commonQuestions from "./common-questions";
import knowledgeArchaeology from "./knowledge-archaeology";
import performanceOptimization from "./performance-optimization";
import debuggingTips from "./debugging-tips";
import essenceInsights from "./essence-insights";

export const useContextAPI: ApiItem = {
  id: "useContext",
  title: "useContext",
  description: "React Hook，用于消费上下文值并实现组件树数据共享",
  category: "Context",
  difficulty: "medium",
  version: "16.8+",
  isNew: false,
  
  // 基础语法
  syntax: `const value = useContext(MyContext);

// Context creation
const MyContext = React.createContext(defaultValue);

// Provider usage
<MyContext.Provider value={value}>
  {children}
</MyContext.Provider>`,
  
  // 基础示例
  example: `import React, { createContext, useContext, useState } from 'react';

// 1. Create Context
const ThemeContext = createContext('light');

// 2. Provider Component
function App() {
  const [theme, setTheme] = useState('light');
  
  return (
    <ThemeContext.Provider value={theme}>
      <Header />
      <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
        Toggle Theme
      </button>
    </ThemeContext.Provider>
  );
}

// 3. Consumer Component
function Header() {
  const theme = useContext(ThemeContext);
  
  return (
    <header className={'header-' + theme}>
      <h1>My App - {theme} mode</h1>
    </header>
  );
}`,
  
  notes: "⚠️ 当Context值变化时，所有消费者组件都会重新渲染。使用memo和useMemo来优化性能。",
  
  // 必选Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,

  // 可选Tab内容
  performanceOptimization,
  debuggingTips,
  essenceInsights,
};

export default useContextAPI; 