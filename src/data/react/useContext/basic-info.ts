import { BasicInfo } from "@/types/api";

const basicInfo: BasicInfo = {
  definition: "useContext是React中用于消费Context值的Hook，让组件能够访问Context Provider提供的数据，实现跨组件层级的状态共享",

  introduction: "useContext 是 React 提供的用于消费 Context 值的 Hook。它让组件能够订阅 Context 的变化，实现跨组件层级的数据共享，避免了 props drilling（属性逐层传递）的问题。useContext 是实现全局状态管理、主题切换、用户认证等功能的核心工具。",

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react/index.d.ts:1080
 * - 实现文件：packages/react/src/ReactHooks.js
 */

// 基础语法
function useContext<T>(context: React.Context<T>): T;

// 完整用法
const MyContext = React.createContext<string>('default');

function MyComponent() {
  const value = useContext(MyContext);
  return <div>{value}</div>;
}

// Provider包装
function App() {
  return (
    <MyContext.Provider value="Hello World">
      <MyComponent />
    </MyContext.Provider>
  );
}

// TypeScript完整语法
interface UserContextType {
  user: User | null;
  setUser: (user: User | null) => void;
}

const UserContext = React.createContext<UserContextType | undefined>(undefined);

function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}`,

  quickExample: `function ContextExample() {
  // 创建Context
  const ThemeContext = React.createContext('light');
  
  // 消费Context的组件
  function ThemedButton() {
    const theme = useContext(ThemeContext);
    return (
      <button 
        style={{
          backgroundColor: theme === 'dark' ? '#333' : '#fff',
          color: theme === 'dark' ? '#fff' : '#333'
        }}
      >
        当前主题: {theme}
      </button>
    );
  }
  
  // 提供Context值
  const [theme, setTheme] = useState('light');
  
  return (
    <div>
      <h2>useContext 示例</h2>
      <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
        切换主题
      </button>
      
      <ThemeContext.Provider value={theme}>
        {/* ThemeButton可以直接访问theme值，无需props传递 */}
        <ThemedButton />
      </ThemeContext.Provider>
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "Context数据流向",
      description: "展示useContext如何在组件树中传递数据，避免props drilling的完整流程",
      diagram: `graph TD
        A[Context Provider] --> B[组件树根节点]
        B --> C[中间层组件A]
        B --> D[中间层组件B]
        C --> E[深层组件C]
        C --> F[深层组件D]
        D --> G[深层组件E]
        
        E --> H[useContext Hook]
        F --> I[useContext Hook]
        G --> J[useContext Hook]
        
        A -.->|直接传递| H
        A -.->|直接传递| I
        A -.->|直接传递| J
        
        style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
        style H fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
        style I fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
        style J fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px`
    },
    {
      title: "Context更新机制",
      description: "展示Context值变化时的组件重渲染机制和优化策略",
      diagram: `graph LR
        A[Provider value改变] --> B[Context通知机制]
        B --> C[订阅的组件检测]
        C --> D{useContext组件}
        
        D -->|使用Context| E[触发重渲染]
        D -->|未使用Context| F[不重渲染]
        
        E --> G[组件更新]
        G --> H[子组件检查]
        H -->|有memo优化| I[条件渲染]
        H -->|无优化| J[全部重渲染]
        
        style A fill:#fff3e0,stroke:#f57c00,stroke-width:2px
        style E fill:#ffebee,stroke:#c62828,stroke-width:2px
        style F fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px`
    },
    {
      title: "多Context组合使用",
      description: "展示如何组合使用多个Context实现复杂的状态管理架构",
      diagram: `graph TD
        A[App Root] --> B[AuthProvider]
        B --> C[ThemeProvider]
        C --> D[LanguageProvider]
        D --> E[Component Tree]
        
        E --> F[useAuth Hook]
        E --> G[useTheme Hook]
        E --> H[useLanguage Hook]
        
        F --> F1[AuthContext]
        G --> G1[ThemeContext]
        H --> H1[LanguageContext]
        
        F1 -.-> B
        G1 -.-> C
        H1 -.-> D
        
        style A fill:#e3f2fd,stroke:#0277bd,stroke-width:3px
        style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
        style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
        style D fill:#fff3e0,stroke:#ef6c00,stroke-width:2px`
    }
  ],
  
  parameters: [
    {
      name: "context",
      type: "React.Context<T>",
      description: "由 React.createContext 创建的 Context 对象",
      required: true,
      example: "const value = useContext(MyContext)"
    }
  ],
  
  returnValue: {
    type: "T",
    description: "返回 Context 的当前值，该值由最近的 Provider 的 value prop 决定",
    example: "const theme = useContext(ThemeContext); // 返回当前主题值"
  },
  
  keyFeatures: [
    {
      title: "跨层级数据传递",
      description: "无需通过props逐层传递，直接在任意深度的组件中访问Context值",
      benefit: "消除props drilling，简化组件间的数据传递"
    },
    {
      title: "自动订阅更新",
      description: "当Context值发生变化时，所有使用该Context的组件自动重新渲染",
      benefit: "确保UI与数据状态的实时同步"
    },
    {
      title: "类型安全支持",
      description: "在TypeScript中提供完整的类型推导和类型检查",
      benefit: "编译时捕获类型错误，提高代码可靠性"
    },
    {
      title: "灵活的默认值机制",
      description: "支持设置默认值，在没有Provider的情况下提供备用数据",
      benefit: "增强组件的健壮性和可复用性"
    }
  ],
  
  limitations: [
    "必须在函数组件或自定义 Hook 中使用，不能在类组件中使用",
    "必须传入 Context 对象本身，而不是 Provider 或 Consumer",
    "当 Provider 的 value 改变时，所有使用该 Context 的组件都会重新渲染",
    "如果没有匹配的 Provider，将返回 createContext 时的默认值",
    "Context 值的变化会绕过 React.memo 和 shouldComponentUpdate"
  ],

  bestPractices: [
    "为Context创建自定义Hook，提供更好的开发体验和错误检查",
    "使用TypeScript定义Context的类型，确保类型安全",
    "合理拆分Context，避免单个Context承载过多状态",
    "在Context值未定义时抛出有意义的错误信息",
    "使用React.memo包装消费Context的组件，避免不必要的重渲染",
    "将Context Provider放在组件树的合适位置，避免过度嵌套",
    "考虑使用useReducer配合Context实现复杂的状态管理",
    "为Context提供有意义的默认值，提高组件的可测试性"
  ],

  warnings: [
    "Context变化会导致所有消费组件重渲染，注意性能影响",
    "不要在没有Provider的情况下使用Context，除非设置了合理的默认值",
    "避免在Context中存储经常变化的值，会导致频繁重渲染",
    "不要滥用Context，简单的状态传递还是使用props更合适",
    "Context值是对象时要注意引用相等性，避免意外的重渲染"
  ]
};

export default basicInfo; 