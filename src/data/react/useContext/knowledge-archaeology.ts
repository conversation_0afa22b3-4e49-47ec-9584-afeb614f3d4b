import { KnowledgeArchaeology } from "@/types/api";

const knowledgeArchaeology: KnowledgeArchaeology = {
  evolution: `useContext 的演进历程：

1. **React 0.14 之前（2015年前）**：
   - 使用 this.context 在类组件中访问上下文
   - 需要声明 contextTypes 进行类型检查
   - 只支持单一 context，使用不便

2. **React 16.3（2018年3月）**：
   - 引入新的 Context API：React.createContext
   - 提供 Provider 和 Consumer 组件
   - 支持多个 Context，解决了旧版本的限制
   - 使用 render props 模式，但嵌套较深

3. **React 16.8（2019年2月）**：
   - 引入 useContext Hook
   - 大幅简化 Context 的使用方式
   - 消除了 Consumer 的嵌套问题
   - 与其他 Hooks 完美配合

4. **React 18+（2022年后）**：
   - 并发模式下的 Context 优化
   - 自动批处理提升性能
   - 与 Suspense 更好的集成`,

  comparison: [
    {
      aspect: "语法简洁性",
      oldWay: `// 旧版 Context API
class MyComponent extends React.Component {
  static contextTypes = {
    theme: PropTypes.object
  };
  
  render() {
    const theme = this.context.theme;
    return <div style={{ color: theme.color }}>...</div>;
  }
}

// 新版 Consumer API
<ThemeContext.Consumer>
  {theme => (
    <div style={{ color: theme.color }}>...</div>
  )}
</ThemeContext.Consumer>`,
      newWay: `// useContext Hook
function MyComponent() {
  const theme = useContext(ThemeContext);
  return <div style={{ color: theme.color }}>...</div>;
}`,
      explanation: "useContext 消除了类组件的样板代码和 Consumer 的嵌套地狱"
    },
    {
      aspect: "多 Context 使用",
      oldWay: `// 多个 Consumer 嵌套
<ThemeContext.Consumer>
  {theme => (
    <UserContext.Consumer>
      {user => (
        <SettingsContext.Consumer>
          {settings => (
            <div>
              {/* 使用 theme, user, settings */}
            </div>
          )}
        </SettingsContext.Consumer>
      )}
    </UserContext.Consumer>
  )}
</ThemeContext.Consumer>`,
      newWay: `// 多个 useContext 平行使用
function MyComponent() {
  const theme = useContext(ThemeContext);
  const user = useContext(UserContext);
  const settings = useContext(SettingsContext);
  
  return <div>{/* 使用 theme, user, settings */}</div>;
}`,
      explanation: "useContext 让多个 Context 的使用变得清晰简洁"
    },
    {
      aspect: "条件使用和组合",
      oldWay: `// Consumer 中难以实现条件逻辑
class MyComponent extends React.Component {
  render() {
    return (
      <ThemeContext.Consumer>
        {theme => {
          // 在 render props 中处理逻辑很别扭
          if (theme.mode === 'dark') {
            return <DarkComponent />;
          }
          return <LightComponent />;
        }}
      </ThemeContext.Consumer>
    );
  }
}`,
      newWay: `// Hook 中自然的条件逻辑
function MyComponent() {
  const theme = useContext(ThemeContext);
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    // 可以在 Effect 中使用 Context
    if (theme.mode === 'dark') {
      document.body.classList.add('dark-mode');
    }
    setMounted(true);
  }, [theme.mode]);
  
  if (!mounted) return null;
  
  return theme.mode === 'dark' ? <DarkComponent /> : <LightComponent />;
}`,
      explanation: "useContext 与其他 Hooks 的组合使用更加自然"
    }
  ],

  philosophy: `useContext 的设计哲学：

1. **简化状态共享**：
   - 提供比 props drilling 更优雅的解决方案
   - 让跨组件通信变得简单直接
   - 保持 React 单向数据流的原则

2. **函数式编程思想**：
   - 将 Context 消费视为"使用"一个值
   - 与其他 Hooks 保持一致的使用模式
   - 支持组合和抽象

3. **关注点分离**：
   - Provider 负责提供数据
   - useContext 负责消费数据
   - 自定义 Hook 负责封装逻辑

4. **性能考虑**：
   - 故意不提供细粒度订阅
   - 鼓励合理拆分 Context
   - 推动开发者思考数据结构`,

  impact: `useContext 对 React 生态的影响：

1. **状态管理模式的改变**：
   - 减少了对外部状态管理库的依赖
   - 促进了更多基于 Context 的轻量级方案
   - 影响了 Redux、MobX 等库的发展方向

2. **组件设计模式**：
   - Compound Components 模式的流行
   - Provider 模式成为标准做法
   - 推动了更好的组件封装实践

3. **开发体验提升**：
   - 降低了 React 的学习曲线
   - 提高了代码的可读性和可维护性
   - 促进了更好的 TypeScript 支持

4. **生态系统发展**：
   - 许多库采用 Context + Hooks 的模式
   - 如 React Router v6、React Hook Form 等
   - 推动了整个生态向函数式组件迁移`,

  bestPractices: [
    "合理拆分 Context，避免单一大 Context",
    "使用 useMemo 优化 Provider 的 value",
    "创建自定义 Hook 封装 Context 逻辑",
    "提供有意义的错误信息和默认值",
    "考虑性能影响，必要时使用其他状态管理方案",
    "利用 TypeScript 提供完整的类型支持"
  ],

  resources: [
    {
      title: "React Context API 官方文档",
      url: "https://react.dev/reference/react/useContext",
      description: "最权威的 API 参考和使用指南"
    },
    {
      title: "Passing Data Deeply with Context",
      url: "https://react.dev/learn/passing-data-deeply-with-context",
      description: "React 官方的 Context 深入教程"
    },
    {
      title: "useContext 性能优化指南",
      url: "https://github.com/facebook/react/issues/15156",
      description: "React 团队关于 Context 性能的讨论"
    }
  ]
};

export default knowledgeArchaeology;
