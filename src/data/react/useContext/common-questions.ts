import { CommonQuestion } from "@/types/api";

const commonQuestions: CommonQuestion[] = [
  {
    id: 'context-value-memo',
    question: "为什么 Context.Provider 的 value 要用 useMemo 包裹？",
    answer: `使用 useMemo 包裹 Provider 的 value 是一个重要的性能优化技巧：

**问题原因**：
如果不使用 useMemo，每次 Provider 组件重新渲染时，都会创建一个新的 value 对象。即使对象的内容没有变化，由于引用不同，React 会认为 Context 值发生了变化，导致所有消费该 Context 的组件重新渲染。

**解决方案**：
1. 使用 useMemo 缓存 value 对象
2. 只在依赖项变化时才创建新对象
3. 保持对象引用的稳定性

**最佳实践**：
- 对于静态值，可以在组件外定义
- 对于动态值，使用 useMemo 或 useCallback
- 考虑拆分 Context 以减少更新范围`,
    
    code: `// ❌ 错误：每次渲染都创建新对象
function BadProvider({ children }) {
  const [user, setUser] = useState(null);
  const [settings, setSettings] = useState({});
  
  // 每次渲染都创建新的 value 对象
  return (
    <MyContext.Provider value={{
      user,
      settings,
      setUser,
      setSettings
    }}>
      {children}
    </MyContext.Provider>
  );
}

// ✅ 正确：使用 useMemo 缓存
function GoodProvider({ children }) {
  const [user, setUser] = useState(null);
  const [settings, setSettings] = useState({});
  
  // 只在依赖变化时创建新对象
  const value = useMemo(() => ({
    user,
    settings,
    setUser,
    setSettings
  }), [user, settings]);
  
  return (
    <MyContext.Provider value={value}>
      {children}
    </MyContext.Provider>
  );
}

// ✅ 更好：拆分状态和操作
function BetterProvider({ children }) {
  const [user, setUser] = useState(null);
  const [settings, setSettings] = useState({});
  
  // 状态值
  const stateValue = useMemo(() => ({
    user,
    settings
  }), [user, settings]);
  
  // 操作函数（稳定引用）
  const actionsValue = useMemo(() => ({
    setUser,
    setSettings
  }), []); // 空依赖，因为 setState 函数是稳定的
  
  return (
    <StateContext.Provider value={stateValue}>
      <ActionsContext.Provider value={actionsValue}>
        {children}
      </ActionsContext.Provider>
    </StateContext.Provider>
  );
}`,
    
    tags: ["性能优化", "最佳实践"]
  },
  {
    id: 'provider-missing-error',
    question: "如何解决 'useContext must be used within Provider' 错误？",
    answer: `这个错误表示你在 Provider 组件外部使用了 useContext，常见原因和解决方案：

**常见原因**：
1. 忘记包裹 Provider
2. 在错误的组件树位置使用
3. Provider 和 Consumer 不匹配
4. 条件渲染导致的问题

**解决方案**：
1. 确保组件在 Provider 内部
2. 创建自定义 Hook 进行错误检查
3. 提供有意义的错误信息
4. 使用默认值作为后备方案`,
    
    code: `// ❌ 错误：在 Provider 外使用
function App() {
  return (
    <div>
      <Header /> {/* 这里会报错 */}
      <ThemeProvider>
        <Main />
      </ThemeProvider>
    </div>
  );
}

function Header() {
  const theme = useContext(ThemeContext); // Error!
  return <div>Header</div>;
}

// ✅ 解决方案1：确保在 Provider 内
function App() {
  return (
    <ThemeProvider>
      <Header /> {/* 现在在 Provider 内 */}
      <Main />
    </ThemeProvider>
  );
}

// ✅ 解决方案2：创建安全的自定义 Hook
const ThemeContext = createContext(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  
  if (context === undefined) {
    throw new Error(
      'useTheme 必须在 ThemeProvider 内部使用。' +
      '请确保组件被 <ThemeProvider> 包裹。'
    );
  }
  
  return context;
}

// ✅ 解决方案3：提供默认值
const ThemeContext = createContext({
  theme: 'light',
  toggleTheme: () => {
    console.warn('ThemeProvider not found, using default theme');
  }
});

// ✅ 解决方案4：条件渲染检查
function SafeComponent() {
  const context = useContext(ThemeContext);
  
  if (!context) {
    return <div>Loading theme...</div>;
  }
  
  return <div>Theme: {context.theme}</div>;
}

// ✅ 解决方案5：开发环境提示
function createSafeContext(name) {
  const Context = createContext(undefined);
  
  function useContext() {
    const context = React.useContext(Context);
    
    if (context === undefined) {
      // 开发环境下显示组件树帮助调试
      if (process.env.NODE_ENV === 'development') {
        console.error(
          name + " Context 未找到。组件树：",
          document.querySelector('#root')?._reactRootContainer
        );
      }
      
      throw new Error("use" + name + " must be used within " + name + "Provider");
    }
    
    return context;
  }
  
  return { Provider: Context.Provider, useContext };
}`,
    
    tags: ["错误处理", "调试技巧"]
  },
  {
    id: 'memo-context-rerender',
    question: "Context 更新后，为什么 React.memo 包裹的组件还是会重新渲染？",
    answer: `这是 React Context 的一个重要特性：Context 的更新会绕过 React.memo 的优化。

**原因分析**：
1. Context 消费者的更新机制独立于 props
2. React 会直接标记所有 Context 消费者需要更新
3. memo 只能阻止 props 变化导致的渲染
4. Context 被视为"隐式 props"

**解决方案**：
1. 拆分 Context 粒度
2. 使用组件组合模式
3. 实现自定义订阅机制
4. 使用状态管理库`,
    
    code: `// ❌ 问题演示：memo 无法阻止 Context 更新
const ThemeContext = createContext();

const ExpensiveComponent = React.memo(({ data }) => {
  const theme = useContext(ThemeContext); // 使用了 Context
  console.log('ExpensiveComponent render'); // 主题变化时会打印
  
  return (
    <div style={{ color: theme.color }}>
      {/* 复杂的渲染逻辑 */}
    </div>
  );
});

// ✅ 解决方案1：拆分组件
const ExpensiveContent = React.memo(({ data, color }) => {
  console.log('ExpensiveContent render'); // 只在 data 或 color 变化时打印
  return (
    <div style={{ color }}>
      {/* 复杂的渲染逻辑 */}
    </div>
  );
});

const ExpensiveComponentWrapper = ({ data }) => {
  const theme = useContext(ThemeContext);
  return <ExpensiveContent data={data} color={theme.color} />;
};

// ✅ 解决方案2：使用多个 Context
const ThemeColorContext = createContext();
const ThemeModeContext = createContext();

const ColorConsumer = React.memo(() => {
  const color = useContext(ThemeColorContext);
  console.log('ColorConsumer render'); // 只在颜色变化时渲染
  return <div style={{ color }}>Color: {color}</div>;
});

const ModeConsumer = React.memo(() => {
  const mode = useContext(ThemeModeContext);
  console.log('ModeConsumer render'); // 只在模式变化时渲染
  return <div>Mode: {mode}</div>;
});

// ✅ 解决方案3：使用 useMemo 在 Context 内部
function OptimizedConsumer() {
  const { data, expensiveCompute } = useContext(DataContext);
  
  // 即使 Context 更新，计算结果也会被缓存
  const computedValue = useMemo(() => {
    return expensiveCompute(data);
  }, [data, expensiveCompute]);
  
  return <div>{computedValue}</div>;
}

// ✅ 解决方案4：实现选择器模式
function createSelectableContext() {
  const Context = createContext();
  
  function useContextSelector(selector) {
    const value = useContext(Context);
    const selectedRef = useRef(selector(value));
    const [, forceUpdate] = useReducer(x => x + 1, 0);
    
    useEffect(() => {
      const newSelected = selector(value);
      if (!Object.is(selectedRef.current, newSelected)) {
        selectedRef.current = newSelected;
        forceUpdate();
      }
    });
    
    return selectedRef.current;
  }
  
  return { Provider: Context.Provider, useContextSelector };
}

// 使用选择器
function SelectiveConsumer() {
  // 只在 user.name 变化时重新渲染
  const userName = useContextSelector(state => state.user.name);
  return <div>{userName}</div>;
}`,
    
    tags: ["性能优化", "React.memo", "渲染优化"]
  }
];

export default commonQuestions; 