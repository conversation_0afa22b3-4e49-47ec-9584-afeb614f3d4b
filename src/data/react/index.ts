import { ApiItem } from '@/types/api';

import useStateData from './useState';
import useEffectData from './useEffect';
import useContextData from './useContext';
import useReducerData from './useReducer';
import useMemoData from './useMemo';
import useCallbackData from './useCallback';
import useRefData from './useRef';
import useImperativeHandleData from './useImperativeHandle';
import useLayoutEffectData from './useLayoutEffect';
import useTransitionData from './useTransition';
import useDeferredValueData from './useDeferredValue';
import useSyncExternalStoreData from './useSyncExternalStore';
import useIdData from './useId';
import useInsertionEffectData from './useInsertionEffect';
import customHooksData from './customHooks';
import useDebugValueData from './useDebugValue';
import ReactComponentData from './Component';
import ReactMemoData from './Memo';
import ReactForwardRefData from './ForwardRef';
import ReactLazyData from './Lazy';
import createContextData from './createContext';
import FragmentData from './Fragment';
import PortalData from './Portal';
import ReactSuspenseData from './Suspense';
import ReactDevToolsData from './DevTools';
import ReactVersionData from './Version';

import useCustomHookData from './useCustomHook';
import useOptimisticData from './useOptimistic';
import StrictModeData from './StrictMode';
import ReactPureComponentData from './PureComponent';
import ReactFunctionComponentData from './FunctionComponent';
import ErrorBoundaryData from './ErrorBoundary';
import ReactComponentClassData from './ComponentClass';
import ReactElementData from './ReactElement';
import ReactNodeData from './ReactNode';
import createRootData from './createRoot';
import hydrateRootData from './hydrateRoot';

import createPortalData from './createPortal';
import flushSyncData from './flushSync';
import useActionStateData from './useActionState';
import useFormStatusData from './useFormStatus';
import preloadData from './preload';
import createRefData from './createRef';
import startTransitionData from './startTransition';
import preinitModuleData from './preinitModule';
import preinitData from './preinit';
import preloadModuleData from './preloadModule';
import preconnectData from './preconnect';
import prefetchDNSData from './prefetchDNS';
import cacheData from './cache';
import renderToPipeableStreamData from './renderToPipeableStream';
import renderToReadableStreamData from './renderToReadableStream';
import renderToStaticMarkupData from './renderToStaticMarkup';
import renderToStringData from './renderToString';
import useClientData from './use-client';
import useserverData from './use-server';
import constletData from './hooks/const-let';


// React Hooks
const hooksApis: ApiItem[] = [
  useStateData,
  useEffectData,
  useContextData,
  useReducerData,
  useMemoData,
  useCallbackData,
  useRefData,
  useImperativeHandleData,
  useLayoutEffectData,
  useTransitionData,
  useDeferredValueData,
  useSyncExternalStoreData,
  useIdData,
  useInsertionEffectData,
  customHooksData,
  useDebugValueData,
  useCustomHookData,
  useOptimisticData,
  useActionStateData,
  useFormStatusData,
  renderToStringData,
  useClientData,
  useserverData
];

// React Components & JSX
const componentApis: ApiItem[] = [
  ReactComponentData,
  ReactMemoData,
  ReactForwardRefData,
  ReactLazyData,
  ReactSuspenseData,
  createContextData,
  FragmentData,
  PortalData,
  StrictModeData,
  ReactPureComponentData,
  ReactFunctionComponentData,
  ErrorBoundaryData,
  ReactComponentClassData,
  ReactElementData,
  ReactNodeData,
  ReactDevToolsData,
  ReactVersionData,
];

// ReactDOM APIs
const reactDOMApis: ApiItem[] = [
  createRootData,
  createPortalData,
  flushSyncData,
  preloadData,
  hydrateRootData,
  createRefData,
  startTransitionData,
  preinitModuleData,
  preinitData,
  preloadModuleData,
  preconnectData,
  prefetchDNSData,
  cacheData,
  renderToPipeableStreamData,
  renderToReadableStreamData,
  renderToStaticMarkupData,
];

// React 18 新特性
const react18Apis: ApiItem[] = [
  // useIdApi,
  // useDeferredValueApi,
  // useTransitionApi,
  // useSyncExternalStoreApi,
  // useInsertionEffectApi
];

// React 19 新特性 (即将发布)
const react19Apis: ApiItem[] = [
  // useApi,
  // useFormStatusApi,
  // useFormStateApi,
  // useOptimisticApi
];

// 合并所有API
export const allReactApis: ApiItem[] = [
  ...hooksApis,
  ...componentApis,
  ...reactDOMApis,
  ...react18Apis,
  ...react19Apis,
];

// 按分类导出
export const apisByCategory = {
  'React Hooks': hooksApis,
  'Components & JSX': componentApis,
  'ReactDOM APIs': reactDOMApis,
  'React 18': react18Apis,
  'React 19': react19Apis,
};

// 按ID查询API的辅助函数
export function getApiById(id: string): ApiItem | undefined {
  return allReactApis.find(api => api.id === id);
}

// 导出默认对象
export default {
  allApis: allReactApis,
  apisByCategory,
  getApiById
}; 