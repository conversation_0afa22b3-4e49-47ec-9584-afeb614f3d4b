import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: '订阅函数优化策略',
      description: '确保subscribe函数的稳定性和高效性，避免不必要的重新订阅',
      techniques: [
        {
          name: '使用稳定引用',
          description: '将subscribe函数定义在组件外部或使用useCallback包装',
          code: `// ❌ 低效：每次渲染创建新函数
function BadComponent() {
  return useSyncExternalStore(
    (callback) => store.subscribe(callback), // 每次都是新函数
    store.getSnapshot
  );
}

// ✅ 高效：稳定引用
const stableSubscribe = (callback) => store.subscribe(callback);

function GoodComponent() {
  return useSyncExternalStore(
    stableSubscribe, // 稳定引用
    store.getSnapshot
  );
}

// ✅ 高效：使用useCallback
function OptimizedComponent() {
  const subscribe = useCallback((callback) => {
    return store.subscribe(callback);
  }, []);
  
  return useSyncExternalStore(subscribe, store.getSnapshot);
}`,
          impact: 'high',
          difficulty: 'easy'
        },
        {
          name: '批量订阅管理',
          description: '对多个订阅进行批量管理，减少订阅开销',
          code: `class BatchedSubscriptionManager {
  constructor() {
    this.subscriptions = new Map();
    this.listeners = new Set();
    this.batchedNotify = null;
  }
  
  subscribe = (key, callback) => {
    // 如果是第一次订阅此key，创建实际订阅
    if (!this.subscriptions.has(key)) {
      const unsubscribe = this.createSubscription(key);
      this.subscriptions.set(key, {
        unsubscribe,
        callbacks: new Set()
      });
    }
    
    // 添加回调到对应的key
    this.subscriptions.get(key).callbacks.add(callback);
    
    return () => {
      const subscription = this.subscriptions.get(key);
      if (subscription) {
        subscription.callbacks.delete(callback);
        
        // 如果没有更多回调，取消订阅
        if (subscription.callbacks.size === 0) {
          subscription.unsubscribe();
          this.subscriptions.delete(key);
        }
      }
    };
  }
  
  // 批量通知所有监听者
  notifyBatched = () => {
    if (this.batchedNotify) return;
    
    this.batchedNotify = Promise.resolve().then(() => {
      this.subscriptions.forEach((subscription) => {
        subscription.callbacks.forEach(callback => {
          try {
            callback();
          } catch (error) {
            console.error('订阅回调执行失败:', error);
          }
        });
      });
      this.batchedNotify = null;
    });
  }
}

const subscriptionManager = new BatchedSubscriptionManager();

function useBatchedStore(storeKey) {
  return useSyncExternalStore(
    useCallback((callback) => 
      subscriptionManager.subscribe(storeKey, callback), [storeKey]
    ),
    () => store.getSnapshot(storeKey)
  );
}`,
          impact: 'high',
          difficulty: 'medium'
        }
      ]
    },
    {
      title: '快照获取优化',
      description: '优化getSnapshot函数的性能，减少不必要的计算和对象创建',
      techniques: [
        {
          name: '快照缓存机制',
          description: '缓存快照结果，避免重复计算',
          code: `class CachedStore {
  constructor() {
    this.state = { users: [], posts: [], metadata: {} };
    this.listeners = new Set();
    this.cachedSnapshot = null;
    this.snapshotVersion = 0;
  }
  
  // 获取缓存的快照
  getSnapshot = () => {
    // 如果缓存存在且有效，直接返回
    if (this.cachedSnapshot && this.cachedSnapshot.version === this.snapshotVersion) {
      return this.cachedSnapshot.data;
    }
    
    // 创建新快照并缓存
    const snapshot = {
      users: this.state.users,
      posts: this.state.posts,
      metadata: this.state.metadata,
      // 添加一些计算属性
      userCount: this.state.users.length,
      postCount: this.state.posts.length,
      timestamp: Date.now()
    };
    
    this.cachedSnapshot = {
      data: snapshot,
      version: this.snapshotVersion
    };
    
    return snapshot;
  }
  
  // 更新状态并清除缓存
  updateState = (newState) => {
    this.state = { ...this.state, ...newState };
    this.snapshotVersion++; // 增加版本号，使缓存失效
    this.cachedSnapshot = null; // 清除缓存
    this.notifyListeners();
  }
  
  subscribe = (callback) => {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }
  
  notifyListeners = () => {
    this.listeners.forEach(callback => callback());
  }
}`,
          impact: 'high',
          difficulty: 'medium'
        },
        {
          name: '选择性快照',
          description: '只返回组件需要的部分数据，减少不必要的重渲染',
          code: `// 创建选择器Hook
function useStoreSelector(selector, deps = []) {
  const selectorMemoized = useMemo(() => selector, deps);
  
  return useSyncExternalStore(
    store.subscribe,
    useCallback(() => selectorMemoized(store.getSnapshot()), [selectorMemoized])
  );
}

// 使用示例：只订阅用户数据
function UserComponent({ userId }) {
  const user = useStoreSelector(
    state => state.users.find(u => u.id === userId),
    [userId]
  );
  
  const userPosts = useStoreSelector(
    state => state.posts.filter(p => p.authorId === userId),
    [userId]
  );
  
  return (
    <div>
      <h3>{user?.name}</h3>
      <p>文章数量: {userPosts.length}</p>
    </div>
  );
}

// 高级选择器：使用Proxy进行细粒度订阅
class ProxyBasedStore {
  constructor(initialState) {
    this.state = initialState;
    this.listeners = new Map(); // key -> Set<callback>
    this.proxiedState = this.createProxy(this.state);
  }
  
  createProxy = (obj, path = []) => {
    return new Proxy(obj, {
      get: (target, prop) => {
        const value = target[prop];
        const currentPath = [...path, prop];
        
        // 如果是对象，继续代理
        if (typeof value === 'object' && value !== null) {
          return this.createProxy(value, currentPath);
        }
        
        return value;
      }
    });
  }
  
  // 订阅特定路径的变化
  subscribeToPath = (path, callback) => {
    const pathKey = path.join('.');
    if (!this.listeners.has(pathKey)) {
      this.listeners.set(pathKey, new Set());
    }
    this.listeners.get(pathKey).add(callback);
    
    return () => {
      const listeners = this.listeners.get(pathKey);
      if (listeners) {
        listeners.delete(callback);
        if (listeners.size === 0) {
          this.listeners.delete(pathKey);
        }
      }
    };
  }
  
  getSnapshot = () => this.proxiedState;
}`,
          impact: 'high',
          difficulty: 'hard'
        }
      ]
    },
    {
      title: '内存管理优化',
      description: '优化内存使用，防止内存泄漏和过度消耗',
      techniques: [
        {
          name: '弱引用监听器',
          description: '使用WeakRef和FinalizationRegistry管理监听器生命周期',
          code: `class WeakRefStore {
  constructor() {
    this.state = {};
    this.weakListeners = new Set();
    this.cleanupRegistry = new FinalizationRegistry((heldValue) => {
      this.weakListeners.delete(heldValue);
    });
  }
  
  subscribe = (callback) => {
    const weakCallback = new WeakRef(callback);
    const wrapper = () => {
      const cb = weakCallback.deref();
      if (cb) {
        cb();
      } else {
        // 回调已被GC，自动清理
        this.weakListeners.delete(wrapper);
      }
    };
    
    this.weakListeners.add(wrapper);
    this.cleanupRegistry.register(callback, wrapper);
    
    return () => {
      this.weakListeners.delete(wrapper);
      this.cleanupRegistry.unregister(callback);
    };
  }
  
  notifyListeners = () => {
    const listenersToRemove = [];
    
    this.weakListeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        // 如果执行失败，可能是已被GC
        listenersToRemove.push(listener);
      }
    });
    
    // 清理失效的监听器
    listenersToRemove.forEach(listener => {
      this.weakListeners.delete(listener);
    });
  }
}`,
          impact: 'medium',
          difficulty: 'hard'
        },
        {
          name: '对象池优化',
          description: '重用对象实例，减少GC压力',
          code: `class ObjectPoolStore {
  constructor() {
    this.state = {};
    this.listeners = new Set();
    this.snapshotPool = [];
    this.maxPoolSize = 10;
  }
  
  // 从对象池获取快照对象
  getSnapshotObject = () => {
    if (this.snapshotPool.length > 0) {
      return this.snapshotPool.pop();
    }
    return {};
  }
  
  // 将快照对象返回到池中
  returnSnapshotObject = (obj) => {
    if (this.snapshotPool.length < this.maxPoolSize) {
      // 清空对象属性
      Object.keys(obj).forEach(key => delete obj[key]);
      this.snapshotPool.push(obj);
    }
  }
  
  getSnapshot = () => {
    const snapshot = this.getSnapshotObject();
    
    // 填充当前状态
    Object.assign(snapshot, this.state);
    
    // 设置一个标记，用于回收
    snapshot._pooled = true;
    
    return snapshot;
  }
  
  // 在适当的时机回收快照对象
  recycleSnapshot = (snapshot) => {
    if (snapshot._pooled) {
      this.returnSnapshotObject(snapshot);
    }
  }
}`,
          impact: 'medium',
          difficulty: 'medium'
        }
      ]
    }
  ],
  
  performanceMetrics: {
    'subscription-establishment': {
      description: '从调用subscribe到监听器生效的时间',
      tool: 'Performance.mark() 和 Performance.measure()',
      example: 'performance.mark("subscribe-start"); // 在subscribe调用前\nperformance.mark("subscribe-end"); // 在subscribe返回后\nperformance.measure("subscription-time", "subscribe-start", "subscribe-end");'
    },
    'snapshot-retrieval': {
      description: 'getSnapshot函数的执行时间',
      tool: 'React DevTools Profiler 或 console.time()',
      example: 'console.time("getSnapshot"); const snapshot = store.getSnapshot(); console.timeEnd("getSnapshot");'
    },
    'state-propagation': {
      description: '从状态变化到组件重渲染完成的时间',
      tool: 'React DevTools Profiler',
      example: '在Profiler中记录状态更新，查看Committed时间和Effects执行时间'
    },
    'memory-usage': {
      description: '长时间运行时的内存增长速度',
      tool: 'Chrome DevTools Memory 面板',
      example: '定期拍摄Heap Snapshot，对比内存使用变化：\n1. 拍摄基准快照\n2. 执行操作\n3. 强制GC\n4. 拍摄新快照\n5. 对比差异'
    }
  },
  
  bestPractices: [
    '使用稳定的subscribe函数引用，避免每次渲染都重新订阅',
    '缓存getSnapshot的计算结果，特别是涉及复杂计算时',
    '使用选择器模式，只订阅组件真正需要的数据',
    '批量处理状态更新，减少频繁的通知',
    '及时清理不再使用的监听器，防止内存泄漏',
    '在开发环境中添加性能监控，及时发现性能问题',
    '对于大型应用，考虑使用虚拟化技术处理大量数据',
    '合理使用防抖和节流，避免过于频繁的更新'
  ],
  
  commonPitfalls: [
    {
      issue: 'getSnapshot每次返回新对象导致无限重渲染',
      cause: '在getSnapshot函数中每次都创建新的对象或数组，即使内容相同，引用不同导致React认为状态发生了变化',
      solution: '缓存计算结果，只在数据真正变化时才创建新对象。使用稳定的引用，或者使用深度比较来避免不必要的更新'
    },
    {
      issue: 'subscribe函数没有稳定引用造成频繁重新订阅',
      cause: '在组件内部定义subscribe函数，每次渲染都会创建新函数，导致useSyncExternalStore重新订阅',
      solution: '将subscribe函数定义在组件外部，或者使用useCallback进行记忆化，确保函数引用的稳定性'
    },
    {
      issue: '忘记返回清理函数导致内存泄漏',
      cause: 'subscribe函数没有返回有效的清理函数，导致监听器无法被正确移除，造成内存泄漏',
      solution: '确保subscribe函数总是返回一个清理函数，并在该函数中正确移除监听器。使用开发工具监控内存使用情况'
    },
    {
      issue: '在getSnapshot中进行重计算影响性能',
      cause: '每次调用getSnapshot都进行复杂的计算或数据转换，导致性能问题',
      solution: '实施缓存策略，只在底层数据变化时重新计算。考虑使用选择器模式，只计算组件真正需要的数据'
    },
    {
      issue: '没有考虑并发场景下的状态一致性',
      cause: '在React并发模式下，组件可能在不同时间点读取状态，导致状态撕裂问题',
      solution: '确保getSnapshot的原子性，使用适当的同步机制。理解并发渲染的工作原理，设计并发安全的状态管理'
    },
    {
      issue: '过度优化导致代码复杂度增加反而影响维护性',
      cause: '为了追求极致性能而引入过多的优化技巧，使代码变得难以理解和维护',
      solution: '平衡性能和可维护性，只在确实存在性能问题时才进行优化。使用性能监控工具量化优化效果'
    }
  ]
};

export default performanceOptimization; 