import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'infinite-rerender',
    question: '为什么useSyncExternalStore会导致无限重渲染？',
    answer: '这通常是因为getSnapshot函数每次返回不同的对象引用。即使内容相同，React也会认为状态发生了变化，从而触发重渲染。解决方案是确保getSnapshot返回稳定的引用，或者使用适当的比较逻辑。',
    code: `// ❌ 错误：每次返回新对象
const badStore = {
  subscribe: (callback) => { /* ... */ },
  getSnapshot: () => ({ count: globalCount }) // 每次都是新对象！
};

// ✅ 正确：返回稳定引用
let stableSnapshot = { count: 0 };
const goodStore = {
  subscribe: (callback) => { /* ... */ },
  getSnapshot: () => stableSnapshot,
  updateCount: (newCount) => {
    stableSnapshot = { count: newCount }; // 只在真正变化时创建新对象
    notifyListeners();
  }
};

// 或者使用immutable库
import { Map } from 'immutable';
let immutableState = Map({ count: 0 });
const immutableStore = {
  getSnapshot: () => immutableState,
  updateCount: (newCount) => {
    immutableState = immutableState.set('count', newCount);
  }
};`,
    tags: ['无限渲染', '对象引用', '性能问题'],
    relatedQuestions: ['如何优化getSnapshot性能', 'subscribe函数的最佳实践']
  },
  {
    id: 'memory-leak',
    question: 'subscribe函数忘记返回清理函数会发生什么？',
    answer: '如果subscribe函数没有返回清理函数，会导致严重的内存泄漏。即使组件卸载，监听器仍然存在，继续监听外部store的变化。这可能导致应用性能下降，甚至崩溃。',
    code: `// ❌ 危险：没有返回清理函数
const dangerousStore = {
  listeners: new Set(),
  subscribe: (callback) => {
    this.listeners.add(callback);
    // 忘记返回清理函数！组件卸载后callback仍然存在
  }
};

// ✅ 安全：正确返回清理函数
const safeStore = {
  listeners: new Set(),
  subscribe: (callback) => {
    this.listeners.add(callback);
    
    // 返回清理函数
    return () => {
      this.listeners.delete(callback);
      console.log('已清理监听器，当前数量:', this.listeners.size);
    };
  }
};

// 检测内存泄漏的方法
function useStoreWithLeakDetection() {
  const [leakCount, setLeakCount] = useState(0);
  
  useEffect(() => {
    const timer = setInterval(() => {
      setLeakCount(store.listeners.size);
    }, 1000);
    
    return () => clearInterval(timer);
  }, []);
  
  // 如果leakCount持续增长，说明有内存泄漏
  if (leakCount > 100) {
    console.warn('检测到可能的内存泄漏！监听器数量:', leakCount);
  }
  
  return useSyncExternalStore(store.subscribe, store.getSnapshot);
}`,
    tags: ['内存泄漏', '清理函数', '组件卸载'],
    relatedQuestions: ['如何检测内存泄漏', '组件卸载时的清理工作']
  },
  {
    id: 'ssr-hydration-mismatch',
    question: 'SSR场景下出现hydration不匹配警告怎么办？',
    answer: '这通常是因为没有提供getServerSnapshot参数，或者服务端和客户端的初始状态不一致。需要确保getServerSnapshot返回与客户端初始状态相同的值。',
    code: `// ❌ 问题：缺少getServerSnapshot
function BadSSRComponent() {
  const data = useSyncExternalStore(
    store.subscribe,
    store.getSnapshot
    // 缺少第三个参数，SSR时会有警告
  );
}

// ✅ 解决：提供正确的getServerSnapshot
function GoodSSRComponent() {
  const data = useSyncExternalStore(
    store.subscribe,
    () => {
      // 客户端状态获取
      if (typeof window === 'undefined') {
        return getDefaultState(); // 服务端环境
      }
      return store.getSnapshot(); // 客户端环境
    },
    () => getDefaultState() // 服务端快照，必须与客户端初始状态一致
  );
}

// localStorage场景的正确处理
function useLocalStorageWithSSR(key, defaultValue) {
  return useSyncExternalStore(
    // 订阅storage事件
    useCallback((callback) => {
      if (typeof window === 'undefined') return () => {};
      
      const handleStorage = (e) => {
        if (e.key === key) callback();
      };
      
      window.addEventListener('storage', handleStorage);
      return () => window.removeEventListener('storage', handleStorage);
    }, [key]),
    
    // 客户端获取值
    () => {
      if (typeof window === 'undefined') return defaultValue;
      
      try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
      } catch {
        return defaultValue;
      }
    },
    
    // 服务端始终返回默认值
    () => defaultValue
  );
}`,
    tags: ['SSR', 'hydration', '服务端渲染'],
    relatedQuestions: ['SSR环境检测方法', 'localStorage在SSR中的处理']
  },
  {
    id: 'frequent-resubscription',
    question: '为什么组件频繁重新订阅外部store？',
    answer: '这通常是因为subscribe函数没有稳定的引用，每次渲染都创建新函数。应该使用useCallback包装subscribe函数，或者将其定义在组件外部。',
    code: `// ❌ 问题：每次渲染都创建新的subscribe函数
function BadComponent() {
  const data = useSyncExternalStore(
    // 每次渲染都是新函数，导致频繁重新订阅
    (callback) => store.subscribe(callback),
    store.getSnapshot
  );
}

// ✅ 解决方案1：使用useCallback
function GoodComponent1() {
  const subscribe = useCallback((callback) => {
    console.log('建立订阅');
    const unsubscribe = store.subscribe(callback);
    return () => {
      console.log('取消订阅');
      unsubscribe();
    };
  }, []); // 空依赖数组确保函数引用稳定
  
  const data = useSyncExternalStore(subscribe, store.getSnapshot);
}

// ✅ 解决方案2：定义在组件外部（推荐）
const stableSubscribe = (callback) => {
  return store.subscribe(callback);
};

function GoodComponent2() {
  const data = useSyncExternalStore(stableSubscribe, store.getSnapshot);
}

// 监控重新订阅的工具
function useSubscriptionMonitor(storeName = 'unknown') {
  const subscriptionCount = useRef(0);
  
  const monitoredSubscribe = useCallback((callback) => {
    subscriptionCount.current++;
    console.log(\`[\${storeName}] 第\${subscriptionCount.current}次订阅\`);
    
    if (subscriptionCount.current > 5) {
      console.warn(\`[\${storeName}] 检测到频繁重新订阅！\`);
    }
    
    return store.subscribe(callback);
  }, [storeName]);
  
  return monitoredSubscribe;
}`,
    tags: ['重新订阅', 'useCallback', '性能优化'],
    relatedQuestions: ['useCallback的正确使用方式', '如何监控组件重渲染']
  },
  {
    id: 'external-store-sync',
    question: '多个组件使用同一个外部store时，如何确保状态同步？',
    answer: 'useSyncExternalStore天然支持多组件同步。只要多个组件订阅同一个store且实现了正确的subscribe和getSnapshot函数，React会确保所有组件看到一致的状态。关键是store的notify机制要正确触发。',
    code: `// 创建可被多个组件共享的store
class SharedCounterStore {
  constructor() {
    this.count = 0;
    this.listeners = new Set();
  }
  
  // 获取当前状态
  getSnapshot = () => {
    return { count: this.count, timestamp: Date.now() };
  }
  
  // 订阅状态变化
  subscribe = (callback) => {
    this.listeners.add(callback);
    console.log(\`新增监听器，当前总数: \${this.listeners.size}\`);
    
    return () => {
      this.listeners.delete(callback);
      console.log(\`移除监听器，当前总数: \${this.listeners.size}\`);
    };
  }
  
  // 更新状态并通知所有监听者
  increment = () => {
    this.count++;
    this.notifyListeners();
  }
  
  decrement = () => {
    this.count--;
    this.notifyListeners();
  }
  
  // 通知所有监听者
  notifyListeners = () => {
    console.log(\`通知\${this.listeners.size}个监听者状态已更新\`);
    this.listeners.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('监听器执行出错:', error);
      }
    });
  }
}

// 全局store实例
const sharedStore = new SharedCounterStore();

// 组件A
function ComponentA() {
  const { count } = useSyncExternalStore(
    sharedStore.subscribe,
    sharedStore.getSnapshot
  );
  
  return (
    <div>
      <h3>组件A</h3>
      <p>计数: {count}</p>
      <button onClick={sharedStore.increment}>+1</button>
    </div>
  );
}

// 组件B
function ComponentB() {
  const { count } = useSyncExternalStore(
    sharedStore.subscribe,
    sharedStore.getSnapshot
  );
  
  return (
    <div>
      <h3>组件B</h3>
      <p>计数: {count}</p>
      <button onClick={sharedStore.decrement}>-1</button>
    </div>
  );
}

// 主应用 - 两个组件会自动保持同步
function App() {
  return (
    <div>
      <ComponentA />
      <ComponentB />
    </div>
  );
}`,
    tags: ['多组件同步', '状态共享', 'store设计'],
    relatedQuestions: ['如何设计全局状态管理', '组件间通信的最佳实践']
  },
  {
    id: 'performance-optimization',
    question: 'useSyncExternalStore的性能如何优化？',
    answer: '主要优化点包括：1）使用稳定的subscribe函数引用；2）优化getSnapshot避免不必要的计算；3）合理使用选择器模式减少不必要的重渲染；4）批处理状态更新。',
    code: `// 优化1：使用选择器模式，只订阅需要的部分状态
function useStoreSelector(selector) {
  const selectMemoized = useMemo(() => selector, [selector]);
  
  return useSyncExternalStore(
    store.subscribe,
    useCallback(() => selectMemoized(store.getSnapshot()), [selectMemoized])
  );
}

// 使用选择器只获取需要的字段
function UserProfile() {
  // 只有用户名变化时才重渲染
  const userName = useStoreSelector(state => state.user.name);
  
  return <div>用户: {userName}</div>;
}

// 优化2：批处理状态更新
class OptimizedStore {
  constructor() {
    this.state = { users: [], posts: [], comments: [] };
    this.listeners = new Set();
    this.pendingUpdate = false;
  }
  
  // 批处理更新
  batchUpdate = (updates) => {
    if (this.pendingUpdate) return;
    
    this.pendingUpdate = true;
    
    // 使用微任务批处理多个更新
    Promise.resolve().then(() => {
      updates.forEach(update => {
        this.state = { ...this.state, ...update };
      });
      
      this.pendingUpdate = false;
      this.notifyListeners();
    });
  }
  
  // 高性能的批量数据更新
  updateMultipleEntities = (users, posts, comments) => {
    this.batchUpdate([
      { users },
      { posts }, 
      { comments }
    ]);
  }
}

// 优化3：使用虚拟化处理大量数据
function useVirtualizedStore(itemHeight = 50, containerHeight = 400) {
  const storeData = useSyncExternalStore(
    store.subscribe,
    store.getSnapshot
  );
  
  return useMemo(() => {
    const visibleItemsCount = Math.ceil(containerHeight / itemHeight);
    const startIndex = Math.max(0, storeData.scrollTop - visibleItemsCount);
    const endIndex = Math.min(storeData.items.length, startIndex + visibleItemsCount * 2);
    
    return {
      ...storeData,
      visibleItems: storeData.items.slice(startIndex, endIndex),
      startIndex,
      endIndex
    };
  }, [storeData, itemHeight, containerHeight]);
}

// 优化4：防抖更新
function useDebouncedStore(delay = 100) {
  const [debouncedState, setDebouncedState] = useState(null);
  
  const storeState = useSyncExternalStore(
    store.subscribe,
    store.getSnapshot
  );
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedState(storeState);
    }, delay);
    
    return () => clearTimeout(timer);
  }, [storeState, delay]);
  
  return debouncedState;
}`,
    tags: ['性能优化', '选择器模式', '批处理', '虚拟化'],
    relatedQuestions: ['React性能优化技巧', '大数据量渲染优化']
  }
];

export default commonQuestions; 