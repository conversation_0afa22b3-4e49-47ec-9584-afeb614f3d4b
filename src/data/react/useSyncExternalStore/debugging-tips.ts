import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  commonErrors: [
    {
      error: '组件无限重渲染',
      cause: 'getSnapshot函数每次返回不同的对象引用，即使内容相同',
      solution: '确保getSnapshot返回稳定的引用，使用缓存或immutable数据',
      prevention: '在开发环境中添加引用稳定性检查',
      code: `// ❌ 问题：每次返回新对象
const badStore = {
  getSnapshot: () => ({ count: globalCount }) // 新对象！
};

// ✅ 解决：返回稳定引用
let stableSnapshot = { count: 0 };
const goodStore = {
  getSnapshot: () => stableSnapshot,
  updateCount: (newCount) => {
    if (stableSnapshot.count !== newCount) {
      stableSnapshot = { count: newCount }; // 仅在变化时创建新对象
    }
  }
};

// 🔧 调试工具：检测无限渲染
function useRenderTracker(name) {
  const renderCount = useRef(0);
  renderCount.current++;
  
  useEffect(() => {
    if (renderCount.current > 100) {
      console.error(\`\${name}: 检测到可能的无限渲染！渲染次数: \${renderCount.current}\`);
    }
  });
  
  return renderCount.current;
}`
    },
    {
      error: '内存泄漏警告',
      cause: 'subscribe函数没有返回清理函数，或清理逻辑不正确',
      solution: '确保subscribe函数总是返回有效的清理函数',
      prevention: '使用开发工具监控内存使用和监听器数量',
      code: `// ❌ 问题：没有返回清理函数
const leakyStore = {
  listeners: new Set(),
  subscribe: (callback) => {
    this.listeners.add(callback);
    // 没有返回清理函数！
  }
};

// ✅ 解决：正确的清理逻辑
const safeStore = {
  listeners: new Set(),
  subscribe: (callback) => {
    this.listeners.add(callback);
    
    // 返回清理函数
    return () => {
      const deleted = this.listeners.delete(callback);
      if (!deleted) {
        console.warn('尝试删除不存在的监听器');
      }
    };
  }
};

// 🔧 内存泄漏检测工具
class MemoryLeakDetector {
  constructor(store, warningThreshold = 100) {
    this.store = store;
    this.warningThreshold = warningThreshold;
    this.startMonitoring();
  }
  
  startMonitoring() {
    setInterval(() => {
      const listenerCount = this.store.listeners?.size || 0;
      if (listenerCount > this.warningThreshold) {
        console.warn(\`内存泄漏警告: 监听器数量异常(\${listenerCount})\`);
      }
    }, 5000);
  }
}`
    },
    {
      error: 'SSR hydration 不匹配',
      cause: '服务端和客户端的初始状态不一致，或没有提供getServerSnapshot',
      solution: '确保getServerSnapshot与客户端初始状态完全一致',
      prevention: '在开发环境中模拟SSR场景进行测试',
      code: `// ❌ 问题：SSR状态不一致
function BadSSRStore() {
  return useSyncExternalStore(
    store.subscribe,
    () => localStorage.getItem('data') || 'default', // 服务端没有localStorage
    // 缺少getServerSnapshot参数
  );
}

// ✅ 解决：正确的SSR处理
function GoodSSRStore() {
  return useSyncExternalStore(
    store.subscribe,
    () => {
      if (typeof window === 'undefined') return 'default';
      return localStorage.getItem('data') || 'default';
    },
    () => 'default' // 服务端快照与客户端初始状态一致
  );
}

// 🔧 SSR调试工具
function useSSRDebugger(storeName) {
  useEffect(() => {
    const isClient = typeof window !== 'undefined';
    console.log(\`[\${storeName}] 环境检测: \${isClient ? '客户端' : '服务端'}\`);
    
    if (isClient) {
      console.log(\`[\${storeName}] 客户端hydration完成\`);
    }
  }, [storeName]);
}`
    },
    {
      error: '状态更新丢失',
      cause: '多个异步更新竞争，或批处理机制工作不正确',
      solution: '使用适当的状态合并策略和版本控制',
      prevention: '添加状态更新日志和版本跟踪',
      code: `// ❌ 问题：状态更新竞争
class ProblematicStore {
  constructor() {
    this.state = { count: 0 };
  }
  
  // 可能丢失更新
  async updateCount() {
    const current = this.state.count;
    await delay(100);
    this.state = { count: current + 1 }; // 可能基于过期的值
  }
}

// ✅ 解决：使用版本控制
class SafeStore {
  constructor() {
    this.state = { count: 0, version: 0 };
    this.pendingUpdates = new Map();
  }
  
  updateCount = async (updateId = Date.now()) => {
    const currentVersion = this.state.version;
    
    // 记录待处理的更新
    this.pendingUpdates.set(updateId, {
      baseVersion: currentVersion,
      timestamp: Date.now()
    });
    
    await delay(100);
    
    // 检查版本是否仍然有效
    if (this.state.version === currentVersion) {
      this.state = {
        count: this.state.count + 1,
        version: currentVersion + 1
      };
      this.notifyListeners();
    } else {
      console.warn(\`更新\${updateId}被丢弃：基于过期版本\`);
    }
    
    this.pendingUpdates.delete(updateId);
  }
}

// 🔧 状态更新跟踪器
function useStateUpdateTracker(store) {
  const lastUpdate = useRef(Date.now());
  const updateCount = useRef(0);
  
  useEffect(() => {
    const unsubscribe = store.subscribe(() => {
      const now = Date.now();
      const timeSinceLastUpdate = now - lastUpdate.current;
      updateCount.current++;
      
      console.log(\`状态更新 #\${updateCount.current}: 距离上次更新 \${timeSinceLastUpdate}ms\`);
      lastUpdate.current = now;
    });
    
    return unsubscribe;
  }, [store]);
}`
    }
  ],
  
  devToolsTips: [
    {
      tool: 'React DevTools',
      technique: '使用Profiler追踪组件重渲染',
      example: '启动录制 → 触发状态变化 → 分析火焰图中的重渲染原因'
    },
    {
      tool: 'Chrome DevTools Memory',
      technique: '检测内存泄漏',
      example: '拍摄快照 → 操作应用 → 拍摄新快照 → 对比内存使用变化'
    },
    {
      tool: 'Console调试',
      technique: '添加调试日志',
      example: '在关键函数中添加日志，观察调用模式和异常情况'
    },
    {
      tool: 'Performance API',
      technique: '性能监控',
      example: '标记关键时间点，测量订阅建立、状态获取等操作的耗时'
    }
  ],
  
  troubleshooting: [
    {
      symptom: '组件不响应状态变化',
      possibleCauses: [
        'subscribe函数没有正确建立监听',
        'getSnapshot返回的值没有变化',
        '状态更新后没有触发通知'
      ],
      solutions: [
        '检查subscribe函数是否被正确调用',
        '验证状态更新后是否调用了所有监听器',
        '确认getSnapshot返回的值确实发生了变化',
        '使用console.log追踪订阅和通知流程'
      ]
    },
    {
      symptom: '性能问题，页面卡顿',
      possibleCauses: [
        'getSnapshot函数执行耗时过长',
        '频繁的重新订阅',
        '过多的组件同时重渲染'
      ],
      solutions: [
        '使用Performance API测量getSnapshot执行时间',
        '检查subscribe函数的调用频率',
        '使用React Profiler分析重渲染模式',
        '考虑使用选择器减少不必要的更新'
      ]
    },
    {
      symptom: '状态不一致或撕裂',
      possibleCauses: [
        '并发渲染时的状态竞争',
        '异步状态更新的时序问题',
        '缓存策略不当'
      ],
      solutions: [
        '启用React的Strict Mode检测并发问题',
        '添加状态版本号跟踪更新顺序',
        '检查是否存在异步状态更新竞争',
        '验证getSnapshot的原子性'
      ]
    }
  ]
};

export default debuggingTips; 