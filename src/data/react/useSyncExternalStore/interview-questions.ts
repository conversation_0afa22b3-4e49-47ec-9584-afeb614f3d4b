import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: 'useSyncExternalStore是什么？它解决了什么问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'useSyncExternalStore是React 18引入的Hook，用于订阅外部数据源，主要解决并发渲染下的状态撕裂问题。',
      detailed: `useSyncExternalStore是React 18.0版本引入的一个新Hook，专门用于订阅外部数据源。它的核心作用是：

1. **解决状态撕裂问题**：在React并发渲染模式下，确保所有组件看到的外部状态是一致的
2. **提供标准接口**：为第三方状态管理库（如Redux、Zustand、MobX等）提供与React并发特性兼容的标准订阅接口
3. **支持SSR场景**：通过getServerSnapshot参数解决服务端渲染时的hydration不匹配问题
4. **自动订阅管理**：自动处理订阅和取消订阅的生命周期，防止内存泄漏

这个Hook的出现主要是因为React 18引入的并发特性（如时间切片、Suspense等）可能导致不同组件在同一时间看到外部store的不同状态，从而产生状态撕裂问题。useSyncExternalStore通过特殊的内部机制确保状态的原子性更新。`,
      code: `import { useSyncExternalStore } from 'react';

// 基本用法
const snapshot = useSyncExternalStore(
  subscribe,      // 订阅函数
  getSnapshot,    // 获取快照函数
  getServerSnapshot? // 服务端快照函数（可选）
);

// 实际例子
function useExternalStore() {
  return useSyncExternalStore(
    // 订阅store变化
    (callback) => store.subscribe(callback),
    // 获取当前状态
    () => store.getState(),
    // SSR时的初始状态
    () => store.getInitialState()
  );
}`
    }
  },
  {
    id: 2,
    question: 'useSyncExternalStore的三个参数分别是什么？各有什么作用？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: '三个参数是subscribe（订阅函数）、getSnapshot（获取快照）、getServerSnapshot（服务端快照，可选），分别用于订阅变化、获取状态和SSR支持。',
      detailed: `useSyncExternalStore接收三个参数：

**1. subscribe: (onStoreChange: () => void) => () => void**
- 作用：订阅外部store的变化
- 参数：接收一个回调函数，当store发生变化时调用
- 返回值：必须返回一个清理函数，用于取消订阅
- 注意：这个函数应该是稳定的，建议用useCallback包装

**2. getSnapshot: () => any**
- 作用：获取当前store的快照（状态值）
- 返回值：返回当前的状态值
- 注意：必须返回相同引用的值（除非store确实变化了），否则会导致无限重渲染

**3. getServerSnapshot?: () => any（可选）**
- 作用：在SSR场景下获取服务端的初始状态
- 用途：避免hydration不匹配的警告
- 注意：如果提供，必须与客户端初始状态保持一致`,
      code: `// 详细示例
const store = createExternalStore();

function useStore() {
  return useSyncExternalStore(
    // 参数1：subscribe - 订阅store变化
    useCallback((callback) => {
      const unsubscribe = store.subscribe(callback);
      return unsubscribe; // 必须返回清理函数
    }, []),
    
    // 参数2：getSnapshot - 获取当前状态
    () => store.getState(),
    
    // 参数3：getServerSnapshot - SSR时的状态（可选）
    () => store.getInitialState()
  );
}`
    }
  },
  {
    id: 3,
    question: '什么是状态撕裂？useSyncExternalStore如何解决这个问题？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: '状态撕裂是指在并发渲染下不同组件看到不同的状态快照。useSyncExternalStore通过内部的版本号机制和双缓冲确保状态一致性。',
      detailed: `**状态撕裂（Tearing）问题**：

在React并发模式下，渲染过程可能被中断和恢复。如果在这个过程中外部store发生了变化，可能导致：
- 组件A看到旧状态：{ count: 1 }
- 组件B看到新状态：{ count: 2 }
- 这就是"撕裂"：不同组件看到了不一致的状态

**useSyncExternalStore的解决方案**：

1. **版本号机制**：内部维护一个版本号，每次状态变化时递增
2. **双缓冲策略**：使用两个缓冲区交替存储状态快照
3. **原子性检查**：在渲染期间检查状态是否发生变化，如果发生变化则重新开始渲染
4. **并发安全**：利用React内部的并发安全机制确保状态读取的原子性

这样确保在一次渲染周期内，所有组件都看到相同版本的状态快照。`,
      code: `// 问题示例：可能出现状态撕裂
let externalState = { count: 0 };
const listeners = new Set();

// 没有使用useSyncExternalStore，可能出现撕裂
function ComponentA() {
  const [state, setState] = useState(externalState);
  
  useEffect(() => {
    const callback = () => setState({...externalState});
    listeners.add(callback);
    return () => listeners.delete(callback);
  }, []);
  
  return <div>A: {state.count}</div>; // 可能显示旧值
}

function ComponentB() {
  const [state, setState] = useState(externalState);
  
  useEffect(() => {
    const callback = () => setState({...externalState});
    listeners.add(callback);
    return () => listeners.delete(callback);
  }, []);
  
  return <div>B: {state.count}</div>; // 可能显示新值
}

// 解决方案：使用useSyncExternalStore
const store = {
  getSnapshot: () => externalState,
  subscribe: (callback) => {
    listeners.add(callback);
    return () => listeners.delete(callback);
  }
};

function SafeComponent() {
  // 确保状态一致性，不会出现撕裂
  const state = useSyncExternalStore(
    store.subscribe,
    store.getSnapshot
  );
  
  return <div>Safe: {state.count}</div>;
}`
    }
  },
  {
    id: 4,
    question: '如何正确实现subscribe函数？有哪些注意事项？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '实战应用',
    answer: {
      brief: 'subscribe函数必须返回清理函数，应该是稳定引用，避免在每次渲染时创建新函数，正确处理错误情况。',
      detailed: `**正确实现subscribe函数的关键点**：

1. **必须返回清理函数**：防止内存泄漏
2. **保持引用稳定**：使用useCallback或定义在组件外部
3. **错误处理**：妥善处理订阅过程中可能出现的异常
4. **避免重复订阅**：确保同一个监听器不会被重复添加
5. **及时清理**：组件卸载时确保所有监听器都被移除

**常见错误**：
- 忘记返回清理函数
- 每次渲染都创建新的subscribe函数
- 没有处理异常情况
- 清理函数中的逻辑错误`,
      code: `// ❌ 错误实现
function BadExample() {
  return useSyncExternalStore(
    // 错误：每次渲染都创建新函数，导致重复订阅
    (callback) => {
      store.subscribe(callback);
      // 错误：没有返回清理函数，会导致内存泄漏
    },
    store.getSnapshot
  );
}

// ✅ 正确实现
function GoodExample() {
  // 方法1：使用useCallback确保引用稳定
  const subscribe = useCallback((callback) => {
    console.log('订阅store变化');
    
    try {
      // 添加监听器
      const unsubscribe = store.subscribe(callback);
      
      // 返回清理函数
      return () => {
        console.log('取消订阅');
        unsubscribe();
      };
    } catch (error) {
      console.error('订阅失败:', error);
      // 即使出错也要返回清理函数
      return () => {};
    }
  }, []);
  
  return useSyncExternalStore(
    subscribe,
    store.getSnapshot
  );
}

// 方法2：定义在组件外部（推荐）
const storeSubscribe = (callback) => {
  const listeners = store.listeners;
  listeners.add(callback);
  
  return () => {
    listeners.delete(callback);
  };
};

function BestExample() {
  return useSyncExternalStore(
    storeSubscribe, // 稳定引用
    store.getSnapshot
  );
}

// 复杂store的subscribe实现
class ComplexStore {
  constructor() {
    this.listeners = new Set();
    this.state = { count: 0 };
  }
  
  subscribe = (callback) => {
    // 防止重复订阅
    if (this.listeners.has(callback)) {
      console.warn('重复订阅同一个callback');
      return () => {}; // 返回空的清理函数
    }
    
    this.listeners.add(callback);
    console.log(\`当前监听者数量: \${this.listeners.size}\`);
    
    // 返回清理函数
    return () => {
      const deleted = this.listeners.delete(callback);
      if (deleted) {
        console.log(\`清理成功，剩余监听者: \${this.listeners.size}\`);
      }
    };
  }
  
  setState(newState) {
    this.state = { ...this.state, ...newState };
    // 通知所有监听者
    this.listeners.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('监听器执行出错:', error);
      }
    });
  }
  
  getSnapshot = () => this.state;
}`
    }
  },
  {
    id: 5,
    question: '在SSR场景下如何正确使用useSyncExternalStore？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '实战应用',
    answer: {
      brief: '必须提供getServerSnapshot参数，确保服务端和客户端初始状态一致，避免hydration不匹配警告。',
      detailed: `**SSR场景下的关键问题**：

1. **Hydration不匹配**：服务端渲染的内容和客户端初始渲染内容不一致
2. **环境差异**：服务端没有window、localStorage等浏览器API
3. **状态同步**：确保服务端和客户端看到相同的初始状态

**解决方案**：

1. **必须提供getServerSnapshot**：避免hydration警告
2. **保持状态一致性**：确保服务端和客户端的初始状态完全相同
3. **环境检查**：在getSnapshot中处理服务端环境的特殊情况
4. **延迟订阅**：在客户端hydration完成后再建立订阅`,
      code: `// ❌ 错误：SSR时会有hydration警告
function BadSSRExample() {
  return useSyncExternalStore(
    store.subscribe,
    store.getSnapshot
    // 缺少getServerSnapshot参数
  );
}

// ✅ 正确：SSR兼容实现
function GoodSSRExample() {
  return useSyncExternalStore(
    store.subscribe,
    // 客户端获取状态
    () => {
      // 环境检查
      if (typeof window === 'undefined') {
        return store.getInitialState();
      }
      return store.getSnapshot();
    },
    // 服务端获取状态
    () => store.getInitialState()
  );
}

// 复杂SSR场景：localStorage状态管理
function useLocalStorageState(key, defaultValue) {
  const subscribe = useCallback((callback) => {
    // 服务端没有window，直接返回空清理函数
    if (typeof window === 'undefined') {
      return () => {};
    }
    
    const handleStorage = (e) => {
      if (e.key === key) {
        callback();
      }
    };
    
    window.addEventListener('storage', handleStorage);
    return () => window.removeEventListener('storage', handleStorage);
  }, [key]);
  
  const getSnapshot = () => {
    if (typeof window === 'undefined') {
      return defaultValue;
    }
    
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('localStorage读取失败:', error);
      return defaultValue;
    }
  };
  
  const getServerSnapshot = () => defaultValue;
  
  return useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);
}

// 使用示例
function UserPreferences() {
  const theme = useLocalStorageState('theme', 'light');
  const language = useLocalStorageState('language', 'zh-CN');
  
  return (
    <div className={\`theme-\${theme}\`}>
      <p>当前主题: {theme}</p>
      <p>当前语言: {language}</p>
    </div>
  );
}

// Redux + SSR示例
function useReduxWithSSR() {
  const store = useStore(); // Redux store
  
  return useSyncExternalStore(
    // 订阅Redux store
    useCallback((callback) => store.subscribe(callback), [store]),
    // 客户端获取状态
    () => store.getState(),
    // 服务端获取状态（确保与客户端初始状态一致）
    () => store.getState()
  );
}`
    }
  },
  {
    id: 6,
    question: 'useSyncExternalStore和useState/useReducer有什么区别？什么时候使用？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '对比分析',
    answer: {
      brief: 'useSyncExternalStore专用于外部状态管理，而useState/useReducer用于组件内部状态。前者提供并发安全保证，后者更简单但不能解决状态撕裂问题。',
      detailed: `**核心区别**：

**useSyncExternalStore**：
- 用途：订阅外部数据源（Redux、Zustand、全局变量等）
- 并发安全：专门解决并发渲染下的状态撕裂问题
- 跨组件：多个组件可以订阅同一个外部store
- SSR支持：原生支持服务端渲染场景
- 复杂度：需要实现subscribe和getSnapshot函数

**useState/useReducer**：
- 用途：管理组件内部状态
- 并发安全：React内部处理，但不能防止外部状态撕裂
- 作用域：仅限于单个组件及其子组件
- SSR支持：天然支持，无需特殊处理
- 复杂度：使用简单，API直观

**使用场景**：

**选择useSyncExternalStore的情况**：
- 需要订阅第三方状态管理库
- 需要订阅浏览器API（localStorage、window大小等）
- 需要订阅WebSocket或其他外部数据流
- 多个组件需要共享同一份状态
- 需要在并发模式下确保状态一致性

**选择useState/useReducer的情况**：
- 管理组件内部状态
- 简单的表单状态管理
- 临时的UI状态（loading、error等）
- 不需要跨组件共享的状态`,
      code: `// useState示例：组件内部状态
function Counter() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <span>{count}</span>
      <button onClick={() => setCount(c => c + 1)}>+1</button>
    </div>
  );
}

// useSyncExternalStore示例：外部状态
const globalCounter = {
  value: 0,
  listeners: new Set(),
  
  increment() {
    this.value++;
    this.listeners.forEach(listener => listener());
  },
  
  subscribe(callback) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  },
  
  getSnapshot() {
    return this.value;
  }
};

function GlobalCounter() {
  const count = useSyncExternalStore(
    globalCounter.subscribe.bind(globalCounter),
    globalCounter.getSnapshot.bind(globalCounter)
  );
  
  return (
    <div>
      <span>{count}</span>
      <button onClick={() => globalCounter.increment()}>+1</button>
    </div>
  );
}

// 对比：多组件状态同步
// useState方式：需要状态提升
function App() {
  const [sharedState, setSharedState] = useState(0);
  
  return (
    <div>
      <ComponentA state={sharedState} setState={setSharedState} />
      <ComponentB state={sharedState} setState={setSharedState} />
    </div>
  );
}

// useSyncExternalStore方式：直接订阅
function ComponentA() {
  const state = useSyncExternalStore(
    store.subscribe,
    store.getSnapshot
  );
  // 直接使用，无需props传递
}

function ComponentB() {
  const state = useSyncExternalStore(
    store.subscribe,
    store.getSnapshot
  );
  // 自动保持同步
}

// 性能对比
// useState：只影响当前组件及其子组件
function LocalStateExample() {
  const [localState, setLocalState] = useState(0);
  // 状态变化只会重渲染当前组件树
}

// useSyncExternalStore：可能影响所有订阅的组件
function ExternalStateExample() {
  const externalState = useSyncExternalStore(
    store.subscribe,
    store.getSnapshot
  );
  // 外部状态变化会影响所有订阅的组件
}`
    }
  }
];

export default interviewQuestions; 