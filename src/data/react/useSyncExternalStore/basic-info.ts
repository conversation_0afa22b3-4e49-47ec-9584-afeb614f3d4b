import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "useSyncExternalStore是React 18中专门用于订阅外部数据源的Hook，解决并发渲染下的状态撕裂问题，确保数据一致性",
  
  introduction: `
## 🎯 **战略定位**

useSyncExternalStore是React 18.0的**并发安全核心组件**，专为现代复杂应用的外部状态管理而设计。它不仅仅是一个Hook，更是React生态系统向**混合状态管理时代**迈进的里程碑。

## 💡 **核心价值主张**

在React的并发特性（Concurrent Features）环境下，传统的外部状态订阅会导致**状态撕裂**（State Tearing）问题——同一次渲染中不同组件可能看到不同的状态快照。useSyncExternalStore通过**双重快照机制**和**版本控制策略**，确保状态观察的**原子性一致性**。

## 🏗️ **技术创新点**

- **🛡️ 并发安全保障**：内置状态撕裂预防机制
- **⚡ 零配置集成**：两个函数即可完成复杂的外部状态管理  
- **🌐 SSR原生支持**：通过getServerSnapshot参数解决同构应用痛点
- **🎯 生态系统友好**：为Redux、Zustand等状态库提供标准接口
- **📊 性能优化内置**：智能的引用比较和批量更新机制

## 🚀 **适用场景矩阵**

| 场景类型 | 复杂度 | 推荐指数 | 典型应用 |
|---------|--------|----------|----------|
| 浏览器API订阅 | 🟢 简单 | ⭐⭐⭐⭐⭐ | 窗口大小、在线状态、地理位置 |
| 第三方状态库 | 🟡 中等 | ⭐⭐⭐⭐⭐ | Redux、Zustand、Valtio集成 |
| 实时数据流 | 🔴 复杂 | ⭐⭐⭐⭐ | WebSocket、SSE、轮询数据 |
| 全局配置管理 | 🟡 中等 | ⭐⭐⭐⭐ | 主题、语言、用户偏好 |`,
  
  syntax: `const snapshot = useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot?)`,
  
  parameters: [
    {
      name: "subscribe",
      type: "(onStoreChange: () => void) => () => void",
      required: true,
      description: "🔔 **订阅函数** - 建立与外部store的监听关系，接收变化回调，返回清理函数",
      default: "无",
      details: "⚠️ **关键要求**：必须返回unsubscribe函数避免内存泄漏；建议使用useCallback包装避免重复订阅"
    },
    {
      name: "getSnapshot",
      type: "() => any",
      required: true,
      description: "📸 **快照函数** - 同步获取当前store状态的纯函数，React内部会频繁调用",
      default: "无",
      details: "🎯 **性能要点**：返回值必须具有稳定引用（推荐immutable数据），避免Object.is比较失败导致无限渲染"
    },
    {
      name: "getServerSnapshot",
      type: "() => any",
      required: false,
      description: "🌐 **服务端快照** - SSR场景下的状态获取函数，确保hydration一致性",
      default: "undefined",
      details: "🔧 **SSR必需**：如果应用使用SSR，此参数是必需的；服务端和客户端必须返回相同的初始值"
    }
  ],
  
  returnValue: {
    type: "any",
    description: "🎯 返回当前外部store的状态快照。当外部状态发生变化时，Hook会触发组件重新渲染并返回最新快照"
  },
  
  keyFeatures: [
    {
      feature: "🛡️ 并发安全保障",
      description: "解决React 18并发渲染环境下的状态撕裂问题，确保状态观察的一致性",
      importance: "critical",
      details: "通过快照版本控制和双重检查机制，保证同一次渲染周期内所有组件观察到相同的状态快照，避免时间切片导致的状态不一致"
    },
    {
      feature: "🔌 外部状态桥接",
      description: "为第三方状态管理库提供React兼容的标准接口，实现生态无缝集成",
      importance: "high",
      details: "专为Redux、Zustand、MobX、Valtio等外部状态库设计，提供统一的订阅模式和状态获取接口"
    },
    {
      feature: "🌐 SSR原生支持",
      description: "通过getServerSnapshot参数原生支持服务端渲染，避免hydration不匹配问题",
      importance: "high",
      details: "解决了SSR应用中服务端状态与客户端状态不一致的经典问题，支持Next.js、Remix等全栈框架"
    },
    {
      feature: "⚡ 智能性能优化",
      description: "内置引用比较优化和批量更新机制，最小化不必要的重渲染",
      importance: "medium",
      details: "使用Object.is进行高效的浅比较，自动批处理同步状态更新，与React的并发特性深度集成"
    },
    {
      feature: "🧹 自动资源管理",
      description: "自动处理订阅生命周期，包括组件挂载时订阅和卸载时清理",
      importance: "medium",
      details: "基于React的Effect系统，自动管理外部资源的获取和释放，防止内存泄漏和资源浪费"
    }
  ],
  
  limitations: [
    "⚠️ **适用范围限制**：仅适用于外部状态管理，不应用于组件内部状态",
    "🔄 **引用稳定性要求**：getSnapshot必须返回稳定引用，否则导致无限重渲染",
    "🧹 **清理函数必需**：subscribe必须返回清理函数，否则造成严重内存泄漏",
    "🌐 **SSR依赖性**：服务端渲染场景下getServerSnapshot是必需的，增加复杂度",
    "📊 **调试复杂度**：外部状态变化的调试和追踪相比内部状态更加困难"
  ],
  
  bestPractices: [
    "🎯 **订阅函数优化**：使用useCallback包装subscribe函数，避免不必要的重新订阅",
    "📸 **快照稳定性**：确保getSnapshot返回immutable数据或使用正确的比较逻辑",
    "🌐 **SSR一致性**：在SSR应用中必须提供getServerSnapshot，且与客户端初始状态保持一致",
    "⚡ **性能配合**：可配合useMemo优化复杂快照计算，避免每次渲染重复计算",
    "🔧 **TypeScript增强**：为外部store和snapshot提供准确的类型定义，提升开发体验",
    "🧪 **测试策略**：为外部store编写独立的单元测试，确保subscribe和getSnapshot逻辑正确"
  ],
  
  warnings: [
    "🚨 **致命错误**：在getSnapshot中直接修改store状态会导致无限循环渲染",
    "💀 **内存泄漏**：subscribe函数忘记返回清理函数将导致严重的内存泄漏问题",
    "🔄 **重复订阅**：避免在每次渲染时创建新的subscribe函数，会导致频繁的重新订阅",
    "⏱️ **时序问题**：不要依赖getSnapshot的调用时机，React可能在任何时候调用它",
    "🌐 **SSR陷阱**：getServerSnapshot与客户端状态不一致会导致hydration错误和界面闪烁"
  ]
};

export default basicInfo; 