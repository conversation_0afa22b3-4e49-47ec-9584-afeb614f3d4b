import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  timeline: [
    {
      year: '2013',
      event: 'React首次发布',
      description: 'Facebook开源React，引入虚拟DOM概念，但状态管理主要依赖组件内部状态',
      significance: '奠定了声明式UI编程的基础，但外部状态管理尚未成为主要关注点'
    },
    {
      year: '2015',
      event: 'Redux生态兴起',
      description: 'Dan Abramov发布Redux，提供可预测的状态管理容器，React社区开始重视外部状态',
      significance: '标志着React生态向大型应用状态管理的重大转向，奠定了外部状态管理的理论基础'
    },
    {
      year: '2016',
      event: 'MobX等响应式方案出现',
      description: '基于响应式编程的状态管理库开始出现，提供了与Redux不同的状态管理哲学',
      significance: '证明了响应式状态管理的可行性，为后续的自动订阅机制提供了理论支撑'
    },
    {
      year: '2019',
      event: 'React Hooks发布',
      description: 'React 16.8引入Hooks，改变了状态管理的范式，但外部状态集成仍有挑战',
      significance: '函数式组件成为主流，为新的状态管理模式提供了技术基础'
    },
    {
      year: '2020',
      event: 'Zustand、Valtio等新兴状态库',
      description: '新一代轻量级状态管理库出现，强调简洁API和React Hooks的深度集成',
      significance: '推动了状态管理库向更简洁、更React友好的方向发展'
    },
    {
      year: '2021',
      event: 'React 18 Concurrent特性预告',
      description: 'React团队公布并发渲染特性，发现了状态撕裂（State Tearing）问题',
      significance: '首次明确识别了并发渲染环境下外部状态的一致性挑战，成为useSyncExternalStore的直接驱动力'
    },
    {
      year: '2021-Q2',
      event: 'useMutableSource实验性API',
      description: 'React团队发布实验性的useMutableSource，试图解决外部状态同步问题',
      significance: '首次尝试提供官方的外部状态订阅解决方案，但API复杂度较高，为简化设计提供了经验教训'
    },
    {
      year: '2021-Q4',
      event: 'useSyncExternalStore RFC',
      description: 'React团队发布useSyncExternalStore RFC，基于社区反馈重新设计API',
      significance: '标志着从实验性方案向生产就绪方案的关键转折，API设计更加简洁和实用'
    },
    {
      year: '2022-Q1',
      event: 'React 18正式发布',
      description: 'useSyncExternalStore作为稳定API正式发布，成为并发渲染的重要组成部分',
      significance: '标志着React正式进入并发时代，外部状态管理获得了原生支持'
    },
    {
      year: '2022-Q3',
      event: '生态系统大规模适配',
      description: 'Redux、Zustand、Jotai等主流状态库开始基于useSyncExternalStore重构',
      significance: '证明了API设计的成功，成为外部状态管理的事实标准'
    },
    {
      year: '2023-Q1',
      event: 'Next.js、Remix深度集成',
      description: '全栈框架开始原生支持useSyncExternalStore，解决SSR场景下的状态同步问题',
      significance: '从前端Hook演进为全栈开发的基础设施，影响力扩展到整个React生态'
    },
    {
      year: '2023-Q4',
      event: 'React Server Components兼容',
      description: 'useSyncExternalStore与React Server Components的兼容性优化完成',
      significance: '为React的未来架构演进提供了坚实的状态管理基础'
    }
  ],
  
  background: `## 🏛️ **历史背景：从理想到现实的妥协之路**

### 📚 **React状态管理的哲学演进**

**🎯 初期理想主义阶段（2013-2015）**
React最初的设计哲学极其纯粹：组件是纯函数，props是输入，state是局部状态，一切都在React的掌控之下。Facebook的工程师们相信"单向数据流"能解决所有状态管理问题。这种理想主义的设计确实优雅，但很快就遇到了现实世界的复杂性挑战。

**🌍 现实复杂性的冲击（2015-2019）**
随着React应用规模的增长，开发者发现纯粹的组件状态管理难以应对复杂场景：
- 跨组件状态共享导致props drilling地狱
- 全局状态管理需求催生了Redux等外部状态库
- 浏览器API、第三方服务、WebSocket等外部数据源无法纳入React的状态管理体系

**🔄 Hook时代的新机遇（2019-2021）**
React Hooks的引入为状态管理带来了新的可能性。函数式组件配合自定义Hook，让状态逻辑的复用变得简单。但这也暴露了一个新问题：如何让外部状态库与Hooks深度集成？

**⚡ 并发渲染的挑战（2021）**
React 18并发特性的开发过程中，团队发现了一个严重问题：**状态撕裂**（State Tearing）。在并发渲染环境下，同一次渲染中不同组件可能观察到外部状态的不同快照，导致UI不一致。

### 🧬 **设计哲学的三次重大转变**

**第一次转变：从内部状态到外部状态**
- **驱动力**：大型应用的状态管理复杂性
- **结果**：Redux等外部状态库的兴起
- **影响**：React生态从纯粹走向混合

**第二次转变：从Class组件到Hooks**
- **驱动力**：函数式编程的优势和状态逻辑复用的需求
- **结果**：React Hooks成为主流
- **影响**：状态管理模式的根本性改变

**第三次转变：从同步渲染到并发渲染**
- **驱动力**：用户体验的极致追求和性能优化需求
- **结果**：useSyncExternalStore的诞生
- **影响**：外部状态管理获得原生支持

### 🎭 **技术决策背后的深层思考**

**为什么是"Sync"而不是"Async"？**
这个命名体现了React团队的深度思考。虽然状态订阅是异步的，但getSnapshot必须是同步的。这种设计确保了：
1. **一致性保证**：同一次渲染中所有组件看到相同的状态快照
2. **性能优化**：避免异步状态获取导致的瀑布式渲染
3. **调试友好**：同步获取使得状态变化的时序更加可预测

**为什么需要getServerSnapshot？**
这个参数的存在体现了React团队对全栈开发的深刻理解：
1. **SSR现实需求**：现代Web应用大多使用服务端渲染
2. **一致性挑战**：服务端和客户端的状态初始化往往不同
3. **用户体验**：避免hydration不匹配导致的界面闪烁

### 🌊 **更深层的技术演进趋势**

useSyncExternalStore的出现不是孤立事件，而是整个前端技术演进的必然结果：

**1. 架构复杂度的螺旋上升**
- 简单应用 → 复杂应用
- 单一状态源 → 多状态源协调
- 同步渲染 → 并发渲染

**2. 抽象层次的不断提升**
- 直接DOM操作 → 虚拟DOM
- 命令式编程 → 声明式编程  
- 手动状态管理 → 自动状态同步

**3. 开发体验的持续优化**
- 复杂API → 简洁API
- 手动优化 → 自动优化
- 割裂的生态 → 统一的标准

### 🔮 **历史经验与未来预测**

从useSyncExternalStore的演进历程，我们可以总结出前端技术发展的几个规律：

**技术成熟度曲线**：实验性API（useMutableSource）→ 社区反馈 → 简化重设计 → 正式发布 → 生态采纳

**复杂性管理策略**：将复杂性从用户代码转移到框架内部，通过简洁的API隐藏实现复杂度

**生态系统效应**：成功的API不仅解决技术问题，更要能够统一生态系统，降低整体学习成本

这种历史视角告诉我们：useSyncExternalStore不仅仅是一个Hook，它是React生态系统走向成熟的重要里程碑，标志着前端开发从"能用"向"好用"的重大跃升。`,
  
  evolution: `## 🔄 **技术演进的深层逻辑**

### 🧠 **认知复杂度的转移策略**

useSyncExternalStore的演进体现了一个重要的软件工程原则：**认知复杂度的战略性转移**。

**从开发者认知负担到框架内部复杂度**：
- **转移前**：开发者需要理解并发渲染、状态撕裂、订阅管理等复杂概念
- **转移后**：开发者只需提供两个简单函数：subscribe + getSnapshot
- **净效果**：总复杂度不变，但集中到框架核心，受益整个生态系统

### ⚡ **性能与正确性的平衡艺术**

**第一代方案（手动集成）**：
- 性能：✅ 高性能（手动优化）
- 正确性：❌ 容易出错（状态撕裂风险）
- 开发体验：❌ 复杂（需要深度理解并发机制）

**第二代方案（useMutableSource）**：
- 性能：✅ 高性能（内置优化）
- 正确性：✅ 正确性保证
- 开发体验：⚠️ 中等（API复杂度较高）

**第三代方案（useSyncExternalStore）**：
- 性能：✅ 高性能（智能优化）
- 正确性：✅ 强一致性保证
- 开发体验：✅ 优秀（极简API）

### 🎯 **API设计的哲学进化**

**从配置化到约定化**：
- **useMutableSource时代**：提供大量配置选项，试图满足所有用例
- **useSyncExternalStore时代**：通过合理约定减少配置，核心用例开箱即用

**从暴露复杂性到隐藏复杂性**：
- **早期方案**：将并发渲染的复杂性暴露给开发者
- **成熟方案**：将复杂性封装在框架内部，提供简洁的抽象

**从功能完备到职责单一**：
- **大而全的API**：试图解决状态管理的所有问题
- **职责清晰的API**：专注于外部状态订阅这一核心问题

### 🌊 **生态系统的网络效应**

useSyncExternalStore的成功不仅在于技术优势，更在于它创造了强大的网络效应：

**1. 标准化红利**
- 所有状态库使用相同的底层API
- 开发者只需学习一套订阅模式
- 工具链和调试器可以统一优化

**2. 生态协同效应**
- Redux、Zustand、Jotai等库的集成降低了迁移成本
- Next.js、Remix等框架的原生支持提升了开发体验
- React DevTools的深度集成改善了调试体验

**3. 创新促进机制**
- 标准化的基础设施释放了上层创新的空间
- 状态库可以专注于DX优化而非底层实现
- 新的状态管理模式（如signals）可以轻松集成

### 🔬 **技术债务的主动管理**

React团队在useSyncExternalStore的开发中展现了出色的技术债务管理：

**主动废弃实验性API**：
- 及时发现useMutableSource的设计问题
- 果断废弃并重新设计，避免长期技术债务
- 通过公开RFC征集社区意见，确保新方案的可行性

**向后兼容的渐进策略**：
- 提供polyfill确保旧版本React的兼容性
- 渐进式迁移路径，降低升级成本
- 详细的迁移指南和最佳实践文档

**前瞻性设计预留**：
- API设计考虑了未来的扩展需求
- 与React Server Components的兼容性预设
- 为并发特性的进一步发展预留了空间

### 🎭 **设计决策的多维度权衡**

每个API设计决策都体现了多个维度的深度权衡：

**subscribe函数的设计**：
- **简洁性** vs **功能性**：选择了极简设计，只要求返回取消订阅函数
- **性能** vs **易用性**：要求用户使用useCallback优化，而非内置复杂的优化逻辑
- **类型安全** vs **灵活性**：TypeScript类型设计在安全性和灵活性间找到平衡

**getSnapshot函数的约束**：
- **正确性** vs **性能**：强制要求引用稳定性，确保正确性优先
- **自动化** vs **控制力**：将引用比较的决策权交给用户，而非自动深度比较
- **调试友好** vs **抽象程度**：保持函数的透明性，便于调试和理解

这种多维度权衡的结果是一个既简洁又强大、既安全又高效的API设计，代表了React团队在API设计上的成熟思考。`,
  
  keyFigures: [
    {
      name: 'Sebastian Markbåge',
      role: 'React核心团队成员',
      contribution: '并发渲染架构的主要设计者，状态撕裂问题的发现者和useSyncExternalStore的最初提出者',
      significance: '从底层架构层面奠定了useSyncExternalStore的技术基础，确保了并发安全的实现可能性'
    },
    {
      name: 'Dan Abramov',
      role: 'React核心团队成员、Redux作者',
      contribution: '作为状态管理领域的专家，深度参与了API设计讨论，确保了与现有生态系统的兼容性',
      significance: '桥接了Redux生态系统与React官方解决方案，推动了生态系统的统一标准化'
    },
    {
      name: 'Andrew Clark',
      role: 'React核心团队成员',
      contribution: 'React 18并发特性的核心开发者，负责useSyncExternalStore的具体实现和优化',
      significance: '将理论设计转化为高性能的生产级实现，确保了API的性能和稳定性'
    },
    {
      name: 'Brian Vaughn',
      role: 'React DevTools维护者',
      contribution: '为useSyncExternalStore提供了调试工具支持，改善了开发者体验',
      significance: '通过工具支持降低了学习曲线，促进了API的广泛采用'
    },
    {
      name: 'Daishi Kato',
      role: 'Zustand、Valtio作者',
      contribution: '作为状态管理库作者，积极参与RFC讨论，提供了大量实际使用场景的反馈',
      significance: '代表社区开发者的声音，确保API设计贴近实际开发需求'
    },
    {
      name: 'Mark Erikson',
      role: 'Redux维护者',
      contribution: '推动Redux生态系统采用useSyncExternalStore，编写了大量集成指南和最佳实践',
      significance: '确保了最大的状态管理生态系统与新API的无缝集成，降低了迁移成本'
    }
  ],
  
  concepts: [
    {
      term: '状态撕裂（State Tearing）',
      definition: '在并发渲染环境下，同一次渲染中不同组件观察到外部状态的不同快照，导致UI状态不一致的现象',
      evolution: '从React 18并发特性开发中发现的新问题 → 推动useSyncExternalStore的诞生 → 成为现代状态管理必须考虑的核心问题',
      modernRelevance: '现代前端应用的基础挑战，任何外部状态管理方案都必须解决这个问题，影响了整个React生态系统的架构设计'
    },
    {
      term: '并发安全（Concurrent Safety）',
      definition: '在React并发渲染模式下，确保状态读取和更新的一致性，避免时间切片导致的状态不一致',
      evolution: '从传统的同步渲染安全 → 异步渲染兼容 → 时间切片安全 → 中断恢复安全',
      modernRelevance: '现代React应用的必备特性，决定了应用在高负载情况下的稳定性和用户体验'
    },
    {
      term: '快照一致性（Snapshot Consistency）',
      definition: '确保同一次渲染周期内所有组件观察到的外部状态快照完全一致的机制',
      evolution: '从数据库的快照隔离概念 → 前端状态管理领域 → React并发渲染的核心保证',
      modernRelevance: '分布式前端应用状态管理的理论基础，为复杂状态协调提供了一致性模型'
    },
    {
      term: '订阅-发布模式在React中的演进',
      definition: '从传统的事件监听模式演进为React组件生命周期感知的智能订阅模式',
      evolution: '传统事件监听 → React类组件的componentDidMount/Unmount → useEffect的依赖管理 → useSyncExternalStore的自动化订阅',
      modernRelevance: '现代响应式编程在React中的标准实现，影响了整个前端生态的状态管理模式'
    },
    {
      term: 'SSR状态同步（SSR State Synchronization）',
      definition: '确保服务端渲染的初始状态与客户端hydration后的状态完全一致的技术机制',
      evolution: '从简单的数据序列化 → 状态脱水/重水化 → 智能的服务端/客户端状态协调',
      modernRelevance: '全栈React应用的核心挑战，直接影响SEO性能、首屏加载体验和用户感知性能'
    },
    {
      term: '引用稳定性（Reference Stability）',
      definition: '确保状态对象的引用在值不变时保持稳定，避免不必要的重渲染的优化策略',
      evolution: '从性能优化技巧 → React.memo的前提条件 → useSyncExternalStore的强制要求',
      modernRelevance: '现代React性能优化的基石，影响了状态管理库的设计和最佳实践的制定'
    }
  ]
};

export default knowledgeArchaeology; 