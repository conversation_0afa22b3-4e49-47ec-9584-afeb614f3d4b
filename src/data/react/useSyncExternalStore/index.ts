import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useSyncExternalStoreData: ApiItem = {
  id: 'useSyncExternalStore',
  title: 'useSyncExternalStore Hook',
  description: 'React 18 引入的用于订阅外部数据源的 Hook，提供线程安全的数据同步机制',
  category: 'React Hooks',
  difficulty: 'medium',
  syntax: 'const snapshot = useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot?)',
  example: `import { useSyncExternalStore } from 'react';

// 订阅外部store
const useStore = () => {
  return useSyncExternalStore(
    store.subscribe,    // 订阅函数
    store.getSnapshot,  // 获取快照
    store.getSnapshot   // 服务端快照
  );
};

function App() {
  const data = useStore();
  return <div>数据: {JSON.stringify(data)}</div>;
}`,
  notes: '专为外部状态管理库设计，支持并发特性和SSR场景',
  version: '18.0.0',
  tags: ['外部数据源', '并发安全', 'SSR', '状态订阅'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useSyncExternalStoreData; 