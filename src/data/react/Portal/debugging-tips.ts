import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'Portal开发中经常遇到的问题主要集中在DOM容器管理、事件处理、服务端渲染兼容性和性能优化等方面。本节提供了常见问题的快速诊断和解决方案，帮助开发者高效排查Portal相关bug。',
        sections: [
          {
            title: 'Portal容器和DOM问题',
            description: '最常见的Portal问题往往与DOM容器的创建、管理和清理相关，包括容器不存在、重复创建、清理不当等问题',
            items: [
              {
                title: 'Portal容器不存在错误',
                description: '尝试将Portal渲染到不存在的DOM节点时发生的错误，通常在组件早期渲染或服务端渲染环境中出现',
                solution: '添加容器存在性检查，使用useEffect确保容器在Portal使用前创建，实现容器的动态创建和管理',
                prevention: '使用自定义Hook管理Portal容器生命周期，避免直接操作DOM，在组件卸载时正确清理容器',
                code: `// ❌ 问题代码：直接使用可能不存在的容器
function ProblematicPortal({ children }) {
  return ReactDOM.createPortal(
    children,
    document.getElementById('portal-root') // 可能返回null
  );
}

// ✅ 解决方案：安全的Portal Hook
function useSafePortal(containerId = 'portal-root') {
  const [container, setContainer] = useState(null);

  useEffect(() => {
    let portalContainer = document.getElementById(containerId);
    
    // 如果容器不存在，创建它
    if (!portalContainer) {
      portalContainer = document.createElement('div');
      portalContainer.id = containerId;
      portalContainer.className = 'portal-container';
      document.body.appendChild(portalContainer);
    }
    
    setContainer(portalContainer);

    return () => {
      // 清理：如果是我们创建的容器且没有其他内容，移除它
      if (portalContainer && 
          portalContainer.id === containerId && 
          portalContainer.children.length === 0 &&
          document.body.contains(portalContainer)) {
        document.body.removeChild(portalContainer);
      }
    };
  }, [containerId]);

  return container;
}

// 使用安全的Portal
function SafePortal({ children, containerId }) {
  const container = useSafePortal(containerId);

  if (!container) {
    return null; // 容器未准备好时不渲染
  }

  return ReactDOM.createPortal(children, container);
}

// 调试工具：Portal容器状态检查
function usePortalDebug(containerId) {
  useEffect(() => {
    const container = document.getElementById(containerId);
    console.log('Portal调试信息:', {
      containerId,
      exists: !!container,
      childrenCount: container?.children.length || 0,
      classList: container?.className || 'N/A',
      parentNode: container?.parentNode?.tagName || 'N/A'
    });
  }, [containerId]);
}`
              },
              {
                title: '事件冒泡和传播问题',
                description: '事件在Portal内外传播时的异常行为，包括事件不冒泡、冒泡到错误的父元素、或事件处理函数未被调用',
                solution: '理解React合成事件和原生事件的差异，正确设置事件监听器，使用事件委托和stopPropagation控制事件流',
                prevention: '在Portal组件中明确事件边界，避免混用合成事件和原生事件，测试事件在不同DOM层级的行为',
                code: `// ❌ 问题：事件冒泡混乱
function ProblematicModal({ onClose }) {
  // 原生事件监听器不会按React组件树冒泡
  useEffect(() => {
    const handleClick = (e) => {
      console.log('这个事件可能不会按预期冒泡');
      onClose();
    };
    
    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, [onClose]);

  return ReactDOM.createPortal(
    <div className="modal-backdrop">
      <div className="modal-content">
        模态框内容
      </div>
    </div>,
    document.body
  );
}

// ✅ 解决方案：正确的事件处理
function CorrectModal({ onClose, children }) {
  const handleBackdropClick = (e) => {
    // React合成事件会正确冒泡
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleEscapeKey = useCallback((e) => {
    if (e.key === 'Escape') {
      onClose();
    }
  }, [onClose]);

  useEffect(() => {
    // 全局键盘事件需要使用原生事件
    document.addEventListener('keydown', handleEscapeKey);
    return () => document.removeEventListener('keydown', handleEscapeKey);
  }, [handleEscapeKey]);

  return ReactDOM.createPortal(
    <div 
      className="modal-backdrop" 
      onClick={handleBackdropClick} // React合成事件
    >
      <div 
        className="modal-content"
        onClick={(e) => e.stopPropagation()} // 阻止冒泡到背景
      >
        {children}
      </div>
    </div>,
    document.body
  );
}

// 调试工具：事件流追踪
function useEventDebug(elementRef, eventTypes = ['click', 'keydown']) {
  useEffect(() => {
    if (!elementRef.current) return;

    const handlers = eventTypes.map(type => {
      const handler = (e) => {
        console.log(\`事件调试 - \${type}:\`, {
          type: e.type,
          target: e.target.tagName + (e.target.className ? '.' + e.target.className : ''),
          currentTarget: e.currentTarget.tagName,
          bubbles: e.bubbles,
          eventPhase: e.eventPhase,
          timeStamp: e.timeStamp
        });
      };

      elementRef.current.addEventListener(type, handler, true); // 捕获阶段
      return { type, handler };
    });

    return () => {
      handlers.forEach(({ type, handler }) => {
        if (elementRef.current) {
          elementRef.current.removeEventListener(type, handler, true);
        }
      });
    };
  }, [elementRef, eventTypes]);
}`
              },
              {
                title: 'SSR和Hydration不匹配',
                description: '服务端渲染环境中Portal导致的hydration错误，包括DOM结构不匹配、Portal内容缺失或重复等问题',
                solution: '使用客户端专用Portal组件，延迟Portal渲染到hydration完成后，实现SSR安全的Portal Hook',
                prevention: '在SSR应用中始终使用延迟渲染策略，避免在服务端执行Portal相关代码，提供fallback UI',
                code: `// ❌ 问题：SSR环境中的Portal错误
function ProblematicSSRPortal({ children }) {
  // 服务端没有document.body，会导致hydration不匹配
  return ReactDOM.createPortal(children, document.body);
}

// ✅ 解决方案：SSR安全的Portal
function SSRSafePortal({ children, fallback = null }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 服务端和初次渲染返回fallback
  if (!mounted) {
    return fallback;
  }

  // 客户端hydration后渲染Portal
  return ReactDOM.createPortal(children, document.body);
}

// 高级解决方案：带状态同步的SSR Portal
function AdvancedSSRPortal({ children, isOpen, onClose }) {
  const [mounted, setMounted] = useState(false);
  const [clientOpen, setClientOpen] = useState(false);

  useEffect(() => {
    setMounted(true);
    setClientOpen(isOpen);
  }, [isOpen]);

  // 服务端渲染占位元素
  if (!mounted) {
    return (
      <div 
        style={{ display: 'none' }} 
        data-portal-placeholder
        data-portal-state={isOpen ? 'open' : 'closed'}
      >
        Portal Placeholder
      </div>
    );
  }

  if (!clientOpen) return null;

  return ReactDOM.createPortal(
    <div className="modal-backdrop">
      <div className="modal-content">
        {children}
        <button onClick={onClose}>关闭</button>
      </div>
    </div>,
    document.body
  );
}

// Next.js专用解决方案
import dynamic from 'next/dynamic';

const DynamicPortal = dynamic(
  () => import('./Portal'),
  { 
    ssr: false, // 禁用SSR
    loading: () => <div>加载中...</div> 
  }
);

// 调试工具：SSR状态检测
function useSSRDetection() {
  const [isSSR, setIsSSR] = useState(true);
  const [hydrationMismatch, setHydrationMismatch] = useState(false);

  useEffect(() => {
    setIsSSR(false);
    
    // 检测hydration不匹配
    const placeholders = document.querySelectorAll('[data-portal-placeholder]');
    if (placeholders.length > 0) {
      console.warn('检测到Portal SSR占位符，可能存在hydration问题:', placeholders);
      setHydrationMismatch(true);
    }
  }, []);

  return { isSSR, hydrationMismatch };
}`
              },
              {
                title: '样式和层级冲突',
                description: 'Portal内容的CSS样式不生效、z-index层级冲突、样式隔离失效等视觉问题',
                solution: '使用CSS-in-JS或模块化CSS确保样式隔离，建立z-index层级管理系统，检查CSS继承链',
                prevention: '为Portal内容使用独立的样式命名空间，建立统一的z-index约定，避免全局样式污染',
                code: `// ❌ 问题：样式冲突和层级问题
function ProblematicStyledPortal({ children }) {
  return ReactDOM.createPortal(
    <div className="modal"> {/* 可能与页面其他.modal冲突 */}
      {children}
    </div>,
    document.body
  );
}

// ✅ 解决方案：样式隔离和层级管理
const Z_INDEX_LAYERS = {
  DROPDOWN: 1000,
  TOOLTIP: 2000,
  MODAL: 3000,
  TOAST: 4000,
  LOADING: 5000
};

function StyledPortal({ children, layer = 'MODAL', className = '' }) {
  const [container] = useState(() => {
    const div = document.createElement('div');
    div.className = \`portal-container portal-\${layer.toLowerCase()}\`;
    div.style.position = 'fixed';
    div.style.top = '0';
    div.style.left = '0';
    div.style.zIndex = Z_INDEX_LAYERS[layer].toString();
    div.style.pointerEvents = 'none';
    return div;
  });

  useEffect(() => {
    document.body.appendChild(container);
    return () => {
      if (document.body.contains(container)) {
        document.body.removeChild(container);
      }
    };
  }, [container]);

  return ReactDOM.createPortal(
    <div 
      className={\`portal-content \${className}\`}
      style={{ 
        pointerEvents: 'auto',
        isolation: 'isolate' // 创建新的层叠上下文
      }}
    >
      {children}
    </div>,
    container
  );
}

// CSS-in-JS解决方案（styled-components）
const PortalContainer = styled.div\`
  /* 确保独立的层叠上下文 */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: \${props => Z_INDEX_LAYERS[props.layer] || Z_INDEX_LAYERS.MODAL};
  
  /* 防止指针事件穿透 */
  pointer-events: none;
  
  /* 样式隔离 */
  isolation: isolate;
  
  /* 子元素样式重置 */
  * {
    box-sizing: border-box;
  }
  
  /* 恢复内容区域的交互 */
  .portal-content {
    pointer-events: auto;
  }
\`;

// 调试工具：样式诊断
function useStyleDebug(elementRef) {
  useEffect(() => {
    if (!elementRef.current) return;

    const element = elementRef.current;
    const computedStyle = window.getComputedStyle(element);
    
    console.log('Portal样式诊断:', {
      zIndex: computedStyle.zIndex,
      position: computedStyle.position,
      isolation: computedStyle.isolation,
      pointerEvents: computedStyle.pointerEvents,
      display: computedStyle.display,
      visibility: computedStyle.visibility,
      opacity: computedStyle.opacity,
      transform: computedStyle.transform,
      stackingContext: {
        createsNew: computedStyle.isolation === 'isolate' || 
                   computedStyle.transform !== 'none' ||
                   computedStyle.opacity !== '1'
      }
    });

    // 检查z-index冲突
    const siblings = Array.from(element.parentNode.children);
    const zIndexConflicts = siblings
      .filter(sibling => sibling !== element)
      .map(sibling => ({
        element: sibling,
        zIndex: window.getComputedStyle(sibling).zIndex
      }))
      .filter(({ zIndex }) => zIndex === computedStyle.zIndex && zIndex !== 'auto');

    if (zIndexConflicts.length > 0) {
      console.warn('发现z-index冲突:', zIndexConflicts);
    }
  }, [elementRef]);
}`
              },
              {
                title: '内存泄漏和性能问题',
                description: 'Portal相关的内存泄漏、DOM节点堆积、事件监听器未清理等性能问题',
                solution: '实现自动清理机制，使用WeakMap管理引用，监控Portal生命周期，及时移除事件监听器',
                prevention: '建立Portal使用规范，定期进行内存检查，使用性能监控工具，避免长时间保持Portal引用',
                code: `// ❌ 问题：内存泄漏的Portal
function LeakyPortal({ children, onClose }) {
  const [container] = useState(() => document.createElement('div'));
  
  useEffect(() => {
    document.body.appendChild(container);
    
    // 忘记清理事件监听器
    const handleClick = () => onClose();
    document.addEventListener('click', handleClick);
    
    // 忘记清理容器 - 内存泄漏
  }, []);

  return ReactDOM.createPortal(children, container);
}

// ✅ 解决方案：自动内存管理
class PortalMemoryManager {
  private static instance: PortalMemoryManager;
  private portals = new WeakMap<HTMLElement, PortalInfo>();
  private cleanup = new Map<string, Function>();
  private checkInterval: NodeJS.Timeout | null = null;

  static getInstance() {
    if (!this.instance) {
      this.instance = new PortalMemoryManager();
      this.instance.startMemoryMonitoring();
    }
    return this.instance;
  }

  registerPortal(id: string, container: HTMLElement, component: string) {
    this.portals.set(container, {
      id,
      component,
      createdAt: Date.now(),
      lastAccessed: Date.now()
    });

    console.log(\`Portal注册: \${component} (\${id})\`);
  }

  addCleanup(id: string, cleanupFn: Function) {
    this.cleanup.set(id, cleanupFn);
  }

  unregisterPortal(id: string) {
    const cleanupFn = this.cleanup.get(id);
    if (cleanupFn) {
      cleanupFn();
      this.cleanup.delete(id);
    }
    
    console.log(\`Portal清理: \${id}\`);
  }

  private startMemoryMonitoring() {
    this.checkInterval = setInterval(() => {
      this.performMemoryCheck();
    }, 30000); // 每30秒检查
  }

  private performMemoryCheck() {
    const now = Date.now();
    const staleThreshold = 5 * 60 * 1000; // 5分钟

    this.cleanup.forEach((cleanupFn, id) => {
      // 检查是否有孤立的清理函数
      const container = Array.from(document.querySelectorAll('.portal-container'))
        .find(el => el.id.includes(id));
      
      if (!container) {
        console.warn(\`发现孤立的Portal清理函数: \${id}\`);
        cleanupFn();
        this.cleanup.delete(id);
      }
    });

    // 性能内存报告
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      console.log('Portal内存状态:', {
        usedJSHeapSize: (memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + 'MB',
        activePortals: this.cleanup.size,
        timestamp: new Date().toISOString()
      });
    }
  }

  getMemoryReport() {
    return {
      activePortals: this.cleanup.size,
      cleanupFunctions: Array.from(this.cleanup.keys()),
      memoryUsage: 'memory' in performance ? 
        (performance as any).memory.usedJSHeapSize : 'N/A'
    };
  }

  destroy() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }
    this.cleanup.forEach(cleanupFn => cleanupFn());
    this.cleanup.clear();
  }
}

interface PortalInfo {
  id: string;
  component: string;
  createdAt: number;
  lastAccessed: number;
}

// 内存安全的Portal Hook
function useMemorySafePortal(componentName: string) {
  const [portalId] = useState(() => \`portal-\${Date.now()}-\${Math.random()}\`);
  const [container, setContainer] = useState<HTMLElement | null>(null);
  const memoryManager = PortalMemoryManager.getInstance();

  useEffect(() => {
    const div = document.createElement('div');
    div.id = portalId;
    div.className = 'portal-container memory-managed';
    
    document.body.appendChild(div);
    setContainer(div);
    
    memoryManager.registerPortal(portalId, div, componentName);

    // 注册清理函数
    memoryManager.addCleanup(portalId, () => {
      if (document.body.contains(div)) {
        document.body.removeChild(div);
      }
      setContainer(null);
    });

    return () => {
      memoryManager.unregisterPortal(portalId);
    };
  }, [portalId, componentName, memoryManager]);

  return container;
}

// 调试工具：内存使用监控
function useMemoryMonitor(enabled = process.env.NODE_ENV === 'development') {
  useEffect(() => {
    if (!enabled || !('memory' in performance)) return;

    const checkMemory = () => {
      const memory = (performance as any).memory;
      const portals = document.querySelectorAll('.portal-container').length;
      
      console.log('内存监控:', {
        usedJSHeapSize: (memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + 'MB',
        totalJSHeapSize: (memory.totalJSHeapSize / 1024 / 1024).toFixed(2) + 'MB',
        activePortals: portals,
        timestamp: Date.now()
      });
    };

    const interval = setInterval(checkMemory, 10000); // 每10秒检查
    return () => clearInterval(interval);
  }, [enabled]);
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '高效的Portal开发需要合适的调试工具和监控手段。本节介绍专门用于Portal开发的工具、浏览器扩展、自定义Hook和可视化方案，帮助开发者快速定位问题和优化性能。',
        sections: [
          {
            title: '浏览器开发工具使用技巧',
            description: '利用Chrome DevTools、React DevTools等浏览器工具高效调试Portal组件，包括DOM检查、性能分析和事件追踪',
            items: [
              {
                title: 'React DevTools Portal调试',
                description: 'React DevTools提供了专门的Portal调试功能，可以可视化Portal的组件树关系和状态变化',
                solution: '使用React DevTools的Profiler和Components面板分析Portal性能，通过highlight updates查看重渲染',
                prevention: '在开发环境中启用React DevTools，定期检查Portal组件的渲染性能和状态变化',
                code: `// React DevTools Portal调试技巧

// 1. 添加显示名称便于调试
function Modal({ children, isOpen, onClose }) {
  Modal.displayName = 'Portal.Modal';
  
  if (!isOpen) return null;

  return ReactDOM.createPortal(
    <div className="modal-backdrop">
      <div className="modal-content">
        {children}
      </div>
    </div>,
    document.body
  );
}

// 2. 使用React.Profiler监控性能
import { Profiler } from 'react';

function ProfiledPortal({ children, id }) {
  const onRenderCallback = (id, phase, actualDuration, baseDuration, startTime, commitTime) => {
    // 发送到性能监控系统
    console.log('Portal性能指标:', {
      id,
      phase, // 'mount' 或 'update'
      actualDuration, // 实际渲染时间
      baseDuration,   // 预期渲染时间
      startTime,
      commitTime
    });

    // 性能警告
    if (actualDuration > 16) { // 超过一帧时间
      console.warn(\`Portal \${id} 渲染过慢: \${actualDuration.toFixed(2)}ms\`);
    }
  };

  return (
    <Profiler id={id} onRender={onRenderCallback}>
      {children}
    </Profiler>
  );
}

// 3. Portal组件包装器用于调试
function DebugPortal({ children, name, ...props }) {
  useEffect(() => {
    console.log(\`Portal \${name} mounted\`);
    return () => console.log(\`Portal \${name} unmounted\`);
  }, [name]);

  return (
    <ProfiledPortal id={\`portal-\${name}\`}>
      <Modal {...props}>
        {children}
      </Modal>
    </ProfiledPortal>
  );
}

// 4. 开发模式下的Portal可视化
function PortalVisualizer() {
  const [portals, setPortals] = useState([]);

  useEffect(() => {
    const updatePortals = () => {
      const portalContainers = Array.from(document.querySelectorAll('.portal-container, [id*="portal"]'));
      setPortals(portalContainers.map(container => ({
        id: container.id || 'unnamed',
        className: container.className,
        zIndex: window.getComputedStyle(container).zIndex,
        childrenCount: container.children.length,
        rect: container.getBoundingClientRect()
      })));
    };

    updatePortals();
    const observer = new MutationObserver(updatePortals);
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'id', 'style']
    });

    return () => observer.disconnect();
  }, []);

  if (process.env.NODE_ENV !== 'development') return null;

  return (
    <div 
      style={{
        position: 'fixed',
        top: 10,
        right: 10,
        background: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '10px',
        borderRadius: '4px',
        fontSize: '12px',
        zIndex: 99999,
        maxWidth: '300px'
      }}
    >
      <h4>Portal调试器 ({portals.length}个)</h4>
      {portals.map(portal => (
        <div key={portal.id} style={{ margin: '5px 0', padding: '5px', background: 'rgba(255,255,255,0.1)' }}>
          <div>ID: {portal.id}</div>
          <div>Z-Index: {portal.zIndex}</div>
          <div>Children: {portal.childrenCount}</div>
          <div>Size: {Math.round(portal.rect.width)}×{Math.round(portal.rect.height)}</div>
        </div>
      ))}
    </div>
  );
}`
              },
              {
                title: 'Chrome DevTools DOM检查',
                description: '使用Chrome DevTools的Elements面板检查Portal的DOM结构、样式计算和层级关系',
                solution: '通过Elements面板定位Portal元素，使用Computed样式检查z-index层级，利用Event Listeners面板查看事件绑定',
                prevention: '定期检查Portal DOM结构是否符合预期，验证样式隔离效果，确认事件监听器正确绑定和清理',
                code: `// Chrome DevTools DOM检查辅助工具

// 1. Portal DOM结构检查器
function inspectPortalDOM() {
  const portals = document.querySelectorAll('[class*="portal"], [id*="portal"]');
  
  portals.forEach((portal, index) => {
    console.group(\`Portal \${index + 1}: \${portal.id || portal.className}\`);
    
    // 基本信息
    console.log('Element:', portal);
    console.log('Parent:', portal.parentNode);
    console.log('Children count:', portal.children.length);
    
    // 样式信息
    const styles = window.getComputedStyle(portal);
    console.log('Computed styles:', {
      position: styles.position,
      zIndex: styles.zIndex,
      display: styles.display,
      visibility: styles.visibility,
      opacity: styles.opacity,
      pointerEvents: styles.pointerEvents
    });
    
    // 层叠上下文信息
    console.log('Stacking context:', {
      createsNew: styles.isolation === 'isolate' || 
                 styles.transform !== 'none' ||
                 parseFloat(styles.opacity) < 1,
      isolation: styles.isolation,
      transform: styles.transform
    });
    
    // 事件监听器检查（需要在DevTools中手动查看）
    console.log('💡 在Elements面板选择此元素，然后查看Event Listeners面板');
    
    console.groupEnd();
  });
}

// 2. Portal层级可视化
function visualizePortalLayers() {
  const allElements = Array.from(document.querySelectorAll('*'));
  const layeredElements = allElements
    .map(el => ({
      element: el,
      zIndex: window.getComputedStyle(el).zIndex,
      position: window.getComputedStyle(el).position
    }))
    .filter(item => item.zIndex !== 'auto' && item.position !== 'static')
    .sort((a, b) => parseInt(b.zIndex) - parseInt(a.zIndex));

  console.log('Z-Index层级分析:');
  layeredElements.forEach(({ element, zIndex, position }) => {
    const isPortal = element.className.includes('portal') || 
                    element.id.includes('portal');
    console.log(
      \`\${isPortal ? '🎯' : '📄'} Z-Index: \${zIndex}, Position: \${position}\`,
      element
    );
  });
}

// 3. Portal事件流追踪
function tracePortalEvents(portalElement) {
  const events = ['click', 'mousedown', 'mouseup', 'keydown', 'keyup'];
  
  events.forEach(eventType => {
    // 捕获阶段
    portalElement.addEventListener(eventType, (e) => {
      console.log(\`📥 \${eventType} - 捕获阶段\`, {
        target: e.target,
        currentTarget: e.currentTarget,
        phase: e.eventPhase,
        bubbles: e.bubbles
      });
    }, true);
    
    // 冒泡阶段
    portalElement.addEventListener(eventType, (e) => {
      console.log(\`📤 \${eventType} - 冒泡阶段\`, {
        target: e.target,
        currentTarget: e.currentTarget,
        phase: e.eventPhase,
        bubbles: e.bubbles
      });
    }, false);
  });
  
  console.log('Portal事件追踪已启用，打开Console查看事件流');
}

// 4. 在DevTools Console中使用的调试命令
window.portalDebug = {
  inspect: inspectPortalDOM,
  layers: visualizePortalLayers,
  trace: tracePortalEvents,
  
  // 快速查找Portal
  find: (selector = '[class*="portal"], [id*="portal"]') => {
    return Array.from(document.querySelectorAll(selector));
  },
  
  // 测试Portal性能
  benchmark: (iterations = 1000) => {
    console.time('Portal Performance Test');
    
    for (let i = 0; i < iterations; i++) {
      const div = document.createElement('div');
      div.className = 'test-portal';
      document.body.appendChild(div);
      document.body.removeChild(div);
    }
    
    console.timeEnd('Portal Performance Test');
  }
};

console.log('Portal调试工具已加载，使用 portalDebug.inspect() 开始调试');`
              },
              {
                title: '性能监控和分析工具',
                description: '使用Performance面板、Memory面板和自定义监控工具分析Portal的性能表现和内存使用',
                solution: '建立Portal性能监控体系，使用浏览器Performance API收集指标，实现实时性能告警',
                prevention: '定期进行性能基准测试，监控关键性能指标，及时发现性能回归问题',
                code: `// Portal性能监控系统

class PortalPerformanceMonitor {
  private static instance: PortalPerformanceMonitor;
  private metrics = new Map<string, PerformanceMetric[]>();
  private observers = new Map<string, PerformanceObserver>();

  static getInstance() {
    if (!this.instance) {
      this.instance = new PortalPerformanceMonitor();
      this.instance.initialize();
    }
    return this.instance;
  }

  private initialize() {
    // 监控长任务
    if ('PerformanceObserver' in window) {
      const longTaskObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.duration > 50) { // 长任务阈值
            console.warn('检测到长任务:', {
              duration: entry.duration,
              startTime: entry.startTime,
              name: entry.name
            });
          }
        });
      });

      longTaskObserver.observe({ entryTypes: ['longtask'] });
      this.observers.set('longtask', longTaskObserver);
    }

    // 监控布局偏移
    if ('LayoutShift' in window) {
      const layoutShiftObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.value > 0.1) { // CLS阈值
            console.warn('Portal导致的布局偏移:', {
              value: entry.value,
              sources: entry.sources
            });
          }
        });
      });

      layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.set('layout-shift', layoutShiftObserver);
    }
  }

  recordPortalMetric(portalId: string, operation: string, duration: number) {
    if (!this.metrics.has(portalId)) {
      this.metrics.set(portalId, []);
    }

    const metric: PerformanceMetric = {
      operation,
      duration,
      timestamp: performance.now(),
      memory: 'memory' in performance ? (performance as any).memory.usedJSHeapSize : 0
    };

    this.metrics.get(portalId)!.push(metric);

    // 性能警告
    if (duration > 16) { // 超过一帧时间
      console.warn(\`Portal \${portalId} \${operation} 性能警告: \${duration.toFixed(2)}ms\`);
    }
  }

  getMetrics(portalId: string) {
    return this.metrics.get(portalId) || [];
  }

  generateReport() {
    const report = {
      totalPortals: this.metrics.size,
      averageMetrics: new Map<string, number>(),
      slowOperations: [] as Array<{ portalId: string; operation: string; duration: number }>,
      memoryTrend: [] as number[]
    };

    this.metrics.forEach((metrics, portalId) => {
      metrics.forEach(metric => {
        // 收集慢操作
        if (metric.duration > 16) {
          report.slowOperations.push({
            portalId,
            operation: metric.operation,
            duration: metric.duration
          });
        }

        // 内存趋势
        if (metric.memory > 0) {
          report.memoryTrend.push(metric.memory);
        }
      });
    });

    return report;
  }

  startContinuousMonitoring(interval = 30000) {
    setInterval(() => {
      const report = this.generateReport();
      console.log('Portal性能报告:', report);

      // 发送到监控服务
      if (report.slowOperations.length > 10) {
        console.error('Portal性能告警: 检测到大量慢操作');
      }
    }, interval);
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.metrics.clear();
  }
}

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: number;
  memory: number;
}

// Portal性能监控Hook
function usePortalPerformance(portalId: string) {
  const monitor = PortalPerformanceMonitor.getInstance();

  const measureOperation = useCallback((operation: string, fn: Function) => {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    
    monitor.recordPortalMetric(portalId, operation, end - start);
    return result;
  }, [portalId, monitor]);

  const measureAsync = useCallback(async (operation: string, fn: Function) => {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    
    monitor.recordPortalMetric(portalId, operation, end - start);
    return result;
  }, [portalId, monitor]);

  return { measureOperation, measureAsync };
}

// 使用示例
function PerformanceMonitoredPortal({ children, isOpen, onClose }) {
  const portalId = 'modal-portal';
  const { measureOperation, measureAsync } = usePortalPerformance(portalId);
  const [container, setContainer] = useState(null);

  useEffect(() => {
    measureOperation('container-creation', () => {
      const div = document.createElement('div');
      document.body.appendChild(div);
      setContainer(div);
    });

    return () => {
      measureOperation('container-cleanup', () => {
        if (container && document.body.contains(container)) {
          document.body.removeChild(container);
        }
      });
    };
  }, [measureOperation]);

  const handleClose = () => {
    measureOperation('modal-close', () => {
      onClose();
    });
  };

  if (!isOpen || !container) return null;

  return ReactDOM.createPortal(
    <div className="modal-backdrop" onClick={handleClose}>
      <div className="modal-content" onClick={e => e.stopPropagation()}>
        {children}
      </div>
    </div>,
    container
  );
}

// 全局性能监控启动
if (process.env.NODE_ENV === 'development') {
  PortalPerformanceMonitor.getInstance().startContinuousMonitoring();
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;