import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "ReactDOM.createPortal是React中用于将子组件渲染到父组件DOM层次结构外部指定DOM节点的API，实现跨层级渲染",

  introduction: `ReactDOM.createPortal是React 16.0版本引入的强大API，允许你将子组件渲染到父组件DOM层次结构之外的DOM节点中。这打破了传统的DOM树层次限制，是实现Modal、Tooltip、Dropdown、Toast等悬浮组件的核心技术。

Portal最重要的特性是保持React的组件树关系和事件冒泡机制，同时将DOM元素渲染到不同的位置。这意味着Portal内的组件仍然可以访问React Context，props传递和事件处理都按照React组件树的逻辑工作，而不是按照DOM树结构。

在现代Web开发中，Portal是构建高质量用户界面的基础工具，特别是在处理z-index层叠、滚动容器限制、样式隔离等问题时表现出色。它是许多UI库（如Material-UI、Ant Design）实现Modal和悬浮组件的底层技术。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react-dom/index.d.ts:45
 * - 实现文件：packages/react-dom/src/client/ReactDOMHostConfig.js
 */

// 基础语法
ReactDOM.createPortal(children, container, key?)

// TypeScript完整接口
function createPortal(
  children: ReactNode,
  container: Element | DocumentFragment,
  key?: null | string
): React.ReactPortal;

// 典型使用模式
const portalRoot = document.getElementById('portal-root');
return ReactDOM.createPortal(
  <ComponentToRender />,
  portalRoot
);

// 动态创建容器
const [container] = useState(() => document.createElement('div'));
useEffect(() => {
  document.body.appendChild(container);
  return () => document.body.removeChild(container);
}, []);

return ReactDOM.createPortal(<ModalContent />, container);`,

  quickExample: `function Toast({ message, isVisible, onClose }) {
  const [toastContainer] = useState(() => {
    const div = document.createElement('div');
    div.className = 'toast-container';
    return div;
  });

  useEffect(() => {
    document.body.appendChild(toastContainer);
    return () => {
      if (document.body.contains(toastContainer)) {
        document.body.removeChild(toastContainer);
      }
    };
  }, [toastContainer]);

  if (!isVisible) return null;

  return ReactDOM.createPortal(
    <div className="toast">
      <span>{message}</span>
      <button onClick={onClose}>×</button>
    </div>,
    toastContainer
  );
}

// 使用示例
function App() {
  const [showToast, setShowToast] = useState(false);

  return (
    <div>
      <button onClick={() => setShowToast(true)}>
        显示Toast
      </button>
      <Toast 
        message="操作成功！" 
        isVisible={showToast}
        onClose={() => setShowToast(false)}
      />
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "Portal渲染机制",
      description: "Portal如何将React组件渲染到DOM树的不同位置",
      diagram: `graph TB
        A[React组件树] --> B[Portal组件]
        B --> C[ReactDOM.createPortal]
        
        D[DOM树] --> E[目标容器]
        C --> E
        
        B --> F[保持React关系]
        F --> G[Context访问]
        F --> H[事件冒泡]
        F --> I[Props传递]

        style B fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
        style C fill:#fff3e0,stroke:#f57c00,stroke-width:2px
        style E fill:#e8f5e8,stroke:#388e3c,stroke-width:2px`
    }
  ],
  
  parameters: [
    {
      name: "children",
      type: "ReactNode",
      required: true,
      description: "要渲染到portal中的React元素或组件",
      example: "<ModalContent />, <div>Hello</div>"
    },
    {
      name: "container",
      type: "Element | DocumentFragment",
      required: true,
      description: "目标DOM容器，Portal内容将渲染到此容器中",
      example: "document.getElementById('modal-root')"
    },
    {
      name: "key",
      type: "string | null",
      required: false,
      description: "可选的key，用于React的reconciliation过程",
      example: "'modal-key', 'tooltip-' + id"
    }
  ],
  
  returnValue: {
    type: "React.ReactPortal",
    description: "返回一个Portal对象，React会将其内容渲染到指定的DOM容器中",
    example: "Portal对象，在DOM中表现为children在container中的渲染"
  },
  
  keyFeatures: [
    {
      title: "跨层级DOM渲染",
      description: "可以将组件渲染到任意DOM位置，不受父组件层级限制",
      benefit: "解决z-index、滚动容器、CSS隔离等布局问题"
    },
    {
      title: "保持React语义",
      description: "维持完整的React组件树关系，Context、props、事件都正常工作",
      benefit: "开发体验与普通组件一致，无需额外的状态管理"
    },
    {
      title: "事件冒泡机制",
      description: "事件按照React组件树冒泡，而不是DOM树结构",
      benefit: "事件处理逻辑符合预期，便于组件间通信"
    }
  ],
  
  limitations: [
    "需要确保目标DOM容器在Portal创建时已存在",
    "Portal内容的样式和布局不受父组件影响，需要独立处理",
    "在SSR环境中需要特殊处理，避免hydration不匹配",
    "过度使用Portal可能导致DOM结构难以理解和调试"
  ],
  
  bestPractices: [
    "为Portal创建专门的DOM容器，通常在body或html元素下",
    "使用useEffect管理Portal容器的生命周期",
    "为不同类型的Portal使用不同的容器，避免样式冲突",
    "在Portal组件中处理点击外部关闭的逻辑",
    "使用CSS-in-JS或模块化CSS避免样式泄露"
  ],
  
  warnings: [
    "确保Portal容器在组件卸载时正确清理",
    "注意Portal内容的可访问性（ARIA）属性设置",
    "避免在Portal内使用依赖父组件样式的组件",
    "在测试环境中确保Portal容器的正确设置"
  ]
};

export default basicInfo;