import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: '什么是React Portal？请解释它的作用和典型使用场景。',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'React Portal是ReactDOM.createPortal API，用于将子组件渲染到父组件DOM层次结构外的指定DOM节点中，常用于Modal、Tooltip等悬浮组件。',
      detailed: `React Portal是React 16.0引入的一个强大功能，允许开发者将组件渲染到DOM树中的任意位置，而不受父组件的DOM层次结构限制。

**核心概念：**
Portal打破了传统的DOM渲染规则，让组件可以"传送"到指定位置，同时保持React组件树的逻辑关系。这意味着Portal内的组件仍然可以访问父组件的Context、接收props、触发事件等。

**典型使用场景：**
1. **模态框(Modal)**: 避免父容器的z-index、overflow等CSS限制
2. **工具提示(Tooltip)**: 确保提示框不被父容器裁剪
3. **下拉菜单(Dropdown)**: 突破容器边界，正确显示选项
4. **全局通知(Toast)**: 在页面顶层显示系统消息
5. **悬浮面板(Popover)**: 展示详细信息而不影响页面布局

**解决的问题：**
- z-index层级冲突
- 父容器overflow: hidden的限制
- 滚动容器的边界问题
- CSS样式隔离需求

Portal是现代React应用构建高质量用户界面的基础工具。`,
      code: `// 基础Portal使用
function Modal({ isOpen, onClose, children }) {
  if (!isOpen) return null;

  return ReactDOM.createPortal(
    <div className="modal-backdrop">
      <div className="modal-content">
        <button onClick={onClose}>×</button>
        {children}
      </div>
    </div>,
    document.getElementById('modal-root') // 渲染到指定DOM节点
  );
}

// 使用示例
function App() {
  const [showModal, setShowModal] = useState(false);

  return (
    <div>
      <button onClick={() => setShowModal(true)}>
        打开模态框
      </button>
      <Modal isOpen={showModal} onClose={() => setShowModal(false)}>
        <h2>这是Portal渲染的模态框</h2>
        <p>它渲染在父组件DOM层次之外，但保持React逻辑关系</p>
      </Modal>
    </div>
  );
}`
    },
    tags: ['Portal基础', 'DOM渲染', 'Modal实现']
  },
  {
    id: 2,
    question: 'Portal内的事件处理是如何工作的？请解释事件冒泡机制和实际应用。',
    difficulty: 'medium',
    frequency: 'high',
    category: '事件机制',
    answer: {
      brief: 'Portal内的事件按照React组件树进行冒泡，而不是DOM树。这确保了事件处理的逻辑一致性，父组件可以正常捕获Portal内触发的事件。',
      detailed: `Portal的事件处理机制是其最重要的特性之一，React确保事件按照组件树的逻辑关系冒泡，而不是DOM树的物理结构。

**事件冒泡原理：**
1. **React组件树优先**: 事件沿着React组件树向上冒泡
2. **跨DOM边界**: 即使Portal内容渲染在不同的DOM节点，事件仍然能正确冒泡到React父组件
3. **一致性保证**: 开发者无需关心DOM结构差异，事件处理逻辑保持一致

**实际应用场景：**

**1. 模态框关闭处理**
父组件可以监听Portal内的事件，实现点击外部关闭等功能。

**2. 表单提交处理**
Portal内的表单提交事件可以被父组件捕获和处理。

**3. 键盘事件处理**
ESC键等全局快捷键可以在父组件统一处理。

**4. 数据通信**
通过事件冒泡实现Portal内组件与父组件的数据通信。

**注意事项：**
- 阻止事件冒泡会影响父组件的事件处理
- DOM事件（如addEventListener）不会跨Portal边界冒泡
- React合成事件正常工作，原生DOM事件需要特殊处理`,
      code: `// 事件冒泡示例
function ParentComponent() {
  // 父组件可以捕获Portal内的事件
  const handlePortalClick = (e) => {
    console.log('Portal内的点击事件冒泡到父组件');
    if (e.target.closest('.modal-content')) {
      console.log('点击在模态框内容区域');
    } else {
      console.log('点击在模态框背景区域');
      setShowModal(false); // 点击背景关闭模态框
    }
  };

  return (
    <div onClick={handlePortalClick}>
      <h1>父组件</h1>
      <ModalPortal>
        <div className="modal-content">
          <button onClick={(e) => {
            console.log('按钮点击事件');
            // 事件会继续冒泡到父组件的handlePortalClick
          }}>
            Portal内的按钮
          </button>
        </div>
      </ModalPortal>
    </div>
  );
}

// Portal组件
function ModalPortal({ children }) {
  return ReactDOM.createPortal(
    <div className="modal-backdrop">
      {children}
    </div>,
    document.body
  );
}

// 键盘事件处理示例
function AppWithKeyboard() {
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && showModal) {
        setShowModal(false);
      }
    };

    // 在父组件监听键盘事件
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showModal]);

  return (
    <div>
      <button onClick={() => setShowModal(true)}>打开模态框</button>
      {showModal && (
        <ModalPortal>
          <div className="modal">
            <h2>按ESC键关闭</h2>
            <input placeholder="Portal内的输入框" />
          </div>
        </ModalPortal>
      )}
    </div>
  );
}`
    },
    tags: ['事件冒泡', '事件处理', 'React合成事件']
  },
  {
    id: 3,
    question: '如何处理Portal在SSR环境中的hydration问题？请分析问题原因并提供解决方案。',
    difficulty: 'hard',
    frequency: 'medium',
    category: '服务端渲染',
    answer: {
      brief: 'Portal在SSR中容易导致hydration不匹配，因为服务端无法渲染到指定DOM节点。解决方案包括使用useEffect延迟渲染、条件渲染和自定义hydration策略。',
      detailed: `Portal在服务端渲染(SSR)环境中面临独特的挑战，主要是因为服务端和客户端的DOM结构差异导致的hydration不匹配问题。

**问题分析：**

**1. hydration不匹配**
- 服务端：Portal内容无法渲染到指定DOM节点（节点不存在）
- 客户端：Portal内容渲染到指定DOM节点
- 结果：React检测到差异，抛出hydration警告或错误

**2. DOM节点缺失**
- SSR阶段目标DOM节点不存在
- 无法使用document.getElementById等DOM API
- Portal容器未在服务端HTML中生成

**3. 时序问题**
- 组件渲染时机与DOM节点创建时机不匹配
- useEffect在hydration之后执行

**解决方案：**

**方案1: useEffect延迟渲染**
最常用的解决方案，在客户端hydration后再渲染Portal。

**方案2: 条件渲染**
使用状态标志控制Portal的渲染时机。

**方案3: 自定义hydration策略**
预先在HTML中准备Portal容器。

**方案4: 框架级解决方案**
使用Next.js等框架的内置Portal支持。

**最佳实践：**
1. 总是在客户端渲染Portal
2. 使用loading状态优化用户体验
3. 确保Portal容器在hydration前存在
4. 避免在首屏渲染中使用Portal`,
      code: `// 方案1: useEffect延迟渲染（推荐）
function SSRSafePortal({ children }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 只在客户端渲染Portal
  if (!mounted) return null;

  return ReactDOM.createPortal(
    children,
    document.getElementById('portal-root')
  );
}

// 方案2: 自定义Hook简化
function useSSRSafePortal() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return mounted;
}

function SafeModal({ children, isOpen }) {
  const mounted = useSSRSafePortal();

  if (!isOpen || !mounted) return null;

  return ReactDOM.createPortal(
    <div className="modal">{children}</div>,
    document.body
  );
}

// 方案3: 动态Portal容器
function DynamicPortal({ children }) {
  const [container, setContainer] = useState(null);

  useEffect(() => {
    // 动态创建容器
    const div = document.createElement('div');
    div.id = 'dynamic-portal';
    document.body.appendChild(div);
    setContainer(div);

    return () => {
      // 清理容器
      if (document.body.contains(div)) {
        document.body.removeChild(div);
      }
    };
  }, []);

  if (!container) return null;

  return ReactDOM.createPortal(children, container);
}

// 方案4: 带loading的Portal
function LoadingPortal({ children, loading = false }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // 显示placeholder或loading状态
    return loading ? <div className="portal-loading">Loading...</div> : null;
  }

  return ReactDOM.createPortal(children, document.body);
}

// Next.js中的最佳实践
import dynamic from 'next/dynamic';

// 动态导入Portal组件，禁用SSR
const Modal = dynamic(() => import('./Modal'), {
  ssr: false,
  loading: () => <div>Loading modal...</div>
});

function App() {
  const [showModal, setShowModal] = useState(false);

  return (
    <div>
      <button onClick={() => setShowModal(true)}>
        打开模态框
      </button>
      {showModal && (
        <Modal onClose={() => setShowModal(false)}>
          <h2>SSR安全的模态框</h2>
        </Modal>
      )}
    </div>
  );
}`
    },
    tags: ['SSR', 'hydration', '服务端渲染', 'Next.js']
  }
];

export default interviewQuestions;