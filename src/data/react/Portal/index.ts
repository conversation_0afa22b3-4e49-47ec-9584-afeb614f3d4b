import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const PortalData: ApiItem = {
  id: 'Portal',
  title: 'ReactDOM.createPortal',
  description: 'ReactDOM.createPortal是React中用于将子组件渲染到父组件DOM层次结构外部指定DOM节点的API，实现跨层级渲染',
  category: 'ReactDOM APIs',
  difficulty: 'medium',
  
  syntax: `// 基础语法
ReactDOM.createPortal(child, container)

// TypeScript接口
function createPortal(
  children: ReactNode,
  container: Element | DocumentFragment,
  key?: null | string
): React.ReactPortal;

// 使用示例
const portalRoot = document.getElementById('portal-root');
return ReactDOM.createPortal(
  <ModalContent />,
  portalRoot
);`,
  example: `function Modal({ isOpen, onClose, children }) {
  const [portalContainer] = useState(() => {
    // 创建portal容器
    const div = document.createElement('div');
    div.className = 'modal-portal';
    return div;
  });

  useEffect(() => {
    // 添加到document.body
    document.body.appendChild(portalContainer);
    
    return () => {
      // 清理portal容器
      document.body.removeChild(portalContainer);
    };
  }, [portalContainer]);

  if (!isOpen) return null;

  // 将Modal内容渲染到body中的portal容器
  return ReactDOM.createPortal(
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={e => e.stopPropagation()}>
        <button className="close-btn" onClick={onClose}>×</button>
        {children}
      </div>
    </div>,
    portalContainer
  );
}

// 使用Modal组件
function App() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <div className="app">
      <h1>应用主界面</h1>
      <button onClick={() => setIsModalOpen(true)}>
        打开Modal
      </button>
      
      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <h2>Modal标题</h2>
        <p>这是Portal渲染的Modal内容</p>
      </Modal>
    </div>
  );
}`,
  notes: 'Portal子组件仍然可以访问React上下文，但DOM事件会在portal容器中冒泡',
  
  version: 'React 16.0.0+',
  tags: ["ReactDOM", "API", "Portal", "跨层级渲染", "Modal"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default PortalData;