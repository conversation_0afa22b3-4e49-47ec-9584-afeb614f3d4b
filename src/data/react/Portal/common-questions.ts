import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'question-1',
    question: 'Portal如何处理CSS样式和z-index层级问题？',
    answer: `Portal的主要优势之一就是解决CSS层级问题，但需要正确使用：

**z-index管理：**
Portal渲染到DOM树的其他位置，可以避免父容器的z-index限制。但Portal内容的z-index仍然需要考虑全局层级。

**样式隔离：**
Portal内容继承渲染位置的样式上下文，而不是原组件的样式。这可能导致样式丢失或冲突。

**最佳实践：**
1. 为Portal内容使用独立的CSS类名或CSS-in-JS
2. 设置合适的z-index值（如9999）确保在最顶层
3. 使用CSS Custom Properties进行主题传递
4. 考虑使用CSS容器查询处理响应式布局

**常见问题：**
- 父组件的样式不会应用到Portal内容
- 全局样式可能影响Portal内容
- 主题切换时Portal内容可能不同步`,
    code: `// 解决样式问题的最佳实践
function StyledPortal({ children, className = '' }) {
  const [container] = useState(() => {
    const div = document.createElement('div');
    // 设置基础样式确保正确层级
    div.style.position = 'fixed';
    div.style.zIndex = '9999';
    div.style.top = '0';
    div.style.left = '0';
    div.style.width = '100%';
    div.style.height = '100%';
    div.style.pointerEvents = 'none'; // 允许点击穿透
    return div;
  });

  useEffect(() => {
    document.body.appendChild(container);
    return () => {
      if (document.body.contains(container)) {
        document.body.removeChild(container);
      }
    };
  }, [container]);

  return ReactDOM.createPortal(
    <div 
      className={className}
      style={{ pointerEvents: 'auto' }} // 恢复交互
    >
      {children}
    </div>,
    container
  );
}

// 主题传递示例
const ThemeContext = createContext();

function ThemedModal({ children }) {
  const theme = useContext(ThemeContext);
  
  return ReactDOM.createPortal(
    <div 
      className="modal-backdrop"
      style={{
        '--theme-bg': theme.backgroundColor,
        '--theme-text': theme.textColor,
        backgroundColor: 'var(--theme-bg)',
        color: 'var(--theme-text)'
      }}
    >
      {children}
    </div>,
    document.body
  );
}`,
    tags: ['CSS样式', 'z-index', '主题传递', '样式隔离'],
    relatedQuestions: ['如何在Portal中使用CSS-in-JS？', 'Portal内容如何响应主题变化？']
  },
  {
    id: 'question-2',
    question: 'Portal在移动端开发中有哪些特殊考虑？',
    answer: `移动端使用Portal需要考虑屏幕尺寸、触摸操作和性能等因素：

**屏幕适配：**
移动设备屏幕小，Portal内容需要特别注意响应式设计和可用空间管理。

**触摸交互：**
移动端的触摸事件处理与桌面端不同，需要考虑触摸穿透和手势操作。

**性能优化：**
移动设备性能有限，Portal的动画和渲染需要优化以保证流畅体验。

**安全区域：**
现代手机的刘海屏、底部指示器等需要特殊处理，避免Portal内容被遮挡。

**键盘处理：**
虚拟键盘弹出时会影响布局，Portal需要动态调整位置。

**滚动穿透：**
防止Portal打开时背景页面滚动，特别是在iOS Safari中。`,
    code: `// 移动端优化的Portal组件
function MobilePortal({ children, onClose }) {
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);

  useEffect(() => {
    // 检测虚拟键盘
    const handleResize = () => {
      const heightDiff = window.innerHeight - document.documentElement.clientHeight;
      setIsKeyboardOpen(heightDiff > 150); // 键盘高度通常>150px
    };

    // 防止背景滚动（iOS兼容）
    const originalOverflow = document.body.style.overflow;
    const originalPosition = document.body.style.position;
    
    document.body.style.overflow = 'hidden';
    document.body.style.position = 'fixed';
    document.body.style.width = '100%';
    document.body.style.height = '100%';

    window.addEventListener('resize', handleResize);

    return () => {
      document.body.style.overflow = originalOverflow;
      document.body.style.position = originalPosition;
      document.body.style.width = '';
      document.body.style.height = '';
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 处理触摸穿透
  const handleTouchStart = (e) => {
    if (e.target === e.currentTarget) {
      e.preventDefault();
    }
  };

  return ReactDOM.createPortal(
    <div 
      className="mobile-portal"
      onTouchStart={handleTouchStart}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 9999,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        // 安全区域处理
        paddingTop: 'env(safe-area-inset-top)',
        paddingBottom: 'env(safe-area-inset-bottom)',
        paddingLeft: 'env(safe-area-inset-left)',
        paddingRight: 'env(safe-area-inset-right)',
        // 键盘打开时调整
        paddingBottom: isKeyboardOpen ? '0' : 'env(safe-area-inset-bottom)'
      }}
    >
      <div 
        className="modal-content"
        style={{
          position: 'absolute',
          bottom: '0',
          left: '0',
          right: '0',
          maxHeight: '90vh',
          backgroundColor: 'white',
          borderRadius: '16px 16px 0 0',
          transform: 'translateY(0)',
          transition: 'transform 0.3s ease-out'
        }}
      >
        {/* 拖拽指示器 */}
        <div 
          style={{
            width: '40px',
            height: '4px',
            backgroundColor: '#ddd',
            borderRadius: '2px',
            margin: '8px auto',
            cursor: 'grab'
          }}
        />
        {children}
      </div>
    </div>,
    document.body
  );
}

// 手势关闭支持
function SwipeableModal({ children, onClose }) {
  const [dragY, setDragY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  const handleTouchStart = (e) => {
    setIsDragging(true);
    const touch = e.touches[0];
    setDragY(touch.clientY);
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    
    const touch = e.touches[0];
    const diff = touch.clientY - dragY;
    
    if (diff > 0) { // 向下拖拽
      e.currentTarget.style.transform = \`translateY(\${diff}px)\`;
    }
  };

  const handleTouchEnd = (e) => {
    setIsDragging(false);
    const transform = e.currentTarget.style.transform;
    const translateY = parseFloat(transform.replace(/[^0-9.-]/g, '')) || 0;
    
    if (translateY > 100) { // 拖拽超过100px就关闭
      onClose();
    } else {
      e.currentTarget.style.transform = 'translateY(0)'; // 回弹
    }
  };

  return (
    <MobilePortal onClose={onClose}>
      <div 
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {children}
      </div>
    </MobilePortal>
  );
}`,
    tags: ['移动端', '响应式设计', '触摸交互', '安全区域'],
    relatedQuestions: ['如何处理iOS Safari中的滚动问题？', 'Portal在PWA中的使用注意事项？']
  },
  {
    id: 'question-3',
    question: '如何测试使用Portal的组件？',
    answer: `测试Portal组件需要特殊的设置和技巧，因为Portal会将内容渲染到DOM树的其他位置：

**测试环境设置：**
需要确保测试环境中有Portal的目标容器，或者模拟Portal的行为。

**DOM查询策略：**
Portal内容不在组件的DOM树中，需要使用不同的查询策略来找到元素。

**事件测试：**
验证Portal内的事件能否正确冒泡到父组件。

**Mock策略：**
在某些情况下可以Mock Portal来简化测试。

**端到端测试：**
使用Cypress等工具进行真实的Portal交互测试。

**快照测试：**
Portal内容的快照需要特殊处理，因为它不在预期的DOM位置。`,
    code: `// Jest + React Testing Library测试示例
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { createPortal } from 'react-dom';

// 1. 设置Portal容器
beforeEach(() => {
  // 创建Portal目标容器
  const portalRoot = document.createElement('div');
  portalRoot.setAttribute('id', 'portal-root');
  document.body.appendChild(portalRoot);
});

afterEach(() => {
  // 清理Portal容器
  const portalRoot = document.getElementById('portal-root');
  if (portalRoot) {
    document.body.removeChild(portalRoot);
  }
});

// 2. 测试Portal渲染
test('Portal内容正确渲染', () => {
  const TestComponent = () => (
    <div>
      <button>触发按钮</button>
      {createPortal(
        <div data-testid="portal-content">Portal内容</div>,
        document.getElementById('portal-root')
      )}
    </div>
  );

  render(<TestComponent />);

  // 在portal容器中查找内容
  expect(screen.getByTestId('portal-content')).toBeInTheDocument();
  
  // 验证内容不在主组件树中
  const mainDiv = screen.getByRole('button').parentElement;
  expect(mainDiv).not.toContainElement(screen.getByTestId('portal-content'));
});

// 3. 测试事件冒泡
test('Portal事件正确冒泡', () => {
  const handleClick = jest.fn();
  
  const TestComponent = () => (
    <div onClick={handleClick}>
      {createPortal(
        <button data-testid="portal-button">Portal按钮</button>,
        document.getElementById('portal-root')
      )}
    </div>
  );

  render(<TestComponent />);

  fireEvent.click(screen.getByTestId('portal-button'));
  expect(handleClick).toHaveBeenCalled();
});

// 4. Mock Portal简化测试
jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  createPortal: (children) => children // 直接返回children
}));

test('使用Mock的Portal测试', () => {
  const TestComponent = () => (
    <div>
      <span>主要内容</span>
      {createPortal(
        <div data-testid="mocked-portal">Portal内容</div>,
        document.body
      )}
    </div>
  );

  const { container } = render(<TestComponent />);
  
  // Mock后，Portal内容会在主容器中
  expect(container).toHaveTextContent('Portal内容');
});

// 5. 自定义测试工具
function renderWithPortal(component) {
  const portalRoot = document.createElement('div');
  portalRoot.id = 'test-portal-root';
  document.body.appendChild(portalRoot);

  const result = render(component);

  return {
    ...result,
    portalRoot,
    cleanup: () => {
      result.unmount();
      if (document.body.contains(portalRoot)) {
        document.body.removeChild(portalRoot);
      }
    }
  };
}

// 使用自定义工具
test('使用自定义Portal测试工具', () => {
  const TestModal = ({ isOpen }) => 
    isOpen ? createPortal(
      <div data-testid="modal">模态框</div>,
      document.getElementById('test-portal-root')
    ) : null;

  const { cleanup } = renderWithPortal(<TestModal isOpen={true} />);

  expect(screen.getByTestId('modal')).toBeInTheDocument();
  
  cleanup(); // 清理测试环境
});

// 6. Cypress端到端测试示例
// cypress/integration/portal.spec.js
describe('Portal组件测试', () => {
  it('模态框正确显示和关闭', () => {
    cy.visit('/portal-demo');
    
    // 打开模态框
    cy.get('[data-testid="open-modal"]').click();
    
    // 验证Portal内容显示
    cy.get('[data-testid="modal-content"]').should('be.visible');
    
    // 测试键盘交互
    cy.get('body').type('{esc}');
    cy.get('[data-testid="modal-content"]').should('not.exist');
    
    // 测试点击外部关闭
    cy.get('[data-testid="open-modal"]').click();
    cy.get('.modal-backdrop').click({ force: true });
    cy.get('[data-testid="modal-content"]').should('not.exist');
  });
});`,
    tags: ['单元测试', 'React Testing Library', 'Jest', 'Cypress'],
    relatedQuestions: ['如何测试Portal的性能？', '如何在Storybook中展示Portal组件？']
  }
];

export default commonQuestions;