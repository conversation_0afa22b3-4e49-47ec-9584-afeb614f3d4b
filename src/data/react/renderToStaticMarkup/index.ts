import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const renderToStaticMarkupData: ApiItem = {
  id: 'renderToStaticMarkup',
  title: 'renderToStaticMarkup',
  description: 'React服务端渲染API，用于将React组件渲染为静态HTML字符串，不包含任何React特定的DOM属性，适用于生成纯静态内容。',
  category: 'ReactDOM Server API',
  difficulty: 'medium',

  syntax: `renderToStaticMarkup(reactNode)`,
  example: `import { renderToStaticMarkup } from 'react-dom/server';

function EmailTemplate({ name, message }) {
  return (
    <div>
      <h1>Hello {name}!</h1>
      <p>{message}</p>
    </div>
  );
}

// 生成纯静态HTML
const html = renderToStaticMarkup(
  <EmailTemplate name="张三" message="欢迎使用我们的服务！" />
);

console.log(html);
// <div><h1>Hello 张三!</h1><p>欢迎使用我们的服务！</p></div>`,
  notes: '生成纯净的HTML字符串，不包含任何React特定属性，适用于邮件模板、静态页面生成等场景。',

  version: 'React 16.0+',
  tags: ["ReactDOM", "Server", "SSR", "Static", "HTML"],

  // 9个Tab内容
  basicInfo,
  businessScenarios, // 现在直接是数组
  implementation,
  interviewQuestions, // 现在直接是数组
  commonQuestions, // 现在直接是数组
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default renderToStaticMarkupData;
