import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  id: 'renderToStaticMarkup',
  title: 'renderToStaticMarkup',
  description: 'React服务端渲染API，用于将React组件渲染为静态HTML字符串，不包含任何React特定的DOM属性，适用于生成纯静态内容。',
  
  syntax: `renderToStaticMarkup(reactNode)`,

  // 业务场景图表 - 展示使用场景和相关API
  scenarioDiagram: [
    {
      title: '静态HTML生成场景',
      description: '展示renderToStaticMarkup在不同业务场景中的应用',
      diagram: `graph TD
        A[React组件] --> B[renderToStaticMarkup]
        B --> C[纯静态HTML]

        C --> D[邮件模板]
        C --> E[静态站点]
        C --> F[PDF生成]
        C --> G[SEO预渲染]

        D --> D1[订单确认邮件]
        D --> D2[营销邮件]
        D --> D3[系统通知]

        E --> E1[博客文章]
        E --> E2[产品页面]
        E --> E3[文档站点]

        F --> F1[报告生成]
        F --> F2[发票模板]

        G --> G1[搜索引擎优化]
        G --> G2[社交媒体预览]

        style A fill:#e1f5fe
        style B fill:#f3e5f5
        style C fill:#e8f5e8
        style D fill:#fff3e0
        style E fill:#fce4ec
        style F fill:#f1f8e9
        style G fill:#e0f2f1`
    },
    {
      title: 'API对比分析',
      description: '对比renderToStaticMarkup与其他React渲染API的差异',
      diagram: `graph LR
        A[React渲染API] --> B[renderToString]
        A --> C[renderToStaticMarkup]
        A --> D[renderToPipeableStream]
        A --> E[renderToReadableStream]

        B --> B1[包含React属性]
        B --> B2[支持hydration]
        B --> B3[客户端激活]

        C --> C1[纯静态HTML]
        C --> C2[无React属性]
        C --> C3[不支持hydration]

        D --> D1[流式渲染]
        D --> D2[Node.js环境]
        D --> D3[支持Suspense]

        E --> E1[流式渲染]
        E --> E2[Web Streams]
        E --> E3[现代浏览器]

        style C fill:#4caf50,color:#fff
        style C1 fill:#81c784
        style C2 fill:#81c784
        style C3 fill:#81c784`
    }
  ],

  parameters: [
    {
      name: 'reactNode',
      type: 'ReactNode',
      description: '要渲染的React节点，可以是JSX元素、组件或任何有效的React节点',
      required: true,
      example: '<App />'
    }
  ],
  
  returnValue: {
    type: 'string',
    description: '返回不包含React特定属性的纯HTML字符串，适用于静态站点生成和邮件模板'
  },

  // 快速示例 - 完整且简单的基础用法
  quickExample: `import { renderToStaticMarkup } from 'react-dom/server';

function WelcomeEmail({ name, message }) {
  return (
    <div style={{ fontFamily: 'Arial, sans-serif', padding: '20px' }}>
      <h1 style={{ color: '#333' }}>欢迎 {name}!</h1>
      <p style={{ fontSize: '16px', lineHeight: '1.5' }}>{message}</p>
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f5f5f5' }}>
        <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>
          此邮件由系统自动发送，请勿回复。
        </p>
      </div>
    </div>
  );
}

// 生成纯静态HTML邮件
const emailHTML = renderToStaticMarkup(
  <WelcomeEmail
    name="张三"
    message="感谢您注册我们的服务！我们很高兴为您提供优质的体验。"
  />
);

console.log(emailHTML);
// 输出纯净的HTML，无React属性：
// <div style="font-family:Arial,sans-serif;padding:20px">
//   <h1 style="color:#333">欢迎 张三!</h1>
//   <p style="font-size:16px;line-height:1.5">感谢您注册我们的服务！...</p>
//   ...
// </div>`,

  category: 'ReactDOM Server API',
  
  keyFeatures: [
    {
      feature: '纯静态HTML生成',
      description: '生成不包含data-reactroot等React特定属性的纯净HTML',
      importance: 'high'
    },
    {
      feature: '无事件处理器',
      description: '移除所有事件处理器，生成的HTML完全静态',
      importance: 'high'
    },
    {
      feature: '同步渲染',
      description: '同步执行渲染过程，立即返回完整的HTML字符串',
      importance: 'medium'
    },
    {
      feature: '轻量级输出',
      description: '生成的HTML体积更小，没有React运行时标记',
      importance: 'medium'
    },
    {
      feature: '邮件模板友好',
      description: '生成的HTML兼容邮件客户端和静态站点生成器',
      importance: 'medium'
    }
  ],
  
  commonUseCase: {
    scenario: '静态站点生成',
    description: '为博客文章、产品页面等生成纯静态HTML，用于SEO优化和快速加载',
    code: `import { renderToStaticMarkup } from 'react-dom/server';

function BlogPost({ title, content }) {
  return (
    <article>
      <h1>{title}</h1>
      <div dangerouslySetInnerHTML={{ __html: content }} />
    </article>
  );
}

// 生成静态HTML
const html = renderToStaticMarkup(
  <BlogPost 
    title="React服务端渲染指南" 
    content="<p>这是博客内容...</p>" 
  />
);

// 输出纯净的HTML，无React属性
console.log(html);
// <article><h1>React服务端渲染指南</h1><div><p>这是博客内容...</p></div></article>`
  },
  
  codeExamples: [
    {
      title: '基础静态渲染',
      description: '将React组件渲染为纯静态HTML字符串',
      code: `import { renderToStaticMarkup } from 'react-dom/server';

function WelcomeMessage({ name }) {
  return <h1>欢迎, {name}!</h1>;
}

const staticHTML = renderToStaticMarkup(<WelcomeMessage name="张三" />);
console.log(staticHTML); // <h1>欢迎, 张三!</h1>`
    },
    {
      title: '邮件模板生成',
      description: '为邮件系统生成兼容的HTML模板',
      code: `import { renderToStaticMarkup } from 'react-dom/server';

function EmailTemplate({ user, orderNumber, items }) {
  return (
    <div style={{ fontFamily: 'Arial, sans-serif', maxWidth: '600px' }}>
      <h2>订单确认</h2>
      <p>亲爱的 {user.name}，</p>
      <p>您的订单 #{orderNumber} 已确认。</p>
      <ul>
        {items.map(item => (
          <li key={item.id}>{item.name} - ¥{item.price}</li>
        ))}
      </ul>
      <p>感谢您的购买！</p>
    </div>
  );
}

const emailHTML = renderToStaticMarkup(
  <EmailTemplate 
    user={{ name: '李四' }}
    orderNumber="12345"
    items={[
      { id: 1, name: 'React指南', price: 99 },
      { id: 2, name: 'TypeScript教程', price: 79 }
    ]}
  />
);

// 生成的HTML可直接用于邮件发送
sendEmail(userEmail, '订单确认', emailHTML);`
    },
    {
      title: '静态站点生成',
      description: '为静态站点生成器生成纯HTML页面',
      code: `import { renderToStaticMarkup } from 'react-dom/server';
import fs from 'fs';

function ProductPage({ product }) {
  return (
    <html>
      <head>
        <title>{product.name} - 商品详情</title>
        <meta name="description" content={product.description} />
      </head>
      <body>
        <main>
          <h1>{product.name}</h1>
          <img src={product.image} alt={product.name} />
          <p>{product.description}</p>
          <div className="price">¥{product.price}</div>
        </main>
      </body>
    </html>
  );
}

// 批量生成产品页面
products.forEach(product => {
  const html = '<!DOCTYPE html>' + renderToStaticMarkup(
    <ProductPage product={product} />
  );
  
  fs.writeFileSync(
    'dist/products/' + product.slug + '.html', 
    html
  );
});`
    }
  ],
  
  relatedAPIs: [
    {
      name: 'renderToString',
      description: '渲染为包含React属性的HTML字符串，支持客户端水合',
      relationship: '对比：renderToString保留React属性，renderToStaticMarkup生成纯静态HTML'
    },
    {
      name: 'renderToPipeableStream',
      description: 'Node.js环境下的流式服务端渲染',
      relationship: '对比：流式渲染vs同步静态渲染'
    },
    {
      name: 'renderToReadableStream',
      description: '基于Web Streams的跨平台流式渲染',
      relationship: '对比：现代流式渲染vs传统静态渲染'
    }
  ],
  
  bestPractices: [
    {
      title: '选择合适的使用场景',
      description: '仅在需要纯静态HTML时使用，如邮件模板、静态站点生成',
      example: '✅ 邮件模板、RSS feed、静态博客\n❌ 需要交互的SPA应用'
    },
    {
      title: '避免使用事件处理器',
      description: '组件中的事件处理器会被忽略，确保组件适合静态渲染',
      example: '// ❌ 事件处理器会被忽略\n<button onClick={handleClick}>点击</button>\n\n// ✅ 纯展示组件\n<div className="content">{text}</div>'
    },
    {
      title: '注意样式处理',
      description: '使用内联样式或确保CSS文件在最终页面中正确引用',
      example: '// ✅ 内联样式在静态HTML中有效\n<div style={{ color: "red" }}>文本</div>'
    }
  ],
  
  warnings: [
    {
      type: 'performance',
      message: '不支持Suspense和异步组件，所有数据必须在渲染前准备好'
    },
    {
      type: 'compatibility',
      message: '生成的HTML无法进行客户端水合，不适用于需要交互的应用'
    },
    {
      type: 'limitation',
      message: '同步渲染可能阻塞事件循环，不适合渲染大型复杂组件'
    }
  ],

  // 对比分析 - 与其他React渲染API的详细对比
  comparisonAnalysis: {
    title: 'React服务端渲染API对比分析',
    description: '深入对比renderToStaticMarkup与其他React渲染API的特性、性能和适用场景',
    comparisons: [
      {
        name: 'renderToString',
        description: '标准的服务端渲染API，生成包含React属性的HTML',
        advantages: [
          '支持客户端hydration',
          '保留React运行时信息',
          '可以无缝切换到客户端渲染',
          '支持事件处理器绑定'
        ],
        disadvantages: [
          'HTML体积较大（包含React属性）',
          '生成的HTML不够纯净',
          '不适合纯静态场景',
          '邮件客户端兼容性差'
        ],
        useCases: [
          'SSR应用',
          '需要hydration的页面',
          '同构应用',
          'SEO优化页面'
        ],
        performance: '中等（额外的React属性增加体积）',
        complexity: '中等（需要考虑hydration）'
      },
      {
        name: 'renderToPipeableStream',
        description: 'Node.js环境下的流式渲染API，支持Suspense和并发特性',
        advantages: [
          '流式渲染，更快的首字节时间',
          '支持Suspense和并发特性',
          '更好的用户体验',
          '支持渐进式加载'
        ],
        disadvantages: [
          '只支持Node.js环境',
          '实现复杂度高',
          '不适合简单静态内容',
          '需要流式处理能力'
        ],
        useCases: [
          '大型SSR应用',
          '需要流式加载的页面',
          '复杂的异步数据获取',
          '现代React应用'
        ],
        performance: '高（流式渲染优化）',
        complexity: '高（流式处理复杂）'
      },
      {
        name: 'renderToReadableStream',
        description: '基于Web Streams的跨平台流式渲染API',
        advantages: [
          '跨平台支持（Node.js + 浏览器）',
          '基于Web标准',
          '支持现代流式特性',
          '更好的性能表现'
        ],
        disadvantages: [
          '需要现代环境支持',
          '学习成本高',
          '生态系统相对较新',
          '调试复杂'
        ],
        useCases: [
          '现代Web应用',
          '边缘计算环境',
          'Cloudflare Workers',
          '跨平台SSR'
        ],
        performance: '高（现代流式优化）',
        complexity: '高（新技术栈）'
      }
    ],
    decisionMatrix: {
      description: '根据不同场景选择合适的渲染API',
      scenarios: [
        {
          scenario: '邮件模板生成',
          recommended: 'renderToStaticMarkup',
          reason: '邮件客户端需要纯净的HTML，不支持JavaScript和React属性'
        },
        {
          scenario: '静态站点生成',
          recommended: 'renderToStaticMarkup',
          reason: '生成纯静态HTML，体积小，加载快，SEO友好'
        },
        {
          scenario: 'PDF文档生成',
          recommended: 'renderToStaticMarkup',
          reason: 'PDF生成器需要纯净的HTML/CSS，不需要交互功能'
        },
        {
          scenario: 'SSR应用',
          recommended: 'renderToString',
          reason: '需要客户端hydration，保持React应用的交互性'
        },
        {
          scenario: '大型复杂应用',
          recommended: 'renderToPipeableStream',
          reason: '流式渲染提供更好的性能和用户体验'
        },
        {
          scenario: '边缘计算环境',
          recommended: 'renderToReadableStream',
          reason: '跨平台支持，适合现代边缘计算场景'
        }
      ]
    },
    summary: 'renderToStaticMarkup专注于生成纯静态HTML，是邮件模板、静态站点生成和PDF文档的最佳选择。对于需要交互性的应用，应选择renderToString或流式渲染API。'
  }
};

export default basicInfo;
