import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 'email-template-system',
    title: '企业邮件模板系统',
    difficulty: 'easy',
    description: '为企业级邮件系统构建可复用的HTML邮件模板，支持订单确认、营销推广、系统通知等多种邮件类型。',
    
    businessValue: '提升邮件营销效果，降低模板维护成本，确保跨邮件客户端兼容性',
    
    scenario: '电商平台需要发送订单确认邮件，要求邮件在各种邮件客户端（Gmail、Outlook、Apple Mail等）中都能正确显示，同时支持品牌定制和动态内容。',
    
    code: `import { renderToStaticMarkup } from 'react-dom/server';

// 邮件模板基础组件
function EmailTemplate({ children, branding }) {
  return (
    <div style={{
      fontFamily: 'Arial, sans-serif',
      maxWidth: '600px',
      margin: '0 auto',
      backgroundColor: '#ffffff'
    }}>
      <header style={{
        backgroundColor: branding.primaryColor,
        padding: '20px',
        textAlign: 'center'
      }}>
        <img src={branding.logo} alt={branding.name} style={{ height: '40px' }} />
      </header>
      <main style={{ padding: '20px' }}>
        {children}
      </main>
      <footer style={{
        backgroundColor: '#f8f9fa',
        padding: '15px',
        fontSize: '12px',
        color: '#666'
      }}>
        <p>© 2024 {branding.name}. 保留所有权利。</p>
      </footer>
    </div>
  );
}

// 订单确认邮件组件
function OrderConfirmationEmail({ order, customer, branding }) {
  return (
    <EmailTemplate branding={branding}>
      <h2 style={{ color: '#333', marginBottom: '20px' }}>
        订单确认 #{order.number}
      </h2>
      
      <p>亲爱的 {customer.name}，</p>
      <p>感谢您的订购！您的订单已确认，详情如下：</p>
      
      <table style={{
        width: '100%',
        borderCollapse: 'collapse',
        marginBottom: '20px'
      }}>
        <thead>
          <tr style={{ backgroundColor: '#f8f9fa' }}>
            <th style={{ padding: '10px', textAlign: 'left', border: '1px solid #ddd' }}>
              商品
            </th>
            <th style={{ padding: '10px', textAlign: 'right', border: '1px solid #ddd' }}>
              价格
            </th>
          </tr>
        </thead>
        <tbody>
          {order.items.map(item => (
            <tr key={item.id}>
              <td style={{ padding: '10px', border: '1px solid #ddd' }}>
                {item.name}
              </td>
              <td style={{ padding: '10px', textAlign: 'right', border: '1px solid #ddd' }}>
                ¥{item.price}
              </td>
            </tr>
          ))}
        </tbody>
        <tfoot>
          <tr style={{ fontWeight: 'bold' }}>
            <td style={{ padding: '10px', border: '1px solid #ddd' }}>
              总计
            </td>
            <td style={{ padding: '10px', textAlign: 'right', border: '1px solid #ddd' }}>
              ¥{order.total}
            </td>
          </tr>
        </tfoot>
      </table>
      
      <div style={{
        backgroundColor: '#e3f2fd',
        padding: '15px',
        borderRadius: '4px',
        marginBottom: '20px'
      }}>
        <h3 style={{ margin: '0 0 10px 0', color: '#1976d2' }}>配送信息</h3>
        <p style={{ margin: 0 }}>
          预计配送时间：{order.estimatedDelivery}<br/>
          配送地址：{order.shippingAddress}
        </p>
      </div>
      
      <p>如有任何问题，请联系我们的客服团队。</p>
    </EmailTemplate>
  );
}

// 邮件生成服务
class EmailService {
  generateOrderConfirmationEmail(order, customer) {
    const branding = {
      name: '优质商城',
      logo: 'https://example.com/logo.png',
      primaryColor: '#1976d2'
    };
    
    const html = renderToStaticMarkup(
      <OrderConfirmationEmail 
        order={order}
        customer={customer}
        branding={branding}
      />
    );
    
    return {
      subject: '订单确认 #' + order.number,
      html: html,
      to: customer.email
    };
  }
  
  async sendEmail(emailData) {
    // 集成邮件发送服务
    await mailService.send({
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html
    });
  }
}

// 使用示例
const emailService = new EmailService();
const order = {
  number: 'ORD-2024-001',
  items: [
    { id: 1, name: 'React开发指南', price: 99 },
    { id: 2, name: 'TypeScript实战', price: 89 }
  ],
  total: 188,
  estimatedDelivery: '2024-01-15',
  shippingAddress: '北京市朝阳区xxx街道xxx号'
};

const customer = {
  name: '张三',
  email: '<EMAIL>'
};

const emailData = emailService.generateOrderConfirmationEmail(order, customer);
await emailService.sendEmail(emailData);

console.log('生成的HTML邮件：');
console.log(emailData.html);`,
    
    explanation: `这个示例展示了如何使用 renderToStaticMarkup 构建企业级邮件模板系统：

**核心优势：**
1. **组件化设计**：EmailTemplate 作为基础模板，OrderConfirmationEmail 作为具体业务组件
2. **样式内联**：所有样式都使用内联 style，确保邮件客户端兼容性
3. **纯静态输出**：renderToStaticMarkup 生成不含 React 属性的纯净 HTML
4. **类型安全**：TypeScript 支持确保数据结构正确性

**关键技术点：**
- 使用内联样式而非 CSS 类，避免邮件客户端样式丢失
- 表格布局确保在各种邮件客户端中的一致性
- 图片使用绝对 URL，确保邮件中能正确显示
- 响应式设计考虑移动端邮件阅读体验

**实际应用场景：**
- 订单确认邮件
- 营销推广邮件  
- 系统通知邮件
- 用户注册欢迎邮件`,
    
    benefits: [
      '组件化设计提升模板复用性',
      '纯HTML输出确保邮件客户端兼容性',
      'React生态工具支持提升开发效率',
      '类型安全的模板开发体验'
    ],
    
    tags: ['邮件模板', '企业应用', 'HTML生成', '跨客户端兼容']
  },
  
  {
    id: 'static-blog-generator',
    title: '高性能静态博客生成器',
    difficulty: 'medium',
    description: '构建现代化的静态博客生成系统，支持Markdown内容、SEO优化、多主题切换和增量构建。',
    
    businessValue: '极快的页面加载速度，优秀的SEO表现，零服务器成本，高可用性',
    
    scenario: '技术博客需要快速的页面加载速度和优秀的SEO表现，同时希望降低服务器成本。使用静态生成可以实现CDN部署，获得全球访问加速。',
    
    code: `import { renderToStaticMarkup } from 'react-dom/server';
import { marked } from 'marked';
import fs from 'fs';
import path from 'path';

// 博客页面组件
function BlogPost({ post, theme }) {
  return (
    <html lang="zh-CN">
      <head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>{post.title} - 技术博客</title>
        <meta name="description" content={post.excerpt} />
        <meta name="keywords" content={post.tags.join(', ')} />
        
        {/* SEO优化 */}
        <meta property="og:title" content={post.title} />
        <meta property="og:description" content={post.excerpt} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={\`https://blog.example.com/\${post.slug}\`} />
        
        <link rel="stylesheet" href={\`/themes/\${theme}/style.css\`} />
      </head>
      <body>
        <header className="blog-header">
          <nav>
            <a href="/">首页</a>
            <a href="/about">关于</a>
            <a href="/archive">归档</a>
          </nav>
        </header>
        
        <main className="blog-main">
          <article className="blog-post">
            <header className="post-header">
              <h1>{post.title}</h1>
              <div className="post-meta">
                <time dateTime={post.date}>{post.formattedDate}</time>
                <div className="post-tags">
                  {post.tags.map(tag => (
                    <span key={tag} className="tag">#{tag}</span>
                  ))}
                </div>
              </div>
            </header>
            
            <div 
              className="post-content"
              dangerouslySetInnerHTML={{ __html: post.htmlContent }}
            />
            
            <footer className="post-footer">
              <div className="post-nav">
                {post.prevPost && (
                  <a href={\`/\${post.prevPost.slug}\`} className="prev-post">
                    ← {post.prevPost.title}
                  </a>
                )}
                {post.nextPost && (
                  <a href={\`/\${post.nextPost.slug}\`} className="next-post">
                    {post.nextPost.title} →
                  </a>
                )}
              </div>
            </footer>
          </article>
        </main>
        
        <footer className="blog-footer">
          <p>© 2024 技术博客. 使用 renderToStaticMarkup 生成</p>
        </footer>
      </body>
    </html>
  );
}

// 静态站点生成器
class StaticSiteGenerator {
  constructor(config) {
    this.config = config;
    this.posts = [];
  }
  
  // 读取并解析Markdown文件
  async loadPosts() {
    const postsDir = path.join(process.cwd(), 'content/posts');
    const files = fs.readdirSync(postsDir);
    
    this.posts = files
      .filter(file => file.endsWith('.md'))
      .map(file => {
        const filePath = path.join(postsDir, file);
        const content = fs.readFileSync(filePath, 'utf-8');
        const { data: frontmatter, content: markdown } = matter(content);
        
        return {
          ...frontmatter,
          slug: file.replace('.md', ''),
          htmlContent: marked(markdown),
          formattedDate: new Date(frontmatter.date).toLocaleDateString('zh-CN')
        };
      })
      .sort((a, b) => new Date(b.date) - new Date(a.date));
  }
  
  // 生成单个博客页面
  generatePostPage(post, index) {
    const prevPost = index < this.posts.length - 1 ? this.posts[index + 1] : null;
    const nextPost = index > 0 ? this.posts[index - 1] : null;
    
    const postWithNav = {
      ...post,
      prevPost,
      nextPost
    };
    
    const html = renderToStaticMarkup(
      <BlogPost post={postWithNav} theme={this.config.theme} />
    );
    
    // 添加DOCTYPE声明
    return \`<!DOCTYPE html>\n\${html}\`;
  }
  
  // 构建所有页面
  async build() {
    await this.loadPosts();
    
    const outputDir = path.join(process.cwd(), 'dist');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // 生成博客文章页面
    this.posts.forEach((post, index) => {
      const html = this.generatePostPage(post, index);
      const outputPath = path.join(outputDir, \`\${post.slug}.html\`);
      fs.writeFileSync(outputPath, html);
      console.log(\`Generated: \${post.slug}.html\`);
    });
    
    console.log(\`✅ 成功生成 \${this.posts.length} 个页面\`);
  }
}

// 使用示例
const generator = new StaticSiteGenerator({
  theme: 'modern',
  siteUrl: 'https://blog.example.com'
});

generator.build().catch(console.error);`,
    
    explanation: `这个示例展示了如何使用 renderToStaticMarkup 构建静态博客生成器：

**核心特性：**
1. **完整的HTML文档**：生成包含 DOCTYPE、head、body 的完整HTML页面
2. **SEO优化**：自动生成 meta 标签、Open Graph 标签
3. **Markdown支持**：将 Markdown 内容转换为HTML
4. **主题系统**：支持多主题切换
5. **导航功能**：自动生成上一篇/下一篇链接

**技术亮点：**
- 使用 dangerouslySetInnerHTML 安全地插入Markdown转换的HTML
- 文件系统操作实现批量页面生成
- 前置元数据（frontmatter）解析
- 自动化的构建流程

**性能优势：**
- 构建时生成，运行时零计算开销
- 可部署到CDN，全球访问加速
- 无服务器依赖，高可用性
- 优秀的SEO表现`,
    
    benefits: [
      '极快的页面加载速度',
      '优秀的SEO表现',
      '零服务器运行成本',
      '高可用性和稳定性',
      '支持CDN全球部署'
    ],
    
    tags: ['静态生成', 'SEO优化', '博客系统', '性能优化']
  }
];

export default businessScenarios;
