import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
    {
      id: 1,
      category: 'basic',
      difficulty: 'easy',
      question: 'renderToStaticMarkup与renderToString有什么区别？分别适用于什么场景？',

      answer: {
        brief: 'renderToString生成包含React属性的HTML用于水合，renderToStaticMarkup生成纯净HTML用于静态内容。前者适用于SPA，后者适用于邮件模板和静态站点。',

        detailed: `renderToStaticMarkup和renderToString都是React服务端渲染API，但有重要区别：

**主要区别**：
1. **输出内容**：
   - renderToString：生成包含React特定属性（如data-reactroot）的HTML，支持客户端水合
   - renderToStaticMarkup：生成纯净的HTML，不包含任何React属性

2. **事件处理器**：
   - renderToString：保留事件处理器信息用于水合
   - renderToStaticMarkup：完全移除所有事件处理器

3. **客户端水合**：
   - renderToString：支持hydrateRoot进行客户端水合
   - renderToStaticMarkup：不支持水合，生成的HTML完全静态

**适用场景**：

**renderToString适用于**：
- 需要客户端交互的SPA应用
- 需要水合的同构应用
- 需要保持React组件状态的场景

**renderToStaticMarkup适用于**：
- 邮件模板生成
- 静态站点生成（如博客、文档站点）
- RSS feed生成
- 不需要交互的纯展示页面`,

        code: `// 对比示例
const App = () => (
  <div onClick={() => alert('clicked')}>
    <h1>Hello World</h1>
  </div>
);

// renderToString输出
const htmlWithReact = renderToString(<App />);
console.log(htmlWithReact);
// <div data-reactroot="">
//   <h1>Hello World</h1>
// </div>

// renderToStaticMarkup输出
const staticHtml = renderToStaticMarkup(<App />);
console.log(staticHtml);
// <div>
//   <h1>Hello World</h1>
// </div>

// 使用场景对比
// ✅ 邮件模板 - 使用renderToStaticMarkup
const emailHtml = renderToStaticMarkup(
  <EmailTemplate user={user} order={order} />
);

// ✅ SPA应用 - 使用renderToString
const appHtml = renderToString(<App />);
const fullPage = \`
<!DOCTYPE html>
<html>
  <body>
    <div id="root">\${appHtml}</div>
    <script src="/bundle.js"></script>
  </body>
</html>
\`;`
      }
    },
    
    {
      id: 2,
      category: 'intermediate',
      difficulty: 'medium',
      question: 'renderToStaticMarkup的内部实现原理是什么？它如何处理组件树和生成HTML？',

      answer: {
        brief: 'renderToStaticMarkup通过深度优先遍历React组件树，将虚拟DOM转换为HTML字符串，过滤事件处理器和React属性，进行HTML转义后生成纯净的静态HTML。',

        detailed: `renderToStaticMarkup的实现原理基于React的虚拟DOM遍历和HTML字符串生成：

**核心实现步骤**：

1. **组件树遍历**：
   - 使用深度优先搜索遍历整个React组件树
   - 递归执行函数组件，获取返回的React元素
   - 处理各种节点类型（元素、文本、数组、null等）

2. **HTML生成**：
   - 将React元素转换为对应的HTML标签
   - 过滤掉事件处理器和React特定属性
   - 处理属性名转换（className → class）

3. **字符串拼接**：
   - 使用高效的字符串拼接策略
   - 进行HTML转义防止XSS攻击
   - 生成最终的HTML字符串

**关键特性**：
- **同步渲染**：不支持Suspense，所有数据必须同步可用
- **静态模式**：移除所有React运行时标记
- **纯函数**：无副作用，相同输入产生相同输出`,

        code: `// 简化的实现原理
function renderToStaticMarkup(element) {
  const renderer = new StaticRenderer();
  return renderer.render(element);
}

class StaticRenderer {
  constructor() {
    this.html = [];
  }
  
  render(element) {
    this.renderElement(element);
    return this.html.join('');
  }
  
  renderElement(element) {
    // 处理不同类型的节点
    if (typeof element === 'string' || typeof element === 'number') {
      // 文本节点
      this.html.push(escapeHtml(String(element)));
    } else if (element && element.type) {
      // React元素
      this.renderReactElement(element);
    } else if (Array.isArray(element)) {
      // 数组
      element.forEach(child => this.renderElement(child));
    }
    // null, undefined, boolean 不输出任何内容
  }
  
  renderReactElement(element) {
    const { type, props } = element;
    
    if (typeof type === 'string') {
      // DOM元素
      this.renderDOMElement(type, props);
    } else if (typeof type === 'function') {
      // 组件
      const result = type(props);
      this.renderElement(result);
    }
  }
  
  renderDOMElement(tagName, props) {
    this.html.push('<' + tagName);
    
    // 渲染属性（过滤事件处理器）
    for (const [key, value] of Object.entries(props)) {
      if (key === 'children') continue;
      if (key.startsWith('on')) continue; // 跳过事件处理器
      
      if (value != null && value !== false) {
        const attrName = key === 'className' ? 'class' : key;
        this.html.push(' ' + attrName + '="' + escapeHtml(value) + '"');
      }
    }
    
    if (isVoidElement(tagName)) {
      this.html.push(' />');
    } else {
      this.html.push('>');
      
      // 渲染子元素
      if (props.children) {
        this.renderElement(props.children);
      }
      
      this.html.push('</' + tagName + '>');
    }
  }
}

// HTML转义函数
function escapeHtml(text) {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;');
}`
      }
    },
    
    {
      id: 3,
      category: 'advanced',
      difficulty: 'hard',
      question: '在构建一个大型静态站点生成器时，如何优化renderToStaticMarkup的性能？请设计一个高效的解决方案。',

      answer: {
        brief: '通过分块渲染、Worker线程并行处理、增量构建、内存管理、组件缓存和智能依赖分析等策略，可以显著提升大型静态站点生成器的性能。',

        detailed: `构建大型静态站点生成器时，需要从多个维度优化renderToStaticMarkup的性能：

**性能优化策略**：

1. **分块渲染**：
   - 将大型页面拆分为多个小块独立渲染
   - 使用Worker线程并行处理
   - 实现增量构建，只重新生成变更的页面

2. **内存管理**：
   - 使用流式写入避免大量HTML字符串在内存中累积
   - 及时释放已处理的组件引用
   - 实现组件缓存机制

3. **构建优化**：
   - 实现智能依赖分析
   - 使用文件系统缓存
   - 并行处理独立页面

4. **模板优化**：
   - 预编译常用组件
   - 使用组件缓存减少重复渲染
   - 优化组件结构减少嵌套层级`,

        code: `// 高性能静态站点生成器设计
class OptimizedStaticGenerator {
  constructor(options) {
    this.options = options;
    this.cache = new Map();
    this.workerPool = new WorkerPool(options.maxWorkers || 4);
  }
  
  async generateSite(pages) {
    // 1. 依赖分析和分组
    const pageGroups = this.analyzeAndGroupPages(pages);
    
    // 2. 并行处理页面组
    const results = await Promise.all(
      pageGroups.map(group => this.processPageGroup(group))
    );
    
    return results.flat();
  }
  
  async processPageGroup(pages) {
    return await this.workerPool.execute(async () => {
      const results = [];
      
      for (const page of pages) {
        // 检查缓存
        const cacheKey = this.getCacheKey(page);
        if (this.cache.has(cacheKey) && !page.isDirty) {
          results.push(this.cache.get(cacheKey));
          continue;
        }
        
        // 分块渲染大型页面
        const html = await this.renderPageInChunks(page);
        
        // 缓存结果
        this.cache.set(cacheKey, html);
        results.push(html);
      }
      
      return results;
    });
  }
  
  async renderPageInChunks(page) {
    const chunks = this.splitPageIntoChunks(page);
    const htmlChunks = [];
    
    for (const chunk of chunks) {
      // 使用renderToStaticMarkup渲染每个块
      const chunkHtml = renderToStaticMarkup(chunk.component);
      htmlChunks.push(chunkHtml);
      
      // 内存管理：及时释放大对象
      if (global.gc && htmlChunks.length % 100 === 0) {
        global.gc();
      }
    }
    
    return this.assembleChunks(htmlChunks, page.template);
  }
  
  splitPageIntoChunks(page) {
    // 智能分块策略
    return [
      { component: <PageHeader {...page.header} />, type: 'header' },
      { component: <PageContent {...page.content} />, type: 'content' },
      { component: <PageSidebar {...page.sidebar} />, type: 'sidebar' },
      { component: <PageFooter {...page.footer} />, type: 'footer' }
    ];
  }
  
  // 增量构建实现
  async incrementalBuild(changedFiles) {
    // 1. 分析影响范围
    const affectedPages = this.analyzeAffectedPages(changedFiles);
    
    // 2. 清理相关缓存
    affectedPages.forEach(page => {
      const cacheKey = this.getCacheKey(page);
      this.cache.delete(cacheKey);
    });
    
    // 3. 只重新生成受影响的页面
    return await this.generateSite(affectedPages);
  }
  
  // 组件缓存机制
  getCachedComponent(Component, props) {
    const cacheKey = JSON.stringify({ Component: Component.name, props });
    
    if (this.componentCache.has(cacheKey)) {
      return this.componentCache.get(cacheKey);
    }
    
    const html = renderToStaticMarkup(<Component {...props} />);
    this.componentCache.set(cacheKey, html);
    
    return html;
  }
}

// Worker线程池实现
class WorkerPool {
  constructor(maxWorkers) {
    this.maxWorkers = maxWorkers;
    this.workers = [];
    this.queue = [];
    this.activeJobs = 0;
  }
  
  async execute(task) {
    return new Promise((resolve, reject) => {
      this.queue.push({ task, resolve, reject });
      this.processQueue();
    });
  }
  
  async processQueue() {
    if (this.queue.length === 0 || this.activeJobs >= this.maxWorkers) {
      return;
    }
    
    const { task, resolve, reject } = this.queue.shift();
    this.activeJobs++;
    
    try {
      const result = await task();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.activeJobs--;
      this.processQueue();
    }
  }
}

// 使用示例
const generator = new OptimizedStaticGenerator({
  maxWorkers: 4,
  cacheDir: '.cache',
  outputDir: 'dist'
});

// 全量构建
await generator.generateSite(allPages);

// 增量构建
await generator.incrementalBuild(['content/blog/new-post.md']);`
      }
    }
];

export default interviewQuestions;
