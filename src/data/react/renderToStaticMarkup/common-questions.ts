import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
    {
      id: 'question-1',
      question: 'renderToStaticMarkup生成的HTML为什么不能进行客户端水合？',
      answer: `renderToStaticMarkup生成的HTML无法进行客户端水合的根本原因是缺少React运行时所需的标识信息。

**技术原因**：
1. **缺少React标识**：没有data-reactroot等React特定属性，React无法识别这是服务端渲染的内容
2. **事件处理器丢失**：所有事件处理器在渲染时被移除，客户端无法恢复事件绑定
3. **组件状态缺失**：没有保存组件的初始状态信息，无法在客户端重建状态
4. **结构信息不完整**：缺少组件边界和层级信息，React无法正确匹配虚拟DOM

**水合过程需要的信息**：
- 组件树结构映射
- 初始props和state
- 事件处理器绑定信息
- React内部的fiber节点标识

**解决方案**：
如果需要客户端交互，应该使用renderToString配合hydrateRoot进行水合。`,
      code: `// ❌ 错误：尝试水合renderToStaticMarkup的输出
const staticHtml = renderToStaticMarkup(<App />);
// 生成：<div><h1>Hello</h1></div>

// 客户端尝试水合会失败
const root = hydrateRoot(document.getElementById('root'), <App />);
// 错误：无法匹配服务端渲染的内容

// ✅ 正确：使用renderToString进行水合
const serverHtml = renderToString(<App />);
// 生成：<div data-reactroot=""><h1>Hello</h1></div>

// 客户端水合成功
const root = hydrateRoot(document.getElementById('root'), <App />);`,
      tags: ['水合', '客户端渲染', 'SSR'],
      relatedQuestions: ['什么时候应该选择renderToString而不是renderToStaticMarkup？', '如何处理renderToStaticMarkup的交互需求？']
    },
    
    {
      id: 'question-2',
      question: 'renderToStaticMarkup中的样式如何处理？CSS-in-JS库还能正常工作吗？',
      answer: `renderToStaticMarkup对样式的处理有特殊考虑，CSS-in-JS库的支持情况因库而异。

**样式处理方式**：
1. **内联样式**：style属性正常工作，会被渲染到HTML中
2. **CSS类名**：className会被转换为class属性
3. **CSS-in-JS**：大部分库需要特殊配置才能在静态渲染中工作

**CSS-in-JS库兼容性**：
- **styled-components**：需要使用ServerStyleSheet进行服务端渲染
- **emotion**：需要配置服务端渲染支持
- **stitches**：原生支持静态渲染
- **vanilla-extract**：构建时生成CSS，完全兼容

**最佳实践**：
1. 优先使用内联样式确保兼容性
2. 使用构建时CSS生成的方案
3. 避免依赖客户端JavaScript的样式库`,
      code: `// ✅ 内联样式 - 完全兼容
const EmailComponent = () => (
  <div style={{
    fontFamily: 'Arial, sans-serif',
    backgroundColor: '#f5f5f5',
    padding: '20px'
  }}>
    <h1 style={{ color: '#333' }}>标题</h1>
  </div>
);

// ✅ CSS类名 - 需要确保CSS文件存在
const BlogPost = () => (
  <article className="blog-post">
    <h1 className="post-title">文章标题</h1>
  </article>
);

// ⚠️ styled-components - 需要特殊处理
import styled, { ServerStyleSheet } from 'styled-components';

const StyledButton = styled.button\`
  background: blue;
  color: white;
\`;

// 服务端渲染配置
const sheet = new ServerStyleSheet();
try {
  const html = renderToStaticMarkup(
    sheet.collectStyles(<StyledButton>按钮</StyledButton>)
  );
  const styleTags = sheet.getStyleTags();
  
  // 需要手动插入样式标签
  const fullHtml = \`
    <html>
      <head>\${styleTags}</head>
      <body>\${html}</body>
    </html>
  \`;
} finally {
  sheet.seal();
}

// ✅ 推荐：使用构建时CSS
// 使用PostCSS、Sass等构建时处理CSS
const Component = () => (
  <div className="component">
    {/* CSS通过构建工具生成 */}
  </div>
);`,
      tags: ['样式', 'CSS-in-JS', '服务端渲染'],
      relatedQuestions: ['如何在邮件模板中确保样式兼容性？', '哪些CSS-in-JS库最适合静态渲染？']
    },
    
    {
      id: 'question-3',
      question: 'renderToStaticMarkup可以处理异步数据吗？如何处理数据获取？',
      answer: `renderToStaticMarkup不支持异步数据处理，所有数据必须在渲染前同步准备好。

**限制说明**：
1. **不支持Suspense**：遇到Suspense组件会直接渲染fallback内容
2. **不支持异步组件**：无法等待异步数据加载完成
3. **同步渲染**：整个渲染过程必须同步完成

**数据处理策略**：
1. **预获取数据**：在调用renderToStaticMarkup前获取所有需要的数据
2. **数据注入**：通过props将数据传递给组件
3. **静态数据**：使用构建时可确定的静态数据
4. **缓存机制**：对频繁使用的数据进行缓存

**实现模式**：
- 数据获取层与渲染层分离
- 使用数据预处理管道
- 实现数据依赖分析`,
      code: `// ❌ 错误：在组件内部进行异步数据获取
function BlogPost({ postId }) {
  const [post, setPost] = useState(null);
  
  useEffect(() => {
    fetchPost(postId).then(setPost); // 这在静态渲染中不会执行
  }, [postId]);
  
  if (!post) return <div>Loading...</div>; // 总是显示Loading
  
  return <article>{post.content}</article>;
}

// ✅ 正确：预获取数据然后渲染
async function generateBlogPost(postId) {
  // 1. 预获取所有需要的数据
  const post = await fetchPost(postId);
  const author = await fetchAuthor(post.authorId);
  const comments = await fetchComments(postId);
  
  // 2. 将数据作为props传递
  const html = renderToStaticMarkup(
    <BlogPost 
      post={post}
      author={author}
      comments={comments}
    />
  );
  
  return html;
}

// 组件只负责渲染，不处理数据获取
function BlogPost({ post, author, comments }) {
  return (
    <article>
      <h1>{post.title}</h1>
      <div className="author">作者：{author.name}</div>
      <div dangerouslySetInnerHTML={{ __html: post.content }} />
      
      <section className="comments">
        <h3>评论 ({comments.length})</h3>
        {comments.map(comment => (
          <div key={comment.id} className="comment">
            <strong>{comment.author}</strong>: {comment.content}
          </div>
        ))}
      </section>
    </article>
  );
}

// 批量处理多个页面
async function generateAllPosts() {
  const posts = await fetchAllPosts();
  
  const htmlPages = await Promise.all(
    posts.map(async post => {
      const postData = await fetchPostData(post.id);
      return {
        path: '/posts/' + post.slug + '.html',
        html: renderToStaticMarkup(<BlogPost {...postData} />)
      };
    })
  );
  
  return htmlPages;
}

// 数据缓存优化
class DataCache {
  constructor() {
    this.cache = new Map();
  }
  
  async getOrFetch(key, fetcher) {
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }
    
    const data = await fetcher();
    this.cache.set(key, data);
    return data;
  }
}

const cache = new DataCache();

async function generatePostWithCache(postId) {
  const post = await cache.getOrFetch('post:' + postId, () => fetchPost(postId));
  const author = await cache.getOrFetch('author:' + post.authorId, () => fetchAuthor(post.authorId));
  
  return renderToStaticMarkup(<BlogPost post={post} author={author} />);
}`,
      tags: ['异步数据', '数据获取', '静态渲染'],
      relatedQuestions: ['如何设计高效的数据预获取策略？', '什么情况下应该考虑使用流式渲染替代静态渲染？']
    },
    
    {
      id: 'question-4',
      question: '在邮件模板中使用renderToStaticMarkup时，如何确保跨邮件客户端兼容性？',
      answer: `邮件客户端的HTML和CSS支持非常有限，使用renderToStaticMarkup生成邮件模板需要特别注意兼容性。

**邮件客户端限制**：
1. **CSS支持有限**：不支持现代CSS特性，建议使用内联样式
2. **JavaScript禁用**：所有JavaScript都会被禁用
3. **HTML标签限制**：某些HTML5标签不被支持
4. **图片处理**：需要使用绝对URL

**兼容性策略**：
1. **使用表格布局**：table元素在邮件中兼容性最好
2. **内联样式**：避免使用外部CSS文件
3. **绝对定位避免**：使用相对定位和表格布局
4. **图片优化**：使用CDN和绝对URL

**测试方法**：
- 使用Email on Acid等测试工具
- 在主流邮件客户端中测试
- 提供纯文本备选方案`,
      code: `// ✅ 邮件兼容的组件设计
function EmailTemplate({ order, customer }) {
  return (
    <table 
      style={{
        width: '100%',
        maxWidth: '600px',
        margin: '0 auto',
        fontFamily: 'Arial, sans-serif',
        backgroundColor: '#ffffff'
      }}
      cellPadding="0" 
      cellSpacing="0"
    >
      {/* 头部 */}
      <tr>
        <td style={{
          backgroundColor: '#1976d2',
          padding: '20px',
          textAlign: 'center'
        }}>
          <img 
            src="https://cdn.example.com/logo.png" 
            alt="公司Logo"
            style={{
              height: '40px',
              display: 'block',
              margin: '0 auto'
            }}
          />
        </td>
      </tr>
      
      {/* 内容区域 */}
      <tr>
        <td style={{ padding: '20px' }}>
          <h2 style={{
            color: '#333333',
            fontSize: '24px',
            marginBottom: '20px',
            marginTop: '0'
          }}>
            订单确认 #{order.number}
          </h2>
          
          <p style={{
            color: '#666666',
            fontSize: '16px',
            lineHeight: '1.5',
            margin: '0 0 20px 0'
          }}>
            亲爱的 {customer.name}，感谢您的订购！
          </p>
          
          {/* 订单详情表格 */}
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            marginBottom: '20px'
          }}>
            <tr style={{ backgroundColor: '#f8f9fa' }}>
              <td style={{
                padding: '12px',
                border: '1px solid #dddddd',
                fontWeight: 'bold'
              }}>
                商品
              </td>
              <td style={{
                padding: '12px',
                border: '1px solid #dddddd',
                fontWeight: 'bold',
                textAlign: 'right'
              }}>
                价格
              </td>
            </tr>
            {order.items.map(item => (
              <tr key={item.id}>
                <td style={{
                  padding: '12px',
                  border: '1px solid #dddddd'
                }}>
                  {item.name}
                </td>
                <td style={{
                  padding: '12px',
                  border: '1px solid #dddddd',
                  textAlign: 'right'
                }}>
                  ¥{item.price}
                </td>
              </tr>
            ))}
          </table>
          
          {/* CTA按钮 */}
          <table style={{ margin: '20px 0' }}>
            <tr>
              <td>
                <a 
                  href={\`https://example.com/orders/\${order.id}\`}
                  style={{
                    display: 'inline-block',
                    padding: '12px 24px',
                    backgroundColor: '#1976d2',
                    color: '#ffffff',
                    textDecoration: 'none',
                    borderRadius: '4px',
                    fontSize: '16px',
                    fontWeight: 'bold'
                  }}
                >
                  查看订单详情
                </a>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      
      {/* 页脚 */}
      <tr>
        <td style={{
          backgroundColor: '#f8f9fa',
          padding: '15px',
          textAlign: 'center',
          fontSize: '12px',
          color: '#666666'
        }}>
          <p style={{ margin: '0' }}>
            © 2024 优质商城. 保留所有权利.
          </p>
          <p style={{ margin: '5px 0 0 0' }}>
            如不想接收此类邮件，请 
            <a href="#" style={{ color: '#1976d2' }}>取消订阅</a>
          </p>
        </td>
      </tr>
    </table>
  );
}

// 邮件生成和测试工具
class EmailGenerator {
  generateEmail(template, data) {
    const html = renderToStaticMarkup(template(data));
    
    // 添加邮件特定的DOCTYPE和meta标签
    return \`
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>订单确认</title>
</head>
<body style="margin: 0; padding: 0;">
  \${html}
</body>
</html>
    \`.trim();
  }
  
  // 兼容性检查
  validateEmailHtml(html) {
    const issues = [];
    
    // 检查是否使用了外部CSS
    if (html.includes('<link') || html.includes('@import')) {
      issues.push('避免使用外部CSS文件');
    }
    
    // 检查是否使用了不兼容的标签
    const incompatibleTags = ['video', 'audio', 'canvas', 'svg'];
    incompatibleTags.forEach(tag => {
      if (html.includes('<' + tag)) {
        issues.push(\`避免使用 <\${tag}> 标签\`);
      }
    });
    
    return issues;
  }
}`,
      tags: ['邮件模板', '兼容性', '跨客户端'],
      relatedQuestions: ['如何测试邮件模板在不同客户端中的显示效果？', '邮件模板中的图片应该如何处理？']
    },
    
    {
      id: 'question-5',
      question: 'renderToStaticMarkup的性能如何？在什么情况下可能遇到性能问题？',
      answer: `renderToStaticMarkup的性能特点是同步渲染，在大多数场景下性能良好，但在特定情况下可能遇到瓶颈。

**性能特点**：
1. **同步执行**：整个渲染过程在单线程中同步完成
2. **内存效率**：直接生成字符串，避免了DOM操作开销
3. **CPU密集**：大量的字符串拼接和组件执行

**可能的性能问题**：
1. **大型组件树**：深层嵌套或大量组件会增加渲染时间
2. **复杂计算**：组件中的复杂逻辑会阻塞渲染
3. **内存压力**：大量HTML字符串可能导致内存峰值
4. **事件循环阻塞**：长时间的同步渲染会阻塞其他任务

**优化策略**：
1. **分块渲染**：将大型页面拆分为小块
2. **组件优化**：减少不必要的组件嵌套
3. **数据预处理**：避免在渲染过程中进行复杂计算
4. **并行处理**：使用Worker线程处理独立页面`,
      code: `// ⚠️ 性能问题示例
function SlowComponent({ items }) {
  return (
    <div>
      {items.map(item => (
        <div key={item.id}>
          {/* 避免在渲染中进行复杂计算 */}
          {expensiveCalculation(item)} {/* ❌ 性能问题 */}
          
          {/* 避免深层嵌套 */}
          <div>
            <div>
              <div>
                <div>{item.name}</div> {/* ❌ 过度嵌套 */}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

// ✅ 性能优化版本
function OptimizedComponent({ items }) {
  // 预处理数据，避免在渲染中计算
  const processedItems = useMemo(() => 
    items.map(item => ({
      ...item,
      processedValue: expensiveCalculation(item)
    })), 
    [items]
  );
  
  return (
    <div>
      {processedItems.map(item => (
        <div key={item.id} className="item">
          {item.processedValue}
          <span>{item.name}</span>
        </div>
      ))}
    </div>
  );
}

// 分块渲染策略
class ChunkedRenderer {
  constructor(chunkSize = 100) {
    this.chunkSize = chunkSize;
  }
  
  async renderLargeList(items, ItemComponent) {
    const chunks = this.chunkArray(items, this.chunkSize);
    const htmlChunks = [];
    
    for (const chunk of chunks) {
      const chunkHtml = renderToStaticMarkup(
        <div>
          {chunk.map(item => (
            <ItemComponent key={item.id} {...item} />
          ))}
        </div>
      );
      
      htmlChunks.push(chunkHtml);
      
      // 让出控制权，避免阻塞事件循环
      await new Promise(resolve => setImmediate(resolve));
    }
    
    return htmlChunks.join('');
  }
  
  chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}

// 性能监控
function measureRenderPerformance(component, name) {
  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();
  
  const html = renderToStaticMarkup(component);
  
  const endTime = process.hrtime.bigint();
  const endMemory = process.memoryUsage();
  
  const renderTime = Number(endTime - startTime) / 1000000; // 转换为毫秒
  const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;
  
  console.log(\`\${name} 渲染性能:\`);
  console.log(\`  时间: \${renderTime.toFixed(2)}ms\`);
  console.log(\`  内存: \${(memoryDelta / 1024 / 1024).toFixed(2)}MB\`);
  console.log(\`  HTML大小: \${(html.length / 1024).toFixed(2)}KB\`);
  
  return html;
}

// 使用示例
const html = measureRenderPerformance(
  <OptimizedComponent items={largeItemList} />,
  'LargeList'
);

// Worker线程并行处理
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');

if (isMainThread) {
  // 主线程：分发任务
  async function parallelRender(pages) {
    const workers = [];
    const results = [];
    
    for (let i = 0; i < pages.length; i++) {
      const worker = new Worker(__filename, {
        workerData: { page: pages[i] }
      });
      
      workers.push(
        new Promise((resolve) => {
          worker.on('message', resolve);
          worker.on('error', resolve);
        })
      );
    }
    
    return await Promise.all(workers);
  }
} else {
  // Worker线程：执行渲染
  const { page } = workerData;
  const html = renderToStaticMarkup(page.component);
  parentPort.postMessage({ id: page.id, html });
}`,
      tags: ['性能优化', '内存管理', '并行处理'],
      relatedQuestions: ['如何监控renderToStaticMarkup的性能指标？', '什么时候应该考虑使用流式渲染替代静态渲染？']
    }
];

export default commonQuestions;
