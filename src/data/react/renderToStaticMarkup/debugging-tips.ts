import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'renderToStaticMarkup虽然相对简单，但在实际使用中仍可能遇到各种问题。了解这些常见问题和解决方案，可以帮助开发者快速定位和解决问题。',
        sections: [
          {
            title: '渲染输出问题',
            description: '生成的HTML不符合预期的常见问题',
            items: [
              {
                title: 'HTML结构不正确',
                description: '生成的HTML标签不匹配或结构混乱',
                solution: '检查JSX语法，确保所有标签正确闭合，避免条件渲染中的语法错误',
                prevention: '使用TypeScript和ESLint进行静态检查，编写单元测试验证输出',
                code: `// ❌ 常见错误：条件渲染导致的结构问题
function ProblematicComponent({ showContent, items }) {
  return (
    <div>
      <h1>标题</h1>
      {showContent && 
        <div>
          <p>内容</p>
        // 缺少闭合标签
      }
      {items.map(item => 
        <div key={item.id}>
          <span>{item.name}
          // 缺少闭合标签
        </div>
      )}
    </div>
  );
}

// ✅ 正确的写法
function CorrectComponent({ showContent, items }) {
  return (
    <div>
      <h1>标题</h1>
      {showContent && (
        <div>
          <p>内容</p>
        </div>
      )}
      {items.map(item => (
        <div key={item.id}>
          <span>{item.name}</span>
        </div>
      ))}
    </div>
  );
}

// 调试工具：HTML结构验证
function validateHtmlStructure(html) {
  const parser = new DOMParser();
  try {
    const doc = parser.parseFromString(html, 'text/html');
    const errors = doc.querySelectorAll('parsererror');
    
    if (errors.length > 0) {
      console.error('HTML解析错误:', errors[0].textContent);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('HTML验证失败:', error);
    return false;
  }
}`
              },
              {
                title: '样式丢失或不生效',
                description: '生成的HTML中样式没有正确应用',
                solution: '确保使用内联样式或正确的className，检查CSS文件是否正确引用',
                prevention: '在静态渲染中优先使用内联样式，建立样式测试流程',
                code: `// 样式调试工具
function debugStyles(component, props) {
  const html = renderToStaticMarkup(component(props));
  
  // 检查内联样式
  const inlineStyleMatches = html.match(/style="[^"]*"/g) || [];
  console.log('内联样式数量:', inlineStyleMatches.length);
  
  // 检查CSS类名
  const classMatches = html.match(/class="[^"]*"/g) || [];
  console.log('CSS类名:', classMatches);
  
  // 检查可能的样式问题
  if (html.includes('className=')) {
    console.warn('发现未转换的className属性');
  }
  
  return html;
}

// 样式兼容性检查
function checkStyleCompatibility(html) {
  const issues = [];
  
  // 检查CSS3特性
  if (html.includes('flexbox') || html.includes('grid')) {
    issues.push('使用了可能不兼容的CSS3特性');
  }
  
  // 检查vendor前缀
  if (html.includes('-webkit-') || html.includes('-moz-')) {
    issues.push('包含vendor前缀，可能影响兼容性');
  }
  
  return issues;
}`
              }
            ]
          },
          {
            title: '性能问题',
            description: '渲染性能相关的问题诊断',
            items: [
              {
                title: '渲染速度慢',
                description: '大型组件或大量数据导致渲染时间过长',
                solution: '分析组件复杂度，优化数据结构，使用分块渲染',
                prevention: '建立性能基准测试，监控渲染时间变化',
                code: `// 性能分析工具
class RenderProfiler {
  constructor() {
    this.profiles = new Map();
  }
  
  profile(name, renderFn) {
    const start = process.hrtime.bigint();
    const startMemory = process.memoryUsage();
    
    const result = renderFn();
    
    const end = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    
    const profile = {
      name,
      duration: Number(end - start) / 1000000, // 转换为毫秒
      memoryDelta: endMemory.heapUsed - startMemory.heapUsed,
      outputSize: result.length,
      timestamp: Date.now()
    };
    
    this.profiles.set(name, profile);
    
    console.log(\`\${name} 性能分析:\`);
    console.log(\`  渲染时间: \${profile.duration.toFixed(2)}ms\`);
    console.log(\`  内存变化: \${(profile.memoryDelta / 1024 / 1024).toFixed(2)}MB\`);
    console.log(\`  输出大小: \${(profile.outputSize / 1024).toFixed(2)}KB\`);
    
    return result;
  }
  
  compare(name1, name2) {
    const profile1 = this.profiles.get(name1);
    const profile2 = this.profiles.get(name2);
    
    if (!profile1 || !profile2) {
      console.error('找不到对比的性能数据');
      return;
    }
    
    console.log(\`性能对比 \${name1} vs \${name2}:\`);
    console.log(\`  时间差异: \${((profile2.duration - profile1.duration) / profile1.duration * 100).toFixed(1)}%\`);
    console.log(\`  内存差异: \${((profile2.memoryDelta - profile1.memoryDelta) / profile1.memoryDelta * 100).toFixed(1)}%\`);
  }
}

// 使用示例
const profiler = new RenderProfiler();

const html1 = profiler.profile('原始版本', () => 
  renderToStaticMarkup(<OriginalComponent data={largeData} />)
);

const html2 = profiler.profile('优化版本', () => 
  renderToStaticMarkup(<OptimizedComponent data={largeData} />)
);

profiler.compare('原始版本', '优化版本');`
              }
            ]
          }
        ]
      }
    },
    
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '有效的开发工具可以大大提升renderToStaticMarkup的开发和调试效率。本节介绍一些实用的工具和技巧。',
        sections: [
          {
            title: 'HTML输出分析工具',
            description: '分析和验证生成的HTML输出',
            items: [
              {
                title: 'HTML美化和分析器',
                description: '格式化HTML输出，便于阅读和分析',
                solution: '使用HTML美化工具和结构分析器',
                prevention: '集成到开发流程中，自动化HTML质量检查',
                code: `// HTML分析工具
const prettier = require('prettier');
const { JSDOM } = require('jsdom');

class HtmlAnalyzer {
  constructor() {
    this.stats = {
      totalElements: 0,
      maxDepth: 0,
      inlineStyles: 0,
      externalClasses: 0
    };
  }
  
  analyze(html) {
    // 美化HTML
    const formatted = prettier.format(html, { 
      parser: 'html',
      printWidth: 80,
      tabWidth: 2
    });
    
    // 解析DOM结构
    const dom = new JSDOM(html);
    const document = dom.window.document;
    
    // 统计信息
    this.stats.totalElements = document.querySelectorAll('*').length;
    this.stats.maxDepth = this.calculateMaxDepth(document.body);
    this.stats.inlineStyles = document.querySelectorAll('[style]').length;
    this.stats.externalClasses = document.querySelectorAll('[class]').length;
    
    return {
      formatted,
      stats: this.stats,
      issues: this.findIssues(document)
    };
  }
  
  calculateMaxDepth(element, currentDepth = 0) {
    let maxDepth = currentDepth;
    
    for (const child of element.children) {
      const childDepth = this.calculateMaxDepth(child, currentDepth + 1);
      maxDepth = Math.max(maxDepth, childDepth);
    }
    
    return maxDepth;
  }
  
  findIssues(document) {
    const issues = [];
    
    // 检查空元素
    const emptyElements = document.querySelectorAll('div:empty, span:empty, p:empty');
    if (emptyElements.length > 0) {
      issues.push(\`发现 \${emptyElements.length} 个空元素\`);
    }
    
    // 检查过深的嵌套
    if (this.stats.maxDepth > 10) {
      issues.push(\`DOM嵌套过深: \${this.stats.maxDepth} 层\`);
    }
    
    // 检查缺少alt属性的图片
    const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
    if (imagesWithoutAlt.length > 0) {
      issues.push(\`\${imagesWithoutAlt.length} 个图片缺少alt属性\`);
    }
    
    return issues;
  }
}

// 使用示例
const analyzer = new HtmlAnalyzer();

function debugRender(component, props, name = 'Component') {
  const html = renderToStaticMarkup(component(props));
  const analysis = analyzer.analyze(html);
  
  console.log(\`\${name} 分析结果:\`);
  console.log('统计信息:', analysis.stats);
  
  if (analysis.issues.length > 0) {
    console.warn('发现问题:', analysis.issues);
  }
  
  // 保存格式化的HTML到文件
  const fs = require('fs');
  fs.writeFileSync(\`debug-\${name.toLowerCase()}.html\`, analysis.formatted);
  
  return html;
}`
              }
            ]
          },
          {
            title: '组件测试工具',
            description: '测试组件在静态渲染中的表现',
            items: [
              {
                title: '静态渲染测试套件',
                description: '专门用于测试renderToStaticMarkup输出的测试工具',
                solution: '建立完整的测试套件，覆盖各种渲染场景',
                prevention: '集成到CI/CD流程，确保渲染质量',
                code: `// 静态渲染测试工具
class StaticRenderTester {
  constructor() {
    this.tests = [];
    this.results = [];
  }
  
  addTest(name, component, props, expectations) {
    this.tests.push({
      name,
      component,
      props,
      expectations
    });
  }
  
  runTests() {
    this.results = [];
    
    for (const test of this.tests) {
      const result = this.runSingleTest(test);
      this.results.push(result);
    }
    
    return this.generateReport();
  }
  
  runSingleTest(test) {
    const startTime = Date.now();
    
    try {
      const html = renderToStaticMarkup(
        React.createElement(test.component, test.props)
      );
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // 验证期望
      const validationResults = this.validateExpectations(html, test.expectations);
      
      return {
        name: test.name,
        success: validationResults.every(r => r.passed),
        duration,
        html,
        validations: validationResults
      };
      
    } catch (error) {
      return {
        name: test.name,
        success: false,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }
  
  validateExpectations(html, expectations) {
    const results = [];
    
    for (const expectation of expectations) {
      let passed = false;
      let message = '';
      
      switch (expectation.type) {
        case 'contains':
          passed = html.includes(expectation.value);
          message = passed ? 
            \`包含期望的内容: \${expectation.value}\` :
            \`缺少期望的内容: \${expectation.value}\`;
          break;
          
        case 'not-contains':
          passed = !html.includes(expectation.value);
          message = passed ?
            \`正确排除了内容: \${expectation.value}\` :
            \`意外包含了内容: \${expectation.value}\`;
          break;
          
        case 'matches':
          const regex = new RegExp(expectation.pattern);
          passed = regex.test(html);
          message = passed ?
            \`匹配正则表达式: \${expectation.pattern}\` :
            \`不匹配正则表达式: \${expectation.pattern}\`;
          break;
          
        case 'element-count':
          const dom = new JSDOM(html);
          const elements = dom.window.document.querySelectorAll(expectation.selector);
          passed = elements.length === expectation.count;
          message = passed ?
            \`元素数量正确: \${expectation.selector} = \${expectation.count}\` :
            \`元素数量错误: \${expectation.selector} = \${elements.length}, 期望 \${expectation.count}\`;
          break;
      }
      
      results.push({ type: expectation.type, passed, message });
    }
    
    return results;
  }
  
  generateReport() {
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log('\\n=== 静态渲染测试报告 ===');
    console.log(\`总测试数: \${totalTests}\`);
    console.log(\`通过: \${passedTests}\`);
    console.log(\`失败: \${failedTests}\`);
    console.log(\`成功率: \${(passedTests / totalTests * 100).toFixed(1)}%\`);
    
    // 详细结果
    this.results.forEach(result => {
      if (result.success) {
        console.log(\`✅ \${result.name} (\${result.duration}ms)\`);
      } else {
        console.log(\`❌ \${result.name} (\${result.duration}ms)\`);
        if (result.error) {
          console.log(\`   错误: \${result.error}\`);
        }
        if (result.validations) {
          result.validations.forEach(v => {
            if (!v.passed) {
              console.log(\`   验证失败: \${v.message}\`);
            }
          });
        }
      }
    });
    
    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: passedTests / totalTests
    };
  }
}

// 使用示例
const tester = new StaticRenderTester();

// 添加测试用例
tester.addTest('BlogPost基本渲染', BlogPost, {
  post: {
    title: '测试文章',
    content: '<p>测试内容</p>',
    author: '作者'
  }
}, [
  { type: 'contains', value: '测试文章' },
  { type: 'contains', value: '测试内容' },
  { type: 'element-count', selector: 'h1', count: 1 },
  { type: 'not-contains', value: 'onClick' }
]);

// 运行测试
const report = tester.runTests();`
              }
            ]
          }
        ]
      }
    },
    
    {
      key: 'troubleshooting',
      title: '🔍 问题排查',
      content: {
        introduction: '当遇到复杂问题时，需要系统化的排查方法来快速定位问题根源。',
        sections: [
          {
            title: '系统化排查流程',
            description: '按照优先级进行问题排查',
            items: [
              {
                title: '渲染错误排查',
                description: '当renderToStaticMarkup抛出错误时的排查步骤',
                steps: [
                  '检查组件语法和JSX结构',
                  '验证传入的props数据类型',
                  '确认所有依赖的组件都已正确导入',
                  '检查是否使用了不支持的React特性'
                ],
                code: `// 错误排查工具
function safeRenderToStaticMarkup(component, props, options = {}) {
  const { debug = false, validateProps = true } = options;
  
  try {
    // 1. Props验证
    if (validateProps && component.propTypes) {
      const PropTypes = require('prop-types');
      PropTypes.checkPropTypes(
        component.propTypes,
        props,
        'prop',
        component.name || 'Component'
      );
    }
    
    // 2. 组件渲染
    const element = React.createElement(component, props);
    
    if (debug) {
      console.log('渲染组件:', component.name || 'Anonymous');
      console.log('Props:', JSON.stringify(props, null, 2));
    }
    
    const html = renderToStaticMarkup(element);
    
    if (debug) {
      console.log('渲染成功，HTML长度:', html.length);
    }
    
    return { success: true, html };
    
  } catch (error) {
    const errorInfo = {
      success: false,
      error: error.message,
      stack: error.stack,
      component: component.name || 'Anonymous',
      props: props
    };
    
    if (debug) {
      console.error('渲染失败:', errorInfo);
    }
    
    return errorInfo;
  }
}

// 批量错误检测
function batchRenderCheck(components) {
  const results = [];
  
  for (const { component, props, name } of components) {
    const result = safeRenderToStaticMarkup(component, props, { debug: true });
    results.push({
      name: name || component.name,
      ...result
    });
  }
  
  // 生成错误报告
  const errors = results.filter(r => !r.success);
  if (errors.length > 0) {
    console.log('\\n发现渲染错误:');
    errors.forEach(error => {
      console.log(\`❌ \${error.name}: \${error.error}\`);
    });
  }
  
  return results;
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;
