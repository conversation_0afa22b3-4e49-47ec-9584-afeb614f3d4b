import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "自定义Hook是以'use'开头的JavaScript函数，用于在React函数组件之间复用状态逻辑。它可以调用其他Hook，并返回需要的状态和函数，是React函数组件复用逻辑的主要方式。",
  
  introduction: `自定义Hook是React 16.8引入Hooks后出现的强大特性，它解决了组件间逻辑复用的难题。通过将状态逻辑抽象到可复用的函数中，自定义Hook让开发者能够：

**核心价值**：
- 🔄 **逻辑复用**：一次编写，多处使用，减少代码重复
- 🎯 **关注点分离**：将业务逻辑从UI渲染中分离
- 🧪 **易于测试**：逻辑独立，便于单元测试
- 🏗️ **组合式架构**：多个Hook可以组合使用

自定义Hook本质上是函数式编程在React中的体现，它让组件变得更加简洁，逻辑变得更加可维护。`,

  syntax: `// 基本语法模式
function useCustomHook(parameter1, parameter2) {
  // 1. 可以调用其他Hook
  const [state, setState] = useState(initialValue);
  
  // 2. 可以包含业务逻辑
  const handleSomething = useCallback(() => {
    // 业务逻辑处理
  }, [dependencies]);
  
  // 3. 可以包含副作用
  useEffect(() => {
    // 副作用逻辑
  }, [dependencies]);
  
  // 4. 返回需要的状态和函数
  return {
    state,
    handleSomething,
    // 其他需要暴露的接口
  };
}

// 在组件中使用
function MyComponent() {
  const { state, handleSomething } = useCustomHook(param1, param2);
  
  return (
    <div>
      <p>{state}</p>
      <button onClick={handleSomething}>Action</button>
    </div>
  );
}`,

  quickExample: `// 简单的计数器Hook示例
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);
  
  const decrement = useCallback(() => {
    setCount(prev => prev - 1);
  }, []);
  
  const reset = useCallback(() => {
    setCount(initialValue);
  }, [initialValue]);
  
  return { count, increment, decrement, reset };
}

// 使用示例
function Counter() {
  const { count, increment, decrement, reset } = useCounter(0);
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={increment}>+</button>
      <button onClick={decrement}>-</button>
      <button onClick={reset}>Reset</button>
    </div>
  );
}`,

  scenarioDiagram: null,
  
  parameters: [
    {
      name: "dependencies",
      type: "any[]",
      required: false,
      description: "Hook依赖的参数，当这些参数变化时Hook会重新执行相关逻辑"
    },
    {
      name: "options",
      type: "object",
      required: false,
      description: "配置选项对象，包含各种可选的配置参数"
    },
    {
      name: "initialValue",
      type: "any",
      required: false,
      description: "初始值参数，用于初始化Hook的内部状态"
    }
  ],
  
  returnValue: {
    type: "object",
    description: "返回包含状态和方法的对象，具体结构根据Hook功能而定。通常包括：状态值、更新函数、配置方法、工具函数等。"
  },
  
  keyFeatures: [
    {
      feature: "状态复用",
      description: "将组件间的共同状态逻辑抽取到Hook中，实现真正的逻辑复用"
    },
    {
      feature: "Hook组合",
      description: "可以在自定义Hook中调用其他Hook，形成强大的组合能力"
    },
    {
      feature: "生命周期管理",
      description: "通过useEffect等Hook管理副作用的完整生命周期"
    },
    {
      feature: "性能优化",
      description: "内置useCallback、useMemo等优化策略，提升应用性能"
    },
    {
      feature: "类型安全",
      description: "完整的TypeScript支持，提供类型推断和编译时检查"
    },
    {
      feature: "测试友好",
      description: "逻辑独立于UI，便于进行单元测试和集成测试"
    }
  ],
  
  limitations: [
    "必须遵循Hook规则：只能在函数组件顶层或其他Hook中调用",
    "不能在条件语句、循环或嵌套函数中调用Hook",
    "Hook的调用顺序在每次渲染时必须保持一致",
    "复杂的Hook可能会增加调试难度",
    "过度抽象可能导致代码可读性下降"
  ],
  
  bestPractices: [
    "使用'use'前缀命名，让React工具能够识别和检查",
    "保持Hook的单一职责，避免一个Hook承担过多功能",
    "合理使用useCallback和useMemo进行性能优化",
    "提供清晰的TypeScript类型定义",
    "编写完整的单元测试",
    "添加适当的错误处理和边界情况处理",
    "设计直观的API接口，隐藏实现复杂性"
  ],
  
  warnings: [
    "不要在条件语句中调用Hook，会破坏Hook链的稳定性",
    "避免在Hook中直接操作DOM，应该通过useRef或useEffect",
    "注意Hook的依赖数组设置，避免无限循环或内存泄漏",
    "复杂Hook要考虑性能影响，避免不必要的重渲染"
  ]
};

export default basicInfo;