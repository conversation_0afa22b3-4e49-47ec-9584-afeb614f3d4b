import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useCustomHookData: ApiItem = {
  id: 'useCustomHook',
  title: 'useCustomHook',
  description: '自定义Hook是以"use"开头的JavaScript函数，用于在React函数组件之间复用状态逻辑。它可以调用其他Hook，并返回需要的状态和函数，是React函数组件复用逻辑的主要方式。',
  category: 'React Hooks',
  difficulty: 'medium',
  
  syntax: `// 基本语法模式
function useCustomHook(parameter1, parameter2) {
  // 1. 可以调用其他Hook
  const [state, setState] = useState(initialValue);
  
  // 2. 可以包含业务逻辑
  const handleSomething = useCallback(() => {
    // 业务逻辑处理
  }, [dependencies]);
  
  // 3. 返回需要的状态和函数
  return { state, handleSomething };
}`,
  
  example: `// 简单的计数器Hook示例
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);
  
  const reset = useCallback(() => {
    setCount(initialValue);
  }, [initialValue]);
  
  return { count, increment, reset };
}

// 使用示例
function Counter() {
  const { count, increment, reset } = useCounter(0);
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={increment}>+</button>
      <button onClick={reset}>Reset</button>
    </div>
  );
}`,
  
  notes: '必须遵循Hook规则：只能在函数组件顶层或其他Hook中调用，不能在条件语句、循环或嵌套函数中调用Hook',
  
  version: 'React 16.8.0+',
  tags: ["React","Hook","逻辑复用","状态管理"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useCustomHookData;