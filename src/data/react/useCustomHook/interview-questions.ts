import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: '什么是自定义Hook？它解决了什么问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: '自定义Hook是以"use"开头的函数，用于封装和复用组件间的状态逻辑，解决了组件逻辑复用的问题。',
      detailed: `**定义**：自定义Hook是React中用于封装和复用状态逻辑的函数，必须以"use"开头，内部可以调用其他Hook。

**解决的问题**：
1. **逻辑复用难题**：解决了类组件时代render props和高阶组件的复杂性
2. **状态逻辑封装**：将相关的状态和副作用逻辑组织在一起
3. **关注点分离**：让组件专注于UI渲染，逻辑层面交给自定义Hook
4. **代码维护性**：提高代码的可读性、可测试性和可维护性

**核心优势**：
- **简单易懂**：相比HOC和render props，语法更直观
- **完全可复用**：一次编写，多处使用
- **类型安全**：完整的TypeScript支持
- **组合性强**：Hook之间可以相互组合使用

**应用场景**：
- API数据获取逻辑
- 表单状态管理
- 本地存储同步
- 事件监听管理
- 复杂业务逻辑封装`,
      code: `// 自定义Hook示例：计数器逻辑封装
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);

  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);

  const decrement = useCallback(() => {
    setCount(prev => prev - 1);
  }, []);

  const reset = useCallback(() => {
    setCount(initialValue);
  }, [initialValue]);

  return { count, increment, decrement, reset };
}

// 在组件中使用
function CounterComponent() {
  const { count, increment, decrement, reset } = useCounter(10);

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={increment}>+</button>
      <button onClick={decrement}>-</button>
      <button onClick={reset}>Reset</button>
    </div>
  );
}`
    },
    tags: ['基础概念', '逻辑复用', 'Hook']
  },

  {
    id: 2,
    question: '自定义Hook的命名规则和使用规则是什么？为什么这样设计？',
    difficulty: 'easy',
    frequency: 'high',
    category: '使用规范',
    answer: {
      brief: '自定义Hook必须以"use"开头命名，遵循Hook规则：只在顶层调用，不在条件/循环中调用。这样设计是为了让React能够正确追踪Hook状态。',
      detailed: `**命名规则**：
1. **必须以"use"开头**：如useCounter、useApiData、useLocalStorage
2. **使用驼峰命名**：遵循JavaScript函数命名约定
3. **语义化命名**：名称应清楚表达Hook的功能

**使用规则（Hook Rules）**：
1. **只在React函数组件或自定义Hook中调用**
2. **只在函数顶层调用**：不在循环、条件或嵌套函数中调用
3. **保持调用顺序一致**：每次渲染时Hook调用顺序必须相同

**设计原理**：
- **"use"前缀**：让React DevTools和ESLint能够识别并检查Hook规则
- **顶层调用**：确保Hook链表结构的稳定性，React依靠调用顺序来管理Hook状态
- **顺序一致性**：React使用索引来匹配Hook状态，顺序改变会导致状态混乱

**ESLint规则**：
React团队提供了eslint-plugin-react-hooks来自动检查这些规则：
- rules-of-hooks：检查Hook调用位置
- exhaustive-deps：检查useEffect依赖数组`,
      code: `// ✅ 正确的自定义Hook
function useApiData(url) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    // 副作用逻辑
  }, [url]);
  
  return { data, loading };
}

// ❌ 错误：不以use开头
function apiData(url) {
  const [data, setData] = useState(null);
  return { data };
}

// ❌ 错误：条件调用Hook
function useBadExample(condition) {
  const [state, setState] = useState(0);
  
  if (condition) {
    const [other, setOther] = useState(0); // 违反Hook规则
  }
  
  return { state, setState };
}

// ✅ 正确：条件逻辑在Hook内部处理
function useGoodExample(condition) {
  const [state, setState] = useState(0);
  const [other, setOther] = useState(0);
  
  useEffect(() => {
    if (condition) {
      // 条件逻辑
    }
  }, [condition]);
  
  return { state, setState, other, setOther };
}`
    },
    tags: ['命名规则', 'Hook规则', 'ESLint']
  },

  {
    id: 3,
    question: '如何设计一个高质量的自定义Hook？有哪些最佳实践？',
    difficulty: 'medium',
    frequency: 'high',
    category: '设计模式',
    answer: {
      brief: '设计高质量自定义Hook需要遵循单一职责、类型安全、性能优化、错误处理四个原则，并提供清晰的API接口。',
      detailed: `**设计原则**：

**1. 单一职责原则**
- 每个Hook只解决一个特定问题
- 避免功能过于复杂的"万能Hook"
- 通过组合多个小Hook来实现复杂功能

**2. API设计最佳实践**
- 返回对象而非数组，提供清晰的属性名
- 使用TypeScript提供完整的类型定义
- 提供合理的默认值和参数校验
- 保持API的向后兼容性

**3. 性能优化策略**
- 使用useCallback包装函数，避免不必要的重渲染
- 使用useMemo缓存复杂计算结果
- 合理设计依赖数组，避免无限循环
- 考虑Hook的执行频率和开销

**4. 错误处理和边界情况**
- 提供清晰的错误信息和恢复机制
- 处理异步操作的各种状态
- 考虑网络错误、权限问题等边界情况
- 提供loading和error状态

**5. 可测试性设计**
- 避免直接依赖全局变量
- 提供依赖注入能力
- 设计清晰的输入输出接口
- 支持mock和测试环境`,
      code: `// 高质量自定义Hook示例
interface UseApiDataOptions {
  enabled?: boolean;
  retryCount?: number;
  cacheTime?: number;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}

interface UseApiDataReturn<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  cancel: () => void;
}

function useApiData<T = any>(
  url: string | null,
  options: UseApiDataOptions = {}
): UseApiDataReturn<T> {
  const {
    enabled = true,
    retryCount = 3,
    cacheTime = 5 * 60 * 1000,
    onSuccess,
    onError
  } = options;

  const [state, setState] = useState({
    data: null as T | null,
    loading: false,
    error: null as Error | null
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const cacheRef = useRef(new Map());

  // 获取数据的主函数
  const fetchData = useCallback(async (retries = 0) => {
    if (!url || !enabled) return;

    // 检查缓存
    const cached = cacheRef.current.get(url);
    if (cached && Date.now() - cached.timestamp < cacheTime) {
      setState(prev => ({ ...prev, data: cached.data, loading: false }));
      onSuccess?.(cached.data);
      return;
    }

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch(url, {
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error(\`HTTP error! status: \${response.status}\`);
      }

      const data = await response.json();
      
      // 缓存数据
      cacheRef.current.set(url, {
        data,
        timestamp: Date.now()
      });

      setState({ data, loading: false, error: null });
      onSuccess?.(data);
    } catch (error: any) {
      if (error.name === 'AbortError') return;

      const apiError = new Error(error.message || 'API request failed');
      setState(prev => ({ ...prev, loading: false, error: apiError }));
      onError?.(apiError);

      // 自动重试
      if (retries < retryCount) {
        setTimeout(() => {
          fetchData(retries + 1);
        }, Math.pow(2, retries) * 1000);
      }
    }
  }, [url, enabled, cacheTime, retryCount, onSuccess, onError]);

  // 手动刷新
  const refetch = useCallback(async () => {
    if (url) {
      cacheRef.current.delete(url);
      await fetchData();
    }
  }, [fetchData, url]);

  // 取消请求
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setState(prev => ({ ...prev, loading: false }));
  }, []);

  // 自动获取数据
  useEffect(() => {
    fetchData();

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchData]);

  return {
    ...state,
    refetch,
    cancel
  };
}

// 使用示例
function UserProfile({ userId }: { userId: string }) {
  const { data: user, loading, error, refetch } = useApiData<User>(
    \`/api/users/\${userId}\`,
    {
      onSuccess: (user) => console.log('User loaded:', user),
      onError: (error) => console.error('Failed to load user:', error)
    }
  );

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div>
      <h1>{user.name}</h1>
      <button onClick={refetch}>Refresh</button>
    </div>
  );
}`
    },
    tags: ['设计模式', '最佳实践', 'TypeScript', '性能优化']
  },

  {
    id: 4,
    question: '自定义Hook与高阶组件(HOC)、Render Props相比有什么优势？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '模式对比',
    answer: {
      brief: '自定义Hook相比HOC和Render Props具有语法简洁、类型友好、组合灵活、调试方便等优势，是React推荐的逻辑复用方案。',
      detailed: `**三种模式对比**：

**1. 语法复杂度**
- **HOC**：需要理解高阶函数概念，嵌套结构复杂
- **Render Props**：容易形成"回调地狱"，JSX嵌套深
- **自定义Hook**：语法直观，就像使用普通函数

**2. 类型安全性**
- **HOC**：TypeScript类型推断困难，Props类型传递复杂
- **Render Props**：泛型类型定义复杂，容易出错
- **自定义Hook**：完美的TypeScript支持，类型推断准确

**3. 组合能力**
- **HOC**：多个HOC组合时容易出现Props冲突
- **Render Props**：组合多个Render Props会形成深层嵌套
- **自定义Hook**：Hook之间可以自由组合，没有嵌套问题

**4. 调试体验**
- **HOC**：调试时组件树复杂，难以定位问题
- **Render Props**：函数组件树结构不清晰
- **自定义Hook**：React DevTools原生支持，调试清晰

**5. 性能影响**
- **HOC**：每个HOC都会创建新的组件实例
- **Render Props**：可能导致不必要的重渲染
- **自定义Hook**：没有额外的组件包装，性能最优

**6. 学习成本**
- **HOC**：需要理解高阶函数、柯里化等概念
- **Render Props**：需要理解函数作为children的模式
- **自定义Hook**：概念简单，学习成本最低`,
      code: `// 1. HOC方式实现计数器逻辑复用
function withCounter(WrappedComponent) {
  return function CounterHOC(props) {
    const [count, setCount] = useState(0);
    
    return (
      <WrappedComponent
        {...props}
        count={count}
        increment={() => setCount(c => c + 1)}
      />
    );
  };
}

const CounterWithHOC = withCounter(function Counter({ count, increment }) {
  return <button onClick={increment}>Count: {count}</button>;
});

// 2. Render Props方式
function Counter({ children }) {
  const [count, setCount] = useState(0);
  
  return children({
    count,
    increment: () => setCount(c => c + 1)
  });
}

function CounterWithRenderProps() {
  return (
    <Counter>
      {({ count, increment }) => (
        <button onClick={increment}>Count: {count}</button>
      )}
    </Counter>
  );
}

// 3. 自定义Hook方式
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => {
    setCount(c => c + 1);
  }, []);
  
  return { count, increment };
}

function CounterWithHook() {
  const { count, increment } = useCounter();
  
  return <button onClick={increment}>Count: {count}</button>;
}

// 复杂组合示例
// HOC嵌套地狱
const EnhancedComponent = withAuth(withTheme(withCounter(MyComponent)));

// Render Props嵌套地狱
<Auth>
  {authProps => (
    <Theme>
      {themeProps => (
        <Counter>
          {counterProps => (
            <MyComponent {...authProps} {...themeProps} {...counterProps} />
          )}
        </Counter>
      )}
    </Theme>
  )}
</Auth>

// 自定义Hook简洁组合
function MyComponent() {
  const auth = useAuth();
  const theme = useTheme();
  const counter = useCounter();
  
  // 清晰的逻辑，没有嵌套
  return <div>...</div>;
}`
    },
    tags: ['模式对比', 'HOC', 'Render Props', '优势分析']
  },

  {
    id: 5,
    question: '如何测试自定义Hook？有哪些测试策略和工具？',
    difficulty: 'hard',
    frequency: 'low',
    category: '测试策略',
    answer: {
      brief: '测试自定义Hook可以使用@testing-library/react-hooks库，采用单元测试、集成测试和端到端测试的分层策略。',
      detailed: `**测试策略分层**：

**1. 单元测试（推荐）**
- 使用@testing-library/react-hooks的renderHook
- 测试Hook的输入输出行为
- 测试状态变化和副作用
- Mock外部依赖

**2. 集成测试**
- 在真实组件中测试Hook的使用
- 测试Hook与其他Hook的组合
- 测试Hook在组件生命周期中的行为

**3. 端到端测试**
- 测试包含Hook的完整用户流程
- 验证Hook在真实浏览器环境中的表现

**测试工具和库**：
- **@testing-library/react-hooks**：专门用于测试Hook
- **@testing-library/react**：测试使用Hook的组件
- **Jest**：测试运行器和断言库
- **MSW**：Mock API请求
- **React Testing Library**：用户行为测试

**测试最佳实践**：
1. **测试行为而非实现**：关注Hook的输出和副作用
2. **隔离测试**：每个测试应该独立，不依赖其他测试
3. **Mock外部依赖**：API、localStorage、定时器等
4. **测试边界情况**：错误状态、空值、网络失败等
5. **性能测试**：测试Hook的渲染次数和性能影响`,
      code: `// 待测试的自定义Hook
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);
  
  const decrement = useCallback(() => {
    setCount(prev => prev - 1);
  }, []);
  
  const reset = useCallback(() => {
    setCount(initialValue);
  }, [initialValue]);
  
  return { count, increment, decrement, reset };
}

// 单元测试示例
import { renderHook, act } from '@testing-library/react-hooks';

describe('useCounter', () => {
  it('should initialize with default value', () => {
    const { result } = renderHook(() => useCounter());
    
    expect(result.current.count).toBe(0);
  });
  
  it('should initialize with provided value', () => {
    const { result } = renderHook(() => useCounter(10));
    
    expect(result.current.count).toBe(10);
  });
  
  it('should increment count', () => {
    const { result } = renderHook(() => useCounter());
    
    act(() => {
      result.current.increment();
    });
    
    expect(result.current.count).toBe(1);
  });
  
  it('should reset to initial value', () => {
    const { result } = renderHook(() => useCounter(5));
    
    act(() => {
      result.current.increment();
      result.current.increment();
    });
    
    expect(result.current.count).toBe(7);
    
    act(() => {
      result.current.reset();
    });
    
    expect(result.current.count).toBe(5);
  });
});

// 异步Hook测试示例
function useApiData(url) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    if (!url) return;
    
    setLoading(true);
    fetch(url)
      .then(res => res.json())
      .then(data => {
        setData(data);
        setLoading(false);
      })
      .catch(err => {
        setError(err);
        setLoading(false);
      });
  }, [url]);
  
  return { data, loading, error };
}

// 异步Hook测试
import { waitFor } from '@testing-library/react';

// Mock fetch
global.fetch = jest.fn();

describe('useApiData', () => {
  beforeEach(() => {
    fetch.mockClear();
  });
  
  it('should fetch data successfully', async () => {
    const mockData = { id: 1, name: 'Test' };
    fetch.mockResolvedValueOnce({
      json: async () => mockData
    });
    
    const { result } = renderHook(() => useApiData('/api/test'));
    
    expect(result.current.loading).toBe(true);
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    expect(result.current.data).toEqual(mockData);
    expect(result.current.error).toBeNull();
  });
  
  it('should handle fetch errors', async () => {
    const mockError = new Error('API Error');
    fetch.mockRejectedValueOnce(mockError);
    
    const { result } = renderHook(() => useApiData('/api/test'));
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    expect(result.current.data).toBeNull();
    expect(result.current.error).toEqual(mockError);
  });
});

// 集成测试示例
function Counter() {
  const { count, increment } = useCounter(0);
  
  return (
    <div>
      <span data-testid="count">{count}</span>
      <button data-testid="increment" onClick={increment}>
        Increment
      </button>
    </div>
  );
}

import { render, fireEvent } from '@testing-library/react';

describe('Counter Component', () => {
  it('should work with useCounter hook', () => {
    const { getByTestId } = render(<Counter />);
    
    expect(getByTestId('count')).toHaveTextContent('0');
    
    fireEvent.click(getByTestId('increment'));
    
    expect(getByTestId('count')).toHaveTextContent('1');
  });
});`
    },
    tags: ['测试策略', 'Testing Library', '单元测试', '集成测试']
  }
];

export default interviewQuestions;