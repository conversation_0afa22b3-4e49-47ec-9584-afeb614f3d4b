import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  introduction: `自定义Hook的调试是React开发中的重要技能。由于Hook的特殊性质和React的内部机制，传统的调试方法可能不够有效。

本指南将帮助你掌握各种调试技巧，从基础的console.log到高级的性能分析，让你能够快速定位和解决自定义Hook中的问题。

**调试的核心原则**：
- 系统性思考：从Hook规则到业务逻辑，逐层排查
- 工具组合：结合多种调试工具获得全面视图
- 渐进式调试：从简单到复杂，逐步深入
- 预防为主：通过良好的编程习惯减少bug产生`,

  troubleshooting: [
    {
      symptom: 'Hook状态不更新或更新异常',
      possibleCauses: [
        '状态更新函数使用错误，直接修改state而非返回新值',
        'useEffect依赖数组配置错误，导致状态更新时机不对',
        '在事件处理器中直接调用Hook违反Hook规则',
        '状态更新时机问题，在异步操作中状态已过期'
      ],
      solutions: [
        '确保状态更新使用函数式更新：setState(prev => newValue)',
        '检查useEffect依赖数组，确保包含所有必要依赖',
        '将Hook调用移到组件顶层，事件处理器中只调用Hook返回的函数',
        '使用useRef保存最新状态引用，或使用useCallback确保获取最新状态'
      ],
      code: `// ❌ 错误：直接修改状态
function useBadCounter() {
  const [count, setCount] = useState(0);
  
  const increment = () => {
    count++; // 错误：直接修改状态
    setCount(count); // 错误：使用过期状态
  };
  
  return { count, increment };
}

// ✅ 正确：函数式更新
function useGoodCounter() {
  const [count, setCount] = useState(0);
  
  const increment = useCallback(() => {
    setCount(prev => prev + 1); // 正确：函数式更新
  }, []);
  
  return { count, increment };
}

// 调试技巧：添加日志追踪状态变化
function useDebugCounter() {
  const [count, setCount] = useState(0);
  
  // 监控状态变化
  useEffect(() => {
    console.log('📊 Count changed:', count);
  }, [count]);
  
  const increment = useCallback(() => {
    console.log('🔄 Incrementing from:', count);
    setCount(prev => {
      const newValue = prev + 1;
      console.log('➡️ New value:', newValue);
      return newValue;
    });
  }, [count]);
  
  return { count, increment };
}`
    },
    {
      symptom: 'Hook违反规则错误："Rendered more hooks than during the previous render"',
      possibleCauses: [
        '在条件语句中调用Hook',
        '在循环中调用Hook',
        '在嵌套函数中调用Hook',
        'Hook调用顺序在不同渲染间发生变化'
      ],
      solutions: [
        '确保所有Hook调用都在组件/Hook函数的顶层',
        '将条件逻辑移到Hook内部，而不是Hook调用外部',
        '使用ESLint规则eslint-plugin-react-hooks自动检查',
        '检查组件是否有条件渲染导致Hook调用顺序变化'
      ],
      code: `// ❌ 错误：条件调用Hook
function useBadHook(condition) {
  const [count, setCount] = useState(0);
  
  if (condition) {
    const [name, setName] = useState(''); // 违反Hook规则
    return { count, name, setCount, setName };
  }
  
  return { count, setCount };
}

// ✅ 正确：Hook在顶层，条件在内部
function useGoodHook(condition) {
  const [count, setCount] = useState(0);
  const [name, setName] = useState('');
  
  // 条件逻辑在Hook内部
  const displayName = condition ? name : '';
  
  const setDisplayName = useCallback((value) => {
    if (condition) {
      setName(value);
    }
  }, [condition]);
  
  return { count, displayName, setCount, setDisplayName };
}

// 调试技巧：Hook调用追踪
function useHookCallTracker(hookName) {
  const callCountRef = useRef(0);
  callCountRef.current++;
  
  console.log(hookName + ' called ' + callCountRef.current + ' times');
  
  useEffect(() => {
    console.log(hookName + ' effect run');
    return () => {
      console.log(hookName + ' effect cleanup');
    };
  });
}`
    },
    {
      symptom: '性能问题：Hook导致组件频繁重渲染',
      possibleCauses: [
        '没有使用useCallback缓存函数，导致每次渲染都创建新函数',
        '没有使用useMemo缓存复杂计算结果',
        'useEffect依赖数组包含不必要的依赖',
        '状态结构设计不合理，一个状态变化影响多个部分'
      ],
      solutions: [
        '使用useCallback包装所有返回的函数',
        '使用useMemo缓存昂贵的计算结果',
        '精确设计依赖数组，只包含必要的依赖',
        '拆分状态，减少单个状态变化的影响范围'
      ],
      code: `// ❌ 性能问题的Hook
function useSlowHook(data) {
  const [result, setResult] = useState(null);
  
  // 每次渲染都创建新函数
  const processData = () => {
    return expensiveComputation(data);
  };
  
  // 每次渲染都执行昂贵计算
  const computed = expensiveComputation(data);
  
  useEffect(() => {
    setResult(processData());
  }, [data]); // processData没有缓存，可能导致无限循环
  
  return { result, processData, computed };
}

// ✅ 性能优化的Hook
function useFastHook(data) {
  const [result, setResult] = useState(null);
  
  // 缓存函数
  const processData = useCallback(() => {
    return expensiveComputation(data);
  }, [data]);
  
  // 缓存计算结果
  const computed = useMemo(() => {
    return expensiveComputation(data);
  }, [data]);
  
  useEffect(() => {
    setResult(processData());
  }, [processData]);
  
  return { result, processData, computed };
}

// 调试技巧：性能监控Hook
function usePerformanceMonitor(name) {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());
  
  renderCount.current++;
  const currentTime = Date.now();
  const timeSinceLastRender = currentTime - lastRenderTime.current;
  lastRenderTime.current = currentTime;
  
  console.log(name + ' - Render #' + renderCount.current + ', Time since last: ' + timeSinceLastRender + 'ms');
  
  if (timeSinceLastRender < 16) {
    console.warn(name + ' - Potential performance issue: rendering too frequently');
  }
  
  return renderCount.current;
}`
    },
    {
      symptom: '异步操作错误：内存泄漏或状态不一致',
      possibleCauses: [
        '组件卸载后异步操作仍在执行并尝试更新状态',
        '没有正确取消进行中的请求',
        '竞态条件：多个异步操作同时执行导致状态混乱',
        '异步操作中使用了过期的状态值'
      ],
      solutions: [
        '使用useRef标记组件挂载状态，避免在卸载后更新状态',
        '使用AbortController取消网络请求',
        '使用状态机或标识符管理并发请求',
        '在异步操作中使用函数式更新获取最新状态'
      ],
      code: `// ❌ 有内存泄漏风险的Hook
function useBadAsyncData(url) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    setLoading(true);
    fetch(url)
      .then(res => res.json())
      .then(data => {
        setData(data); // 可能在组件卸载后执行
        setLoading(false);
      })
      .catch(err => {
        setLoading(false);
      });
  }, [url]);
  
  return { data, loading };
}

// ✅ 安全的异步Hook
function useSafeAsyncData(url) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // 用于标记组件是否已挂载
  const isMountedRef = useRef(true);
  const abortControllerRef = useRef(null);
  
  useEffect(() => {
    if (!url) return;
    
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();
    setLoading(true);
    setError(null);
    
    fetch(url, { signal: abortControllerRef.current.signal })
      .then(res => res.json())
      .then(data => {
        if (isMountedRef.current) {
          setData(data);
          setLoading(false);
        }
      })
      .catch(err => {
        if (err.name !== 'AbortError' && isMountedRef.current) {
          setError(err);
          setLoading(false);
        }
      });
    
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [url]);
  
  // 组件卸载时清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);
  
  return { data, loading, error };
}

// 调试技巧：异步操作追踪
function useAsyncTracker(operation, dependencies) {
  const operationIdRef = useRef(0);
  
  useEffect(() => {
    const currentId = ++operationIdRef.current;
    console.log('🚀 Starting async operation #' + currentId);
    
    const startTime = Date.now();
    operation()
      .then(result => {
        const duration = Date.now() - startTime;
        console.log('✅ Async operation #' + currentId + ' completed in ' + duration + 'ms');
        return result;
      })
      .catch(error => {
        const duration = Date.now() - startTime;
        console.error('❌ Async operation #' + currentId + ' failed after ' + duration + 'ms:', error);
      });
  }, dependencies);
}`
    }
  ],

  debuggingTools: [
    {
      name: 'React DevTools',
      description: '官方的React调试工具，提供组件树查看、Hook状态检查、性能分析等功能',
      usage: `React DevTools是调试自定义Hook的首选工具，特别适合查看Hook状态和组件重渲染情况。

**主要功能**：
1. **Components标签**：查看组件树和Hook状态
2. **Profiler标签**：分析组件渲染性能
3. **Hook状态查看**：实时查看Hook的当前值
4. **时间旅行**：回溯状态变化历史

**使用技巧**：
- 选择组件后在右侧面板查看所有Hook状态
- 使用Search功能快速定位特定组件
- 开启"Highlight updates"查看重渲染组件
- 在Profiler中录制性能数据分析渲染瓶颈`,
      tips: [
        '使用useDebugValue为自定义Hook添加调试标签',
        '开启"Highlight updates"可视化组件重渲染',
        '在Profiler中使用"Flamegraph"视图分析性能',
        '利用组件名称过滤功能快速定位问题组件',
        '右键组件可以快速跳转到代码位置'
      ],
      code: `// 为Hook添加React DevTools支持
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  // 在DevTools中显示调试信息
  useDebugValue(count > 10 ? 'High' : 'Low');
  
  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);
  
  return { count, increment };
}

// 复杂Hook的调试信息
function useApiData(url) {
  const [state, setState] = useState({
    data: null,
    loading: false,
    error: null
  });
  
  // 格式化调试信息
  useDebugValue(state, ({ loading, data, error }) => {
    if (loading) return '🔄 Loading...';
    if (error) return '❌ Error: ' + error.message;
    if (data) return '✅ Data loaded';
    return '⭕ No data';
  });
  
  // Hook逻辑...
  
  return state;
}`
    },
    {
      name: 'Console调试',
      description: '使用console.log、console.group等方法进行详细的日志调试',
      usage: `Console调试是最基础但也是最灵活的调试方法，特别适合追踪数据流和执行流程。

**常用方法**：
1. **console.log**：输出基本信息
2. **console.group/groupEnd**：分组显示相关日志
3. **console.table**：表格形式显示数据
4. **console.time/timeEnd**：测量执行时间
5. **console.trace**：显示调用堆栈

**最佳实践**：
- 使用有意义的前缀标识不同类型的日志
- 在生产环境中自动移除调试日志
- 使用条件日志避免信息过载
- 结合堆栈信息定位问题源头`,
      tips: [
        '使用Emoji前缀让日志更易识别：🚀启动、✅成功、❌错误',
        '利用console.group创建层次化的日志结构',
        '使用console.table显示对象数组更清晰',
        '在关键分支点添加日志，追踪执行路径',
        '使用process.env.NODE_ENV控制调试日志的显示'
      ],
      code: `// 完整的Console调试示例
function useDebuggedApiData(url) {
  const [state, setState] = useState({
    data: null,
    loading: false,
    error: null
  });
  
  // 开发环境调试开关
  const DEBUG = process.env.NODE_ENV === 'development';
  
  const log = useCallback((message, data = null) => {
    if (DEBUG) {
      console.group('🎯 useApiData - ' + message);
      console.log('📍 URL:', url);
      console.log('📊 Current State:', state);
      if (data) console.log('📦 Data:', data);
      console.trace('📚 Call Stack');
      console.groupEnd();
    }
  }, [DEBUG, url, state]);
  
  useEffect(() => {
    if (!url) {
      log('⏭️ Skipping fetch - no URL');
      return;
    }
    
    log('🚀 Starting fetch');
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    const startTime = Date.now();
    
    fetch(url)
      .then(response => {
        const duration = Date.now() - startTime;
        log('📡 Response received (' + duration + 'ms)', { 
          status: response.status,
          headers: Object.fromEntries(response.headers.entries())
        });
        
        if (!response.ok) {
          throw new Error('HTTP ' + response.status);
        }
        
        return response.json();
      })
      .then(data => {
        log('✅ Data parsed successfully', data);
        setState({ data, loading: false, error: null });
      })
      .catch(error => {
        log('❌ Fetch failed', { error: error.message });
        setState(prev => ({ ...prev, loading: false, error }));
      });
  }, [url, log]);
  
  // 状态变化监控
  useEffect(() => {
    if (DEBUG) {
      console.table({
        'URL': url,
        'Has Data': !!state.data,
        'Loading': state.loading,
        'Has Error': !!state.error,
        'Timestamp': new Date().toISOString()
      });
    }
  }, [DEBUG, url, state]);
  
  return state;
}`
    },
    {
      name: '断点调试',
      description: '使用浏览器开发者工具设置断点，逐步执行代码进行深入调试',
      usage: `断点调试是最强大的调试方法，可以暂停代码执行并检查当前状态。

**设置断点的方法**：
1. **代码断点**：在代码中添加debugger语句
2. **浏览器断点**：在Sources面板中点击行号设置
3. **条件断点**：只在特定条件下触发的断点
4. **异常断点**：在抛出异常时自动暂停

**调试技巧**：
- 使用Step Over/Into/Out控制执行流程
- 在Variables面板查看所有变量值
- 使用Watch表达式监控特定值
- 利用Call Stack查看函数调用链`,
      tips: [
        '在Hook的关键逻辑处设置断点，如状态更新前后',
        '使用条件断点只在特定情况下暂停（如count > 10）',
        '在异步操作的then/catch中设置断点检查结果',
        '利用Console面板在断点处执行任意代码',
        '使用Live Edit功能在断点时修改代码并继续执行'
      ],
      code: `// 断点调试示例
function useDebugBreakpoints(data) {
  const [processedData, setProcessedData] = useState(null);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    // 设置条件断点：只在data长度大于5时暂停
    if (data && data.length > 5) {
      debugger; // 代码断点
    }
    
    try {
      // 复杂处理逻辑的调试点
      const processed = processComplexData(data);
      
      // 检查处理结果的断点
      if (processed.hasError) {
        debugger; // 错误情况的断点
      }
      
      setProcessedData(processed);
    } catch (err) {
      // 异常处理的断点
      debugger; // 异常断点
      setError(err);
    }
  }, [data]);
  
  return { processedData, error };
}

// 带断点的异步Hook
function useAsyncWithBreakpoints(url) {
  const [state, setState] = useState({ data: null, loading: false });
  
  const fetchData = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      // 请求开始断点
      debugger;
      
      const response = await fetch(url);
      
      // 响应检查断点
      if (!response.ok) {
        debugger; // HTTP错误断点
        throw new Error('HTTP ' + response.status);
      }
      
      const data = await response.json();
      
      // 数据验证断点
      if (!isValidData(data)) {
        debugger; // 数据验证失败断点
      }
      
      setState({ data, loading: false });
    } catch (error) {
      debugger; // 异常处理断点
      setState(prev => ({ ...prev, loading: false, error }));
    }
  }, [url]);
  
  return { ...state, fetchData };
}`
    },
    {
      name: '性能分析工具',
      description: '使用React Profiler和浏览器Performance面板分析Hook性能问题',
      usage: `性能分析工具帮助识别Hook中的性能瓶颈和不必要的重渲染。

**React Profiler使用**：
1. 在React DevTools的Profiler标签中录制
2. 查看Flamegraph识别慢组件
3. 使用Ranked视图按渲染时间排序
4. 检查为什么组件重新渲染

**浏览器Performance面板**：
1. 录制用户交互过程
2. 查看Main线程的活动
3. 分析JavaScript执行时间
4. 识别长任务和性能瓶颈`,
      tips: [
        '使用React.memo减少不必要的组件重渲染',
        '在Profiler中查看"Why did this render?"了解重渲染原因',
        '使用Performance面板的User Timing API标记自定义事件',
        '关注Hook中的重计算和副作用执行频率',
        '利用Profiler的Interactions功能追踪用户交互性能'
      ],
      code: `// 性能监控Hook
function usePerformanceTracker(name) {
  const renderCount = useRef(0);
  const startTime = useRef(Date.now());
  
  renderCount.current++;
  
  // 使用Performance API标记
  useEffect(() => {
    performance.mark(name + '-render-start');
    
    return () => {
      performance.mark(name + '-render-end');
      performance.measure(
        name + '-render-duration',
        name + '-render-start',
        name + '-render-end'
      );
      
      // 获取性能指标
      const measures = performance.getEntriesByName(name + '-render-duration');
      const lastMeasure = measures[measures.length - 1];
      
      if (lastMeasure && lastMeasure.duration > 16) {
        console.warn('⚠️ Slow render in ' + name + ': ' + lastMeasure.duration.toFixed(2) + 'ms');
      }
    };
  });
  
  // 渲染频率监控
  useEffect(() => {
    const now = Date.now();
    const timeSinceStart = now - startTime.current;
    const avgRenderTime = timeSinceStart / renderCount.current;
    
    console.log(name + ' Performance:', {
      totalRenders: renderCount.current,
      totalTime: timeSinceStart,
      avgRenderTime: avgRenderTime.toFixed(2)
    });
  });
  
  return { renderCount: renderCount.current };
}

// 使用性能追踪的Hook
function useApiDataWithPerformance(url) {
  usePerformanceTracker('useApiData');
  
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  // 测量异步操作性能
  const fetchData = useCallback(async () => {
    const operationId = 'fetch-' + Date.now();
    performance.mark(operationId + '-start');
    
    setLoading(true);
    
    try {
      const response = await fetch(url);
      const data = await response.json();
      
      performance.mark(operationId + '-end');
      performance.measure(operationId, operationId + '-start', operationId + '-end');
      
      const measure = performance.getEntriesByName(operationId)[0];
      console.log('🚀 Fetch completed in ' + measure.duration.toFixed(2) + 'ms');
      
      setData(data);
    } catch (error) {
      console.error('Fetch failed:', error);
    } finally {
      setLoading(false);
    }
  }, [url]);
  
  return { data, loading, fetchData };
}`
    }
  ],

  bestPractices: [
    '始终使用ESLint插件eslint-plugin-react-hooks检查Hook规则',
    '在开发环境中添加详细的调试日志，生产环境中自动移除',
    '使用useDebugValue为复杂Hook提供DevTools调试信息',
    '建立系统化的错误处理机制，包括错误边界和异常捕获',
    '定期进行性能分析，识别和优化性能瓶颈',
    '为异步操作添加适当的清理逻辑，避免内存泄漏',
    '使用TypeScript增强类型检查，在编译时发现更多错误',
    '建立全面的测试套件，包括单元测试和集成测试',
    '使用代码分割和懒加载减少初始包大小',
    '在团队中建立统一的调试规范和最佳实践文档'
  ],

  commonPatterns: [
    {
      pattern: '状态变化追踪模式',
      description: '系统地追踪Hook中状态的变化过程，帮助理解数据流',
      implementation: `// 状态变化追踪Hook
function useStateTracker(stateName, value) {
  const prevValueRef = useRef(value);
  const changeCountRef = useRef(0);
  
  useEffect(() => {
    if (prevValueRef.current !== value) {
      changeCountRef.current++;
      console.group('📈 ' + stateName + ' State Change #' + changeCountRef.current);
      console.log('Previous value:', prevValueRef.current);
      console.log('New value:', value);
      console.log('Change time:', new Date().toISOString());
      console.trace('Change triggered by:');
      console.groupEnd();
      
      prevValueRef.current = value;
    }
  }, [stateName, value]);
  
  return changeCountRef.current;
}

// 使用示例
function useCounterWithTracking(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  // 追踪count状态变化
  useStateTracker('count', count);
  
  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);
  
  return { count, increment };
}`
    },
    {
      pattern: '异步操作调试模式',
      description: '为异步操作添加完整的调试信息，包括开始、进行中、成功、失败各个阶段',
      implementation: `// 异步操作调试Hook
function useAsyncDebugger(name) {
  const activeOperations = useRef(new Set());
  
  const trackOperation = useCallback(async (operation, ...args) => {
    const operationId = name + '-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    
    console.group('🚀 Starting ' + name + ' operation: ' + operationId);
    console.log('Arguments:', args);
    console.log('Active operations before:', Array.from(activeOperations.current));
    
    activeOperations.current.add(operationId);
    const startTime = Date.now();
    
    try {
      console.log('⏳ ' + operationId + ' in progress...');
      const result = await operation(...args);
      
      const duration = Date.now() - startTime;
      console.log('✅ ' + operationId + ' completed in ' + duration + 'ms');
      console.log('Result:', result);
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error('❌ ' + operationId + ' failed after ' + duration + 'ms');
      console.error('Error:', error);
      throw error;
    } finally {
      activeOperations.current.delete(operationId);
      console.log('Active operations after:', Array.from(activeOperations.current));
      console.groupEnd();
    }
  }, [name]);
  
  return { trackOperation, activeOperations: activeOperations.current };
}

// 使用示例
function useApiWithDebug(url) {
  const [state, setState] = useState({ data: null, loading: false, error: null });
  const { trackOperation } = useAsyncDebugger('API Request');
  
  const fetchData = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const data = await trackOperation(async (url) => {
        const response = await fetch(url);
        if (!response.ok) throw new Error('HTTP ' + response.status);
        return response.json();
      }, url);
      
      setState({ data, loading: false, error: null });
    } catch (error) {
      setState(prev => ({ ...prev, loading: false, error }));
    }
  }, [url, trackOperation]);
  
  return { ...state, fetchData };
}`
    },
    {
      pattern: '性能监控模式',
      description: '实时监控Hook的性能指标，包括渲染次数、执行时间、内存使用等',
      implementation: `// 性能监控Hook
function usePerformanceMonitor(hookName) {
  const metricsRef = useRef({
    renderCount: 0,
    totalRenderTime: 0,
    maxRenderTime: 0,
    minRenderTime: Infinity,
    lastRenderTime: 0
  });
  
  const renderStartTime = useRef(Date.now());
  
  // 渲染开始
  metricsRef.current.renderCount++;
  renderStartTime.current = Date.now();
  
  useEffect(() => {
    // 渲染结束
    const renderTime = Date.now() - renderStartTime.current;
    const metrics = metricsRef.current;
    
    metrics.totalRenderTime += renderTime;
    metrics.maxRenderTime = Math.max(metrics.maxRenderTime, renderTime);
    metrics.minRenderTime = Math.min(metrics.minRenderTime, renderTime);
    metrics.lastRenderTime = renderTime;
    
    const avgRenderTime = metrics.totalRenderTime / metrics.renderCount;
    
    // 性能警告
    if (renderTime > 16) {
      console.warn('⚠️ Slow render in ' + hookName + ': ' + renderTime + 'ms');
    }
    
    // 定期报告性能指标
    if (metrics.renderCount % 10 === 0) {
      const metricsTable = {};
      metricsTable[hookName + ' Metrics'] = {
        'Total Renders': metrics.renderCount,
        'Avg Render Time': avgRenderTime.toFixed(2) + 'ms',
        'Max Render Time': metrics.maxRenderTime + 'ms',
        'Min Render Time': metrics.minRenderTime + 'ms',
        'Last Render Time': metrics.lastRenderTime + 'ms'
      };
      console.table(metricsTable);
    }
  });
  
  // 内存使用监控
  useEffect(() => {
    if (performance.memory) {
      const memoryInfo = {
        used: Math.round(performance.memory.usedJSHeapSize / 1048576) + 'MB',
        total: Math.round(performance.memory.totalJSHeapSize / 1048576) + 'MB',
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576) + 'MB'
      };
      
      console.log('🧠 ' + hookName + ' Memory:', memoryInfo);
    }
  }, [hookName]);
  
  return metricsRef.current;
}`
    }
  ]
};

export default debuggingTips;