import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'custom-hook-rules',
    question: '自定义Hook必须以"use"开头吗？如果不以use开头会怎样？',
    answer: `是的，自定义Hook必须以"use"开头，这是React的强制约定。

**为什么必须以use开头**：
1. **ESLint检查**：eslint-plugin-react-hooks依靠"use"前缀来识别Hook并检查规则
2. **React DevTools**：开发工具通过前缀识别和展示Hook信息
3. **团队约定**：统一的命名约定提高代码可读性和维护性

**不以use开头的后果**：
- ESLint不会检查Hook规则，可能导致运行时错误
- React DevTools无法正确识别和调试
- 违反了React官方约定，影响代码规范

**正确示例**：
- useCounter、useApiData、useLocalStorage、useAuth

即使函数内部使用了Hook，如果不以"use"开头，React也不会将其视为Hook。`,
    code: `// ❌ 错误：不以use开头
function counterLogic() {
  const [count, setCount] = useState(0); // ESLint不会检查Hook规则
  return { count, setCount };
}

// ✅ 正确：以use开头
function useCounter() {
  const [count, setCount] = useState(0); // ESLint会检查Hook规则
  return { count, setCount };
}`,
    tags: ['命名规则', 'ESLint', 'React约定'],
    relatedQuestions: ['hook-rules-violation', 'custom-hook-best-practices']
  },

  {
    id: 'hook-rules-violation',
    question: '在自定义Hook中违反Hook规则会导致什么问题？如何避免？',
    answer: `违反Hook规则会导致组件状态错乱、意外的渲染行为，甚至应用崩溃。

**常见违反情况**：
1. **条件调用Hook**：在if语句、循环中调用Hook
2. **嵌套函数调用**：在事件处理器或回调函数中调用Hook
3. **顺序不一致**：不同渲染周期中Hook调用顺序发生变化

**导致的问题**：
- Hook状态与实际使用不匹配
- "Rendered more hooks than during the previous render"错误
- 组件状态混乱或丢失
- 应用崩溃或异常行为

**避免方法**：
1. 始终在函数顶层调用Hook
2. 使用ESLint规则自动检查
3. 将条件逻辑放在Hook内部而非外部
4. 确保每次渲染时Hook调用顺序一致`,
    code: `// ❌ 错误：条件调用Hook
function useBadHook(condition) {
  const [count, setCount] = useState(0);
  
  if (condition) {
    const [name, setName] = useState(''); // 违反Hook规则
  }
  
  return { count, setCount };
}

// ✅ 正确：Hook在顶层，条件在内部
function useGoodHook(condition) {
  const [count, setCount] = useState(0);
  const [name, setName] = useState('');
  
  // 条件逻辑在Hook内部处理
  const displayName = condition ? name : '';
  
  return { count, setCount, displayName };
}

// ❌ 错误：在事件处理器中调用Hook
function useWrongEventHandler() {
  const [count, setCount] = useState(0);
  
  const handleClick = () => {
    const [temp] = useState(0); // 违反Hook规则
  };
  
  return { count, handleClick };
}`,
    tags: ['Hook规则', '错误处理', 'ESLint检查'],
    relatedQuestions: ['custom-hook-rules', 'hook-debugging']
  },

  {
    id: 'custom-hook-performance',
    question: '自定义Hook会影响性能吗？如何优化自定义Hook的性能？',
    answer: `自定义Hook本身不会产生额外的性能开销，但需要注意Hook内部的优化策略。

**性能影响因素**：
1. **Hook内部的状态更新频率**
2. **依赖数组的设计**
3. **函数和对象的重新创建**
4. **副作用的执行频率**

**优化策略**：

**1. 使用useCallback缓存函数**
防止不必要的函数重新创建和子组件重渲染

**2. 使用useMemo缓存复杂计算**
避免每次渲染都执行昂贵的计算

**3. 合理设计依赖数组**
确保依赖数组包含所有必要依赖，但避免不必要的依赖

**4. 状态结构优化**
合理组织状态结构，避免不必要的状态更新

**5. 延迟初始化**
对于昂贵的初始化操作，使用惰性初始化`,
    code: `// ❌ 性能问题的Hook
function useSlowHook(data) {
  const [result, setResult] = useState(null);
  
  // 每次渲染都重新创建函数
  const processData = () => {
    return expensiveComputation(data);
  };
  
  // 每次渲染都执行昂贵计算
  const computed = expensiveComputation(data);
  
  useEffect(() => {
    setResult(processData());
  }, [data]); // processData没有缓存，会导致无限循环
  
  return { result, processData, computed };
}

// ✅ 性能优化的Hook
function useFastHook(data) {
  const [result, setResult] = useState(null);
  
  // 缓存函数，避免重新创建
  const processData = useCallback(() => {
    return expensiveComputation(data);
  }, [data]);
  
  // 缓存昂贵计算
  const computed = useMemo(() => {
    return expensiveComputation(data);
  }, [data]);
  
  // 惰性初始化状态
  const [expensiveState] = useState(() => {
    return createExpensiveInitialState();
  });
  
  useEffect(() => {
    setResult(processData());
  }, [processData]); // 现在processData是稳定的引用
  
  return { result, processData, computed, expensiveState };
}`,
    tags: ['性能优化', 'useCallback', 'useMemo', '惰性初始化'],
    relatedQuestions: ['custom-hook-best-practices', 'hook-rerendering']
  },

  {
    id: 'custom-hook-testing',
    question: '如何测试自定义Hook？可以直接调用Hook函数进行测试吗？',
    answer: `不能直接调用Hook函数进行测试，必须在React组件上下文中测试Hook。

**为什么不能直接调用**：
Hook依赖React的内部机制（Hook链表、Fiber等），必须在组件渲染过程中调用

**推荐测试方法**：

**1. 使用@testing-library/react-hooks**
专门为Hook测试设计的库，提供renderHook函数

**2. 创建测试组件**
创建一个简单的测试组件来使用Hook

**3. 集成测试**
在实际组件中测试Hook的使用

**测试策略**：
- 测试Hook的输入输出
- 测试状态变化
- 测试副作用
- 测试错误情况
- Mock外部依赖`,
    code: `// 待测试的Hook
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);
  
  return { count, increment };
}

// ❌ 错误：直接调用Hook
describe('useCounter', () => {
  it('should work', () => {
    const result = useCounter(); // 这会报错
    expect(result.count).toBe(0);
  });
});

// ✅ 正确：使用renderHook
import { renderHook, act } from '@testing-library/react-hooks';

describe('useCounter', () => {
  it('should initialize with default value', () => {
    const { result } = renderHook(() => useCounter());
    expect(result.current.count).toBe(0);
  });
  
  it('should increment count', () => {
    const { result } = renderHook(() => useCounter());
    
    act(() => {
      result.current.increment();
    });
    
    expect(result.current.count).toBe(1);
  });
});

// ✅ 正确：使用测试组件
function TestComponent({ initialValue }) {
  const { count, increment } = useCounter(initialValue);
  
  return (
    <div>
      <span data-testid="count">{count}</span>
      <button data-testid="increment" onClick={increment}>
        Increment
      </button>
    </div>
  );
}

import { render, fireEvent } from '@testing-library/react';

describe('useCounter with component', () => {
  it('should work in component', () => {
    const { getByTestId } = render(<TestComponent initialValue={5} />);
    
    expect(getByTestId('count')).toHaveTextContent('5');
    
    fireEvent.click(getByTestId('increment'));
    
    expect(getByTestId('count')).toHaveTextContent('6');
  });
});`,
    tags: ['测试方法', 'Testing Library', 'renderHook'],
    relatedQuestions: ['hook-testing-strategies', 'hook-mocking']
  },

  {
    id: 'custom-hook-composition',
    question: '如何在一个自定义Hook中使用另一个自定义Hook？有什么注意事项？',
    answer: `自定义Hook可以相互组合使用，这是Hook设计的核心优势之一。只需要遵循Hook规则即可。

**组合原则**：
1. **在顶层调用**：被组合的Hook也必须在顶层调用
2. **遵循Hook规则**：所有Hook规则依然适用
3. **依赖管理**：注意Hook之间的依赖关系
4. **避免循环依赖**：Hook A不能依赖Hook B，同时Hook B又依赖Hook A

**组合策略**：
- **分层组合**：基础Hook → 中级Hook → 高级Hook
- **功能组合**：将不同功能的Hook组合成复合功能
- **条件组合**：根据条件决定使用哪些Hook

**注意事项**：
- 保持单一职责，避免过度复杂的组合
- 注意性能影响，避免不必要的重渲染
- 合理设计Hook的接口，便于组合使用`,
    code: `// 基础Hook：本地存储
function useLocalStorage(key, initialValue) {
  const [value, setValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch {
      return initialValue;
    }
  });
  
  const setStoredValue = useCallback((value) => {
    setValue(value);
    try {
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  }, [key]);
  
  return [value, setStoredValue];
}

// 基础Hook：API请求
function useApiData(url) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(url);
      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  }, [url]);
  
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  
  return { data, loading, error, refetch: fetchData };
}

// 组合Hook：带缓存的API数据
function useCachedApiData(url, cacheKey) {
  // 组合使用两个自定义Hook
  const [cachedData, setCachedData] = useLocalStorage(cacheKey, null);
  const { data, loading, error, refetch } = useApiData(url);
  
  // 当API数据更新时，更新缓存
  useEffect(() => {
    if (data) {
      setCachedData(data);
    }
  }, [data, setCachedData]);
  
  // 如果有缓存且正在加载，使用缓存数据
  const displayData = (loading && cachedData) ? cachedData : data;
  
  return {
    data: displayData,
    loading: loading && !cachedData,
    error,
    refetch,
    hasCache: !!cachedData
  };
}

// 高级组合Hook：用户管理
function useUserManager() {
  // 组合多个Hook
  const [currentUserId, setCurrentUserId] = useLocalStorage('currentUserId', null);
  const { data: user, loading, error } = useCachedApiData(
    currentUserId ? '/api/users/' + currentUserId : null,
    'user-' + currentUserId
  );
  
  const login = useCallback((userId) => {
    setCurrentUserId(userId);
  }, [setCurrentUserId]);
  
  const logout = useCallback(() => {
    setCurrentUserId(null);
  }, [setCurrentUserId]);
  
  return {
    user,
    loading,
    error,
    login,
    logout,
    isLoggedIn: !!currentUserId
  };
}

// 使用组合Hook
function UserProfile() {
  const { user, loading, error, logout, isLoggedIn } = useUserManager();
  
  if (!isLoggedIn) return <div>Please login</div>;
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <div>
      <h1>{user?.name}</h1>
      <button onClick={logout}>Logout</button>
    </div>
  );
}`,
    tags: ['Hook组合', '依赖管理', '架构设计'],
    relatedQuestions: ['custom-hook-best-practices', 'hook-architecture']
  },

  {
    id: 'custom-hook-typescript',
    question: '如何为自定义Hook添加TypeScript类型？有哪些最佳实践？',
    answer: `为自定义Hook添加TypeScript类型可以提供更好的开发体验和类型安全。

**核心原则**：
1. **明确的参数类型**：为所有参数定义清晰的类型
2. **准确的返回类型**：确保返回值类型准确且完整
3. **泛型支持**：为可复用的Hook添加泛型支持
4. **可选参数**：合理使用可选参数和默认值

**最佳实践**：

**1. 定义接口而非内联类型**
使用interface定义复杂的类型结构

**2. 使用泛型提高复用性**
让Hook能够处理不同类型的数据

**3. 提供完整的JSDoc注释**
为参数和返回值添加详细说明

**4. 导出类型定义**
让使用者能够引用Hook相关的类型`,
    code: `// 1. 基础类型定义
interface UseCounterOptions {
  min?: number;
  max?: number;
  step?: number;
}

interface UseCounterReturn {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
  set: (value: number) => void;
}

/**
 * 计数器Hook，提供计数状态和操作方法
 * @param initialValue 初始值，默认为0
 * @param options 配置选项
 * @returns 计数器状态和操作方法
 */
function useCounter(
  initialValue: number = 0,
  options: UseCounterOptions = {}
): UseCounterReturn {
  const { min, max, step = 1 } = options;
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => {
    setCount(prev => {
      const newValue = prev + step;
      return max !== undefined ? Math.min(newValue, max) : newValue;
    });
  }, [step, max]);
  
  const decrement = useCallback(() => {
    setCount(prev => {
      const newValue = prev - step;
      return min !== undefined ? Math.max(newValue, min) : newValue;
    });
  }, [step, min]);
  
  const reset = useCallback(() => {
    setCount(initialValue);
  }, [initialValue]);
  
  const set = useCallback((value: number) => {
    setCount(value);
  }, []);
  
  return { count, increment, decrement, reset, set };
}

// 2. 泛型Hook示例
interface UseApiDataOptions {
  enabled?: boolean;
  retryCount?: number;
}

interface UseApiDataReturn<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

/**
 * API数据获取Hook
 * @param url API地址
 * @param options 配置选项
 * @returns API数据状态和操作方法
 */
function useApiData<T = unknown>(
  url: string | null,
  options: UseApiDataOptions = {}
): UseApiDataReturn<T> {
  const { enabled = true, retryCount = 3 } = options;
  
  const [state, setState] = useState<{
    data: T | null;
    loading: boolean;
    error: Error | null;
  }>({
    data: null,
    loading: false,
    error: null
  });
  
  const fetchData = useCallback(async () => {
    if (!url || !enabled) return;
    
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('HTTP error! status: ' + response.status);
      }
      const data: T = await response.json();
      setState({ data, loading: false, error: null });
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error as Error 
      }));
    }
  }, [url, enabled]);
  
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  
  return {
    ...state,
    refetch: fetchData
  };
}

// 3. 复杂类型示例
interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
}

interface UseUserReturn {
  user: User | null;
  loading: boolean;
  error: Error | null;
  updateUser: (updates: Partial<User>) => Promise<void>;
  deleteUser: () => Promise<void>;
}

function useUser(userId: string): UseUserReturn {
  const { data: user, loading, error, refetch } = useApiData<User>(
    '/api/users/' + userId
  );
  
  const updateUser = useCallback(async (updates: Partial<User>) => {
    try {
      const response = await fetch('/api/users/' + userId, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      });
      
      if (!response.ok) {
        throw new Error('Failed to update user');
      }
      
      await refetch();
    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  }, [userId, refetch]);
  
  const deleteUser = useCallback(async () => {
    try {
      const response = await fetch('/api/users/' + userId, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete user');
      }
    } catch (error) {
      console.error('Delete user error:', error);
      throw error;
    }
  }, [userId]);
  
  return {
    user,
    loading,
    error,
    updateUser,
    deleteUser
  };
}

// 4. 导出类型供外部使用
export type {
  UseCounterOptions,
  UseCounterReturn,
  UseApiDataOptions,
  UseApiDataReturn,
  UseUserReturn
};

// 使用示例
function UserProfile({ userId }: { userId: string }) {
  const { user, loading, updateUser }: UseUserReturn = useUser(userId);
  
  const handleNameUpdate = async () => {
    await updateUser({ name: 'New Name' });
  };
  
  if (loading) return <div>Loading...</div>;
  
  return (
    <div>
      <h1>{user?.name}</h1>
      <button onClick={handleNameUpdate}>Update Name</button>
    </div>
  );
}`,
    tags: ['TypeScript', '类型定义', '泛型', 'JSDoc'],
    relatedQuestions: ['typescript-best-practices', 'hook-interface-design']
  },

  {
    id: 'custom-hook-debugging',
    question: '如何调试自定义Hook？有哪些常用的调试技巧？',
    answer: `调试自定义Hook需要结合多种工具和技术，从简单的日志输出到高级的调试工具。

**调试工具**：
1. **React DevTools**：查看Hook状态和组件树
2. **console.log**：输出关键变量和执行流程
3. **useDebugValue**：在DevTools中显示Hook调试信息
4. **浏览器断点**：在关键位置设置断点
5. **错误边界**：捕获Hook中的错误

**常用调试技巧**：

**1. 添加调试信息**
使用useDebugValue显示Hook状态

**2. 日志输出优化**
有选择性地输出关键信息

**3. 状态追踪**
跟踪状态变化的原因和时机

**4. 性能监控**
监控Hook的执行次数和耗时

**5. 错误处理**
优雅地处理和显示错误信息`,
    code: `// 1. 使用useDebugValue进行调试
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  // 在React DevTools中显示调试信息
  useDebugValue(count, count => 'Count: ' + count);
  
  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);
  
  return { count, increment };
}

// 2. 添加详细日志的Hook
function useApiData(url) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // 开发环境调试日志
  const debug = process.env.NODE_ENV === 'development';
  
  useEffect(() => {
    if (debug) {
      console.group('useApiData Effect');
      console.log('URL:', url);
      console.log('Current state:', { data, loading, error });
      console.groupEnd();
    }
  });
  
  const fetchData = useCallback(async () => {
    if (!url) return;
    
    if (debug) {
      console.log('🚀 Starting fetch for:', url);
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(url);
      const result = await response.json();
      
      if (debug) {
        console.log('✅ Fetch successful:', result);
      }
      
      setData(result);
    } catch (err) {
      if (debug) {
        console.error('❌ Fetch failed:', err);
      }
      setError(err);
    } finally {
      setLoading(false);
    }
  }, [url, debug]);
  
  // DevTools调试信息
  useDebugValue({ url, loading, hasData: !!data, hasError: !!error });
  
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  
  return { data, loading, error, refetch: fetchData };
}

// 3. 性能监控Hook
function usePerformanceMonitor(name) {
  const renderCount = useRef(0);
  const startTime = useRef(Date.now());
  
  renderCount.current += 1;
  
  useEffect(() => {
    const endTime = Date.now();
    console.log('📊 ' + name + ' - Render #' + renderCount.current + ', Time: ' + (endTime - startTime.current) + 'ms');
    startTime.current = endTime;
  });
  
  useDebugValue(name + ': ' + renderCount.current + ' renders');
  
  return renderCount.current;
}

// 4. 错误边界Hook
function useErrorHandler() {
  const [error, setError] = useState(null);
  
  const resetError = useCallback(() => {
    setError(null);
  }, []);
  
  const handleError = useCallback((error, errorInfo) => {
    console.error('Hook Error:', error);
    console.error('Error Info:', errorInfo);
    setError(error);
  }, []);
  
  useEffect(() => {
    if (error) {
      // 可以在这里上报错误
      console.error('Unhandled error in Hook:', error);
    }
  }, [error]);
  
  return { error, resetError, handleError };
}

// 5. 带调试功能的复合Hook
function useDebuggedApiData(url, options = {}) {
  const { enableDebug = process.env.NODE_ENV === 'development' } = options;
  
  // 性能监控
  const renderCount = usePerformanceMonitor('useApiData');
  
  // 错误处理
  const { error: hookError, handleError } = useErrorHandler();
  
  // 状态追踪
  const [debugInfo, setDebugInfo] = useState({
    fetchCount: 0,
    lastFetchTime: null,
    cacheHits: 0
  });
  
  const { data, loading, error, refetch } = useApiData(url);
  
  // 更新调试信息
  useEffect(() => {
    if (loading) {
      setDebugInfo(prev => ({
        ...prev,
        fetchCount: prev.fetchCount + 1,
        lastFetchTime: new Date().toISOString()
      }));
    }
  }, [loading]);
  
  // 综合调试信息
  useDebugValue(
    enableDebug ? {
      url,
      renders: renderCount,
      fetches: debugInfo.fetchCount,
      lastFetch: debugInfo.lastFetchTime,
      status: loading ? 'loading' : error ? 'error' : 'success'
    } : null
  );
  
  if (enableDebug) {
    console.table({
      'URL': url,
      'Render Count': renderCount,
      'Fetch Count': debugInfo.fetchCount,
      'Has Data': !!data,
      'Is Loading': loading,
      'Has Error': !!error
    });
  }
  
  return {
    data,
    loading,
    error: error || hookError,
    refetch,
    debugInfo: enableDebug ? debugInfo : undefined
  };
}

// 使用调试Hook
function DebugExample() {
  const { data, loading, error, debugInfo } = useDebuggedApiData(
    '/api/users',
    { enableDebug: true }
  );
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <div>
      <h1>Users</h1>
      {data && <pre>{JSON.stringify(data, null, 2)}</pre>}
      {debugInfo && (
        <details>
          <summary>Debug Info</summary>
          <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
        </details>
      )}
    </div>
  );
}`,
    tags: ['调试技巧', 'useDebugValue', '性能监控', '错误处理'],
    relatedQuestions: ['react-devtools-usage', 'hook-performance-debugging']
  },

  {
    id: 'custom-hook-state-management',
    question: '自定义Hook与状态管理库（如Redux、Zustand）相比有什么区别？何时使用哪个？',
    answer: `自定义Hook和状态管理库各有优势，适用于不同的场景和需求。

**核心区别**：

**1. 作用域**
- **自定义Hook**：组件级别的状态，每个使用Hook的组件都有独立的状态实例
- **状态管理库**：应用级别的全局状态，所有组件共享同一个状态

**2. 复杂度**
- **自定义Hook**：简单直观，学习成本低
- **状态管理库**：需要理解额外的概念（store、action、reducer等）

**3. 性能**
- **自定义Hook**：只影响使用Hook的组件
- **状态管理库**：可能影响订阅了相同状态的所有组件

**使用场景建议**：

**使用自定义Hook的场景**：
- 组件间的逻辑复用
- 简单的状态管理
- 与外部API的交互
- 表单状态管理
- 本地存储同步

**使用状态管理库的场景**：
- 复杂的全局状态
- 多组件间的状态共享
- 需要时间旅行调试
- 复杂的状态更新逻辑
- 大型应用的架构需求`,
    code: `// 1. 自定义Hook示例 - 组件级状态
function useShoppingCart() {
  const [items, setItems] = useState([]);
  const [total, setTotal] = useState(0);
  
  const addItem = useCallback((item) => {
    setItems(prev => [...prev, item]);
    setTotal(prev => prev + item.price);
  }, []);
  
  const removeItem = useCallback((itemId) => {
    setItems(prev => {
      const newItems = prev.filter(item => item.id !== itemId);
      const newTotal = newItems.reduce((sum, item) => sum + item.price, 0);
      setTotal(newTotal);
      return newItems;
    });
  }, []);
  
  return { items, total, addItem, removeItem };
}

// 每个组件都有独立的购物车状态
function ComponentA() {
  const cart = useShoppingCart(); // 独立的购物车实例
  return <div>Cart A: {cart.items.length} items</div>;
}

function ComponentB() {
  const cart = useShoppingCart(); // 另一个独立的购物车实例
  return <div>Cart B: {cart.items.length} items</div>;
}

// 2. 状态管理库示例 - 全局状态（Zustand）
import { create } from 'zustand';

const useGlobalCartStore = create((set, get) => ({
  items: [],
  total: 0,
  
  addItem: (item) => set(state => ({
    items: [...state.items, item],
    total: state.total + item.price
  })),
  
  removeItem: (itemId) => set(state => {
    const newItems = state.items.filter(item => item.id !== itemId);
    return {
      items: newItems,
      total: newItems.reduce((sum, item) => sum + item.price, 0)
    };
  }),
  
  clearCart: () => set({ items: [], total: 0 })
}));

// 所有组件共享同一个购物车状态
function GlobalComponentA() {
  const { items, addItem } = useGlobalCartStore();
  return <div>Global Cart: {items.length} items</div>; // 状态共享
}

function GlobalComponentB() {
  const { total, clearCart } = useGlobalCartStore();
  return (
    <div>
      Total: {total}
      <button onClick={clearCart}>Clear</button>
    </div>
  ); // 同样的状态，会同步更新
}

// 3. 混合使用示例
function useUserPreferences() {
  // 使用全局状态获取用户信息
  const user = useGlobalUserStore(state => state.user);
  
  // 使用本地Hook管理UI偏好
  const [theme, setTheme] = useLocalStorage('theme', 'light');
  const [language, setLanguage] = useLocalStorage('language', 'en');
  
  // 组合全局和本地状态
  return {
    user,
    theme,
    language,
    setTheme,
    setLanguage
  };
}

// 4. 何时选择的决策树
function decisionExample() {
  // 问题1：状态需要在多个组件间共享吗？
  // 是 → 考虑状态管理库
  // 否 → 使用自定义Hook
  
  // 问题2：状态逻辑复杂吗？
  // 是 → 状态管理库（如Redux）
  // 否 → 简单的Hook或轻量状态库（如Zustand）
  
  // 问题3：需要持久化吗？
  // 是 → Hook + localStorage 或 状态管理库 + 持久化插件
  
  // 问题4：需要时间旅行调试吗？
  // 是 → Redux DevTools
  // 否 → Hook足够
  
  // 问题5：团队熟悉度如何？
  // 新手团队 → 先用Hook，逐步引入状态管理
  // 经验团队 → 根据项目需求选择
}

// 5. 性能对比示例
function PerformanceComparison() {
  // Hook方式：只有使用Hook的组件重渲染
  const { count, increment } = useCounter();
  
  // 全局状态方式：所有订阅的组件都可能重渲染
  const globalCount = useGlobalStore(state => state.count);
  const incrementGlobal = useGlobalStore(state => state.increment);
  
  // 使用selector优化全局状态性能
  const optimizedCount = useGlobalStore(
    state => state.count,
    (prev, next) => prev === next // 浅比较优化
  );
  
  return (
    <div>
      <div>Local count: {count}</div>
      <div>Global count: {globalCount}</div>
      <div>Optimized count: {optimizedCount}</div>
    </div>
  );
}`,
    tags: ['状态管理', 'Redux', 'Zustand', '架构选择'],
    relatedQuestions: ['global-vs-local-state', 'state-management-patterns']
  }
];

export default commonQuestions;