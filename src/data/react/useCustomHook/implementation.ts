import { Implementation } from "@/types/api";

const implementation: Implementation = {
  mechanism: `自定义Hook是React函数组件中复用状态逻辑的核心机制，通过遵循"以use开头的函数"约定，在内部调用其他Hook来封装复杂逻辑。

## 🔧 核心实现机制

### 1. Hook规则遵循
自定义Hook必须严格遵循React Hook的使用规则：

\`\`\`typescript
// ✅ 正确：自定义Hook遵循Hook规则
function useCounter(initialValue = 0) {
  // Hook调用在函数顶层，不在条件语句中
  const [count, setCount] = useState(initialValue);
  const [history, setHistory] = useState<number[]>([initialValue]);
  
  const increment = useCallback(() => {
    setCount(prev => {
      const newValue = prev + 1;
      setHistory(prevHistory => [...prevHistory, newValue]);
      return newValue;
    });
  }, []);
  
  return { count, increment, history };
}

// ❌ 错误：违反Hook规则
function useBrokenCounter(initialValue = 0) {
  if (initialValue < 0) {
    // Hook不能在条件语句中调用
    const [count] = useState(0);
    return { count };
  }
  
  const [count, setCount] = useState(initialValue);
  return { count, setCount };
}
\`\`\`

### 2. 状态逻辑封装
自定义Hook通过封装useState、useEffect等内置Hook，提供高层次的状态管理抽象：

\`\`\`typescript
// 复杂状态逻辑的封装示例
function useAsyncData<T>(
  asyncFunction: () => Promise<T>,
  dependencies: React.DependencyList = []
) {
  const [state, setState] = useState<{
    data: T | null;
    loading: boolean;
    error: Error | null;
  }>({
    data: null,
    loading: false,
    error: null
  });
  
  // 使用useCallback缓存异步函数
  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const result = await asyncFunction();
      setState({ data: result, loading: false, error: null });
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error as Error 
      }));
    }
  }, dependencies);
  
  // 自动执行和依赖更新
  useEffect(() => {
    execute();
  }, [execute]);
  
  return { ...state, retry: execute };
}
\`\`\`

### 3. 副作用管理
自定义Hook通过useEffect管理副作用的生命周期：

\`\`\`typescript
function useWindowSize() {
  const [windowSize, setWindowSize] = useState(() => {
    if (typeof window === 'undefined') {
      return { width: 0, height: 0 };
    }
    
    return {
      width: window.innerWidth,
      height: window.innerHeight
    };
  });
  
  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    }
    
    window.addEventListener('resize', handleResize);
    
    // 清理函数：移除事件监听器
    return () => window.removeEventListener('resize', handleResize);
  }, []); // 空依赖数组，只在挂载时执行
  
  return windowSize;
}
\`\`\`

### 4. 性能优化集成
自定义Hook内置性能优化策略：

\`\`\`typescript
function useDebounceValue<T>(value: T, delay: number) {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  
  useEffect(() => {
    // 使用定时器实现防抖
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    // 清理函数：清除之前的定时器
    return () => clearTimeout(timer);
  }, [value, delay]);
  
  return debouncedValue;
}

// 使用useMemo缓存复杂计算
function useExpensiveCalculation(data: number[]) {
  const result = useMemo(() => {
    // 模拟昂贵的计算
    return data.reduce((sum, item) => sum + Math.pow(item, 2), 0);
  }, [data]); // 只有data变化时才重新计算
  
  return result;
}
\`\`\`

### 5. 类型安全保障
通过TypeScript提供完整的类型推断和约束：

\`\`\`typescript
// 泛型自定义Hook
function useLocalStorageState<T>(
  key: string,
  defaultValue: T
): [T, React.Dispatch<React.SetStateAction<T>>] {
  const [state, setState] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch {
      return defaultValue;
    }
  });
  
  const setStorageState = useCallback((value: React.SetStateAction<T>) => {
    setState(prevState => {
      const newValue = typeof value === 'function' 
        ? (value as (prevState: T) => T)(prevState)
        : value;
      
      try {
        window.localStorage.setItem(key, JSON.stringify(newValue));
      } catch {
        console.warn('Failed to save to localStorage');
      }
      
      return newValue;
    });
  }, [key]);
  
  return [state, setStorageState];
}
\`\`\``,

  visualization: null,
    
  plainExplanation: `想象自定义Hook就像是给你的React应用创建"智能工具包"。

🧰 **工具包比喻**：
- 工具包 = 自定义Hook函数
- 工具 = 内置Hook（useState, useEffect等）
- 使用说明书 = Hook的返回接口
- 安全规范 = Hook使用规则

🔧 **工具包的特点**：
1. **标准化工具组合**：每个工具包都按照标准流程组装（Hook规则）
2. **专业用途设计**：每个工具包解决特定问题（如数据获取、表单管理）
3. **可重复使用**：一次创建，多处使用，避免重复造轮子
4. **安全保障**：内置错误处理和性能优化
5. **使用简单**：复杂逻辑封装，对外提供简洁接口

🎯 **实际场景**：
就像电工有专门的电路测试工具包，水管工有专门的管道维修工具包一样，React开发者通过自定义Hook创建专门的"状态管理工具包"、"数据获取工具包"、"表单处理工具包"等。

每个工具包内部可能包含多个工具（useState管理状态，useEffect处理副作用，useCallback优化性能），但对使用者来说，只需要知道工具包的输入和输出即可。`,

  designConsiderations: [
    "遵循Hook命名约定，以'use'开头，便于React开发工具识别和检查",
    "保持Hook的纯净性，避免在Hook内部直接操作DOM或执行副作用",
    "合理设计Hook的依赖数组，避免不必要的重新执行和无限循环",
    "提供清晰的TypeScript类型定义，增强开发体验和代码安全性",
    "考虑Hook的组合性，设计时要能够与其他Hook良好协作",
    "实现适当的错误处理和边界情况处理，提高Hook的健壮性",
    "优化性能，使用useMemo和useCallback避免不必要的重渲染",
    "设计简洁明了的API，隐藏复杂性，暴露必要的控制接口"
  ],
  
  relatedConcepts: [
    "useState - 基础状态管理Hook，自定义Hook的核心构建块",
    "useEffect - 副作用处理Hook，管理生命周期和外部系统集成",
    "useCallback - 函数记忆化Hook，优化自定义Hook的性能",
    "useMemo - 值记忆化Hook，缓存昂贵计算结果",
    "useRef - 引用管理Hook，在自定义Hook中存储可变值",
    "useContext - 上下文消费Hook，在自定义Hook中访问全局状态",
    "useReducer - 复杂状态管理Hook，替代useState处理复杂逻辑",
    "React.memo - 组件记忆化，与自定义Hook配合优化组件性能"
  ]
};

export default implementation;