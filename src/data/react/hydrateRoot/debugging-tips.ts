import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'hydrateRoot在实际应用中经常遇到的问题和解决方案。这些问题多数源于服务端和客户端渲染的不一致性。',
        sections: [
          {
            title: 'Hydration不匹配错误',
            description: '最常见的hydrateRoot问题，表现为Warning: Text content did not match等错误',
            items: [
              {
                title: '时间敏感数据不一致',
                description: '服务端和客户端渲染时间不同导致的内容差异',
                solution: '使用固定的时间戳或在useEffect中更新时间相关内容',
                prevention: '避免在组件渲染中直接使用Date.now()或Math.random()',
                code: `// ❌ 错误：会导致hydration不匹配
function TimeDisplay() {
  return <div>当前时间: {new Date().toLocaleString()}</div>;
}

// ✅ 正确：避免hydration不匹配
function TimeDisplay() {
  const [currentTime, setCurrentTime] = useState(null);
  
  useEffect(() => {
    setCurrentTime(new Date().toLocaleString());
    const timer = setInterval(() => {
      setCurrentTime(new Date().toLocaleString());
    }, 1000);
    
    return () => clearInterval(timer);
  }, []);
  
  return <div>当前时间: {currentTime || '加载中...'}</div>;
}`
              },
              {
                title: '客户端特有API使用',
                description: 'localStorage、window等浏览器API在服务端不存在',
                solution: '使用条件渲染或useEffect处理客户端特有逻辑',
                prevention: '检查typeof window !== "undefined"再使用浏览器API',
                code: `// ❌ 错误：服务端没有localStorage
function UserSettings() {
  const theme = localStorage.getItem('theme') || 'light';
  return <div className={theme}>内容</div>;
}

// ✅ 正确：条件检查
function UserSettings() {
  const [theme, setTheme] = useState('light');
  
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme') || 'light';
      setTheme(savedTheme);
    }
  }, []);
  
  return <div className={theme}>内容</div>;
}`
              },
              {
                title: '条件渲染逻辑差异',
                description: '服务端和客户端的条件判断结果不同',
                solution: '确保条件判断的依据在两端完全一致',
                prevention: '使用稳定的props或状态作为条件判断依据',
                code: `// ❌ 错误：用户代理检测可能不一致
function ResponsiveComponent() {
  const isMobile = /Mobile/.test(navigator.userAgent);
  return isMobile ? <MobileView /> : <DesktopView />;
}

// ✅ 正确：使用CSS媒体查询或ResizeObserver
function ResponsiveComponent() {
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  return isMobile ? <MobileView /> : <DesktopView />;
}`
              }
            ]
          },
          {
            title: 'Hydration性能问题',
            description: '激活过程缓慢或阻塞用户交互的问题',
            items: [
              {
                title: 'JavaScript包过大导致激活缓慢',
                description: '初始JavaScript包太大，导致hydrateRoot激活时间过长',
                solution: '使用代码分割，延迟加载非关键组件',
                prevention: '定期分析bundle大小，控制初始包体积',
                code: `// 代码分割优化
const HeavyComponent = lazy(() => import('./HeavyComponent'));

function App() {
  return (
    <div>
      {/* 关键内容：立即激活 */}
      <Header />
      <MainContent />
      
      {/* 重型组件：懒加载 */}
      <Suspense fallback={<div>加载中...</div>}>
        <HeavyComponent />
      </Suspense>
    </div>
  );
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '使用开发工具快速定位和解决hydrateRoot相关问题的方法和技巧。',
        sections: [
          {
            title: 'React DevTools调试',
            description: '利用React DevTools的Components和Profiler面板调试hydration问题',
            items: [
              {
                title: '组件状态检查',
                description: '在Components面板查看组件的props和state是否符合预期',
                solution: '对比服务端渲染和客户端hydration后的组件状态',
                prevention: '定期使用DevTools验证关键组件的状态一致性',
                code: `// 在开发环境添加调试信息
function DebugWrapper({ children, componentName }) {
  if (process.env.NODE_ENV === 'development') {
    console.log(componentName + '渲染时间:', new Date().toISOString());
  }
  
  return (
    <div data-component={componentName}>
      {children}
    </div>
  );
}

// 使用示例
<DebugWrapper componentName="ProductCard">
  <ProductCard product={product} />
</DebugWrapper>`
              },
              {
                title: '性能分析',
                description: '使用Profiler面板分析hydration过程的性能瓶颈',
                solution: '记录和分析组件渲染时间，找出性能瓶颈',
                prevention: '定期进行性能分析，优化慢组件',
                code: `// 添加性能监控
import { Profiler } from 'react';

function onRenderCallback(id, phase, actualDuration, baseDuration, startTime, commitTime) {
  if (phase === 'mount') {
    console.log('组件首次挂载:', {
      component: id,
      duration: actualDuration,
      timestamp: commitTime
    });
    
    // 发送到性能监控系统
    if (actualDuration > 100) {
      console.warn('慢组件detected:', id, actualDuration + 'ms');
    }
  }
}

<Profiler id="App" onRender={onRenderCallback}>
  <App />
</Profiler>`
              },
              {
                title: '控制台调试技巧',
                description: '在浏览器控制台中调试hydrateRoot相关问题的技巧',
                items: [
                  {
                    title: '检测hydration状态',
                    description: '在控制台中检查应用的hydration状态',
                    solution: '添加全局调试函数，方便在控制台检查状态',
                    prevention: '在开发环境暴露调试接口',
                    code: `// 全局调试工具
if (process.env.NODE_ENV === 'development') {
  window.debugHydration = {
    // 检查元素是否已激活
    isHydrated: (selector) => {
      const element = document.querySelector(selector);
      return element && element._reactInternalFiber;
    },
    
    // 获取React组件实例
    getReactInstance: (selector) => {
      const element = document.querySelector(selector);
      return element._reactInternalFiber;
    },
    
    // 检查hydration不匹配
    checkMismatch: () => {
      const warnings = [];
      console.group('Hydration检查');
      // 实现具体检查逻辑
      console.groupEnd();
      return warnings;
    }
  };
}

// 使用方式
// 在控制台输入：debugHydration.isHydrated('.header')
// 或者：debugHydration.checkMismatch()`
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;