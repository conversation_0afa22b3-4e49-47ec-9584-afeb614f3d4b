import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    completionStatus: '内容已完成',
    
    id: 'scenario-1',
    title: 'Next.js全栈应用的SSR激活',
    description: '大型电商平台使用Next.js构建，需要在服务端渲染产品列表页面以优化SEO，然后在客户端激活交互功能。',
    businessValue: 'SEO排名提升40%，首屏加载时间减少60%，转化率增加25%',
    scenario: '用户访问产品列表页面时，服务器返回完整的HTML内容，包括产品卡片、价格信息和基础样式。页面加载后，hydrateRoot激活所有交互功能，如添加购物车、筛选器、分页等。',
    code: `// pages/products/index.tsx (Next.js)
import { hydrateRoot } from 'react-dom/client';
import { GetServerSideProps } from 'next';

interface ProductsPageProps {
  products: Product[];
  categories: Category[];
}

// 服务端渲染：获取产品数据
export const getServerSideProps: GetServerSideProps = async () => {
  const products = await fetchProducts();
  const categories = await fetchCategories();
  
  return {
    props: {
      products,
      categories,
    },
  };
};

// 客户端组件
function ProductsPage({ products, categories }: ProductsPageProps) {
  const [filteredProducts, setFilteredProducts] = useState(products);
  const [selectedCategory, setSelectedCategory] = useState('all');

  // 客户端交互逻辑
  const handleCategoryFilter = (categoryId: string) => {
    setSelectedCategory(categoryId);
    const filtered = categoryId === 'all' 
      ? products 
      : products.filter(p => p.categoryId === categoryId);
    setFilteredProducts(filtered);
  };

  const handleAddToCart = async (productId: string) => {
    // 客户端购物车逻辑
    await addToCart(productId);
    showToast('商品已添加到购物车');
  };

  return (
    <div className="products-page">
      {/* 服务端渲染的筛选器 */}
      <CategoryFilter 
        categories={categories}
        selectedCategory={selectedCategory}
        onCategoryChange={handleCategoryFilter}
      />
      
      {/* 服务端渲染的产品网格 */}
      <ProductGrid 
        products={filteredProducts}
        onAddToCart={handleAddToCart}
      />
    </div>
  );
}

// 客户端激活 (pages/_app.tsx)
if (typeof window !== 'undefined') {
  const container = document.getElementById('__next');
  if (container && container.hasChildNodes()) {
    // Next.js自动处理hydrateRoot
    // 这里展示手动处理的概念
    hydrateRoot(container, <App Component={ProductsPage} pageProps={pageProps} />);
  }
}

export default ProductsPage;`,
    explanation: 'hydrateRoot在Next.js中被框架自动调用，它将服务端渲染的静态产品列表转换为可交互的动态应用。用户可以立即看到产品内容（SEO友好），然后无缝地使用筛选、购物车等交互功能。',
    benefits: [
      '首屏内容立即可见，无需等待JS加载',
      'SEO优化，搜索引擎可以索引完整的产品信息',
      '渐进增强，即使JS加载失败用户仍能看到基础内容',
      '性能优化，关键渲染路径得到优化'
    ],
    metrics: {
      performance: '首屏渲染时间从2.5s降至0.8s，LCP改善65%',
      userExperience: '用户满意度提升30%，跳出率降低45%',
      technicalMetrics: 'Lighthouse SEO分数提升至95分，TTI改善40%'
    },
    difficulty: 'medium',
    tags: ['Next.js', 'SSR', '电商', 'SEO']
  },

  {
    completionStatus: '内容已完成',
    
    id: 'scenario-2',
    title: '博客系统的静态生成与动态激活',
    description: '技术博客平台使用SSG在构建时生成文章页面，然后使用hydrateRoot在客户端激活评论、点赞和分享功能。',
    businessValue: '页面加载速度提升70%，CDN缓存命中率达到95%，用户参与度增加50%',
    scenario: '博客文章在构建时预渲染为静态HTML，包含完整的文章内容、代码高亮和SEO标签。用户访问时立即看到文章内容，然后hydrateRoot激活评论系统、代码复制按钮、文章目录导航等交互功能。',
    code: `// components/BlogPost.tsx
import { hydrateRoot } from 'react-dom/client';
import { useState, useEffect } from 'react';

interface BlogPostProps {
  post: {
    id: string;
    title: string;
    content: string;
    publishedAt: string;
    author: Author;
  };
  comments: Comment[];
}

function BlogPost({ post, comments: initialComments }: BlogPostProps) {
  const [comments, setComments] = useState(initialComments);
  const [likes, setLikes] = useState(0);
  const [isLiked, setIsLiked] = useState(false);

  // 客户端功能：评论系统
  const handleSubmitComment = async (content: string) => {
    const newComment = await submitComment(post.id, content);
    setComments(prev => [...prev, newComment]);
  };

  // 客户端功能：点赞功能
  const handleLike = async () => {
    if (isLiked) {
      await unlikePost(post.id);
      setLikes(prev => prev - 1);
    } else {
      await likePost(post.id);
      setLikes(prev => prev + 1);
    }
    setIsLiked(!isLiked);
  };

  // 客户端功能：代码复制
  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    showToast('代码已复制到剪贴板');
  };

  return (
    <article className="blog-post">
      {/* 静态生成的文章头部 */}
      <header>
        <h1>{post.title}</h1>
        <div className="post-meta">
          <time>{post.publishedAt}</time>
          <span>作者：{post.author.name}</span>
        </div>
      </header>

      {/* 静态生成的文章内容 */}
      <div 
        className="post-content"
        dangerouslySetInnerHTML={{ __html: post.content }}
      />

      {/* 客户端激活的交互功能 */}
      <div className="post-actions">
        <button 
          onClick={handleLike}
          className={isLiked ? 'liked' : ''}
        >
          👍 {likes}
        </button>
        <ShareButtons postUrl={window.location.href} />
      </div>

      {/* 客户端激活的评论系统 */}
      <CommentSection 
        comments={comments}
        onSubmitComment={handleSubmitComment}
      />
    </article>
  );
}

// 静态生成 + 客户端激活
export default function BlogPostPage({ post, comments }) {
  useEffect(() => {
    // 为代码块添加复制按钮
    const codeBlocks = document.querySelectorAll('pre code');
    codeBlocks.forEach(block => {
      const copyButton = document.createElement('button');
      copyButton.textContent = '复制';
      copyButton.onclick = () => handleCopyCode(block.textContent);
      block.parentElement.appendChild(copyButton);
    });
  }, []);

  return <BlogPost post={post} comments={comments} />;
}

// 构建时静态生成
export async function getStaticProps({ params }) {
  const post = await getPost(params.slug);
  const comments = await getComments(post.id);
  
  return {
    props: { post, comments },
    revalidate: 3600, // ISR: 每小时重新生成
  };
}`,
    explanation: '这种模式结合了静态生成的性能优势和客户端交互的动态性。文章内容在构建时预渲染，确保快速加载和SEO优化，而评论、点赞等功能通过hydrateRoot激活，提供完整的用户体验。',
    benefits: [
      '极快的首屏加载，文章内容立即可见',
      'CDN友好，静态文件可以全球分发',
      '完整的交互功能，评论和社交功能正常工作',
      'SEO优化，搜索引擎可以完整索引文章内容',
      '离线友好，缓存的静态内容可离线访问'
    ],
    metrics: {
      performance: '首屏渲染时间降至0.5s，Core Web Vitals全绿',
      userExperience: '用户停留时间增加45%，评论参与率提升60%',
      technicalMetrics: 'CDN缓存命中率95%，服务器负载减少80%'
    },
    difficulty: 'easy',
    tags: ['SSG', 'ISR', '博客', '内容管理']
  },

  {
    completionStatus: '内容已完成',
    
    id: 'scenario-3',
    title: '企业级混合渲染架构',
    description: '大型企业应用采用微前端架构，不同模块使用不同的渲染策略：关键页面SSR，管理后台CSR，公共组件通过hydrateRoot实现一致的激活体验。',
    businessValue: '开发效率提升40%，系统可维护性增强，用户体验一致性达到95%',
    scenario: '企业门户的首页、产品展示页使用SSR以优化SEO；用户Dashboard、设置页面使用CSR以提升交互性；公共的导航栏、用户信息组件通过hydrateRoot在不同页面间保持一致的激活体验。',
    code: `// 企业级渲染策略管理器
class EnterpriseRenderManager {
  private renderStrategies = new Map();
  private sharedComponents = new Map();

  // 注册渲染策略
  registerStrategy(pageType: string, strategy: RenderStrategy) {
    this.renderStrategies.set(pageType, strategy);
  }

  // 智能选择渲染方式
  async renderPage(pageType: string, container: Element, props: any) {
    const strategy = this.renderStrategies.get(pageType);
    
    switch (strategy.type) {
      case 'SSR':
        return this.hydrateSSRPage(container, props);
      case 'CSR':
        return this.renderCSRPage(container, props);
      case 'HYBRID':
        return this.renderHybridPage(container, props);
      default:
        throw new Error('未知的渲染策略: ' + strategy.type);
    }
  }

  // SSR页面激活
  private hydrateSSRPage(container: Element, props: any) {
    // 检测服务端渲染内容
    if (container.hasChildNodes()) {
      const { component, errorHandler } = this.getPageConfig(props.pageType);
      
      const root = hydrateRoot(container, component, {
        onUncaughtError: errorHandler.onUncaughtError,
        onCaughtError: errorHandler.onCaughtError,
        onRecoverableError: (error) => {
          // 企业级错误处理
          this.reportToEnterpriseDashboard(error, {
            pageType: props.pageType,
            userId: props.user?.id,
            timestamp: Date.now()
          });
        }
      });

      // 激活公共组件
      this.hydrateSharedComponents(props);
      return root;
    } else {
      // 降级到CSR
      return this.renderCSRPage(container, props);
    }
  }

  // 激活公共组件（如导航栏、用户信息等）
  private hydrateSharedComponents(props: any) {
    // 导航栏激活
    const navbar = document.getElementById('enterprise-navbar');
    if (navbar && navbar.hasChildNodes()) {
      hydrateRoot(navbar, <EnterpriseNavbar user={props.user} />);
    }

    // 用户状态栏激活
    const userStatusBar = document.getElementById('user-status');
    if (userStatusBar && userStatusBar.hasChildNodes()) {
      hydrateRoot(userStatusBar, <UserStatusBar user={props.user} />);
    }

    // 通知中心激活
    const notificationCenter = document.getElementById('notifications');
    if (notificationCenter && notificationCenter.hasChildNodes()) {
      hydrateRoot(notificationCenter, <NotificationCenter userId={props.user.id} />);
    }
  }

  // 错误恢复和降级策略
  private handleHydrationFailure(container: Element, props: any, error: Error) {
    console.warn('Hydration失败，降级到客户端渲染:', error);
    
    // 清空容器，重新客户端渲染
    container.innerHTML = '';
    return this.renderCSRPage(container, props);
  }
}

// 页面级组件示例
function EnterpriseDashboard({ user, initialData }) {
  const [dashboardData, setDashboardData] = useState(initialData);
  const [loading, setLoading] = useState(false);

  // 企业级数据刷新
  const refreshDashboard = async () => {
    setLoading(true);
    try {
      const newData = await fetchDashboardData(user.id);
      setDashboardData(newData);
    } catch (error) {
      // 企业级错误处理
      showErrorDialog('数据刷新失败，请联系系统管理员');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="enterprise-dashboard">
      <DashboardHeader onRefresh={refreshDashboard} loading={loading} />
      <DashboardWidgets data={dashboardData} />
      <DashboardFooter />
    </div>
  );
}

// 使用示例
const renderManager = new EnterpriseRenderManager();

// 配置不同页面的渲染策略
renderManager.registerStrategy('homepage', { type: 'SSR', component: Homepage });
renderManager.registerStrategy('dashboard', { type: 'HYBRID', component: EnterpriseDashboard });
renderManager.registerStrategy('settings', { type: 'CSR', component: UserSettings });

// 根据页面类型智能渲染
renderManager.renderPage(window.pageType, document.getElementById('root'), window.pageProps);`,
    explanation: '企业级混合渲染架构通过hydrateRoot提供了灵活的渲染策略。关键业务页面通过SSR优化首屏性能和SEO，交互密集的管理界面使用CSR提升用户体验，公共组件在不同页面间保持一致的激活行为。完善的错误处理和降级机制确保系统的稳定性。',
    benefits: [
      '灵活的渲染策略，根据页面特性选择最优方案',
      '公共组件复用，减少开发成本和维护负担',
      '完善的错误处理，提高系统稳定性',
      '统一的用户体验，不同页面间无缝切换',
      '企业级监控和错误报告，便于运维管理'
    ],
    metrics: {
      performance: '关键页面首屏时间提升50%，交互页面响应速度提升30%',
      userExperience: '页面切换流畅度提升40%，用户满意度评分达到4.7/5',
      technicalMetrics: '系统稳定性99.9%，错误恢复成功率95%，开发效率提升40%'
    },
    difficulty: 'hard',
    tags: ['企业级', '混合渲染', '微前端', '错误处理']
  },

  {
    completionStatus: '内容已完成',
    
    id: 'scenario-4',
    title: '国际化多语言站点的SSR激活',
    description: '多语言电商平台需要为不同地区提供服务端渲染的本地化内容，同时在客户端激活语言切换、货币转换和本地化的交互功能。',
    businessValue: '全球用户覆盖率提升35%，本地化转化率增加45%，SEO多语言排名显著提升',
    scenario: '用户访问不同语言版本的产品页面时，服务器根据URL路径或Accept-Language头返回对应语言的HTML内容。hydrateRoot激活后，用户可以动态切换语言、货币，所有交互功能都支持本地化。',
    code: `// i18n-ssr-manager.tsx
import { hydrateRoot } from 'react-dom/client';
import { I18nextProvider } from 'react-i18next';
import i18n from './i18n-config';

interface MultiLanguageAppProps {
  locale: string;
  translations: Record<string, any>;
  currency: string;
  products: Product[];
}

// 国际化SSR组件
function MultiLanguageApp({ locale, translations, currency, products }: MultiLanguageAppProps) {
  const [currentLocale, setCurrentLocale] = useState(locale);
  const [currentCurrency, setCurrentCurrency] = useState(currency);
  const [prices, setPrices] = useState(products.map(p => p.price));

  // 动态语言切换
  const handleLanguageChange = async (newLocale: string) => {
    // 更新i18n实例
    await i18n.changeLanguage(newLocale);
    setCurrentLocale(newLocale);
    
    // 更新URL但不刷新页面
    const newUrl = window.location.pathname.replace('/' + locale, '/' + newLocale);
    window.history.pushState({}, '', newUrl);
    
    // 动态加载新语言的翻译
    const newTranslations = await loadTranslations(newLocale);
    i18n.addResourceBundle(newLocale, 'translation', newTranslations);
  };

  // 动态货币切换
  const handleCurrencyChange = async (newCurrency: string) => {
    setCurrentCurrency(newCurrency);
    
    // 实时汇率转换
    const exchangeRate = await getExchangeRate(currency, newCurrency);
    const convertedPrices = prices.map(price => price * exchangeRate);
    setPrices(convertedPrices);
  };

  // 本地化的添加购物车
  const handleAddToCart = async (productId: string, quantity: number) => {
    const cartData = {
      productId,
      quantity,
      locale: currentLocale,
      currency: currentCurrency,
      price: prices[products.findIndex(p => p.id === productId)]
    };
    
    await addToCart(cartData);
    
    // 本地化成功消息
    const message = i18n.t('cart.addSuccess', { 
      productName: i18n.t('products.' + productId + '.name')
    });
    showToast(message);
  };

  return (
    <I18nextProvider i18n={i18n}>
      <div className="multi-language-app">
        {/* 服务端渲染的语言/货币选择器 */}
        <header className="site-header">
          <LanguageSelector 
            currentLocale={currentLocale}
            onLanguageChange={handleLanguageChange}
          />
          <CurrencySelector 
            currentCurrency={currentCurrency}
            onCurrencyChange={handleCurrencyChange}
          />
        </header>

        {/* 服务端渲染的本地化产品网格 */}
        <main>
          <ProductGrid 
            products={products}
            prices={prices}
            currency={currentCurrency}
            locale={currentLocale}
            onAddToCart={handleAddToCart}
          />
        </main>

        {/* 本地化的购物车组件 */}
        <ShoppingCart 
          locale={currentLocale}
          currency={currentCurrency}
        />
      </div>
    </I18nextProvider>
  );
}

// 服务端渲染配置
export async function getServerSideProps({ params, req }) {
  const locale = params.locale || detectLocaleFromRequest(req);
  const currency = detectCurrencyFromLocale(locale);
  
  // 服务端加载翻译和产品数据
  const [translations, products] = await Promise.all([
    loadTranslations(locale),
    loadLocalizedProducts(locale)
  ]);

  // 配置服务端i18n实例
  await i18n.changeLanguage(locale);
  i18n.addResourceBundle(locale, 'translation', translations);

  return {
    props: {
      locale,
      translations,
      currency,
      products
    }
  };
}

// 客户端激活
function HydrateMultiLanguageApp() {
  useEffect(() => {
    const container = document.getElementById('root');
    if (container && container.hasChildNodes()) {
      // 获取服务端注入的初始数据
      const serverData = JSON.parse(
        document.getElementById('server-data').textContent
      );

      // 配置客户端i18n
      i18n.init({
        lng: serverData.locale,
        resources: {
          [serverData.locale]: {
            translation: serverData.translations
          }
        }
      });

      // 激活多语言应用
      hydrateRoot(
        container, 
        <MultiLanguageApp {...serverData} />,
        {
          onRecoverableError: (error) => {
            // 本地化错误处理
            if (error.message.includes('hydration')) {
              console.warn('多语言hydration警告:', error);
              // 记录但不中断用户体验
            }
          }
        }
      );
    }
  }, []);

  return null; // 该组件仅用于激活
}

export default HydrateMultiLanguageApp;`,
    explanation: '国际化SSR激活解决了多语言网站的复杂需求：服务端根据用户的语言偏好渲染对应的HTML内容，确保SEO友好和快速首屏加载；hydrateRoot激活后，用户可以无刷新地切换语言和货币，所有功能都支持动态本地化。这种架构特别适合全球化电商平台。',
    benefits: [
      '多语言SEO优化，每种语言都有独立的可索引内容',
      '用户体验优化，支持动态语言切换无需页面刷新',
      '本地化完整性，价格、日期、数字格式都正确本地化',
      '性能优化，服务端渲染减少客户端计算负担',
      '全球化支持，为不同地区用户提供原生化体验'
    ],
    metrics: {
      performance: '多语言页面首屏时间统一优化至1.2s内',
      userExperience: '语言切换响应时间<200ms，本地化准确率99.5%',
      technicalMetrics: '多语言SEO覆盖率提升60%，全球CDN缓存命中率92%'
    },
    difficulty: 'hard',
    tags: ['国际化', 'i18n', 'SSR', '多语言']
  }
];

export default businessScenarios;