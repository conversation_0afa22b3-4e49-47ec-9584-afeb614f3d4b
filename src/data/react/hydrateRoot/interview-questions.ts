import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'hydrateRoot和createRoot的区别是什么？什么时候使用哪个API？',
    difficulty: 'easy',
    frequency: 'high',
    category: 'ReactDOM API对比',
    answer: {
      brief: 'hydrateRoot用于激活服务端渲染的HTML，createRoot用于纯客户端渲染。hydrateRoot保持现有DOM结构，createRoot会清空容器重新渲染。',
      detailed: `两个API的核心区别：

**hydrateRoot (SSR激活)**：
- 用于激活已存在的服务端渲染HTML
- 保持现有DOM结构，只附加事件和状态
- 假设DOM内容与React组件匹配
- 用于SSR、SSG应用的客户端激活

**createRoot (客户端渲染)**：
- 用于全新的客户端渲染
- 清空容器内容，重新创建DOM树
- 没有对现有内容的假设
- 用于SPA、纯客户端应用

**选择标准**：
- 有服务端渲染内容 → hydrateRoot
- 空容器或需要重新渲染 → createRoot
- Next.js、Gatsby等SSR框架 → hydrateRoot
- Create React App等纯客户端 → createRoot`,
      code: `// 检测容器内容，智能选择API
function smartRender(container, app) {
  if (container.hasChildNodes()) {
    // 有内容：使用hydrateRoot激活
    console.log('检测到服务端渲染内容，使用hydrateRoot');
    const root = hydrateRoot(container, app);
    return root;
  } else {
    // 空容器：使用createRoot渲染
    console.log('空容器，使用createRoot');
    const root = createRoot(container);
    root.render(app);
    return root;
  }
}

// Next.js中的典型使用
// pages/_app.tsx
function MyApp({ Component, pageProps }) {
  return <Component {...pageProps} />;
}

// Next.js自动调用hydrateRoot：
// hydrateRoot(document.getElementById('__next'), <MyApp />)

// Create React App中的典型使用
// index.tsx
const container = document.getElementById('root');
const root = createRoot(container); // 纯客户端渲染
root.render(<App />);`
    },
    tags: ['API对比', 'SSR vs CSR']
  },
  {
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'hydrateRoot出现hydration不匹配警告时如何排查和解决？',
    difficulty: 'medium',
    frequency: 'high',
    category: '错误处理',
    answer: {
      brief: 'hydration不匹配通常由服务端和客户端渲染差异导致。需要检查时间敏感数据、随机数、客户端特有API的使用，并采用条件渲染或useEffect进行修复。',
      detailed: `**常见不匹配原因及解决方案**：

**1. 时间相关数据不一致**
- 问题：服务端和客户端渲染时间不同
- 解决：使用固定时间戳或客户端延迟渲染

**2. 随机数据差异**
- 问题：Math.random()、ID生成等在两端结果不同
- 解决：使用确定性算法或客户端生成

**3. 客户端特有API**
- 问题：localStorage、navigator等服务端不存在
- 解决：条件渲染或useEffect中处理

**4. 环境差异**
- 问题：用户代理、屏幕尺寸等检测差异
- 解决：使用useEffect进行客户端检测

**调试策略**：
- 开发模式会显示详细的不匹配警告
- 对比服务端和客户端的DOM结构
- 使用React DevTools的"Components"tab检查
- 通过suppressHydrationWarning临时抑制警告进行定位`,
      code: `// ❌ 错误：会导致hydration不匹配
function ProblematicComponent() {
  return (
    <div>
      <p>当前时间：{new Date().toLocaleString()}</p>
      <p>随机数：{Math.random()}</p>
      <p>是否支持localStorage：{typeof localStorage !== 'undefined' ? '是' : '否'}</p>
    </div>
  );
}

// ✅ 正确：避免hydration不匹配
function FixedComponent() {
  const [clientTime, setClientTime] = useState(null);
  const [randomValue, setRandomValue] = useState(null);
  const [hasLocalStorage, setHasLocalStorage] = useState(false);

  useEffect(() => {
    // 客户端渲染时更新
    setClientTime(new Date().toLocaleString());
    setRandomValue(Math.random());
    setHasLocalStorage(typeof localStorage !== 'undefined');
  }, []);

  return (
    <div>
      <p>当前时间：{clientTime || '加载中...'}</p>
      <p>随机数：{randomValue || '生成中...'}</p>
      <p>是否支持localStorage：{hasLocalStorage ? '是' : '否'}</p>
    </div>
  );
}

// 使用suppressHydrationWarning抑制特定警告
function TimeComponent() {
  return (
    <div suppressHydrationWarning>
      {new Date().toLocaleString()}
    </div>
  );
}

// hydrateRoot的错误处理配置
hydrateRoot(container, <App />, {
  onRecoverableError: (error) => {
    console.log('Hydration警告:', error);
    // 发送错误报告到监控系统
    sendToAnalytics('hydration_warning', { error: error.message });
  }
});`
    },
    tags: ['调试技巧', 'Hydration错误']
  },
  {
    completionStatus: '内容已完成',
    
    id: 3,
    question: '如何优化hydrateRoot的性能？在大型应用中有哪些最佳实践？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '性能优化',
    answer: {
      brief: '通过渐进式hydration、代码分割、关键渲染路径优化、Suspense边界等技术来优化hydrateRoot性能。重点是减少初始JavaScript包大小，延迟非关键组件的激活。',
      detailed: `**大型应用hydrateRoot优化策略**：

**1. 渐进式Hydration**
- 优先激活关键交互组件
- 延迟激活非视口内容
- 使用Intersection Observer按需激活

**2. 代码分割优化**
- 路由级别的代码分割
- 组件级别的懒加载
- 第三方库的动态导入

**3. 关键渲染路径优化**
- 内联关键CSS
- 预加载关键资源
- 优化首屏必需的JavaScript

**4. 服务端渲染优化**
- 流式SSR (renderToPipeableStream)
- 选择性hydration
- Suspense边界策略

**5. 缓存策略**
- 静态资源的长期缓存
- HTML的合理缓存策略
- API数据的预取和缓存`,
      code: `// 渐进式Hydration实现
import { lazy, Suspense } from 'react';

// 关键组件：立即激活
const CriticalHeader = ({ user }) => (
  <header>
    <Navigation />
    <UserProfile user={user} />
  </header>
);

// 非关键组件：懒加载
const LazyFooter = lazy(() => import('./Footer'));
const LazyRecommendations = lazy(() => import('./Recommendations'));

function App({ user, initialData }) {
  return (
    <div>
      {/* 立即激活的关键内容 */}
      <CriticalHeader user={user} />
      
      <main>
        {/* 主要内容：立即激活 */}
        <ProductList products={initialData.products} />
        
        {/* 非关键内容：延迟激活 */}
        <Suspense fallback={<div>加载推荐内容...</div>}>
          <LazyRecommendations userId={user.id} />
        </Suspense>
      </main>
      
      {/* 页脚：延迟激活 */}
      <Suspense fallback={<div>加载页脚...</div>}>
        <LazyFooter />
      </Suspense>
    </div>
  );
}

// 基于可见性的条件激活
function ConditionalHydration({ children, threshold = 0.1 }) {
  const [shouldHydrate, setShouldHydrate] = useState(false);
  const ref = useRef();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setShouldHydrate(true);
          observer.disconnect();
        }
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold]);

  return (
    <div ref={ref}>
      {shouldHydrate ? children : <div>等待激活...</div>}
    </div>
  );
}

// 生产环境优化配置
function optimizedHydrateRoot(container, app) {
  return hydrateRoot(container, app, {
    // 错误边界处理
    onRecoverableError: (error) => {
      // 只在开发环境打印，生产环境静默处理
      if (process.env.NODE_ENV === 'development') {
        console.warn('Hydration warning:', error);
      }
    },
    
    // 错误恢复
    onUncaughtError: (error) => {
      // 发送到错误监控系统
      if (typeof window !== 'undefined' && window.Sentry) {
        window.Sentry.captureException(error);
      }
    }
  });
}

// Next.js中的优化配置
// next.config.js
module.exports = {
  experimental: {
    reactRoot: true,
    streaming: true, // 启用流式SSR
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production', // 生产环境移除console
  }
};`
    },
    tags: ['性能优化', '大型应用']
  }
];

export default interviewQuestions;