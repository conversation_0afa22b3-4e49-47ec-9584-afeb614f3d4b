import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  completionStatus: '内容已完成',
  
  definition: "hydrateRoot是ReactDOM中用于在客户端激活服务端渲染HTML的API，它能够将静态HTML转换为可交互的React应用。",
  
  introduction: `hydrateRoot是React 18引入的客户端渲染API，主要用于服务端渲染(SSR)场景、Next.js应用和静态站点生成(SSG)。它采用渐进增强的设计模式，提供了从静态HTML到动态React应用的无缝过渡能力。`,

  syntax: `import { hydrateRoot } from 'react-dom/client';

hydrateRoot(domNode, reactNode, options?)`,

  quickExample: `function HydrateExample() {
  // 检测是否有服务端渲染的内容
  const container = document.getElementById('root');
  
  if (container && container.hasChildNodes()) {
    // 激活服务端渲染的HTML
    hydrateRoot(container, <App />);
  } else {
    // 降级到客户端渲染
    const root = createRoot(container);
    root.render(<App />);
  }
}`,

  scenarioDiagram: `graph TD
    A[SSR渲染场景] --> B[服务端渲染]
    A --> C[静态站点生成]
    A --> D[混合渲染]

    B --> B1[Next.js SSR]
    B --> B2[Node.js服务器]
    B --> B3[边缘计算渲染]

    C --> C1[Gatsby构建]
    C --> C2[预渲染页面]
    C --> C3[静态HTML激活]

    D --> D1[部分页面SSR]
    D --> D2[渐进式水合]
    D --> D3[客户端回退]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "domNode",
      type: "Element",
      required: true,
      description: "要激活的DOM元素，必须包含服务端渲染的HTML内容",
      example: "document.getElementById('root')"
    },
    {
      name: "reactNode",
      type: "ReactNode",
      description: "要渲染的React组件树，必须与服务端渲染的内容匹配",
      example: "<App />",
      required: true
    },
    {
      name: "options",
      type: "HydrateRootOptions",
      description: "可选的配置选项",
      required: false,
      details: "包含错误处理、标识符前缀等配置"
    }
  ],
  
  returnValue: {
    type: "RootType",
    description: "返回一个根对象，包含render和unmount方法",
    example: "const root = hydrateRoot(container, <App />); root.render(<NewApp />);"
  },
  
  keyFeatures: [
    {
      title: "SSR激活",
      description: "将服务端渲染的静态HTML转换为可交互的React应用",
      benefit: "首屏加载快速，SEO友好，用户体验优良"
    },
    {
      title: "Hydration不匹配检测",
      description: "自动检测服务端和客户端渲染内容的差异",
      benefit: "开发时提供警告，生产时优雅处理不匹配"
    },
    {
      title: "并发特性支持",
      description: "支持React 18的并发渲染和Suspense特性",
      benefit: "激活后享受现代React的所有性能优势"
    },
    {
      title: "错误恢复机制",
      description: "当hydration失败时能够优雅降级到客户端渲染",
      benefit: "提高应用的鲁棒性和容错能力"
    }
  ],
  
  limitations: [
    "只能用于包含服务端渲染HTML的DOM节点",
    "服务端和客户端的React组件树必须完全匹配",
    "不支持IE等不兼容并发特性的浏览器",
    "Hydration过程可能存在延迟，影响首次交互时间"
  ],
  
  bestPractices: [
    "确保服务端和客户端使用相同的React版本和组件代码",
    "避免在组件初始化时使用客户端特有的API（如Math.random、Date.now）",
    "使用Suspense和Error Boundary处理异步内容和错误",
    "在生产环境配置适当的错误处理和监控",
    "优先考虑关键渲染路径的内容进行服务端渲染"
  ],
  
  warnings: [
    "hydrateRoot会假设DOM内容与React组件匹配，不匹配时会有性能损失",
    "避免在hydration期间进行DOM操作，这可能导致不一致性错误",
    "在开发模式下会有额外的警告和检查，影响性能表现"
  ]
};

export default basicInfo;