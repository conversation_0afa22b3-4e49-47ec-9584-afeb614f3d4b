import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: 'hydrateRoot激活失败，页面无法交互怎么办？',
    answer: `hydrateRoot激活失败通常有几个常见原因和解决方案：

**1. JavaScript加载失败**
检查网络连接和JavaScript文件是否正确加载。使用浏览器开发者工具查看Network面板。

**2. 组件树不匹配**
确保服务端和客户端渲染的组件结构完全一致，特别注意条件渲染的逻辑。

**3. 错误处理机制**
配置错误回调函数，当hydration失败时自动降级到客户端渲染。

**4. 调试方法**
- 开启React开发模式查看详细错误信息
- 使用React DevTools检查组件状态
- 对比服务端HTML和客户端期望的结构`,
    code: `// 配置hydrateRoot的错误处理
function safeHydrateRoot(container, app) {
  try {
    return hydrateRoot(container, app, {
      onUncaughtError: (error) => {
        console.error('Hydration错误:', error);
        // 降级到客户端渲染
        fallbackToCSR(container, app);
      },
      onRecoverableError: (error) => {
        console.warn('Hydration警告:', error);
        // 记录但继续运行
      }
    });
  } catch (error) {
    console.error('hydrateRoot初始化失败:', error);
    // 立即降级
    return fallbackToCSR(container, app);
  }
}

// 降级策略：清空容器，使用createRoot
function fallbackToCSR(container, app) {
  console.log('降级到客户端渲染');
  container.innerHTML = ''; // 清空服务端内容
  const root = createRoot(container);
  root.render(app);
  return root;
}

// 使用示例
const container = document.getElementById('root');
const root = safeHydrateRoot(container, <App />);`,
    tags: ['错误处理', '降级策略'],
    relatedQuestions: ['hydrateRoot和createRoot的区别', 'hydration不匹配如何解决']
  },
  {
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '什么情况下应该使用hydrateRoot而不是createRoot？',
    answer: `选择hydrateRoot还是createRoot主要看是否有服务端渲染内容：

**使用hydrateRoot的场景：**
- Next.js、Nuxt.js等SSR框架
- Gatsby、VuePress等静态站点生成器
- 自建的服务端渲染应用
- 需要SEO优化的页面
- 首屏性能要求较高的应用

**使用createRoot的场景：**
- Create React App等纯客户端应用
- 管理后台、Dashboard等内部系统
- 不需要SEO的应用
- 空白页面开始构建的SPA

**判断标准：**
1. 容器是否包含服务端渲染的HTML？
2. 是否需要SEO优化？
3. 首屏加载性能是否重要？
4. 是否已有服务端渲染基础设施？

如果以上问题的答案是"是"，就选择hydrateRoot；否则选择createRoot。`,
    code: `// 智能检测容器内容并选择合适的API
function smartReactMount(containerId, app) {
  const container = document.getElementById(containerId);
  
  if (!container) {
    throw new Error('找不到容器元素: ' + containerId);
  }
  
  // 检测容器是否有内容
  const hasContent = container.hasChildNodes() && 
    container.innerHTML.trim().length > 0;
  
  if (hasContent) {
    console.log('检测到服务端渲染内容，使用hydrateRoot');
    return hydrateRoot(container, app);
  } else {
    console.log('空容器，使用createRoot');
    const root = createRoot(container);
    root.render(app);
    return root;
  }
}

// 框架检测：根据运行环境自动选择
function detectFrameworkAndMount(app) {
  // Next.js环境
  if (typeof window !== 'undefined' && window.__NEXT_DATA__) {
    const container = document.getElementById('__next');
    return hydrateRoot(container, app);
  }
  
  // Gatsby环境
  if (typeof window !== 'undefined' && window.___gatsby) {
    const container = document.getElementById('___gatsby');
    return hydrateRoot(container, app);
  }
  
  // 默认：Create React App
  const container = document.getElementById('root');
  const root = createRoot(container);
  root.render(app);
  return root;
}`,
    tags: ['使用场景', 'API选择'],
    relatedQuestions: ['SSR和CSR的区别', 'Next.js中如何使用hydrateRoot']
  },
  {
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: 'hydrateRoot激活很慢，如何优化性能？',
    answer: `hydrateRoot性能优化可以从多个层面入手：

**1. 减少初始JavaScript包大小**
- 使用代码分割，只加载必需的代码
- 延迟加载非关键组件
- 移除不必要的第三方库

**2. 优化服务端渲染**
- 使用流式SSR减少TTFB
- 选择性渲染关键内容
- 压缩HTML输出

**3. 渐进式激活**
- 优先激活视口内的组件
- 使用Intersection Observer延迟激活
- 分批次激活不同优先级的组件

**4. 缓存优化**
- 静态资源使用长期缓存
- HTML使用适当的缓存策略
- 预加载关键资源

**5. 监控和分析**
- 使用React DevTools Profiler
- 测量激活时间
- 分析瓶颈组件`,
    code: `// 性能优化的hydrateRoot实现
import { unstable_trace as trace } from 'scheduler/tracing';

// 1. 渐进式激活策略
function ProgressiveHydration({ children, priority = 'normal' }) {
  const [shouldHydrate, setShouldHydrate] = useState(priority === 'high');
  
  useEffect(() => {
    if (priority === 'low') {
      // 延迟激活低优先级组件
      const timer = setTimeout(() => setShouldHydrate(true), 100);
      return () => clearTimeout(timer);
    } else if (priority === 'normal') {
      // 使用requestIdleCallback
      const handle = requestIdleCallback(() => setShouldHydrate(true));
      return () => cancelIdleCallback(handle);
    }
  }, [priority]);
  
  return shouldHydrate ? children : <div>等待激活...</div>;
}

// 2. 性能监控的激活函数
function monitoredHydrateRoot(container, app) {
  const startTime = performance.now();
  
  const root = hydrateRoot(container, app, {
    onUncaughtError: (error) => {
      console.error('Hydration错误:', error);
      // 记录性能数据
      const duration = performance.now() - startTime;
      analytics.track('hydration_error', { duration, error: error.message });
    }
  });
  
  // 激活完成后记录性能
  requestAnimationFrame(() => {
    const duration = performance.now() - startTime;
    console.log('Hydration耗时:', duration, 'ms');
    analytics.track('hydration_complete', { duration });
  });
  
  return root;
}

// 3. 代码分割优化
const LazyDashboard = lazy(() => 
  import(/* webpackChunkName: "dashboard" */ './Dashboard')
);

function App() {
  return (
    <div>
      {/* 关键内容：立即激活 */}
      <ProgressiveHydration priority="high">
        <Header />
      </ProgressiveHydration>
      
      {/* 非关键内容：延迟激活 */}
      <ProgressiveHydration priority="low">
        <Suspense fallback={<div>加载中...</div>}>
          <LazyDashboard />
        </Suspense>
      </ProgressiveHydration>
    </div>
  );
}`,
    tags: ['性能优化', '渐进式激活'],
    relatedQuestions: ['React代码分割最佳实践', 'SSR性能优化指南']
  }
];

export default commonQuestions;