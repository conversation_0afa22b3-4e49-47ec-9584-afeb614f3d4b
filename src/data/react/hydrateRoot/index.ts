import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const hydrateRootData: ApiItem = {
  id: 'hydrateRoot',
  title: 'hydrateRoot',
  description: 'React 18用于在客户端激活服务端渲染HTML的API，将静态HTML转换为可交互的React应用',
  category: 'ReactDOM',
  difficulty: 'medium',
  
  syntax: `import { hydrateRoot } from 'react-dom/client';
hydrateRoot(domNode, reactNode, options?)`,
  example: `const container = document.getElementById('root');
if (container && container.hasChildNodes()) {
  hydrateRoot(container, <App />);
}`,
  notes: '仅用于激活服务端渲染的HTML，服务端和客户端组件树必须匹配',
  
  version: 'React 18.0+',
  tags: ["ReactDOM", "SSR", "Hydration", "React 18"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default hydrateRootData;