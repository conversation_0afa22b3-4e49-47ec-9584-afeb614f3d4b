import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
  
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'useActionState 和 useState 有什么区别？什么时候应该使用 useActionState？',
    difficulty: 'easy',
    frequency: 'high',
    category: 'API基础',
    answer: {
      brief: 'useActionState专门用于管理表单和异步操作状态，自动处理pending、error状态，而useState是通用的状态管理Hook。',
      detailed: `useActionState和useState的主要区别：

**设计目的不同：**
- useState：通用状态管理，适用于各种数据类型和使用场景
- useActionState：专门为表单提交和异步操作设计，内置状态机模式

**状态结构不同：**
- useState：用户完全控制状态结构，通常是简单值
- useActionState：返回复合状态，包含用户数据、pending标志、错误信息

**更新机制不同：**
- useState：直接设置新值，需要手动管理异步状态
- useActionState：通过Action函数更新，自动管理异步生命周期

**使用场景：**
- 选择useActionState：表单提交、数据获取、文件上传等异步操作
- 选择useState：简单的组件状态、计数器、开关状态等

**集成能力：**
- useActionState天然支持Server Actions和FormData
- useState需要额外的封装来处理复杂的异步逻辑`,
      code: `// useState - 传统方式
function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      await loginUser({ email, password });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <input 
        value={email}
        onChange={(e) => setEmail(e.target.value)}
      />
      <input 
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
      />
      <button disabled={loading}>
        {loading ? 'Logging in...' : 'Login'}
      </button>
      {error && <div className="error">{error}</div>}
    </form>
  );
}

// useActionState - 简化方式
function LoginForm() {
  const [state, formAction] = useActionState(loginAction, {
    error: null,
    success: false,
    user: null
  });
  
  return (
    <form action={formAction}>
      <input name="email" required />
      <input name="password" type="password" required />
      <button disabled={state.pending}>
        {state.pending ? 'Logging in...' : 'Login'}
      </button>
      {state.error && <div className="error">{state.error}</div>}
    </form>
  );
}

async function loginAction(prevState, formData) {
  try {
    const user = await loginUser({
      email: formData.get('email'),
      password: formData.get('password')
    });
    return { error: null, success: true, user };
  } catch (error) {
    return { error: error.message, success: false, user: null };
  }
}`
    },
    tags: ['状态管理', 'API对比']
  },
  {
  
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'useActionState 的 Action 函数有哪些要求？如何正确处理错误？',
    difficulty: 'medium',
    frequency: 'high',
    category: 'API使用',
    answer: {
      brief: 'Action函数必须接收(prevState, formData)参数，返回新状态。错误处理通过try-catch捕获，并在返回状态中包含错误信息。',
      detailed: `Action函数的要求和最佳实践：

**函数签名要求：**
- 必须接收两个参数：prevState（前一个状态）和formData（表单数据）
- 可以是异步函数（推荐）或同步函数
- 必须返回新的状态对象

**错误处理策略：**
1. **同步错误**：使用try-catch包装同步操作
2. **异步错误**：在async函数中使用try-catch
3. **验证错误**：在Action开始时进行数据验证
4. **网络错误**：特殊处理网络请求失败

**状态返回规范：**
- 成功时：返回包含新数据的状态对象
- 失败时：返回包含错误信息的状态对象
- 保持状态结构一致性

**性能考虑：**
- 避免在Action中进行昂贵的计算
- 合理使用缓存和防抖
- 考虑用户体验，提供适当的反馈`,
      code: `// ✅ 正确的Action函数实现
async function updateUserAction(prevState, formData) {
  // 1. 数据验证
  const name = formData.get('name');
  const email = formData.get('email');
  
  if (!name || name.trim().length < 2) {
    return {
      ...prevState,
      error: '姓名至少需要2个字符',
      success: false
    };
  }
  
  if (!email || !email.includes('@')) {
    return {
      ...prevState,
      error: '请输入有效的邮箱地址',
      success: false
    };
  }
  
  // 2. 异步操作错误处理
  try {
    const updatedUser = await updateUser({
      id: prevState.user.id,
      name: name.trim(),
      email: email.toLowerCase()
    });
    
    return {
      error: null,
      success: true,
      user: updatedUser,
      message: '用户信息更新成功'
    };
  } catch (error) {
    // 3. 详细错误分类处理
    if (error.code === 'EMAIL_EXISTS') {
      return {
        ...prevState,
        error: '该邮箱已被其他用户使用',
        success: false
      };
    }
    
    if (error.code === 'NETWORK_ERROR') {
      return {
        ...prevState,
        error: '网络连接失败，请检查网络后重试',
        success: false
      };
    }
    
    return {
      ...prevState,
      error: error.message || '更新失败，请稍后重试',
      success: false
    };
  }
}

// ❌ 错误的Action函数实现
async function badAction(formData) { // 错误：参数不完整
  const data = formData.get('data');
  
  // 错误：没有错误处理
  const result = await riskyOperation(data);
  
  // 错误：没有返回完整状态
  return result;
}

// 🔧 处理复杂验证的Action
async function complexValidationAction(prevState, formData) {
  const formValues = {
    name: formData.get('name'),
    email: formData.get('email'),
    age: parseInt(formData.get('age'))
  };
  
  // 使用验证库
  const validation = validateUserData(formValues);
  if (!validation.valid) {
    return {
      ...prevState,
      errors: validation.errors, // 返回详细的字段错误
      success: false
    };
  }
  
  try {
    const result = await saveUser(formValues);
    return {
      errors: null,
      success: true,
      user: result,
      timestamp: Date.now()
    };
  } catch (error) {
    return {
      ...prevState,
      errors: { general: error.message },
      success: false
    };
  }
}`
    },
    tags: ['错误处理', 'Action函数']
  },
  {
  
    completionStatus: '内容已完成',
    
    id: 3,
    question: '如何在 useActionState 中实现复杂的表单验证和字段级错误处理？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '高级应用',
    answer: {
      brief: '通过在Action函数中实现多层验证，在状态中存储字段级错误信息，并在组件中根据错误信息渲染对应的验证提示。',
      detailed: `复杂表单验证的实现策略：

**验证层次结构：**
1. **客户端验证**：HTML5验证、实时验证
2. **Action验证**：业务规则验证、数据格式验证
3. **服务器验证**：唯一性检查、权限验证

**错误状态设计：**
- 使用嵌套的错误对象结构
- 区分字段级错误和表单级错误
- 支持多种错误类型（必填、格式、业务规则）

**实时验证集成：**
- 结合useActionState和本地状态
- 实现防抖和节流优化
- 提供即时的用户反馈

**用户体验优化：**
- 渐进式错误显示
- 错误恢复机制
- 无障碍访问支持

**性能考虑：**
- 避免过度验证
- 合理使用缓存
- 优化重新渲染`,
      code: `// 🎯 完整的复杂表单验证实现
import { useState, useActionState } from 'react';

// 验证规则定义
const validationRules = {
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入有效的邮箱地址'
  },
  password: {
    required: true,
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    message: '密码必须包含大小写字母和数字，至少8位'
  },
  confirmPassword: {
    required: true,
    match: 'password',
    message: '确认密码必须与密码一致'
  },
  name: {
    required: true,
    minLength: 2,
    maxLength: 50,
    message: '姓名长度必须在2-50个字符之间'
  }
};

// 验证函数
function validateField(fieldName, value, allValues = {}) {
  const rules = validationRules[fieldName];
  if (!rules) return null;
  
  if (rules.required && (!value || value.trim() === '')) {
    return \`\${fieldName}为必填项\`;
  }
  
  if (rules.minLength && value.length < rules.minLength) {
    return \`\${fieldName}长度不能少于\${rules.minLength}个字符\`;
  }
  
  if (rules.maxLength && value.length > rules.maxLength) {
    return \`\${fieldName}长度不能超过\${rules.maxLength}个字符\`;
  }
  
  if (rules.pattern && !rules.pattern.test(value)) {
    return rules.message;
  }
  
  if (rules.match && value !== allValues[rules.match]) {
    return rules.message;
  }
  
  return null;
}

function validateAllFields(values) {
  const errors = {};
  
  Object.keys(validationRules).forEach(fieldName => {
    const error = validateField(fieldName, values[fieldName], values);
    if (error) {
      errors[fieldName] = error;
    }
  });
  
  return {
    valid: Object.keys(errors).length === 0,
    errors
  };
}

// 复杂表单Action
async function complexFormAction(prevState, formData) {
  const formValues = {
    name: formData.get('name'),
    email: formData.get('email'),
    password: formData.get('password'),
    confirmPassword: formData.get('confirmPassword')
  };
  
  // 1. 基础验证
  const validation = validateAllFields(formValues);
  if (!validation.valid) {
    return {
      ...prevState,
      fieldErrors: validation.errors,
      globalError: null,
      success: false
    };
  }
  
  // 2. 业务规则验证
  try {
    // 检查邮箱唯一性
    const emailExists = await checkEmailExists(formValues.email);
    if (emailExists) {
      return {
        ...prevState,
        fieldErrors: { email: '该邮箱已被注册' },
        globalError: null,
        success: false
      };
    }
    
    // 3. 创建用户
    const user = await createUser({
      name: formValues.name,
      email: formValues.email,
      password: formValues.password
    });
    
    return {
      fieldErrors: {},
      globalError: null,
      success: true,
      user: user,
      message: '注册成功！'
    };
    
  } catch (error) {
    // 4. 服务器错误处理
    if (error.code === 'VALIDATION_ERROR') {
      return {
        ...prevState,
        fieldErrors: error.fieldErrors || {},
        globalError: error.message,
        success: false
      };
    }
    
    return {
      ...prevState,
      fieldErrors: {},
      globalError: '注册失败，请稍后重试',
      success: false
    };
  }
}

// 复杂表单组件
function ComplexForm() {
  const [state, formAction] = useActionState(complexFormAction, {
    fieldErrors: {},
    globalError: null,
    success: false,
    user: null
  });
  
  // 客户端实时验证状态
  const [clientErrors, setClientErrors] = useState({});
  const [touched, setTouched] = useState({});
  
  const handleBlur = (fieldName, value) => {
    setTouched(prev => ({ ...prev, [fieldName]: true }));
    const error = validateField(fieldName, value);
    setClientErrors(prev => ({
      ...prev,
      [fieldName]: error
    }));
  };
  
  const getFieldError = (fieldName) => {
    return state.fieldErrors[fieldName] || 
           (touched[fieldName] ? clientErrors[fieldName] : null);
  };
  
  if (state.success) {
    return (
      <div className="success-message">
        <h2>注册成功！</h2>
        <p>欢迎 {state.user.name}！</p>
      </div>
    );
  }
  
  return (
    <form action={formAction} className="complex-form">
      <h2>用户注册</h2>
      
      {state.globalError && (
        <div className="global-error" role="alert">
          {state.globalError}
        </div>
      )}
      
      <div className="form-group">
        <label htmlFor="name">姓名</label>
        <input
          id="name"
          name="name"
          type="text"
          onBlur={(e) => handleBlur('name', e.target.value)}
          className={getFieldError('name') ? 'error' : ''}
          aria-describedby={getFieldError('name') ? 'name-error' : undefined}
        />
        {getFieldError('name') && (
          <div id="name-error" className="field-error" role="alert">
            {getFieldError('name')}
          </div>
        )}
      </div>
      
      <div className="form-group">
        <label htmlFor="email">邮箱</label>
        <input
          id="email"
          name="email"
          type="email"
          onBlur={(e) => handleBlur('email', e.target.value)}
          className={getFieldError('email') ? 'error' : ''}
          aria-describedby={getFieldError('email') ? 'email-error' : undefined}
        />
        {getFieldError('email') && (
          <div id="email-error" className="field-error" role="alert">
            {getFieldError('email')}
          </div>
        )}
      </div>
      
      <div className="form-group">
        <label htmlFor="password">密码</label>
        <input
          id="password"
          name="password"
          type="password"
          onBlur={(e) => handleBlur('password', e.target.value)}
          className={getFieldError('password') ? 'error' : ''}
          aria-describedby={getFieldError('password') ? 'password-error' : undefined}
        />
        {getFieldError('password') && (
          <div id="password-error" className="field-error" role="alert">
            {getFieldError('password')}
          </div>
        )}
      </div>
      
      <div className="form-group">
        <label htmlFor="confirmPassword">确认密码</label>
        <input
          id="confirmPassword"
          name="confirmPassword"
          type="password"
          onBlur={(e) => handleBlur('confirmPassword', e.target.value)}
          className={getFieldError('confirmPassword') ? 'error' : ''}
          aria-describedby={getFieldError('confirmPassword') ? 'confirm-password-error' : undefined}
        />
        {getFieldError('confirmPassword') && (
          <div id="confirm-password-error" className="field-error" role="alert">
            {getFieldError('confirmPassword')}
          </div>
        )}
      </div>
      
      <button 
        type="submit" 
        disabled={state.pending}
        className="submit-button"
      >
        {state.pending ? '注册中...' : '注册'}
      </button>
    </form>
  );
}`
    },
    tags: ['表单验证', '错误处理', '用户体验']
  }
];

export default interviewQuestions;