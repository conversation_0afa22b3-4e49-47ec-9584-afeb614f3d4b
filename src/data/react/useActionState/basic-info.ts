import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  completionStatus: '内容已完成',
  
  definition: "useActionState是React 19中用于管理表单和异步Action状态的Hook",
  
  introduction: `useActionState是React 19引入的新Hook，主要用于表单提交状态管理、异步Action执行和Server Actions集成。它采用状态机的设计模式，提供了自动的pending、error、success状态管理，简化了复杂表单的状态处理逻辑。`,

  syntax: `const [state, formAction, isPending] = useActionState(action, initialState, permalink?)`,

  quickExample: `function ContactForm() {
  // 使用useActionState管理表单提交状态
  const [state, formAction] = useActionState(submitContact, {
    pending: false,
    error: null,
    success: false,
    data: null
  });

  return (
    <form action={formAction}>
      {/* 表单输入字段 */}
      <input 
        name="name" 
        placeholder="姓名"
        required 
      />
      <input 
        name="email" 
        type="email" 
        placeholder="邮箱"
        required 
      />
      <textarea 
        name="message" 
        placeholder="留言内容"
        required 
      />
      
      {/* 提交按钮显示loading状态 */}
      <button type="submit" disabled={state.pending}>
        {state.pending ? '提交中...' : '提交'}
      </button>
      
      {/* 错误和成功状态显示 */}
      {state.error && (
        <div className="error">{state.error}</div>
      )}
      {state.success && (
        <div className="success">提交成功！</div>
      )}
    </form>
  );
}`,

  scenarioDiagram: `graph TD
    A[用户交互场景] --> B[表单提交]
    A --> C[数据更新]
    A --> D[文件上传]

    B --> B1[登录表单]
    B --> B2[注册表单]
    B --> B3[联系表单]

    C --> C1[用户资料更新]
    C --> C2[设置修改]
    C --> C3[内容发布]

    D --> D1[头像上传]
    D --> D2[文档上传]
    D --> D3[批量文件上传]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "action",
      type: "Function",
      required: true,
      description: "异步函数，接收prevState和formData参数，返回新状态",
      example: "async (prevState, formData) => { /* 处理逻辑 */ }"
    },
    {
      name: "initialState",
      type: "any",
      required: true,
      description: "初始状态值，通常包含pending、error、data等字段",
      example: "{ pending: false, error: null, data: null }"
    },
    {
      name: "permalink",
      type: "string",
      required: false,
      description: "可选的永久链接，用于表单提交后的URL更新",
      example: "/success"
    }
  ],
  
  returnValue: {
    type: "[state, formAction, isPending]",
    description: "返回数组：当前状态、表单动作函数、是否正在处理的布尔值",
    example: "const [state, formAction, isPending] = useActionState(action, initialState)"
  },
  
  keyFeatures: [
    {
      title: "自动状态管理",
      description: "自动管理pending、error、success等状态，无需手动维护",
      benefit: "减少样板代码，降低状态管理复杂度"
    },
    {
      title: "表单集成",
      description: "直接与HTML表单action属性集成，支持渐进式增强",
      benefit: "提供更好的用户体验和无JavaScript降级支持"
    },
    {
      title: "Server Actions支持",
      description: "原生支持React 19的Server Actions特性",
      benefit: "简化客户端-服务器交互，减少网络请求代码"
    },
    {
      title: "错误处理",
      description: "内置错误处理机制，自动捕获和管理异步操作错误",
      benefit: "提高应用健壮性，改善错误用户体验"
    }
  ],
  
  limitations: [
    "仅在React 19及以上版本可用",
    "需要与异步函数或Server Actions配合使用",
    "状态结构需要预先定义，不如useState灵活",
    "在复杂状态转换场景下可能需要额外的状态管理库"
  ],
  
  bestPractices: [
    "为初始状态定义清晰的数据结构，包含所有必要字段",
    "在action函数中进行适当的错误处理和验证",
    "使用TypeScript定义状态和action的类型以提高类型安全",
    "合理使用loading状态来改善用户体验",
    "考虑使用permalink参数来改善SEO和用户导航体验"
  ],
  
  warnings: [
    "action函数必须是异步函数，否则可能导致状态更新异常",
    "避免在action函数中直接修改外部状态，应通过返回值更新状态",
    "注意防止表单重复提交，可使用pending状态禁用提交按钮"
  ]
};

export default basicInfo;