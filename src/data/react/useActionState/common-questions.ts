import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: 'useActionState 中的 pending 状态是如何自动管理的？',
    answer: `useActionState 会自动管理 pending 状态，无需手动设置。当 Action 函数被调用时，pending 自动变为 true；当 Action 执行完成（无论成功或失败）时，pending 自动变为 false。

**自动管理的机制：**
1. **触发阶段**：当用户提交表单或调用 formAction 时，React 自动设置 pending = true
2. **执行阶段**：Action 函数执行期间，pending 保持 true 状态
3. **完成阶段**：无论 Action 成功返回还是抛出错误，pending 都会自动重置为 false

**优势：**
- 减少样板代码，不需要手动管理 loading 状态
- 确保状态的一致性，避免忘记重置 pending 状态
- 简化异步操作的 UI 反馈逻辑`,
    code: `function AutoPendingExample() {
  const [state, formAction] = useActionState(submitAction, {
    message: '',
    error: null
  });

  // ✅ pending 状态自动管理，无需手动设置
  return (
    <form action={formAction}>
      <input name="data" placeholder="输入数据" required />
      
      {/* 自动显示 pending 状态 */}
      <button type="submit" disabled={state.pending}>
        {state.pending ? '提交中...' : '提交'}
      </button>
      
      {/* 在 pending 期间禁用其他操作 */}
      {state.pending && (
        <div className="loading-overlay">
          <LoadingSpinner />
          <p>正在处理，请稍候...</p>
        </div>
      )}
      
      {state.error && (
        <div className="error">{state.error}</div>
      )}
      
      {state.message && (
        <div className="success">{state.message}</div>
      )}
    </form>
  );
}

async function submitAction(prevState, formData) {
  // 在这个函数执行期间，pending 自动为 true
  const data = formData.get('data');
  
  try {
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 返回结果时，pending 自动变为 false
    return {
      message: '提交成功！',
      error: null
    };
  } catch (error) {
    // 即使发生错误，pending 也会自动变为 false
    return {
      message: '',
      error: error.message
    };
  }
}`,
    tags: ['pending状态', '自动管理'],
    relatedQuestions: ['如何监听 pending 状态变化？', '可以手动控制 pending 状态吗？']
  },
  {
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: 'useActionState 可以与 Server Actions 一起使用吗？如何配置？',
    answer: `是的，useActionState 是专门为 React 19 的 Server Actions 特性设计的，可以完美配合使用。

**配置要求：**
1. 需要 React 19 或更高版本
2. 需要支持 Server Actions 的 React 框架（如 Next.js 13.4+）
3. 需要在文件顶部添加 'use server' 指令（对于 Server Actions）

**工作原理：**
- Server Actions 在服务器端执行，自动处理数据序列化
- useActionState 在客户端管理状态，与服务器端 Action 通信
- 表单数据通过 FormData 自动传输到服务器

**优势：**
- 减少客户端 JavaScript 代码
- 提高安全性，敏感操作在服务器端执行
- 支持渐进式增强和 SSR`,
    code: `// 📄 actions/user.js (Server Actions)
'use server';

import { redirect } from 'next/navigation';
import { createUser } from '@/lib/database';

export async function createUserAction(prevState, formData) {
  const userData = {
    name: formData.get('name'),
    email: formData.get('email'),
    password: formData.get('password')
  };
  
  // 服务器端验证
  if (!userData.email || !userData.email.includes('@')) {
    return {
      error: '请输入有效的邮箱地址',
      success: false
    };
  }
  
  try {
    // 在服务器端创建用户
    const user = await createUser(userData);
    
    // 可以直接重定向到其他页面
    redirect('/dashboard');
    
  } catch (error) {
    return {
      error: '创建用户失败',
      success: false
    };
  }
}

// 📄 components/SignupForm.js (Client Component)
'use client';

import { useActionState } from 'react';
import { createUserAction } from '@/actions/user';

export default function SignupForm() {
  const [state, formAction] = useActionState(createUserAction, {
    error: null,
    success: false
  });

  return (
    <form action={formAction}>
      <div>
        <label htmlFor="name">姓名</label>
        <input 
          id="name" 
          name="name" 
          type="text" 
          required 
          disabled={state.pending}
        />
      </div>
      
      <div>
        <label htmlFor="email">邮箱</label>
        <input 
          id="email" 
          name="email" 
          type="email" 
          required 
          disabled={state.pending}
        />
      </div>
      
      <div>
        <label htmlFor="password">密码</label>
        <input 
          id="password" 
          name="password" 
          type="password" 
          required 
          disabled={state.pending}
        />
      </div>
      
      <button type="submit" disabled={state.pending}>
        {state.pending ? '创建中...' : '创建账户'}
      </button>
      
      {state.error && (
        <div className="error" role="alert">
          {state.error}
        </div>
      )}
    </form>
  );
}

// 📄 更复杂的 Server Action 示例
'use server';

export async function uploadFileAction(prevState, formData) {
  const file = formData.get('file');
  
  if (!file || file.size === 0) {
    return {
      error: '请选择要上传的文件',
      success: false
    };
  }
  
  try {
    // 在服务器端处理文件上传
    const buffer = await file.arrayBuffer();
    const filename = \`uploads/\${Date.now()}-\${file.name}\`;
    
    // 保存文件到服务器
    await fs.writeFile(filename, Buffer.from(buffer));
    
    // 更新数据库
    const fileRecord = await saveFileRecord({
      filename,
      originalName: file.name,
      size: file.size,
      mimeType: file.type
    });
    
    return {
      error: null,
      success: true,
      file: fileRecord
    };
  } catch (error) {
    return {
      error: '文件上传失败',
      success: false
    };
  }
}`,
    tags: ['Server Actions', '服务器端'],
    relatedQuestions: ['如何在 Next.js 中使用 useActionState？', '服务器端验证怎么处理？']
  },
  {
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: '如何在 useActionState 中处理复杂的状态结构和嵌套数据？',
    answer: `useActionState 支持任意复杂的状态结构，包括嵌套对象、数组和复合状态。关键是保持状态结构的一致性和使用不可变更新模式。

**状态设计原则：**
1. **结构一致性**：初始状态和返回状态保持相同的结构
2. **不可变更新**：使用展开运算符创建新对象，避免直接修改
3. **类型安全**：使用 TypeScript 定义状态接口
4. **逻辑分离**：将复杂逻辑拆分到辅助函数中

**处理技巧：**
- 使用嵌套的状态对象组织相关数据
- 合理使用数组存储列表数据
- 通过 ID 索引管理动态数据
- 保持状态的可序列化性`,
    code: `// 🎯 复杂状态结构示例
interface ComplexState {
  // 用户相关状态
  user: {
    profile: {
      name: string;
      email: string;
      avatar: string | null;
    };
    preferences: {
      theme: 'light' | 'dark';
      notifications: boolean;
      language: string;
    };
  } | null;
  
  // 表单相关状态
  form: {
    currentStep: number;
    completedSteps: number[];
    data: {
      personal: Record<string, any>;
      contact: Record<string, any>;
      additional: Record<string, any>;
    };
    errors: {
      [fieldName: string]: string;
    };
  };
  
  // 应用状态
  ui: {
    loading: boolean;
    message: string | null;
    modal: {
      open: boolean;
      type: string | null;
      data: any;
    };
  };
  
  // 数据集合
  items: Array<{
    id: string;
    name: string;
    status: 'active' | 'inactive';
    metadata: Record<string, any>;
  }>;
}

function ComplexStateForm() {
  const [state, formAction] = useActionState(complexAction, {
    user: null,
    form: {
      currentStep: 1,
      completedSteps: [],
      data: {
        personal: {},
        contact: {},
        additional: {}
      },
      errors: {}
    },
    ui: {
      loading: false,
      message: null,
      modal: {
        open: false,
        type: null,
        data: null
      }
    },
    items: []
  });

  return (
    <div className="complex-form">
      {/* 用户信息显示 */}
      {state.user && (
        <div className="user-info">
          <h3>欢迎，{state.user.profile.name}</h3>
          <p>主题：{state.user.preferences.theme}</p>
        </div>
      )}
      
      {/* 步骤指示器 */}
      <div className="steps">
        {[1, 2, 3].map(step => (
          <div 
            key={step}
            className={\`step \${
              step === state.form.currentStep ? 'active' : ''
            } \${
              state.form.completedSteps.includes(step) ? 'completed' : ''
            }\`}
          >
            步骤 {step}
          </div>
        ))}
      </div>
      
      <form action={formAction}>
        <input type="hidden" name="action" value="updateForm" />
        <input type="hidden" name="step" value={state.form.currentStep} />
        
        {/* 根据当前步骤渲染不同内容 */}
        {state.form.currentStep === 1 && (
          <PersonalInfoStep 
            data={state.form.data.personal}
            errors={state.form.errors}
          />
        )}
        
        {state.form.currentStep === 2 && (
          <ContactInfoStep 
            data={state.form.data.contact}
            errors={state.form.errors}
          />
        )}
        
        {state.form.currentStep === 3 && (
          <AdditionalInfoStep 
            data={state.form.data.additional}
            errors={state.form.errors}
          />
        )}
        
        <div className="form-actions">
          {state.form.currentStep > 1 && (
            <button 
              type="submit" 
              name="action" 
              value="previousStep"
              disabled={state.ui.loading}
            >
              上一步
            </button>
          )}
          
          <button 
            type="submit" 
            name="action" 
            value={state.form.currentStep < 3 ? 'nextStep' : 'submit'}
            disabled={state.ui.loading}
          >
            {state.ui.loading ? '处理中...' : 
             state.form.currentStep < 3 ? '下一步' : '提交'}
          </button>
        </div>
      </form>
      
      {/* 项目列表 */}
      {state.items.length > 0 && (
        <div className="items-list">
          <h3>已添加的项目</h3>
          {state.items.map(item => (
            <div key={item.id} className="item">
              <span>{item.name}</span>
              <span className={\`status \${item.status}\`}>
                {item.status}
              </span>
            </div>
          ))}
        </div>
      )}
      
      {/* 消息显示 */}
      {state.ui.message && (
        <div className="message">
          {state.ui.message}
        </div>
      )}
    </div>
  );
}

// 复杂状态处理的 Action 函数
async function complexAction(prevState, formData) {
  const action = formData.get('action');
  const step = parseInt(formData.get('step') || '1');
  
  switch (action) {
    case 'nextStep':
      return handleNextStep(prevState, formData, step);
    
    case 'previousStep':
      return handlePreviousStep(prevState, step);
    
    case 'updateForm':
      return handleFormUpdate(prevState, formData, step);
    
    case 'addItem':
      return handleAddItem(prevState, formData);
    
    case 'removeItem':
      return handleRemoveItem(prevState, formData);
    
    default:
      return prevState;
  }
}

// 处理步骤前进的辅助函数
function handleNextStep(prevState, formData, currentStep) {
  // 提取当前步骤的数据
  const stepData = extractStepData(formData, currentStep);
  
  // 验证当前步骤数据
  const errors = validateStepData(stepData, currentStep);
  if (Object.keys(errors).length > 0) {
    return {
      ...prevState,
      form: {
        ...prevState.form,
        errors
      },
      ui: {
        ...prevState.ui,
        message: '请修正表单中的错误'
      }
    };
  }
  
  // 更新状态：保存数据并前进到下一步
  const stepKey = getStepKey(currentStep);
  return {
    ...prevState,
    form: {
      ...prevState.form,
      currentStep: Math.min(currentStep + 1, 3),
      completedSteps: [...prevState.form.completedSteps, currentStep],
      data: {
        ...prevState.form.data,
        [stepKey]: {
          ...prevState.form.data[stepKey],
          ...stepData
        }
      },
      errors: {}
    },
    ui: {
      ...prevState.ui,
      message: null
    }
  };
}

// 处理添加项目的辅助函数
function handleAddItem(prevState, formData) {
  const itemName = formData.get('itemName');
  const itemType = formData.get('itemType');
  
  if (!itemName) {
    return {
      ...prevState,
      ui: {
        ...prevState.ui,
        message: '请输入项目名称'
      }
    };
  }
  
  const newItem = {
    id: \`item-\${Date.now()}\`,
    name: itemName,
    status: 'active',
    metadata: {
      type: itemType,
      createdAt: new Date().toISOString()
    }
  };
  
  return {
    ...prevState,
    items: [...prevState.items, newItem],
    ui: {
      ...prevState.ui,
      message: \`已添加项目：\${itemName}\`
    }
  };
}`,
    tags: ['复杂状态', '嵌套数据'],
    relatedQuestions: ['如何优化复杂状态的性能？', 'TypeScript 如何定义 useActionState 的类型？']
  }
];

export default commonQuestions;