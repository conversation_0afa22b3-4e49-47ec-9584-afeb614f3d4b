import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useActionStateData: ApiItem = {
  id: 'useActionState',
  title: 'useActionState',
  description: 'React 19新增Hook，用于管理表单提交和异步Action状态，提供pending、error、data状态管理',
  category: 'React Hooks',
  difficulty: 'medium',
  
  syntax: `useActionState(action, initialState, permalink?)`,
  example: `function LoginForm() {
  const [state, formAction] = useActionState(loginAction, { 
    pending: false, 
    error: null, 
    data: null 
  });

  return (
    <form action={formAction}>
      <input name="email" type="email" required />
      <input name="password" type="password" required />
      <button type="submit" disabled={state.pending}>
        {state.pending ? '登录中...' : '登录'}
      </button>
      {state.error && <p className="error">{state.error}</p>}
    </form>
  );
}`,
  notes: '仅在React 19+中可用，需要与Server Actions或异步函数配合使用，自动管理表单提交状态',
  
  version: 'React 19.0+',
  tags: ["React 19", "Hook", "Forms", "Actions"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useActionStateData;