import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {

  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'useActionState 在使用过程中可能遇到的典型问题、错误原因分析和解决方案。掌握这些调试技巧能帮助你快速定位和解决问题，提高开发效率。',
        sections: [
          {
            title: '状态管理相关问题',
            description: '涉及状态更新、数据流和组件重渲染的常见问题',
            items: [
              {
                title: 'Action函数返回状态后组件不更新',
                description: '最常见的问题之一，通常是由于状态对象引用没有改变，或者状态结构不一致导致的',
                solution: '确保 Action 函数返回新的状态对象，使用展开运算符创建新引用，保持状态结构一致性',
                prevention: '始终使用不可变更新模式，避免直接修改状态对象，使用 TypeScript 确保类型安全',
                code: `// ❌ 错误：直接修改状态对象
async function badAction(prevState, formData) {
  prevState.user.name = formData.get('name'); // 修改了原对象
  return prevState; // 引用没变，不会触发更新
}

// ✅ 正确：创建新的状态对象
async function goodAction(prevState, formData) {
  return {
    ...prevState,
    user: {
      ...prevState.user,
      name: formData.get('name')
    }
  };
}

// 🔍 调试技巧：使用 console.log 检查状态变化
async function debugAction(prevState, formData) {
  const newState = {
    ...prevState,
    user: {
      ...prevState.user,
      name: formData.get('name')
    }
  };
  
  console.log('Previous state:', prevState);
  console.log('New state:', newState);
  console.log('References equal:', prevState === newState); // 应该是 false
  
  return newState;
}`
              },
              {
                title: 'pending 状态显示不正确',
                description: 'pending 状态不会自动变为 true，或者在 Action 完成后仍然保持 true',
                solution: '检查 Action 函数是否正确返回状态，确保没有忘记处理异常情况，验证表单提交方式是否正确',
                prevention: '使用 try-catch 包装 Action 函数，确保所有代码路径都返回状态，避免在 Action 中抛出未处理的异常',
                code: `// ❌ 错误：Action函数抛出异常，pending状态可能卡住
async function riskyAction(prevState, formData) {
  const data = await fetchData(); // 可能抛出异常
  return { ...prevState, data };
}

// ✅ 正确：完整的错误处理
async function safeAction(prevState, formData) {
  try {
    const data = await fetchData();
    return {
      ...prevState,
      data,
      error: null,
      success: true
    };
  } catch (error) {
    return {
      ...prevState,
      error: error.message,
      success: false
    };
  }
}

// 🔍 调试 pending 状态
function DebugForm() {
  const [state, formAction] = useActionState(safeAction, {
    data: null,
    error: null,
    success: false
  });
  
  // 添加调试信息
  console.log('Current pending state:', state.pending);
  
  return (
    <form action={formAction}>
      <button type="submit" disabled={state.pending}>
        {state.pending ? 'Loading...' : 'Submit'}
      </button>
      
      {/* 显示调试信息 */}
      <div style={{ fontSize: '12px', color: '#666' }}>
        Pending: {String(state.pending)} | 
        Success: {String(state.success)} | 
        Error: {state.error || 'None'}
      </div>
    </form>
  );
}`
              },
              {
                title: 'FormData 数据获取为空或错误',
                description: '在 Action 函数中无法正确获取表单数据，或者获取到的数据格式不正确',
                solution: '检查表单字段的 name 属性，确保 FormData 键名正确，验证表单提交方式，处理文件和复杂数据类型',
                prevention: '使用一致的命名约定，添加表单数据验证，使用 TypeScript 定义 FormData 接口',
                code: `// 🔍 调试 FormData 的实用函数
function debugFormData(formData) {
  console.log('FormData entries:');
  for (const [key, value] of formData.entries()) {
    console.log(\`\${key}:\`, value);
  }
  
  // 转换为普通对象便于查看
  const obj = Object.fromEntries(formData.entries());
  console.log('FormData as object:', obj);
  
  return obj;
}

// ✅ 完整的 Action 函数调试
async function debugAction(prevState, formData) {
  // 1. 调试 FormData
  console.log('=== FormData Debug ===');
  const debugData = debugFormData(formData);
  
  // 2. 验证必需字段
  const requiredFields = ['name', 'email', 'message'];
  const missingFields = requiredFields.filter(field => !formData.get(field));
  
  if (missingFields.length > 0) {
    console.error('Missing required fields:', missingFields);
    return {
      ...prevState,
      error: \`请填写必需字段：\${missingFields.join(', ')}\`
    };
  }
  
  // 3. 处理特殊数据类型
  const file = formData.get('avatar');
  if (file && file instanceof File) {
    console.log('File details:', {
      name: file.name,
      size: file.size,
      type: file.type
    });
  }
  
  // 4. 返回调试信息
  return {
    ...prevState,
    debugInfo: debugData,
    success: true
  };
}

// 🔍 表单调试组件
function DebugFormData() {
  const [state, formAction] = useActionState(debugAction, {
    debugInfo: null,
    success: false,
    error: null
  });
  
  return (
    <div>
      <form action={formAction}>
        <input name="name" placeholder="姓名" required />
        <input name="email" type="email" placeholder="邮箱" required />
        <textarea name="message" placeholder="消息" required />
        <input name="avatar" type="file" accept="image/*" />
        <button type="submit">提交</button>
      </form>
      
      {/* 显示调试信息 */}
      {state.debugInfo && (
        <pre style={{ background: '#f5f5f5', padding: '10px' }}>
          {JSON.stringify(state.debugInfo, null, 2)}
        </pre>
      )}
    </div>
  );
}`
              }
            ]
          },
          {
            title: 'Server Actions 集成问题',
            description: '与 React 19 Server Actions 集成时遇到的特殊问题',
            items: [
              {
                title: 'Server Action 函数序列化错误',
                description: 'Server Actions 无法正确序列化，或者在客户端-服务器通信中出现错误',
                solution: '确保 Server Action 文件包含 \'use server\' 指令，检查函数参数和返回值的可序列化性，验证 Next.js 版本兼容性',
                prevention: '遵循 Server Actions 最佳实践，避免在 Action 中使用不可序列化的对象，使用类型检查',
                code: `// ❌ 错误：缺少 'use server' 指令
export async function serverAction(prevState, formData) {
  // 这不是有效的 Server Action
}

// ✅ 正确：包含 'use server' 指令
'use server';

export async function serverAction(prevState, formData) {
  // 有效的 Server Action
  return { success: true };
}

// 🔍 调试 Server Actions
'use server';

export async function debugServerAction(prevState, formData) {
  console.log('Server Action executed at:', new Date().toISOString());
  console.log('Previous state:', prevState);
  
  // 验证服务器环境
  if (typeof window !== 'undefined') {
    console.error('Server Action running on client side!');
  }
  
  try {
    // 服务器端逻辑
    const result = await processOnServer(formData);
    
    return {
      ...prevState,
      result,
      serverTimestamp: Date.now(),
      success: true
    };
  } catch (error) {
    console.error('Server Action error:', error);
    return {
      ...prevState,
      error: error.message,
      success: false
    };
  }
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '使用现代开发工具来调试 useActionState，包括浏览器开发者工具、React DevTools 和自定义调试工具。这些工具能帮助你深入了解状态变化、性能瓶颈和错误原因。',
        sections: [
          {
            title: 'React DevTools 调试技巧',
            description: '使用 React DevTools 深入分析 useActionState 的状态变化和性能',
            items: [
              {
                title: 'Hook 状态追踪',
                description: '在 React DevTools 中实时查看 useActionState 的状态变化和 Hook 调用栈',
                solution: '使用 Components 面板查看 Hook 状态，利用 Profiler 分析重渲染，设置断点追踪状态变化',
                prevention: '定期使用 DevTools 检查应用状态，设置性能阈值告警，建立调试检查清单',
                code: `// 🔧 为 React DevTools 添加调试信息
function useDebugActionState(action, initialState, debugName = 'ActionState') {
  const [state, formAction] = useActionState(action, initialState);
  
  // 在 DevTools 中显示自定义名称
  useDebugValue(state, (state) => ({
    pending: state.pending,
    hasError: !!state.error,
    dataKeys: Object.keys(state).filter(key => !['pending', 'error'].includes(key))
  }));
  
  // 记录状态变化历史
  const stateHistory = useRef([]);
  useEffect(() => {
    stateHistory.current.push({
      timestamp: Date.now(),
      state: JSON.parse(JSON.stringify(state))
    });
    
    // 保留最近10次状态变化
    if (stateHistory.current.length > 10) {
      stateHistory.current.shift();
    }
    
    // 在控制台中显示状态变化
    console.log(\`[\${debugName}] State changed:\`, state);
  }, [state, debugName]);
  
  // 暴露调试方法到全局
  useEffect(() => {
    window.debugActionState = {
      getCurrentState: () => state,
      getStateHistory: () => stateHistory.current,
      clearHistory: () => { stateHistory.current = []; }
    };
  }, [state]);
  
  return [state, formAction];
}

// 📊 使用示例
function MyForm() {
  const [state, formAction] = useDebugActionState(
    myAction, 
    { data: null }, 
    'UserForm'
  );
  
  return (
    <form action={formAction}>
      {/* 表单内容 */}
    </form>
  );
}`
              },
              {
                title: '性能分析和瓶颈诊断',
                description: '使用 React DevTools Profiler 分析 useActionState 导致的性能问题',
                solution: '启用 Profiler 记录，分析渲染时间和频率，识别不必要的重渲染，优化状态结构',
                prevention: '设置性能预算，定期进行性能测试，使用 React.memo 和 useMemo 优化',
                code: `// 📈 性能监控组件
import { Profiler } from 'react';

function PerformanceMonitor({ children, id }) {
  const handleRender = useCallback((
    id,
    phase,
    actualDuration,
    baseDuration,
    startTime,
    commitTime,
    interactions
  ) => {
    // 记录性能数据
    const perfData = {
      id,
      phase,
      actualDuration,
      baseDuration,
      startTime,
      commitTime,
      interactions: interactions ? Array.from(interactions) : []
    };
    
    // 发送到性能监控服务
    if (actualDuration > 16) { // 超过一帧的时间
      console.warn('Slow render detected:', perfData);
    }
    
    // 存储到本地用于分析
    const perfHistory = JSON.parse(
      localStorage.getItem('perfHistory') || '[]'
    );
    perfHistory.push(perfData);
    
    // 保留最近100条记录
    if (perfHistory.length > 100) {
      perfHistory.shift();
    }
    
    localStorage.setItem('perfHistory', JSON.stringify(perfHistory));
  }, []);
  
  return (
    <Profiler id={id} onRender={handleRender}>
      {children}
    </Profiler>
  );
}

// 🔍 性能分析工具
function PerformanceAnalyzer() {
  const [analysis, setAnalysis] = useState(null);
  
  const analyzePerformance = () => {
    const history = JSON.parse(localStorage.getItem('perfHistory') || '[]');
    
    const analysis = {
      totalRenders: history.length,
      avgDuration: history.reduce((sum, item) => sum + item.actualDuration, 0) / history.length,
      slowRenders: history.filter(item => item.actualDuration > 16),
      phases: history.reduce((acc, item) => {
        acc[item.phase] = (acc[item.phase] || 0) + 1;
        return acc;
      }, {})
    };
    
    setAnalysis(analysis);
  };
  
  return (
    <div>
      <button onClick={analyzePerformance}>分析性能</button>
      {analysis && (
        <pre style={{ background: '#f0f0f0', padding: '10px' }}>
          {JSON.stringify(analysis, null, 2)}
        </pre>
      )}
    </div>
  );
}`
              }
            ]
          },
          {
            title: '自定义调试工具',
            description: '创建专门的调试工具来监控 useActionState 的行为',
            items: [
              {
                title: 'Action 执行日志记录器',
                description: '记录所有 Action 函数的执行过程，包括输入参数、执行时间和返回结果',
                solution: '创建 Action 包装器记录执行日志，实现可视化的日志查看器，支持过滤和搜索',
                prevention: '在开发环境中默认启用日志，生产环境中可选择性启用，定期清理日志文件',
                code: `// 📝 Action 日志记录器
class ActionLogger {
  constructor() {
    this.logs = [];
    this.listeners = [];
  }
  
  log(entry) {
    this.logs.push({
      ...entry,
      timestamp: Date.now(),
      id: Math.random().toString(36)
    });
    
    // 通知监听器
    this.listeners.forEach(listener => listener(entry));
    
    // 保留最近1000条日志
    if (this.logs.length > 1000) {
      this.logs.shift();
    }
  }
  
  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }
  
  getLogs(filter = {}) {
    return this.logs.filter(log => {
      if (filter.actionName && log.actionName !== filter.actionName) {
        return false;
      }
      if (filter.status && log.status !== filter.status) {
        return false;
      }
      if (filter.minDuration && log.duration < filter.minDuration) {
        return false;
      }
      return true;
    });
  }
  
  clear() {
    this.logs = [];
  }
}

const actionLogger = new ActionLogger();

// 🔧 创建记录 Action 的高阶函数
function withLogging(actionName, action) {
  return async (prevState, formData) => {
    const startTime = performance.now();
    
    actionLogger.log({
      actionName,
      type: 'start',
      prevState: JSON.parse(JSON.stringify(prevState)),
      formData: Object.fromEntries(formData.entries())
    });
    
    try {
      const result = await action(prevState, formData);
      const endTime = performance.now();
      
      actionLogger.log({
        actionName,
        type: 'success',
        duration: endTime - startTime,
        result: JSON.parse(JSON.stringify(result)),
        status: 'success'
      });
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      
      actionLogger.log({
        actionName,
        type: 'error',
        duration: endTime - startTime,
        error: error.message,
        status: 'error'
      });
      
      throw error;
    }
  };
}

// 📊 日志查看器组件
function ActionLogViewer() {
  const [logs, setLogs] = useState([]);
  const [filter, setFilter] = useState({ actionName: '', status: '' });
  
  useEffect(() => {
    const unsubscribe = actionLogger.subscribe(() => {
      setLogs([...actionLogger.getLogs(filter)]);
    });
    
    return unsubscribe;
  }, [filter]);
  
  return (
    <div style={{ fontFamily: 'monospace', fontSize: '12px' }}>
      <div style={{ marginBottom: '10px' }}>
        <input
          placeholder="过滤 Action 名称"
          value={filter.actionName}
          onChange={(e) => setFilter(prev => ({
            ...prev,
            actionName: e.target.value
          }))}
        />
        <select
          value={filter.status}
          onChange={(e) => setFilter(prev => ({
            ...prev,
            status: e.target.value
          }))}
        >
          <option value="">所有状态</option>
          <option value="success">成功</option>
          <option value="error">错误</option>
        </select>
        <button onClick={() => actionLogger.clear()}>
          清空日志
        </button>
      </div>
      
      <div style={{ maxHeight: '400px', overflow: 'auto' }}>
        {logs.map(log => (
          <div 
            key={log.id}
            style={{
              padding: '5px',
              borderBottom: '1px solid #eee',
              backgroundColor: log.status === 'error' ? '#ffe6e6' : '#f9f9f9'
            }}
          >
            <div>
              <strong>{log.actionName}</strong> - {log.type}
              {log.duration && \` (\${log.duration.toFixed(2)}ms)\`}
            </div>
            <div style={{ color: '#666', fontSize: '11px' }}>
              {new Date(log.timestamp).toLocaleTimeString()}
            </div>
            {log.error && (
              <div style={{ color: 'red' }}>{log.error}</div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

// 使用示例
const loggedAction = withLogging('userRegistration', registrationAction);

function MyForm() {
  const [state, formAction] = useActionState(loggedAction, initialState);
  
  return (
    <div>
      <form action={formAction}>
        {/* 表单内容 */}
      </form>
      
      {/* 在开发环境中显示日志查看器 */}
      {process.env.NODE_ENV === 'development' && (
        <ActionLogViewer />
      )}
    </div>
  );
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;