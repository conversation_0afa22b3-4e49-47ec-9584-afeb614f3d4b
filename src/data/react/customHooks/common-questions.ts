import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'hook-rules-violation',
    question: '为什么我的自定义Hook会出现"Invalid hook call"错误？',
    answer: `## Hook调用规则违反

这个错误通常由以下几种情况引起：

### 1. 在条件语句中调用Hook

\`\`\`tsx
// ❌ 错误：在条件语句中调用Hook
function MyComponent({ shouldUseCustom }) {
  if (shouldUseCustom) {
    const data = useCustomHook(); // 违反Hook规则
    return <div>{data}</div>;
  }
  return <div>Default</div>;
}

// ✅ 正确：始终调用Hook
function MyComponent({ shouldUseCustom }) {
  const data = useCustomHook();
  
  if (shouldUseCustom) {
    return <div>{data}</div>;
  }
  return <div>Default</div>;
}
\`\`\`

### 2. 在循环中调用Hook

\`\`\`tsx
// ❌ 错误：在循环中调用Hook
function MyComponent({ items }) {
  const results = [];
  for (let item of items) {
    const result = useCustomHook(item); // 违反规则
    results.push(result);
  }
  return <div>{results}</div>;
}

// ✅ 正确：将Hook移到组件顶层
function MyComponent({ items }) {
  const results = items.map(item => {
    // 将每个item处理逻辑移到单独组件
    return <ItemComponent key={item.id} item={item} />;
  });
  return <div>{results}</div>;
}

function ItemComponent({ item }) {
  const result = useCustomHook(item); // 在组件顶层调用
  return <div>{result}</div>;
}
\`\`\`

### 3. 在普通函数中调用Hook

\`\`\`tsx
// ❌ 错误：在普通函数中调用Hook
function processData(data) {
  const processed = useCustomHook(data); // 不能在普通函数中调用
  return processed;
}

// ✅ 正确：只在React函数组件或自定义Hook中调用
function useProcessedData(data) {
  const processed = useCustomHook(data);
  return processed;
}

function MyComponent({ data }) {
  const processed = useProcessedData(data);
  return <div>{processed}</div>;
}
\`\`\`

### 4. Hook调用顺序不一致

\`\`\`tsx
// ❌ 错误：Hook调用顺序可能改变
function MyComponent({ condition }) {
  const [count, setCount] = useState(0);
  
  if (condition) {
    const [name, setName] = useState(''); // 有时调用，有时不调用
  }
  
  const data = useCustomHook();
  return <div>{count}</div>;
}

// ✅ 正确：确保Hook调用顺序一致
function MyComponent({ condition }) {
  const [count, setCount] = useState(0);
  const [name, setName] = useState('');
  const data = useCustomHook();
  
  return (
    <div>
      {count}
      {condition && <input value={name} onChange={(e) => setName(e.target.value)} />}
    </div>
  );
}
\`\`\`

### 解决方案总结

1. **始终在顶层调用Hook**：不要在循环、条件或嵌套函数中调用
2. **保持调用顺序一致**：确保每次渲染时Hook的调用顺序相同
3. **只在React函数或自定义Hook中调用**：不要在普通JavaScript函数中调用
4. **使用ESLint规则**：安装并启用react-hooks相关的ESLint规则`,
    tags: ['Hook规则', '错误处理', '调试'],
    relatedTopics: ['React Hook规则', 'ESLint', '组件设计']
  },

  {
    id: 'hook-state-synchronization',
    question: '如何确保多个组件之间的Hook状态保持同步？',
    answer: `Hook状态同步解决方案

方案1: Context + Provider模式

\`\`\`tsx
// 1. 创建Context
const ThemeContext = createContext();

// 2. 创建Provider组件
function ThemeProvider({ children }) {
  const [theme, setTheme] = useState('light');
  const [fontSize, setFontSize] = useState(16);
  
  const toggleTheme = useCallback(() => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  }, []);
  
  const value = useMemo(() => ({
    theme,
    fontSize,
    setTheme,
    setFontSize,
    toggleTheme
  }), [theme, fontSize, toggleTheme]);
  
  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

// 3. 创建自定义Hook
function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme必须在ThemeProvider内使用');
  }
  return context;
}

// 4. 在组件中使用
function Header() {
  const { theme, toggleTheme } = useTheme();
  return (
    <header className={theme}>
      <button onClick={toggleTheme}>切换主题</button>
    </header>
  );
}

function Sidebar() {
  const { theme, fontSize } = useTheme();
  return (
    <aside className={theme} style={{ fontSize }}>
      侧边栏内容
    </aside>
  );
}
\`\`\`

方案2: 外部状态管理器

\`\`\`tsx
// 创建简单的状态管理器
class StateManager {
  private state = {};
  private listeners = new Set();
  
  getState() {
    return this.state;
  }
  
  setState(updates) {
    this.state = { ...this.state, ...updates };
    this.notify();
  }
  
  subscribe(listener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }
  
  private notify() {
    this.listeners.forEach(listener => listener(this.state));
  }
}

const globalState = new StateManager();

// 创建订阅Hook
function useGlobalState(selector) {
  const [state, setState] = useState(() => selector(globalState.getState()));
  
  useEffect(() => {
    const unsubscribe = globalState.subscribe((newState) => {
      const selectedState = selector(newState);
      setState(selectedState);
    });
    
    return unsubscribe;
  }, [selector]);
  
  const updateState = useCallback((updates) => {
    globalState.setState(updates);
  }, []);
  
  return [state, updateState];
}

// 使用示例
function ComponentA() {
  const [userInfo, setUserInfo] = useGlobalState(state => state.user);
  
  return (
    <div>
      <p>用户: {userInfo?.name}</p>
      <button onClick={() => setUserInfo({ user: { name: 'Alice' } })}>
        更新用户
      </button>
    </div>
  );
}

function ComponentB() {
  const [userInfo] = useGlobalState(state => state.user);
  
  return <div>欢迎, {userInfo?.name}!</div>;
}
\`\`\`

性能优化建议：
1. 减少不必要的重渲染：使用React.memo和useMemo
2. 合理分割Context：避免大而全的Context
3. 选择性订阅：只订阅需要的状态片段
4. 批量更新：合并相关的状态更新`,
    tags: ['状态同步', 'Context API', '事件系统', '本地存储'],
    relatedTopics: ['useContext', '状态管理', '组件通信', '数据持久化']
  },

  {
    id: 'hook-memory-leaks',
    question: '自定义Hook中常见的内存泄漏问题有哪些？如何预防和解决？',
    answer: `## 自定义Hook内存泄漏问题

### 1. 未清理的定时器

\`\`\`tsx
// ❌ 错误：未清理定时器
function useBadTimer() {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    setInterval(() => {
      setCount(c => c + 1);
    }, 1000); // 没有清理函数，导致内存泄漏
  }, []);
  
  return count;
}

// ✅ 正确：清理定时器
function useGoodTimer() {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    const timer = setInterval(() => {
      setCount(c => c + 1);
    }, 1000);
    
    // 清理函数
    return () => clearInterval(timer);
  }, []);
  
  return count;
}
\`\`\`

### 2. 未取消的网络请求

\`\`\`tsx
// ❌ 错误：未取消的请求
function useBadApi(url) {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    fetch(url)
      .then(response => response.json())
      .then(data => setData(data)); // 组件卸载后仍可能调用
  }, [url]);
  
  return data;
}

// ✅ 正确：使用AbortController取消请求
function useGoodApi(url) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    const abortController = new AbortController();
    
    setLoading(true);
    fetch(url, { signal: abortController.signal })
      .then(response => response.json())
      .then(data => {
        setData(data);
        setLoading(false);
      })
      .catch(error => {
        if (error.name !== 'AbortError') {
          console.error('Fetch error:', error);
          setLoading(false);
        }
      });
    
    return () => {
      abortController.abort();
    };
  }, [url]);
  
  return { data, loading };
}
\`\`\`

### 3. 未移除的事件监听器

\`\`\`tsx
// ❌ 错误：未移除事件监听器
function useBadWindowSize() {
  const [size, setSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });
  
  useEffect(() => {
    const handleResize = () => {
      setSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };
    
    window.addEventListener('resize', handleResize);
    // 缺少清理函数
  }, []);
  
  return size;
}

// ✅ 正确：移除事件监听器
function useGoodWindowSize() {
  const [size, setSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });
  
  useEffect(() => {
    const handleResize = () => {
      setSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  return size;
}
\`\`\`

### 4. 组件卸载后的状态更新

\`\`\`tsx
// ❌ 错误：组件卸载后仍更新状态
function useBadAsyncData(id) {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    fetchData(id).then(result => {
      setData(result); // 组件卸载后可能仍会执行
    });
  }, [id]);
  
  return data;
}

// ✅ 正确：检查组件是否已卸载
function useGoodAsyncData(id) {
  const [data, setData] = useState(null);
  const mountedRef = useRef(true);
  
  useEffect(() => {
    fetchData(id).then(result => {
      if (mountedRef.current) {
        setData(result);
      }
    });
  }, [id]);
  
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);
  
  return data;
}
\`\`\`

### 5. 观察者模式的订阅

\`\`\`tsx
// ❌ 错误：未取消订阅
function useBadObserver(observable) {
  const [value, setValue] = useState(null);
  
  useEffect(() => {
    observable.subscribe(setValue);
    // 缺少取消订阅
  }, [observable]);
  
  return value;
}

// ✅ 正确：取消订阅
function useGoodObserver(observable) {
  const [value, setValue] = useState(null);
  
  useEffect(() => {
    const subscription = observable.subscribe(setValue);
    
    return () => {
      subscription.unsubscribe();
    };
  }, [observable]);
  
  return value;
}
\`\`\`

### 内存泄漏检测工具

\`\`\`tsx
// 内存泄漏检测Hook
function useMemoryLeakDetector(componentName) {
  const mountTimeRef = useRef(Date.now());
  const renderCountRef = useRef(0);
  
  renderCountRef.current += 1;
  
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log(\`\${componentName} mounted at, new Date(mountTimeRef.current));
      
      return () => {
        const lifeTime = Date.now() - mountTimeRef.current;
        console.log(\'使用字符串拼接代替模板字符串');
      };
    }
  }, [componentName]);
  
  // 检查异常的重复渲染
  useEffect(() => {
    if (renderCountRef.current > 100) {
      console.warn(\'使用字符串拼接代替模板字符串');
    }
  });
}

// 使用示例
function MyComponent() {
  useMemoryLeakDetector('MyComponent');
  
  // 组件逻辑
  return <div>My Component</div>;
}
\`\`\`

### 通用清理Hook

\`\`\`tsx
function useCleanup() {
  const cleanupFunctionsRef = useRef([]);
  
  const addCleanup = useCallback((cleanupFunction) => {
    cleanupFunctionsRef.current.push(cleanupFunction);
  }, []);
  
  useEffect(() => {
    return () => {
      cleanupFunctionsRef.current.forEach(cleanup => {
        try {
          cleanup();
        } catch (error) {
          console.error('Cleanup error:', error);
        }
      });
      cleanupFunctionsRef.current = [];
    };
  }, []);
  
  return addCleanup;
}

// 使用示例
function useRobustAsyncOperation() {
  const addCleanup = useCleanup();
  const [data, setData] = useState(null);
  
  const fetchData = useCallback(async (url) => {
    const abortController = new AbortController();
    
    // 注册清理函数
    addCleanup(() => {
      abortController.abort();
    });
    
    try {
      const response = await fetch(url, {
        signal: abortController.signal
      });
      const result = await response.json();
      setData(result);
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Fetch error:', error);
      }
    }
  }, [addCleanup]);
  
  return { data, fetchData };
}
\`\`\`

### 内存泄漏预防清单

1. **✅ 清理定时器**：setTimeout、setInterval
2. **✅ 取消网络请求**：使用AbortController
3. **✅ 移除事件监听器**：window、document事件
4. **✅ 取消订阅**：观察者模式、WebSocket
5. **✅ 检查组件状态**：避免卸载后状态更新
6. **✅ 清理第三方库**：地图、图表库等
7. **✅ 使用开发工具**：React DevTools Profiler

### 调试建议

1. **使用React DevTools Profiler**查看组件性能
2. **使用浏览器内存面板**监控内存使用
3. **添加console.log**跟踪生命周期
4. **使用内存泄漏检测工具**如Chrome DevTools`,
    tags: ['内存泄漏', '性能优化', '资源清理', '调试技巧'],
    relatedTopics: ['useEffect', '性能优化', '调试工具', '最佳实践']
  },

  {
    id: 'hook-testing-difficulties',
    question: '测试自定义Hook时遇到的常见困难有哪些？如何解决？',
    answer: `## 自定义Hook测试常见困难与解决方案

### 1. 异步Hook测试困难

**问题**：异步操作难以控制和验证

\`\`\`tsx
// 难以测试的异步Hook
function useAsyncData(url) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    setLoading(true);
    fetch(url)
      .then(response => response.json())
      .then(data => {
        setData(data);
        setLoading(false);
      });
  }, [url]);
  
  return { data, loading };
}

// ✅ 解决方案：使用waitFor和mock


describe('useAsyncData', () => {
  beforeEach(() => {
    global.fetch = jest.fn();
  });
  
  it('应该处理异步数据获取', async () => {
    const mockData = { id: 1, name: 'Test' };
    
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      json: async () => mockData
    });
    
    const { result } = renderHook(() => useAsyncData('/api/test'));
    
    // 初始状态
    expect(result.current.loading).toBe(true);
    expect(result.current.data).toBe(null);
    
    // 等待异步操作完成
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    expect(result.current.data).toEqual(mockData);
  });
});
\`\`\`

### 2. Context依赖测试复杂

**问题**：Hook依赖Context，测试需要Provider

\`\`\`tsx
// 依赖Context的Hook
const UserContext = createContext(null);

function useUser() {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser必须在UserProvider内使用');
  }
  return context;
}

// ✅ 解决方案：创建测试wrapper
function createTestWrapper(providerValue) {
  return function TestWrapper({ children }) {
    return (
      <UserContext.Provider value={providerValue}>
        {children}
      </UserContext.Provider>
    );
  };
}

describe('useUser', () => {
  it('应该返回用户信息', () => {
    const mockUser = { id: 1, name: 'Test User' };
    const wrapper = createTestWrapper({ user: mockUser });
    
    const { result } = renderHook(() => useUser(), { wrapper });
    
    expect(result.current.user).toEqual(mockUser);
  });
  
  it('应该在没有Provider时抛出错误', () => {
    const { result } = renderHook(() => useUser());
    
    expect(result.error).toEqual(
      new Error('useUser必须在UserProvider内使用')
    );
  });
});
\`\`\`

### 3. 副作用Mock困难

**问题**：localStorage、sessionStorage、window对象等副作用难以Mock

\`\`\`tsx
// 使用localStorage的Hook
function useLocalStorage(key, initialValue) {
  const [value, setValue] = useState(() => {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : initialValue;
  });
  
  const updateValue = useCallback((newValue) => {
    setValue(newValue);
    localStorage.setItem(key, JSON.stringify(newValue));
  }, [key]);
  
  return [value, updateValue];
}

// ✅ 解决方案：Mock localStorage
describe('useLocalStorage', () => {
  const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    clear: jest.fn()
  };
  
  beforeEach(() => {
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true
    });
    
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
  });
  
  it('应该从localStorage读取初始值', () => {
    localStorageMock.getItem.mockReturnValue('"stored value"');
    
    const { result } = renderHook(() => 
      useLocalStorage('test-key', 'default')
    );
    
    expect(result.current[0]).toBe('stored value');
    expect(localStorageMock.getItem).toHaveBeenCalledWith('test-key');
  });
  
  it('应该保存值到localStorage', () => {
    const { result } = renderHook(() => 
      useLocalStorage('test-key', 'default')
    );
    
    act(() => {
      result.current[1]('new value');
    });
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'test-key', 
      '"new value"'
    );
  });
});
\`\`\`

### 4. 复杂依赖关系测试

**问题**：Hook之间的依赖关系复杂，难以单独测试

\`\`\`tsx
// 复杂的Hook组合
function useComplexLogic(userId) {
  const { data: user } = useAsyncData(\'使用字符串拼接代替模板字符串');
  const { preferences } = useUserPreferences(user?.id);
  const { theme } = useTheme(preferences?.theme);
  
  return { user, preferences, theme };
}

// ✅ 解决方案：分层测试 + Mock子Hook
jest.mock('./useAsyncData');
jest.mock('./useUserPreferences');
jest.mock('./useTheme');

describe('useComplexLogic', () => {
  const mockUseAsyncData = useAsyncData as jest.MockedFunction<typeof useAsyncData>;
  const mockUseUserPreferences = useUserPreferences as jest.MockedFunction<typeof useUserPreferences>;
  const mockUseTheme = useTheme as jest.MockedFunction<typeof useTheme>;
  
  beforeEach(() => {
    mockUseAsyncData.mockReturnValue({ 
      data: { id: 1, name: 'Test User' }, 
      loading: false 
    });
    mockUseUserPreferences.mockReturnValue({ 
      preferences: { theme: 'dark' } 
    });
    mockUseTheme.mockReturnValue({ 
      theme: { primary: '#000' } 
    });
  });
  
  it('应该正确组合所有Hook的结果', () => {
    const { result } = renderHook(() => useComplexLogic(1));
    
    expect(result.current.user).toEqual({ id: 1, name: 'Test User' });
    expect(result.current.preferences).toEqual({ theme: 'dark' });
    expect(result.current.theme).toEqual({ primary: '#000' });
  });
});
\`\`\`

### 5. 性能测试困难

**问题**：Hook的性能特性（缓存、重新计算）难以验证

\`\`\`tsx
// 需要性能测试的Hook
function useExpensiveCalculation(input) {
  return useMemo(() => {
    // 模拟昂贵计算
    let result = 0;
    for (let i = 0; i < input * 1000000; i++) {
      result += i;
    }
    return result;
  }, [input]);
}

// ✅ 解决方案：监控函数调用次数
describe('useExpensiveCalculation performance', () => {
  let calculationCount = 0;
  
  // Mock原始计算函数
  const originalCalculation = jest.fn((input) => {
    calculationCount++;
    return input * 2; // 简化计算
  });
  
  function useTestableExpensiveCalculation(input) {
    return useMemo(() => {
      return originalCalculation(input);
    }, [input]);
  }
  
  beforeEach(() => {
    calculationCount = 0;
    originalCalculation.mockClear();
  });
  
  it('应该缓存计算结果', () => {
    const { result, rerender } = renderHook(
      ({ input }) => useTestableExpensiveCalculation(input),
      { initialProps: { input: 5 } }
    );
    
    const firstResult = result.current;
    expect(originalCalculation).toHaveBeenCalledTimes(1);
    
    // 重新渲染，但输入未变
    rerender({ input: 5 });
    
    // 应该使用缓存，不重新计算
    expect(result.current).toBe(firstResult);
    expect(originalCalculation).toHaveBeenCalledTimes(1);
    
    // 输入改变，应该重新计算
    rerender({ input: 10 });
    expect(originalCalculation).toHaveBeenCalledTimes(2);
  });
});
\`\`\`

### 6. 时序相关测试

**问题**：涉及定时器、延迟的Hook测试时机难以控制

\`\`\`tsx
// 涉及定时器的Hook
function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => clearTimeout(timer);
  }, [value, delay]);
  
  return debouncedValue;
}

// ✅ 解决方案：使用fake timers
describe('useDebounce', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });
  
  afterEach(() => {
    jest.useRealTimers();
  });
  
  it('应该延迟更新值', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 500 } }
    );
    
    expect(result.current).toBe('initial');
    
    // 更新值
    rerender({ value: 'updated', delay: 500 });
    
    // 立即检查，应该还是旧值
    expect(result.current).toBe('initial');
    
    // 快进时间
    act(() => {
      jest.advanceTimersByTime(500);
    });
    
    // 现在应该是新值
    expect(result.current).toBe('updated');
  });
});
\`\`\`

### 测试工具推荐

1. **@testing-library/react-2. **jest-mock-extended**: 增强的Mock功能
3. **msw**: Mock Service Worker，用于API Mock
4. **react-testing-library**: 组件集成测试

### 测试最佳实践

1. **分层测试**: 单元测试 + 集成测试 + 端到端测试
2. **Mock策略**: 明确什么需要Mock，什么不需要
3. **测试用例设计**: 覆盖正常流程、边界条件、错误情况
4. **性能测试**: 验证缓存和优化是否生效`,
    tags: ['单元测试', '异步测试', 'Mock', '性能测试'],
    relatedTopics: ['React Testing Library', 'Jest', '测试驱动开发', '质量保证']
  }
];

export default commonQuestions; 