import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 'local-storage-hook',
    title: '本地存储Hook',
    description: '封装localStorage操作，自动处理序列化和类型安全',
    difficulty: 'easy',
    tags: ['localStorage', '状态管理', '数据持久化'],
    estimatedTime: '15分钟',
    code: `import { useState, useEffect, useCallback } from 'react';

function useLocalStorage<T>(
  key: string, 
  initialValue: T
): [T, (value: T | ((prev: T) => T)) => void, () => void] {
  // 从localStorage读取初始值
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(\`Error reading localStorage key "\${key}":, error);
      return initialValue;
    }
  });

  // 设置值到state和localStorage
  const setValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(\`Error setting localStorage key "\${key}":, error);
    }
  }, [key, storedValue]);

  // 清除localStorage中的值
  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue);
      window.localStorage.removeItem(key);
    } catch (error) {
      console.error(\`Error removing localStorage key "\${key}":, error);
    }
  }, [key, initialValue]);

  // 监听其他标签页的localStorage变化
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key) {
        try {
          const newValue = e.newValue ? JSON.parse(e.newValue) : initialValue;
          setStoredValue(newValue);
        } catch (error) {
          console.warn(\`Error parsing localStorage change for key "\${key}":, error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue];
}

// 使用示例
function UserPreferences() {
  const [theme, setTheme, clearTheme] = useLocalStorage('theme', 'light');
  const [language, setLanguage] = useLocalStorage('language', 'zh-CN');
  const [settings, setSettings] = useLocalStorage('userSettings', {
    notifications: true,
    autoSave: false,
    fontSize: 14
  });

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const updateSettings = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className={\`app-\${theme}\`}>
      <h2>用户偏好设置</h2>
      
      <div className="preference-group">
        <label>主题模式</label>
        <button onClick={toggleTheme}>
          当前: {theme === 'light' ? '浅色' : '深色'}
        </button>
        <button onClick={clearTheme}>重置主题</button>
      </div>

      <div className="preference-group">
        <label>语言设置</label>
        <select 
          value={language} 
          onChange={(e) => setLanguage(e.target.value)}
        >
          <option value="zh-CN">中文</option>
          <option value="en-US">English</option>
          <option value="ja-JP">日本語</option>
        </select>
      </div>

      <div className="preference-group">
        <label>通知设置</label>
        <input
          type="checkbox"
          checked={settings.notifications}
          onChange={(e) => updateSettings('notifications', e.target.checked)}
        />
        <span>启用通知</span>
      </div>

      <div className="preference-group">
        <label>自动保存</label>
        <input
          type="checkbox"
          checked={settings.autoSave}
          onChange={(e) => updateSettings('autoSave', e.target.checked)}
        />
        <span>自动保存草稿</span>
      </div>

      <div className="preference-group">
        <label>字体大小: {settings.fontSize}px</label>
        <input
          type="range"
          min="12"
          max="20"
          value={settings.fontSize}
          onChange={(e) => updateSettings('fontSize', parseInt(e.target.value))}
        />
      </div>
    </div>
  );
}

export default UserPreferences;`,
    explanation: '这个Hook封装了localStorage的常用操作，提供了类型安全的数据存储，自动处理JSON序列化，并支持跨标签页同步。它简化了本地数据持久化的使用，让组件专注于业务逻辑。'
  },
  {
    id: 'api-data-fetching',
    title: 'API数据获取Hook',
    description: '封装异步数据获取逻辑，包含加载状态、错误处理和缓存机制',
    difficulty: 'medium',
    tags: ['API', '异步处理', '状态管理', '缓存'],
    estimatedTime: '25分钟',
    code: `import { useState, useEffect, useCallback, useRef } from 'react';

interface UseApiOptions<T> {
  immediate?: boolean;
  cacheTime?: number;
  retryCount?: number;
  retryDelay?: number;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
}

interface UseApiReturn<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  execute: (...args: any[]) => Promise<T | null>;
  retry: () => Promise<T | null>;
  reset: () => void;
}

// 简单的内存缓存
const apiCache = new Map<string, { data: any; timestamp: number }>();

function useApi<T = any>(
  apiFunction: (...args: any[]) => Promise<T>,
  dependencies: any[] = [],
  options: UseApiOptions<T> = {}
): UseApiReturn<T> {
  const {
    immediate = true,
    cacheTime = 5 * 60 * 1000, // 5分钟缓存
    retryCount = 3,
    retryDelay = 1000,
    onSuccess,
    onError
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const lastArgsRef = useRef<any[]>([]);
  const retryCountRef = useRef(0);
  const mountedRef = useRef(true);

  // 生成缓存键
  const generateCacheKey = useCallback((args: any[]) => {
    return JSON.stringify({ fn: apiFunction.toString(), args });
  }, [apiFunction]);

  // 检查缓存
  const checkCache = useCallback((cacheKey: string) => {
    const cached = apiCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < cacheTime) {
      return cached.data;
    }
    return null;
  }, [cacheTime]);

  // 设置缓存
  const setCache = useCallback((cacheKey: string, data: T) => {
    apiCache.set(cacheKey, { data, timestamp: Date.now() });
  }, []);

  // 执行API调用
  const execute = useCallback(async (...args: any[]): Promise<T | null> => {
    const cacheKey = generateCacheKey(args);
    
    // 检查缓存
    const cachedData = checkCache(cacheKey);
    if (cachedData) {
      setData(cachedData);
      onSuccess?.(cachedData);
      return cachedData;
    }

    setLoading(true);
    setError(null);
    lastArgsRef.current = args;

    try {
      const result = await apiFunction(...args);
      
      if (mountedRef.current) {
        setData(result);
        setError(null);
        setCache(cacheKey, result);
        onSuccess?.(result);
        retryCountRef.current = 0;
      }
      
      return result;
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error(String(err));
      
      if (mountedRef.current) {
        setError(errorObj);
        onError?.(errorObj);
      }
      
      return null;
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, [apiFunction, generateCacheKey, checkCache, setCache, onSuccess, onError]);

  // 重试逻辑
  const retry = useCallback(async (): Promise<T | null> => {
    if (retryCountRef.current < retryCount) {
      retryCountRef.current += 1;
      
      // 延迟重试
      await new Promise(resolve => setTimeout(resolve, retryDelay * retryCountRef.current));
      
      return execute(...lastArgsRef.current);
    }
    return null;
  }, [execute, retryCount, retryDelay]);

  // 重置状态
  const reset = useCallback(() => {
    setData(null);
    setLoading(false);
    setError(null);
    retryCountRef.current = 0;
  }, []);

  // 自动执行
  useEffect(() => {
    if (immediate) {
      execute();
    }
    
    return () => {
      mountedRef.current = false;
    };
  }, dependencies);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return {
    data,
    loading,
    error,
    execute,
    retry,
    reset
  };
}

// 使用示例：用户列表管理
interface User {
  id: number;
  name: string;
  email: string;
  avatar: string;
}

// 模拟API函数
const fetchUsers = async (page: number = 1, pageSize: number = 10): Promise<User[]> => {
  const response = await fetch(\`/api/users?page=\${page}&pageSize=\${pageSize}\`);
  if (!response.ok) {
    throw new Error(\`HTTP error! status: \${response.status}\`);
  }
  return response.json();
};

const fetchUserById = async (id: number): Promise<User> => {
  const response = await fetch(\`/api/users/\${id}\`);
  if (!response.ok) {
    throw new Error(\`User not found: \${id}\`);
  }
  return response.json();
};

function UserManagement() {
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);

  // 获取用户列表
  const {
    data: users,
    loading: usersLoading,
    error: usersError,
    execute: fetchUsersList,
    retry: retryUsers
  } = useApi(fetchUsers, [currentPage], {
    immediate: true,
    cacheTime: 2 * 60 * 1000, // 2分钟缓存
    onSuccess: (data) => {
      console.log(\`成功获取 \${data.length} 个用户\`);
    },
    onError: (error) => {
      console.error('获取用户列表失败:', error);
    }
  });

  // 获取单个用户详情
  const {
    data: selectedUser,
    loading: userLoading,
    error: userError,
    execute: fetchUser
  } = useApi(fetchUserById, [], {
    immediate: false
  });

  const handleUserSelect = async (userId: number) => {
    setSelectedUserId(userId);
    await fetchUser(userId);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRefresh = () => {
    fetchUsersList(currentPage);
  };

  return (
    <div className="user-management">
      <div className="header">
        <h2>用户管理</h2>
        <button onClick={handleRefresh} disabled={usersLoading}>
          {usersLoading ? '刷新中...' : '刷新'}
        </button>
      </div>

      {usersError && (
        <div className="error-banner">
          <p>加载失败: {usersError.message}</p>
          <button onClick={retryUsers}>重试</button>
        </div>
      )}

      <div className="content">
        <div className="user-list">
          <h3>用户列表</h3>
          {usersLoading ? (
            <div className="loading">加载中...</div>
          ) : users ? (
            <>
              <ul>
                {users.map(user => (
                  <li 
                    key={user.id}
                    onClick={() => handleUserSelect(user.id)}
                    className={selectedUserId === user.id ? 'selected' : ''}
                  >
                    <img src={user.avatar} alt={user.name} />
                    <div>
                      <div className="name">{user.name}</div>
                      <div className="email">{user.email}</div>
                    </div>
                  </li>
                ))}
              </ul>
              
              <div className="pagination">
                <button 
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                >
                  上一页
                </button>
                <span>第 {currentPage} 页</span>
                <button 
                  onClick={() => handlePageChange(currentPage + 1)}
                >
                  下一页
                </button>
              </div>
            </>
          ) : null}
        </div>

        <div className="user-detail">
          <h3>用户详情</h3>
          {userLoading ? (
            <div className="loading">加载用户详情中...</div>
          ) : userError ? (
            <div className="error">
              加载失败: {userError.message}
            </div>
          ) : selectedUser ? (
            <div className="user-info">
              <img src={selectedUser.avatar} alt={selectedUser.name} />
              <h4>{selectedUser.name}</h4>
              <p>{selectedUser.email}</p>
              <p>用户ID: {selectedUser.id}</p>
            </div>
          ) : (
            <div className="placeholder">请选择一个用户查看详情</div>
          )}
        </div>
      </div>
    </div>
  );
}

export default UserManagement;`,
    explanation: '这个Hook封装了API数据获取的完整流程，包括加载状态管理、错误处理、自动重试、内存缓存等功能。它让组件专注于UI渲染，而不需要处理复杂的异步逻辑。支持缓存机制可以减少不必要的网络请求，提升用户体验。'
  },
  {
    id: 'form-validation-hook',
    title: '表单验证Hook',
    description: '复杂表单验证逻辑封装，支持字段级验证、异步验证和自定义规则',
    difficulty: 'hard',
    tags: ['表单验证', '异步验证', '状态管理', '用户体验'],
    estimatedTime: '40分钟',
    code: `import { useState, useCallback, useRef, useEffect } from 'react';

// 验证规则类型定义
type ValidatorFunction = (value: any, formData: Record<string, any>) => string | Promise<string>;

interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  min?: number;
  max?: number;
  custom?: ValidatorFunction;
  message?: string;
}

interface FieldConfig {
  initialValue?: any;
  rules?: ValidationRule[];
  validateOn?: 'change' | 'blur' | 'submit';
  debounce?: number;
}

interface FormConfig {
  [fieldName: string]: FieldConfig;
}

interface FieldState {
  value: any;
  error: string;
  touched: boolean;
  validating: boolean;
}

interface UseFormReturn {
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  validating: Record<string, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
  register: (name: string) => {
    value: any;
    onChange: (e: React.ChangeEvent<any>) => void;
    onBlur: () => void;
    error: string;
    touched: boolean;
  };
  setValue: (name: string, value: any) => void;
  setError: (name: string, error: string) => void;
  clearError: (name: string) => void;
  validateField: (name: string) => Promise<boolean>;
  validateForm: () => Promise<boolean>;
  resetForm: () => void;
  handleSubmit: (onSubmit: (values: Record<string, any>) => void | Promise<void>) => (e?: React.FormEvent) => Promise<void>;
}

function useForm(config: FormConfig): UseFormReturn {
  // 初始化表单状态
  const [formState, setFormState] = useState<Record<string, FieldState>>(() => {
    const initialState: Record<string, FieldState> = {};
    Object.keys(config).forEach(fieldName => {
      initialState[fieldName] = {
        value: config[fieldName].initialValue ?? '',
        error: '',
        touched: false,
        validating: false
      };
    });
    return initialState;
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const debounceTimers = useRef<Record<string, NodeJS.Timeout>>({});
  const validationPromises = useRef<Record<string, Promise<string>>>({});

  // 内置验证器
  const validators = {
    required: (value: any): string => {
      if (value === undefined || value === null || value === '') {
        return '此字段为必填项';
      }
      return '';
    },
    
    minLength: (value: string, min: number): string => {
      if (value && value.length < min) {
        return \`最少需要\${min}个字符\`;
      }
      return '';
    },
    
    maxLength: (value: string, max: number): string => {
      if (value && value.length > max) {
        return \`最多允许\${max}个字符\`;
      }
      return '';
    },
    
    pattern: (value: string, pattern: RegExp): string => {
      if (value && !pattern.test(value)) {
        return '格式不正确';
      }
      return '';
    },
    
    min: (value: number, min: number): string => {
      if (value < min) {
        return \`最小值为\${min}\`;
      }
      return '';
    },
    
    max: (value: number, max: number): string => {
      if (value > max) {
        return \`最大值为\${max}\`;
      }
      return '';
    }
  };

  // 执行字段验证
  const validateField = useCallback(async (fieldName: string): Promise<boolean> => {
    const fieldConfig = config[fieldName];
    const fieldValue = formState[fieldName]?.value;
    
    if (!fieldConfig?.rules) return true;

    // 设置验证中状态
    setFormState(prev => ({
      ...prev,
      [fieldName]: { ...prev[fieldName], validating: true }
    }));

    let errorMessage = '';

    try {
      // 执行验证规则
      for (const rule of fieldConfig.rules) {
        if (errorMessage) break;

        // 必填验证
        if (rule.required) {
          errorMessage = validators.required(fieldValue);
          if (errorMessage && rule.message) {
            errorMessage = rule.message;
          }
        }

        // 其他验证（只在有值或必填时执行）
        if (!errorMessage && (fieldValue || rule.required)) {
          if (rule.minLength !== undefined) {
            errorMessage = validators.minLength(fieldValue, rule.minLength);
          }
          
          if (!errorMessage && rule.maxLength !== undefined) {
            errorMessage = validators.maxLength(fieldValue, rule.maxLength);
          }
          
          if (!errorMessage && rule.pattern) {
            errorMessage = validators.pattern(fieldValue, rule.pattern);
          }
          
          if (!errorMessage && rule.min !== undefined) {
            errorMessage = validators.min(Number(fieldValue), rule.min);
          }
          
          if (!errorMessage && rule.max !== undefined) {
            errorMessage = validators.max(Number(fieldValue), rule.max);
          }
          
          // 自定义验证
          if (!errorMessage && rule.custom) {
            const allValues = Object.keys(formState).reduce((acc, key) => {
              acc[key] = formState[key].value;
              return acc;
            }, {} as Record<string, any>);
            
            const customResult = await rule.custom(fieldValue, allValues);
            errorMessage = customResult;
          }
        }
      }
    } catch (error) {
      errorMessage = '验证过程中发生错误';
    }

    // 更新验证结果
    setFormState(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        error: errorMessage,
        validating: false
      }
    }));

    return !errorMessage;
  }, [config, formState]);

  // 防抖验证
  const debouncedValidate = useCallback((fieldName: string, delay: number = 300) => {
    if (debounceTimers.current[fieldName]) {
      clearTimeout(debounceTimers.current[fieldName]);
    }

    debounceTimers.current[fieldName] = setTimeout(() => {
      validateField(fieldName);
    }, delay);
  }, [validateField]);

  // 设置字段值
  const setValue = useCallback((fieldName: string, value: any) => {
    setFormState(prev => ({
      ...prev,
      [fieldName]: { ...prev[fieldName], value }
    }));

    const fieldConfig = config[fieldName];
    if (fieldConfig?.validateOn === 'change') {
      const debounceDelay = fieldConfig.debounce ?? 300;
      debouncedValidate(fieldName, debounceDelay);
    }
  }, [config, debouncedValidate]);

  // 字段注册函数
  const register = useCallback((fieldName: string) => {
    const fieldState = formState[fieldName];
    const fieldConfig = config[fieldName];

    return {
      value: fieldState?.value ?? '',
      onChange: (e: React.ChangeEvent<any>) => {
        const newValue = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
        setValue(fieldName, newValue);
      },
      onBlur: () => {
        setFormState(prev => ({
          ...prev,
          [fieldName]: { ...prev[fieldName], touched: true }
        }));

        if (fieldConfig?.validateOn === 'blur') {
          validateField(fieldName);
        }
      },
      error: fieldState?.error ?? '',
      touched: fieldState?.touched ?? false
    };
  }, [formState, config, setValue, validateField]);

  // 验证整个表单
  const validateForm = useCallback(async (): Promise<boolean> => {
    const validationResults = await Promise.all(
      Object.keys(config).map(fieldName => validateField(fieldName))
    );
    
    return validationResults.every(isValid => isValid);
  }, [config, validateField]);

  // 表单提交处理
  const handleSubmit = useCallback((onSubmit: (values: Record<string, any>) => void | Promise<void>) => {
    return async (e?: React.FormEvent) => {
      if (e) {
        e.preventDefault();
      }

      setIsSubmitting(true);

      try {
        // 标记所有字段为已触摸
        setFormState(prev => {
          const newState = { ...prev };
          Object.keys(newState).forEach(key => {
            newState[key] = { ...newState[key], touched: true };
          });
          return newState;
        });

        // 验证表单
        const isValid = await validateForm();
        
        if (isValid) {
          const values = Object.keys(formState).reduce((acc, key) => {
            acc[key] = formState[key].value;
            return acc;
          }, {} as Record<string, any>);

          await onSubmit(values);
        }
      } finally {
        setIsSubmitting(false);
      }
    };
  }, [formState, validateForm]);

  // 其他辅助方法
  const setError = useCallback((fieldName: string, error: string) => {
    setFormState(prev => ({
      ...prev,
      [fieldName]: { ...prev[fieldName], error }
    }));
  }, []);

  const clearError = useCallback((fieldName: string) => {
    setFormState(prev => ({
      ...prev,
      [fieldName]: { ...prev[fieldName], error: '' }
    }));
  }, []);

  const resetForm = useCallback(() => {
    setFormState(() => {
      const initialState: Record<string, FieldState> = {};
      Object.keys(config).forEach(fieldName => {
        initialState[fieldName] = {
          value: config[fieldName].initialValue ?? '',
          error: '',
          touched: false,
          validating: false
        };
      });
      return initialState;
    });
  }, [config]);

  // 计算派生状态
  const values = Object.keys(formState).reduce((acc, key) => {
    acc[key] = formState[key].value;
    return acc;
  }, {} as Record<string, any>);

  const errors = Object.keys(formState).reduce((acc, key) => {
    acc[key] = formState[key].error;
    return acc;
  }, {} as Record<string, string>);

  const touched = Object.keys(formState).reduce((acc, key) => {
    acc[key] = formState[key].touched;
    return acc;
  }, {} as Record<string, boolean>);

  const validating = Object.keys(formState).reduce((acc, key) => {
    acc[key] = formState[key].validating;
    return acc;
  }, {} as Record<string, boolean>);

  const isValid = Object.values(formState).every(field => !field.error);

  // 清理定时器
  useEffect(() => {
    return () => {
      Object.values(debounceTimers.current).forEach(timer => {
        if (timer) clearTimeout(timer);
      });
    };
  }, []);

  return {
    values,
    errors,
    touched,
    validating,
    isValid,
    isSubmitting,
    register,
    setValue,
    setError,
    clearError,
    validateField,
    validateForm,
    resetForm,
    handleSubmit
  };
}

// 使用示例：用户注册表单
function UserRegistrationForm() {
  // 异步验证：检查用户名是否已存在
  const checkUsernameAvailability = async (username: string): Promise<string> => {
    if (!username) return '';
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const existingUsers = ['admin', 'user', 'test'];
    if (existingUsers.includes(username.toLowerCase())) {
      return '用户名已存在';
    }
    
    return '';
  };

  const form = useForm({
    username: {
      initialValue: '',
      rules: [
        { required: true, message: '用户名不能为空' },
        { minLength: 3, message: '用户名至少3个字符' },
        { maxLength: 20, message: '用户名最多20个字符' },
        { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
        { custom: checkUsernameAvailability }
      ],
      validateOn: 'blur',
      debounce: 500
    },
    email: {
      initialValue: '',
      rules: [
        { required: true, message: '邮箱不能为空' },
        { pattern: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/, message: '邮箱格式不正确' }
      ],
      validateOn: 'blur'
    },
    password: {
      initialValue: '',
      rules: [
        { required: true, message: '密码不能为空' },
        { minLength: 8, message: '密码至少8个字符' },
        { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, message: '密码必须包含大小写字母和数字' }
      ],
      validateOn: 'blur'
    },
    confirmPassword: {
      initialValue: '',
      rules: [
        { required: true, message: '请确认密码' },
        { 
          custom: (value, formData) => {
            if (value !== formData.password) {
              return '两次输入的密码不一致';
            }
            return '';
          }
        }
      ],
      validateOn: 'blur'
    },
    age: {
      initialValue: '',
      rules: [
        { required: true, message: '年龄不能为空' },
        { min: 18, message: '年龄不能小于18岁' },
        { max: 120, message: '年龄不能大于120岁' }
      ],
      validateOn: 'blur'
    },
    acceptTerms: {
      initialValue: false,
      rules: [
        { 
          custom: (value) => {
            if (!value) {
              return '请同意服务条款';
            }
            return '';
          }
        }
      ],
      validateOn: 'change'
    }
  });

  const onSubmit = async (values: Record<string, any>) => {
    console.log('提交表单:', values);
    
    // 模拟API提交
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    alert('注册成功！');
    form.resetForm();
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="registration-form">
      <h2>用户注册</h2>

      <div className="form-field">
        <label>用户名 *</label>
        <input
          type="text"
          {...form.register('username')}
          className={form.errors.username ? 'error' : ''}
        />
        {form.validating.username && <span className="validating">验证中...</span>}
        {form.touched.username && form.errors.username && (
          <span className="error-message">{form.errors.username}</span>
        )}
      </div>

      <div className="form-field">
        <label>邮箱 *</label>
        <input
          type="email"
          {...form.register('email')}
          className={form.errors.email ? 'error' : ''}
        />
        {form.touched.email && form.errors.email && (
          <span className="error-message">{form.errors.email}</span>
        )}
      </div>

      <div className="form-field">
        <label>密码 *</label>
        <input
          type="password"
          {...form.register('password')}
          className={form.errors.password ? 'error' : ''}
        />
        {form.touched.password && form.errors.password && (
          <span className="error-message">{form.errors.password}</span>
        )}
      </div>

      <div className="form-field">
        <label>确认密码 *</label>
        <input
          type="password"
          {...form.register('confirmPassword')}
          className={form.errors.confirmPassword ? 'error' : ''}
        />
        {form.touched.confirmPassword && form.errors.confirmPassword && (
          <span className="error-message">{form.errors.confirmPassword}</span>
        )}
      </div>

      <div className="form-field">
        <label>年龄 *</label>
        <input
          type="number"
          {...form.register('age')}
          className={form.errors.age ? 'error' : ''}
        />
        {form.touched.age && form.errors.age && (
          <span className="error-message">{form.errors.age}</span>
        )}
      </div>

      <div className="form-field checkbox-field">
        <label>
          <input
            type="checkbox"
            {...form.register('acceptTerms')}
          />
          我同意服务条款和隐私政策 *
        </label>
        {form.touched.acceptTerms && form.errors.acceptTerms && (
          <span className="error-message">{form.errors.acceptTerms}</span>
        )}
      </div>

      <div className="form-actions">
        <button 
          type="submit" 
          disabled={!form.isValid || form.isSubmitting}
          className="submit-button"
        >
          {form.isSubmitting ? '注册中...' : '注册'}
        </button>
        <button 
          type="button" 
          onClick={form.resetForm}
          className="reset-button"
        >
          重置
        </button>
      </div>

      <div className="form-debug">
        <details>
          <summary>调试信息</summary>
          <pre>{JSON.stringify({ 
            values: form.values, 
            errors: form.errors, 
            isValid: form.isValid 
          }, null, 2)}</pre>
        </details>
      </div>
    </form>
  );
}

export default UserRegistrationForm;`,
    explanation: '这个表单验证Hook提供了完整的表单管理解决方案，包括字段级验证、异步验证、防抖处理、错误状态管理等。它支持自定义验证规则，可以处理复杂的表单逻辑，同时提供良好的用户体验。Hook的设计让表单组件更简洁，验证逻辑更可维护。'
  }
];

export default businessScenarios; 