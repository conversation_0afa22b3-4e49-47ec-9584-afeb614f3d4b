import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 'custom-hook-design-principles',
    question: '设计自定义Hook时应该遵循哪些原则？请举例说明如何设计一个高质量的自定义Hook。',
    difficulty: 'intermediate',
    category: '设计原则',
    answer: `## 自定义Hook设计原则

### 1. 单一职责原则
每个Hook应该有明确的单一职责，专注于解决特定问题。

### 2. 命名规范
- 必须以"use"开头
- 名称应该清晰表达Hook的功能
- 使用驼峰命名法

### 3. 输入输出设计
- 参数设计要简洁明了
- 返回值结构要合理，便于解构
- 提供合理的默认值

### 4. 性能考虑
- 正确使用依赖数组
- 避免不必要的重新计算
- 合理使用useMemo和useCallback

## 实际设计示例

\`\`\`tsx
// ✅ 良好的自定义Hook设计
function useApiWithRetry<T>(
  url: string,
  options: {
    immediate?: boolean;
    retryCount?: number;
    retryDelay?: number;
    onSuccess?: (data: T) => void;
    onError?: (error: Error) => void;
  } = {}
) {
  const {
    immediate = true,
    retryCount = 3,
    retryDelay = 1000,
    onSuccess,
    onError
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const retryCountRef = useRef(0);

  const execute = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(url);
      if (!response.ok) throw new Error(\'使用字符串拼接代替模板字符串');
      
      const result = await response.json();
      setData(result);
      onSuccess?.(result);
      retryCountRef.current = 0;
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error(String(err));
      
      if (retryCountRef.current < retryCount) {
        retryCountRef.current++;
        setTimeout(execute, retryDelay * retryCountRef.current);
      } else {
        setError(errorObj);
        onError?.(errorObj);
      }
    } finally {
      setLoading(false);
    }
  }, [url, retryCount, retryDelay, onSuccess, onError]);

  useEffect(() => {
    if (immediate) execute();
  }, [immediate, execute]);

  return {
    data,
    loading,
    error,
    execute,
    retry: execute,
    reset: () => {
      setData(null);
      setError(null);
      retryCountRef.current = 0;
    }
  };
}
\`\`\`

这个Hook遵循了所有设计原则：
- **单一职责**：专注于API请求和重试逻辑
- **命名规范**：以use开头，名称描述性强
- **参数设计**：支持配置项，有合理默认值
- **返回值设计**：结构清晰，便于使用
- **性能优化**：使用useCallback避免不必要的重新执行`,
    tags: ['设计原则', 'Hook规范', '最佳实践', '代码质量'],
    relatedTopics: ['React Hook规则', '组件设计', 'API设计']
  },

  {
    id: 'hook-state-sharing',
    question: '如何在多个组件之间共享自定义Hook的状态？有哪些不同的实现方式？',
    difficulty: 'intermediate',
    category: '状态共享',
    answer: `## 自定义Hook状态共享方案

### 方案1: Context + Hook模式（推荐）

\`\`\`tsx
// 1. 创建Context和Provider
const CounterContext = createContext<{
  count: number;
  increment: () => void;
  decrement: () => void;
} | null>(null);

function CounterProvider({ children }: { children: ReactNode }) {
  const [count, setCount] = useState(0);
  
  const value = useMemo(() => ({
    count,
    increment: () => setCount(c => c + 1),
    decrement: () => setCount(c => c - 1)
  }), [count]);
  
  return (
    <CounterContext.Provider value={value}>
      {children}
    </CounterContext.Provider>
  );
}

// 2. 创建自定义Hook
function useSharedCounter() {
  const context = useContext(CounterContext);
  if (!context) {
    throw new Error('useSharedCounter必须在CounterProvider内使用');
  }
  return context;
}

// 3. 在组件中使用
function ComponentA() {
  const { count, increment } = useSharedCounter();
  return <button onClick={increment}>A: {count}</button>;
}

function ComponentB() {
  const { count, decrement } = useSharedCounter();
  return <button onClick={decrement}>B: {count}</button>;
}
\`\`\`

### 方案2: 外部状态管理

\`\`\`tsx
// 创建外部状态对象
class CounterStore {
  private listeners = new Set<() => void>();
  private _count = 0;

  get count() {
    return this._count;
  }

  increment() {
    this._count++;
    this.notify();
  }

  decrement() {
    this._count--;
    this.notify();
  }

  subscribe(listener: () => void) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notify() {
    this.listeners.forEach(listener => listener());
  }
}

const counterStore = new CounterStore();

// 自定义Hook订阅外部状态
function useExternalCounter() {
  const [count, setCount] = useState(counterStore.count);

  useEffect(() => {
    const unsubscribe = counterStore.subscribe(() => {
      setCount(counterStore.count);
    });
    return unsubscribe;
  }, []);

  return {
    count,
    increment: () => counterStore.increment(),
    decrement: () => counterStore.decrement()
  };
}
\`\`\`

### 方案3: 自定义事件总线

\`\`\`tsx
class EventBus {
  private events: { [key: string]: Function[] } = {};

  on(event: string, callback: Function) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  off(event: string, callback: Function) {
    if (this.events[event]) {
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    }
  }

  emit(event: string, data?: any) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data));
    }
  }
}

const eventBus = new EventBus();

function useEventBusState(initialValue: any, eventName: string) {
  const [state, setState] = useState(initialValue);

  useEffect(() => {
    const handleUpdate = (newState: any) => setState(newState);
    eventBus.on(eventName, handleUpdate);
    
    return () => eventBus.off(eventName, handleUpdate);
  }, [eventName]);

  const updateState = useCallback((newState: any) => {
    setState(newState);
    eventBus.emit(eventName, newState);
  }, [eventName]);

  return [state, updateState];
}
\`\`\`

## 方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| Context + Hook | React原生、类型安全、Provider作用域控制 | 需要包裹组件、可能导致不必要重渲染 | 中小型应用、局部状态共享 |
| 外部状态管理 | 性能好、不依赖React Context | 需要手动管理订阅、脱离React生态 | 大型应用、复杂状态逻辑 |
| 事件总线 | 松耦合、灵活 | 调试困难、类型检查困难 | 跨组件通信、事件驱动场景 |

### 推荐选择

- **小到中型应用**：使用Context + Hook模式
- **大型复杂应用**：考虑Redux、Zustand等状态管理库
- **简单通信需求**：使用事件总线或props传递`,
    tags: ['状态共享', 'Context API', '外部状态', '事件总线'],
    relatedTopics: ['useContext', '状态管理', 'Redux', 'Provider模式']
  },

  {
    id: 'hook-testing-strategies',
    question: '如何对自定义Hook进行单元测试？请说明测试策略和常用工具。',
    difficulty: 'advanced',
    category: '测试策略',
    answer: `## 自定义Hook测试策略

### 测试工具准备

\`\`\`bash
npm install --save-dev @testing-library/react-\`\`\`

### 1. 基础Hook测试

\`\`\`tsx


function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => setCount(c => c + 1), []);
  const decrement = useCallback(() => setCount(c => c - 1), []);
  const reset = useCallback(() => setCount(initialValue), [initialValue]);
  
  return { count, increment, decrement, reset };
}

describe('useCounter', () => {
  it('应该使用初始值初始化', () => {
    const { result } = renderHook(() => useCounter(10));
    expect(result.current.count).toBe(10);
  });

  it('应该正确递增计数', () => {
    const { result } = renderHook(() => useCounter());
    
    act(() => {
      result.current.increment();
    });
    
    expect(result.current.count).toBe(1);
  });

  it('应该正确重置计数', () => {
    const { result } = renderHook(() => useCounter(5));
    
    act(() => {
      result.current.increment();
      result.current.reset();
    });
    
    expect(result.current.count).toBe(5);
  });
});
\`\`\`

### 2. 异步Hook测试

\`\`\`tsx


function useAsyncData(url: string) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    let cancelled = false;
    
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await fetch(url);
        const result = await response.json();
        
        if (!cancelled) {
          setData(result);
        }
      } catch (err) {
        if (!cancelled) {
          setError(err);
        }
      } finally {
        if (!cancelled) {
          setLoading(false);
        }
      }
    };

    fetchData();
    
    return () => {
      cancelled = true;
    };
  }, [url]);

  return { data, loading, error };
}

// 测试异步Hook
describe('useAsyncData', () => {
  beforeEach(() => {
    global.fetch = jest.fn();
  });

  it('应该处理成功的API调用', async () => {
    const mockData = { id: 1, name: 'Test' };
    
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      json: async () => mockData
    });

    const { result, waitForNextUpdate } = renderHook(() => 
      useAsyncData('/api/test')
    );

    expect(result.current.loading).toBe(true);
    
    await waitForNextUpdate();
    
    expect(result.current.loading).toBe(false);
    expect(result.current.data).toEqual(mockData);
    expect(result.current.error).toBe(null);
  });

  it('应该处理API错误', async () => {
    const mockError = new Error('Network error');
    
    (global.fetch as jest.Mock).mockRejectedValueOnce(mockError);

    const { result, waitForNextUpdate } = renderHook(() => 
      useAsyncData('/api/test')
    );

    await waitForNextUpdate();
    
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(mockError);
  });
});
\`\`\`

### 3. 依赖Context的Hook测试

\`\`\`tsx
const TestContext = createContext({ value: 'test' });

function useTestContext() {
  return useContext(TestContext);
}

describe('useTestContext', () => {
  it('应该返回context值', () => {
    const wrapper = ({ children }) => (
      <TestContext.Provider value={{ value: 'custom' }}>
        {children}
      </TestContext.Provider>
    );

    const { result } = renderHook(() => useTestContext(), { wrapper });
    
    expect(result.current.value).toBe('custom');
  });

  it('应该在没有provider时抛出错误', () => {
    const { result } = renderHook(() => useTestContext());
    
    // 如果Hook有错误检查
    expect(result.error).toBeDefined();
  });
});
\`\`\`

### 4. 性能测试

\`\`\`tsx
function useExpensiveCalculation(input: number) {
  return useMemo(() => {
    // 模拟昂贵计算
    let result = 0;
    for (let i = 0; i < input * 1000000; i++) {
      result += i;
    }
    return result;
  }, [input]);
}

describe('useExpensiveCalculation performance', () => {
  it('应该缓存计算结果', () => {
    const { result, rerender } = renderHook(
      ({ input }) => useExpensiveCalculation(input),
      { initialProps: { input: 10 } }
    );

    const firstResult = result.current;
    
    // 重新渲染但输入未变化
    rerender({ input: 10 });
    
    // 应该返回相同的引用（缓存命中）
    expect(result.current).toBe(firstResult);
    
    // 输入变化时重新计算
    rerender({ input: 20 });
    expect(result.current).not.toBe(firstResult);
  });
});
\`\`\`

### 5. 副作用测试

\`\`\`tsx
function useLocalStorage(key: string, initialValue: any) {
  const [value, setValue] = useState(() => {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : initialValue;
  });

  useEffect(() => {
    localStorage.setItem(key, JSON.stringify(value));
  }, [key, value]);

  return [value, setValue];
}

describe('useLocalStorage', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  it('应该从localStorage读取初始值', () => {
    localStorage.setItem('test-key', JSON.stringify('stored-value'));
    
    const { result } = renderHook(() => 
      useLocalStorage('test-key', 'default')
    );
    
    expect(result.current[0]).toBe('stored-value');
  });

  it('应该将值保存到localStorage', () => {
    const { result } = renderHook(() => 
      useLocalStorage('test-key', 'initial')
    );
    
    act(() => {
      result.current[1]('new-value');
    });
    
    expect(localStorage.getItem('test-key')).toBe('"new-value"');
  });
});
\`\`\`

## 测试最佳实践

### 1. 测试策略
- **单元测试**：测试Hook的核心逻辑
- **集成测试**：测试Hook在实际组件中的使用
- **端到端测试**：测试完整用户流程

### 2. Mock策略
- Mock外部依赖（API、localStorage等）
- 使用jest.mock模拟模块
- 创建测试专用的Context Provider

### 3. 覆盖率要求
- 确保所有分支路径被测试
- 测试边界条件和错误情况
- 验证清理函数是否正确执行

### 4. 性能测试
- 验证缓存是否正常工作
- 检查是否有不必要的重新计算
- 测试内存泄漏情况`,
    tags: ['单元测试', 'React Testing Library', '异步测试', '性能测试'],
    relatedTopics: ['Jest', 'Testing Library', '测试驱动开发', 'Mock']
  },

  {
    id: 'hook-performance-optimization',
    question: '自定义Hook的性能优化有哪些策略？如何避免常见的性能陷阱？',
    difficulty: 'advanced',
    category: '性能优化',
    answer: `## 自定义Hook性能优化策略

### 1. 依赖数组优化

\`\`\`tsx
// ❌ 错误：引用类型依赖导致无限循环
function useBadApi(options) {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    fetchData(options); // options每次都是新对象
  }, [options]); // 导致无限重新请求
  
  return data;
}

// ✅ 正确：稳定化依赖
function useGoodApi(options) {
  const [data, setData] = useState(null);
  
  // 稳定化options引用
  const stableOptions = useMemo(() => ({
    method: options.method || 'GET',
    headers: options.headers || {},
    ...options
  }), [
    options.method,
    options.headers,
    // 列出所有相关属性
  ]);
  
  useEffect(() => {
    fetchData(stableOptions);
  }, [stableOptions]);
  
  return data;
}
\`\`\`

### 2. 回调函数优化

\`\`\`tsx
// ❌ 错误：每次渲染都创建新函数
function useBadCounter() {
  const [count, setCount] = useState(0);
  
  return {
    count,
    increment: () => setCount(count + 1), // 每次都是新函数
    decrement: () => setCount(count - 1)
  };
}

// ✅ 正确：使用useCallback缓存函数
function useGoodCounter() {
  const [count, setCount] = useState(0);
  
  const increment = useCallback(() => {
    setCount(prev => prev + 1); // 使用函数式更新
  }, []);
  
  const decrement = useCallback(() => {
    setCount(prev => prev - 1);
  }, []);
  
  return { count, increment, decrement };
}
\`\`\`

### 3. 计算结果缓存

\`\`\`tsx
function useExpensiveCalculation(data, filters) {
  // ❌ 错误：每次渲染都重新计算
  // const result = expensiveFunction(data, filters);
  
  // ✅ 正确：缓存计算结果
  const result = useMemo(() => {
    if (!data) return null;
    
    return expensiveFunction(data, filters);
  }, [data, filters]);
  
  // 进一步优化：深度比较
  const stableFilters = useMemo(() => filters, [
    filters.type,
    filters.category,
    filters.dateRange?.start,
    filters.dateRange?.end
  ]);
  
  const optimizedResult = useMemo(() => {
    return expensiveFunction(data, stableFilters);
  }, [data, stableFilters]);
  
  return optimizedResult;
}
\`\`\`

### 4. 防抖和节流

\`\`\`tsx
// 防抖Hook
function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => clearTimeout(timer);
  }, [value, delay]);
  
  return debouncedValue;
}

// 节流Hook
function useThrottle(value, limit) {
  const [throttledValue, setThrottledValue] = useState(value);
  const lastRan = useRef(Date.now());
  
  useEffect(() => {
    const timer = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value);
        lastRan.current = Date.now();
      }
    }, limit - (Date.now() - lastRan.current));
    
    return () => clearTimeout(timer);
  }, [value, limit]);
  
  return throttledValue;
}

// 使用示例
function SearchComponent() {
  const [query, setQuery] = useState('');
  const debouncedQuery = useDebounce(query, 300);
  
  const { data } = useApi(\'使用字符串拼接代替模板字符串');
  
  return (
    <input 
      value={query}
      onChange={(e) => setQuery(e.target.value)}
    />
  );
}
\`\`\`

### 5. 智能订阅管理

\`\`\`tsx
function useSmartSubscription(eventName, handler, options = {}) {
  const { 
    immediate = true, 
    once = false,
    throttle = 0 
  } = options;
  
  const handlerRef = useRef(handler);
  const throttleRef = useRef(null);
  
  // 更新处理函数引用，避免重新订阅
  handlerRef.current = handler;
  
  useEffect(() => {
    if (!immediate) return;
    
    const throttledHandler = throttle > 0 
      ? (event) => {
          if (throttleRef.current) return;
          
          throttleRef.current = setTimeout(() => {
            handlerRef.current(event);
            throttleRef.current = null;
          }, throttle);
        }
      : (event) => handlerRef.current(event);
    
    const listener = once 
      ? (event) => {
          throttledHandler(event);
          window.removeEventListener(eventName, listener);
        }
      : throttledHandler;
    
    window.addEventListener(eventName, listener);
    
    return () => {
      window.removeEventListener(eventName, listener);
      if (throttleRef.current) {
        clearTimeout(throttleRef.current);
      }
    };
  }, [eventName, immediate, once, throttle]);
}
\`\`\`

### 6. 组件卸载时清理

\`\`\`tsx
function useAsyncEffect(asyncFunction, dependencies) {
  const isMountedRef = useRef(true);
  
  useEffect(() => {
    let cancelled = false;
    
    const runAsync = async () => {
      try {
        const result = await asyncFunction();
        
        if (!cancelled && isMountedRef.current) {
          // 处理结果
        }
      } catch (error) {
        if (!cancelled && isMountedRef.current) {
          // 处理错误
        }
      }
    };
    
    runAsync();
    
    return () => {
      cancelled = true;
    };
  }, dependencies);
  
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);
}
\`\`\`

### 7. 性能监控Hook

\`\`\`tsx
function usePerformanceMonitor(name, enabled = true) {
  const startTime = useRef(performance.now());
  const renderCount = useRef(0);
  
  if (enabled) {
    renderCount.current++;
    
    useEffect(() => {
      const endTime = performance.now();
      const duration = endTime - startTime.current;
      
      console.log(\'使用字符串拼接代替模板字符串');
      
      // 重置计时器
      startTime.current = performance.now();
    });
  }
  
  return {
    renderCount: renderCount.current,
    logPerformance: (label: string) => {
      if (enabled) {
        console.time(\'使用字符串拼接代替模板字符串');
        return () => console.timeEnd(\'使用字符串拼接代替模板字符串');
      }
      return () => {};
    }
  };
}
\`\`\`

## 常见性能陷阱

### 1. 过度使用useEffect
- **问题**：将所有逻辑都放在useEffect中
- **解决**：区分副作用和计算，使用适当的Hook

### 2. 依赖数组错误
- **问题**：依赖数组不完整或包含不稳定引用
- **解决**：使用ESLint规则检查，稳定化对象引用

### 3. 不必要的状态
- **问题**：将可计算的值存储为状态
- **解决**：使用useMemo进行派生计算

### 4. 函数重新创建
- **问题**：每次渲染都创建新的函数引用
- **解决**：使用useCallback缓存函数

### 5. 内存泄漏
- **问题**：忘记清理订阅、定时器等
- **解决**：在useEffect中返回清理函数`,
    tags: ['性能优化', '内存管理', '防抖节流', '缓存策略'],
    relatedTopics: ['useMemo', 'useCallback', '性能监控', '内存泄漏']
  },

  {
    id: 'hook-composition-patterns',
    question: '描述几种常见的Hook组合模式，以及它们的适用场景和最佳实践。',
    difficulty: 'advanced',
    category: '组合模式',
    answer: `## Hook组合模式详解

### 1. 状态聚合模式

将相关的状态和操作聚合到一个Hook中，提供统一的接口。

\`\`\`tsx
// 表单状态聚合
function useFormState(initialValues) {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const setValue = useCallback((name, value) => {
    setValues(prev => ({ ...prev, [name]: value }));
    // 清除相关错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  }, [errors]);
  
  const setFieldTouched = useCallback((name) => {
    setTouched(prev => ({ ...prev, [name]: true }));
  }, []);
  
  const reset = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  }, [initialValues]);
  
  return {
    values,
    errors,
    touched,
    isSubmitting,
    setValue,
    setErrors,
    setFieldTouched,
    setIsSubmitting,
    reset
  };
}
\`\`\`

### 2. 组合器模式

将多个简单Hook组合成更复杂的功能。

\`\`\`tsx
// 基础Hook
function useToggle(initial = false) {
  const [value, setValue] = useState(initial);
  const toggle = useCallback(() => setValue(v => !v), []);
  return [value, toggle, setValue];
}

function useCounter(initial = 0) {
  const [count, setCount] = useState(initial);
  const increment = useCallback(() => setCount(c => c + 1), []);
  const decrement = useCallback(() => setCount(c => c - 1), []);
  return { count, increment, decrement, setCount };
}

// 组合Hook
function useCountdownTimer(initialTime) {
  const [isActive, toggleActive] = useToggle(false);
  const { count: timeLeft, setCount: setTimeLeft, decrement } = useCounter(initialTime);
  
  useEffect(() => {
    if (!isActive || timeLeft <= 0) return;
    
    const timer = setInterval(decrement, 1000);
    return () => clearInterval(timer);
  }, [isActive, timeLeft, decrement]);
  
  const start = useCallback(() => {
    if (timeLeft > 0) toggleActive();
  }, [timeLeft, toggleActive]);
  
  const pause = useCallback(() => {
    toggleActive();
  }, [toggleActive]);
  
  const reset = useCallback(() => {
    setTimeLeft(initialTime);
    if (isActive) toggleActive();
  }, [initialTime, isActive, setTimeLeft, toggleActive]);
  
  return {
    timeLeft,
    isActive,
    start,
    pause,
    reset,
    isFinished: timeLeft === 0
  };
}
\`\`\`

### 3. 高阶Hook模式

创建返回Hook的函数，实现Hook的参数化配置。

\`\`\`tsx
// 高阶Hook工厂
function createAsyncHook(config) {
  return function useAsync(asyncFunction, dependencies = []) {
    const [state, setState] = useState({
      data: null,
      loading: false,
      error: null
    });
    
    const execute = useCallback(async (...args) => {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      try {
        // 应用配置
        if (config.onStart) config.onStart();
        
        const data = await asyncFunction(...args);
        
        setState({ data, loading: false, error: null });
        
        if (config.onSuccess) config.onSuccess(data);
        
        return data;
      } catch (error) {
        setState(prev => ({ ...prev, loading: false, error }));
        
        if (config.onError) config.onError(error);
        
        throw error;
      }
    }, [asyncFunction, ...dependencies]);
    
    return { ...state, execute };
  };
}

// 使用高阶Hook
const useApiWithLogging = createAsyncHook({
  onStart: () => console.log('API request started'),
  onSuccess: (data) => console.log('API request succeeded:', data),
  onError: (error) => console.error('API request failed:', error)
});
\`\`\`

### 4. 管道模式

将数据通过一系列Hook进行处理和转换。

\`\`\`tsx
function useDataPipeline(initialData, transformers = []) {
  // 应用所有转换器
  const processedData = useMemo(() => {
    return transformers.reduce((data, transformer) => {
      return transformer(data);
    }, initialData);
  }, [initialData, transformers]);
  
  return processedData;
}

// 数据转换器
const filterActive = (items) => items.filter(item => item.active);
const sortByDate = (items) => [...items].sort((a, b) => new Date(b.date) - new Date(a.date));
const limitResults = (limit) => (items) => items.slice(0, limit);

// 使用管道
function DataList({ rawData }) {
  const processedData = useDataPipeline(rawData, [
    filterActive,
    sortByDate,
    limitResults(10)
  ]);
  
  return <div>{/* 渲染处理后的数据 */}</div>;
}
\`\`\`

### 5. 装饰器模式

为现有Hook添加额外功能。

\`\`\`tsx
// 基础Hook
function useBasicCounter(initial = 0) {
  const [count, setCount] = useState(initial);
  const increment = () => setCount(c => c + 1);
  const decrement = () => setCount(c => c - 1);
  return { count, increment, decrement };
}

// 装饰器：添加历史记录
function withHistory(useHook) {
  return function useHookWithHistory(...args) {
    const hookResult = useHook(...args);
    const [history, setHistory] = useState([]);
    
    // 记录状态变化
    useEffect(() => {
      setHistory(prev => [...prev, hookResult.count].slice(-10)); // 保留最近10条
    }, [hookResult.count]);
    
    const undo = useCallback(() => {
      if (history.length > 1) {
        const previousValue = history[history.length - 2];
        hookResult.setCount(previousValue);
      }
    }, [history, hookResult]);
    
    return {
      ...hookResult,
      history,
      undo,
      canUndo: history.length > 1
    };
  };
}

// 装饰器：添加持久化
function withPersistence(useHook, storageKey) {
  return function useHookWithPersistence(...args) {
    // 从localStorage读取初始值
    const [initial] = args;
    const persistedValue = localStorage.getItem(storageKey);
    const initialValue = persistedValue ? JSON.parse(persistedValue) : initial;
    
    const hookResult = useHook(initialValue, ...args.slice(1));
    
    // 保存状态变化
    useEffect(() => {
      localStorage.setItem(storageKey, JSON.stringify(hookResult.count));
    }, [hookResult.count]);
    
    return hookResult;
  };
}

// 组合装饰器
const useEnhancedCounter = withHistory(
  withPersistence(useBasicCounter, 'counter-value')
);
\`\`\`

### 6. 策略模式

根据不同条件使用不同的Hook实现。

\`\`\`tsx
function useStorage(key, initialValue, strategy = 'localStorage') {
  const strategies = {
    localStorage: useLocalStorage,
    sessionStorage: useSessionStorage,
    memory: useMemoryStorage
  };
  
  const StorageHook = strategies[strategy] || strategies.localStorage;
  return StorageHook(key, initialValue);
}

function useLocalStorage(key, initialValue) {
  const [value, setValue] = useState(() => {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : initialValue;
  });
  
  const updateValue = useCallback((newValue) => {
    setValue(newValue);
    localStorage.setItem(key, JSON.stringify(newValue));
  }, [key]);
  
  return [value, updateValue];
}

function useSessionStorage(key, initialValue) {
  // 类似实现，但使用sessionStorage
}

function useMemoryStorage(key, initialValue) {
  // 内存存储实现
}
\`\`\`

## 最佳实践总结

### 1. 选择合适的模式
- **简单状态管理**：状态聚合模式
- **复杂逻辑组合**：组合器模式
- **可配置Hook**：高阶Hook模式
- **数据处理**：管道模式
- **功能增强**：装饰器模式
- **多种实现**：策略模式

### 2. 性能考虑
- 合理使用useMemo和useCallback
- 避免不必要的依赖
- 考虑Hook的调用顺序

### 3. 可维护性
- 保持Hook的单一职责
- 提供清晰的API接口
- 添加适当的错误处理
- 编写完善的文档和测试`,
    tags: ['设计模式', 'Hook组合', '高阶函数', '装饰器模式'],
    relatedTopics: ['设计模式', '函数式编程', '组件设计', '代码复用']
  }
];

export default interviewQuestions; 