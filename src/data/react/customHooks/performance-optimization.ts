import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  introduction: `自定义Hook的性能优化是React应用整体性能的关键环节。由于Hook会在每次组件渲染时执行，不当的Hook设计可能导致严重的性能问题。

优化自定义Hook的性能不仅要关注Hook本身的执行效率，还要考虑Hook对使用它的组件的影响。通过合理的缓存策略、依赖管理和计算优化，可以确保Hook既提供了强大的功能，又保持了良好的性能表现。`,

  bestPractices: [
    {
      practice: "使用useMemo缓存昂贵的计算结果",
      description: "对于计算量大的操作，使用useMemo避免在每次渲染时重复计算",
      example: `
// ❌ 每次渲染都重新计算
function useExpensiveData(items) {
  const processedData = items.map(item => expensiveOperation(item));
  return processedData;
}

// ✅ 使用useMemo缓存计算结果
function useExpensiveData(items) {
  const processedData = useMemo(() => {
    return items.map(item => expensiveOperation(item));
  }, [items]);
  
  return processedData;
}`
    },
    {
      practice: "使用useCallback稳定函数引用",
      description: "对Hook返回的函数使用useCallback，避免不必要的重新渲染",
      example: `
// ❌ 每次都返回新的函数
function useCounter(initialValue) {
  const [count, setCount] = useState(initialValue);
  
  const increment = () => setCount(c => c + 1);
  const decrement = () => setCount(c => c - 1);
  const reset = () => setCount(initialValue);
  
  return { count, increment, decrement, reset };
}

// ✅ 使用useCallback稳定函数引用
function useCounter(initialValue) {
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => setCount(c => c + 1), []);
  const decrement = useCallback(() => setCount(c => c - 1), []);
  const reset = useCallback(() => setCount(initialValue), [initialValue]);
  
  return { count, increment, decrement, reset };
}`
    },
    {
      practice: "优化Hook的返回值结构",
      description: "使用useMemo包装Hook的返回对象，避免每次都创建新对象",
      example: `
// ❌ 每次返回新对象
function useUserState(userId) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);
  
  return {
    user,
    loading,
    refetch: () => fetchUser(userId)
  };
}

// ✅ 使用useMemo优化返回值
function useUserState(userId) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const refetch = useCallback(() => fetchUser(userId), [userId]);
  
  return useMemo(() => ({
    user,
    loading,
    refetch
  }), [user, loading, refetch]);
}`
    },
    {
      practice: "实现懒初始化状态",
      description: "对于初始化成本高的状态，使用函数形式的useState避免重复计算",
      example: `
// ❌ 每次渲染都计算初始值
function useLocalStorage(key) {
  const [value, setValue] = useState(
    JSON.parse(localStorage.getItem(key) || 'null')
  );
  
  return [value, setValue];
}

// ✅ 使用懒初始化
function useLocalStorage(key) {
  const [value, setValue] = useState(() => {
    try {
      return JSON.parse(localStorage.getItem(key) || 'null');
    } catch {
      return null;
    }
  });
  
  return [value, setValue];
}`
    }
  ],

  commonBottlenecks: [
    {
      bottleneck: "Hook内部频繁的状态更新",
      impact: "导致组件频繁重新渲染，影响整体性能",
      solution: "使用状态合并、防抖或批量更新减少状态变化频率",
      codeExample: `
// ❌ 频繁的状态更新
function useFormState() {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  
  return {
    firstName, setFirstName,
    lastName, setLastName,
    email, setEmail
  };
}

// ✅ 合并状态减少更新
function useFormState() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: ''
  });
  
  const updateField = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);
  
  return { formData, updateField };
}`
    },
    {
      bottleneck: "复杂的依赖项计算",
      impact: "useEffect和useMemo的依赖项计算开销过大",
      solution: "优化依赖项的计算逻辑，使用基本类型或稳定的引用",
      codeExample: `
// ❌ 复杂对象作为依赖项
function useApiData(config) {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    fetchData(config).then(setData);
  }, [config]); // config是复杂对象，可能频繁变化
  
  return data;
}

// ✅ 使用基本类型作为依赖项
function useApiData(config) {
  const [data, setData] = useState(null);
  
  // 提取关键字段作为依赖项
  const { url, method, params } = config;
  const paramsKey = JSON.stringify(params);
  
  useEffect(() => {
    fetchData(config).then(setData);
  }, [url, method, paramsKey]);
  
  return data;
}`
    }
  ],

  optimizationStrategies: [
    {
      strategy: "实现Hook级别的防抖和节流",
      description: "对频繁调用的Hook操作进行防抖或节流处理",
      implementation: `
// 防抖Hook
function useDebouncedCallback(callback, delay) {
  const callbackRef = useRef(callback);
  const timeoutRef = useRef();
  
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  return useMemo(
    () =>
      (...args) => {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = setTimeout(() => {
          callbackRef.current(...args);
        }, delay);
      },
    [delay]
  );
}

// 节流Hook
function useThrottledCallback(callback, delay) {
  const callbackRef = useRef(callback);
  const lastCallRef = useRef(0);
  
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  return useMemo(
    () =>
      (...args) => {
        const now = Date.now();
        if (now - lastCallRef.current >= delay) {
          lastCallRef.current = now;
          callbackRef.current(...args);
        }
      },
    [delay]
  );
}`
    },
    {
      strategy: "使用虚拟化技术处理大量数据",
      description: "对于处理大量数据的Hook，实现虚拟化减少DOM节点数量",
      implementation: `
function useVirtualList(items, itemHeight, containerHeight) {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length]);
  
  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex);
  }, [items, visibleRange]);
  
  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.startIndex * itemHeight;
  
  return {
    visibleItems,
    totalHeight,
    offsetY,
    setScrollTop
  };
}`
    },
    {
      strategy: "实现智能缓存机制",
      description: "为Hook添加多层缓存，包括内存缓存和持久化缓存",
      implementation: `
function useSmartCache(key, fetcher, options = {}) {
  const { 
    ttl = 5 * 60 * 1000, // 5分钟
    maxSize = 100,
    persistToStorage = false 
  } = options;
  
  const cache = useRef(new Map());
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const getCachedData = useCallback((cacheKey) => {
    const cached = cache.current.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.data;
    }
    
    if (persistToStorage) {
      const stored = localStorage.getItem('cache-' + cacheKey);
      if (stored) {
        try {
          const parsed = JSON.parse(stored);
          if (Date.now() - parsed.timestamp < ttl) {
            return parsed.data;
          }
        } catch (e) {
          // 忽略解析错误
        }
      }
    }
    
    return null;
  }, [ttl, persistToStorage]);
  
  const setCachedData = useCallback((cacheKey, data) => {
    const cacheEntry = { data, timestamp: Date.now() };
    
    // 内存缓存
    if (cache.current.size >= maxSize) {
      const firstKey = cache.current.keys().next().value;
      cache.current.delete(firstKey);
    }
    cache.current.set(cacheKey, cacheEntry);
    
    // 持久化缓存
    if (persistToStorage) {
      localStorage.setItem('cache-' + cacheKey, JSON.stringify(cacheEntry));
    }
  }, [maxSize, persistToStorage]);
  
  useEffect(() => {
    const cachedData = getCachedData(key);
    if (cachedData) {
      setData(cachedData);
      return;
    }
    
    setLoading(true);
    fetcher(key)
      .then(result => {
        setData(result);
        setCachedData(key, result);
      })
      .finally(() => setLoading(false));
  }, [key, fetcher, getCachedData, setCachedData]);
  
  return { data, loading };
}`
    }
  ],

  performanceMetrics: [
    {
      metric: "Hook执行时间",
      description: "测量Hook内部逻辑的执行耗时",
      measurement: `
function usePerformanceMonitor(hookName) {
  const startTime = useRef();
  
  // 记录开始时间
  startTime.current = performance.now();
  
  useEffect(() => {
    const endTime = performance.now();
    const duration = endTime - startTime.current;
    
    if (duration > 16) { // 超过一帧的时间
      console.warn('Hook执行耗时过长:', hookName, duration + 'ms');
    }
  });
}`,
      tools: "使用React DevTools Profiler和浏览器Performance API"
    },
    {
      metric: "内存使用情况",
      description: "监控Hook是否存在内存泄漏",
      measurement: `
function useMemoryMonitor(hookName) {
  useEffect(() => {
    const checkMemory = () => {
      if (performance.memory) {
        console.log(hookName + ' 内存使用:', {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        });
      }
    };
    
    const interval = setInterval(checkMemory, 5000);
    return () => clearInterval(interval);
  }, [hookName]);
}`,
      tools: "Chrome DevTools的Memory面板和Heap Snapshot"
    },
    {
      metric: "重新渲染频率",
      description: "统计Hook导致的组件重新渲染次数",
      measurement: `
function useRenderCounter(componentName) {
  const renderCount = useRef(0);
  const startTime = useRef(Date.now());
  
  renderCount.current++;
  
  useEffect(() => {
    const duration = Date.now() - startTime.current;
    const rps = renderCount.current / (duration / 1000);
    
    if (rps > 10) { // 每秒超过10次渲染
      console.warn('组件渲染频率过高:', componentName, rps + ' RPS');
    }
  });
  
  return renderCount.current;
}`,
      tools: "React DevTools Profiler的渲染追踪功能"
    }
  ],

  tools: [
    {
      name: "React DevTools Profiler",
      description: "官方性能分析工具，可以分析组件和Hook的性能",
      usage: "安装浏览器扩展，在Components面板中启用Profiler进行性能分析"
    },
    {
      name: "React-Hook-Testing-Library",
      description: "专门用于测试Hook性能的库",
      usage: "编写Hook的性能测试用例，确保性能回归测试"
    },
    {
      name: "Bundle Analyzer",
      description: "分析Hook对打包体积的影响",
      usage: "使用webpack-bundle-analyzer等工具分析Hook的代码体积"
    }
  ]
};

export default performanceOptimization; 