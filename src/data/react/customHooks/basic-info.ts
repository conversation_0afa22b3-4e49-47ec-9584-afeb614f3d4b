import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  introduction: `自定义Hook是React中实现逻辑复用的强大机制，通过以"use"开头的函数，可以提取和共享组件之间的状态逻辑。自定义Hook本质上是调用其他Hook的JavaScript函数，让你能够在多个组件之间共享复杂的逻辑。

自定义Hook的核心价值在于**关注点分离**和**逻辑复用**。它让你能够将组件中的状态逻辑、副作用处理和其他复杂逻辑抽离到独立的函数中，既提高了代码的可重用性，又增强了代码的可测试性和可维护性。`,

  syntax: `function useCustomHook(params?: any): ReturnType {
  // 可以调用其他Hook
  const [state, setState] = useState(initialValue);
  const ref = useRef(null);
  
  // 副作用处理
  useEffect(() => {
    // 自定义逻辑
  }, []);
  
  // 返回状态和方法
  return {
    state,
    setState,
    // 其他需要暴露的值和函数
  };
}`,

  parameters: [
    {
      name: 'params',
      type: 'any',
      description: '自定义Hook的输入参数，根据具体需求定义',
      required: false,
      details: '参数应该明确定义类型，遵循TypeScript最佳实践'
    }
  ],

  returnValue: {
    type: 'any',
    description: '返回Hook的状态、方法和其他暴露的值，通常是对象或数组形式'
  },

  keyFeatures: [
    {
      feature: '逻辑复用',
      description: '在多个组件之间共享相同的状态逻辑',
      importance: 'critical',
      details: '避免代码重复，提高开发效率'
    },
    {
      feature: '关注点分离',
      description: '将复杂逻辑从组件中抽离，提高可读性',
      importance: 'high',
      details: '让组件专注于渲染，逻辑处理交给Hook'
    },
    {
      feature: '组合能力',
      description: '可以调用其他Hook，包括内置Hook和自定义Hook',
      importance: 'high',
      details: '支持Hook的嵌套和组合使用'
    },
    {
      feature: '测试友好',
      description: '独立的逻辑单元，易于单元测试',
      importance: 'medium',
      details: '可以独立于组件进行测试'
    },
    {
      feature: '类型安全',
      description: '完全支持TypeScript类型推断',
      importance: 'medium',
      details: '提供完整的类型提示和检查'
    }
  ],

  commonUseCases: [
    {
      title: '状态管理Hook',
      description: '管理复杂的本地状态逻辑',
      code: `function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => setCount(c => c + 1), []);
  const decrement = useCallback(() => setCount(c => c - 1), []);
  const reset = useCallback(() => setCount(initialValue), [initialValue]);
  
  return { count, increment, decrement, reset };
}`
    },
    {
      title: '数据获取Hook',
      description: '封装API调用和数据管理逻辑',
      code: `function useApi<T>(url: string) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await fetch(url);
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [url]);
  
  return { data, loading, error };
}`
    },
    {
      title: '本地存储Hook',
      description: '与localStorage的交互逻辑',
      code: `function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error('保存到localStorage失败:', error);
    }
  };

  return [storedValue, setValue] as const;
}`
    }
  ],

  bestPractices: [
    '自定义Hook必须以"use"开头，遵循Hook命名约定',
    '只在函数组件或其他Hook中调用，不能在循环、条件语句或嵌套函数中调用',
    '每个Hook应该有明确的单一职责，避免承担过多功能',
    '合理设计返回值结构，便于使用和理解',
    '使用TypeScript定义清晰的参数和返回值类型',
    '对复杂的返回值使用useMemo和useCallback进行优化',
    '为Hook编写单元测试，确保逻辑的正确性',
    '提供清晰的文档说明Hook的用途、参数和返回值'
  ],

  warnings: [
    '必须遵循React Hook的使用规则，违反规则会导致错误',
    '注意Hook的性能影响，避免不必要的重新计算和渲染',
    '小心处理副作用的清理，避免内存泄漏',
    '避免在Hook中直接修改状态对象，要创建新的对象或数组'
  ],

  limitations: [
    '只能在函数组件或其他Hook中使用，不能在类组件中使用',
    '不能在条件语句、循环或嵌套函数中调用',
    'Hook的调用顺序必须在每次渲染时保持一致',
    '复杂的Hook可能增加调试难度',
    '过度抽象可能导致代码理解困难'
  ]
};

export default basicInfo; 