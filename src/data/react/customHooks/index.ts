import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import knowledgeArchaeology from './knowledge-archaeology';
import essenceInsights from './essence-insights';

const customHooksApi: ApiItem = {
  id: 'CustomHooks',
  title: '自定义Hooks',
  subtitle: 'React自定义Hook，将组件逻辑提取到可重用的函数中，是函数组件共享状态逻辑的最佳方式',
  description: '自定义Hook是React中实现逻辑复用的强大机制，通过以"use"开头的函数，可以提取和共享组件之间的状态逻辑。自定义Hook本质上是调用其他Hook的JavaScript函数，让你能够在多个组件之间共享复杂的逻辑。',
  
  // 必需的基础字段
  syntax: 'function useCustomHook(params?: any) { /* Hook逻辑 */ return result; }',
  example: `import React, { useState, useEffect, useCallback } from 'react';

// 自定义Hook：本地存储管理
function useLocalStorage<T>(key: string, initialValue: T) {
  // 从localStorage读取初始值
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn('读取localStorage失败: ' + key, error);
      return initialValue;
    }
  });

  // 设置值到state和localStorage
  const setValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error('保存到localStorage失败: ' + key, error);
    }
  }, [key, storedValue]);

  // 清除localStorage中的值
  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue);
      window.localStorage.removeItem(key);
    } catch (error) {
      console.error('删除localStorage失败: ' + key, error);
    }
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue] as const;
}

// 使用自定义Hook的组件
function UserSettings() {
  const [theme, setTheme, clearTheme] = useLocalStorage('theme', 'light');
  const [settings, setSettings] = useLocalStorage('userSettings', {
    notifications: true,
    autoSave: false,
    language: 'zh-CN'
  });

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const updateSettings = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className={theme === 'light' ? 'app app-light' : 'app app-dark'}>
      <h2>用户设置</h2>
      
      <div className="setting-group">
        <label>主题模式</label>
        <button onClick={toggleTheme}>
          {theme === 'light' ? '🌞 浅色模式' : '🌙 深色模式'}
        </button>
        <button onClick={clearTheme}>重置</button>
      </div>

      <div className="setting-group">
        <label>
          <input
            type="checkbox"
            checked={settings.notifications}
            onChange={(e) => updateSettings('notifications', e.target.checked)}
          />
          启用通知
        </label>
      </div>

      <div className="setting-group">
        <label>
          <input
            type="checkbox"
            checked={settings.autoSave}
            onChange={(e) => updateSettings('autoSave', e.target.checked)}
          />
          自动保存
        </label>
      </div>

      <div className="setting-group">
        <label>语言设置</label>
        <select 
          value={settings.language} 
          onChange={(e) => updateSettings('language', e.target.value)}
        >
          <option value="zh-CN">中文</option>
          <option value="en-US">English</option>
          <option value="ja-JP">日本語</option>
        </select>
      </div>
    </div>
  );
}

export default UserSettings;`,
  
  // 基础信息
  basicInfo,
  
  // 业务场景（3个不同复杂度）
  businessScenarios,
  
  // 实现原理
  implementation,
  
  // 面试题目（5个高质量题目）
  interviewQuestions,
  
  // 常见问题（4个实际问题）
  commonQuestions,
  
  // 性能优化
  performanceOptimization,
  
  // 调试技巧
  debuggingTips,
  
  // 知识考古
  knowledgeArchaeology,

  // 本质洞察
  essenceInsights,

  // 元数据
  category: 'React Hooks',
  tags: ['Hook', '逻辑复用', '状态共享', '组件设计', '函数式编程'],
  difficulty: 'intermediate',
  
  // 统计信息
  readTime: '25分钟',
  practiceTime: '45分钟',
  
  // 相关主题
  relatedAPIs: ['useState', 'useEffect', 'useContext', 'useCallback', 'useMemo'],
  
  // 版本信息
  version: 'React 16.8+',
  
  // 更新信息
  lastUpdated: '2024-12-27',
  
  // 完成状态
  isCompleted: true,
  completedTabs: [
    'basicInfo',
    'businessScenarios', 
    'implementation',
    'interviewQuestions',
    'commonQuestions',
    'performanceOptimization',
    'debuggingTips',
    'knowledgeArchaeology',
    'essenceInsights'
  ]
};

export default customHooksApi; 