import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  introduction: `自定义Hook的调试需要特别关注Hook的规则遵循、状态管理逻辑、副作用处理以及组件间的依赖关系。由于自定义Hook封装了复杂的逻辑，调试时需要同时考虑Hook内部实现和使用它的组件的行为。

掌握自定义Hook的调试技巧，不仅能快速定位问题，还能帮助你编写更健壮、更易维护的自定义Hook。`,

  troubleshooting: [
    {
      symptom: "Hook在不同组件中行为不一致",
      possibleCauses: [
        "Hook内部使用了闭包，捕获了过时的值",
        "Hook依赖外部变量，不同组件传入不同的值",
        "Hook内部的useEffect依赖项设置不正确",
        "Hook返回的函数没有使用useCallback优化"
      ],
      solutions: [
        "使用useRef保存最新的值，避免闭包陷阱",
        "确保Hook的所有依赖都通过参数传入",
        "仔细检查useEffect的依赖数组",
        "对返回的函数使用useCallback包装",
        "为Hook添加调试日志，跟踪状态变化"
      ]
    },
    {
      symptom: "Hook引起无限重新渲染",
      possibleCauses: [
        "Hook返回的对象每次都是新的引用",
        "useEffect的依赖项包含不稳定的引用",
        "Hook内部的状态更新逻辑有问题",
        "Hook的返回值没有正确memoization"
      ],
      solutions: [
        "使用useMemo包装返回的对象",
        "确保依赖项的引用稳定性",
        "检查状态更新是否基于最新状态",
        "对复杂的返回值使用适当的缓存策略",
        "添加渲染计数器监控重新渲染次数"
      ]
    },
    {
      symptom: "Hook的副作用没有正确清理",
      possibleCauses: [
        "useEffect的清理函数缺失或不完整",
        "异步操作没有正确取消",
        "事件监听器没有移除",
        "定时器没有清除"
      ],
      solutions: [
        "为每个useEffect添加适当的清理函数",
        "使用AbortController取消网络请求",
        "在清理函数中移除所有事件监听器",
        "清除所有定时器和间隔器",
        "使用React DevTools检查组件卸载时的行为"
      ]
    },
    {
      symptom: "Hook的性能问题",
      possibleCauses: [
        "Hook内部计算量过大",
        "频繁的状态更新",
        "没有适当的缓存机制",
        "Hook被不必要地重新创建"
      ],
      solutions: [
        "使用useMemo缓存昂贵的计算",
        "合并多个状态更新",
        "实现防抖或节流机制",
        "确保Hook的创建条件稳定",
        "使用React Profiler分析性能瓶颈"
      ]
    }
  ],

  debuggingTools: [
    {
      name: "React DevTools Profiler",
      purpose: "分析Hook的性能影响",
      usage: `
// 使用Profiler包装使用Hook的组件
import { Profiler } from 'react';

function App() {
  const onRenderCallback = (id, phase, actualDuration) => {
    console.log('组件渲染性能:', {
      id, phase, actualDuration
    });
  };

  return (
    <Profiler id="CustomHookComponent" onRender={onRenderCallback}>
      <ComponentUsingCustomHook />
    </Profiler>
  );
}`,
      example: "适用于定位Hook导致的性能问题，特别是重复渲染和计算量大的情况"
    },
    {
      name: "自定义调试Hook",
      purpose: "跟踪Hook的状态变化和调用时机",
      usage: `
// 创建调试工具Hook
function useDebugHook(hookName, values) {
  const renderCount = useRef(0);
  renderCount.current++;
  
  useEffect(() => {
    console.group(hookName + ' Debug Info');
    console.log('渲染次数:', renderCount.current);
    console.log('当前值:', values);
    console.log('时间:', new Date().toISOString());
    console.groupEnd();
  });
  
  return values;
}

// 在自定义Hook中使用
function useCustomCounter(initialValue) {
  const [count, setCount] = useState(initialValue);
  
  // 添加调试
  useDebugHook('useCustomCounter', { count, initialValue });
  
  return [count, setCount];
}`,
      example: "帮助理解Hook的调用频率和参数变化，快速定位问题所在"
    },
    {
      name: "状态变化跟踪器",
      purpose: "监控Hook内部状态的变化历史",
      usage: `
function useStateTracker(initialState, hookName) {
  const [state, setState] = useState(initialState);
  const history = useRef([initialState]);
  
  const trackedSetState = useCallback((newState) => {
    const finalState = typeof newState === 'function' 
      ? newState(state) 
      : newState;
    
    history.current.push({
      state: finalState,
      timestamp: Date.now(),
      stackTrace: new Error().stack
    });
    
    console.log(hookName + ' 状态变化:', {
      from: state,
      to: finalState,
      history: history.current.slice(-5) // 显示最近5次变化
    });
    
    setState(finalState);
  }, [state, hookName]);
  
  return [state, trackedSetState];
}`,
      example: "记录状态变化的完整历史，便于追踪问题的根源"
    },
    {
      name: "Hook依赖检查器",
      purpose: "验证Hook依赖项的正确性",
      usage: `
function useDependencyChecker(dependencies, hookName) {
  const prevDeps = useRef();
  const renderCount = useRef(0);
  
  renderCount.current++;
  
  useEffect(() => {
    if (prevDeps.current) {
      const changedDeps = dependencies.map((dep, index) => ({
        index,
        prev: prevDeps.current[index],
        current: dep,
        changed: prevDeps.current[index] !== dep
      })).filter(item => item.changed);
      
      if (changedDeps.length > 0) {
        console.warn(hookName + ' 依赖项变化:', changedDeps);
      }
    }
    
    prevDeps.current = [...dependencies];
  });
  
  console.log(hookName + ' 第', renderCount.current, '次渲染');
}`,
      example: "检查useEffect和useCallback的依赖项是否正确设置"
    }
  ],

  commonMistakes: [
    {
      mistake: "在Hook中直接修改状态对象",
      example: `
// ❌ 错误做法
function useUserProfile(userId) {
  const [profile, setProfile] = useState({});
  
  const updateName = (name) => {
    profile.name = name; // 直接修改状态
    setProfile(profile); // 不会触发重新渲染
  };
  
  return { profile, updateName };
}`,
      solution: `
// ✅ 正确做法
function useUserProfile(userId) {
  const [profile, setProfile] = useState({});
  
  const updateName = useCallback((name) => {
    setProfile(prev => ({
      ...prev,
      name // 创建新对象
    }));
  }, []);
  
  return { profile, updateName };
}`
    },
    {
      mistake: "Hook返回不稳定的引用",
      example: `
// ❌ 错误做法
function useApiData(url) {
  const [data, setData] = useState(null);
  
  // 每次渲染都返回新对象
  return {
    data,
    refresh: () => fetchData(url),
    isLoading: data === null
  };
}`,
      solution: `
// ✅ 正确做法
function useApiData(url) {
  const [data, setData] = useState(null);
  
  const refresh = useCallback(() => {
    fetchData(url).then(setData);
  }, [url]);
  
  const result = useMemo(() => ({
    data,
    refresh,
    isLoading: data === null
  }), [data, refresh]);
  
  return result;
}`
    },
    {
      mistake: "忽略Hook的清理逻辑",
      example: `
// ❌ 错误做法
function useWebSocket(url) {
  const [socket, setSocket] = useState(null);
  
  useEffect(() => {
    const ws = new WebSocket(url);
    setSocket(ws);
    // 没有清理函数
  }, [url]);
  
  return socket;
}`,
      solution: `
// ✅ 正确做法
function useWebSocket(url) {
  const [socket, setSocket] = useState(null);
  
  useEffect(() => {
    const ws = new WebSocket(url);
    setSocket(ws);
    
    return () => {
      ws.close(); // 清理WebSocket连接
      setSocket(null);
    };
  }, [url]);
  
  return socket;
}`
    }
  ],

  bestPractices: [
    "在Hook中使用TypeScript定义清晰的类型，便于调试时理解数据结构",
    "为复杂的Hook编写单元测试，确保各种边界情况都能正确处理",
    "使用ESLint的react-hooks/exhaustive-deps规则检查依赖项",
    "在开发环境中添加适当的警告和错误信息",
    "使用React DevTools的Hook调试功能查看Hook的状态",
    "保持Hook的单一职责原则，避免一个Hook承担过多功能",
    "使用描述性的Hook名称，便于调试时快速定位问题",
    "在Hook中添加参数验证，确保传入的参数符合预期",
    "使用React.StrictMode开发环境下的双重调用检测副作用问题"
  ],

  preventionTips: [
    "设计Hook时就考虑可调试性，添加必要的日志和检查点",
    "使用一致的错误处理模式，便于统一调试和监控",
    "为Hook创建详细的文档，说明参数、返回值和副作用",
    "使用代码分割和懒加载减少Hook的复杂度",
    "定期review Hook的代码，确保遵循React的最佳实践",
    "建立Hook的测试策略，包括单元测试和集成测试",
    "使用性能监控工具定期检查Hook的性能表现",
    "在团队中建立Hook开发的编码规范和review流程"
  ]
};

export default debuggingTips; 