import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const flushSyncData: ApiItem = {
  id: 'flushSync',
  title: 'flushSync',
  description: 'ReactDOM API，用于强制同步执行状态更新并立即应用DOM变更，突破React批处理机制。',
  category: 'ReactDOM APIs',
  difficulty: 'medium',
  
  syntax: `// 基础语法
flushSync(callback: () => void)

// TypeScript接口
function flushSync<R>(fn: () => R): R;

// 使用示例
flushSync(() => {
  setCount(count + 1);
});`,

  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default flushSyncData; 