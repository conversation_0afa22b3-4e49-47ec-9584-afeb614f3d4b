import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'flushSync API 功能强大但使用不当会带来性能和行为问题。本节介绍使用此API时常见的错误模式及其解决方案。',
        sections: [
          {
            title: '性能陷阱',
            description: 'flushSync 打破React批处理机制，过度使用会导致严重性能问题',
            items: [
              {
                title: '循环中使用flushSync',
                description: '在循环中使用flushSync会强制每次循环都进行完整渲染，导致性能崩溃',
                solution: '将状态更新集中在单个flushSync调用中，或使用普通状态更新让React自动批处理',
                prevention: '审查代码中所有flushSync调用，确保不在循环或频繁触发的事件处理程序中使用',
                code: `// ❌ 错误用法：循环中使用flushSync
function updateItems(items) {
  // 每次循环都强制完整渲染，性能灾难
  items.forEach(item => {
    flushSync(() => {
      setItems(prev => [...prev, item]);
    });
  });
}

// ✅ 正确用法：单次更新或标准批处理
function updateItems(items) {
  // 方法1: 单个flushSync包裹所有更新
  flushSync(() => {
    setItems(prev => [...prev, ...items]);
  });
  
  // 方法2: 使用普通更新，让React自动批处理
  setItems(prev => [...prev, ...items]);
}`
              },
              {
                title: '嵌套使用flushSync',
                description: '在flushSync回调内部嵌套另一个flushSync，导致不必要的多次同步更新',
                solution: '合并多个flushSync调用，或确保它们不嵌套',
                prevention: '编写清晰的状态更新逻辑，避免嵌套的flushSync调用',
                code: `// ❌ 错误用法：嵌套flushSync
flushSync(() => {
  setCount(count + 1);
  
  flushSync(() => {
    setFlag(true); // 内部嵌套的flushSync是多余的
  });
});

// ✅ 正确用法：合并flushSync调用
flushSync(() => {
  setCount(count + 1);
  setFlag(true);
});`
              },
              {
                title: '在大型组件树中过度使用flushSync',
                description: '在大型组件树中使用flushSync会强制整个树重新渲染，造成性能下降',
                solution: '将flushSync限制在小型、独立的组件中，或使用其他替代方案如useLayoutEffect',
                prevention: '在大型应用中谨慎使用flushSync，监控性能指标',
                code: `// ❌ 问题用法：在应用根组件使用flushSync
function App() {
  // 这将导致整个应用树同步重渲染
  const handleGlobalAction = () => {
    flushSync(() => {
      setGlobalState(newState);
    });
  };
  
  return <ComplexAppTree />;
}

// ✅ 改进用法：限制在小型组件内使用
function SmallFeature() {
  // 只影响此组件及其子组件
  const handleLocalAction = () => {
    flushSync(() => {
      setLocalState(newState);
    });
  };
  
  return <SimpleComponentTree />;
}`
              }
            ]
          },
          {
            title: '行为一致性问题',
            description: 'flushSync 改变了React的正常渲染行为，可能导致难以预测的结果',
            items: [
              {
                title: '依赖立即DOM更新的脆弱代码',
                description: '编写依赖flushSync后立即访问DOM的代码，可能在React未来版本中出现问题',
                solution: '优先使用useLayoutEffect进行DOM测量，并遵循React数据流',
                prevention: '遵循React声明式编程模型，减少对DOM直接操作的依赖',
                code: `// ❌ 脆弱代码：依赖flushSync后立即访问DOM
function MeasureAfterUpdate() {
  const boxRef = useRef(null);
  
  const handleResize = () => {
    flushSync(() => {
      setDimensions({ width: 100, height: 100 });
    });
    
    // 假设DOM已经更新，直接测量
    // 这在未来React版本中可能不可靠
    const { width, height } = boxRef.current.getBoundingClientRect();
    console.log(width, height);
  };
  
  return <div ref={boxRef} style={dimensions} />;
}

// ✅ 健壮代码：使用useLayoutEffect测量
function MeasureAfterUpdate() {
  const boxRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  
  const handleResize = () => {
    setDimensions({ width: 100, height: 100 });
  };
  
  useLayoutEffect(() => {
    // 这里保证DOM已更新
    const { width, height } = boxRef.current.getBoundingClientRect();
    console.log(width, height);
  }, [dimensions]);
  
  return <div ref={boxRef} style={dimensions} />;
}`
              },
              {
                title: '异步上下文中的flushSync',
                description: 'flushSync在异步回调中使用可能与预期行为不符',
                solution: '理解flushSync在异步上下文中的行为，必要时使用其他解决方案',
                prevention: '在计划使用flushSync的异步上下文中测试其实际行为',
                code: `// ❌ 可能出乎意料的异步flushSync
function AsyncFlushExample() {
  const handleClick = () => {
    // 启动异步操作
    Promise.resolve().then(() => {
      console.log('Before state update');
      
      flushSync(() => {
        setCount(c => c + 1);
      });
      
      // 在React 18中，这里可能不会看到最新的DOM
      // 因为微任务可能在paint之前执行
      console.log('DOM updated?', document.getElementById('count').textContent);
    });
  };
  
  return (
    <div>
      <button onClick={handleClick}>Update</button>
      <div id="count">{count}</div>
    </div>
  );
}

// ✅ 更可靠的方法：结合requestAnimationFrame
function ReliableAsyncUpdate() {
  const handleClick = () => {
    // 启动异步操作
    Promise.resolve().then(() => {
      flushSync(() => {
        setCount(c => c + 1);
      });
      
      // 使用rAF确保在下一帧渲染后执行
      requestAnimationFrame(() => {
        console.log('Now DOM is definitely updated', 
          document.getElementById('count').textContent);
      });
    });
  };
}`
              }
            ]
          },
          {
            title: '并发模式兼容性',
            description: '在React 18并发模式下，flushSync可能与其他功能产生复杂交互',
            items: [
              {
                title: 'flushSync与Suspense交互',
                description: 'flushSync在Suspense边界内使用可能导致意外行为',
                solution: '避免在可能触发Suspense的组件中使用flushSync',
                prevention: '谨慎管理flushSync与Suspense的边界，必要时分离它们',
                code: `// ❌ 潜在问题：flushSync与Suspense交互
function DataComponent() {
  const [resource, setResource] = useState(initialResource);
  
  const refreshData = () => {
    flushSync(() => {
      // 这可能触发Suspense
      setResource(fetchNewData());
    });
    
    // 如果上面的更新触发了Suspense
    // 这里的代码可能不会按预期执行
    console.log('Data refreshed');
    measureUpdatedDOM();
  };
  
  return (
    <Suspense fallback={<Spinner />}>
      <DataDisplay resource={resource} />
      <button onClick={refreshData}>Refresh</button>
    </Suspense>
  );
}

// ✅ 改进方法：分离关注点
function DataContainer() {
  const [resource, setResource] = useState(initialResource);
  
  // 控制刷新但不执行DOM测量
  const refreshData = () => {
    setResource(fetchNewData());
  };
  
  return (
    <>
      <Suspense fallback={<Spinner />}>
        <DataDisplay resource={resource} />
      </Suspense>
      
      {/* 测量DOM的组件在Suspense边界外 */}
      <RefreshControl 
        onRefresh={refreshData}
        resource={resource} 
      />
    </>
  );
}

// 此组件使用flushSync但不直接与Suspense交互
function RefreshControl({ onRefresh, resource }) {
  const buttonRef = useRef(null);
  
  const handleRefresh = () => {
    onRefresh();
    
    // 仅测量按钮自身，不涉及Suspense内容
    flushSync(() => {
      setButtonState('loading');
    });
    measureButtonChanges();
  };
}`
              },
              {
                title: 'flushSync与transitions冲突',
                description: 'flushSync会打断并覆盖transition标记的低优先级更新',
                solution: '避免在同一更新流程中混用flushSync与useTransition',
                prevention: '设计清晰的更新优先级策略，不混合使用这些API',
                code: `// ❌ 错误用法：flushSync与transitions混用
function SearchComponent() {
  const [isPending, startTransition] = useTransition();
  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState([]);
  
  const handleSearch = (term) => {
    // 这是低优先级更新
    startTransition(() => {
      setSearchTerm(term);
    });
    
    // 错误：这会打断上面的transition
    flushSync(() => {
      setResults([]); // 清空结果
    });
    
    // 开始搜索...
  };
  
  return (/* 组件JSX */);
}

// ✅ 正确用法：清晰的优先级分离
function SearchComponent() {
  const [isPending, startTransition] = useTransition();
  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState([]);
  
  const handleSearch = (term) => {
    // 高优先级更新放在一起
    flushSync(() => {
      setResults([]); // 清空结果是高优先级的
    });
    
    // 低优先级更新分开处理
    startTransition(() => {
      setSearchTerm(term); // 搜索词更新是低优先级的
    });
  };
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🛠️ 调试工具',
      content: {
        introduction: '调试flushSync相关问题需要特殊工具和技术，本节介绍有效识别和解决这些问题的方法。',
        sections: [
          {
            title: 'React DevTools配合使用',
            description: '使用React DevTools分析flushSync引起的渲染问题',
            items: [
              {
                title: 'Profiler识别同步渲染',
                description: 'React DevTools Profiler可以帮助识别flushSync导致的同步渲染',
                solution: '使用Profiler记录应用运行，寻找渲染时间异常的组件',
                steps: [
                  '启动React DevTools并切换到Profiler标签',
                  '点击"Record"开始记录应用行为',
                  '执行包含flushSync调用的操作',
                  '停止记录并查看渲染时间火焰图',
                  '寻找渲染时间显著高于平均值的组件',
                  '检查这些组件是否直接或间接受到flushSync影响'
                ]
              },
              {
                title: '监测批处理被打断',
                description: '使用DevTools Components面板确认更新是否按预期分批',
                solution: '通过观察组件高亮频率判断批处理是否被打断',
                steps: [
                  '打开React DevTools Components面板',
                  '启用"Highlight updates when components render"选项',
                  '执行正常的状态更新（应看到一次高亮闪烁）',
                  '执行包含flushSync的操作',
                  '观察是否出现多次连续高亮，表明批处理被打断'
                ]
              }
            ]
          },
          {
            title: 'flushSync专用调试工具',
            description: '针对flushSync特定问题的调试技术和工具',
            items: [
              {
                title: '跟踪flushSync执行时机',
                description: '创建包装函数跟踪所有flushSync调用',
                solution: '使用自定义封装追踪flushSync调用',
                code: `// flushSync调试包装函数
import { flushSync as originalFlushSync } from 'react-dom';

// 替换原始flushSync以添加调试功能
export const flushSync = (callback) => {
  console.group('flushSync called');
  console.trace('Call stack');
  
  const startTime = performance.now();
  
  try {
    // 调用原始flushSync
    return originalFlushSync(callback);
  } catch (error) {
    console.error('flushSync error:', error);
    throw error;
  } finally {
    const duration = performance.now() - startTime;
    console.log('flushSync duration:', duration.toFixed(2) + 'ms');
    console.groupEnd();
  }
};

// 使用方法：替换导入
// import { flushSync } from './debugFlushSync';`
              },
              {
                title: 'DOM更新确认工具',
                description: '验证flushSync后DOM是否按预期更新',
                solution: '创建工具函数验证DOM更新',
                code: `// DOM更新验证工具
function verifyDomUpdate(callback, elementId, expectedChange) {
  // 记录更新前状态
  const before = document.getElementById(elementId)?.textContent;
  console.log(\`Before update: "\${before}"\`);
  
  // 执行flushSync更新
  flushSync(callback);
  
  // 验证DOM是否立即更新
  const after = document.getElementById(elementId)?.textContent;
  console.log(\`After update: "\${after}"\`);
  
  if (expectedChange && expectedChange(before, after)) {
    console.log('✅ DOM updated as expected');
  } else {
    console.warn('⚠️ DOM update might not match expectation');
  }
  
  // 额外检查：等待下一帧再次检查
  requestAnimationFrame(() => {
    const final = document.getElementById(elementId)?.textContent;
    if (final !== after) {
      console.error('⛔ DOM changed again in next frame!', final);
    } else {
      console.log('✓ DOM stable in next frame');
    }
  });
}

// 使用示例
verifyDomUpdate(
  () => setCount(count + 1),
  'counter-display',
  (before, after) => parseInt(after) === parseInt(before) + 1
);`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'troubleshooting',
      title: '🔍 排查指南',
      content: {
        introduction: '系统性的flushSync问题排查流程和常见症状诊断。',
        sections: [
          {
            title: '常见症状诊断',
            description: '根据常见现象快速定位flushSync问题',
            items: [
              {
                title: '性能突然下降',
                description: '应用在特定操作后性能显著下降',
                solution: '系统检查flushSync使用情况',
                steps: [
                  '使用Performance面板记录性能问题',
                  '检查是否有长时间的"Rendering"或"Painting"阶段',
                  '查找代码中所有flushSync调用',
                  '特别关注循环中或事件处理程序中的flushSync',
                  '尝试替换为标准更新或优化批处理策略',
                  '验证更改后性能是否改善'
                ],
                code: `// 性能问题检测工具
function detectFlushSyncPerformanceIssues() {
  // 保存所有渲染时间
  const renderTimes = [];
  let flushSyncDetected = false;
  
  // 替换console.trace以检测flushSync
  const originalTrace = console.trace;
  console.trace = function(...args) {
    if (args.join(' ').includes('flushSync')) {
      flushSyncDetected = true;
      console.warn('flushSync detected in call stack');
    }
    originalTrace.apply(this, args);
  };
  
  // 监控渲染性能
  const observer = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    
    entries.forEach(entry => {
      if (entry.name.includes('render')) {
        renderTimes.push({
          duration: entry.duration,
          timestamp: entry.startTime,
          flushSyncActive: flushSyncDetected
        });
        
        if (flushSyncDetected && entry.duration > 16) {
          console.warn(\`Possible flushSync performance issue detected! Render took \${entry.duration.toFixed(2)}ms\`);
        }
        
        flushSyncDetected = false;
      }
    });
  });
  
  // 开始观察渲染性能
  observer.observe({ entryTypes: ['measure'] });
  
  // 手动添加性能标记
  function trackRender(id) {
    performance.mark(\`render-start-\${id}\`);
    return () => {
      performance.mark(\`render-end-\${id}\`);
      performance.measure(
        \`render-\${id}\`, 
        \`render-start-\${id}\`, 
        \`render-end-\${id}\`
      );
    };
  }
  
  // 返回清理函数和工具函数
  return {
    trackRender,
    getRenderStats: () => renderTimes,
    cleanup: () => {
      observer.disconnect();
      console.trace = originalTrace;
    }
  };
}`
              },
              {
                title: 'DOM测量不一致',
                description: '使用flushSync后DOM测量结果不符合预期',
                solution: '理解渲染时序并调整测量策略',
                steps: [
                  '确认flushSync调用是否成功完成（无错误）',
                  '验证DOM引用是否正确（ref.current存在）',
                  '检查是否有CSS过渡效果干扰测量',
                  '考虑浏览器渲染时序问题',
                  '尝试使用requestAnimationFrame额外延迟测量',
                  '考虑切换到useLayoutEffect方案'
                ],
                code: `// DOM测量调试辅助工具
function debugDomMeasurements(elementRef, updateFn) {
  if (!elementRef.current) {
    console.error('Element ref is not attached');
    return;
  }
  
  // 记录初始状态
  const initialRect = elementRef.current.getBoundingClientRect();
  console.log('Initial dimensions:', {
    width: initialRect.width,
    height: initialRect.height
  });
  
  // 执行更新
  console.log('Executing update with flushSync');
  try {
    flushSync(updateFn);
  } catch (e) {
    console.error('Error in flushSync:', e);
    return;
  }
  
  // 立即测量
  const immediateRect = elementRef.current.getBoundingClientRect();
  console.log('Immediate measurements after flushSync:', {
    width: immediateRect.width,
    height: immediateRect.height,
    changedWidth: immediateRect.width !== initialRect.width,
    changedHeight: immediateRect.height !== initialRect.height
  });
  
  // 检查计算样式
  const computedStyle = window.getComputedStyle(elementRef.current);
  console.log('Computed style:', {
    width: computedStyle.width,
    height: computedStyle.height,
    transition: computedStyle.transition,
    display: computedStyle.display,
    position: computedStyle.position
  });
  
  // rAF检查
  requestAnimationFrame(() => {
    const rafRect = elementRef.current.getBoundingClientRect();
    console.log('Measurements in next frame:', {
      width: rafRect.width,
      height: rafRect.height,
      changedFromImmediate: (
        rafRect.width !== immediateRect.width || 
        rafRect.height !== immediateRect.height
      )
    });
    
    // 再检查一次以确认稳定
    requestAnimationFrame(() => {
      const finalRect = elementRef.current.getBoundingClientRect();
      console.log('Final measurements after two frames:', {
        width: finalRect.width,
        height: finalRect.height,
        stable: (
          finalRect.width === rafRect.width && 
          finalRect.height === rafRect.height
        )
      });
    });
  });
}`
              }
            ]
          },
          {
            title: '典型错误模式',
            description: 'flushSync使用中的常见错误模式识别',
            items: [
              {
                title: '使用flushSync却仍在读取旧DOM',
                description: '即使使用了flushSync，仍然读取到旧的DOM状态',
                solution: '确保正确的执行顺序和DOM访问时机',
                prevention: '理解React渲染周期和DOM更新时序',
                code: `// ❌ 常见错误模式
function MeasurementBug() {
  const boxRef = useRef(null);
  const [size, setSize] = useState(100);
  
  const handleResize = () => {
    // 看似正确，但可能有问题
    flushSync(() => {
      setSize(200);
    });
    
    // 问题：这里仍然可能测量到旧尺寸
    // React虽然同步处理了状态更新，但浏览器可能尚未完成渲染
    console.log(boxRef.current.offsetWidth); // 可能仍是100
  };
  
  return (
    <div>
      <div 
        ref={boxRef} 
        style={{width: size, height: size}}
      />
      <button onClick={handleResize}>Resize</button>
    </div>
  );
}

// ✅ 更可靠的方法
function ReliableMeasurement() {
  const boxRef = useRef(null);
  const [size, setSize] = useState(100);
  const sizeRef = useRef(size);
  
  // 使用useLayoutEffect确保在DOM更新后测量
  useLayoutEffect(() => {
    if (size !== sizeRef.current) {
      sizeRef.current = size;
      console.log('真实DOM尺寸:', boxRef.current.offsetWidth);
    }
  }, [size]);
  
  const handleResize = () => {
    setSize(200);
    // 不再需要flushSync和测量
  };
  
  return (
    <div>
      <div 
        ref={boxRef} 
        style={{width: size, height: size}}
      />
      <button onClick={handleResize}>Resize</button>
    </div>
  );
}`
              }
            ]
          }
        ]
      }
    }
  ],
  
  // 兼容旧版本的平铺结构
  troubleshooting: [
    {
      symptom: "使用flushSync后应用性能明显下降",
      possibleCauses: [
        "循环或频繁事件中使用flushSync",
        "大型组件树中过度使用flushSync",
        "嵌套flushSync调用导致重复渲染",
        "与Concurrent Mode功能冲突"
      ],
      solutions: [
        "限制flushSync使用，仅在必要场景（如DOM测量）中使用",
        "合并多个状态更新到单个flushSync调用中",
        "使用性能分析工具识别问题区域，考虑替代方案如useLayoutEffect",
        "遵循React数据流，减少对立即DOM更新的依赖"
      ]
    },
    {
      symptom: "flushSync后DOM测量结果不符预期",
      possibleCauses: [
        "浏览器渲染管道延迟实际DOM更新",
        "CSS过渡效果干扰测量",
        "组件渲染出错但没有报错",
        "React并发特性与flushSync交互"
      ],
      solutions: [
        "使用requestAnimationFrame延迟DOM测量",
        "添加错误边界捕获可能的渲染错误",
        "考虑使用useLayoutEffect代替flushSync进行测量",
        "检查DOM元素是否确实存在并可测量"
      ]
    },
    {
      symptom: "React 18中flushSync行为与之前版本不同",
      possibleCauses: [
        "React 18的自动批处理机制改变",
        "并发渲染与Suspense交互",
        "useTransition等新API与flushSync冲突",
        "客户端与服务器渲染差异"
      ],
      solutions: [
        "更新对React 18中flushSync行为的理解",
        "避免flushSync与useTransition、Suspense等并发特性混用",
        "使用React DevTools检查实际渲染行为",
        "考虑使用React 18友好的替代方案"
      ]
    }
  ]
};

export default debuggingTips; 