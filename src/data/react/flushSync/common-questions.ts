import { CommonQuestion } from '@/types/api';

const data: CommonQuestion[] = [
  {
    id: 1,
    question: "flushSync的基本用法是什么？",
    answer: `flushSync的基本用法是将需要同步执行的状态更新包装在一个回调函数中：

\`\`\`jsx
import { flushSync } from 'react-dom';

function handleClick() {
  // 使用flushSync包装状态更新
  flushSync(() => {
    setCount(count + 1);
  });
  
  // 此时DOM已经更新，可以立即获取最新DOM信息
  console.log(document.getElementById('counter').textContent); // 显示更新后的值
}
\`\`\`

flushSync接受一个回调函数作为参数，该回调函数中的所有状态更新都会被同步执行，并立即应用到DOM。这意味着在flushSync调用完成后，DOM已经反映了最新的状态变化。

注意事项：
1. flushSync会破坏React的批处理优化，可能影响性能
2. 应该只在真正需要立即DOM更新的场景使用
3. React 18中，自动批处理机制使得flushSync的使用场景更加明确`,
    category: "使用方法",
    tags: ["入门问题", "基础用法", "ReactDOM API"],
    completionStatus: '内容已完成',
  },
  {
    id: 2,
    question: "在React 18中，flushSync与自动批处理有什么关系？",
    answer: `在React 18中，flushSync与自动批处理(Automatic Batching)存在密切关系：

**自动批处理的变化**

在React 18之前，React只在事件处理函数中进行批处理（合并多个状态更新为一次渲染）：

\`\`\`jsx
// React 17中
function handleClick() {
  // 在事件处理函数中，这两次更新会被批处理为一次渲染
  setCount(c => c + 1);
  setFlag(f => !f);
}

// 但在异步回调中，每次更新都会单独渲染
setTimeout(() => {
  setCount(c => c + 1); // 触发一次渲染
  setFlag(f => !f);     // 触发另一次渲染
}, 0);
\`\`\`

React 18引入了自动批处理，扩展了批处理范围到所有更新，包括Promise、setTimeout和原生事件处理函数等：

\`\`\`jsx
// React 18中
setTimeout(() => {
  setCount(c => c + 1); // 不会立即渲染
  setFlag(f => !f);     // 这两次更新会被批处理为一次渲染
}, 0);
\`\`\`

**flushSync作为突破口**

在这种环境下，flushSync成为开发者能够打破自动批处理的主要方法：

\`\`\`jsx
// React 18中使用flushSync
setTimeout(() => {
  // 强制同步执行此更新
  flushSync(() => {
    setCount(c => c + 1);
  });
  // 此时DOM已更新
  
  // 这个更新会在新的批处理中执行
  setFlag(f => !f);
}, 0);
\`\`\`

**使用场景**

自动批处理让React应用性能更好，但某些场景确实需要立即访问更新后的DOM：

1. **与第三方库集成**：更新状态后需要立即调用DOM操作的库
2. **测量DOM变化**：如更新尺寸后需要立即测量新尺寸
3. **动画起始状态**：设置初始状态后立即开始动画
4. **复杂的用户反馈**：需要在特定操作后立即显示视觉反馈

**最佳实践**

- 默认依赖React 18的自动批处理机制
- 只在确实需要立即DOM更新的场景使用flushSync
- 如果可能，考虑使用useLayoutEffect替代flushSync
- 避免在性能关键路径中过度使用flushSync`,
    category: "概念理解",
    tags: ["React 18", "批处理", "性能优化"],
    completionStatus: '内容已完成',
  },
  {
    id: 3,
    question: "flushSync会破坏React的批处理机制吗？这会造成什么影响？",
    answer: `是的，flushSync确实会破坏React的批处理机制，这会带来一系列性能和行为影响：

### 如何破坏批处理

flushSync会强制React立即处理其回调函数中的所有状态更新，同步刷新DOM，而不是等待批处理周期结束：

\`\`\`jsx
// 正常的批处理行为
function handleNormalUpdate() {
  setCount(c => c + 1);  // 这些更新会被批处理
  setFlag(true);         // 合并成一次渲染
  setText('Updated');    // 
}

// 使用flushSync破坏批处理
function handleFlushUpdate() {
  flushSync(() => {
    setCount(c => c + 1);  // 这些更新会被立即处理
    setFlag(true);         // 并同步应用到DOM
  });
  
  // 这个更新会在新的批处理周期中处理
  setText('Updated');
}
\`\`\`

### 影响和后果

**1. 性能影响**

- **额外的渲染周期**：每次flushSync调用可能触发完整的渲染周期，包括协调和提交阶段
- **布局抖动**：强制同步DOM更新可能导致浏览器额外的重排和重绘
- **阻塞主线程**：同步更新会阻塞UI线程，可能导致交互延迟

\`\`\`jsx
// 性能问题示例：在循环中使用flushSync
function updateManyItems(items) {
  items.forEach(item => {
    flushSync(() => {
      setItems(prev => [...prev, item]);
    });
    // 每次循环都会触发完整渲染周期！
  });
  // 导致性能严重下降
}
\`\`\`

**2. 行为变化**

- **可观察到中间状态**：多个状态更新不再被合并，用户可能看到中间状态
- **事件处理不同步**：可能改变事件触发的时序
- **与并发模式交互复杂**：可能与React 18的并发特性产生冲突

\`\`\`jsx
// 中间状态可见的例子
function handleTransition() {
  // 用户会看到这个中间状态
  flushSync(() => {
    setStatus('loading');
  });
  
  // 然后可能会看到闪烁
  setTimeout(() => {
    setStatus('success');
  }, 100);
}
\`\`\`

**3. 调试难度增加**

- 组件行为变得不那么可预测
- 可能导致难以追踪的更新顺序问题
- 在不同环境中行为可能不一致

### 如何减轻负面影响

1. **限制使用范围**
   - 只在真正需要立即DOM更新的场景使用
   - 避免在循环或频繁执行的代码中使用

2. **合并多个更新**
   \`\`\`javascript
   // 不好的做法
   flushSync(() => { setA(1); });
   flushSync(() => { setB(2); });
   
   // 更好的做法
   flushSync(() => {
     setA(1);
     setB(2);
   });
   \`\`\`

3. **考虑替代方案**
   - 对于测量DOM：优先使用useLayoutEffect
   - 对于动画：考虑CSS过渡或requestAnimationFrame
   - 对于状态协调：考虑使用reducer合并相关状态

4. **性能监控**
   - 使用React DevTools Profiler监控渲染性能
   - 在使用flushSync前后进行测量比较

总结来说，flushSync确实破坏了React的批处理机制，这是它的设计初衷 - 提供一种方式在必要时突破批处理限制。但这种能力应当谨慎使用，因为它会对性能产生负面影响，并可能导致UI行为变得不那么可预测。`,
    category: "性能影响",
    tags: ["性能优化", "渲染机制", "批处理"],
    completionStatus: '内容已完成',
  },
  {
    id: 4,
    question: "flushSync和useLayoutEffect有什么区别？什么情况下应该使用哪一个？",
    answer: `flushSync和useLayoutEffect都涉及React的同步更新机制，但它们有不同的用途和适用场景。

### 核心区别

| 特性 | flushSync | useLayoutEffect |
|------|-----------|----------------|
| **类型** | 命令式API | 声明式Hook |
| **作用** | 强制同步执行状态更新 | 在DOM更新后同步执行副作用 |
| **调用时机** | 可以在任何时候调用 | 在组件渲染后、浏览器绘制前调用 |
| **批处理关系** | 打破批处理 | 不打破批处理，等待批处理完成 |
| **影响渲染流程** | 强制立即渲染 | 不会触发额外渲染，等待正常渲染 |

### 代码对比

\`\`\`jsx
// 使用flushSync
function MeasureWithFlushSync() {
  const [height, setHeight] = useState(100);
  const boxRef = useRef(null);
  
  const handleClick = () => {
    // 1. 更新状态
    flushSync(() => {
      setHeight(height + 50);
    });
    
    // 2. DOM已更新，立即测量
    console.log('New height:', boxRef.current.offsetHeight); // 150px
  };
  
  return (
    <div>
      <div ref={boxRef} style={{height}}></div>
      <button onClick={handleClick}>Increase Height</button>
    </div>
  );
}

// 使用useLayoutEffect
function MeasureWithLayoutEffect() {
  const [height, setHeight] = useState(100);
  const boxRef = useRef(null);
  const [measuredHeight, setMeasuredHeight] = useState(0);
  
  // 在每次height改变后同步测量
  useLayoutEffect(() => {
    setMeasuredHeight(boxRef.current.offsetHeight);
  }, [height]);
  
  return (
    <div>
      <div ref={boxRef} style={{height}}></div>
      <div>Measured height: {measuredHeight}px</div>
      <button onClick={() => setHeight(height + 50)}>Increase Height</button>
    </div>
  );
}
\`\`\`

### 适用场景

**适合使用flushSync的场景：**

1. **事件处理中需要立即测量DOM**
   \`\`\`jsx
   function handleClick() {
     flushSync(() => setIsOpen(true));
     // 立即测量新打开的元素
     const height = menuRef.current.getBoundingClientRect().height;
     // 根据高度调整位置
     setPosition(calculatePosition(height));
   }
   \`\`\`

2. **第三方库集成需要最新DOM**
   \`\`\`jsx
   function updateChart() {
     flushSync(() => setChartData(newData));
     // 确保DOM更新后调用第三方库API
     chartLibrary.update(chartRef.current);
   }
   \`\`\`

3. **复杂的交互序列需要中间DOM状态**
   \`\`\`jsx
   function handleComplexInteraction() {
     flushSync(() => setStep(1));
     // 做一些基于步骤1的DOM操作
     doSomethingWithDOM();
     flushSync(() => setStep(2));
     // 做一些基于步骤2的DOM操作
     doSomethingElseWithDOM();
   }
   \`\`\`

**适合使用useLayoutEffect的场景：**

1. **组件需要在每次渲染后测量DOM**
   \`\`\`jsx
   function Tooltip() {
     useLayoutEffect(() => {
       // 计算工具提示位置
       const {top, left} = calculatePosition();
       setTooltipStyle({top, left});
     }, [position]);
   }
   \`\`\`

2. **防止闪烁，调整渲染后的元素**
   \`\`\`jsx
   function AutosizeTextarea() {
     useLayoutEffect(() => {
       // 调整文本区域高度以适应内容
       textareaRef.current.style.height = 'auto';
       textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
     }, [value]);
   }
   \`\`\`

3. **DOM操作需要与组件生命周期同步**
   \`\`\`jsx
   function FocusInput() {
     useLayoutEffect(() => {
       // 组件挂载或更新后立即聚焦
       inputRef.current.focus();
     }, [isEditing]);
   }
   \`\`\`

### 选择指南

- **优先使用useLayoutEffect**：如果操作是与组件渲染相关的，需要在每次更新后执行
- **谨慎使用flushSync**：仅用于需要立即打破批处理并获取DOM更新的特定交互场景
- **考虑可维护性**：useLayoutEffect更加声明式，与React组件生命周期整合更好
- **性能考量**：flushSync可能导致额外的渲染周期，而useLayoutEffect只在正常渲染周期后执行

总的来说，flushSync是一个较为底层的命令式API，适用于特定交互场景；而useLayoutEffect是一个更符合React声明式风格的API，适用于组件中需要同步DOM操作的场景。在大多数情况下，应该优先考虑useLayoutEffect，只有在确实需要手动控制更新时序的情况下才使用flushSync。`,
    category: "使用比较",
    tags: ["Hook", "DOM操作", "生命周期"],
    completionStatus: '内容已完成',
  },
  {
    id: 5,
    question: "如何正确地在React 18的并发模式下使用flushSync？",
    answer: `在React 18的并发模式下正确使用flushSync需要理解它与并发特性的交互方式。以下是完整指南：

### 理解核心原则

React 18引入的并发特性(如startTransition、useDeferredValue、Suspense)与flushSync的同步特性存在根本性差异：
- **并发特性**：允许渲染被中断、恢复和丢弃，优先处理更重要的更新
- **flushSync**：强制同步、立即、不可中断地执行更新

### 正确的使用模式

**1. 分离紧急和非紧急更新**

\`\`\`jsx
function SearchComponent() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  
  const handleSearch = (newQuery) => {
    // 紧急更新：UI反馈应该立即显示
    flushSync(() => {
      setQuery(newQuery);
      setIsSearching(true);
    });
    
    // 非紧急更新：数据获取和结果更新可以并发
    startTransition(() => {
      fetchResults(newQuery).then(data => {
        setResults(data);
        setIsSearching(false);
      });
    });
  };
  
  return (
    <div>
      <input 
        value={query} 
        onChange={(e) => handleSearch(e.target.value)} 
      />
      {isSearching && <Spinner />}
      <ResultsList results={results} />
    </div>
  );
}
\`\`\`

**2. 避免在并发更新中使用flushSync**

\`\`\`jsx
// ❌ 错误: 在transition中使用flushSync
function handleAction() {
  startTransition(() => {
    // 这违背了transition的目的
    flushSync(() => {
      setData(newData);
    });
  });
}

// ✅ 正确: 清晰分离同步和并发更新
function handleAction() {
  // 先执行需要同步的部分
  flushSync(() => {
    setStatus('processing');
  });
  
  // 然后进行可以并发的部分
  startTransition(() => {
    setData(newData);
  });
}
\`\`\`

**3. 谨慎处理Suspense边界**

\`\`\`jsx
function DataComponent() {
  const [resource, setResource] = useState(initialResource);
  
  const refreshData = () => {
    // 同步更新加载状态
    flushSync(() => {
      setLoadingState('loading');
    });
    
    // 获取可能触发挂起的数据作为并发更新
    startTransition(() => {
      const newResource = fetchData();
      setResource(newResource);
    });
  };
  
  return (
    <div>
      <button onClick={refreshData}>刷新</button>
      <Suspense fallback={<Spinner />}>
        <DataContent resource={resource} />
      </Suspense>
    </div>
  );
}
\`\`\`

**4. 在useTransition中安全使用flushSync**

\`\`\`jsx
function TransitionComponent() {
  const [isPending, startTransition] = useTransition();
  const [syncState, setSyncState] = useState(false);
  const [asyncState, setAsyncState] = useState(false);
  
  const handleAction = () => {
    // 首先进行同步更新
    flushSync(() => {
      setSyncState(true);
    });
    
    // 然后开始过渡更新
    startTransition(() => {
      // 复杂或可能较慢的更新
      setAsyncState(true);
    });
  };
  
  return (
    <div>
      {syncState && <ImmediateFeedback />}
      {isPending && <LoadingIndicator />}
      {asyncState && <ComplexResult />}
    </div>
  );
}
\`\`\`

### 实际应用场景

**1. 表单提交与反馈**

\`\`\`jsx
function FormWithFeedback() {
  const [formState, setFormState] = useState({});
  const [status, setStatus] = useState('idle');
  const [isPending, startTransition] = useTransition();
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // 同步更新UI状态提供即时反馈
    flushSync(() => {
      setStatus('submitting');
    });
    
    try {
      // 使用并发特性处理异步操作和后续状态更新
      startTransition(async () => {
        const result = await submitForm(formState);
        setStatus('success');
        // 其他后续更新...
      });
    } catch (error) {
      // 错误处理也可以是并发的
      startTransition(() => {
        setStatus('error');
        setError(error);
      });
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* 表单内容 */}
      <SubmitButton 
        status={status} 
        isPending={isPending} 
      />
    </form>
  );
}
\`\`\`

**2. 复杂数据可视化更新**

\`\`\`jsx
function DataVisualization() {
  const [highlight, setHighlight] = useState(null);
  const [dataSet, setDataSet] = useState(initialData);
  const chartRef = useRef(null);
  
  const handleDataPointClick = (point) => {
    // 立即高亮显示点击的数据点
    flushSync(() => {
      setHighlight(point.id);
    });
    
    // 获取并更新相关的数据集可以是并发的
    startTransition(() => {
      const relatedData = fetchRelatedData(point.id);
      setDataSet(relatedData);
    });
    
    // 使用第三方图表库进行DOM更新
    chartRef.current.highlightElement(point.id);
  };
  
  return (
    <div>
      <ChartComponent 
        ref={chartRef}
        data={dataSet}
        highlightedPoint={highlight}
        onPointClick={handleDataPointClick}
      />
    </div>
  );
}
\`\`\`

**3. 多步骤交互流程**

\`\`\`jsx
function WizardFlow() {
  const [step, setStep] = useState(1);
  const [stepData, setStepData] = useState({});
  const [isProcessing, startProcessing] = useTransition();
  
  const goToNextStep = async (currentStepData) => {
    // 同步更新当前步骤数据和UI状态
    flushSync(() => {
      setStepData({...stepData, ...currentStepData});
    });
    
    // 使用并发特性加载下一步数据
    startProcessing(async () => {
      // 可能较慢的数据处理
      await processStepData(currentStepData);
      // 转到下一步
      setStep(step + 1);
    });
  };
  
  return (
    <div>
      <StepIndicator current={step} />
      <StepContent 
        step={step} 
        data={stepData} 
        onComplete={goToNextStep}
        isProcessing={isProcessing}
      />
    </div>
  );
}
\`\`\`

### 最佳实践总结

1. **清晰区分更新类型**
   - 使用flushSync处理需要立即反映在DOM中的UI反馈
   - 使用并发特性(startTransition)处理可以延迟的计算和更新

2. **构建可靠的交互模式**
   - 紧急更新 → 同步 (flushSync)
   - 非紧急更新 → 并发 (startTransition)

3. **避免常见错误**
   - 不要在startTransition回调内部使用flushSync
   - 不要过度使用flushSync，可能抵消并发模式的性能优势
   - 避免在可能触发Suspense的更新中使用flushSync

4. **性能考量**
   - 监控flushSync使用对应用性能的影响
   - 使用React DevTools Profiler比较不同更新策略

通过这些实践，你可以在享受React 18并发模式优势的同时，适当地使用flushSync满足立即更新DOM的需求。`,
    category: "进阶使用",
    tags: ["并发模式", "React 18", "Transition API"],
    completionStatus: '内容已完成',
  }
];

export default data; 