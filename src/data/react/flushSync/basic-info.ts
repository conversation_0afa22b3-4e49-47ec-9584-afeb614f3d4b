import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  completionStatus: '内容已完成',
  
  definition: "flushSync是React中用于强制同步执行更新并立即应用DOM变更的ReactDOM API",
  
  introduction: `flushSync是ReactDOM提供的一个强大但应谨慎使用的API，它允许开发者打破React的批处理更新机制，强制同步执行状态更新并立即应用DOM变更。在React 18的自动批处理机制下，此API变得尤为重要，它能够在特定场景（如与第三方库集成、测量DOM等）中确保DOM立即反映最新的状态变化。然而，过度使用会抵消React批处理带来的性能优势，因此应当有选择地使用。`,

  syntax: `import { flushSync } from 'react-dom';

flushSync(callback);`,

  quickExample: `import { flushSync } from 'react-dom';
import { useState, useRef } from 'react';

function MeasureExample() {
  const [height, setHeight] = useState(0);
  const ref = useRef(null);
  
  const handleClick = () => {
    // 先更新状态
    flushSync(() => {
      setHeight(h => h + 10);
    });
    
    // DOM已更新，可以立即测量
    console.log('新高度:', ref.current.getBoundingClientRect().height);
  };
  
  return (
    <div>
      <div 
        ref={ref}
        style={{
          height: height + 'px',
          background: 'lightblue',
          transition: 'height 0.5s'
        }}
      >
        当前高度: {height}px
      </div>
      <button onClick={handleClick}>
        增加高度并立即测量
      </button>
    </div>
  );
}`,

  scenarioDiagram: `graph TD
    A[主要使用场景] --> B[DOM测量]
    A --> C[第三方库集成]
    A --> D[动画处理]
    A --> E[同步状态更新]
    A --> F[事件处理特殊需求]

    B --> B1[获取元素尺寸]
    B --> B2[滚动位置计算]
    B --> B3[布局计算]

    C --> C1[非React UI库集成]
    C --> C2[图表库同步更新]
    C --> C3[表单库集成]

    D --> D1[过渡动画起点设置]
    D --> D2[基于DOM的动画库]

    E --> E1[连续操作的中间状态]
    E --> E2[严格顺序依赖的更新]

    F --> F1[失去焦点前的状态保存]
    F --> F2[文件操作前的状态确认]
    F --> F3[打印前的布局确认]`,
  
  parameters: [
    {
      name: "callback",
      type: "() => void",
      required: true,
      description: "一个函数，其中的状态更新会被同步执行并立即应用到DOM",
      example: "() => setCount(count + 1)"
    }
  ],
  
  returnValue: {
    type: "any",
    description: "返回回调函数的返回值",
    example: "const result = flushSync(() => { setCount(1); return 'completed'; }); // result为'completed'"
  },
  
  keyFeatures: [
    {
      title: "强制同步更新",
      description: "打破React的批处理机制，强制立即执行状态更新并刷新DOM",
      benefit: "确保DOM立即反映最新状态，便于后续操作依赖最新DOM状态"
    },
    {
      title: "批处理范围限定",
      description: "仅针对回调内的状态更新进行同步刷新，不影响其他更新",
      benefit: "提供细粒度控制，将性能影响限制在最小范围内"
    },
    {
      title: "React 18兼容性",
      description: "在React 18的自动批处理机制下，保持一致的行为",
      benefit: "提供可预测的行为，便于迁移到React 18及更高版本"
    }
  ],
  
  limitations: [
    "使用flushSync会破坏React的批处理优化，可能导致性能下降",
    "在Concurrent模式下，过度使用可能破坏时间切片和优先级调度",
    "不应在常规渲染流程中使用，仅适用于特殊场景",
    "与Suspense和并发特性结合使用时可能有不可预期的行为"
  ],
  
  bestPractices: [
    "只在必要时使用flushSync，如需要立即测量DOM或与非React代码集成时",
    "保持回调函数简洁，只包含必要的状态更新操作",
    "避免在渲染函数内部或循环中使用",
    "考虑使用useLayoutEffect作为更符合React范式的替代方案"
  ],
  
  warnings: [
    "滥用flushSync会显著降低应用性能，特别是在频繁更新的场景",
    "React可能会在未来版本中更改flushSync的行为，应尽量减少依赖",
    "在并发渲染模式下，过度使用flushSync会抵消并发模式的优势"
  ]
};

export default basicInfo; 