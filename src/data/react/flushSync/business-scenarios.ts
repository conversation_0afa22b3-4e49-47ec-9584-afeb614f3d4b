import { BusinessScenario } from '@/types/api';

const data: BusinessScenario[] = [
  {
    id: 1,
    title: "DOM测量与动态布局计算",
    description: "在状态更新后立即测量DOM元素尺寸，用于自适应布局和动态定位",
    scenario: `在复杂的企业级应用中，常常需要根据DOM元素的实际尺寸和位置动态计算布局。例如，自适应的弹出菜单需要根据触发按钮的位置和大小调整自身位置，确保菜单完全可见；自动调整大小的组件需要根据内容变化重新测量并计算布局。

传统方式下，由于React的批处理机制，状态更新后DOM不会立即变更，导致测量时获取的是更新前的尺寸。使用flushSync可以强制立即应用DOM变更，确保获取到最新的DOM尺寸。

这种场景在数据可视化、自定义布局系统、复杂表单交互中特别常见，能够极大提升用户体验的流畅性和准确性。`,
    businessValue: "提高交互精确度，增强用户体验，减少UI跳动和布局偏移，使组件行为更可预测",
    code: `import React, { useState, useRef } from 'react';
import { flushSync } from 'react-dom';

// 自适应提示框组件
function AdaptiveTooltip({ triggerRef, content, isOpen, onClose }) {
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const tooltipRef = useRef(null);
  
  // 计算并设置提示框位置
  React.useEffect(() => {
    if (isOpen && triggerRef.current) {
      // 获取触发元素位置
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;
      
      // 强制立即更新并测量tooltip尺寸
      flushSync(() => {
        setPosition({ top: triggerRect.bottom + 5, left: triggerRect.left });
      });
      
      // 现在可以安全地获取更新后的tooltip尺寸
      const tooltipRect = tooltipRef.current.getBoundingClientRect();
      
      // 检查是否溢出视口
      if (tooltipRect.bottom > viewportHeight) {
        // 如果会溢出底部，改为在触发元素上方显示
        flushSync(() => {
          setPosition(prev => ({ ...prev, top: triggerRect.top - tooltipRect.height - 5 }));
        });
      }
      
      if (tooltipRect.right > viewportWidth) {
        // 如果会溢出右侧，向左对齐
        flushSync(() => {
          setPosition(prev => ({ ...prev, left: triggerRect.right - tooltipRect.width }));
        });
      }
    }
  }, [isOpen, triggerRef]);
  
  if (!isOpen) return null;
  
  return (
    <div 
      ref={tooltipRef}
      className="adaptive-tooltip"
      style={{
        position: 'fixed',
        top: position.top + 'px',
        left: position.left + 'px',
        zIndex: 1000,
        padding: '8px 12px',
        background: '#333',
        color: 'white',
        borderRadius: '4px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.2)'
      }}
    >
      {content}
      <button onClick={onClose} style={{ marginLeft: '8px' }}>关闭</button>
    </div>
  );
}

// 使用示例
function App() {
  const [showTooltip, setShowTooltip] = useState(false);
  const buttonRef = useRef(null);
  
  return (
    <div style={{ padding: '100px', height: '200vh' }}>
      <h1>自适应提示框示例</h1>
      
      <button 
        ref={buttonRef}
        onClick={() => setShowTooltip(true)}
      >
        显示提示框
      </button>
      
      <AdaptiveTooltip
        triggerRef={buttonRef}
        content="这是一个自适应位置的提示框，会根据可用空间智能调整位置"
        isOpen={showTooltip}
        onClose={() => setShowTooltip(false)}
      />
    </div>
  );
}`,
    completionStatus: '内容已完成',
  },
  {
    id: 2,
    title: "第三方库集成与数据同步",
    description: "与非React UI库集成时，确保React状态更新后立即同步到第三方库",
    scenario: `企业应用经常需要集成各种专业的第三方库，如图表库、地图组件、富文本编辑器等。这些库通常有自己的渲染和状态管理机制，不受React生命周期控制。

当React状态变更需要同步到这些第三方库时，由于React的批处理机制，状态更新和DOM变化可能会延迟，导致第三方库接收到过时的DOM状态，产生不一致或闪烁。

使用flushSync可以确保React状态更新立即应用到DOM，然后再执行第三方库的更新操作，保持两者状态的一致性，特别适用于数据可视化、地理信息系统等复杂场景。`,
    businessValue: "确保数据一致性，消除UI闪烁和不协调，提高第三方集成质量，增强用户信任度",
    code: `import React, { useState, useEffect, useRef } from 'react';
import { flushSync } from 'react-dom';
import * as echarts from 'echarts';

// 自定义React封装的ECharts组件
function ReactEChart({ data, title, theme = 'light' }) {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);
  
  // 初始化图表
  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current, theme);
      
      // 清理函数
      return () => {
        chartInstance.current?.dispose();
        chartInstance.current = null;
      };
    }
  }, [theme]);
  
  // 数据更新时更新图表
  useEffect(() => {
    if (chartInstance.current) {
      const options = {
        title: { text: title },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: data.map(item => item.name)
        },
        yAxis: { type: 'value' },
        series: [{
          data: data.map(item => item.value),
          type: 'bar'
        }]
      };
      
      chartInstance.current.setOption(options);
    }
  }, [data, title]);
  
  // 响应窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      chartInstance.current?.resize();
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: '400px' }}
    />
  );
}

// 数据控制面板组件
function DataControlPanel() {
  const [chartData, setChartData] = useState([
    { name: '产品A', value: 120 },
    { name: '产品B', value: 200 },
    { name: '产品C', value: 150 },
    { name: '产品D', value: 80 },
    { name: '产品E', value: 170 }
  ]);
  const [chartTitle, setChartTitle] = useState('销售数据分析');
  
  // 随机更新数据
  const updateData = () => {
    // 使用flushSync确保状态立即更新
    flushSync(() => {
      setChartData(prevData => 
        prevData.map(item => ({
          ...item,
          value: Math.floor(Math.random() * 300) + 50
        }))
      );
      setChartTitle('销售数据分析 (更新于 ' + new Date().toLocaleTimeString() + ')');
    });
    
    // 此时DOM已更新，可以执行需要同步的第三方库操作
    console.log('数据已同步更新到图表');
    
    // 如有需要，可以在此调用第三方库的额外方法
    // 例如触发图表的某些交互行为
  };
  
  return (
    <div className="dashboard-container">
      <div className="control-panel">
        <h2>销售数据控制面板</h2>
        <button onClick={updateData}>更新数据</button>
      </div>
      
      <div className="chart-container">
        <ReactEChart 
          data={chartData}
          title={chartTitle}
        />
      </div>
    </div>
  );
}

export default DataControlPanel;`,
    completionStatus: '内容已完成',
  },
  {
    id: 3,
    title: "复杂表单交互与验证",
    description: "在表单操作中确保状态更新和DOM变更同步，提高复杂表单的交互质量",
    scenario: `企业级应用中的表单往往十分复杂，包含大量的动态字段、条件逻辑和实时验证。当用户在表单中进行操作（如选择选项、切换显示/隐藏字段、动态添加表单项）时，需要即时反馈和验证，以提供流畅的用户体验。

由于React的批处理机制，连续的状态更新可能会被合并，导致某些中间状态的DOM变更被跳过。在需要基于每一步状态变化进行DOM操作的场景中，这可能会导致问题。

使用flushSync可以确保关键的表单状态变更立即应用到DOM，然后再执行依赖这些变更的操作，如焦点管理、错误提示定位、动态内容测量等，从而提升复杂表单的交互质量。`,
    businessValue: "提升表单填写体验，降低用户错误率，加速数据录入流程，提高企业数据收集效率",
    code: `import React, { useState, useRef } from 'react';
import { flushSync } from 'react-dom';

// 动态表单组件
function DynamicForm() {
  // 表单字段状态
  const [formFields, setFormFields] = useState([
    { id: 1, type: 'text', label: '姓名', value: '', required: true, error: '' },
    { id: 2, type: 'email', label: '邮箱', value: '', required: true, error: '' },
  ]);
  
  // 表单类型选项
  const [selectedType, setSelectedType] = useState('personal');
  
  // 保存最后一个字段引用，用于自动聚焦
  const lastFieldRef = useRef(null);
  
  // 处理字段变更
  const handleFieldChange = (id, value) => {
    setFormFields(fields => 
      fields.map(field => 
        field.id === id 
          ? { ...field, value, error: '' } 
          : field
      )
    );
  };
  
  // 添加新字段
  const addField = (type) => {
    const newField = {
      id: Date.now(),
      type: type || 'text',
      label: type === 'email' ? '备用邮箱' : '补充信息',
      value: '',
      required: false,
      error: ''
    };
    
    // 使用flushSync确保DOM立即更新，便于后续聚焦
    flushSync(() => {
      setFormFields(fields => [...fields, newField]);
    });
    
    // DOM已更新，可以安全地聚焦到新字段
    lastFieldRef.current?.focus();
  };
  
  // 切换表单类型
  const handleTypeChange = (type) => {
    // 使用flushSync确保表单类型立即更新
    flushSync(() => {
      setSelectedType(type);
      
      // 根据类型调整字段
      if (type === 'personal') {
        setFormFields([
          { id: 1, type: 'text', label: '姓名', value: '', required: true, error: '' },
          { id: 2, type: 'email', label: '邮箱', value: '', required: true, error: '' },
        ]);
      } else if (type === 'company') {
        setFormFields([
          { id: 1, type: 'text', label: '公司名称', value: '', required: true, error: '' },
          { id: 2, type: 'text', label: '职位', value: '', required: false, error: '' },
          { id: 3, type: 'email', label: '企业邮箱', value: '', required: true, error: '' },
        ]);
      }
    });
    
    // DOM已更新，执行额外操作
    document.getElementById('form-container').scrollIntoView({ behavior: 'smooth' });
    console.log('表单类型已切换为:', type);
  };
  
  // 验证并提交表单
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // 验证表单
    let hasError = false;
    let updatedFields = formFields.map(field => {
      if (field.required && !field.value) {
        hasError = true;
                  return { ...field, error: field.label + '不能为空' };
      }
      if (field.type === 'email' && field.value && !field.value.includes('@')) {
        hasError = true;
        return { ...field, error: '请输入有效的邮箱地址' };
      }
      return field;
    });
    
    // 使用flushSync确保错误状态立即更新
    flushSync(() => {
      setFormFields(updatedFields);
    });
    
    // 如有错误，滚动到第一个错误字段
    if (hasError) {
      const firstErrorField = document.querySelector('.field-error');
      firstErrorField?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      return;
    }
    
    // 表单验证通过，处理提交
    console.log('表单数据:', formFields);
    alert('表单提交成功！');
  };
  
  return (
    <div className="dynamic-form-container">
      <div className="form-type-selector">
        <h2>选择表单类型</h2>
        <div className="type-buttons">
          <button 
            className={selectedType === 'personal' ? 'active' : ''}
            onClick={() => handleTypeChange('personal')}
          >
            个人信息
          </button>
          <button 
            className={selectedType === 'company' ? 'active' : ''}
            onClick={() => handleTypeChange('company')}
          >
            企业信息
          </button>
        </div>
      </div>
      
      <div id="form-container" className="form-fields">
        <h2>{selectedType === 'personal' ? '个人信息表单' : '企业信息表单'}</h2>
        
        <form onSubmit={handleSubmit}>
          {formFields.map((field, index) => (
            <div 
              key={field.id} 
                              className={'form-field ' + (field.error ? 'field-error' : '')}
            >
                              <label htmlFor={'field-' + field.id}>
                {field.label}
                {field.required && <span className="required">*</span>}
              </label>
              
              <input
                                  id={'field-' + field.id}
                type={field.type}
                value={field.value}
                onChange={(e) => handleFieldChange(field.id, e.target.value)}
                ref={index === formFields.length - 1 ? lastFieldRef : null}
              />
              
              {field.error && (
                <div className="error-message">{field.error}</div>
              )}
            </div>
          ))}
          
          <div className="form-actions">
            <button type="button" onClick={() => addField('text')}>
              添加文本字段
            </button>
            <button type="button" onClick={() => addField('email')}>
              添加邮箱字段
            </button>
            <button type="submit" className="submit-button">
              提交表单
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default DynamicForm;`,
    completionStatus: '内容已完成',
  }
];

export default data; 