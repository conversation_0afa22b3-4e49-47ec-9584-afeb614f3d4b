import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: "什么是flushSync，它的主要用途是什么？",
    difficulty: "easy",
    frequency: "high",
    category: "基础概念",
    answer: {
      brief: "flushSync是ReactDOM提供的API，用于强制React立即同步执行状态更新并刷新DOM，主要用于需要立即测量DOM、第三方库集成等场景。",
      detailed: `flushSync是ReactDOM提供的一个API，用于强制React立即同步执行状态更新并刷新DOM，而不是等待React的批处理机制。

**基本语法**：
\`\`\`
import { flushSync } from 'react-dom';

flushSync(() => {
  // 在这里的状态更新会被立即同步执行
  setCount(count + 1);
});
// 此时DOM已经更新
\`\`\`

**主要用途**：

1. **立即测量DOM**：当你需要在状态更新后立即测量DOM元素的尺寸或位置时
\`\`\`
function updateAndMeasure() {
  flushSync(() => {
    setHeight(100);
  });
  // DOM已更新，现在可以测量
  console.log(divRef.current.getBoundingClientRect().height); // 100
}
\`\`\`

2. **与第三方DOM库集成**：确保React状态更新后再调用第三方库的方法
\`\`\`
function updateChart() {
  flushSync(() => {
    setChartData(newData);
  });
  // DOM已更新，安全调用第三方库
  chartLibrary.update(chartRef.current);
}
\`\`\`

3. **用户体验关键时刻**：需要立即反馈的场景
\`\`\`
function handleImportantAction() {
  flushSync(() => {
    setStatus('处理中');
  });
  // 用户立即看到"处理中"状态
  await processData();
}
\`\`\`

4. **动画控制**：需要精确控制动画起始状态
\`\`\`
function startAnimation() {
  flushSync(() => {
    setExpanded(true);
  });
  // DOM已展开，可以开始动画
  element.animate([...keyframes], options);
}
\`\`\`

**重要注意事项**：
- flushSync会破坏React的批处理优化，可能导致性能问题
- 应谨慎使用，仅在确实需要立即DOM更新的场景下使用
- React 18中的自动批处理使flushSync的使用场景更加明确`,
      code: `
// 基本使用示例
import React, { useState, useRef } from 'react';
import { flushSync } from 'react-dom';

function FlushSyncDemo() {
  const [boxSize, setBoxSize] = useState(100);
  const boxRef = useRef(null);
  const [measurement, setMeasurement] = useState('未测量');

  // 正常更新 - 不使用flushSync
  const handleNormalUpdate = () => {
    setBoxSize(200);
    
    // 在这里测量DOM元素会得到旧值
    // 因为React还没有更新DOM
    if (boxRef.current) {
      const width = boxRef.current.offsetWidth;
      setMeasurement(\`普通更新测量: \${width}px (错误值，使用旧状态)\`);
    }
  };

  // 使用flushSync强制同步更新
  const handleSyncUpdate = () => {
    flushSync(() => {
      setBoxSize(200);
    });
    
    // 在flushSync之后测量DOM元素会得到新值
    // 因为React已经同步更新了DOM
    if (boxRef.current) {
      const width = boxRef.current.offsetWidth;
      setMeasurement(\`flushSync更新测量: \${width}px (正确值)\`);
    }
  };

  return (
    <div>
      <h2>flushSync API 演示</h2>
      <div 
        ref={boxRef}
        style={{ 
          width: boxSize, 
          height: boxSize, 
          backgroundColor: 'lightblue',
          margin: '20px 0'
        }}
      >
        尺寸: {boxSize}px
      </div>
      
      <div>
        <button onClick={handleNormalUpdate}>
          普通更新 (批处理)
        </button>
        <button onClick={handleSyncUpdate}>
          同步更新 (flushSync)
        </button>
      </div>
      
      <div>
        <p><strong>测量结果:</strong> {measurement}</p>
      </div>
    </div>
  );
}
`
    },
    tags: ['状态更新', 'DOM操作', 'React18']
  },
  {
    id: 2,
    question: "React 18中的自动批处理与flushSync有什么关系？何时需要使用flushSync？",
    difficulty: "medium",
    frequency: "medium",
    category: "高级特性",
    answer: {
      brief: "React 18引入的自动批处理使多个状态更新自动合并为一次渲染，包括异步代码中的更新。flushSync可以打破这种批处理，用于需要立即DOM更新的场景。",
      detailed: `React 18引入了**自动批处理（Automatic Batching）**机制，它与flushSync有着密切的关系：

**1. 自动批处理的变化**
在React 18之前，React只在事件处理函数中进行批处理（合并多个状态更新为一次渲染）。而React 18扩展了批处理范围，使其在Promise、setTimeout、原生事件处理函数等异步代码中也能自动进行批处理，从而提高性能。

\`\`\`
// React 18之前：两次独立渲染
setTimeout(() => {
  setCount(c => c + 1); // 触发一次渲染
  setFlag(f => !f);     // 触发另一次渲染
}, 0);

// React 18：自动批处理为一次渲染
setTimeout(() => {
  setCount(c => c + 1); // 不会立即渲染
  setFlag(f => !f);     // 与上面的更新一起，只触发一次渲染
}, 0);
\`\`\`

**2. flushSync的作用**
在这种背景下，flushSync变得尤为重要，因为它是开发者能够突破自动批处理的主要方式，允许在需要时强制同步更新DOM：

\`\`\`
import { flushSync } from 'react-dom';

setTimeout(() => {
  // 第一个更新会被同步执行
  flushSync(() => {
    setCount(c => c + 1);
  });
  // 此时DOM已更新
  
  // 第二个更新会在下一个批处理中执行
  setFlag(f => !f);
}, 0);
\`\`\`

**3. 何时需要使用flushSync**

以下场景适合使用flushSync：

a) **需要立即测量DOM**：
\`\`\`
function handleClick() {
  flushSync(() => {
    setHeight(height + 100);
  });
  // 现在可以测量新高度
  const newHeight = boxRef.current.getBoundingClientRect().height;
  console.log(newHeight); // 正确反映更新后的高度
}
\`\`\`

b) **与第三方DOM库集成**：
\`\`\`
function updateChart() {
  flushSync(() => {
    setChartData(newData);
  });
  // 现在DOM更新了，可以安全调用第三方库
  chartLibrary.update(chartRef.current);
}
\`\`\`

c) **动画的起始状态确定**：
\`\`\`
function startAnimation() {
  flushSync(() => {
    setIsExpanded(true);
  });
  // DOM已更新，现在可以基于新状态设置动画起点
  element.animate([/* 动画关键帧 */], { duration: 500 });
}
\`\`\`

d) **特定事件序列**：
\`\`\`
function handleSpecialAction() {
  flushSync(() => {
    setSaving(true);
  });
  // 确保"保存中"状态立即显示给用户
  
  try {
    await saveData();
    flushSync(() => {
      setSaving(false);
      setSuccess(true);
    });
  } catch (err) {
    flushSync(() => {
      setSaving(false);
      setError(err);
    });
  }
}
\`\`\`

**重要提示**：虽然flushSync很有用，但应该谨慎使用，因为它会打破React的批处理优化，可能导致性能问题。大多数情况下，应该依赖React的默认批处理机制。`,
      code: `
// React 18自动批处理与flushSync对比
import React, { useState } from 'react';
import { flushSync } from 'react-dom';

function BatchingDemo() {
  const [count, setCount] = useState(0);
  const [flag, setFlag] = useState(false);
  const [updates, setUpdates] = useState([]);
  
  const logUpdate = (message) => {
    setUpdates(prev => [...prev, \`\${new Date().toLocaleTimeString()}: \${message}\`]);
  };
  
  // 演示React 18的自动批处理
  const handleAutoBatching = () => {
    logUpdate('自动批处理开始');
    
    // 在Promise中的多个状态更新会被批处理
    Promise.resolve().then(() => {
      logUpdate('Promise内 - 第一次更新');
      setCount(c => c + 1);
      
      logUpdate('Promise内 - 第二次更新');
      setFlag(f => !f);
      // 这两个状态更新会被批处理为一次渲染
      
      logUpdate('Promise内 - 结束，将在下一个tick看到UI更新');
    });
  };
  
  // 演示使用flushSync打破自动批处理
  const handleFlushSync = () => {
    logUpdate('flushSync演示开始');
    
    Promise.resolve().then(() => {
      // 第一个更新使用flushSync
      logUpdate('Promise内 - 第一次更新(flushSync)');
      flushSync(() => {
        setCount(c => c + 1);
      });
      logUpdate('Promise内 - 第一次更新后，UI已更新');
      
      // 第二个更新正常批处理
      logUpdate('Promise内 - 第二次更新(正常)');
      setFlag(f => !f);
      logUpdate('Promise内 - 结束，第二次更新将在下一个tick生效');
    });
  };
  
  return (
    <div className="batching-demo">
      <h2>React 18 批处理与flushSync</h2>
      
      <div className="state-display">
        <p><strong>Count:</strong> {count}</p>
        <p><strong>Flag:</strong> {flag.toString()}</p>
      </div>
      
      <div className="controls">
        <button onClick={handleAutoBatching}>
          自动批处理演示
        </button>
        <button onClick={handleFlushSync}>
          FlushSync演示
        </button>
      </div>
      
      <div className="log">
        <h3>更新日志:</h3>
        <ul>
          {updates.map((update, i) => (
            <li key={i}>{update}</li>
          ))}
        </ul>
      </div>
    </div>
  );
}
`
    },
    tags: ['批处理', 'React18', '性能优化']
  },
  {
    id: 3,
    question: "flushSync与useLayoutEffect有什么区别？应该在什么场景下选择使用哪一个？",
    difficulty: "medium",
    frequency: "medium",
    category: "原理对比",
    answer: {
      brief: "flushSync是命令式API，强制立即执行状态更新；useLayoutEffect是声明式Hook，在DOM更新后、浏览器绘制前同步执行副作用。前者用于即时DOM测量，后者适合在每次DOM更新后执行操作。",
      detailed: `flushSync和useLayoutEffect都与React的同步DOM更新有关，但它们有明显区别，适用于不同场景。

### 主要区别

**1. 本质差异**
- **flushSync**：是一个命令式API，用于强制同步执行状态更新并立即刷新DOM
- **useLayoutEffect**：是一个生命周期Hook，在DOM更新后同步执行副作用

**2. 调用时机**
- **flushSync**：可以在任何时机调用，立即触发同步渲染
- **useLayoutEffect**：总是在React完成DOM更新后、浏览器绘制前执行

**3. 使用方式**
- **flushSync**：接受一个回调函数，该函数中的状态更新会被同步应用
- **useLayoutEffect**：接受副作用函数和依赖数组，遵循React Hooks规则

**4. 批处理关系**
- **flushSync**：打破批处理，强制立即更新
- **useLayoutEffect**：不打破批处理，而是在批处理完成、DOM更新后执行

### 代码对比

\`\`\`
// 使用flushSync立即测量DOM
function MeasureWithFlushSync() {
  const boxRef = useRef(null);
  const [height, setHeight] = useState(100);
  
  const handleResize = () => {
    flushSync(() => {
      setHeight(height + 50);
    });
    // 此时DOM已更新，可以立即测量
    console.log("新高度:", boxRef.current.offsetHeight);
  };
  
  return (
    <div>
      <div ref={boxRef} style={{ height }}>内容</div>
      <button onClick={handleResize}>增加高度</button>
    </div>
  );
}

// 使用useLayoutEffect测量DOM
function MeasureWithLayoutEffect() {
  const boxRef = useRef(null);
  const [height, setHeight] = useState(100);
  
  // 保存上一个高度值
  const prevHeightRef = useRef(height);
  
  useLayoutEffect(() => {
    // 只有当高度变化时才执行测量
    if (prevHeightRef.current !== height) {
      console.log("新高度:", boxRef.current.offsetHeight);
      prevHeightRef.current = height;
    }
  }, [height]);
  
  return (
    <div>
      <div ref={boxRef} style={{ height }}>内容</div>
      <button onClick={() => setHeight(height + 50)}>增加高度</button>
    </div>
  );
}
\`\`\`

### 适用场景

**1. 何时使用flushSync**

- **外部交互需要即时DOM更新**：如第三方库集成时
  \`\`\`
  function updateExternalLibrary() {
    flushSync(() => {
      setData(newData);
    });
    // 立即调用外部库API
    externalLib.update(elementRef.current);
  }
  \`\`\`

- **事件处理中需要测量中间状态**：如拖拽实现
  \`\`\`
  function handleDrag(e) {
    flushSync(() => {
      setPosition({ x: e.clientX, y: e.clientY });
    });
    // 立即测量新位置，计算其他元素的关系
    checkCollisions();
  }
  \`\`\`

**2. 何时使用useLayoutEffect**

- **响应式布局计算**：基于DOM变化调整布局
  \`\`\`
  function ResponsiveLayout() {
    const [width, setWidth] = useState(0);
    const containerRef = useRef(null);
    
    useLayoutEffect(() => {
      const updateWidth = () => {
        setWidth(containerRef.current.offsetWidth);
      };
      
      updateWidth(); // 初始测量
      window.addEventListener('resize', updateWidth);
      return () => window.removeEventListener('resize', updateWidth);
    }, []);
    
    return (
      <div ref={containerRef} className="container">
        {width > 768 ? <DesktopLayout /> : <MobileLayout />}
      </div>
    );
  }
  \`\`\`

- **动画初始状态设置**：在渲染后立即设置动画初始状态
  \`\`\`
  function AnimatedComponent() {
    const elementRef = useRef(null);
    const [isVisible, setIsVisible] = useState(false);
    
    useLayoutEffect(() => {
      if (isVisible && elementRef.current) {
        // 设置初始状态
        elementRef.current.style.opacity = '0';
        elementRef.current.style.transform = 'translateY(20px)';
        
        // 强制回流
        elementRef.current.getBoundingClientRect();
        
        // 应用动画
        elementRef.current.style.transition = 'all 0.3s ease';
        elementRef.current.style.opacity = '1';
        elementRef.current.style.transform = 'translateY(0)';
      }
    }, [isVisible]);
    
    return (
      <>
        <button onClick={() => setIsVisible(true)}>显示</button>
        {isVisible && <div ref={elementRef}>动画内容</div>}
      </>
    );
  }
  \`\`\`

### 选择指南

1. **使用flushSync当**：
   - 需要在事件处理函数中立即应用状态更新并测量DOM
   - 需要与外部命令式库同步
   - 需要控制更新的精确时机

2. **使用useLayoutEffect当**：
   - 需要在每次渲染后测量DOM并可能触发额外渲染
   - 需要设置初始DOM状态（如动画起点）
   - 需要在视觉更新前同步修改DOM

3. **两者结合使用**：
   - 在复杂场景中，可能需要结合使用这两个API
   - 例如，在useLayoutEffect中使用flushSync处理特定的状态更新

记住，无论使用哪种方式，都应该谨慎处理同步DOM操作，因为它们会阻塞浏览器渲染，可能导致性能问题。`,
      code: `
// 比较flushSync和useLayoutEffect的完整示例
import React, { useState, useRef, useLayoutEffect } from 'react';
import { flushSync } from 'react-dom';

function ComparisonDemo() {
  // 共享状态
  const [boxHeight, setBoxHeight] = useState(50);
  const [selectedTab, setSelectedTab] = useState('flushSync');
  
  // 测量结果
  const [flushSyncMeasurement, setFlushSyncMeasurement] = useState('未测量');
  const [layoutEffectMeasurement, setLayoutEffectMeasurement] = useState('未测量');
  
  // flushSync示例
  const flushBoxRef = useRef(null);
  
  const handleFlushSyncResize = () => {
    const startTime = performance.now();
    
    flushSync(() => {
      setBoxHeight(prev => prev + 50);
    });
    
    // DOM已同步更新，可以立即测量
    const newHeight = flushBoxRef.current.offsetHeight;
    const endTime = performance.now();
    
    setFlushSyncMeasurement(
      \`高度: \${newHeight}px (耗时: \${(endTime - startTime).toFixed(2)}ms)\`
    );
  };
  
  // useLayoutEffect示例
  const layoutBoxRef = useRef(null);
  
  const handleLayoutEffectResize = () => {
    const startTime = performance.now();
    setBoxHeight(prev => prev + 50);
    // 在这里不能测量DOM，因为它尚未更新
  };
  
  // 使用useLayoutEffect在DOM更新后测量
  useLayoutEffect(() => {
    if (layoutBoxRef.current) {
      const endTime = performance.now();
      const newHeight = layoutBoxRef.current.offsetHeight;
      
      setLayoutEffectMeasurement(
        \`高度: \${newHeight}px (在useLayoutEffect中测量)\`
      );
    }
  }, [boxHeight]); // 当boxHeight变化时执行
  
  return (
    <div className="comparison-demo">
      <h2>flushSync vs useLayoutEffect</h2>
      
      <div className="tabs">
        <button 
          className={selectedTab === 'flushSync' ? 'active' : ''}
          onClick={() => setSelectedTab('flushSync')}
        >
          flushSync示例
        </button>
        <button 
          className={selectedTab === 'layoutEffect' ? 'active' : ''}
          onClick={() => setSelectedTab('layoutEffect')}
        >
          useLayoutEffect示例
        </button>
      </div>
      
      <div className="tab-content">
        {selectedTab === 'flushSync' && (
          <div className="tab-panel">
            <h3>flushSync - 命令式立即更新</h3>
            <div 
              ref={flushBoxRef}
              style={{ 
                height: boxHeight,
                backgroundColor: 'lightblue',
                padding: '10px',
                margin: '20px 0'
              }}
            >
              flushSync Box
            </div>
            <button onClick={handleFlushSyncResize}>
              增加高度并立即测量
            </button>
            <p>测量结果: {flushSyncMeasurement}</p>
          </div>
        )}
        
        {selectedTab === 'layoutEffect' && (
          <div className="tab-panel">
            <h3>useLayoutEffect - 声明式同步副作用</h3>
            <div 
              ref={layoutBoxRef}
              style={{ 
                height: boxHeight,
                backgroundColor: 'lightgreen',
                padding: '10px',
                margin: '20px 0'
              }}
            >
              useLayoutEffect Box
            </div>
            <button onClick={handleLayoutEffectResize}>
              增加高度 (测量在useLayoutEffect中)
            </button>
            <p>测量结果: {layoutEffectMeasurement}</p>
          </div>
        )}
      </div>
      
      <div className="comparison-notes">
        <h4>核心区别：</h4>
        <ul>
          <li><strong>flushSync</strong>: 命令式API，立即同步执行更新</li>
          <li><strong>useLayoutEffect</strong>: 声明式Hook，在DOM更新后执行</li>
          <li>选择取决于你需要"立即测量一次" vs "每次渲染都测量"</li>
        </ul>
      </div>
    </div>
  );
}
`
    },
    tags: ['副作用', 'DOM更新', 'Hooks对比']
  }
];

export default interviewQuestions; 