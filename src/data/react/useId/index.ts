import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useIdData: ApiItem = {
  id: 'useId',
  title: 'useId Hook',
  description: 'React 18中用于生成唯一标识符的Hook，专门解决组件中需要唯一ID的场景，确保服务端渲染和客户端渲染的一致性',
  category: 'React Hooks',
  difficulty: 'easy',
  syntax: 'function useId(): string',
  example: `function LoginForm() {
  const usernameId = useId();
  const passwordId = useId();

  return (
    <form>
      <label htmlFor={usernameId}>用户名</label>
      <input id={usernameId} type="text" />

      <label htmlFor={passwordId}>密码</label>
      <input id={passwordId} type="password" />
    </form>
  );
}`,
  notes: '仅在React 18+版本中可用，不能用作key属性，确保SSR和CSR的一致性',
  version: 'React 18.0.0+',
  tags: ['ID生成', 'SSR兼容', '无障碍性'],

  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useIdData;
