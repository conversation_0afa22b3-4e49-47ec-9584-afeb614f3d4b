import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 1,
    question: '为什么useId生成的ID每次都不一样？如何获得稳定的ID？',
    answer: `**问题原因**：useId生成的ID基于组件在Fiber树中的位置，每次组件重新挂载时位置可能发生变化，导致ID改变。

**解决方案**：
1. **理解useId的设计目的**：useId是为了解决SSR一致性问题，不是为了提供持久化的稳定ID
2. **使用场景区分**：
   - 表单元素关联：使用useId（每次渲染保持一致）
   - 业务逻辑标识：使用props传入的稳定ID
   - 数据库存储：使用后端生成的UUID

**最佳实践**：
- 在组件生命周期内，useId返回的值是稳定的
- 如需持久化ID，应该从props或context获取
- 避免将useId的值存储到localStorage或数据库`,
    category: '使用错误',
    difficulty: 'easy',
    frequency: 'high',
    tags: ['ID稳定性', '组件生命周期'],
    relatedQuestions: [2, 3],
    codeExample: `// ❌ 错误：期望useId提供持久化ID
function UserProfile({ userId }) {
  const profileId = useId(); // 每次组件重新挂载都会变化

  // 错误：将useId的值存储到localStorage
  localStorage.setItem('profileId', profileId);

  return <div id={profileId}>用户资料</div>;
}

// ✅ 正确：区分不同类型的ID
function UserProfile({ userId }) {
  const formId = useId(); // 用于表单元素关联
  const fieldId = useId(); // 用于字段标识

  // 业务ID来自props，持久化ID来自后端
  const persistentId = userId; // 来自后端的稳定ID

  return (
    <div data-user-id={persistentId}>
      <form id={formId}>
        <label htmlFor={fieldId}>用户名</label>
        <input id={fieldId} type="text" />
      </form>
    </div>
  );
}

// ✅ 正确：在组件内部useId是稳定的
function StableIdExample() {
  const id1 = useId();
  const id2 = useId();

  // 在同一个组件实例的生命周期内，这些ID不会变化
  console.log('ID1:', id1); // 每次渲染都是相同的值
  console.log('ID2:', id2); // 每次渲染都是相同的值

  return (
    <div>
      <div id={id1}>区域1</div>
      <div id={id2}>区域2</div>
    </div>
  );
}`
  },
  {
    id: 2,
    question: '在SSR应用中useId导致hydration错误，如何解决？',
    answer: `**问题症状**：控制台出现"Warning: Prop \`id\` did not match. Server: \":r1:\" Client: \":r2:\""等hydration警告。

**根本原因**：
1. **组件树结构不一致**：服务端和客户端的组件树结构不同
2. **异步数据影响**：服务端和客户端的数据不同导致条件渲染差异
3. **环境差异**：开发环境和生产环境的React配置不同

**解决步骤**：
1. **确保组件树一致性**：
   - 检查条件渲染逻辑
   - 确保初始数据在服务端和客户端相同
   - 避免在useEffect中修改组件结构

2. **正确处理异步数据**：
   - 使用getServerSideProps或getStaticProps预取数据
   - 避免在客户端首次渲染时改变组件结构

3. **配置React 18**：
   - 确保使用正确的React 18 API
   - 配置identifierPrefix保持一致

**调试技巧**：
- 使用React DevTools比较服务端和客户端的组件树
- 在开发环境启用Strict Mode检测问题
- 添加日志记录组件渲染过程`,
    category: '兼容性问题',
    difficulty: 'medium',
    frequency: 'high',
    tags: ['SSR', 'Hydration', '调试'],
    relatedQuestions: [1, 4],
    codeExample: `// ❌ 错误：导致hydration不匹配的代码
function ProblematicComponent({ initialData }) {
  const [data, setData] = useState(null);
  const id = useId();

  useEffect(() => {
    // 客户端才会执行，导致结构不一致
    setData(initialData || fetchDefaultData());
  }, []);

  // 服务端渲染时data为null，客户端为实际数据
  return (
    <div id={id}>
      {data ? (
        <div>有数据: {data.title}</div>
      ) : (
        <div>加载中...</div>
      )}
    </div>
  );
}

// ✅ 正确：确保SSR一致性
function FixedComponent({ initialData }) {
  // 确保初始状态在服务端和客户端一致
  const [data, setData] = useState(initialData);
  const id = useId();

  useEffect(() => {
    // 只在有必要时更新数据
    if (!data) {
      fetchData().then(setData);
    }
  }, [data]);

  return (
    <div id={id}>
      {data ? (
        <div>数据: {data.title}</div>
      ) : (
        <div>暂无数据</div>
      )}
    </div>
  );
}

// ✅ Next.js中的正确用法
export async function getServerSideProps() {
  const data = await fetchData();
  return {
    props: { initialData: data }
  };
}

export default function Page({ initialData }) {
  const formId = useId();

  return (
    <div>
      <form id={formId}>
        <h1>{initialData.title}</h1>
        {/* 组件结构在服务端和客户端完全一致 */}
      </form>
    </div>
  );
}

// ✅ 调试工具：检测hydration问题
function HydrationDebugger() {
  const id = useId();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // 开发环境下的调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log('Hydration Debug:', {
      id,
      isClient,
      isServer: typeof window === 'undefined'
    });
  }

  return (
    <div id={id} data-hydration-id={id}>
      {isClient ? '客户端渲染' : '服务端渲染'}
    </div>
  );
}`
  },
  {
    id: 3,
    question: '为什么不能将useId的返回值用作React元素的key属性？',
    answer: `**核心原因**：key属性需要在列表重新排序时保持稳定，而useId会随组件重新挂载而变化，违背了key的设计原则。

**key属性的作用**：
1. **元素追踪**：React通过key追踪列表中的元素
2. **性能优化**：避免不必要的DOM操作
3. **状态保持**：确保组件状态在重排序时正确保持

**useId的特性**：
- 基于组件在Fiber树中的位置生成
- 组件重新挂载时会发生变化
- 主要用于DOM元素的id属性，不是业务标识

**正确的key选择**：
1. **稳定的业务ID**：数据库ID、UUID等
2. **稳定的索引**：仅在列表不会重排序时使用
3. **组合键**：多个稳定字段的组合

**错误后果**：
- 列表重排序时组件状态丢失
- 不必要的DOM重建
- 性能问题和用户体验下降`,
    category: '使用错误',
    difficulty: 'medium',
    frequency: 'medium',
    tags: ['key属性', '列表渲染', '性能'],
    relatedQuestions: [1, 5],
    codeExample: `// ❌ 错误：使用useId作为key
function TodoList({ todos }) {
  return (
    <ul>
      {todos.map((todo) => {
        const id = useId(); // 错误！每次渲染都会变化
        return (
          <TodoItem
            key={id} // 这会导致严重问题
            todo={todo}
          />
        );
      })}
    </ul>
  );
}

// ❌ 错误示例的问题演示
function ProblematicExample() {
  const [todos, setTodos] = useState([
    { id: 1, text: '学习React', completed: false },
    { id: 2, text: '写代码', completed: true }
  ]);

  const reorderTodos = () => {
    setTodos(prev => [...prev].reverse());
  };

  return (
    <div>
      <button onClick={reorderTodos}>重新排序</button>
      <ul>
        {todos.map(() => {
          const badKey = useId(); // 每次重排序都会生成新的key
          return <li key={badKey}>...</li>; // 组件状态会丢失
        })}
      </ul>
    </div>
  );
}

// ✅ 正确：使用稳定的业务ID作为key
function CorrectTodoList({ todos }) {
  return (
    <ul>
      {todos.map((todo) => (
        <TodoItem
          key={todo.id} // 使用稳定的业务ID
          todo={todo}
        />
      ))}
    </ul>
  );
}

// ✅ 正确：useId用于DOM元素的id属性
function TodoItem({ todo }) {
  const itemId = useId(); // 用于DOM元素标识
  const checkboxId = useId(); // 用于表单元素关联

  return (
    <li id={itemId}>
      <input
        id={checkboxId}
        type="checkbox"
        checked={todo.completed}
      />
      <label htmlFor={checkboxId}>
        {todo.text}
      </label>
    </li>
  );
}

// ✅ 正确：不同场景的key选择策略
function KeySelectionExamples() {
  // 1. 有稳定ID的数据
  const usersWithId = [
    { id: 'user-1', name: 'Alice' },
    { id: 'user-2', name: 'Bob' }
  ];

  // 2. 没有ID但有唯一字段的数据
  const emails = ['<EMAIL>', '<EMAIL>'];

  // 3. 纯展示数据（不会重排序）
  const staticItems = ['项目1', '项目2', '项目3'];

  return (
    <div>
      {/* 使用业务ID */}
      {usersWithId.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}

      {/* 使用唯一字段 */}
      {emails.map(email => (
        <div key={email}>{email}</div>
      ))}

      {/* 静态列表可以使用索引 */}
      {staticItems.map((item, index) => (
        <div key={index}>{item}</div>
      ))}
    </div>
  );
}`
  },
  {
    id: 4,
    question: '如何在React 18之前的版本中实现类似useId的功能？',
    answer: `**兼容性方案**：对于React 18之前的版本，可以通过自定义Hook和polyfill实现类似功能。

**实现策略**：
1. **全局计数器方案**：使用全局计数器生成唯一ID
2. **组件实例ID**：结合组件挂载时间和随机数
3. **Context传递**：通过Context传递ID生成器
4. **第三方库**：使用uuid等成熟的ID生成库

**SSR兼容性考虑**：
- 确保服务端和客户端生成相同的ID序列
- 使用确定性的种子值
- 避免依赖客户端特有的API

**性能优化**：
- 缓存生成的ID避免重复计算
- 使用轻量级的ID格式
- 避免在渲染过程中频繁生成ID

**迁移建议**：
- 升级到React 18后逐步替换自定义实现
- 保持API接口一致性便于迁移
- 添加类型定义确保类型安全`,
    category: '兼容性问题',
    difficulty: 'hard',
    frequency: 'medium',
    tags: ['React 17', 'Polyfill', '向后兼容'],
    relatedQuestions: [2, 6],
    codeExample: `// ✅ React 18之前的useId polyfill实现
import { useRef, useContext, createContext } from 'react';

// 1. 全局计数器方案
let globalIdCounter = 0;

function useIdPolyfill() {
  const idRef = useRef(null);

  if (idRef.current === null) {
    idRef.current = 'react-id-' + (++globalIdCounter);
  }

  return idRef.current;
}

// 2. SSR兼容的ID生成器
const IdGeneratorContext = createContext(null);

function IdGeneratorProvider({ children, prefix = 'app' }) {
  const counterRef = useRef(0);

  const generateId = () => {
    return prefix + '-' + (++counterRef.current);
  };

  return (
    <IdGeneratorContext.Provider value={generateId}>
      {children}
    </IdGeneratorContext.Provider>
  );
}

function useIdWithContext() {
  const generateId = useContext(IdGeneratorContext);
  const idRef = useRef(null);

  if (idRef.current === null) {
    idRef.current = generateId ? generateId() : useIdPolyfill();
  }

  return idRef.current;
}

// 3. 更完整的polyfill实现
function createUseIdPolyfill() {
  let serverCounter = 0;
  let clientCounter = 0;

  return function useId() {
    const idRef = useRef(null);

    if (idRef.current === null) {
      // 检测环境
      const isServer = typeof window === 'undefined';

      if (isServer) {
        // 服务端使用确定性计数器
        idRef.current = ':r' + (++serverCounter) + ':';
      } else {
        // 客户端使用相同的格式
        idRef.current = ':r' + (++clientCounter) + ':';
      }
    }

    return idRef.current;
  };
}

// 4. 条件导入：优雅的兼容性处理
import { useId as useIdNative } from 'react';

// 检测React版本
const hasNativeUseId = typeof useIdNative === 'function';

export const useId = hasNativeUseId
  ? useIdNative
  : createUseIdPolyfill();

// 5. 使用示例
function CompatibleComponent() {
  const formId = useId(); // 在所有React版本中都能工作
  const fieldId = useId();

  return (
    <form id={formId}>
      <label htmlFor={fieldId}>用户名</label>
      <input id={fieldId} type="text" />
    </form>
  );
}

// 6. 完整的应用设置
function App() {
  return (
    <IdGeneratorProvider prefix="my-app">
      <CompatibleComponent />
    </IdGeneratorProvider>
  );
}

// 7. TypeScript类型定义
declare module 'react' {
  function useId(): string;
}

// 8. 测试用例
function testIdGeneration() {
  const TestComponent = () => {
    const id1 = useId();
    const id2 = useId();

    console.log('Generated IDs:', { id1, id2 });

    return (
      <div>
        <div id={id1}>Element 1</div>
        <div id={id2}>Element 2</div>
      </div>
    );
  };

  // 验证ID的唯一性和稳定性
  return TestComponent;
}`
  },
  {
    id: 5,
    question: 'useId生成的ID格式很奇怪（如:r1:），可以自定义格式吗？',
    answer: `**ID格式说明**：useId生成的ID格式（如:r1:、:r2:1:）是React内部优化的结果，不应该依赖或修改这个格式。

**格式含义**：
- \`:r1:\`：第一个useId调用
- \`:r2:1:\`：第二层组件的第二个useId调用
- 冒号分隔符便于解析和调试

**为什么不能自定义**：
1. **内部优化**：格式经过精心设计，确保性能和唯一性
2. **SSR一致性**：自定义格式可能破坏服务端渲染的一致性
3. **未来兼容性**：React可能在未来版本中改变格式

**正确的处理方式**：
1. **接受原始格式**：直接使用useId返回的值
2. **添加前缀后缀**：基于useId的值构建自定义ID
3. **业务ID分离**：区分DOM ID和业务标识符

**常见误区**：
- 尝试解析ID格式获取信息
- 依赖ID格式进行业务逻辑判断
- 期望ID具有可读性`,
    category: '最佳实践',
    difficulty: 'medium',
    frequency: 'low',
    tags: ['ID格式', '自定义', '最佳实践'],
    relatedQuestions: [4, 5],
    codeExample: `// ❌ 错误：尝试自定义useId格式
function WrongCustomId() {
  const rawId = useId();

  // 错误：尝试解析和修改ID格式
  const customId = rawId.replace(/:/g, '-').replace(/r/, 'custom');

  // 错误：依赖ID格式进行逻辑判断
  const isFirstId = rawId.includes('r1');

  return <div id={customId}>内容</div>;
}

// ✅ 正确：基于useId构建自定义ID
function CorrectCustomId({ prefix = 'my-component' }) {
  const baseId = useId();

  // 正确：添加有意义的前缀
  const formId = prefix + '-form-' + baseId;
  const fieldId = prefix + '-field-' + baseId;
  const buttonId = prefix + '-button-' + baseId;

  return (
    <form id={formId}>
      <input id={fieldId} />
      <button id={buttonId}>提交</button>
    </form>
  );
}

// ✅ 正确：创建ID生成工具函数
function useCustomId(prefix = '', suffix = '') {
  const baseId = useId();

  return useMemo(() => {
    const parts = [prefix, baseId, suffix].filter(Boolean);
    return parts.join('-');
  }, [prefix, baseId, suffix]);
}

// ✅ 使用示例
function ComponentWithCustomIds() {
  const modalId = useCustomId('modal');
  const titleId = useCustomId('modal', 'title');
  const contentId = useCustomId('modal', 'content');

  return (
    <div id={modalId} role="dialog" aria-labelledby={titleId}>
      <h2 id={titleId}>标题</h2>
      <div id={contentId}>内容</div>
    </div>
  );
}

// ✅ 正确：业务ID和DOM ID分离
function UserCard({ user }) {
  const cardId = useId(); // DOM元素ID
  const businessId = user.id; // 业务标识符

  return (
    <div
      id={cardId} // 用于DOM操作和样式
      data-user-id={businessId} // 用于业务逻辑
      className="user-card"
    >
      <h3>用户: {user.name}</h3>
      <p>ID: {businessId}</p>
    </div>
  );
}

// ✅ 高级：创建命名空间ID管理器
function createIdManager(namespace) {
  return function useNamespacedId(suffix = '') {
    const baseId = useId();

    return useMemo(() => {
      const parts = [namespace, baseId, suffix].filter(Boolean);
      return parts.join('-');
    }, [baseId, suffix]);
  };
}

// 使用命名空间ID管理器
const useDataTableId = createIdManager('data-table');

function DataTable() {
  const tableId = useDataTableId();
  const headerId = useDataTableId('header');
  const bodyId = useDataTableId('body');

  return (
    <table id={tableId}>
      <thead id={headerId}>...</thead>
      <tbody id={bodyId}>...</tbody>
    </table>
  );
}`
  },
  {
    id: 6,
    question: '在测试环境中useId的行为不一致，如何处理测试中的ID问题？',
    answer: `**测试环境问题**：useId在不同测试运行中可能生成不同的ID，导致快照测试失败或测试不稳定。

**常见测试问题**：
1. **快照测试失败**：每次运行生成不同的ID
2. **DOM查询困难**：无法预测生成的ID值
3. **测试隔离问题**：不同测试用例间ID冲突

**解决策略**：
1. **Mock useId**：在测试中使用可预测的ID
2. **测试工具配置**：配置测试框架忽略ID差异
3. **语义化查询**：使用role、label等属性而非ID查询
4. **测试ID属性**：添加专门的测试标识符

**最佳实践**：
- 避免在测试中依赖具体的ID值
- 使用语义化的查询方式
- 为测试添加稳定的标识符
- 配置合适的测试环境`,
    category: '调试困难',
    difficulty: 'hard',
    frequency: 'low',
    tags: ['测试', 'Jest', 'Mock', '快照测试'],
    relatedQuestions: [3, 4],
    codeExample: `// ✅ Jest中Mock useId
import { jest } from '@jest/globals';

// 1. 全局Mock useId
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useId: jest.fn(() => 'test-id')
}));

// 2. 可预测的ID序列Mock
let idCounter = 0;
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useId: jest.fn(() => 'test-id-' + (++idCounter))
}));

// 3. 测试用例中的Mock
describe('Component with useId', () => {
  beforeEach(() => {
    idCounter = 0; // 重置计数器确保测试隔离
  });

  it('should render with stable IDs', () => {
    const { container } = render(<MyComponent />);
    expect(container.querySelector('#test-id-1')).toBeInTheDocument();
  });
});

// ✅ React Testing Library最佳实践
import { render, screen } from '@testing-library/react';

function LoginForm() {
  const usernameId = useId();
  const passwordId = useId();

  return (
    <form>
      <label htmlFor={usernameId}>用户名</label>
      <input
        id={usernameId}
        data-testid="username-input" // 添加测试ID
        aria-label="用户名输入框"
      />

      <label htmlFor={passwordId}>密码</label>
      <input
        id={passwordId}
        data-testid="password-input"
        aria-label="密码输入框"
        type="password"
      />
    </form>
  );
}

// 测试：使用语义化查询而非ID
test('login form accessibility', () => {
  render(<LoginForm />);

  // ✅ 推荐：使用role和label查询
  const usernameInput = screen.getByLabelText('用户名输入框');
  const passwordInput = screen.getByLabelText('密码输入框');

  // ✅ 推荐：使用testid查询
  const usernameByTestId = screen.getByTestId('username-input');

  // ❌ 避免：依赖具体的ID值
  // const usernameById = screen.getByDisplayValue('#:r1:');

  expect(usernameInput).toBeInTheDocument();
  expect(passwordInput).toBeInTheDocument();
});

// ✅ 快照测试：忽略动态ID
test('component snapshot', () => {
  const { container } = render(<LoginForm />);

  // 替换动态ID为稳定值
  const html = container.innerHTML.replace(/id="[^"]*"/g, 'id="stable-id"');
  expect(html).toMatchSnapshot();
});

// ✅ 自定义测试工具
function renderWithStableIds(component) {
  const mockUseId = jest.fn();
  let counter = 0;

  mockUseId.mockImplementation(() => 'stable-id-' + (++counter));

  jest.doMock('react', () => ({
    ...jest.requireActual('react'),
    useId: mockUseId
  }));

  return render(component);
}

// ✅ E2E测试中的处理
// Cypress示例
cy.get('[data-testid="username-input"]').type('<EMAIL>');
cy.get('[aria-label="密码输入框"]').type('password');

// ✅ 测试组件的可访问性
import { axe, toHaveNoViolations } from 'jest-axe';

test('component accessibility', async () => {
  const { container } = render(<LoginForm />);
  const results = await axe(container);

  expect(results).toHaveNoViolations();
});`
  }
];

export default commonQuestions;
