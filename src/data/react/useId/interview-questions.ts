import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: 'useId是什么？它解决了什么问题？',
    answer: {
      brief: 'useId是React 18引入的Hook，用于生成唯一ID，主要解决SSR环境下ID一致性和组件间ID冲突问题。',
      detailed: `**核心概念**：useId是React 18新增的Hook，专门用于生成在组件树中唯一的字符串标识符。

**解决的问题**：
1. **SSR一致性问题**：传统的Math.random()或Date.now()在服务端和客户端会生成不同的ID，导致hydration失败
2. **组件ID冲突**：多个组件实例使用相同的硬编码ID会产生冲突
3. **无障碍属性关联**：表单元素、aria属性需要稳定的ID引用

**工作原理**：基于React内部的Fiber树结构，通过组件在树中的位置生成确定性的ID，确保相同组件树结构下生成相同的ID。

**典型应用场景**：表单元素关联、无障碍属性绑定、组件内部元素标识等。`,
      code: `// 基础用法
function LoginForm() {
  const usernameId = useId();
  const passwordId = useId();

  return (
    <form>
      <label htmlFor={usernameId}>用户名</label>
      <input id={usernameId} type="text" />

      <label htmlFor={passwordId}>密码</label>
      <input id={passwordId} type="password" />
    </form>
  );
}

// 无障碍属性应用
function AccessibleButton() {
  const buttonId = useId();
  const descId = useId();

  return (
    <>
      <button
        id={buttonId}
        aria-describedby={descId}
      >
        提交
      </button>
      <div id={descId}>点击提交表单</div>
    </>
  );
}`
    },
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    tags: ['React 18', 'SSR', '唯一ID', '基础概念']
  },
  {
    id: 2,
    question: 'useId的内部实现原理是什么？它是如何确保ID唯一性的？',
    answer: {
      brief: 'useId基于Fiber树结构和组件位置生成确定性ID，通过树路径编码确保唯一性，使用identifierPrefix隔离不同应用实例。',
      detailed: `**实现原理**：
1. **Fiber树路径编码**：useId遍历当前组件到根节点的Fiber路径，将每个节点的index编码到ID中
2. **确定性算法**：相同的组件树结构总是生成相同的ID序列
3. **Hook计数器**：在同一组件内，多次调用useId会递增计数器确保唯一性

**核心数据结构**：
- **identifierPrefix**：全局前缀，由ReactDOMRoot设置，隔离不同应用
- **Fiber节点index**：组件在父节点中的位置索引
- **Hook链表**：记录组件内Hook调用顺序

**ID格式解析**：
- 格式：\`:r{hookIndex}:{fiberPath}:{rootPrefix}\`
- 示例：\`:r1:0:\` 表示根组件的第一个useId
- 示例：\`:r2:1:0:\` 表示第二层组件的第二个useId

**唯一性保证机制**：
1. **树路径唯一性**：每个组件在Fiber树中的路径是唯一的
2. **Hook顺序唯一性**：同一组件内Hook调用顺序固定
3. **应用实例隔离**：不同React应用通过prefix隔离`,
      code: `// 简化版实现原理
function mountId() {
  const hook = mountWorkInProgressHook();
  const root = getWorkInProgressRoot();
  const identifierPrefix = root.identifierPrefix;

  let id = identifierPrefix;

  // 遍历Fiber树路径
  const fiber = currentlyRenderingFiber;
  let node = fiber.return;

  while (node !== null) {
    if (node.index !== 0) {
      // 使用32进制编码节点位置
      id = ':' + node.index.toString(32) + ':' + id;
    }
    node = node.return;
  }

  // 添加Hook计数器
  id = ':r' + (++idCounter).toString(32) + ':' + id;

  hook.memoizedState = id;
  return id;
}

// 使用示例：理解ID生成
function ComponentA() {
  const id1 = useId(); // :r1:
  const id2 = useId(); // :r2:
  return <ComponentB />;
}

function ComponentB() {
  const id1 = useId(); // :r1:0: (第0个子组件的第1个useId)
  return null;
}`
    },
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    tags: ['Fiber树', '算法原理', '源码分析', '唯一性']
  },
  {
    id: 3,
    question: 'useId与传统的ID生成方式（如Math.random()、uuid）相比有什么优势和限制？',
    answer: {
      brief: 'useId相比传统方式的优势是SSR一致性和零配置，限制是不能用于key属性和持久化存储。',
      detailed: `**useId的优势**：
1. **SSR一致性**：确保服务端和客户端生成相同ID，避免hydration错误
2. **零配置使用**：无需手动管理计数器或种子值
3. **性能优化**：基于Fiber树结构，无额外状态管理开销
4. **React集成**：与React渲染机制深度集成，自动处理组件生命周期

**传统方式的问题**：
1. **Math.random()**：每次调用结果不同，SSR环境下会导致不一致
2. **Date.now()**：时间戳在服务端和客户端可能不同
3. **uuid库**：增加bundle大小，且在SSR下仍有一致性问题
4. **手动计数器**：需要全局状态管理，容易出错

**useId的限制**：
1. **不能用作key**：key需要在列表重排时保持稳定，useId会随组件重新挂载变化
2. **不适合持久化**：ID会随组件重新挂载而改变，不能存储到数据库
3. **版本限制**：仅React 18+支持
4. **格式依赖**：不应依赖具体的ID格式，React可能改变实现

**选择建议**：
- 表单关联、无障碍属性：优先使用useId
- 列表key：使用稳定的业务ID
- 持久化标识：使用业务ID或uuid
- 临时标识：可以使用useId`,
      code: `// ❌ 错误用法：用作key
function TodoList({ todos }) {
  return (
    <ul>
      {todos.map(() => {
        const id = useId(); // 错误！每次渲染都会变化
        return <li key={id}>...</li>;
      })}
    </ul>
  );
}

// ✅ 正确用法：表单关联
function TodoForm() {
  const titleId = useId();
  const descId = useId();

  return (
    <form>
      <label htmlFor={titleId}>标题</label>
      <input id={titleId} />

      <label htmlFor={descId}>描述</label>
      <textarea id={descId} />
    </form>
  );
}

// ✅ 对比：不同场景的ID选择
function ComparisonExample() {
  // 1. 表单元素 - 使用useId
  const formId = useId();

  // 2. 列表key - 使用业务ID
  const todos = [
    { id: 'todo-1', text: '学习React' },
    { id: 'todo-2', text: '写代码' }
  ];

  // 3. 持久化ID - 使用uuid或业务ID
  const userId = 'user-12345'; // 来自后端

  return (
    <div>
      <form id={formId}>...</form>
      {todos.map(todo => (
        <div key={todo.id}>{todo.text}</div>
      ))}
      <div data-user-id={userId}>用户信息</div>
    </div>
  );
}`
    },
    difficulty: 'medium',
    frequency: 'medium',
    category: '最佳实践',
    tags: ['对比分析', 'SSR', '最佳实践', '限制']
  },
  {
    id: 4,
    question: '在构建组件库时，如何使用useId避免ID冲突？请设计一个支持命名空间的ID管理方案。',
    answer: {
      brief: '通过Context提供命名空间，自定义useNamespacedId Hook，结合useId实现层级ID管理，确保组件库的ID隔离。',
      detailed: `**组件库ID冲突问题**：
1. **多实例冲突**：同一页面多个组件实例可能生成相同ID
2. **嵌套组件冲突**：复合组件内部ID可能与外部冲突
3. **第三方集成**：与其他组件库的ID可能冲突

**解决方案设计**：
1. **命名空间隔离**：为每个组件库分配独立的命名空间
2. **层级ID管理**：支持组件嵌套的层级ID结构
3. **Context传递**：通过React Context传递命名空间信息
4. **自定义Hook**：封装useNamespacedId提供增强功能

**架构设计原则**：
- **隔离性**：不同组件库之间完全隔离
- **可扩展性**：支持任意层级的嵌套
- **向后兼容**：不影响现有useId的使用
- **性能优化**：最小化Context更新和重渲染

**实际应用场景**：
- 企业级组件库开发
- 多团队协作的大型项目
- 第三方组件集成
- 复杂的复合组件设计`,
      code: `// 命名空间ID管理方案
import React, { createContext, useContext, useMemo, useId } from 'react';

// 1. 创建命名空间Context
const IdNamespaceContext = createContext(null);

// 2. 命名空间提供者
function IdNamespaceProvider({ namespace, children }) {
  const parentNamespace = useContext(IdNamespaceContext);
  const baseId = useId();

  // 构建层级命名空间
  const fullNamespace = useMemo(() => {
    const parts = [parentNamespace, namespace, baseId].filter(Boolean);
    return parts.join('-');
  }, [parentNamespace, namespace, baseId]);

  return (
    <IdNamespaceContext.Provider value={fullNamespace}>
      {children}
    </IdNamespaceContext.Provider>
  );
}

// 3. 增强的useId Hook
function useNamespacedId(suffix = '') {
  const namespace = useContext(IdNamespaceContext);
  const id = useId();

  return useMemo(() => {
    const parts = [namespace, id, suffix].filter(Boolean);
    return parts.join('-');
  }, [namespace, id, suffix]);
}

// 4. 组件库使用示例
function DataTable({ data, columns }) {
  const tableId = useNamespacedId('table');
  const headerId = useNamespacedId('header');

  return (
    <IdNamespaceProvider namespace="data-table">
      <table id={tableId} aria-labelledby={headerId}>
        <thead>
          <tr id={headerId}>
            {columns.map((col, index) => (
              <TableHeaderCell key={col.key} column={col} index={index} />
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <TableRow key={row.id} row={row} index={index} />
          ))}
        </tbody>
      </table>
    </IdNamespaceProvider>
  );
}

function TableHeaderCell({ column, index }) {
  const cellId = useNamespacedId('header-cell-' + index);
  const sortId = useNamespacedId('sort-' + column.key);

  return (
    <th id={cellId}>
      <span>{column.title}</span>
      <button id={sortId} aria-describedby={cellId}>
        排序
      </button>
    </th>
  );
}

// 5. 使用效果
function App() {
  return (
    <IdNamespaceProvider namespace="my-app">
      {/* 生成的ID格式：my-app-:r1:-data-table-:r2:-table */}
      <DataTable data={data1} columns={columns} />

      {/* 不同实例有不同的ID */}
      <DataTable data={data2} columns={columns} />
    </IdNamespaceProvider>
  );
}`
    },
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    tags: ['组件库', '命名空间', '架构设计', 'Context']
  },
  {
    id: 5,
    question: '在SSR应用中，useId是如何解决hydration不匹配问题的？请分析其在Next.js中的具体应用。',
    answer: {
      brief: 'useId通过确定性算法确保服务端和客户端生成相同ID，解决hydration不匹配，在Next.js中需要配合正确的SSR配置。',
      detailed: `**Hydration不匹配问题**：
1. **传统ID生成问题**：Math.random()、Date.now()在服务端和客户端产生不同值
2. **DOM结构不一致**：ID不匹配导致React无法正确关联服务端和客户端的DOM
3. **用户体验影响**：页面闪烁、表单状态丢失、无障碍功能失效

**useId的解决机制**：
1. **确定性算法**：基于组件树结构生成，相同结构产生相同ID
2. **Fiber树一致性**：服务端和客户端的Fiber树结构完全一致
3. **identifierPrefix同步**：通过ReactDOMRoot配置确保前缀一致

**Next.js集成要点**：
1. **_app.js配置**：确保React 18的正确配置
2. **getServerSideProps**：处理动态数据时的ID一致性
3. **Suspense边界**：处理异步组件的ID生成
4. **开发vs生产**：不同环境下的ID格式可能不同

**最佳实践**：
- 避免在useEffect中动态修改useId生成的ID
- 确保组件树结构在SSR和CSR中完全一致
- 使用React.StrictMode检测潜在问题
- 配合Suspense处理异步加载场景`,
      code: `// Next.js中的useId应用示例

// 1. _app.js - 确保React 18配置
import { createRoot } from 'react-dom/client';

export default function MyApp({ Component, pageProps }) {
  return <Component {...pageProps} />;
}

// 2. 表单组件 - SSR安全的ID生成
function ContactForm({ initialData }) {
  const nameId = useId();
  const emailId = useId();
  const messageId = useId();

  const [formData, setFormData] = useState(initialData || {
    name: '',
    email: '',
    message: ''
  });

  return (
    <form>
      <label htmlFor={nameId}>姓名</label>
      <input
        id={nameId}
        value={formData.name}
        onChange={(e) => setFormData(prev => ({
          ...prev,
          name: e.target.value
        }))}
      />

      <label htmlFor={emailId}>邮箱</label>
      <input
        id={emailId}
        type="email"
        value={formData.email}
        onChange={(e) => setFormData(prev => ({
          ...prev,
          email: e.target.value
        }))}
      />

      <label htmlFor={messageId}>留言</label>
      <textarea
        id={messageId}
        value={formData.message}
        onChange={(e) => setFormData(prev => ({
          ...prev,
          message: e.target.value
        }))}
      />
    </form>
  );
}

// 3. 页面组件 - getServerSideProps集成
export async function getServerSideProps(context) {
  // 获取初始数据
  const initialData = await fetchUserData(context.params.id);

  return {
    props: {
      initialData
    }
  };
}

export default function ContactPage({ initialData }) {
  return (
    <div>
      <h1>联系我们</h1>
      <ContactForm initialData={initialData} />
    </div>
  );
}

// 4. 调试工具 - 检测ID一致性
function IdConsistencyChecker() {
  const id = useId();

  useEffect(() => {
    // 开发环境下检查ID一致性
    if (process.env.NODE_ENV === 'development') {
      const serverRendered = document.querySelector('[data-server-id]');
      if (serverRendered) {
        const serverId = serverRendered.getAttribute('data-server-id');
        if (serverId !== id) {
          console.warn('ID不一致:', { serverId, clientId: id });
        }
      }
    }
  }, [id]);

  return (
    <div data-server-id={id} data-client-id={id}>
      ID: {id}
    </div>
  );
}

// 5. 错误处理 - Hydration错误恢复
function ErrorBoundary({ children }) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const handleError = (event) => {
      if (event.message.includes('Hydration')) {
        console.error('Hydration错误，可能是ID不匹配');
        setHasError(true);
      }
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return <div>页面加载出错，请刷新重试</div>;
  }

  return children;
}`
    },
    difficulty: 'hard',
    frequency: 'low',
    category: '高级应用',
    tags: ['SSR', 'Next.js', 'Hydration', '错误处理']
  }
];

export default interviewQuestions;
