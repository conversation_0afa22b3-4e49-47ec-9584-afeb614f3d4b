import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  overview: `useId虽然设计简洁，但在实际使用中可能遇到SSR一致性问题、ID格式依赖、测试环境不稳定等调试挑战。这些问题往往表现为hydration警告、测试失败或ID冲突，需要系统的调试方法来快速定位和解决。

本指南提供4种核心调试策略、6个实用调试工具，以及5个预防技巧，帮助开发者快速诊断useId相关问题，提升开发效率和应用稳定性。`,

  troubleshooting: [
    {
      symptom: 'SSR Hydration不匹配警告',
      possibleCauses: [
        '服务端和客户端的组件树结构不一致',
        '条件渲染导致的组件挂载顺序差异',
        'identifierPrefix配置在不同环境下不一致'
      ],
      solutions: [
        '使用React DevTools检查组件树差异，确保SSR和CSR结构完全一致',
        '添加hydration调试代码，记录ID生成过程和组件挂载顺序',
        '配置统一的identifierPrefix，避免环境差异导致的ID不匹配'
      ],
      codeExample: `// 调试SSR hydration问题
function HydrationDebugger() {
  const id = useId();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);

    // 开发环境下记录hydration信息
    if (process.env.NODE_ENV === 'development') {
      console.log('Hydration Debug:', {
        id,
        isServer: typeof window === 'undefined',
        timestamp: Date.now()
      });
    }
  }, [id]);

  // 添加数据属性便于调试
  return (
    <div
      id={id}
      data-hydration-id={id}
      data-is-client={isClient}
    >
      内容
    </div>
  );
}

// 检查ID一致性的工具函数
function checkIdConsistency() {
  const serverIds = document.querySelectorAll('[data-server-id]');
  const clientIds = document.querySelectorAll('[data-client-id]');

  serverIds.forEach(element => {
    const serverId = element.getAttribute('data-server-id');
    const clientId = element.getAttribute('data-client-id');

    if (serverId !== clientId) {
      console.error('ID不匹配:', { serverId, clientId, element });
    }
  });
}`,
      severity: 'high'
    },
    {
      symptom: '测试环境中ID不稳定导致快照测试失败',
      possibleCauses: [
        'useId在不同测试运行中生成不同的ID值',
        '测试环境没有正确Mock useId',
        '组件挂载顺序在测试中不稳定'
      ],
      solutions: [
        '在测试中Mock useId返回可预测的值',
        '使用data-testid而不是依赖useId生成的ID进行元素查询',
        '配置测试环境的快照序列化器忽略动态ID'
      ],
      codeExample: `// Jest中Mock useId
import { jest } from '@jest/globals';

// 全局Mock useId
let idCounter = 0;
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useId: jest.fn(() => 'test-id-' + (++idCounter))
}));

// 测试用例
describe('Component with useId', () => {
  beforeEach(() => {
    idCounter = 0; // 重置计数器
  });

  it('should render with stable IDs', () => {
    const { container } = render(<MyComponent />);

    // 使用testid而不是ID查询
    expect(screen.getByTestId('username-input')).toBeInTheDocument();

    // 快照测试时替换动态ID
    const html = container.innerHTML.replace(/id="[^"]*"/g, 'id="stable-id"');
    expect(html).toMatchSnapshot();
  });
});

// 自定义测试工具
function renderWithStableIds(component) {
  const mockUseId = jest.fn();
  let counter = 0;

  mockUseId.mockImplementation(() => 'stable-id-' + (++counter));

  return render(component);
}`,
      severity: 'medium'
    },
    {
      symptom: 'ID格式依赖导致的代码脆弱性',
      possibleCauses: [
        '代码中解析或依赖useId返回的具体格式',
        '将useId的值用于业务逻辑判断',
        '尝试从ID中提取组件层级信息'
      ],
      solutions: [
        '重构代码，移除对ID格式的依赖',
        '使用业务ID和DOM ID分离的设计模式',
        '添加代码检查规则，防止ID格式依赖'
      ],
      codeExample: `// ❌ 错误：依赖ID格式
function BadComponent() {
  const id = useId();

  // 错误：解析ID格式
  const isFirstComponent = id.includes('r1');
  const componentLevel = id.split(':').length;

  return <div id={id}>内容</div>;
}

// ✅ 正确：ID格式无关的设计
function GoodComponent({ level, isFirst, businessId }) {
  const domId = useId(); // 仅用于DOM标识

  return (
    <div
      id={domId}
      data-business-id={businessId}
      data-level={level}
      data-is-first={isFirst}
    >
      内容
    </div>
  );
}

// ESLint规则检查ID格式依赖
const eslintRule = {
  'no-useid-format-dependency': {
    create(context) {
      return {
        CallExpression(node) {
          if (node.callee.name === 'useId') {
            // 检查是否有格式依赖的使用
            // 实现检查逻辑...
          }
        }
      };
    }
  }
};`,
      severity: 'low'
    }
  ],

  tools: [
    {
      name: 'React DevTools',
      description: 'React官方开发工具，用于检查组件树和Hook状态',
      usage: '在Components面板中查看useId的Hook状态，在Profiler中分析渲染性能',
      category: '浏览器扩展',
      documentation: 'https://react.dev/learn/react-developer-tools'
    },
    {
      name: 'Chrome DevTools Console',
      description: '浏览器控制台，用于运行调试脚本和查看日志',
      usage: '使用console.log记录ID生成过程，运行ID一致性检查脚本',
      category: '浏览器工具',
      documentation: 'https://developer.chrome.com/docs/devtools/console/'
    },
    {
      name: 'Jest Testing Framework',
      description: 'JavaScript测试框架，支持Mock和快照测试',
      usage: 'Mock useId返回稳定值，使用快照测试验证组件结构',
      category: '测试工具',
      documentation: 'https://jestjs.io/docs/mock-functions'
    }
  ],

  bestPractices: [
    '在开发环境中添加ID调试信息，便于问题排查',
    '使用data-testid属性进行测试，避免依赖useId生成的ID',
    '建立ID一致性检查机制，及时发现SSR问题',
    '在代码审查中检查ID格式依赖，确保代码健壮性',
    '配置ESLint规则，自动检测useId的不当使用'
  ],

  commonMistakes: [
    {
      mistake: '在条件语句中调用useId',
      consequence: '违反Hook规则，导致组件状态不一致和难以调试的错误',
      solution: '将useId调用移到组件顶层，在条件中使用生成的ID',
      prevention: '启用React Hook ESLint规则，自动检测Hook规则违反'
    },
    {
      mistake: '将useId生成的ID用作React元素的key',
      consequence: '破坏React的元素追踪机制，导致性能问题和状态丢失',
      solution: '使用稳定的业务ID作为key，useId仅用于DOM元素标识',
      prevention: '在代码审查中重点检查key属性的使用，建立最佳实践文档'
    },
    {
      mistake: '在测试中依赖useId的具体返回值',
      consequence: '测试脆弱且不稳定，在不同环境下可能失败',
      solution: 'Mock useId返回可预测的值，或使用语义化的查询方式',
      prevention: '建立测试最佳实践，使用Testing Library的语义化查询'
    }
  ]
};

export default debuggingTips;
