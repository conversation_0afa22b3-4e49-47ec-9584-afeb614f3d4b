import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "useId是React 18中用于生成唯一标识符的Hook，专门解决了组件中需要唯一ID的场景，确保服务端渲染和客户端渲染的一致性",

  introduction: `useId是React 18引入的革命性Hook，为解决长期困扰开发者的ID生成和SSR一致性问题而生。

它遵循"确定性生成"的设计理念，在性能和可靠性之间做出了完美权衡：既保证了ID的唯一性，又确保了服务端和客户端的完全一致。

主要用于表单元素关联、无障碍属性绑定和组件内部标识。相比手动生成ID或使用第三方库，它的创新在于与React渲染机制的深度集成和内置的SSR支持。

在React生态中，它是基础设施层的核心工具，常见于UI组件库、表单处理和无障碍功能，特别适合需要稳定ID且支持SSR的场景。

核心优势包括零配置使用、SSR安全性，但也需要注意不能用于key属性或持久化存储。`,

  syntax: `// 基础语法
function useId(): string

// 源码位置：react/packages/react/src/ReactHooks.js
// 类型定义：react/packages/shared/ReactTypes.js

// 完整TypeScript定义
declare function useId(): string;

// 使用示例
const id = useId();`,

  parameters: [],

  returnValue: {
    type: "string",
    description: "返回一个在当前组件树中唯一的字符串ID，格式类似 ':r1:' 或 ':r1:r2:'，确保在服务端和客户端渲染时保持一致"
  },

  keyFeatures: [
    {
      feature: "SSR一致性保证",
      description: "确保服务端渲染和客户端渲染生成相同的ID，彻底解决hydration不匹配问题",
      importance: 'critical'
    },
    {
      feature: "零配置唯一性",
      description: "无需手动管理计数器或种子值，React自动确保在组件树中的唯一性",
      importance: 'high'
    },
    {
      feature: "性能优化设计",
      description: "基于React内部的Fiber树结构生成，避免了额外的状态管理和重新渲染",
      importance: 'high'
    },
    {
      feature: "无障碍友好",
      description: "专为aria-labelledby、aria-describedby等无障碍属性设计，提升应用可访问性",
      importance: 'medium'
    }
  ],

  // 快速示例 - 完整且简单的基础用法
  quickExample: `function UserForm() {
  // 生成唯一ID，确保SSR一致性
  const nameId = useId();
  const emailId = useId();

  return (
    <form>
      {/* 表单元素关联 - 提升可访问性 */}
      <label htmlFor={nameId}>姓名</label>
      <input
        id={nameId}
        type="text"
        placeholder="请输入姓名"
      />

      {/* 多个ID使用 - 每次调用都是唯一的 */}
      <label htmlFor={emailId}>邮箱</label>
      <input
        id={emailId}
        type="email"
        placeholder="请输入邮箱"
      />
    </form>
  );
}`,

  // 业务场景图表
  scenarioDiagram: `graph TD
    A[用户交互场景] --> B[表单元素关联]
    A --> C[无障碍属性绑定]
    A --> D[组件内部标识]

    B --> B1[label + input]
    B --> B2[fieldset + legend]
    B --> B3[表单验证提示]

    C --> C1[aria-labelledby]
    C --> C2[aria-describedby]
    C --> C3[role属性关联]

    D --> D1[Modal对话框]
    D --> D2[Tooltip提示]
    D --> D3[Tab面板切换]

    E[技术特性] --> F[SSR一致性]
    E --> G[零配置使用]
    E --> H[性能优化]

    F --> F1[服务端渲染]
    F --> F2[客户端水合]
    F --> F3[避免ID冲突]

    G --> G1[自动生成]
    G --> G2[无需管理状态]
    G --> G3[React内置支持]

    H --> H1[基于Fiber树]
    H --> H2[避免重渲染]
    H --> H3[内存优化]

    I[相关API] --> J[forwardRef]
    I --> K[createPortal]
    I --> L[useRef]

    J --> J1[ref传递场景]
    K --> K1[Portal ID管理]
    L --> L1[DOM引用配合]

    style A fill:#e1f5fe
    style E fill:#f3e5f5
    style I fill:#e8f5e8`,

  commonUseCases: [
    {
      title: "表单元素关联",
      description: "为input和label建立关联关系，提升表单的可访问性和用户体验",
      code: `function LoginForm() {
  const usernameId = useId();
  const passwordId = useId();

  return (
    <form>
      <label htmlFor={usernameId}>用户名</label>
      <input id={usernameId} type="text" />

      <label htmlFor={passwordId}>密码</label>
      <input id={passwordId} type="password" />
    </form>
  );
}`
    },
    {
      title: "无障碍属性绑定",
      description: "为aria-labelledby、aria-describedby等属性提供稳定的ID引用",
      code: `function AccessibleButton() {
  const buttonId = useId();
  const descriptionId = useId();

  return (
    <>
      <button
        id={buttonId}
        aria-describedby={descriptionId}
      >
        提交订单
      </button>
      <div id={descriptionId}>
        点击后将跳转到支付页面
      </div>
    </>
  );
}`
    },
    {
      title: "组件内部标识",
      description: "为组件内部元素生成唯一标识，避免多个组件实例之间的ID冲突",
      code: `function Modal({ title, children }) {
  const titleId = useId();
  const contentId = useId();

  return (
    <div
      role="dialog"
      aria-labelledby={titleId}
      aria-describedby={contentId}
    >
      <h2 id={titleId}>{title}</h2>
      <div id={contentId}>{children}</div>
    </div>
  );
}`
    }
  ],

  limitations: [
    "不能用作React元素的key属性，key需要在列表渲染中保持稳定",
    "不适合持久化存储，每次组件重新挂载时ID会发生变化",
    "仅在React 18+版本中可用，旧版本需要使用polyfill或替代方案",
    "生成的ID格式为React内部格式，不适合作为业务逻辑的标识符",
    "在严格模式下开发环境中，组件会被双重渲染但ID保持一致"
  ],

  bestPractices: [
    "优先用于表单元素的htmlFor和id属性关联，提升可访问性",
    "结合aria-*属性使用，为屏幕阅读器提供更好的语义信息",
    "在组件库开发中使用，确保组件实例之间的ID不冲突",
    "避免将useId生成的值用于业务逻辑判断或数据库存储",
    "在需要多个相关ID时，可以基于useId返回值构建：id + '-suffix'",
    "配合forwardRef使用时，确保ID在ref传递过程中的正确性",
    "在SSR应用中优先使用useId而不是Math.random()或Date.now()",
    "将useId与useMemo结合使用时要谨慎，避免破坏ID的稳定性"
  ],

  warnings: [
    "绝对不要将useId的返回值用作key属性，这会导致React无法正确追踪元素",
    "不要尝试解析或依赖useId返回的具体格式，React可能在未来版本中改变格式",
    "避免在条件语句中调用useId，必须在组件顶层调用以确保Hook规则",
    "不要将useId生成的ID存储到localStorage或发送到服务器",
    "在使用useId + 后缀的模式时，确保后缀不包含冒号字符以避免格式冲突"
  ]
};

export default basicInfo;
