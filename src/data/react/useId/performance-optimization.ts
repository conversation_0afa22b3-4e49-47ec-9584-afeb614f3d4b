import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: '减少useId调用频率',
      description: '通过合理的组件设计和ID复用策略，减少不必要的useId调用，提升整体性能',
      techniques: [
        {
          name: 'ID复用和组合策略',
          description: '在同一组件中使用基础ID生成多个相关ID，避免多次调用useId',
          code: `// ❌ 性能问题 - 多次调用useId
function FormWithManyFields() {
  const field1Id = useId();
  const field2Id = useId();
  const field3Id = useId();
  const field4Id = useId();
  const field5Id = useId();
  // 每个字段都调用一次useId，增加了Hook链表长度

  return (
    <form>
      <input id={field1Id} />
      <input id={field2Id} />
      <input id={field3Id} />
      <input id={field4Id} />
      <input id={field5Id} />
    </form>
  );
}

// ✅ 性能优化 - ID复用策略
function OptimizedFormWithManyFields() {
  const baseId = useId(); // 只调用一次useId

  // 基于基础ID生成相关ID
  const field1Id = baseId + '-field1';
  const field2Id = baseId + '-field2';
  const field3Id = baseId + '-field3';
  const field4Id = baseId + '-field4';
  const field5Id = baseId + '-field5';

  return (
    <form>
      <input id={field1Id} />
      <input id={field2Id} />
      <input id={field3Id} />
      <input id={field4Id} />
      <input id={field5Id} />
    </form>
  );
}

// ✅ 更高级的优化 - 自定义Hook
function useFormIds(fieldNames) {
  const baseId = useId();

  return useMemo(() => {
    const ids = {};
    fieldNames.forEach(name => {
      ids[name] = baseId + '-' + name;
    });
    return ids;
  }, [baseId, fieldNames]);
}

function AdvancedOptimizedForm() {
  const fieldIds = useFormIds(['username', 'email', 'password', 'confirm']);

  return (
    <form>
      <input id={fieldIds.username} placeholder="用户名" />
      <input id={fieldIds.email} placeholder="邮箱" />
      <input id={fieldIds.password} type="password" placeholder="密码" />
      <input id={fieldIds.confirm} type="password" placeholder="确认密码" />
    </form>
  );
}`,
          impact: 'high',
          difficulty: 'medium'
        },
        {
          name: '组件层级优化',
          description: '合理设计组件层级，避免深层嵌套中的频繁useId调用',
          code: `// ❌ 性能问题 - 深层嵌套中频繁调用useId
function DeepNestedForm() {
  return (
    <div>
      <FormSection title="个人信息">
        <FormField label="姓名" />  {/* 内部调用useId */}
        <FormField label="年龄" />  {/* 内部调用useId */}
      </FormSection>
      <FormSection title="联系方式">
        <FormField label="邮箱" />  {/* 内部调用useId */}
        <FormField label="电话" />  {/* 内部调用useId */}
      </FormSection>
    </div>
  );
}

function FormField({ label }) {
  const fieldId = useId(); // 每个字段都生成新ID
  return (
    <div>
      <label htmlFor={fieldId}>{label}</label>
      <input id={fieldId} />
    </div>
  );
}

// ✅ 性能优化 - 统一ID管理
function OptimizedForm() {
  const formId = useId();

  // 在顶层生成所有需要的ID
  const fieldIds = useMemo(() => ({
    name: formId + '-name',
    age: formId + '-age',
    email: formId + '-email',
    phone: formId + '-phone'
  }), [formId]);

  return (
    <div>
      <FormSection title="个人信息">
        <OptimizedFormField label="姓名" id={fieldIds.name} />
        <OptimizedFormField label="年龄" id={fieldIds.age} />
      </FormSection>
      <FormSection title="联系方式">
        <OptimizedFormField label="邮箱" id={fieldIds.email} />
        <OptimizedFormField label="电话" id={fieldIds.phone} />
      </FormSection>
    </div>
  );
}

function OptimizedFormField({ label, id }) {
  // 不再调用useId，直接使用传入的ID
  return (
    <div>
      <label htmlFor={id}>{label}</label>
      <input id={id} />
    </div>
  );
}`,
          impact: 'medium',
          difficulty: 'easy'
        }
      ]
    },
    {
      title: 'SSR性能优化',
      description: '在服务端渲染环境中优化useId的性能，减少服务端计算开销和客户端hydration时间',
      techniques: [
        {
          name: 'identifierPrefix优化',
          description: '合理配置identifierPrefix，减少ID字符串长度和计算复杂度',
          code: `// ❌ 性能问题 - 过长的prefix
import { createRoot } from 'react-dom/client';

const container = document.getElementById('root');
const root = createRoot(container, {
  identifierPrefix: 'my-very-long-application-name-with-version-1-2-3-'
  // 过长的prefix会增加每个ID的长度，影响性能
});

// ✅ 性能优化 - 简短的prefix
const optimizedRoot = createRoot(container, {
  identifierPrefix: 'app-' // 简短但仍然唯一
});

// ✅ 更好的优化 - 环境相关的prefix
const getOptimalPrefix = () => {
  if (typeof window === 'undefined') {
    // 服务端使用更短的prefix
    return 's-';
  } else {
    // 客户端使用稍长但仍然简短的prefix
    return 'c-';
  }
};

const environmentOptimizedRoot = createRoot(container, {
  identifierPrefix: getOptimalPrefix()
});

// ✅ 生产环境优化
const productionRoot = createRoot(container, {
  identifierPrefix: process.env.NODE_ENV === 'production' ? 'p-' : 'dev-'
});`,
          impact: 'medium',
          difficulty: 'easy'
        },
        {
          name: 'Hydration性能优化',
          description: '优化客户端hydration过程中的ID验证和匹配性能',
          code: `// ❌ 性能问题 - 复杂的条件渲染影响hydration
function ProblematicSSRComponent({ user, isLoggedIn }) {
  const [clientData, setClientData] = useState(null);
  const formId = useId();

  // 客户端特有的数据获取
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setClientData(getClientOnlyData());
    }
  }, []);

  // 条件渲染可能导致服务端和客户端结构不一致
  return (
    <div>
      {isLoggedIn && clientData ? (
        <form id={formId}>
          <input placeholder="搜索..." />
        </form>
      ) : (
        <div id={formId}>请登录</div>
      )}
    </div>
  );
}

// ✅ 性能优化 - 确保SSR/CSR结构一致
function OptimizedSSRComponent({ user, isLoggedIn, initialData }) {
  const [clientData, setClientData] = useState(initialData);
  const formId = useId();
  const loginPromptId = useId();

  // 延迟加载客户端特有数据，不影响初始结构
  useEffect(() => {
    if (isLoggedIn && !clientData) {
      getClientOnlyData().then(setClientData);
    }
  }, [isLoggedIn, clientData]);

  // 保持结构一致，只改变内容
  return (
    <div>
      {isLoggedIn ? (
        <form id={formId}>
          <input
            placeholder={clientData ? "搜索..." : "加载中..."}
            disabled={!clientData}
          />
        </form>
      ) : (
        <div id={loginPromptId}>请登录</div>
      )}
    </div>
  );
}

// ✅ 高级优化 - 使用Suspense优化hydration
function SuspenseOptimizedComponent({ userId }) {
  const formId = useId();

  return (
    <div>
      <form id={formId}>
        <Suspense fallback={<input placeholder="加载中..." disabled />}>
          <AsyncSearchInput userId={userId} />
        </Suspense>
      </form>
    </div>
  );
}

function AsyncSearchInput({ userId }) {
  const data = use(fetchUserPreferences(userId)); // React 18+ use hook

  return (
    <input
      placeholder="搜索..."
      defaultValue={data.lastSearch}
    />
  );
}`,
          impact: 'high',
          difficulty: 'hard'
        }
      ]
    }
  ],

  performanceMetrics: {
    idGenerationTime: {
      description: '测量useId生成ID的时间开销，通常应该在微秒级别',
      tool: 'Performance API',
      example: 'performance.mark("useId-start"); const id = useId(); performance.mark("useId-end");'
    },
    hydrationTime: {
      description: '测量包含useId的组件在hydration过程中的时间消耗',
      tool: 'React DevTools Profiler',
      example: '在Profiler中查看组件的hydration阶段耗时，关注ID相关的验证时间'
    },
    memoryUsage: {
      description: '监控useId在Hook链表中的内存占用和ID字符串的内存开销',
      tool: 'Chrome DevTools Memory Profiler',
      example: '对比使用useId前后的内存快照，关注字符串对象的增长'
    }
  },

  bestPractices: [
    '在同一组件中优先使用ID复用策略，避免多次调用useId',
    '合理设计组件层级，在顶层统一管理ID生成和分发',
    '配置简短但唯一的identifierPrefix，平衡唯一性和性能',
    '确保SSR和CSR的组件结构完全一致，避免hydration性能问题',
    '在大型表单中使用自定义Hook统一管理ID生成',
    '避免在循环或条件语句中调用useId，保持Hook调用的稳定性',
    '使用React.memo包装使用useId的组件，减少不必要的重新渲染',
    '在性能敏感的场景中，考虑使用Context传递ID而不是在每个组件中生成'
  ],

  commonPitfalls: [
    {
      issue: '在循环中调用useId导致性能下降',
      cause: '违反了Hook规则，每次渲染时Hook调用次数可能不同，导致React内部状态混乱',
      solution: '在循环外生成基础ID，然后在循环中使用索引或其他稳定标识符组合生成唯一ID'
    },
    {
      issue: 'SSR环境下ID生成过慢影响首屏渲染',
      cause: 'identifierPrefix过长或组件层级过深，导致ID生成和字符串拼接开销过大',
      solution: '优化identifierPrefix长度，减少不必要的组件嵌套，使用ID复用策略'
    },
    {
      issue: '客户端hydration时ID不匹配导致性能问题',
      cause: '服务端和客户端的组件树结构不一致，或者存在条件渲染差异',
      solution: '确保初始渲染的组件结构完全一致，将客户端特有的逻辑延迟到hydration之后执行'
    }
  ]
};

export default performanceOptimization;
