import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  background: `useId的诞生标志着React在ID管理领域的一次重要突破，它不仅解决了长期困扰开发者的SSR一致性问题，更体现了React团队对开发者体验的深度思考。

从早期的手动ID管理到现在的自动化生成，这一演进过程反映了前端开发从"手工作坊"向"工业化生产"的转变。了解useId的历史背景，有助于理解React生态系统的设计哲学，掌握现代前端开发的核心理念。`,

  timeline: [
    {
      year: '2015-2017',
      event: 'SSR一致性问题凸显',
      significance: 'React SSR广泛应用，ID不一致导致的hydration错误成为普遍痛点',
      technicalDetails: '开发者普遍使用Math.random()或Date.now()生成ID，在SSR环境下导致服务端和客户端不一致，引发大量hydration警告和用户体验问题'
    },
    {
      year: '2018-2019',
      event: 'React Hooks革命',
      significance: 'Hooks的引入为状态管理带来新范式，但ID管理问题依然存在',
      technicalDetails: 'React 16.8引入Hooks，虽然解决了状态逻辑复用问题，但开发者仍需手动处理ID生成，特别是在表单和无障碍场景中'
    },
    {
      year: '2020-2021',
      event: 'React 18并发特性开发',
      significance: 'React 18的并发渲染特性对ID一致性提出更高要求',
      technicalDetails: 'Concurrent Rendering和Suspense的引入使得ID管理变得更加复杂，传统的ID生成方案在并发环境下可能产生竞态条件'
    },
    {
      year: '2022年3月',
      event: 'useId正式发布',
      significance: 'React 18.0.0正式发布useId，彻底解决ID管理的历史难题',
      technicalDetails: 'useId基于Fiber树结构生成确定性ID，确保SSR一致性，支持并发渲染，成为React生态系统的重要基础设施'
    }
  ],

  keyFigures: [
    {
      name: 'Dan Abramov',
      role: 'React核心团队成员',
      contribution: '在多个技术分享中强调了ID一致性问题的重要性，推动了useId的设计和实现',
      significance: '作为React生态的重要布道者，他的技术洞察直接影响了useId的设计理念和开发者接受度'
    },
    {
      name: 'Sebastian Markbåge',
      role: 'React架构师',
      contribution: '主导了React 18并发特性的设计，useId是其架构愿景的重要组成部分',
      significance: '他对React未来架构的思考直接塑造了useId的技术实现和API设计'
    }
  ],

  concepts: [
    {
      term: 'SSR Hydration一致性',
      definition: '服务端渲染的HTML与客户端首次渲染结果必须完全一致，包括所有属性值',
      evolution: '从早期的"能用就行"到现在的"完美一致"，体现了React对用户体验的不断追求',
      modernRelevance: '现代Web应用的基础要求，直接影响SEO、性能和用户体验，是衡量SSR方案成熟度的重要指标'
    },
    {
      term: 'Fiber树路径编码',
      definition: '基于React Fiber架构的组件树结构，将组件位置信息编码为唯一标识符',
      evolution: '从简单的全局计数器到基于树结构的路径编码，体现了算法设计的精妙和React架构的成熟',
      modernRelevance: '为现代React应用提供了高效、可靠的ID生成机制，是大规模应用架构的重要支撑'
    },
    {
      term: '确定性算法',
      definition: '在相同输入条件下总是产生相同输出的算法，确保了跨环境的一致性',
      evolution: '从随机性ID生成到确定性算法，反映了前端工程化从"随意"到"严谨"的转变',
      modernRelevance: '现代前端开发的核心原则，不仅适用于ID生成，更是可预测、可测试代码的基础'
    }
  ],

  designPhilosophy: `useId的设计哲学体现了React团队的三个核心理念：

**1. 开发者体验至上**
React团队深知ID管理是开发者的痛点，useId的设计目标是"零配置、零学习成本"。开发者只需调用useId()就能获得可靠的ID，无需关心内部实现细节。

**2. 架构一致性**
useId完美融入了React的Hooks体系和Fiber架构，它不是一个独立的工具，而是React生态系统的有机组成部分。这种设计确保了API的一致性和可预测性。

**3. 面向未来的设计**
useId的实现考虑了React的长期发展规划，包括并发渲染、Suspense、Server Components等未来特性。它不仅解决了当前问题，更为未来的技术演进奠定了基础。

这种设计哲学反映了React团队对技术发展趋势的深刻洞察和对开发者需求的精准把握。`,

  impact: `useId的推出对前端生态系统产生了深远影响：

**技术标准化**：建立了React生态系统中ID管理的标准模式，推动了相关工具和库的统一。

**开发效率提升**：消除了开发者在ID管理上的认知负担，让开发者能够专注于业务逻辑。

**生态系统成熟**：促进了React SSR生态的进一步成熟，为Next.js、Remix等框架提供了更可靠的基础设施。

**无障碍性推进**：降低了实现Web无障碍的技术门槛，推动了更多应用支持屏幕阅读器等辅助技术。`,

  modernRelevance: `在当今的前端开发环境中，useId的价值愈发凸显：

**现代Web应用的基础设施**：随着SSR、SSG的普及，useId已成为现代React应用不可或缺的基础工具。

**企业级应用的可靠保障**：在大规模、高并发的企业级应用中，useId提供了稳定可靠的ID管理方案。

**技术演进的重要节点**：useId代表了React从"库"向"框架"转变的重要里程碑，体现了现代前端框架的成熟度。

**未来发展的坚实基础**：为React Server Components、并发特性等未来技术提供了重要的基础支撑。

学习useId不仅是掌握一个API，更是理解现代前端开发理念和React生态系统演进的重要途径。`
};

export default knowledgeArchaeology;
