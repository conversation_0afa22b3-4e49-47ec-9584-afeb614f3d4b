import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么TypeScript报错"Type is not assignable to ComponentClass"？如何解决类型不匹配问题？',
    answer: `这个错误通常发生在以下几种情况：

**常见原因：**
1. **Props类型不匹配**：组件实际接受的props与ComponentClass声明的类型不一致
2. **State类型错误**：组件的state类型与声明不符
3. **静态属性缺失**：组件缺少ComponentClass期望的静态属性
4. **泛型参数错误**：泛型参数使用不当或类型推断失败

**解决方案：**
- 确保组件props和state类型与ComponentClass声明完全匹配
- 使用类型断言或显式类型声明解决推断问题
- 检查组件是否正确继承React.Component
- 验证静态属性（如defaultProps、displayName）的类型`,
    code: `// ❌ 常见错误案例
interface MyProps {
  title: string;
  count: number;
}

// 错误：组件props不匹配
class BadComponent extends React.Component<{ name: string }> {
  render() {
    return <div>{this.props.name}</div>;
  }
}

// 这里会报错：类型不匹配
const badComponentClass: ComponentClass<MyProps> = BadComponent;

// ✅ 正确的解决方案
interface MyProps {
  title: string;
  count: number;
}

interface MyState {
  isActive: boolean;
}

// 1. 确保props类型完全匹配
class GoodComponent extends React.Component<MyProps, MyState> {
  state: MyState = {
    isActive: false
  };

  // 2. 添加正确的静态属性
  static defaultProps: Partial<MyProps> = {
    count: 0
  };

  static displayName = 'GoodComponent';

  render() {
    return (
      <div>
        <h1>{this.props.title}</h1>
        <p>Count: {this.props.count}</p>
      </div>
    );
  }
}

// 现在类型匹配正确
const goodComponentClass: ComponentClass<MyProps, MyState> = GoodComponent;

// 3. 使用类型断言处理复杂情况
const flexibleComponent = GoodComponent as ComponentClass<MyProps>;

// 4. 使用工具类型解决推断问题
type ExtractProps<T> = T extends ComponentClass<infer P> ? P : never;
type ComponentProps = ExtractProps<typeof GoodComponent>; // MyProps`,
    tags: ['TypeScript错误', '类型匹配'],
    relatedQuestions: ['如何调试TypeScript类型错误？', '泛型参数如何正确使用？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '如何在ComponentClass中正确使用泛型约束和条件类型？复杂的类型推断为什么失败？',
    answer: `泛型约束和条件类型是ComponentClass的高级特性，正确使用需要理解TypeScript的类型系统：

**泛型约束的作用：**
- 限制泛型参数必须满足特定条件
- 提供更精确的类型检查和智能提示
- 避免运行时类型错误

**常见推断失败原因：**
1. **过度复杂的嵌套泛型**：TypeScript推断能力有限
2. **循环类型引用**：相互依赖的类型定义
3. **条件类型递归过深**：超出TypeScript递归限制
4. **缺少类型约束**：泛型参数范围过宽

**解决策略：**
- 使用extends关键字添加类型约束
- 将复杂类型分解为多个简单类型
- 使用显式类型注解辅助推断
- 利用映射类型和工具类型简化定义`,
    code: `// 复杂泛型约束的正确使用方式

// 1. 基础约束模式
interface BaseProps {
  id: string;
  className?: string;
}

// 约束泛型必须包含基础属性
type ConstrainedComponentClass<P extends BaseProps> = ComponentClass<P>;

// 2. 条件类型应用
type PropsWithChildren<P> = P extends { children?: any } 
  ? P 
  : P & { children?: ReactNode };

type ComponentWithChildren<P extends BaseProps> = ComponentClass<PropsWithChildren<P>>;

// 3. 复杂约束示例
interface FormFieldProps extends BaseProps {
  name: string;
  value: any;
  onChange: (value: any) => void;
}

interface ValidatableProps {
  validate?: (value: any) => string | null;
}

// 组合约束：必须是表单字段且可验证
type ValidatableFormComponent<P extends FormFieldProps & ValidatableProps> = 
  ComponentClass<P>;

// 4. 解决推断失败的实际案例
// ❌ 推断失败：过度复杂的嵌套
type ComplexBadType<T> = T extends ComponentClass<infer P> 
  ? P extends { [K in keyof P]: infer U } 
    ? U extends string 
      ? Record<K, U>
      : never 
    : never 
  : never;

// ✅ 简化版本：分步推断
type ExtractComponentProps<T> = T extends ComponentClass<infer P> ? P : never;
type StringProps<P> = {
  [K in keyof P]: P[K] extends string ? P[K] : never;
};
type SimplifiedType<T> = StringProps<ExtractComponentProps<T>>;

// 5. 实用的工具类型
// 检查组件是否有特定prop
type HasProp<T, K extends string> = T extends ComponentClass<infer P>
  ? K extends keyof P 
    ? true 
    : false
  : false;

// 提取组件的必需props
type RequiredProps<T> = T extends ComponentClass<infer P>
  ? {
      [K in keyof P as P[K] extends undefined ? never : K]: P[K];
    }
  : never;

// 使用示例
interface UserProps extends BaseProps {
  name: string;
  email?: string;
  age: number;
}

class UserComponent extends React.Component<UserProps> {
  render() {
    return <div>{this.props.name}</div>;
  }
}

// 类型检查和推断测试
type HasNameProp = HasProp<typeof UserComponent, 'name'>; // true
type HasPhoneProp = HasProp<typeof UserComponent, 'phone'>; // false
type UserRequiredProps = RequiredProps<typeof UserComponent>; // { id: string; name: string; age: number }

// 6. 高级工厂模式
function createTypedComponentFactory<P extends BaseProps>() {
  return function createComponent<T extends P>(
    Component: ComponentClass<T>
  ): ComponentClass<T> {
    return class extends React.Component<T> {
      static displayName = \`Wrapped\${Component.displayName || Component.name}\`;
      
      render() {
        return React.createElement(Component, this.props);
      }
    };
  };
}

// 使用工厂创建类型安全的组件
const formComponentFactory = createTypedComponentFactory<FormFieldProps>();
const WrappedFormComponent = formComponentFactory(SomeFormComponent);`,
    tags: ['泛型约束', '类型推断'],
    relatedQuestions: ['条件类型如何使用？', '如何调试复杂类型定义？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: '组件工厂和动态组件创建时，如何保证类型安全？为什么React.createElement会丢失类型信息？',
    answer: `动态组件创建是ComponentClass的高级应用，类型安全是关键挑战：

**类型丢失的原因：**
1. **运行时组件选择**：TypeScript无法在编译时确定具体组件类型
2. **React.createElement的泛型限制**：无法完美推断复杂组件类型
3. **Props类型联合**：多个组件的props类型合并导致信息丢失
4. **动态导入**：异步加载的组件类型推断困难

**解决方案：**
- 使用类型守卫确保运行时类型安全
- 建立组件注册表配合类型映射
- 利用重载和条件类型改善类型推断
- 实现类型安全的组件工厂模式

**最佳实践：**
- 预定义组件类型映射表
- 使用严格的类型约束
- 实现运行时类型验证
- 提供类型安全的工厂函数`,
    code: `// 类型安全的动态组件创建解决方案

// 1. 定义组件注册表和类型映射
interface ButtonProps {
  variant: 'primary' | 'secondary';
  onClick: () => void;
  children: ReactNode;
}

interface InputProps {
  type: 'text' | 'email' | 'password';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
}

// 组件映射表
const COMPONENT_MAP = {
  'button': ButtonComponent,
  'input': InputComponent,
  'modal': ModalComponent
} as const;

// 类型映射表
interface ComponentPropsMap {
  'button': ButtonProps;
  'input': InputProps;
  'modal': ModalProps;
}

// 2. 类型安全的组件工厂
class TypeSafeComponentFactory {
  // 重载确保类型安全
  static create<K extends keyof ComponentPropsMap>(
    type: K,
    props: ComponentPropsMap[K]
  ): ReactElement<ComponentPropsMap[K]> | null {
    const Component = COMPONENT_MAP[type];
    
    if (!Component) {
      console.error(\`Unknown component type: \${type}\`);
      return null;
    }

    // 类型断言确保编译时安全
    return React.createElement(
      Component as ComponentClass<ComponentPropsMap[K]>,
      props
    );
  }

  // 批量创建组件
  static createMultiple<K extends keyof ComponentPropsMap>(
    components: Array<{
      type: K;
      props: ComponentPropsMap[K];
      key?: string;
    }>
  ): ReactElement[] {
    return components
      .map((config, index) => {
        const element = this.create(config.type, config.props);
        return element ? React.cloneElement(element, { key: config.key || index }) : null;
      })
      .filter(Boolean) as ReactElement[];
  }
}

// 3. 高级类型守卫和验证
function isValidComponentType<K extends keyof ComponentPropsMap>(
  type: string
): type is K {
  return type in COMPONENT_MAP;
}

function validateComponentProps<K extends keyof ComponentPropsMap>(
  type: K,
  props: any
): props is ComponentPropsMap[K] {
  // 运行时验证逻辑
  switch (type) {
    case 'button':
      return typeof props.variant === 'string' && 
             ['primary', 'secondary'].includes(props.variant) &&
             typeof props.onClick === 'function';
    case 'input':
      return typeof props.type === 'string' &&
             ['text', 'email', 'password'].includes(props.type) &&
             typeof props.value === 'string' &&
             typeof props.onChange === 'function';
    case 'modal':
      return typeof props.isOpen === 'boolean' &&
             typeof props.onClose === 'function' &&
             typeof props.title === 'string';
    default:
      return false;
  }
}

// 4. 类型安全的动态组件渲染器
function DynamicComponentRenderer<K extends keyof ComponentPropsMap>({
  componentType,
  componentProps,
  fallback = null
}: {
  componentType: string;
  componentProps: any;
  fallback?: ReactNode;
}) {
  // 类型守卫确保类型安全
  if (!isValidComponentType(componentType)) {
    console.error(\`Invalid component type: \${componentType}\`);
    return fallback;
  }

  // 运行时验证props
  if (!validateComponentProps(componentType, componentProps)) {
    console.error(\`Invalid props for component \${componentType}\`);
    return fallback;
  }

  // 现在TypeScript知道类型是安全的
  return TypeSafeComponentFactory.create(componentType, componentProps);
}

// 5. 使用示例和类型推断验证
function ExampleUsage() {
  // ✅ 类型安全：TypeScript会检查props类型
  const button = TypeSafeComponentFactory.create('button', {
    variant: 'primary',
    onClick: () => console.log('clicked'),
    children: 'Click me'
  });

  const input = TypeSafeComponentFactory.create('input', {
    type: 'email',
    value: '<EMAIL>',
    onChange: (value) => console.log(value),
    placeholder: 'Enter email'
  });

  // ❌ TypeScript错误：props类型不匹配
  // const invalidButton = TypeSafeComponentFactory.create('button', {
  //   variant: 'invalid', // 错误：不是有效的variant
  //   onClick: 'not a function' // 错误：不是函数
  // });

  // 批量创建
  const components = TypeSafeComponentFactory.createMultiple([
    {
      type: 'button',
      props: {
        variant: 'primary',
        onClick: () => {},
        children: 'Button 1'
      }
    },
    {
      type: 'input',
      props: {
        type: 'text',
        value: '',
        onChange: () => {}
      }
    }
  ]);

  return (
    <div>
      {button}
      {input}
      {components}
    </div>
  );
}

// 6. 高级：支持异步组件加载
interface AsyncComponentLoader {
  [K in keyof ComponentPropsMap]: () => Promise<ComponentClass<ComponentPropsMap[K]>>;
}

const asyncComponentLoader: AsyncComponentLoader = {
  'button': () => import('./ButtonComponent').then(m => m.default),
  'input': () => import('./InputComponent').then(m => m.default),
  'modal': () => import('./ModalComponent').then(m => m.default)
};

async function createAsyncComponent<K extends keyof ComponentPropsMap>(
  type: K,
  props: ComponentPropsMap[K]
): Promise<ReactElement<ComponentPropsMap[K]> | null> {
  try {
    const Component = await asyncComponentLoader[type]();
    return React.createElement(Component, props);
  } catch (error) {
    console.error(\`Failed to load component \${type}:\`, error);
    return null;
  }
}`,
    tags: ['组件工厂', '动态创建'],
    relatedQuestions: ['如何实现类型安全的HOC？', '异步组件加载如何处理类型？']
  }
];

// ComponentClass常见问题内容已完成
export default commonQuestions;