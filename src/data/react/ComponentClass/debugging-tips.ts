import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'ComponentClass开发中常见的问题及其解决方案。这些问题通常涉及TypeScript类型系统、组件生命周期、以及React与TypeScript的集成问题。',
        sections: [
          {
            title: 'TypeScript类型错误诊断',
            description: '最常见的ComponentClass相关错误，包括类型不匹配、泛型推断失败等问题',
            items: [
              {
                title: '"Type is not assignable to ComponentClass" 错误',
                description: '当组件类型与ComponentClass声明不匹配时出现的编译错误',
                solution: '检查组件的Props和State类型定义，确保与ComponentClass泛型参数完全匹配',
                prevention: '使用严格的TypeScript配置，定义明确的接口类型，避免使用any类型',
                code: `// ❌ 错误示例
interface Props { name: string; }
class MyComponent extends React.Component<{ title: string }> {} // Props不匹配
const comp: ComponentClass<Props> = MyComponent; // 类型错误

// ✅ 正确解决
interface Props { name: string; }
class MyComponent extends React.Component<Props> {
  render() {
    return <div>{this.props.name}</div>;
  }
}
const comp: ComponentClass<Props> = MyComponent; // 类型匹配

// 🔧 调试技巧
// 1. 使用类型断言临时解决
const comp = MyComponent as ComponentClass<Props>;

// 2. 提取实际Props类型进行对比
type ActualProps = React.ComponentProps<typeof MyComponent>;
type ExpectedProps = Props;
// 比较两个类型的差异`
              },
              {
                title: '泛型推断失败问题',
                description: 'TypeScript无法正确推断ComponentClass的泛型参数，导致类型检查失效',
                solution: '显式提供泛型参数，使用类型约束，或简化复杂的类型定义',
                prevention: '避免过度复杂的嵌套泛型，使用extends约束限制泛型范围',
                code: `// ❌ 推断失败的复杂类型
type ComplexComponent<T> = T extends Record<string, any> 
  ? ComponentClass<T & { nested: { deep: T } }>
  : never;

// ✅ 简化后的类型定义
interface BaseProps {
  id: string;
  className?: string;
}

type SimpleComponent<P extends BaseProps> = ComponentClass<P>;

// 🔧 调试步骤
// 1. 检查泛型约束
function debugGeneric<P extends BaseProps>(
  Component: ComponentClass<P>
): P {
  // 使用函数参数推导检查类型
  return {} as P;
}

// 2. 分步验证类型
type Step1 = ComponentClass<BaseProps>;
type Step2<P> = P extends BaseProps ? ComponentClass<P> : never;

// 3. 使用工具类型诊断
type Diagnosis<T> = T extends ComponentClass<infer P> 
  ? { props: P; valid: true }
  : { props: never; valid: false };`
              },
              {
                title: '组件实例化运行时错误',
                description: '组件在运行时创建失败，或者props传递错误导致的运行时异常',
                solution: '添加运行时类型检查，使用PropTypes验证，实现错误边界',
                prevention: '使用TypeScript严格模式，添加运行时验证，实现完善的错误处理',
                code: `// 🔧 运行时错误调试工具
class ComponentDebugger {
  // 验证组件类型
  static validateComponentClass<P>(
    Component: any,
    expectedProps: P
  ): Component is ComponentClass<P> {
    if (typeof Component !== 'function') {
      console.error('Component is not a function:', Component);
      return false;
    }

    if (!Component.prototype || !Component.prototype.render) {
      console.error('Component is not a class component:', Component);
      return false;
    }

    return true;
  }

  // 验证Props类型
  static validateProps<P>(props: any, schema: any): props is P {
    try {
      // 简单的运行时验证
      for (const key in schema) {
        if (!(key in props)) {
          console.error(\`Missing required prop: \${key}\`);
          return false;
        }
        
        const expectedType = typeof schema[key];
        const actualType = typeof props[key];
        
        if (expectedType !== actualType) {
          console.error(\`Prop type mismatch: \${key} expected \${expectedType}, got \${actualType}\`);
          return false;
        }
      }
      return true;
    } catch (error) {
      console.error('Props validation error:', error);
      return false;
    }
  }

  // 安全的组件创建
  static safeCreateElement<P>(
    Component: ComponentClass<P>,
    props: P
  ): ReactElement<P> | null {
    try {
      if (!this.validateComponentClass(Component, props)) {
        return null;
      }

      return React.createElement(Component, props);
    } catch (error) {
      console.error('Component creation failed:', error);
      return null;
    }
  }
}

// 错误边界组件
class ComponentClassErrorBoundary extends React.Component<
  { children: ReactNode },
  { hasError: boolean; error?: Error }
> {
  state = { hasError: false };

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ComponentClass Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div>
          <h2>Component Error</h2>
          <details>
            <summary>Error Details</summary>
            <pre>{this.state.error?.stack}</pre>
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}`
              }
            ]
          },
          {
            title: '性能调试和分析',
            description: 'ComponentClass相关的性能问题诊断和优化建议',
            items: [
              {
                title: '组件渲染性能分析',
                description: '识别和解决ComponentClass组件的渲染性能问题',
                solution: '使用React Profiler，分析组件渲染时间，识别性能瓶颈',
                prevention: '实现shouldComponentUpdate，使用React.memo，优化组件结构',
                code: `// 🔧 性能分析工具
class PerformanceMonitor {
  private static renderTimes = new Map<string, number[]>();

  static startMeasure(componentName: string): void {
    const key = \`\${componentName}-start\`;
    performance.mark(key);
  }

  static endMeasure(componentName: string): number {
    const startKey = \`\${componentName}-start\`;
    const endKey = \`\${componentName}-end\`;
    
    performance.mark(endKey);
    performance.measure(componentName, startKey, endKey);
    
    const measure = performance.getEntriesByName(componentName)[0];
    const duration = measure.duration;
    
    // 记录渲染时间
    if (!this.renderTimes.has(componentName)) {
      this.renderTimes.set(componentName, []);
    }
    this.renderTimes.get(componentName)!.push(duration);
    
    return duration;
  }

  static getAverageRenderTime(componentName: string): number {
    const times = this.renderTimes.get(componentName) || [];
    return times.reduce((a, b) => a + b, 0) / times.length;
  }
}

// 性能监控HOC
function withPerformanceMonitoring<P>(
  WrappedComponent: ComponentClass<P>
): ComponentClass<P> {
  return class extends React.Component<P> {
    componentDidMount() {
      PerformanceMonitor.startMeasure(WrappedComponent.name);
    }

    componentDidUpdate() {
      const duration = PerformanceMonitor.endMeasure(WrappedComponent.name);
      if (duration > 16) { // 超过一帧时间
        console.warn(\`Slow render detected: \${WrappedComponent.name} took \${duration}ms\`);
      }
    }

    render() {
      return React.createElement(WrappedComponent, this.props);
    }
  };
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '高效调试ComponentClass的开发工具配置和使用技巧，包括IDE配置、浏览器工具、以及专用的调试脚本。',
        sections: [
          {
            title: 'TypeScript IDE配置和调试',
            description: '配置IDE以获得最佳的ComponentClass开发和调试体验',
            items: [
              {
                title: 'VS Code TypeScript调试配置',
                description: '配置VS Code以获得完整的TypeScript类型检查和调试支持',
                solution: '安装相关扩展，配置调试器，启用严格类型检查',
                prevention: '保持扩展更新，使用工作区配置，定期清理缓存',
                code: `// .vscode/settings.json
{
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  
  // 严格类型检查
  "typescript.reportStyleChecksAsWarnings": false,
  "typescript.check.npmIsInstalled": true,
  
  // React特定配置
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  
  // 调试配置
  "debug.allowBreakpointsEverywhere": true,
  "debug.inlineValues": "auto"
}

// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug TypeScript",
      "program": "\${workspaceFolder}/src/index.ts",
      "outFiles": ["\${workspaceFolder}/dist/**/*.js"],
      "sourceMaps": true,
      "resolveSourceMapLocations": [
        "\${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    }
  ]
}

// tsconfig.json 调试优化配置
{
  "compilerOptions": {
    "sourceMap": true,
    "inlineSources": true,
    "declaration": true,
    "declarationMap": true,
    
    // 严格类型检查
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  }
}`
              },
              {
                title: 'React DevTools组件调试',
                description: '使用React DevTools深入调试ComponentClass组件的状态和props',
                solution: '安装DevTools扩展，配置组件显示名称，使用Profiler分析性能',
                prevention: '为组件设置displayName，避免匿名组件，使用React DevTools Profiler',
                code: `// React DevTools调试增强
class ComponentClassDebugger {
  // 为组件添加调试信息
  static enhanceForDebugging<P, S>(
    ComponentClass: ComponentClass<P, S>,
    debugName?: string
  ): ComponentClass<P, S> {
    // 设置displayName
    if (debugName) {
      ComponentClass.displayName = debugName;
    }

    // 添加调试方法
    const originalRender = ComponentClass.prototype.render;
    ComponentClass.prototype.render = function(this: React.Component<P, S>) {
      // 在控制台输出渲染信息
      if (process.env.NODE_ENV === 'development') {
        console.log(\`Rendering \${ComponentClass.displayName || ComponentClass.name}\`, {
          props: this.props,
          state: this.state
        });
      }

      return originalRender.call(this);
    };

    return ComponentClass;
  }

  // DevTools自定义钩子
  static addDevToolsHook<P, S>(
    ComponentClass: ComponentClass<P, S>
  ): void {
    if (typeof window !== 'undefined' && window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      const hook = window.__REACT_DEVTOOLS_GLOBAL_HOOK__;
      
      // 添加自定义调试信息
      hook.onCommitFiberRoot = (id, root, priorityLevel) => {
        console.log('Component tree updated:', {
          id,
          root,
          priorityLevel,
          componentName: ComponentClass.name
        });
      };
    }
  }
}

// 使用Profiler包装组件
function withDevToolsProfiler<P>(
  ComponentClass: ComponentClass<P>,
  id: string
): ComponentClass<P> {
  return class extends React.Component<P> {
    onRenderCallback = (
      id: string,
      phase: 'mount' | 'update',
      actualDuration: number,
      baseDuration: number,
      startTime: number,
      commitTime: number
    ) => {
      console.log('Profiler Data:', {
        id,
        phase,
        actualDuration,
        baseDuration,
        startTime,
        commitTime
      });
    };

    render() {
      return React.createElement(
        React.Profiler,
        {
          id,
          onRender: this.onRenderCallback
        },
        React.createElement(ComponentClass, this.props)
      );
    }
  };
}`
              },
              {
                title: '浏览器调试工具集成',
                description: '集成浏览器原生调试工具进行ComponentClass问题排查',
                solution: '使用Console API，配置断点，利用Performance面板分析性能',
                prevention: '建立调试流程，使用条件断点，定期性能分析',
                code: `// 浏览器调试工具集成
class BrowserDebugIntegration {
  // 高级console调试
  static logComponentState<P, S>(
    component: React.Component<P, S>,
    context?: string
  ): void {
    const componentName = component.constructor.name;
    
    console.group(\`🔍 \${componentName} Debug Info \${context ? \`(\${context})\` : ''}\`);
    console.log('Props:', component.props);
    console.log('State:', component.state);
    console.log('Component Instance:', component);
    console.groupEnd();
  }

  // 性能标记
  static markPerformance(label: string, data?: any): void {
    if (performance.mark) {
      performance.mark(label);
    }
    
    if (data) {
      console.log(\`📊 Performance Mark: \${label}\`, data);
    }
  }

  // 条件调试断点
  static conditionalBreakpoint(
    condition: boolean,
    message?: string
  ): void {
    if (condition) {
      console.log(\`🚨 Breakpoint triggered: \${message || 'Condition met'}\`);
      debugger; // 触发断点
    }
  }

  // 内存使用监控
  static logMemoryUsage(label?: string): void {
    if (performance.memory) {
      const memory = performance.memory;
      console.log(\`💾 Memory Usage \${label ? \`(\${label})\` : ''}\`, {
        used: \`\${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB\`,
        total: \`\${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB\`,
        limit: \`\${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB\`
      });
    }
  }
}

// 调试专用组件包装器
function withBrowserDebug<P>(
  ComponentClass: ComponentClass<P>,
  options: {
    logProps?: boolean;
    logState?: boolean;
    performanceTracking?: boolean;
    memoryTracking?: boolean;
  } = {}
): ComponentClass<P> {
  return class extends React.Component<P> {
    componentDidMount() {
      if (options.performanceTracking) {
        BrowserDebugIntegration.markPerformance(\`\${ComponentClass.name}-mount\`);
      }
      
      if (options.memoryTracking) {
        BrowserDebugIntegration.logMemoryUsage(\`\${ComponentClass.name}-mount\`);
      }

      if (options.logProps || options.logState) {
        BrowserDebugIntegration.logComponentState(this, 'mount');
      }
    }

    componentDidUpdate() {
      if (options.performanceTracking) {
        BrowserDebugIntegration.markPerformance(\`\${ComponentClass.name}-update\`);
      }

      if (options.logProps || options.logState) {
        BrowserDebugIntegration.logComponentState(this, 'update');
      }
    }

    render() {
      return React.createElement(ComponentClass, this.props);
    }
  };
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

// ComponentClass调试技巧内容已完成
export default debuggingTips;