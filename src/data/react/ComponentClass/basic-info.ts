import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: "ComponentClass是React中用于表示类组件类型的TypeScript接口，定义了类组件的类型约束和结构规范",
  
  introduction: `ComponentClass是React TypeScript类型定义中的核心接口，用于表示类组件的类型。它定义了继承自React.Component的类组件必须遵循的类型约束，包括props、state、生命周期方法等。这个类型接口为TypeScript开发者提供了严格的类型检查，确保类组件的正确实现和使用。`,

  syntax: `// 基本类型定义
interface ComponentClass<P = {}, S = ComponentState> {
  new (props: P, context?: any): Component<P, S>;
  propTypes?: WeakValidationMap<P>;
  contextTypes?: ValidationMap<any>;
  childContextTypes?: ValidationMap<any>;
  defaultProps?: Partial<P>;
  displayName?: string;
}

// 使用示例
type MyComponentClass = ComponentClass<MyProps, MyState>;

// 组件类型约束
const MyComponent: ComponentClass<Props> = class extends React.Component<Props> {
  render() {
    return <div>{this.props.children}</div>;
  }
};`,

  quickExample: `// 定义Props和State类型
interface UserProps {
  name: string;
  age: number;
}

interface UserState {
  isActive: boolean;
}

// 使用ComponentClass类型约束
const UserComponent: ComponentClass<UserProps, UserState> = class extends React.Component<UserProps, UserState> {
  state: UserState = {
    isActive: false
  };

  render() {
    return (
      <div>
        <h1>{this.props.name}</h1>
        <p>年龄: {this.props.age}</p>
        <p>状态: {this.state.isActive ? '活跃' : '非活跃'}</p>
      </div>
    );
  }
};`,

  scenarioDiagram: `graph TD
    A[ComponentClass使用场景] --> B[类型约束]
    A --> C[组件工厂]
    A --> D[高阶组件]

    B --> B1[Props类型检查]
    B --> B2[State类型验证]
    B --> B3[方法签名约束]

    C --> C1[动态组件创建]
    C --> C2[组件注册系统]
    C --> C3[插件化架构]

    D --> D1[组件包装器]
    D --> D2[属性注入]
    D --> D3[行为增强]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "P (Props)",
      type: "object",
      required: false,
      description: "组件的props类型，默认为空对象类型",
      example: "interface MyProps { title: string; count: number; }"
    },
    {
      name: "S (State)",
      type: "ComponentState",
      required: false,
      description: "组件的state类型，默认为ComponentState",
      example: "interface MyState { isLoading: boolean; data: any[]; }"
    }
  ],
  
  returnValue: {
    type: "ComponentClass<P, S>",
    description: "返回符合ComponentClass接口的类组件类型定义",
    example: "ComponentClass<UserProps, UserState>"
  },
  
  keyFeatures: [
    {
      title: "严格类型检查",
      description: "为类组件提供完整的TypeScript类型约束和验证",
      benefit: "编译时发现类型错误，提高代码质量和开发效率"
    },
    {
      title: "组件构造函数约束",
      description: "定义组件构造函数的参数和返回值类型",
      benefit: "确保组件实例化过程的类型安全性"
    },
    {
      title: "生命周期方法支持",
      description: "包含所有React类组件生命周期方法的类型定义",
      benefit: "为生命周期方法提供准确的类型提示和检查"
    },
    {
      title: "静态属性类型定义",
      description: "支持propTypes、defaultProps等静态属性的类型约束",
      benefit: "增强组件API的类型安全性和文档化"
    }
  ],
  
  limitations: [
    "仅适用于TypeScript环境，JavaScript项目无法使用",
    "类组件模式在现代React开发中使用频率降低",
    "相比函数组件，类型定义更加复杂和冗长",
    "不支持React Hooks的类型约束",
    "对于简单组件来说类型定义可能过于繁重"
  ],
  
  bestPractices: [
    "为Props和State定义明确的TypeScript接口",
    "使用泛型参数提供灵活的类型约束",
    "结合defaultProps提供默认值的类型安全",
    "在高阶组件中使用ComponentClass进行类型传递",
    "配合React.Component<P, S>确保类型一致性"
  ],
  
  warnings: [
    "函数组件和Hooks已成为React开发的主流模式",
    "新项目建议优先考虑函数组件的类型定义",
    "类组件的类型定义相对复杂，增加学习成本"
  ]
};

// ComponentClass基本信息内容已完成
export default basicInfo;