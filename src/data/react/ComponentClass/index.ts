import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ReactComponentClassData: ApiItem = {
  id: 'ComponentClass',
  title: 'ComponentClass',
  description: 'ComponentClass是React中用于表示类组件类型的TypeScript接口，定义了类组件的类型约束和结构规范',
  category: 'React Types',
  difficulty: 'easy',
  
  syntax: `// 基本类型定义
interface ComponentClass<P = {}, S = ComponentState> {
  new (props: P, context?: any): Component<P, S>;
  propTypes?: WeakValidationMap<P>;
  contextTypes?: ValidationMap<any>;
  childContextTypes?: ValidationMap<any>;
  defaultProps?: Partial<P>;
  displayName?: string;
}

// 使用示例
type MyComponentClass = ComponentClass<MyProps, MyState>;`,
  example: `// 定义Props和State类型
interface UserProps {
  name: string;
  age: number;
}

interface UserState {
  isActive: boolean;
}

// 使用ComponentClass类型约束
const UserComponent: ComponentClass<UserProps, UserState> = class extends React.Component<UserProps, UserState> {
  state: UserState = {
    isActive: false
  };

  render() {
    return (
      <div>
        <h1>{this.props.name}</h1>
        <p>年龄: {this.props.age}</p>
        <p>状态: {this.state.isActive ? '活跃' : '非活跃'}</p>
      </div>
    );
  }
};`,
  notes: '仅适用于TypeScript环境，JavaScript项目无法使用',
  
  version: 'React 16.0.0+',
  tags: ["React","TypeScript","ComponentClass","Types"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ReactComponentClassData;