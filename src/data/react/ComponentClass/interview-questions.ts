import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'ComponentClass是什么？它在TypeScript中的作用是什么？请解释它与普通类组件的关系。',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'ComponentClass是TypeScript中表示类组件类型的接口，用于类型约束和检查，确保类组件符合React的规范。',
      detailed: `ComponentClass是React TypeScript类型定义中的核心接口，用于表示类组件的类型约束：

**主要作用：**
1. **类型约束**：定义类组件必须遵循的接口规范
2. **构造函数约束**：确保组件构造函数的参数和返回值类型正确
3. **静态属性支持**：包含propTypes、defaultProps等静态属性的类型定义
4. **生命周期方法**：提供所有生命周期方法的类型签名

**接口定义：**
- 接受两个泛型参数：P (Props类型) 和 S (State类型)
- 包含构造函数签名：new (props: P, context?: any): Component<P, S>
- 支持静态属性：propTypes、contextTypes、defaultProps等

**与普通类组件的关系：**
- 普通类组件是具体实现，ComponentClass是类型约束
- 类组件通过extends React.Component<P, S>间接实现ComponentClass接口
- TypeScript编译器使用ComponentClass进行类型检查和推断`,
      code: `// ComponentClass的基本使用
interface MyProps {
  title: string;
  count: number;
}

interface MyState {
  isActive: boolean;
}

// 使用ComponentClass类型约束
const MyComponent: ComponentClass<MyProps, MyState> = class extends React.Component<MyProps, MyState> {
  state: MyState = {
    isActive: false
  };

  static defaultProps = {
    count: 0
  };

  render() {
    return (
      <div>
        <h1>{this.props.title}</h1>
        <p>计数: {this.props.count}</p>
        <p>状态: {this.state.isActive ? '激活' : '未激活'}</p>
      </div>
    );
  }
};

// 类型检查示例
function createComponent(): ComponentClass<MyProps, MyState> {
  return MyComponent; // TypeScript会验证类型匹配
}

// 组件实例化
const element = React.createElement(MyComponent, {
  title: '示例标题',
  count: 42
});`
    },
    tags: ['TypeScript', '类型约束']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '在什么场景下会使用ComponentClass？如何结合泛型实现灵活的组件类型约束？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实际应用',
    answer: {
      brief: 'ComponentClass主要用于组件工厂、高阶组件、动态组件创建等场景，通过泛型提供灵活的类型约束和复用能力。',
      detailed: `ComponentClass在实际开发中的应用场景：

**1. 组件工厂模式**
- 动态创建不同类型的组件
- 根据配置生成组件实例
- 插件化系统中的组件注册

**2. 高阶组件(HOC)开发**
- 为现有组件添加功能
- 组件属性注入和增强
- 跨组件逻辑复用

**3. 组件库开发**
- 统一组件接口规范
- 类型安全的组件继承
- 企业级组件标准化

**4. 运行时组件选择**
- 条件渲染不同组件
- A/B测试组件切换
- 主题化组件系统

**泛型的灵活应用：**
- 使用约束泛型限制props类型
- 条件类型实现高级类型推断
- 映射类型生成组件变体`,
      code: `// 高级泛型应用场景
// 1. 约束泛型 - 限制Props必须包含特定字段
interface BaseProps {
  id: string;
  className?: string;
}

type ConstrainedComponentClass<P extends BaseProps> = ComponentClass<P>;

// 2. 组件工厂与泛型
class ComponentFactory {
  static create<P extends BaseProps, S = {}>(
    ComponentClass: ComponentClass<P, S>,
    props: P
  ): ReactElement<P> {
    return React.createElement(ComponentClass, props);
  }
}

// 3. 高阶组件与泛型
function withLoading<P extends BaseProps>(
  WrappedComponent: ComponentClass<P>
): ComponentClass<P & { loading?: boolean }> {
  return class extends React.Component<P & { loading?: boolean }> {
    render() {
      const { loading, ...props } = this.props;
      
      if (loading) {
        return <div>加载中...</div>;
      }
      
      return React.createElement(WrappedComponent, props as P);
    }
  };
}

// 4. 条件类型应用
type ComponentProps<T> = T extends ComponentClass<infer P> ? P : never;

// 使用示例
interface UserProps extends BaseProps {
  name: string;
  email: string;
}

class UserComponent extends React.Component<UserProps> {
  render() {
    return <div>{this.props.name}</div>;
  }
}

// 工厂创建
const userElement = ComponentFactory.create(UserComponent, {
  id: '1',
  name: 'Alice',
  email: '<EMAIL>'
});

// HOC增强
const LoadingUserComponent = withLoading(UserComponent);

// 类型推断
type ExtractedProps = ComponentProps<typeof UserComponent>; // UserProps`
    },
    tags: ['组件工厂', '高阶组件']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 3,
    question: '如何设计一个类型安全的插件系统，使第三方开发者能够基于ComponentClass规范开发组件插件？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    answer: {
      brief: '设计插件系统需要定义统一的插件接口、元数据标准、权限验证机制，并通过ComponentClass确保类型安全和API一致性。',
      detailed: `设计类型安全的插件系统需要考虑以下几个关键方面：

**1. 插件接口标准化**
- 定义统一的插件基类和接口规范
- 建立插件元数据和配置schema
- 确保API向后兼容性

**2. 类型安全机制**
- 使用泛型约束确保插件类型正确
- 编译时验证插件接口实现
- 运行时类型检查和错误处理

**3. 权限和安全控制**
- 插件权限声明和验证
- 沙箱环境隔离
- API调用权限控制

**4. 插件生命周期管理**
- 插件注册、初始化、销毁流程
- 依赖管理和版本兼容
- 错误隔离和恢复机制

**5. 开发者体验**
- 类型提示和智能感知
- 开发工具和调试支持
- 文档和示例完整性`,
      code: `// 完整的插件系统设计
// 1. 插件元数据定义
interface PluginMetadata {
  name: string;
  version: string;
  author: string;
  description: string;
  keywords: string[];
  dependencies?: Record<string, string>;
  permissions: Permission[];
  configSchema?: JSONSchema;
}

interface Permission {
  name: string;
  description: string;
  required: boolean;
}

// 2. 平台API接口
interface PlatformAPI {
  // 核心服务
  logger: {
    info: (message: string) => void;
    warn: (message: string) => void;
    error: (message: string, error?: Error) => void;
  };
  
  // 数据服务
  dataService: {
    get: <T>(url: string) => Promise<T>;
    post: <T>(url: string, data: any) => Promise<T>;
  };
  
  // 事件系统
  eventBus: {
    emit: (event: string, data: any) => void;
    on: (event: string, handler: (data: any) => void) => void;
    off: (event: string, handler: (data: any) => void) => void;
  };
  
  // UI服务
  ui: {
    notify: (message: string, type: 'success' | 'error' | 'info') => void;
    modal: {
      show: (content: ReactNode) => void;
      hide: () => void;
    };
  };
}

// 3. 插件基类约束
interface PluginComponentProps {
  api: PlatformAPI;
  config: Record<string, any>;
  context: PluginContext;
}

interface PluginComponentState {
  initialized: boolean;
  error: string | null;
  data: any;
}

interface PluginContext {
  pluginId: string;
  permissions: string[];
  theme: ThemeConfig;
  locale: string;
}

// 4. 插件组件类型约束
type PluginComponentClass<P = {}, S = {}> = ComponentClass<
  P & PluginComponentProps,
  S & PluginComponentState
> & {
  metadata: PluginMetadata;
  validatePermissions: (permissions: string[]) => boolean;
  validateConfig: (config: any) => { valid: boolean; errors: string[] };
  onInstall?: () => Promise<void>;
  onUninstall?: () => Promise<void>;
  onActivate?: () => Promise<void>;
  onDeactivate?: () => Promise<void>;
};

// 5. 插件管理器
class PluginManager {
  private plugins = new Map<string, PluginComponentClass>();
  private activePlugins = new Set<string>();
  
  // 注册插件
  async registerPlugin<P, S>(
    PluginClass: PluginComponentClass<P, S>
  ): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    try {
      // 验证元数据
      if (!this.validateMetadata(PluginClass.metadata)) {
        errors.push('插件元数据不完整或格式错误');
      }
      
      // 验证接口实现
      if (!this.validateInterface(PluginClass)) {
        errors.push('插件未正确实现必需的接口方法');
      }
      
      // 检查依赖
      const depResult = await this.checkDependencies(PluginClass.metadata.dependencies);
      if (!depResult.satisfied) {
        errors.push('插件依赖不满足：' + depResult.missing.join(', '));
      }
      
      if (errors.length > 0) {
        return { success: false, errors };
      }
      
      // 执行安装钩子
      if (PluginClass.onInstall) {
        await PluginClass.onInstall();
      }
      
      // 注册插件
      this.plugins.set(PluginClass.metadata.name, PluginClass);
      
      return { success: true, errors: [] };
      
    } catch (error) {
      errors.push('插件注册过程中发生错误：' + error.message);
      return { success: false, errors };
    }
  }
  
  // 激活插件
  async activatePlugin(name: string): Promise<boolean> {
    const PluginClass = this.plugins.get(name);
    if (!PluginClass) return false;
    
    try {
      if (PluginClass.onActivate) {
        await PluginClass.onActivate();
      }
      
      this.activePlugins.add(name);
      return true;
    } catch (error) {
      console.error('插件激活失败:', error);
      return false;
    }
  }
  
  // 创建插件实例
  createPluginInstance(
    name: string,
    props: PluginComponentProps
  ): ReactElement | null {
    const PluginClass = this.plugins.get(name);
    
    if (!PluginClass || !this.activePlugins.has(name)) {
      return null;
    }
    
    // 验证权限
    if (!PluginClass.validatePermissions(props.context.permissions)) {
      console.error('插件权限不足:', name);
      return null;
    }
    
    // 验证配置
    const configResult = PluginClass.validateConfig(props.config);
    if (!configResult.valid) {
      console.error('插件配置错误:', configResult.errors);
      return null;
    }
    
    return React.createElement(PluginClass, props);
  }
  
  private validateMetadata(metadata: PluginMetadata): boolean {
    return !!(
      metadata.name &&
      metadata.version &&
      metadata.author &&
      metadata.description &&
      Array.isArray(metadata.permissions)
    );
  }
  
  private validateInterface(PluginClass: any): boolean {
    return !!(
      typeof PluginClass.validatePermissions === 'function' &&
      typeof PluginClass.validateConfig === 'function'
    );
  }
  
  private async checkDependencies(
    dependencies?: Record<string, string>
  ): Promise<{ satisfied: boolean; missing: string[] }> {
    // 依赖检查逻辑
    return { satisfied: true, missing: [] };
  }
}

// 6. 使用示例 - 第三方插件开发
class WeatherWidget extends React.Component<
  { location: string } & PluginComponentProps,
  { temperature: number } & PluginComponentState
> {
  static metadata: PluginMetadata = {
    name: 'weather-widget',
    version: '1.0.0',
    author: 'WeatherCorp',
    description: '天气预报小组件',
    keywords: ['weather', 'widget'],
    permissions: [
      { name: 'network:http', description: '网络访问权限', required: true },
      { name: 'location:read', description: '位置读取权限', required: true }
    ]
  };
  
  static validatePermissions(permissions: string[]): boolean {
    const required = ['network:http', 'location:read'];
    return required.every(perm => permissions.includes(perm));
  }
  
  static validateConfig(config: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!config.location || typeof config.location !== 'string') {
      errors.push('location配置项必须是非空字符串');
    }
    
    return { valid: errors.length === 0, errors };
  }
  
  state = {
    initialized: false,
    error: null,
    data: null,
    temperature: 0
  };
  
  async componentDidMount() {
    try {
      const weather = await this.props.api.dataService.get(
        'https://api.weather.com/v1/current?location=' + this.props.location
      );
      
      this.setState({
        initialized: true,
        temperature: weather.temperature
      });
    } catch (error) {
      this.setState({
        error: '获取天气数据失败',
        initialized: false
      });
    }
  }
  
  render() {
    if (this.state.error) {
      return <div>错误: {this.state.error}</div>;
    }
    
    if (!this.state.initialized) {
      return <div>加载中...</div>;
    }
    
    return (
      <div className="weather-widget">
        <h3>{this.props.location}天气</h3>
        <p>温度: {this.state.temperature}°C</p>
      </div>
    );
  }
}

// 插件注册和使用
const pluginManager = new PluginManager();
pluginManager.registerPlugin(WeatherWidget);`
    },
    tags: ['插件系统', '架构设计']
  }
];

// ComponentClass面试问题内容已完成
export default interviewQuestions;