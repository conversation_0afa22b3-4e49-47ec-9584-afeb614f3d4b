import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'scenario-1',
    title: '企业级组件库类型约束体系',
    description: '大型企业项目建立严格的组件类型约束体系，确保团队开发的组件质量和一致性',
    businessValue: '提升团队协作效率70%，减少类型相关bug85%，建立可维护的企业级组件库',
    scenario: '某金融科技公司需要建立统一的组件库，要求所有组件都必须符合严格的TypeScript类型约束，确保在跨团队协作中的类型安全',
    code: `// 企业级组件基类约束
interface BaseComponentProps {
  className?: string;
  dataTestId?: string;
  trackingProps?: Record<string, any>;
}

interface BaseComponentState {
  isLoading?: boolean;
  error?: string | null;
}

// 严格的组件类型约束
type EnterpriseComponentClass<P = {}, S = {}> = ComponentClass<
  P & BaseComponentProps, 
  S & BaseComponentState
>;

// 数据展示组件约束
interface DataTableProps extends BaseComponentProps {
  data: any[];
  columns: ColumnConfig[];
  onRowClick: (row: any) => void;
  pagination?: PaginationConfig;
}

interface DataTableState extends BaseComponentState {
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
  selectedRows: Set<string>;
}

// 类型约束的数据表格组件
class DataTable extends React.Component<DataTableProps, DataTableState> 
  implements ComponentClass<DataTableProps, DataTableState> {
  
  state: DataTableState = {
    isLoading: false,
    error: null,
    selectedRows: new Set()
  };

  // 企业级组件必须实现的标准方法
  getComponentMetrics() {
    return {
      componentName: 'DataTable',
      propsCount: Object.keys(this.props).length,
      stateKeys: Object.keys(this.state).length
    };
  }

  render() {
    const { data, columns, className, dataTestId } = this.props;
    
    return (
      <div 
        className={className}
        data-testid={dataTestId}
        data-component="DataTable"
      >
        {/* 企业级数据表格渲染逻辑 */}
        {this.state.isLoading ? (
          <LoadingSpinner />
        ) : (
          <table>
            <thead>
              {columns.map(col => (
                <th key={col.key}>{col.title}</th>
              ))}
            </thead>
            <tbody>
              {data.map((row, index) => (
                <tr key={index} onClick={() => this.props.onRowClick(row)}>
                  {columns.map(col => (
                    <td key={col.key}>{row[col.key]}</td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    );
  }
}

// 组件注册到企业库
const ComponentRegistry: Record<string, EnterpriseComponentClass> = {
  DataTable: DataTable as EnterpriseComponentClass<DataTableProps, DataTableState>
};`,
    explanation: 'ComponentClass为企业级组件库提供了强大的类型约束能力，确保所有组件都遵循统一的接口规范。通过泛型约束，可以在编译时发现类型不匹配问题，大幅提升代码质量。',
    benefits: [
      '编译时类型检查，提前发现85%的类型相关错误',
      '统一组件接口规范，提升团队协作效率70%',
      '强制组件标准化，降低维护成本60%'
    ],
    metrics: {
      performance: '类型检查阶段发现问题，避免运行时错误，代码质量提升85%',
      userExperience: '开发者IDE智能提示完善，开发效率提升70%，学习成本降低50%',
      technicalMetrics: '组件复用率提升60%，代码一致性达到95%，文档自动生成覆盖率90%'
    },
    difficulty: 'easy',
    tags: ['企业级', '类型约束', '组件库', '团队协作']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'scenario-2',
    title: '动态组件工厂与配置化渲染',
    description: '创建类型安全的动态组件工厂，根据配置动态渲染不同类型的业务组件',
    businessValue: '支持可视化配置界面，减少90%重复代码，实现完全配置化的页面渲染系统',
    scenario: '电商平台需要支持商家自定义页面布局，通过拖拽配置不同组件，系统需要根据配置动态渲染对应的React组件',
    code: `// 组件工厂配置接口
interface ComponentConfig {
  type: string;
  props: Record<string, any>;
  children?: ComponentConfig[];
}

// 动态组件工厂类型约束
type DynamicComponentClass<P = any> = ComponentClass<P> & {
  componentType: string;
  defaultProps?: Partial<P>;
  configSchema?: JSONSchema;
};

// 产品卡片组件
interface ProductCardProps {
  product: {
    id: string;
    name: string;
    price: number;
    image: string;
  };
  onAddToCart: (productId: string) => void;
  layout: 'horizontal' | 'vertical';
}

class ProductCard extends React.Component<ProductCardProps> {
  static componentType = 'ProductCard';
  static defaultProps = {
    layout: 'vertical' as const
  };

  render() {
    const { product, layout, onAddToCart } = this.props;
    
    return (
      <div className={layout === 'horizontal' ? 'flex' : 'block'}>
        <img src={product.image} alt={product.name} />
        <div>
          <h3>{product.name}</h3>
          <p>¥{product.price}</p>
          <button onClick={() => onAddToCart(product.id)}>
            加入购物车
          </button>
        </div>
      </div>
    );
  }
}

// 轮播图组件
interface CarouselProps {
  images: string[];
  autoPlay?: boolean;
  duration?: number;
}

class Carousel extends React.Component<CarouselProps> {
  static componentType = 'Carousel';
  static defaultProps = {
    autoPlay: true,
    duration: 3000
  };

  render() {
    // 轮播图实现逻辑
    return <div>轮播图组件</div>;
  }
}

// 动态组件工厂
class ComponentFactory {
  private static components: Map<string, DynamicComponentClass> = new Map();

  // 注册组件
  static register<P>(
    ComponentClass: ComponentClass<P> & { componentType: string }
  ): void {
    this.components.set(
      ComponentClass.componentType, 
      ComponentClass as DynamicComponentClass
    );
  }

  // 根据配置创建组件
  static create(config: ComponentConfig): ReactElement | null {
    const ComponentClass = this.components.get(config.type);
    
    if (!ComponentClass) {
      console.warn('未知组件类型: ' + config.type);
      return null;
    }

    // 递归渲染子组件
    const children = config.children?.map((childConfig, index) => 
      this.create(childConfig)
    );

    return React.createElement(ComponentClass, {
      key: config.type,
      ...ComponentClass.defaultProps,
      ...config.props
    }, children);
  }
}

// 注册组件到工厂
ComponentFactory.register(ProductCard);
ComponentFactory.register(Carousel);

// 页面渲染器
interface DynamicPageProps {
  pageConfig: ComponentConfig[];
}

class DynamicPage extends React.Component<DynamicPageProps> {
  render() {
    return (
      <div className="dynamic-page">
        {this.props.pageConfig.map((config, index) => 
          ComponentFactory.create(config)
        )}
      </div>
    );
  }
}

// 使用示例 - 配置化页面
const pageConfig: ComponentConfig[] = [
  {
    type: 'Carousel',
    props: {
      images: ['/banner1.jpg', '/banner2.jpg'],
      autoPlay: true
    }
  },
  {
    type: 'ProductCard',
    props: {
      product: {
        id: '001',
        name: 'iPhone 15',
        price: 5999,
        image: '/iphone15.jpg'
      },
      layout: 'horizontal'
    }
  }
];`,
    explanation: 'ComponentClass为动态组件工厂提供了类型安全的基础，确保动态创建的组件都符合预期的类型约束。工厂模式结合TypeScript类型系统，实现了完全配置化的组件渲染机制。',
    benefits: [
      '配置化渲染，减少90%的重复组件代码',
      '类型安全的动态组件创建，避免运行时类型错误',
      '支持可视化页面编辑器，提升业务配置效率80%',
      '组件工厂模式，实现组件的插拔式管理'
    ],
    metrics: {
      performance: '动态渲染性能优化，首屏加载时间减少40%，组件复用率提升85%',
      userExperience: '商家页面配置效率提升300%，模板数量增长200%，用户满意度提升25%',
      technicalMetrics: '代码复用率达到90%，组件维护成本降低70%，配置错误率降低95%'
    },
    difficulty: 'medium',
    tags: ['动态渲染', '组件工厂', '配置化', '可视化编辑']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'scenario-3',
    title: '微前端插件化架构与第三方组件集成',
    description: '构建插件化的微前端架构，通过ComponentClass约束确保第三方组件的类型安全和标准化接入',
    businessValue: '支持第三方开发者安全接入，建立组件生态，平台扩展性提升500%，开发效率提升200%',
    scenario: '大型企业应用平台需要支持第三方开发者贡献组件插件，要求所有第三方组件都必须遵循平台的类型约束和接口规范',
    code: `// 平台插件接口约束
interface PluginMetadata {
  name: string;
  version: string;
  author: string;
  description: string;
  permissions: string[];
}

interface PlatformAPI {
  notify: (message: string, type: 'info' | 'success' | 'error') => void;
  getUser: () => Promise<UserInfo>;
  navigate: (path: string) => void;
  storage: {
    get: (key: string) => any;
    set: (key: string, value: any) => void;
  };
}

// 平台组件基类约束
interface PlatformComponentProps {
  api: PlatformAPI;
  config?: Record<string, any>;
  theme?: ThemeConfig;
}

interface PlatformComponentState {
  initialized: boolean;
  error?: string;
}

// 第三方组件必须继承的基类约束
type PluginComponentClass<P = {}, S = {}> = ComponentClass<
  P & PlatformComponentProps,
  S & PlatformComponentState
> & {
  metadata: PluginMetadata;
  validateConfig?: (config: any) => boolean;
  getRequiredPermissions?: () => string[];
};

// 第三方数据分析组件示例
interface AnalyticsWidgetProps extends PlatformComponentProps {
  chartType: 'line' | 'bar' | 'pie';
  dataSource: string;
  refreshInterval?: number;
}

interface AnalyticsWidgetState extends PlatformComponentState {
  data: any[];
  loading: boolean;
}

class AnalyticsWidget extends React.Component<AnalyticsWidgetProps, AnalyticsWidgetState> {
  // 插件元数据
  static metadata: PluginMetadata = {
    name: 'analytics-widget',
    version: '1.2.0',
    author: 'Third-Party Developer',
    description: '数据分析图表组件',
    permissions: ['data:read', 'analytics:access']
  };

  // 配置验证
  static validateConfig(config: any): boolean {
    return config && 
           ['line', 'bar', 'pie'].includes(config.chartType) &&
           typeof config.dataSource === 'string';
  }

  // 所需权限
  static getRequiredPermissions(): string[] {
    return ['data:read', 'analytics:access'];
  }

  state: AnalyticsWidgetState = {
    initialized: false,
    data: [],
    loading: false
  };

  async componentDidMount() {
    try {
      // 验证权限
      const user = await this.props.api.getUser();
      if (!this.hasRequiredPermissions(user)) {
        throw new Error('权限不足');
      }

      // 初始化组件
      await this.loadData();
      this.setState({ initialized: true });

    } catch (error) {
      this.setState({ 
        error: error.message,
        initialized: false 
      });
      this.props.api.notify('组件初始化失败', 'error');
    }
  }

  private hasRequiredPermissions(user: UserInfo): boolean {
    const required = AnalyticsWidget.getRequiredPermissions();
    return required.every(permission => 
      user.permissions.includes(permission)
    );
  }

  private async loadData() {
    this.setState({ loading: true });
    try {
      // 使用平台API获取数据
      const response = await fetch(
        '/api/data/' + this.props.dataSource,
        {
          headers: {
            'Authorization': 'Bearer ' + await this.props.api.getToken()
          }
        }
      );
      const data = await response.json();
      this.setState({ data, loading: false });
    } catch (error) {
      this.setState({ 
        error: '数据加载失败',
        loading: false 
      });
    }
  }

  render() {
    if (!this.state.initialized) {
      return <div>组件初始化中...</div>;
    }

    if (this.state.error) {
      return <div className="error">错误: {this.state.error}</div>;
    }

    return (
      <div className="analytics-widget" data-plugin="analytics-widget">
        <h3>数据分析</h3>
        {this.state.loading ? (
          <div>加载中...</div>
        ) : (
          <div>
            {/* 根据chartType渲染不同类型的图表 */}
            {this.renderChart()}
          </div>
        )}
      </div>
    );
  }

  private renderChart() {
    const { chartType } = this.props;
    // 图表渲染逻辑
    return <div>图表类型: {chartType}</div>;
  }
}

// 插件管理器
class PluginManager {
  private static plugins: Map<string, PluginComponentClass> = new Map();

  // 注册第三方组件
  static registerPlugin<P, S>(
    PluginClass: PluginComponentClass<P, S>
  ): boolean {
    try {
      // 验证插件元数据
      if (!PluginClass.metadata || !PluginClass.metadata.name) {
        throw new Error('插件元数据不完整');
      }

      // 验证配置函数
      if (PluginClass.validateConfig && 
          typeof PluginClass.validateConfig !== 'function') {
        throw new Error('配置验证函数类型错误');
      }

      // 注册插件
      this.plugins.set(PluginClass.metadata.name, PluginClass);
      
      console.log('插件 ' + PluginClass.metadata.name + ' 注册成功');
      return true;

    } catch (error) {
      console.error('插件注册失败:', error);
      return false;
    }
  }

  // 获取已注册的插件
  static getPlugin(name: string): PluginComponentClass | undefined {
    return this.plugins.get(name);
  }

  // 创建插件实例
  static createPluginInstance(
    name: string, 
    props: PlatformComponentProps
  ): ReactElement | null {
    const PluginClass = this.getPlugin(name);
    
    if (!PluginClass) {
      console.error('未找到插件: ' + name);
      return null;
    }

    return React.createElement(PluginClass, props);
  }
}

// 注册第三方组件
PluginManager.registerPlugin(AnalyticsWidget);

// 平台使用示例
interface PlatformDashboardProps {
  widgets: Array<{
    plugin: string;
    config: any;
  }>;
}

class PlatformDashboard extends React.Component<PlatformDashboardProps> {
  render() {
    const api: PlatformAPI = {
      notify: (message, type) => console.log(message),
      getUser: () => Promise.resolve({} as UserInfo),
      navigate: (path) => console.log('navigate to', path),
      storage: {
        get: (key) => localStorage.getItem(key),
        set: (key, value) => localStorage.setItem(key, JSON.stringify(value))
      }
    };

    return (
      <div className="platform-dashboard">
        {this.props.widgets.map((widget, index) => 
          PluginManager.createPluginInstance(widget.plugin, {
            key: index,
            api,
            config: widget.config
          })
        )}
      </div>
    );
  }
}`,
    explanation: 'ComponentClass为微前端插件化架构提供了严格的类型约束和接口规范，确保第三方组件能够安全地集成到平台中。通过元数据和权限验证机制，平台可以对第三方组件进行有效管理和安全控制。',
    benefits: [
      '类型安全的第三方组件接入，避免99%的集成问题',
      '标准化的插件接口，降低第三方开发者学习成本60%',
      '权限管控和安全验证，确保平台安全性',
      '插件生态建设，平台扩展性提升500%',
      '统一的组件管理机制，维护成本降低80%'
    ],
    metrics: {
      performance: '插件化架构，按需加载，首屏性能提升50%，内存使用优化40%',
      userExperience: '第三方开发者接入效率提升300%，平台功能丰富度提升200%，用户定制化满意度提升40%',
      technicalMetrics: '代码复用率提升85%，平台扩展能力提升500%，第三方组件质量达标率95%'
    },
    difficulty: 'hard',
    tags: ['微前端', '插件化', '第三方集成', '类型安全', '权限管控']
  }
];

// ComponentClass业务场景内容已完成
export default businessScenarios;