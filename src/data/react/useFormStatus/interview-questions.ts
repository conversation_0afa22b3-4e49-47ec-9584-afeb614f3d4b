import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'useFormStatus 是什么？它在 React 19 中解决了什么问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基本概念',
    answer: {
      brief: 'useFormStatus 是 React 19 新增的 Hook，用于获取表单的提交状态信息，解决了表单状态管理和用户体验反馈的问题',
      detailed: `useFormStatus 是 React 19 引入的一个新 Hook，专门用于获取表单的提交状态。它解决了以下几个关键问题：

**主要功能**：
1. **实时状态获取**：自动跟踪表单的提交状态（pending、完成、失败等）
2. **数据访问**：提供对当前提交的 FormData 的访问
3. **元信息获取**：获取表单的 method 和 action 信息

**解决的问题**：
- **用户体验**：在表单提交时提供即时反馈，避免用户不确定性
- **重复提交**：通过 pending 状态防止用户重复提交表单
- **状态管理复杂性**：简化表单状态的管理，无需手动维护 loading 状态
- **代码复用**：可以在表单内的任何子组件中使用，提高代码复用性

**核心特性**：
- 只能在表单内部的组件中使用
- 自动与 React 19 的表单增强功能集成
- 提供类型安全的状态访问
- 支持服务器组件和客户端组件`,
      code: `import { useFormStatus } from 'react-dom';

// 基本用法示例
function SubmitButton() {
  const { pending, data, method, action } = useFormStatus();

  return (
    <button 
      type="submit" 
      disabled={pending}
    >
      {pending ? '提交中...' : '提交'}
    </button>
  );
}

// 在表单中使用
function ContactForm() {
  const handleSubmit = async (formData) => {
    // 处理表单提交
    await submitToServer(formData);
  };

  return (
    <form action={handleSubmit}>
      <input name="email" type="email" />
      <textarea name="message" />
      
      {/* SubmitButton 可以访问表单状态 */}
      <SubmitButton />
    </form>
  );
}`
    },
    tags: ['React 19', '基础概念']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '如何使用 useFormStatus 实现一个完整的文件上传组件，包括进度指示和取消功能？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实际应用',
    answer: {
      brief: '结合 useFormStatus 和自定义上传逻辑，实现文件上传的状态管理、进度显示和取消功能，提供完整的用户体验',
      detailed: `实现一个完整的文件上传组件需要考虑多个方面：状态管理、进度指示、错误处理和取消功能。

**实现策略**：
1. **状态层级设计**：
   - useFormStatus 管理表单级别的状态
   - 本地 state 管理上传进度和错误信息
   - 使用 ref 存储取消控制器

2. **进度跟踪**：
   - 利用 XMLHttpRequest 的 progress 事件
   - 结合 useFormStatus 的 pending 状态
   - 实时更新进度条和文件信息

3. **用户体验优化**：
   - 防止重复提交
   - 提供取消功能
   - 显示详细的上传信息
   - 错误状态处理

**关键技术点**：
- FormData API 用于文件数据处理
- AbortController 用于取消上传
- 自定义 Hook 封装上传逻辑
- 组件间状态协调`,
      code: `import { useFormStatus } from 'react-dom';
import { useState, useRef, useCallback } from 'react';

// 文件上传进度组件
function UploadProgress() {
  const { pending, data } = useFormStatus();
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState(null);

  return (
    <div className="upload-status">
      {pending && (
        <div className="progress-container">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: progress + '%' }}
            />
          </div>
          <span>{progress}%</span>
          
          {data && (
            <div className="file-info">
              文件: {data.get('file')?.name}
              大小: {formatFileSize(data.get('file')?.size)}
            </div>
          )}
        </div>
      )}
      
      {error && (
        <div className="error-message">
          上传失败: {error}
        </div>
      )}
    </div>
  );
}

// 自定义上传 Hook
function useFileUpload() {
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState(null);
  const abortController = useRef(null);

  const upload = useCallback(async (formData) => {
    const file = formData.get('file');
    if (!file) return;

    setError(null);
    setProgress(0);
    
    // 创建取消控制器
    abortController.current = new AbortController();

    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // 监听上传进度
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const percentComplete = Math.round(
            (event.loaded * 100) / event.total
          );
          setProgress(percentComplete);
        }
      });

      // 处理完成
      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          setProgress(100);
          resolve(xhr.response);
        } else {
          setError('上传失败');
          reject(new Error('Upload failed'));
        }
      });

      // 处理错误
      xhr.addEventListener('error', () => {
        setError('网络错误');
        reject(new Error('Network error'));
      });

      // 处理取消
      xhr.addEventListener('abort', () => {
        setError('上传已取消');
        setProgress(0);
        reject(new Error('Upload cancelled'));
      });

      // 配置和发送请求
      xhr.open('POST', '/api/upload');
      xhr.send(formData);

      // 绑定取消功能
      abortController.current.signal.addEventListener('abort', () => {
        xhr.abort();
      });
    });
  }, []);

  const cancel = useCallback(() => {
    if (abortController.current) {
      abortController.current.abort();
    }
  }, []);

  return { upload, cancel, progress, error };
}

// 主文件上传组件
function FileUploadForm() {
  const [selectedFile, setSelectedFile] = useState(null);
  const { upload, cancel, progress, error } = useFileUpload();
  const { pending } = useFormStatus();

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    setSelectedFile(file);
  };

  const handleSubmit = async (formData) => {
    try {
      await upload(formData);
      alert('上传成功！');
    } catch (error) {
      console.error('Upload error:', error);
    }
  };

  return (
    <form action={handleSubmit} encType="multipart/form-data">
      <div className="file-input">
        <input
          type="file"
          name="file"
          onChange={handleFileSelect}
          accept=".jpg,.png,.pdf,.doc"
          required
        />
        
        {selectedFile && (
          <div className="selected-file">
            {selectedFile.name} ({formatFileSize(selectedFile.size)})
          </div>
        )}
      </div>

      <div className="upload-actions">
        <button 
          type="submit" 
          disabled={!selectedFile || pending}
        >
          {pending ? '上传中...' : '开始上传'}
        </button>
        
        {pending && (
          <button 
            type="button" 
            onClick={cancel}
            className="cancel-btn"
          >
            取消上传
          </button>
        )}
      </div>

      <UploadProgress />
    </form>
  );
}

// 工具函数
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}`
    },
    tags: ['文件上传', '进度指示']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 3,
    question: 'useFormStatus 的内部实现原理是什么？它如何在 React 19 的架构中工作？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '深入原理',
    answer: {
      brief: 'useFormStatus 基于 React 19 的新表单上下文系统，利用 Fiber 架构的上下文传播机制和调度器协调状态更新，实现表单状态的自动管理',
      detailed: `useFormStatus 的实现涉及 React 19 的多个核心机制，是一个深度集成的系统级特性。

**🏗️ 核心架构设计**：

1. **FormContext 系统**：
   - React 19 为每个 form 元素自动创建 FormContext
   - 上下文存储 { pending, data, method, action } 状态
   - 通过 Fiber 节点的 context 字段传播

2. **Fiber 集成机制**：
   - 表单状态绑定到对应的 Fiber 节点
   - 利用 context 的向下传播机制
   - 与组件树的渲染周期深度集成

3. **调度器协调**：
   - 状态更新通过 Scheduler 协调
   - 利用 React 18+ 的并发特性
   - 支持优先级调度和时间分片

**🔬 状态更新流程**：

1. **表单提交触发**：
   \`\`\`
   form.onSubmit -> scheduleUpdate -> updateFormContext
   \`\`\`

2. **上下文更新传播**：
   \`\`\`
   FormContext.pending = true -> triggerRerender -> 
   useFormStatus components rerender
   \`\`\`

3. **数据捕获机制**：
   \`\`\`
   FormData capture -> context.data = formData -> 
   accessible via useFormStatus
   \`\`\`

**⚡ 性能优化策略**：

1. **订阅优化**：只有使用 useFormStatus 的组件才订阅状态变化
2. **浅比较**：使用 Object.is 进行状态比较，避免不必要更新
3. **批处理**：多个状态更新会被批处理到单次渲染中

**🧠 深度技术细节**：

- **内存管理**：FormContext 与表单生命周期绑定，自动清理
- **类型推导**：利用 TypeScript 的模板字面量类型推导 FormData 结构
- **错误边界**：表单外调用 useFormStatus 返回 null，避免运行时错误
- **SSR 支持**：在服务器端渲染时提供一致的初始状态`,
      code: `// React 19 useFormStatus 内部实现原理（简化版）

// 1. FormContext 定义
interface FormContextValue {
  pending: boolean;
  data: FormData | null;
  method: string | null;
  action: string | ((formData: FormData) => void) | null;
}

// 2. 内部状态管理
function createFormContext(): FormContextValue {
  return {
    pending: false,
    data: null,
    method: null,
    action: null
  };
}

// 3. useFormStatus Hook 实现（概念版）
function useFormStatus(): FormContextValue {
  // 获取当前 Fiber 节点
  const currentFiber = getCurrentFiber();
  
  // 向上遍历找到表单上下文
  const formContext = findFormContext(currentFiber);
  
  // 如果不在表单内，返回 null 状态
  if (!formContext) {
    return {
      pending: false,
      data: null,
      method: null,
      action: null
    };
  }
  
  // 订阅上下文变化
  subscribeToFormContext(formContext);
  
  return formContext.value;
}

// 4. 表单提交处理
function handleFormSubmit(form: HTMLFormElement, action: Function) {
  const formContext = getFormContext(form);
  
  // 更新提交状态
  scheduleUpdate(() => {
    formContext.pending = true;
    formContext.data = new FormData(form);
    formContext.method = form.method;
    formContext.action = action;
  });
  
  // 执行提交逻辑
  Promise.resolve(action(formContext.data))
    .then(() => {
      // 提交成功，重置状态
      scheduleUpdate(() => {
        formContext.pending = false;
        formContext.data = null;
      });
    })
    .catch((error) => {
      // 提交失败处理
      scheduleUpdate(() => {
        formContext.pending = false;
        // 可能包含错误信息
      });
    });
}

// 5. Fiber 集成示例
function createFormFiber(element: ReactElement) {
  const fiber = createFiber(element);
  
  // 为表单 Fiber 添加上下文
  fiber.context = {
    ...fiber.context,
    form: createFormContext()
  };
  
  return fiber;
}

// 6. 性能优化：订阅机制
const formContextSubscriptions = new WeakMap();

function subscribeToFormContext(context: FormContextValue) {
  const currentComponent = getCurrentComponent();
  
  if (!formContextSubscriptions.has(context)) {
    formContextSubscriptions.set(context, new Set());
  }
  
  const subscribers = formContextSubscriptions.get(context);
  subscribers.add(currentComponent);
  
  // 清理订阅
  useEffect(() => {
    return () => {
      subscribers.delete(currentComponent);
    };
  }, []);
}

// 7. 状态更新通知
function notifyFormContextChange(context: FormContextValue) {
  const subscribers = formContextSubscriptions.get(context);
  
  if (subscribers) {
    subscribers.forEach(component => {
      scheduleComponentUpdate(component);
    });
  }
}`
    },
    tags: ['React 19 架构', '深入原理']
  }
];

// 基于 React 19 useFormStatus 的专业面试问题
export default interviewQuestions;