import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: "useFormStatus是React 19中用于获取表单提交状态信息的Hook",
  
  introduction: `useFormStatus是React 19引入的表单状态Hook，主要用于获取表单的提交状态、检测表单是否正在提交和访问表单提交的相关数据。它采用声明式状态管理的设计模式，提供了简化表单用户体验反馈的能力。`,

  syntax: `function useFormStatus(): {
  pending: boolean;
  data: FormData | null;
  method: string | null;
  action: string | ((formData: FormData) => void) | null;
}`,

  quickExample: `function SubmitButton() {
  // 获取表单提交状态
  const { pending, data, method, action } = useFormStatus();

  return (
    <div>
      {/* 根据提交状态显示不同UI */}
      <button
        type="submit"
        disabled={pending}
        className={pending ? 'submitting' : ''}
      >
        {pending ? '提交中...' : '提交表单'}
      </button>
      
      {/* 显示提交详情 */}
      {pending && (
        <div className="status">
          方法: {method}
          {data && <span>数据大小: {data.keys().length} 字段</span>}
        </div>
      )}
    </div>
  );
}`,

  scenarioDiagram: `graph TD
    A[表单交互场景] --> B[提交状态反馈]
    A --> C[提交进度指示]
    A --> D[表单验证状态]

    B --> B1[禁用提交按钮]
    B --> B2[显示提交中文案]
    B --> B3[加载动画效果]

    C --> C1[进度条显示]
    C --> C2[步骤指示器]
    C --> C3[上传进度]

    D --> D1[实时验证反馈]
    D --> D2[错误信息显示]
    D --> D3[成功状态确认]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "无参数",
      type: "void",
      required: false,
      description: "useFormStatus不接受任何参数，直接调用即可获取当前表单上下文的状态",
      example: "const status = useFormStatus();"
    }
  ],
  
  returnValue: {
    type: "{ pending: boolean; data: FormData | null; method: string | null; action: string | ((formData: FormData) => void) | null; }",
    description: "返回包含表单提交状态的对象，包括是否正在提交、表单数据、提交方法和action",
    example: "const { pending, data, method, action } = useFormStatus();"
  },
  
  keyFeatures: [
    {
      title: "实时提交状态",
      description: "自动跟踪表单的提交状态，无需手动管理loading状态",
      benefit: "简化表单状态管理，减少样板代码，提升用户体验"
    },
    {
      title: "表单数据访问",
      description: "提供对当前提交的FormData的访问能力",
      benefit: "便于实现表单数据的验证、预览和调试功能"
    },
    {
      title: "Action信息获取",
      description: "获取表单的action属性和提交方法信息",
      benefit: "支持动态表单处理和多种提交方式的统一管理"
    },
    {
      title: "React 19优化",
      description: "与React 19的服务器组件和并发特性深度集成",
      benefit: "实现更好的性能和用户体验，支持服务器端表单处理"
    }
  ],
  
  limitations: [
    "仅在React 19+版本中可用，不向后兼容",
    "必须在表单组件内部使用，无法在表单外获取状态",
    "只能访问当前表单上下文的状态，不能跨表单共享",
    "对于复杂的多步骤表单可能需要额外的状态管理方案"
  ],
  
  bestPractices: [
    "在表单内的子组件中使用，而不是表单组件本身",
    "结合disabled属性防止重复提交",
    "使用pending状态提供视觉反馈，提升用户体验",
    "合理处理提交失败的情况，提供错误信息反馈",
    "避免在useFormStatus组件中直接修改表单状态"
  ],
  
  warnings: [
    "useFormStatus必须在表单内部的组件中调用，在表单外调用会返回null",
    "不要依赖useFormStatus来验证表单数据，应使用专门的验证逻辑",
    "pending状态可能在某些边缘情况下不准确，需要配合适当的错误处理"
  ]
};

// 根据React 19官方文档和最佳实践填充
export default basicInfo;