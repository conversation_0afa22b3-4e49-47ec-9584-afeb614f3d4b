import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useFormStatusData: ApiItem = {
  id: 'useFormStatus',
  title: 'useFormStatus',
  description: 'useFormStatus是React 19中用于获取表单提交状态信息的Hook，专门用于获取表单的提交状态、检测表单是否正在提交和访问表单提交的相关数据，提供了简化表单用户体验反馈的能力',
  category: 'React Hooks',
  difficulty: 'medium',
  
  syntax: `function useFormStatus(): {
  pending: boolean;
  data: FormData | null;
  method: string | null;
  action: string | ((formData: FormData) => void) | null;
}`,
  example: `function SubmitButton() {
  // 获取表单提交状态
  const { pending, data, method, action } = useFormStatus();

  return (
    <button
      type="submit"
      disabled={pending}
      className={pending ? 'submitting' : ''}
    >
      {pending ? '提交中...' : '提交表单'}
    </button>
  );
}`,
  notes: 'useFormStatus必须在表单内部的组件中调用，在表单外调用会返回null。仅在React 19+版本中可用，不向后兼容',
  
  version: 'React 19.0+',
  tags: ["React 19", "表单状态", "用户体验", "Hook", "表单提交"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useFormStatusData;