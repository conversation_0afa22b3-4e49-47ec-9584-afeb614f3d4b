import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么 useFormStatus 在表单外使用时返回的所有状态都是 null 或 false？',
    answer: `这是 useFormStatus 的设计限制，它只能在表单内部的组件中使用。React 19 通过表单上下文系统来管理状态，如果组件不在表单树中，就无法访问到表单的状态信息。

**原因分析**：
1. **上下文限制**：useFormStatus 依赖 React 的上下文机制，只有在表单组件树内才能访问 FormContext
2. **安全设计**：防止意外访问不相关表单的状态，确保状态隔离
3. **性能考虑**：避免全局状态污染，只有需要的组件才订阅状态变化

**正确的解决方案**：
- 将使用 useFormStatus 的组件移到表单内部
- 如果需要在表单外显示状态，通过 props 或状态管理库传递
- 使用条件渲染确保组件在正确的上下文中`,
    code: `// ❌ 错误：在表单外使用 useFormStatus
function App() {
  return (
    <div>
      <StatusDisplay /> {/* 这里无法获取表单状态 */}
      <form>
        <input name="email" />
        <button type="submit">提交</button>
      </form>
    </div>
  );
}

function StatusDisplay() {
  const { pending } = useFormStatus(); // 返回 { pending: false, data: null, ... }
  return <div>提交状态: {pending ? '提交中' : '空闲'}</div>;
}

// ✅ 正确：在表单内使用 useFormStatus
function App() {
  return (
    <form>
      <input name="email" />
      <StatusDisplay /> {/* 可以正确获取表单状态 */}
      <SubmitButton />
    </form>
  );
}`,
    tags: ['使用限制', '上下文'],
    relatedQuestions: ['表单嵌套问题', '状态传递']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: 'useFormStatus 的 pending 状态什么时候会变成 true？如何确保状态及时更新？',
    answer: `useFormStatus 的 pending 状态在表单开始提交时变为 true，在提交完成或失败时变为 false。理解其更新时机对于正确使用很重要。

**状态变化时机**：
1. **pending: true**：当表单的 action 函数开始执行时
2. **pending: false**：当 action 函数执行完成（无论成功或失败）
3. **data 更新**：在提交开始时捕获 FormData
4. **方法信息**：同时更新 method 和 action 信息

**确保及时更新的要点**：
- action 函数必须是异步的（返回 Promise）
- 避免在 action 中进行同步的重定向
- 正确处理错误，确保 Promise 正确 resolve/reject
- 使用 React 19 的原生表单功能，而不是手动事件处理`,
    code: `// ✅ 正确：使用异步 action 确保状态正确更新
function ContactForm() {
  const handleSubmit = async (formData) => {
    try {
      console.log('开始提交，pending: true');
      
      const response = await fetch('/api/contact', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error('提交失败');
      }
      
      console.log('提交完成，pending: false');
    } catch (error) {
      console.log('提交失败，pending: false');
    }
  };

  return (
    <form action={handleSubmit}>
      <input name="name" required />
      <SubmitButton />
    </form>
  );
}`,
    tags: ['状态更新', '异步处理'],
    relatedQuestions: ['异步表单处理', '错误处理']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: '在嵌套表单或复杂组件结构中，useFormStatus 如何区分不同表单的状态？',
    answer: `useFormStatus 自动获取最近的父级表单的状态，每个表单都有独立的上下文。在复杂结构中，需要理解上下文的查找机制和作用域。

**上下文查找机制**：
1. **就近原则**：useFormStatus 会向上遍历组件树，找到最近的表单上下文
2. **独立作用域**：每个表单都有自己独立的状态，不会相互影响
3. **嵌套支持**：支持表单嵌套，内层表单的状态不会影响外层表单

**最佳实践**：
- 确保组件在正确的表单作用域内
- 避免不必要的表单嵌套
- 使用明确的组件结构设计
- 考虑使用自定义 Hook 封装表单逻辑`,
    code: `// ✅ 正确：多个独立表单，各自维护状态
function MultiFormPage() {
  return (
    <div>
      <form action={handleLogin}>
        <h2>登录</h2>
        <input name="username" />
        <LoginButton /> {/* 只获取登录表单的状态 */}
      </form>

      <form action={handleRegister}>
        <h2>注册</h2>
        <input name="email" />
        <RegisterButton /> {/* 只获取注册表单的状态 */}
      </form>
    </div>
  );
}

function LoginButton() {
  const { pending } = useFormStatus(); // 获取登录表单状态
  return (
    <button type="submit" disabled={pending}>
      {pending ? '登录中...' : '登录'}
    </button>
  );
}`,
    tags: ['嵌套表单', '作用域'],
    relatedQuestions: ['表单设计', '组件架构']
  }
];

// 基于 React 19 useFormStatus 的常见开发问题
export default commonQuestions;