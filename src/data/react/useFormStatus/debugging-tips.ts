import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'useFormStatus 作为 React 19 的新特性，在实际使用中可能会遇到一些常见问题。掌握这些调试技巧能够帮助快速定位和解决问题，提高开发效率。',
        sections: [
          {
            title: '状态获取问题',
            description: 'useFormStatus 返回空值或不正确状态的调试方法',
            items: [
              {
                title: 'useFormStatus 返回 null 或默认值',
                description: '在表单外调用 useFormStatus 或组件结构不正确导致无法获取表单状态',
                solution: '确保组件在表单内部，检查组件树结构，使用 React DevTools 验证上下文',
                prevention: '始终在表单内部使用 useFormStatus，建立清晰的组件层级关系',
                code: `// 🔍 调试技巧：添加状态检测
function DebugFormStatus() {
  const status = useFormStatus();
  
  // 添加调试信息
  useEffect(() => {
    console.log('FormStatus Debug:', {
      pending: status.pending,
      hasData: !!status.data,
      method: status.method,
      action: typeof status.action,
      location: 'DebugFormStatus component'
    });
  }, [status]);

  // 显示调试信息
  if (process.env.NODE_ENV === 'development') {
    return (
      <div style={{ 
        background: '#f0f0f0', 
        padding: '8px', 
        fontSize: '12px',
        border: '1px solid #ccc'
      }}>
        <strong>🔍 useFormStatus Debug:</strong>
        <br />
        Pending: {status.pending.toString()}
        <br />
        Data: {status.data ? 'Available' : 'null'}
        <br />
        Method: {status.method || 'null'}
        <br />
        Action: {status.action ? typeof status.action : 'null'}
      </div>
    );
  }

  return null;
}

// ✅ 正确使用方式
function MyForm() {
  return (
    <form action={handleSubmit}>
      {/* 调试组件帮助检查状态 */}
      <DebugFormStatus />
      
      <input name="test" />
      <SubmitButton />
    </form>
  );
}`
              },
              {
                title: 'pending 状态不更新',
                description: 'action 函数执行但 pending 状态没有正确变化',
                solution: '检查 action 函数是否为异步函数，确保返回 Promise，避免同步重定向',
                prevention: '使用 async/await 模式，正确处理异步操作和错误',
                code: `// ❌ 错误：同步 action 导致状态问题
function syncAction(formData) {
  // 同步操作，pending 可能不正确更新
  console.log('Sync action');
  return; // 没有返回 Promise
}

// ✅ 正确：异步 action 确保状态更新
async function asyncAction(formData) {
  try {
    console.log('Async action start');
    
    // 确保是异步操作
    const response = await fetch('/api/submit', {
      method: 'POST',
      body: formData
    });
    
    if (!response.ok) {
      throw new Error('Submit failed');
    }
    
    console.log('Async action success');
    return response.json();
  } catch (error) {
    console.error('Async action error:', error);
    throw error; // 重要：重新抛出错误
  }
}

// 🔍 调试技巧：监控 action 执行
function createDebugAction(originalAction, actionName) {
  return async (formData) => {
    console.log(actionName + ' - Start', {
      timestamp: new Date().toISOString(),
      dataKeys: Array.from(formData.keys())
    });
    
    try {
      const result = await originalAction(formData);
      console.log(actionName + ' - Success', {
        timestamp: new Date().toISOString(),
        result
      });
      return result;
    } catch (error) {
      console.error(actionName + ' - Error', {
        timestamp: new Date().toISOString(),
        error: error.message
      });
      throw error;
    }
  };
}

// 使用调试包装器
const debugSubmitAction = createDebugAction(submitAction, 'SubmitForm');`
              }
            ]
          },
          {
            title: '性能问题',
            description: '表单状态更新导致的性能问题和解决方案',
            items: [
              {
                title: '频繁重渲染问题',
                description: '多个组件使用 useFormStatus 导致不必要的重渲染',
                solution: '使用 React.memo 优化组件，只订阅需要的状态字段，避免在渲染函数中直接使用',
                prevention: '合理拆分组件，只在需要的地方使用 useFormStatus',
                code: `// 🔍 调试技巧：渲染次数追踪
function RenderTracker({ name, children }) {
  const renderCount = useRef(0);
  renderCount.current += 1;
  
  useEffect(() => {
    console.log(name + ' rendered ' + renderCount.current + ' times');
  });
  
  return children;
}

// ❌ 问题：过度使用导致频繁渲染
function BadComponent() {
  const { pending, data, method, action } = useFormStatus();
  
  return (
    <RenderTracker name="BadComponent">
      <div>
        {/* 所有状态变化都会导致重渲染 */}
        Status: {pending ? 'Submitting' : 'Idle'}
        Data: {data ? 'Has data' : 'No data'}
        Method: {method || 'None'}
      </div>
    </RenderTracker>
  );
}

// ✅ 优化：只使用需要的状态
const SubmitButton = React.memo(() => {
  const { pending } = useFormStatus(); // 只使用 pending
  
  return (
    <RenderTracker name="SubmitButton">
      <button type="submit" disabled={pending}>
        {pending ? 'Submitting...' : 'Submit'}
      </button>
    </RenderTracker>
  );
});

const FormInfo = React.memo(() => {
  const { data, method } = useFormStatus(); // 只使用 data 和 method
  
  return (
    <RenderTracker name="FormInfo">
      <div className="form-info">
        Method: {method || 'POST'}
        Fields: {data ? data.keys().length : 0}
      </div>
    </RenderTracker>
  );
});

// 🔍 性能监控钩子
function useFormStatusPerformance() {
  const status = useFormStatus();
  const prevStatus = useRef(status);
  const changeCount = useRef(0);
  
  useEffect(() => {
    if (prevStatus.current.pending !== status.pending) {
      changeCount.current += 1;
      console.log('useFormStatus change #' + changeCount.current, {
        from: prevStatus.current.pending,
        to: status.pending,
        timestamp: performance.now()
      });
    }
    prevStatus.current = status;
  }, [status]);
  
  return status;
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '利用现代开发工具来调试和监控 useFormStatus 的行为，提高开发效率和问题排查能力。',
        sections: [
          {
            title: 'React DevTools 使用',
            description: '使用 React DevTools 检查表单状态和组件树',
            items: [
              {
                title: 'DevTools 中的 useFormStatus 调试',
                description: '在 React DevTools 中查看和修改表单状态',
                solution: '使用 useDebugValue 暴露状态信息，利用 Profiler 分析性能',
                prevention: '开发环境下启用详细的调试信息，生产环境下关闭调试代码',
                code: `// 🔍 调试技巧：使用 useDebugValue
function useFormStatusWithDebug() {
  const status = useFormStatus();
  
  // 在 DevTools 中显示有用的调试信息
  useDebugValue(status, (status) => ({
    state: status.pending ? 'SUBMITTING' : 'IDLE',
    hasData: !!status.data,
    fieldCount: status.data?.keys()?.length || 0,
    method: status.method,
    actionType: typeof status.action
  }));
  
  return status;
}

// 🔍 调试技巧：表单状态快照
function useFormStatusSnapshot() {
  const status = useFormStatus();
  const [snapshots, setSnapshots] = useState([]);
  
  useEffect(() => {
    const snapshot = {
      timestamp: Date.now(),
      pending: status.pending,
      dataKeys: status.data ? Array.from(status.data.keys()) : [],
      method: status.method,
      actionType: typeof status.action
    };
    
    setSnapshots(prev => [...prev.slice(-9), snapshot]); // 保留最近10个快照
  }, [status.pending, status.data, status.method, status.action]);
  
  // 在开发环境中暴露快照历史
  useDebugValue(snapshots, (snapshots) => 
    'Last ' + snapshots.length + ' state changes'
  );
  
  return { status, snapshots };
}

// 🔧 开发工具：状态变化可视化
function FormStatusVisualizer() {
  const { status, snapshots } = useFormStatusSnapshot();
  
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }
  
  return (
    <div className="form-status-visualizer" style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'white',
      border: '1px solid #ccc',
      padding: '10px',
      fontSize: '12px',
      maxWidth: '300px',
      zIndex: 9999
    }}>
      <h4>🔍 FormStatus Debug Panel</h4>
      
      <div>
        <strong>Current Status:</strong>
        <br />
        Pending: {status.pending ? '✅' : '❌'}
        <br />
        Data: {status.data ? Object.keys(status.data).length + ' fields' : 'None'}
      </div>
      
      <details>
        <summary>History ({snapshots.length})</summary>
        {snapshots.slice(-5).map((snapshot, index) => (
          <div key={snapshot.timestamp} style={{
            background: index === snapshots.length - 1 ? '#e6f3ff' : 'transparent',
            padding: '2px',
            margin: '2px 0'
          }}>
            {new Date(snapshot.timestamp).toLocaleTimeString()}: 
            {snapshot.pending ? ' SUBMITTING' : ' IDLE'}
            {snapshot.dataKeys.length > 0 && 
              ' (' + snapshot.dataKeys.length + ' fields)'
            }
          </div>
        ))}
      </details>
    </div>
  );
}`
              }
            ]
          },
          {
            title: '测试工具',
            description: '编写测试用例来验证 useFormStatus 的行为',
            items: [
              {
                title: '单元测试和集成测试',
                description: '使用 Jest 和 React Testing Library 测试表单状态',
                solution: '模拟表单提交，测试状态变化，验证用户交互',
                prevention: '编写覆盖各种场景的测试用例，包括成功和失败情况',
                code: `// 🧪 测试工具：useFormStatus 测试套件
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';

// 测试辅助组件
function TestFormWithStatus({ action, children }) {
  return (
    <form action={action}>
      {children}
      <TestStatusReporter />
    </form>
  );
}

function TestStatusReporter() {
  const status = useFormStatus();
  
  return (
    <div data-testid="form-status">
      <span data-testid="pending">{status.pending.toString()}</span>
      <span data-testid="has-data">{(!!status.data).toString()}</span>
      <span data-testid="method">{status.method || 'null'}</span>
    </div>
  );
}

// 🧪 测试用例：基本状态变化
describe('useFormStatus', () => {
  test('should update pending state during submission', async () => {
    let resolveSubmit;
    const submitPromise = new Promise(resolve => {
      resolveSubmit = resolve;
    });
    
    const mockAction = jest.fn(() => submitPromise);
    
    render(
      <TestFormWithStatus action={mockAction}>
        <input name="test" defaultValue="value" />
        <button type="submit">Submit</button>
      </TestFormWithStatus>
    );
    
    // 初始状态
    expect(screen.getByTestId('pending')).toHaveTextContent('false');
    
    // 提交表单
    fireEvent.click(screen.getByRole('button', { name: /submit/i }));
    
    // 检查 pending 状态
    await waitFor(() => {
      expect(screen.getByTestId('pending')).toHaveTextContent('true');
    });
    
    // 完成提交
    act(() => {
      resolveSubmit();
    });
    
    // 检查状态重置
    await waitFor(() => {
      expect(screen.getByTestId('pending')).toHaveTextContent('false');
    });
    
    expect(mockAction).toHaveBeenCalledTimes(1);
  });
  
  test('should handle form data correctly', async () => {
    const mockAction = jest.fn(async () => {});
    
    render(
      <TestFormWithStatus action={mockAction}>
        <input name="username" defaultValue="testuser" />
        <input name="email" defaultValue="<EMAIL>" />
        <button type="submit">Submit</button>
      </TestFormWithStatus>
    );
    
    fireEvent.click(screen.getByRole('button', { name: /submit/i }));
    
    await waitFor(() => {
      expect(screen.getByTestId('has-data')).toHaveTextContent('true');
    });
    
    expect(mockAction).toHaveBeenCalledWith(
      expect.objectContaining({
        get: expect.any(Function)
      })
    );
  });
});

// 🧪 E2E 测试辅助工具
function createFormTestHarness() {
  const events = [];
  const statusHistory = [];
  
  function FormTestHarness({ action, children }) {
    const status = useFormStatus();
    
    useEffect(() => {
      statusHistory.push({
        timestamp: Date.now(),
        ...status
      });
    }, [status.pending]);
    
    const wrappedAction = async (formData) => {
      events.push({ type: 'action-start', timestamp: Date.now() });
      try {
        const result = await action(formData);
        events.push({ type: 'action-success', timestamp: Date.now() });
        return result;
      } catch (error) {
        events.push({ type: 'action-error', timestamp: Date.now(), error });
        throw error;
      }
    };
    
    return (
      <form action={wrappedAction}>
        {children}
      </form>
    );
  }
  
  return {
    FormTestHarness,
    getEvents: () => events,
    getStatusHistory: () => statusHistory,
    reset: () => {
      events.length = 0;
      statusHistory.length = 0;
    }
  };
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

// 基于 React 19 useFormStatus 的实用调试技巧和工具
export default debuggingTips;