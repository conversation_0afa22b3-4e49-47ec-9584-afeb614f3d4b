import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: "renderToReadableStream是React中用于Web Streams标准的服务端渲染API，专门为边缘运行时和现代Web环境设计，支持流式HTML传输和Suspense异步渲染。",

  introduction: `renderToReadableStream是React 18引入的现代服务端渲染API，专门为Web Streams标准和边缘运行时环境设计。

它遵循Web标准的流式传输原则，在服务端渲染和客户端水合之间做出了智能优化，允许开发者将React组件渲染为符合Web Streams标准的ReadableStream，实现跨平台的流式HTML传输。

主要用于边缘计算环境、Cloudflare Workers、Deno部署和现代Web标准兼容的服务端渲染。相比Node.js专用的renderToPipeableStream，它的优势在于跨平台兼容性和Web标准一致性。

在React SSR生态中，它是现代边缘计算和无服务器环境的核心工具，常见于需要全球分布式部署的应用，特别适合CDN边缘节点和轻量级运行时的场景。

核心优势包括Web标准兼容、跨平台部署、边缘计算优化，同时提供了与renderToPipeableStream相似的流式渲染能力，但更适合现代云原生架构。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react-dom/server.d.ts:78
 * - 实现文件：packages/react-dom/src/server/ReactDOMFizzServerBrowser.js:45
 * - 流处理：packages/react-dom/src/server/ReactDOMServerStreaming.js:89
 */

// 基础语法
function renderToReadableStream(
  children: ReactNode,
  options?: {
    identifierPrefix?: string;
    namespaceURI?: string;
    nonce?: string;
    bootstrapScriptContent?: string;
    bootstrapScripts?: string[];
    bootstrapModules?: string[];
    progressiveChunkSize?: number;
    signal?: AbortSignal;
    onError?: (error: unknown) => void;
  }
): Promise<ReadableStream<Uint8Array>>

// 使用示例
const stream = await renderToReadableStream(<App />, {
  bootstrapScripts: ['/static/js/main.js'],
  signal: controller.signal,
  onError(error) {
    console.error('SSR Error:', error);
  }
});

/**
 * 参数约束：
 * - children 必须是有效的React节点
 * - 返回Promise<ReadableStream>，符合Web Streams标准
 * - 支持AbortSignal进行取消控制
 * - 自动处理客户端水合标记
 */`,

  quickExample: `// Cloudflare Workers示例
export default {
  async fetch(request, env, ctx) {
    // 渲染React应用为Web Stream
    const stream = await renderToReadableStream(<App />, {
      // 客户端JavaScript文件
      bootstrapScripts: ['/static/js/main.js'],

      // 中止信号控制
      signal: request.signal,

      // 渲染错误处理
      onError(error) {
        console.error('Edge SSR error:', error);
      }
    });

    // 返回流式响应
    return new Response(stream, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Transfer-Encoding': 'chunked'
      }
    });
  }
};`,

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "renderToReadableStream在现代Web环境中的核心应用场景，展示其在边缘计算和跨平台部署中的优势",
      diagram: `graph LR
      A[renderToReadableStream核心场景] --> B[边缘计算]
      A --> C[跨平台部署]
      A --> D[现代Web标准]

      B --> B1["🌐 Cloudflare Workers<br/>全球边缘节点部署"]
      B --> B2["⚡ Deno Deploy<br/>轻量级运行时"]

      C --> C1["🔄 多环境兼容<br/>Node.js/Deno/Workers"]
      C --> C2["📦 无服务器函数<br/>Vercel/Netlify Edge"]

      D --> D1["🌊 Web Streams标准<br/>原生浏览器支持"]
      D --> D2["🎯 现代API设计<br/>Promise/AbortSignal"]

      style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
      style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
      style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "技术架构对比",
      description: "renderToReadableStream与其他SSR方案的技术架构对比，突出其在现代Web生态中的定位",
      diagram: `graph TB
      A[React SSR生态] --> B[Node.js环境]
      A --> C[Web标准环境]
      A --> D[静态生成]

      B --> B1["renderToPipeableStream<br/>Node.js流式渲染"]
      B --> B2["renderToString<br/>同步字符串渲染"]

      C --> C1["renderToReadableStream<br/>Web Streams渲染"]
      C --> C2["renderToStaticMarkup<br/>静态HTML生成"]

      D --> D1["Static Generation<br/>构建时预渲染"]
      D --> D2["ISR<br/>增量静态再生"]

      C1 -.-> B1
      C2 -.-> B2
      C1 -.-> D1

      style A fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
      style B fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
      style C fill:#fce4ec,stroke:#c2185b,stroke-width:2px
      style D fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
      style C1 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
    },
    {
      title: "边缘计算生态系统",
      description: "renderToReadableStream在边缘计算生态系统中的位置和与各种边缘运行时的集成关系",
      diagram: `graph TD
      A[边缘计算生态] --> B[边缘运行时]
      A --> C[CDN集成]
      A --> D[开发工具链]

      B --> B1["Cloudflare Workers<br/>V8引擎"]
      B --> B2["Deno Deploy<br/>TypeScript原生"]
      B --> B3["Vercel Edge<br/>Web标准API"]
      B --> B4["Netlify Edge<br/>Deno运行时"]

      C --> C1["全球分发<br/>就近响应"]
      C --> C2["缓存策略<br/>智能更新"]

      D --> D1["Wrangler CLI<br/>Workers开发"]
      D --> D2["Edge Functions<br/>本地调试"]

      B1 -.-> A
      B2 -.-> A
      B3 -.-> A
      B4 -.-> A

      style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
      style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
      style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px`
    }
  ],
  
  parameters: [
    {
      name: "children",
      type: "ReactNode",
      required: true,
      description: "要渲染的React组件树，可以是JSX元素、组件或任何有效的React节点。这是渲染的根内容，将被转换为HTML流。",
      example: `<App />
// 或者
<html>
  <head><title>My App</title></head>
  <body><App /></body>
</html>`
    },
    {
      name: "options",
      type: "RenderToReadableStreamOptions",
      required: false,
      description: "可选的配置对象，用于控制渲染行为、错误处理和客户端水合。包含多个子选项来定制SSR过程。",
      example: `{
  bootstrapScripts: ['/static/js/main.js'],
  signal: abortController.signal,
  onError: (error) => console.error(error)
}`
    },
    {
      name: "options.identifierPrefix",
      type: "string",
      required: false,
      description: "用于生成HTML元素ID的前缀，避免多个React应用实例之间的ID冲突。在微前端或多实例部署中特别有用。",
      example: `{ identifierPrefix: 'app1-' }`
    },
    {
      name: "options.bootstrapScripts",
      type: "string[]",
      required: false,
      description: "客户端JavaScript文件的URL数组，这些脚本将在HTML中自动包含，用于客户端水合和应用启动。",
      example: `{ bootstrapScripts: ['/static/js/main.js', '/static/js/vendor.js'] }`
    },
    {
      name: "options.signal",
      type: "AbortSignal",
      required: false,
      description: "用于取消渲染过程的AbortSignal，支持现代Web标准的取消机制。当请求被取消时，渲染会立即停止。",
      example: `const controller = new AbortController();
{ signal: controller.signal }`
    },
    {
      name: "options.onError",
      type: "(error: unknown) => void",
      required: false,
      description: "渲染过程中发生错误时的回调函数。用于记录错误、发送监控数据或执行错误恢复逻辑。",
      example: `{
  onError: (error) => {
    console.error('SSR Error:', error);
    // 发送到错误监控服务
    errorReporting.captureException(error);
  }
}`
    }
  ],
  
  returnValue: {
    type: "Promise<ReadableStream<Uint8Array>>",
    description: "返回一个Promise，解析为符合Web Streams标准的ReadableStream。这个流包含渲染后的HTML内容，可以直接用于HTTP响应或进一步处理。",
    example: `const stream = await renderToReadableStream(<App />, {
  bootstrapScripts: ['/static/js/main.js']
});

// 在Cloudflare Workers中使用
return new Response(stream, {
  headers: { 'Content-Type': 'text/html' }
});

// 在Deno中使用
const response = new Response(stream);
await response.body?.pipeTo(writable);`
  },

  keyFeatures: [
    {
      title: "Web Streams标准兼容",
      description: "完全符合Web Streams API标准，返回原生ReadableStream对象，可以与现代Web平台的所有流处理API无缝集成。",
      benefit: "提供跨平台兼容性，支持在任何符合Web标准的环境中运行，包括浏览器、Deno、Cloudflare Workers等。"
    },
    {
      title: "边缘计算优化",
      description: "专门为边缘计算环境设计，具有更小的内存占用和更快的启动时间，适合在资源受限的边缘节点上运行。",
      benefit: "实现全球分布式部署，用户可以从最近的边缘节点获得更快的响应速度和更好的用户体验。"
    },
    {
      title: "现代异步API设计",
      description: "采用Promise-based的异步API设计，支持AbortSignal取消机制，与现代JavaScript异步编程模式完美契合。",
      benefit: "提供更好的错误处理和资源管理，支持优雅的请求取消和超时控制。"
    },
    {
      title: "流式HTML传输",
      description: "支持流式HTML传输，可以在渲染完成之前就开始向客户端发送内容，特别是对于包含Suspense的应用。",
      benefit: "显著改善首屏加载时间，用户可以更早看到页面内容，提升感知性能。"
    },
    {
      title: "轻量级运行时支持",
      description: "不依赖Node.js特定的API，可以在各种轻量级JavaScript运行时中运行，包括Deno、Bun等。",
      benefit: "提供更多的部署选择和更好的性能特性，适合现代云原生和无服务器架构。"
    }
  ],
  
  limitations: [
    "不支持Node.js特定的流API，无法直接与Node.js的fs.createWriteStream()等API集成，需要适配层。",
    "在某些边缘运行时中可能有内存限制，大型应用的SSR可能受到运行时环境的内存约束。",
    "相比renderToPipeableStream缺少一些Node.js特定的优化，如背压控制和更精细的流控制。",
    "调试工具支持相对较少，在边缘环境中的错误追踪和性能分析可能比传统Node.js环境更困难。",
    "某些第三方库可能不兼容边缘运行时环境，需要仔细选择依赖项。"
  ],

  bestPractices: [
    "优先在边缘计算和无服务器环境中使用，充分发挥其跨平台兼容性优势。",
    "合理设置AbortSignal超时时间，避免长时间运行的渲染任务占用边缘节点资源。",
    "使用bootstrapScripts预加载关键的客户端代码，确保水合过程的顺利进行。",
    "在onError回调中实现完善的错误处理和监控，及时发现和解决渲染问题。",
    "针对边缘环境优化组件代码，避免使用Node.js特定的API和大型依赖库。",
    "利用Suspense和流式渲染特性，优化大型页面的加载性能。",
    "在开发环境中充分测试不同边缘运行时的兼容性，确保生产环境的稳定性。",
    "合理配置identifierPrefix，避免多实例部署时的ID冲突问题。"
  ],

  warnings: [
    "不要在Node.js环境中优先选择此API，renderToPipeableStream在Node.js中有更好的性能和功能。",
    "注意边缘运行时的内存和执行时间限制，避免渲染过于复杂的组件树。",
    "确保所有依赖库都兼容目标边缘运行时环境，避免运行时错误。",
    "在使用AbortSignal时要正确处理取消逻辑，避免资源泄漏。",
    "注意不同边缘平台的API差异，进行充分的跨平台测试。"
  ]
};

export default basicInfo;