import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 1,
    question: 'renderToReadableStream是什么？它与renderToPipeableStream有什么区别？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'renderToReadableStream是React 18的Web Streams标准SSR API，专门为边缘计算环境设计，与renderToPipeableStream的主要区别是跨平台兼容性。',
      detailed: `**核心概念**：renderToReadableStream是React 18引入的服务端渲染API，基于Web Streams标准，返回Promise<ReadableStream>，专门为边缘计算和现代Web环境设计。

**主要区别**：
1. **平台兼容性**：renderToReadableStream支持所有符合Web标准的环境（Cloudflare Workers、Deno、浏览器），而renderToPipeableStream仅支持Node.js
2. **API设计**：前者基于Promise和Web Streams，后者基于Node.js流和回调
3. **性能特性**：renderToPipeableStream在Node.js中有更多优化（如背压控制），renderToReadableStream更注重标准化

**使用场景**：
- renderToReadableStream：边缘函数、CDN部署、跨平台应用
- renderToPipeableStream：传统Node.js服务器、需要高性能优化的场景

**注意事项**：选择时要考虑部署环境和性能需求，不是简单的替换关系。`,
      code: `// renderToReadableStream - Web标准
const stream = await renderToReadableStream(<App />, {
  bootstrapScripts: ['/static/js/main.js'],
  signal: request.signal
});

return new Response(stream, {
  headers: { 'Content-Type': 'text/html' }
});

// renderToPipeableStream - Node.js专用
const { pipe } = renderToPipeableStream(<App />, {
  bootstrapScripts: ['/static/js/main.js'],
  onShellReady() {
    response.statusCode = 200;
    response.setHeader('Content-Type', 'text/html');
    pipe(response);
  }
});`
    },
    tags: ['SSR', 'Web Streams', '边缘计算', 'React 18']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 2,
    question: 'renderToReadableStream如何与Suspense配合实现流式渲染？内部机制是什么？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: 'renderToReadableStream通过Fiber架构和Suspense边界实现分层渲染，先发送已完成的HTML，异步数据到达后通过流更新剩余内容。',
      detailed: `**核心机制**：renderToReadableStream基于React Fiber的可中断渲染特性，配合Suspense边界实现智能的流式传输。

**工作流程**：
1. **初始渲染**：开始渲染组件树，遇到Suspense边界时暂停
2. **部分传输**：将已完成的HTML片段推送到ReadableStream
3. **异步等待**：Suspense内的组件继续获取数据
4. **增量更新**：数据到达后，渲染剩余内容并推送到流
5. **完成关闭**：所有内容渲染完成后关闭流

**技术实现**：
- 使用Fiber的时间切片能力，避免长时间阻塞
- 通过ReadableStream控制器管理数据推送
- 利用HTML注释标记异步内容的位置
- 客户端通过水合过程接管交互

**优势**：用户可以更早看到页面内容，提升感知性能，特别适合包含大量异步数据的复杂页面。`,
      code: `// 带Suspense的组件
function ProductPage({ productId }) {
  return (
    <div>
      <ProductHeader productId={productId} />

      {/* 异步加载的评论区 */}
      <Suspense fallback={<div>加载评论中...</div>}>
        <ProductReviews productId={productId} />
      </Suspense>

      {/* 异步加载的推荐商品 */}
      <Suspense fallback={<div>加载推荐中...</div>}>
        <RecommendedProducts productId={productId} />
      </Suspense>
    </div>
  );
}

// 流式渲染
const stream = await renderToReadableStream(
  <ProductPage productId="123" />,
  {
    onError(error) {
      console.error('流式渲染错误:', error);
    }
  }
);

// 渲染顺序：
// 1. 立即发送：ProductHeader + fallback UI
// 2. 异步发送：ProductReviews 替换 fallback
// 3. 异步发送：RecommendedProducts 替换 fallback`
    },
    tags: ['Suspense', '流式渲染', 'Fiber架构', '异步数据']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 3,
    question: '在Cloudflare Workers中使用renderToReadableStream时，如何处理错误、超时和资源限制？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '实战应用',
    answer: {
      brief: '需要实现完善的错误边界、AbortSignal超时控制、内存优化和降级策略，确保在边缘环境的资源限制下稳定运行。',
      detailed: `**核心挑战**：Cloudflare Workers有严格的CPU时间（10ms-50ms）、内存（128MB）和执行时间限制，需要特殊的优化策略。

**错误处理策略**：
1. **渲染错误**：使用onError回调捕获渲染异常，提供降级HTML
2. **超时控制**：通过AbortSignal设置合理的超时时间
3. **资源限制**：监控内存使用，避免大型组件树
4. **网络错误**：处理外部API调用失败的情况

**性能优化**：
1. **组件分割**：将大型页面拆分为多个小组件
2. **缓存策略**：利用Workers KV存储缓存渲染结果
3. **流控制**：控制并发渲染数量，避免资源耗尽
4. **预渲染**：对静态内容进行预渲染和缓存

**监控和调试**：
- 使用Workers Analytics监控性能指标
- 实现结构化日志记录
- 设置告警机制处理异常情况

**最佳实践**：始终提供降级方案，确保即使在最坏情况下也能返回可用的HTML。`,
      code: `// Cloudflare Workers中的完整实现
export default {
  async fetch(request, env, ctx) {
    // 创建超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, 8000); // 8秒超时，留2秒缓冲

    try {
      // 获取缓存
      const cacheKey = new URL(request.url).pathname;
      const cached = await env.CACHE_KV.get(cacheKey);
      if (cached) {
        return new Response(cached, {
          headers: { 'Content-Type': 'text/html' }
        });
      }

      // 渲染React应用
      const stream = await renderToReadableStream(
        <App url={request.url} />,
        {
          bootstrapScripts: ['/static/js/main.js'],
          signal: controller.signal,

          onError(error) {
            console.error('SSR Error:', error);

            // 发送错误到监控服务
            ctx.waitUntil(
              env.ERROR_WEBHOOK && fetch(env.ERROR_WEBHOOK, {
                method: 'POST',
                body: JSON.stringify({
                  error: error.message,
                  stack: error.stack,
                  url: request.url,
                  timestamp: Date.now()
                })
              })
            );
          }
        }
      );

      clearTimeout(timeoutId);

      // 缓存结果（异步）
      ctx.waitUntil(
        stream.tee()[1].then(async (cacheStream) => {
          const html = await new Response(cacheStream).text();
          await env.CACHE_KV.put(cacheKey, html, {
            expirationTtl: 3600 // 1小时缓存
          });
        })
      );

      return new Response(stream.tee()[0], {
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
          'Cache-Control': 'public, max-age=300'
        }
      });

    } catch (error) {
      clearTimeout(timeoutId);

      // 降级到静态HTML
      const fallbackHtml = '<!DOCTYPE html><html><head><title>服务暂时不可用</title></head><body><h1>页面加载中...</h1><script>setTimeout(() => location.reload(), 3000);</script></body></html>';

      return new Response(fallbackHtml, {
        status: 500,
        headers: { 'Content-Type': 'text/html' }
      });
    }
  }
};`
    },
    tags: ['Cloudflare Workers', '错误处理', '性能优化', '边缘计算']
  }
];

export default interviewQuestions;