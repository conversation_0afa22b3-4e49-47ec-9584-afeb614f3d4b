import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'renderToReadableStream作为现代流式SSR的核心API，在边缘环境和跨平台部署中容易遇到各种调试挑战。这些问题往往涉及流处理、异步渲染和环境兼容性，需要专门的调试技巧来快速定位和解决。',
        sections: [
          {
            title: '流式渲染问题',
            description: '流式渲染过程中的常见问题和解决方案',
            items: [
              {
                title: 'ReadableStream未定义错误',
                description: '在某些环境中出现"ReadableStream is not defined"错误，导致渲染失败',
                solution: '检查运行环境是否支持Web Streams API，添加polyfill或使用条件渲染',
                prevention: '在代码中添加环境检测，为不支持的环境提供降级方案',
                code: `// 环境检测和降级处理
async function safeRenderToReadableStream(element, options) {
  // 检查Web Streams支持
  if (typeof ReadableStream === 'undefined') {
    console.warn('ReadableStream not supported, falling back to renderToString');
    const { renderToString } = await import('react-dom/server');
    return new Response(renderToString(element), {
      headers: { 'Content-Type': 'text/html' }
    });
  }

  try {
    const { renderToReadableStream } = await import('react-dom/server');
    return new Response(await renderToReadableStream(element, options));
  } catch (error) {
    console.error('renderToReadableStream failed:', error);
    // 降级到同步渲染
    const { renderToString } = await import('react-dom/server');
    return new Response(renderToString(element), {
      headers: { 'Content-Type': 'text/html' }
    });
  }
}`
              },
              {
                title: '流被提前终止',
                description: '流式传输过程中连接断开或被中断，导致页面加载不完整',
                solution: '实现流错误处理和重试机制，添加AbortSignal支持',
                prevention: '设置合理的超时时间，监控网络状态，实现优雅降级',
                code: `// 流错误处理和重试
async function robustStreamRender(element, options = {}) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000);

  try {
    const stream = await renderToReadableStream(element, {
      ...options,
      signal: controller.signal,
      onError(error) {
        console.error('Stream error:', error);
        // 记录错误但不中断流
      }
    });

    // 监控流状态
    const reader = stream.getReader();
    const chunks = [];

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      chunks.push(value);
    }

    clearTimeout(timeoutId);
    return new Response(new ReadableStream({
      start(controller) {
        chunks.forEach(chunk => controller.enqueue(chunk));
        controller.close();
      }
    }));

  } catch (error) {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
      console.warn('Stream timeout, falling back to static render');
    }
    throw error;
  }
}`
              },
              {
                title: 'Suspense组件无限loading',
                description: 'Suspense边界一直显示loading状态，异步组件无法正常渲染',
                solution: '检查数据获取逻辑，确保Promise正确resolve，添加错误边界',
                prevention: '使用正确的Suspense数据获取模式，设置超时机制',
                code: `// 正确的Suspense数据获取
const dataCache = new Map();

function fetchWithSuspense(key, fetcher) {
  if (dataCache.has(key)) {
    const cached = dataCache.get(key);
    if (cached instanceof Promise) {
      throw cached; // 仍在加载中
    }
    return cached; // 已加载完成
  }

  const promise = Promise.race([
    fetcher(),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Timeout')), 5000)
    )
  ]).then(
    data => {
      dataCache.set(key, data);
      return data;
    },
    error => {
      dataCache.delete(key); // 清除失败的缓存
      throw error;
    }
  );

  dataCache.set(key, promise);
  throw promise;
}

// 使用示例
function AsyncComponent({ id }) {
  const data = fetchWithSuspense('data-' + id, () =>
    fetch('/api/data/' + id).then(r => r.json())
  );

  return <div>{data.content}</div>;
}`
              }
            ]
          },
          {
            title: '性能调试问题',
            description: '流式渲染性能相关的调试技巧',
            items: [
              {
                title: '内存泄漏检测',
                description: '边缘环境中出现内存持续增长，影响应用稳定性',
                solution: '使用内存监控工具，检查对象引用，实现资源清理',
                prevention: '定期检查内存使用，避免全局变量累积，及时清理事件监听器',
                code: `// 内存监控和清理
class MemoryMonitor {
  constructor() {
    this.references = new WeakMap();
    this.timers = new Set();
  }

  trackObject(obj, name) {
    this.references.set(obj, { name, created: Date.now() });
  }

  createTimer(callback, interval) {
    const timer = setInterval(callback, interval);
    this.timers.add(timer);
    return timer;
  }

  cleanup() {
    // 清理所有定时器
    this.timers.forEach(timer => clearInterval(timer));
    this.timers.clear();

    // 检查内存使用
    if (performance.memory) {
      const memUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
      console.log('Memory usage:', memUsage.toFixed(2) + 'MB');

      if (memUsage > 100) {
        console.warn('High memory usage detected');
      }
    }
  }
}

// 在renderToReadableStream中使用
const monitor = new MemoryMonitor();

export default {
  async fetch(request, env, ctx) {
    try {
      const stream = await renderToReadableStream(<App />, {
        onError: (error) => {
          monitor.trackObject(error, 'render-error');
        }
      });

      return new Response(stream);
    } finally {
      // 确保资源清理
      ctx.waitUntil(
        new Promise(resolve => {
          setTimeout(() => {
            monitor.cleanup();
            resolve();
          }, 1000);
        })
      );
    }
  }
};`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '有效的调试工具是快速定位renderToReadableStream问题的关键。本节介绍专门针对流式SSR的调试工具和技巧，帮助开发者在不同环境中进行高效调试。',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '使用浏览器内置工具调试流式渲染',
            items: [
              {
                title: 'Network面板流分析',
                description: '通过Network面板监控流式传输的实时状态和性能表现',
                solution: '使用Network面板的Response tab查看流式内容，分析传输时序',
                prevention: '定期检查网络传输模式，确保流式传输正常工作',
                code: `// 在浏览器中监控流式传输
// 1. 打开DevTools -> Network面板
// 2. 刷新页面，找到HTML文档请求
// 3. 点击Response tab，观察内容是否分块传输
// 4. 查看Timing tab，分析TTFB和传输时间

// 编程方式监控流传输
function monitorStreamResponse(url) {
  const startTime = performance.now();

  fetch(url).then(response => {
    console.log('TTFB:', performance.now() - startTime + 'ms');

    const reader = response.body.getReader();
    let chunkCount = 0;

    function readChunk() {
      return reader.read().then(({ done, value }) => {
        if (done) {
          console.log('Stream complete, total chunks:', chunkCount);
          return;
        }

        chunkCount++;
        console.log('Chunk', chunkCount, 'size:', value.length);

        return readChunk();
      });
    }

    return readChunk();
  });
}`
              },
              {
                title: 'React DevTools Profiler',
                description: '使用React DevTools分析组件渲染性能和Suspense行为',
                solution: '启用Profiler记录，分析组件渲染时序和Suspense状态变化',
                prevention: '定期进行性能分析，识别渲染瓶颈和优化机会',
                code: `// React DevTools Profiler使用技巧
// 1. 安装React DevTools浏览器扩展
// 2. 打开Profiler tab
// 3. 点击录制按钮，刷新页面
// 4. 分析组件渲染时序图

// 编程方式添加性能标记
import { Profiler } from 'react';

function ProfiledApp() {
  function onRenderCallback(id, phase, actualDuration, baseDuration, startTime, commitTime) {
    console.log('Profiler:', {
      id,
      phase,
      actualDuration,
      baseDuration,
      startTime,
      commitTime
    });

    // 发送性能数据到分析服务
    if (actualDuration > 16) { // 超过一帧时间
      console.warn('Slow render detected:', id, actualDuration + 'ms');
    }
  }

  return (
    <Profiler id="App" onRender={onRenderCallback}>
      <App />
    </Profiler>
  );
}

// Suspense状态监控
function SuspenseMonitor({ children, fallback, name }) {
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    console.log('Suspense', name, 'loading state:', isLoading);
  }, [isLoading, name]);

  return (
    <Suspense
      fallback={
        <div>
          {fallback}
          {console.log('Suspense', name, 'showing fallback')}
        </div>
      }
    >
      {children}
    </Suspense>
  );
}`
              }
            ]
          },
          {
            title: '边缘环境调试',
            description: '在Cloudflare Workers等边缘环境中的调试技巧',
            items: [
              {
                title: 'Cloudflare Workers调试',
                description: '在Cloudflare Workers环境中调试renderToReadableStream',
                solution: '使用wrangler dev本地调试，添加详细日志，利用Workers Analytics',
                prevention: '建立完善的日志系统，设置错误监控和告警',
                code: `// Cloudflare Workers调试配置
export default {
  async fetch(request, env, ctx) {
    const startTime = Date.now();
    const url = new URL(request.url);

    // 详细日志记录
    console.log('Request:', {
      method: request.method,
      url: url.pathname,
      userAgent: request.headers.get('user-agent'),
      cf: request.cf
    });

    try {
      const stream = await renderToReadableStream(<App />, {
        onError(error) {
          // 错误详细记录
          console.error('SSR Error:', {
            message: error.message,
            stack: error.stack,
            url: url.pathname,
            timestamp: new Date().toISOString()
          });

          // 发送到外部监控服务
          ctx.waitUntil(
            fetch('https://api.monitoring.com/errors', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                error: error.message,
                stack: error.stack,
                url: url.pathname,
                userAgent: request.headers.get('user-agent'),
                timestamp: Date.now()
              })
            })
          );
        }
      });

      const renderTime = Date.now() - startTime;
      console.log('Render completed:', renderTime + 'ms');

      // 性能指标记录
      ctx.waitUntil(
        env.ANALYTICS?.writeDataPoint({
          blobs: ['ssr-success'],
          doubles: [renderTime],
          indexes: [request.cf?.colo || 'unknown']
        })
      );

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/html',
          'X-Render-Time': renderTime + 'ms'
        }
      });

    } catch (error) {
      const errorTime = Date.now() - startTime;
      console.error('Fatal error:', error);

      // 错误指标记录
      ctx.waitUntil(
        env.ANALYTICS?.writeDataPoint({
          blobs: ['ssr-error'],
          doubles: [errorTime],
          indexes: [request.cf?.colo || 'unknown']
        })
      );

      // 返回错误页面
      return new Response('Internal Server Error', {
        status: 500,
        headers: { 'Content-Type': 'text/plain' }
      });
    }
  }
};

// 本地调试配置 (wrangler.toml)
/*
[env.development]
compatibility_date = "2023-05-18"
compatibility_flags = ["streams_enable_constructors"]

[env.development.vars]
DEBUG = "true"
LOG_LEVEL = "debug"
*/`
              },
              {
                title: '自定义调试工具',
                description: '创建专门的调试工具来监控流式渲染状态',
                solution: '开发调试面板，实时监控流状态、性能指标和错误信息',
                prevention: '在开发环境中集成调试工具，生产环境中保留关键监控',
                code: `// 自定义流式渲染调试器
class StreamDebugger {
  constructor() {
    this.streams = new Map();
    this.metrics = [];
    this.errors = [];
  }

  trackStream(id, stream) {
    const startTime = Date.now();
    const reader = stream.getReader();
    const chunks = [];

    this.streams.set(id, {
      id,
      startTime,
      chunks,
      status: 'reading'
    });

    this.readStream(reader, id, chunks);
    return stream;
  }

  async readStream(reader, id, chunks) {
    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          const streamInfo = this.streams.get(id);
          streamInfo.status = 'complete';
          streamInfo.endTime = Date.now();
          streamInfo.duration = streamInfo.endTime - streamInfo.startTime;

          console.log('Stream complete:', {
            id,
            duration: streamInfo.duration + 'ms',
            chunks: chunks.length,
            totalSize: chunks.reduce((sum, chunk) => sum + chunk.length, 0)
          });
          break;
        }

        chunks.push(value);
        console.log('Stream chunk:', {
          id,
          chunkIndex: chunks.length,
          chunkSize: value.length,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('Stream error:', { id, error });
      this.errors.push({ id, error, timestamp: Date.now() });
    }
  }

  getDebugInfo() {
    return {
      activeStreams: Array.from(this.streams.values()),
      metrics: this.metrics,
      errors: this.errors
    };
  }

  createDebugPanel() {
    if (typeof window === 'undefined') return;

    const panel = document.createElement('div');
    panel.style.cssText = \`
      position: fixed;
      top: 10px;
      right: 10px;
      width: 300px;
      background: rgba(0,0,0,0.9);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      font-size: 12px;
      z-index: 10000;
      max-height: 400px;
      overflow-y: auto;
    \`;

    const updatePanel = () => {
      const info = this.getDebugInfo();
      panel.innerHTML = \`
        <h3>Stream Debugger</h3>
        <div>Active Streams: \${info.activeStreams.length}</div>
        <div>Total Errors: \${info.errors.length}</div>
        <div>
          \${info.activeStreams.map(stream => \`
            <div>
              \${stream.id}: \${stream.status}
              (\${stream.chunks.length} chunks)
            </div>
          \`).join('')}
        </div>
      \`;
    };

    updatePanel();
    setInterval(updatePanel, 1000);
    document.body.appendChild(panel);
  }
}

// 使用调试器
const debugger = new StreamDebugger();

// 在开发环境中启用调试面板
if (process.env.NODE_ENV === 'development') {
  debugger.createDebugPanel();
}

// 包装renderToReadableStream
async function debugRenderToReadableStream(element, options) {
  const stream = await renderToReadableStream(element, options);
  return debugger.trackStream('main-render', stream);
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'troubleshooting',
      title: '🔍 问题排查',
      content: {
        introduction: '系统化的问题排查方法能够帮助开发者快速定位renderToReadableStream的复杂问题。本节提供结构化的排查流程和诊断技巧。',
        sections: [
          {
            title: '系统化排查流程',
            description: '按照优先级和影响范围进行问题排查',
            items: [
              {
                title: '环境兼容性检查',
                description: '首先确认运行环境是否支持renderToReadableStream的所有特性',
                steps: [
                  '检查JavaScript运行时版本和Web Streams API支持',
                  '验证React版本是否为18+',
                  '确认边缘环境的API兼容性',
                  '测试polyfill的有效性'
                ],
                code: `// 环境兼容性检查脚本
function checkEnvironmentCompatibility() {
  const checks = {
    react: typeof React !== 'undefined' && React.version >= '18.0.0',
    readableStream: typeof ReadableStream !== 'undefined',
    webStreams: typeof ReadableStream !== 'undefined' &&
                typeof WritableStream !== 'undefined',
    abortController: typeof AbortController !== 'undefined',
    promise: typeof Promise !== 'undefined'
  };

  console.log('Environment Compatibility Check:', checks);

  const incompatible = Object.entries(checks)
    .filter(([key, supported]) => !supported)
    .map(([key]) => key);

  if (incompatible.length > 0) {
    console.error('Incompatible features:', incompatible);
    return false;
  }

  return true;
}

// 运行时检查
if (!checkEnvironmentCompatibility()) {
  console.warn('Environment may not fully support renderToReadableStream');
}`
              },
              {
                title: '流状态诊断',
                description: '诊断流式传输过程中的状态和数据流问题',
                steps: [
                  '监控流的创建和初始化',
                  '跟踪数据块的传输状态',
                  '检查流的终止条件',
                  '分析错误传播路径'
                ],
                code: `// 流状态诊断工具
class StreamDiagnostics {
  constructor() {
    this.diagnostics = new Map();
  }

  diagnoseStream(stream, id = 'default') {
    const diagnosis = {
      id,
      created: Date.now(),
      state: 'created',
      chunks: [],
      errors: [],
      metrics: {}
    };

    this.diagnostics.set(id, diagnosis);

    // 包装流以监控状态
    const reader = stream.getReader();
    diagnosis.state = 'reading';

    const monitoredStream = new ReadableStream({
      start(controller) {
        this.readAndMonitor(reader, controller, diagnosis);
      },

      cancel(reason) {
        diagnosis.state = 'cancelled';
        diagnosis.cancelReason = reason;
        console.log('Stream cancelled:', { id, reason });
      }
    });

    return monitoredStream;
  }

  async readAndMonitor(reader, controller, diagnosis) {
    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          diagnosis.state = 'complete';
          diagnosis.completed = Date.now();
          diagnosis.metrics.duration = diagnosis.completed - diagnosis.created;
          diagnosis.metrics.totalSize = diagnosis.chunks.reduce(
            (sum, chunk) => sum + chunk.size, 0
          );

          controller.close();
          console.log('Stream diagnosis complete:', diagnosis.metrics);
          break;
        }

        const chunkInfo = {
          index: diagnosis.chunks.length,
          size: value.length,
          timestamp: Date.now()
        };

        diagnosis.chunks.push(chunkInfo);
        controller.enqueue(value);

        console.log('Stream chunk processed:', chunkInfo);
      }
    } catch (error) {
      diagnosis.state = 'error';
      diagnosis.errors.push({
        error: error.message,
        timestamp: Date.now()
      });

      controller.error(error);
      console.error('Stream diagnosis error:', error);
    }
  }

  getDiagnostics(id) {
    return id ? this.diagnostics.get(id) :
                Array.from(this.diagnostics.values());
  }
}

// 使用诊断工具
const diagnostics = new StreamDiagnostics();

async function diagnosedRender(element, options) {
  const stream = await renderToReadableStream(element, options);
  return diagnostics.diagnoseStream(stream, 'main-render');
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;