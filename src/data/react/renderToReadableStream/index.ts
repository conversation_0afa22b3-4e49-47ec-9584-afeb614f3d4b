import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const renderToReadableStreamData: ApiItem = {
  id: 'renderToReadableStream',
  title: 'renderToReadableStream',
  description: 'React中用于Web Streams标准的服务端渲染API，专门为边缘运行时和现代Web环境设计，支持流式HTML传输和Suspense异步渲染。',
  category: 'ReactDOM Server API',
  difficulty: 'hard',

  syntax: `renderToReadableStream(children: ReactNode, options?: RenderToReadableStreamOptions): Promise<ReadableStream<Uint8Array>>`,
  example: `const stream = await renderToReadableStream(<App />, {
  bootstrapScripts: ['/static/js/main.js'],
  signal: controller.signal,
  onError: (error) => console.error(error)
});`,
  notes: '专门用于Web Streams标准和边缘运行时的流式SSR，支持跨平台部署和现代异步API设计。',

  version: 'React 18.0+',
  tags: ["ReactDOM", "Server", "SSR", "Streaming", "Web Streams", "Edge Computing"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default renderToReadableStreamData;