import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-1',
    title: '边缘函数博客渲染',
    description: '使用Cloudflare Workers实现博客文章的边缘SSR，展示renderToReadableStream在边缘计算环境中的基础应用。',
    businessValue: '通过边缘计算实现全球用户的快速内容访问，减少服务器负载，提升用户体验和SEO效果。',
    scenario: `一个技术博客网站需要在全球范围内快速加载文章内容。传统的服务器渲染需要用户请求到达中心服务器，导致远距离用户访问缓慢。

使用Cloudflare Workers部署边缘SSR，可以让用户从最近的边缘节点获取渲染好的HTML，大幅提升加载速度。

技术要求：
- 支持动态路由和文章内容渲染
- 实现SEO友好的HTML输出
- 处理错误和降级方案
- 保持与传统SSR的兼容性`,
    code: `// Cloudflare Workers 边缘函数
import { renderToReadableStream } from 'react-dom/server';
import BlogPost from './components/BlogPost';

export default {
  async fetch(request, env, ctx) {
    try {
      // 解析文章ID
      const url = new URL(request.url);
      const postId = url.pathname.split('/').pop();

      // 从KV存储获取文章数据
      const postData = await env.BLOG_KV.get('post:' + postId, 'json');

      if (!postData) {
        return new Response('文章未找到', { status: 404 });
      }

      // 渲染React组件为流
      const stream = await renderToReadableStream(
        <BlogPost
          title={postData.title}
          content={postData.content}
          author={postData.author}
          publishDate={postData.publishDate}
        />,
        {
          // 客户端水合脚本
          bootstrapScripts: ['/static/js/blog.js'],

          // 请求取消控制
          signal: request.signal,

          // 错误处理
          onError(error) {
            console.error('边缘SSR错误:', error);
            // 发送到错误监控服务
            ctx.waitUntil(
              fetch(env.ERROR_WEBHOOK, {
                method: 'POST',
                body: JSON.stringify({
                  error: error.message,
                  postId,
                  timestamp: new Date().toISOString()
                })
              })
            );
          }
        }
      );

      // 返回流式HTML响应
      return new Response(stream, {
        status: 200,
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
          'Cache-Control': 'public, max-age=3600',
          'X-Rendered-By': 'Cloudflare-Workers'
        }
      });

    } catch (error) {
      // 降级到静态HTML
      return new Response(
        '<html><body><h1>服务暂时不可用</h1><p>请稍后重试</p></body></html>',
        {
          status: 500,
          headers: { 'Content-Type': 'text/html' }
        }
      );
    }
  }
};`,
    explanation: '这个示例展示了renderToReadableStream在边缘计算环境中的基础应用。通过Cloudflare Workers，我们可以在全球边缘节点上运行React SSR，实现就近渲染和快速响应。关键技术点包括Web Streams API的使用、错误处理机制、以及与边缘存储的集成。',
    benefits: [
      '全球边缘节点部署，用户访问速度提升60-80%',
      '减少中心服务器负载，支持更高并发访问',
      'SEO友好的HTML输出，搜索引擎收录效果更好'
    ],
    metrics: {
      performance: 'TTFB从800ms降低到150ms，FCP提升65%',
      userExperience: '全球用户平均加载时间减少70%，跳出率降低25%',
      technicalMetrics: '边缘缓存命中率95%，服务器资源使用减少40%'
    },
    difficulty: 'easy',
    tags: ['边缘计算', 'Cloudflare Workers', 'SSR', '性能优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-2',
    title: '电商产品页面流式渲染',
    description: '构建支持Suspense的电商产品页面，实现数据分层加载和流式传输，优化大型页面的首屏性能。',
    businessValue: '通过流式渲染技术，让用户能够更早看到页面内容，提升购物体验和转化率，同时支持复杂的数据依赖关系。',
    scenario: `一个电商平台的产品详情页包含多个数据源：基础产品信息、库存状态、用户评论、推荐商品等。传统SSR需要等待所有数据加载完成才能返回页面，导致首屏时间过长。

使用renderToReadableStream配合Suspense，可以实现分层渲染：
1. 首先渲染页面骨架和基础产品信息
2. 异步加载库存、评论等次要信息
3. 流式传输更新内容到客户端

技术要求：
- 支持多数据源的异步加载
- 实现Suspense边界和fallback UI
- 处理数据加载失败的降级方案
- 优化SEO和社交媒体分享`,
    code: `// Deno Deploy 边缘函数
import { renderToReadableStream } from 'react-dom/server';
import { Suspense } from 'react';
import ProductPage from './components/ProductPage';
import ProductSkeleton from './components/ProductSkeleton';

// 产品数据获取函数
async function fetchProductData(productId: string) {
  const [basicInfo, inventory] = await Promise.all([
    fetch('https://api.shop.com/products/' + productId).then(r => r.json()),
    fetch('https://api.shop.com/inventory/' + productId).then(r => r.json())
  ]);

  return { basicInfo, inventory };
}

// 异步组件：用户评论
function ProductReviews({ productId }: { productId: string }) {
  // 这里会触发Suspense
  const reviews = use(fetch('https://api.shop.com/reviews/' + productId));

  return (
    <div className="reviews-section">
      <h3>用户评价</h3>
      {reviews.map(review => (
        <div key={review.id} className="review-item">
          <div className="rating">{'★'.repeat(review.rating)}</div>
          <p>{review.content}</p>
          <span className="author">- {review.author}</span>
        </div>
      ))}
    </div>
  );
}

// 异步组件：推荐商品
function RecommendedProducts({ productId }: { productId: string }) {
  const recommendations = use(
    fetch('https://api.shop.com/recommendations/' + productId)
  );

  return (
    <div className="recommendations">
      <h3>推荐商品</h3>
      <div className="product-grid">
        {recommendations.map(product => (
          <div key={product.id} className="product-card">
            <img src={product.image} alt={product.name} />
            <h4>{product.name}</h4>
            <span className="price">¥{product.price}</span>
          </div>
        ))}
      </div>
    </div>
  );
}

export default async function handler(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const productId = url.pathname.split('/').pop();

  if (!productId) {
    return new Response('产品ID无效', { status: 400 });
  }

  try {
    // 获取关键产品数据（阻塞渲染）
    const productData = await fetchProductData(productId);

    // 创建取消控制器
    const controller = new AbortController();

    // 设置超时
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    // 渲染包含Suspense的页面
    const stream = await renderToReadableStream(
      <html>
        <head>
          <title>{productData.basicInfo.name} - 商城</title>
          <meta name="description" content={productData.basicInfo.description} />
          <meta property="og:title" content={productData.basicInfo.name} />
          <meta property="og:image" content={productData.basicInfo.image} />
        </head>
        <body>
          <div id="root">
            <ProductPage
              product={productData.basicInfo}
              inventory={productData.inventory}
            />

            {/* 异步加载的评论区域 */}
            <Suspense fallback={<div className="loading">加载评论中...</div>}>
              <ProductReviews productId={productId} />
            </Suspense>

            {/* 异步加载的推荐商品 */}
            <Suspense fallback={<div className="loading">加载推荐商品...</div>}>
              <RecommendedProducts productId={productId} />
            </Suspense>
          </div>
        </body>
      </html>,
      {
        bootstrapScripts: ['/static/js/product.js'],
        signal: controller.signal,

        onError(error) {
          console.error('产品页面渲染错误:', error);
          clearTimeout(timeoutId);
        }
      }
    );

    clearTimeout(timeoutId);

    return new Response(stream, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'public, max-age=300, stale-while-revalidate=3600',
        'X-Product-ID': productId
      }
    });

  } catch (error) {
    return new Response(
      '<html><body><h1>产品加载失败</h1><p>请刷新页面重试</p></body></html>',
      {
        status: 500,
        headers: { 'Content-Type': 'text/html' }
      }
    );
  }
}`,
    explanation: '这个中级场景展示了renderToReadableStream与Suspense的深度集成。通过分层数据加载策略，关键内容（产品基础信息）优先渲染并传输给用户，而次要内容（评论、推荐）通过Suspense异步加载。这种方式显著改善了大型页面的首屏性能，同时保持了良好的SEO效果。',
    benefits: [
      '首屏内容提前展示，用户感知加载时间减少50%',
      'SEO关键信息优先渲染，搜索引擎抓取效果更好',
      '支持复杂数据依赖，提升开发效率和代码可维护性',
      '异步加载失败不影响主要内容展示，提升系统稳定性'
    ],
    metrics: {
      performance: 'LCP从3.2s优化到1.8s，FID保持在100ms以下',
      userExperience: '页面跳出率降低35%，商品转化率提升18%',
      technicalMetrics: 'Suspense组件缓存命中率85%，API调用并发度提升3倍'
    },
    difficulty: 'medium',
    tags: ['电商', 'Suspense', '流式渲染', '性能优化', 'SEO']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-3',
    title: '多环境统一SSR架构',
    description: '构建支持Node.js、Deno、Cloudflare Workers等多环境的统一SSR解决方案，实现一套代码多平台部署。',
    businessValue: '通过统一的SSR架构，企业可以根据不同需求选择最适合的部署环境，降低维护成本，提升系统灵活性和可扩展性。',
    scenario: `大型企业应用需要支持多种部署环境：
- 开发环境：Node.js本地开发服务器
- 测试环境：Deno Deploy边缘测试
- 生产环境：Cloudflare Workers全球分发
- 备用环境：传统服务器Node.js部署

挑战在于不同环境的API差异、性能特性和限制条件各不相同。需要设计一套统一的抽象层，让同一套React代码能够在所有环境中正常运行。

技术要求：
- 抽象不同环境的API差异
- 统一的错误处理和监控
- 环境特定的性能优化
- 自动化的多环境部署流程
- 完整的类型安全保障`,
    code: `// 统一SSR抽象层
interface SSREnvironment {
  name: string;
  renderToStream: (element: ReactNode, options?: any) => Promise<ReadableStream>;
  createResponse: (stream: ReadableStream, init?: ResponseInit) => Response;
  handleError: (error: Error, context: any) => void;
}

// Cloudflare Workers环境适配器
class CloudflareWorkerEnvironment implements SSREnvironment {
  name = 'cloudflare-workers';

  async renderToStream(element: ReactNode, options = {}) {
    return await renderToReadableStream(element, {
      ...options,
      // Workers特定优化
      progressiveChunkSize: 8192,
    });
  }

  createResponse(stream: ReadableStream, init: ResponseInit = {}) {
    return new Response(stream, {
      ...init,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'X-Rendered-By': 'Cloudflare-Workers',
        ...init.headers
      }
    });
  }

  handleError(error: Error, context: any) {
    // Workers错误处理
    console.error('CF Workers SSR Error:', error);
    context.waitUntil?.(
      fetch(context.env?.ERROR_WEBHOOK, {
        method: 'POST',
        body: JSON.stringify({
          error: error.message,
          stack: error.stack,
          environment: 'cloudflare-workers',
          timestamp: Date.now()
        })
      })
    );
  }
}

// Deno环境适配器
class DenoEnvironment implements SSREnvironment {
  name = 'deno';

  async renderToStream(element: ReactNode, options = {}) {
    return await renderToReadableStream(element, {
      ...options,
      // Deno特定优化
      identifierPrefix: 'deno-',
    });
  }

  createResponse(stream: ReadableStream, init: ResponseInit = {}) {
    return new Response(stream, {
      ...init,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'X-Rendered-By': 'Deno-Deploy',
        ...init.headers
      }
    });
  }

  handleError(error: Error, context: any) {
    console.error('Deno SSR Error:', error);
    // Deno特定的错误上报
  }
}

// Node.js环境适配器（使用renderToPipeableStream）
class NodeEnvironment implements SSREnvironment {
  name = 'nodejs';

  async renderToStream(element: ReactNode, options = {}) {
    // Node.js环境转换为Web Streams
    const { renderToPipeableStream } = await import('react-dom/server');

    return new Promise<ReadableStream>((resolve, reject) => {
      let resolved = false;

      const { pipe, abort } = renderToPipeableStream(element, {
        ...options,
        onShellReady() {
          if (resolved) return;
          resolved = true;

          // 将Node.js流转换为Web Streams
          const readable = new ReadableStream({
            start(controller) {
              const nodeStream = new PassThrough();
              pipe(nodeStream);

              nodeStream.on('data', (chunk) => {
                controller.enqueue(new Uint8Array(chunk));
              });

              nodeStream.on('end', () => {
                controller.close();
              });

              nodeStream.on('error', (err) => {
                controller.error(err);
              });
            }
          });

          resolve(readable);
        },
        onShellError(error) {
          if (!resolved) {
            resolved = true;
            reject(error);
          }
        }
      });
    });
  }

  createResponse(stream: ReadableStream, init: ResponseInit = {}) {
    return new Response(stream, {
      ...init,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'X-Rendered-By': 'Node.js',
        ...init.headers
      }
    });
  }

  handleError(error: Error, context: any) {
    console.error('Node.js SSR Error:', error);
    // Node.js特定的错误处理
  }
}

// 环境检测和适配器工厂
function createSSREnvironment(): SSREnvironment {
  // Cloudflare Workers检测
  if (typeof caches !== 'undefined' && typeof EdgeRuntime !== 'undefined') {
    return new CloudflareWorkerEnvironment();
  }

  // Deno检测
  if (typeof Deno !== 'undefined') {
    return new DenoEnvironment();
  }

  // Node.js检测
  if (typeof process !== 'undefined' && process.versions?.node) {
    return new NodeEnvironment();
  }

  throw new Error('不支持的运行环境');
}

// 统一的SSR处理函数
export async function universalSSRHandler(
  request: Request,
  getAppElement: (props: any) => ReactNode,
  options: {
    getProps?: (request: Request) => Promise<any>;
    onError?: (error: Error, env: SSREnvironment) => void;
  } = {}
): Promise<Response> {
  const env = createSSREnvironment();

  try {
    // 获取应用属性
    const props = options.getProps ? await options.getProps(request) : {};

    // 创建应用元素
    const appElement = getAppElement(props);

    // 渲染为流
    const stream = await env.renderToStream(appElement, {
      bootstrapScripts: ['/static/js/app.js'],
      signal: request.signal,
      onError: (error) => {
        env.handleError(error, { request, env });
        options.onError?.(error, env);
      }
    });

    // 创建响应
    return env.createResponse(stream, {
      status: 200,
      headers: {
        'Cache-Control': 'public, max-age=300',
        'Vary': 'Accept-Encoding'
      }
    });

  } catch (error) {
    env.handleError(error as Error, { request, env });

    return env.createResponse(
      new ReadableStream({
        start(controller) {
          const errorHtml = '<html><body><h1>服务暂时不可用</h1></body></html>';
          controller.enqueue(new TextEncoder().encode(errorHtml));
          controller.close();
        }
      }),
      { status: 500 }
    );
  }
}

// 使用示例
export default async function handler(request: Request) {
  return universalSSRHandler(
    request,
    (props) => <App {...props} />,
    {
      async getProps(request) {
        const url = new URL(request.url);
        return {
          path: url.pathname,
          query: Object.fromEntries(url.searchParams)
        };
      },
      onError(error, env) {
        console.log('SSR错误在环境:', env.name, error.message);
      }
    }
  );
}`,
    explanation: '这个高级场景展示了如何构建跨环境的统一SSR架构。通过抽象层设计，同一套React代码可以在不同的运行时环境中无缝运行。关键技术包括环境检测、API适配、错误处理统一化、以及性能优化的环境特定实现。这种架构为企业级应用提供了极大的部署灵活性。',
    benefits: [
      '一套代码支持多环境部署，开发维护成本降低60%',
      '根据业务需求灵活选择最优部署环境，性能提升显著',
      '统一的错误处理和监控，运维效率提升40%',
      '环境特定优化，各平台性能都能达到最佳状态',
      '完整的类型安全保障，减少运行时错误90%'
    ],
    metrics: {
      performance: '各环境性能优化：CF Workers TTFB 120ms，Deno 180ms，Node.js 200ms',
      userExperience: '全球用户体验一致性提升，可用性达到99.9%',
      technicalMetrics: '部署效率提升5倍，环境切换时间从2小时缩短到15分钟'
    },
    difficulty: 'hard',
    tags: ['多环境部署', '架构设计', '抽象层', '企业级', '跨平台']
  }
];

export default businessScenarios;