import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-1',
    question: 'renderToReadableStream在Cloudflare Workers中报错"ReadableStream is not defined"怎么解决？',
    answer: `这个错误通常出现在较老版本的Cloudflare Workers运行时中，或者在本地开发环境中缺少Web Streams polyfill。

**解决方案**：
1. **更新Workers运行时**：确保使用最新的Cloudflare Workers运行时，它原生支持Web Streams API
2. **检查兼容性日期**：在wrangler.toml中设置compatibility_date为2022-03-21或更新版本
3. **本地开发环境**：使用Miniflare 2.0+或wrangler dev进行本地测试
4. **Polyfill方案**：如果必须支持旧环境，可以添加web-streams-polyfill

**预防措施**：
- 始终在目标环境中测试代码
- 使用最新的开发工具和运行时
- 在CI/CD中包含边缘环境测试`,
    code: `// ❌ 错误：在不支持的环境中直接使用
const stream = await renderToReadableStream(<App />);

// ✅ 正确：检查环境支持
if (typeof ReadableStream === 'undefined') {
  // 降级到字符串渲染或抛出友好错误
  throw new Error('当前环境不支持流式渲染');
}

// wrangler.toml 配置
[env.production]
compatibility_date = "2022-03-21"
compatibility_flags = ["streams_enable_constructors"]

// 本地开发时的polyfill
if (typeof ReadableStream === 'undefined') {
  const { ReadableStream } = await import('web-streams-polyfill');
  globalThis.ReadableStream = ReadableStream;
}`,
    tags: ['Cloudflare Workers', '环境兼容性', 'Web Streams', '错误处理'],
    relatedQuestions: ['如何在不同边缘环境中测试renderToReadableStream？', '如何处理renderToReadableStream的运行时错误？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-2',
    question: 'renderToReadableStream渲染的页面在客户端水合时出现hydration mismatch错误？',
    answer: `Hydration mismatch是SSR中最常见的问题之一，在renderToReadableStream中通常由以下原因引起：

**常见原因**：
1. **服务端和客户端状态不一致**：时间戳、随机数、用户特定数据
2. **Suspense边界处理不当**：服务端和客户端的异步数据加载时机不同
3. **环境差异**：浏览器API在服务端不可用
4. **条件渲染逻辑**：基于客户端特性的条件渲染

**解决策略**：
1. **使用useEffect处理客户端特定逻辑**：将客户端专用代码移到useEffect中
2. **统一数据源**：确保服务端和客户端使用相同的初始数据
3. **正确处理Suspense**：使用一致的fallback UI和数据获取策略
4. **环境检测**：使用typeof window !== 'undefined'进行环境判断

**调试技巧**：
- 开启React的hydration警告
- 使用React DevTools检查组件树差异
- 在开发环境中启用严格模式`,
    code: `// ❌ 错误：服务端和客户端不一致
function TimeComponent() {
  const now = new Date().toISOString(); // 每次渲染都不同
  return <div>当前时间: {now}</div>;
}

// ✅ 正确：使用useEffect处理客户端更新
function TimeComponent({ initialTime }) {
  const [time, setTime] = useState(initialTime);

  useEffect(() => {
    // 客户端水合后再更新时间
    setTime(new Date().toISOString());
  }, []);

  return <div>当前时间: {time}</div>;
}

// ❌ 错误：条件渲染基于客户端API
function UserAgent() {
  const isMobile = navigator.userAgent.includes('Mobile');
  return <div>{isMobile ? '移动端' : '桌面端'}</div>;
}

// ✅ 正确：延迟客户端特定渲染
function UserAgent() {
  const [userAgent, setUserAgent] = useState('未知');

  useEffect(() => {
    setUserAgent(navigator.userAgent.includes('Mobile') ? '移动端' : '桌面端');
  }, []);

  return <div>{userAgent}</div>;
}

// 服务端渲染时传递初始数据
const stream = await renderToReadableStream(
  <App initialTime={new Date().toISOString()} />,
  { bootstrapScripts: ['/static/js/main.js'] }
);`,
    tags: ['Hydration', 'SSR', '客户端水合', '状态同步'],
    relatedQuestions: ['如何调试renderToReadableStream的hydration问题？', '如何在SSR中正确处理客户端专用代码？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-3',
    question: 'renderToReadableStream的性能比renderToPipeableStream差很多，如何优化？',
    answer: `这是一个常见的误解。renderToReadableStream在某些场景下确实可能比renderToPipeableStream慢，但这通常是配置或使用方式的问题，而非API本身的限制。

**性能差异的真实原因**：
1. **平台优化差异**：renderToPipeableStream针对Node.js深度优化，而renderToReadableStream追求跨平台兼容
2. **背压控制**：Node.js流有更精细的背压控制机制
3. **内存管理**：不同运行时的垃圾回收策略差异
4. **网络传输**：边缘节点的网络延迟可能影响感知性能

**优化策略**：
1. **选择合适的环境**：在边缘计算场景下，renderToReadableStream通常更快
2. **优化组件结构**：减少深层嵌套，使用React.memo优化重渲染
3. **合理使用Suspense**：避免过多的Suspense边界
4. **缓存策略**：在边缘节点实现智能缓存
5. **分块大小调优**：调整progressiveChunkSize参数

**基准测试建议**：
- 在实际部署环境中测试，而非本地开发环境
- 考虑全链路性能，包括CDN和网络延迟
- 测试不同负载下的表现`,
    code: `// 性能优化配置
const stream = await renderToReadableStream(<App />, {
  // 调整分块大小以优化传输
  progressiveChunkSize: 8192, // 8KB chunks

  // 预加载关键资源
  bootstrapScripts: ['/static/js/main.js'],
  bootstrapModules: ['/static/js/app.mjs'],

  // 优化错误处理
  onError(error) {
    // 快速错误处理，避免阻塞渲染
    console.error('SSR Error:', error);
  }
});

// 边缘缓存优化
const cacheKey = 'page:' + url.pathname;
let cachedResponse = await cache.get(cacheKey);

if (!cachedResponse) {
  const stream = await renderToReadableStream(<App />);

  // 使用tee()分流：一个用于响应，一个用于缓存
  const [responseStream, cacheStream] = stream.tee();

  // 异步缓存，不阻塞响应
  event.waitUntil(
    cacheStream.pipeTo(cache.put(cacheKey))
  );

  return new Response(responseStream);
}

// 组件级优化
const OptimizedComponent = React.memo(function MyComponent({ data }) {
  // 使用useMemo缓存昂贵计算
  const processedData = useMemo(() => {
    return expensiveDataProcessing(data);
  }, [data]);

  return <div>{processedData}</div>;
});`,
    tags: ['性能优化', '基准测试', '缓存策略', '边缘计算'],
    relatedQuestions: ['如何正确测试renderToReadableStream的性能？', '在什么场景下应该选择renderToReadableStream而非renderToPipeableStream？']
  },

  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-4',
    question: 'renderToReadableStream中的Suspense组件一直显示loading状态，不会更新？',
    answer: `这是流式SSR中的一个常见问题，通常由异步数据获取的实现方式不当引起。

**问题根源**：
1. **数据获取方式错误**：没有正确实现Suspense兼容的数据获取
2. **Promise状态管理**：Promise没有正确resolve或reject
3. **缓存机制缺失**：重复的数据请求导致无限loading
4. **错误边界缺失**：数据获取错误被静默忽略

**解决方案**：
1. **使用正确的数据获取模式**：实现符合Suspense规范的数据获取
2. **添加错误边界**：捕获和处理数据获取错误
3. **实现请求缓存**：避免重复请求相同数据
4. **设置超时机制**：防止无限等待

**调试技巧**：
- 检查网络请求是否正常完成
- 使用React DevTools查看Suspense状态
- 添加日志跟踪Promise状态变化
- 在开发环境中模拟网络延迟`,
    code: `// ❌ 错误：直接在组件中发起异步请求
function UserProfile({ userId }) {
  const [user, setUser] = useState(null);

  useEffect(() => {
    fetchUser(userId).then(setUser); // 这不会触发Suspense
  }, [userId]);

  if (!user) return <div>Loading...</div>; // 手动loading状态
  return <div>{user.name}</div>;
}

// ✅ 正确：使用Suspense兼容的数据获取
const userCache = new Map();

function fetchUserSuspense(userId) {
  if (userCache.has(userId)) {
    return userCache.get(userId);
  }

  const promise = fetch('/api/users/' + userId)
    .then(res => res.json())
    .then(user => {
      userCache.set(userId, user);
      return user;
    })
    .catch(error => {
      userCache.delete(userId); // 清除失败的缓存
      throw error;
    });

  userCache.set(userId, promise);
  throw promise; // 抛出Promise触发Suspense
}

function UserProfile({ userId }) {
  const user = fetchUserSuspense(userId); // 会触发Suspense
  return <div>{user.name}</div>;
}

// 使用错误边界和超时
function App() {
  return (
    <ErrorBoundary fallback={<div>加载用户信息失败</div>}>
      <Suspense fallback={<div>加载用户信息中...</div>}>
        <UserProfile userId="123" />
      </Suspense>
    </ErrorBoundary>
  );
}

// 带超时的数据获取
function fetchWithTimeout(url, timeout = 5000) {
  return Promise.race([
    fetch(url),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error('请求超时')), timeout)
    )
  ]);
}`,
    tags: ['Suspense', '数据获取', '异步渲染', '错误处理'],
    relatedQuestions: ['如何实现Suspense兼容的数据获取？', '如何调试Suspense组件的渲染问题？']
  },

  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-5',
    question: 'renderToReadableStream在生产环境中频繁出现内存泄漏，如何排查和解决？',
    answer: `内存泄漏是边缘计算环境中的严重问题，需要系统性的排查和解决。

**常见内存泄漏原因**：
1. **流未正确关闭**：ReadableStream没有被正确消费或关闭
2. **事件监听器未清理**：组件卸载时没有移除事件监听器
3. **定时器未清理**：setTimeout/setInterval没有被清除
4. **闭包引用**：长期存在的闭包持有大对象引用
5. **缓存无限增长**：没有LRU机制的缓存导致内存持续增长

**排查步骤**：
1. **监控内存使用**：使用performance.memory API监控
2. **分析堆快照**：在支持的环境中生成堆快照
3. **追踪对象生命周期**：添加对象创建和销毁日志
4. **压力测试**：模拟高并发场景测试内存表现

**解决方案**：
1. **正确处理流**：确保ReadableStream被完全消费
2. **实现清理机制**：在组件卸载时清理资源
3. **使用WeakMap/WeakSet**：避免强引用导致的内存泄漏
4. **限制缓存大小**：实现LRU缓存机制
5. **定期垃圾回收**：在适当时机手动触发GC`,
    code: `// ❌ 错误：可能导致内存泄漏的代码
const globalCache = new Map(); // 无限增长的缓存

function leakyComponent() {
  const timer = setInterval(() => {
    // 定时器没有清理
    console.log('tick');
  }, 1000);

  const stream = await renderToReadableStream(<App />);
  // 流没有被正确处理
  return stream;
}

// ✅ 正确：防止内存泄漏的实现
class LRUCache {
  constructor(maxSize = 100) {
    this.maxSize = maxSize;
    this.cache = new Map();
  }

  get(key) {
    if (this.cache.has(key)) {
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value); // 移到最后
      return value;
    }
  }

  set(key, value) {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }
}

const cache = new LRUCache(50);

// 正确的资源管理
export default {
  async fetch(request, env, ctx) {
    const controller = new AbortController();

    // 设置超时自动清理
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, 10000);

    try {
      const stream = await renderToReadableStream(<App />, {
        signal: controller.signal,
        onError(error) {
          console.error('Render error:', error);
          // 清理资源
          clearTimeout(timeoutId);
        }
      });

      // 确保流被完全消费
      const response = new Response(stream, {
        headers: { 'Content-Type': 'text/html' }
      });

      // 监控内存使用
      if (performance.memory) {
        const memUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
        if (memUsage > 100) { // 超过100MB警告
          console.warn('High memory usage:', memUsage + 'MB');
        }
      }

      return response;

    } finally {
      clearTimeout(timeoutId);
    }
  }
};

// 组件中的资源清理
function MyComponent() {
  useEffect(() => {
    const timer = setInterval(() => {
      // 一些定时任务
    }, 1000);

    return () => {
      clearInterval(timer); // 清理定时器
    };
  }, []);

  return <div>Component content</div>;
}`,
    tags: ['内存泄漏', '资源管理', '性能监控', '边缘计算'],
    relatedQuestions: ['如何监控renderToReadableStream的内存使用？', '如何在边缘环境中实现有效的缓存策略？']
  }
];

export default commonQuestions;