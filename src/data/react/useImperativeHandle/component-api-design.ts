import { AdvancedTopic } from '../../../types/api';

const componentApiDesign: AdvancedTopic = {
  id: 'component-api-design',
  title: '组件API设计模式',
  description: '深入探讨useImperativeHandle在组件库设计中的高级应用模式',
  content: `## 企业级组件API设计的系统性方法

useImperativeHandle不仅是一个Hook，更是构建企业级组件库的核心工具。通过系统性的API设计模式，可以创建既灵活又稳定的组件接口。

### 设计原则体系

#### 1. 分层API架构
将API按复杂度和使用频率分层：
- **核心层**：最常用的基础操作
- **扩展层**：高级功能和配置
- **插件层**：第三方扩展接口

#### 2. 一致性设计规范
- 统一的命名约定
- 标准化的参数传递
- 一致的错误处理
- 统一的类型定义

#### 3. 向前兼容策略
- 版本化的API设计
- 渐进式功能增强
- 废弃警告机制
- 平滑迁移路径`,

  examples: [
    {
      title: '企业级表单组件设计',
      description: '构建支持复杂验证、多种输入类型、国际化的表单组件系统',
      code: `// 1. 核心类型定义
interface BaseFormItemAPI {
  focus(): void;
  blur(): void;
  clear(): void;
  validate(): Promise<boolean>;
  getValue(): any;
  setValue(value: any): void;
  reset(): void;
}

interface ExtendedFormItemAPI extends BaseFormItemAPI {
  // 高级验证
  validateWithDetails(): Promise<ValidationResult>;
  setValidator(validator: Validator): void;
  
  // 状态管理
  setLoading(loading: boolean): void;
  setDisabled(disabled: boolean): void;
  
  // 国际化
  setLocale(locale: string): void;
  updateMessages(messages: Record<string, string>): void;
}

// 2. 基础表单项工厂
function createFormItem<T = string>() {
  return forwardRef<ExtendedFormItemAPI, FormItemProps<T>>((props, ref) => {
    const [value, setValue] = useState<T>(props.defaultValue);
    const [errors, setErrors] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);
    const [disabled, setDisabled] = useState(false);
    
    const validators = useRef<Validator[]>([]);
    const locale = useRef(props.locale || 'zh-CN');
    
    useImperativeHandle(ref, () => ({
      // 核心API
      focus: () => inputRef.current?.focus(),
      blur: () => inputRef.current?.blur(),
      clear: () => {
        setValue(props.defaultValue);
        setErrors([]);
      },
      
      validate: async () => {
        const results = await Promise.all(
          validators.current.map(v => v.validate(value))
        );
        const newErrors = results.filter(r => !r.valid).map(r => r.message);
        setErrors(newErrors);
        return newErrors.length === 0;
      },
      
      getValue: () => value,
      setValue: (newValue: T) => setValue(newValue),
      reset: () => {
        setValue(props.defaultValue);
        setErrors([]);
        setLoading(false);
        setDisabled(false);
      },
      
      // 扩展API
      validateWithDetails: async () => ({
        valid: await this.validate(),
        errors,
        value,
        fieldName: props.name
      }),
      
      setValidator: (validator: Validator) => {
        validators.current = [...validators.current, validator];
      },
      
      setLoading: (isLoading: boolean) => setLoading(isLoading),
      setDisabled: (isDisabled: boolean) => setDisabled(isDisabled),
      
      setLocale: (newLocale: string) => {
        locale.current = newLocale;
      },
      
      updateMessages: (messages: Record<string, string>) => {
        // 更新错误消息的国际化
      }
    }), [value, errors, loading, disabled]);
    
    return (
      <div className="form-item">
        <label>{props.label}</label>
        <input
          ref={inputRef}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          disabled={disabled}
        />
        {loading && <Spinner />}
        {errors.map(error => <ErrorMessage key={error} message={error} />)}
      </div>
    );
  });
}

// 3. 特化组件
const TextInput = createFormItem<string>();
const NumberInput = createFormItem<number>();
const DateInput = createFormItem<Date>();

// 4. 复合表单组件
const FormGroup = forwardRef<FormGroupAPI, FormGroupProps>((props, ref) => {
  const formItemRefs = useRef<Map<string, ExtendedFormItemAPI>>(new Map());
  
  useImperativeHandle(ref, () => ({
    validateAll: async () => {
      const results = await Promise.all(
        Array.from(formItemRefs.current.values()).map(ref => ref.validate())
      );
      return results.every(valid => valid);
    },
    
    getValues: () => {
      const values = {};
      formItemRefs.current.forEach((ref, name) => {
        values[name] = ref.getValue();
      });
      return values;
    },
    
    setValues: (values: Record<string, any>) => {
      Object.entries(values).forEach(([name, value]) => {
        formItemRefs.current.get(name)?.setValue(value);
      });
    },
    
    resetAll: () => {
      formItemRefs.current.forEach(ref => ref.reset());
    },
    
    focusFirst: () => {
      const firstRef = Array.from(formItemRefs.current.values())[0];
      firstRef?.focus();
    },
    
    focusError: () => {
      // 聚焦到第一个有错误的字段
      for (const ref of formItemRefs.current.values()) {
        if (!(await ref.validate())) {
          ref.focus();
          break;
        }
      }
    }
  }), []);
  
  return (
    <div className="form-group">
      {props.children}
    </div>
  );
});`
    },
    
    {
      title: '可视化图表组件架构',
      description: '设计支持多种图表类型、交互操作、数据更新的通用图表组件',
      code: `// 1. 图表API分层设计
interface BaseChartAPI {
  // 数据操作
  updateData(data: any[]): void;
  getData(): any[];
  clearData(): void;
  
  // 渲染控制
  render(): void;
  resize(): void;
  destroy(): void;
}

interface InteractiveChartAPI extends BaseChartAPI {
  // 交互控制
  enableZoom(enable: boolean): void;
  enablePan(enable: boolean): void;
  enableTooltip(enable: boolean): void;
  
  // 选择操作
  selectDataPoints(indices: number[]): void;
  getSelectedPoints(): number[];
  clearSelection(): void;
  
  // 视图控制
  zoomToFit(): void;
  zoomToSelection(): void;
  resetZoom(): void;
}

interface AdvancedChartAPI extends InteractiveChartAPI {
  // 动画控制
  animate(config: AnimationConfig): Promise<void>;
  pauseAnimation(): void;
  resumeAnimation(): void;
  
  // 导出功能
  exportImage(format: 'png' | 'jpg' | 'svg'): Promise<string>;
  exportData(format: 'json' | 'csv' | 'excel'): Promise<Blob>;
  
  // 插件系统
  registerPlugin(plugin: ChartPlugin): void;
  unregisterPlugin(name: string): void;
  getPlugin(name: string): ChartPlugin | null;
}

// 2. 通用图表组件
const Chart = forwardRef<AdvancedChartAPI, ChartProps>((props, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<any>(null);
  const [data, setData] = useState(props.data);
  const [selectedPoints, setSelectedPoints] = useState<number[]>([]);
  const [plugins, setPlugins] = useState<Map<string, ChartPlugin>>(new Map());
  
  useEffect(() => {
    // 初始化图表实例
    if (containerRef.current) {
      chartInstance.current = createChartInstance(props.type, containerRef.current);
    }
    
    return () => {
      chartInstance.current?.destroy();
    };
  }, []);
  
  useImperativeHandle(ref, () => ({
    // 基础API
    updateData: (newData: any[]) => {
      setData(newData);
      chartInstance.current?.updateData(newData);
    },
    
    getData: () => data,
    
    clearData: () => {
      setData([]);
      chartInstance.current?.clearData();
    },
    
    render: () => {
      chartInstance.current?.render();
    },
    
    resize: () => {
      chartInstance.current?.resize();
    },
    
    destroy: () => {
      chartInstance.current?.destroy();
    },
    
    // 交互API
    enableZoom: (enable: boolean) => {
      chartInstance.current?.setOption('zoom.enabled', enable);
    },
    
    enablePan: (enable: boolean) => {
      chartInstance.current?.setOption('pan.enabled', enable);
    },
    
    enableTooltip: (enable: boolean) => {
      chartInstance.current?.setOption('tooltip.enabled', enable);
    },
    
    selectDataPoints: (indices: number[]) => {
      setSelectedPoints(indices);
      chartInstance.current?.selectPoints(indices);
    },
    
    getSelectedPoints: () => selectedPoints,
    
    clearSelection: () => {
      setSelectedPoints([]);
      chartInstance.current?.clearSelection();
    },
    
    zoomToFit: () => {
      chartInstance.current?.zoomToFit();
    },
    
    zoomToSelection: () => {
      if (selectedPoints.length > 0) {
        chartInstance.current?.zoomToPoints(selectedPoints);
      }
    },
    
    resetZoom: () => {
      chartInstance.current?.resetZoom();
    },
    
    // 高级API
    animate: async (config: AnimationConfig) => {
      return new Promise((resolve) => {
        chartInstance.current?.animate(config, resolve);
      });
    },
    
    pauseAnimation: () => {
      chartInstance.current?.pauseAnimation();
    },
    
    resumeAnimation: () => {
      chartInstance.current?.resumeAnimation();
    },
    
    exportImage: async (format: 'png' | 'jpg' | 'svg') => {
      return chartInstance.current?.exportImage(format);
    },
    
    exportData: async (format: 'json' | 'csv' | 'excel') => {
      return chartInstance.current?.exportData(format, data);
    },
    
    // 插件API
    registerPlugin: (plugin: ChartPlugin) => {
      setPlugins(prev => new Map(prev).set(plugin.name, plugin));
      chartInstance.current?.registerPlugin(plugin);
    },
    
    unregisterPlugin: (name: string) => {
      setPlugins(prev => {
        const newMap = new Map(prev);
        newMap.delete(name);
        return newMap;
      });
      chartInstance.current?.unregisterPlugin(name);
    },
    
    getPlugin: (name: string) => {
      return plugins.get(name) || null;
    }
  }), [data, selectedPoints, plugins]);
  
  return (
    <div className="chart-container">
      <div ref={containerRef} className="chart" />
      {props.showToolbar && (
        <ChartToolbar chartRef={ref} />
      )}
    </div>
  );
});

// 3. 专门化图表组件
const LineChart = forwardRef<AdvancedChartAPI, LineChartProps>((props, ref) => {
  return <Chart ref={ref} type="line" {...props} />;
});

const BarChart = forwardRef<AdvancedChartAPI, BarChartProps>((props, ref) => {
  return <Chart ref={ref} type="bar" {...props} />;
});

// 4. 图表仪表板组件
const ChartDashboard = forwardRef<DashboardAPI, DashboardProps>((props, ref) => {
  const chartRefs = useRef<Map<string, AdvancedChartAPI>>(new Map());
  
  useImperativeHandle(ref, () => ({
    updateAllCharts: (dataMap: Record<string, any[]>) => {
      Object.entries(dataMap).forEach(([chartId, data]) => {
        chartRefs.current.get(chartId)?.updateData(data);
      });
    },
    
    exportDashboard: async () => {
      const exports = await Promise.all(
        Array.from(chartRefs.current.entries()).map(async ([id, ref]) => ({
          id,
          image: await ref.exportImage('png'),
          data: await ref.exportData('json')
        }))
      );
      return exports;
    },
    
    syncZoom: (enable: boolean) => {
      if (enable) {
        // 实现图表间的缩放同步
        chartRefs.current.forEach(ref => {
          ref.enableZoom(true);
        });
      }
    },
    
    broadcastSelection: (chartId: string, selection: number[]) => {
      // 广播选择事件到其他图表
      chartRefs.current.forEach((ref, id) => {
        if (id !== chartId) {
          ref.selectDataPoints(selection);
        }
      });
    }
  }), []);
  
  return (
    <div className="dashboard">
      {props.charts.map(chart => (
        <Chart
          key={chart.id}
          ref={(ref) => chartRefs.current.set(chart.id, ref)}
          {...chart.config}
        />
      ))}
    </div>
  );
});`
    }
  ],

  bestPractices: [
    '🏗️ **分层架构**：按使用频率和复杂度设计API层次',
    '📝 **接口先行**：先定义TypeScript接口，再实现组件',
    '🔄 **版本管理**：为API变更提供向前兼容策略',
    '🧪 **可测试性**：确保所有API方法都可以单元测试',
    '📚 **文档化**：为每个API方法提供详细文档和示例',
    '⚡ **性能优化**：使用依赖数组和缓存避免不必要的重建',
    '🛡️ **错误处理**：提供统一的错误处理和恢复机制',
    '🔧 **开发工具**：集成React DevTools和自定义调试工具'
  ],

  commonPitfalls: [
    '❌ **过度设计**：避免创建过于复杂的API结构',
    '❌ **不一致性**：确保所有组件遵循相同的API约定',
    '❌ **遗漏依赖**：正确管理useImperativeHandle的依赖数组',
    '❌ **缺乏类型**：为所有API提供完整的TypeScript类型',
    '❌ **状态泄漏**：避免暴露过多内部状态和实现细节',
    '❌ **性能问题**：避免在API方法中进行昂贵计算',
    '❌ **内存泄漏**：确保组件卸载时正确清理资源',
    '❌ **测试遗漏**：为所有公共API编写测试用例'
  ],

  relatedConcepts: [
    'forwardRef模式与组件组合',
    'TypeScript泛型在API设计中的应用',
    '企业级组件库的架构模式',
    '微前端环境下的组件通信',
    '插件化系统的设计原则',
    '性能监控和调试工具集成'
  ]
};

export default componentApiDesign; 