import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: 'useImperativeHandle必须与forwardRef一起使用，请解释它们的协作关系和工作原理。',
    difficulty: 'medium',
    category: 'concept',
    
    answer: `useImperativeHandle和forwardRef形成了React中处理ref的完整解决方案，它们的协作关系体现在以下几个方面：

## 协作关系分析

**forwardRef的作用**：
- 函数组件默认不能接收ref参数
- forwardRef将ref作为第二个参数传递给组件
- 创建了ref传递的通道

**useImperativeHandle的作用**：
- 接收forwardRef传递的ref
- 自定义ref.current的内容
- 控制暴露给父组件的API

## 工作流程

1. **父组件阶段**：创建ref并传递给子组件
2. **forwardRef阶段**：接收ref并转发给组件内部
3. **useImperativeHandle阶段**：修改ref.current为自定义API
4. **父组件使用阶段**：通过ref.current访问自定义API

## 为什么必须配合使用？

没有forwardRef，函数组件无法接收ref：`,
    
    code: `// ❌ 错误：函数组件无法直接接收ref
function MyComponent(props) {
  // 无法访问ref参数
  useImperativeHandle(ref, () => ({ ... })); // ref未定义
}

// ✅ 正确：使用forwardRef包装
const MyComponent = forwardRef((props, ref) => {
  useImperativeHandle(ref, () => ({
    focus: () => { /* 实现 */ },
    clear: () => { /* 实现 */ }
  }), []);
  
  return <input />;
});

// 使用示例
function Parent() {
  const ref = useRef();
  
  return (
    <div>
      <MyComponent ref={ref} />
      <button onClick={() => ref.current?.focus()}>
        聚焦
      </button>
    </div>
  );
}`,
    
    tags: ['forwardRef', 'ref传递', '组件协作', 'Hook组合'],
    followUp: '如果不使用forwardRef，有其他方式实现类似功能吗？'
  },

  {
    id: 2,
    question: 'useImperativeHandle违反了React的声明式编程原则，什么时候应该使用它？什么时候应该避免？',
    difficulty: 'hard',
    category: 'best-practices',
    
    answer: `useImperativeHandle确实打破了React的声明式原则，但在特定场景下是必要的。正确的使用原则是：优先声明式，必要时命令式。

## 应该使用的场景

### 1. DOM操作需求
当需要直接操作DOM时，声明式方案无法满足：`,
    
    code: `// ✅ 适合使用：焦点管理
const Input = forwardRef((props, ref) => {
  const inputRef = useRef();
  
  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
    blur: () => inputRef.current?.blur(),
    select: () => inputRef.current?.select()
  }));
  
  return <input ref={inputRef} {...props} />;
});

// ✅ 适合使用：滚动控制
const ScrollContainer = forwardRef((props, ref) => {
  const containerRef = useRef();
  
  useImperativeHandle(ref, () => ({
    scrollToTop: () => containerRef.current?.scrollTo({ top: 0 }),
    scrollToBottom: () => {
      const el = containerRef.current;
      el?.scrollTo({ top: el.scrollHeight });
    }
  }));
  
  return <div ref={containerRef}>{props.children}</div>;
});`,
    
    followUpCode: `// ❌ 应该避免：简单状态管理
const Counter = forwardRef((props, ref) => {
  const [count, setCount] = useState(0);
  
  // 不推荐：简单状态用命令式
  useImperativeHandle(ref, () => ({
    increment: () => setCount(c => c + 1),
    decrement: () => setCount(c => c - 1),
    reset: () => setCount(0)
  }));
  
  return <div>{count}</div>;
});

// ✅ 更好的声明式方案
function Counter({ value, onChange }) {
  return (
    <div>
      {value}
      <button onClick={() => onChange(value + 1)}>+</button>
      <button onClick={() => onChange(value - 1)}>-</button>
    </div>
  );
}`,
    
    tags: ['设计原则', '声明式vs命令式', '最佳实践', 'API设计'],
    followUp: '如何在团队中建立useImperativeHandle的使用规范？'
  },

  {
    id: 3,
    question: 'useImperativeHandle的依赖数组如何正确使用？不正确使用会导致什么问题？',
    difficulty: 'medium',
    category: 'optimization',
    
    answer: `useImperativeHandle的依赖数组遵循与useEffect相同的规则，但有其特殊的注意事项。依赖管理不当会导致API对象过期、内存泄漏或性能问题。

## 依赖数组的正确使用

### 1. 空依赖数组 - 最常见模式`,
    
    code: `// ✅ 推荐：API对象不依赖外部状态
const Input = forwardRef((props, ref) => {
  const inputRef = useRef();
  
  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
    blur: () => inputRef.current?.blur(),
    // 这些方法不依赖组件状态，使用空数组
  }), []); // 空依赖数组
  
  return <input ref={inputRef} />;
});

// ✅ 包含外部依赖的情况
const SearchBox = forwardRef((props, ref) => {
  const [query, setQuery] = useState('');
  const inputRef = useRef();
  
  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
    getQuery: () => query, // 依赖组件状态
    setQuery: (newQuery) => setQuery(newQuery),
    clear: () => {
      setQuery(''); // 使用最新的setQuery
      inputRef.current?.focus();
    }
  }), [query]); // 包含query依赖
  
  return (
    <input 
      ref={inputRef}
      value={query}
      onChange={(e) => setQuery(e.target.value)}
    />
  );
});`,
    
    followUpCode: `// ❌ 常见错误：遗漏依赖
const Timer = forwardRef((props, ref) => {
  const [count, setCount] = useState(0);
  
  useImperativeHandle(ref, () => ({
    getCurrentCount: () => count, // 使用了count但未包含在依赖中
    reset: () => setCount(0)
  }), []); // ❌ 错误：遗漏count依赖
  
  // 父组件调用getCurrentCount可能得到过期的count值
});

// ✅ 修正版本
const Timer = forwardRef((props, ref) => {
  const [count, setCount] = useState(0);
  
  useImperativeHandle(ref, () => ({
    getCurrentCount: () => count,
    reset: () => setCount(0)
  }), [count]); // ✅ 包含count依赖
});

// 🔧 优化版本：减少重建
const Timer = forwardRef((props, ref) => {
  const [count, setCount] = useState(0);
  const countRef = useRef(count);
  countRef.current = count;
  
  useImperativeHandle(ref, () => ({
    getCurrentCount: () => countRef.current, // 通过ref获取最新值
    reset: () => setCount(0)
  }), []); // 可以使用空数组，提高性能
});`,
    
    tags: ['依赖管理', '性能优化', '闭包陷阱', 'useRef技巧'],
    followUp: '如何检测和修复useImperativeHandle中的依赖问题？'
  },

  {
    id: 4,
    question: '设计一个组件库时，如何设计useImperativeHandle的API接口？有什么设计原则和最佳实践？',
    difficulty: 'hard',
    category: 'design',
    
    answer: `组件库的API设计需要考虑易用性、一致性、扩展性和向后兼容性。以下是设计useImperativeHandle API的核心原则和实践：

## 设计原则

### 1. 最小化暴露原则
只暴露必要的方法，隐藏内部实现：`,
    
    code: `// ✅ 良好的API设计
interface VideoPlayerAPI {
  // 基础控制 - 最常用功能
  play(): Promise<void>;
  pause(): void;
  stop(): void;
  
  // 状态查询 - 只读信息
  getCurrentTime(): number;
  getDuration(): number;
  isPlaying(): boolean;
  
  // 高级控制 - 可选功能
  seek(time: number): void;
  setVolume(volume: number): void;
  toggleFullscreen(): Promise<void>;
}

const VideoPlayer = forwardRef<VideoPlayerAPI, VideoPlayerProps>((props, ref) => {
  // 内部状态和逻辑保持私有
  const [state, setState] = useState();
  const videoRef = useRef<HTMLVideoElement>();
  
  useImperativeHandle(ref, () => ({
    // 只暴露稳定的公共接口
    play: async () => {
      await videoRef.current?.play();
      updateInternalState(); // 内部方法不暴露
    },
    
    pause: () => {
      videoRef.current?.pause();
      updateInternalState();
    },
    
    // 状态查询方法
    getCurrentTime: () => videoRef.current?.currentTime || 0,
    isPlaying: () => !videoRef.current?.paused
  }), []);
});`,
    
    followUpCode: `// ✅ 分层API设计
interface BasePlayerAPI {
  play(): Promise<void>;
  pause(): void;
  stop(): void;
}

interface AdvancedPlayerAPI extends BasePlayerAPI {
  seek(time: number): void;
  setVolume(volume: number): void;
  setPlaybackRate(rate: number): void;
}

interface PluginPlayerAPI extends AdvancedPlayerAPI {
  registerPlugin(name: string, plugin: Plugin): void;
  getPlugin(name: string): Plugin | undefined;
}

// 根据使用场景提供不同层次的API
const SimplePlayer = forwardRef<BasePlayerAPI, Props>(...);
const AdvancedPlayer = forwardRef<AdvancedPlayerAPI, Props>(...);
const PluginPlayer = forwardRef<PluginPlayerAPI, Props>(...);

// ✅ 一致性设计模式
// 所有组件都遵循相同的命名规范
interface FormControlAPI {
  focus(): void;          // 动词开头
  blur(): void;
  clear(): void;
  getValue(): string;     // get前缀表示查询
  setValue(value: string): void; // set前缀表示设置
  isValid(): boolean;     // is前缀表示状态查询
}`,
    
    tags: ['API设计', '组件库', '接口设计', '设计模式'],
    followUp: '如何处理API的版本升级和向后兼容性？'
  },

  {
    id: 5,
    question: 'useImperativeHandle对性能有什么影响？如何进行性能优化？',
    difficulty: 'medium',
    category: 'performance',
    
    answer: `useImperativeHandle的性能影响主要来自API对象的重建和依赖项检查。理解这些影响并采用适当的优化策略可以避免性能问题。

## 性能影响分析

### 1. API对象重建的成本`,
    
    code: `// ❌ 性能问题：每次渲染都重建API对象
const ExpensiveComponent = forwardRef((props, ref) => {
  const [state, setState] = useState();
  
  useImperativeHandle(ref, () => ({
    // 复杂的计算在每次重建时都会执行
    getComputedValue: () => {
      return heavyComputation(state); // 昂贵计算
    },
    
    // 包含复杂逻辑的方法
    performComplexOperation: () => {
      return complexOperation(state);
    }
  })); // 没有依赖数组，每次都重建
});

// ✅ 优化：合理使用依赖数组
const OptimizedComponent = forwardRef((props, ref) => {
  const [state, setState] = useState();
  
  // 缓存昂贵计算结果
  const computedValue = useMemo(() => {
    return heavyComputation(state);
  }, [state]);
  
  useImperativeHandle(ref, () => ({
    getComputedValue: () => computedValue, // 直接返回缓存值
    
    performComplexOperation: () => {
      return complexOperation(state);
    }
  }), [computedValue, state]); // 明确依赖
});`,
    
    followUpCode: `// 🚀 高级优化：稳定引用模式
const AdvancedOptimizedComponent = forwardRef((props, ref) => {
  const [state, setState] = useState();
  const stateRef = useRef(state);
  stateRef.current = state;
  
  // 使用useCallback稳定方法引用
  const performComplexOperation = useCallback(() => {
    return complexOperation(stateRef.current);
  }, []);
  
  // 缓存API对象
  const api = useMemo(() => ({
    getState: () => stateRef.current,
    setState: (newState) => setState(newState),
    performComplexOperation,
    
    // 简单方法可以内联
    reset: () => setState(initialState)
  }), [performComplexOperation]);
  
  useImperativeHandle(ref, () => api, [api]);
});

// 🎯 终极优化：懒加载API
const LazyAPIComponent = forwardRef((props, ref) => {
  const [state, setState] = useState();
  const apiCache = useRef();
  
  useImperativeHandle(ref, () => {
    // 懒加载：只在首次访问时创建API
    if (!apiCache.current) {
      apiCache.current = {
        getState: () => state,
        setState,
        reset: () => setState(initialState)
      };
    }
    return apiCache.current;
  }, [state]);
});`,
    
    tags: ['性能优化', 'useMemo', 'useCallback', '缓存策略'],
    followUp: '在大型组件库中，如何监控和优化useImperativeHandle的性能？'
  }
];

export default interviewQuestions; 