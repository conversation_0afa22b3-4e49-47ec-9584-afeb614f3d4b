import { ApiItem } from '../../../types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import componentApiDesign from './component-api-design';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useImperativeHandleData: ApiItem = {
  id: 'useImperativeHandle',
  title: 'useImperativeHandle',
  description: 'forwardRef配合使用，暴露自定义命令式API的Hook',
  content: `useImperativeHandle让你在使用forwardRef时自定义暴露给父组件的ref值。
在大多数情况下，应该避免使用命令式代码，但在某些特定场景下，它可以帮助你创建更好的组件API。`,
  
  syntax: `useImperativeHandle(ref, createHandle, dependencies?)

function useImperativeHandle<T>(
  ref: React.ForwardedRef<T>,
  createHandle: () => T,
  dependencies?: React.DependencyList
): void`,

  example: `import React, { useState, useImperativeHandle, forwardRef, useRef } from 'react';

// 自定义输入框组件，暴露focus和clear方法
const CustomInput = forwardRef<
  { focus: () => void; clear: () => void },
  { placeholder?: string }
>((props, ref) => {
  const [value, setValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  useImperativeHandle(ref, () => ({
    focus: () => {
      inputRef.current?.focus();
    },
    clear: () => {
      setValue('');
      inputRef.current?.focus();
    }
  }), []);

  return (
    <input
      ref={inputRef}
      value={value}
      onChange={(e) => setValue(e.target.value)}
      placeholder={props.placeholder}
    />
  );
});

// 父组件使用
function App() {
  const inputRef = useRef<{ focus: () => void; clear: () => void }>(null);

  return (
    <div>
      <CustomInput ref={inputRef} placeholder="输入内容..." />
      <button onClick={() => inputRef.current?.focus()}>
        聚焦输入框
      </button>
      <button onClick={() => inputRef.current?.clear()}>
        清空内容
      </button>
    </div>
  );
}

// 这个示例展示了useImperativeHandle如何让父组件访问子组件的自定义方法`,

  parameters: [
    {
      name: 'ref',
      type: 'React.ForwardedRef<T>',
      description: 'forwardRef传递的ref对象，用于接收自定义的命令式API'
    },
    {
      name: 'createHandle',
      type: '() => T',
      description: '返回要暴露给父组件的对象或方法的函数，只在依赖项变化时重新调用'
    },
    {
      name: 'dependencies',
      type: 'React.DependencyList',
      description: '可选的依赖数组，控制createHandle函数的重新执行时机'
    }
  ],

  returnValue: {
    type: 'void',
    description: `useImperativeHandle不返回任何值，这是设计上的考虑：

**为什么不返回值？**
- Hook的作用是配置ref对象，而不是提供返回值
- 避免与ref的直接使用产生混淆
- 保持API的单一职责原则

**使用方式**：
通过ref.current访问暴露的API，而不是Hook的返回值`
  },

  notes: '注意：useImperativeHandle应该与forwardRef一起使用。在大多数情况下应该避免使用，仅在需要暴露自定义命令式API时使用',

  commonMistakes: [
    {
      title: '忘记使用forwardRef',
      description: 'useImperativeHandle必须与forwardRef配合使用，否则ref参数为null',
      wrongCode: `// ❌ 错误：没有使用forwardRef
const MyComponent = (props, ref) => {
  useImperativeHandle(ref, () => ({ ... }));
}`,
      correctCode: `// ✅ 正确：使用forwardRef包装
const MyComponent = forwardRef((props, ref) => {
  useImperativeHandle(ref, () => ({ ... }));
})`
    },
    {
      title: '过度使用命令式API',
      description: '应该优先考虑声明式方案，只在必要时使用useImperativeHandle',
      wrongCode: `// ❌ 过度使用：简单状态用命令式
useImperativeHandle(ref, () => ({
  setValue: (value) => setInternalValue(value)
}));`,
      correctCode: `// ✅ 优先声明式：通过props传递
<Component value={value} onChange={setValue} />`
    }
  ],

  relatedHooks: ['useRef', 'forwardRef'],
  category: 'advanced',

  // 导入所有Tab内容 - 10个完整Tab
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights,
  advancedTopics: [componentApiDesign]
};

export default useImperativeHandleData; 