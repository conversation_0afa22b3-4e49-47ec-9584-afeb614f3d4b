import { BasicInfo } from '../../../types/api';

const basicInfo: BasicInfo = {
  definition: 'useImperativeHandle是React提供的高级Hook，专门用于与forwardRef配合自定义暴露给父组件的命令式API。它打破了React声明式编程的常规模式，允许子组件向父组件暴露特定的方法或属性，是实现高度封装组件和第三方库集成的重要工具。',
  
  introduction: 'useImperativeHandle解决了在React函数组件中暴露自定义命令式接口的问题。当你需要让父组件能够调用子组件的特定方法（如focus、scroll、play等）时，这个Hook提供了优雅的解决方案。它与forwardRef形成完美配对，在保持组件封装性的同时，提供必要的命令式操作入口，是构建高质量组件库和复杂UI交互的核心技术之一。',

  syntax: `useImperativeHandle(ref, createHandle, dependencies?)

// 完整类型定义
function useImperativeHandle<T, R = T>(
  ref: React.Ref<T> | undefined,
  init: () => R,
  deps?: React.DependencyList
): void

// 常见使用模式
const Component = forwardRef<API, Props>((props, ref) => {
  useImperativeHandle(ref, () => ({
    // 暴露的方法和属性
  }), []);
});`,

  parameters: [
    {
      name: 'ref',
      type: 'React.ForwardedRef<T>',
      description: 'forwardRef传递的ref引用，接收自定义API对象',
      required: true,
      details: `ref参数的特点：
- 必须来自forwardRef的第二个参数
- 可以是null或undefined（组件未被引用时）
- 支持函数ref和对象ref两种形式
- 在TypeScript中需要明确指定泛型类型`
    },
    {
      name: 'createHandle',
      type: '() => T',
      description: '返回要暴露给父组件的API对象的函数',
      required: true,
      details: `createHandle函数的特征：
- 返回值将成为ref.current的内容
- 仅在依赖项变化时重新执行
- 应该返回稳定的对象结构
- 可以包含方法、属性或计算值`
    },
    {
      name: 'dependencies',
      type: 'React.DependencyList',
      description: '依赖数组，控制createHandle的重新执行',
      required: false,
      details: `依赖数组的作用：
- 为空数组[]：只在组件挂载时执行一次
- 包含变量：变量变化时重新创建API对象
- 未传递：每次渲染都重新创建（不推荐）
- 遵循与useEffect相同的依赖规则`
    }
  ],

  returnValue: {
    type: 'void',
    description: `useImperativeHandle不返回任何值，这是设计上的考虑：

**为什么不返回值？**
- Hook的作用是配置ref对象，而不是提供返回值
- 避免与ref的直接使用产生混淆
- 保持API的单一职责原则
- 父组件通过ref.current访问暴露的API

**使用方式**：
\`\`\`typescript
// 子组件暴露API
useImperativeHandle(ref, () => ({
  focus: () => inputRef.current?.focus(),
  getValue: () => value
}));

// 父组件访问API
const childRef = useRef<{focus: () => void, getValue: () => string}>(null);
childRef.current?.focus(); // 调用子组件方法
const value = childRef.current?.getValue(); // 获取子组件状态
\`\`\``
  },

  keyFeatures: [
    {
      feature: 'forwardRef集成',
      description: '与forwardRef形成完美配对，实现ref的自定义行为',
      importance: 'critical',
      details: 'useImperativeHandle必须在forwardRef包装的组件中使用，两者协作实现命令式API的暴露和使用'
    },
    {
      feature: '自定义API设计',
      description: '允许精确控制暴露给父组件的方法和属性',
      importance: 'high',
      details: '可以选择性地暴露内部功能，隐藏实现细节，提供清晰的组件接口'
    },
    {
      feature: '依赖优化',
      description: '通过依赖数组控制API对象的重新创建时机',
      importance: 'medium',
      details: '避免不必要的对象重建，优化性能，特别是在API对象包含复杂计算时'
    },
    {
      feature: 'TypeScript支持',
      description: '完整的类型安全支持，提供优秀的开发体验',
      importance: 'high',
      details: '通过泛型约束确保API接口的类型正确性，IDE提供完整的自动补全和错误检查'
    }
  ],

  commonUseCases: [
    {
      title: '表单控件封装',
      description: '自定义输入框、选择器等表单组件的命令式API',
      code: `const CustomInput = forwardRef<{focus: () => void, clear: () => void}>((props, ref) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [value, setValue] = useState('');
  
  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
    clear: () => {
      setValue('');
      inputRef.current?.focus();
    }
  }), []);
  
  return <input ref={inputRef} value={value} onChange={e => setValue(e.target.value)} />;
});`
    },
    {
      title: '媒体控件管理',
      description: '视频播放器、音频组件的播放控制接口',
      code: `const VideoPlayer = forwardRef<{play: () => void, pause: () => void}>((props, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  
  useImperativeHandle(ref, () => ({
    play: () => videoRef.current?.play(),
    pause: () => videoRef.current?.pause(),
    currentTime: () => videoRef.current?.currentTime || 0
  }), []);
  
  return <video ref={videoRef} src={props.src} />;
});`
    },
    {
      title: '滚动容器控制',
      description: '列表、容器等组件的滚动操作接口',
      code: `const ScrollContainer = forwardRef<{scrollToTop: () => void}>((props, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  
  useImperativeHandle(ref, () => ({
    scrollToTop: () => containerRef.current?.scrollTo({top: 0, behavior: 'smooth'}),
    scrollToBottom: () => {
      const el = containerRef.current;
      if (el) el.scrollTo({top: el.scrollHeight, behavior: 'smooth'});
    }
  }), []);
  
  return <div ref={containerRef}>{props.children}</div>;
});`
    }
  ],

  bestPractices: [
    '🎯 **最小化暴露**：只暴露必要的方法和属性，保持API简洁',
    '🔒 **encapsulation**：隐藏内部实现细节，提供稳定的外部接口',
    '⚡ **性能优化**：合理使用依赖数组，避免不必要的API重建',
    '🏷️ **类型安全**：在TypeScript中明确定义API接口类型',
    '📝 **文档化**：为暴露的API提供清晰的文档和使用示例',
    '🚨 **谨慎使用**：优先考虑声明式方案，仅在必要时使用命令式API',
    '🔄 **一致性**：在组件库中保持一致的API设计模式',
    '🧪 **可测试性**：确保暴露的API可以被单元测试覆盖'
  ],

  warnings: [
    '🚨 **必须与forwardRef配合**：useImperativeHandle只能在forwardRef包装的组件中使用',
    '📱 **避免过度使用**：优先考虑props和回调的声明式方案',
    '🔄 **依赖管理**：注意依赖数组的正确性，避免过期引用',
    '🎭 **类型一致性**：确保暴露的API类型与父组件期望的类型匹配',
    '⚡ **性能影响**：复杂的API对象创建可能影响渲染性能',
    '🧩 **组合限制**：与某些高阶组件组合使用时可能存在兼容性问题'
  ],

  notes: [
    'useImperativeHandle打破了React的声明式原则，应谨慎使用',
    '在组件库开发中，这是实现高级封装的重要工具',
    '与useRef和forwardRef形成React中ref操作的完整生态',
    'React 18中与并发特性兼容良好，不会影响时间切片'
  ]
};

export default basicInfo; 