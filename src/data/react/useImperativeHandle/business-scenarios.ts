import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 'input-focus-control',
    title: '输入框焦点控制',
    description: '自定义输入框组件，暴露focus、blur、clear等焦点和内容管理方法',
    difficulty: 'beginner',
    
    problem: `在表单开发中，经常需要从父组件控制子输入框的焦点状态和内容清理。
传统的受控组件模式无法直接提供这些命令式操作，需要通过useImperativeHandle暴露自定义API。`,

    solution: `通过useImperativeHandle暴露输入框的focus、blur、clear等方法，
让父组件能够直接控制输入框的交互行为，同时保持组件的封装性。`,

    keyTechniques: [
      'forwardRef + useImperativeHandle的基础配合',
      '自定义ref API的设计和暴露',
      'DOM引用的获取和操作',
      'TypeScript类型约束和接口定义'
    ],

    businessValue: '提升表单交互体验，支持快捷键操作、自动聚焦、验证后清理等高级功能',

    code: `import React, { useState, useRef, useImperativeHandle, forwardRef } from 'react';

// 定义暴露给父组件的API接口
interface CustomInputAPI {
  focus: () => void;
  blur: () => void;
  clear: () => void;
  getValue: () => string;
  setValue: (value: string) => void;
}

// 组件Props接口
interface CustomInputProps {
  placeholder?: string;
  type?: 'text' | 'password' | 'email';
  maxLength?: number;
  onChange?: (value: string) => void;
  onEnter?: (value: string) => void;
}

// 自定义输入框组件
const CustomInput = forwardRef<CustomInputAPI, CustomInputProps>((props, ref) => {
  const [value, setValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // 暴露给父组件的API
  useImperativeHandle(ref, () => ({
    // 聚焦输入框
    focus: () => {
      inputRef.current?.focus();
    },
    
    // 失去焦点
    blur: () => {
      inputRef.current?.blur();
    },
    
    // 清空内容并聚焦
    clear: () => {
      setValue('');
      props.onChange?.('');
      inputRef.current?.focus();
    },
    
    // 获取当前值
    getValue: () => value,
    
    // 设置值
    setValue: (newValue: string) => {
      setValue(newValue);
      props.onChange?.(newValue);
    }
  }), [value, props.onChange]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    props.onChange?.(newValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      props.onEnter?.(value);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  return (
    <div className={isFocused ? 'input-wrapper focused' : 'input-wrapper'}>
      <input
        ref={inputRef}
        type={props.type || 'text'}
        value={value}
        placeholder={props.placeholder}
        maxLength={props.maxLength}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className="custom-input"
      />
      {value && (
        <button
          type="button"
          className="clear-button"
          onClick={() => {
            setValue('');
            props.onChange?.('');
          }}
        >
          ×
        </button>
      )}
    </div>
  );
});

CustomInput.displayName = 'CustomInput';

// 父组件使用示例
function LoginForm() {
  const usernameRef = useRef<CustomInputAPI>(null);
  const passwordRef = useRef<CustomInputAPI>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const username = usernameRef.current?.getValue();
    const password = passwordRef.current?.getValue();
    
    if (!username) {
      alert('请输入用户名');
      usernameRef.current?.focus();
      return;
    }
    
    if (!password) {
      alert('请输入密码');
      passwordRef.current?.focus();
      return;
    }
    
    console.log('登录信息:', { username, password });
  };

  const handleClearAll = () => {
    usernameRef.current?.clear();
    passwordRef.current?.clear();
  };

  const handleQuickFill = () => {
    usernameRef.current?.setValue('<EMAIL>');
    passwordRef.current?.setValue('123456');
    passwordRef.current?.focus();
  };

  return (
    <form onSubmit={handleSubmit} className="login-form">
      <h2>用户登录</h2>
      
      <div className="form-group">
        <label htmlFor="username">用户名</label>
        <CustomInput
          ref={usernameRef}
          type="email"
          placeholder="请输入邮箱地址"
          onChange={(value) => console.log('用户名变化:', value)}
          onEnter={() => passwordRef.current?.focus()}
        />
      </div>
      
      <div className="form-group">
        <label htmlFor="password">密码</label>
        <CustomInput
          ref={passwordRef}
          type="password"
          placeholder="请输入密码"
          maxLength={20}
          onChange={(value) => console.log('密码变化:', value)}
          onEnter={handleSubmit}
        />
      </div>
      
      <div className="form-actions">
        <button type="submit">登录</button>
        <button type="button" onClick={handleClearAll}>
          清空
        </button>
        <button type="button" onClick={handleQuickFill}>
          快速填充
        </button>
      </div>
    </form>
  );
}

export default LoginForm;`,

    explanation: `这个示例展示了useImperativeHandle在表单控件中的典型应用：

**核心技术要点**：
1. **API设计**：通过TypeScript接口定义清晰的组件API
2. **方法暴露**：focus、blur、clear、getValue、setValue等常用操作
3. **状态管理**：组件内部状态与暴露API的协调
4. **用户体验**：支持快捷键、自动聚焦、验证反馈等

**使用场景**：
- 表单验证失败后自动聚焦到错误字段
- 快捷键清空所有输入内容
- 自动填充和快速操作
- 复杂表单的流程控制

**最佳实践**：
- 明确定义API接口类型
- 保持方法的单一职责
- 考虑用户体验的细节处理
- 合理组合内部状态和外部控制`
  },

  {
    id: 'scroll-container',
    title: '滚动容器控制',
    description: '滚动列表组件，提供scrollToTop、scrollToBottom、scrollToElement等滚动控制方法',
    difficulty: 'intermediate',
    
    problem: `在长列表、聊天界面、时间线等场景中，需要从外部控制滚动位置。
传统方案需要暴露DOM引用或使用复杂的状态管理，不够优雅且难以维护。`,

    solution: `通过useImperativeHandle暴露滚动控制方法，提供平滑滚动、定位到特定元素、
获取滚动状态等功能，封装复杂的滚动逻辑，提供简洁的API接口。`,

    keyTechniques: [
      '复杂API对象的设计和暴露',
      'DOM滚动操作的封装和优化',
      '滚动状态的监听和反馈',
      '平滑滚动和性能优化技术'
    ],

    businessValue: '提升列表交互体验，支持快速导航、自动定位、滚动状态同步等功能',

    code: `import React, { 
  useRef, 
  useImperativeHandle, 
  forwardRef, 
  useState, 
  useEffect, 
  useCallback 
} from 'react';

// 滚动容器API接口
interface ScrollContainerAPI {
  scrollToTop: (smooth?: boolean) => void;
  scrollToBottom: (smooth?: boolean) => void;
  scrollToElement: (elementId: string, smooth?: boolean) => void;
  scrollToIndex: (index: number, smooth?: boolean) => void;
  getCurrentScrollTop: () => number;
  getScrollHeight: () => number;
  isAtTop: () => boolean;
  isAtBottom: () => boolean;
}

// 滚动容器Props
interface ScrollContainerProps {
  children: ReactNode;
  className?: string;
  maxHeight?: number;
  onScroll?: (scrollTop: number, isAtTop: boolean, isAtBottom: boolean) => void;
  itemHeight?: number; // 用于scrollToIndex计算
}

// 滚动容器组件
const ScrollContainer = forwardRef<ScrollContainerAPI, ScrollContainerProps>((props, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrollState, setScrollState] = useState({
    scrollTop: 0,
    isAtTop: true,
    isAtBottom: false
  });

  // 暴露滚动控制API
  useImperativeHandle(ref, () => ({
    // 滚动到顶部
    scrollToTop: (smooth = true) => {
      const container = containerRef.current;
      if (container) {
        container.scrollTo({
          top: 0,
          behavior: smooth ? 'smooth' : 'instant'
        });
      }
    },

    // 滚动到底部
    scrollToBottom: (smooth = true) => {
      const container = containerRef.current;
      if (container) {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: smooth ? 'smooth' : 'instant'
        });
      }
    },

    // 滚动到指定元素
    scrollToElement: (elementId: string, smooth = true) => {
      const container = containerRef.current;
      const element = document.getElementById(elementId);
      
      if (container && element) {
        const containerRect = container.getBoundingClientRect();
        const elementRect = element.getBoundingClientRect();
        const scrollTop = container.scrollTop + elementRect.top - containerRect.top;
        
        container.scrollTo({
          top: scrollTop,
          behavior: smooth ? 'smooth' : 'instant'
        });
      }
    },

    // 滚动到指定索引
    scrollToIndex: (index: number, smooth = true) => {
      const container = containerRef.current;
      if (container && props.itemHeight) {
        const scrollTop = index * props.itemHeight;
        container.scrollTo({
          top: scrollTop,
          behavior: smooth ? 'smooth' : 'instant'
        });
      }
    },

    // 获取当前滚动位置
    getCurrentScrollTop: () => {
      return containerRef.current?.scrollTop || 0;
    },

    // 获取滚动高度
    getScrollHeight: () => {
      return containerRef.current?.scrollHeight || 0;
    },

    // 是否在顶部
    isAtTop: () => {
      return scrollState.isAtTop;
    },

    // 是否在底部
    isAtBottom: () => {
      return scrollState.isAtBottom;
    }
  }), [scrollState, props.itemHeight]);

  // 滚动事件处理
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;
    
    const isAtTop = scrollTop === 0;
    const isAtBottom = Math.abs(scrollHeight - clientHeight - scrollTop) < 1;
    
    const newScrollState = {
      scrollTop,
      isAtTop,
      isAtBottom
    };
    
    setScrollState(newScrollState);
    props.onScroll?.(scrollTop, isAtTop, isAtBottom);
  }, [props.onScroll]);

  return (
    <div
      ref={containerRef}
      className={"scroll-container " + (props.className || "")}
      style={{
        maxHeight: props.maxHeight ? props.maxHeight + "px" : 'auto',
        overflowY: 'auto',
        overflowX: 'hidden'
      }}
      onScroll={handleScroll}
    >
      {props.children}
    </div>
  );
});

ScrollContainer.displayName = 'ScrollContainer';

// 使用示例：聊天界面
function ChatApp() {
  const scrollRef = useRef<ScrollContainerAPI>(null);
  const [messages, setMessages] = useState([
    { id: '1', text: '你好！', time: '10:00', sender: 'other' },
    { id: '2', text: '我是AI助手', time: '10:01', sender: 'other' },
    { id: '3', text: '有什么可以帮你的吗？', time: '10:02', sender: 'other' }
  ]);
  const [newMessage, setNewMessage] = useState('');
  const [showScrollTop, setShowScrollTop] = useState(false);

  // 发送消息
  const sendMessage = () => {
    if (newMessage.trim()) {
      const message = {
        id: Date.now().toString(),
        text: newMessage,
        time: new Date().toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        }),
        sender: 'self'
      };
      
      setMessages(prev => [...prev, message]);
      setNewMessage('');
      
      // 发送消息后自动滚动到底部
      setTimeout(() => {
        scrollRef.current?.scrollToBottom();
      }, 100);
    }
  };

  // 滚动状态变化处理
  const handleScrollChange = (scrollTop: number, isAtTop: boolean, isAtBottom: boolean) => {
    setShowScrollTop(!isAtTop && !isAtBottom);
  };

  // 快速操作
  const handleQuickActions = {
    // 滚动到顶部
    scrollToTop: () => scrollRef.current?.scrollToTop(),
    
    // 滚动到底部
    scrollToBottom: () => scrollRef.current?.scrollToBottom(),
    
    // 跳转到特定消息
    jumpToMessage: (messageId: string) => {
      scrollRef.current?.scrollToElement(messageId);
    },
    
    // 获取滚动信息
    getScrollInfo: () => {
      const scrollTop = scrollRef.current?.getCurrentScrollTop() || 0;
      const scrollHeight = scrollRef.current?.getScrollHeight() || 0;
      const isAtTop = scrollRef.current?.isAtTop() || false;
      const isAtBottom = scrollRef.current?.isAtBottom() || false;
      
      console.log('滚动信息:', {
        scrollTop,
        scrollHeight,
        isAtTop,
        isAtBottom
      });
    }
  };

  // 模拟收到新消息
  useEffect(() => {
    const timer = setInterval(() => {
      if (Math.random() > 0.7) {
        const responses = [
          '收到！', '好的', '明白了', '没问题', '正在处理...'
        ];
        const response = responses[Math.floor(Math.random() * responses.length)];
        
        setMessages(prev => [...prev, {
          id: Date.now().toString(),
          text: response,
          time: new Date().toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
          }),
          sender: 'other'
        }]);
        
        // 如果用户在底部，自动滚动
        if (scrollRef.current?.isAtBottom()) {
          setTimeout(() => {
            scrollRef.current?.scrollToBottom();
          }, 100);
        }
      }
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="chat-app">
      <div className="chat-header">
        <h3>聊天界面</h3>
        <div className="chat-actions">
          <button onClick={handleQuickActions.scrollToTop}>
            📈 顶部
          </button>
          <button onClick={handleQuickActions.scrollToBottom}>
            📉 底部
          </button>
          <button onClick={handleQuickActions.getScrollInfo}>
            📊 信息
          </button>
        </div>
      </div>

      <ScrollContainer
        ref={scrollRef}
        className="chat-messages"
        maxHeight={400}
        onScroll={handleScrollChange}
      >
        {messages.map((message) => (
          <div
            key={message.id}
            id={message.id}
            className={message.sender === 'self' ? 'message self' : 'message other'}
          >
            <div className="message-content">
              <div className="message-text">{message.text}</div>
              <div className="message-time">{message.time}</div>
            </div>
          </div>
        ))}
      </ScrollContainer>

      {showScrollTop && (
        <button
          className="scroll-to-top"
          onClick={handleQuickActions.scrollToBottom}
        >
          ⬇️ 回到底部
        </button>
      )}

      <div className="chat-input">
        <input
          type="text"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && sendMessage()}
          placeholder="输入消息..."
        />
        <button onClick={sendMessage}>发送</button>
      </div>
    </div>
  );
}

export default ChatApp;`,

    explanation: `这个示例展示了useImperativeHandle在滚动控制中的高级应用：

**核心技术要点**：
1. **复杂API设计**：提供多种滚动控制方法和状态查询
2. **性能优化**：使用useCallback防止不必要的重建
3. **状态同步**：滚动状态与组件状态的双向同步
4. **用户体验**：平滑滚动、自动定位、状态反馈

**高级特性**：
- 支持元素ID定位和索引定位
- 滚动状态的实时监听和反馈
- 边界检测（顶部/底部）
- 平滑滚动的性能优化

**实际应用**：
- 聊天界面的消息定位
- 长列表的快速导航
- 时间线的历史回溯
- 文档阅读的章节跳转`
  },

  {
    id: 'media-player',
    title: '复杂组件API设计',
    description: '视频播放器组件，暴露播放控制、进度管理、音量调节等完整的媒体控制API',
    difficulty: 'advanced',
    
    problem: `在媒体播放器、图表组件、富文本编辑器等复杂组件中，需要暴露大量的控制方法和状态查询接口。
这些组件通常包含复杂的内部状态和交互逻辑，需要精心设计API来平衡功能完整性和使用简洁性。`,

    solution: `通过useImperativeHandle设计分层的API结构，将相关功能分组，
提供高级操作和底层控制两个层次的接口，支持插件化扩展和事件回调机制。`,

    keyTechniques: [
      '分层API架构设计',
      '复杂状态管理和同步',
      '事件系统和回调机制',
      '插件化扩展支持',
      '错误处理和容错机制'
    ],

    businessValue: '支持复杂业务场景的组件化开发，提供企业级组件库的API设计范式',

    code: `import React, { 
  useRef, 
  useImperativeHandle, 
  forwardRef, 
  useState, 
  useEffect, 
  useCallback 
} from 'react';

// 播放状态枚举
enum PlaybackState {
  IDLE = 'idle',
  LOADING = 'loading',
  PLAYING = 'playing',
  PAUSED = 'paused',
  ENDED = 'ended',
  ERROR = 'error'
}

// 视频信息接口
interface VideoInfo {
  duration: number;
  currentTime: number;
  buffered: number;
  volume: number;
  muted: boolean;
  playbackRate: number;
  resolution: { width: number; height: number };
}

// 播放器事件接口
interface PlayerEvents {
  onStateChange?: (state: PlaybackState) => void;
  onProgress?: (currentTime: number, duration: number) => void;
  onVolumeChange?: (volume: number, muted: boolean) => void;
  onError?: (error: string) => void;
  onFullscreenChange?: (isFullscreen: boolean) => void;
}

// 播放器API接口
interface VideoPlayerAPI {
  // 基础播放控制
  play: () => Promise<void>;
  pause: () => void;
  stop: () => void;
  seek: (time: number) => void;
  
  // 音量控制
  setVolume: (volume: number) => void;
  mute: () => void;
  unmute: () => void;
  toggleMute: () => void;
  
  // 播放速度控制
  setPlaybackRate: (rate: number) => void;
  
  // 全屏控制
  enterFullscreen: () => Promise<void>;
  exitFullscreen: () => Promise<void>;
  toggleFullscreen: () => Promise<void>;
  
  // 状态查询
  getState: () => PlaybackState;
  getCurrentTime: () => number;
  getDuration: () => number;
  getVideoInfo: () => VideoInfo;
  isPlaying: () => boolean;
  isPaused: () => boolean;
  isMuted: () => boolean;
  isFullscreen: () => boolean;
  
  // 高级功能
  takeScreenshot: () => Promise<string>;
  setSubtitles: (url: string) => void;
  addMarker: (time: number, label: string) => void;
  removeMarker: (time: number) => void;
  
  // 插件接口
  registerPlugin: (name: string, plugin: any) => void;
  getPlugin: (name: string) => any;
}

// 播放器Props
interface VideoPlayerProps extends PlayerEvents {
  src: string;
  poster?: string;
  autoplay?: boolean;
  controls?: boolean;
  loop?: boolean;
  muted?: boolean;
  className?: string;
  width?: number;
  height?: number;
}

// 视频播放器组件
const VideoPlayer = forwardRef<VideoPlayerAPI, VideoPlayerProps>((props, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const [state, setState] = useState<PlaybackState>(PlaybackState.IDLE);
  const [videoInfo, setVideoInfo] = useState<VideoInfo>({
    duration: 0,
    currentTime: 0,
    buffered: 0,
    volume: 1,
    muted: false,
    playbackRate: 1,
    resolution: { width: 0, height: 0 }
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [markers, setMarkers] = useState<Array<{time: number, label: string}>>([]);
  const [plugins, setPlugins] = useState<Map<string, any>>(new Map());

  // 暴露播放器API
  useImperativeHandle(ref, () => ({
    // 基础播放控制
    play: async () => {
      const video = videoRef.current;
      if (video) {
        try {
          setState(PlaybackState.LOADING);
          await video.play();
          setState(PlaybackState.PLAYING);
        } catch (error) {
          setState(PlaybackState.ERROR);
          props.onError?.("播放失败: " + error);
          throw error;
        }
      }
    },

    pause: () => {
      const video = videoRef.current;
      if (video) {
        video.pause();
        setState(PlaybackState.PAUSED);
      }
    },

    stop: () => {
      const video = videoRef.current;
      if (video) {
        video.pause();
        video.currentTime = 0;
        setState(PlaybackState.IDLE);
      }
    },

    seek: (time: number) => {
      const video = videoRef.current;
      if (video) {
        video.currentTime = Math.max(0, Math.min(time, video.duration || 0));
      }
    },

    // 音量控制
    setVolume: (volume: number) => {
      const video = videoRef.current;
      if (video) {
        const clampedVolume = Math.max(0, Math.min(1, volume));
        video.volume = clampedVolume;
        setVideoInfo(prev => ({ ...prev, volume: clampedVolume }));
        props.onVolumeChange?.(clampedVolume, video.muted);
      }
    },

    mute: () => {
      const video = videoRef.current;
      if (video) {
        video.muted = true;
        setVideoInfo(prev => ({ ...prev, muted: true }));
        props.onVolumeChange?.(video.volume, true);
      }
    },

    unmute: () => {
      const video = videoRef.current;
      if (video) {
        video.muted = false;
        setVideoInfo(prev => ({ ...prev, muted: false }));
        props.onVolumeChange?.(video.volume, false);
      }
    },

    toggleMute: () => {
      const video = videoRef.current;
      if (video) {
        const newMuted = !video.muted;
        video.muted = newMuted;
        setVideoInfo(prev => ({ ...prev, muted: newMuted }));
        props.onVolumeChange?.(video.volume, newMuted);
      }
    },

    // 播放速度控制
    setPlaybackRate: (rate: number) => {
      const video = videoRef.current;
      if (video) {
        const clampedRate = Math.max(0.25, Math.min(4, rate));
        video.playbackRate = clampedRate;
        setVideoInfo(prev => ({ ...prev, playbackRate: clampedRate }));
      }
    },

    // 全屏控制
    enterFullscreen: async () => {
      const container = containerRef.current;
      if (container && container.requestFullscreen) {
        try {
          await container.requestFullscreen();
          setIsFullscreen(true);
          props.onFullscreenChange?.(true);
        } catch (error) {
          console.error('进入全屏失败:', error);
        }
      }
    },

    exitFullscreen: async () => {
      if (document.fullscreenElement) {
        try {
          await document.exitFullscreen();
          setIsFullscreen(false);
          props.onFullscreenChange?.(false);
        } catch (error) {
          console.error('退出全屏失败:', error);
        }
      }
    },

    toggleFullscreen: async () => {
      if (isFullscreen) {
        await (ref as any).current?.exitFullscreen();
      } else {
        await (ref as any).current?.enterFullscreen();
      }
    },

    // 状态查询
    getState: () => state,
    getCurrentTime: () => videoRef.current?.currentTime || 0,
    getDuration: () => videoRef.current?.duration || 0,
    getVideoInfo: () => videoInfo,
    isPlaying: () => state === PlaybackState.PLAYING,
    isPaused: () => state === PlaybackState.PAUSED,
    isMuted: () => videoInfo.muted,
    isFullscreen: () => isFullscreen,

    // 高级功能
    takeScreenshot: async (): Promise<string> => {
      const video = videoRef.current;
      if (video) {
        const canvas = document.createElement('canvas');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
          return canvas.toDataURL('image/png');
        }
      }
      throw new Error('截图失败');
    },

    setSubtitles: (url: string) => {
      const video = videoRef.current;
      if (video) {
        // 移除现有字幕
        const existingTrack = video.querySelector('track');
        if (existingTrack) {
          video.removeChild(existingTrack);
        }
        
        // 添加新字幕
        const track = document.createElement('track');
        track.kind = 'subtitles';
        track.src = url;
        track.srclang = 'zh';
        track.label = '中文字幕';
        track.default = true;
        video.appendChild(track);
      }
    },

    addMarker: (time: number, label: string) => {
      setMarkers(prev => [...prev, { time, label }].sort((a, b) => a.time - b.time));
    },

    removeMarker: (time: number) => {
      setMarkers(prev => prev.filter(marker => marker.time !== time));
    },

    // 插件接口
    registerPlugin: (name: string, plugin: any) => {
      setPlugins(prev => new Map(prev).set(name, plugin));
    },

    getPlugin: (name: string) => {
      return plugins.get(name);
    }
  }), [state, videoInfo, isFullscreen, markers, plugins, props]);

  // 视频事件处理
  const handleTimeUpdate = useCallback(() => {
    const video = videoRef.current;
    if (video) {
      const currentTime = video.currentTime;
      const duration = video.duration || 0;
      
      setVideoInfo(prev => ({ ...prev, currentTime, duration }));
      props.onProgress?.(currentTime, duration);
    }
  }, [props.onProgress]);

  const handleLoadedMetadata = useCallback(() => {
    const video = videoRef.current;
    if (video) {
      setVideoInfo(prev => ({
        ...prev,
        duration: video.duration,
        resolution: {
          width: video.videoWidth,
          height: video.videoHeight
        }
      }));
    }
  }, []);

  const handleStateChange = useCallback((newState: PlaybackState) => {
    setState(newState);
    props.onStateChange?.(newState);
  }, [props.onStateChange]);

  // 监听全屏变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isNowFullscreen = document.fullscreenElement === containerRef.current;
      setIsFullscreen(isNowFullscreen);
      props.onFullscreenChange?.(isNowFullscreen);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, [props.onFullscreenChange]);

  return (
    <div
      ref={containerRef}
      className={(isFullscreen ? 'video-player-container fullscreen' : 'video-player-container') + ' ' + (props.className || '')}
      style={{
        width: props.width ? props.width + 'px' : '100%',
        height: props.height ? props.height + 'px' : 'auto',
        position: 'relative'
      }}
    >
      <video
        ref={videoRef}
        src={props.src}
        poster={props.poster}
        autoPlay={props.autoplay}
        controls={props.controls}
        loop={props.loop}
        muted={props.muted}
        style={{ width: '100%', height: '100%' }}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onPlay={() => handleStateChange(PlaybackState.PLAYING)}
        onPause={() => handleStateChange(PlaybackState.PAUSED)}
        onEnded={() => handleStateChange(PlaybackState.ENDED)}
        onError={(error) => handleStateChange(PlaybackState.ERROR)}
      />
      
      {/* 时间标记显示 */}
      {markers.length > 0 && (
        <div className="video-markers">
          {markers.map((marker, index) => (
            <div
              key={index}
              className="marker"
              style={{
                left: ((marker.time / videoInfo.duration) * 100) + '%',
              }}
              title={marker.label}
            />
          ))}
        </div>
      )}
      
      {/* 状态显示 */}
      <div className="video-status">
        <span>状态: {state}</span>
        <span>时间: {Math.floor(videoInfo.currentTime)}s / {Math.floor(videoInfo.duration)}s</span>
        <span>速度: {videoInfo.playbackRate}x</span>
      </div>
    </div>
  );
});

VideoPlayer.displayName = 'VideoPlayer';

// 使用示例：视频播放器应用
function VideoApp() {
  const playerRef = useRef<VideoPlayerAPI>(null);
  const [playbackState, setPlaybackState] = useState<PlaybackState>(PlaybackState.IDLE);
  const [progress, setProgress] = useState({ current: 0, duration: 0 });

  const handlePlayerStateChange = (state: PlaybackState) => {
    setPlaybackState(state);
    console.log('播放状态变化:', state);
  };

  const handleProgress = (currentTime: number, duration: number) => {
    setProgress({ current: currentTime, duration });
  };

  // 播放器控制方法
  const playerControls = {
    play: () => playerRef.current?.play(),
    pause: () => playerRef.current?.pause(),
    stop: () => playerRef.current?.stop(),
    toggleMute: () => playerRef.current?.toggleMute(),
    setSpeed: (speed: number) => playerRef.current?.setPlaybackRate(speed),
    toggleFullscreen: () => playerRef.current?.toggleFullscreen(),
    
    // 高级功能
    takeScreenshot: async () => {
      try {
        const screenshot = await playerRef.current?.takeScreenshot();
        if (screenshot) {
          // 创建下载链接
          const link = document.createElement('a');
          link.download = 'screenshot' + Date.now() + '.png';
          link.href = screenshot;
          link.click();
        }
      } catch (error) {
        console.error('截图失败:', error);
      }
    },
    
    addMarker: () => {
      const currentTime = playerRef.current?.getCurrentTime() || 0;
      const label = prompt('输入标记名称:');
      if (label) {
        playerRef.current?.addMarker(currentTime, label);
      }
    },
    
    getInfo: () => {
      const info = playerRef.current?.getVideoInfo();
      console.log('视频信息:', info);
    }
  };

  return (
    <div className="video-app">
      <h2>高级视频播放器</h2>
      
      <VideoPlayer
        ref={playerRef}
        src="https://example.com/sample-video.mp4"
        poster="https://example.com/poster.jpg"
        width={800}
        height={450}
        controls={false}
        onStateChange={handlePlayerStateChange}
        onProgress={handleProgress}
        onError={(error) => console.error('播放器错误:', error)}
      />
      
      <div className="player-controls">
        <div className="basic-controls">
          <button onClick={playerControls.play}>▶️ 播放</button>
          <button onClick={playerControls.pause}>⏸️ 暂停</button>
          <button onClick={playerControls.stop}>⏹️ 停止</button>
          <button onClick={playerControls.toggleMute}>🔇 静音</button>
        </div>
        
        <div className="advanced-controls">
          <label>
            播放速度:
            <select onChange={(e) => playerControls.setSpeed(Number(e.target.value))}>
              <option value="0.5">0.5x</option>
              <option value="1" selected>1x</option>
              <option value="1.5">1.5x</option>
              <option value="2">2x</option>
            </select>
          </label>
          
          <button onClick={playerControls.toggleFullscreen}>
            🔳 全屏
          </button>
          <button onClick={playerControls.takeScreenshot}>
            📸 截图
          </button>
          <button onClick={playerControls.addMarker}>
            📍 标记
          </button>
          <button onClick={playerControls.getInfo}>
            ℹ️ 信息
          </button>
        </div>
      </div>
      
      <div className="player-status">
        <p>状态: {playbackState}</p>
        <p>进度: {Math.floor(progress.current)}s / {Math.floor(progress.duration)}s</p>
        <div className="progress-bar">
          <div
            className="progress-fill"
            style={{
              width: ((progress.current / progress.duration) * 100 || 0) + '%'
            }}
          />
        </div>
      </div>
    </div>
  );
}

export default VideoApp;`,

    explanation: `这个示例展示了useImperativeHandle在复杂组件中的企业级应用：

**架构设计要点**：
1. **分层API结构**：基础控制、高级功能、插件接口三个层次
2. **完整的状态管理**：播放状态、音视频信息、全屏状态等
3. **事件回调机制**：状态变化、进度更新、错误处理等
4. **插件化扩展**：支持第三方功能扩展

**技术亮点**：
- TypeScript完整类型约束
- Promise-based异步API设计
- 错误处理和容错机制
- 性能优化和内存管理

**企业级特性**：
- 完整的API文档和类型提示
- 可测试的模块化设计
- 插件系统支持业务扩展
- 事件驱动的架构模式

**应用场景**：
- 在线教育平台的视频播放器
- 企业培训系统的媒体控制
- 直播平台的播放器组件
- 视频会议系统的媒体管理`
  }
];

export default businessScenarios; 