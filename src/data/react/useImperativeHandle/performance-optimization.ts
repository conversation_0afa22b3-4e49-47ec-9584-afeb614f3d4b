import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: '依赖数组优化',
      description: '合理使用依赖数组，避免不必要的handle重新创建',
      techniques: [
        {
          name: '稳定化依赖项',
          description: '使用useCallback和useMemo稳定依赖项，避免handle频繁重新创建',
          code: `// ❌ 性能问题 - 依赖项不稳定导致频繁重新创建
function BadComponent({ onSubmit, config }) {
  const inputRef = useRef(null);
  
  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
    submit: () => {
      // 每次渲染都会重新创建，因为onSubmit和config可能是新的引用
      onSubmit(inputRef.current?.value, config);
    },
    validate: () => {
      // 复杂的验证逻辑
      return config.rules.every(rule => rule.test(inputRef.current?.value));
    }
  }), [onSubmit, config]); // 不稳定的依赖
  
  return <input ref={inputRef} />;
}

// ✅ 性能优化 - 稳定化依赖项
function GoodComponent({ onSubmit, config }) {
  const inputRef = useRef(null);
  
  // 稳定化回调函数
  const stableOnSubmit = useCallback((value, cfg) => {
    onSubmit(value, cfg);
  }, [onSubmit]);
  
  // 稳定化配置对象
  const stableConfig = useMemo(() => ({
    rules: config.rules,
    options: config.options
  }), [config.rules, config.options]);
  
  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
    submit: () => {
      stableOnSubmit(inputRef.current?.value, stableConfig);
    },
    validate: () => {
      return stableConfig.rules.every(rule => rule.test(inputRef.current?.value));
    }
  }), [stableOnSubmit, stableConfig]); // 稳定的依赖
  
  return <input ref={inputRef} />;
}

// ✅ 更好的优化 - 最小化依赖
function BestComponent({ onSubmit, config }) {
  const inputRef = useRef(null);
  const onSubmitRef = useRef(onSubmit);
  const configRef = useRef(config);
  
  // 保持ref同步
  useEffect(() => {
    onSubmitRef.current = onSubmit;
    configRef.current = config;
  });
  
  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
    submit: () => {
      onSubmitRef.current(inputRef.current?.value, configRef.current);
    },
    validate: () => {
      return configRef.current.rules.every(rule => 
        rule.test(inputRef.current?.value)
      );
    }
  }), []); // 空依赖数组，handle只创建一次
  
  return <input ref={inputRef} />;
}`,
          impact: 'high',
          difficulty: 'medium'
        },
        {
          name: '延迟计算优化',
          description: '在handle方法中进行延迟计算，而不是在创建时预计算',
          code: `// ❌ 性能问题 - 在handle创建时进行昂贵计算
function BadExpensiveComponent({ data }) {
  const [processedData, setProcessedData] = useState([]);
  
  useImperativeHandle(ref, () => {
    // 每次依赖变化都会执行昂贵计算
    const expensiveResult = data.map(item => ({
      ...item,
      computed: heavyComputation(item),
      formatted: formatData(item)
    }));
    
    return {
      getData: () => expensiveResult,
      getCount: () => expensiveResult.length,
      export: () => JSON.stringify(expensiveResult)
    };
  }, [data]); // data变化时重新计算
  
  return <div>数据处理组件</div>;
}

// ✅ 性能优化 - 延迟计算
function GoodExpensiveComponent({ data }) {
  const cacheRef = useRef(new Map());
  
  useImperativeHandle(ref, () => ({
    getData: () => {
      // 延迟计算，只在需要时执行
      if (!cacheRef.current.has('processedData')) {
        const result = data.map(item => ({
          ...item,
          computed: heavyComputation(item),
          formatted: formatData(item)
        }));
        cacheRef.current.set('processedData', result);
      }
      return cacheRef.current.get('processedData');
    },
    getCount: () => {
      const processedData = cacheRef.current.get('processedData');
      return processedData ? processedData.length : data.length;
    },
    export: () => {
      const processedData = cacheRef.current.get('processedData');
      return JSON.stringify(processedData || data);
    },
    clearCache: () => {
      cacheRef.current.clear();
    }
  }), []); // 空依赖，handle只创建一次
  
  // 数据变化时清理缓存
  useEffect(() => {
    cacheRef.current.clear();
  }, [data]);
  
  return <div>数据处理组件</div>;
}`,
          impact: 'high',
          difficulty: 'hard'
        }
      ]
    },
    {
      title: 'API设计优化',
      description: '设计高效的命令式API，减少不必要的计算和内存占用',
      techniques: [
        {
          name: '方法合并优化',
          description: '将相关操作合并为单个方法，减少多次调用的开销',
          code: `// ❌ 性能问题 - 多个独立方法导致多次操作
function BadAPIComponent() {
  const listRef = useRef([]);
  
  useImperativeHandle(ref, () => ({
    addItem: (item) => {
      listRef.current.push(item);
      updateUI();
    },
    removeItem: (id) => {
      listRef.current = listRef.current.filter(item => item.id !== id);
      updateUI();
    },
    updateItem: (id, updates) => {
      const index = listRef.current.findIndex(item => item.id === id);
      if (index !== -1) {
        listRef.current[index] = { ...listRef.current[index], ...updates };
        updateUI();
      }
    },
    sortItems: (compareFn) => {
      listRef.current.sort(compareFn);
      updateUI();
    }
  }), []);
  
  const updateUI = () => {
    // 每次操作都触发UI更新，性能差
    forceUpdate();
  };
  
  return <div>列表组件</div>;
}

// ✅ 性能优化 - 批量操作和延迟更新
function GoodAPIComponent() {
  const listRef = useRef([]);
  const pendingUpdates = useRef(false);
  
  useImperativeHandle(ref, () => ({
    // 批量操作方法
    batchUpdate: (operations) => {
      operations.forEach(op => {
        switch (op.type) {
          case 'add':
            listRef.current.push(op.item);
            break;
          case 'remove':
            listRef.current = listRef.current.filter(item => item.id !== op.id);
            break;
          case 'update':
            const index = listRef.current.findIndex(item => item.id === op.id);
            if (index !== -1) {
              listRef.current[index] = { ...listRef.current[index], ...op.updates };
            }
            break;
        }
      });
      scheduleUpdate();
    },
    
    // 单个操作方法（内部使用批量逻辑）
    addItem: (item) => {
      listRef.current.push(item);
      scheduleUpdate();
    },
    
    removeItem: (id) => {
      listRef.current = listRef.current.filter(item => item.id !== id);
      scheduleUpdate();
    },
    
    // 获取数据的方法（不触发更新）
    getItems: () => [...listRef.current],
    getItemCount: () => listRef.current.length,
    
    // 手动刷新UI
    forceRefresh: () => {
      forceUpdate();
    }
  }), []);
  
  const scheduleUpdate = () => {
    if (!pendingUpdates.current) {
      pendingUpdates.current = true;
      // 使用requestAnimationFrame延迟更新
      requestAnimationFrame(() => {
        pendingUpdates.current = false;
        forceUpdate();
      });
    }
  };
  
  return <div>列表组件</div>;
}`,
          impact: 'medium',
          difficulty: 'medium'
        }
      ]
    }
  ],

  performanceMetrics: {
    handleCreationTime: {
      description: '测量handle对象创建的时间开销',
      tool: 'Performance API',
      example: 'performance.mark("handle-start"); /* useImperativeHandle */ performance.mark("handle-end");'
    },
    methodCallTime: {
      description: '监控暴露方法的执行时间',
      tool: 'Console.time',
      example: 'console.time("method-call"); ref.current.method(); console.timeEnd("method-call");'
    },
    memoryUsage: {
      description: '监控handle对象和相关数据的内存占用',
      tool: 'Chrome DevTools Memory',
      example: '对比使用useImperativeHandle前后的内存快照'
    }
  },

  bestPractices: [
    '使用空依赖数组或稳定的依赖项，避免handle频繁重新创建',
    '在handle方法中进行延迟计算，而不是在创建时预计算',
    '设计批量操作的API，减少多次调用的开销',
    '使用ref存储状态，避免在handle中捕获过期的闭包',
    '合理设计API粒度，平衡功能性和性能',
    '避免在handle中返回大对象或复杂数据结构',
    '使用缓存机制优化重复计算',
    '监控handle方法的执行性能，及时优化瓶颈'
  ],

  commonPitfalls: [
    {
      issue: '依赖数组包含不稳定的引用导致频繁重新创建',
      cause: '每次渲染时传入的props或state都是新的对象引用',
      solution: '使用useCallback和useMemo稳定依赖项，或使用ref存储最新值'
    },
    {
      issue: '在handle创建时执行昂贵计算导致性能问题',
      cause: '将计算逻辑放在useImperativeHandle的第二个参数中',
      solution: '将计算逻辑移到handle方法内部，实现延迟计算'
    },
    {
      issue: '过度细粒度的API设计导致频繁调用',
      cause: '将每个小操作都暴露为独立的方法',
      solution: '设计批量操作的API，或提供组合操作的方法'
    }
  ]
};

export default performanceOptimization;
