import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  overview: `useImperativeHandle作为命令式API的暴露机制，在调试时容易出现ref为null、方法调用失败、forwardRef配置错误等问题。这些问题往往表现为运行时错误、方法无法调用或组件行为异常，需要专门的调试方法来快速定位和解决。

本指南提供4种核心调试策略、6个实用调试工具，以及5个预防技巧，帮助开发者掌握useImperativeHandle的调试精髓，避免常见陷阱，构建可靠的命令式API。`,
  
  troubleshooting: [
    {
      symptom: 'ref.current为null，无法调用暴露的方法',
      possibleCauses: [
        '组件没有使用forwardRef包装',
        'ref在组件挂载前被访问',
        'useImperativeHandle的依赖数组导致handle未创建'
      ],
      solutions: [
        '确保组件使用forwardRef包装',
        '在useEffect或事件处理器中访问ref',
        '检查依赖数组的正确性'
      ],
      codeExample: `// 调试ref为null的问题
import React, { forwardRef, useImperativeHandle, useRef, useEffect } from 'react';

// ❌ 错误：没有使用forwardRef
function BadComponent(props, ref) {
  useImperativeHandle(ref, () => ({
    focus: () => console.log('focus called')
  }));
  
  return <input />;
}

// ✅ 正确：使用forwardRef
const GoodComponent = forwardRef((props, ref) => {
  const inputRef = useRef(null);
  
  useImperativeHandle(ref, () => ({
    focus: () => {
      console.log('focus called');
      inputRef.current?.focus();
    },
    getValue: () => inputRef.current?.value || ''
  }), []);
  
  return <input ref={inputRef} />;
});

// 调试工具：ref状态检查器
function RefDebugger() {
  const componentRef = useRef(null);
  const [debugInfo, setDebugInfo] = useState({});
  
  useEffect(() => {
    // 检查ref状态
    const checkRef = () => {
      const info = {
        refExists: !!componentRef.current,
        refType: componentRef.current?.constructor.name,
        availableMethods: componentRef.current ? 
          Object.getOwnPropertyNames(componentRef.current) : [],
        timestamp: new Date().toISOString()
      };
      
      setDebugInfo(info);
      console.log('🔍 Ref调试信息:', info);
    };
    
    // 立即检查
    checkRef();
    
    // 延迟检查（确保组件已挂载）
    const timer = setTimeout(checkRef, 100);
    
    return () => clearTimeout(timer);
  }, []);
  
  const testMethods = () => {
    if (componentRef.current) {
      try {
        console.log('测试focus方法');
        componentRef.current.focus();
        
        console.log('测试getValue方法');
        const value = componentRef.current.getValue();
        console.log('获取到的值:', value);
      } catch (error) {
        console.error('方法调用失败:', error);
      }
    } else {
      console.error('❌ ref.current为null，无法调用方法');
    }
  };
  
  return (
    <div>
      <GoodComponent ref={componentRef} />
      <button onClick={testMethods}>测试方法调用</button>
      
      <div style={{ marginTop: '10px', fontSize: '12px' }}>
        <h4>调试信息:</h4>
        <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
      </div>
    </div>
  );
}

// 高级调试：方法调用追踪
function createTrackedHandle(originalHandle, componentName = 'Unknown') {
  const trackedHandle = {};
  
  Object.keys(originalHandle).forEach(key => {
    if (typeof originalHandle[key] === 'function') {
      trackedHandle[key] = (...args) => {
        console.log(\`📞 [\${componentName}] 调用方法: \${key}\`, args);
        const start = performance.now();
        
        try {
          const result = originalHandle[key](...args);
          const duration = performance.now() - start;
          console.log(\`✅ [\${componentName}] 方法 \${key} 执行成功，耗时: \${duration.toFixed(2)}ms\`);
          return result;
        } catch (error) {
          const duration = performance.now() - start;
          console.error(\`❌ [\${componentName}] 方法 \${key} 执行失败，耗时: \${duration.toFixed(2)}ms\`, error);
          throw error;
        }
      };
    } else {
      trackedHandle[key] = originalHandle[key];
    }
  });
  
  return trackedHandle;
}

// 使用追踪的组件
const TrackedComponent = forwardRef((props, ref) => {
  const inputRef = useRef(null);
  
  useImperativeHandle(ref, () => {
    const handle = {
      focus: () => inputRef.current?.focus(),
      clear: () => {
        if (inputRef.current) {
          inputRef.current.value = '';
        }
      },
      getValue: () => inputRef.current?.value || ''
    };
    
    // 在开发环境中启用追踪
    return process.env.NODE_ENV === 'development' 
      ? createTrackedHandle(handle, 'TrackedComponent')
      : handle;
  }, []);
  
  return <input ref={inputRef} />;
});`,
      severity: 'high'
    },
    {
      symptom: '暴露的方法调用后没有预期效果',
      possibleCauses: [
        '方法内部的DOM引用为null',
        '闭包捕获了过期的状态值',
        '依赖数组设置错误导致方法过期'
      ],
      solutions: [
        '检查内部DOM引用的有效性',
        '使用ref存储最新状态值',
        '正确设置依赖数组'
      ],
      codeExample: `// 调试方法效果问题
function MethodEffectDebugger() {
  const [count, setCount] = useState(0);
  const [message, setMessage] = useState('');
  const componentRef = useRef(null);
  
  // ❌ 问题：闭包捕获过期值
  const BadComponent = forwardRef((props, ref) => {
    const inputRef = useRef(null);
    
    useImperativeHandle(ref, () => ({
      updateWithCount: () => {
        // 这里的count可能是过期的值
        if (inputRef.current) {
          inputRef.current.value = \`Count: \${count}\`;
        }
      },
      showMessage: () => {
        // 这里的message可能是过期的值
        alert(message);
      }
    }), []); // 空依赖数组导致闭包问题
    
    return <input ref={inputRef} />;
  });
  
  // ✅ 正确：使用ref存储最新值
  const GoodComponent = forwardRef((props, ref) => {
    const inputRef = useRef(null);
    const countRef = useRef(count);
    const messageRef = useRef(message);
    
    // 保持ref同步
    useEffect(() => {
      countRef.current = count;
      messageRef.current = message;
    });
    
    useImperativeHandle(ref, () => ({
      updateWithCount: () => {
        console.log('🔄 updateWithCount调用');
        console.log('当前count值:', countRef.current);
        
        if (inputRef.current) {
          inputRef.current.value = \`Count: \${countRef.current}\`;
          console.log('✅ 输入框值已更新');
        } else {
          console.error('❌ inputRef.current为null');
        }
      },
      showMessage: () => {
        console.log('💬 showMessage调用');
        console.log('当前message值:', messageRef.current);
        alert(messageRef.current || '无消息');
      },
      // 调试方法：获取内部状态
      getDebugInfo: () => ({
        count: countRef.current,
        message: messageRef.current,
        inputValue: inputRef.current?.value,
        inputExists: !!inputRef.current
      })
    }), []);
    
    return <input ref={inputRef} placeholder="这里会显示count值" />;
  });
  
  const testMethods = () => {
    if (componentRef.current) {
      // 获取调试信息
      const debugInfo = componentRef.current.getDebugInfo();
      console.log('📊 组件内部状态:', debugInfo);
      
      // 测试方法
      componentRef.current.updateWithCount();
      componentRef.current.showMessage();
    }
  };
  
  return (
    <div>
      <div>
        <label>Count: {count}</label>
        <button onClick={() => setCount(c => c + 1)}>增加</button>
      </div>
      
      <div>
        <label>Message:</label>
        <input 
          value={message} 
          onChange={(e) => setMessage(e.target.value)}
          placeholder="输入消息"
        />
      </div>
      
      <GoodComponent ref={componentRef} />
      <button onClick={testMethods}>测试方法</button>
    </div>
  );
}`,
      severity: 'medium'
    },
    {
      symptom: 'TypeScript类型错误或方法不存在',
      possibleCauses: [
        'ref类型定义不正确',
        'forwardRef的泛型参数错误',
        'useImperativeHandle返回的对象类型不匹配'
      ],
      solutions: [
        '正确定义ref的类型',
        '使用正确的forwardRef泛型',
        '确保类型定义与实际返回对象一致'
      ],
      codeExample: `// 调试TypeScript类型问题
import React, { forwardRef, useImperativeHandle, useRef } from 'react';

// 定义API接口
interface CustomInputAPI {
  focus: () => void;
  clear: () => void;
  getValue: () => string;
  setValue: (value: string) => void;
}

// ❌ 类型问题：泛型参数错误
const BadTypedComponent = forwardRef<HTMLInputElement, {}>((props, ref) => {
  const inputRef = useRef<HTMLInputElement>(null);
  
  // 类型错误：ref期望HTMLInputElement，但我们返回自定义对象
  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
    clear: () => {
      if (inputRef.current) {
        inputRef.current.value = '';
      }
    }
  }), []);
  
  return <input ref={inputRef} />;
});

// ✅ 正确：使用正确的类型定义
const GoodTypedComponent = forwardRef<CustomInputAPI, {}>((props, ref) => {
  const inputRef = useRef<HTMLInputElement>(null);
  
  useImperativeHandle(ref, (): CustomInputAPI => ({
    focus: () => {
      inputRef.current?.focus();
    },
    clear: () => {
      if (inputRef.current) {
        inputRef.current.value = '';
      }
    },
    getValue: () => {
      return inputRef.current?.value || '';
    },
    setValue: (value: string) => {
      if (inputRef.current) {
        inputRef.current.value = value;
      }
    }
  }), []);
  
  return <input ref={inputRef} />;
});

// 类型安全的使用方式
function TypeSafeUsage() {
  const componentRef = useRef<CustomInputAPI>(null);
  
  const handleTest = () => {
    if (componentRef.current) {
      // TypeScript会提供完整的类型检查和自动补全
      componentRef.current.focus();
      componentRef.current.setValue('测试值');
      const value = componentRef.current.getValue();
      console.log('获取到的值:', value);
    }
  };
  
  return (
    <div>
      <GoodTypedComponent ref={componentRef} />
      <button onClick={handleTest}>测试类型安全的方法</button>
    </div>
  );
}

// 调试工具：运行时类型检查
function createTypeCheckedHandle<T>(handle: T, expectedInterface: (keyof T)[]): T {
  const checkedHandle = {} as T;
  
  expectedInterface.forEach(key => {
    if (key in handle) {
      if (typeof handle[key] === 'function') {
        (checkedHandle as any)[key] = (...args: any[]) => {
          console.log(\`🔍 调用方法: \${String(key)}\`, args);
          return (handle[key] as any)(...args);
        };
      } else {
        (checkedHandle as any)[key] = handle[key];
      }
    } else {
      console.error(\`❌ 缺少必需的方法或属性: \${String(key)}\`);
    }
  });
  
  return checkedHandle;
}`,
      severity: 'medium'
    }
  ],
  
  tools: [
    {
      name: 'React DevTools',
      description: 'React官方开发工具，用于检查组件的ref和forwardRef状态',
      usage: '在Components面板中查看组件的ref属性和暴露的方法',
      category: '浏览器扩展',
      documentation: 'https://react.dev/learn/react-developer-tools'
    },
    {
      name: 'Chrome DevTools Console',
      description: '浏览器控制台，用于测试和调试暴露的方法',
      usage: '使用console.log记录方法调用，设置断点调试方法执行',
      category: '浏览器工具',
      documentation: 'https://developer.chrome.com/docs/devtools/console/'
    },
    {
      name: 'TypeScript Compiler',
      description: 'TypeScript编译器，用于检查类型定义的正确性',
      usage: '使用tsc --noEmit检查类型错误，确保ref类型定义正确',
      category: '开发工具',
      documentation: 'https://www.typescriptlang.org/docs/'
    }
  ],
  
  bestPractices: [
    '使用React DevTools检查组件的ref状态和暴露的方法',
    '在开发环境中添加方法调用追踪，便于调试',
    '使用TypeScript确保类型安全，避免运行时错误',
    '建立ref状态检查机制，及时发现ref为null的问题',
    '在方法内部添加防御性编程，检查DOM引用的有效性'
  ],
  
  commonMistakes: [
    {
      mistake: '忘记使用forwardRef包装组件',
      consequence: 'ref参数为null，useImperativeHandle无法正常工作',
      solution: '确保组件使用forwardRef包装，并正确传递ref参数',
      prevention: '建立代码审查规则，检查useImperativeHandle的使用是否配合forwardRef'
    },
    {
      mistake: '在组件挂载前访问ref.current',
      consequence: 'ref.current为null，导致方法调用失败',
      solution: '在useEffect或事件处理器中访问ref，确保组件已挂载',
      prevention: '使用ESLint规则检查ref的访问时机'
    },
    {
      mistake: '依赖数组设置错误导致闭包问题',
      consequence: '暴露的方法捕获过期的状态值，行为不符合预期',
      solution: '使用ref存储最新状态值，或正确设置依赖数组',
      prevention: '理解useImperativeHandle的依赖机制，建立最佳实践'
    }
  ]
};

export default debuggingTips;
