import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 1,
    question: '过度使用useImperativeHandle，总是想用命令式API解决问题，如何避免？',
    answer: `过度使用useImperativeHandle违背了React的设计哲学，会导致代码难以维护和测试。建立正确的判断标准和使用原则是关键。

## 判断是否应该使用的标准

### ✅ 应该使用的场景
1. **DOM直接操作需求**：focus、scroll、测量等
2. **第三方库集成**：需要命令式API的外部库
3. **复杂动画控制**：需要精确时机控制的动画
4. **性能关键路径**：避免不必要的重渲染

### ❌ 不应该使用的场景  
1. **简单状态管理**：用props和回调就能解决
2. **数据流控制**：应该通过状态提升处理
3. **组件间通信**：用Context或状态管理库
4. **条件渲染**：用声明式的条件语句

## 重构指南`,
    
    code: `// ❌ 过度使用：简单状态用命令式
const BadCounter = forwardRef((props, ref) => {
  const [count, setCount] = useState(0);
  
  useImperativeHandle(ref, () => ({
    increment: () => setCount(c => c + 1),
    decrement: () => setCount(c => c - 1),
    reset: () => setCount(0),
    getValue: () => count
  }));
  
  return <div>{count}</div>;
});

function BadParent() {
  const counterRef = useRef();
  
  return (
    <div>
      <BadCounter ref={counterRef} />
      <button onClick={() => counterRef.current?.increment()}>+</button>
      <button onClick={() => counterRef.current?.decrement()}>-</button>
    </div>
  );
}

// ✅ 重构为声明式
function GoodCounter({ value, onChange }) {
  return <div>{value}</div>;
}

function GoodParent() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <GoodCounter value={count} onChange={setCount} />
      <button onClick={() => setCount(c => c + 1)}>+</button>
      <button onClick={() => setCount(c => c - 1)}>-</button>
      <button onClick={() => setCount(0)}>重置</button>
    </div>
  );
}

// ✅ 合理使用：DOM操作
const GoodInput = forwardRef((props, ref) => {
  const inputRef = useRef();
  
  useImperativeHandle(ref, () => ({
    focus: () => inputRef.current?.focus(),
    blur: () => inputRef.current?.blur(),
    select: () => inputRef.current?.select()
  }), []);
  
  return <input ref={inputRef} {...props} />;
});`,
    
    tags: ['过度使用', '重构', '声明式设计', '最佳实践'],
    category: 'best-practices'
  },

  {
    id: 2,
    question: 'useImperativeHandle与React声明式范式冲突，如何在两者之间找到平衡？',
    answer: `useImperativeHandle确实打破了React的声明式原则，但在特定场景下是必要的。关键是建立清晰的边界和使用原则。

## 平衡策略

### 1. 分层设计原则
在组件内部保持声明式，只在必要的边界暴露命令式API：`,
    
    code: `// ✅ 良好的平衡设计
const MediaPlayer = forwardRef((props, ref) => {
  // 内部使用声明式状态管理
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(1);
  const videoRef = useRef();
  
  // 只在边界暴露必要的命令式API
  useImperativeHandle(ref, () => ({
    // 暴露DOM无法声明式处理的操作
    play: async () => {
      await videoRef.current?.play();
      setIsPlaying(true); // 内部状态仍然声明式管理
    },
    
    pause: () => {
      videoRef.current?.pause();
      setIsPlaying(false);
    },
    
    seek: (time) => {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  }), []);
  
  // 组件内部渲染完全声明式
  return (
    <div className="media-player">
      <video
        ref={videoRef}
        onTimeUpdate={(e) => setCurrentTime(e.target.currentTime)}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
      />
      
      {/* 声明式UI */}
      <div className="controls">
        <button onClick={() => setIsPlaying(!isPlaying)}>
          {isPlaying ? '暂停' : '播放'}
        </button>
        <input
          type="range"
          value={volume}
          onChange={(e) => setVolume(e.target.value)}
        />
      </div>
      
      {/* 状态显示 */}
      <div className="status">
        状态: {isPlaying ? '播放中' : '已暂停'}
        时间: {currentTime}s
      </div>
    </div>
  );
});

// 父组件可以选择使用声明式props或命令式API
function App() {
  const playerRef = useRef();
  const [shouldPlay, setShouldPlay] = useState(false);
  
  // 快捷键等场景使用命令式API
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.code === 'Space') {
        if (shouldPlay) {
          playerRef.current?.pause();
        } else {
          playerRef.current?.play();
        }
        setShouldPlay(!shouldPlay);
      }
    };
    
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [shouldPlay]);
  
  return (
    <div>
      <MediaPlayer ref={playerRef} />
      <button onClick={() => playerRef.current?.seek(0)}>
        回到开始
      </button>
    </div>
  );
}`,
    
    followUpCode: `// ✅ 混合模式：同时支持声明式和命令式
const FlexibleComponent = forwardRef(({ 
  value, 
  onChange, 
  autoFocus = false 
}, ref) => {
  const [internalValue, setInternalValue] = useState('');
  const inputRef = useRef();
  
  // 声明式props优先
  const currentValue = value !== undefined ? value : internalValue;
  const handleChange = value !== undefined ? onChange : setInternalValue;
  
  useImperativeHandle(ref, () => ({
    // 命令式API作为补充
    focus: () => inputRef.current?.focus(),
    blur: () => inputRef.current?.blur(),
    clear: () => {
      handleChange('');
      inputRef.current?.focus();
    }
  }), [handleChange]);
  
  // 自动聚焦：声明式配置 + 命令式实现
  useEffect(() => {
    if (autoFocus) {
      inputRef.current?.focus();
    }
  }, [autoFocus]);
  
  return (
    <input
      ref={inputRef}
      value={currentValue}
      onChange={(e) => handleChange(e.target.value)}
    />
  );
});`,
    
    tags: ['声明式vs命令式', '设计平衡', '混合模式', '架构设计'],
    category: 'design-principles'
  },

  {
    id: 3,
    question: 'TypeScript中useImperativeHandle的类型定义总是很复杂，如何优雅地处理类型？',
    answer: `TypeScript与useImperativeHandle的类型定义确实复杂，但通过合理的类型设计可以大大简化使用。

## 类型设计策略

### 1. 接口优先设计
先定义清晰的API接口，再实现组件：`,
    
    code: `// ✅ 第一步：定义清晰的API接口
interface InputControlAPI {
  focus(): void;
  blur(): void;
  clear(): void;
  getValue(): string;
  setValue(value: string): void;
  isValid(): boolean;
}

interface InputControlProps {
  defaultValue?: string;
  placeholder?: string;
  maxLength?: number;
  validator?: (value: string) => boolean;
  onChange?: (value: string) => void;
}

// 第二步：实现组件时类型会自动推导
const InputControl = forwardRef<InputControlAPI, InputControlProps>((props, ref) => {
  const [value, setValue] = useState(props.defaultValue || '');
  const [isValid, setIsValid] = useState(true);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const validate = useCallback((val: string) => {
    const valid = props.validator ? props.validator(val) : true;
    setIsValid(valid);
    return valid;
  }, [props.validator]);
  
  // TypeScript会自动检查API完整性
  useImperativeHandle(ref, (): InputControlAPI => ({
    focus: () => inputRef.current?.focus(),
    blur: () => inputRef.current?.blur(),
    clear: () => {
      setValue('');
      props.onChange?.('');
      validate('');
    },
    getValue: () => value,
    setValue: (newValue: string) => {
      setValue(newValue);
      props.onChange?.(newValue);
      validate(newValue);
    },
    isValid: () => isValid
  }), [value, isValid, props.onChange, validate]);
  
  return (
    <input
      ref={inputRef}
      value={value}
      placeholder={props.placeholder}
      maxLength={props.maxLength}
      onChange={(e) => {
        const newValue = e.target.value;
        setValue(newValue);
        props.onChange?.(newValue);
        validate(newValue);
      }}
      className={isValid ? 'valid' : 'invalid'}
    />
  );
});`,
    
    followUpCode: `// ✅ 高级类型技巧：泛型和条件类型
interface BaseAPI {
  focus(): void;
  blur(): void;
}

interface ExtendedAPI<T = string> extends BaseAPI {
  getValue(): T;
  setValue(value: T): void;
  clear(): void;
}

// 泛型组件支持不同的值类型
function createTypedInput<T = string>() {
  return forwardRef<ExtendedAPI<T>, {
    value?: T;
    onChange?: (value: T) => void;
    converter?: (input: string) => T;
    formatter?: (value: T) => string;
  }>((props, ref) => {
    const [internalValue, setInternalValue] = useState<T | undefined>(props.value);
    const inputRef = useRef<HTMLInputElement>(null);
    
    useImperativeHandle(ref, (): ExtendedAPI<T> => ({
      focus: () => inputRef.current?.focus(),
      blur: () => inputRef.current?.blur(),
      getValue: () => internalValue!,
      setValue: (value: T) => {
        setInternalValue(value);
        props.onChange?.(value);
      },
      clear: () => {
        setInternalValue(undefined);
        if (inputRef.current) {
          inputRef.current.value = '';
        }
      }
    }), [internalValue, props.onChange]);
    
    return (
      <input
        ref={inputRef}
        defaultValue={props.formatter ? props.formatter(internalValue!) : String(internalValue || '')}
        onChange={(e) => {
          const converted = props.converter ? props.converter(e.target.value) : e.target.value as T;
          setInternalValue(converted);
          props.onChange?.(converted);
        }}
      />
    );
  });
}

// 使用示例：类型安全的特化组件
const NumberInput = createTypedInput<number>();
const DateInput = createTypedInput<Date>();

function TypedInputExample() {
  const numberRef = useRef<ExtendedAPI<number>>(null);
  const dateRef = useRef<ExtendedAPI<Date>>(null);
  
  return (
    <div>
      <NumberInput
        ref={numberRef}
        converter={(str) => parseFloat(str) || 0}
        formatter={(num) => num.toString()}
        onChange={(num) => console.log('数字:', num)}
      />
      
      <DateInput
        ref={dateRef}
        converter={(str) => new Date(str)}
        formatter={(date) => date.toISOString().split('T')[0]}
        onChange={(date) => console.log('日期:', date)}
      />
      
      <button onClick={() => {
        // TypeScript确保类型安全
        const num = numberRef.current?.getValue(); // number
        const date = dateRef.current?.getValue(); // Date
      }}>
        获取值
      </button>
    </div>
  );
}`,
    
    tags: ['TypeScript', '类型设计', '泛型', '接口定义'],
    category: 'typescript'
  },

  {
    id: 4,
    question: '在复杂组件中，如何设计和维护大量的useImperativeHandle API？',
    answer: `复杂组件的API设计需要考虑可维护性、扩展性和用户体验。通过分层架构和模块化设计可以有效管理复杂度。

## 大型API设计策略

### 1. 分层模块化设计
将API按功能分组，避免单一对象包含过多方法：`,
    
    code: `// ✅ 分层API设计
interface PlayerControlAPI {
  play(): Promise<void>;
  pause(): void;
  stop(): void;
  seek(time: number): void;
}

interface PlayerStateAPI {
  getCurrentTime(): number;
  getDuration(): number;
  getVolume(): number;
  isPlaying(): boolean;
  isPaused(): boolean;
  isEnded(): boolean;
}

interface PlayerConfigAPI {
  setVolume(volume: number): void;
  setPlaybackRate(rate: number): void;
  toggleMute(): void;
  toggleFullscreen(): Promise<void>;
}

interface PlayerAdvancedAPI {
  takeScreenshot(): Promise<string>;
  addMarker(time: number, label: string): void;
  removeMarker(time: number): void;
  exportSettings(): PlayerSettings;
  importSettings(settings: PlayerSettings): void;
}

// 组合所有API
interface CompletePlayerAPI extends 
  PlayerControlAPI, 
  PlayerStateAPI, 
  PlayerConfigAPI, 
  PlayerAdvancedAPI {}

const AdvancedPlayer = forwardRef<CompletePlayerAPI, PlayerProps>((props, ref) => {
  // 状态管理
  const [playerState, setPlayerState] = useState();
  const videoRef = useRef<HTMLVideoElement>();
  
  // 分模块实现API
  const controlAPI = useMemo((): PlayerControlAPI => ({
    play: async () => {
      await videoRef.current?.play();
      updateState({ isPlaying: true });
    },
    pause: () => {
      videoRef.current?.pause();
      updateState({ isPlaying: false });
    },
    stop: () => {
      videoRef.current?.pause();
      videoRef.current.currentTime = 0;
      updateState({ isPlaying: false, currentTime: 0 });
    },
    seek: (time: number) => {
      if (videoRef.current) {
        videoRef.current.currentTime = time;
        updateState({ currentTime: time });
      }
    }
  }), []);
  
  const stateAPI = useMemo((): PlayerStateAPI => ({
    getCurrentTime: () => videoRef.current?.currentTime || 0,
    getDuration: () => videoRef.current?.duration || 0,
    getVolume: () => videoRef.current?.volume || 0,
    isPlaying: () => !videoRef.current?.paused,
    isPaused: () => videoRef.current?.paused || false,
    isEnded: () => videoRef.current?.ended || false
  }), []);
  
  const configAPI = useMemo((): PlayerConfigAPI => ({
    setVolume: (volume: number) => {
      if (videoRef.current) {
        videoRef.current.volume = Math.max(0, Math.min(1, volume));
      }
    },
    setPlaybackRate: (rate: number) => {
      if (videoRef.current) {
        videoRef.current.playbackRate = rate;
      }
    },
    toggleMute: () => {
      if (videoRef.current) {
        videoRef.current.muted = !videoRef.current.muted;
      }
    },
    toggleFullscreen: async () => {
      // 全屏实现
    }
  }), []);
  
  const advancedAPI = useMemo((): PlayerAdvancedAPI => ({
    takeScreenshot: async () => {
      // 截图实现
      return '';
    },
    addMarker: (time: number, label: string) => {
      // 添加标记
    },
    removeMarker: (time: number) => {
      // 删除标记
    },
    exportSettings: () => {
      // 导出设置
      return {};
    },
    importSettings: (settings: PlayerSettings) => {
      // 导入设置
    }
  }), []);
  
  // 组合所有API
  useImperativeHandle(ref, (): CompletePlayerAPI => ({
    ...controlAPI,
    ...stateAPI,
    ...configAPI,
    ...advancedAPI
  }), [controlAPI, stateAPI, configAPI, advancedAPI]);
  
  return <video ref={videoRef} />;
});`,
    
    followUpCode: `// ✅ 插件化扩展系统
interface PluginAPI {
  name: string;
  version: string;
  methods: Record<string, Function>;
}

interface ExtensiblePlayerAPI extends CompletePlayerAPI {
  // 插件管理
  registerPlugin(plugin: PluginAPI): void;
  unregisterPlugin(name: string): void;
  getPlugin(name: string): PluginAPI | null;
  listPlugins(): string[];
  
  // 事件系统
  addEventListener(event: string, handler: Function): void;
  removeEventListener(event: string, handler: Function): void;
  emit(event: string, data?: any): void;
}

const ExtensiblePlayer = forwardRef<ExtensiblePlayerAPI, PlayerProps>((props, ref) => {
  const [plugins, setPlugins] = useState<Map<string, PluginAPI>>(new Map());
  const [eventListeners, setEventListeners] = useState<Map<string, Set<Function>>>(new Map());
  
  // 基础API (复用上面的实现)
  const baseAPI = usePlayerAPI(); // 自定义Hook封装基础功能
  
  // 插件管理API
  const pluginAPI = useMemo(() => ({
    registerPlugin: (plugin: PluginAPI) => {
      setPlugins(prev => new Map(prev).set(plugin.name, plugin));
      emit('plugin:registered', { plugin: plugin.name });
    },
    
    unregisterPlugin: (name: string) => {
      setPlugins(prev => {
        const newMap = new Map(prev);
        newMap.delete(name);
        return newMap;
      });
      emit('plugin:unregistered', { plugin: name });
    },
    
    getPlugin: (name: string) => plugins.get(name) || null,
    listPlugins: () => Array.from(plugins.keys())
  }), [plugins]);
  
  // 事件系统API
  const eventAPI = useMemo(() => ({
    addEventListener: (event: string, handler: Function) => {
      setEventListeners(prev => {
        const newMap = new Map(prev);
        if (!newMap.has(event)) {
          newMap.set(event, new Set());
        }
        newMap.get(event)!.add(handler);
        return newMap;
      });
    },
    
    removeEventListener: (event: string, handler: Function) => {
      setEventListeners(prev => {
        const newMap = new Map(prev);
        newMap.get(event)?.delete(handler);
        return newMap;
      });
    },
    
    emit: (event: string, data?: any) => {
      eventListeners.get(event)?.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(\`Event handler error for \${event}:\`, error);
        }
      });
    }
  }), [eventListeners]);
  
  // 组合完整API
  useImperativeHandle(ref, (): ExtensiblePlayerAPI => ({
    ...baseAPI,
    ...pluginAPI,
    ...eventAPI
  }), [baseAPI, pluginAPI, eventAPI]);
  
  return (
    <div className="extensible-player">
      {/* 播放器UI */}
      <video />
      
      {/* 插件渲染区域 */}
      <div className="plugin-area">
        {Array.from(plugins.values()).map(plugin => (
          <PluginRenderer key={plugin.name} plugin={plugin} />
        ))}
      </div>
    </div>
  );
});`,
    
    tags: ['复杂API', '模块化设计', '插件系统', '架构设计'],
    category: 'architecture'
  }
];

export default commonQuestions; 