import { DebuggingTips } from "@/types/api";

const debuggingTips: DebuggingTips = {
  commonErrors: [
    {
      error: "Warning: Can't perform a React state update on an unmounted component",
      cause: "在组件卸载后尝试更新状态，通常发生在异步操作完成时组件已经卸载",
      solution: "使用清理函数和标志位来取消未完成的操作",
      prevention: "始终为异步操作实现清理逻辑",
      code: `// ❌ 错误：未处理组件卸载
function BadComponent() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    fetchData().then(setData); // 如果组件卸载，这里会报错
  }, []);
  
  return <div>{data}</div>;
}

// ✅ 正确：使用清理标志
function GoodComponent() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    let isMounted = true;
    
    fetchData().then(result => {
      if (isMounted) {
        setData(result);
      }
    });
    
    return () => {
      isMounted = false;
    };
  }, []);
  
  return <div>{data}</div>;
}`
    },
    {
      error: "Maximum update depth exceeded",
      cause: "useEffect中的状态更新触发了无限循环，通常是因为依赖数组设置不当",
      solution: "检查依赖数组，确保不在Effect中直接更新其依赖的状态",
      prevention: "使用ESLint的exhaustive-deps规则，仔细设计状态更新逻辑",
      code: `// ❌ 错误：无限循环
function InfiniteLoop() {
  const [count, setCount] = useState(0);
  const [double, setDouble] = useState(0);
  
  useEffect(() => {
    setDouble(count * 2); // 更新 double
  }, [count, double]); // double 在依赖中，但又被更新
  
  return <div>{double}</div>;
}

// ✅ 正确：移除不必要的依赖
function NoLoop() {
  const [count, setCount] = useState(0);
  const [double, setDouble] = useState(0);
  
  useEffect(() => {
    setDouble(count * 2);
  }, [count]); // 只依赖 count
  
  return <div>{double}</div>;
}

// ✅ 更好：使用派生状态
function BestApproach() {
  const [count, setCount] = useState(0);
  const double = count * 2; // 直接计算，不需要 Effect
  
  return <div>{double}</div>;
}`
    },
    {
      error: "React Hook useEffect has missing dependencies",
      cause: "ESLint检测到Effect使用了未在依赖数组中声明的变量",
      solution: "添加所有使用的外部变量到依赖数组，或使用适当的替代方案",
      prevention: "理解闭包原理，使用函数式更新或useCallback",
      code: `// ❌ 错误：缺少依赖
function MissingDeps({ userId }) {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    fetchUser(userId).then(setUser); // ESLint警告：userId未在依赖中
  }, []); // 缺少 userId
  
  return <div>{user?.name}</div>;
}

// ✅ 方案1：添加依赖
function WithDeps({ userId }) {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    fetchUser(userId).then(setUser);
  }, [userId]); // 正确的依赖
  
  return <div>{user?.name}</div>;
}

// ✅ 方案2：使用 useCallback（适用于函数）
function WithCallback({ onUserLoad }) {
  const [user, setUser] = useState(null);
  
  const loadUser = useCallback(async () => {
    const data = await fetchUser();
    setUser(data);
    onUserLoad?.(data); // 使用可选链
  }, [onUserLoad]); // 依赖稳定的回调
  
  useEffect(() => {
    loadUser();
  }, [loadUser]);
  
  return <div>{user?.name}</div>;
}`
    }
  ],
  
  devToolsTips: [
    {
      tool: "React DevTools",
      technique: "使用Profiler追踪Effect执行",
      example: `// 1. 打开React DevTools的Profiler标签
// 2. 点击"开始记录"
// 3. 触发组件更新
// 4. 停止记录并分析：
//    - 查看组件的渲染原因
//    - 检查Effect的执行时间
//    - 识别性能瓶颈

// 在代码中添加性能标记
useEffect(() => {
  performance.mark('effect-start');
  
  // Effect逻辑
  expensiveOperation();
  
  performance.mark('effect-end');
  performance.measure('effect-duration', 'effect-start', 'effect-end');
  
  // 在控制台查看性能数据
  const measure = performance.getEntriesByName('effect-duration')[0];
  console.log('Effect执行时间:', measure.duration);
}, [dependency]);`
    },
    {
      tool: "Chrome DevTools",
      technique: "使用Console API调试Effect执行",
      example: `// 使用 console 方法追踪 Effect
function DebugEffect({ prop }) {
  const [state, setState] = useState(0);
  
  useEffect(() => {
    console.group('Effect执行');
    console.log('当前prop:', prop);
    console.log('当前state:', state);
    console.trace('调用栈');
    console.groupEnd();
    
    // 使用条件断点
    if (prop > 10) {
      debugger; // 只在特定条件下暂停
    }
    
    return () => {
      console.log('清理函数执行');
    };
  }, [prop, state]);
  
  // 使用 console.count 统计执行次数
  useEffect(() => {
    console.count('Effect执行次数');
  });
}`
    },
    {
      tool: "自定义调试Hook",
      technique: "创建useDebugEffect来追踪依赖变化",
      example: `// 自定义调试Hook
function useDebugEffect(effectHook, dependencies, dependencyNames = []) {
  const previousDeps = useRef();
  
  useEffect(() => {
    const changedDeps = dependencies.reduce((acc, dependency, index) => {
      if (dependency !== previousDeps.current?.[index]) {
        const keyName = dependencyNames[index] || index;
        return {
          ...acc,
          [keyName]: {
            before: previousDeps.current?.[index],
            after: dependency
          }
        };
      }
      return acc;
    }, {});
    
    if (Object.keys(changedDeps).length) {
      console.log('[useEffect]依赖变化:', changedDeps);
    }
    
    previousDeps.current = dependencies;
    effectHook();
  }, dependencies);
}

// 使用示例
function MyComponent({ userId, filters }) {
  const [data, setData] = useState(null);
  
  useDebugEffect(() => {
    fetchData(userId, filters).then(setData);
  }, [userId, filters], ['userId', 'filters']);
  
  return <div>{/* 渲染逻辑 */}</div>;
}`
    }
  ],
  
  troubleshooting: [
    {
      symptom: "Effect执行了多次，但依赖没有变化",
      possibleCauses: [
        "React StrictMode在开发环境下的双重调用",
        "父组件频繁重渲染导致子组件重新创建",
        "依赖数组中包含了每次渲染都会变化的对象或数组"
      ],
      solutions: [
        "确认是否在StrictMode下运行，这是正常行为",
        "使用React.memo优化父组件的渲染",
        "使用useMemo或useCallback稳定依赖的引用"
      ]
    },
    {
      symptom: "异步数据加载后界面没有更新",
      possibleCauses: [
        "在条件语句中调用了Hook",
        "忘记将数据设置到状态中",
        "组件已经卸载，状态更新被忽略"
      ],
      solutions: [
        "确保Hook在组件顶层调用，不在条件语句中",
        "检查setState调用是否正确执行",
        "添加mounted标志防止卸载后的状态更新"
      ]
    },
    {
      symptom: "清理函数没有执行",
      possibleCauses: [
        "Effect函数没有返回清理函数",
        "返回的不是函数类型",
        "语法错误导致清理函数未定义"
      ],
      solutions: [
        "确保Effect返回一个函数或undefined",
        "检查清理函数的语法是否正确",
        "使用console.log验证清理函数是否被调用"
      ]
    }
  ]
};

export default debuggingTips; 