import { BasicInfo } from "@/types/api";

const basicInfo: BasicInfo = {
  definition: "useEffect是React中用于处理副作用的Hook，允许在函数组件中执行数据获取、订阅、DOM操作等副作用",
  
  syntax: `useEffect(effect: () => void | (() => void), dependencies?: DependencyList)

// 基础用法
useEffect(() => {
  // 副作用逻辑
});

// 带依赖数组
useEffect(() => {
  // 副作用逻辑
}, [dependency1, dependency2]);

// 带清理函数
useEffect(() => {
  const subscription = subscribeToSomething();
  return () => {
    subscription.unsubscribe();
  };
}, []);`,

  introduction: "useEffect 是 React 中处理副作用的核心 Hook，用于在函数组件中执行数据获取、订阅、手动修改 DOM 等操作。它将组件的副作用与渲染逻辑分离，使代码更加清晰和可维护。",
  
  parameters: [
    {
      name: "effect",
      type: "() => void | (() => void)",
      description: "副作用函数，可以返回一个清理函数",
      required: true
    },
    {
      name: "dependencies",
      type: "DependencyList | undefined",
      description: "依赖数组，控制副作用何时重新执行。空数组表示仅在挂载时执行，undefined 表示每次渲染都执行",
      required: false,
      default: "undefined"
    }
  ],
  
  returnValue: {
    type: "void",
    description: "useEffect 不返回任何值"
  },
  
  limitations: [
    "必须在函数组件的顶层调用，不能在条件语句、循环或嵌套函数中调用",
    "副作用函数不能是 async 函数，需要异步操作时应在内部定义 async 函数",
    "依赖数组必须包含所有在副作用中使用的外部变量，否则可能导致闭包问题",
    "清理函数会在组件卸载时以及下一次副作用执行前调用"
  ]
};

export default basicInfo; 