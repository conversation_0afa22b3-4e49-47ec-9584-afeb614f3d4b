import { KnowledgeArchaeology } from "@/types/api";

const knowledgeArchaeology: KnowledgeArchaeology = {
  background: `## useEffect 的诞生背景

在 React Hooks 出现之前，函数组件被称为"无状态组件"，只能接收 props 并返回 JSX。所有的副作用操作都必须在类组件中通过生命周期方法处理：

### 类组件时代的痛点

1. **逻辑分散**：相关的逻辑被迫分散在不同的生命周期方法中
2. **重复代码**：componentDidMount 和 componentDidUpdate 经常包含重复逻辑
3. **难以复用**：副作用逻辑难以在组件间共享
4. **理解成本**：需要理解 this 绑定和生命周期的复杂性

### Hooks 的设计目标

React 团队在 2018 年 React Conf 上首次介绍 Hooks，目标是：
- 让函数组件具有状态和副作用能力
- 提供更好的逻辑复用机制
- 简化组件逻辑，降低学习成本
- 为未来的并发特性做准备`,

  evolution: `## useEffect 的演进历程

### React 16.8 (2019.02) - Hooks 正式发布
- useEffect 作为核心 Hook 之一首次亮相
- 统一了 componentDidMount、componentDidUpdate 和 componentWillUnmount
- 引入依赖数组概念

### React 17 (2020.10) - 渐进式升级
- 改进了 useEffect 的时序一致性
- 优化了清理函数的执行时机
- 为并发模式做准备

### React 18 (2022.03) - 并发特性
- StrictMode 下的双重调用机制
- 自动批处理优化
- Suspense 与 useEffect 的协作改进

### React 19 (开发中) - 未来展望
- 更智能的依赖追踪
- 编译时优化
- 服务端组件的影响`,

  comparisons: `## 框架对比分析

### React useEffect vs Vue watchEffect

**相似点**：
- 都用于处理副作用
- 都支持清理机制
- 都能追踪依赖变化

**差异点**：
| 特性 | React useEffect | Vue watchEffect |
|-----|----------------|-----------------|
| 依赖追踪 | 手动声明 | 自动追踪 |
| 执行时机 | 异步（默认） | 同步 |
| 清理函数 | 返回函数 | onInvalidate |
| 条件执行 | 依赖数组 | 条件语句 |

### 与其他框架的副作用处理

**Angular**：
- 使用 RxJS 和生命周期钩子
- 更面向响应式编程范式
- 学习曲线较陡

**Svelte**：
- 使用 reactive 语句（$:）
- 编译时优化，运行时开销小
- 语法更接近原生 JavaScript

**SolidJS**：
- createEffect 自动追踪依赖
- 细粒度响应式更新
- 性能优于 React 的虚拟 DOM`,

  philosophy: `## 设计哲学

### 1. 声明式副作用
useEffect 让副作用变得声明式，开发者只需描述"什么时候做什么"，而不是"怎么做"。

### 2. 关注点分离
将副作用逻辑与渲染逻辑分离，使组件更容易理解和测试。

### 3. 组合优于继承
通过 Hook 的组合，实现了比类组件继承更灵活的代码复用。

### 4. 函数式编程
useEffect 体现了 React 向函数式编程范式的转变：
- 纯函数组件 + 副作用隔离
- 不可变性原则
- 组合式 API 设计

### 5. 用户体验优先
异步执行机制确保副作用不会阻塞用户界面，提供流畅的交互体验。`,

  presentValue: `## 现实价值

### 1. 开发效率提升
- **减少代码量**：相比类组件减少约 30-50% 的样板代码
- **逻辑复用**：自定义 Hook 让逻辑复用变得简单
- **心智负担降低**：不再需要考虑 this 绑定和生命周期

### 2. 应用场景广泛
- **数据获取**：API 调用、GraphQL 查询
- **事件订阅**：WebSocket、EventSource、DOM 事件
- **状态同步**：localStorage、URL 参数、第三方库
- **性能优化**：防抖、节流、虚拟滚动

### 3. 生态系统繁荣
- 数千个基于 useEffect 的自定义 Hook
- 完善的开发工具支持（React DevTools）
- 丰富的学习资源和最佳实践

### 4. 企业级应用
- Facebook、Netflix、Airbnb 等大规模应用
- 提升了代码可维护性和团队协作效率
- 降低了新人上手成本

### 5. 未来趋势
- 服务端组件和客户端组件的协作
- 更智能的编译时优化
- AI 辅助的 Hook 编写和优化`
};

export default knowledgeArchaeology;
