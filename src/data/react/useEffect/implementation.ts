import { Implementation } from "@/types/api";

const implementation: Implementation = {
  mechanism: `useEffect 基于 React Fiber 架构实现，在组件渲染的不同阶段执行副作用：

1. **渲染阶段（Render Phase）**：
   - React 调用函数组件，收集所有 useEffect 调用
   - 将 effect 存储在 Fiber 节点的 updateQueue 中
   - 比较依赖数组，标记需要执行的 effect

2. **提交阶段（Commit Phase）**：
   - Layout 阶段：执行 useLayoutEffect（同步）
   - Passive 阶段：调度 useEffect（异步）
   - 使用 scheduler 在浏览器空闲时执行 effect

3. **执行机制**：
   - Effect 函数在组件渲染后异步执行
   - 清理函数在下次 effect 执行前或组件卸载时调用
   - 依赖数组通过 Object.is 进行浅比较`,

  plainExplanation: `可以把 useEffect 想象成一个"智能管家"：

- **管家的工作时机**：主人（组件）完成装修（渲染）后，管家才开始工作（执行副作用）
- **工作清单**：依赖数组就像工作清单，只有清单上的物品（依赖）发生变化，管家才会重新工作
- **收尾工作**：管家会记住上次的工作，在开始新工作前先做好收尾（清理函数）
- **不影响主人**：管家的工作是异步的，不会阻塞主人的正常活动（不阻塞渲染）`,

  visualization: `graph TD
    A[组件渲染] --> B{检查依赖}
    B -->|依赖变化| C[标记 Effect]
    B -->|依赖未变| D[跳过 Effect]
    
    C --> E[提交阶段]
    E --> F[执行清理函数]
    F --> G[执行 Effect 函数]
    
    G --> H{返回清理函数?}
    H -->|是| I[保存清理函数]
    H -->|否| J[完成]
    
    I --> J
    
    K[组件卸载] --> L[执行所有清理函数]
    
    style A fill:#e1f5fe
    style E fill:#fff3e0
    style G fill:#e8f5e9
    style K fill:#ffebee`,

  designConsiderations: [
    "**异步执行**：避免阻塞浏览器渲染，提升用户体验",
    "**依赖追踪**：通过依赖数组精确控制副作用的执行时机",
    "**清理机制**：自动管理资源清理，防止内存泄漏",
    "**批处理优化**：多个 effect 会被批量调度执行",
    "**错误边界**：effect 中的错误不会导致组件树崩溃"
  ],

  codeExample: `// useEffect 的简化实现原理
function mountEffect(create, deps) {
  const hook = {
    memoizedState: null,
    deps: deps,
  };
  
  // 创建 effect 对象
  const effect = {
    create,    // effect 函数
    destroy: undefined, // 清理函数
    deps,      // 依赖数组
    next: null,
    tag: HookPassive, // 标记为 passive effect
  };
  
  // 将 effect 加入队列
  pushEffect(effect);
  
  // 保存到 hook 链表
  currentHook.memoizedState = effect;
  return effect;
}

function updateEffect(create, deps) {
  const prevEffect = currentHook.memoizedState;
  
  // 比较依赖
  if (deps !== undefined) {
    const prevDeps = prevEffect.deps;
    if (areHookInputsEqual(deps, prevDeps)) {
      // 依赖未变，跳过此 effect
      pushEffect(NoHookEffect, create, undefined, deps);
      return;
    }
  }
  
  // 依赖变化，创建新 effect
  const effect = {
    create,
    destroy: prevEffect.destroy, // 复用清理函数
    deps,
    next: null,
    tag: HookPassive | HookHasEffect,
  };
  
  pushEffect(effect);
  currentHook.memoizedState = effect;
}`
};

export default implementation; 