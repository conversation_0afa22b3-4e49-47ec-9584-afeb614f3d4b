import { LearningPath } from "@/types/api";

const learningPath: LearningPath = {
  prerequisites: [
    "JavaScript基础：闭包、异步编程、Promise",
    "React基础：组件、props、state概念",
    "函数组件的基本使用",
    "理解React的渲染机制"
  ],
  
  learningSteps: [
    {
      step: 1,
      title: "理解副作用概念",
      description: "学习什么是副作用，为什么需要将副作用与渲染逻辑分离",
      resources: [
        "React官方文档：Effect Hook",
        "MDN：副作用编程",
        "视频：React Hooks入门"
      ],
      practiceExercises: [
        {
          title: "基础数据获取",
          description: "创建一个组件，使用useEffect从API获取用户列表并展示",
          difficulty: "easy",
          estimatedTime: "30分钟"
        },
        {
          title: "事件监听器",
          description: "实现一个跟踪鼠标位置的组件，在组件卸载时清理监听器",
          difficulty: "easy",
          estimatedTime: "20分钟"
        }
      ]
    },
    {
      step: 2,
      title: "掌握依赖数组",
      description: "深入理解依赖数组的工作原理，学习如何正确声明依赖",
      resources: [
        "文章：useEffect完整指南",
        "ESLint规则：exhaustive-deps",
        "示例：常见的依赖数组错误"
      ],
      practiceExercises: [
        {
          title: "搜索功能实现",
          description: "创建一个实时搜索组件，正确处理输入变化和API调用",
          difficulty: "medium",
          estimatedTime: "45分钟"
        },
        {
          title: "定时器管理",
          description: "实现一个倒计时组件，支持暂停、继续和重置功能",
          difficulty: "medium",
          estimatedTime: "40分钟"
        }
      ]
    },
    {
      step: 3,
      title: "处理异步操作",
      description: "学习在useEffect中正确处理异步操作，避免内存泄漏",
      resources: [
        "React官方文档：Effect中的数据获取",
        "文章：AbortController使用指南",
        "代码示例：防止setState内存泄漏"
      ],
      practiceExercises: [
        {
          title: "带取消的数据获取",
          description: "实现一个用户详情组件，支持取消未完成的请求",
          difficulty: "medium",
          estimatedTime: "50分钟"
        },
        {
          title: "无限滚动列表",
          description: "创建一个支持无限滚动的列表组件，正确处理加载状态",
          difficulty: "hard",
          estimatedTime: "90分钟"
        }
      ]
    },
    {
      step: 4,
      title: "性能优化技巧",
      description: "学习如何优化Effect的性能，避免不必要的执行",
      resources: [
        "文章：React性能优化最佳实践",
        "工具：React DevTools Profiler",
        "视频：useEffect性能陷阱"
      ],
      practiceExercises: [
        {
          title: "防抖搜索优化",
          description: "优化搜索组件，添加防抖功能减少API调用",
          difficulty: "medium",
          estimatedTime: "40分钟"
        },
        {
          title: "大数据列表优化",
          description: "实现虚拟滚动，优化长列表的渲染性能",
          difficulty: "hard",
          estimatedTime: "120分钟"
        }
      ]
    },
    {
      step: 5,
      title: "高级模式与自定义Hook",
      description: "学习将Effect逻辑抽取为自定义Hook，实现逻辑复用",
      resources: [
        "React官方文档：自定义Hook",
        "示例集：常用的自定义Hook",
        "文章：Hook设计模式"
      ],
      practiceExercises: [
        {
          title: "创建useAsync Hook",
          description: "实现一个通用的异步操作Hook，处理loading、error和data状态",
          difficulty: "hard",
          estimatedTime: "60分钟"
        },
        {
          title: "WebSocket Hook",
          description: "创建一个可复用的WebSocket Hook，支持自动重连",
          difficulty: "hard",
          estimatedTime: "90分钟"
        }
      ]
    },
    {
      step: 6,
      title: "实战项目整合",
      description: "在真实项目中综合运用useEffect的各种技巧",
      resources: [
        "项目模板：React + TypeScript",
        "架构指南：大型React应用",
        "最佳实践：企业级React开发"
      ],
      practiceExercises: [
        {
          title: "实时聊天应用",
          description: "构建一个完整的实时聊天应用，包含用户认证、消息同步等功能",
          difficulty: "hard",
          estimatedTime: "240分钟"
        },
        {
          title: "数据仪表盘",
          description: "创建一个数据可视化仪表盘，实时更新多个数据源",
          difficulty: "hard",
          estimatedTime: "180分钟"
        }
      ]
    }
  ],
  
  nextSteps: [
    "学习其他React Hooks（useContext、useReducer、useMemo等）",
    "探索React 18的并发特性（useTransition、useDeferredValue）",
    "学习状态管理库（Redux、MobX、Zustand）",
    "深入React性能优化（React.memo、代码分割）",
    "学习服务端渲染（Next.js）和静态生成"
  ],
  
  estimatedTime: "20-30小时",
  skillLevel: "intermediate"
};

export default learningPath; 