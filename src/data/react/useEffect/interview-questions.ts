import { InterviewQuestion } from "@/types/api";

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: "useEffect 和 useLayoutEffect 有什么区别？什么时候应该使用 useLayoutEffect？",
    answer: {
      brief: "useEffect 异步执行不阻塞渲染，useLayoutEffect 同步执行会阻塞渲染。useLayoutEffect 适用于需要同步读取或修改 DOM 的场景。",
      detailed: `主要区别在于执行时机和同步性：

**useEffect**：
- 在浏览器完成布局和绘制之后异步执行
- 不会阻塞浏览器渲染
- 适用于大多数副作用场景

**useLayoutEffect**：
- 在浏览器布局之后、绘制之前同步执行
- 会阻塞浏览器渲染
- 适用于需要同步读取或修改 DOM 的场景

**使用场景**：
1. useLayoutEffect 适用于：
   - 需要测量 DOM 元素尺寸
   - 需要同步更新 DOM 避免闪烁
   - 需要在浏览器绘制前调整滚动位置

2. useEffect 适用于：
   - 数据获取
   - 订阅事件
   - 日志记录
   - 大多数不需要同步的副作用`,
      code: `// useLayoutEffect 示例：避免闪烁
function Tooltip({ children, text }) {
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const ref = useRef(null);
  
  // 使用 useLayoutEffect 同步计算位置
  useLayoutEffect(() => {
    if (ref.current) {
      const rect = ref.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + 5,
        left: rect.left + rect.width / 2
      });
    }
  }, []);
  
  return (
    <>
      <span ref={ref}>{children}</span>
      <div 
        className="tooltip" 
        style={{ 
          position: 'absolute',
          top: position.top,
          left: position.left
        }}
      >
        {text}
      </div>
    </>
  );
}`
    },
    difficulty: "medium",
    frequency: "high",
    category: "生命周期"
  },
  {
    id: 2,
    question: "为什么 useEffect 的依赖数组中必须包含所有使用的外部变量？如何解决闭包陷阱？",
    answer: {
      brief: "因为闭包机制会捕获创建时的变量值，依赖未声明会导致 Effect 使用过期值。解决方案包括完整声明依赖、函数式更新、使用 useRef 等。",
      detailed: `这是因为 JavaScript 的闭包机制和 React 的渲染模型：

**原因**：
1. 每次渲染都会创建新的函数作用域
2. Effect 函数会捕获创建时的变量值（闭包）
3. 如果依赖未声明，Effect 会一直使用旧值

**闭包陷阱示例**：
- Effect 中使用了过期的 state 或 props
- 定时器或事件处理器中的值不更新

**解决方案**：
1. 完整声明依赖数组
2. 使用函数式更新避免依赖 state
3. 使用 useRef 保存可变值
4. 使用 useCallback 缓存函数
5. 启用 eslint-plugin-react-hooks规则检查`,
      code: `// 闭包陷阱示例
function Counter() {
  const [count, setCount] = useState(0);
  
  // ❌ 错误：闭包陷阱
  useEffect(() => {
    const timer = setInterval(() => {
      setCount(count + 1); // count 永远是 0
    }, 1000);
    return () => clearInterval(timer);
  }, []); // 缺少 count 依赖
  
  // ✅ 方案1：添加依赖
  useEffect(() => {
    const timer = setInterval(() => {
      setCount(count + 1);
    }, 1000);
    return () => clearInterval(timer);
  }, [count]); // 但会导致定时器重建
  
  // ✅ 方案2：函数式更新（推荐）
  useEffect(() => {
    const timer = setInterval(() => {
      setCount(c => c + 1); // 使用最新值
    }, 1000);
    return () => clearInterval(timer);
  }, []); // 不需要依赖
  
  return <div>{count}</div>;
}`
    },
    difficulty: "hard",
    frequency: "high",
    category: "闭包与依赖"
  },
  {
    id: 3,
    question: "如何正确地在 useEffect 中处理异步操作？为什么不能直接使用 async/await？",
    answer: {
      brief: "useEffect 不能直接使用 async 函数，因为它必须返回清理函数或 undefined，而 async 函数返回 Promise。应该在内部定义 async 函数。",
      detailed: `useEffect 不能直接使用 async 函数，因为：

**原因**：
1. Effect 函数必须返回清理函数或 undefined
2. Async 函数返回 Promise，不符合要求
3. React 无法正确处理 Promise 作为清理函数

**正确做法**：
1. 在 Effect 内部定义 async 函数
2. 使用 AbortController 取消请求
3. 处理组件卸载时的状态更新
4. 使用标志位防止内存泄漏`,
      code: `// ❌ 错误：直接使用 async
useEffect(async () => {
  const data = await fetchData();
  setData(data);
}, []); // Error: Effect must return cleanup function or undefined

// ✅ 正确做法1：内部 async 函数
useEffect(() => {
  let cancelled = false;
  
  async function loadData() {
    try {
      const data = await fetchData();
      if (!cancelled) {
        setData(data);
      }
    } catch (error) {
      if (!cancelled) {
        setError(error);
      }
    }
  }
  
  loadData();
  
  return () => {
    cancelled = true;
  };
}, []);

// ✅ 正确做法2：使用 AbortController
useEffect(() => {
  const controller = new AbortController();
  
  async function fetchUser() {
    try {
      const response = await fetch('/api/user', {
        signal: controller.signal
      });
      const user = await response.json();
      setUser(user);
    } catch (error) {
      if (error.name !== 'AbortError') {
        setError(error);
      }
    }
  }
  
  fetchUser();
  
  return () => {
    controller.abort();
  };
}, []);`
    },
    difficulty: "medium",
    frequency: "high",
    category: "异步处理",
    visualization: `graph LR
    A[useEffect 调用] --> B{是否为 async?}
    B -->|是| C[❌ 错误]
    B -->|否| D[内部定义 async]
    D --> E[执行异步操作]
    E --> F{组件是否已卸载?}
    F -->|是| G[忽略结果]
    F -->|否| H[更新状态]
    
    I[清理函数] --> J[取消请求/设置标志]
    
    style C fill:#ffebee
    style H fill:#e8f5e9`
  }
];

export default interviewQuestions; 