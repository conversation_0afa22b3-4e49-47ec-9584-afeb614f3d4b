import { CommonQuestion } from "@/types/api";

const commonQuestions: CommonQuestion[] = [
  {
    id: "useeffect-double-execution",
    question: "为什么我的 useEffect 会执行两次？",
    answer: `在开发环境中，React 18 的 StrictMode 会故意执行两次 Effect 来帮助发现副作用问题：

**原因**：
1. React StrictMode 在开发环境下会重复调用组件
2. 用于检测不安全的副作用和帮助发现 bug
3. 生产环境不会有这个行为

**解决方案**：
1. 这是正常行为，不需要"修复"
2. 确保 Effect 是幂等的（多次执行结果相同）
3. 正确实现清理函数
4. 使用 AbortController 处理请求`,
    
    code: `// 处理 StrictMode 下的双重执行
function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    let cancelled = false;
    const controller = new AbortController();
    
    // 即使执行两次也能正确工作
    async function fetchUser() {
      try {
        const response = await fetch("/api/users/" + userId, {
          signal: controller.signal
        });
        const data = await response.json();
        
        if (!cancelled) {
          setUser(data);
        }
      } catch (error) {
        if (error.name !== 'AbortError' && !cancelled) {
          console.error('Failed to fetch user:', error);
        }
      }
    }
    
    fetchUser();
    
    // 清理函数确保取消之前的请求
    return () => {
      cancelled = true;
      controller.abort();
    };
  }, [userId]);
  
  return user ? <div>{user.name}</div> : <div>Loading...</div>;
}`,
    
    tags: ["React 18", "StrictMode", "开发环境"]
  },
  {
    id: "useeffect-infinite-loop",
    question: "如何避免 useEffect 的无限循环？",
    answer: `无限循环通常是因为依赖数组设置不当或在 Effect 中直接修改依赖：

**常见原因**：
1. 依赖数组中包含每次渲染都会变化的对象或数组
2. 在 Effect 中更新了依赖数组中的值
3. 忘记添加依赖数组

**预防措施**：
1. 使用基本类型作为依赖
2. 使用 useMemo/useCallback 缓存引用类型
3. 将对象属性解构为基本类型
4. 使用函数式更新避免依赖 state`,
    
    code: `// ❌ 错误：无限循环
function BadExample() {
  const [data, setData] = useState({});
  
  // 每次渲染都创建新对象
  const config = { apiKey: 'xxx' };
  
  useEffect(() => {
    fetchData(config).then(setData);
  }, [config]); // config 每次都是新对象！
}

// ✅ 方案1：使用 useMemo
function GoodExample1() {
  const [data, setData] = useState({});
  
  const config = useMemo(() => ({
    apiKey: 'xxx'
  }), []); // 只创建一次
  
  useEffect(() => {
    fetchData(config).then(setData);
  }, [config]);
}

// ✅ 方案2：将对象移到组件外
const CONFIG = { apiKey: 'xxx' };

function GoodExample2() {
  const [data, setData] = useState({});
  
  useEffect(() => {
    fetchData(CONFIG).then(setData);
  }, []); // 不需要依赖
}

// ✅ 方案3：只依赖必要的属性
function GoodExample3({ user }) {
  const [profile, setProfile] = useState(null);
  
  // 只依赖 user.id，而不是整个 user 对象
  useEffect(() => {
    if (user?.id) {
      fetchProfile(user.id).then(setProfile);
    }
  }, [user?.id]); // 只依赖 id
}

// ❌ 错误：在 Effect 中更新依赖
function BadCounter() {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    setCount(count + 1); // 更新了依赖的 count
  }, [count]); // 无限循环！
}

// ✅ 正确：使用条件或其他触发器
function GoodCounter() {
  const [count, setCount] = useState(0);
  const [trigger, setTrigger] = useState(false);
  
  useEffect(() => {
    if (trigger) {
      setCount(c => c + 1);
      setTrigger(false);
    }
  }, [trigger]); // 依赖 trigger 而不是 count
}`,
    
    tags: ["性能", "最佳实践", "常见错误"]
  },
  {
    id: "useeffect-cleanup",
    question: "如何正确清理 useEffect 中的定时器、事件监听器等资源？",
    answer: `清理函数是防止内存泄漏的关键，需要在组件卸载或依赖变化时正确清理所有资源：

**需要清理的资源**：
1. 定时器（setTimeout, setInterval）
2. 事件监听器（addEventListener）
3. 订阅（WebSocket, EventSource）
4. 未完成的异步请求
5. DOM 引用

**清理时机**：
- 组件卸载时
- 依赖变化导致 Effect 重新执行前
- 手动触发清理`,
    
    code: `// 综合示例：多种资源的清理
function ResourceManager({ eventType, url }) {
  const [data, setData] = useState(null);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const elementRef = useRef(null);
  
  useEffect(() => {
    // 1. 定时器清理
    const timer = setTimeout(() => {
      console.log('Delayed action');
    }, 1000);
    
    const interval = setInterval(() => {
      console.log('Periodic action');
    }, 5000);
    
    // 2. 事件监听器清理
    const handleMouseMove = (e) => {
      setMousePos({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    
    // 3. 自定义事件清理
    const handleCustomEvent = (e) => {
      console.log('Custom event:', e.detail);
    };
    elementRef.current?.addEventListener(eventType, handleCustomEvent);
    
    // 4. 异步请求清理
    const controller = new AbortController();
    fetch(url, { signal: controller.signal })
      .then(res => res.json())
      .then(data => setData(data))
      .catch(err => {
        if (err.name !== 'AbortError') {
          console.error('Fetch error:', err);
        }
      });
    
    // 5. WebSocket 清理
    const ws = new WebSocket('wss://example.com');
    ws.onmessage = (event) => {
      console.log('WebSocket message:', event.data);
    };
    
    // 清理函数
    return () => {
      // 清理所有定时器
      clearTimeout(timer);
      clearInterval(interval);
      
      // 移除事件监听器
      window.removeEventListener('mousemove', handleMouseMove);
      elementRef.current?.removeEventListener(eventType, handleCustomEvent);
      
      // 取消请求
      controller.abort();
      
      // 关闭 WebSocket
      ws.close();
      
      console.log('All resources cleaned up');
    };
  }, [eventType, url]); // 依赖变化时也会执行清理
  
  return (
    <div ref={elementRef}>
      <p>Mouse: {mousePos.x}, {mousePos.y}</p>
      <p>Data: {JSON.stringify(data)}</p>
    </div>
  );
}

// 自定义 Hook 封装清理逻辑
function useEventListener(eventName, handler, element = window) {
  const savedHandler = useRef(handler);
  
  useEffect(() => {
    savedHandler.current = handler;
  }, [handler]);
  
  useEffect(() => {
    const eventListener = (event) => savedHandler.current(event);
    
    element.addEventListener(eventName, eventListener);
    
    return () => {
      element.removeEventListener(eventName, eventListener);
    };
  }, [eventName, element]);
}`,
    
    tags: ["内存泄漏", "清理函数", "最佳实践"]
  }
];

export default commonQuestions; 