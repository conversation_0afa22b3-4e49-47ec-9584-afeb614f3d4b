import { BusinessScenario } from "@/types/api";

const businessScenarios: BusinessScenario[] = [
  {
    title: "数据获取与展示",
    difficulty: "easy",
    description: "使用 useEffect 从 API 获取数据并展示，包含加载状态和错误处理",
    code: `import React, { useState, useEffect } from 'react';

function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // 重置状态
    setLoading(true);
    setError(null);
    
    // 创建 AbortController 用于取消请求
    const controller = new AbortController();
    
    // 异步获取数据
    async function fetchUser() {
      try {
        const response = await fetch(
          "https://api.example.com/users/" + userId,
          { signal: controller.signal }
        );
        
        if (!response.ok) {
          throw new Error('Failed to fetch user');
        }
        
        const data = await response.json();
        setUser(data);
      } catch (err) {
        // 忽略取消请求的错误
        if (err.name !== 'AbortError') {
          setError(err.message);
        }
      } finally {
        setLoading(false);
      }
    }
    
    fetchUser();
    
    // 清理函数：取消未完成的请求
    return () => {
      controller.abort();
    };
  }, [userId]); // 当 userId 改变时重新获取数据

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!user) return <div>No user found</div>;

  return (
    <div className="user-profile">
      <h2>{user.name}</h2>
      <p>Email: {user.email}</p>
      <p>Role: {user.role}</p>
    </div>
  );
}`,
    explanation: `这个示例展示了 useEffect 的几个关键概念：

1. **异步操作处理**：在 useEffect 内部定义 async 函数，而不是将 effect 函数本身设为 async
2. **依赖数组**：当 userId 改变时，重新执行副作用获取新用户数据
3. **清理函数**：使用 AbortController 取消未完成的请求，防止内存泄漏和状态更新错误
4. **状态管理**：合理管理 loading、error 和 data 三种状态，提供良好的用户体验`
  },
  {
    title: "实时聊天订阅",
    difficulty: "medium",
    description: "实现 WebSocket 连接管理，处理实时消息订阅和心跳检测",
    code: `import React, { useState, useEffect, useRef, useCallback } from 'react';

function ChatRoom({ roomId, userId }) {
  const [messages, setMessages] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const heartbeatIntervalRef = useRef(null);

  // 发送消息的函数
  const sendMessage = useCallback((text) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'message',
        roomId,
        userId,
        text,
        timestamp: Date.now()
      }));
    }
  }, [roomId, userId]);

  useEffect(() => {
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;
    
    function connect() {
      try {
        // 创建 WebSocket 连接
        const ws = new WebSocket("wss://chat.example.com/rooms/" + roomId);
        wsRef.current = ws;
        
        ws.onopen = () => {
          console.log('WebSocket connected');
          setConnectionStatus('connected');
          reconnectAttempts = 0;
          
          // 发送认证信息
          ws.send(JSON.stringify({
            type: 'auth',
            userId,
            roomId
          }));
          
          // 启动心跳检测
          heartbeatIntervalRef.current = setInterval(() => {
            if (ws.readyState === WebSocket.OPEN) {
              ws.send(JSON.stringify({ type: 'ping' }));
            }
          }, 30000); // 每30秒发送一次心跳
        };
        
        ws.onmessage = (event) => {
          const data = JSON.parse(event.data);
          
          switch (data.type) {
            case 'message':
              setMessages(prev => [...prev, {
                id: data.id,
                userId: data.userId,
                text: data.text,
                timestamp: data.timestamp
              }]);
              break;
            
            case 'history':
              setMessages(data.messages);
              break;
            
            case 'pong':
              // 心跳响应
              console.log('Heartbeat received');
              break;
          }
        };
        
        ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          setConnectionStatus('error');
        };
        
        ws.onclose = () => {
          console.log('WebSocket disconnected');
          setConnectionStatus('disconnected');
          
          // 清理心跳检测
          if (heartbeatIntervalRef.current) {
            clearInterval(heartbeatIntervalRef.current);
          }
          
          // 尝试重连
          if (reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            console.log("Reconnecting... Attempt " + reconnectAttempts);
            reconnectTimeoutRef.current = setTimeout(() => {
              connect();
            }, Math.min(1000 * Math.pow(2, reconnectAttempts), 30000));
          }
        };
        
      } catch (error) {
        console.error('Failed to create WebSocket:', error);
        setConnectionStatus('error');
      }
    }
    
    // 初始连接
    connect();
    
    // 清理函数
    return () => {
      // 清理重连定时器
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      
      // 清理心跳定时器
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }
      
      // 关闭 WebSocket 连接
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
    };
  }, [roomId, userId]); // 当房间或用户改变时重新连接

  return (
    <div className="chat-room">
      <div className="connection-status">
        Status: <span className={'status-' + connectionStatus}>
          {connectionStatus}
        </span>
      </div>
      
      <div className="messages">
        {messages.map(msg => (
          <div key={msg.id} className="message">
            <span className="user">{msg.userId}:</span>
            <span className="text">{msg.text}</span>
            <span className="time">
              {new Date(msg.timestamp).toLocaleTimeString()}
            </span>
          </div>
        ))}
      </div>
      
      <MessageInput onSend={sendMessage} disabled={connectionStatus !== 'connected'} />
    </div>
  );
}`,
    explanation: `这个中级示例展示了 useEffect 在复杂异步场景中的应用：

1. **WebSocket 生命周期管理**：在 useEffect 中创建连接，在清理函数中关闭连接
2. **自动重连机制**：使用指数退避算法实现断线重连，最多尝试5次
3. **心跳检测**：定期发送 ping 消息保持连接活跃，使用 setInterval 管理
4. **多个定时器管理**：使用 useRef 存储定时器引用，确保在清理时正确取消
5. **状态同步**：通过 WebSocket 消息更新本地状态，保持界面与服务器同步`
  },
  {
    title: "无限滚动与虚拟列表",
    difficulty: "hard", 
    description: "实现高性能的无限滚动列表，包含虚拟滚动、懒加载和内存优化",
    code: `import React, { useState, useEffect, useRef, useCallback } from 'react';

function VirtualInfiniteList({ 
  fetchItems, 
  itemHeight = 50,
  bufferSize = 5,
  threshold = 0.8 
}) {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 20 });
  
  const containerRef = useRef(null);
  const scrollPositionRef = useRef(0);
  const isLoadingRef = useRef(false);
  
  // 计算可见项范围
  const calculateVisibleRange = useCallback(() => {
    if (!containerRef.current) return;
    
    const container = containerRef.current;
    const scrollTop = container.scrollTop;
    const containerHeight = container.clientHeight;
    
    const start = Math.max(0, Math.floor(scrollTop / itemHeight) - bufferSize);
    const end = Math.min(
      items.length,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + bufferSize
    );
    
    setVisibleRange({ start, end });
  }, [items.length, itemHeight, bufferSize]);
  
  // 加载更多数据
  const loadMore = useCallback(async () => {
    if (isLoadingRef.current || !hasMore) return;
    
    isLoadingRef.current = true;
    setLoading(true);
    
    try {
      const { data, hasMore: more } = await fetchItems(page, 50);
      
      setItems(prev => {
        // 去重处理
        const existingIds = new Set(prev.map(item => item.id));
        const newItems = data.filter(item => !existingIds.has(item.id));
        return [...prev, ...newItems];
      });
      
      setHasMore(more);
      setPage(prev => prev + 1);
    } catch (error) {
      console.error('Failed to load items:', error);
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [fetchItems, page, hasMore]);
  
  // 处理滚动事件
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    let rafId;
    let lastScrollTop = 0;
    
    const handleScroll = () => {
      if (rafId) cancelAnimationFrame(rafId);
      
      rafId = requestAnimationFrame(() => {
        const scrollTop = container.scrollTop;
        const scrollHeight = container.scrollHeight;
        const clientHeight = container.clientHeight;
        
        // 更新可见范围
        calculateVisibleRange();
        
        // 检查是否需要加载更多
        const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;
        if (scrollPercentage > threshold && !isLoadingRef.current && hasMore) {
          loadMore();
        }
        
        // 保存滚动位置
        scrollPositionRef.current = scrollTop;
        lastScrollTop = scrollTop;
      });
    };
    
    container.addEventListener('scroll', handleScroll, { passive: true });
    
    // 初始计算
    calculateVisibleRange();
    
    return () => {
      container.removeEventListener('scroll', handleScroll);
      if (rafId) cancelAnimationFrame(rafId);
    };
  }, [calculateVisibleRange, loadMore, threshold, hasMore]);
  
  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      calculateVisibleRange();
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [calculateVisibleRange]);
  
  // 初始加载
  useEffect(() => {
    loadMore();
  }, []); // 仅在组件挂载时执行
  
  // 清理不可见的图片以节省内存
  useEffect(() => {
    const images = containerRef.current?.querySelectorAll('img[data-src]');
    if (!images) return;
    
    const imageObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            imageObserver.unobserve(img);
          }
        });
      },
      { rootMargin: '50px' }
    );
    
    images.forEach(img => imageObserver.observe(img));
    
    return () => imageObserver.disconnect();
  }, [visibleRange]);
  
  const visibleItems = items.slice(visibleRange.start, visibleRange.end);
  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.start * itemHeight;
  
  return (
    <div 
      ref={containerRef}
      className="virtual-list-container"
      style={{ height: '600px', overflow: 'auto', position: 'relative' }}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div 
          style={{ 
            transform: "translateY(" + offsetY + "px)",
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleItems.map((item, index) => (
            <div 
              key={item.id} 
              className="list-item"
              style={{ height: itemHeight }}
            >
              <img 
                data-src={item.thumbnail} 
                alt={item.title}
                className="lazy-image"
              />
              <div className="item-content">
                <h3>{item.title}</h3>
                <p>{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {loading && (
        <div className="loading-indicator">
          Loading more items...
        </div>
      )}
      
      {!hasMore && items.length > 0 && (
        <div className="end-message">
          No more items to load
        </div>
      )}
    </div>
  );
}`,
    explanation: `这个高级示例展示了 useEffect 在性能优化场景中的复杂应用：

1. **虚拟滚动实现**：只渲染可见区域的元素，大幅减少 DOM 节点数量
2. **多个 useEffect 协作**：分别处理滚动监听、窗口大小变化、初始加载和图片懒加载
3. **性能优化技巧**：
   - 使用 requestAnimationFrame 节流滚动事件
   - 使用 IntersectionObserver 实现图片懒加载
   - 使用 useRef 避免不必要的重渲染
4. **内存管理**：及时清理事件监听器和观察器，防止内存泄漏
5. **复杂状态同步**：管理加载状态、分页、可见范围等多个相关状态`
  }
];

export default businessScenarios; 