import { ApiItem } from "@/types/api";
import basicInfo from "./basic-info";
import businessScenarios from "./business-scenarios";
import implementation from "./implementation";
import interviewQuestions from "./interview-questions";
import commonQuestions from "./common-questions";
import knowledgeArchaeology from "./knowledge-archaeology";
import performanceOptimization from "./performance-optimization";
import learningPath from "./learning-path";
import debuggingTips from "./debugging-tips";
import essenceInsights from "./essence-insights";

const useEffectApi: ApiItem = {
  id: "react-useEffect",
  title: "useEffect",
  description: "useEffect是React中用于处理副作用的Hook，允许在函数组件中执行数据获取、订阅、DOM操作等副作用",
  category: "React Hooks",
  difficulty: "medium",
  version: "16.8+",
  isNew: false,
  
  // 基础语法 - 从 basic-info.ts 同步
  syntax: `useEffect(effect: () => void | (() => void), dependencies?: DependencyList)

// 基础用法
useEffect(() => {
  // 副作用逻辑
});

// 带依赖数组
useEffect(() => {
  // 副作用逻辑
}, [dependency1, dependency2]);

// 带清理函数
useEffect(() => {
  const subscription = subscribeToSomething();
  return () => {
    subscription.unsubscribe();
  };
}, []);`,
  
  // 基础示例 - 从 business-scenarios.ts 提取
  example: `import React, { useState, useEffect } from 'react';

function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // 重置状态
    setLoading(true);
    setError(null);
    
    // 创建 AbortController 用于取消请求
    const controller = new AbortController();
    
    // 异步获取数据
    async function fetchUser() {
      try {
        const response = await fetch(
          "https://api.example.com/users/" + userId,
          { signal: controller.signal }
        );
        
        if (!response.ok) {
          throw new Error('Failed to fetch user');
        }
        
        const data = await response.json();
        setUser(data);
      } catch (err) {
        // 忽略取消请求的错误
        if (err.name !== 'AbortError') {
          setError(err.message);
        }
      } finally {
        setLoading(false);
      }
    }
    
    fetchUser();
    
    // 清理函数：取消未完成的请求
    return () => {
      controller.abort();
    };
  }, [userId]); // 当 userId 改变时重新获取数据

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!user) return <div>No user found</div>;

  return (
    <div className="user-profile">
      <h2>{user.name}</h2>
      <p>Email: {user.email}</p>
      <p>Role: {user.role}</p>
    </div>
  );
}`,
  
  notes: "必须在函数组件的顶层调用，不能在条件语句、循环或嵌套函数中调用。副作用函数不能是 async 函数，需要异步操作时应在内部定义 async 函数。",
  
  // 必选Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  
  // 可选Tab内容
  performanceOptimization,
  learningPath,
  debuggingTips,
  essenceInsights,
  // versionMigration,  // 待实现
  // ecosystemTools,    // 待实现
  // realWorldProjects, // 待实现
};

export default useEffectApi; 