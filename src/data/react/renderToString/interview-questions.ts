import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 1,
    question: 'renderToString是什么？它的主要用途是什么？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'renderToString是ReactDOM提供的服务端渲染API，用于将React组件同步渲染为HTML字符串',
      detailed: `renderToString是ReactDOM/server包中的核心API，主要用于服务端渲染(SSR)场景。

**主要特点：**
1. **同步渲染**：采用阻塞式渲染，一次性完成整个组件树的渲染
2. **纯HTML输出**：生成不包含React运行时信息的纯HTML字符串
3. **服务端专用**：只能在Node.js环境中使用，不能在浏览器中运行

**主要用途：**
- 传统SSR应用的服务端渲染
- 静态站点生成(SSG)
- 邮件模板渲染
- PDF报告生成
- SEO优化和首屏性能提升

**与客户端渲染的区别：**
- 服务端渲染：HTML在服务器生成，用户立即看到内容
- 客户端渲染：HTML在浏览器生成，需要等待JavaScript执行`,
      code: `import { renderToString } from 'react-dom/server';

function App() {
  return (
    <div>
      <h1>Hello, SSR!</h1>
      <p>This is server-side rendered content.</p>
    </div>
  );
}

// 服务端渲染
const html = renderToString(<App />);
console.log(html);
// 输出: <div><h1>Hello, SSR!</h1><p>This is server-side rendered content.</p></div>

// Express.js集成示例
app.get('/', (req, res) => {
  const html = renderToString(<App />);
  res.send(\`
    <!DOCTYPE html>
    <html>
      <head><title>SSR App</title></head>
      <body>
        <div id="root">\${html}</div>
        <script src="/bundle.js"></script>
      </body>
    </html>
  \`);
});`
    },
    tags: ['基础概念', 'SSR']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 2,
    question: 'renderToString与renderToPipeableStream、renderToReadableStream有什么区别？',
    difficulty: 'medium',
    frequency: 'high',
    category: 'API对比',
    answer: {
      brief: 'renderToString是同步渲染，而renderToPipeableStream和renderToReadableStream是流式渲染，支持Suspense和并发特性',
      detailed: `这三个API代表了React服务端渲染的不同发展阶段和使用场景：

**renderToString（传统同步渲染）：**
- **渲染方式**：同步阻塞渲染，一次性完成
- **Suspense支持**：不支持，遇到会抛出错误
- **性能特点**：简单直接，但大型应用可能阻塞服务器
- **适用场景**：简单SSR、静态生成、邮件模板

**renderToPipeableStream（Node.js流式渲染）：**
- **渲染方式**：流式渲染，支持渐进式传输
- **Suspense支持**：完全支持，可以处理异步组件
- **性能特点**：更好的TTFB，支持Shell优先渲染
- **适用场景**：Node.js服务器，复杂应用的SSR

**renderToReadableStream（Web标准流式渲染）：**
- **渲染方式**：基于Web Streams API的流式渲染
- **Suspense支持**：完全支持异步组件和数据获取
- **性能特点**：跨平台兼容，支持边缘计算
- **适用场景**：边缘运行时、Cloudflare Workers、Deno

**选择建议：**
- 简单应用或静态生成：使用renderToString
- Node.js复杂应用：使用renderToPipeableStream
- 边缘计算或跨平台：使用renderToReadableStream`,
      code: `// renderToString - 同步渲染
import { renderToString } from 'react-dom/server';

const html = renderToString(<App />);
// 一次性返回完整HTML字符串

// renderToPipeableStream - Node.js流式渲染
import { renderToPipeableStream } from 'react-dom/server';

const stream = renderToPipeableStream(<App />, {
  onShellReady() {
    // Shell准备就绪，可以开始传输
    response.statusCode = 200;
    response.setHeader('Content-type', 'text/html');
    stream.pipe(response);
  },
  onError(error) {
    console.error('SSR Error:', error);
  }
});

// renderToReadableStream - Web标准流式渲染
import { renderToReadableStream } from 'react-dom/server';

const stream = await renderToReadableStream(<App />, {
  bootstrapScripts: ['/client.js']
});

// 在Cloudflare Workers中使用
return new Response(stream, {
  headers: { 'Content-Type': 'text/html' }
});

// Suspense支持对比
function AppWithSuspense() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AsyncComponent />
    </Suspense>
  );
}

// ❌ renderToString - 会抛出错误
// const html = renderToString(<AppWithSuspense />); // Error!

// ✅ renderToPipeableStream - 支持Suspense
const stream = renderToPipeableStream(<AppWithSuspense />);

// ✅ renderToReadableStream - 支持Suspense
const stream = await renderToReadableStream(<AppWithSuspense />);`
    },
    tags: ['API对比', '流式渲染']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 3,
    question: '在使用renderToString时如何处理异步数据获取和状态管理？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '高级应用',
    answer: {
      brief: 'renderToString不支持异步操作，需要在渲染前预先获取数据，并通过props或context传递给组件',
      detailed: `renderToString是同步API，无法处理异步数据获取，需要采用"数据预获取"模式：

**核心策略：**
1. **服务端数据预获取**：在调用renderToString前获取所有必要数据
2. **状态注入**：将获取的数据通过props或context传递给组件
3. **客户端状态同步**：确保客户端能够获取相同的初始状态
4. **Hydration一致性**：保证服务端和客户端渲染结果一致

**实现模式：**

**1. 路由级数据预获取**
为每个路由定义数据获取函数，在渲染前执行

**2. 组件级数据需求声明**
组件声明所需数据，服务端根据组件树收集并获取

**3. 状态管理集成**
与Redux、Zustand等状态管理库集成，预填充store

**4. 缓存策略**
实现数据缓存减少重复获取，提高性能

**注意事项：**
- 避免在组件内使用useEffect等异步Hook
- 确保所有异步操作在renderToString调用前完成
- 处理数据获取失败的降级策略
- 考虑数据获取的性能影响`,
      code: `// 1. 路由级数据预获取模式
import { renderToString } from 'react-dom/server';
import { matchRoutes } from 'react-router-dom';

// 路由配置与数据获取函数
const routes = [
  {
    path: '/product/:id',
    component: ProductPage,
    loadData: async (params) => {
      const product = await fetchProduct(params.id);
      const reviews = await fetchReviews(params.id);
      return { product, reviews };
    }
  },
  {
    path: '/user/:id',
    component: UserProfile,
    loadData: async (params) => {
      const user = await fetchUser(params.id);
      const posts = await fetchUserPosts(params.id);
      return { user, posts };
    }
  }
];

// 服务端渲染处理器
async function handleSSR(req, res) {
  const matches = matchRoutes(routes, req.url);

  if (!matches) {
    return res.status(404).send('Not Found');
  }

  try {
    // 预获取所有匹配路由的数据
    const dataPromises = matches.map(match => {
      const { route, params } = match;
      return route.loadData ? route.loadData(params) : Promise.resolve({});
    });

    const dataResults = await Promise.all(dataPromises);
    const initialData = Object.assign({}, ...dataResults);

    // 使用预获取的数据渲染组件
    const html = renderToString(
      <DataProvider data={initialData}>
        <App />
      </DataProvider>
    );

    // 将数据注入到HTML中供客户端使用
    const fullHtml = \`
      <!DOCTYPE html>
      <html>
        <head><title>SSR App</title></head>
        <body>
          <div id="root">\${html}</div>
          <script>
            window.__INITIAL_DATA__ = \${JSON.stringify(initialData)};
          </script>
          <script src="/bundle.js"></script>
        </body>
      </html>
    \`;

    res.send(fullHtml);
  } catch (error) {
    console.error('SSR Error:', error);
    res.status(500).send('Server Error');
  }
}

// 2. Redux集成示例
import { createStore } from 'redux';
import { Provider } from 'react-redux';

async function renderWithRedux(req, res) {
  // 创建服务端store
  const store = createStore(rootReducer);

  // 根据路由预填充store
  const routeData = await getRouteData(req.url);
  store.dispatch({ type: 'HYDRATE', payload: routeData });

  // 渲染应用
  const html = renderToString(
    <Provider store={store}>
      <App />
    </Provider>
  );

  // 获取最终状态
  const finalState = store.getState();

  const fullHtml = \`
    <!DOCTYPE html>
    <html>
      <body>
        <div id="root">\${html}</div>
        <script>
          window.__REDUX_STATE__ = \${JSON.stringify(finalState)};
        </script>
        <script src="/bundle.js"></script>
      </body>
    </html>
  \`;

  res.send(fullHtml);
}

// 3. 组件数据需求声明模式
function ProductPage({ productId }) {
  // 从context或props获取预获取的数据
  const { product, reviews } = useSSRData();

  return (
    <div>
      <h1>{product.name}</h1>
      <p>{product.description}</p>
      <ReviewList reviews={reviews} />
    </div>
  );
}

// 声明组件的数据需求
ProductPage.getServerSideProps = async ({ params }) => {
  const product = await fetchProduct(params.id);
  const reviews = await fetchReviews(params.id);
  return { product, reviews };
};

// 4. 错误处理和降级策略
async function robustSSR(req, res) {
  try {
    const data = await fetchDataWithTimeout(req.url, 5000); // 5秒超时
    const html = renderToString(<App data={data} />);
    res.send(createFullHTML(html, data));
  } catch (error) {
    console.error('SSR failed, falling back to CSR:', error);

    // 降级到客户端渲染
    const fallbackHTML = \`
      <!DOCTYPE html>
      <html>
        <body>
          <div id="root">
            <div>Loading...</div>
          </div>
          <script src="/bundle.js"></script>
        </body>
      </html>
    \`;

    res.send(fallbackHTML);
  }
}`
    },
    tags: ['异步数据', '状态管理']
  }
];

export default interviewQuestions;