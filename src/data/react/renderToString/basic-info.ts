import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: "renderToString是ReactDOM中用于同步服务端渲染的核心API",

  introduction: `renderToString是ReactDOM Server引入的同步渲染API，主要用于传统服务端渲染、静态HTML生成和简单SSR场景。它采用同步阻塞的设计模式，提供了最简单直接的服务端渲染解决方案。`,

  syntax: `import { renderToString } from 'react-dom/server';

const html = renderToString(element);`,

  quickExample: `import { renderToString } from 'react-dom/server';

function App() {
  return (
    <div>
      <h1>Hello, Server-Side Rendering!</h1>
      <p>This content is rendered on the server.</p>
    </div>
  );
}

// 服务端渲染
const html = renderToString(<App />);
console.log(html); // <div><h1>Hello, Server-Side Rendering!</h1><p>This content is rendered on the server.</p></div>`,

  scenarioDiagram: `graph TD
    A[renderToString应用场景] --> B[传统SSR]
    A --> C[静态站点生成]
    A --> D[邮件模板渲染]

    B --> B1[Express.js集成]
    B --> B2[Koa.js集成]
    B --> B3[Next.js自定义服务器]

    C --> C1[博客文章生成]
    C --> C2[文档站点构建]
    C --> C3[营销页面预渲染]

    D --> D1[HTML邮件生成]
    D --> D2[PDF报告渲染]
    D --> D3[通知模板生成]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0`,
  
  parameters: [
    {
      name: "element",
      type: "ReactElement",
      required: true,
      description: "要渲染为HTML字符串的React元素",
      details: "可以是任何有效的React元素，包括组件、JSX元素或React.createElement()的返回值"
    }
  ],

  returnValue: {
    type: "string",
    description: "渲染后的HTML字符串，包含完整的标记但不包含React特定的属性（如data-reactroot）"
  },

  keyFeatures: [
    {
      feature: "同步渲染",
      description: "采用同步阻塞方式渲染，简单直接",
      importance: "high" as const,
      details: "不支持Suspense和并发特性，但提供最简单的SSR实现"
    },
    {
      feature: "纯HTML输出",
      description: "生成纯净的HTML字符串，不包含React运行时信息",
      importance: "high" as const,
      details: "输出的HTML可以直接用于静态站点或邮件模板"
    },
    {
      feature: "传统SSR支持",
      description: "为传统服务端渲染场景提供完整支持",
      importance: "medium" as const,
      details: "与Express、Koa等传统Node.js框架完美集成"
    }
  ],
  
  limitations: [
    "不支持Suspense和异步组件，遇到Suspense会抛出错误",
    "同步阻塞渲染，大型组件树可能影响服务器响应时间",
    "不支持React 18的并发特性和流式渲染",
    "无法处理客户端特定的API（如window、document）"
  ],

  bestPractices: [
    "用于简单的SSR场景和静态内容生成",
    "避免在组件中使用浏览器特定的API",
    "对于复杂应用考虑使用renderToPipeableStream或renderToReadableStream",
    "结合缓存策略提高服务端渲染性能",
    "确保服务端和客户端的React版本一致"
  ],

  warnings: [
    "遇到Suspense组件会直接抛出错误，需要避免使用",
    "大型组件树的同步渲染可能阻塞事件循环",
    "不适合需要流式传输或渐进式加载的场景"
  ]
};

export default basicInfo;