import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const renderToStringData: ApiItem = {
  id: 'renderToString',
  title: 'renderToString',
  description: 'ReactDOM Server API，用于将React组件同步渲染为HTML字符串，支持传统SSR、静态站点生成和邮件模板渲染',
  category: 'ReactDOM Server APIs',
  difficulty: 'medium',

  syntax: `import { renderToString } from 'react-dom/server';
const html = renderToString(element);`,
  example: `import { renderToString } from 'react-dom/server';

function App() {
  return <h1>Hello, SSR!</h1>;
}

const html = renderToString(<App />);
console.log(html); // <h1>Hello, SSR!</h1>`,
  notes: '同步渲染API，不支持Suspense和异步组件，适用于简单SSR场景',

  version: 'React 16.0.0+',
  tags: ["ReactDOM", "Server", "SSR", "同步渲染"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default renderToStringData;