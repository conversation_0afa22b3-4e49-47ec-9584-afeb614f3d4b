import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-1',
    question: '为什么renderToString遇到Suspense会抛出错误？',
    answer: `renderToString采用同步渲染模式，无法处理Suspense的异步特性。当遇到Suspense组件时，renderToString不知道如何等待异步内容加载完成，因此会直接抛出错误。

解决方案：
1. 避免在使用renderToString的组件树中使用Suspense
2. 改用renderToPipeableStream或renderToReadableStream
3. 在服务端预先获取所有异步数据，避免需要Suspense`,
    code: `// ❌ 错误：renderToString不支持Suspense
import { Suspense } from 'react';
import { renderToString } from 'react-dom/server';

function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AsyncComponent />
    </Suspense>
  );
}

// 这会抛出错误
const html = renderToString(<App />); // Error!

// ✅ 正确：使用流式渲染API
import { renderToPipeableStream } from 'react-dom/server';

const stream = renderToPipeableStream(<App />, {
  onShellReady() {
    stream.pipe(response);
  }
});

// ✅ 或者预先获取数据
async function renderWithData() {
  const data = await fetchAsyncData();
  const html = renderToString(<App data={data} />);
  return html;
}`,
    tags: ['Suspense', '错误处理'],
    relatedQuestions: ['如何在SSR中处理异步数据？', '什么时候使用流式渲染？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-2',
    question: 'renderToString生成的HTML如何在客户端进行hydration？',
    answer: `renderToString生成的HTML需要在客户端使用hydrateRoot进行hydration，使静态HTML变成可交互的React应用。

关键步骤：
1. 确保服务端和客户端渲染结果一致
2. 传递初始数据给客户端
3. 使用hydrateRoot而不是createRoot
4. 处理hydration不匹配的情况`,
    code: `// 服务端渲染
import { renderToString } from 'react-dom/server';

const initialData = { user: { name: 'John' } };
const html = renderToString(<App data={initialData} />);

const fullHTML = \`
<!DOCTYPE html>
<html>
  <body>
    <div id="root">\${html}</div>
    <script>
      window.__INITIAL_DATA__ = \${JSON.stringify(initialData)};
    </script>
    <script src="/client.js"></script>
  </body>
</html>
\`;

// 客户端hydration
import { hydrateRoot } from 'react-dom/client';

// 获取服务端传递的初始数据
const initialData = window.__INITIAL_DATA__;

// 使用相同的数据进行hydration
hydrateRoot(
  document.getElementById('root'),
  <App data={initialData} />
);

// 注意：确保服务端和客户端的props完全一致
// 否则会出现hydration不匹配警告`,
    tags: ['hydration', '客户端'],
    relatedQuestions: ['什么是hydration不匹配？', '如何调试SSR问题？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-3',
    question: 'renderToString的性能如何？什么情况下会成为瓶颈？',
    answer: `renderToString的性能主要受组件复杂度和数据量影响。作为同步渲染API，它会阻塞事件循环直到渲染完成。

性能特点：
- 简单组件：渲染速度很快，通常<10ms
- 复杂组件树：可能需要100ms+，影响服务器响应
- 大量数据：数据序列化和DOM生成是主要开销

成为瓶颈的情况：
1. 深层嵌套的组件树
2. 大量列表数据渲染
3. 复杂的计算逻辑
4. 高并发请求场景

优化建议：
- 使用组件缓存
- 简化组件结构
- 考虑流式渲染API`,
    code: `// 性能监控示例
import { performance } from 'perf_hooks';

function measureRenderPerformance(element, label) {
  const start = performance.now();
  const html = renderToString(element);
  const end = performance.now();

  console.log(\`\${label}: \${(end - start).toFixed(2)}ms\`);
  return html;
}

// 使用示例
const html = measureRenderPerformance(
  <ProductList products={products} />,
  'Product List Render'
);

// 性能优化：组件缓存
const cache = new Map();

function cachedRender(element, cacheKey) {
  if (cache.has(cacheKey)) {
    return cache.get(cacheKey);
  }

  const html = renderToString(element);
  cache.set(cacheKey, html);
  return html;
}`,
    tags: ['性能优化', '监控'],
    relatedQuestions: ['如何优化SSR性能？', '什么时候使用流式渲染？']
  }
];

export default commonQuestions;