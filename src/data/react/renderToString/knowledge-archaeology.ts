import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: `renderToString是React服务端渲染历史上的重要里程碑，它见证了Web开发从纯客户端渲染向同构应用的演进。这个API的诞生和发展反映了开发者对性能、SEO和用户体验的不断追求。`,

  background: `在React早期，所有渲染都发生在客户端，这导致了首屏加载慢、SEO困难等问题。随着React应用的复杂化和企业级需求的增长，社区迫切需要一种能够在服务端生成HTML的解决方案。renderToString正是在这种背景下诞生的。`,

  evolution: `renderToString的演进历程体现了React团队对服务端渲染理解的深化：从最初的简单HTML生成，到考虑性能优化，再到与现代并发特性的权衡。它的设计哲学从"功能完备"逐渐转向"简单可靠"，成为React生态系统中的稳定基石。`,

  timeline: [
    {
      year: '2013',
      event: 'React开源发布',
      description: 'Facebook开源React，但最初只支持客户端渲染',
      significance: '奠定了组件化开发的基础，但SSR需求尚未显现'
    },
    {
      year: '2014',
      event: 'React 0.12引入renderToString',
      description: '首次引入服务端渲染能力，支持将React组件渲染为HTML字符串',
      significance: '标志着React从纯客户端框架向同构应用框架的转变'
    },
    {
      year: '2015',
      event: 'React 0.14分离ReactDOM',
      description: 'renderToString移至react-dom/server包，明确了服务端渲染的定位',
      significance: '确立了React渲染层的架构分离，为后续发展奠定基础'
    },
    {
      year: '2016',
      event: 'React 15性能优化',
      description: '优化renderToString的性能，减少内存占用和渲染时间',
      significance: '使SSR在生产环境中更加实用，推动了SSR的广泛采用'
    },
    {
      year: '2017',
      event: 'React 16 Fiber架构',
      description: 'renderToString基于新的Fiber架构重写，但保持同步渲染特性',
      significance: '在架构升级的同时保持了API的稳定性和向后兼容'
    },
    {
      year: '2021',
      event: 'React 18并发特性',
      description: '引入流式渲染API，renderToString成为传统同步渲染的代表',
      significance: '明确了renderToString在现代React生态中的定位：简单可靠的同步渲染'
    }
  ],

  keyFigures: [
    {
      name: 'Jordan Walke',
      role: 'React创始人',
      contribution: '创建了React的基础架构，为后续的服务端渲染奠定了基础',
      significance: '他的组件化思想使得服务端渲染成为可能'
    },
    {
      name: 'Sebastian Markbåge',
      role: 'React核心开发者',
      contribution: '设计了React的渲染架构，包括服务端渲染的核心机制',
      significance: '他的架构设计确保了renderToString的稳定性和性能'
    },
    {
      name: 'Dan Abramov',
      role: 'React生态推广者',
      contribution: '通过文档和教程推广了SSR的最佳实践',
      significance: '他的工作让renderToString被更广泛地理解和采用'
    }
  ],

  concepts: [
    {
      term: '同构应用(Isomorphic/Universal Apps)',
      definition: '能够在服务端和客户端运行相同代码的应用架构',
      evolution: '从纯客户端应用发展到服务端预渲染，再到现代的流式渲染',
      modernRelevance: 'renderToString是实现同构应用的基础工具，虽然简单但依然重要'
    },
    {
      term: '服务端渲染(Server-Side Rendering)',
      definition: '在服务器上生成HTML内容，然后发送给客户端的渲染策略',
      evolution: '从传统的模板引擎发展到React组件的服务端渲染',
      modernRelevance: '现代SSR已发展出流式渲染等高级特性，但renderToString仍是入门首选'
    }
  ],

  designPhilosophy: `renderToString的设计哲学体现了"简单性胜过复杂性"的原则。它选择了同步阻塞的方式，虽然牺牲了一些现代特性，但换来了可预测性和稳定性。这种设计反映了React团队对不同场景需求的深刻理解：有时候，简单可靠比功能丰富更重要。`,

  impact: `renderToString对Web开发产生了深远影响：它使React应用能够享受SEO优势，改善了首屏加载体验，推动了同构应用的发展。更重要的是，它证明了组件化架构在服务端的可行性，为后续的SSR框架（如Next.js、Gatsby）奠定了基础。`,

  modernRelevance: `在React 18时代，虽然有了更先进的流式渲染API，但renderToString依然具有重要价值。它是学习SSR概念的最佳起点，适用于简单场景和静态内容生成。对于不需要复杂异步特性的应用，renderToString仍然是最简单可靠的选择。它的存在提醒我们：技术的进步不意味着要抛弃简单有效的解决方案。`
};

export default knowledgeArchaeology;