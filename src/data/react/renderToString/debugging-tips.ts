import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'renderToString作为同步渲染API，虽然相对简单，但在实际使用中仍可能遇到一些常见问题。以下是最常见的问题及其解决方案。',
        sections: [
          {
            title: 'Suspense兼容性问题',
            description: 'renderToString不支持Suspense，遇到会直接抛出错误',
            items: [
              {
                title: 'Suspense组件导致渲染失败',
                description: '当组件树中包含Suspense组件时，renderToString会抛出错误',
                solution: '移除Suspense组件或改用流式渲染API',
                prevention: '在服务端渲染前预先获取所有异步数据',
                code: `// ❌ 错误：包含Suspense
function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AsyncComponent />
    </Suspense>
  );
}

// 这会抛出错误
const html = renderToString(<App />);

// ✅ 解决方案1：预先获取数据
async function renderApp() {
  const data = await fetchData();
  const html = renderToString(<App data={data} />);
  return html;
}

// ✅ 解决方案2：使用流式渲染
import { renderToPipeableStream } from 'react-dom/server';
const stream = renderToPipeableStream(<App />);`
              }
            ]
          },
          {
            title: 'Hydration不匹配问题',
            description: '服务端和客户端渲染结果不一致导致的问题',
            items: [
              {
                title: 'Date/Time相关的不匹配',
                description: '服务端和客户端的时间不同导致渲染结果不一致',
                solution: '使用统一的时间源或在客户端重新渲染时间相关内容',
                prevention: '避免在初始渲染中使用当前时间，或使用ISO字符串格式',
                code: `// ❌ 问题：服务端和客户端时间不同
function TimeDisplay() {
  return <div>{new Date().toLocaleString()}</div>;
}

// ✅ 解决方案：使用传入的时间
function TimeDisplay({ timestamp }) {
  return <div>{new Date(timestamp).toLocaleString()}</div>;
}

// 服务端传递时间戳
const timestamp = Date.now();
const html = renderToString(<App timestamp={timestamp} />);`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '有效的调试工具可以帮助快速定位和解决renderToString相关的问题。以下是推荐的调试工具和技巧。',
        sections: [
          {
            title: '性能监控工具',
            description: '监控renderToString的性能表现，识别性能瓶颈',
            items: [
              {
                title: '渲染时间监控',
                description: '测量renderToString的执行时间，识别性能问题',
                solution: '使用performance API或自定义计时器',
                prevention: '建立性能基准，设置告警阈值',
                code: `// 性能监控工具
import { performance } from 'perf_hooks';

class RenderMonitor {
  static measure(component, label) {
    const start = performance.now();
    const html = renderToString(component);
    const end = performance.now();

    const duration = end - start;
    console.log(\`[\${label}] 渲染时间: \${duration.toFixed(2)}ms\`);

    // 性能告警
    if (duration > 100) {
      console.warn(\`⚠️ 渲染时间过长: \${duration.toFixed(2)}ms\`);
    }

    return html;
  }

  static profile(component, iterations = 10) {
    const times = [];

    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      renderToString(component);
      const end = performance.now();
      times.push(end - start);
    }

    const avg = times.reduce((a, b) => a + b) / times.length;
    const min = Math.min(...times);
    const max = Math.max(...times);

    console.log(\`性能分析结果:
      平均时间: \${avg.toFixed(2)}ms
      最短时间: \${min.toFixed(2)}ms
      最长时间: \${max.toFixed(2)}ms\`);
  }
}

// 使用示例
const html = RenderMonitor.measure(<App />, 'Homepage');
RenderMonitor.profile(<ProductList products={products} />);`
              }
            ]
          },
          {
            title: '错误追踪工具',
            description: '捕获和分析renderToString过程中的错误',
            items: [
              {
                title: 'SSR错误边界',
                description: '创建专门的错误边界来处理服务端渲染错误',
                solution: '实现服务端错误边界和降级策略',
                prevention: '建立完善的错误监控和告警机制',
                code: `// SSR错误处理工具
class SSRErrorBoundary {
  static safeRender(component, fallback = '<div>渲染失败</div>') {
    try {
      return renderToString(component);
    } catch (error) {
      console.error('SSR渲染错误:', error);

      // 错误上报
      this.reportError(error, component);

      return fallback;
    }
  }

  static reportError(error, component) {
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      component: component.type?.name || 'Unknown',
      timestamp: new Date().toISOString()
    };

    // 发送到错误监控服务
    console.error('SSR Error Report:', errorInfo);
  }
}

// 使用示例
const html = SSRErrorBoundary.safeRender(
  <App />,
  '<div>页面暂时无法显示，请稍后刷新</div>'
);`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;