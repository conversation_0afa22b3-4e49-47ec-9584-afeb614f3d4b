import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-1',
    title: 'Express.js传统SSR集成',
    description: '在Express.js应用中集成renderToString实现传统服务端渲染，为SEO优化和首屏加载提供支持',
    businessValue: '提升搜索引擎排名，改善首屏加载体验，降低客户端渲染负担，提高页面可访问性',
    scenario: '电商网站需要为产品详情页实现SSR，确保搜索引擎能够正确抓取产品信息，同时为用户提供快速的首屏展示',
    code: `// server.js - Express.js集成
import express from 'express';
import React from 'react';
import { renderToString } from 'react-dom/server';
import ProductPage from './components/ProductPage';

const app = express();

// 产品详情页SSR路由
app.get('/product/:id', async (req, res) => {
  try {
    // 获取产品数据
    const productData = await fetchProductData(req.params.id);

    // 渲染React组件为HTML字符串
    const html = renderToString(
      <ProductPage product={productData} />
    );

    // 生成完整的HTML页面
    const fullHtml = \`
      <!DOCTYPE html>
      <html>
        <head>
          <title>\${productData.name} - 商城</title>
          <meta name="description" content="\${productData.description}" />
          <meta property="og:title" content="\${productData.name}" />
          <meta property="og:description" content="\${productData.description}" />
        </head>
        <body>
          <div id="root">\${html}</div>
          <script>
            window.__INITIAL_DATA__ = \${JSON.stringify(productData)};
          </script>
          <script src="/bundle.js"></script>
        </body>
      </html>
    \`;

    res.send(fullHtml);
  } catch (error) {
    console.error('SSR Error:', error);
    res.status(500).send('Server Error');
  }
});

// ProductPage组件
function ProductPage({ product }) {
  return (
    <div className="product-page">
      <h1>{product.name}</h1>
      <img src={product.image} alt={product.name} />
      <p className="price">¥{product.price}</p>
      <p className="description">{product.description}</p>
      <button className="add-to-cart">加入购物车</button>
    </div>
  );
}`,
    explanation: '通过Express.js路由集成renderToString，实现产品页面的服务端渲染。服务器获取产品数据后，使用renderToString将React组件渲染为HTML字符串，然后嵌入到完整的HTML模板中返回给客户端。',
    benefits: [
      'SEO友好：搜索引擎可以直接抓取到完整的产品信息',
      '首屏快速加载：用户立即看到产品内容，无需等待JavaScript执行',
      '社交媒体分享优化：Open Graph标签确保分享链接显示正确的产品信息'
    ],
    metrics: {
      performance: '首屏渲染时间减少60%，从2.5s降至1.0s',
      userExperience: '用户满意度提升25%，跳出率降低15%',
      technicalMetrics: 'SEO评分提升40%，搜索引擎收录率达到95%'
    },
    difficulty: 'easy',
    tags: ['Express.js', 'SSR', 'SEO', '电商']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-2',
    title: '静态博客站点生成',
    description: '使用renderToString为博客系统生成静态HTML页面，实现高性能的静态站点生成器',
    businessValue: '零服务器成本部署，极致的加载性能，完美的SEO支持，降低运维复杂度',
    scenario: '技术博客需要将Markdown文章转换为静态HTML页面，支持代码高亮、数学公式渲染，并生成RSS订阅源',
    code: `// static-generator.js - 静态博客生成器
import fs from 'fs';
import path from 'path';
import React from 'react';
import { renderToString } from 'react-dom/server';
import { marked } from 'marked';
import BlogPost from './components/BlogPost';
import BlogIndex from './components/BlogIndex';

class StaticBlogGenerator {
  constructor(config) {
    this.config = config;
    this.posts = [];
  }

  // 解析Markdown文章
  async parseMarkdownPosts() {
    const postsDir = path.join(process.cwd(), 'content/posts');
    const files = fs.readdirSync(postsDir);

    this.posts = files
      .filter(file => file.endsWith('.md'))
      .map(file => {
        const content = fs.readFileSync(path.join(postsDir, file), 'utf-8');
        const [, frontMatter, body] = content.match(/^---(.*?)---(.*)/s) || [];

        const meta = frontMatter.split('\\n').reduce((acc, line) => {
          const [key, value] = line.split(': ');
          if (key && value) acc[key.trim()] = value.trim();
          return acc;
        }, {});

        return {
          slug: file.replace('.md', ''),
          title: meta.title,
          date: meta.date,
          author: meta.author,
          tags: meta.tags?.split(',').map(tag => tag.trim()) || [],
          content: marked(body),
          excerpt: body.substring(0, 200) + '...'
        };
      })
      .sort((a, b) => new Date(b.date) - new Date(a.date));
  }

  // 生成单篇文章页面
  generatePostPage(post) {
    const html = renderToString(
      <BlogPost
        post={post}
        siteConfig={this.config}
      />
    );

    return this.wrapWithLayout(html, {
      title: post.title,
      description: post.excerpt,
      canonical: \`\${this.config.baseUrl}/posts/\${post.slug}\`
    });
  }

  // 生成首页和列表页
  generateIndexPage() {
    const html = renderToString(
      <BlogIndex
        posts={this.posts}
        siteConfig={this.config}
      />
    );

    return this.wrapWithLayout(html, {
      title: this.config.title,
      description: this.config.description,
      canonical: this.config.baseUrl
    });
  }

  // HTML布局包装
  wrapWithLayout(content, meta) {
    return \`<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>\${meta.title}</title>
  <meta name="description" content="\${meta.description}">
  <link rel="canonical" href="\${meta.canonical}">
  <link rel="stylesheet" href="/styles.css">
  <link rel="alternate" type="application/rss+xml" href="/rss.xml">
</head>
<body>
  <div id="root">\${content}</div>
  <script src="/app.js"></script>
</body>
</html>\`;
  }

  // 执行生成
  async build() {
    await this.parseMarkdownPosts();

    // 生成首页
    const indexHtml = this.generateIndexPage();
    fs.writeFileSync('dist/index.html', indexHtml);

    // 生成文章页面
    this.posts.forEach(post => {
      const postHtml = this.generatePostPage(post);
      const postDir = \`dist/posts/\${post.slug}\`;
      fs.mkdirSync(postDir, { recursive: true });
      fs.writeFileSync(\`\${postDir}/index.html\`, postHtml);
    });

    console.log(\`Generated \${this.posts.length} posts successfully!\`);
  }
}

// 使用示例
const generator = new StaticBlogGenerator({
  title: '技术博客',
  description: '分享前端开发经验和技术洞察',
  baseUrl: 'https://blog.example.com'
});

generator.build();`,
    explanation: '构建一个完整的静态博客生成器，使用renderToString将React组件渲染为静态HTML。系统解析Markdown文章，提取元数据，然后为每篇文章和索引页面生成对应的HTML文件。',
    benefits: [
      '零运行时成本：生成的静态文件可以部署到CDN',
      '极致性能：预渲染的HTML提供最快的加载速度',
      'SEO完美支持：搜索引擎可以直接索引所有内容',
      '部署简单：支持GitHub Pages、Netlify等静态托管服务'
    ],
    metrics: {
      performance: '页面加载时间<500ms，Lighthouse性能评分95+',
      userExperience: '用户停留时间增加40%，页面跳出率降低30%',
      technicalMetrics: '构建时间：100篇文章<10秒，部署成本降低90%'
    },
    difficulty: 'medium',
    tags: ['静态生成', '博客', 'Markdown', 'SEO']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-3',
    title: '企业级邮件模板渲染系统',
    description: '构建企业级邮件模板渲染系统，使用renderToString生成兼容各种邮件客户端的HTML邮件',
    businessValue: '提升邮件营销效果，降低模板维护成本，提高邮件送达率和用户参与度，支持个性化内容',
    scenario: '电商平台需要发送订单确认、促销活动、用户通知等多种类型的邮件，要求模板可复用、支持个性化、兼容主流邮件客户端',
    code: `// email-template-system.js - 企业级邮件模板系统
import React from 'react';
import { renderToString } from 'react-dom/server';
import nodemailer from 'nodemailer';

// 邮件模板组件基类
class EmailTemplate extends React.Component {
  render() {
    return (
      <html>
        <head>
          <meta charSet="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>{this.props.subject}</title>
          <style dangerouslySetInnerHTML={{
            __html: \`
              /* 邮件客户端兼容样式 */
              body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
              .container { max-width: 600px; margin: 0 auto; background: #ffffff; }
              .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
              .content { padding: 30px 20px; }
              .footer { background: #f3f4f6; padding: 20px; text-align: center; font-size: 12px; }
              .button {
                display: inline-block;
                padding: 12px 24px;
                background: #2563eb;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                margin: 10px 0;
              }
              @media only screen and (max-width: 600px) {
                .container { width: 100% !important; }
                .content { padding: 20px 15px !important; }
              }
            \`
          }} />
        </head>
        <body>
          <div className="container">
            {this.renderHeader()}
            <div className="content">
              {this.renderContent()}
            </div>
            {this.renderFooter()}
          </div>
        </body>
      </html>
    );
  }

  renderHeader() {
    return (
      <div className="header">
        <h1>{this.props.companyName}</h1>
      </div>
    );
  }

  renderFooter() {
    return (
      <div className="footer">
        <p>© 2024 {this.props.companyName}. 保留所有权利。</p>
        <p>
          <a href={this.props.unsubscribeUrl}>取消订阅</a> |
          <a href={this.props.privacyUrl}>隐私政策</a>
        </p>
      </div>
    );
  }
}

// 订单确认邮件模板
class OrderConfirmationEmail extends EmailTemplate {
  renderContent() {
    const { order, customer } = this.props;

    return (
      <div>
        <h2>订单确认</h2>
        <p>亲爱的 {customer.name}，</p>
        <p>感谢您的订购！您的订单已确认，详情如下：</p>

        <div style={{ border: '1px solid #e5e7eb', borderRadius: '8px', padding: '20px', margin: '20px 0' }}>
          <h3>订单信息</h3>
          <p><strong>订单号：</strong>{order.id}</p>
          <p><strong>下单时间：</strong>{new Date(order.createdAt).toLocaleString()}</p>
          <p><strong>预计送达：</strong>{order.estimatedDelivery}</p>

          <h4>商品清单</h4>
          {order.items.map(item => (
            <div key={item.id} style={{ borderBottom: '1px solid #f3f4f6', padding: '10px 0' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>{item.name} × {item.quantity}</span>
                <span>¥{item.price * item.quantity}</span>
              </div>
            </div>
          ))}

          <div style={{ marginTop: '15px', fontSize: '18px', fontWeight: 'bold' }}>
            总计：¥{order.total}
          </div>
        </div>

        <div style={{ textAlign: 'center', margin: '30px 0' }}>
          <a href={\`\${this.props.baseUrl}/orders/\${order.id}\`} className="button">
            查看订单详情
          </a>
        </div>

        <p>如有任何问题，请随时联系我们的客服团队。</p>
      </div>
    );
  }
}

// 促销邮件模板
class PromotionEmail extends EmailTemplate {
  renderContent() {
    const { promotion, customer } = this.props;

    return (
      <div>
        <h2 style={{ color: '#dc2626' }}>{promotion.title}</h2>
        <p>Hi {customer.name}，</p>
        <p>{promotion.description}</p>

        <div style={{
          background: 'linear-gradient(135deg, #fef3c7 0%, #fcd34d 100%)',
          padding: '30px',
          borderRadius: '12px',
          textAlign: 'center',
          margin: '25px 0'
        }}>
          <h3 style={{ margin: '0 0 10px 0', fontSize: '24px' }}>
            {promotion.discount}% OFF
          </h3>
          <p style={{ margin: '0 0 20px 0', fontSize: '16px' }}>
            优惠码：<strong>{promotion.code}</strong>
          </p>
          <p style={{ margin: '0', fontSize: '14px', color: '#92400e' }}>
            有效期至：{promotion.expiryDate}
          </p>
        </div>

        <div style={{ textAlign: 'center' }}>
          <a href={\`\${this.props.baseUrl}/shop?promo=\${promotion.code}\`} className="button">
            立即购买
          </a>
        </div>
      </div>
    );
  }
}

// 邮件渲染服务
class EmailRenderService {
  constructor(config) {
    this.config = config;
    this.transporter = nodemailer.createTransporter(config.smtp);
  }

  // 渲染邮件模板
  renderTemplate(TemplateComponent, props) {
    const html = renderToString(
      <TemplateComponent
        {...props}
        companyName={this.config.companyName}
        baseUrl={this.config.baseUrl}
        unsubscribeUrl={\`\${this.config.baseUrl}/unsubscribe\`}
        privacyUrl={\`\${this.config.baseUrl}/privacy\`}
      />
    );

    // 添加DOCTYPE和邮件客户端兼容性处理
    return \`<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">\` + html;
  }

  // 发送订单确认邮件
  async sendOrderConfirmation(order, customer) {
    const html = this.renderTemplate(OrderConfirmationEmail, {
      subject: \`订单确认 - \${order.id}\`,
      order,
      customer
    });

    await this.transporter.sendMail({
      from: this.config.fromEmail,
      to: customer.email,
      subject: \`订单确认 - \${order.id}\`,
      html
    });
  }

  // 发送促销邮件
  async sendPromotion(promotion, customers) {
    const promises = customers.map(customer => {
      const html = this.renderTemplate(PromotionEmail, {
        subject: promotion.title,
        promotion,
        customer
      });

      return this.transporter.sendMail({
        from: this.config.fromEmail,
        to: customer.email,
        subject: promotion.title,
        html
      });
    });

    await Promise.all(promises);
  }
}

// 使用示例
const emailService = new EmailRenderService({
  companyName: '优选商城',
  baseUrl: 'https://shop.example.com',
  fromEmail: '<EMAIL>',
  smtp: {
    host: 'smtp.example.com',
    port: 587,
    secure: false,
    auth: {
      user: 'smtp_user',
      pass: 'smtp_password'
    }
  }
});

// 发送订单确认
await emailService.sendOrderConfirmation(orderData, customerData);`,
    explanation: '构建企业级邮件模板渲染系统，使用renderToString将React组件渲染为兼容各种邮件客户端的HTML。系统支持模板继承、个性化内容、响应式设计，并集成了邮件发送功能。',
    benefits: [
      '模板复用性：基于React组件的模板系统，支持继承和组合',
      '个性化内容：动态渲染用户特定信息，提高邮件相关性',
      '兼容性保证：生成的HTML兼容主流邮件客户端',
      '维护效率：统一的模板管理，降低维护成本',
      '性能优化：服务端渲染确保邮件内容即时可见'
    ],
    metrics: {
      performance: '邮件渲染速度<100ms，支持并发1000+邮件/分钟',
      userExperience: '邮件打开率提升35%，点击率提升28%',
      technicalMetrics: '模板开发效率提升50%，邮件客户端兼容率99%'
    },
    difficulty: 'hard',
    tags: ['邮件系统', '模板引擎', '企业应用', 'React组件']
  }
];

export default businessScenarios;