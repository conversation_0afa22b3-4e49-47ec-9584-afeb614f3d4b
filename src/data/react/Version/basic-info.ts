import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "React.version是React库中用于获取当前React版本号的字符串常量，它提供了运行时的版本信息用于调试、兼容性检查和功能检测。",
  
  introduction: `React.version是React库自带的版本信息常量，包含当前使用的React版本的字符串表示。这个API从React最初版本就存在，主要用于运行时版本检测、调试信息输出和第三方库的兼容性判断。

**核心价值**：
- 🔍 **版本检测**：运行时获取React版本，用于兼容性判断
- 🐛 **调试支持**：在错误报告和日志中包含版本信息
- 📦 **库开发**：第三方库根据React版本选择不同实现策略
- 🔧 **工具集成**：开发工具和构建工具的版本感知功能

React.version是一个简单而重要的API，它为React生态系统提供了统一的版本查询机制。`,

  syntax: `// 直接访问版本字符串
import React from 'react';
console.log(React.version); // "18.2.0"

// 解构访问
import { version } from 'react';
console.log(version); // "18.2.0"

// 在组件中使用
function VersionDisplay() {
  return <div>React版本: {React.version}</div>;
}`,

  quickExample: `function ReactVersionChecker() {
  // 获取当前React版本
  const currentVersion = React.version;
  
  // 版本兼容性检查
  const isReact18 = currentVersion.startsWith('18');
  const supportsNewFeatures = parseFloat(currentVersion) >= 18.0;

  return (
    <div>
      {/* 显示版本信息 */}
      <p>当前React版本: {currentVersion}</p>
      <p>支持并发特性: {isReact18 ? '是' : '否'}</p>
      <p>支持新特性: {supportsNewFeatures ? '是' : '否'}</p>
      
      {/* 基于版本的条件渲染 */}
      {isReact18 && (
        <div>您正在使用React 18，享受并发特性！</div>
      )}
    </div>
  );
}`,

  scenarioDiagram: null,
  
  parameters: [],
  
  returnValue: {
    type: "string",
    description: "表示当前React版本的字符串，格式通常为 'major.minor.patch'，如 '18.2.0'",
    example: "'18.2.0'"
  },
  
  keyFeatures: [
    {
      title: "运行时版本检测",
      description: "在代码执行时动态获取React版本信息，无需额外配置",
      benefit: "支持版本感知的功能实现和兼容性处理"
    },
    {
      title: "语义化版本格式",
      description: "遵循semver规范，提供major.minor.patch格式的版本号",
      benefit: "便于进行版本比较和兼容性判断"
    },
    {
      title: "全局可访问",
      description: "作为React库的直接属性，任何地方都可以访问",
      benefit: "无需额外导入或配置，使用简单直接"
    },
    {
      title: "构建时常量",
      description: "在构建过程中被替换为实际版本字符串，不影响运行时性能",
      benefit: "零运行时开销，不会增加应用体积"
    }
  ],
  
  limitations: [
    "只提供字符串格式的版本号，需要手动解析进行版本比较",
    "不包含构建时间、提交hash等详细构建信息",
    "在某些打包工具配置下可能被意外替换或移除"
  ],
  
  bestPractices: [
    "在第三方库中使用React.version进行兼容性检查，而不是假设特定版本",
    "在错误报告和调试信息中包含React.version，便于问题排查",
    "使用semver库进行专业的版本比较，而不是简单的字符串比较",
    "在开发工具中展示React.version信息，提升开发体验",
    "避免在生产代码中过度依赖版本检测，优先使用特性检测"
  ],
  
  warnings: [
    "不要基于版本号进行复杂的功能分支判断，优先使用特性检测",
    "注意某些构建工具可能会在代码压缩时影响React.version的访问",
    "在服务端渲染时确保客户端和服务端的React版本一致"
  ]
};

export default basicInfo;