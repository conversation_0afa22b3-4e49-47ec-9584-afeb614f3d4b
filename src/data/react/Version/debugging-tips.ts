import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'React.version作为一个简单的字符串常量，虽然用法直观，但在实际开发中可能遇到版本检测、比较和兼容性相关的问题。以下是最常见的问题和解决方案。',
        sections: [
          {
            title: '版本比较陷阱',
            description: '直接使用字符串比较React版本是最常见的错误，可能导致意外的比较结果',
            items: [
              {
                title: '字符串比较失效',
                description: '使用简单的字符串比较无法正确处理版本号的语义',
                solution: '使用semver库或手动解析版本号进行数值比较',
                prevention: '建立标准的版本比较工具函数，避免在代码中直接比较字符串',
                code: `// ❌ 错误做法 - 字符串比较不可靠
if (React.version > '18.0.0') {
  // 可能得到错误结果
}

// ✅ 正确做法 - 使用semver库
import semver from 'semver';
if (semver.gte(React.version, '18.0.0')) {
  // 可靠的版本比较
}

// ✅ 或手动解析版本
function compareVersion(version, target) {
  const parseVersion = v => v.split('.').map(Number);
  const [vMajor, vMinor, vPatch] = parseVersion(version);
  const [tMajor, tMinor, tPatch] = parseVersion(target);
  
  if (vMajor !== tMajor) return vMajor - tMajor;
  if (vMinor !== tMinor) return vMinor - tMinor;
  return vPatch - tPatch;
}

if (compareVersion(React.version, '18.0.0') >= 0) {
  // 正确的版本比较
}`
              }
            ]
          },
          {
            title: 'SSR版本不匹配问题',
            description: '服务端和客户端React版本不一致可能导致hydration错误',
            items: [
              {
                title: 'hydration失败',
                description: '客户端和服务端React版本不匹配导致组件渲染不一致',
                solution: '在打包配置中锁定React版本，建立版本检测机制',
                prevention: '在CI/CD流程中验证版本一致性，使用lockfile锁定依赖版本',
                code: `// 版本一致性检测
function validateVersions() {
  const clientVersion = React.version;
  const serverVersion = window.__SERVER_REACT_VERSION__;
  
  if (clientVersion !== serverVersion) {
    console.error('React版本不匹配:', {
      client: clientVersion,
      server: serverVersion
    });
    
    // 发送监控告警
    sendErrorReport({
      type: 'VERSION_MISMATCH',
      client: clientVersion,
      server: serverVersion,
      userAgent: navigator.userAgent
    });
  }
}

// 在应用初始化时检测
if (typeof window !== 'undefined') {
  validateVersions();
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '利用开发工具和监控手段可以更好地管理React版本相关的问题，提前发现潜在的兼容性问题并建立有效的调试策略。',
        sections: [
          {
            title: '版本监控工具',
            description: '建立完善的版本监控和错误报告机制，及时发现版本相关问题',
            items: [
              {
                title: '版本感知的错误监控',
                description: '在错误报告中包含详细的版本信息，便于问题定位和解决',
                solution: '集成版本信息到错误监控系统，建立版本-错误关联分析',
                prevention: '定期分析版本相关错误模式，建立预警机制',
                code: `// 增强的错误监控
class VersionAwareErrorReporter {
  constructor() {
    this.reactVersion = React.version;
    this.setupGlobalErrorHandler();
  }

  setupGlobalErrorHandler() {
    window.addEventListener('error', (event) => {
      this.reportError({
        message: event.message,
        filename: event.filename,
        line: event.lineno,
        column: event.colno,
        stack: event.error?.stack,
        reactVersion: this.reactVersion,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        url: window.location.href
      });
    });

    // React错误边界集成
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError({
        type: 'UNHANDLED_PROMISE_REJECTION',
        reason: event.reason,
        reactVersion: this.reactVersion,
        timestamp: new Date().toISOString()
      });
    });
  }

  reportError(errorData) {
    // 发送到监控服务
    fetch('/api/errors', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(errorData)
    });

    // 开发环境下的详细日志
    if (process.env.NODE_ENV === 'development') {
      console.group('🔍 版本感知错误报告');
      console.error('错误详情:', errorData);
      console.info('React版本:', this.reactVersion);
      console.groupEnd();
    }
  }
}

// 在应用启动时初始化
const errorReporter = new VersionAwareErrorReporter();`
              }
            ]
          },
          {
            title: '版本兼容性测试',
            description: '建立自动化的版本兼容性测试，确保代码在不同React版本下正常工作',
            items: [
              {
                title: '多版本测试环境',
                description: '在CI中建立多个React版本的测试环境，验证向后兼容性',
                solution: '使用Docker和测试矩阵建立多版本测试pipeline',
                prevention: '定期运行兼容性测试，在版本升级前进行充分验证',
                code: `// package.json - 多版本测试配置
{
  "scripts": {
    "test:react-16": "REACT_VERSION=16 npm test",
    "test:react-17": "REACT_VERSION=17 npm test", 
    "test:react-18": "REACT_VERSION=18 npm test",
    "test:all-versions": "npm run test:react-16 && npm run test:react-17 && npm run test:react-18"
  },
  "devDependencies": {
    "react-16": "npm:react@16.14.0",
    "react-17": "npm:react@17.0.2", 
    "react-18": "npm:react@18.2.0"
  }
}

// 测试用例中的版本检测
describe('Version Compatibility Tests', () => {
  test('should work across React versions', () => {
    const version = React.version;
    const majorVersion = parseInt(version.split('.')[0]);
    
    if (majorVersion >= 18) {
      // 测试并发特性
      expect(typeof React.startTransition).toBe('function');
    } else if (majorVersion >= 16) {
      // 测试Hooks
      expect(typeof React.useState).toBe('function');
    }
    
    // 通用功能测试
    expect(typeof React.createElement).toBe('function');
    expect(typeof React.Component).toBe('function');
  });
});`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;