import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 'scenario-1',
    title: '第三方库兼容性检测系统',
    description: '在组件库开发中使用React.version进行兼容性检测，确保组件库在不同React版本下正常工作',
    businessValue: '提升组件库的兼容性和稳定性，减少版本升级带来的问题，降低技术支持成本',
    scenario: '开发一个通用组件库，需要同时支持React 16.8+和React 18，根据不同React版本提供相应的功能实现',
    code: `// 组件库兼容性检测
import React from 'react';

class CompatibilityDetector {
  private static reactVersion = React.version;
  
  // 检测是否支持并发特性
  static supportsConcurrent() {
    const majorVersion = parseInt(this.reactVersion.split('.')[0]);
    return majorVersion >= 18;
  }
  
  // 检测是否支持Hooks
  static supportsHooks() {
    const [major, minor] = this.reactVersion.split('.').map(Number);
    return major > 16 || (major === 16 && minor >= 8);
  }
  
  // 获取版本信息用于错误报告
  static getVersionInfo() {
    return {
      version: this.reactVersion,
      supportsConcurrent: this.supportsConcurrent(),
      supportsHooks: this.supportsHooks()
    };
  }
}

// 基于版本的条件组件
function AdaptiveComponent({ data, onUpdate }: Props) {
  const versionInfo = CompatibilityDetector.getVersionInfo();
  
  // React 18+ 使用并发特性
  if (versionInfo.supportsConcurrent) {
    return (
      <div>
        <h3>React {React.version} - 并发模式</h3>
        <ConcurrentDataList 
          data={data} 
          onUpdate={onUpdate}
          useDeferredValue={true}
        />
      </div>
    );
  }
  
  // React 16.8+ 使用Hooks
  if (versionInfo.supportsHooks) {
    return (
      <div>
        <h3>React {React.version} - Hooks模式</h3>
        <HooksDataList 
          data={data} 
          onUpdate={onUpdate}
        />
      </div>
    );
  }
  
  // 降级到类组件
  return <LegacyClassComponent data={data} onUpdate={onUpdate} />;
}

// 错误边界中包含版本信息
class ErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 在错误报告中包含React版本
    const errorReport = {
      error: error.message,
      reactVersion: React.version,
      timestamp: new Date().toISOString(),
      componentStack: errorInfo.componentStack
    };
    
    console.error('组件错误报告:', errorReport);
    // 发送到错误监控服务
    this.sendErrorReport(errorReport);
  }
}`,
    explanation: '通过React.version实现智能的版本兼容性检测，使组件库能够根据运行时的React版本选择合适的实现策略，确保在不同版本下的最佳体验。',
    benefits: [
      '提升组件库的通用性和兼容性覆盖范围',
      '减少版本升级时的破坏性变更和迁移成本',
      '提供更准确的错误报告和调试信息',
      '支持渐进式的功能升级和特性采用'
    ],
    metrics: {
      performance: '兼容性检测开销 < 1ms，不影响组件渲染性能',
      userExperience: '零破坏性升级，90%以上的版本自动兼容',
      technicalMetrics: '支持React 16.8+所有版本，错误定位准确率95%'
    },
    difficulty: 'medium',
    tags: ['兼容性', '组件库', '版本检测', '错误处理']
  },
  {
    id: 'scenario-2',
    title: '企业级应用监控和诊断系统',
    description: '在大型企业应用中集成React.version用于应用健康监控、性能分析和问题诊断',
    businessValue: '提升应用的可观测性和问题排查效率，降低生产环境问题的平均修复时间(MTTR)',
    scenario: '企业级单页应用需要完整的监控体系，包括性能监控、错误追踪、用户行为分析等，需要版本信息支持精确诊断',
    code: `// 企业级应用监控系统
import React from 'react';

interface MonitoringData {
  reactVersion: string;
  buildInfo: {
    timestamp: string;
    commitHash: string;
    environment: string;
  };
  performance: {
    renderTime: number;
    memoryUsage: number;
    componentCount: number;
  };
  userAgent: string;
}

class ApplicationMonitor {
  private static instance: ApplicationMonitor;
  private sessionId: string;
  private startTime: number;
  
  constructor() {
    this.sessionId = this.generateSessionId();
    this.startTime = performance.now();
  }
  
  static getInstance() {
    if (!this.instance) {
      this.instance = new ApplicationMonitor();
    }
    return this.instance;
  }
  
  // 收集基础监控数据
  collectBaselineMetrics(): MonitoringData {
    return {
      reactVersion: React.version,
      buildInfo: {
        timestamp: process.env.REACT_APP_BUILD_TIME || '',
        commitHash: process.env.REACT_APP_COMMIT_HASH || '',
        environment: process.env.NODE_ENV
      },
      performance: {
        renderTime: performance.now() - this.startTime,
        memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
        componentCount: this.getComponentCount()
      },
      userAgent: navigator.userAgent
    };
  }
  
  // 性能监控Hook
  usePerformanceMonitor(componentName: string) {
    React.useEffect(() => {
      const startTime = performance.now();
      
      return () => {
        const endTime = performance.now();
        const renderTime = endTime - startTime;
        
        this.reportPerformanceMetric({
          component: componentName,
          renderTime,
          reactVersion: React.version,
          timestamp: new Date().toISOString()
        });
      };
    }, [componentName]);
  }
  
  // 错误监控
  setupErrorMonitoring() {
    window.addEventListener('error', (event) => {
      this.reportError({
        type: 'JavaScript Error',
        message: event.error?.message || event.message,
        stack: event.error?.stack,
        reactVersion: React.version,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      });
    });
    
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError({
        type: 'Unhandled Promise Rejection',
        message: event.reason?.message || String(event.reason),
        reactVersion: React.version,
        url: window.location.href,
        timestamp: new Date().toISOString()
      });
    });
  }
}

// 诊断面板组件
function DiagnosticPanel() {
  const monitor = ApplicationMonitor.getInstance();
  const [metrics, setMetrics] = React.useState<MonitoringData | null>(null);
  
  React.useEffect(() => {
    const baseline = monitor.collectBaselineMetrics();
    setMetrics(baseline);
  }, [monitor]);
  
  return (
    <div className="diagnostic-panel">
      <h3>应用诊断信息</h3>
      {metrics && (
        <div>
          <div>React版本: {metrics.reactVersion}</div>
          <div>构建时间: {metrics.buildInfo.timestamp}</div>
          <div>提交哈希: {metrics.buildInfo.commitHash}</div>
          <div>运行环境: {metrics.buildInfo.environment}</div>
          <div>渲染时间: {metrics.performance.renderTime.toFixed(2)}ms</div>
          <div>内存使用: {(metrics.performance.memoryUsage / 1024 / 1024).toFixed(2)}MB</div>
          <div>组件数量: {metrics.performance.componentCount}</div>
        </div>
      )}
    </div>
  );
}`,
    explanation: '构建企业级的应用监控体系，将React.version作为核心标识信息，支持精确的问题定位、性能分析和运维决策。',
    benefits: [
      '提升生产环境问题的定位和解决效率',
      '建立完整的应用健康度监控体系',
      '支持基于版本的性能基准对比分析',
      '为技术团队提供数据驱动的优化决策支持'
    ],
    metrics: {
      performance: '监控开销 < 0.5%总体性能，数据收集延迟 < 100ms',
      userExperience: 'MTTR降低60%，问题定位准确率提升85%',
      technicalMetrics: '覆盖98%的错误场景，支持自动化报警和分析'
    },
    difficulty: 'hard',
    tags: ['监控', '诊断', '性能分析', '企业应用']
  },
  {
    id: 'scenario-3',
    title: '开发工具和调试面板集成',
    description: '在开发工具、浏览器扩展和调试面板中使用React.version提供版本感知的调试功能',
    businessValue: '提升开发效率和调试体验，减少环境相关问题的排查时间，加速开发迭代',
    scenario: '开发一个React开发工具扩展，需要根据不同React版本提供相应的调试功能和性能分析工具',
    code: `// React开发工具扩展
import React from 'react';

interface DevToolsState {
  reactVersion: string;
  supportedFeatures: string[];
  debugMode: boolean;
  performanceProfiler: boolean;
}

class ReactDevToolsExtension {
  private devToolsState: DevToolsState;
  
  constructor() {
    this.devToolsState = this.initializeDevTools();
  }
  
  private initializeDevTools(): DevToolsState {
    const version = React.version;
    const majorVersion = parseInt(version.split('.')[0]);
    const minorVersion = parseInt(version.split('.')[1]);
    
    return {
      reactVersion: version,
      supportedFeatures: this.getSupportedFeatures(majorVersion, minorVersion),
      debugMode: process.env.NODE_ENV === 'development',
      performanceProfiler: majorVersion >= 16
    };
  }
  
  private getSupportedFeatures(major: number, minor: number): string[] {
    const features = ['基础调试', '组件树查看'];
    
    if (major >= 16 && minor >= 8) {
      features.push('Hooks调试', 'Hooks性能分析');
    }
    
    if (major >= 17) {
      features.push('新JSX转换调试', '自动批处理分析');
    }
    
    if (major >= 18) {
      features.push('并发特性调试', 'Suspense边界分析', 'useTransition性能监控');
    }
    
    return features;
  }
  
  // 版本感知的调试工具
  renderVersionSpecificTools() {
    const { reactVersion, supportedFeatures, performanceProfiler } = this.devToolsState;
    
    return (
      <div className="dev-tools-panel">
        <div className="version-info">
          <h3>React DevTools</h3>
          <p>当前版本: {reactVersion}</p>
          <div className="features-list">
            <h4>支持的调试功能:</h4>
            {supportedFeatures.map(feature => (
              <div key={feature} className="feature-item">
                ✅ {feature}
              </div>
            ))}
          </div>
        </div>
        
        {performanceProfiler && (
          <div className="performance-tools">
            <h4>性能分析工具</h4>
            <button onClick={this.startProfiler}>开始性能分析</button>
            <button onClick={this.exportProfile}>导出性能报告</button>
          </div>
        )}
        
        <div className="debugging-helpers">
          <h4>调试助手</h4>
          <button onClick={this.highlightRenders}>高亮重渲染</button>
          <button onClick={this.inspectProps}>检查Props变化</button>
          <button onClick={this.analyzeMemory}>内存使用分析</button>
        </div>
      </div>
    );
  }
  
  // 智能错误提示
  provideVersionSpecificHelp(error: Error): string {
    const version = React.version;
    const majorVersion = parseInt(version.split('.')[0]);
    
    // 基于版本提供相关的解决建议
    if (error.message.includes('useEffect')) {
      if (majorVersion < 16) {
        return '当前React版本不支持Hooks，请升级到16.8+或使用类组件生命周期方法';
      }
      return '检查useEffect的依赖数组和清理函数';
    }
    
    if (error.message.includes('Suspense')) {
      if (majorVersion < 16) {
        return '当前React版本不支持Suspense，请升级到16.6+';
      }
      return '确保Suspense边界正确配置，检查fallback属性';
    }
    
    return '通用错误处理建议...';
  }
}

// 开发环境调试组件
function DebugInfo() {
  const [showDetails, setShowDetails] = React.useState(false);
  const devTools = new ReactDevToolsExtension();
  
  // 仅在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }
  
  return (
    <div className="debug-info" style={{ 
      position: 'fixed', 
      bottom: 10, 
      right: 10, 
      background: '#f0f0f0',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999
    }}>
      <div onClick={() => setShowDetails(!showDetails)} style={{ cursor: 'pointer' }}>
        🐛 React {React.version} Debug
      </div>
      
      {showDetails && (
        <div>
          {devTools.renderVersionSpecificTools()}
        </div>
      )}
    </div>
  );
}`,
    explanation: '创建版本感知的开发工具，根据不同React版本提供相应的调试功能，提升开发者的调试效率和问题定位能力。',
    benefits: [
      '提供版本特定的调试功能和工具建议',
      '智能化的错误提示和解决方案推荐',
      '简化开发环境的配置和问题排查流程',
      '为团队提供统一的调试和性能分析标准'
    ],
    metrics: {
      performance: '调试工具加载时间 < 200ms，不影响应用运行性能',
      userExperience: '问题排查效率提升70%，开发调试时间减少50%',
      technicalMetrics: '支持React 16+所有主要版本，覆盖95%的调试场景'
    },
    difficulty: 'medium',
    tags: ['开发工具', '调试', '浏览器扩展', '版本感知']
  }
];

export default businessScenarios;