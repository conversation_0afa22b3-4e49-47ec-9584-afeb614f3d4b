import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: "React.version的作用是什么？它在什么场景下会被使用？",
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: "React.version是获取当前React版本号的字符串常量，主要用于版本检测、调试和兼容性判断。",
      detailed: `React.version提供了获取当前React版本信息的简单方式，它的主要作用包括：

**核心作用**：
1. **版本检测**：在运行时获取React版本，进行兼容性判断
2. **调试支持**：在错误报告中包含版本信息，便于问题排查
3. **条件功能**：根据版本启用或禁用特定功能
4. **库开发**：第三方库根据React版本选择不同实现

**使用场景**：
- 组件库兼容性检测
- 错误监控和日志记录
- 开发工具版本感知
- 渐进式功能升级`,
      code: `// 版本检测示例
console.log('当前React版本:', React.version);

// 兼容性检测
const isReact18 = React.version.startsWith('18');
if (isReact18) {
  // 使用React 18特性
  enableConcurrentFeatures();
}

// 错误报告
const errorReport = {
  message: error.message,
  reactVersion: React.version,
  timestamp: new Date().toISOString()
};`
    }
  },
  {
    id: 2,
    question: "如何正确地进行React版本比较和兼容性检测？",
    difficulty: 'medium',
    frequency: 'high',
    category: '实践应用',
    answer: {
      brief: "应该使用专门的版本比较库如semver，或编写健壮的版本解析逻辑，避免简单的字符串比较。",
      detailed: `正确的React版本比较需要考虑语义化版本的复杂性：

**推荐方法**：
1. **使用semver库**：最专业和安全的版本比较方式
2. **版本解析**：将版本字符串解析为数字进行比较
3. **特性检测**：优先使用特性检测而非版本检测
4. **版本范围**：支持版本范围判断而非精确匹配

**避免的做法**：
- 简单的字符串比较
- 只比较主版本号
- 假设版本格式固定
- 忽略预发布版本标识`,
      code: `// ❌ 错误做法
if (React.version > '18.0.0') { /* 危险的字符串比较 */ }

// ✅ 正确做法1: 使用semver库
import semver from 'semver';
if (semver.gte(React.version, '18.0.0')) {
  // 安全的版本比较
}

// ✅ 正确做法2: 手动解析版本
function parseVersion(version) {
  const [major, minor, patch] = version.split('.').map(Number);
  return { major, minor, patch };
}

const currentVersion = parseVersion(React.version);
if (currentVersion.major >= 18) {
  // 基于解析结果的比较
}

// ✅ 最佳做法：特性检测优先
const supportsConcurrent = typeof React.startTransition === 'function';
if (supportsConcurrent) {
  // 基于特性而非版本的判断
}`
    }
  },
  {
    id: 3,
    question: "在企业级应用中，React.version应该如何集成到监控和错误报告系统中？",
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    answer: {
      brief: "将React.version作为核心监控指标，集成到错误报告、性能监控和版本分析系统中，建立版本感知的运维体系。",
      detailed: `企业级应用中React.version的集成需要系统性设计：

**监控集成策略**：
1. **错误报告增强**：所有错误报告自动包含React版本
2. **性能基准分析**：基于版本建立性能基准对比
3. **用户环境分析**：分析不同版本的用户分布和问题
4. **版本升级跟踪**：监控版本升级的影响和回归

**实施要点**：
- 自动化数据收集，减少手动介入
- 建立版本-问题关联分析
- 支持多环境版本对比
- 提供版本升级决策支持`,
      code: `// 企业级监控集成
class EnterpriseMonitor {
  constructor() {
    this.reactVersion = React.version;
    this.sessionId = this.generateSessionId();
  }
  
  // 增强错误报告
  reportError(error, context = {}) {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      reactVersion: this.reactVersion,
      sessionId: this.sessionId,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      buildInfo: {
        commitHash: process.env.REACT_APP_COMMIT_HASH,
        buildTime: process.env.REACT_APP_BUILD_TIME
      },
      ...context
    };
    
    // 发送到监控服务
    this.sendToMonitoringService(errorReport);
  }
  
  // 性能指标收集
  collectPerformanceMetrics() {
    return {
      reactVersion: this.reactVersion,
      renderTime: performance.now(),
      memoryUsage: (performance as any).memory?.usedJSHeapSize,
      componentCount: this.getComponentCount()
    };
  }
}`
    }
  }
];

export default interviewQuestions;