import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ReactVersionData: ApiItem = {
  id: 'Version',
  title: 'React.version',
  description: 'React.version是React库中用于获取当前React版本号的字符串常量，它提供了运行时的版本信息用于调试、兼容性检查和功能检测。',
  category: 'React API',
  difficulty: 'easy',
  
  syntax: `// 直接访问版本字符串
import React from 'react';
console.log(React.version); // "18.2.0"

// 解构访问
import { version } from 'react';
console.log(version); // "18.2.0"`,
  
  example: `// 版本检测示例
console.log('当前React版本:', React.version);

// 兼容性检测
const isReact18 = React.version.startsWith('18');
if (isReact18) {
  // 使用React 18特性
  enableConcurrentFeatures();
}`,
  
  notes: '只提供字符串格式的版本号，需要手动解析进行版本比较',
  
  version: 'React 0.3.0+',
  tags: ["React","version","API","metadata","兼容性","调试"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ReactVersionData;