import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'version-access',
    question: "如何获取当前React版本？",
    answer: "有两种方式：1) 通过React.version直接访问；2) 通过import { version } from 'react'解构导入。两种方式都返回相同的版本字符串。",
    code: `// 方式1：直接访问
import React from 'react';
console.log(React.version); // "18.2.0"

// 方式2：解构导入
import { version } from 'react';
console.log(version); // "18.2.0"`,
    tags: ["基础用法", "导入方式"],
    relatedQuestions: ["React.version返回什么格式？", "在组件中如何使用React.version？"]
  },
  {
    id: 'version-format',
    question: "React.version返回什么格式的字符串？",
    answer: "React.version返回遵循语义化版本规范的字符串，格式为'major.minor.patch'，如'18.2.0'。对于预发布版本，可能包含额外标识符。",
    code: `// 典型版本格式
"18.2.0"     // 正式版本
"18.0.0-rc.0" // 候选版本
"19.0.0-beta.0" // 测试版本`,
    tags: ["版本格式", "语义化版本"],
    relatedQuestions: ["如何解析React版本号？", "如何比较React版本？"]
  },
  {
    id: 'version-comparison',
    question: "如何正确比较React版本？",
    answer: "不要使用简单的字符串比较。推荐使用semver库进行版本比较，或手动解析版本号为数字进行比较。优先考虑特性检测而非版本检测。",
    code: `// ❌ 错误方式
if (React.version > '18.0.0') { /* 不可靠 */ }

// ✅ 推荐方式1：使用semver库
import semver from 'semver';
if (semver.gte(React.version, '18.0.0')) {
  // 安全的版本比较
}

// ✅ 推荐方式2：手动解析
function parseVersion(version) {
  return version.split('.').map(Number);
}
const [major] = parseVersion(React.version);
if (major >= 18) {
  // 基于主版本号的比较
}

// ✅ 最佳方式：特性检测
if (typeof React.startTransition === 'function') {
  // 基于特性而非版本的判断
}`,
    tags: ["版本比较", "最佳实践", "特性检测"],
    relatedQuestions: ["semver库如何使用？", "特性检测vs版本检测的优缺点？"]
  },
  {
    id: 'component-usage',
    question: "在React组件中如何使用React.version？",
    answer: "可以在组件中直接使用React.version，常用于版本显示、条件渲染或错误报告。注意它是一个字符串常量，访问性能很高。",
    code: `function VersionInfo() {
  const version = React.version;
  const majorVersion = parseInt(version.split('.')[0]);
  
  return (
    <div>
      <p>React版本: {version}</p>
      {majorVersion >= 18 && (
        <p>✅ 支持并发特性</p>
      )}
    </div>
  );
}

// 在错误边界中使用
class ErrorBoundary extends React.Component {
  componentDidCatch(error, errorInfo) {
    console.error('Error details:', {
      message: error.message,
      reactVersion: React.version,
      timestamp: new Date().toISOString()
    });
  }
}`,
    tags: ["组件使用", "条件渲染", "错误处理"],
    relatedQuestions: ["如何在错误报告中包含版本信息？", "版本检测的性能影响？"]
  },
  {
    id: 'build-time-behavior',
    question: "React.version在构建时是如何工作的？",
    answer: "React.version在构建时被webpack等工具替换为实际的版本字符串字面量，因此在运行时没有任何计算开销，只是一个简单的字符串常量。",
    code: `// 构建前的源码（简化）
const version = __VERSION__;

// 构建后的代码
const version = "18.2.0";

// 在应用中的表现
console.log(React.version); // 直接返回字符串，零开销`,
    tags: ["构建机制", "性能", "构建时优化"],
    relatedQuestions: ["React.version对性能有影响吗？", "构建工具如何处理版本信息？"]
  },
  {
    id: 'debugging-usage',
    question: "如何在调试中有效使用React.version？",
    answer: "React.version是调试的重要工具，应该包含在错误报告、日志记录和开发工具中。它有助于快速识别版本相关的问题。",
    code: `// 错误报告增强
function reportError(error) {
  const errorReport = {
    message: error.message,
    stack: error.stack,
    reactVersion: React.version,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString(),
    url: window.location.href
  };
  
  // 发送到监控服务
  sendToErrorService(errorReport);
}

// 开发工具集成
if (process.env.NODE_ENV === 'development') {
  console.log('🚀 App started with React', React.version);
}

// 控制台信息
console.table({
  'React版本': React.version,
  '构建时间': process.env.REACT_APP_BUILD_TIME,
  '环境': process.env.NODE_ENV
});`,
    tags: ["调试", "错误报告", "日志记录"],
    relatedQuestions: ["如何建立版本感知的错误监控？", "开发工具如何利用版本信息？"]
  },
  {
    id: 'ssr-considerations',
    question: "在服务端渲染(SSR)中使用React.version需要注意什么？",
    answer: "在SSR环境中，确保客户端和服务端使用相同的React版本很重要。React.version可以用于版本一致性检查，避免hydration问题。",
    code: `// 服务端版本检查
const serverReactVersion = React.version;

// 客户端版本验证
function validateClientVersion() {
  const clientVersion = React.version;
  const serverVersion = window.__SERVER_REACT_VERSION__;
  
  if (clientVersion !== serverVersion) {
    console.warn('版本不匹配:', {
      client: clientVersion,
      server: serverVersion
    });
  }
}

// 在hydration前检查
if (typeof window !== 'undefined') {
  validateClientVersion();
}`,
    tags: ["SSR", "版本一致性", "hydration"],
    relatedQuestions: ["如何防止SSR版本不匹配？", "hydration错误与版本的关系？"]
  },
  {
    id: 'library-development',
    question: "开发React库时如何使用React.version？",
    answer: "在库开发中，React.version用于兼容性检查、功能降级和用户警告。应该建立清晰的版本支持策略，并在运行时进行适当的检查。",
    code: `// 库的兼容性检查
class MyLibrary {
  constructor() {
    this.checkCompatibility();
  }
  
  checkCompatibility() {
    const version = React.version;
    const [major, minor] = version.split('.').map(Number);
    
    // 检查最低版本要求
    if (major < 16 || (major === 16 && minor < 8)) {
      throw new Error(
        '此库需要React 16.8+，当前版本：' + version
      );
    }
    
    // 功能特性检查
    this.supportsConcurrent = major >= 18;
    this.supportsHooks = major > 16 || (major === 16 && minor >= 8);
  }
  
  // 基于版本的功能实现
  renderComponent(props) {
    if (this.supportsConcurrent) {
      return this.renderWithConcurrent(props);
    } else {
      return this.renderLegacy(props);
    }
  }
}`,
    tags: ["库开发", "兼容性检查", "版本支持"],
    relatedQuestions: ["如何设计库的版本兼容策略？", "库应该支持哪些React版本？"]
  }
];

export default commonQuestions;