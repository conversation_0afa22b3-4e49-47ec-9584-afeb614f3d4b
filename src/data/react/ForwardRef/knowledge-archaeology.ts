import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  introduction: `React.forwardRef的诞生是React组件系统演进过程中的一个重要里程碑。它解决了函数组件与类组件在ref处理上的不对等问题，体现了React团队对开发者体验一致性的追求。`,
  
  background: `在React早期，只有类组件能够接收ref，这是因为类组件有实例，而函数组件没有。随着React Hooks的引入，函数组件变得越来越强大，但ref传递的问题一直是一个痛点。开发者经常需要将函数组件重写为类组件，仅仅是为了支持ref传递。

这种不一致性不仅影响了开发效率，也违背了React"函数组件优先"的设计理念。React团队意识到需要一个解决方案来弥合这个gap。`,
  
  evolution: `React.forwardRef的演进体现了React团队对"零成本抽象"理念的追求。从最初的类组件ref特权，到forwardRef的透明传递，再到
forwardRef的设计哲学是"让组件封装不影响API使用"，这与现代软件工程中的"透明性原则"高度一致。它不仅解决了技术问题，更重要的是维护了React生态系统的一致性。`,
  
  timeline: [
    {
      year: '2013-2016',
      event: 'React早期时代',
      description: '只有类组件支持ref，函数组件无法接收ref属性，导致组件封装时的局限性',
      significance: '建立了ref系统的基础，但存在函数组件与类组件的不对等问题'
    },
    {
      year: '2018年3月',
      event: 'React 16.3引入forwardRef',
      description: 'React团队正式引入React.forwardRef API，解决函数组件无法接收ref的问题',
      significance: '实现了函数组件与类组件在ref处理上的平等，为组件库开发奠定基础'
    },
    {
      year: '2018年10月',
      event: 'React 16.6优化forwardRef',
      description: '改进了forwardRef的类型定义和DevTools支持，提升开发体验',
      significance: '完善了forwardRef的生态支持，特别是TypeScript和调试工具的集成'
    },
    {
      year: '2019-2024',
      event: 'Hooks时代的forwardRef',
      description: '随着Hooks的普及，forwardRef成为组件库和高阶组件开发的标准工具',
      significance: '确立了现代React开发中ref传递的最佳实践模式'
    }
  ],
  
  keyFigures: [
    {
      name: 'Dan Abramov',
      role: 'React核心团队成员',
      contribution: '推动了forwardRef API的设计和实现，强调了函数组件与类组件的平等性',
      significance: '他的设计理念确保了forwardRef的简洁性和一致性'
    },
    {
      name: 'Sebastian Markbåge',
      role: 'React架构师',
      contribution: '设计了React的组件系统架构，为forwardRef的实现提供了理论基础',
      significance: '他的架构设计确保了forwardRef能够无缝集成到React的渲染系统中'
    }
  ],
  
  concepts: [
    {
      term: 'Ref透明性',
      definition: '组件封装不应该影响ref的传递和使用体验',
      evolution: '从早期的"ref只能传给类组件"到现在的"ref可以透明传递给任何组件"',
      modernRelevance: '这个概念影响了现代React组件库的设计原则，确保了良好的开发者体验'
    },
    {
      term: 'ExoticComponent',
      definition: 'React中具有特殊行为的组件类型，通过$$typeof标记识别',
      evolution: '从普通函数组件到具有特殊ref处理能力的ExoticComponent',
      modernRelevance: '这种设计模式被React.memo、React.lazy等其他API采用，形成了统一的特殊组件处理机制'
    }
  ],
  
  designPhilosophy: `React.forwardRef体现了React的核心设计哲学：

**声明式编程**：通过declarative的方式描述ref的传递，而不是imperative的手动处理。

**组合优于继承**：forwardRef可以与其他React特性（如memo、lazy）组合使用，而不需要复杂的继承体系。

**开发者体验优先**：确保函数组件和类组件在ref使用上的一致性，降低学习成本。

**向后兼容**：新API的引入不会破坏现有代码，体现了React对稳定性的承诺。`,
  
  impact: `React.forwardRef对整个React生态系统产生了深远影响：

**组件库革命**：使得纯函数组件的UI库成为可能，推动了Ant Design、Material-UI等主流组件库的函数组件化重构。

**开发范式转变**：确立了"函数组件 + Hooks + forwardRef"的现代React开发模式，取代了传统的类组件模式。

**第三方生态繁荣**：降低了组件封装的技术门槛，促进了React第三方组件生态的蓬勃发展。

**TypeScript集成**：推动了React TypeScript类型系统的完善，特别是在泛型和工具类型方面的创新。`,
  
  modernRelevance: `在现代React开发中，forwardRef已经成为不可或缺的基础设施：

**Server Components时代**：在React Server Components中，forwardRef确保了服务端和客户端组件的ref处理一致性。

**并发特性支持**：在React 18的并发特性中，forwardRef与Suspense、useDeferredValue等新特性完美协作。

**框架无关性**：forwardRef的设计理念影响了Vue 3的ref传递机制，成为前端框架的通用模式。

**工程化实践**：现代前端工程化工具链对forwardRef提供了完整支持，包括构建优化、类型检查、lint规则等。`
};

export default knowledgeArchaeology;