import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: '社区工程师在使用React.forwardRef时最常遇到的问题和解决方案。这些都是来自真实项目的经验总结，帮助你快速定位和解决调试问题。',
        sections: [
          {
            title: '环境配置问题',
            description: '最常见的问题类型，通常与开发环境配置相关',
            items: [
              {
                title: 'TypeScript类型错误',
                description: 'forwardRef组件的TypeScript类型定义出现编译错误',
                solution: '明确指定泛型参数，确保ref类型和props类型正确匹配',
                prevention: '在定义forwardRef组件时总是明确指定泛型类型参数',
                code: `// ❌ 错误：缺少类型参数
const Button = React.forwardRef((props, ref) => {
  return <button ref={ref} {...props} />;
});

// ✅ 正确：明确指定类型
interface ButtonProps {
  children: ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (props, ref) => <button ref={ref} {...props} />
);`
              },
              {
                title: 'displayName显示问题',
                description: 'forwardRef组件在React DevTools中显示为Anonymous',
                solution: '手动设置displayName属性或使用命名函数',
                prevention: '为所有forwardRef组件设置有意义的displayName',
                code: `// ❌ 问题：匿名显示
const Button = React.forwardRef((props, ref) => {
  return <button ref={ref} {...props} />;
});

// ✅ 解决方案1：设置displayName
Button.displayName = 'Button';

// ✅ 解决方案2：使用命名函数
const Button = React.forwardRef(function Button(props, ref) {
  return <button ref={ref} {...props} />;
});`
              }
            ]
          },
          {
            title: '使用方法问题',
            description: 'API使用方法相关的常见错误和解决方案',
            items: [
              {
                title: 'ref为null错误',
                description: '访问ref.current时出现null错误',
                solution: '使用可选链操作符或进行null检查',
                prevention: '始终检查ref.current是否存在再进行操作',
                code: `// ❌ 错误：直接访问
const handleClick = () => {
  buttonRef.current.focus(); // 可能为null
};

// ✅ 正确：安全访问
const handleClick = () => {
  buttonRef.current?.focus();
  // 或者
  if (buttonRef.current) {
    buttonRef.current.focus();
  }
};`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'devtools-usage',
      title: '🛠️ DevTools使用',
      content: {
        introduction: '掌握开发工具的高级使用技巧，充分发挥React.forwardRef的调试能力。这些技巧来自资深开发者的实战经验。',
        sections: [
          {
            title: 'React DevTools面板',
            description: '最常用的调试面板，学会高效查看和分析forwardRef组件',
            items: [
              {
                title: '组件树中的forwardRef识别',
                description: '在React DevTools组件树中识别和查看forwardRef组件',
                steps: ['打开React DevTools', '查看Components面板', '寻找ForwardRef标记的组件', '检查组件的displayName设置'],
                tips: ['使用搜索功能快速定位组件', '检查组件的props和ref传递情况'],
                code: `// 在DevTools中查看的组件结构
<ForwardRef(Button)>  // 有displayName的情况
  <button />
</ForwardRef(Button)>

<ForwardRef>  // 没有displayName的情况  
  <button />
</ForwardRef>`
              },
              {
                title: 'ref传递链跟踪',
                description: '在复杂的组件层级中跟踪ref的传递路径',
                steps: ['选中目标组件', '查看Properties面板', '跟踪ref属性传递'],
                tips: ['使用高亮功能查看DOM对应关系', '检查ref是否正确绑定到DOM元素'],
                code: `// 在DevTools中追踪ref传递
Parent Component
├── useRef hook (buttonRef)
└── ForwardRef(Button)
    ├── props: { ref: buttonRef }
    └── <button ref={buttonRef} />`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'troubleshooting',
      title: '🔧 问题排查',
      content: {
        introduction: '系统化的问题排查方法，帮助你快速定位和解决React.forwardRef相关的各种问题。这些方法经过大量实践验证。',
        sections: [
          {
            title: '显示问题排查',
            description: '当forwardRef组件显示不正常时的排查方法',
            items: [
              {
                title: '组件未正确渲染',
                description: 'forwardRef组件没有按预期渲染或显示',
                steps: ['检查forwardRef语法是否正确', '验证props传递是否完整', '确认ref是否正确绑定', '查看控制台错误信息'],
                code: `// 排查步骤示例
// 1. 检查forwardRef语法
const MyComponent = React.forwardRef((props, ref) => {
  console.log('Props:', props); // 调试props
  console.log('Ref:', ref);     // 调试ref
  return <div ref={ref} {...props} />;
});

// 2. 验证使用方式
function Parent() {
  const ref = useRef(null);
  console.log('Parent ref:', ref); // 调试父组件ref
  
  return <MyComponent ref={ref} />;
}`
              },
              {
                title: 'ref绑定失败',
                description: 'ref没有正确绑定到DOM元素',
                steps: ['检查ref是否传递给正确的DOM元素', '验证组件是否已挂载', '确认没有条件渲染导致的问题'],
                code: `// 排查ref绑定问题
const MyComponent = React.forwardRef((props, ref) => {
  useEffect(() => {
    console.log('组件挂载，ref绑定状态:', ref?.current);
  }, [ref]);
  
  return (
    <div ref={ref}>
      {/* 确保ref绑定到正确的元素 */}
    </div>
  );
});`
              }
            ]
          },
          {
            title: '性能问题排查',
            description: '当forwardRef组件影响性能时的排查方法',
            items: [
              {
                title: '组件频繁重渲染',
                description: 'forwardRef组件出现不必要的重渲染',
                steps: ['使用React DevTools Profiler检查渲染次数', '添加React.memo包装组件', '检查props是否发生不必要的变化'],
                code: `// 性能问题排查
// 1. 添加渲染计数器
let renderCount = 0;
const MyComponent = React.forwardRef((props, ref) => {
  console.log('Render count:', ++renderCount);
  return <div ref={ref} {...props} />;
});

// 2. 使用memo优化
const OptimizedComponent = React.memo(
  React.forwardRef((props, ref) => {
    return <div ref={ref} {...props} />;
  })
);`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;