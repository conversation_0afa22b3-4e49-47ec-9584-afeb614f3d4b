import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ReactForwardRefData: ApiItem = {
  id: 'ForwardRef',
  title: 'React.forwardRef',
  description: 'React.forwardRef是React中用于转发ref到子组件的高阶组件，解决函数组件无法直接接收ref的问题',
  category: 'React Components',
  difficulty: 'medium',
  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react/index.d.ts:1200
 * - 实现文件：packages/react/src/ForwardRef.js:15
 */

// 基础语法
const Component = React.forwardRef((props, ref) => {
  return <div ref={ref}>{props.children}</div>;
});

// TypeScript 完整语法
function forwardRef<T, P = {}>(
  render: ForwardRefRenderFunction<T, P>
): ForwardRefExoticComponent<PropsWithoutRef<P> & RefAttributes<T>>;

// 完整示例
interface ButtonProps {
  children: ReactNode;
  onClick?: () => void;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  return (
    <button ref={ref} onClick={props.onClick}>
      {props.children}
    </button>
  );
});`,
  example: `function ForwardRefExample() {
  // 创建ref引用
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleClick = () => {
    // 可以直接访问DOM节点
    buttonRef.current?.focus();
  };

  return (
    <div>
      {/* forwardRef组件可以接收ref */}
      <Button 
        ref={buttonRef}
        onClick={() => console.log('点击')}
      >
        点击我
      </Button>
      <button onClick={handleClick}>聚焦按钮</button>
    </div>
  );
}`,
  notes: '只能用于函数组件，类组件本身就支持ref',
  version: 'React 16.3+',
  tags: ['forwardRef', 'ref转发', '组件库', 'TypeScript', 'HOC'],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ReactForwardRefData;