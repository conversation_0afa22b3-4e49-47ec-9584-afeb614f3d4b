import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 'ui-library',
    title: '🎯 UI组件库开发',
    description: '开发可复用的UI组件库，需要向外暴露DOM节点以支持focus、scroll等操作',
    businessValue: '提高组件库的易用性和兼容性，让用户能够直接操作底层DOM节点',
    scenario: '你正在开发一个企业级UI组件库，需要创建Button、Input等基础组件。这些组件需要支持用户通过ref直接访问DOM节点，以便进行focus、blur、scroll等操作。',
    code: `// UI组件库中的Button组件
import React, { forwardRef } from 'react';

interface ButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary';
  size?: 'small' | 'medium' | 'large';
  onClick?: () => void;
  disabled?: boolean;
}

// 使用forwardRef使Button支持ref传递
const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ children, variant = 'primary', size = 'medium', ...props }, ref) => {
    return (
      <button
        ref={ref}
        className={\`btn btn-\${variant} btn-\${size}\`}
        {...props}
      >
        {children}
      </button>
    );
  }
);

// 为组件添加displayName以便调试
Button.displayName = 'Button';

export default Button;

// 使用示例
function App() {
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleFocus = () => {
    // 直接操作DOM节点
    buttonRef.current?.focus();
  };

  return (
    <div>
      <Button ref={buttonRef} variant="primary">
        主要按钮
      </Button>
      <button onClick={handleFocus}>聚焦主要按钮</button>
    </div>
  );
}`,
    explanation: '通过forwardRef，UI组件库的Button组件可以透明地传递ref到底层的button元素。用户可以通过ref直接访问DOM节点进行focus等操作，提高了组件的易用性。displayName的设置让组件在React DevTools中有更好的可读性。',
    benefits: [
      '组件API保持简洁，用户使用体验与原生HTML元素一致',
      '支持所有DOM节点的原生方法，如focus、blur、scrollIntoView等',
      '提高组件库的兼容性，可以与其他需要ref的库无缝集成',
      '便于单元测试中直接访问DOM元素进行断言'
    ],
    metrics: {
      performance: '几乎无性能开销，只增加一层轻量的包装',
      userExperience: '用户可以直接操作DOM，体验更加直观',
      technicalMetrics: '减少50%的API学习成本，提高组件库adoption率'
    },
    difficulty: 'easy',
    tags: ['组件库', 'ref转发', 'DOM操作', 'TypeScript']
  },
  {
    id: 'hoc-wrapper',
    title: '🔗 高阶组件封装',
    description: '在高阶组件中保持ref的正确传递，确保封装前后组件行为一致',
    businessValue: '提高代码复用性，同时保持组件API的一致性，避免破坏性变更',
    scenario: '你需要创建一个高阶组件来为现有组件添加埋点、权限控制或样式增强功能。但是不能破坏原组件的ref使用，确保用户代码不需要任何修改。',
    code: `// 高阶组件：添加埋点功能
import React, { forwardRef, useEffect } from 'react';

interface WithTrackingProps {
  trackingId?: string;
  trackingEvent?: string;
}

function withTracking<T extends React.ElementType>(
  WrappedComponent: T
) {
  const WithTrackingComponent = forwardRef<
    React.ComponentRef<T>,
    React.ComponentProps<T> & WithTrackingProps
  >((props, ref) => {
    const { trackingId, trackingEvent, ...restProps } = props;

    useEffect(() => {
      if (trackingId) {
        // 埋点逻辑
        console.log(\`Component \${trackingId} mounted\`);
        
        // 发送埋点数据到分析平台
        analytics.track(trackingEvent || 'component_mounted', {
          componentId: trackingId,
          timestamp: Date.now()
        });
      }
    }, [trackingId, trackingEvent]);

    // 透传ref到原组件
    return <WrappedComponent ref={ref} {...restProps} />;
  });

  WithTrackingComponent.displayName = \`withTracking(\${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })\`;

  return WithTrackingComponent;
}

// 原始Input组件
const Input = forwardRef<HTMLInputElement, { placeholder?: string }>((props, ref) => {
  return <input ref={ref} {...props} />;
});

// 增强后的Input组件
const TrackedInput = withTracking(Input);

// 使用示例
function LoginForm() {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = () => {
    // ref功能完全正常
    const value = inputRef.current?.value;
    if (!value) {
      inputRef.current?.focus(); // 聚焦到输入框
    }
  };

  return (
    <form>
      <TrackedInput
        ref={inputRef}
        placeholder="请输入用户名"
        trackingId="login-username-input"
        trackingEvent="username_input_interaction"
      />
      <button onClick={handleSubmit}>登录</button>
    </form>
  );
}`,
    explanation: '高阶组件withTracking通过forwardRef保持了ref的透明传递。封装后的TrackedInput组件可以像原始Input一样使用ref，用户代码不需要任何修改。同时增加了埋点功能，实现了功能增强而不破坏原有API。',
    benefits: [
      '保持组件API的向后兼容性，用户代码无需修改',
      '实现关注点分离，埋点逻辑与业务逻辑解耦',
      '支持组合多个HOC而不丢失ref功能',
      '便于A/B测试和逐步迁移'
    ],
    metrics: {
      performance: '增加约1-2ms的组件包装开销',
      userExperience: '用户感知不到任何差异，使用体验保持一致',
      technicalMetrics: '减少80%的重构成本，提高代码复用率30%'
    },
    difficulty: 'medium',
    tags: ['高阶组件', 'ref转发', '埋点', '代码复用']
  },
  {
    id: 'third-party-integration',
    title: '🔌 第三方组件集成',
    description: '集成第三方组件库时保持ref传递能力，确保与现有代码库的兼容性',
    businessValue: '降低技术迁移成本，提高第三方组件的集成效率',
    scenario: '你的项目需要集成一个第三方图表库或富文本编辑器，但这些组件需要通过ref访问底层实例来调用特定方法。你需要创建一个包装组件来适配项目的设计系统，同时保持ref功能。',
    code: `// 第三方富文本编辑器集成
import React, { forwardRef, useImperativeHandle } from 'react';
import { Editor } from 'third-party-rich-editor';

interface RichEditorProps {
  initialValue?: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  theme?: 'light' | 'dark';
}

// 暴露给外部的API接口
export interface RichEditorRef {
  getContent: () => string;
  setContent: (content: string) => void;
  focus: () => void;
  insertText: (text: string) => void;
  undo: () => void;
  redo: () => void;
}

// 包装第三方编辑器
const RichEditor = forwardRef<RichEditorRef, RichEditorProps>(
  ({ initialValue, placeholder, onChange, theme = 'light' }, ref) => {
    const editorRef = useRef<any>(null);

    // 暴露精简的API接口
    useImperativeHandle(ref, () => ({
      getContent: () => {
        return editorRef.current?.getHTML() || '';
      },
      setContent: (content: string) => {
        editorRef.current?.setHTML(content);
      },
      focus: () => {
        editorRef.current?.focus();
      },
      insertText: (text: string) => {
        editorRef.current?.insertContent(text);
      },
      undo: () => {
        editorRef.current?.chain().undo().run();
      },
      redo: () => {
        editorRef.current?.chain().redo().run();
      }
    }), []);

    return (
      <div className={\`editor-wrapper theme-\${theme}\`}>
        <Editor
          ref={editorRef}
          content={initialValue}
          placeholder={placeholder}
          onUpdate={({ editor }) => {
            onChange?.(editor.getHTML());
          }}
          extensions={[
            // 配置第三方编辑器的扩展
          ]}
        />
      </div>
    );
  }
);

RichEditor.displayName = 'RichEditor';

// 使用示例
function ArticleEditor() {
  const editorRef = useRef<RichEditorRef>(null);

  const handleSave = () => {
    const content = editorRef.current?.getContent();
    if (content) {
      saveArticle(content);
    }
  };

  const handleInsertTemplate = () => {
    editorRef.current?.insertText('这是一个模板文本');
  };

  const handleUndo = () => {
    editorRef.current?.undo();
  };

  return (
    <div>
      <div className="toolbar">
        <button onClick={handleInsertTemplate}>插入模板</button>
        <button onClick={handleUndo}>撤销</button>
        <button onClick={handleSave}>保存</button>
      </div>
      
      <RichEditor
        ref={editorRef}
        placeholder="请输入文章内容..."
        theme="light"
        onChange={(content) => console.log('内容变化:', content)}
      />
    </div>
  );
}`,
    explanation: '通过forwardRef和useImperativeHandle的组合，我们创建了一个包装第三方富文本编辑器的组件。它不仅适配了项目的设计系统，还通过ref暴露了精简而实用的API。用户可以通过ref调用各种编辑器功能，而不需要了解底层第三方库的复杂API。',
    benefits: [
      '隐藏第三方库的复杂性，提供统一的API接口',
      '便于替换底层实现而不影响使用方代码',
      '提高代码的可维护性和可测试性',
      '支持渐进式迁移和版本升级'
    ],
    metrics: {
      performance: '通过API精简减少30%的不必要调用',
      userExperience: '统一的API设计降低学习成本50%',
      technicalMetrics: '减少第三方依赖暴露面，提高系统稳定性'
    },
    difficulty: 'hard',
    tags: ['第三方集成', 'useImperativeHandle', 'API设计', '组件封装']
  }
];

export default businessScenarios;