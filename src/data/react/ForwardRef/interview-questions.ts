import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: 'React.forwardRef是什么？它解决了什么问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'React.forwardRef是一个高阶组件，用于将ref透明地传递给子组件，解决函数组件无法直接接收ref的问题。',
      detailed: `React.forwardRef是React 16.3引入的一个高阶组件，主要解决以下问题：

**核心问题**：
函数组件无法像类组件那样直接接收ref属性，这在组件封装时会导致ref传递中断。

**解决方案**：
forwardRef通过包装组件，将ref作为第二个参数传递给渲染函数，使函数组件能够接收和使用ref。

**主要用途**：
1. **组件库开发**：UI组件需要暴露DOM节点供用户操作
2. **高阶组件封装**：保持ref传递的透明性
3. **第三方组件集成**：包装外部组件时保持ref功能

**技术实现**：
- 创建ForwardRefExoticComponent类型的特殊组件
- 运行时添加$$typeof标记让React识别
- 通过类型系统确保ref类型安全`,
      code: `// 基础用法
const Button = React.forwardRef((props, ref) => {
  return <button ref={ref} {...props} />;
});

// TypeScript用法
interface ButtonProps {
  children: ReactNode;
  onClick?: () => void;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (props, ref) => {
    return <button ref={ref} {...props} />;
  }
);

// 使用示例
function App() {
  const buttonRef = useRef<HTMLButtonElement>(null);
  
  const handleClick = () => {
    buttonRef.current?.focus();
  };
  
  return (
    <div>
      <Button ref={buttonRef}>点击我</Button>
      <button onClick={handleClick}>聚焦</button>
    </div>
  );
}`
    },
    tags: ['forwardRef', 'ref传递', '函数组件', 'React基础']
  },
  {
    id: 2,
    question: 'forwardRef的实现原理是什么？React内部是如何处理的？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: 'forwardRef通过创建特殊的ExoticComponent，在渲染时提取ref并作为第二个参数传递给渲染函数。',
      detailed: `**源码实现分析**：

1. **组件创建阶段**：
   - forwardRef接收渲染函数，返回ForwardRefExoticComponent
   - 添加$$typeof: REACT_FORWARD_REF_TYPE标记
   - 保存原始渲染函数到render属性

2. **React渲染阶段**：
   - React识别$$typeof标记，知道这是forwardRef组件
   - 从props中提取ref，避免传递给子组件
   - 调用render(props, ref)而不是render(props)

3. **类型系统支持**：
   - ForwardRefRenderFunction定义渲染函数类型
   - PropsWithoutRef确保props中不包含ref
   - RefAttributes添加ref属性支持

**关键代码流程**：
\`\`\`javascript
// 简化的实现逻辑
function forwardRef(render) {
  return {
    $$typeof: REACT_FORWARD_REF_TYPE,
    render,
    displayName: render.displayName || render.name
  };
}

// React渲染时的处理
function renderForwardRef(type, props, ref) {
  return type.render(props, ref);
}
\`\`\`

**内存和性能考虑**：
- forwardRef本身几乎没有运行时开销
- 只是改变了组件的调用方式，不影响渲染性能
- 类型检查在编译时完成，运行时无额外成本`,
      code: `// React内部的处理逻辑（简化版）
function forwardRef(render) {
  const elementType = {
    $$typeof: REACT_FORWARD_REF_TYPE,
    render
  };
  
  // 开发环境下保留调试信息
  if (__DEV__) {
    elementType.displayName = render.displayName || render.name || 'ForwardRef';
  }
  
  return elementType;
}

// 渲染阶段的处理
function beginWork(current, workInProgress) {
  switch (workInProgress.tag) {
    case ForwardRef:
      const Component = workInProgress.type;
      const props = workInProgress.pendingProps;
      const ref = workInProgress.ref;
      
      // 调用渲染函数，传入props和ref
      return Component.render(props, ref);
  }
}`
    },
    tags: ['源码分析', '渲染原理', 'ExoticComponent', '类型系统']
  },
  {
    id: 3,
    question: 'forwardRef与useImperativeHandle有什么关系？如何配合使用？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '实战应用',
    answer: {
      brief: 'forwardRef负责ref传递，useImperativeHandle负责定制ref暴露的API。两者配合可以精确控制组件对外暴露的接口。',
      detailed: `**两者的关系**：

**forwardRef**：
- 负责ref的传递机制
- 解决"能不能传"的问题
- 提供ref传递的基础设施

**useImperativeHandle**：
- 负责ref暴露的内容
- 解决"传什么"的问题
- 自定义ref的API接口

**配合使用的优势**：
1. **API精简**：只暴露必要的方法，隐藏内部实现
2. **类型安全**：通过TypeScript接口约束暴露的API
3. **向后兼容**：可以在不破坏现有API的情况下修改内部实现
4. **测试友好**：暴露的API更容易进行单元测试

**设计模式**：
这种组合体现了"外观模式"的设计思想，为复杂的内部实现提供简洁的外部接口。`,
      code: `// 组合使用示例：自定义Input组件
interface InputRef {
  focus: () => void;
  blur: () => void;
  getValue: () => string;
  setValue: (value: string) => void;
  select: () => void;
}

interface InputProps {
  placeholder?: string;
  defaultValue?: string;
  onChange?: (value: string) => void;
}

const Input = forwardRef<InputRef, InputProps>((props, ref) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [value, setValue] = useState(props.defaultValue || '');

  // 自定义ref暴露的API
  useImperativeHandle(ref, () => ({
    focus: () => {
      inputRef.current?.focus();
    },
    blur: () => {
      inputRef.current?.blur();
    },
    getValue: () => {
      return value;
    },
    setValue: (newValue: string) => {
      setValue(newValue);
      props.onChange?.(newValue);
    },
    select: () => {
      inputRef.current?.select();
    }
  }), [value, props.onChange]);

  return (
    <input
      ref={inputRef}
      value={value}
      placeholder={props.placeholder}
      onChange={(e) => {
        setValue(e.target.value);
        props.onChange?.(e.target.value);
      }}
    />
  );
});

// 使用示例
function Form() {
  const inputRef = useRef<InputRef>(null);

  const handleSubmit = () => {
    const value = inputRef.current?.getValue();
    if (!value) {
      inputRef.current?.focus();
      return;
    }
    console.log('提交值:', value);
  };

  const handleReset = () => {
    inputRef.current?.setValue('');
    inputRef.current?.focus();
  };

  return (
    <div>
      <Input ref={inputRef} placeholder="请输入内容" />
      <button onClick={handleSubmit}>提交</button>
      <button onClick={handleReset}>重置</button>
    </div>
  );
}`
    },
    tags: ['useImperativeHandle', 'API设计', '组件封装', '类型安全']
  },
  {
    id: 4,
    question: '在高阶组件中如何正确使用forwardRef？有哪些注意事项？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '高级应用',
    answer: {
      brief: '在HOC中使用forwardRef需要正确处理泛型类型、displayName和ref传递。关键是保持组件API的透明性。',
      detailed: `**高阶组件中forwardRef的挑战**：

1. **类型复杂性**：需要正确处理泛型类型推导
2. **displayName保持**：确保调试信息的可读性
3. **ref透明传递**：不能中断ref的传递链
4. **性能考虑**：避免不必要的重渲染

**最佳实践模式**：

**1. 泛型HOC模式**：
使用React.ComponentType泛型确保类型安全

**2. displayName继承**：
保持组件名称的可追踪性

**3. 条件ref处理**：
支持可选的ref传递

**常见陷阱**：
- 忘记传递ref导致功能缺失
- displayName设置不当导致调试困难
- 类型定义不准确导致编译错误
- 在HOC中创建新函数导致性能问题`,
      code: `// 完整的HOC forwardRef实现
function withEnhancement<T extends React.ElementType>(
  WrappedComponent: T
) {
  const WithEnhancementComponent = React.forwardRef<
    React.ComponentRef<T>,
    React.ComponentProps<T> & { enhancement?: boolean }
  >((props, ref) => {
    const { enhancement = false, ...restProps } = props;
    
    // 增强逻辑
    const enhancedProps = enhancement 
      ? { ...restProps, className: \`\${restProps.className || ''} enhanced\` }
      : restProps;

    // 透传ref
    return <WrappedComponent ref={ref} {...enhancedProps} />;
  });

  // 保持displayName
  const wrappedName = WrappedComponent.displayName || WrappedComponent.name || 'Component';
  WithEnhancementComponent.displayName = \`withEnhancement(\${wrappedName})\`;

  return WithEnhancementComponent;
}

// 支持可选ref的HOC
function withOptionalRef<T extends React.ElementType>(
  WrappedComponent: T
) {
  const WithOptionalRefComponent = React.forwardRef<
    React.ComponentRef<T>,
    React.ComponentProps<T>
  >((props, ref) => {
    // 处理可选ref的逻辑
    const componentProps = ref 
      ? { ...props, ref }
      : props;

    return <WrappedComponent {...componentProps} />;
  });

  WithOptionalRefComponent.displayName = \`withOptionalRef(\${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })\`;

  return WithOptionalRefComponent;
}

// 使用示例
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  return <button ref={ref} {...props} />;
});

const EnhancedButton = withEnhancement(Button);

// 使用增强后的组件
function App() {
  const buttonRef = useRef<HTMLButtonElement>(null);
  
  return (
    <EnhancedButton 
      ref={buttonRef}
      enhancement={true}
      onClick={() => buttonRef.current?.focus()}
    >
      增强按钮
    </EnhancedButton>
  );
}`
    },
    tags: ['高阶组件', '泛型类型', 'displayName', 'TypeScript']
  }
];

export default interviewQuestions;