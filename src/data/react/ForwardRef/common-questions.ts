import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'type-error',
    question: 'TypeScript中使用forwardRef时出现类型错误怎么办？',
    answer: '常见的类型错误包括ref类型不匹配、props类型推导失败等。解决方法是明确指定泛型参数，确保ref类型和props类型的正确定义。使用React.ComponentRef和React.ComponentProps工具类型可以帮助正确推导类型。',
    code: `// ❌ 错误：缺少泛型参数
const Button = React.forwardRef((props, ref) => {
  return <button ref={ref} {...props} />;
});

// ✅ 正确：明确指定泛型参数
interface ButtonProps {
  children: ReactNode;
  onClick?: () => void;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (props, ref) => {
    return <button ref={ref} {...props} />;
  }
);

// ✅ 使用工具类型
function withRef<T extends React.ElementType>(Component: T) {
  return React.forwardRef<
    React.ComponentRef<T>,
    React.ComponentProps<T>
  >((props, ref) => {
    return <Component ref={ref} {...props} />;
  });
}`,
    tags: ['TypeScript', '类型错误', '泛型', 'React.ComponentRef'],
    relatedQuestions: ['如何在HOC中正确使用forwardRef', 'forwardRef的类型定义是什么']
  },
  {
    id: 'display-name',
    question: 'forwardRef组件在React DevTools中显示为Anonymous，如何设置正确的名称？',
    answer: '需要手动设置displayName属性来改善调试体验。React DevTools会使用displayName来显示组件名称，这对于开发和调试非常重要。',
    code: `// ❌ 问题：在DevTools中显示为Anonymous
const Button = React.forwardRef((props, ref) => {
  return <button ref={ref} {...props} />;
});

// ✅ 解决方案：设置displayName
const Button = React.forwardRef((props, ref) => {
  return <button ref={ref} {...props} />;
});
Button.displayName = 'Button';

// ✅ 在HOC中保持displayName
function withTracking(WrappedComponent) {
  const WithTrackingComponent = React.forwardRef((props, ref) => {
    return <WrappedComponent ref={ref} {...props} />;
  });
  
  WithTrackingComponent.displayName = \`withTracking(\${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })\`;
  
  return WithTrackingComponent;
}

// ✅ 开发环境自动设置
const Button = React.forwardRef(function Button(props, ref) {
  return <button ref={ref} {...props} />;
});
// 函数名会自动成为displayName`,
    tags: ['displayName', 'React DevTools', '调试', '组件名称'],
    relatedQuestions: ['如何在高阶组件中保持组件名称', 'React DevTools调试技巧']
  },
  {
    id: 'ref-null',
    question: 'forwardRef组件的ref有时候是null，如何安全地使用？',
    answer: 'ref可能为null的原因包括组件未挂载、条件渲染等。使用可选链操作符(?.)和空值检查可以安全地访问ref。另外，理解ref的生命周期也很重要。',
    code: `// ❌ 危险：直接访问可能为null的ref
const MyComponent = () => {
  const ref = useRef();
  
  const handleClick = () => {
    ref.current.focus(); // 可能报错
  };
  
  return <Button ref={ref} onClick={handleClick} />;
};

// ✅ 安全：使用可选链和空值检查
const MyComponent = () => {
  const ref = useRef<HTMLButtonElement>(null);
  
  const handleClick = () => {
    // 方法1：可选链操作符
    ref.current?.focus();
    
    // 方法2：显式检查
    if (ref.current) {
      ref.current.focus();
    }
  };
  
  return <Button ref={ref} onClick={handleClick} />;
};

// ✅ 在useEffect中安全使用
const MyComponent = () => {
  const ref = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // 组件挂载后ref才会有值
    if (ref.current) {
      ref.current.scrollIntoView();
    }
  }, []);
  
  return <div ref={ref}>内容</div>;
};`,
    tags: ['ref安全', '可选链', '空值检查', 'useRef生命周期'],
    relatedQuestions: ['useRef的生命周期是什么', 'ref什么时候会是null']
  },
  {
    id: 'performance-impact',
    question: 'forwardRef会影响组件性能吗？如何优化？',
    answer: 'forwardRef本身的性能开销很小，但在某些场景下可能会导致不必要的重渲染。结合React.memo使用可以优化性能。主要优化策略包括避免在渲染时创建新函数、合理使用memo等。',
    code: `// ❌ 性能问题：在渲染时创建新函数
const Button = React.forwardRef((props, ref) => {
  return (
    <button
      ref={ref}
      onClick={() => props.onClick?.()} // 每次渲染都创建新函数
      {...props}
    />
  );
});

// ✅ 优化：直接传递函数
const Button = React.forwardRef((props, ref) => {
  return <button ref={ref} {...props} />;
});

// ✅ 结合React.memo优化
const Button = React.memo(
  React.forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
    return <button ref={ref} {...props} />;
  })
);

// ✅ 自定义比较函数
const Button = React.memo(
  React.forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
    return <button ref={ref} {...props} />;
  }),
  (prevProps, nextProps) => {
    // 自定义比较逻辑
    return prevProps.children === nextProps.children &&
           prevProps.disabled === nextProps.disabled;
  }
);

// ✅ 使用useCallback优化事件处理
const MyComponent = () => {
  const [count, setCount] = useState(0);
  
  const handleClick = useCallback(() => {
    setCount(c => c + 1);
  }, []);
  
  return <Button onClick={handleClick}>点击次数: {count}</Button>;
};`,
    tags: ['性能优化', 'React.memo', 'useCallback', '重渲染'],
    relatedQuestions: ['React.memo如何与forwardRef结合使用', '如何避免不必要的重渲染']
  },
  {
    id: 'multiple-refs',
    question: '如何在一个组件中处理多个ref？',
    answer: '可以通过useImperativeHandle暴露多个DOM节点的引用，或者使用callback ref来处理多个ref。关键是设计清晰的API接口。',
    code: `// ✅ 方案1：使用useImperativeHandle暴露多个ref
interface MultiRefComponent {
  focusInput: () => void;
  focusButton: () => void;
  getFormData: () => { input: string; checked: boolean };
}

const Form = React.forwardRef<MultiRefComponent>((props, ref) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const checkboxRef = useRef<HTMLInputElement>(null);
  
  useImperativeHandle(ref, () => ({
    focusInput: () => inputRef.current?.focus(),
    focusButton: () => buttonRef.current?.focus(),
    getFormData: () => ({
      input: inputRef.current?.value || '',
      checked: checkboxRef.current?.checked || false
    })
  }));
  
  return (
    <form>
      <input ref={inputRef} type="text" />
      <input ref={checkboxRef} type="checkbox" />
      <button ref={buttonRef} type="submit">提交</button>
    </form>
  );
});

// ✅ 方案2：使用callback ref
const MultiRefComponent = React.forwardRef((props, ref) => {
  const refs = useRef<{ [key: string]: HTMLElement | null }>({});
  
  const setRef = (key: string) => (element: HTMLElement | null) => {
    refs.current[key] = element;
  };
  
  useImperativeHandle(ref, () => ({
    getRef: (key: string) => refs.current[key],
    focusElement: (key: string) => {
      const element = refs.current[key];
      if (element && 'focus' in element) {
        element.focus();
      }
    }
  }));
  
  return (
    <div>
      <input ref={setRef('input')} />
      <button ref={setRef('button')}>按钮</button>
    </div>
  );
});`,
    tags: ['多ref管理', 'useImperativeHandle', 'callback ref', 'API设计'],
    relatedQuestions: ['useImperativeHandle的最佳实践', 'callback ref与useRef的区别']
  }
];

export default commonQuestions;