import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "React.forwardRef是React中用于转发ref到子组件的高阶组件，解决函数组件无法直接接收ref的问题",

  introduction: `React.forwardRef是React 16.3版本引入的高阶组件，主要用于ref转发、组件库开发和高阶组件封装。它采用高阶组件包装的设计模式，提供了透明的ref传递能力。

在React生态中，它是组件封装层的核心工具，常见于UI组件库、复合组件设计和第三方组件集成，特别适合需要暴露DOM节点或组件实例的场景。

核心优势包括ref透明传递、组件封装友好，但也需要注意类型定义复杂性和调试信息显示的问题。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react/index.d.ts:1200
 * - 实现文件：packages/react/src/ForwardRef.js:15
 */

// 基础语法
const Component = React.forwardRef((props, ref) => {
  return <div ref={ref}>{props.children}</div>;
});

// TypeScript 完整语法
function forwardRef<T, P = {}>(
  render: ForwardRefRenderFunction<T, P>
): ForwardRefExoticComponent<PropsWithoutRef<P> & RefAttributes<T>>;

// 完整示例
interface ButtonProps {
  children: ReactNode;
  onClick?: () => void;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  return (
    <button ref={ref} onClick={props.onClick}>
      {props.children}
    </button>
  );
});`,

  quickExample: `function ForwardRefExample() {
  // 创建ref引用
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleClick = () => {
    // 可以直接访问DOM节点
    buttonRef.current?.focus();
  };

  return (
    <div>
      {/* forwardRef组件可以接收ref */}
      <Button 
        ref={buttonRef}
        onClick={() => console.log('点击')}
      >
        点击我
      </Button>
      <button onClick={handleClick}>聚焦按钮</button>
    </div>
  );
}`,

  scenarioDiagram: `graph TD
    A[用户交互场景] --> B[组件库开发]
    A --> C[高阶组件封装] 
    A --> D[第三方组件集成]

    B --> B1["📦 UI组件库<br/>Button、Input等基础组件"]
    B --> B2["🔄 复合组件<br/>Form、Dialog等复杂组件"]
    B --> B3["🎨 主题组件<br/>带样式的封装组件"]

    C --> C1["🔗 功能增强<br/>添加额外props的HOC"]
    C --> C2["📊 状态管理<br/>连接状态的容器组件"]
    C --> C3["🛡️ 权限控制<br/>访问控制的包装组件"]

    D --> D1["📱 移动端组件<br/>RN组件桥接"]
    D --> D2["🌐 Web组件<br/>原生组件封装"]
    D --> D3["🔌 插件组件<br/>第三方库组件"]

    E[技术特性] --> F[ref透明传递]
    E --> G[组件封装支持]
    E --> H[TypeScript友好]

    F --> F1["🎯 DOM节点访问<br/>focus、scroll等操作"]
    F --> F2["📏 尺寸测量<br/>getBoundingClientRect"]
    F --> F3["🎪 动画控制<br/>imperative API调用"]

    G --> G1["🔧 API保持一致<br/>封装前后使用相同"]
    G --> G2["📝 组件文档化<br/>displayName保持可读"]
    G --> G3["🐛 调试友好<br/>DevTools显示正确"]

    H --> H1["🏷️ 类型推导<br/>完整的类型安全"]
    H --> H2["📚 代码提示<br/>IDE智能补全"]
    H --> H3["🔍 类型检查<br/>编译期错误检测"]

    I[相关API] --> J[useRef]
    I --> K[useImperativeHandle]
    I --> L[React.memo]

    J --> J1["🎯 ref创建和管理"]
    K --> K1["🔧 自定义ref暴露接口"]
    L --> L1["⚡ 性能优化结合使用"]

    style A fill:#e1f5fe
    style E fill:#f3e5f5
    style I fill:#e8f5e8`,
  
  parameters: [
    {
      name: "render",
      type: "ForwardRefRenderFunction<T, P>",
      required: true,
      description: "渲染函数，接收props和ref两个参数",
      example: "(props, ref) => <div ref={ref}>{props.children}</div>"
    }
  ],
  
  returnValue: {
    type: "ForwardRefExoticComponent<PropsWithoutRef<P> & RefAttributes<T>>",
    description: "返回可以接收ref的组件，具有特殊的类型标记",
    example: "const Component = forwardRef(...)"
  },
  
  keyFeatures: [
    {
      title: "ref透明传递",
      description: "将ref从父组件透明传递到子组件的DOM节点或组件实例",
      benefit: "解决函数组件无法直接接收ref的限制"
    },
    {
      title: "组件封装友好", 
      description: "保持组件API的一致性，封装前后使用方式相同",
      benefit: "提高组件库的易用性和兼容性"
    },
    {
      title: "TypeScript完美支持",
      description: "提供完整的类型推导和类型检查支持",
      benefit: "确保开发时的类型安全和代码提示"
    },
    {
      title: "调试信息保持",
      description: "通过displayName保持组件在DevTools中的可读性",
      benefit: "提高开发调试效率"
    }
  ],
  
  limitations: [
    "只能用于函数组件，类组件本身就支持ref",
    "增加了一层组件包装，可能影响性能",
    "TypeScript类型定义相对复杂",
    "ref的类型需要明确指定泛型参数"
  ],
  
  bestPractices: [
    "为forwardRef组件添加displayName以便调试",
    "结合useImperativeHandle暴露特定的API而不是整个DOM",
    "在TypeScript中明确指定ref的类型参数",
    "避免在高频重渲染的场景中过度使用",
    "组件库开发中优先考虑使用forwardRef"
  ],
  
  warnings: [
    "不要在渲染函数内部创建新的函数，会导致不必要的重渲染",
    "ref可能为null，使用前需要进行空值检查",
    "避免直接操作DOM，优先使用React的声明式方式"
  ],

  // 添加对比分析
  comparisonAnalysis: {
    title: "React.forwardRef vs 其他ref解决方案对比分析",
    description: "全面对比React.forwardRef与其他ref处理方案的优劣势、适用场景和实现成本",
    comparisons: [
      {
        name: "React.forwardRef",
        description: "函数组件ref传递的标准解决方案",
        advantages: [
          "官方标准方案，稳定可靠",
          "TypeScript支持完善，类型安全",
          "与React生态系统深度集成",
          "支持自定义ref API（配合useImperativeHandle）",
          "调试信息保持良好"
        ],
        disadvantages: [
          "增加了组件包装层级",
          "TypeScript类型定义相对复杂",
          "只解决ref传递问题，不解决其他问题"
        ],
        useCases: ["UI组件库开发", "高阶组件封装", "第三方组件集成"],
        performance: "高 - 轻量包装，几乎无性能开销",
        complexity: "中等 - 需要理解ref传递机制"
      },
      {
        name: "类组件 + createRef",
        description: "传统的类组件ref处理方案",
        advantages: [
          "原生支持，无需额外包装",
          "可以直接访问组件实例",
          "生命周期方法中ref必定可用",
          "历史悠久，文档资料丰富"
        ],
        disadvantages: [
          "只适用于类组件，无法用于函数组件",
          "类组件相对重量级",
          "与现代React Hook生态不兼容",
          "代码冗长，样板代码多"
        ],
        useCases: ["遗留系统维护", "复杂生命周期场景", "需要实例方法的场景"],
        performance: "中等 - 类组件开销较大",
        complexity: "低 - 直接使用"
      },
      {
        name: "useImperativeHandle",
        description: "自定义ref暴露内容的Hook方案",
        advantages: [
          "精确控制暴露的API，避免过度暴露",
          "可以组合多个内部ref",
          "支持复杂的ref逻辑",
          "与函数组件完美配合"
        ],
        disadvantages: [
          "必须配合forwardRef使用",
          "增加代码复杂度",
          "需要手动管理依赖项",
          "API设计需要仔细考虑"
        ],
        useCases: ["复杂组件API设计", "多ref管理", "第三方组件包装"],
        performance: "中等 - 额外的API创建开销",
        complexity: "高 - 需要精心设计API"
      },
      {
        name: "Callback Ref",
        description: "使用函数形式的ref回调",
        advantages: [
          "灵活性最高，可以处理复杂逻辑",
          "支持动态ref切换",
          "可以在ref变化时执行副作用",
          "不需要额外的Hook或HOC"
        ],
        disadvantages: [
          "每次渲染都可能创建新函数",
          "需要手动处理ref的生命周期",
          "代码可读性相对较差",
          "容易出现闭包陷阱"
        ],
        useCases: ["动态ref切换", "ref变化副作用", "复杂ref逻辑"],
        performance: "中等 - 函数创建开销",
        complexity: "高 - 需要理解ref生命周期"
      },
      {
        name: "Portal + DOM操作",
        description: "绕过React直接进行DOM操作",
        advantages: [
          "完全控制DOM节点",
          "可以实现React无法实现的功能",
          "与第三方库集成友好",
          "性能可以极致优化"
        ],
        disadvantages: [
          "破坏React的数据流",
          "难以维护和调试",
          "与React生态系统脱节",
          "容易产生内存泄漏"
        ],
        useCases: ["第三方库集成", "极致性能优化", "特殊DOM操作"],
        performance: "极高 - 直接DOM操作",
        complexity: "极高 - 需要深度理解DOM和React"
      }
    ],
    decisionMatrix: {
      description: "根据不同场景选择最适合的ref解决方案",
      scenarios: [
        {
          scenario: "UI组件库开发",
          recommended: "React.forwardRef",
          reason: "标准方案，类型安全，易于维护和扩展"
        },
        {
          scenario: "复杂第三方组件包装",
          recommended: "forwardRef + useImperativeHandle",
          reason: "既保持ref传递，又能精确控制暴露的API"
        },
        {
          scenario: "简单组件ref传递",
          recommended: "React.forwardRef",
          reason: "轻量级解决方案，代码简洁明了"
        },
        {
          scenario: "遗留类组件维护",
          recommended: "类组件 + createRef",
          reason: "保持现有架构，避免大规模重构"
        },
        {
          scenario: "动态ref管理",
          recommended: "Callback Ref",
          reason: "提供最大的灵活性处理复杂的ref逻辑"
        },
        {
          scenario: "高性能图表/游戏组件",
          recommended: "Portal + 直接DOM操作",
          reason: "绕过React渲染，实现极致性能"
        }
      ]
    },
    summary: "React.forwardRef是现代React开发中ref传递的首选方案，特别适合组件库和标准业务场景。对于复杂场景，建议与useImperativeHandle组合使用。选择方案时应优先考虑维护性和团队技术栈的一致性，避免过度工程化。"
  }
};

export default basicInfo;