import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'createRef在使用过程中开发者经常遇到一些典型问题，特别是在ref生命周期管理、DOM访问时机和内存管理方面。本节提供完整的问题诊断和解决方案。',
        sections: [
          {
            title: 'ref.current为null问题',
            description: '最常见的createRef问题，通常由于访问时机不当或组件卸载导致',
            items: [
              {
                title: 'DOM元素未渲染时访问ref',
                description: '在组件挂载前或条件渲染导致元素不存在时访问ref.current',
                solution: '1. 使用可选链操作符(?.)安全访问；2. 在componentDidMount或useEffect中访问；3. 添加存在性检查',
                prevention: '建立ref访问的安全模式，始终验证ref.current的存在性',
                code: `// ❌ 错误：不安全的ref访问
class BadRefAccess extends React.Component {
  inputRef = React.createRef();
  
  componentWillMount() {
    // 错误！此时DOM还未渲染
    this.inputRef.current.focus(); // TypeError: Cannot read properties of null
  }
  
  render() {
    return this.props.visible ? <input ref={this.inputRef} /> : null;
  }
}

// ✅ 正确：安全的ref访问模式
class SafeRefAccess extends React.Component {
  inputRef = React.createRef();
  
  componentDidMount() {
    // 正确的访问时机
    this.focusInput();
  }
  
  focusInput = () => {
    // 方法1：使用可选链
    this.inputRef.current?.focus();
    
    // 方法2：显式检查
    if (this.inputRef.current) {
      this.inputRef.current.focus();
    }
  };
  
  render() {
    return this.props.visible ? <input ref={this.inputRef} /> : null;
  }
}`
              },
              {
                title: '组件卸载后访问ref',
                description: '在异步操作中组件已卸载但仍尝试访问ref，导致警告或错误',
                solution: '1. 使用isMounted标志位；2. 清理异步操作；3. 在组件卸载时重置ref',
                prevention: '在异步操作前检查组件挂载状态，及时清理定时器和异步任务',
                code: `// ✅ 防止组件卸载后访问ref
class AsyncSafeComponent extends React.Component {
  inputRef = React.createRef();
  isMounted = false;
  timeouts = [];
  
  componentDidMount() {
    this.isMounted = true;
  }
  
  componentWillUnmount() {
    this.isMounted = false;
    // 清理所有定时器
    this.timeouts.forEach(timeout => clearTimeout(timeout));
    this.timeouts = [];
  }
  
  handleAsyncOperation = () => {
    const timeout = setTimeout(() => {
      // 检查组件是否仍然挂载
      if (this.isMounted && this.inputRef.current) {
        this.inputRef.current.focus();
      }
    }, 1000);
    
    this.timeouts.push(timeout);
  };
  
  render() {
    return <input ref={this.inputRef} />;
  }
}`
              }
            ]
          },
          {
            title: 'ref更新和重新赋值问题',
            description: '动态ref赋值、ref对象替换和条件渲染中的ref管理问题',
            items: [
              {
                title: 'render中重新创建ref对象',
                description: '在render方法中创建新的ref对象，导致每次渲染都丢失之前的引用',
                solution: '1. 将ref创建移到构造函数或类字段；2. 使用条件逻辑而不是重新创建；3. 考虑使用useRef钩子',
                prevention: '建立ref创建的最佳实践，避免在render中进行对象创建',
                code: `// ❌ 错误：每次render都创建新ref
class BadRefCreation extends React.Component {
  render() {
    // 每次渲染都创建新的ref对象！
    const myRef = React.createRef();
    return <input ref={myRef} />;
  }
}

// ✅ 正确：在类字段中创建ref
class GoodRefCreation extends React.Component {
  myRef = React.createRef();
  
  render() {
    return <input ref={this.myRef} />;
  }
}

// ✅ 使用Hook的解决方案
function HookSolution() {
  const myRef = useRef(null); // 在组件生命周期中保持不变
  return <input ref={myRef} />;
}`
              },
              {
                title: '动态ref数量管理',
                description: '根据数据动态创建不同数量的ref，管理复杂度高',
                solution: '1. 使用Map或对象存储多个ref；2. 实现ref的懒创建；3. 及时清理不需要的ref',
                prevention: '建立动态ref的管理机制，避免内存泄漏和访问错误',
                code: `// ✅ 动态ref管理最佳实践
class DynamicRefsManager extends React.Component {
  itemRefs = new Map();
  
  // 获取或创建ref
  getItemRef = (id) => {
    if (!this.itemRefs.has(id)) {
      this.itemRefs.set(id, React.createRef());
    }
    return this.itemRefs.get(id);
  };
  
  // 清理不需要的ref
  cleanupRefs = (activeIds) => {
    const currentIds = new Set(this.itemRefs.keys());
    currentIds.forEach(id => {
      if (!activeIds.includes(id)) {
        this.itemRefs.delete(id);
      }
    });
  };
  
  componentDidUpdate(prevProps) {
    // 清理不再存在的项目的ref
    if (prevProps.items !== this.props.items) {
      const activeIds = this.props.items.map(item => item.id);
      this.cleanupRefs(activeIds);
    }
  }
  
  render() {
    return (
      <div>
        {this.props.items.map(item => (
          <input 
            key={item.id}
            ref={this.getItemRef(item.id)}
            defaultValue={item.value}
          />
        ))}
      </div>
    );
  }
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '有效的调试工具和技术能帮助开发者快速定位createRef相关问题，提高开发效率。',
        sections: [
          {
            title: 'React DevTools调试',
            description: '使用React DevTools深入分析ref的状态和生命周期',
            items: [
              {
                title: 'Ref状态检查',
                description: '在React DevTools中检查组件的ref属性和当前值',
                solution: '1. 使用Components面板查看ref属性；2. 在Console中访问$r.refs；3. 设置断点检查ref状态',
                prevention: '建立系统性的ref调试流程，定期检查ref状态',
                code: `// React DevTools调试技巧

// 1. 在Console中检查当前选中组件的refs
console.log($r); // 当前选中的组件实例
console.log($r.inputRef); // 检查特定的ref
console.log($r.inputRef.current); // 检查ref当前指向的DOM元素

// 2. 批量检查所有ref状态
function debugAllRefs(component) {
  const instance = component;
  const refs = {};
  
  // 遍历组件实例的所有属性，找出ref对象
  Object.keys(instance).forEach(key => {
    if (instance[key] && typeof instance[key] === 'object' && 
        'current' in instance[key]) {
      refs[key] = {
        refObject: instance[key],
        currentValue: instance[key].current,
        type: instance[key].current?.constructor.name || 'null'
      };
    }
  });
  
  console.table(refs);
  return refs;
}

// 3. ref状态监控器
class RefMonitor {
  static monitor(component, refName, interval = 1000) {
    const monitor = setInterval(() => {
      const ref = component[refName];
      console.log(refName + ' status:', {
        exists: !!ref,
        current: ref?.current,
        type: ref?.current?.constructor.name || 'null',
        timestamp: new Date().toISOString()
      });
    }, interval);
    
    return () => clearInterval(monitor);
  }
}`
              },
              {
                title: 'DOM关系追踪',
                description: '追踪ref指向的DOM元素与React组件树的关系',
                solution: '1. 使用Elements面板查看DOM结构；2. 关联React组件与DOM元素；3. 检查DOM事件监听器',
                prevention: '建立DOM-React映射的调试方法，快速定位问题元素',
                code: `// DOM关系追踪工具
class DOMRefTracker {
  static trackRef(component, refName) {
    const ref = component[refName];
    if (!ref || !ref.current) {
      console.warn('Ref not found or current is null:', refName);
      return;
    }
    
    const element = ref.current;
    
    // 基本信息
    console.group('Ref追踪: ' + refName);
    console.log('DOM元素:', element);
    console.log('元素类型:', element.constructor.name);
    console.log('CSS类名:', element.className);
    console.log('ID:', element.id);
    
    // React Fiber信息（内部API，可能随版本变化）
    const fiberKey = Object.keys(element).find(key => 
      key.startsWith('__reactFiber') || key.startsWith('__reactInternalInstance')
    );
    if (fiberKey) {
      console.log('React Fiber:', element[fiberKey]);
    }
    
    // 事件监听器
    console.log('事件监听器:', getEventListeners(element));
    
    // 样式计算
    console.log('计算样式:', getComputedStyle(element));
    
    console.groupEnd();
  }
  
  // 批量追踪所有ref
  static trackAllRefs(component) {
    Object.keys(component).forEach(key => {
      if (component[key] && 'current' in component[key]) {
        this.trackRef(component, key);
      }
    });
  }
}`
              }
            ]
          },
          {
            title: '自动化测试工具',
            description: '为createRef相关功能编写单元测试和集成测试',
            items: [
              {
                title: 'Jest + React Testing Library',
                description: '使用现代测试工具测试ref的行为和DOM交互',
                solution: '1. 模拟ref行为；2. 测试DOM操作结果；3. 验证ref生命周期',
                prevention: '建立全面的测试覆盖，确保ref功能的稳定性',
                code: `// createRef测试示例
import { render, fireEvent, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// 测试组件
class TestComponent extends React.Component {
  inputRef = React.createRef();
  
  focusInput = () => {
    this.inputRef.current?.focus();
  };
  
  render() {
    return (
      <div>
        <input ref={this.inputRef} data-testid="test-input" />
        <button onClick={this.focusInput} data-testid="focus-btn">
          Focus Input
        </button>
      </div>
    );
  }
}

describe('createRef行为测试', () => {
  test('ref应该正确指向DOM元素', () => {
    let componentRef = React.createRef();
    
    render(<TestComponent ref={componentRef} />);
    
    const input = screen.getByTestId('test-input');
    
    // 验证ref指向正确的DOM元素
    expect(componentRef.current.inputRef.current).toBe(input);
    expect(componentRef.current.inputRef.current).toBeInstanceOf(HTMLInputElement);
  });
  
  test('通过ref调用DOM方法', () => {
    let componentRef = React.createRef();
    
    render(<TestComponent ref={componentRef} />);
    
    const input = screen.getByTestId('test-input');
    const focusBtn = screen.getByTestId('focus-btn');
    
    // 模拟focus方法
    const focusSpy = jest.spyOn(input, 'focus');
    
    // 点击按钮触发focus
    fireEvent.click(focusBtn);
    
    // 验证focus方法被调用
    expect(focusSpy).toHaveBeenCalled();
    expect(input).toHaveFocus();
    
    focusSpy.mockRestore();
  });
  
  test('ref在组件卸载时应该被清理', () => {
    let componentRef = React.createRef();
    
    const { unmount } = render(<TestComponent ref={componentRef} />);
    
    // 卸载前ref应该存在
    expect(componentRef.current.inputRef.current).toBeTruthy();
    
    // 卸载组件
    unmount();
    
    // 验证组件实例仍然存在，但DOM元素已被移除
    expect(componentRef.current).toBeTruthy();
    // 注意：createRef的行为与useRef不同，不会自动清理
  });
});`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;