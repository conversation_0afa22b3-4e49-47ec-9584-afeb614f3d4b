import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  completionStatus: '内容已完成',
  
  introduction: `createRef是React 16.3引入的重要API，标志着React ref系统现代化的关键节点。它的出现解决了字符串ref的诸多问题，为后续的useRef Hook奠定了基础。回顾createRef的发展历程，我们能够看到React团队在API设计上的深思熟虑和对开发体验的持续追求。`,
  
  background: `在createRef出现之前，React开发者主要使用两种方式访问DOM：字符串ref和回调ref。字符串ref虽然简单易用，但存在性能问题和潜在的内存泄漏风险。回调ref功能强大但语法复杂，对新手不够友好。React团队需要一个既安全又易用的ref解决方案，createRef正是在这样的背景下诞生的。`,

  evolution: `createRef的演进体现了React生态的成熟过程：
  
  **字符串ref时代(React 0.13-16.2)**：简单但有缺陷
  - 语法：<input ref="myInput" />
  - 问题：性能损失、内存泄漏、难以调试
  
  **回调ref时代(React 0.14-16.2)**：功能强大但复杂
  - 语法：<input ref={node => this.myInput = node} />
  - 优势：灵活性高、性能好
  - 缺点：语法冗长、容易出错
  
  **createRef时代(React 16.3-至今)**：平衡易用性和性能
  - 语法：this.myRef = React.createRef(); <input ref={this.myRef} />
  - 优势：API简洁、类型安全、性能优良
  
  **Hooks时代(React 16.8-至今)**：函数组件的完美解决方案
  - useRef成为函数组件的首选
  - createRef主要用于类组件和向下兼容`,

  timeline: [
    {
      year: '2017',
      event: '字符串ref弃用警告',
      description: 'React开始对字符串ref发出弃用警告，推动社区寻找更好的替代方案',
      significance: '为createRef的引入铺平了道路，标志着React ref系统重构的开始'
    },
    {
      year: '2018.3',
      event: 'React 16.3发布 - createRef诞生',
      description: 'React团队正式引入createRef API，提供了比字符串ref更安全的DOM访问方式',
      significance: '这是React ref系统发展的重要里程碑，建立了现代ref的基础概念'
    },
    {
      year: '2018.10',
      event: 'React 16.8 - useRef Hook引入',
      description: 'React Hooks发布，useRef为函数组件提供了ref解决方案',
      significance: 'createRef开始主要服务于类组件，useRef成为函数组件的首选'
    },
    {
      year: '2019',
      event: '社区最佳实践确立',
      description: '社区普遍接受"类组件用createRef，函数组件用useRef"的实践模式',
      significance: '明确了createRef在React生态中的定位和适用场景'
    },
    {
      year: '2022',
      event: 'React 18并发特性兼容',
      description: 'createRef与React 18的并发渲染特性完全兼容，保持稳定运行',
      significance: '证明了createRef设计的稳健性，能够适应React架构的重大变革'
    }
  ],

  keyFigures: [
    {
      name: 'Dan Abramov',
      role: 'React核心团队成员',
      contribution: '参与了createRef API的设计讨论和Hooks系统的开发，推动了React ref系统的现代化',
      significance: '他的技术洞察和设计理念深刻影响了React ref系统的发展方向'
    },
    {
      name: 'Sebastian Markbage',
      role: 'React架构师',
      contribution: '作为React的主要架构师，确保了createRef在React 16重构中的稳定性和一致性',
      significance: '他的架构设计保证了createRef与React整体生态的良好集成'
    },
    {
      name: 'Andrew Clark',
      role: 'React核心开发者',
      contribution: '参与了createRef的具体实现工作，确保其性能和可靠性',
      significance: '他的实现工作确保了createRef在各种使用场景下的稳定表现'
    }
  ],

  concepts: [
    {
      term: 'Ref System Evolution',
      definition: 'React ref系统从字符串ref到现代ref的演进过程',
      evolution: '字符串ref → 回调ref → createRef → useRef，每一步都解决了前一代的核心问题',
      modernRelevance: '理解这个演进有助于选择合适的ref方案和理解各自的适用场景'
    },
    {
      term: 'Imperative Escape Hatch',
      definition: 'createRef作为React声明式编程模式中的命令式逃生舱',
      evolution: '从避免命令式操作到提供受控的命令式访问机制',
      modernRelevance: '在现代React中，合理使用ref进行必要的命令式操作仍然重要'
    },
    {
      term: 'Forward Ref Pattern',
      definition: 'createRef与React.forwardRef的配合使用模式',
      evolution: '从简单的DOM ref到支持组件间ref传递的复杂场景',
      modernRelevance: '在组件库开发和高阶组件设计中仍然是重要的模式'
    },
    {
      term: 'Lifecycle Integration',
      definition: 'createRef与类组件生命周期的紧密集成',
      evolution: '建立了在构造函数创建、在componentDidMount使用的标准模式',
      modernRelevance: '为理解useRef在函数组件中的使用提供了重要参考'
    }
  ],

  designPhilosophy: `createRef的设计体现了React团队的几个核心理念：
  
  **简洁性原则**：API应该简单直观，降低学习成本
  **类型安全**：提供良好的TypeScript支持，减少运行时错误
  **性能优先**：避免字符串ref的性能问题，提供高效的DOM访问
  **向下兼容**：平滑迁移路径，不破坏现有代码
  **职责单一**：专注于DOM引用，不承担额外的状态管理责任`,

  impact: `createRef对React生态产生了深远影响：
  
  **技术影响**：建立了现代ref的概念基础，为useRef的设计提供了重要参考
  **开发体验**：显著提升了DOM操作的开发体验，减少了常见错误
  **生态演进**：推动了React向更现代化API的演进，体现了持续改进的理念
  **社区实践**：确立了ref使用的最佳实践，影响了整个React社区的开发模式`,

  modernRelevance: `在现代React开发中，createRef仍然具有重要价值：
  
  **类组件首选**：在类组件中仍然是推荐的ref创建方式
  **向下兼容**：为legacy代码提供稳定的迁移路径
  **教育价值**：帮助理解React ref系统的演进和设计思想
  **特定场景**：在某些需要与类组件配合的场景中仍有用武之地
  **历史意义**：作为React发展史的重要节点，具有重要的参考价值`
};

export default knowledgeArchaeology;