import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  completionStatus: '内容已完成',
  
  definition: "React.createRef是React中用于创建ref对象的API，主要在类组件中使用，用于获取DOM元素或组件实例的直接引用。",
  
  introduction: `React.createRef是React提供的用于创建引用(ref)的API，它返回一个可变的ref对象，其.current属性被初始化为null。createRef主要用于类组件中，为开发者提供了访问DOM元素和组件实例的能力。

在React的发展历程中，createRef代表了引用管理的一个重要演进。它解决了早期字符串ref的诸多问题，提供了类型安全的引用创建方式。虽然在函数组件时代被useRef所取代，但在类组件和某些特定场景中仍然具有重要价值。

理解createRef不仅有助于维护传统代码库，更重要的是能够深入理解React的引用系统设计理念和演进历程。`,

  syntax: `const myRef = React.createRef();

// 类组件中使用
class MyComponent extends React.Component {
  constructor(props) {
    super(props);
    this.myRef = React.createRef();
  }

  render() {
    return <div ref={this.myRef} />;
  }
}`,

  quickExample: `// 基础DOM引用示例
class TextInputWithFocusButton extends React.Component {
  constructor(props) {
    super(props);
    this.textInput = React.createRef();
  }

  focusTextInput = () => {
    // 直接访问DOM元素
    this.textInput.current.focus();
  }

  render() {
    return (
      <div>
        <input
          type="text"
          ref={this.textInput}
          placeholder="点击按钮聚焦"
        />
        <button onClick={this.focusTextInput}>
          聚焦输入框
        </button>
      </div>
    );
  }
}`,

  scenarioDiagram: [
    {
      title: "createRef生命周期",
      description: "展示createRef从创建到使用的完整生命周期",
      diagram: `graph TD
    A[React.createRef调用] --> B[创建ref对象]
    B --> C[ref.current = null]
    C --> D[组件渲染]
    D --> E[ref绑定到元素]
    E --> F[ref.current = DOM元素]
    F --> G[可以访问DOM]
    G --> H[组件卸载]
    H --> I[ref.current = null]

    style A fill:#e3f2fd
    style F fill:#e8f5e8
    style I fill:#ffebee`
    },
    {
      title: "ref传递模式",
      description: "展示ref在组件间的传递和转发机制",
      diagram: `graph LR
    A[父组件] --> B[createRef创建]
    B --> C[传递给子组件]
    C --> D[子组件接收ref]
    D --> E[绑定到DOM元素]
    E --> F[父组件可访问]

    style A fill:#e1f5fe
    style E fill:#f3e5f5
    style F fill:#e8f5e8`
    },
    {
      title: "类组件vs函数组件引用对比",
      description: "对比createRef和useRef的使用场景和差异",
      diagram: `graph TD
    A[引用需求] --> B{组件类型}
    B -->|类组件| C[React.createRef]
    B -->|函数组件| D[useRef]
    C --> E[构造函数中创建]
    D --> F[函数体内创建]
    E --> G[this.myRef.current]
    F --> H[myRef.current]

    style C fill:#fff3e0
    style D fill:#e8f5e8
    style G fill:#f3e5f5
    style H fill:#e3f2fd`
    }
  ],
  
  parameters: [
    {
      name: "无参数",
      type: "void",
      required: false,
      description: "createRef不接受任何参数，返回一个新的ref对象"
    }
  ],
  
  returnValue: {
    type: "RefObject<T>",
    description: "返回一个ref对象，包含current属性，初始值为null。当ref被绑定到元素后，current将指向该DOM元素或组件实例。"
  },
  
  keyFeatures: [
    {
      name: "类型安全",
      description: "提供TypeScript类型支持，相比字符串ref更加安全可靠"
    },
    {
      name: "直接DOM访问",
      description: "允许直接访问和操作DOM元素，实现命令式编程"
    },
    {
      name: "组件实例引用",
      description: "可以获取类组件实例的引用，调用其方法和访问属性"
    },
    {
      name: "引用转发兼容",
      description: "配合React.forwardRef可以实现引用的转发和传递"
    },
    {
      name: "生命周期集成",
      description: "与组件生命周期紧密集成，在挂载时绑定，卸载时清理"
    }
  ],
  
  limitations: [
    {
      limitation: "仅限类组件",
      description: "createRef主要设计用于类组件，在函数组件中使用useRef更合适"
    },
    {
      limitation: "渲染时重新创建",
      description: "如果在render方法中调用createRef，每次渲染都会创建新的ref对象"
    },
    {
      limitation: "命令式编程",
      description: "打破了React的声明式编程模式，需要谨慎使用避免代码难以维护"
    },
    {
      limitation: "无法跨渲染保持状态",
      description: "不像useRef那样可以在函数组件的多次渲染间保持相同的引用"
    }
  ],
  
  bestPractices: [
    {
      practice: "构造函数中创建",
      description: "在类组件的constructor中创建ref，避免在render中重复创建"
    },
    {
      practice: "谨慎使用DOM操作",
      description: "尽量通过props和state控制组件行为，只在必要时使用ref进行DOM操作"
    },
    {
      practice: "清理引用",
      description: "在componentWillUnmount中清理ref相关的事件监听器和定时器"
    },
    {
      practice: "配合forwardRef使用",
      description: "需要传递ref给子组件时，结合React.forwardRef实现引用转发"
    },
    {
      practice: "类型声明",
      description: "在TypeScript中明确声明ref的类型，提高代码的类型安全性"
    }
  ],
  
  warnings: [
    {
      warning: "避免在render中创建",
      description: "不要在render方法中调用createRef，这会导致每次渲染都创建新的ref对象"
    },
    {
      warning: "注意引用时机",
      description: "ref.current在组件挂载前为null，访问前需要检查null值"
    },
    {
      warning: "谨慎修改DOM",
      description: "通过ref直接修改DOM可能与React的状态管理冲突，需要谨慎处理"
    },
    {
      warning: "内存泄漏风险",
      description: "长期持有DOM元素引用可能导致内存泄漏，需要适时清理"
    }
  ]
};

export default basicInfo;