import { ApiItem } from '@/types/api';

const createRefApi: ApiItem = {
  id: 'createRef',
  name: 'React.createRef',
  description: 'React.createRef是React中用于创建ref对象的API，主要在类组件中使用，用于获取DOM元素或组件实例的直接引用。',
  category: 'Legacy APIs',
  syntax: `const myRef = React.createRef();

// 类组件中使用
class MyComponent extends React.Component {
  constructor(props) {
    super(props);
    this.myRef = React.createRef();
  }

  render() {
    return <div ref={this.myRef} />;
  }
}`,
  example: `// 基础DOM引用示例
class TextInputWithFocusButton extends React.Component {
  constructor(props) {
    super(props);
    this.textInput = React.createRef();
  }

  focusTextInput = () => {
    // 直接访问DOM元素
    this.textInput.current.focus();
  }

  render() {
    return (
      <div>
        <input
          type="text"
          ref={this.textInput}
          placeholder="点击按钮聚焦"
        />
        <button onClick={this.focusTextInput}>
          聚焦输入框
        </button>
      </div>
    );
  }
}`,
  notes: '仅限类组件使用，需要在构造函数中创建ref避免性能问题，在组件挂载前ref.current为null',
  version: 'React 16.3.0+',
  tags: ['Legacy API', 'DOM引用', '类组件', 'ref创建'],
  importance: 'medium',
  difficulty: 'easy'
};

export default createRefApi;