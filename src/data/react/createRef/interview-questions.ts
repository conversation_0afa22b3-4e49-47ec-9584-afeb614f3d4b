import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    completionStatus: '内容已完成',
    
    id: 'createref-basic',
    difficulty: 'easy',
    category: 'React基础',
    question: '什么是React.createRef？它解决了什么问题？请写一个简单的使用示例。',
    shortAnswer: 'createRef是React提供的API，用于创建ref对象来访问DOM元素或组件实例，它解决了字符串ref的类型安全问题。',
    detailedAnswer: `React.createRef是React 16.3引入的API，用于创建引用(ref)对象。它主要解决了以下问题：

1. **类型安全**：相比字符串ref，createRef提供了更好的TypeScript支持
2. **性能优化**：避免了字符串ref在每次渲染时的查找开销
3. **调试友好**：提供了更清晰的调试信息

**基本用法示例：**

\`\`\`javascript
class TextInput extends React.Component {
  constructor(props) {
    super(props);
    // 在构造函数中创建ref
    this.textInputRef = React.createRef();
  }

  focusTextInput = () => {
    // 通过.current访问DOM元素
    this.textInputRef.current.focus();
  }

  render() {
    return (
      <div>
        <input 
          type="text" 
          ref={this.textInputRef} 
          placeholder="点击按钮聚焦我"
        />
        <button onClick={this.focusTextInput}>
          聚焦输入框
        </button>
      </div>
    );
  }
}
\`\`\`

**工作原理：**
- createRef()返回一个对象：\`{current: null}\`
- React在DOM挂载时会将元素赋值给\`ref.current\`
- 组件卸载时，\`current\`会被重置为\`null\``,
    codeExample: `// 创建ref
const myRef = React.createRef();

// 使用ref
<input ref={myRef} />

// 访问DOM
myRef.current.focus();`,
    tags: ['React基础', 'DOM操作', 'ref引用'],
    relatedQuestions: [
      'useRef与createRef有什么区别？',
      '什么时候应该使用ref？',
      '如何在函数组件中使用ref？'
    ],
    difficulty_justification: '这是createRef的基础概念题，主要考查候选人对React ref系统的基本理解。'
  },
  {
    completionStatus: '内容已完成',
    
    id: 'createref-vs-useref',
    difficulty: 'medium',
    category: 'React进阶',
    question: 'createRef和useRef有什么区别？在什么场景下应该选择使用createRef？请分析它们的性能差异。',
    shortAnswer: 'createRef主要用于类组件，每次调用都创建新对象；useRef用于函数组件，在组件生命周期内保持同一引用。createRef适合类组件和需要引用转发的场景。',
    detailedAnswer: `createRef和useRef的主要区别：

## 使用场景差异

**createRef：**
- 主要用于类组件
- 每次调用都创建新的ref对象
- 适合在构造函数中使用
- 配合forwardRef进行引用转发

**useRef：**
- 只能在函数组件中使用
- 在组件整个生命周期内返回同一个ref对象
- 除了DOM引用，还可以保存任何可变值
- 不会触发重新渲染

## 性能分析

\`\`\`javascript
// createRef - 类组件模式
class ClassComponent extends React.Component {
  constructor(props) {
    super(props);
    this.inputRef = React.createRef(); // 只创建一次
  }
  
  // ❌ 错误用法 - 每次渲染都创建新ref
  render() {
    const badRef = React.createRef(); // 性能问题
    return <input ref={this.inputRef} />;
  }
}

// useRef - 函数组件模式
function FunctionComponent() {
  const inputRef = useRef(null); // 始终返回同一个对象
  
  useEffect(() => {
    // inputRef.current 在整个生命周期内保持稳定
    console.log(inputRef.current);
  }, [inputRef]); // 依赖不会改变
  
  return <input ref={inputRef} />;
}
\`\`\`

## 引用转发场景

createRef在引用转发中的应用：

\`\`\`javascript
// 高阶组件中使用createRef
function withLogging(WrappedComponent) {
  class LoggingComponent extends React.Component {
    constructor(props) {
      super(props);
      this.wrappedRef = React.createRef();
    }
    
    componentDidMount() {
      console.log('Wrapped component:', this.wrappedRef.current);
    }
    
    render() {
      return <WrappedComponent ref={this.wrappedRef} {...this.props} />;
    }
  }
  
  return React.forwardRef((props, ref) => (
    <LoggingComponent {...props} ref={ref} />
  ));
}
\`\`\`

## 选择原则

1. **类组件场景**：必须使用createRef
2. **函数组件场景**：推荐使用useRef
3. **引用转发**：createRef更直观
4. **性能敏感**：useRef更优（函数组件中）
5. **Legacy代码**：保持createRef以减少重构风险`,
    codeExample: `// createRef vs useRef 对比

// 类组件 - createRef
class ClassComponent extends React.Component {
  constructor(props) {
    super(props);
    this.ref = React.createRef();
  }
  render() {
    return <input ref={this.ref} />;
  }
}

// 函数组件 - useRef  
function FunctionComponent() {
  const ref = useRef(null);
  return <input ref={ref} />;
}`,
    tags: ['React进阶', 'Hook对比', '性能优化', 'API选择'],
    relatedQuestions: [
      'forwardRef的工作原理是什么？',
      '什么时候需要使用引用转发？',
      'ref的性能考虑有哪些？'
    ],
    difficulty_justification: '考查候选人对React不同API的深入理解，需要分析使用场景和性能影响。'
  },
  {
    completionStatus: '内容已完成',
    
    id: 'createref-advanced',
    difficulty: 'hard',
    category: 'React架构',
    question: '请分析createRef在React内部的实现原理，以及在Fiber架构中ref是如何被处理的？如何实现一个高性能的动态ref管理系统？',
    shortAnswer: 'createRef返回{current: null}对象，React在commit阶段通过attachRef/detachRef处理ref绑定。在Fiber中，ref被标记为副作用，在commitRoot时统一处理。',
    detailedAnswer: `## createRef内部实现原理

\`\`\`javascript
// React内部createRef实现（简化版）
function createRef() {
  const refObject = {
    current: null
  };
  
  if (__DEV__) {
    // 开发环境下的额外保护
    Object.seal(refObject);
  }
  
  return refObject;
}
\`\`\`

## Fiber架构中的ref处理

### 1. ref标记阶段（Reconciliation）
\`\`\`javascript
// 在beginWork阶段标记ref
function markRef(current, workInProgress) {
  const ref = workInProgress.ref;
  if (
    (current === null && ref !== null) ||
    (current !== null && current.ref !== ref)
  ) {
    // 标记需要处理ref的副作用
    workInProgress.flags |= Ref;
  }
}
\`\`\`

### 2. ref处理阶段（Commit）
\`\`\`javascript
// 在commit阶段处理ref
function commitAttachRef(finishedWork) {
  const ref = finishedWork.ref;
  if (ref !== null) {
    const instance = finishedWork.stateNode;
    
    // 根据组件类型获取正确的实例
    let instanceToUse;
    switch (finishedWork.tag) {
      case HostComponent: // DOM元素
        instanceToUse = getPublicInstance(instance);
        break;
      case ClassComponent: // 类组件
        instanceToUse = instance;
        break;
      default:
        instanceToUse = instance;
    }
    
    // 执行ref绑定
    if (typeof ref === 'function') {
      ref(instanceToUse);
    } else {
      ref.current = instanceToUse;
    }
  }
}

function commitDetachRef(current) {
  const currentRef = current.ref;
  if (currentRef !== null) {
    if (typeof currentRef === 'function') {
      currentRef(null);
    } else {
      currentRef.current = null;
    }
  }
}
\`\`\`

## 高性能动态ref管理系统设计

### 1. 基础架构
\`\`\`javascript
class DynamicRefManager {
  constructor() {
    this.refs = new Map();
    this.pendingRefs = new Set();
    this.cleanupTasks = new Set();
  }
  
  // 创建或获取ref
  getRef(key) {
    if (!this.refs.has(key)) {
      const ref = React.createRef();
      this.refs.set(key, ref);
      
      // 标记为待处理
      this.pendingRefs.add(key);
    }
    
    return this.refs.get(key);
  }
  
  // 批量清理不再使用的ref
  cleanup(activeKeys) {
    const keysToDelete = [];
    
    this.refs.forEach((ref, key) => {
      if (!activeKeys.has(key)) {
        // 清理DOM引用
        if (ref.current) {
          this.cleanupRef(ref.current);
        }
        keysToDelete.push(key);
      }
    });
    
    // 批量删除
    keysToDelete.forEach(key => {
      this.refs.delete(key);
      this.pendingRefs.delete(key);
    });
  }
  
  // 清理单个ref的资源
  cleanupRef(element) {
    // 移除事件监听器
    const listeners = element._listeners || [];
    listeners.forEach(({ event, handler }) => {
      element.removeEventListener(event, handler);
    });
    
    // 清理定时器
    if (element._timers) {
      element._timers.forEach(clearTimeout);
    }
    
    // 清理观察器
    if (element._observers) {
      element._observers.forEach(observer => observer.disconnect());
    }
  }
}
\`\`\`

### 2. 性能优化策略
\`\`\`javascript
class OptimizedRefManager extends DynamicRefManager {
  constructor() {
    super();
    this.rafId = null;
    this.batchedOperations = [];
  }
  
  // 批量处理ref操作
  batchProcess(operation) {
    this.batchedOperations.push(operation);
    
    if (this.rafId === null) {
      this.rafId = requestAnimationFrame(() => {
        this.flushOperations();
        this.rafId = null;
      });
    }
  }
  
  flushOperations() {
    const operations = this.batchedOperations.splice(0);
    
    // 按类型分组操作
    const groups = {
      create: [],
      update: [],
      delete: []
    };
    
    operations.forEach(op => {
      groups[op.type].push(op);
    });
    
    // 按优先级执行
    groups.create.forEach(op => op.execute());
    groups.update.forEach(op => op.execute());
    groups.delete.forEach(op => op.execute());
  }
  
  // 内存监控
  getMemoryUsage() {
    return {
      totalRefs: this.refs.size,
      pendingRefs: this.pendingRefs.size,
      memoryEstimate: this.refs.size * 64 // 估算字节
    };
  }
}
\`\`\`

### 3. 实际应用示例
\`\`\`javascript
class VirtualizedList extends React.Component {
  constructor(props) {
    super(props);
    this.refManager = new OptimizedRefManager();
    this.visibleItems = new Set();
  }
  
  componentDidUpdate() {
    // 清理不可见项的ref
    this.refManager.cleanup(this.visibleItems);
  }
  
  componentWillUnmount() {
    // 清理所有ref
    this.refManager.cleanup(new Set());
  }
  
  renderItem = (item, index) => {
    const key = 'item-' + item.id;
    const ref = this.refManager.getRef(key);
    
    // 标记为可见
    this.visibleItems.add(key);
    
    return (
      <div
        key={item.id}
        ref={ref}
        onIntersectionChange={(isVisible) => {
          if (!isVisible) {
            this.visibleItems.delete(key);
          }
        }}
      >
        {item.content}
      </div>
    );
  }
  
  render() {
    return (
      <div>
        {this.props.items.map(this.renderItem)}
      </div>
    );
  }
}
\`\`\`

## 关键优化点

1. **内存管理**：及时清理不再使用的ref
2. **批量处理**：使用RAF批量处理ref操作
3. **懒加载**：按需创建ref对象
4. **缓存策略**：复用ref对象减少GC压力
5. **监控系统**：实时监控ref使用情况`,
    codeExample: `// 高性能动态ref管理器
class RefManager {
  constructor() {
    this.refs = new Map();
  }
  
  getRef(key) {
    if (!this.refs.has(key)) {
      this.refs.set(key, React.createRef());
    }
    return this.refs.get(key);
  }
  
  cleanup(activeKeys) {
    for (const [key, ref] of this.refs) {
      if (!activeKeys.has(key)) {
        if (ref.current) {
          // 清理资源
        }
        this.refs.delete(key);
      }
    }
  }
}`,
    tags: ['React架构', 'Fiber原理', '性能优化', '内存管理'],
    relatedQuestions: [
      'React的Fiber架构是如何工作的？',
      '如何进行React应用的内存优化？',
      'React的commit阶段都做了什么？'
    ],
    difficulty_justification: '需要深入理解React内部机制和Fiber架构，以及设计高性能系统的能力。'
  }
];

// React.createRef - 从基础使用到架构原理的全方位面试准备
export default interviewQuestions;