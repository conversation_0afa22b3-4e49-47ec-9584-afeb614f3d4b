import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    completionStatus: '内容已完成',
    
    id: 'createref-null-issue',
    question: '为什么我的ref.current总是null？如何正确处理ref的null值？',
    shortAnswer: 'ref.current为null通常是因为在组件挂载前访问ref，或者ref没有正确绑定到DOM元素。需要检查访问时机和ref绑定。',
    detailedAnswer: `这是使用createRef时最常见的问题之一。ref.current为null的几种原因和解决方案：

## 常见原因分析

### 1. 在组件挂载前访问ref
\`\`\`javascript
// ❌ 错误示例
class ProblematicComponent extends React.Component {
  constructor(props) {
    super(props);
    this.myRef = React.createRef();
    
    // 构造函数中ref.current总是null
    console.log(this.myRef.current); // null
  }
  
  render() {
    // render阶段ref.current也是null
    console.log(this.myRef.current); // null
    
    return <input ref={this.myRef} />;
  }
}

// ✅ 正确做法
class CorrectComponent extends React.Component {
  constructor(props) {
    super(props);
    this.myRef = React.createRef();
  }
  
  componentDidMount() {
    // 在componentDidMount中ref.current已经可用
    console.log(this.myRef.current); // <input>
    this.myRef.current.focus();
  }
  
  render() {
    return <input ref={this.myRef} />;
  }
}
\`\`\`

### 2. 条件渲染导致ref未绑定
\`\`\`javascript
// ❌ 问题代码
class ConditionalRender extends React.Component {
  constructor(props) {
    super(props);
    this.inputRef = React.createRef();
  }
  
  componentDidMount() {
    // 如果showInput为false，这里会报错
    this.inputRef.current.focus(); // Cannot read property 'focus' of null
  }
  
  render() {
    return (
      <div>
        {this.props.showInput && (
          <input ref={this.inputRef} />
        )}
      </div>
    );
  }
}

// ✅ 安全处理
class SafeConditionalRender extends React.Component {
  constructor(props) {
    super(props);
    this.inputRef = React.createRef();
  }
  
  componentDidMount() {
    // 安全检查ref是否存在
    if (this.inputRef.current) {
      this.inputRef.current.focus();
    }
  }
  
  componentDidUpdate(prevProps) {
    // 在更新后也要检查
    if (!prevProps.showInput && this.props.showInput) {
      if (this.inputRef.current) {
        this.inputRef.current.focus();
      }
    }
  }
  
  render() {
    return (
      <div>
        {this.props.showInput && (
          <input ref={this.inputRef} />
        )}
      </div>
    );
  }
}
\`\`\`

### 3. ref绑定到函数组件
\`\`\`javascript
// ❌ 错误示例
const FunctionComponent = () => <div>I'm a function component</div>;

class Parent extends React.Component {
  constructor(props) {
    super(props);
    this.funcRef = React.createRef();
  }
  
  componentDidMount() {
    // 函数组件没有实例，所以ref.current为null
    console.log(this.funcRef.current); // null
  }
  
  render() {
    return <FunctionComponent ref={this.funcRef} />; // 警告
  }
}

// ✅ 正确做法 - 使用forwardRef
const ForwardedComponent = React.forwardRef((props, ref) => (
  <div ref={ref}>I'm a forwarded function component</div>
));

class CorrectParent extends React.Component {
  constructor(props) {
    super(props);
    this.funcRef = React.createRef();
  }
  
  componentDidMount() {
    // 现在可以正确访问DOM元素
    console.log(this.funcRef.current); // <div>
  }
  
  render() {
    return <ForwardedComponent ref={this.funcRef} />;
  }
}
\`\`\`

## 最佳实践模式

### 1. 防御性编程
\`\`\`javascript
class DefensiveComponent extends React.Component {
  constructor(props) {
    super(props);
    this.elementRef = React.createRef();
  }
  
  // 创建安全的ref访问方法
  safelyAccessRef = (callback) => {
    if (this.elementRef.current) {
      return callback(this.elementRef.current);
    }
    console.warn('Ref is not available yet');
    return null;
  }
  
  handleClick = () => {
    this.safelyAccessRef(element => {
      element.scrollIntoView({ behavior: 'smooth' });
    });
  }
  
  componentDidMount() {
    this.safelyAccessRef(element => {
      element.focus();
    });
  }
  
  render() {
    return (
      <div>
        <input ref={this.elementRef} />
        <button onClick={this.handleClick}>滚动到输入框</button>
      </div>
    );
  }
}
\`\`\`

### 2. 使用自定义Hook进行封装（如果混用函数组件）
\`\`\`javascript
// 创建一个安全的ref Hook
function useSafeRef(initialValue = null) {
  const ref = useRef(initialValue);
  
  const safeAccess = useCallback((callback) => {
    if (ref.current) {
      return callback(ref.current);
    }
    return null;
  }, []);
  
  return [ref, safeAccess];
}

// 在函数组件中使用
function SafeFunctionComponent() {
  const [inputRef, safeAccess] = useSafeRef();
  
  const handleFocus = () => {
    safeAccess(element => element.focus());
  };
  
  return (
    <div>
      <input ref={inputRef} />
      <button onClick={handleFocus}>聚焦</button>
    </div>
  );
}
\`\`\`

### 3. 错误边界处理
\`\`\`javascript
class RefErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true };
  }
  
  componentDidCatch(error, errorInfo) {
    if (error.message.includes('Cannot read property') && 
        error.message.includes('of null')) {
      console.error('Ref access error:', error, errorInfo);
    }
  }
  
  render() {
    if (this.state.hasError) {
      return <div>Ref相关错误，请检查组件状态</div>;
    }
    
    return this.props.children;
  }
}
\`\`\`

## 调试技巧

### 1. 添加ref状态监控
\`\`\`javascript
class DebugRef extends React.Component {
  constructor(props) {
    super(props);
    this.debugRef = React.createRef();
    this.state = { refStatus: 'not-mounted' };
  }
  
  componentDidMount() {
    this.setState({ 
      refStatus: this.debugRef.current ? 'mounted' : 'null'
    });
  }
  
  componentDidUpdate() {
    const newStatus = this.debugRef.current ? 'available' : 'null';
    if (this.state.refStatus !== newStatus) {
      this.setState({ refStatus: newStatus });
    }
  }
  
  render() {
    return (
      <div>
        <div>Ref状态: {this.state.refStatus}</div>
        {this.props.showElement && (
          <input ref={this.debugRef} />
        )}
      </div>
    );
  }
}
\`\`\`

### 2. ref变化监听器
\`\`\`javascript
class RefChangeMonitor extends React.Component {
  constructor(props) {
    super(props);
    this.monitoredRef = React.createRef();
    this.refObserver = null;
  }
  
  componentDidMount() {
    // 监控ref变化
    this.refObserver = setInterval(() => {
      const current = this.monitoredRef.current;
      if (current !== this.lastRefValue) {
        console.log('Ref changed:', this.lastRefValue, '->', current);
        this.lastRefValue = current;
      }
    }, 100);
  }
  
  componentWillUnmount() {
    if (this.refObserver) {
      clearInterval(this.refObserver);
    }
  }
  
  render() {
    return <input ref={this.monitoredRef} />;
  }
}
\`\`\``,
    tags: ['错误处理', 'ref调试', '空值检查', '生命周期'],
    difficulty: 'medium',
    category: '错误排查'
  },
  {
    completionStatus: '内容已完成',
    
    id: 'createref-render-issue',
    question: '我在render方法中使用createRef，为什么会有性能问题？如何避免？',
    shortAnswer: '在render中调用createRef会导致每次渲染都创建新的ref对象，破坏引用稳定性，应该在构造函数中创建ref。',
    detailedAnswer: `在render方法中使用createRef是一个常见的性能陷阱，会导致多个严重问题：

## 问题分析

### 1. 引用不稳定导致的性能问题
\`\`\`javascript
// ❌ 严重的性能问题
class BadPerformanceComponent extends React.Component {
  render() {
    // 每次渲染都创建新的ref对象
    const newRef = React.createRef();
    
    return (
      <ExpensiveComponent 
        ref={newRef}
        onUpdate={() => {
          // 这个回调也会每次重新创建
          if (newRef.current) {
            newRef.current.update();
          }
        }}
      />
    );
  }
}

// 结果：ExpensiveComponent每次都会重新挂载！
\`\`\`

### 2. useEffect依赖数组的问题
\`\`\`javascript
// ❌ 会导致无限循环
function ProblemComponent() {
  const [count, setCount] = useState(0);
  
  const render = () => {
    const badRef = React.createRef(); // 每次都是新对象
    
    useEffect(() => {
      console.log('Effect triggered');
      // 一些副作用操作
    }, [badRef]); // badRef每次都变化，导致effect重复执行
    
    return <div ref={badRef}>{count}</div>;
  };
  
  return render();
}
\`\`\`

### 3. 内存泄漏风险
\`\`\`javascript
// ❌ 内存泄漏风险
class MemoryLeakRisk extends React.Component {
  componentDidMount() {
    this.timer = setInterval(() => {
      this.forceUpdate(); // 强制重新渲染
    }, 1000);
  }
  
  componentWillUnmount() {
    clearInterval(this.timer);
  }
  
  render() {
    // 每秒创建新的ref对象，旧的无法被GC
    const leakyRef = React.createRef();
    
    // 这里绑定了事件监听器，但ref变化后无法清理
    if (leakyRef.current) {
      leakyRef.current.addEventListener('click', this.handleClick);
    }
    
    return <div ref={leakyRef}>点击我</div>;
  }
}
\`\`\`

## 正确的解决方案

### 1. 在构造函数中创建ref
\`\`\`javascript
// ✅ 正确的模式
class CorrectComponent extends React.Component {
  constructor(props) {
    super(props);
    // 在构造函数中创建，整个生命周期保持不变
    this.stableRef = React.createRef();
    this.expensiveChildRef = React.createRef();
  }
  
  // 创建稳定的回调函数
  handleUpdate = () => {
    if (this.expensiveChildRef.current) {
      this.expensiveChildRef.current.update();
    }
  }
  
  render() {
    return (
      <div ref={this.stableRef}>
        <ExpensiveComponent 
          ref={this.expensiveChildRef}
          onUpdate={this.handleUpdate} // 稳定的引用
        />
      </div>
    );
  }
}
\`\`\`

### 2. 动态ref的正确管理
\`\`\`javascript
// ✅ 正确处理动态ref的模式
class DynamicRefManager extends React.Component {
  constructor(props) {
    super(props);
    this.itemRefs = new Map();
  }
  
  // 获取或创建ref
  getItemRef = (itemId) => {
    if (!this.itemRefs.has(itemId)) {
      this.itemRefs.set(itemId, React.createRef());
    }
    return this.itemRefs.get(itemId);
  }
  
  // 清理不再使用的ref
  cleanupRefs = (activeItemIds) => {
    const currentIds = new Set(this.itemRefs.keys());
    const activeIds = new Set(activeItemIds);
    
    // 找出需要删除的ref
    const toDelete = [...currentIds].filter(id => !activeIds.has(id));
    
    toDelete.forEach(id => {
      const ref = this.itemRefs.get(id);
      // 清理可能的事件监听器
      if (ref.current) {
        ref.current.removeEventListener('click', this.handleItemClick);
      }
      this.itemRefs.delete(id);
    });
  }
  
  componentDidUpdate() {
    // 在更新后清理不再需要的ref
    const activeIds = this.props.items.map(item => item.id);
    this.cleanupRefs(activeIds);
  }
  
  componentWillUnmount() {
    // 组件卸载时清理所有ref
    this.cleanupRefs([]);
  }
  
  render() {
    return (
      <div>
        {this.props.items.map(item => (
          <div 
            key={item.id}
            ref={this.getItemRef(item.id)} // 复用现有ref或创建新ref
          >
            {item.content}
          </div>
        ))}
      </div>
    );
  }
}
\`\`\`

### 3. 性能监控和优化
\`\`\`javascript
// ✅ 带性能监控的ref管理
class PerformanceOptimizedComponent extends React.Component {
  constructor(props) {
    super(props);
    this.containerRef = React.createRef();
    this.renderCount = 0;
    this.refCreationCount = 0;
  }
  
  componentDidMount() {
    // 监控性能指标
    this.performanceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      console.log('Performance entries:', entries);
    });
    
    if (typeof PerformanceObserver !== 'undefined') {
      this.performanceObserver.observe({ entryTypes: ['measure'] });
    }
  }
  
  componentWillUnmount() {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
  }
  
  render() {
    this.renderCount++;
    
    // 性能标记
    performance.mark('render-start');
    
    const result = (
      <div ref={this.containerRef}>
        <div>渲染次数: {this.renderCount}</div>
        <div>Ref创建次数: {this.refCreationCount}</div>
      </div>
    );
    
    performance.mark('render-end');
    performance.measure('render-time', 'render-start', 'render-end');
    
    return result;
  }
}
\`\`\`

## 最佳实践总结

### 1. Ref创建时机
- ✅ 构造函数中创建：性能最优，引用稳定
- ✅ 类字段声明：现代语法，同样稳定
- ❌ render方法中创建：性能差，引用不稳定
- ❌ 组件方法中创建：可能导致重复创建

### 2. 动态ref管理策略
\`\`\`javascript
// ✅ 推荐的动态ref管理模式
class OptimalDynamicRefs extends React.Component {
  constructor(props) {
    super(props);
    this.refsCache = new Map();
    this.lastActiveSet = new Set();
  }
  
  getRef = (key) => {
    if (!this.refsCache.has(key)) {
      this.refsCache.set(key, React.createRef());
    }
    return this.refsCache.get(key);
  }
  
  // 批量清理优化
  cleanupUnusedRefs = () => {
    const currentActiveSet = new Set(this.props.items.map(item => item.id));
    
    // 只有当活跃集合真正变化时才清理
    if (!this.setsEqual(currentActiveSet, this.lastActiveSet)) {
      this.refsCache.forEach((ref, key) => {
        if (!currentActiveSet.has(key)) {
          this.refsCache.delete(key);
        }
      });
      
      this.lastActiveSet = currentActiveSet;
    }
  }
  
  setsEqual = (set1, set2) => {
    if (set1.size !== set2.size) return false;
    for (let item of set1) {
      if (!set2.has(item)) return false;
    }
    return true;
  }
  
  render() {
    this.cleanupUnusedRefs();
    
    return (
      <div>
        {this.props.items.map(item => (
          <div key={item.id} ref={this.getRef(item.id)}>
            {item.content}
          </div>
        ))}
      </div>
    );
  }
}
\`\`\``,
    tags: ['性能优化', 'render优化', '内存管理', '最佳实践'],
    difficulty: 'medium',
    category: '性能问题'
  },
  {
    completionStatus: '内容已完成',
    
    id: 'createref-vs-useref-choice',
    question: '我应该什么时候选择createRef而不是useRef？有什么具体的判断标准？',
    shortAnswer: '主要根据组件类型选择：类组件必须用createRef，函数组件推荐useRef。特殊情况如引用转发、Legacy代码维护可以考虑createRef。',
    detailedAnswer: `选择createRef还是useRef不仅仅是语法差异，而是涉及到架构设计、性能考虑和团队协作的重要决策：

## 核心判断标准

### 1. 组件类型决定（硬性约束）
\`\`\`javascript
// 类组件 - 必须使用createRef
class ClassComponent extends React.Component {
  constructor(props) {
    super(props);
    this.myRef = React.createRef(); // 唯一选择
  }
  
  render() {
    return <input ref={this.myRef} />;
  }
}

// 函数组件 - 推荐使用useRef
function FunctionComponent() {
  const myRef = useRef(null); // 推荐选择
  return <input ref={myRef} />;
}
\`\`\`

### 2. 项目架构考虑
\`\`\`javascript
// 混合架构项目 - 保持一致性
class MixedArchProject {
  // 场景1：Legacy系统逐步迁移
  // 策略：保持createRef以减少重构风险
  
  // 场景2：新功能开发
  // 策略：优先选择函数组件+useRef
  
  // 场景3：高阶组件和工具函数
  // 策略：createRef更适合HOC模式
}

// HOC场景 - createRef更直观
function withScrollPosition(WrappedComponent) {
  return class extends React.Component {
    constructor(props) {
      super(props);
      this.wrappedRef = React.createRef(); // HOC模式下更清晰
    }
    
    componentDidMount() {
      if (this.wrappedRef.current) {
        this.trackScrollPosition();
      }
    }
    
    render() {
      return <WrappedComponent ref={this.wrappedRef} {...this.props} />;
    }
  };
}
\`\`\`

### 3. 性能和内存考虑
\`\`\`javascript
// useRef的性能优势（函数组件中）
function PerformanceComparison() {
  const optimizedRef = useRef(null); // 整个生命周期单一实例
  
  useEffect(() => {
    // optimizedRef在依赖数组中是稳定的
    console.log('Effect only runs when needed');
  }, [optimizedRef]); // 依赖不会变化
  
  return <div ref={optimizedRef} />;
}

// createRef在类组件中的性能特点
class ClassPerformance extends React.Component {
  constructor(props) {
    super(props);
    this.stableRef = React.createRef(); // 构造时创建，性能良好
  }
  
  shouldComponentUpdate(nextProps) {
    // ref不影响shallow compare
    return nextProps.data !== this.props.data;
  }
  
  render() {
    return <div ref={this.stableRef} />;
  }
}
\`\`\`

## 具体场景选择指南

### 1. 引用转发场景
\`\`\`javascript
// createRef在forwardRef中的优势
const EnhancedInput = React.forwardRef((props, ref) => {
  // 内部也需要ref时，createRef更清晰
  const internalRef = React.createRef();
  
  useImperativeHandle(ref, () => ({
    focus: () => internalRef.current?.focus(),
    blur: () => internalRef.current?.blur(),
    getValue: () => internalRef.current?.value
  }));
  
  return <input ref={internalRef} {...props} />;
});

// 父组件使用
class Parent extends React.Component {
  constructor(props) {
    super(props);
    this.inputRef = React.createRef(); // 与forwardRef配合很自然
  }
  
  handleSubmit = () => {
    const value = this.inputRef.current.getValue();
    console.log('Input value:', value);
  }
  
  render() {
    return (
      <form onSubmit={this.handleSubmit}>
        <EnhancedInput ref={this.inputRef} />
        <button type="submit">提交</button>
      </form>
    );
  }
}
\`\`\`

### 2. 第三方库集成
\`\`\`javascript
// createRef在第三方库集成中的应用
class ChartComponent extends React.Component {
  constructor(props) {
    super(props);
    this.chartContainer = React.createRef();
    this.chart = null;
  }
  
  componentDidMount() {
    // 第三方库通常在类组件中更容易管理
    this.chart = new ExternalChartLibrary(this.chartContainer.current);
    this.chart.setData(this.props.data);
  }
  
  componentDidUpdate(prevProps) {
    if (prevProps.data !== this.props.data) {
      this.chart.updateData(this.props.data);
    }
  }
  
  componentWillUnmount() {
    // 清理第三方库资源
    if (this.chart) {
      this.chart.destroy();
    }
  }
  
  render() {
    return <div ref={this.chartContainer} className="chart-container" />;
  }
}

// 对比：函数组件+useRef的方式
function FunctionChartComponent({ data }) {
  const chartContainer = useRef(null);
  const chartInstance = useRef(null);
  
  useEffect(() => {
    chartInstance.current = new ExternalChartLibrary(chartContainer.current);
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, []);
  
  useEffect(() => {
    if (chartInstance.current) {
      chartInstance.current.updateData(data);
    }
  }, [data]);
  
  return <div ref={chartContainer} className="chart-container" />;
}
\`\`\`

### 3. 测试和调试场景
\`\`\`javascript
// createRef在测试中的便利性
class TestableComponent extends React.Component {
  constructor(props) {
    super(props);
    this.testableRef = React.createRef();
  }
  
  // 提供测试专用方法
  getTestHelpers() {
    return {
      getElement: () => this.testableRef.current,
      triggerEvent: (eventType) => {
        const element = this.testableRef.current;
        if (element) {
          element.dispatchEvent(new Event(eventType));
        }
      }
    };
  }
  
  render() {
    return <button ref={this.testableRef} onClick={this.props.onClick} />;
  }
}

// 测试代码
test('TestableComponent interactions', () => {
  const component = mount(<TestableComponent />);
  const helpers = component.instance().getTestHelpers();
  
  expect(helpers.getElement()).toBeInTheDocument();
  helpers.triggerEvent('click');
  // 验证交互结果
});
\`\`\`

## 决策流程图

\`\`\`
开始
  ↓
是类组件吗？
  ↓ 是
使用createRef
  ↓
结束

  ↓ 否
是函数组件吗？
  ↓ 是
需要引用转发吗？
  ↓ 是
考虑createRef（配合forwardRef）
  ↓
  ↓ 否
使用useRef
  ↓
结束
\`\`\`

## 团队协作建议

### 1. 制定项目规范
\`\`\`javascript
// 项目ref使用规范示例
const PROJECT_REF_GUIDELINES = {
  // 类组件
  classComponents: {
    required: 'createRef',
    location: 'constructor',
    naming: 'camelCase + Ref suffix'
  },
  
  // 函数组件
  functionComponents: {
    preferred: 'useRef',
    alternatives: 'createRef (特殊场景)',
    naming: 'camelCase + Ref suffix'
  },
  
  // 引用转发
  forwardRef: {
    internal: 'createRef or useRef',
    external: 'createRef (推荐)'
  }
};
\`\`\`

### 2. 代码审查检查点
- ✅ ref是否在正确的位置创建？
- ✅ 是否有内存泄漏风险？
- ✅ 是否正确处理了null值？
- ✅ 是否与组件类型匹配？
- ✅ 是否影响了组件性能？`,
    tags: ['API选择', '架构设计', '团队协作', '最佳实践'],
    difficulty: 'medium',
    category: 'API选择'
  }
];

// React.createRef - 常见开发问题的实用解决方案
export default commonQuestions;