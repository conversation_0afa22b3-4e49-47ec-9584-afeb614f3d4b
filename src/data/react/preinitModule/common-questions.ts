import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-1',
    question: 'preinitModule什么时候调用最合适？',
    answer: `preinitModule的调用时机非常重要，需要在模块实际使用之前的合适时机调用：

**最佳调用时机：**
1. **应用启动时**：在应用初始化阶段预加载核心模块
2. **路由变化时**：在路由切换前预加载目标页面的模块
3. **用户行为预测**：根据用户行为模式预测性加载
4. **条件满足时**：当特定条件满足时立即预加载

**避免的时机：**
- 不要在模块即将使用时才调用（太晚了）
- 不要在应用启动时预加载所有可能的模块（浪费资源）
- 不要在每次渲染时重复调用（应该有条件控制）`,
    code: `// ✅ 好的做法：应用启动时预加载核心模块
function App() {
  useEffect(() => {
    // 预加载用户可能需要的核心功能
    preinitModule('/modules/user-profile.js', {
      as: 'script',
      crossOrigin: 'anonymous'
    });
  }, []);
}

// ✅ 好的做法：路由变化时预加载
function Router() {
  const location = useLocation();

  useEffect(() => {
    if (location.pathname === '/dashboard') {
      preinitModule('/modules/dashboard-widgets.js', {
        as: 'script',
        crossOrigin: 'anonymous'
      });
    }
  }, [location]);
}

// ❌ 不好的做法：使用时才预加载
const handleClick = async () => {
  preinitModule('/modules/feature.js'); // 太晚了
  const module = await import('/modules/feature.js');
};`,
    tags: ['最佳实践', '性能优化'],
    relatedQuestions: ['如何避免过度预加载？', '预加载的模块什么时候会被清理？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-2',
    question: 'preinitModule会影响首屏加载性能吗？',
    answer: `preinitModule对首屏加载性能的影响取决于使用方式：

**正面影响：**
1. **减少后续延迟**：虽然首屏可能稍慢，但后续功能响应更快
2. **并行加载**：可以与首屏内容并行加载，不阻塞关键渲染路径
3. **智能调度**：浏览器会在空闲时间进行预加载

**潜在负面影响：**
1. **网络竞争**：过多预加载可能与首屏资源竞争带宽
2. **内存占用**：预加载的模块会占用额外内存
3. **不必要的请求**：预加载了用户不会使用的模块

**最佳实践：**
- 只预加载高概率使用的模块
- 在首屏加载完成后再进行预加载
- 根据网络条件调整预加载策略`,
    code: `// ✅ 好的做法：首屏完成后预加载
function App() {
  const [isFirstLoadComplete, setIsFirstLoadComplete] = useState(false);

  useEffect(() => {
    // 等待首屏内容加载完成
    const timer = setTimeout(() => {
      setIsFirstLoadComplete(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (isFirstLoadComplete) {
      // 首屏完成后再预加载次要功能
      preinitModule('/modules/secondary-features.js', {
        as: 'script',
        crossOrigin: 'anonymous'
      });
    }
  }, [isFirstLoadComplete]);
}

// ✅ 根据网络条件调整
function useNetworkAwarePreloading() {
  useEffect(() => {
    if ('connection' in navigator) {
      const connection = navigator.connection;

      // 只在良好网络条件下预加载
      if (connection.effectiveType === '4g') {
        preinitModule('/modules/advanced-features.js');
      }
    }
  }, []);
}`,
    tags: ['性能优化', '首屏加载'],
    relatedQuestions: ['如何监控预加载的效果？', '什么情况下不应该使用preinitModule？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-3',
    question: 'preinitModule在SSR环境中如何工作？',
    answer: `preinitModule在服务端渲染(SSR)环境中有特殊的处理机制：

**服务端行为：**
1. **静默忽略**：在服务端，preinitModule调用会被忽略，不会执行任何操作
2. **不影响渲染**：不会影响服务端的HTML生成过程
3. **无副作用**：不会产生网络请求或其他副作用

**客户端激活：**
1. **Hydration后生效**：只有在客户端hydration完成后才开始工作
2. **状态同步**：需要确保服务端和客户端的组件状态一致
3. **渐进增强**：作为性能优化的渐进增强功能

**最佳实践：**
- 在useEffect中调用，确保只在客户端执行
- 不要依赖preinitModule的执行来维护应用状态
- 考虑使用条件判断确保环境兼容性`,
    code: `// ✅ SSR友好的使用方式
function MyComponent() {
  useEffect(() => {
    // 只在客户端执行
    if (typeof window !== 'undefined') {
      preinitModule('/modules/client-only-feature.js', {
        as: 'script',
        crossOrigin: 'anonymous'
      });
    }
  }, []);

  return <div>内容</div>;
}

// ✅ 使用自定义Hook封装
function useClientSidePreloading(modulePath, options) {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      preinitModule(modulePath, options);
    }
  }, [modulePath, options]);
}

// 使用示例
function App() {
  useClientSidePreloading('/modules/analytics.js', {
    as: 'script',
    crossOrigin: 'anonymous'
  });

  return <div>应用内容</div>;
}`,
    tags: ['SSR', '服务端渲染'],
    relatedQuestions: ['如何在Next.js中使用preinitModule？', 'Hydration过程中需要注意什么？']
  }
];

export default commonQuestions;