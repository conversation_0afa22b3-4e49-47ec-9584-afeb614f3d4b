import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: "preinitModule是React DOM中用于预初始化ES模块的函数，专门用于提前加载和初始化JavaScript模块，确保模块在需要时能够立即可用，显著提升应用的加载性能和用户体验。",

  introduction: `preinitModule是React 18.2+引入的ReactDOM资源预加载API，专门为现代ES模块系统设计的预初始化函数。

它遵循现代Web性能优化的最佳实践，在资源预加载和模块初始化之间做出了智能平衡，允许开发者提前声明需要的模块依赖，让浏览器在最佳时机进行预加载和预初始化。

主要用于关键路径模块预加载、动态导入优化和代码分割场景的性能提升。相比传统的动态import()，它的创新在于将模块的加载时机从"使用时"提前到"声明时"，实现了更精细的资源加载控制。

在React生态中，它是Resource Preloading APIs的重要组成部分，常见于需要优化首屏加载性能的大型应用，特别适合需要动态加载大量模块的复杂场景。

核心优势包括减少模块加载延迟、优化代码分割效果、提升动态导入性能，但也需要注意合理控制预加载的模块数量以避免资源浪费。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react-dom/index.d.ts:45
 * - 实现文件：packages/react-dom/src/shared/ReactDOMResourceUtils.js:120
 * - 内部类型：packages/react-dom-bindings/src/shared/ReactDOMResourceValidation.js:85
 */

// 基础语法
function preinitModule(href: string, options?: PreinitModuleOptions): void;

// 选项接口
interface PreinitModuleOptions {
  as?: 'script';           // 资源类型，目前只支持 'script'
  crossOrigin?: string;    // CORS设置：'anonymous' | 'use-credentials'
  integrity?: string;      // 子资源完整性校验
  nonce?: string;         // 内容安全策略随机数
}

// 使用示例
preinitModule('/modules/analytics.js', {
  as: 'script',
  crossOrigin: 'anonymous',
  integrity: 'sha384-...'
});

/**
 * 参数约束：
 * - href 必须是有效的模块URL
 * - options.as 目前只支持 'script'
 * - 同一个href多次调用会被去重
 */`,

  quickExample: `function ModulePreloadExample() {
  useEffect(() => {
    // 预初始化关键业务模块
    preinitModule('/modules/user-analytics.js', {
      as: 'script',
      crossOrigin: 'anonymous'
    });

    // 预初始化第三方库模块
    preinitModule('/modules/chart-library.js', {
      as: 'script',
      integrity: 'sha384-oqVuAfXRKap7fdgcCY5uykM6+R9GqQ8K/uxy9rx7HNQlGYl1kPzQho1wx4JwY8wC'
    });

    // 预初始化条件性加载的模块
    if (shouldLoadAdvancedFeatures) {
      preinitModule('/modules/advanced-features.js', {
        as: 'script',
        crossOrigin: 'anonymous'
      });
    }
  }, []);

  const handleLoadAnalytics = async () => {
    // 由于已经预初始化，这里的动态导入会更快
    const analytics = await import('/modules/user-analytics.js');
    analytics.trackUserAction('button_click');
  };

  const handleLoadChart = async () => {
    // 预初始化的模块可以立即使用
    const chartLib = await import('/modules/chart-library.js');
    chartLib.renderChart(chartData);
  };

  return (
    <div>
      {/* 用户交互触发已预初始化的模块加载 */}
      <button onClick={handleLoadAnalytics}>
        开始分析 (已预加载)
      </button>
      <button onClick={handleLoadChart}>
        显示图表 (已预加载)
      </button>
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "preinitModule在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用这个API来优化模块加载性能",
      diagram: `graph LR
      A[preinitModule核心场景] --> B[代码分割优化]
      A --> C[第三方库预加载]
      A --> D[条件性模块]

      B --> B1["📦 路由模块<br/>提前加载页面组件"]
      B --> B2["🔧 功能模块<br/>预加载业务逻辑"]

      C --> C1["📊 图表库<br/>数据可视化组件预加载"]
      C --> C2["🎨 UI库<br/>设计系统组件预初始化"]

      D --> D1["🔐 权限模块<br/>根据用户角色预加载"]
      D --> D2["🌐 国际化<br/>根据语言预加载翻译"]

      style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
      style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
      style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "技术实现架构",
      description: "preinitModule的技术实现架构，展示其与浏览器模块系统和React资源管理的集成关系",
      diagram: `graph TB
      A[preinitModule技术架构] --> B[浏览器模块系统]
      A --> C[React资源管理]
      A --> D[性能优化层]

      B --> B1["🌐 ES Module Loader<br/>原生模块加载机制"]
      B --> B2["📋 Module Cache<br/>浏览器模块缓存"]

      C --> C1["🎯 Resource Hints<br/>资源预加载提示"]
      C --> C2["🔄 Hydration Support<br/>SSR模块同步"]

      D --> D1["⚡ Preload Strategy<br/>智能预加载策略"]
      D --> D2["📈 Performance Metrics<br/>加载性能监控"]

      style A fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
      style B fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
      style C fill:#fce4ec,stroke:#c2185b,stroke-width:2px
      style D fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px`
    },
    {
      title: "生态系统集成",
      description: "preinitModule在React资源预加载生态系统中的位置和与其他Resource APIs的协作关系",
      diagram: `graph TD
      A[React Resource APIs生态] --> B[预加载APIs]
      A --> C[构建工具链]
      A --> D[性能监控]

      B --> B1["preload<br/>通用资源预加载"]
      B --> B2["preloadModule<br/>模块预加载"]
      B --> B3["preinitModule<br/>模块预初始化"]
      B --> B4["preinit<br/>通用资源预初始化"]

      C --> C1["Webpack<br/>模块打包集成"]
      C --> C2["Vite<br/>现代构建工具支持"]

      D --> D1["Web Vitals<br/>性能指标监控"]
      D --> D2["Resource Timing<br/>资源加载分析"]

      B3 -.-> B1
      B3 -.-> B2
      B3 -.-> C1
      B3 -.-> D1

      style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
      style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
      style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style B3 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
    }
  ],
  
  parameters: [
    {
      name: "href",
      type: "string",
      required: true,
      description: "要预初始化的ES模块的URL路径。必须是有效的模块路径，支持相对路径和绝对路径。",
      example: `preinitModule('/modules/analytics.js');
preinitModule('https://cdn.example.com/chart-lib.js');`
    },
    {
      name: "options",
      type: "PreinitModuleOptions",
      required: false,
      description: "可选的配置对象，用于指定模块加载的额外选项，包括资源类型、CORS设置、完整性校验等。",
      example: `preinitModule('/modules/secure-module.js', {
  as: 'script',
  crossOrigin: 'anonymous',
  integrity: 'sha384-...',
  nonce: 'random-nonce-value'
});`
    }
  ],

  returnValue: {
    type: "void",
    description: "preinitModule不返回任何值，它是一个纯粹的副作用函数，用于向浏览器发出模块预初始化的指令。",
    example: `// preinitModule没有返回值
preinitModule('/modules/feature.js', {
  as: 'script',
  crossOrigin: 'anonymous'
});
// 函数执行后，浏览器开始预初始化指定模块`
  },

  keyFeatures: [
    {
      title: "ES模块预初始化",
      description: "专门为ES模块系统设计，能够提前初始化JavaScript模块，确保模块在需要时立即可用",
      benefit: "显著减少动态import()的延迟，提升代码分割场景下的用户体验"
    },
    {
      title: "智能资源管理",
      description: "与React的资源管理系统深度集成，自动处理重复调用和资源去重",
      benefit: "避免重复预加载同一模块，优化网络资源使用效率"
    },
    {
      title: "安全性支持",
      description: "支持子资源完整性校验(SRI)和内容安全策略(CSP)，确保模块加载的安全性",
      benefit: "在预加载性能优化的同时，保障应用的安全性要求"
    },
    {
      title: "CORS兼容性",
      description: "提供完整的跨域资源共享配置选项，支持不同的CORS策略",
      benefit: "能够安全地预加载来自不同域的第三方模块和CDN资源"
    },
    {
      title: "SSR友好",
      description: "在服务端渲染环境中能够正确处理，不会影响服务端的渲染过程",
      benefit: "确保同构应用在服务端和客户端都能正常工作，提升开发体验"
    },
    {
      title: "性能监控集成",
      description: "与浏览器的Resource Timing API集成，支持性能监控和分析",
      benefit: "开发者可以精确监控模块预加载的效果，持续优化加载策略"
    }
  ],

  limitations: [
    "只能在React 18.2+版本中使用，在旧版本中不可用",
    "目前options.as参数只支持'script'类型，不支持其他资源类型",
    "预初始化的模块仍然需要通过动态import()来实际使用，preinitModule只是优化加载时机",
    "在服务端渲染环境中，preinitModule调用会被忽略，只在客户端生效",
    "过度使用可能导致不必要的网络请求和内存占用，需要合理控制预加载的模块数量"
  ],

  bestPractices: [
    "在应用启动时或路由变化时调用preinitModule，为即将需要的模块做好准备",
    "结合代码分割策略，只预初始化高概率会被使用的关键模块",
    "为第三方模块设置适当的integrity校验，确保资源的完整性和安全性",
    "使用crossOrigin设置来正确处理跨域模块的预加载",
    "在开发环境中监控预加载效果，避免预加载不必要的模块",
    "结合用户行为分析，智能地预测和预加载用户可能需要的功能模块",
    "在移动设备上谨慎使用，考虑网络条件和电池消耗的影响",
    "配合Webpack或Vite等构建工具的代码分割功能，实现最佳的模块加载策略"
  ],

  warnings: [
    "不要预初始化所有可能的模块，这会导致不必要的网络开销和内存占用",
    "确保预初始化的模块路径正确，错误的路径会导致404错误但不会抛出异常",
    "在使用第三方CDN模块时，务必设置正确的crossOrigin和integrity参数",
    "注意preinitModule不会自动执行模块代码，只是预加载和预解析，实际使用仍需import()",
    "在严格的CSP环境中，确保nonce参数与页面的CSP策略匹配"
  ]
};

export default basicInfo;