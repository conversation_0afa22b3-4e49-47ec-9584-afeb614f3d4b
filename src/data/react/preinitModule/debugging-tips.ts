import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'preinitModule虽然API简单，但在实际使用中开发者经常遇到一些典型问题。本节提供完整的问题诊断和解决方案，帮助快速定位和修复preinitModule相关的技术问题。',
        sections: [
          {
            title: '预加载无效果问题',
            description: '最常见的问题是preinitModule看起来没有任何效果，模块加载时间没有改善。这通常涉及调用时机、网络条件、浏览器支持等多个方面',
            items: [
              {
                title: '调用时机过晚',
                description: 'preinitModule在模块即将使用时才调用，没有足够的时间进行预加载和预初始化',
                solution: '1. 在应用启动时预加载核心模块；2. 在路由变化时预加载目标页面模块；3. 根据用户行为预测性加载',
                prevention: '建立预加载策略，在合适的时机提前调用preinitModule',
                code: `// ❌ 错误：调用时机过晚
const handleClick = async () => {
  preinitModule('/modules/feature.js'); // 太晚了
  const module = await import('/modules/feature.js');
};

// ✅ 正确：提前预加载
function App() {
  useEffect(() => {
    // 应用启动时预加载
    preinitModule('/modules/feature.js', {
      as: 'script',
      crossOrigin: 'anonymous'
    });
  }, []);

  const handleClick = async () => {
    // 模块已经预加载，立即可用
    const module = await import('/modules/feature.js');
  };
}`
              },
              {
                title: '浏览器不支持modulepreload',
                description: '在不支持modulepreload的旧浏览器中，preinitModule会降级但效果有限',
                solution: '1. 检查浏览器支持情况；2. 提供polyfill或fallback方案；3. 使用特性检测',
                prevention: '在使用前检测浏览器支持，提供备用方案',
                code: `// 检测浏览器支持
function supportsModulePreload() {
  const link = document.createElement('link');
  return link.relList && link.relList.supports &&
         link.relList.supports('modulepreload');
}

// 带检测的预加载
function safePreinitModule(href, options) {
  if (supportsModulePreload()) {
    preinitModule(href, options);
  } else {
    // 降级方案：使用传统preload
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = 'script';
    document.head.appendChild(link);
  }
}`
              }
            ]
          },
          {
            title: '性能问题',
            description: '过度使用preinitModule或配置不当可能导致性能问题，包括网络拥塞、内存占用过高等',
            items: [
              {
                title: '过度预加载导致网络拥塞',
                description: '同时预加载过多模块，导致网络带宽竞争，反而影响关键资源加载',
                solution: '1. 限制并发预加载数量；2. 按优先级分批预加载；3. 根据网络条件调整策略',
                prevention: '建立预加载队列管理机制，控制并发数量',
                code: `// 预加载队列管理
class PreloadQueue {
  constructor(maxConcurrent = 3) {
    this.queue = [];
    this.active = new Set();
    this.maxConcurrent = maxConcurrent;
  }

  add(href, options) {
    this.queue.push({ href, options });
    this.process();
  }

  async process() {
    while (this.queue.length > 0 && this.active.size < this.maxConcurrent) {
      const { href, options } = this.queue.shift();
      this.active.add(href);

      try {
        preinitModule(href, options);
        // 模拟预加载完成
        await new Promise(resolve => setTimeout(resolve, 100));
      } finally {
        this.active.delete(href);
        this.process(); // 处理下一个
      }
    }
  }
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '有效的调试工具可以帮助开发者监控preinitModule的效果，诊断性能问题，优化预加载策略。掌握这些工具是高效使用preinitModule的关键。',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '现代浏览器提供了强大的网络和性能分析工具，可以直观地监控模块预加载的效果',
            items: [
              {
                title: '网络面板监控',
                description: '使用浏览器开发者工具的网络面板监控modulepreload请求的状态和时机',
                solution: '1. 打开开发者工具网络面板；2. 筛选显示"Other"类型请求；3. 查看modulepreload请求的时机和状态；4. 分析预加载效果',
                prevention: '定期检查网络面板，确保预加载按预期工作',
                code: `// 监控预加载状态的工具函数
function monitorPreloadStatus() {
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.initiatorType === 'link' &&
          entry.name.includes('modulepreload')) {
        console.log('Module preload:', {
          url: entry.name,
          startTime: entry.startTime,
          duration: entry.duration,
          transferSize: entry.transferSize
        });
      }
    }
  });

  observer.observe({ entryTypes: ['resource'] });
}

// 检查模块是否已预加载
function isModulePreloaded(href) {
  const links = document.querySelectorAll('link[rel="modulepreload"]');
  return Array.from(links).some(link => link.href === href);
}

// 预加载状态调试工具
function debugPreloadStatus() {
  const preloadedModules = Array.from(
    document.querySelectorAll('link[rel="modulepreload"]')
  ).map(link => ({
    href: link.href,
    crossOrigin: link.crossOrigin,
    integrity: link.integrity,
    loaded: link.onload ? 'Yes' : 'Unknown'
  }));

  console.table(preloadedModules);
}`
              },
              {
                title: 'Performance API分析',
                description: '使用Performance API精确测量模块加载性能，对比预加载前后的效果',
                solution: '1. 使用performance.mark标记关键时间点；2. 使用performance.measure计算时间差；3. 分析Resource Timing数据',
                prevention: '建立性能监控机制，持续跟踪预加载效果',
                code: `// 性能测量工具
class ModulePerformanceMonitor {
  constructor() {
    this.measurements = new Map();
  }

  // 开始测量
  startMeasure(moduleName) {
    performance.mark(\`\${moduleName}-start\`);
  }

  // 结束测量
  endMeasure(moduleName) {
    performance.mark(\`\${moduleName}-end\`);
    performance.measure(
      \`\${moduleName}-load-time\`,
      \`\${moduleName}-start\`,
      \`\${moduleName}-end\`
    );

    const measure = performance.getEntriesByName(\`\${moduleName}-load-time\`)[0];
    this.measurements.set(moduleName, measure.duration);

    console.log(\`Module \${moduleName} load time: \${measure.duration}ms\`);
  }

  // 获取所有测量结果
  getAllMeasurements() {
    return Object.fromEntries(this.measurements);
  }

  // 对比预加载效果
  compareWithBaseline(moduleName, baselineTime) {
    const currentTime = this.measurements.get(moduleName);
    if (currentTime) {
      const improvement = ((baselineTime - currentTime) / baselineTime * 100).toFixed(2);
      console.log(\`Module \${moduleName} improvement: \${improvement}%\`);
    }
  }
}

// 使用示例
const monitor = new ModulePerformanceMonitor();

// 测量动态导入性能
async function loadModuleWithMeasure(modulePath) {
  monitor.startMeasure('feature-module');
  const module = await import(modulePath);
  monitor.endMeasure('feature-module');
  return module;
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;