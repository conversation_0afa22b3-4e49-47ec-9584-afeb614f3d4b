import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 1,
    question: 'preinitModule是什么？它与preloadModule有什么区别？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'preinitModule是React DOM中用于预初始化ES模块的函数，比preloadModule更进一步，不仅预加载还预解析模块。',
      detailed: `preinitModule是React 18.2+引入的资源预加载API，专门用于ES模块的预初始化：

**preinitModule的特点：**
1. **预初始化**：不仅下载模块，还预解析和预编译
2. **ES模块专用**：专门针对JavaScript模块优化
3. **缓存优化**：利用浏览器模块缓存系统
4. **安全性**：支持完整性校验和CORS

**与preloadModule的区别：**
- **preloadModule**：只是预加载模块文件，不进行解析
- **preinitModule**：预加载+预解析+预初始化，更彻底的优化

**使用场景：**
- 关键路径模块的预加载
- 代码分割场景的性能优化
- 条件性加载的模块准备`,
      code: `// preloadModule - 只预加载
preloadModule('/modules/chart.js', {
  as: 'script',
  crossOrigin: 'anonymous'
});

// preinitModule - 预加载+预初始化
preinitModule('/modules/chart.js', {
  as: 'script',
  crossOrigin: 'anonymous'
});

// 使用时的差异
const handleLoadChart = async () => {
  // preloadModule: 仍需要解析时间
  // preinitModule: 立即可用
  const chartModule = await import('/modules/chart.js');
  chartModule.renderChart(data);
};`
    },
    tags: ['基础概念', '资源预加载']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 2,
    question: '如何在实际项目中设计preinitModule的预加载策略？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实践应用',
    answer: {
      brief: '设计预加载策略需要考虑用户行为分析、网络条件、业务优先级和资源约束等多个维度。',
      detailed: `设计有效的preinitModule预加载策略需要系统性的方法：

**1. 用户行为分析：**
- 收集用户路径数据，分析最常用的功能模块
- 识别用户操作的时间模式和序列模式
- 建立用户画像，针对不同用户群体制定策略

**2. 业务优先级评估：**
- 核心功能模块优先级最高
- 高转化率功能次之
- 辅助功能最后考虑

**3. 技术约束考虑：**
- 网络条件自适应
- 设备性能评估
- 缓存策略配合

**4. 动态调整机制：**
- A/B测试验证效果
- 实时监控和调整
- 用户反馈收集`,
      code: `// 预加载策略设计示例
class PreloadStrategy {
  constructor() {
    this.userProfile = null;
    this.networkInfo = null;
    this.businessRules = new Map();
  }

  // 初始化策略
  async initialize() {
    this.userProfile = await this.analyzeUserBehavior();
    this.networkInfo = this.getNetworkInfo();
    this.setupBusinessRules();
  }

  // 分析用户行为
  async analyzeUserBehavior() {
    const history = await getUserActionHistory();
    return {
      frequentModules: this.calculateModuleFrequency(history),
      userType: this.classifyUser(history),
      timePatterns: this.analyzeTimePatterns(history)
    };
  }

  // 执行预加载决策
  executePreloadStrategy(currentContext) {
    const decisions = this.makePreloadDecisions(currentContext);

    decisions.forEach(decision => {
      if (decision.shouldPreload) {
        preinitModule(decision.modulePath, {
          as: 'script',
          crossOrigin: 'anonymous',
          priority: decision.priority
        });
      }
    });
  }

  // 预加载决策算法
  makePreloadDecisions(context) {
    const candidates = this.getCandidateModules(context);

    return candidates.map(module => ({
      modulePath: module.path,
      shouldPreload: this.shouldPreload(module, context),
      priority: this.calculatePriority(module, context)
    }));
  }

  // 判断是否应该预加载
  shouldPreload(module, context) {
    const probability = this.calculateUsageProbability(module, context);
    const networkScore = this.getNetworkScore();
    const resourceScore = this.getResourceScore();

    return probability * networkScore * resourceScore > 0.6;
  }
}`
    },
    tags: ['策略设计', '实践应用']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 3,
    question: 'preinitModule的内部实现原理是什么？它如何与浏览器的模块系统集成？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '内部原理',
    answer: {
      brief: 'preinitModule基于浏览器的modulepreload资源提示和ES模块缓存系统，通过DOM操作和模块解析器实现预初始化。',
      detailed: `preinitModule的内部实现涉及多个浏览器底层机制：

**1. Resource Hints机制：**
- 生成<link rel="modulepreload">标签
- 浏览器识别并开始预加载
- 利用HTTP/2多路复用优化

**2. ES模块解析流程：**
- 模块图构建和依赖分析
- 预解析和预编译
- 模块缓存存储

**3. React集成机制：**
- 资源管理器去重处理
- SSR兼容性处理
- 错误边界集成

**4. 安全验证流程：**
- SRI完整性校验
- CORS策略验证
- CSP策略检查

**5. 性能优化策略：**
- 优先级队列管理
- 网络资源调度
- 缓存策略优化`,
      code: `// preinitModule内部实现简化版本
function preinitModule(href, options = {}) {
  // 1. 参数验证和标准化
  const normalizedHref = normalizeModuleHref(href);
  const normalizedOptions = normalizeOptions(options);

  // 2. 检查是否已经预加载
  if (isAlreadyPreloaded(normalizedHref)) {
    return;
  }

  // 3. 创建modulepreload链接
  const link = document.createElement('link');
  link.rel = 'modulepreload';
  link.href = normalizedHref;

  // 4. 设置安全选项
  if (normalizedOptions.crossOrigin) {
    link.crossOrigin = normalizedOptions.crossOrigin;
  }

  if (normalizedOptions.integrity) {
    link.integrity = normalizedOptions.integrity;
  }

  if (normalizedOptions.nonce) {
    link.nonce = normalizedOptions.nonce;
  }

  // 5. 添加到文档头部
  document.head.appendChild(link);

  // 6. 注册到React资源管理器
  registerPreloadedResource(normalizedHref, normalizedOptions);

  // 7. 设置错误处理
  link.onerror = () => {
    handlePreloadError(normalizedHref);
  };

  link.onload = () => {
    markAsPreloaded(normalizedHref);
  };
}

// 浏览器模块加载器集成
class ModulePreloadManager {
  constructor() {
    this.preloadedModules = new Set();
    this.pendingPreloads = new Map();
  }

  // 与浏览器模块缓存集成
  integrateWithModuleCache() {
    const originalImport = window.import;

    window.import = async (specifier) => {
      // 检查是否已预加载
      if (this.preloadedModules.has(specifier)) {
        // 直接从缓存获取
        return originalImport(specifier);
      }

      // 正常加载流程
      return originalImport(specifier);
    };
  }
}`
    },
    tags: ['内部原理', '浏览器集成']
  }
];

export default interviewQuestions;