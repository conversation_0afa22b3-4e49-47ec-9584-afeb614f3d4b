import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const preinitModuleData: ApiItem = {
  id: 'preinitModule',
  title: 'preinitModule',
  description: 'preinitModule是React DOM中用于预初始化ES模块的函数，专门用于提前加载和初始化JavaScript模块，确保模块在需要时能够立即可用，显著提升应用的加载性能和用户体验。',
  category: 'ReactDOM APIs',
  difficulty: 'medium',

  syntax: `function preinitModule(href: string, options?: PreinitModuleOptions): void;`,
  example: `function ModulePreloadExample() {
  useEffect(() => {
    // 预初始化关键业务模块
    preinitModule('/modules/user-analytics.js', {
      as: 'script',
      crossOrigin: 'anonymous'
    });

    // 预初始化第三方库模块
    preinitModule('/modules/chart-library.js', {
      as: 'script',
      integrity: 'sha384-...'
    });
  }, []);

  const handleLoadAnalytics = async () => {
    // 由于已经预初始化，这里的动态导入会更快
    const analytics = await import('/modules/user-analytics.js');
    analytics.trackUserAction('button_click');
  };

  return (
    <button onClick={handleLoadAnalytics}>
      开始分析 (已预加载)
    </button>
  );
}`,
  notes: '只能在React 18.2+版本中使用，目前options.as参数只支持\'script\'类型',

  version: 'React 18.2.0+',
  tags: ["资源预加载", "ES模块", "性能优化", "代码分割", "动态导入"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default preinitModuleData;