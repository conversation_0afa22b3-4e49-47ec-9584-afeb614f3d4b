import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-1',
    title: '电商平台动态功能预加载',
    description: '在大型电商平台中，用户浏览商品时需要根据不同的商品类型动态加载相应的功能模块，如3D预览、AR试穿、价格比较等高级功能。',
    businessValue: '提升用户购物体验，减少功能加载等待时间，提高转化率。据统计，功能响应时间每减少100ms，用户互动率可提升5-8%。',
    scenario: '用户在浏览服装商品时，系统需要预加载AR试穿模块；浏览电子产品时，预加载3D展示和规格对比模块。',
    code: `function ProductPageOptimizer() {
  const [productType, setProductType] = useState('');
  const [currentProduct, setCurrentProduct] = useState(null);

  useEffect(() => {
    // 根据产品类型预初始化相关功能模块
    if (productType === 'clothing') {
      preinitModule('/modules/ar-try-on.js', {
        as: 'script',
        crossOrigin: 'anonymous'
      });
      preinitModule('/modules/size-guide.js', {
        as: 'script',
        crossOrigin: 'anonymous'
      });
    } else if (productType === 'electronics') {
      preinitModule('/modules/3d-viewer.js', {
        as: 'script',
        crossOrigin: 'anonymous'
      });
      preinitModule('/modules/spec-comparison.js', {
        as: 'script',
        crossOrigin: 'anonymous'
      });
    } else if (productType === 'furniture') {
      preinitModule('/modules/room-planner.js', {
        as: 'script',
        crossOrigin: 'anonymous'
      });
    }
  }, [productType]);

  const handleTryAR = async () => {
    // 由于已经预初始化，AR模块加载会很快
    const arModule = await import('/modules/ar-try-on.js');
    arModule.startARExperience(currentProduct);
  };

  const handle3DView = async () => {
    // 预初始化的3D模块可以立即使用
    const viewer3D = await import('/modules/3d-viewer.js');
    viewer3D.render3DModel(currentProduct.modelUrl);
  };

  return (
    <div className="product-page">
      <ProductInfo product={currentProduct} />

      {productType === 'clothing' && (
        <button onClick={handleTryAR} className="ar-button">
          AR试穿 (已预加载)
        </button>
      )}

      {productType === 'electronics' && (
        <button onClick={handle3DView} className="3d-button">
          3D查看 (已预加载)
        </button>
      )}
    </div>
  );
}`,
    explanation: '通过preinitModule根据商品类型预初始化相关功能模块，确保用户点击高级功能时能够立即响应。系统智能识别商品类型，只预加载必要的模块，避免资源浪费。',
    benefits: [
      '高级功能响应时间减少80%，用户体验显著提升',
      '智能预加载策略，只加载必要模块，优化网络资源使用',
      '提高功能使用率，AR试穿和3D查看的使用率提升45%'
    ],
    metrics: {
      performance: '功能加载时间从800ms降低到150ms，响应速度提升81%',
      userExperience: '用户满意度提升35%，高级功能使用率增加45%',
      technicalMetrics: '网络请求优化60%，缓存命中率提升到85%'
    },
    difficulty: 'easy',
    tags: ['电商平台', '动态加载', '用户体验']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-2',
    title: '在线教育平台智能模块预加载',
    description: '在大型在线教育平台中，根据学生的学习路径和课程类型，智能预加载相关的学习工具模块，如代码编辑器、数学公式编辑器、3D模型查看器等。',
    businessValue: '提升学习体验，减少工具加载等待时间，提高学习效率和完课率。优化后的工具响应速度可以提升学生专注度15-20%。',
    scenario: '学生进入编程课程时预加载代码编辑器和调试工具；进入数学课程时预加载公式编辑器和图形计算器；进入3D设计课程时预加载建模工具。',
    code: `function LearningPlatform() {
  const [currentCourse, setCurrentCourse] = useState(null);
  const [studentProfile, setStudentProfile] = useState(null);

  useEffect(() => {
    if (!currentCourse) return;

    // 根据课程类型预初始化相关工具模块
    switch (currentCourse.type) {
      case 'programming':
        preinitModule('/modules/code-editor.js', {
          as: 'script',
          crossOrigin: 'anonymous'
        });
        preinitModule('/modules/debugger-tools.js', {
          as: 'script',
          crossOrigin: 'anonymous'
        });
        preinitModule('/modules/syntax-highlighter.js', {
          as: 'script',
          crossOrigin: 'anonymous'
        });
        break;

      case 'mathematics':
        preinitModule('/modules/formula-editor.js', {
          as: 'script',
          crossOrigin: 'anonymous'
        });
        preinitModule('/modules/graph-calculator.js', {
          as: 'script',
          crossOrigin: 'anonymous'
        });
        break;

      case '3d-design':
        preinitModule('/modules/3d-modeling.js', {
          as: 'script',
          crossOrigin: 'anonymous'
        });
        preinitModule('/modules/material-library.js', {
          as: 'script',
          crossOrigin: 'anonymous'
        });
        break;

      case 'language':
        preinitModule('/modules/pronunciation-tool.js', {
          as: 'script',
          crossOrigin: 'anonymous'
        });
        preinitModule('/modules/translation-helper.js', {
          as: 'script',
          crossOrigin: 'anonymous'
        });
        break;
    }

    // 根据学生历史行为预加载常用工具
    if (studentProfile?.frequentTools) {
      studentProfile.frequentTools.forEach(tool => {
        preinitModule(\`/modules/\${tool}.js\`, {
          as: 'script',
          crossOrigin: 'anonymous'
        });
      });
    }
  }, [currentCourse, studentProfile]);

  const handleOpenCodeEditor = async () => {
    // 预初始化的代码编辑器可以立即启动
    const codeEditor = await import('/modules/code-editor.js');
    codeEditor.initialize({
      language: currentCourse.programmingLanguage,
      theme: studentProfile.preferredTheme
    });
  };

  const handleOpenFormulaEditor = async () => {
    // 预加载的公式编辑器立即可用
    const formulaEditor = await import('/modules/formula-editor.js');
    formulaEditor.createEditor({
      mathType: currentCourse.mathLevel,
      symbols: currentCourse.requiredSymbols
    });
  };

  return (
    <div className="learning-platform">
      <CourseContent course={currentCourse} />

      {currentCourse?.type === 'programming' && (
        <button onClick={handleOpenCodeEditor} className="tool-button">
          打开代码编辑器 (已预加载)
        </button>
      )}

      {currentCourse?.type === 'mathematics' && (
        <button onClick={handleOpenFormulaEditor} className="tool-button">
          打开公式编辑器 (已预加载)
        </button>
      )}
    </div>
  );
}`,
    explanation: '通过分析课程类型和学生行为模式，智能预初始化相关的学习工具模块。系统根据课程内容自动判断需要的工具类型，并结合学生的历史使用习惯进行个性化预加载，确保学习工具能够即时响应。',
    benefits: [
      '学习工具启动时间减少85%，学习流程更加流畅',
      '个性化预加载策略，根据学生习惯优化资源使用',
      '提升学习专注度，减少因工具加载导致的学习中断',
      '支持多种学科的专业工具，覆盖编程、数学、设计等领域'
    ],
    metrics: {
      performance: '工具启动时间从1.2s降低到180ms，响应速度提升85%',
      userExperience: '学生满意度提升40%，完课率增加25%',
      technicalMetrics: '工具使用率提升60%，平台粘性增加35%'
    },
    difficulty: 'medium',
    tags: ['在线教育', '智能预加载', '个性化学习']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-3',
    title: '金融交易平台风险控制模块预加载',
    description: '在高频金融交易平台中，根据市场波动性和用户交易行为，动态预加载风险分析、实时监控、合规检查等关键安全模块，确保交易安全和监管合规。',
    businessValue: '提升交易安全性，减少风险检测延迟，确保监管合规。在高频交易场景中，每毫秒的延迟都可能影响巨额资金安全，优化后可减少99.9%的安全检测延迟。',
    scenario: '用户进行大额交易时预加载反洗钱检测模块；市场异常波动时预加载风险评估工具；跨境交易时预加载合规性检查模块。',
    code: `function TradingPlatform() {
  const [marketCondition, setMarketCondition] = useState('normal');
  const [userRiskProfile, setUserRiskProfile] = useState(null);
  const [currentTransaction, setCurrentTransaction] = useState(null);

  useEffect(() => {
    // 根据市场条件预初始化风险控制模块
    if (marketCondition === 'volatile') {
      preinitModule('/modules/risk-assessment.js', {
        as: 'script',
        crossOrigin: 'anonymous',
        integrity: 'sha384-risk-module-hash'
      });
      preinitModule('/modules/volatility-monitor.js', {
        as: 'script',
        crossOrigin: 'anonymous',
        integrity: 'sha384-volatility-hash'
      });
    }

    // 根据用户风险等级预加载相应模块
    if (userRiskProfile?.level === 'high') {
      preinitModule('/modules/enhanced-kyc.js', {
        as: 'script',
        crossOrigin: 'anonymous',
        integrity: 'sha384-kyc-module-hash'
      });
      preinitModule('/modules/transaction-monitor.js', {
        as: 'script',
        crossOrigin: 'anonymous',
        integrity: 'sha384-monitor-hash'
      });
    }

    // 预加载基础合规模块
    preinitModule('/modules/aml-detection.js', {
      as: 'script',
      crossOrigin: 'anonymous',
      integrity: 'sha384-aml-module-hash'
    });

    preinitModule('/modules/fraud-prevention.js', {
      as: 'script',
      crossOrigin: 'anonymous',
      integrity: 'sha384-fraud-hash'
    });
  }, [marketCondition, userRiskProfile]);

  useEffect(() => {
    // 根据交易类型预加载特定检查模块
    if (currentTransaction?.type === 'cross-border') {
      preinitModule('/modules/cross-border-compliance.js', {
        as: 'script',
        crossOrigin: 'anonymous',
        integrity: 'sha384-compliance-hash'
      });
    }

    if (currentTransaction?.amount > 10000) {
      preinitModule('/modules/large-transaction-monitor.js', {
        as: 'script',
        crossOrigin: 'anonymous',
        integrity: 'sha384-large-tx-hash'
      });
    }
  }, [currentTransaction]);

  const handleExecuteTransaction = async () => {
    // 预初始化的安全模块可以立即执行检查
    const [amlModule, fraudModule] = await Promise.all([
      import('/modules/aml-detection.js'),
      import('/modules/fraud-prevention.js')
    ]);

    // 并行执行多重安全检查
    const [amlResult, fraudResult] = await Promise.all([
      amlModule.checkTransaction(currentTransaction),
      fraudModule.analyzeRisk(currentTransaction, userRiskProfile)
    ]);

    if (amlResult.passed && fraudResult.riskLevel < 0.3) {
      // 执行交易
      executeSecureTransaction(currentTransaction);
    } else {
      // 触发人工审核
      triggerManualReview(currentTransaction, { amlResult, fraudResult });
    }
  };

  const handleRiskAssessment = async () => {
    // 市场波动时的风险评估
    const riskModule = await import('/modules/risk-assessment.js');
    const assessment = await riskModule.assessPortfolioRisk(
      userRiskProfile.portfolio,
      marketCondition
    );

    displayRiskWarning(assessment);
  };

  return (
    <div className="trading-platform">
      <MarketOverview condition={marketCondition} />
      <UserPortfolio profile={userRiskProfile} />

      <div className="transaction-panel">
        <TransactionForm
          transaction={currentTransaction}
          onChange={setCurrentTransaction}
        />

        <button
          onClick={handleExecuteTransaction}
          className="execute-button"
          disabled={!currentTransaction}
        >
          执行交易 (安全模块已预加载)
        </button>

        {marketCondition === 'volatile' && (
          <button
            onClick={handleRiskAssessment}
            className="risk-button"
          >
            风险评估 (已预加载)
          </button>
        )}
      </div>
    </div>
  );
}`,
    explanation: '通过智能分析市场条件、用户风险等级和交易特征，预初始化相应的安全和合规检查模块。系统采用多层次的预加载策略，确保在任何交易场景下都能立即执行必要的安全检查，同时通过子资源完整性校验确保模块的安全性。',
    benefits: [
      '交易安全检查延迟减少99.9%，从秒级降低到毫秒级',
      '多重安全模块并行加载，提升整体安全检测效率',
      '智能预测交易风险，提前准备相应的检测工具',
      '确保监管合规，满足金融行业的严格安全要求',
      '支持高频交易场景，不影响交易执行速度'
    ],
    metrics: {
      performance: '安全检查响应时间从2.5s降低到5ms，性能提升99.8%',
      userExperience: '交易成功率提升15%，用户信任度增加45%',
      technicalMetrics: '风险检测准确率提升30%，合规检查覆盖率达到100%'
    },
    difficulty: 'hard',
    tags: ['金融交易', '风险控制', '安全合规', '高频交易']
  }
];

export default businessScenarios;