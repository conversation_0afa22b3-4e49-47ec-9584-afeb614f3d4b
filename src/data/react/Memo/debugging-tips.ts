import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  subTabs: [
    {
      key: 'common-errors',
      title: '🚨 常见错误',
      content: {
        introduction: 'React.memo使用过程中最常遇到的错误类型及其解决方案，帮助快速定位和修复问题。',
        sections: [
          {
            title: 'memo不生效类错误',
            description: 'memo组件仍然重新渲染的各种原因及解决方案',
            items: [
              {
                title: '内联对象导致memo失效',
                description: '每次渲染都创建新对象引用，导致浅比较失败',
                code: `// ❌ 问题代码
function Parent() {
  const [count, setCount] = useState(0);
  
  return (
    <MemoChild 
      style={{color: 'red'}}  // 每次都是新对象
      config={{theme: 'dark'}}  // 每次都是新对象
    />
  );
}

// ✅ 解决方案
function Parent() {
  const [count, setCount] = useState(0);
  
  const style = useMemo(() => ({color: 'red'}), []);
  const config = useMemo(() => ({theme: 'dark'}), []);
  
  return <MemoChild style={style} config={config} />;
}`,
                solution: '使用useMemo缓存对象类型的props，确保引用稳定性',
                prevention: '建立ESLint规则检查内联对象的使用',
                tips: [
                  '对于不变的对象，依赖数组可以为空[]',
                  '对于样式对象，考虑使用CSS-in-JS库的styled-components',
                  '复杂配置对象可以提取到组件外部或使用useRef'
                ]
              },
              {
                title: '内联函数导致memo失效',
                description: '函数props每次渲染都是新的引用，破坏memo优化',
                code: `// ❌ 问题代码
function Parent() {
  const [items, setItems] = useState([]);
  
  return (
    <div>
      {items.map(item => (
        <MemoItem 
          key={item.id}
          item={item}
          onClick={() => handleClick(item.id)}  // 每次都是新函数
        />
      ))}
    </div>
  );
}

// ✅ 解决方案
function Parent() {
  const [items, setItems] = useState([]);
  
  const handleItemClick = useCallback((id) => {
    handleClick(id);
  }, []);
  
  return (
    <div>
      {items.map(item => (
        <MemoItem 
          key={item.id}
          item={item}
          onClick={handleItemClick}
        />
      ))}
    </div>
  );
}`,
                solution: '使用useCallback包装函数props，或者将函数逻辑移到子组件内部',
                prevention: '使用eslint-plugin-react-hooks规则检查依赖',
                tips: [
                  '对于不依赖组件状态的函数，可以提取到组件外部',
                  '考虑使用data-*属性传递参数，在子组件内处理事件',
                  '复杂的事件处理逻辑可以使用useReducer管理'
                ]
              },
              {
                title: 'children属性导致memo失效',
                description: 'JSX children每次渲染都会创建新的React元素',
                code: `// ❌ 问题代码
function Parent() {
  const [count, setCount] = useState(0);
  
  return (
    <MemoWrapper>
      <div>
        <h1>标题</h1>
        <p>内容</p>
      </div>
    </MemoWrapper>
  );
}

// ✅ 解决方案1：使用useMemo缓存children
function Parent() {
  const [count, setCount] = useState(0);
  
  const children = useMemo(() => (
    <div>
      <h1>标题</h1>
      <p>内容</p>
    </div>
  ), []);
  
  return <MemoWrapper>{children}</MemoWrapper>;
}

// ✅ 解决方案2：使用props替代children
function Parent() {
  const [count, setCount] = useState(0);
  
  const content = useMemo(() => ({
    title: '标题',
    description: '内容'
  }), []);
  
  return <MemoWrapper content={content} />;
}`,
                solution: '使用useMemo缓存children，或者改用props传递内容',
                prevention: '避免在memo组件中使用复杂的children结构',
                tips: [
                  '静态内容可以提取为常量',
                  '动态内容考虑使用render props模式',
                  '复杂的children可以拆分为多个独立的props'
                ]
              }
            ]
          },
          {
            title: '自定义比较函数错误',
            description: '自定义比较函数的常见错误及调试方法',
            items: [
              {
                title: '比较函数返回值逻辑错误',
                description: 'areEqual函数与shouldComponentUpdate的返回值含义相反',
                code: `// ❌ 错误的比较逻辑
const MemoComponent = memo(MyComponent, (prevProps, nextProps) => {
  if (prevProps.id !== nextProps.id) {
    return true;  // ❌ 错误：应该返回false表示需要更新
  }
  return false;   // ❌ 错误：应该返回true表示不需要更新
});

// ✅ 正确的比较逻辑
const MemoComponent = memo(MyComponent, (prevProps, nextProps) => {
  return prevProps.id === nextProps.id;  // ✅ 直接返回是否相等
});

// ✅ 更清晰的写法
const MemoComponent = memo(MyComponent, (prevProps, nextProps) => {
  const areEqual = prevProps.id === nextProps.id;
  console.log('Props comparison:', areEqual);  // 调试日志
  return areEqual;
});`,
                solution: '记住areEqual返回true表示相等（不重新渲染），false表示不等（重新渲染）',
                prevention: '添加调试日志验证比较逻辑，使用清晰的变量命名',
                tips: [
                  '使用areEqual变量名明确表达语义',
                  '在开发环境添加比较结果的日志',
                  '可以参考PureComponent的shouldComponentUpdate逻辑'
                ]
              },
              {
                title: '比较函数性能问题',
                description: '复杂的比较函数可能比重新渲染更昂贵',
                code: `// ❌ 昂贵的比较函数
const MemoComponent = memo(MyComponent, (prevProps, nextProps) => {
  // 深度比较整个对象 - 性能很差
  return JSON.stringify(prevProps) === JSON.stringify(nextProps);
});

// ✅ 优化的比较函数
const MemoComponent = memo(MyComponent, (prevProps, nextProps) => {
  // 只比较关键字段
  return prevProps.id === nextProps.id &&
         prevProps.name === nextProps.name &&
         prevProps.status === nextProps.status;
});

// ✅ 性能监控的比较函数
const MemoComponent = memo(MyComponent, (prevProps, nextProps) => {
  const start = performance.now();
  
  const areEqual = prevProps.id === nextProps.id;
  
  const end = performance.now();
  if (end - start > 1) {  // 比较时间超过1ms时警告
    console.warn('比较函数耗时过长:', end - start, 'ms');
  }
  
  return areEqual;
});`,
                solution: '只比较必要的字段，避免深度比较，监控比较函数性能',
                prevention: '设置性能预算，比较时间不应超过重新渲染时间',
                tips: [
                  '优先比较最容易变化的字段',
                  '使用对象的lastModified时间戳快速判断',
                  '复杂对象可以预计算hash值进行比较'
                ]
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '使用各种开发工具来调试和分析React.memo的性能表现。',
        sections: [
          {
            title: 'React DevTools调试技巧',
            description: '使用React DevTools深入分析组件渲染和memo效果',
            items: [
              {
                title: 'Profiler面板使用技巧',
                description: '使用Profiler分析组件渲染性能和memo效果',
                code: `// 1. 编程式Profiler使用
import { Profiler } from 'react';

function onRenderCallback(id, phase, actualDuration, baseDuration) {
  console.log('组件渲染信息:', {
    componentId: id,
    phase: phase,           // "mount" 或 "update"
    actualDuration: actualDuration,  // 实际渲染时间
    baseDuration: baseDuration       // 预估渲染时间
  });
  
  // memo效果分析
  if (phase === 'update' && actualDuration < 0.1) {
    console.log('✅ memo生效，跳过了渲染');
  }
}

function App() {
  return (
    <Profiler id="UserList" onRender={onRenderCallback}>
      <UserList />
    </Profiler>
  );
}

// 2. 条件性Profiler（仅开发环境）
const ConditionalProfiler = ({ children, id }) => {
  if (process.env.NODE_ENV === 'development') {
    return (
      <Profiler id={id} onRender={onRenderCallback}>
        {children}
      </Profiler>
    );
  }
  return children;
};`,
                solution: '使用Profiler组件和Chrome DevTools监控组件性能',
                tips: [
                  '在Profiler面板中查看"Why did this render?"信息',
                  '使用火焰图识别渲染瓶颈',
                  '对比memo前后的渲染时间差异'
                ]
              },
              {
                title: 'Components面板调试',
                description: '在Components面板查看memo组件的props变化',
                code: `// 开发环境下添加调试信息
function DebugMemoComponent(props) {
  // 在开发环境显示props变化
  if (process.env.NODE_ENV === 'development') {
    const prevProps = useRef();
    
    useEffect(() => {
      if (prevProps.current) {
        const changedProps = Object.keys(props).filter(key =>
          props[key] !== prevProps.current[key]
        );
        
        if (changedProps.length > 0) {
          console.log('Props changed:', changedProps);
        }
      }
      prevProps.current = props;
    });
  }
  
  return <div>{/* 组件内容 */}</div>;
}

// 为memo组件添加displayName方便调试
const MemoDebugComponent = memo(DebugMemoComponent);
MemoDebugComponent.displayName = 'MemoDebugComponent';`,
                solution: '使用displayName和props追踪来调试memo组件',
                tips: [
                  '在Components面板查看组件的重新渲染标记',
                  '使用"Highlight updates when components render"选项',
                  '检查props的引用是否发生变化'
                ]
              }
            ]
          },
          {
            title: '自定义调试工具',
            description: '开发自定义工具来监控和调试memo性能',
            items: [
              {
                title: 'memo效果监控器',
                description: '创建工具来监控memo的命中率和性能效果',
                code: `// memo性能监控工具
class MemoPerformanceMonitor {
  static stats = new Map();
  
  static wrapMemoComponent(Component, compareFunction, name) {
    let renderCount = 0;
    let skipCount = 0;
    
    const WrappedComponent = memo(Component, (prevProps, nextProps) => {
      renderCount++;
      
      const shouldSkip = compareFunction ? 
        compareFunction(prevProps, nextProps) : 
        this.shallowEqual(prevProps, nextProps);
      
      if (shouldSkip) {
        skipCount++;
      }
      
      // 更新统计信息
      this.updateStats(name, renderCount, skipCount);
      
      return shouldSkip;
    });
    
    WrappedComponent.displayName = name;
    return WrappedComponent;
  }
  
  static updateStats(componentName, total, skipped) {
    const hitRate = ((skipped / total) * 100).toFixed(1);
    this.stats.set(componentName, {
      total,
      skipped,
      hitRate: hitRate + '%'
    });
  }
  
  static getReport() {
    console.table(Object.fromEntries(this.stats));
  }
  
  static shallowEqual(obj1, obj2) {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    return keys1.every(key => obj1[key] === obj2[key]);
  }
}

// 使用示例
const UserCard = MemoPerformanceMonitor.wrapMemoComponent(
  UserCardComponent, 
  null, 
  'UserCard'
);

// 在控制台查看性能报告
// MemoPerformanceMonitor.getReport();`,
                solution: '开发自定义监控工具来跟踪memo性能',
                tips: [
                  '定期查看memo命中率报告',
                  '设置性能阈值，低于阈值时发出警告',
                  '在生产环境禁用详细的性能监控'
                ]
              },
              {
                title: '渲染追踪器',
                description: '追踪组件渲染的原因和频率',
                code: `// 组件渲染原因追踪器
function useRenderTracker(componentName, props) {
  const prevProps = useRef();
  const renderCount = useRef(0);
  
  useEffect(() => {
    renderCount.current++;
    
    if (prevProps.current) {
      const changes = Object.keys(props).filter(key => 
        props[key] !== prevProps.current[key]
      );
      
      console.log(componentName + ' 重新渲染 #' + renderCount.current, {
        reason: changes.length > 0 ? 'props changed' : 'parent render',
        changedProps: changes,
        currentProps: props
      });
    } else {
      console.log(componentName + ' 首次渲染');
    }
    
    prevProps.current = props;
  });
  
  return renderCount.current;
}

// 使用示例
function MyComponent(props) {
  const renderCount = useRenderTracker('MyComponent', props);
  
  return (
    <div>
      <span>Render #{renderCount}</span>
      {/* 组件内容 */}
    </div>
  );
}`,
                solution: '使用自定义Hook追踪组件重新渲染的原因',
                tips: [
                  '结合memo使用，分析哪些props变化导致重新渲染',
                  '在开发环境启用，生产环境禁用',
                  '可以扩展为性能分析的可视化界面'
                ]
              }
            ]
          }
        ]
      }
    },
    {
      key: 'troubleshooting',
      title: '🔍 问题排查',
      content: {
        introduction: '系统性的问题排查流程，帮助快速定位和解决memo相关问题。',
        sections: [
          {
            title: '问题诊断流程',
            description: '按步骤排查memo不生效的问题',
            items: [
              {
                title: '第一步：确认memo包装正确',
                description: '检查memo的基本使用是否正确',
                code: `// 检查清单
// 1. 确认使用了memo包装
const MyComponent = memo(function MyComponent(props) {
  return <div>{props.content}</div>;
});

// 2. 确认组件有正确的displayName
MyComponent.displayName = 'MyComponent';

// 3. 确认export的是memo包装后的组件
export default MyComponent; // ✅ 正确
// export default function MyComponent() {} // ❌ 错误

// 4. 检查是否有条件性memo（应该避免）
// ❌ 错误：条件性memo
const ConditionalMemo = shouldOptimize ? memo(Component) : Component;

// ✅ 正确：一致性memo
const Component = memo(function Component(props) {
  return shouldOptimize ? <OptimizedContent /> : <RegularContent />;
});`,
                solution: '确保memo正确包装组件，且导出的是包装后的组件',
                tips: [
                  '使用React DevTools确认组件显示为memo类型',
                  '检查import/export语句是否正确',
                  '避免运行时条件性地应用memo'
                ]
              },
              {
                title: '第二步：分析props稳定性',
                description: '系统性检查传入memo组件的props是否稳定',
                code: `// Props稳定性检查工具
function analyzePropsStability(componentName, props) {
  const propsAnalysis = {};
  
  Object.keys(props).forEach(key => {
    const value = props[key];
    const type = typeof value;
    
    propsAnalysis[key] = {
      type,
      isFunction: type === 'function',
      isObject: type === 'object' && value !== null,
      isArray: Array.isArray(value),
      hasToString: value && typeof value.toString === 'function',
      stringValue: type === 'object' ? '[Object]' : String(value).slice(0, 50)
    };
  });
  
  console.table(propsAnalysis);
  
  // 检查不稳定的props类型
  const unstableProps = Object.keys(propsAnalysis).filter(key => {
    const analysis = propsAnalysis[key];
    return analysis.isFunction || analysis.isObject || analysis.isArray;
  });
  
  if (unstableProps.length > 0) {
    console.warn(componentName + ' 有可能不稳定的props:', unstableProps);
  }
}

// 使用示例
function AnalyzedComponent(props) {
  if (process.env.NODE_ENV === 'development') {
    analyzePropsStability('AnalyzedComponent', props);
  }
  
  return <div>{/* 组件内容 */}</div>;
}`,
                solution: '开发工具检查props的类型和稳定性',
                tips: [
                  '重点关注object、function、array类型的props',
                  '使用浅比较工具验证props引用是否变化',
                  '建立props稳定性的检查清单'
                ]
              },
              {
                title: '第三步：验证比较函数逻辑',
                description: '如果使用了自定义比较函数，验证其逻辑正确性',
                code: `// 比较函数测试工具
function testCompareFunction(compareFunction, testCases) {
  console.log('测试比较函数...');
  
  testCases.forEach((testCase, index) => {
    const { prevProps, nextProps, expectedResult, description } = testCase;
    const actualResult = compareFunction(prevProps, nextProps);
    
    const isCorrect = actualResult === expectedResult;
    
    console.log('测试用例 #' + (index + 1) + ':', {
      description,
      expected: expectedResult,
      actual: actualResult,
      status: isCorrect ? '✅ 通过' : '❌ 失败'
    });
    
    if (!isCorrect) {
      console.warn('比较函数逻辑可能有误');
    }
  });
}

// 使用示例
const myCompareFunction = (prevProps, nextProps) => {
  return prevProps.id === nextProps.id;
};

const testCases = [
  {
    prevProps: { id: 1, name: 'John' },
    nextProps: { id: 1, name: 'Jane' },
    expectedResult: true,  // id相同，应该跳过渲染
    description: '相同id，不同name'
  },
  {
    prevProps: { id: 1, name: 'John' },
    nextProps: { id: 2, name: 'John' },
    expectedResult: false, // id不同，应该重新渲染
    description: '不同id，相同name'
  }
];

testCompareFunction(myCompareFunction, testCases);`,
                solution: '编写测试用例验证自定义比较函数的逻辑',
                tips: [
                  '覆盖各种props变化的场景',
                  '测试边界情况，如undefined、null值',
                  '验证比较函数的性能表现'
                ]
              }
            ]
          },
          {
            title: '性能回归排查',
            description: '当memo性能出现回归时的排查方法',
            items: [
              {
                title: '性能基线对比',
                description: '建立性能基线，定期对比检查性能回归',
                code: `// 性能基线记录工具
class PerformanceBaseline {
  static baselines = new Map();
  
  static recordBaseline(componentName, metrics) {
    this.baselines.set(componentName, {
      ...metrics,
      timestamp: Date.now(),
      version: process.env.REACT_APP_VERSION || 'unknown'
    });
  }
  
  static compareWithBaseline(componentName, currentMetrics) {
    const baseline = this.baselines.get(componentName);
    
    if (!baseline) {
      console.warn('没有找到' + componentName + '的性能基线');
      return;
    }
    
    const comparison = {
      renderTime: {
        baseline: baseline.renderTime,
        current: currentMetrics.renderTime,
        change: ((currentMetrics.renderTime - baseline.renderTime) / baseline.renderTime * 100).toFixed(1) + '%'
      },
      memoHitRate: {
        baseline: baseline.memoHitRate,
        current: currentMetrics.memoHitRate,
        change: (currentMetrics.memoHitRate - baseline.memoHitRate).toFixed(1) + '%'
      }
    };
    
    console.table(comparison);
    
    // 检查是否有显著回归
    if (currentMetrics.renderTime > baseline.renderTime * 1.2) {
      console.error('⚠️ 渲染性能回归超过20%');
    }
    
    if (currentMetrics.memoHitRate < baseline.memoHitRate - 10) {
      console.error('⚠️ memo命中率下降超过10%');
    }
  }
}

// 使用示例
// 记录基线
PerformanceBaseline.recordBaseline('UserList', {
  renderTime: 15.2,
  memoHitRate: 85.3
});

// 对比当前性能
PerformanceBaseline.compareWithBaseline('UserList', {
  renderTime: 18.7,
  memoHitRate: 78.1
});`,
                solution: '建立性能监控体系，及时发现性能回归',
                tips: [
                  '在CI/CD流程中集成性能测试',
                  '设置性能回归的告警阈值',
                  '记录每次发布的性能基线'
                ]
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;