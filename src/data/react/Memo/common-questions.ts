import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'memo-not-working',
    question: 'React.memo不起作用，组件仍然重新渲染',
    answer: `**问题根因**：React.memo默认进行浅比较，当props包含内联对象、函数或数组时，引用地址不同会导致比较失败。

**解决方案**：
1. 使用useCallback包装函数props
2. 使用useMemo包装对象和数组props  
3. 避免在JSX中直接写内联对象、函数、数组
4. 考虑使用自定义比较函数

**预防措施**：
- 建立代码规范，避免内联prop的使用
- 使用ESLint规则检查内联prop
- 在开发环境中添加渲染计数器监控`,
    code: `// ❌ 错误做法 - 导致memo失效
function ParentComponent() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <button onClick={() => setCount(count + 1)}>
        Count: {count}
      </button>
      
      {/* 内联对象 - 每次都是新引用 */}
      <MemoChild 
        style={{color: 'red', fontSize: '14px'}}
        data={[1, 2, 3]}
        onClick={() => console.log('clicked')}
      />
    </div>
  );
}

// ✅ 正确做法 - memo正常工作
function ParentComponent() {
  const [count, setCount] = useState(0);
  
  // 使用useMemo缓存对象props
  const style = useMemo(() => ({
    color: 'red', 
    fontSize: '14px'
  }), []);
  
  const data = useMemo(() => [1, 2, 3], []);
  
  // 使用useCallback包装函数
  const handleClick = useCallback(() => {
    console.log('clicked');
  }, []);
  
  return (
    <div>
      <button onClick={() => setCount(count + 1)}>
        Count: {count}
      </button>
      
      {/* 现在memo可以正常工作 */}
      <MemoChild 
        style={style}
        data={data}
        onClick={handleClick}
      />
    </div>
  );
}`,
    tags: ['memo失效', 'props比较', 'useCallback', 'useMemo'],
    relatedQuestions: [
      'useCallback依赖数组不正确导致函数重新创建',
      'useMemo依赖数组缺失导致值重新计算',
      '复杂对象的深层比较问题'
    ]
  },

  {
    id: 'custom-compare-function-error',
    question: '自定义比较函数返回值搞反了，导致组件过度渲染',
    answer: `**问题根因**：React.memo的areEqual函数与类组件的shouldComponentUpdate返回值含义相反。

**返回值含义**：
- areEqual返回true：props相等，跳过重新渲染
- areEqual返回false：props不相等，需要重新渲染
- shouldComponentUpdate相反：true=更新，false=不更新

**解决方案**：
1. 确认areEqual函数的返回值逻辑
2. 修正比较逻辑，返回正确的boolean值
3. 添加调试日志验证比较结果
4. 测试验证组件渲染行为

**记忆技巧**：areEqual的语义就是"相等"，相等返回true表示不需要重新渲染`,
    code: `// ❌ 错误做法 - 返回值逻辑错误
const MemoComponent = memo(MyComponent, (prevProps, nextProps) => {
  // 错误：这里的逻辑是shouldComponentUpdate的逻辑
  if (prevProps.userId !== nextProps.userId) {
    return true; // ❌ 认为需要更新时返回true，实际应该返回false
  }
  
  if (prevProps.name !== nextProps.name) {
    return true; // ❌ 错误的返回值
  }
  
  return false; // ❌ 认为不需要更新时返回false，实际应该返回true
});

// ✅ 正确做法 - 返回值逻辑正确
const MemoComponent = memo(MyComponent, (prevProps, nextProps) => {
  // 正确：areEqual函数返回props是否相等
  const isUserIdEqual = prevProps.userId === nextProps.userId;
  const isNameEqual = prevProps.name === nextProps.name;
  
  // 返回true表示props相等，不需要重新渲染
  return isUserIdEqual && isNameEqual;
});

// 🚀 最佳实践 - 更清晰的实现
const MemoComponent = memo(MyComponent, (prevProps, nextProps) => {
  // 明确的变量命名，表达语义
  const propsAreEqual = 
    prevProps.userId === nextProps.userId &&
    prevProps.name === nextProps.name;
  
  // 添加调试日志（开发环境）
  if (process.env.NODE_ENV === 'development') {
    console.log('Props comparison result:', propsAreEqual);
  }
  
  return propsAreEqual; // true = 相等不渲染，false = 不等要渲染
});`,
    tags: ['自定义比较', '返回值错误', 'shouldComponentUpdate'],
    relatedQuestions: [
      'shouldComponentUpdate与areEqual的区别',
      '比较函数性能开销过大',
      '复杂props的比较策略'
    ]
  },

  {
    id: 'memo-with-context',
    question: 'memo组件仍然会因为Context变化而重新渲染',
    answer: `**问题根因**：React.memo只能优化props的变化，无法阻止Context变化引起的重新渲染。

**技术原理**：当组件使用useContext Hook时，Context值的任何变化都会导致组件重新渲染，即使组件被memo包装。这是React的设计机制：Context的订阅是在组件内部进行的，memo只能在组件外部进行props比较。

**解决方案**：
1. 拆分Context，只传递组件需要的数据
2. 使用useMemo包装Context value
3. 将不常变化的数据提升到Context之外
4. 考虑使用状态管理库替代Context

**架构优化**：
- 细粒度Context设计
- Context Provider的合理嵌套
- 避免在高频更新的Context中放置静态数据`,
    code: `// ❌ 错误做法 - 所有数据放在一个Context中
const AppContext = createContext();

function App() {
  const [user, setUser] = useState(null);
  const [theme, setTheme] = useState('light');
  const [count, setCount] = useState(0); // 频繁变化的数据
  
  // 每次count变化，所有使用Context的组件都会重新渲染
  const value = { user, theme, count, setUser, setTheme, setCount };
  
  return (
    <AppContext.Provider value={value}>
      <Header /> {/* 只需要user和theme，但count变化也会重新渲染 */}
      <Counter /> {/* 需要count */}
    </AppContext.Provider>
  );
}

// memo无法阻止Context变化引起的重新渲染
const Header = memo(function Header() {
  const { user, theme } = useContext(AppContext);
  return <div>{user?.name} - {theme}</div>;
});

// ✅ 正确做法 - Context分层设计
const UserContext = createContext();
const ThemeContext = createContext();
const CountContext = createContext();

function App() {
  const [user, setUser] = useState(null);
  const [theme, setTheme] = useState('light');
  const [count, setCount] = useState(0);
  
  // 分层Context，减少不必要的重新渲染
  const userValue = useMemo(() => ({ user, setUser }), [user]);
  const themeValue = useMemo(() => ({ theme, setTheme }), [theme]);
  const countValue = useMemo(() => ({ count, setCount }), [count]);
  
  return (
    <UserContext.Provider value={userValue}>
      <ThemeContext.Provider value={themeValue}>
        <CountContext.Provider value={countValue}>
          <Header /> {/* 只会因为user或theme变化而重新渲染 */}
          <Counter /> {/* 只会因为count变化而重新渲染 */}
        </CountContext.Provider>
      </ThemeContext.Provider>
    </UserContext.Provider>
  );
}

// 现在Header只订阅需要的Context
const Header = memo(function Header() {
  const { user } = useContext(UserContext);
  const { theme } = useContext(ThemeContext);
  return <div>{user?.name} - {theme}</div>;
});`,
    tags: ['Context', 'useContext', '重新渲染', '架构设计'],
    relatedQuestions: [
      'Context value频繁重新创建',
      'useContext与props的性能对比',
      '状态管理库的选择'
    ]
  },

  {
    id: 'memo-performance-overhead',
    question: '过度使用memo导致性能反而变差',
    answer: `**问题根因**：memo本身有一定的开销，对于简单组件或props经常变化的组件，memo的开销可能超过其带来的收益。

**memo的开销包括**：
1. props比较的计算成本
2. 额外的内存占用（存储上一次的props）
3. 函数调用的开销

**解决策略**：
1. 移除简单组件上不必要的memo
2. 使用React DevTools Profiler测量实际性能
3. 对比使用memo前后的渲染时间
4. 分析memo的命中率（跳过渲染的比例）

**使用原则**：
- 只在确实需要的地方使用memo
- 监控memo的实际效果
- 定期清理无效的性能优化
- 优先解决真正的性能瓶颈`,
    code: `// ❌ 错误做法 - 过度使用memo
// 简单组件不需要memo
const SimpleText = memo(function SimpleText({ text }) {
  return <span>{text}</span>; // 渲染成本极低
});

// props经常变化的组件memo效果差
const Clock = memo(function Clock({ time }) {
  return <div>{time}</div>; // time每秒都变化，memo几乎不会生效
});

// 复杂比较函数可能比重新渲染更昂贵
const ExpensiveCompare = memo(function ExpensiveCompare({ data }) {
  return <div>{data.title}</div>;
}, (prevProps, nextProps) => {
  // 昂贵的深度比较
  return JSON.stringify(prevProps.data) === JSON.stringify(nextProps.data);
});

// ✅ 正确做法 - 合理使用memo
// 简单组件不使用memo
function SimpleText({ text }) {
  return <span>{text}</span>;
}

// 高频变化的组件不使用memo
function Clock({ time }) {
  return <div>{time}</div>;
}

// 只对真正需要的组件使用memo
const ExpensiveChart = memo(function ExpensiveChart({ data, options }) {
  // 复杂的图表渲染逻辑
  const chartData = processChartData(data, options);
  return <Canvas data={chartData} />;
});

// 使用简单有效的比较
const UserCard = memo(function UserCard({ user }) {
  return (
    <div>
      <h3>{user.name}</h3>
      <p>{user.email}</p>
    </div>
  );
}, (prevProps, nextProps) => {
  // 简单比较，只比较关键字段
  return prevProps.user.id === nextProps.user.id &&
         prevProps.user.lastModified === nextProps.user.lastModified;
});`,
    tags: ['性能开销', '过度优化', '性能测量'],
    relatedQuestions: [
      '如何测量memo的实际效果',
      '性能优化的投资回报率',
      '组件渲染成本的评估方法'
    ]
  },

  {
    id: 'memo-with-children',
    question: 'memo组件使用children时总是重新渲染',
    answer: `**问题根因**：React中的children每次渲染时都会创建新的元素对象，导致memo的浅比较失效。

**技术原理**：当在JSX中写<Component><div>content</div></Component>时，每次渲染都会创建新的React元素对象。即使内容完全相同，元素的引用地址不同，导致memo认为props发生了变化。这是React的设计特性：JSX会被编译为React.createElement调用，每次调用都返回新对象。

**解决方案**：
1. 将children提取为变量，避免每次渲染时重新创建
2. 使用useMemo缓存复杂的children结构
3. 考虑重构组件设计，减少对children的依赖
4. 使用render props或其他模式替代children

**设计模式**：
- 使用useMemo缓存children
- 采用compound component模式
- 考虑使用配置对象替代children`,
    code: `// ❌ 错误做法 - children每次都是新对象
function Parent() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <button onClick={() => setCount(count + 1)}>
        Count: {count}
      </button>
      
      {/* 每次渲染都创建新的children */}
      <MemoWrapper>
        <div>
          <h3>标题</h3>
          <p>内容</p>
        </div>
      </MemoWrapper>
    </div>
  );
}

// ✅ 正确做法 - 缓存children
function Parent() {
  const [count, setCount] = useState(0);
  
  // 使用useMemo缓存children
  const memoizedChildren = useMemo(() => (
    <div>
      <h3>标题</h3>
      <p>内容</p>
    </div>
  ), []);
  
  return (
    <div>
      <button onClick={() => setCount(count + 1)}>
        Count: {count}
      </button>
      
      {/* 现在children引用稳定 */}
      <MemoWrapper>
        {memoizedChildren}
      </MemoWrapper>
    </div>
  );
}

// 🚀 最佳实践 - 避免使用children
function Parent() {
  const [count, setCount] = useState(0);
  
  // 使用props传递内容配置
  const contentConfig = useMemo(() => ({
    title: '标题',
    content: '内容'
  }), []);
  
  return (
    <div>
      <button onClick={() => setCount(count + 1)}>
        Count: {count}
      </button>
      
      {/* 使用配置对象，避免children问题 */}
      <MemoWrapper config={contentConfig} />
    </div>
  );
}`,
    tags: ['children', 'React.createElement', '组件设计'],
    relatedQuestions: [
      'render props与children的性能对比',
      'compound component模式的使用',
      'React.createElement的性能考虑'
    ]
  },

  {
    id: 'memo-typescript-issues',
    question: 'TypeScript中memo组件的类型定义问题',
    answer: `**问题根因**：TypeScript的类型系统与React.memo的集成在某些复杂场景下可能需要显式的类型注解。

**主要问题**：
1. memo组件的返回类型不明确
2. props类型的继承问题
3. ref转发的类型处理
4. 自定义比较函数的类型定义

**解决方案**：
1. 使用React.ComponentProps提取原组件的props类型
2. 定义明确的props接口
3. 使用React.NamedExoticComponent作为返回类型
4. 处理ref转发的类型定义

**类型定义最佳实践**：
- 为所有memo组件定义明确的类型
- 使用统一的类型定义模式
- 在团队中建立TypeScript最佳实践`,
    code: `// ❌ 错误做法 - 类型定义不清晰
const MemoComponent = memo((props: any) => {
  return <div>{props.content}</div>;
});

// 丢失了类型信息
const result = <MemoComponent content="test" extra="unexpected" />;

// ✅ 正确做法 - 完整的类型定义
interface ComponentProps {
  content: string;
  optional?: number;
}

const MemoComponent = memo<ComponentProps>(function MyComponent(props) {
  return <div>{props.content}</div>;
});

// 🚀 最佳实践 - 完整的类型系统
interface UserCardProps {
  user: {
    id: string;
    name: string;
    email: string;
  };
  onEdit?: (id: string) => void;
}

// 使用明确的函数声明
const UserCard = memo<UserCardProps>(function UserCard({ user, onEdit }) {
  return (
    <div>
      <h3>{user.name}</h3>
      <p>{user.email}</p>
      {onEdit && (
        <button onClick={() => onEdit(user.id)}>编辑</button>
      )}
    </div>
  );
});

// 自定义比较函数的类型
const UserCardWithCustomCompare = memo<UserCardProps>(
  UserCard,
  (prevProps, nextProps) => {
    return prevProps.user.id === nextProps.user.id &&
           prevProps.user.name === nextProps.user.name;
  }
);`,
    tags: ['TypeScript', '类型定义', 'React.FC'],
    relatedQuestions: [
      'ref转发与memo的类型兼容',
      '泛型组件的memo包装',
      'React.FC与memo的类型冲突'
    ]
  }
];

export default commonQuestions;