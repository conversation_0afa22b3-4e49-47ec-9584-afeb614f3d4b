import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ReactMemoData: ApiItem = {
  id: 'Memo',
  title: 'React.memo',
  description: 'React.memo是React中用于优化函数组件性能的高阶组件，通过浅比较props来避免不必要的重新渲染，是性能优化的核心工具。',
  category: 'React Components',
  difficulty: 'medium',
  
  syntax: `// 基础语法
const MemoizedComponent = React.memo(Component);

// 带自定义比较函数的语法
const MemoizedComponent = React.memo(Component, areEqual);`,
  example: `import React, { useState, memo } from 'react';

// 示例组件：用户信息卡片
const UserCard = memo(function UserCard({ user, theme }) {
  console.log('UserCard 正在渲染:', user.name);
  
  return (
    <div className={"card theme-" + theme}>
      <h3>{user.name}</h3>
      <p>邮箱：{user.email}</p>
      <p>年龄：{user.age}</p>
    </div>
  );
});`,
  notes: '只进行props的浅比较，对于深层嵌套对象的变化无法检测',
  
  version: 'React 16.6.0+',
  tags: ["React","Component","性能优化","HOC"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ReactMemoData;