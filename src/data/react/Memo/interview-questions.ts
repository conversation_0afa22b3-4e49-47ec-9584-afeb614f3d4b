import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 'memo-basic-concept',
    question: 'React.memo是什么？它解决了什么问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'React.memo是用于优化函数组件性能的高阶组件，通过浅比较props来跳过不必要的重新渲染。',
      detailed: `**核心概念**：React.memo是React 16.6版本引入的高阶组件，专门用于优化函数组件的渲染性能。它的工作原理类似于类组件中的PureComponent。

**解决的问题**：
1. 函数组件无法像类组件那样通过继承PureComponent来自动进行性能优化
2. 父组件的重新渲染会导致所有子组件都重新渲染，即使props没有变化
3. 复杂组件的不必要重新渲染会影响应用性能

**主要优势**：
1. 自动进行props的浅比较，跳过不必要的渲染
2. 使用简单，只需要包装一下函数组件即可
3. 支持自定义比较函数，满足特殊需求
4. 不改变组件的API，对外部使用者透明

**使用场景**：特别适用于渲染成本较高的组件、props变化频率较低的组件，以及在大型列表中的子组件优化。

**注意事项**：只进行浅比较，对于深层嵌套的对象或数组props变化无法检测到，需要配合useCallback和useMemo使用。`,
      code: `import React, { memo } from 'react';

// 基础使用方式
const UserCard = memo(function UserCard({ user, onEdit }) {
  console.log('UserCard 渲染'); // 用于观察渲染次数
  
  return (
    <div className="user-card">
      <h3>{user.name}</h3>
      <p>{user.email}</p>
      <button onClick={() => onEdit(user.id)}>编辑</button>
    </div>
  );
});

// 使用示例：即使父组件重新渲染，如果user和onEdit没变，UserCard不会重新渲染
function UserList() {
  const [refresh, setRefresh] = useState(0);
  const users = [
    { id: 1, name: '张三', email: '<EMAIL>' }
  ];
  
  const handleEdit = useCallback((id) => {
    console.log('编辑用户:', id);
  }, []);

  return (
    <div>
      <button onClick={() => setRefresh(r => r + 1)}>
        刷新页面 ({refresh})
      </button>
      {users.map(user => (
        <UserCard key={user.id} user={user} onEdit={handleEdit} />
      ))}
    </div>
  );
}`
    },
    tags: ['基础概念', '性能优化', 'HOC'],
    companies: ['字节跳动', '阿里巴巴', '腾讯', '美团']
  },

  {
    id: 'memo-vs-usememo',
    question: 'React.memo和useMemo有什么区别？什么时候使用哪个？',
    difficulty: 'medium',
    frequency: 'high',
    category: '对比分析',
    answer: {
      brief: 'React.memo优化组件级别的重新渲染，useMemo优化值级别的重新计算，两者解决不同层面的性能问题。',
      detailed: `**核心区别**：

**React.memo（组件级优化）**：
- 优化整个组件的重新渲染
- 通过比较props决定是否重新执行组件函数
- 是高阶组件，包装在组件外部
- 适用于组件渲染成本较高的场景

**useMemo（值级优化）**：
- 优化昂贵计算的重新执行
- 通过依赖数组决定是否重新计算值
- 是Hook，在组件内部使用
- 适用于计算成本较高的场景

**使用场景对比**：

| 场景 | React.memo | useMemo |
|------|------------|---------|
| 复杂列表项 | ✅ 包装整个列表项组件 | ❌ 无法避免组件重新渲染 |
| 昂贵计算 | ❌ 无法避免内部计算 | ✅ 缓存计算结果 |
| 对象prop | ❌ 浅比较会失效 | ✅ 可以缓存对象引用 |
| 简单组件 | ❌ 得不偿失 | ❌ 不需要优化 |

**最佳实践**：
1. 两者经常配合使用：useMemo缓存传给memo组件的复杂props
2. 先考虑memo，再考虑useMemo
3. 不要过度优化，优先解决真正的性能瓶颈`,
      code: `import React, { memo, useMemo, useState } from 'react';

// 使用memo优化组件级别的重新渲染
const ExpensiveList = memo(function ExpensiveList({ items, filter }) {
  console.log('ExpensiveList 渲染');
  
  // 使用useMemo优化昂贵的过滤计算
  const filteredItems = useMemo(() => {
    console.log('执行过滤计算');
    return items.filter(item => 
      item.name.toLowerCase().includes(filter.toLowerCase())
    );
  }, [items, filter]); // 只有items或filter变化时才重新计算

  return (
    <ul>
      {filteredItems.map(item => (
        <li key={item.id}>{item.name}</li>
      ))}
    </ul>
  );
});

function App() {
  const [items] = useState([
    { id: 1, name: 'Apple' },
    { id: 2, name: 'Banana' }
  ]);
  const [filter, setFilter] = useState('');
  const [count, setCount] = useState(0);

  // 使用useMemo缓存传给memo组件的props，避免memo失效
  const memoizedItems = useMemo(() => items, [items]);

  return (
    <div>
      <button onClick={() => setCount(c => c + 1)}>
        Count: {count}
      </button>
      <input 
        value={filter}
        onChange={(e) => setFilter(e.target.value)}
        placeholder="过滤商品"
      />
      {/* memo + useMemo 的完美配合 */}
      <ExpensiveList items={memoizedItems} filter={filter} />
    </div>
  );
}`,
      followUp: [
        '那么useCallback和这两者的关系是什么？',
        '在什么情况下memo会失效？如何避免？',
        '如何测量这些优化的实际效果？'
      ]
    },
    tags: ['对比分析', 'useMemo', '性能优化'],
    companies: ['字节跳动', '小红书', '滴滴']
  },

  {
    id: 'memo-custom-compare',
    question: 'React.memo的自定义比较函数是如何工作的？什么时候需要使用？',
    difficulty: 'medium',
    frequency: 'medium',
    category: '实现原理',
    answer: {
      brief: '自定义比较函数用于替代默认的浅比较逻辑，当默认比较无法满足需求时可以自定义比较策略。',
      detailed: `**工作原理**：React.memo的第二个参数是可选的比较函数areEqual，它接收prevProps和nextProps两个参数，返回boolean值。返回true表示props相等（不重新渲染），返回false表示props不等（重新渲染）。

**默认行为 vs 自定义比较**：
- 默认：使用Object.is对每个prop进行浅比较
- 自定义：完全由开发者控制比较逻辑

**使用场景**：
1. **深层对象比较**：当props包含深层嵌套对象时
2. **忽略特定属性**：某些props变化时不需要重新渲染  
3. **特殊比较逻辑**：如日期对象、函数等特殊类型的比较
4. **性能权衡**：在比较成本和渲染成本之间找平衡

**注意事项**：
- 比较函数应该是纯函数，无副作用
- 复杂的比较逻辑可能比重新渲染更昂贵
- 返回值的含义与shouldComponentUpdate相反
- 避免在比较函数中进行异步操作`,
      code: `import React, { memo } from 'react';

// 场景1：忽略函数prop的变化
const ToolbarButton = memo(function ToolbarButton({ 
  label, 
  icon, 
  onClick, 
  disabled 
}) {
  return (
    <button onClick={onClick} disabled={disabled}>
      {icon} {label}
    </button>
  );
}, (prevProps, nextProps) => {
  // 只比较label、icon、disabled，忽略onClick函数的引用变化
  return prevProps.label === nextProps.label &&
         prevProps.icon === nextProps.icon &&
         prevProps.disabled === nextProps.disabled;
});

// 场景2：深层对象比较
const UserProfile = memo(function UserProfile({ user, settings }) {
  return (
    <div>
      <h2>{user.profile.name}</h2>
      <p>主题: {settings.theme}</p>
    </div>
  );
}, (prevProps, nextProps) => {
  // 自定义深层比较逻辑
  const userEqual = prevProps.user.profile.name === nextProps.user.profile.name &&
                   prevProps.user.profile.email === nextProps.user.profile.email;
  
  const settingsEqual = prevProps.settings.theme === nextProps.settings.theme;
  
  return userEqual && settingsEqual;
});

// 场景3：复杂比较逻辑
const ProductCard = memo(function ProductCard({ 
  product, 
  onAddToCart, 
  timestamp 
}) {
  return (
    <div>
      <h3>{product.name}</h3>
      <p>价格: ¥{product.price}</p>
      <button onClick={() => onAddToCart(product.id)}>
        加入购物车
      </button>
    </div>
  );
}, (prevProps, nextProps) => {
  // 复杂比较：只有产品核心信息变化才重新渲染
  const productEqual = prevProps.product.id === nextProps.product.id &&
                      prevProps.product.name === nextProps.product.name &&
                      prevProps.product.price === nextProps.product.price;
  
  // 忽略timestamp和onAddToCart的变化
  return productEqual;
});

// ❌ 错误示例：昂贵的深度比较
const BadExample = memo(function BadExample({ data }) {
  return <div>{data.content}</div>;
}, (prevProps, nextProps) => {
  // 这种深度比较可能比重新渲染更昂贵
  return JSON.stringify(prevProps.data) === JSON.stringify(nextProps.data);
});`,
      followUp: [
        '自定义比较函数的性能开销如何评估？',
        '为什么返回值含义与shouldComponentUpdate相反？',
        '在什么情况下自定义比较会适得其反？'
      ]
    },
    tags: ['自定义比较', '深度比较', '性能权衡'],
    companies: ['滴滴', '美团', '京东']
  },

  {
    id: 'memo-performance-pitfalls',
    question: '在使用React.memo时容易遇到哪些性能陷阱？如何避免？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '性能优化',
    answer: {
      brief: '主要陷阱包括内联对象、函数props、过度使用memo等，需要配合useCallback、useMemo正确使用。',
      detailed: `**常见性能陷阱**：

**1. 内联对象/数组导致memo失效**
错误做法：每次都创建新对象
正确做法：提取到外部或使用useMemo

**2. 函数props引用变化**
错误做法：每次渲染都创建新函数
正确做法：使用useCallback

**3. 过度使用memo**
- 对简单组件使用memo，检查成本可能超过渲染成本
- 在已经稳定的组件树中过度优化

**4. 错误的依赖管理**
- useCallback/useMemo的依赖数组不正确
- 导致闭包陷阱或过度重新计算

**避免策略**：
1. **性能测量优先**：使用React DevTools Profiler确认性能问题
2. **合理使用useCallback**：包装传给memo组件的函数
3. **对象prop优化**：使用useMemo缓存复杂对象
4. **避免过度优化**：不要给每个组件都加memo

**最佳实践**：
- 先识别性能瓶颈，再针对性优化
- memo + useCallback + useMemo 三者配合使用
- 定期检查优化效果，避免过度工程化`,
      code: `import React, { memo, useCallback, useMemo, useState } from 'react';

// ❌ 常见错误示例
function BadParent() {
  const [count, setCount] = useState(0);
  const [items, setItems] = useState([]);

  return (
    <div>
      <button onClick={() => setCount(count + 1)}>Count: {count}</button>
      
      {/* 陷阱1: 内联对象 - 每次都创建新对象 */}
      <MemoChild 
        user={{name: 'John', age: 25}} 
        styles={{color: 'red'}}
        onUpdate={() => console.log('update')}
      />
      
      {/* 陷阱2: 内联函数 - 每次都创建新函数 */}
      <MemoList 
        items={items}
        renderItem={(item) => <span>{item.name}</span>}
      />
    </div>
  );
}

// ✅ 正确优化示例
function GoodParent() {
  const [count, setCount] = useState(0);
  const [items, setItems] = useState([]);

  // 使用useMemo缓存对象props
  const user = useMemo(() => ({
    name: 'John', 
    age: 25
  }), []); // 空依赖数组，因为值不会变

  const styles = useMemo(() => ({
    color: 'red'
  }), []);

  // 使用useCallback缓存函数props
  const handleUpdate = useCallback(() => {
    console.log('update');
  }, []);

  const renderItem = useCallback((item) => (
    <span>{item.name}</span>
  ), []);

  return (
    <div>
      <button onClick={() => setCount(count + 1)}>Count: {count}</button>
      
      {/* 现在memo能正常工作 */}
      <MemoChild 
        user={user}
        styles={styles}
        onUpdate={handleUpdate}
      />
      
      <MemoList 
        items={items}
        renderItem={renderItem}
      />
    </div>
  );
}

// memo组件示例
const MemoChild = memo(function MemoChild({ user, styles, onUpdate }) {
  console.log('MemoChild 渲染');
  return (
    <div style={styles}>
      <p>{user.name} - {user.age}</p>
      <button onClick={onUpdate}>更新</button>
    </div>
  );
});

const MemoList = memo(function MemoList({ items, renderItem }) {
  console.log('MemoList 渲染');
  return (
    <ul>
      {items.map(item => (
        <li key={item.id}>{renderItem(item)}</li>
      ))}
    </ul>
  );
});`,
      followUp: [
        '如何使用React DevTools Profiler来测量memo的效果？',
        '什么时候应该移除已有的memo优化？',
        '在大型应用中如何系统性地进行性能优化？'
      ]
    },
    tags: ['性能陷阱', 'useCallback', 'useMemo', '最佳实践'],
    companies: ['字节跳动', '阿里巴巴', '美团', '小红书']
  },

  {
    id: 'memo-real-world-application',
    question: '在大型React应用中，如何系统性地使用React.memo进行性能优化？',
    difficulty: 'hard',
    frequency: 'low',
    category: '架构设计',
    answer: {
      brief: '需要建立性能优化策略，包括组件分层、测量工具、优化原则和团队规范，避免过度优化。',
      detailed: `**系统性优化策略**：

**1. 性能瓶颈识别**
- 使用React DevTools Profiler识别渲染热点
- 设置性能预算和监控指标
- 建立性能回归检测机制

**2. 组件分层优化**
- 页面级组件：通常不需要memo
- 功能模块组件：选择性使用memo
- 基础UI组件：重点优化目标
- 列表项组件：几乎总是需要memo

**3. 优化决策树**
1. 组件渲染频率高吗？→ 否：不优化
2. 组件渲染成本高吗？→ 否：谨慎优化  
3. props变化频率低吗？→ 否：优化效果有限
4. 有性能瓶颈证据吗？→ 是：优化

**4. 团队规范建立**
- 定义memo使用标准和检查清单
- 建立代码审查流程
- 制定性能测试要求
- 培训团队成员性能优化意识

**5. 工具链集成**
- ESLint规则检查memo使用
- 性能监控和报警
- 自动化性能测试

**长期维护**：
- 定期性能审查和优化评估
- 移除过时或无效的优化
- 跟踪优化效果和性能指标`,
      code: `// 大型应用中的memo使用策略示例

// 1. 基础UI组件库 - 统一memo包装
const Button = memo(function Button({ 
  children, 
  onClick, 
  variant = 'primary',
  disabled = false 
}) {
  return (
    <button 
      className={"btn btn-" + variant}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
});

// 2. 复杂业务组件 - 选择性优化
const UserDashboard = memo(function UserDashboard({ 
  user, 
  permissions, 
  onAction 
}) {
  // 只在用户信息或权限变化时重新渲染
  console.log('UserDashboard 渲染');
  
  return (
    <div className="dashboard">
      <UserProfile user={user} />
      <ActionPanel permissions={permissions} onAction={onAction} />
    </div>
  );
}, (prevProps, nextProps) => {
  return prevProps.user.id === nextProps.user.id &&
         prevProps.user.lastModified === nextProps.user.lastModified &&
         JSON.stringify(prevProps.permissions) === JSON.stringify(nextProps.permissions);
});

// 3. 列表项组件 - 重点优化
const TodoItem = memo(function TodoItem({ 
  todo, 
  onToggle, 
  onDelete, 
  onEdit 
}) {
  const handleToggle = useCallback(() => {
    onToggle(todo.id);
  }, [todo.id, onToggle]);

  const handleDelete = useCallback(() => {
    onDelete(todo.id);
  }, [todo.id, onDelete]);

  const handleEdit = useCallback((newText) => {
    onEdit(todo.id, newText);
  }, [todo.id, onEdit]);

  return (
    <div className="todo-item">
      <input 
        type="checkbox"
        checked={todo.completed}
        onChange={handleToggle}
      />
      <EditableText 
        text={todo.text}
        onSave={handleEdit}
      />
      <button onClick={handleDelete}>删除</button>
    </div>
  );
});

// 4. 性能监控工具
class PerformanceMonitor {
  static measureRenderTime(componentName, renderFn) {
    const start = performance.now();
    const result = renderFn();
    const end = performance.now();
    
    if (end - start > 16) { // 超过一帧的时间
      console.warn(componentName + ' 渲染时间过长:', end - start, 'ms');
    }
    
    return result;
  }
  
  static trackMemoEffectiveness(componentName, wasRerendered) {
    // 统计memo的命中率
    this.memoStats = this.memoStats || {};
    this.memoStats[componentName] = this.memoStats[componentName] || { hits: 0, misses: 0 };
    
    if (wasRerendered) {
      this.memoStats[componentName].misses++;
    } else {
      this.memoStats[componentName].hits++;
    }
  }
}`,
      followUp: [
        '如何建立React应用的性能监控体系？',
        '什么情况下应该移除已有的memo优化？',
        '如何在代码审查中评估性能优化的质量？'
      ]
    },
    tags: ['架构设计', '性能策略', '团队协作', '大型应用'],
    companies: ['字节跳动', '阿里巴巴', '美团', 'Shopee']
  }
];

export default interviewQuestions;