import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {

  definition: "React.memo是React中用于优化函数组件性能的高阶组件，通过浅比较props来避免不必要的重新渲染，是性能优化的核心工具。",
  
  introduction: `React.memo是React 16.6版本中引入的高阶组件，用于解决函数组件无法像类组件那样使用PureComponent进行性能优化的问题。

它遵循"智能缓存"的设计理念，在性能和内存使用之间做出了合理的权衡选择。通过浅比较props的变化来决定是否重新渲染组件，有效减少了不必要的渲染操作。

主要用于渲染开销较大的组件、props变化频率较低的组件和列表项组件的优化。相比手动优化和useMemo的组合方案，它的创新在于将性能优化封装为简洁的高阶组件形式。

在React生态中，它是性能优化层的核心组件，常见于企业级应用和复杂UI系统，特别适合大型列表、数据密集型界面和高频更新场景。

核心优势包括使用简单、性能提升明显，但也需要注意浅比较的限制和闭包陷阱的问题。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react/index.d.ts:800
 * - 实现文件：packages/react/src/Memo.js:20
 */

// 基础语法
const MemoizedComponent = React.memo(Component);

// 带自定义比较函数的语法
const MemoizedComponent = React.memo(Component, areEqual);

// TypeScript 完整语法
function memo<Props extends object>(
  Component: React.ComponentType<Props>,
  propsAreEqual?: (prevProps: Readonly<Props>, nextProps: Readonly<Props>) => boolean
): React.NamedExoticComponent<Props>;

// 泛型约束分析
/**
 * 泛型参数 Props 的约束：
 * - Props 必须继承 object 类型，确保是引用类型可以进行浅比较
 * - Component 必须是有效的 React 组件类型
 * - areEqual 函数的两个参数类型必须与 Props 一致
 * - 返回值为 NamedExoticComponent，保留组件的调试信息
 */

// 类型推导示例
const Button = (props: { label: string; onClick: () => void }) => <button onClick={props.onClick}>{props.label}</button>;
const MemoButton = React.memo(Button); // 类型推导为 React.NamedExoticComponent<{label: string; onClick: () => void}>

// 自定义比较函数示例
const MemoButtonWithCustom = React.memo(Button, (prevProps, nextProps) => {
  return prevProps.label === nextProps.label; // 只比较 label，忽略 onClick 的变化
});`,

  quickExample: `import React, { useState, memo } from 'react';

// 示例组件：用户信息卡片
const UserCard = memo(function UserCard({ user, theme }) {
  // 模拟渲染开销
  console.log('UserCard 正在渲染:', user.name);
  
  return (
    <div className={"card theme-" + theme}>
      <h3>{user.name}</h3>
      <p>邮箱：{user.email}</p>
      <p>年龄：{user.age}</p>
    </div>
  );
});

// 使用示例：父组件
function UserList() {
  const [users] = useState([
    { id: 1, name: '张三', email: '<EMAIL>', age: 25 },
    { id: 2, name: '李四', email: '<EMAIL>', age: 30 }
  ]);
  const [count, setCount] = useState(0);
  const [theme] = useState('light');

  return (
    <div>
      {/* 每次 count 变化时，UserCard 不会重新渲染（因为 user 和 theme 没变） */}
      <button onClick={() => setCount(count + 1)}>
        点击次数: {count}
      </button>
      
      {users.map(user => (
        <UserCard 
          key={user.id} 
          user={user} 
          theme={theme}
        />
      ))}
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "React.memo 核心应用场景",
      description: "React.memo在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用这个高阶组件进行性能优化",
      diagram: `graph LR
    A[React.memo核心场景] --> B[列表渲染优化]
    A --> C[组件缓存]
    A --> D[昂贵计算]

    B --> B1["📋 大型列表<br/>减少列表项重渲染"]
    B --> B2["🔄 动态列表<br/>优化增删改性能"]

    C --> C1["🎯 稳定组件<br/>props不常变化"]
    C --> C2["📦 重型组件<br/>渲染开销较大"]

    D --> D1["⚡ 复杂UI<br/>计算密集型组件"]
    D --> D2["📊 数据可视化<br/>图表和报表组件"]

    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "React.memo 技术实现架构",
      description: "React.memo的技术实现架构，展示其核心机制和与React内部系统的集成关系",
      diagram: `graph TB
    A[React.memo技术架构] --> B[比较层]
    A --> C[缓存层]
    A --> D[渲染层]

    B --> B1["🔍 浅比较机制<br/>Object.is比较"]
    B --> B2["⚙️ 自定义比较<br/>areEqual函数"]

    C --> C1["💾 组件缓存<br/>NamedExoticComponent"]
    C --> C2["🏷️ 标记系统<br/>_status标记"]

    D --> D1["⚡ 跳过渲染<br/>bailout机制"]
    D --> D2["🔄 强制更新<br/>props变化时"]

    style A fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    style B fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    style C fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    style D fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px`
    },
    {
      title: "React 性能优化生态系统",
      description: "React.memo在性能优化生态系统中的位置和与其他优化API的协作关系",
      diagram: `graph TD
    A[React性能优化生态] --> B[组件优化]
    A --> C[状态优化]
    A --> D[计算优化]

    B --> B1["React.memo<br/>组件记忆化"]
    B --> B2["PureComponent<br/>类组件优化"]
    B --> B3["React.forwardRef<br/>引用传递"]

    C --> C1["useState<br/>状态管理"]
    C --> C2["useCallback<br/>函数记忆化"]
    C --> C3["useContext<br/>上下文优化"]

    D --> D1["useMemo<br/>值记忆化"]
    D --> D2["React.lazy<br/>代码分割"]

    B1 -.-> C2
    B1 -.-> D1
    C2 -.-> D1

    style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
    style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style B1 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
    }
  ],
  
  parameters: [
    {
      name: "Component",
      type: "React.ComponentType<Props>",
      required: true,
      description: "需要被记忆化的React函数组件或类组件",
      designRationale: "支持任意React组件类型是为了保持API的通用性，无论是函数组件还是类组件都能被优化。这体现了React的一致性设计理念。",
      performanceImpact: "传入的组件将被包装为NamedExoticComponent，增加了一层抽象但提供了性能优化能力。包装本身的开销极小，但带来的性能收益通常远大于成本。",
      bestPractices: [
        "优先用于渲染开销较大的组件：memo(ExpensiveChart)",
        "适用于props变化频率较低的组件",
        "避免对过于简单的组件使用memo（如简单的div wrapper）",
        "确保组件的displayName清晰，便于调试"
      ],
      example: `// ✅ 推荐：渲染开销较大的组件
const ExpensiveChart = memo(function ExpensiveChart({ data }) {
  // 复杂的图表渲染逻辑
  return <Canvas>{/* 复杂渲染 */}</Canvas>;
});

// ✅ 推荐：props稳定的组件
const UserProfile = memo(function UserProfile({ user }) {
  return <div>{user.name}</div>;
});

// ❌ 不推荐：过于简单的组件
const SimpleDiv = memo(function SimpleDiv({ children }) {
  return <div>{children}</div>;
});`
    },
    {
      name: "propsAreEqual",
      type: "(prevProps: Props, nextProps: Props) => boolean",
      required: false,
      description: "可选的自定义比较函数，用于替代默认的浅比较逻辑",
      designRationale: "提供自定义比较函数是为了处理浅比较无法满足的特殊场景，如深层对象比较或特定字段的忽略。这种设计既保持了默认的高性能，又提供了灵活性。",
      performanceImpact: "自定义比较函数会在每次props变化时执行，因此函数本身的性能很重要。复杂的深度比较可能比重新渲染更昂贵，需要权衡比较成本和渲染成本。",
      bestPractices: [
        "比较函数应该是纯函数，无副作用",
        "避免在比较函数中进行深度比较，除非确实必要",
        "优先比较最可能变化的属性，提高比较效率",
        "返回true表示props相等（不重新渲染），返回false表示props不等（重新渲染）"
      ],
      example: `// ✅ 推荐：只比较关键属性
const MemoComponent = memo(MyComponent, (prevProps, nextProps) => {
  return prevProps.userId === nextProps.userId && 
         prevProps.isActive === nextProps.isActive;
});

// ✅ 推荐：忽略函数prop的变化
const MemoButton = memo(Button, (prevProps, nextProps) => {
  return prevProps.label === nextProps.label;
  // 忽略 onClick 函数的引用变化
});

// ❌ 避免：昂贵的深度比较
const MemoComponent = memo(MyComponent, (prevProps, nextProps) => {
  return JSON.stringify(prevProps) === JSON.stringify(nextProps);
});`
    }
  ],
  
  returnValue: {
    type: "React.NamedExoticComponent<Props>",
    description: "返回一个记忆化的组件，具有与原组件相同的API，但增加了性能优化能力",
    properties: [
      {
        name: "displayName",
        type: "string",
        description: "组件的显示名称，用于调试和开发工具"
      },
      {
        name: "$$typeof",
        type: "Symbol",
        description: "React内部标识符，标记为记忆化组件"
      }
    ],
    example: `const MemoComponent = React.memo(MyComponent);

// 返回的组件类型
console.log(MemoComponent.displayName); // "MyComponent"
console.log(MemoComponent.$$typeof); // Symbol(react.memo)

// 使用方式与原组件完全相同
<MemoComponent prop1="value1" prop2="value2" />`
  },
  
  keyFeatures: [
    {
      title: "自动浅比较",
      description: "默认使用Object.is进行props的浅比较，自动判断是否需要重新渲染"
    },
    {
      title: "自定义比较策略",
      description: "支持传入自定义比较函数，实现特定业务场景的优化策略"
    },
    {
      title: "零配置优化",
      description: "无需修改组件内部代码，通过包装即可获得性能优化"
    },
    {
      title: "调试友好",
      description: "保留组件的displayName和调试信息，不影响开发体验"
    },
    {
      title: "类型安全",
      description: "完整的TypeScript支持，保持原组件的类型签名"
    }
  ],
  
  limitations: [
    "只进行props的浅比较，对于深层嵌套对象的变化无法检测",
    "不能解决内部state或context变化导致的重新渲染",
    "自定义比较函数如果过于复杂，可能比重新渲染更昂贵",
    "对于已经很轻量的组件，使用memo可能得不偿失",
    "无法阻止由于context变化引起的重新渲染"
  ],
  
  bestPractices: [
    "优先用于渲染成本较高的组件，如复杂列表、图表、表格等",
    "确保传递给memo组件的props保持引用稳定，配合useCallback和useMemo使用",
    "避免在组件内部使用内联对象或数组作为props，会导致浅比较失效",
    "为记忆化组件设置明确的displayName，便于调试和性能分析",
    "在使用自定义比较函数时，优先比较最容易变化的属性",
    "避免过度使用memo，只在确实需要性能优化时使用",
    "测量实际性能收益，确保优化是有效的",
    "结合React DevTools Profiler分析组件渲染性能"
  ],
  
  warnings: [
    "memo不是万能的性能优化方案，需要配合其他优化手段使用",
    "传递给memo组件的函数props需要使用useCallback包装，否则每次都会触发重新渲染",
    "自定义比较函数必须是纯函数，且性能要优于组件重新渲染的成本",
    "过度使用memo可能导致内存占用增加，需要权衡性能和内存",
    "在组件树较深的情况下，memo的效果可能不如预期"
  ],

  // 添加对比分析
  comparisonAnalysis: {
    title: "React.memo vs 其他优化方案对比分析",
    description: "全面对比React.memo与其他React性能优化方案的优劣势、适用场景和使用成本",
    comparisons: [
      {
        name: "React.memo",
        description: "函数组件的高阶组件缓存方案",
        advantages: [
          "使用简单，无需修改组件内部逻辑",
          "自动进行props浅比较，零配置优化",
          "支持自定义比较函数，灵活性高",
          "完整的TypeScript支持和调试信息"
        ],
        disadvantages: [
          "只能优化props变化导致的重渲染",
          "浅比较对深层对象变化无效",
          "需要配合useCallback/useMemo使用"
        ],
        useCases: ["列表项组件", "复杂UI组件", "props较稳定的组件"],
        performance: "中等 - 减少50-80%不必要渲染",
        complexity: "低 - 包装组件即可使用"
      },
      {
        name: "PureComponent",
        description: "类组件的内置浅比较优化",
        advantages: [
          "类组件原生支持，无需额外包装",
          "自动比较props和state变化",
          "性能稳定可靠"
        ],
        disadvantages: [
          "仅支持类组件，无法用于函数组件",
          "无法自定义比较逻辑",
          "类组件生态逐渐被函数组件替代"
        ],
        useCases: ["遗留类组件优化", "复杂类组件缓存"],
        performance: "中等 - 与memo相似的效果",
        complexity: "低 - 继承PureComponent即可"
      },
      {
        name: "useMemo + useCallback",
        description: "Hook级别的值和函数缓存方案",
        advantages: [
          "细粒度控制，可缓存具体值或函数",
          "组件内部使用，不影响组件接口",
          "可以优化组件内部计算和渲染逻辑"
        ],
        disadvantages: [
          "需要手动分析依赖项，容易出错",
          "过度使用可能降低性能",
          "增加代码复杂度和维护成本"
        ],
        useCases: ["昂贵计算缓存", "函数引用稳定化", "对象引用稳定化"],
        performance: "高 - 可精确优化特定场景",
        complexity: "中等 - 需要理解依赖项机制"
      },
      {
        name: "React.lazy + Suspense",
        description: "代码分割和懒加载优化方案",
        advantages: [
          "减少初始包体积，提升首屏加载速度",
          "按需加载，减少不必要的资源消耗",
          "与Suspense结合提供优雅的加载体验"
        ],
        disadvantages: [
          "首次访问时有加载延迟",
          "需要合理规划分割粒度",
          "可能增加网络请求次数"
        ],
        useCases: ["路由级组件", "大型功能模块", "低频使用组件"],
        performance: "高 - 大幅减少初始加载时间",
        complexity: "中等 - 需要配置代码分割"
      }
    ],
    decisionMatrix: {
      description: "根据不同场景选择最适合的优化方案",
      scenarios: [
        {
          scenario: "大型列表渲染",
          recommended: "React.memo + 虚拟化",
          reason: "memo减少单项重渲染，虚拟化处理大量数据"
        },
        {
          scenario: "复杂表单组件",
          recommended: "useMemo + useCallback",
          reason: "需要细粒度控制表单字段和验证逻辑的缓存"
        },
        {
          scenario: "简单展示组件",
          recommended: "React.memo",
          reason: "使用简单，props相对稳定，优化效果明显"
        },
        {
          scenario: "路由页面组件",
          recommended: "React.lazy + Suspense",
          reason: "减少初始包体积，按需加载提升用户体验"
        },
        {
          scenario: "频繁更新的数据组件",
          recommended: "useMemo + 自定义Hook",
          reason: "需要精确控制数据更新和重渲染逻辑"
        }
      ]
    },
    summary: "React.memo适合作为第一选择的性能优化方案，特别是在组件层面的缓存需求。对于更复杂的场景，建议与useMemo、useCallback等Hook组合使用。选择优化方案时应考虑使用复杂度、维护成本和实际性能收益的平衡。"
  }
};

export default basicInfo;