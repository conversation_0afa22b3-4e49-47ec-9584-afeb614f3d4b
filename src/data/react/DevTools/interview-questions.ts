import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'React DevTools是什么？它的主要功能有哪些？在日常开发中你是如何使用它的？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基本概念',
    answer: {
      brief: 'React DevTools是React官方提供的浏览器扩展，主要用于检查组件树、调试props/state、分析性能和调试Hook。',
      detailed: `React DevTools是React官方开发的调试工具，主要包含以下核心功能：

**主要功能模块：**
1. **Components面板**：显示React组件树结构，可以查看和编辑props、state
2. **Profiler面板**：记录和分析组件渲染性能，生成火焰图
3. **Hook调试**：显示函数组件中Hook的状态和依赖关系
4. **Context调试**：查看Context Provider的值变化
5. **Suspense支持**：调试异步组件的加载状态

**日常使用场景：**
- **状态调试**：当组件行为异常时，直接查看props和state的实际值
- **性能优化**：使用Profiler识别渲染性能瓶颈和不必要的重渲染
- **组件定位**：在复杂应用中快速定位特定组件的位置
- **Hook调试**：理解Hook的依赖关系和执行顺序
- **数据流追踪**：追踪props在组件树中的传递路径`,
      code: `// 日常调试示例
function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 在DevTools中检查这个useEffect的依赖
    fetchUser(userId).then(userData => {
      setUser(userData);
      setLoading(false);
    });
  }, [userId]);

  // 在DevTools中可以：
  // 1. 查看user和loading的当前值
  // 2. 手动编辑userId props测试不同用户
  // 3. 观察useEffect的触发时机
  // 4. 检查组件的重渲染次数

  if (loading) return <LoadingSpinner />;
  
  return (
    <div>
      <h1>{user.name}</h1>
      <p>{user.email}</p>
    </div>
  );
}`
    },
    tags: ['基础概念', '调试工具']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'React DevTools的Profiler是如何工作的？如何使用它来识别和解决性能问题？',
    difficulty: 'medium',
    frequency: 'high',
    category: '性能优化',
    answer: {
      brief: 'Profiler通过记录组件渲染时间和次数生成火焰图，帮助识别性能瓶颈。主要关注渲染时间长、重渲染频繁的组件。',
      detailed: `React DevTools Profiler的工作原理和使用方法：

**工作原理：**
1. **数据收集**：利用React Fiber架构的actualDuration等性能指标
2. **时间切片记录**：记录每个组件在不同时间切片中的渲染耗时
3. **火焰图生成**：将渲染数据可视化为火焰图和柱状图
4. **组件排名**：按渲染时间和频率对组件进行排序

**性能问题识别方法：**
1. **火焰图分析**：宽度表示渲染时间，高度表示调用栈深度
2. **颜色编码**：黄色/红色表示渲染时间较长的组件
3. **重渲染检测**：识别不必要的组件重新渲染
4. **交互追踪**：记录用户交互触发的渲染链

**优化策略：**
- 使用React.memo包装纯组件
- 优化useEffect依赖数组
- 使用useMemo和useCallback缓存计算结果
- 拆分大组件，减少重渲染范围`,
      code: `// 性能问题分析示例
function ExpensiveList({ items, filter }) {
  // ❌ 性能问题：每次渲染都重新过滤
  const filteredItems = items.filter(item => 
    item.name.toLowerCase().includes(filter.toLowerCase())
  );

  return (
    <div>
      {filteredItems.map(item => (
        <ExpensiveItem key={item.id} item={item} />
      ))}
    </div>
  );
}

// 在Profiler中会发现：
// 1. ExpensiveList渲染时间很长
// 2. 每次filter变化都重新渲染所有ExpensiveItem
// 3. filter频繁变化导致性能问题

// ✅ 优化后的代码
function OptimizedList({ items, filter }) {
  // 使用useMemo缓存过滤结果
  const filteredItems = useMemo(() => 
    items.filter(item => 
      item.name.toLowerCase().includes(filter.toLowerCase())
    ), [items, filter]
  );

  return (
    <div>
      {filteredItems.map(item => (
        <MemoizedExpensiveItem key={item.id} item={item} />
      ))}
    </div>
  );
}

const MemoizedExpensiveItem = React.memo(ExpensiveItem);

// Profiler使用步骤：
// 1. 打开Profiler面板，点击录制按钮
// 2. 执行需要分析的用户操作
// 3. 停止录制，查看火焰图
// 4. 点击耗时长的组件，查看详细信息
// 5. 根据分析结果进行针对性优化`
    },
    tags: ['性能优化', 'Profiler']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 3,
    question: '在大型团队开发中，如何有效利用React DevTools进行协作调试？如何与其他调试工具集成？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '团队协作',
    answer: {
      brief: '通过统一调试规范、组件命名约定、自定义调试钩子等方式提升团队协作效率，并与Redux DevTools、错误监控等工具集成。',
      detailed: `大型团队中React DevTools的协作使用策略：

**团队协作最佳实践：**
1. **统一命名规范**：使用一致的组件命名和displayName设置
2. **调试信息标准化**：为关键组件添加useDebugValue调试信息
3. **性能基准建立**：定期使用Profiler建立性能基准线
4. **调试文档维护**：记录常见问题的调试方法

**工具集成策略：**
1. **Redux DevTools集成**：状态管理调试
2. **Apollo Client DevTools**：GraphQL数据调试
3. **错误监控集成**：Sentry等工具的DevTools扩展
4. **性能监控工具**：与Lighthouse、Web Vitals集成

**高级调试技巧：**
- 自定义DevTools插件开发
- 微前端架构下的多应用调试
- CI/CD流水线中的性能基准检查
- 生产环境调试数据收集（安全移除后）`,
      code: `// 团队协作调试最佳实践
// 1. 统一的组件调试信息
function TeamComponent({ data, config }) {
  // 添加调试信息
  useDebugValue(
    \`\${data.length} items, config: \${config.mode}\`
  );

  return <div>{/* 组件内容 */}</div>;
}

// 设置组件显示名称
TeamComponent.displayName = 'TeamComponent';

// 2. 自定义调试Hook
function useTeamDebugger(componentName, data) {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.group(\`🔍 \${componentName} Debug\`);
      console.log('Props:', data);
      console.log('Render time:', Date.now());
      console.groupEnd();
    }
  });

  useDebugValue(data, (debugData) => 
    \`\${componentName}: \${JSON.stringify(debugData, null, 2)}\`
  );
}

// 3. DevTools扩展集成
class DevToolsIntegration {
  static setupTeamDebugging() {
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      // 添加团队特定的调试钩子
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.teamDebugger = {
        logRender: (componentName, props, duration) => {
          if (duration > 16) { // 超过一帧的时间
            console.warn(\`Slow render: \${componentName} took \${duration}ms\`);
          }
        },
        
        trackStateChanges: (componentName, oldState, newState) => {
          console.log(\`State change in \${componentName}:\`, {
            from: oldState,
            to: newState
          });
        }
      };
    }
  }
}

// 4. 性能监控集成
function usePerformanceTracking(componentName) {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 发送性能数据到团队监控系统
      teamAnalytics.track('component_render_time', {
        component: componentName,
        duration,
        timestamp: Date.now()
      });
    };
  });
}`
    },
    tags: ['团队协作', '工具集成']
  }
];

// DevTools面试问题内容已完成
export default interviewQuestions;