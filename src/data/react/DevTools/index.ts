import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ReactDevToolsData: ApiItem = {
  id: 'DevTools',
  title: 'React DevTools',
  description: 'React DevTools是React官方提供的浏览器扩展和开发工具，用于检查、调试和分析React应用程序',
  category: 'Development Tools',
  difficulty: 'medium',
  
  syntax: `// 安装浏览器扩展
Chrome: https://chrome.google.com/webstore/detail/react-developer-tools/
Firefox: https://addons.mozilla.org/en-US/firefox/addon/react-devtools/

// 独立应用安装
npm install -g react-devtools
npx react-devtools

// React Native调试
import { connectToDevTools } from 'react-devtools-core';
connectToDevTools({
  host: 'localhost',
  port: 8097,
});`,
  example: `function ReactDevToolsExample() {
  // 使用React DevTools调试组件
  const [count, setCount] = useState(0);
  const [user, setUser] = useState({ name: 'Alice', age: 30 });

  return (
    <div>
      {/* React DevTools可以检查这些组件的props和state */}
      <Counter 
        value={count}
        onIncrement={() => setCount(count + 1)}
        debugLabel="主计数器"
      >
        当前计数: {count}
      </Counter>
      
      <UserProfile 
        user={user}
        onUpdate={setUser}
      />
    </div>
  );
}`,
  notes: '仅在开发环境生效，生产环境会被自动禁用',
  
  version: 'React 15.0.0+',
  tags: ["React","DevTools","Development","Debugging","Performance"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ReactDevToolsData;