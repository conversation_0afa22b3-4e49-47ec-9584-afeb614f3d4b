import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: 'React DevTools显示"This page doesn\'t appear to be using React"，但我确实在使用React，这是为什么？',
    answer: `这个问题通常有以下几种原因和解决方案：

**常见原因：**
1. **React版本过旧**：DevTools可能不兼容非常旧的React版本
2. **生产环境构建**：React生产版本会禁用DevTools检测
3. **框架包装**：某些框架（如Next.js SSR）可能影响检测
4. **扩展版本问题**：DevTools扩展版本与React版本不匹配

**解决方案：**
- 确保使用开发版本的React（react.development.js）
- 更新React和DevTools到最新版本
- 检查页面是否正确加载了React库
- 重启浏览器或重新安装DevTools扩展
- 在控制台检查 \`window.React\` 是否存在`,
    code: `// 检查React是否正确加载
console.log('React version:', React.version);
console.log('DevTools hook:', window.__REACT_DEVTOOLS_GLOBAL_HOOK__);

// 强制启用DevTools（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = window.__REACT_DEVTOOLS_GLOBAL_HOOK__ || {};
}

// 在Webpack配置中确保开发模式
module.exports = {
  mode: 'development', // 确保是开发模式
  resolve: {
    alias: {
      'react-dom$': 'react-dom/profiling',
      'scheduler/tracing': 'scheduler/tracing-profiling',
    }
  }
};`,
    tags: ['安装问题', '环境配置'],
    relatedQuestions: ['DevTools安装失败怎么办？', '如何在生产环境调试？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '在DevTools中修改组件的props或state后，为什么有时候修改不生效或者被重置？',
    answer: `这是React的状态管理机制导致的正常行为，理解原因有助于更好地使用DevTools：

**主要原因：**
1. **父组件重渲染**：父组件重新渲染时会传递新的props，覆盖DevTools的修改
2. **状态依赖**：组件的state依赖其他状态或props，会被自动计算覆盖
3. **Effect副作用**：useEffect等副作用可能会重置状态
4. **受控组件**：表单元素的值由props控制，修改state无效

**解决策略：**
- 理解组件的数据流，从数据源头进行修改
- 使用DevTools的"暂停更新"功能冻结组件状态
- 临时注释掉相关的useEffect或状态同步逻辑
- 修改父组件的state而不是子组件的props`,
    code: `// 示例：为什么修改可能被重置
function ChildComponent({ count, onIncrement }) {
  const [localCount, setLocalCount] = useState(count);

  // ❌ 这会导致DevTools修改被重置
  useEffect(() => {
    setLocalCount(count); // 每次props变化都重置本地状态
  }, [count]);

  return (
    <div>
      <p>Local: {localCount}</p>
      <p>Props: {count}</p>
      <button onClick={() => setLocalCount(prev => prev + 1)}>
        Local +1
      </button>
    </div>
  );
}

// ✅ 更好的调试方式
function DebugFriendlyComponent({ initialCount, onIncrement }) {
  const [count, setCount] = useState(initialCount);

  // 在DevTools中可以直接修改count，不会被重置
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(prev => prev + 1)}>
        +1
      </button>
    </div>
  );
}

// DevTools调试技巧
// 1. 使用组件的key强制重新挂载
<ChildComponent key={Math.random()} count={count} />

// 2. 临时禁用useEffect进行调试
useEffect(() => {
  if (process.env.NODE_ENV === 'development' && window.DEBUG_MODE) {
    return; // 调试时跳过effect
  }
  // 正常的effect逻辑
}, [dependency]);`,
    tags: ['状态管理', '调试技巧'],
    relatedQuestions: ['如何冻结组件状态？', 'useEffect影响调试怎么办？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: 'DevTools的Profiler显示的性能数据看不懂，火焰图和排名图表应该怎么看？',
    answer: `React DevTools Profiler的数据解读需要理解几个关键概念：

**火焰图解读：**
1. **横轴（宽度）**：表示组件渲染耗时，越宽说明渲染越慢
2. **纵轴（高度）**：表示组件调用栈深度，越高说明嵌套越深  
3. **颜色编码**：
   - 绿色/蓝色：渲染较快
   - 黄色：渲染中等
   - 红色：渲染较慢，需要关注

**排名图表解读：**
- **Ranked**：按渲染时间排序，找出最慢的组件
- **Interactions**：显示用户交互触发的渲染
- **Flamegraph**：显示完整的调用栈关系

**优化指导原则：**
- 关注宽度大的红色/黄色区域
- 检查是否有不必要的深层嵌套
- 识别频繁重渲染的组件
- 对比优化前后的性能数据`,
    code: `// 性能分析实例
function PerformanceExample() {
  const [filter, setFilter] = useState('');
  const [items] = useState(generateLargeDataset(1000));

  // ❌ 在火焰图中会显示为宽的红色区域
  const expensiveFilter = items.filter(item => {
    // 复杂计算
    return expensiveComputeRelevance(item, filter) > 0.5;
  }).map(item => ({
    ...item,
    processed: expensiveProcessing(item)
  }));

  return (
    <div>
      <input 
        value={filter} 
        onChange={(e) => setFilter(e.target.value)}
      />
      {/* 这里会显示很深的组件栈 */}
      <ExpensiveList items={expensiveFilter} />
    </div>
  );
}

// Profiler使用技巧
// 1. 录制特定操作
function ProfilerWrapper({ children }) {
  return (
    <Profiler
      id="MyComponent"
      onRender={(id, phase, actualDuration, baseDuration, startTime, commitTime) => {
        if (actualDuration > 16) { // 超过一帧
          console.warn(\`Slow render: \${id} took \${actualDuration}ms\`);
        }
      }}
    >
      {children}
    </Profiler>
  );
}

// 2. 性能基准测试
const performanceMetrics = {
  componentRenderTime: new Map(),
  
  recordRender(componentId, duration) {
    const history = this.componentRenderTime.get(componentId) || [];
    history.push(duration);
    
    // 保留最近50次记录
    if (history.length > 50) {
      history.shift();
    }
    
    this.componentRenderTime.set(componentId, history);
  },
  
  getAverageRenderTime(componentId) {
    const history = this.componentRenderTime.get(componentId) || [];
    return history.reduce((sum, time) => sum + time, 0) / history.length;
  }
};`,
    tags: ['性能分析', 'Profiler使用'],
    relatedQuestions: ['如何建立性能基准？', '性能优化从哪里开始？']
  }
];

// DevTools常见问题内容已完成
export default commonQuestions;