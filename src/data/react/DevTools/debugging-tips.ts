import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: "React DevTools调试需要掌握正确的方法和技巧。本指南提供系统性的故障排除流程，帮助快速定位和解决DevTools使用中的各种问题。",

  troubleshooting: [
    {
      symptom: "DevTools无法检测到React应用",
      possibleCauses: [
        "使用了生产版本的React构建",
        "React版本过旧，不支持DevTools",
        "页面使用iframe或跨域限制",
        "DevTools扩展版本与React不兼容"
      ],
      solutions: [
        "确保使用development版本：npm start或webpack mode: 'development'",
        "更新React到16.0+版本，确保DevTools兼容性",
        "检查浏览器控制台的错误信息和警告",
        "重新安装最新版本的DevTools扩展"
      ]
    },
    {
      symptom: "Profiler录制后显示空白或无数据",
      possibleCauses: [
        "录制期间没有组件重新渲染",
        "应用处于静止状态，无交互行为",
        "Profiler版本不支持当前React特性",
        "浏览器性能限制导致数据丢失"
      ],
      solutions: [
        "确保录制期间有用户交互或状态变化",
        "手动触发组件重渲染进行测试",
        "检查React版本是否支持Profiler功能",
        "使用较短的录制时间段，避免数据过多"
      ]
    },
    {
      symptom: "组件树显示不完整或组件名称为Unknown",
      possibleCauses: [
        "组件没有设置displayName属性",
        "代码压缩时移除了函数名",
        "使用了匿名函数组件",
        "Source map配置不正确"
      ],
      solutions: [
        "为组件设置displayName：Component.displayName = 'MyComponent'",
        "使用具名函数而不是匿名函数定义组件",
        "配置正确的source map以保留调试信息",
        "在开发环境禁用代码压缩和混淆"
      ]
    }
  ],

  debuggingTools: [
    {
      name: "React DevTools Console Commands",
      description: "在浏览器控制台直接操作DevTools功能",
      usage: "使用$r访问选中组件，$0访问DOM元素，inspect()快速定位"
    },
    {
      name: "Profiler编程API",
      description: "通过代码控制性能分析和数据收集",
      usage: "使用React.Profiler组件包装需要分析的部分，实现自动化性能监控"
    },
    {
      name: "DevTools扩展设置",
      description: "配置DevTools的主题、过滤器和显示选项",
      usage: "右键DevTools面板，选择Settings配置过滤规则和显示偏好"
    }
  ],

  commonMistakes: [
    {
      mistake: "在生产环境期望使用DevTools调试",
      explanation: "生产环境的React会自动禁用DevTools检测，这是安全考虑",
      correctApproach: "使用development构建进行调试，生产问题通过日志和监控定位"
    },
    {
      mistake: "长时间开启Profiler录制影响性能",
      explanation: "Profiler录制会收集大量数据，长时间录制会消耗内存和CPU",
      correctApproach: "针对性录制特定操作，及时停止录制并分析数据"
    },
    {
      mistake: "忽略DevTools的warning和建议",
      explanation: "DevTools会给出性能和最佳实践建议，忽略可能错过优化机会",
      correctApproach: "认真阅读DevTools的警告信息，根据建议进行代码优化"
    }
  ],

  performanceAnalysis: [
    {
      metric: "组件查找速度",
      description: "在大型组件树中定位特定组件的时间",
      tools: ["Components面板搜索", "元素选择器", "组件过滤器"],
      interpretation: "搜索应在1秒内完成，超过则考虑组件树优化"
    },
    {
      metric: "Profiler数据准确性",
      description: "性能数据的精确度和一致性检查",
      tools: ["重复录制对比", "不同浏览器验证", "手动计时验证"],
      interpretation: "多次录制结果应在10%误差范围内，确保数据可靠性"
    }
  ]
};

// DevTools调试技巧内容已完成
export default debuggingTips;