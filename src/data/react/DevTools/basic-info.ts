import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: "React DevTools是React官方提供的浏览器扩展和开发工具，用于检查、调试和分析React应用程序",
  
  introduction: `React DevTools是React官方在2015年引入的开发者工具集，主要用于组件树检查、状态调试、性能分析和开发体验优化。它采用浏览器扩展和独立应用的设计模式，提供了无缝的React应用开发和调试体验。`,

  syntax: `// 安装浏览器扩展
Chrome: https://chrome.google.com/webstore/detail/react-developer-tools/
Firefox: https://addons.mozilla.org/en-US/firefox/addon/react-devtools/

// 独立应用安装
npm install -g react-devtools
npx react-devtools

// React Native调试
import { connectToDevTools } from 'react-devtools-core';
connectToDevTools({
  host: 'localhost',
  port: 8097,
});`,

  quickExample: `function ReactDevToolsExample() {
  // 使用React DevTools调试组件
  const [count, setCount] = useState(0);
  const [user, setUser] = useState({ name: 'Alice', age: 30 });

  return (
    <div>
      {/* React DevTools可以检查这些组件的props和state */}
      <Counter 
        value={count}
        onIncrement={() => setCount(count + 1)}
        debugLabel="主计数器"
      >
        当前计数: {count}
      </Counter>
      
      <UserProfile 
        user={user}
        onUpdate={setUser}
      />
    </div>
  );
}`,

  scenarioDiagram: `graph TD
    A[React DevTools使用场景] --> B[组件调试]
    A --> C[性能分析]
    A --> D[状态管理]

    B --> B1[组件树检查]
    B --> B2[Props查看]
    B --> B3[Hook调试]

    C --> C1[渲染性能分析]
    C --> C2[组件重渲染追踪]
    C --> C3[内存使用监控]

    D --> D1[State变化追踪]
    D --> D2[Context值监控]
    D --> D3[Redux集成调试]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "浏览器扩展",
      type: "Browser Extension",
      required: false,
      description: "Chrome/Firefox浏览器扩展，自动检测React应用",
      example: "自动在开发者工具中添加React和Profiler面板"
    },
    {
      name: "独立应用",
      type: "Standalone App",
      required: false,
      description: "独立的Electron应用，支持React Native和Electron应用调试",
      example: "npx react-devtools 启动独立调试器"
    },
    {
      name: "配置选项",
      type: "DevToolsConfig",
      required: false,
      description: "调试器连接和显示配置",
      example: "{ host: 'localhost', port: 8097, theme: 'dark' }"
    }
  ],
  
  returnValue: {
    type: "DebugInfo",
    description: "提供可视化的组件树、状态信息和性能数据",
    example: "组件层次结构、props/state详情、性能火焰图、Hook调用栈"
  },
  
  keyFeatures: [
    {
      title: "组件树检查",
      description: "实时查看React组件的层次结构和嵌套关系",
      benefit: "快速定位组件位置，理解应用架构"
    },
    {
      title: "Props和State调试",
      description: "查看和实时编辑组件的props、state和context值",
      benefit: "无需console.log即可调试组件状态"
    },
    {
      title: "Hook调试支持",
      description: "显示Hook的调用顺序、依赖项和返回值",
      benefit: "深度理解Hook的工作机制和数据流"
    },
    {
      title: "性能分析器",
      description: "记录和分析组件渲染性能，识别性能瓶颈",
      benefit: "优化应用性能，减少不必要的重渲染"
    },
    {
      title: "时间旅行调试",
      description: "记录状态变化历史，支持回放和比较",
      benefit: "追踪bug产生过程，理解状态变化逻辑"
    }
  ],
  
  limitations: [
    "仅在开发环境生效，生产环境会被自动禁用",
    "对于大型应用可能会影响性能，建议选择性开启",
    "某些第三方组件库可能显示信息不完整",
    "React Native调试需要额外配置连接",
    "无法调试服务端渲染过程中的组件状态"
  ],
  
  bestPractices: [
    "使用meaningful的组件名称，便于在DevTools中识别",
    "适当使用React.memo和useMemo等优化，在Profiler中验证效果",
    "利用组件的displayName属性提供更好的调试体验",
    "在开发时定期使用Profiler检查性能问题",
    "配合useDebugValue为自定义Hook添加调试信息"
  ],
  
  warnings: [
    "生产环境会自动禁用DevTools，确保不依赖其进行业务逻辑",
    "大量使用DevTools可能影响开发环境性能",
    "避免在生产代码中留下DevTools相关的调试代码"
  ]
};

// DevTools基本信息内容已完成
export default basicInfo;