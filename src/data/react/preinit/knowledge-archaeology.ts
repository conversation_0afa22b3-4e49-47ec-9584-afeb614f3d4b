import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  introduction: `preinit的诞生标志着Web资源管理从"被动加载"向"主动预初始化"的历史性转变。这不仅仅是一个API的添加，而是整个Web性能优化范式的革命性演进。通过深入挖掘其历史背景、技术演进和设计理念，我们可以更好地理解现代Web资源管理的精髓和未来发展方向。`,

  background: `preinit的出现源于Web应用复杂度的急剧增长和用户体验期望的不断提升。在Web早期，所有资源都是同步加载的，页面要么全部加载完成，要么完全无法使用。随着单页应用(SPA)的兴起，动态资源加载成为必需，但资源加载延迟成为新的性能瓶颈。传统的preload虽然解决了部分问题，但缺乏对CSS优先级的精细控制，无法满足复杂应用的需求。`,

  evolution: `preinit的演进历程体现了Web平台对资源管理的不断探索：从早期的同步加载，到异步加载的探索，再到资源提示标准的建立，最终发展为智能的资源预初始化系统。这个过程反映了Web技术从简单到复杂，从粗放到精细的发展轨迹。`,

  timeline: [
    {
      year: '2018',
      event: 'Resource Hints标准成熟',
      description: 'W3C的Resource Hints标准得到主流浏览器支持，为preinit奠定了技术基础',
      significance: '这是preinit技术实现的基础，没有Resource Hints就没有后续的优化空间'
    },
    {
      year: '2022',
      event: 'React 18.2引入preinit',
      description: 'preinit作为ReactDOM的资源预初始化API首次出现',
      significance: '标志着React开始承担更多的性能优化责任，从功能框架向性能框架演进'
    }
  ],

  keyFigures: [
    {
      name: 'Sebastian Markbåge',
      role: 'React架构师',
      contribution: '设计了React的资源管理系统，包括preinit等预加载API',
      significance: '他将浏览器的底层能力抽象为开发者友好的React API'
    }
  ],

  concepts: [
    {
      term: '资源预初始化(Resource Preinitialization)',
      definition: '在资源实际需要之前提前加载并初始化资源，确保资源在需要时立即可用',
      evolution: '从简单的资源预加载发展为带有优先级控制的预初始化',
      modernRelevance: '现代Web应用性能优化的核心技术，特别是在复杂样式管理场景中'
    }
  ],

  designPhilosophy: `preinit的设计哲学体现了现代Web开发对性能和用户体验的深度思考：优先级驱动的资源管理、声明式的性能优化、渐进增强的理念。`,

  impact: `preinit对Web开发生态系统产生了重要影响：推动了资源预加载标准的发展，提升了框架的性能能力，改变了开发者的性能优化思路。`,

  modernRelevance: `在当今的Web开发环境中，preinit的重要性日益凸显：移动优先的时代需要更精细的资源管理，复杂应用需要更智能的优先级控制。`
};

export default knowledgeArchaeology;