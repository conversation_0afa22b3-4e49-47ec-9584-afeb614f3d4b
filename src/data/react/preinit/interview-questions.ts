import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 1,
    question: '什么是preinit？它解决了什么问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'preinit是React DOM中用于预初始化通用资源的函数，主要解决CSS样式表和JavaScript脚本的预加载性能问题。',
      detailed: `preinit是React 18.2+引入的ReactDOM资源预初始化API，专门用于提前加载和初始化关键资源。

**解决的核心问题：**
1. **样式闪烁问题**：传统方式在加载CSS时会出现无样式内容闪烁(FOUC)
2. **脚本加载延迟**：JavaScript文件的网络请求延迟影响功能可用性
3. **资源优先级混乱**：无法精确控制不同资源的加载优先级
4. **用户体验断层**：资源加载延迟导致的交互响应延迟

**主要特性：**
- 支持CSS样式表和JavaScript脚本的预初始化
- 提供precedence参数控制CSS加载优先级
- 支持integrity校验和CORS配置
- 与React的资源管理系统深度集成
- SSR环境友好，客户端激活

**使用场景：**
- 主题切换系统的样式预加载
- 第三方分析工具的脚本预初始化
- 国际化应用的字体样式预加载
- 动态功能模块的资源预准备`,
      code: `// 基础使用示例
function BasicPreinitExample() {
  useEffect(() => {
    // 预初始化CSS样式表
    preinit('/styles/theme.css', {
      as: 'style',
      precedence: 'high'
    });

    // 预初始化JavaScript脚本
    preinit('/scripts/analytics.js', {
      as: 'script',
      crossOrigin: 'anonymous',
      integrity: 'sha384-...'
    });
  }, []);

  return <div>内容区域</div>;
}`
    },
    tags: ['基础概念', '性能优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 2,
    question: 'preinit的precedence参数是如何工作的？请详细说明其实现原理。',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: 'precedence参数通过为CSS样式表设置优先级，控制其在DOM中的插入顺序和解析优先级，确保关键样式优先加载和应用。',
      detailed: `precedence参数是preinit的核心特性之一，专门用于CSS样式表的优先级管理。

**工作原理：**
1. **DOM插入顺序控制**：React根据precedence值决定<link>标签在document.head中的位置
2. **浏览器解析优先级**：浏览器按照DOM中的顺序解析CSS，高优先级样式先解析
3. **样式覆盖规则**：后解析的同优先级样式会覆盖先解析的样式
4. **渲染阻塞优化**：关键样式优先处理，减少首屏渲染阻塞

**优先级层次：**
- **'high'**：最高优先级，用于基础布局和关键组件样式
- **'medium'**：中等优先级，用于主题和功能模块样式
- **'low'**：最低优先级，用于装饰性和可选样式

**内部实现机制：**
React维护一个优先级映射表，将precedence值转换为具体的DOM插入策略。高优先级的样式表会被插入到head的更前面位置，确保优先解析。

**注意事项：**
- precedence只对as='style'有效，对script无效
- 相同precedence的样式表按调用顺序排列
- 不设置precedence的样式表默认为最低优先级`,
      code: `// precedence参数详细示例
function PrecedenceExample() {
  useEffect(() => {
    // 高优先级：基础样式，最先加载
    preinit('/styles/reset.css', {
      as: 'style',
      precedence: 'high'
    });

    preinit('/styles/layout.css', {
      as: 'style',
      precedence: 'high'
    });

    // 中等优先级：主题样式
    preinit('/styles/theme.css', {
      as: 'style',
      precedence: 'medium'
    });

    // 低优先级：装饰样式，最后加载
    preinit('/styles/animations.css', {
      as: 'style',
      precedence: 'low'
    });
  }, []);

  // 动态优先级调整
  const loadCriticalTheme = () => {
    preinit('/styles/critical-theme.css', {
      as: 'style',
      precedence: 'high' // 提升为高优先级
    });
  };

  return (
    <div>
      <button onClick={loadCriticalTheme}>
        加载关键主题
      </button>
    </div>
  );
}

// 优先级策略示例
const STYLE_PRIORITIES = {
  reset: 'high',      // 重置样式
  layout: 'high',     // 布局样式
  components: 'medium', // 组件样式
  theme: 'medium',    // 主题样式
  animations: 'low',  // 动画样式
  print: 'low'        // 打印样式
};

function loadStyleWithPriority(styleType, path) {
  preinit(path, {
    as: 'style',
    precedence: STYLE_PRIORITIES[styleType]
  });
}`
    },
    tags: ['优先级控制', '实现原理']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 3,
    question: '在大型应用中如何设计preinit的预加载策略？请提供一个完整的解决方案。',
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    answer: {
      brief: '大型应用的preinit策略需要考虑用户行为分析、资源优先级分层、网络条件适配、缓存策略优化等多个维度，建立智能化的资源预加载管理系统。',
      detailed: `大型应用的preinit预加载策略设计是一个复杂的系统工程，需要综合考虑多个因素。

**策略设计原则：**
1. **用户行为驱动**：基于用户行为数据预测资源需求
2. **分层优先级管理**：建立清晰的资源优先级体系
3. **网络条件自适应**：根据网络状况调整预加载策略
4. **资源使用效率**：平衡预加载收益和资源消耗

**核心组件设计：**
1. **资源管理器**：统一管理所有预加载资源
2. **策略引擎**：根据条件动态调整预加载策略
3. **监控系统**：实时监控预加载效果和资源使用
4. **缓存优化**：与浏览器缓存策略协作

**实施步骤：**
1. 分析应用的资源依赖关系
2. 建立用户行为分析模型
3. 设计分层的优先级体系
4. 实现智能预加载算法
5. 建立监控和优化机制

**性能考量：**
- 避免过度预加载导致的资源浪费
- 考虑移动设备的网络和电池限制
- 平衡首屏性能和后续交互性能
- 建立降级和容错机制`,
      code: `// 大型应用preinit策略完整解决方案
class PreinitStrategyManager {
  constructor() {
    this.resourceRegistry = new Map();
    this.userBehaviorAnalyzer = new UserBehaviorAnalyzer();
    this.networkMonitor = new NetworkMonitor();
    this.priorityEngine = new PriorityEngine();
  }

  // 初始化预加载策略
  async initializeStrategy() {
    // 分析用户行为模式
    const behaviorPattern = await this.userBehaviorAnalyzer.analyze();

    // 获取网络条件
    const networkInfo = this.networkMonitor.getCurrentInfo();

    // 生成预加载计划
    const preloadPlan = this.generatePreloadPlan(behaviorPattern, networkInfo);

    // 执行预加载
    this.executePreloadPlan(preloadPlan);
  }

  // 生成预加载计划
  generatePreloadPlan(behaviorPattern, networkInfo) {
    const resources = this.getResourceCandidates();

    return resources.map(resource => ({
      ...resource,
      priority: this.priorityEngine.calculatePriority(resource, behaviorPattern),
      shouldPreload: this.shouldPreload(resource, networkInfo),
      timing: this.calculateOptimalTiming(resource, behaviorPattern)
    })).filter(item => item.shouldPreload)
      .sort((a, b) => b.priority - a.priority);
  }

  // 执行预加载计划
  executePreloadPlan(plan) {
    plan.forEach((item, index) => {
      setTimeout(() => {
        this.preinitResource(item);
      }, item.timing + index * 100); // 错开执行时间
    });
  }

  // 预初始化资源
  preinitResource(resource) {
    const options = {
      as: resource.type,
      precedence: this.mapPriorityToPrecedence(resource.priority)
    };

    // 添加安全性配置
    if (resource.crossOrigin) {
      options.crossOrigin = resource.crossOrigin;
    }

    if (resource.integrity) {
      options.integrity = resource.integrity;
    }

    preinit(resource.url, options);

    // 记录预加载操作
    this.recordPreloadAction(resource);
  }

  // 优先级映射
  mapPriorityToPrecedence(priority) {
    if (priority >= 0.8) return 'high';
    if (priority >= 0.5) return 'medium';
    return 'low';
  }

  // 判断是否应该预加载
  shouldPreload(resource, networkInfo) {
    // 网络条件检查
    if (networkInfo.effectiveType === '2g') return false;
    if (networkInfo.effectiveType === '3g' && resource.size > 100000) return false;

    // 资源重要性检查
    if (resource.importance === 'critical') return true;
    if (resource.importance === 'low' && networkInfo.effectiveType !== '4g') return false;

    // 用户偏好检查
    if (this.userBehaviorAnalyzer.isLikelyToUse(resource)) return true;

    return resource.priority > 0.6;
  }
}

// 用户行为分析器
class UserBehaviorAnalyzer {
  async analyze() {
    const history = await this.getUserActionHistory();
    const patterns = this.extractPatterns(history);

    return {
      frequentPages: patterns.pages,
      preferredFeatures: patterns.features,
      timePatterns: patterns.timing,
      deviceInfo: this.getDeviceInfo()
    };
  }

  isLikelyToUse(resource) {
    // 基于历史数据预测资源使用概率
    const usageHistory = this.getResourceUsageHistory(resource.id);
    const probability = this.calculateUsageProbability(usageHistory);

    return probability > 0.7;
  }
}

// 网络监控器
class NetworkMonitor {
  getCurrentInfo() {
    if ('connection' in navigator) {
      const connection = navigator.connection;
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      };
    }

    return { effectiveType: '4g' }; // 默认假设良好网络
  }
}

// 优先级引擎
class PriorityEngine {
  calculatePriority(resource, behaviorPattern) {
    let priority = 0;

    // 基础重要性权重
    priority += this.getImportanceWeight(resource.importance);

    // 用户行为权重
    priority += this.getBehaviorWeight(resource, behaviorPattern);

    // 时间敏感性权重
    priority += this.getTimingWeight(resource);

    // 资源大小权重（小文件优先）
    priority += this.getSizeWeight(resource.size);

    return Math.min(priority, 1); // 限制在0-1范围内
  }

  getImportanceWeight(importance) {
    const weights = {
      'critical': 0.4,
      'high': 0.3,
      'medium': 0.2,
      'low': 0.1
    };
    return weights[importance] || 0.1;
  }
}

// 使用示例
function AppWithPreinitStrategy() {
  const [strategyManager] = useState(() => new PreinitStrategyManager());

  useEffect(() => {
    strategyManager.initializeStrategy();
  }, []);

  return <div>应用内容</div>;
}`
    },
    tags: ['架构设计', '策略优化']
  }
];

export default interviewQuestions;