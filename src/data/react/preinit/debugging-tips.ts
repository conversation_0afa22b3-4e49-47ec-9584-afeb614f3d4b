import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'preinit虽然API简单，但在实际使用中开发者经常遇到一些典型问题。本节提供完整的问题诊断和解决方案，帮助快速定位和修复preinit相关的技术问题。',
        sections: [
          {
            title: '预初始化无效果问题',
            description: '最常见的问题是preinit看起来没有任何效果，资源加载时间没有改善。这通常涉及调用时机、参数配置、浏览器支持等多个方面',
            items: [
              {
                title: '调用时机过晚',
                description: 'preinit在资源即将使用时才调用，没有足够的时间进行预初始化',
                solution: '1. 在应用启动时预初始化核心资源；2. 在路由变化时预初始化目标页面资源；3. 根据用户行为预测性加载',
                prevention: '建立预初始化策略，在合适的时机提前调用preinit',
                code: `// ❌ 错误：调用时机过晚
const handleClick = () => {
  preinit('/styles/modal.css', { as: 'style' }); // 太晚了
  showModal(); // 样式还没加载完成
};

// ✅ 正确：提前预初始化
function App() {
  useEffect(() => {
    // 应用启动时预初始化可能用到的样式
    preinit('/styles/modal.css', {
      as: 'style',
      precedence: 'medium'
    });
  }, []);

  const handleClick = () => {
    // 样式已经预初始化，立即可用
    showModal();
  };
}`
              },
              {
                title: 'precedence参数配置错误',
                description: 'CSS样式的precedence设置不当，导致样式加载顺序混乱或优先级冲突',
                solution: '1. 为基础样式设置高优先级；2. 为主题样式设置中等优先级；3. 为装饰样式设置低优先级',
                prevention: '建立清晰的样式优先级体系，避免随意设置precedence',
                code: `// ❌ 错误：优先级设置混乱
preinit('/styles/animations.css', {
  as: 'style',
  precedence: 'high' // 装饰性样式不应该是高优先级
});

preinit('/styles/layout.css', {
  as: 'style',
  precedence: 'low' // 基础布局应该是高优先级
});

// ✅ 正确：合理的优先级设置
preinit('/styles/reset.css', {
  as: 'style',
  precedence: 'high' // 重置样式最高优先级
});

preinit('/styles/layout.css', {
  as: 'style',
  precedence: 'high' // 布局样式高优先级
});

preinit('/styles/theme.css', {
  as: 'style',
  precedence: 'medium' // 主题样式中等优先级
});

preinit('/styles/animations.css', {
  as: 'style',
  precedence: 'low' // 动画样式低优先级
});`
              }
            ]
          },
          {
            title: '安全性和跨域问题',
            description: '在使用第三方资源或CDN内容时，经常遇到CORS错误、CSP策略冲突、完整性校验失败等安全相关问题',
            items: [
              {
                title: 'CORS跨域错误',
                description: '预初始化第三方资源时出现跨域访问被阻止的错误',
                solution: '1. 设置正确的crossOrigin参数；2. 确保服务器支持CORS；3. 使用代理服务器转发请求',
                prevention: '在使用第三方资源前检查其CORS策略，正确配置crossOrigin参数',
                code: `// ❌ 错误：缺少crossOrigin配置
preinit('https://cdn.example.com/styles/theme.css', {
  as: 'style'
}); // 可能导致CORS错误

// ✅ 正确：配置crossOrigin
preinit('https://cdn.example.com/styles/theme.css', {
  as: 'style',
  crossOrigin: 'anonymous' // 匿名跨域请求
});

// ✅ 对于需要凭据的请求
preinit('https://private-cdn.example.com/styles/theme.css', {
  as: 'style',
  crossOrigin: 'use-credentials' // 带凭据的跨域请求
});`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '有效的调试工具可以帮助开发者监控preinit的效果，诊断性能问题，优化预初始化策略。掌握这些工具是高效使用preinit的关键。',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '现代浏览器提供了强大的网络和性能分析工具，可以直观地监控资源预初始化的效果',
            items: [
              {
                title: '网络面板监控',
                description: '使用浏览器开发者工具的网络面板监控preinit请求的状态和时机',
                solution: '1. 打开开发者工具网络面板；2. 筛选显示"Other"类型请求；3. 查看preload请求的时机和状态；4. 分析预初始化效果',
                prevention: '定期检查网络面板，确保预初始化按预期工作',
                code: `// 监控预初始化状态的工具函数
function monitorPreinitStatus() {
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.initiatorType === 'link' &&
          entry.name.includes('.css') || entry.name.includes('.js')) {
        console.log('Preinit resource:', {
          url: entry.name,
          startTime: entry.startTime,
          duration: entry.duration,
          transferSize: entry.transferSize,
          type: entry.name.endsWith('.css') ? 'style' : 'script'
        });
      }
    }
  });

  observer.observe({ entryTypes: ['resource'] });
}

// 检查资源是否已预初始化
function isResourcePreinited(href) {
  const links = document.querySelectorAll('link[rel="preload"]');
  return Array.from(links).some(link => link.href === href);
}

// 预初始化状态调试工具
function debugPreinitStatus() {
  const preinitedResources = Array.from(
    document.querySelectorAll('link[rel="preload"]')
  ).map(link => ({
    href: link.href,
    as: link.as,
    crossOrigin: link.crossOrigin,
    integrity: link.integrity,
    precedence: link.dataset.precedence,
    loaded: link.onload ? 'Yes' : 'Unknown'
  }));

  console.table(preinitedResources);
}`
              },
              {
                title: 'Performance API分析',
                description: '使用Performance API精确测量资源加载性能，对比预初始化前后的效果',
                solution: '1. 使用performance.mark标记关键时间点；2. 使用performance.measure计算时间差；3. 分析Resource Timing数据',
                prevention: '建立性能监控机制，持续跟踪预初始化效果',
                code: `// 性能测量工具
class PreinitPerformanceMonitor {
  constructor() {
    this.measurements = new Map();
  }

  // 开始测量
  startMeasure(resourceName) {
    performance.mark(\`\${resourceName}-preinit-start\`);
  }

  // 结束测量
  endMeasure(resourceName) {
    performance.mark(\`\${resourceName}-preinit-end\`);
    performance.measure(
      \`\${resourceName}-preinit-time\`,
      \`\${resourceName}-preinit-start\`,
      \`\${resourceName}-preinit-end\`
    );

    const measure = performance.getEntriesByName(\`\${resourceName}-preinit-time\`)[0];
    this.measurements.set(resourceName, measure.duration);

    console.log(\`Resource \${resourceName} preinit time: \${measure.duration}ms\`);
  }

  // 对比预初始化效果
  compareWithBaseline(resourceName, baselineTime) {
    const currentTime = this.measurements.get(resourceName);
    if (currentTime) {
      const improvement = ((baselineTime - currentTime) / baselineTime * 100).toFixed(2);
      console.log(\`Resource \${resourceName} improvement: \${improvement}%\`);
    }
  }

  // 生成性能报告
  generateReport() {
    const report = {
      totalResources: this.measurements.size,
      averageTime: this.calculateAverageTime(),
      measurements: Object.fromEntries(this.measurements)
    };

    console.log('Preinit Performance Report:', report);
    return report;
  }

  calculateAverageTime() {
    const times = Array.from(this.measurements.values());
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }
}

// 使用示例
const monitor = new PreinitPerformanceMonitor();

function measurePreinitEffect() {
  monitor.startMeasure('theme-css');

  preinit('/styles/theme.css', {
    as: 'style',
    precedence: 'medium'
  });

  // 模拟资源使用
  setTimeout(() => {
    monitor.endMeasure('theme-css');
  }, 100);
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;