import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const preinitData: ApiItem = {
  id: 'preinit',
  title: 'preinit',
  description: 'React DOM中用于预初始化通用资源的函数，支持CSS样式表和JavaScript脚本的预初始化，通过优先级调度和安全性保障，显著提升页面渲染性能和用户体验。',
  category: 'ReactDOM Resource API',
  difficulty: 'medium',

  syntax: `preinit(href: string, options: PreinitOptions): void`,
  example: `preinit('/styles/theme.css', {
  as: 'style',
  precedence: 'high'
});`,
  notes: '专门用于预初始化CSS和JS资源，支持优先级调度和安全性校验，是Resource Preloading APIs的核心组成部分。',

  version: 'React 18.2+',
  tags: ["ReactDOM", "Resource", "Performance", "Preloading"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default preinitData;