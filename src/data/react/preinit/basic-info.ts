import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: "preinit是React DOM中用于预初始化通用资源的函数，专门用于提前加载和初始化CSS样式表、字体文件等关键资源，确保资源在需要时能够立即可用，显著提升页面渲染性能和用户体验。",

  introduction: `preinit是React 18.2+引入的ReactDOM资源预初始化API，专门为现代Web资源管理设计的通用预初始化函数。

它遵循现代Web性能优化的最佳实践，在资源预加载和渲染优化之间做出了智能平衡，允许开发者提前声明需要的关键资源，让浏览器在最佳时机进行预加载和预初始化。

主要用于CSS样式表预初始化、字体文件预加载和关键资源优化。相比传统的link标签预加载，它的创新在于将资源的加载时机从"渲染时"提前到"声明时"，实现了更精细的资源加载控制。

在React生态中，它是Resource Preloading APIs的核心组成部分，常见于需要优化首屏渲染性能的大型应用，特别适合需要动态加载样式和字体的复杂场景。

核心优势包括减少样式闪烁、优化字体加载效果、提升首屏渲染性能，但也需要注意合理控制预初始化的资源数量以避免资源浪费。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react-dom/index.d.ts:42
 * - 实现文件：packages/react-dom/src/shared/ReactDOMResourceUtils.js:95
 * - 内部类型：packages/react-dom-bindings/src/shared/ReactDOMResourceValidation.js:65
 */

// 基础语法
function preinit(href: string, options: PreinitOptions): void;

// 选项接口
interface PreinitOptions {
  as: 'style' | 'script';    // 资源类型：样式表或脚本
  crossOrigin?: string;      // CORS设置：'anonymous' | 'use-credentials'
  integrity?: string;        // 子资源完整性校验
  nonce?: string;           // 内容安全策略随机数
  precedence?: string;      // 样式表优先级（仅as='style'时有效）
}

// 使用示例
preinit('/styles/critical.css', {
  as: 'style',
  precedence: 'high'
});

preinit('/scripts/analytics.js', {
  as: 'script',
  crossOrigin: 'anonymous',
  integrity: 'sha384-...'
});

/**
 * 参数约束：
 * - href 必须是有效的资源URL
 * - options.as 必须指定，支持 'style' 和 'script'
 * - precedence 只在 as='style' 时有效
 * - 同一个href多次调用会被去重
 */`,

  quickExample: `function ResourcePreinitExample() {
  useEffect(() => {
    // 预初始化关键CSS样式表
    preinit('/styles/theme.css', {
      as: 'style',
      precedence: 'high'
    });

    // 预初始化字体文件相关样式
    preinit('/styles/fonts.css', {
      as: 'style',
      precedence: 'medium'
    });

    // 预初始化第三方分析脚本
    preinit('/scripts/analytics.js', {
      as: 'script',
      crossOrigin: 'anonymous',
      integrity: 'sha384-oqVuAfXRKap7fdgcCY5uykM6+R9GqQ8K/uxy9rx7HNQlGYl1kPzQho1wx4JwY8wC'
    });

    // 条件性预初始化主题样式
    if (userPreferences.theme === 'dark') {
      preinit('/styles/dark-theme.css', {
        as: 'style',
        precedence: 'medium'
      });
    }
  }, []);

  const handleThemeChange = (newTheme) => {
    // 动态预初始化新主题样式
    preinit(\`/styles/\${newTheme}-theme.css\`, {
      as: 'style',
      precedence: 'medium'
    });

    // 应用主题变更
    setTheme(newTheme);
  };

  return (
    <div>
      {/* 样式已预初始化，渲染时立即可用 */}
      <header className="themed-header">
        应用标题 (样式已预加载)
      </header>
      <button onClick={() => handleThemeChange('dark')}>
        切换深色主题 (样式将预加载)
      </button>
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "preinit在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用这个API来优化资源加载性能",
      diagram: `graph LR
      A[preinit核心场景] --> B[样式表预初始化]
      A --> C[脚本预初始化]
      A --> D[字体资源优化]

      B --> B1["🎨 主题样式<br/>动态主题切换预加载"]
      B --> B2["📱 响应式CSS<br/>设备适配样式预初始化"]

      C --> C1["📊 分析脚本<br/>第三方统计工具预加载"]
      C --> C2["🔧 工具库<br/>功能模块脚本预初始化"]

      D --> D1["🔤 Web字体<br/>自定义字体文件预加载"]
      D --> D2["🌐 图标字体<br/>图标库字体预初始化"]

      style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
      style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
      style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "技术实现架构",
      description: "preinit的技术实现架构，展示其与浏览器资源管理和React渲染系统的集成关系",
      diagram: `graph TB
      A[preinit技术架构] --> B[浏览器资源系统]
      A --> C[React渲染管理]
      A --> D[性能优化层]

      B --> B1["🌐 Resource Hints<br/>preload/prefetch机制"]
      B --> B2["📋 Resource Cache<br/>浏览器资源缓存"]

      C --> C1["🎯 DOM管理<br/>动态link标签插入"]
      C --> C2["🔄 SSR支持<br/>服务端渲染兼容"]

      D --> D1["⚡ 优先级调度<br/>precedence优先级系统"]
      D --> D2["📈 加载策略<br/>智能资源加载时机"]

      style A fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
      style B fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
      style C fill:#fce4ec,stroke:#c2185b,stroke-width:2px
      style D fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px`
    },
    {
      title: "生态系统集成",
      description: "preinit在React资源管理生态系统中的位置和与其他Resource APIs的协作关系",
      diagram: `graph TD
      A[React Resource APIs生态] --> B[预加载APIs]
      A --> C[构建工具链]
      A --> D[性能监控]

      B --> B1["preload<br/>通用资源预加载"]
      B --> B2["preloadModule<br/>模块预加载"]
      B --> B3["preinit<br/>通用资源预初始化"]
      B --> B4["preinitModule<br/>模块预初始化"]

      C --> C1["Webpack<br/>资源打包集成"]
      C --> C2["PostCSS<br/>样式处理工具"]

      D --> D1["Web Vitals<br/>性能指标监控"]
      D --> D2["Lighthouse<br/>性能审计工具"]

      B3 -.-> B1
      B3 -.-> B4
      B3 -.-> C1
      B3 -.-> D1

      style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
      style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
      style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style B3 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
    }
  ],
  
  parameters: [
    {
      name: "href",
      type: "string",
      required: true,
      description: "要预初始化的资源的URL路径。必须是有效的资源路径，支持相对路径和绝对路径。",
      example: `preinit('/styles/theme.css', { as: 'style' });
preinit('https://cdn.example.com/font.css', { as: 'style' });`
    },
    {
      name: "options",
      type: "PreinitOptions",
      required: true,
      description: "必需的配置对象，用于指定资源类型和加载选项，包括资源类型、CORS设置、完整性校验、优先级等。",
      example: `preinit('/styles/critical.css', {
  as: 'style',
  precedence: 'high',
  crossOrigin: 'anonymous',
  integrity: 'sha384-...'
});`
    }
  ],

  returnValue: {
    type: "void",
    description: "preinit不返回任何值，它是一个纯粹的副作用函数，用于向浏览器发出资源预初始化的指令。",
    example: `// preinit没有返回值
preinit('/styles/theme.css', {
  as: 'style',
  precedence: 'high'
});
// 函数执行后，浏览器开始预初始化指定资源`
  },

  keyFeatures: [
    {
      title: "通用资源预初始化",
      description: "支持CSS样式表和JavaScript脚本的预初始化，确保资源在需要时立即可用",
      benefit: "显著减少样式闪烁和脚本加载延迟，提升页面渲染性能"
    },
    {
      title: "优先级调度系统",
      description: "通过precedence参数为CSS样式表提供优先级控制，确保关键样式优先加载",
      benefit: "优化样式加载顺序，避免样式冲突，提升首屏渲染效果"
    },
    {
      title: "安全性保障",
      description: "支持子资源完整性校验(SRI)和内容安全策略(CSP)，确保资源加载的安全性",
      benefit: "在性能优化的同时，保障应用的安全性要求"
    },
    {
      title: "CORS兼容性",
      description: "提供完整的跨域资源共享配置选项，支持不同的CORS策略",
      benefit: "能够安全地预初始化来自不同域的第三方资源和CDN内容"
    },
    {
      title: "React集成优化",
      description: "与React的资源管理系统深度集成，自动处理重复调用和资源去重",
      benefit: "避免重复预初始化同一资源，优化网络资源使用效率"
    },
    {
      title: "SSR友好设计",
      description: "在服务端渲染环境中能够正确处理，不会影响服务端的渲染过程",
      benefit: "确保同构应用在服务端和客户端都能正常工作，提升开发体验"
    }
  ],

  limitations: [
    "只能在React 18.2+版本中使用，在旧版本中不可用",
    "options.as参数必须指定，目前支持'style'和'script'两种类型",
    "precedence参数只在as='style'时有效，对script类型无效",
    "在服务端渲染环境中，preinit调用会被忽略，只在客户端生效",
    "过度使用可能导致不必要的网络请求和资源竞争，需要合理控制预初始化的资源数量"
  ],

  bestPractices: [
    "在应用启动时或组件挂载时调用preinit，为即将需要的资源做好准备",
    "为CSS样式表设置合适的precedence优先级，确保关键样式优先加载",
    "为第三方资源设置适当的integrity校验，确保资源的完整性和安全性",
    "使用crossOrigin设置来正确处理跨域资源的预初始化",
    "结合条件判断，只预初始化真正需要的资源，避免浪费网络带宽",
    "在主题切换、动态样式加载等场景中提前预初始化相关CSS文件",
    "配合构建工具的资源优化功能，实现最佳的资源加载策略",
    "在移动设备上谨慎使用，考虑网络条件和电池消耗的影响"
  ],

  warnings: [
    "不要预初始化所有可能的资源，这会导致不必要的网络开销和资源竞争",
    "确保预初始化的资源路径正确，错误的路径会导致404错误但不会抛出异常",
    "在使用第三方CDN资源时，务必设置正确的crossOrigin和integrity参数",
    "注意CSS样式的precedence设置，错误的优先级可能导致样式覆盖问题",
    "在严格的CSP环境中，确保nonce参数与页面的CSP策略匹配"
  ]
};

export default basicInfo;