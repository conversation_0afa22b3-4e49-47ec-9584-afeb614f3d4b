import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-1',
    question: 'preinit和preload有什么区别？什么时候应该使用preinit？',
    answer: `preinit和preload都是React的资源预加载API，但它们有重要的区别：

**主要区别：**
1. **功能范围**：preload是通用的资源预加载函数，而preinit专门用于资源的预初始化
2. **支持的资源类型**：preload支持更多资源类型（图片、字体、音频等），preinit主要针对CSS和JavaScript
3. **优先级控制**：preinit为CSS样式表提供了precedence优先级参数，preload没有这个功能
4. **使用场景**：preload适合预加载各种静态资源，preinit更适合需要立即生效的关键资源

**何时使用preinit：**
- 需要预加载CSS样式表并控制其优先级时
- 预加载关键的JavaScript脚本文件时
- 需要确保资源的安全性校验时（支持integrity参数）
- 在主题切换、动态样式加载等场景中

**何时使用preload：**
- 预加载图片、字体、音频等多媒体资源时
- 不需要特殊优先级控制的一般资源预加载
- 需要预加载的资源类型preinit不支持时`,
    code: `// ✅ 使用preinit预初始化CSS样式表
preinit('/styles/theme.css', {
  as: 'style',
  precedence: 'high' // 设置高优先级
});

// ✅ 使用preinit预初始化关键脚本
preinit('/scripts/analytics.js', {
  as: 'script',
  crossOrigin: 'anonymous',
  integrity: 'sha384-...'
});

// ✅ 使用preload预加载图片资源
preload('/images/hero-banner.jpg', {
  as: 'image'
});

// ✅ 使用preload预加载字体文件
preload('/fonts/custom-font.woff2', {
  as: 'font',
  type: 'font/woff2',
  crossOrigin: 'anonymous'
});

// ❌ 错误：preinit不支持图片类型
// preinit('/images/logo.png', { as: 'image' }); // 这会失败

// ❌ 错误：preload无法设置CSS优先级
// preload('/styles/theme.css', {
//   as: 'style',
//   precedence: 'high' // preload不支持precedence
// });`,
    tags: ['API对比', '使用场景'],
    relatedQuestions: ['preinit支持哪些资源类型？', 'precedence参数如何工作？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-2',
    question: 'precedence参数是如何工作的？如何正确设置CSS样式的优先级？',
    answer: `precedence参数是preinit的一个重要特性，专门用于控制CSS样式表的加载和应用优先级。

**工作原理：**
1. **优先级排序**：浏览器根据precedence值对CSS样式表进行排序
2. **加载顺序控制**：高优先级的样式表会优先加载和解析
3. **样式覆盖规则**：后加载的同优先级样式会覆盖先加载的样式
4. **渲染阻塞优化**：关键样式（高优先级）会优先处理，减少渲染阻塞

**优先级设置策略：**
- **'high'**：关键样式，如基础布局、重要组件样式
- **'medium'**：一般样式，如主题样式、功能模块样式
- **'low'**：可选样式，如装饰性样式、非关键组件样式

**最佳实践：**
- 为基础样式设置高优先级，确保页面结构稳定
- 为主题样式设置中等优先级，平衡加载性能
- 为装饰性样式设置低优先级，避免阻塞关键渲染`,
    code: `// ✅ 正确的优先级设置示例
function StylePriorityExample() {
  useEffect(() => {
    // 高优先级：基础样式和布局
    preinit('/styles/reset.css', {
      as: 'style',
      precedence: 'high'
    });

    preinit('/styles/layout.css', {
      as: 'style',
      precedence: 'high'
    });

    // 中等优先级：主题和功能样式
    preinit('/styles/theme.css', {
      as: 'style',
      precedence: 'medium'
    });

    preinit('/styles/components.css', {
      as: 'style',
      precedence: 'medium'
    });

    // 低优先级：装饰和可选样式
    preinit('/styles/animations.css', {
      as: 'style',
      precedence: 'low'
    });

    preinit('/styles/print.css', {
      as: 'style',
      precedence: 'low'
    });
  }, []);
}

// ✅ 动态优先级调整
function DynamicPriorityExample() {
  const [isThemeImportant, setIsThemeImportant] = useState(false);

  const loadThemeWithPriority = (priority) => {
    preinit('/styles/dynamic-theme.css', {
      as: 'style',
      precedence: priority
    });
  };

  return (
    <div>
      <button onClick={() => loadThemeWithPriority('high')}>
        高优先级加载主题
      </button>
      <button onClick={() => loadThemeWithPriority('low')}>
        低优先级加载主题
      </button>
    </div>
  );
}

// ❌ 错误的优先级设置
// 不要为所有样式都设置高优先级
preinit('/styles/decorative.css', {
  as: 'style',
  precedence: 'high' // ❌ 装饰性样式不应该是高优先级
});`,
    tags: ['优先级控制', 'CSS优化'],
    relatedQuestions: ['如何避免样式冲突？', 'CSS加载顺序对性能的影响？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-3',
    question: '在SSR环境中使用preinit需要注意什么？如何确保服务端和客户端的一致性？',
    answer: `在服务端渲染(SSR)环境中使用preinit需要特别注意，因为preinit在服务端和客户端的行为是不同的。

**SSR环境中的行为：**
1. **服务端忽略**：在服务端，preinit调用会被静默忽略，不会执行任何操作
2. **客户端激活**：只有在客户端hydration完成后，preinit才开始工作
3. **状态同步**：需要确保服务端和客户端的组件状态一致

**关键注意事项：**
- 不要依赖preinit的执行来维护应用状态
- 在useEffect中调用preinit，确保只在客户端执行
- 考虑使用条件判断来检测环境
- 为SSR场景提供fallback方案

**最佳实践：**
- 使用环境检测确保兼容性
- 在Next.js等框架中正确配置
- 避免在服务端渲染过程中依赖预初始化的资源
- 为关键样式提供内联备用方案`,
    code: `// ✅ SSR友好的preinit使用方式
function SSRFriendlyComponent() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 确保只在客户端执行
    setIsClient(true);

    if (typeof window !== 'undefined') {
      preinit('/styles/client-theme.css', {
        as: 'style',
        precedence: 'medium'
      });
    }
  }, []);

  // 为SSR提供基础样式
  const serverStyles = {
    backgroundColor: '#ffffff',
    color: '#333333',
    padding: '1rem'
  };

  return (
    <div style={!isClient ? serverStyles : undefined}>
      <h1>SSR兼容组件</h1>
      {isClient && <p>客户端特定内容</p>}
    </div>
  );
}

// ✅ Next.js中的正确使用方式
function NextJSExample() {
  useEffect(() => {
    // Next.js环境检测
    if (typeof window !== 'undefined') {
      preinit('/styles/nextjs-theme.css', {
        as: 'style',
        precedence: 'high'
      });
    }
  }, []);

  return <div>Next.js组件内容</div>;
}

// ✅ 使用自定义Hook封装
function useClientSidePreinit(href, options) {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      preinit(href, options);
    }
  }, [href, options]);
}

function ComponentWithCustomHook() {
  useClientSidePreinit('/styles/component.css', {
    as: 'style',
    precedence: 'medium'
  });

  return <div>使用自定义Hook的组件</div>;
}

// ✅ 条件性预初始化
function ConditionalPreinit() {
  const [shouldPreinit, setShouldPreinit] = useState(false);

  useEffect(() => {
    // 检测客户端环境和条件
    if (typeof window !== 'undefined' && window.innerWidth > 768) {
      setShouldPreinit(true);

      preinit('/styles/desktop-theme.css', {
        as: 'style',
        precedence: 'medium'
      });
    }
  }, []);

  return (
    <div>
      <p>响应式预初始化组件</p>
      {shouldPreinit && <p>桌面端样式已预加载</p>}
    </div>
  );
}

// ❌ 错误的SSR使用方式
function BadSSRExample() {
  // ❌ 不要在组件顶层直接调用
  preinit('/styles/bad-example.css', {
    as: 'style',
    precedence: 'high'
  });

  // ❌ 不要依赖preinit的执行结果
  const [stylesLoaded, setStylesLoaded] = useState(false);

  // 这在SSR中不会工作
  preinit('/styles/dependency.css', {
    as: 'style',
    precedence: 'high'
  });
  setStylesLoaded(true); // ❌ 错误的依赖关系

  return <div>错误的SSR示例</div>;
}`,
    tags: ['SSR兼容性', '服务端渲染'],
    relatedQuestions: ['如何在Next.js中使用preinit？', 'Hydration过程中的注意事项？']
  }
];

export default commonQuestions;