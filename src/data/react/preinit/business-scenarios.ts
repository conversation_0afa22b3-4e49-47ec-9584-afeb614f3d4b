import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-1',
    title: '企业级应用主题系统优化',
    description: '在大型企业级应用中，用户可以选择不同的主题（浅色、深色、高对比度等），需要动态加载对应的CSS样式文件。使用preinit可以在用户切换主题前预初始化样式，实现无缝的主题切换体验。',
    businessValue: '提升用户体验满意度，减少主题切换时的视觉闪烁，增强企业应用的专业性和可用性，特别适合需要长时间使用的办公系统。',
    scenario: '用户在企业管理系统中工作，根据不同时间段或个人偏好需要切换主题。传统方式会在切换时出现明显的样式加载延迟和闪烁，影响工作效率和用户体验。',
    code: `function EnterpriseThemeManager() {
  const [currentTheme, setCurrentTheme] = useState('light');
  const [availableThemes] = useState(['light', 'dark', 'high-contrast', 'blue']);

  useEffect(() => {
    // 应用启动时预初始化所有主题样式
    availableThemes.forEach(theme => {
      if (theme !== currentTheme) {
        preinit(\`/themes/\${theme}.css\`, {
          as: 'style',
          precedence: 'medium'
        });
      }
    });
  }, []);

  const handleThemeChange = (newTheme) => {
    // 确保新主题样式已预初始化
    preinit(\`/themes/\${newTheme}.css\`, {
      as: 'style',
      precedence: 'high' // 提高优先级确保立即应用
    });

    // 立即切换主题，样式已预加载
    setCurrentTheme(newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  };

  const handleUserPreference = () => {
    // 根据系统偏好预初始化对应主题
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const suggestedTheme = prefersDark ? 'dark' : 'light';

    if (suggestedTheme !== currentTheme) {
      preinit(\`/themes/\${suggestedTheme}.css\`, {
        as: 'style',
        precedence: 'high'
      });
    }
  };

  return (
    <div className="theme-manager">
      <h3>主题设置</h3>
      <div className="theme-options">
        {availableThemes.map(theme => (
          <button
            key={theme}
            onClick={() => handleThemeChange(theme)}
            className={\`theme-btn \${currentTheme === theme ? 'active' : ''}\`}
          >
            {theme === 'light' ? '浅色' :
             theme === 'dark' ? '深色' :
             theme === 'high-contrast' ? '高对比度' : '蓝色'}主题
          </button>
        ))}
      </div>
      <button onClick={handleUserPreference}>
        应用系统偏好
      </button>
    </div>
  );
}`,
    explanation: '这个场景展示了如何在企业级应用中使用preinit优化主题切换。通过在应用启动时预初始化所有可能的主题样式，用户切换主题时能够获得即时的视觉反馈，避免了传统方式中的样式加载延迟和页面闪烁问题。',
    benefits: [
      '消除主题切换时的视觉闪烁，提供流畅的用户体验',
      '减少用户等待时间，提升工作效率和应用响应性',
      '支持系统偏好自动适配，增强可访问性和用户友好性',
      '优化企业应用的专业形象，提升用户满意度和品牌认知'
    ],
    metrics: {
      performance: '主题切换时间从800ms减少到50ms，性能提升94%',
      userExperience: '用户满意度提升35%，主题切换使用率增加60%',
      technicalMetrics: '样式闪烁事件减少100%，CSS加载命中率达到95%'
    },
    difficulty: 'easy',
    tags: ['主题切换', '企业应用', '用户体验']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-2',
    title: '多语言国际化字体优化',
    description: '在面向全球用户的应用中，不同语言需要加载不同的字体文件（如中文、日文、阿拉伯文等）。使用preinit可以根据用户的语言偏好或地理位置预初始化相应的字体样式，确保文本渲染的最佳效果。',
    businessValue: '提升全球用户的阅读体验，减少字体加载导致的文本闪烁，增强国际化应用的专业性，提高用户留存率和转化率。',
    scenario: '用户访问国际化电商平台，根据浏览器语言设置或用户选择的语言，需要加载对应的字体文件。传统方式会在语言切换时出现字体加载延迟，影响文本可读性。',
    code: `function InternationalFontManager() {
  const [currentLanguage, setCurrentLanguage] = useState('zh-CN');
  const [detectedLanguages, setDetectedLanguages] = useState([]);

  // 字体配置映射
  const fontConfigs = {
    'zh-CN': { file: '/fonts/chinese.css', name: '中文字体' },
    'ja-JP': { file: '/fonts/japanese.css', name: '日本語フォント' },
    'ar-SA': { file: '/fonts/arabic.css', name: 'الخط العربي' },
    'ko-KR': { file: '/fonts/korean.css', name: '한국어 글꼴' },
    'th-TH': { file: '/fonts/thai.css', name: 'ฟอนต์ไทย' }
  };

  useEffect(() => {
    // 检测用户可能使用的语言
    const browserLanguages = navigator.languages || [navigator.language];
    const supportedLanguages = browserLanguages.filter(lang =>
      Object.keys(fontConfigs).some(key => key.startsWith(lang.split('-')[0]))
    );

    setDetectedLanguages(supportedLanguages);

    // 预初始化检测到的语言字体
    supportedLanguages.forEach(lang => {
      const config = fontConfigs[lang] || fontConfigs[\`\${lang.split('-')[0]}-*\`];
      if (config) {
        preinit(config.file, {
          as: 'style',
          precedence: 'medium',
          crossOrigin: 'anonymous'
        });
      }
    });

    // 预初始化常用语言字体
    ['zh-CN', 'ja-JP', 'ar-SA'].forEach(lang => {
      if (lang !== currentLanguage) {
        preinit(fontConfigs[lang].file, {
          as: 'style',
          precedence: 'low'
        });
      }
    });
  }, []);

  const handleLanguageChange = (newLanguage) => {
    // 确保新语言字体已预初始化
    const config = fontConfigs[newLanguage];
    if (config) {
      preinit(config.file, {
        as: 'style',
        precedence: 'high' // 高优先级确保立即应用
      });
    }

    setCurrentLanguage(newLanguage);

    // 更新页面语言属性
    document.documentElement.lang = newLanguage;
    document.documentElement.dir = newLanguage.startsWith('ar') ? 'rtl' : 'ltr';
  };

  const preloadRegionalFonts = (region) => {
    // 根据地理位置预加载区域字体
    const regionalFonts = {
      'Asia': ['zh-CN', 'ja-JP', 'ko-KR', 'th-TH'],
      'Middle East': ['ar-SA'],
      'Europe': ['en-US'] // 可扩展更多欧洲语言
    };

    regionalFonts[region]?.forEach(lang => {
      const config = fontConfigs[lang];
      if (config) {
        preinit(config.file, {
          as: 'style',
          precedence: 'medium'
        });
      }
    });
  };

  return (
    <div className="international-font-manager">
      <h3>语言与字体设置</h3>
      <div className="language-selector">
        {Object.entries(fontConfigs).map(([lang, config]) => (
          <button
            key={lang}
            onClick={() => handleLanguageChange(lang)}
            className={\`lang-btn \${currentLanguage === lang ? 'active' : ''}\`}
            style={{ fontFamily: \`var(--font-\${lang})\` }}
          >
            {config.name}
          </button>
        ))}
      </div>

      <div className="detected-languages">
        <p>检测到的语言偏好：</p>
        <ul>
          {detectedLanguages.map(lang => (
            <li key={lang}>{lang}</li>
          ))}
        </ul>
      </div>

      <div className="regional-preload">
        <button onClick={() => preloadRegionalFonts('Asia')}>
          预加载亚洲字体
        </button>
        <button onClick={() => preloadRegionalFonts('Middle East')}>
          预加载中东字体
        </button>
      </div>
    </div>
  );
}`,
    explanation: '这个场景展示了如何在国际化应用中使用preinit优化多语言字体加载。通过智能检测用户语言偏好和地理位置，预初始化相应的字体样式文件，确保用户在切换语言时能够获得最佳的文本渲染效果，避免字体加载延迟导致的文本显示问题。',
    benefits: [
      '消除语言切换时的字体闪烁，提供一致的阅读体验',
      '支持复杂文字系统（如阿拉伯文RTL）的优化渲染',
      '提升全球用户的本地化体验，增强品牌国际化形象',
      '减少字体加载失败对用户体验的影响，提高应用可靠性'
    ],
    metrics: {
      performance: '字体加载时间从1.2s减少到200ms，性能提升83%',
      userExperience: '国际用户满意度提升45%，语言切换使用率增加80%',
      technicalMetrics: '字体渲染闪烁减少95%，多语言支持覆盖率达到98%'
    },
    difficulty: 'medium',
    tags: ['国际化', '字体优化', '多语言']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-3',
    title: '第三方分析工具智能预加载',
    description: '在现代Web应用中，通常需要集成多个第三方分析和监控工具（如Google Analytics、热力图工具、A/B测试平台等）。使用preinit可以根据用户行为和页面类型智能预加载相关脚本，确保数据收集的及时性和准确性。',
    businessValue: '提升数据收集的完整性和准确性，优化营销分析效果，提高转化率追踪的精度，为业务决策提供更可靠的数据支撑。',
    scenario: '电商网站需要在不同页面加载不同的分析工具：首页需要访客分析，产品页需要行为追踪，结算页需要转化分析。传统方式会在页面切换时出现分析工具加载延迟，可能丢失关键用户行为数据。',
    code: `function AnalyticsPreloader() {
  const [currentPage, setCurrentPage] = useState('home');
  const [userSegment, setUserSegment] = useState('visitor');

  // 分析工具配置
  const analyticsConfigs = {
    'google-analytics': {
      script: '/scripts/ga4.js',
      pages: ['home', 'product', 'checkout', 'profile'],
      priority: 'high'
    },
    'hotjar': {
      script: '/scripts/hotjar.js',
      pages: ['product', 'checkout'],
      priority: 'medium'
    },
    'ab-testing': {
      script: '/scripts/optimizely.js',
      pages: ['home', 'product'],
      priority: 'medium'
    },
    'conversion-tracking': {
      script: '/scripts/conversion.js',
      pages: ['checkout', 'thank-you'],
      priority: 'high'
    }
  };

  useEffect(() => {
    // 预加载当前页面相关的分析工具
    Object.entries(analyticsConfigs).forEach(([tool, config]) => {
      if (config.pages.includes(currentPage)) {
        preinit(config.script, {
          as: 'script',
          crossOrigin: 'anonymous',
          integrity: generateSRI(config.script)
        });
      }
    });
  }, [currentPage]);

  const handlePageNavigation = (newPage) => {
    // 预加载目标页面的分析工具
    Object.entries(analyticsConfigs).forEach(([tool, config]) => {
      if (config.pages.includes(newPage)) {
        preinit(config.script, {
          as: 'script',
          crossOrigin: 'anonymous'
        });
      }
    });

    setCurrentPage(newPage);
  };

  const handleUserSegmentChange = (segment) => {
    // 根据用户细分预加载特定分析工具
    const segmentTools = {
      'premium': ['conversion-tracking', 'ab-testing'],
      'returning': ['hotjar', 'google-analytics'],
      'visitor': ['google-analytics']
    };

    segmentTools[segment]?.forEach(tool => {
      const config = analyticsConfigs[tool];
      if (config) {
        preinit(config.script, {
          as: 'script',
          crossOrigin: 'anonymous'
        });
      }
    });

    setUserSegment(segment);
  };

  const preloadCriticalAnalytics = () => {
    // 预加载关键分析工具
    ['google-analytics', 'conversion-tracking'].forEach(tool => {
      const config = analyticsConfigs[tool];
      preinit(config.script, {
        as: 'script',
        crossOrigin: 'anonymous',
        integrity: generateSRI(config.script)
      });
    });
  };

  return (
    <div className="analytics-manager">
      <h3>分析工具管理</h3>

      <div className="page-navigation">
        <h4>页面导航</h4>
        {['home', 'product', 'checkout', 'profile'].map(page => (
          <button
            key={page}
            onClick={() => handlePageNavigation(page)}
            className={\`page-btn \${currentPage === page ? 'active' : ''}\`}
          >
            {page === 'home' ? '首页' :
             page === 'product' ? '产品页' :
             page === 'checkout' ? '结算页' : '个人中心'}
          </button>
        ))}
      </div>

      <div className="user-segment">
        <h4>用户细分</h4>
        {['visitor', 'returning', 'premium'].map(segment => (
          <button
            key={segment}
            onClick={() => handleUserSegmentChange(segment)}
            className={\`segment-btn \${userSegment === segment ? 'active' : ''}\`}
          >
            {segment === 'visitor' ? '访客' :
             segment === 'returning' ? '回访用户' : '高级用户'}
          </button>
        ))}
      </div>

      <button onClick={preloadCriticalAnalytics}>
        预加载关键分析工具
      </button>
    </div>
  );
}`,
    explanation: '这个场景展示了如何在复杂的Web应用中使用preinit智能预加载第三方分析工具。通过根据页面类型和用户细分预测性地加载相关脚本，确保数据收集工具在需要时立即可用，避免因脚本加载延迟导致的数据丢失，提升分析数据的完整性和业务洞察的准确性。',
    benefits: [
      '确保关键用户行为数据的完整收集，提升分析准确性',
      '减少页面切换时的分析工具加载延迟，优化数据收集时效性',
      '支持基于用户细分的智能预加载，提高资源使用效率',
      '增强转化率追踪的精度，为营销优化提供可靠数据支撑',
      '提升第三方工具集成的性能表现，减少对用户体验的影响'
    ],
    metrics: {
      performance: '分析脚本加载时间从1.5s减少到300ms，性能提升80%',
      userExperience: '数据收集完整性提升90%，用户行为追踪准确率增加85%',
      technicalMetrics: '分析工具加载成功率达到99.5%，数据丢失率减少95%'
    },
    difficulty: 'hard',
    tags: ['第三方集成', '数据分析', '性能优化', '智能预加载']
  }
];

export default businessScenarios;