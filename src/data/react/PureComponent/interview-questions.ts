import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'PureComponent和React.Component有什么区别？什么时候使用PureComponent？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'PureComponent自动实现了shouldComponentUpdate的浅比较，适用于props和state相对简单的组件',
      detailed: `PureComponent是React.Component的一个子类，它们的主要区别在于：

**1. shouldComponentUpdate实现**
- Component: 需要手动实现shouldComponentUpdate来优化性能
- PureComponent: 自动实现了shouldComponentUpdate，使用浅比较

**2. 比较方式**
- PureComponent对props和state进行浅比较（shallow comparison）
- 只比较对象的第一层属性，不会深入比较嵌套对象

**3. 使用场景**
- 适用于props和state结构相对简单的组件
- 展示型组件（Presentational Components）
- 数据相对稳定，不频繁变化的组件

**4. 性能影响**
- 减少不必要的重新渲染
- 但浅比较本身也有计算开销`,
      code: `// React.Component - 需要手动优化
class MyComponent extends React.Component {
  shouldComponentUpdate(nextProps, nextState) {
    return (
      this.props.name !== nextProps.name ||
      this.props.age !== nextProps.age ||
      this.state.count !== nextState.count
    );
  }
  
  render() {
    return <div>{this.props.name}: {this.state.count}</div>;
  }
}

// PureComponent - 自动浅比较
class MyPureComponent extends PureComponent {
  render() {
    return <div>{this.props.name}: {this.state.count}</div>;
  }
}

// 使用示例
function App() {
  const [count, setCount] = useState(0);
  const user = { name: 'John', age: 30 };
  
  return (
    <div>
      <button onClick={() => setCount(count + 1)}>
        重新渲染父组件: {count}
      </button>
      {/* 只有user对象引用变化时才重新渲染 */}
      <MyPureComponent name={user.name} age={user.age} />
    </div>
  );
}`
    },
    tags: ['基础概念', '性能优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: 'PureComponent的浅比较是如何工作的？有什么限制？如何解决深层对象变化检测问题？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: 'PureComponent使用Object.is()进行浅比较，只检查第一层属性，深层对象变化需要使用immutable更新模式',
      detailed: `**浅比较工作原理：**

1. **比较算法**: 使用类似Object.is()的方法比较props和state
2. **比较层级**: 只比较对象的第一层属性
3. **比较时机**: 在每次可能的重新渲染前自动执行

**浅比较的限制：**

1. **深层对象变化无法检测**
   - 嵌套对象内部的变化不会触发重新渲染
   - 数组内元素的变化可能被忽略

2. **引用比较依赖**
   - 依赖对象引用的变化而非值的变化
   - 直接修改对象属性不会触发更新

**解决方案：**

1. **Immutable更新模式**
2. **使用扩展操作符创建新对象**
3. **配合immutable.js等库**
4. **避免在render中创建新对象**`,
      code: `// ❌ 问题示例 - 深层对象变化无法检测
class ProblematicComponent extends PureComponent {
  render() {
    const { user } = this.props;
    return <div>{user.profile.name}</div>;
  }
}

function Parent() {
  const [user, setUser] = useState({
    id: 1,
    profile: { name: 'John', age: 30 }
  });
  
  const updateName = () => {
    // ❌ 直接修改深层对象 - PureComponent不会重新渲染
    user.profile.name = 'Jane';
    setUser(user);
  };
  
  return (
    <div>
      <button onClick={updateName}>更新姓名</button>
      <ProblematicComponent user={user} />
    </div>
  );
}

// ✅ 正确示例 - 使用immutable更新模式
function Parent() {
  const [user, setUser] = useState({
    id: 1,
    profile: { name: 'John', age: 30 }
  });
  
  const updateName = () => {
    // ✅ 创建新对象 - PureComponent会重新渲染
    setUser({
      ...user,
      profile: {
        ...user.profile,
        name: 'Jane'
      }
    });
  };
  
  return (
    <div>
      <button onClick={updateName}>更新姓名</button>
      <ProblematicComponent user={user} />
    </div>
  );
}

// ✅ 使用useCallback避免函数重新创建
function OptimizedParent() {
  const [user, setUser] = useState({...});
  
  const handleUpdate = useCallback((newName) => {
    setUser(prev => ({
      ...prev,
      profile: { ...prev.profile, name: newName }
    }));
  }, []);
  
  return <ProblematicComponent user={user} onUpdate={handleUpdate} />;
}`
    },
    tags: ['浅比较', 'Immutable']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 3,
    question: '在什么情况下PureComponent可能反而降低性能？如何在现代React中替代PureComponent？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    answer: {
      brief: 'PureComponent在频繁变化的props或复杂比较时可能降低性能，现代React推荐使用函数组件配合React.memo',
      detailed: `**PureComponent降低性能的场景：**

1. **频繁变化的props**
   - 浅比较的开销可能超过重新渲染的开销
   - 每次都需要执行比较算法

2. **复杂的props结构**
   - 大量属性的比较开销
   - 深层嵌套对象的引用检查

3. **错误的使用模式**
   - 在render中创建新对象作为props
   - 使用内联函数作为props

**现代React替代方案：**

1. **React.memo + 自定义比较**
2. **useMemo和useCallback优化**
3. **状态提升和Context模式**
4. **组件拆分和细粒度控制**`,
      code: `// ❌ PureComponent性能问题示例
class ExpensivePureComponent extends PureComponent {
  render() {
    const { items, filters, onItemClick } = this.props;
    
    // 大量props的浅比较开销可能很大
    return (
      <div>
        {items.map(item => (
          <div key={item.id} onClick={() => onItemClick(item)}>
            {item.name}
          </div>
        ))}
      </div>
    );
  }
}

function ProblematicParent() {
  const [data, setData] = useState([]);
  
  return (
    <ExpensivePureComponent 
      items={data}
      filters={{ category: 'all' }} // ❌ 每次渲染都创建新对象
      onItemClick={(item) => console.log(item)} // ❌ 每次渲染都创建新函数
    />
  );
}

// ✅ 现代React优化方案
const OptimizedComponent = React.memo(({ items, filters, onItemClick }) => {
  return (
    <div>
      {items.map(item => (
        <div key={item.id} onClick={() => onItemClick(item)}>
          {item.name}
        </div>
      ))}
    </div>
  );
}, (prevProps, nextProps) => {
  // 自定义比较逻辑 - 只比较真正重要的属性
  return (
    prevProps.items.length === nextProps.items.length &&
    prevProps.filters.category === nextProps.filters.category
  );
});

function OptimizedParent() {
  const [data, setData] = useState([]);
  
  // ✅ 使用useMemo缓存filters对象
  const filters = useMemo(() => ({ category: 'all' }), []);
  
  // ✅ 使用useCallback缓存函数
  const handleItemClick = useCallback((item) => {
    console.log(item);
  }, []);
  
  return (
    <OptimizedComponent 
      items={data}
      filters={filters}
      onItemClick={handleItemClick}
    />
  );
}

// ✅ 更现代的解决方案 - 组件拆分
const ItemComponent = React.memo(({ item, onClick }) => (
  <div onClick={() => onClick(item)}>
    {item.name}
  </div>
));

const ListComponent = ({ items, onItemClick }) => (
  <div>
    {items.map(item => (
      <ItemComponent 
        key={item.id} 
        item={item} 
        onClick={onItemClick} 
      />
    ))}
  </div>
);`
    },
    tags: ['性能优化', '架构设计']
  }
];

// PureComponent面试问题内容已完成
export default interviewQuestions;