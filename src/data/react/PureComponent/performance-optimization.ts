import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  bestPractices: [
    {
      practice: "使用immutable数据更新模式",
      description: "确保PureComponent能够正确检测到props和state的变化",
      implementation: "总是创建新对象而不是修改现有对象",
      example: `// ❌ 错误做法 - 直接修改对象
const updateUser = (user) => {
  user.name = 'New Name';
  return user;
};

// ✅ 正确做法 - 创建新对象
const updateUser = (user) => ({
  ...user,
  name: 'New Name'
});

// ✅ 深层对象更新
const updateUserProfile = (user) => ({
  ...user,
  profile: {
    ...user.profile,
    name: 'New Name'
  }
});`
    },
    {
      practice: "缓存函数引用和对象引用",
      description: "使用useCallback和useMemo避免在每次渲染时创建新的引用",
      implementation: "在父组件中使用React Hooks进行引用缓存",
      example: `function ParentComponent() {
  const [data, setData] = useState([]);
  
  // ✅ 缓存函数引用
  const handleClick = useCallback((id) => {
    console.log('Clicked:', id);
  }, []);
  
  // ✅ 缓存对象引用
  const config = useMemo(() => ({
    theme: 'dark',
    layout: 'grid'
  }), []);
  
  // ✅ 缓存计算结果
  const processedData = useMemo(() => 
    data.filter(item => item.active).map(item => ({
      ...item,
      processed: true
    })), 
    [data]
  );
  
  return (
    <PureChildComponent
      data={processedData}
      config={config}
      onClick={handleClick}
    />
  );
}`
    },
    {
      practice: "合理拆分组件粒度",
      description: "将大组件拆分为更小的PureComponent，减少不必要的重新渲染范围",
      implementation: "按功能和数据依赖关系拆分组件",
      example: `// ❌ 单一大组件 - 任何变化都会重新渲染整个组件
class LargeComponent extends PureComponent {
  render() {
    const { user, posts, comments, settings } = this.props;
    return (
      <div>
        <UserProfile user={user} />
        <PostsList posts={posts} />
        <CommentsList comments={comments} />
        <Settings settings={settings} />
      </div>
    );
  }
}

// ✅ 拆分为独立的PureComponent
const UserProfile = React.memo(({ user }) => (
  <div>{user.name}</div>
));

const PostsList = React.memo(({ posts }) => (
  <div>{posts.map(post => <Post key={post.id} post={post} />)}</div>
));

const CommentsList = React.memo(({ comments }) => (
  <div>{comments.map(comment => <Comment key={comment.id} comment={comment} />)}</div>
));

function MainComponent({ user, posts, comments, settings }) {
  return (
    <div>
      <UserProfile user={user} />
      <PostsList posts={posts} />
      <CommentsList comments={comments} />
      <Settings settings={settings} />
    </div>
  );
}`
    },
    {
      practice: "避免在render中创建新对象",
      description: "在组件渲染函数中避免创建新的对象、数组或函数",
      implementation: "将对象创建移到组件外部或使用缓存机制",
      example: `// ❌ 错误做法 - 在render中创建新对象
function Parent() {
  return (
    <PureChild
      style={{ marginTop: 10 }} // 每次都创建新对象
      options={['option1', 'option2']} // 每次都创建新数组
      onClick={() => console.log('click')} // 每次都创建新函数
    />
  );
}

// ✅ 正确做法 - 提取到组件外部或使用缓存
const defaultStyle = { marginTop: 10 };
const defaultOptions = ['option1', 'option2'];

function Parent() {
  const handleClick = useCallback(() => {
    console.log('click');
  }, []);
  
  return (
    <PureChild
      style={defaultStyle}
      options={defaultOptions}
      onClick={handleClick}
    />
  );
}`
    },
    {
      practice: "选择性使用PureComponent",
      description: "不是所有组件都适合使用PureComponent，需要根据具体场景判断",
      implementation: "分析组件的更新频率和props复杂度",
      example: `// 适合使用PureComponent的场景
class ProductCard extends PureComponent {
  render() {
    const { product } = this.props;
    // 展示型组件，props相对稳定
    return (
      <div>
        <img src={product.image} />
        <h3>{product.name}</h3>
        <p>{product.price}</p>
      </div>
    );
  }
}

// 不适合使用PureComponent的场景
class Timer extends React.Component {
  // 频繁更新的组件，浅比较开销可能超过渲染开销
  render() {
    return <div>{this.props.currentTime}</div>;
  }
}`
    }
  ],
  
  monitoring: {
    tools: [
      {
        name: "React DevTools Profiler",
        description: "分析组件渲染性能和重新渲染原因",
        usage: "开发者工具中的Profiler tab，记录组件渲染过程"
      },
      {
        name: "why-did-you-render",
        description: "检测不必要的组件重新渲染",
        usage: "npm包，开发环境中监控并报告重新渲染原因"
      },
      {
        name: "React DevTools Profiler API",
        description: "程序化性能监控",
        usage: "在代码中使用Profiler组件包装需要监控的组件"
      }
    ],
    
    metrics: [
      "组件渲染次数",
      "浅比较执行时间",
      "props变化频率",
      "父组件更新频率",
      "实际DOM更新次数"
    ],
    
    setupInstructions: `// 1. 安装why-did-you-render (开发环境)
npm install --save-dev @welldone-software/why-did-you-render

// 2. 在应用入口配置
import React from 'react';
import wdyr from '@welldone-software/why-did-you-render';

if (process.env.NODE_ENV === 'development') {
  wdyr(React, {
    trackAllPureComponents: true,
    logOnDifferentValues: true
  });
}

// 3. 在组件上添加监控标记
class MyPureComponent extends PureComponent {
  static whyDidYouRender = true;
  
  render() {
    return <div>{this.props.children}</div>;
  }
}

// 4. 使用Profiler API监控特定组件
function App() {
  return (
    <Profiler id="MyPureComponent" onRender={onRenderCallback}>
      <MyPureComponent />
    </Profiler>
  );
}

function onRenderCallback(id, phase, actualDuration, baseDuration, startTime, commitTime, interactions) {
  console.log('组件渲染信息:', {
    id,
    phase,
    actualDuration,
    baseDuration
  });
}`
  },
  
  optimization: {
    codeOptimization: [
      {
        technique: "props结构优化",
        description: "减少props层级和数量，合并相关属性",
        example: `// ❌ 复杂的props结构
<UserCard 
  firstName={user.firstName}
  lastName={user.lastName}
  email={user.email}
  avatar={user.avatar}
  isOnline={user.status.isOnline}
  lastSeen={user.status.lastSeen}
/>

// ✅ 简化的props结构
<UserCard user={user} />`
      },
      {
        technique: "使用React.memo替代PureComponent",
        description: "在现代React中，优先使用函数组件配合React.memo",
        example: `// 传统PureComponent
class UserCard extends PureComponent {
  render() {
    return <div>{this.props.user.name}</div>;
  }
}

// 现代React.memo方式
const UserCard = React.memo(({ user }) => (
  <div>{user.name}</div>
));

// 带自定义比较的React.memo
const UserCard = React.memo(({ user }) => (
  <div>{user.name}</div>
), (prevProps, nextProps) => (
  prevProps.user.id === nextProps.user.id &&
  prevProps.user.name === nextProps.user.name
));`
      }
    ],
    
    architectureOptimization: [
      {
        pattern: "状态提升和Context模式",
        description: "合理使用Context避免props drilling，减少中间组件的不必要更新",
        benefits: ["减少props传递层级", "避免中间组件重新渲染", "更清晰的数据流"]
      },
      {
        pattern: "组件懒加载",
        description: "使用React.lazy和Suspense延迟加载非关键组件",
        benefits: ["减少初始包大小", "提升首次渲染性能", "按需加载资源"]
      },
      {
        pattern: "虚拟化列表",
        description: "对于大量数据的列表，使用虚拟化技术只渲染可见项",
        benefits: ["减少DOM节点数量", "提升滚动性能", "降低内存使用"]
      }
    ]
  },
  
  caseStudy: {
    title: "电商平台商品列表性能优化案例",
    
    problem: "电商平台的商品列表页面在滚动时出现卡顿，用户体验不佳。列表包含1000+商品，每个商品卡片都是独立组件。",
    
    solution: {
      approach: "使用PureComponent + virtualization + 状态优化的综合方案",
      steps: [
        "将商品卡片组件改为PureComponent",
        "实现虚拟化列表只渲染可见商品",
        "优化商品数据结构，避免深层嵌套",
        "使用useCallback缓存事件处理函数",
        "实现图片懒加载减少网络请求"
      ]
    },
    
    results: {
      performance: "滚动FPS从25提升到60，页面响应时间减少70%",
      userExperience: "用户滚动流畅度显著提升，跳出率降低15%",
      technicalMetrics: "DOM节点数量减少90%，内存使用降低60%"
    },
    
    code: `// 优化前的商品卡片
class ProductCard extends React.Component {
  render() {
    return (
      <div onClick={() => this.props.onCardClick(this.props.product)}>
        <img src={this.props.product.images[0]} />
        <h3>{this.props.product.name}</h3>
        <p>{this.props.product.price}</p>
      </div>
    );
  }
}

// 优化后的商品卡片
const ProductCard = React.memo(({ product, onCardClick }) => {
  return (
    <div onClick={() => onCardClick(product)}>
      <LazyImage src={product.primaryImage} alt={product.name} />
      <h3>{product.name}</h3>
      <p>{product.price}</p>
    </div>
  );
}, (prevProps, nextProps) => (
  prevProps.product.id === nextProps.product.id &&
  prevProps.product.price === nextProps.product.price
));

// 虚拟化列表容器
function ProductList({ products }) {
  const handleCardClick = useCallback((product) => {
    // 处理商品点击
  }, []);
  
  const rowRenderer = useCallback(({ index, key, style }) => (
    <div key={key} style={style}>
      <ProductCard 
        product={products[index]}
        onCardClick={handleCardClick}
      />
    </div>
  ), [products, handleCardClick]);
  
  return (
    <VirtualizedList
      height={600}
      rowCount={products.length}
      rowHeight={200}
      rowRenderer={rowRenderer}
    />
  );
}`
  }
};

// PureComponent性能优化内容已完成
export default performanceOptimization;