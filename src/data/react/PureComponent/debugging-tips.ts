import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  introduction: "PureComponent调试主要涉及浅比较机制的理解和性能问题的排查。掌握正确的调试方法可以快速定位组件重新渲染的原因。",

  troubleshooting: [
    {
      symptom: "PureComponent没有重新渲染，但数据确实变化了",
      possibleCauses: [
        "直接修改了对象或数组，没有创建新引用",
        "深层嵌套对象的变化无法被浅比较检测到",
        "props或state中包含函数，每次都是新的引用"
      ],
      solutions: [
        "使用扩展操作符创建新对象：{...obj, newProp: value}",
        "使用immer库进行immutable更新",
        "检查是否在render中创建了新的对象或函数",
        "使用React DevTools查看props的变化"
      ],
      debugSteps: [
        "在浅比较函数中添加console.log查看比较过程",
        "使用React DevTools Profiler查看组件更新原因",
        "检查父组件是否正确传递了新的引用"
      ]
    },
    {
      symptom: "PureComponent重新渲染过于频繁，性能反而变差",
      possibleCauses: [
        "父组件在render中创建新对象作为props",
        "props包含过多属性，浅比较开销很大",
        "组件的props经常变化，浅比较失去意义"
      ],
      solutions: [
        "使用useCallback和useMemo缓存props",
        "将对象创建移到组件外部或使用静态值",
        "考虑拆分组件，减少props数量",
        "在某些情况下改用普通Component"
      ],
      debugSteps: [
        "使用why-did-you-render库监控重新渲染原因",
        "在shouldComponentUpdate中添加性能计时",
        "使用React DevTools Profiler分析渲染时间"
      ]
    },
    {
      symptom: "浅比较逻辑不符合预期，无法准确判断是否需要更新",
      possibleCauses: [
        "对浅比较的工作原理理解不准确",
        "props中包含复杂的数据结构",
        "期望深比较的行为但使用了浅比较"
      ],
      solutions: [
        "理解浅比较只比较第一层属性",
        "使用React.memo自定义比较函数",
        "重新设计数据结构，避免深层嵌套",
        "考虑使用状态管理库如Redux"
      ],
      debugSteps: [
        "手动实现浅比较函数进行测试",
        "使用Object.is()测试具体值的比较",
        "查看React源码中的浅比较实现"
      ]
    }
  ],

  tools: [
    {
      name: "React DevTools",
      description: "官方浏览器扩展，提供组件树查看、props检查、性能分析等功能",
      usage: "安装浏览器扩展后，在开发者工具中使用React tab",
      features: [
        "查看组件props和state的变化",
        "Profiler分析组件渲染性能",
        "高亮显示重新渲染的组件",
        "查看组件的更新原因"
      ]
    },
    {
      name: "why-did-you-render",
      description: "第三方库，专门用于检测不必要的重新渲染",
      usage: "npm install后在开发环境中配置",
      features: [
        "自动检测PureComponent的重新渲染原因",
        "显示详细的props变化信息",
        "支持自定义检测规则",
        "提供性能优化建议"
      ]
    },
    {
      name: "React DevTools Profiler API",
      description: "React内置的性能监控API",
      usage: "在代码中使用Profiler组件包装需要监控的组件",
      features: [
        "程序化性能监控",
        "获取详细的渲染时间信息",
        "分析组件更新的影响范围",
        "与生产环境监控系统集成"
      ]
    }
  ],

  commonMistakes: [
    {
      mistake: "在render方法中创建新对象",
      description: "每次渲染都创建新的对象、数组或函数作为props",
      wrongExample: `function Parent() {
  return (
    <PureChild 
      config={{ theme: 'dark' }}
      data={[1, 2, 3]}
      onClick={() => console.log('click')}
    />
  );
}`,
      correctExample: `const defaultConfig = { theme: 'dark' };
const defaultData = [1, 2, 3];

function Parent() {
  const handleClick = useCallback(() => {
    console.log('click');
  }, []);
  
  return (
    <PureChild 
      config={defaultConfig}
      data={defaultData}
      onClick={handleClick}
    />
  );
}`,
      howToAvoid: "将静态值移到组件外部，使用useCallback和useMemo缓存动态值"
    },
    {
      mistake: "直接修改props或state",
      description: "修改对象内部属性而不创建新引用",
      wrongExample: `function updateUser(user) {
  user.name = 'New Name'; // 直接修改
  user.profile.email = '<EMAIL>'; // 修改嵌套对象
  setUser(user);
}`,
      correctExample: `function updateUser(user) {
  setUser({
    ...user,
    name: 'New Name',
    profile: {
      ...user.profile,
      email: '<EMAIL>'
    }
  });
}`,
      howToAvoid: "始终使用immutable更新模式，创建新的对象引用"
    },
    {
      mistake: "误解浅比较的工作范围",
      description: "期望PureComponent能检测到深层对象的变化",
      wrongExample: `// 期望这种变化能被检测到
const user = { profile: { name: 'John' } };
user.profile.name = 'Jane'; // 深层变化
<PureComponent user={user} />`,
      correctExample: `// 正确的做法是创建新的对象结构
const user = { 
  ...prevUser,
  profile: { 
    ...prevUser.profile, 
    name: 'Jane' 
  } 
};
<PureComponent user={user} />`,
      howToAvoid: "理解浅比较只检查第一层属性，深层变化需要创建新的引用路径"
    }
  ],

  performanceAnalysis: {
    title: "PureComponent性能分析方法",
    
    steps: [
      {
        step: "1. 使用React DevTools Profiler记录渲染过程",
        description: "开启Profiler，执行导致性能问题的操作，记录渲染数据",
        details: "重点关注渲染时间、渲染次数、组件更新原因"
      },
      {
        step: "2. 分析组件重新渲染的频率和原因",
        description: "查看哪些组件重新渲染最频繁，分析是否必要",
        details: "使用火焰图查看渲染时间分布，识别性能瓶颈"
      },
      {
        step: "3. 检查props变化模式",
        description: "分析props的变化频率和复杂度",
        details: "使用why-did-you-render查看具体的props变化"
      },
      {
        step: "4. 评估浅比较的成本收益",
        description: "计算浅比较的开销与避免重新渲染的收益",
        details: "对于频繁变化的props，浅比较可能不如直接渲染"
      }
    ],
    
    tools: [
      "React DevTools Profiler",
      "why-did-you-render",
      "浏览器Performance API",
      "自定义性能监控代码"
    ],
    
    metrics: [
      "渲染时间 (actualDuration)",
      "基准时间 (baseDuration)", 
      "渲染次数",
      "props比较时间",
      "组件更新频率"
    ]
  },

  advancedTechniques: [
    {
      technique: "自定义浅比较逻辑",
      description: "在特殊情况下，可以重写shouldComponentUpdate实现自定义比较",
      example: `class CustomPureComponent extends React.Component {
  shouldComponentUpdate(nextProps, nextState) {
    // 只比较特定的props
    return (
      this.props.userId !== nextProps.userId ||
      this.props.isActive !== nextProps.isActive
    );
  }
  
  render() {
    return <div>{this.props.userName}</div>;
  }
}`
    },
    {
      technique: "使用Profiler API进行运行时监控",
      description: "在生产环境中监控组件性能",
      example: `function MyApp() {
  const onRenderCallback = useCallback((id, phase, actualDuration) => {
    if (actualDuration > 16) { // 超过一帧的时间
      analytics.track('slow_render', {
        componentId: id,
        duration: actualDuration,
        phase
      });
    }
  }, []);
  
  return (
    <Profiler id="App" onRender={onRenderCallback}>
      <PureComponent />
    </Profiler>
  );
}`
    },
    {
      technique: "条件性PureComponent",
      description: "根据运行时条件决定是否使用PureComponent优化",
      example: `function createOptimizedComponent(shouldOptimize) {
  if (shouldOptimize) {
    return React.memo(MyComponent);
  }
  return MyComponent;
}

// 根据数据复杂度决定是否优化
const OptimizedComponent = createOptimizedComponent(
  data.length > 100
);`
    }
  ]
};

// PureComponent调试技巧内容已完成
export default debuggingTips;