import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  definition: "PureComponent是React中用于性能优化的类组件基类",
  
  introduction: `PureComponent是React 15.3.0引入的类组件基类，主要用于自动性能优化、减少不必要渲染和提升组件渲染效率。它采用浅比较的设计模式，提供了自动shouldComponentUpdate实现。`,

  syntax: `class MyComponent extends PureComponent<Props, State> {
  render() {
    return <div>{this.props.children}</div>;
  }
}`,

  quickExample: `function PureComponentExample() {
  // 使用PureComponent优化类组件性能
  class Counter extends PureComponent<{count: number}> {
    render() {
      console.log('Counter渲染'); // 只在props真正变化时打印
      return <div>计数: {this.props.count}</div>;
    }
  }

  return (
    <div>
      {/* 自动浅比较props，避免不必要的重渲染 */}
      <Counter count={10} />
    </div>
  );
}`,

  scenarioDiagram: `graph TD
    A[PureComponent使用场景] --> B[列表项组件优化]
    A --> C[数据展示组件]
    A --> D[计算密集型组件]

    B --> B1[商品列表项]
    B --> B2[用户评论组件]
    B --> B3[表格单元格]

    C --> C1[统计卡片]
    C --> C2[图表组件]
    C --> C3[信息面板]

    D --> D1[图像处理组件]
    D --> D2[复杂计算组件]
    D --> D3[数据可视化组件]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8`,
  
  parameters: [
    {
      name: "Props",
      type: "object",
      required: false,
      description: "组件的属性对象，PureComponent会对其进行浅比较",
      example: "{ name: 'John', age: 30, data: {...} }"
    },
    {
      name: "State",
      type: "object", 
      required: false,
      description: "组件的状态对象，PureComponent会对其进行浅比较",
      example: "{ count: 0, isLoading: false }"
    }
  ],
  
  returnValue: {
    type: "ComponentClass",
    description: "继承自React.Component的类组件基类，自动实现了shouldComponentUpdate的浅比较逻辑",
    example: "class MyComponent extends PureComponent { ... }"
  },
  
  keyFeatures: [
    {
      title: "自动浅比较",
      description: "自动实现shouldComponentUpdate方法，对props和state进行浅比较",
      benefit: "减少不必要的重新渲染，提升应用性能"
    },
    {
      title: "简化性能优化",
      description: "无需手动编写shouldComponentUpdate逻辑",
      benefit: "降低开发复杂度，减少性能优化代码量"
    },
    {
      title: "类组件专用",
      description: "专为类组件设计的性能优化方案",
      benefit: "提供类组件的最佳性能优化实践"
    }
  ],
  
  limitations: [
    "只进行浅比较，深层对象变化可能无法检测",
    "不适用于频繁变化的深层嵌套数据结构",
    "无法替代手动的深度比较需求"
  ],
  
  bestPractices: [
    "用于数据相对稳定的展示型组件",
    "避免在props中传递复杂的嵌套对象",
    "配合immutable数据结构使用效果更佳",
    "优先考虑函数组件配合React.memo",
    "谨慎在频繁更新的组件中使用"
  ],
  
  warnings: [
    "浅比较可能错过深层对象的变化",
    "过度使用可能导致比较开销超过渲染开销",
    "不建议在新项目中使用，推荐函数组件配合React.memo"
  ]
};

// PureComponent基本信息内容已完成
export default basicInfo;