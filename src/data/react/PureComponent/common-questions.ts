import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: '为什么我的PureComponent没有重新渲染，即使数据确实发生了变化？',
    answer: `这是PureComponent最常见的问题，主要原因是**浅比较无法检测深层对象的变化**。

**常见原因：**
1. **直接修改对象属性**：修改了对象内部属性但没有创建新对象
2. **数组内容变化**：直接push、pop等操作没有创建新数组
3. **嵌套对象更新**：深层对象属性变化但外层对象引用没变

**解决方案：**
1. 使用扩展操作符创建新对象：\`{...oldObj, newProp: newValue}\`
2. 使用数组方法创建新数组：\`[...oldArray, newItem]\`
3. 采用immutable更新模式
4. 考虑使用immutable.js或immer库`,
    code: `// ❌ 错误做法 - 直接修改对象
function updateUser(user) {
  user.profile.name = 'New Name'; // 直接修改
  setUser(user); // PureComponent不会重新渲染
}

// ✅ 正确做法 - 创建新对象
function updateUser(user) {
  setUser({
    ...user,
    profile: {
      ...user.profile,
      name: 'New Name'
    }
  }); // PureComponent会重新渲染
}

// ✅ 数组更新正确做法
function addItem(items, newItem) {
  return [...items, newItem]; // 创建新数组
}`,
    tags: ['浅比较', '对象更新'],
    relatedQuestions: ['如何正确更新嵌套对象？', 'PureComponent和React.memo有什么区别？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '我的PureComponent性能反而更差了，是为什么？',
    answer: `PureComponent在某些情况下确实可能降低性能，主要原因包括：

**性能问题原因：**
1. **浅比较开销**：当props很多时，浅比较的计算成本可能超过重新渲染
2. **频繁变化的props**：如果props经常变化，浅比较就失去了意义
3. **错误的props传递**：在render中创建新对象/函数作为props

**性能优化建议：**
1. 使用React DevTools Profiler分析性能瓶颈
2. 避免在render中创建新对象
3. 使用useCallback和useMemo缓存props
4. 考虑组件拆分，减少不必要的比较`,
    code: `// ❌ 性能问题示例
function Parent() {
  const [count, setCount] = useState(0);
  
  return (
    <PureChild 
      config={{ theme: 'dark' }} // 每次都创建新对象
      onClick={() => console.log('click')} // 每次都创建新函数
      data={heavyData.filter(item => item.active)} // 每次都重新计算
    />
  );
}

// ✅ 性能优化示例
function Parent() {
  const [count, setCount] = useState(0);
  
  // 缓存配置对象
  const config = useMemo(() => ({ theme: 'dark' }), []);
  
  // 缓存回调函数
  const handleClick = useCallback(() => {
    console.log('click');
  }, []);
  
  // 缓存计算结果
  const filteredData = useMemo(() => 
    heavyData.filter(item => item.active), 
    [heavyData]
  );
  
  return (
    <PureChild 
      config={config}
      onClick={handleClick}
      data={filteredData}
    />
  );
}`,
    tags: ['性能优化', '调试'],
    relatedQuestions: ['如何使用React DevTools分析性能？', '什么时候应该避免使用PureComponent？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: '现在应该使用PureComponent还是React.memo？有什么区别？',
    answer: `在现代React开发中，**推荐使用函数组件配合React.memo**，而不是PureComponent。

**React.memo的优势：**
1. **更灵活的比较逻辑**：可以自定义比较函数
2. **函数组件生态**：配合Hooks使用更自然
3. **更细粒度的控制**：可以选择性比较特定props
4. **更好的TypeScript支持**

**迁移建议：**
1. 新项目直接使用函数组件 + React.memo
2. 旧项目可以逐步迁移
3. 复杂逻辑组件优先考虑函数组件重构

**何时仍然使用PureComponent：**
- 现有的类组件代码且运行良好
- 团队对类组件更熟悉的过渡期
- 特定的库或框架要求`,
    code: `// PureComponent方式（传统）
class UserCard extends PureComponent {
  render() {
    const { user, onEdit } = this.props;
    return (
      <div>
        <h3>{user.name}</h3>
        <p>{user.email}</p>
        <button onClick={() => onEdit(user.id)}>
          编辑
        </button>
      </div>
    );
  }
}

// React.memo方式（推荐）
const UserCard = React.memo(({ user, onEdit }) => {
  return (
    <div>
      <h3>{user.name}</h3>
      <p>{user.email}</p>
      <button onClick={() => onEdit(user.id)}>
        编辑
      </button>
    </div>
  );
});

// React.memo + 自定义比较（高级）
const UserCard = React.memo(({ user, onEdit }) => {
  return (
    <div>
      <h3>{user.name}</h3>
      <p>{user.email}</p>
      <button onClick={() => onEdit(user.id)}>
        编辑
      </button>
    </div>
  );
}, (prevProps, nextProps) => {
  // 自定义比较逻辑：只比较user的关键属性
  return (
    prevProps.user.id === nextProps.user.id &&
    prevProps.user.name === nextProps.user.name &&
    prevProps.user.email === nextProps.user.email
  );
});

// 配合Hooks的完整示例
function UserList() {
  const [users, setUsers] = useState([]);
  
  const handleEdit = useCallback((userId) => {
    // 编辑逻辑
  }, []);
  
  return (
    <div>
      {users.map(user => (
        <UserCard 
          key={user.id}
          user={user}
          onEdit={handleEdit}
        />
      ))}
    </div>
  );
}`,
    tags: ['React.memo', '迁移指南'],
    relatedQuestions: ['如何从类组件迁移到函数组件？', 'React.memo的自定义比较函数怎么写？']
  }
];

// PureComponent常见问题内容已完成
export default commonQuestions;