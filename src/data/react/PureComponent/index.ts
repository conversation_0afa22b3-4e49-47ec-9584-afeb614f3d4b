import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ReactPureComponentData: ApiItem = {
  id: 'PureComponent',
  title: 'PureComponent',
  description: 'PureComponent是React中用于性能优化的类组件基类',
  category: 'React Components',
  difficulty: 'medium',
  
  syntax: `class MyComponent extends PureComponent<Props, State> {
  render() {
    return <div>{this.props.children}</div>;
  }
}`,
  example: `function PureComponentExample() {
  // 使用PureComponent优化类组件性能
  class Counter extends PureComponent<{count: number}> {
    render() {
      console.log('Counter渲染'); // 只在props真正变化时打印
      return <div>计数: {this.props.count}</div>;
    }
  }

  return (
    <div>
      {/* 自动浅比较props，避免不必要的重渲染 */}
      <Counter count={10} />
    </div>
  );
}`,
  notes: '只进行浅比较，深层对象变化可能无法检测',
  
  version: 'React 15.3.0+',
  tags: ["React","Component","PureComponent","Performance"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ReactPureComponentData;