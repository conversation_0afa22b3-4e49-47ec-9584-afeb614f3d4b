import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  introduction: `preloadModule的诞生标志着Web模块加载技术从"同步阻塞"向"异步预测"的历史性转变。这不仅仅是一个API的添加，而是整个ES模块生态系统性能优化的革命性演进。通过深入挖掘其历史背景、技术演进和设计理念，我们可以更好地理解现代Web模块系统的精髓和未来发展方向。`,

  background: `preloadModule的出现源于ES模块系统的普及和代码分割技术的广泛应用。在Web早期，所有JavaScript都是同步加载的，后来虽然有了异步加载，但缺乏对ES模块的专门优化。随着单页应用的兴起和代码分割的普及，动态导入成为常见需求，但模块加载延迟成为新的性能瓶颈。`,

  evolution: `preloadModule的演进历程体现了Web平台对ES模块性能优化的不断探索：从早期的script标签预加载，到专门的modulepreload标准，再到React框架层面的智能预加载API，反映了Web技术从通用到专用，从被动到主动的发展轨迹。`,

  timeline: [
    {
      year: '2017',
      event: 'ES模块在浏览器中原生支持',
      description: '主流浏览器开始原生支持ES模块，为模块预加载奠定基础',
      significance: '这是模块预加载技术的基础，没有原生ES模块支持就没有后续的优化空间'
    },
    {
      year: '2019',
      event: 'modulepreload资源提示标准化',
      description: 'W3C标准化了modulepreload资源提示，专门用于ES模块预加载',
      significance: '为preloadModule等高级API提供了底层技术支撑'
    },
    {
      year: '2021',
      event: 'React 18.0引入preloadModule',
      description: 'preloadModule作为ReactDOM的资源预加载API首次出现',
      significance: '标志着框架开始承担ES模块性能优化的责任'
    }
  ],

  keyFigures: [
    {
      name: 'Sebastian Markbåge',
      role: 'React架构师',
      contribution: '设计了React的资源管理系统，包括preloadModule等预加载API',
      significance: '他将浏览器的ES模块能力抽象为开发者友好的React API'
    }
  ],

  concepts: [
    {
      term: 'ES模块预加载(ES Module Preloading)',
      definition: '专门针对ES模块的预加载技术，支持依赖分析和模块缓存优化',
      evolution: '从通用的script预加载发展为专门的ES模块预加载',
      modernRelevance: '现代Web应用性能优化的核心技术，特别是在代码分割场景中'
    }
  ],

  designPhilosophy: `preloadModule的设计哲学体现了现代Web开发对模块化和性能的深度思考：专用化优于通用化、预测优于响应、智能化优于手动化。`,

  impact: `preloadModule对Web开发生态系统产生了重要影响：推动了ES模块预加载标准的发展，提升了框架的模块管理能力，改变了开发者的性能优化思路。`,

  modernRelevance: `在当今的Web开发环境中，preloadModule的重要性日益凸显：代码分割成为标准实践，ES模块系统日趋成熟，用户对应用响应速度的期望不断提高。`
};

export default knowledgeArchaeology;