import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-1',
    question: 'preloadModule和动态import()有什么关系？如何配合使用？',
    answer: `preloadModule和动态import()是完美的搭配，它们共同构成了现代ES模块的高性能加载方案。

**关系说明：**
1. **预加载阶段**：preloadModule提前下载和缓存模块
2. **使用阶段**：动态import()从缓存中立即获取模块
3. **性能优化**：消除动态导入的网络延迟

**工作流程：**
- preloadModule告诉浏览器"这个模块稍后会用到，请提前准备"
- 浏览器在空闲时间下载模块并存储在ES模块缓存中
- 当代码调用import()时，直接从缓存获取，无需等待网络请求

**最佳实践：**
- 在应用启动时或用户操作前调用preloadModule
- 在实际需要时使用import()获取模块
- 两者之间保持合理的时间间隔，让预加载有足够时间完成`,
    code: `// ✅ 正确的配合使用方式
function FeatureManager() {
  useEffect(() => {
    // 应用启动时预加载功能模块
    preloadModule('/modules/advanced-feature.js', {
      crossOrigin: 'anonymous'
    });
  }, []);

  const handleFeatureClick = async () => {
    // 模块已预加载，动态导入立即完成
    const { AdvancedFeature } = await import('/modules/advanced-feature.js');
    return new AdvancedFeature();
  };

  return (
    <button onClick={handleFeatureClick}>
      启动高级功能 (已预加载)
    </button>
  );
}

// ✅ 路由级预加载示例
function RouteManager() {
  const navigate = useNavigate();

  const handleRouteHover = (routePath) => {
    // 鼠标悬停时预加载路由模块
    preloadModule(\`/modules/\${routePath}.js\`);
  };

  const handleRouteClick = async (routePath) => {
    // 模块已预加载，立即导航
    const routeModule = await import(\`/modules/\${routePath}.js\`);
    navigate(routePath);
  };

  return (
    <nav>
      <a
        onMouseEnter={() => handleRouteHover('dashboard')}
        onClick={() => handleRouteClick('dashboard')}
      >
        仪表板
      </a>
    </nav>
  );
}`,
    tags: ['动态导入', '模块缓存'],
    relatedQuestions: ['如何检测模块是否已预加载？', '预加载失败时import()会怎样？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-2',
    question: 'preloadModule会预加载模块的依赖吗？如何处理复杂的依赖关系？',
    answer: `是的，preloadModule会自动分析和预加载模块的所有依赖，这是它相比普通preload的重要优势。

**依赖预加载机制：**
1. **递归分析**：浏览器会解析模块的import语句，识别所有依赖
2. **并行下载**：所有依赖模块会并行下载，提高效率
3. **缓存管理**：整个依赖树都会被存储在ES模块缓存中
4. **去重优化**：相同的依赖只会下载一次

**复杂依赖处理：**
- 支持深层嵌套依赖（A依赖B，B依赖C，C依赖D...）
- 处理循环依赖（A依赖B，B也依赖A）
- 优化共享依赖（多个模块依赖同一个工具库）

**注意事项：**
- 依赖分析是静态的，基于import语句
- 动态import()调用不会被预分析
- 条件导入可能不会被预加载`,
    code: `// 模块依赖示例
// main-feature.js
import { utils } from './utils.js';
import { api } from './api.js';
import { components } from './components.js';

// utils.js
import { lodash } from 'https://cdn.skypack.dev/lodash';

// api.js
import { axios } from 'https://cdn.skypack.dev/axios';

// 预加载主模块时，所有依赖都会被自动预加载
preloadModule('/modules/main-feature.js');
// 这会预加载：
// - /modules/main-feature.js
// - /modules/utils.js
// - /modules/api.js
// - /modules/components.js
// - https://cdn.skypack.dev/lodash
// - https://cdn.skypack.dev/axios

// ✅ 处理复杂依赖的策略
function DependencyManager() {
  useEffect(() => {
    // 预加载核心模块树
    preloadModule('/modules/core.js'); // 包含基础依赖

    // 延迟预加载可选模块树
    setTimeout(() => {
      preloadModule('/modules/optional-features.js');
    }, 1000);

    // 根据条件预加载特定依赖
    if (userHasPermission('advanced')) {
      preloadModule('/modules/advanced.js');
    }
  }, []);
}

// ✅ 监控依赖加载状态
function monitorDependencyLoading() {
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.initiatorType === 'link' && entry.name.includes('.js')) {
        console.log('Dependency loaded:', {
          url: entry.name,
          duration: entry.duration,
          size: entry.transferSize
        });
      }
    }
  });

  observer.observe({ entryTypes: ['resource'] });
}`,
    tags: ['依赖管理', '模块分析'],
    relatedQuestions: ['如何优化大型依赖树的加载？', '循环依赖会影响预加载吗？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-3',
    question: '在构建工具中如何配合preloadModule使用？Webpack和Vite有什么区别？',
    answer: `preloadModule与现代构建工具的集成是实现最佳性能的关键，不同构建工具有不同的优化策略。

**Webpack集成：**
1. **Magic Comments**：使用webpackPreload注释自动生成preloadModule调用
2. **SplitChunksPlugin**：配置代码分割策略，优化模块粒度
3. **PreloadWebpackPlugin**：自动为关键模块添加预加载

**Vite集成：**
1. **原生ES模块**：Vite天然支持ES模块，与preloadModule完美配合
2. **动态导入分析**：开发时实时分析，生产时优化打包
3. **Rollup优化**：利用Rollup的tree-shaking和代码分割

**最佳实践：**
- 在构建时分析模块依赖关系
- 根据路由和功能模块配置预加载策略
- 使用构建工具的分析报告优化模块大小`,
    code: `// Webpack配置示例
// webpack.config.js
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
        }
      }
    }
  }
};

// 使用Magic Comments
const loadFeature = () => {
  // Webpack会自动生成preloadModule调用
  return import(
    /* webpackPreload: true */
    /* webpackChunkName: "feature" */
    './feature-module'
  );
};

// Vite配置示例
// vite.config.js
export default {
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          utils: ['lodash', 'axios']
        }
      }
    }
  }
};

// React组件中的使用
function BuildToolIntegration() {
  useEffect(() => {
    // 预加载构建工具生成的模块
    preloadModule('/assets/feature-chunk.js');
    preloadModule('/assets/vendor-chunk.js');
  }, []);

  const loadDynamicFeature = async () => {
    // 动态导入已预加载的模块
    const feature = await import('./dynamic-feature');
    return feature.default;
  };
}`,
    tags: ['构建工具', 'Webpack', 'Vite'],
    relatedQuestions: ['如何分析模块打包大小？', '代码分割的最佳粒度是什么？']
  }
];

export default commonQuestions;