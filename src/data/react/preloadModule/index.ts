import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const preloadModuleData: ApiItem = {
  id: 'preloadModule',
  title: 'preloadModule',
  description: 'React DOM中用于预加载ES模块的函数，专门用于提前下载JavaScript模块文件，支持模块依赖分析和缓存优化，显著提升动态导入的性能和用户体验。',
  category: 'ReactDOM Resource API',
  difficulty: 'medium',

  syntax: `preloadModule(href: string, options?: PreloadModuleOptions): void`,
  example: `preloadModule('/modules/feature.js', {
  crossOrigin: 'anonymous'
});`,
  notes: '专门用于ES模块预加载，支持模块依赖分析和智能缓存，是Resource Preloading APIs的重要组成部分。',

  version: 'React 18.0+',
  tags: ["ReactDOM", "Resource", "Performance", "Module"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default preloadModuleData;