import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: "preloadModule是React DOM中用于预加载ES模块的函数，专门用于提前下载JavaScript模块文件，确保模块在需要时能够立即可用，显著提升动态导入的性能和用户体验。",

  introduction: `preloadModule是React 18.0+引入的ReactDOM资源预加载API，专门为现代ES模块系统设计的预加载函数。

它遵循现代Web性能优化的最佳实践，在模块预加载和网络效率之间做出了智能平衡，允许开发者提前声明需要的JavaScript模块，让浏览器在最佳时机进行预加载。

主要用于ES模块预加载、动态导入优化和代码分割性能提升。相比传统的script标签预加载，它的创新在于专门针对ES模块的特性进行了优化，支持模块依赖分析和缓存策略。

在React生态中，它是Resource Preloading APIs的重要组成部分，常见于需要优化代码分割性能的大型应用，特别适合需要动态加载功能模块的复杂场景。

核心优势包括减少动态导入延迟、优化模块加载性能、提升代码分割效果，但也需要注意合理控制预加载的模块数量以避免网络资源浪费。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react-dom/index.d.ts:38
 * - 实现文件：packages/react-dom/src/shared/ReactDOMResourceUtils.js:78
 * - 内部类型：packages/react-dom-bindings/src/shared/ReactDOMResourceValidation.js:45
 */

// 基础语法
function preloadModule(href: string, options?: PreloadModuleOptions): void;

// 选项接口
interface PreloadModuleOptions {
  as?: 'script';              // 资源类型，默认为'script'
  crossOrigin?: string;       // CORS设置：'anonymous' | 'use-credentials'
  integrity?: string;         // 子资源完整性校验
  nonce?: string;            // 内容安全策略随机数
}

// 使用示例
preloadModule('/modules/feature.js');

preloadModule('/modules/analytics.js', {
  crossOrigin: 'anonymous',
  integrity: 'sha384-...'
});

/**
 * 参数约束：
 * - href 必须是有效的ES模块URL
 * - options 是可选的配置对象
 * - 同一个href多次调用会被去重
 * - 只支持JavaScript模块预加载
 */`,

  quickExample: `function ModulePreloadExample() {
  useEffect(() => {
    // 预加载功能模块
    preloadModule('/modules/dashboard.js');

    // 预加载带安全校验的第三方模块
    preloadModule('/modules/analytics.js', {
      crossOrigin: 'anonymous',
      integrity: 'sha384-oqVuAfXRKap7fdgcCY5uykM6+R9GqQ8K/uxy9rx7HNQlGYl1kPzQho1wx4JwY8wC'
    });

    // 预加载工具库模块
    preloadModule('/modules/utils.js', {
      crossOrigin: 'anonymous'
    });
  }, []);

  const handleLoadFeature = async () => {
    // 模块已预加载，动态导入会立即完成
    const { FeatureComponent } = await import('/modules/dashboard.js');
    setCurrentFeature(FeatureComponent);
  };

  const handleLoadAnalytics = async () => {
    // 分析模块已预加载，立即可用
    const analytics = await import('/modules/analytics.js');
    analytics.track('user_action', { action: 'feature_loaded' });
  };

  return (
    <div>
      {/* 模块已预加载，点击时立即响应 */}
      <button onClick={handleLoadFeature}>
        加载功能模块 (已预加载)
      </button>
      <button onClick={handleLoadAnalytics}>
        启动分析工具 (已预加载)
      </button>
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "preloadModule在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用这个API来优化ES模块加载性能",
      diagram: `graph LR
      A[preloadModule核心场景] --> B[代码分割优化]
      A --> C[动态功能加载]
      A --> D[第三方模块预加载]

      B --> B1["📦 路由模块<br/>页面级代码分割预加载"]
      B --> B2["🧩 组件模块<br/>大型组件动态加载"]

      C --> C1["⚡ 功能模块<br/>按需功能预加载"]
      C --> C2["🔧 工具模块<br/>工具库动态导入"]

      D --> D1["📊 分析工具<br/>第三方SDK预加载"]
      D --> D2["🎨 UI库<br/>外部组件库预加载"]

      style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
      style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
      style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "技术实现架构",
      description: "preloadModule的技术实现架构，展示其与浏览器模块系统和React资源管理的集成关系",
      diagram: `graph TB
      A[preloadModule技术架构] --> B[浏览器模块系统]
      A --> C[React资源管理]
      A --> D[网络优化层]

      B --> B1["🌐 ES Module Loader<br/>原生模块加载器"]
      B --> B2["📋 Module Cache<br/>浏览器模块缓存"]

      C --> C1["🎯 DOM管理<br/>动态link标签插入"]
      C --> C2["🔄 SSR支持<br/>服务端渲染兼容"]

      D --> D1["⚡ 并行加载<br/>多模块并发预加载"]
      D --> D2["📈 缓存策略<br/>智能缓存管理"]

      style A fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
      style B fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
      style C fill:#fce4ec,stroke:#c2185b,stroke-width:2px
      style D fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px`
    },
    {
      title: "生态系统集成",
      description: "preloadModule在React模块管理生态系统中的位置和与其他APIs的协作关系",
      diagram: `graph TD
      A[React Module APIs生态] --> B[预加载APIs]
      A --> C[构建工具链]
      A --> D[性能监控]

      B --> B1["preload<br/>通用资源预加载"]
      B --> B2["preloadModule<br/>ES模块预加载"]
      B --> B3["preinit<br/>资源预初始化"]
      B --> B4["preinitModule<br/>模块预初始化"]

      C --> C1["Webpack<br/>模块打包集成"]
      C --> C2["Vite<br/>ES模块构建工具"]

      D --> D1["Performance API<br/>模块加载监控"]
      D --> D2["Resource Timing<br/>网络性能分析"]

      B2 -.-> B1
      B2 -.-> B4
      B2 -.-> C1
      B2 -.-> D1

      style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
      style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
      style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style B2 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
    }
  ],
  
  parameters: [
    {
      name: "href",
      type: "string",
      required: true,
      description: "要预加载的ES模块的URL路径。必须是有效的JavaScript模块路径，支持相对路径和绝对路径。",
      example: `preloadModule('/modules/feature.js');
preloadModule('https://cdn.example.com/module.js');`
    },
    {
      name: "options",
      type: "PreloadModuleOptions",
      required: false,
      description: "可选的配置对象，用于指定CORS设置、完整性校验、安全策略等选项。",
      example: `preloadModule('/modules/analytics.js', {
  crossOrigin: 'anonymous',
  integrity: 'sha384-...',
  nonce: 'random-nonce-value'
});`
    }
  ],

  returnValue: {
    type: "void",
    description: "preloadModule不返回任何值，它是一个纯粹的副作用函数，用于向浏览器发出ES模块预加载的指令。",
    example: `// preloadModule没有返回值
preloadModule('/modules/feature.js', {
  crossOrigin: 'anonymous'
});
// 函数执行后，浏览器开始预加载指定模块`
  },

  keyFeatures: [
    {
      title: "ES模块专用预加载",
      description: "专门针对ES模块设计的预加载功能，支持模块依赖分析和缓存优化",
      benefit: "显著减少动态导入延迟，提升代码分割的性能效果"
    },
    {
      title: "智能缓存管理",
      description: "与浏览器的ES模块缓存系统深度集成，确保预加载的模块能够被动态导入复用",
      benefit: "避免重复下载，优化网络资源使用效率"
    },
    {
      title: "安全性保障",
      description: "支持子资源完整性校验(SRI)和内容安全策略(CSP)，确保模块加载的安全性",
      benefit: "在性能优化的同时，保障应用的安全性要求"
    },
    {
      title: "CORS兼容性",
      description: "提供完整的跨域资源共享配置选项，支持不同的CORS策略",
      benefit: "能够安全地预加载来自不同域的第三方模块和CDN内容"
    },
    {
      title: "React集成优化",
      description: "与React的资源管理系统深度集成，自动处理重复调用和资源去重",
      benefit: "避免重复预加载同一模块，优化网络资源使用效率"
    },
    {
      title: "构建工具友好",
      description: "与Webpack、Vite等现代构建工具完美集成，支持模块分析和优化",
      benefit: "在构建时就能确定最佳的预加载策略，提升开发和部署效率"
    }
  ],

  limitations: [
    "只能在React 18.0+版本中使用，在旧版本中不可用",
    "只支持JavaScript模块预加载，不支持CSS、图片等其他资源类型",
    "在服务端渲染环境中，preloadModule调用会被忽略，只在客户端生效",
    "过度使用可能导致不必要的网络请求和带宽浪费，需要合理控制预加载的模块数量",
    "预加载的模块必须是有效的ES模块，不支持CommonJS或其他模块格式"
  ],

  bestPractices: [
    "在应用启动时或路由变化时预加载即将需要的模块，为用户操作做好准备",
    "为第三方模块设置适当的integrity校验，确保模块的完整性和安全性",
    "使用crossOrigin设置来正确处理跨域模块的预加载",
    "结合代码分割策略，只预加载高概率使用的模块，避免浪费网络带宽",
    "在路由级别实现智能预加载，根据用户导航模式预测性加载目标页面模块",
    "配合构建工具的模块分析功能，实现最佳的预加载策略",
    "在移动设备上谨慎使用，考虑网络条件和数据流量的限制",
    "建立模块预加载的监控机制，跟踪预加载效果和资源使用情况"
  ],

  warnings: [
    "不要预加载所有可能的模块，这会导致不必要的网络开销和资源竞争",
    "确保预加载的模块路径正确，错误的路径会导致404错误但不会抛出异常",
    "在使用第三方CDN模块时，务必设置正确的crossOrigin和integrity参数",
    "注意模块的依赖关系，预加载主模块时其依赖模块也会被自动加载",
    "在严格的CSP环境中，确保nonce参数与页面的CSP策略匹配"
  ]
};

export default basicInfo;