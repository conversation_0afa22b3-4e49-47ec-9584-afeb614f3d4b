import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 1,
    question: '什么是preloadModule？它与普通的preload有什么区别？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'preloadModule是React DOM中专门用于预加载ES模块的函数，相比普通preload，它专门针对JavaScript模块进行了优化，支持依赖分析和模块缓存。',
      detailed: `preloadModule是React 18.0+引入的ReactDOM资源预加载API，专门为ES模块系统设计。

**主要区别：**
1. **专用性**：preloadModule专门用于JavaScript模块，preload支持各种资源类型
2. **依赖分析**：preloadModule会自动分析和预加载模块依赖，preload只加载指定资源
3. **缓存机制**：preloadModule利用ES模块缓存，preload使用HTTP缓存
4. **集成度**：preloadModule与动态import()深度集成，preload更通用

**使用场景：**
- 代码分割场景的模块预加载
- 路由级组件的提前加载
- 功能模块的按需预加载
- 第三方库的性能优化

**核心优势：**
- 减少动态导入延迟
- 自动处理模块依赖关系
- 与构建工具完美集成
- 提升代码分割效果`,
      code: `// preloadModule vs preload 对比
function ComparisonExample() {
  useEffect(() => {
    // ✅ preloadModule：专门用于ES模块
    preloadModule('/modules/feature.js', {
      crossOrigin: 'anonymous'
    });

    // ✅ preload：通用资源预加载
    preload('/images/hero.jpg', {
      as: 'image'
    });

    // ❌ 错误：preload不能很好地处理模块依赖
    preload('/modules/feature.js', {
      as: 'script' // 不会预加载依赖
    });
  }, []);

  const loadFeature = async () => {
    // preloadModule预加载的模块立即可用
    const { Feature } = await import('/modules/feature.js');
    return Feature;
  };
}`
    },
    tags: ['基础概念', 'API对比']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 2,
    question: 'preloadModule如何处理模块依赖？请详细说明其依赖分析机制。',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: 'preloadModule通过浏览器的ES模块解析器自动分析模块的import语句，递归预加载所有静态依赖，并将整个依赖树存储在ES模块缓存中。',
      detailed: `preloadModule的依赖分析是其核心特性，基于浏览器的ES模块系统实现。

**依赖分析流程：**
1. **静态分析**：解析模块的import语句，识别直接依赖
2. **递归遍历**：对每个依赖模块重复分析过程
3. **并行下载**：所有依赖模块并行下载，提高效率
4. **缓存存储**：整个依赖树存储在ES模块缓存中

**处理的依赖类型：**
- 静态import语句（import { x } from './module'）
- 默认导入（import Module from './module'）
- 命名空间导入（import * as Module from './module'）
- 相对路径和绝对路径依赖

**不处理的情况：**
- 动态import()调用（运行时决定）
- 条件导入（if语句中的import）
- 字符串拼接的模块路径

**优化机制：**
- 去重：相同依赖只下载一次
- 缓存复用：已缓存的模块直接使用
- 错误处理：单个依赖失败不影响其他模块`,
      code: `// 依赖分析示例
// main.js
import { utils } from './utils.js';
import { api } from './api.js';
import Component from './component.js';

// utils.js
import { lodash } from 'https://cdn.skypack.dev/lodash';
import { format } from './formatter.js';

// api.js
import { axios } from 'https://cdn.skypack.dev/axios';

// 预加载主模块时的依赖分析
preloadModule('/modules/main.js');

// 浏览器会自动分析并预加载：
// 1. /modules/main.js (主模块)
// 2. /modules/utils.js (直接依赖)
// 3. /modules/api.js (直接依赖)
// 4. /modules/component.js (直接依赖)
// 5. https://cdn.skypack.dev/lodash (间接依赖)
// 6. /modules/formatter.js (间接依赖)
// 7. https://cdn.skypack.dev/axios (间接依赖)

// 监控依赖加载
function monitorDependencies() {
  const loadedModules = new Set();

  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.initiatorType === 'link' && entry.name.endsWith('.js')) {
        loadedModules.add(entry.name);
        console.log('Module loaded:', entry.name);
      }
    }
  });

  observer.observe({ entryTypes: ['resource'] });

  return () => {
    console.log('Total modules loaded:', loadedModules.size);
    console.log('Modules:', Array.from(loadedModules));
  };
}`
    },
    tags: ['依赖分析', '实现原理']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 3,
    question: '在大型应用中如何设计preloadModule的预加载策略？请提供完整的架构方案。',
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    answer: {
      brief: '大型应用的preloadModule策略需要考虑路由分析、用户行为预测、网络条件适配、模块优先级管理等多个维度，建立智能化的模块预加载管理系统。',
      detailed: `大型应用的preloadModule策略设计是一个复杂的系统工程，需要综合考虑多个因素。

**策略设计原则：**
1. **路由驱动**：基于路由结构预测模块需求
2. **用户行为分析**：根据用户操作模式优化预加载
3. **网络条件适配**：根据网络状况调整策略
4. **模块优先级管理**：建立清晰的模块重要性层次

**核心组件设计：**
1. **路由分析器**：分析应用路由结构和模块依赖
2. **行为预测器**：基于用户行为预测模块需求
3. **网络监控器**：实时监控网络条件
4. **预加载调度器**：智能调度模块预加载时机

**实施策略：**
1. 应用启动时预加载核心模块
2. 路由变化时预加载目标模块
3. 用户交互时预测性预加载
4. 空闲时间预加载低优先级模块

**性能监控：**
- 预加载命中率统计
- 模块加载时间分析
- 网络资源使用监控
- 用户体验指标跟踪`,
      code: `// 大型应用preloadModule架构方案
class ModulePreloadManager {
  constructor() {
    this.routeModuleMap = new Map();
    this.userBehaviorAnalyzer = new UserBehaviorAnalyzer();
    this.networkMonitor = new NetworkMonitor();
    this.preloadScheduler = new PreloadScheduler();
    this.performanceTracker = new PerformanceTracker();
  }

  // 初始化预加载策略
  async initialize() {
    // 分析路由结构
    await this.analyzeRouteStructure();

    // 启动用户行为分析
    this.userBehaviorAnalyzer.start();

    // 监控网络条件
    this.networkMonitor.start();

    // 预加载核心模块
    this.preloadCoreModules();
  }

  // 分析路由结构
  async analyzeRouteStructure() {
    const routes = await this.getApplicationRoutes();

    routes.forEach(route => {
      this.routeModuleMap.set(route.path, {
        component: route.component,
        dependencies: this.analyzeDependencies(route.component),
        priority: this.calculateRoutePriority(route),
        frequency: this.getRouteFrequency(route.path)
      });
    });
  }

  // 预加载核心模块
  preloadCoreModules() {
    const coreModules = [
      '/modules/layout.js',
      '/modules/navigation.js',
      '/modules/common-components.js'
    ];

    coreModules.forEach(module => {
      preloadModule(module, {
        crossOrigin: 'anonymous'
      });
    });
  }

  // 路由预加载策略
  handleRouteChange(currentRoute, targetRoute) {
    const targetModuleInfo = this.routeModuleMap.get(targetRoute);

    if (targetModuleInfo) {
      // 高优先级立即预加载
      if (targetModuleInfo.priority === 'high') {
        this.preloadRouteModules(targetRoute);
      } else {
        // 低优先级延迟预加载
        this.preloadScheduler.schedule(() => {
          this.preloadRouteModules(targetRoute);
        }, 100);
      }
    }
  }

  // 预加载路由模块
  preloadRouteModules(routePath) {
    const moduleInfo = this.routeModuleMap.get(routePath);

    if (moduleInfo && this.networkMonitor.isGoodCondition()) {
      // 预加载主组件
      preloadModule(moduleInfo.component, {
        crossOrigin: 'anonymous'
      });

      // 预加载依赖模块
      moduleInfo.dependencies.forEach(dep => {
        preloadModule(dep, {
          crossOrigin: 'anonymous'
        });
      });

      // 记录预加载操作
      this.performanceTracker.recordPreload(routePath);
    }
  }

  // 智能预测预加载
  predictivePreload() {
    const predictions = this.userBehaviorAnalyzer.getPredictions();

    predictions.forEach(prediction => {
      if (prediction.probability > 0.7) {
        this.preloadScheduler.schedule(() => {
          this.preloadRouteModules(prediction.route);
        }, prediction.timing);
      }
    });
  }
}

// 用户行为分析器
class UserBehaviorAnalyzer {
  constructor() {
    this.actionHistory = [];
    this.patterns = new Map();
  }

  start() {
    // 监听用户操作
    document.addEventListener('click', this.recordAction.bind(this));
    document.addEventListener('mouseover', this.recordHover.bind(this));

    // 定期分析模式
    setInterval(() => {
      this.analyzePatterns();
    }, 30000);
  }

  recordAction(event) {
    this.actionHistory.push({
      type: 'click',
      target: event.target,
      timestamp: Date.now(),
      route: window.location.pathname
    });
  }

  getPredictions() {
    // 基于历史模式预测用户下一步操作
    return this.patterns.get(window.location.pathname) || [];
  }
}

// 网络监控器
class NetworkMonitor {
  constructor() {
    this.networkInfo = null;
  }

  start() {
    if ('connection' in navigator) {
      this.networkInfo = navigator.connection;
      this.networkInfo.addEventListener('change', this.handleNetworkChange.bind(this));
    }
  }

  isGoodCondition() {
    if (!this.networkInfo) return true;

    return this.networkInfo.effectiveType === '4g' &&
           this.networkInfo.downlink > 1.5 &&
           !this.networkInfo.saveData;
  }
}

// 使用示例
function AppWithPreloadStrategy() {
  const [preloadManager] = useState(() => new ModulePreloadManager());

  useEffect(() => {
    preloadManager.initialize();
  }, []);

  return <div>应用内容</div>;
}`
    },
    tags: ['架构设计', '策略优化']
  }
];

export default interviewQuestions;