import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'preloadModule虽然API简单，但在实际使用中开发者经常遇到一些典型问题。本节提供完整的问题诊断和解决方案，帮助快速定位和修复preloadModule相关的技术问题。',
        sections: [
          {
            title: '模块预加载无效果问题',
            description: '最常见的问题是preloadModule看起来没有任何效果，动态导入时间没有改善。这通常涉及调用时机、模块路径、浏览器支持等多个方面',
            items: [
              {
                title: '调用时机过晚',
                description: 'preloadModule在模块即将使用时才调用，没有足够的时间进行预加载',
                solution: '1. 在应用启动时预加载核心模块；2. 在路由变化时预加载目标页面模块；3. 根据用户行为预测性加载',
                prevention: '建立预加载策略，在合适的时机提前调用preloadModule',
                code: `// ❌ 错误：调用时机过晚
const handleClick = async () => {
  preloadModule('/modules/feature.js'); // 太晚了
  const module = await import('/modules/feature.js');
};

// ✅ 正确：提前预加载
function App() {
  useEffect(() => {
    // 应用启动时预加载
    preloadModule('/modules/feature.js', {
      crossOrigin: 'anonymous'
    });
  }, []);

  const handleClick = async () => {
    // 模块已预加载，立即可用
    const module = await import('/modules/feature.js');
  };
}`
              },
              {
                title: '模块路径不匹配',
                description: 'preloadModule的路径与实际import()的路径不一致，导致缓存未命中',
                solution: '1. 确保路径完全一致；2. 注意相对路径和绝对路径的区别；3. 检查构建工具的路径处理',
                prevention: '使用常量或配置文件统一管理模块路径',
                code: `// ❌ 错误：路径不匹配
preloadModule('/modules/feature.js');
const module = await import('./modules/feature.js'); // 路径不同

// ✅ 正确：路径一致
const MODULE_PATH = '/modules/feature.js';
preloadModule(MODULE_PATH);
const module = await import(MODULE_PATH);

// ✅ 使用配置管理
const MODULES = {
  feature: '/modules/feature.js',
  utils: '/modules/utils.js'
};

preloadModule(MODULES.feature);
const module = await import(MODULES.feature);`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '有效的调试工具可以帮助开发者监控preloadModule的效果，诊断性能问题，优化预加载策略。掌握这些工具是高效使用preloadModule的关键。',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '现代浏览器提供了强大的网络和性能分析工具，可以直观地监控模块预加载的效果',
            items: [
              {
                title: '网络面板监控',
                description: '使用浏览器开发者工具的网络面板监控modulepreload请求的状态和时机',
                solution: '1. 打开开发者工具网络面板；2. 筛选显示"Other"类型请求；3. 查看modulepreload请求的时机和状态；4. 分析预加载效果',
                prevention: '定期检查网络面板，确保预加载按预期工作',
                code: `// 监控模块预加载状态
function monitorModulePreload() {
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.initiatorType === 'link' &&
          entry.name.endsWith('.js')) {
        console.log('Module preload:', {
          url: entry.name,
          startTime: entry.startTime,
          duration: entry.duration,
          transferSize: entry.transferSize
        });
      }
    }
  });

  observer.observe({ entryTypes: ['resource'] });
}

// 检查模块是否已预加载
function isModulePreloaded(href) {
  const links = document.querySelectorAll('link[rel="modulepreload"]');
  return Array.from(links).some(link => link.href === href);
}`
              },
              {
                title: 'Performance API分析',
                description: '使用Performance API精确测量模块加载性能，对比预加载前后的效果',
                solution: '1. 使用performance.mark标记关键时间点；2. 使用performance.measure计算时间差；3. 分析Module Timing数据',
                prevention: '建立性能监控机制，持续跟踪预加载效果',
                code: `// 模块加载性能测量
class ModulePerformanceMonitor {
  constructor() {
    this.measurements = new Map();
  }

  startMeasure(moduleName) {
    performance.mark(\`\${moduleName}-start\`);
  }

  endMeasure(moduleName) {
    performance.mark(\`\${moduleName}-end\`);
    performance.measure(
      \`\${moduleName}-load-time\`,
      \`\${moduleName}-start\`,
      \`\${moduleName}-end\`
    );

    const measure = performance.getEntriesByName(\`\${moduleName}-load-time\`)[0];
    this.measurements.set(moduleName, measure.duration);
  }

  compareWithBaseline(moduleName, baselineTime) {
    const currentTime = this.measurements.get(moduleName);
    if (currentTime) {
      const improvement = ((baselineTime - currentTime) / baselineTime * 100).toFixed(2);
      console.log(\`Module \${moduleName} improvement: \${improvement}%\`);
    }
  }
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;