import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  introduction: `preconnect的诞生标志着Web网络优化技术从"被动响应"向"主动预测"的历史性转变。这不仅仅是一个API的添加，而是整个Web性能优化理念的革命性演进。通过深入挖掘其历史背景、技术演进和设计理念，我们可以更好地理解现代Web网络优化的精髓和未来发展方向。`,

  background: `preconnect的出现源于Web应用对第三方服务依赖的日益增长和网络延迟对用户体验的显著影响。在Web早期，大部分资源都来自同一域名，网络连接相对简单。随着Web服务的复杂化，应用开始大量依赖第三方API、CDN、分析服务等，网络连接建立的延迟成为新的性能瓶颈。`,

  evolution: `preconnect的演进历程体现了Web平台对网络性能优化的不断探索：从早期的手动连接管理，到Resource Hints标准的制定，再到React框架层面的智能预连接API，反映了Web技术从手动到自动，从通用到专用的发展轨迹。`,

  timeline: [
    {
      year: '2014',
      event: 'Resource Hints规范提出',
      description: 'W3C开始制定Resource Hints规范，包括dns-prefetch、preconnect等',
      significance: '为现代Web性能优化奠定了标准基础'
    },
    {
      year: '2016',
      event: 'preconnect标准化',
      description: 'preconnect作为Resource Hints的一部分被正式标准化',
      significance: '确立了域预连接的技术标准和实现规范'
    },
    {
      year: '2022',
      event: 'React 18.0引入preconnect',
      description: 'preconnect作为ReactDOM的网络优化API首次出现',
      significance: '标志着框架开始承担网络连接优化的责任'
    }
  ],

  keyFigures: [
    {
      name: 'Ilya Grigorik',
      role: 'Google性能工程师',
      contribution: '主导了Resource Hints规范的制定，包括preconnect的设计',
      significance: '他将网络优化理论转化为Web标准，推动了现代性能优化技术的发展'
    }
  ],

  concepts: [
    {
      term: '域预连接(Domain Preconnection)',
      definition: '提前建立到外部域的完整网络连接，包括DNS解析、TCP握手和TLS协商',
      evolution: '从手动连接管理发展为声明式的预连接优化',
      modernRelevance: '现代Web应用性能优化的核心技术，特别是在第三方服务集成场景中'
    }
  ],

  designPhilosophy: `preconnect的设计哲学体现了现代Web开发对网络性能的深度思考：预测优于响应、主动优于被动、智能优于盲目。`,

  impact: `preconnect对Web开发生态系统产生了重要影响：推动了Resource Hints标准的普及，提升了框架的网络管理能力，改变了开发者的性能优化思路。`,

  modernRelevance: `在当今的Web开发环境中，preconnect的重要性日益凸显：第三方服务集成成为标准实践，网络延迟对用户体验的影响越来越大，用户对应用响应速度的期望不断提高。`
};

export default knowledgeArchaeology;