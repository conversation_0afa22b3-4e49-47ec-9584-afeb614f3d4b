import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-1',
    question: 'preconnect什么时候调用最合适？如何确定预连接的时机？',
    answer: `preconnect的调用时机是影响其效果的关键因素，需要在"足够早"和"不浪费资源"之间找到平衡。

**最佳调用时机：**
1. **应用启动时**：预连接到核心的第三方服务
2. **路由变化时**：预连接到目标页面可能需要的服务
3. **用户交互前**：根据用户行为预测需要的服务
4. **空闲时间**：利用浏览器空闲时间预连接备用服务

**时机选择原则：**
- 确定性高的服务：尽早预连接
- 可能性中等的服务：用户交互时预连接
- 备用服务：空闲时间预连接
- 避免过早预连接导致连接超时

**监控和优化：**
- 跟踪预连接的命中率
- 分析用户行为模式
- 根据数据调整预连接策略`,
    code: `// 智能预连接时机管理
function SmartPreconnectTiming() {
  const [userBehavior, setUserBehavior] = useState({});
  const [preconnectHistory, setPreconnectHistory] = useState([]);

  useEffect(() => {
    // 应用启动时：预连接核心服务
    const coreServices = [
      'https://api.stripe.com',
      'https://fonts.googleapis.com'
    ];

    coreServices.forEach(service => {
      preconnect(service, { crossOrigin: 'anonymous' });
      recordPreconnect(service, 'app-startup');
    });
  }, []);

  useEffect(() => {
    // 用户行为触发预连接
    if (userBehavior.viewingProduct) {
      preconnect('https://reviews-api.example.com');
      recordPreconnect('https://reviews-api.example.com', 'product-view');
    }

    if (userBehavior.addedToCart) {
      preconnect('https://payment-api.example.com');
      recordPreconnect('https://payment-api.example.com', 'cart-action');
    }
  }, [userBehavior]);

  const recordPreconnect = (url, trigger) => {
    setPreconnectHistory(prev => [...prev, {
      url,
      trigger,
      timestamp: Date.now()
    }]);
  };

  // 空闲时间预连接
  useEffect(() => {
    const idleCallback = requestIdleCallback(() => {
      const backupServices = [
        'https://backup-api.example.com',
        'https://analytics.example.com'
      ];

      backupServices.forEach(service => {
        preconnect(service);
        recordPreconnect(service, 'idle-time');
      });
    });

    return () => cancelIdleCallback(idleCallback);
  }, []);
}`,
    tags: ['调用时机', '性能优化'],
    relatedQuestions: ['如何监控预连接效果？', '预连接失败时如何处理？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-2',
    question: '预连接太多域名会有什么问题？如何控制预连接的数量？',
    answer: `过度预连接会导致多种问题，需要建立合理的控制机制。

**过度预连接的问题：**
1. **资源竞争**：过多连接占用浏览器连接池
2. **网络拥塞**：同时建立多个连接可能导致网络拥塞
3. **电池消耗**：移动设备上增加电池消耗
4. **连接超时**：部分连接可能在使用前就超时

**控制策略：**
1. **优先级分级**：核心服务优先，备用服务延后
2. **数量限制**：通常建议不超过6个并发预连接
3. **条件预连接**：根据网络条件和设备类型调整
4. **智能调度**：错开预连接时机，避免同时建立

**最佳实践：**
- 移动设备：限制在3-4个预连接
- 桌面设备：可以适当增加到6-8个
- 慢网络：减少预连接数量
- 快网络：可以适当增加`,
    code: `// 预连接数量控制策略
class PreconnectManager {
  constructor() {
    this.activeConnections = new Set();
    this.pendingConnections = [];
    this.maxConcurrent = this.getMaxConcurrent();
  }

  getMaxConcurrent() {
    // 根据设备和网络条件确定最大并发数
    const isMobile = /Mobile|Android|iPhone/.test(navigator.userAgent);
    const connection = navigator.connection;

    if (isMobile) {
      return connection?.effectiveType === '4g' ? 4 : 2;
    }

    return connection?.effectiveType === '4g' ? 6 : 4;
  }

  preconnect(url, options = {}) {
    const priority = options.priority || 'normal';

    if (this.activeConnections.size < this.maxConcurrent) {
      this.executePreconnect(url, options);
    } else {
      // 加入队列，按优先级排序
      this.pendingConnections.push({ url, options, priority });
      this.pendingConnections.sort((a, b) =>
        this.getPriorityValue(b.priority) - this.getPriorityValue(a.priority)
      );
    }
  }

  executePreconnect(url, options) {
    preconnect(url, options);
    this.activeConnections.add(url);

    // 模拟连接完成后释放槽位
    setTimeout(() => {
      this.activeConnections.delete(url);
      this.processQueue();
    }, 2000);
  }

  processQueue() {
    if (this.pendingConnections.length > 0 &&
        this.activeConnections.size < this.maxConcurrent) {
      const next = this.pendingConnections.shift();
      this.executePreconnect(next.url, next.options);
    }
  }

  getPriorityValue(priority) {
    const values = { high: 3, normal: 2, low: 1 };
    return values[priority] || 2;
  }
}

// 使用示例
function ControlledPreconnectExample() {
  const [manager] = useState(() => new PreconnectManager());

  useEffect(() => {
    // 高优先级：核心服务
    manager.preconnect('https://api.stripe.com', {
      priority: 'high',
      crossOrigin: 'anonymous'
    });

    // 普通优先级：常用服务
    manager.preconnect('https://fonts.googleapis.com', {
      priority: 'normal'
    });

    // 低优先级：备用服务
    manager.preconnect('https://backup-api.example.com', {
      priority: 'low'
    });
  }, []);
}`,
    tags: ['数量控制', '资源管理'],
    relatedQuestions: ['如何检测网络条件？', '移动设备上的优化策略？']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'question-3',
    question: 'preconnect在不同浏览器中的兼容性如何？如何处理不支持的情况？',
    answer: `preconnect的浏览器兼容性总体良好，但需要考虑降级策略。

**浏览器支持情况：**
1. **现代浏览器**：Chrome 46+、Firefox 39+、Safari 11.1+完全支持
2. **移动浏览器**：iOS Safari 11.1+、Chrome Mobile 46+支持
3. **不支持的浏览器**：IE全系列、旧版Safari不支持
4. **部分支持**：某些浏览器可能忽略crossOrigin等选项

**降级策略：**
1. **特性检测**：检测浏览器是否支持preconnect
2. **优雅降级**：不支持时使用prefetchDNS或无操作
3. **Polyfill方案**：使用JavaScript模拟预连接效果
4. **条件加载**：根据浏览器能力调整策略

**最佳实践：**
- 始终进行特性检测
- 提供降级方案
- 监控不同浏览器的效果
- 考虑用户群体的浏览器分布`,
    code: `// 浏览器兼容性处理
class CompatiblePreconnect {
  constructor() {
    this.isSupported = this.checkSupport();
    this.fallbackStrategy = this.determineFallback();
  }

  checkSupport() {
    // 检测preconnect支持
    if (typeof document === 'undefined') return false;

    const link = document.createElement('link');
    return link.relList && link.relList.supports &&
           link.relList.supports('preconnect');
  }

  determineFallback() {
    // 确定降级策略
    if (this.checkDNSPrefetchSupport()) {
      return 'dns-prefetch';
    }
    return 'none';
  }

  checkDNSPrefetchSupport() {
    if (typeof document === 'undefined') return false;

    const link = document.createElement('link');
    return link.relList && link.relList.supports &&
           link.relList.supports('dns-prefetch');
  }

  preconnect(url, options = {}) {
    if (this.isSupported) {
      // 原生preconnect
      preconnect(url, options);
    } else {
      // 降级处理
      this.handleFallback(url, options);
    }
  }

  handleFallback(url, options) {
    switch (this.fallbackStrategy) {
      case 'dns-prefetch':
        this.dnsPrefetchFallback(url);
        break;
      case 'none':
        // 记录不支持的情况，但不报错
        console.info('preconnect not supported, skipping:', url);
        break;
    }
  }

  dnsPrefetchFallback(url) {
    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = url;
    document.head.appendChild(link);
  }

  // 手动预连接（实验性）
  manualPreconnect(url) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const timeout = setTimeout(() => {
        reject(new Error('Preconnect timeout'));
      }, 5000);

      img.onload = img.onerror = () => {
        clearTimeout(timeout);
        resolve();
      };

      // 触发连接建立
      img.src = url + '/favicon.ico?' + Date.now();
    });
  }
}

// 使用示例
function BrowserCompatibleExample() {
  const [preconnectManager] = useState(() => new CompatiblePreconnect());

  useEffect(() => {
    // 兼容性预连接
    preconnectManager.preconnect('https://api.example.com', {
      crossOrigin: 'anonymous'
    });

    // 检查支持情况
    if (!preconnectManager.isSupported) {
      console.warn('preconnect not supported, using fallback strategy');
    }
  }, []);
}`,
    tags: ['浏览器兼容性', '降级策略'],
    relatedQuestions: ['如何检测浏览器特性？', 'Polyfill的实现原理？']
  }
];

export default commonQuestions;