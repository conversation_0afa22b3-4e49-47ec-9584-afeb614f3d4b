import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-1',
    title: '电商平台第三方服务集成优化',
    description: '在电商平台中，需要集成多个第三方服务，如支付网关、物流API、用户评价系统等。使用preconnect可以提前建立到这些服务的网络连接，确保用户在购买流程中获得流畅的体验。',
    businessValue: '提升购买转化率，减少支付和物流查询的延迟，增强用户购物体验，特别适合需要快速响应的电商交易场景。',
    scenario: '用户在电商网站浏览商品时，可能会查看物流信息、进行支付操作、查看用户评价等。传统方式会在用户点击相关功能时才建立网络连接，导致明显的等待时间，影响购买决策。',
    code: `function EcommerceServicePreconnect() {
  const [userBehavior, setUserBehavior] = useState({});
  const [connectionStatus, setConnectionStatus] = useState(new Set());

  // 第三方服务域名配置
  const thirdPartyServices = {
    payment: 'https://api.stripe.com',
    logistics: 'https://api.sf-express.com',
    reviews: 'https://api.trustpilot.com',
    analytics: 'https://www.google-analytics.com',
    cdn: 'https://cdn.shopify.com'
  };

  useEffect(() => {
    // 应用启动时预连接核心服务
    const coreServices = ['payment', 'logistics', 'cdn'];

    coreServices.forEach(service => {
      const domain = thirdPartyServices[service];
      preconnect(domain, {
        crossOrigin: 'anonymous'
      });
      setConnectionStatus(prev => new Set([...prev, service]));
    });
  }, []);

  useEffect(() => {
    // 根据用户行为预连接相关服务
    if (userBehavior.viewingProduct) {
      // 用户查看商品时预连接评价服务
      preconnect(thirdPartyServices.reviews, {
        crossOrigin: 'anonymous'
      });
    }

    if (userBehavior.addedToCart) {
      // 用户加购物车时预连接支付服务
      preconnect(thirdPartyServices.payment, {
        crossOrigin: 'anonymous'
      });
    }
  }, [userBehavior]);

  const handleProductView = (productId) => {
    setUserBehavior(prev => ({ ...prev, viewingProduct: true }));

    // 预连接商品相关服务
    preconnect(thirdPartyServices.reviews);
    preconnect(thirdPartyServices.logistics);
  };

  const handleAddToCart = () => {
    setUserBehavior(prev => ({ ...prev, addedToCart: true }));

    // 预连接支付相关服务
    preconnect(thirdPartyServices.payment, {
      crossOrigin: 'anonymous'
    });
  };

  const handleCheckout = async () => {
    // 连接已建立，支付请求立即发送
    const paymentResponse = await fetch(\`\${thirdPartyServices.payment}/create-session\`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ items: cartItems })
    });

    const session = await paymentResponse.json();
    window.location.href = session.url;
  };

  const checkLogistics = async (orderId) => {
    // 连接已建立，物流查询立即响应
    const response = await fetch(\`\${thirdPartyServices.logistics}/track/\${orderId}\`);
    const trackingInfo = await response.json();
    setTrackingInfo(trackingInfo);
  };

  return (
    <div className="ecommerce-platform">
      <div className="connection-status">
        <h3>服务连接状态</h3>
        {Object.keys(thirdPartyServices).map(service => (
          <span
            key={service}
            className={\`status-badge \${connectionStatus.has(service) ? 'connected' : 'pending'}\`}
          >
            {service}: {connectionStatus.has(service) ? '已连接 ⚡' : '待连接'}
          </span>
        ))}
      </div>

      <div className="user-actions">
        <button onClick={() => handleProductView('product-123')}>
          查看商品详情
        </button>
        <button onClick={handleAddToCart}>
          加入购物车
        </button>
        <button onClick={handleCheckout}>
          立即结算 (连接已就绪)
        </button>
        <button onClick={() => checkLogistics('order-456')}>
          查询物流 (连接已就绪)
        </button>
      </div>
    </div>
  );
}`,
    explanation: '这个场景展示了如何在电商平台中使用preconnect优化第三方服务集成。通过在应用启动时和用户行为触发时预连接关键服务，确保支付、物流、评价等功能在用户需要时能够立即响应，显著提升购买流程的流畅性。',
    benefits: [
      '减少支付和物流查询的网络延迟，提升交易流程的响应速度',
      '提高用户购买转化率，减少因等待时间导致的购买放弃',
      '优化第三方服务的集成体验，增强平台的专业性和可靠性',
      '支持智能预连接策略，根据用户行为动态优化网络连接'
    ],
    metrics: {
      performance: '第三方API响应时间从800ms减少到200ms，性能提升75%',
      userExperience: '购买转化率提升18%，用户满意度评分从8.2提升到9.1',
      technicalMetrics: '网络连接建立时间减少85%，第三方服务集成延迟降低70%'
    },
    difficulty: 'easy',
    tags: ['电商平台', '第三方集成', '网络优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-2',
    title: '媒体平台CDN和字体服务优化',
    description: '在媒体内容平台中，需要加载大量的图片、视频、字体等资源，这些资源通常来自不同的CDN服务商。使用preconnect可以提前建立到这些CDN的连接，确保媒体内容能够快速加载，提升用户的浏览体验。',
    businessValue: '提升内容加载速度，减少用户等待时间，增强媒体平台的竞争力，特别适合需要快速展示大量视觉内容的平台。',
    scenario: '用户访问媒体平台时需要加载文章配图、视频缩略图、自定义字体等资源。这些资源分布在不同的CDN上，传统方式会在需要时才建立连接，导致内容加载缓慢，影响用户阅读体验。',
    code: `function MediaPlatformPreconnect() {
  const [contentType, setContentType] = useState('article');
  const [preconnectedCDNs, setPreconnectedCDNs] = useState(new Set());

  // CDN服务配置
  const cdnServices = {
    images: 'https://images.unsplash.com',
    videos: 'https://player.vimeo.com',
    fonts: 'https://fonts.googleapis.com',
    fontAssets: 'https://fonts.gstatic.com',
    userContent: 'https://cdn.medium.com',
    analytics: 'https://www.google-analytics.com'
  };

  useEffect(() => {
    // 应用启动时预连接核心CDN服务
    const coreCDNs = ['images', 'fonts', 'fontAssets'];

    coreCDNs.forEach(cdn => {
      const domain = cdnServices[cdn];
      preconnect(domain, {
        crossOrigin: 'anonymous'
      });
      setPreconnectedCDNs(prev => new Set([...prev, cdn]));
    });

    // 预连接分析服务
    preconnect(cdnServices.analytics, {
      crossOrigin: 'anonymous'
    });
  }, []);

  useEffect(() => {
    // 根据内容类型预连接相关CDN
    if (contentType === 'video') {
      preconnect(cdnServices.videos, {
        crossOrigin: 'anonymous'
      });
      setPreconnectedCDNs(prev => new Set([...prev, 'videos']));
    }

    if (contentType === 'userGenerated') {
      preconnect(cdnServices.userContent, {
        crossOrigin: 'anonymous'
      });
      setPreconnectedCDNs(prev => new Set([...prev, 'userContent']));
    }
  }, [contentType]);

  const loadArticleContent = async () => {
    // 连接已建立，图片加载更快
    const images = document.querySelectorAll('img[data-src]');
    images.forEach(img => {
      img.src = img.dataset.src; // 触发图片加载
    });
  };

  const loadVideoContent = async () => {
    setContentType('video');

    // 连接已建立，视频加载更快
    const videoContainer = document.getElementById('video-container');
    const iframe = document.createElement('iframe');
    iframe.src = 'https://player.vimeo.com/video/123456789';
    videoContainer.appendChild(iframe);
  };

  const loadCustomFonts = () => {
    // 连接已建立，字体加载更快
    const fontLink = document.createElement('link');
    fontLink.rel = 'stylesheet';
    fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap';
    document.head.appendChild(fontLink);
  };

  const preloadUserContent = () => {
    setContentType('userGenerated');

    // 预连接用户内容CDN
    preconnect(cdnServices.userContent, {
      crossOrigin: 'anonymous'
    });
  };

  return (
    <div className="media-platform">
      <div className="cdn-status">
        <h3>CDN连接状态</h3>
        {Object.keys(cdnServices).map(cdn => (
          <span
            key={cdn}
            className={\`cdn-badge \${preconnectedCDNs.has(cdn) ? 'connected' : 'pending'}\`}
          >
            {cdn}: {preconnectedCDNs.has(cdn) ? '已连接 ⚡' : '待连接'}
          </span>
        ))}
      </div>

      <div className="content-actions">
        <button onClick={loadArticleContent}>
          加载文章图片 (CDN已连接)
        </button>
        <button onClick={loadVideoContent}>
          加载视频内容 (CDN已连接)
        </button>
        <button onClick={loadCustomFonts}>
          加载自定义字体 (CDN已连接)
        </button>
        <button onClick={preloadUserContent}>
          预加载用户内容
        </button>
      </div>

      <div className="performance-metrics">
        <p>已预连接CDN: {preconnectedCDNs.size}个</p>
        <p>当前内容类型: {contentType}</p>
      </div>
    </div>
  );
}`,
    explanation: '这个场景展示了如何在媒体平台中使用preconnect优化CDN和字体服务。通过在应用启动时预连接核心CDN服务，并根据内容类型动态预连接相关服务，确保图片、视频、字体等媒体资源能够快速加载，显著提升用户的内容浏览体验。',
    benefits: [
      '显著减少媒体资源的加载延迟，提升内容展示速度',
      '优化字体加载性能，避免文字闪烁和布局偏移',
      '提升视频内容的播放启动速度，增强用户体验',
      '支持智能CDN预连接，根据内容类型优化网络连接策略'
    ],
    metrics: {
      performance: '媒体资源加载时间从1.2s减少到300ms，性能提升75%',
      userExperience: '内容加载满意度提升52%，用户停留时间增加35%',
      technicalMetrics: 'CDN连接建立时间减少80%，字体加载闪烁减少90%'
    },
    difficulty: 'medium',
    tags: ['媒体平台', 'CDN优化', '字体服务']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 'scenario-3',
    title: '企业级SaaS平台多区域API优化',
    description: '在企业级SaaS平台中，需要根据用户地理位置和业务需求连接到不同区域的API服务器，如美国、欧洲、亚太等。使用preconnect可以智能预连接到最优的API端点，确保企业用户获得最佳的服务响应速度。',
    businessValue: '提升企业用户的工作效率，减少跨区域API调用延迟，增强SaaS平台的全球竞争力，特别适合服务全球企业客户的平台。',
    scenario: '企业用户在SaaS平台中进行数据分析、报表生成、团队协作等操作时，需要频繁调用不同区域的API服务。传统方式会在API调用时才建立连接，导致跨区域网络延迟影响用户工作效率。',
    code: `function EnterpriseAPIPreconnect() {
  const [userRegion, setUserRegion] = useState('');
  const [apiEndpoints, setApiEndpoints] = useState({});
  const [connectionHealth, setConnectionHealth] = useState({});

  // 全球API端点配置
  const globalAPIEndpoints = {
    'us-east': {
      api: 'https://api-us-east.company.com',
      analytics: 'https://analytics-us-east.company.com',
      storage: 'https://storage-us-east.company.com'
    },
    'eu-west': {
      api: 'https://api-eu-west.company.com',
      analytics: 'https://analytics-eu-west.company.com',
      storage: 'https://storage-eu-west.company.com'
    },
    'asia-pacific': {
      api: 'https://api-ap-southeast.company.com',
      analytics: 'https://analytics-ap-southeast.company.com',
      storage: 'https://storage-ap-southeast.company.com'
    }
  };

  // 检测用户地理位置
  useEffect(() => {
    detectUserRegion().then(region => {
      setUserRegion(region);
      setApiEndpoints(globalAPIEndpoints[region]);
    });
  }, []);

  // 预连接到用户区域的API端点
  useEffect(() => {
    if (userRegion && apiEndpoints.api) {
      const endpoints = Object.values(apiEndpoints);

      endpoints.forEach(endpoint => {
        preconnect(endpoint, {
          crossOrigin: 'use-credentials'
        });
      });

      // 预连接到备用区域（容灾）
      const backupRegions = getBackupRegions(userRegion);
      backupRegions.forEach(region => {
        const backupEndpoints = globalAPIEndpoints[region];
        Object.values(backupEndpoints).forEach(endpoint => {
          preconnect(endpoint, {
            crossOrigin: 'use-credentials'
          });
        });
      });

      // 监控连接健康状态
      monitorConnectionHealth(endpoints);
    }
  }, [userRegion, apiEndpoints]);

  const detectUserRegion = async () => {
    try {
      const response = await fetch('/api/detect-region');
      const { region } = await response.json();
      return region;
    } catch (error) {
      return 'us-east'; // 默认区域
    }
  };

  const getBackupRegions = (primaryRegion) => {
    const backupMap = {
      'us-east': ['eu-west'],
      'eu-west': ['us-east'],
      'asia-pacific': ['us-east', 'eu-west']
    };
    return backupMap[primaryRegion] || [];
  };

  const monitorConnectionHealth = (endpoints) => {
    endpoints.forEach(async (endpoint) => {
      try {
        const start = performance.now();
        await fetch(\`\${endpoint}/health\`, { method: 'HEAD' });
        const latency = performance.now() - start;

        setConnectionHealth(prev => ({
          ...prev,
          [endpoint]: {
            status: 'healthy',
            latency: Math.round(latency)
          }
        }));
      } catch (error) {
        setConnectionHealth(prev => ({
          ...prev,
          [endpoint]: {
            status: 'unhealthy',
            latency: null
          }
        }));
      }
    });
  };

  const performAPICall = async (endpoint, path, data) => {
    // 连接已建立，API调用立即发送
    const response = await fetch(\`\${endpoint}\${path}\`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': \`Bearer \${userToken}\`
      },
      body: JSON.stringify(data)
    });

    return response.json();
  };

  const generateReport = async () => {
    // 使用预连接的分析API
    const reportData = await performAPICall(
      apiEndpoints.analytics,
      '/reports/generate',
      { timeRange: 'last-30-days' }
    );

    setReportData(reportData);
  };

  const uploadFile = async (file) => {
    // 使用预连接的存储API
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(\`\${apiEndpoints.storage}/upload\`, {
      method: 'POST',
      body: formData
    });

    return response.json();
  };

  return (
    <div className="enterprise-saas-platform">
      <div className="region-info">
        <h3>当前区域: {userRegion}</h3>
        <p>API端点: {apiEndpoints.api}</p>
      </div>

      <div className="connection-health">
        <h4>连接健康状态</h4>
        {Object.entries(connectionHealth).map(([endpoint, health]) => (
          <div key={endpoint} className="health-item">
            <span className={\`status \${health.status}\`}>
              {endpoint.split('//')[1]}: {health.status}
            </span>
            {health.latency && (
              <span className="latency">({health.latency}ms)</span>
            )}
          </div>
        ))}
      </div>

      <div className="enterprise-actions">
        <button onClick={generateReport}>
          生成分析报表 (API已预连接)
        </button>
        <button onClick={() => uploadFile(selectedFile)}>
          上传企业文档 (存储已预连接)
        </button>
        <button onClick={() => performAPICall(apiEndpoints.api, '/sync', {})}>
          同步企业数据 (API已预连接)
        </button>
      </div>
    </div>
  );
}`,
    explanation: '这个场景展示了如何在企业级SaaS平台中使用preconnect优化多区域API连接。通过智能检测用户地理位置，预连接到最优的API端点和备用区域，确保企业用户在进行数据分析、文件上传、报表生成等操作时获得最佳的网络性能，显著提升全球企业用户的工作效率。',
    benefits: [
      '显著减少跨区域API调用的网络延迟，提升企业用户工作效率',
      '支持智能区域检测和备用连接，确保服务的高可用性',
      '优化全球企业客户的使用体验，增强SaaS平台的竞争力',
      '提供连接健康监控，实现主动的网络性能管理',
      '支持容灾备份连接，确保业务连续性和数据安全'
    ],
    metrics: {
      performance: '跨区域API响应时间从2.1s减少到450ms，性能提升78%',
      userExperience: '企业用户工作效率提升45%，客户满意度评分从7.9提升到9.3',
      technicalMetrics: '全球API连接成功率达到99.8%，网络故障恢复时间减少60%'
    },
    difficulty: 'hard',
    tags: ['企业SaaS', '多区域API', '网络优化', '全球化部署']
  }
];

export default businessScenarios;