import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  definition: "preconnect是React DOM中用于预连接到外部域的函数，专门用于提前建立与第三方服务器的网络连接，包括DNS解析、TCP握手和TLS协商，显著减少后续资源请求的延迟，提升应用的网络性能。",

  introduction: `preconnect是React 18.0+引入的ReactDOM网络优化API，专门为现代Web应用的第三方资源集成设计的预连接函数。

它遵循现代Web性能优化的最佳实践，在网络连接建立和资源请求之间做出了智能优化，允许开发者提前声明需要连接的外部域，让浏览器在最佳时机建立网络连接。

主要用于第三方API预连接、CDN资源优化和外部服务集成。相比传统的被动连接方式，它的创新在于将网络连接的建立时机从"请求时"提前到"声明时"，实现了更精细的网络性能控制。

在React生态中，它是Resource Preloading APIs的重要组成部分，常见于需要集成多个第三方服务的复杂应用，特别适合需要优化外部资源加载性能的场景。

核心优势包括减少网络延迟、优化第三方服务响应时间、提升用户体验，但也需要注意合理控制预连接的域名数量以避免资源浪费。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react-dom/index.d.ts:35
 * - 实现文件：packages/react-dom/src/shared/ReactDOMResourceUtils.js:58
 * - 内部类型：packages/react-dom-bindings/src/shared/ReactDOMResourceValidation.js:28
 */

// 基础语法
function preconnect(href: string, options?: PreconnectOptions): void;

// 选项接口
interface PreconnectOptions {
  crossOrigin?: string;      // CORS设置：'anonymous' | 'use-credentials'
}

// 使用示例
preconnect('https://api.example.com');

preconnect('https://cdn.example.com', {
  crossOrigin: 'anonymous'
});

/**
 * 参数约束：
 * - href 必须是有效的域名URL
 * - options 是可选的配置对象
 * - 同一个域名多次调用会被去重
 * - 只建立连接，不下载具体资源
 */`,

  quickExample: `function PreconnectExample() {
  useEffect(() => {
    // 预连接到API服务器
    preconnect('https://api.example.com', {
      crossOrigin: 'anonymous'
    });

    // 预连接到CDN服务器
    preconnect('https://cdn.example.com');

    // 预连接到第三方分析服务
    preconnect('https://analytics.google.com', {
      crossOrigin: 'anonymous'
    });

    // 预连接到字体服务
    preconnect('https://fonts.googleapis.com');
    preconnect('https://fonts.gstatic.com', {
      crossOrigin: 'anonymous'
    });
  }, []);

  const handleApiCall = async () => {
    // 连接已建立，API请求立即发送
    const response = await fetch('https://api.example.com/data');
    const data = await response.json();
    setData(data);
  };

  const loadExternalResource = () => {
    // 连接已建立，资源加载更快
    const script = document.createElement('script');
    script.src = 'https://cdn.example.com/library.js';
    document.head.appendChild(script);
  };

  return (
    <div>
      {/* 网络连接已预建立，交互响应更快 */}
      <button onClick={handleApiCall}>
        获取数据 (连接已预建立)
      </button>
      <button onClick={loadExternalResource}>
        加载外部库 (连接已预建立)
      </button>
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "核心应用场景",
      description: "preconnect在实际开发中的核心应用场景，帮助开发者理解何时以及如何使用这个API来优化第三方服务的网络连接性能",
      diagram: `graph LR
      A[preconnect核心场景] --> B[第三方API集成]
      A --> C[CDN资源优化]
      A --> D[外部服务预连接]

      B --> B1["🌐 REST API<br/>后端服务预连接"]
      B --> B2["📊 GraphQL API<br/>数据查询服务预连接"]

      C --> C1["📦 静态资源CDN<br/>图片、字体、库文件"]
      C --> C2["🎨 字体服务<br/>Google Fonts等"]

      D --> D1["📈 分析服务<br/>Google Analytics等"]
      D --> D2["💬 第三方组件<br/>聊天、支付等服务"]

      style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
      style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
      style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "网络连接优化流程",
      description: "preconnect的网络连接优化流程，展示其如何通过提前建立连接来减少后续请求的延迟",
      diagram: `graph TB
      A[preconnect调用] --> B[DNS解析]
      A --> C[TCP连接建立]
      A --> D[TLS握手]

      B --> B1["🔍 域名解析<br/>将域名转换为IP地址"]
      C --> C1["🤝 TCP握手<br/>建立可靠连接"]
      D --> D1["🔒 SSL/TLS协商<br/>建立安全通道"]

      B1 --> E[连接池就绪]
      C1 --> E
      D1 --> E

      E --> F[后续请求]
      F --> F1["⚡ 立即发送<br/>跳过连接建立"]

      style A fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
      style B fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
      style C fill:#fce4ec,stroke:#c2185b,stroke-width:2px
      style D fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
      style E fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
      style F fill:#ffebee,stroke:#d32f2f,stroke-width:2px`
    },
    {
      title: "性能优化生态系统",
      description: "preconnect在React网络优化生态系统中的位置和与其他优化技术的协作关系",
      diagram: `graph TD
      A[React网络优化生态] --> B[连接层优化]
      A --> C[资源层优化]
      A --> D[应用层优化]

      B --> B1["preconnect<br/>域预连接"]
      B --> B2["prefetchDNS<br/>DNS预解析"]

      C --> C1["preload<br/>资源预加载"]
      C --> C2["preloadModule<br/>模块预加载"]

      D --> D1["Suspense<br/>异步组件"]
      D --> D2["startTransition<br/>优先级控制"]

      B1 -.-> C1
      B1 -.-> C2
      B2 -.-> B1
      C1 -.-> D1
      D2 -.-> D1

      style A fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
      style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
      style C fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
      style D fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
      style B1 fill:#ffebee,stroke:#d32f2f,stroke-width:3px`
    }
  ],
  
  parameters: [
    {
      name: "href",
      type: "string",
      required: true,
      description: "要预连接的域名URL。必须是有效的域名地址，支持HTTP和HTTPS协议。",
      example: `preconnect('https://api.example.com');
preconnect('https://fonts.googleapis.com');`
    },
    {
      name: "options",
      type: "PreconnectOptions",
      required: false,
      description: "可选的配置对象，用于指定CORS设置等选项。",
      example: `preconnect('https://cdn.example.com', {
  crossOrigin: 'anonymous'
});`
    }
  ],

  returnValue: {
    type: "void",
    description: "preconnect不返回任何值，它是一个纯粹的副作用函数，用于向浏览器发出域预连接的指令。",
    example: `// preconnect没有返回值
preconnect('https://api.example.com', {
  crossOrigin: 'anonymous'
});
// 函数执行后，浏览器开始建立到指定域的连接`
  },

  keyFeatures: [
    {
      title: "网络连接预建立",
      description: "提前建立到外部域的完整网络连接，包括DNS解析、TCP握手和TLS协商",
      benefit: "显著减少后续请求的网络延迟，提升第三方服务的响应速度"
    },
    {
      title: "智能连接管理",
      description: "浏览器智能管理预连接的生命周期，自动复用连接池中的连接",
      benefit: "避免重复建立连接，优化网络资源使用效率"
    },
    {
      title: "CORS兼容性",
      description: "提供完整的跨域资源共享配置选项，支持不同的CORS策略",
      benefit: "能够安全地预连接到不同域的第三方服务和CDN"
    },
    {
      title: "React集成优化",
      description: "与React的资源管理系统深度集成，自动处理重复调用和连接去重",
      benefit: "避免重复预连接同一域名，优化应用启动性能"
    },
    {
      title: "现代浏览器支持",
      description: "基于现代浏览器的Resource Hints标准，提供原生级别的性能优化",
      benefit: "充分利用浏览器的网络优化能力，实现最佳的连接性能"
    },
    {
      title: "开发者友好",
      description: "简单直观的API设计，易于理解和使用，支持声明式的网络优化",
      benefit: "降低网络优化的复杂度，让开发者专注于业务逻辑"
    }
  ],

  limitations: [
    "只能在React 18.0+版本中使用，在旧版本中不可用",
    "只建立网络连接，不下载具体资源，需要配合其他预加载API使用",
    "在服务端渲染环境中，preconnect调用会被忽略，只在客户端生效",
    "过度使用可能导致不必要的网络连接和资源浪费，需要合理控制预连接的域名数量",
    "预连接的效果依赖于浏览器的Resource Hints支持，在不支持的浏览器中无效果"
  ],

  bestPractices: [
    "在应用启动时预连接到关键的第三方域名，为后续请求做好准备",
    "为需要跨域访问的CDN和API服务设置适当的crossOrigin配置",
    "优先预连接高频使用的外部服务，如分析服务、字体服务、API服务器",
    "结合其他预加载API使用，如先preconnect建立连接，再preload加载具体资源",
    "在移动设备上谨慎使用，考虑网络条件和电池消耗的限制",
    "建立预连接的监控机制，跟踪预连接效果和网络性能改善",
    "避免预连接过多域名，通常建议不超过6个关键域名",
    "根据用户行为和应用场景动态调整预连接策略"
  ],

  warnings: [
    "不要预连接所有可能的外部域名，这会导致不必要的网络开销和连接竞争",
    "确保预连接的域名是真正需要的，错误的域名会浪费网络资源但不会报错",
    "在使用第三方CDN时，务必设置正确的crossOrigin参数以避免CORS问题",
    "注意预连接的时机，过早预连接可能导致连接超时，过晚预连接则无法发挥效果",
    "在严格的网络环境中，确保预连接的域名符合安全策略要求"
  ]
};

export default basicInfo;