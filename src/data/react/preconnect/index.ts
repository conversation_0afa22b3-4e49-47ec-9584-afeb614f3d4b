import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const preconnectData: ApiItem = {
  id: 'preconnect',
  title: 'preconnect',
  description: 'React DOM中用于预连接到外部域的函数，专门用于提前建立与第三方服务器的网络连接，包括DNS解析、TCP握手和TLS协商，显著减少后续资源请求的延迟，提升应用的网络性能。',
  category: 'ReactDOM Resource API',
  difficulty: 'easy',

  syntax: `preconnect(href: string, options?: PreconnectOptions): void`,
  example: `preconnect('https://api.example.com', {
  crossOrigin: 'anonymous'
});`,
  notes: '专门用于外部域预连接，支持DNS解析、TCP握手和TLS协商的完整网络连接建立，是Resource Preloading APIs的重要组成部分。',

  version: 'React 18.0+',
  tags: ["ReactDOM", "Resource", "Network", "Performance"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default preconnectData;