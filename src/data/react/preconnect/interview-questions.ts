import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 1,
    question: '什么是preconnect？它与prefetchDNS有什么区别？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'preconnect是React DOM中用于预连接到外部域的函数，它建立完整的网络连接（DNS+TCP+TLS），而prefetchDNS只进行DNS解析。',
      detailed: `preconnect是React 18.0+引入的ReactDOM网络优化API，专门用于预连接到外部域名。

**主要区别：**
1. **连接完整性**：preconnect建立完整连接（DNS解析+TCP握手+TLS协商），prefetchDNS只做DNS解析
2. **性能提升**：preconnect提供更大的性能提升，prefetchDNS提升相对较小
3. **资源消耗**：preconnect消耗更多资源，prefetchDNS消耗较少
4. **使用场景**：preconnect适合确定会访问的域名，prefetchDNS适合可能访问的域名

**使用时机：**
- 确定会访问的第三方API：使用preconnect
- 可能访问的外部资源：使用prefetchDNS
- 关键的CDN服务：使用preconnect
- 备用或可选服务：使用prefetchDNS

**核心优势：**
- 消除网络连接建立延迟
- 提升第三方服务响应速度
- 优化用户体验和转化率`,
      code: `// preconnect vs prefetchDNS 对比
function NetworkOptimizationExample() {
  useEffect(() => {
    // ✅ preconnect：确定会使用的服务
    preconnect('https://api.stripe.com', {
      crossOrigin: 'anonymous'
    });

    // ✅ prefetchDNS：可能会使用的服务
    prefetchDNS('https://backup-api.example.com');

    // ✅ 组合使用：核心服务用preconnect，备用服务用prefetchDNS
    preconnect('https://primary-cdn.com');
    prefetchDNS('https://backup-cdn.com');
  }, []);

  const makeAPICall = async () => {
    // preconnect的连接已建立，立即发送请求
    const response = await fetch('https://api.stripe.com/v1/charges');
    return response.json();
  };
}`
    },
    tags: ['基础概念', 'API对比']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 2,
    question: 'preconnect如何与其他Resource Preloading APIs配合使用？',
    difficulty: 'medium',
    frequency: 'high',
    category: '实现原理',
    answer: {
      brief: 'preconnect与其他预加载API形成完整的网络优化链：先preconnect建立连接，再preload加载资源，最后preinit预初始化，实现从连接到资源的全链路优化。',
      detailed: `preconnect在Resource Preloading APIs生态系统中扮演基础连接层的角色。

**优化链路：**
1. **连接层**：preconnect建立网络连接
2. **资源层**：preload/preloadModule加载具体资源
3. **初始化层**：preinit/preinitModule预初始化资源

**配合策略：**
- 先建立连接，再加载资源
- 根据优先级分层预加载
- 考虑网络条件和用户行为

**最佳实践：**
- 应用启动时：preconnect关键域名
- 用户交互时：preload具体资源
- 资源就绪时：preinit进行初始化

**性能监控：**
- 连接建立时间
- 资源加载时间
- 整体性能提升`,
      code: `// Resource Preloading APIs 协作示例
function ComprehensivePreloadStrategy() {
  useEffect(() => {
    // 1. 连接层：建立到关键域名的连接
    preconnect('https://fonts.googleapis.com');
    preconnect('https://api.example.com', {
      crossOrigin: 'anonymous'
    });

    // 2. 资源层：预加载具体资源
    setTimeout(() => {
      preload('https://fonts.googleapis.com/css2?family=Inter', {
        as: 'style'
      });
      preloadModule('/modules/critical.js');
    }, 100);

    // 3. 初始化层：预初始化资源
    setTimeout(() => {
      preinit('https://fonts.googleapis.com/css2?family=Inter', {
        as: 'style'
      });
    }, 200);
  }, []);

  const loadFeature = async () => {
    // 所有层级都已优化，功能立即可用
    const module = await import('/modules/critical.js');
    return module.default;
  };
}`
    },
    tags: ['API协作', '性能优化']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',

    id: 3,
    question: '在企业级应用中如何设计preconnect的全球化网络优化策略？',
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    answer: {
      brief: '企业级全球化网络优化需要考虑地理位置检测、多区域API端点、智能路由选择、连接健康监控等多个维度，建立自适应的网络连接管理系统。',
      detailed: `企业级全球化网络优化是一个复杂的系统工程，需要综合考虑多个因素。

**策略设计原则：**
1. **地理位置优化**：根据用户位置选择最优端点
2. **容灾备份**：建立多区域备用连接
3. **智能路由**：动态选择最佳网络路径
4. **健康监控**：实时监控连接质量

**核心组件设计：**
1. **地理检测器**：识别用户地理位置
2. **端点管理器**：管理全球API端点
3. **连接调度器**：智能调度网络连接
4. **健康监控器**：监控连接状态

**实施策略：**
1. 应用启动时检测用户区域
2. 预连接到最优区域端点
3. 建立备用区域连接
4. 持续监控连接健康状态

**性能监控：**
- 区域延迟统计
- 连接成功率监控
- 故障切换时间分析
- 用户体验指标跟踪`,
      code: `// 企业级全球化网络优化架构
class GlobalNetworkOptimizer {
  constructor() {
    this.regionDetector = new RegionDetector();
    this.endpointManager = new EndpointManager();
    this.connectionScheduler = new ConnectionScheduler();
    this.healthMonitor = new HealthMonitor();
  }

  async initialize() {
    // 检测用户区域
    const userRegion = await this.regionDetector.detect();

    // 获取区域端点配置
    const endpoints = this.endpointManager.getEndpoints(userRegion);

    // 预连接到主要端点
    this.preconnectPrimaryEndpoints(endpoints.primary);

    // 预连接到备用端点
    this.preconnectBackupEndpoints(endpoints.backup);

    // 启动健康监控
    this.healthMonitor.start(endpoints.all);
  }

  preconnectPrimaryEndpoints(endpoints) {
    endpoints.forEach(endpoint => {
      preconnect(endpoint.url, {
        crossOrigin: endpoint.cors
      });
    });
  }

  preconnectBackupEndpoints(endpoints) {
    // 延迟预连接备用端点
    setTimeout(() => {
      endpoints.forEach(endpoint => {
        preconnect(endpoint.url, {
          crossOrigin: endpoint.cors
        });
      });
    }, 1000);
  }

  async adaptiveAPICall(path, data) {
    // 选择最优端点
    const optimalEndpoint = await this.selectOptimalEndpoint();

    try {
      return await this.makeAPICall(optimalEndpoint, path, data);
    } catch (error) {
      // 故障切换到备用端点
      const backupEndpoint = await this.selectBackupEndpoint();
      return await this.makeAPICall(backupEndpoint, path, data);
    }
  }
}

// 使用示例
function EnterpriseApplication() {
  const [networkOptimizer] = useState(() => new GlobalNetworkOptimizer());

  useEffect(() => {
    networkOptimizer.initialize();
  }, []);

  return <div>企业应用内容</div>;
}`
    },
    tags: ['架构设计', '全球化']
  }
];

export default interviewQuestions;