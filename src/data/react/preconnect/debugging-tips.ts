import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',

  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'preconnect虽然API简单，但在实际使用中开发者经常遇到一些典型问题。本节提供完整的问题诊断和解决方案，帮助快速定位和修复preconnect相关的网络连接问题。',
        sections: [
          {
            title: '预连接无效果问题',
            description: '最常见的问题是preconnect看起来没有任何效果，网络请求时间没有改善。这通常涉及调用时机、域名配置、浏览器支持等多个方面',
            items: [
              {
                title: '调用时机过晚',
                description: 'preconnect在网络请求即将发起时才调用，没有足够的时间建立连接',
                solution: '1. 在应用启动时预连接核心域名；2. 在用户交互前预连接相关服务；3. 根据用户行为预测性预连接',
                prevention: '建立预连接策略，在合适的时机提前调用preconnect',
                code: `// ❌ 错误：调用时机过晚
const handleAPICall = async () => {
  preconnect('https://api.example.com'); // 太晚了
  const response = await fetch('https://api.example.com/data');
};

// ✅ 正确：提前预连接
function App() {
  useEffect(() => {
    // 应用启动时预连接
    preconnect('https://api.example.com', {
      crossOrigin: 'anonymous'
    });
  }, []);

  const handleAPICall = async () => {
    // 连接已建立，立即发送请求
    const response = await fetch('https://api.example.com/data');
  };
}`
              },
              {
                title: '域名配置错误',
                description: 'preconnect的域名与实际请求的域名不匹配，导致连接无法复用',
                solution: '1. 确保域名完全一致；2. 注意协议（http/https）的匹配；3. 检查子域名的配置',
                prevention: '使用常量或配置文件统一管理域名',
                code: `// ❌ 错误：域名不匹配
preconnect('https://api.example.com');
fetch('https://www.api.example.com/data'); // 子域名不同

// ✅ 正确：域名一致
const API_DOMAIN = 'https://api.example.com';
preconnect(API_DOMAIN);
fetch(\`\${API_DOMAIN}/data\`);

// ✅ 使用配置管理
const DOMAINS = {
  api: 'https://api.example.com',
  cdn: 'https://cdn.example.com'
};

preconnect(DOMAINS.api);
fetch(\`\${DOMAINS.api}/data\`);`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '有效的调试工具可以帮助开发者监控preconnect的效果，诊断网络问题，优化预连接策略。掌握这些工具是高效使用preconnect的关键。',
        sections: [
          {
            title: '浏览器开发者工具',
            description: '现代浏览器提供了强大的网络和性能分析工具，可以直观地监控域预连接的效果',
            items: [
              {
                title: '网络面板监控',
                description: '使用浏览器开发者工具的网络面板监控preconnect请求的状态和时机',
                solution: '1. 打开开发者工具网络面板；2. 筛选显示"Other"类型请求；3. 查看preconnect请求的时机和状态；4. 分析预连接效果',
                prevention: '定期检查网络面板，确保预连接按预期工作',
                code: `// 监控域预连接状态
function monitorPreconnectStatus() {
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.initiatorType === 'link' &&
          entry.name.includes('preconnect')) {
        console.log('Preconnect status:', {
          domain: entry.name,
          startTime: entry.startTime,
          duration: entry.duration,
          status: entry.responseStatus
        });
      }
    }
  });

  observer.observe({ entryTypes: ['resource'] });
}

// 检查域名是否已预连接
function isDomainPreconnected(domain) {
  const links = document.querySelectorAll('link[rel="preconnect"]');
  return Array.from(links).some(link => link.href === domain);
}`
              },
              {
                title: '连接时间分析',
                description: '使用Resource Timing API分析网络连接建立的详细时间',
                solution: '1. 使用performance.getEntriesByType分析；2. 对比预连接前后的时间差；3. 监控连接建立各阶段耗时',
                prevention: '建立连接性能监控机制，持续跟踪预连接效果',
                code: `// 网络连接时间分析
class ConnectionAnalyzer {
  constructor() {
    this.measurements = new Map();
  }

  analyzeConnectionTiming(url) {
    const entries = performance.getEntriesByName(url);
    if (entries.length === 0) return null;

    const entry = entries[0];
    return {
      dns: entry.domainLookupEnd - entry.domainLookupStart,
      tcp: entry.connectEnd - entry.connectStart,
      ssl: entry.secureConnectionStart > 0 ?
           entry.connectEnd - entry.secureConnectionStart : 0,
      total: entry.connectEnd - entry.domainLookupStart
    };
  }

  compareWithBaseline(url, baselineTime) {
    const currentTiming = this.analyzeConnectionTiming(url);
    if (currentTiming && baselineTime) {
      const improvement = ((baselineTime - currentTiming.total) / baselineTime * 100).toFixed(2);
      console.log(\`Connection improvement for \${url}: \${improvement}%\`);
    }
  }
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;