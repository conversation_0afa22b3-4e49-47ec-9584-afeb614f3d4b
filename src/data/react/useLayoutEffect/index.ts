import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useLayoutEffectData: ApiItem = {
  id: 'use-layout-effect',
  title: 'useLayoutEffect',
  description: 'React提供的同步执行副作用Hook，在DOM变更后、浏览器绘制前同步触发，适用于DOM测量和避免视觉闪烁的场景',
  category: 'React Hooks',
  difficulty: 'medium',
  
  syntax: `useLayoutEffect(effect, dependencies?)

// 类型定义
function useLayoutEffect(
  effect: EffectCallback,
  deps?: DependencyList
): void`,

  example: `import React, { useState, useLayoutEffect, useRef } from 'react';

function AutoResizeTextarea() {
  const [text, setText] = useState('');
  const textareaRef = useRef(null);

  useLayoutEffect(() => {
    // 在DOM更新后、浏览器绘制前同步调整高度
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  }); // 每次渲染都执行，确保高度实时调整

  return (
    <div>
      <h3>自适应文本区域</h3>
      <textarea
        ref={textareaRef}
        value={text}
        onChange={(e) => setText(e.target.value)}
        placeholder="输入文本，高度会自动调整..."
        style={{
          width: '100%',
          minHeight: '40px',
          maxHeight: '200px',
          resize: 'none',
          overflow: 'hidden',
          padding: '8px',
          border: '1px solid #ddd',
          borderRadius: '4px',
          fontSize: '14px',
          lineHeight: '1.5'
        }}
      />
      <p style={{ marginTop: '8px', color: '#666', fontSize: '12px' }}>
        这个示例展示了useLayoutEffect如何防止高度调整时的视觉闪烁
      </p>
    </div>
  );
}

export default AutoResizeTextarea;`,

  notes: '注意：useLayoutEffect会阻塞浏览器绘制，仅在需要同步DOM操作时使用，大部分情况应优先考虑useEffect',
  isNew: false,
  version: 'React 16.8+',
  tags: ['DOM操作', '同步执行', '性能优化', '浏览器渲染', '第三方集成'],
  
  // 9个完整Tab
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default useLayoutEffectData; 