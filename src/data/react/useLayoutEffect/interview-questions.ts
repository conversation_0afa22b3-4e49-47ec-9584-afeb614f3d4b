import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 'layout-vs-effect',
    question: 'useLayoutEffect和useEffect的区别是什么？什么时候应该使用useLayoutEffect？',
    difficulty: 'medium',
    category: '基础概念',
    answer: `useLayoutEffect和useEffect的主要区别在于执行时机和是否阻塞浏览器绘制：

## 执行时机差异

**useLayoutEffect**：
- 在Layout阶段同步执行
- DOM变更后、浏览器绘制前
- 会阻塞浏览器绘制

**useEffect**：
- 在Passive阶段异步执行  
- 浏览器绘制后
- 不阻塞浏览器绘制

## 使用场景

**useLayoutEffect适用于**：
1. DOM测量（获取元素尺寸、位置）
2. 防止视觉闪烁的样式调整
3. 第三方DOM库的同步初始化
4. 需要在绘制前完成的操作

**useEffect适用于**：
1. 数据获取
2. 事件监听器设置
3. 定时器管理
4. 大部分副作用处理

## 最佳实践

- 优先使用useEffect
- 只有在出现视觉问题（如闪烁）时才考虑useLayoutEffect
- 保持useLayoutEffect中的逻辑简短`,
    
    code: `// useLayoutEffect示例：防止闪烁
function Component() {
  const [width, setWidth] = useState(0);
  const divRef = useRef();

  // ❌ 使用useEffect会看到闪烁
  // useEffect(() => {
  //   setWidth(divRef.current.offsetWidth);
  // }, []);

  // ✅ 使用useLayoutEffect避免闪烁
  useLayoutEffect(() => {
    setWidth(divRef.current.offsetWidth);
  }, []);

  return (
    <div ref={divRef}>
      宽度: {width}px
    </div>
  );
}`,
    
    tags: ['执行时机', '性能优化', '最佳实践']
  },
  
  {
    id: 'ssr-compatibility',
    question: 'useLayoutEffect在服务端渲染(SSR)中有什么问题？如何解决？',
    difficulty: 'hard',
    category: 'SSR兼容性',
    answer: `useLayoutEffect在SSR环境中会产生问题，因为服务端没有DOM环境，但React仍然会尝试执行Layout Effects。

## 问题表现

1. **警告信息**：React会发出警告，提示useLayoutEffect在服务端不会执行
2. **行为不一致**：服务端和客户端的行为可能不同
3. **潜在错误**：如果Layout Effect中包含DOM操作，可能导致报错

## 解决方案

### 方案一：条件渲染
\`\`\`javascript
function Component() {
  const [isMounted, setIsMounted] = useState(false);
  
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  useLayoutEffect(() => {
    if (isMounted) {
      // DOM操作
    }
  }, [isMounted]);
}
\`\`\`

### 方案二：自定义Hook
\`\`\`javascript
const useIsomorphicLayoutEffect = 
  typeof window !== 'undefined' ? useLayoutEffect : useEffect;

function Component() {
  useIsomorphicLayoutEffect(() => {
    // DOM操作
  }, []);
}
\`\`\`

### 方案三：动态导入
\`\`\`javascript
function Component() {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  if (!mounted) {
    return <div>Loading...</div>;
  }
  
  return <ComponentWithLayoutEffect />;
}
\`\`\``,
    
    code: `// 完整的SSR兼容解决方案
import { useLayoutEffect, useEffect, useState } from 'react';

// 通用的SSR兼容Hook
const useIsomorphicLayoutEffect = 
  typeof window !== 'undefined' ? useLayoutEffect : useEffect;

function SSRCompatibleComponent() {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [isMounted, setIsMounted] = useState(false);
  const ref = useRef();

  // 标记客户端挂载
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 使用同构Hook
  useIsomorphicLayoutEffect(() => {
    if (isMounted && ref.current) {
      const { offsetWidth, offsetHeight } = ref.current;
      setDimensions({ width: offsetWidth, height: offsetHeight });
    }
  }, [isMounted]);

  return (
    <div ref={ref}>
      {isMounted ? (
        <span>尺寸: {dimensions.width} x {dimensions.height}</span>
      ) : (
        <span>测量中...</span>
      )}
    </div>
  );
}`,
    
    tags: ['SSR', '兼容性', '解决方案']
  },
  
  {
    id: 'performance-considerations',
    question: 'useLayoutEffect对性能有什么影响？如何优化？',
    difficulty: 'medium',
    category: '性能优化',
    answer: `useLayoutEffect的同步执行特性对性能有重要影响，需要谨慎使用和优化。

## 性能影响

**阻塞渲染**：
- useLayoutEffect会阻塞浏览器绘制
- 执行时间直接影响页面响应性
- 过长的执行时间会导致页面卡顿

**执行频率**：
- 每次相关状态变化都会同步执行
- 频繁的DOM操作会累积性能开销
- 依赖数组设置不当会导致过度执行

## 优化策略

### 1. 减少执行频率
\`\`\`javascript
// ❌ 每次都执行
useLayoutEffect(() => {
  measureElement();
});

// ✅ 条件执行
useLayoutEffect(() => {
  if (shouldMeasure) {
    measureElement();
  }
}, [shouldMeasure]);
\`\`\`

### 2. 批量操作
\`\`\`javascript
useLayoutEffect(() => {
  // 批量DOM读取
  const measurements = elements.map(el => ({
    width: el.offsetWidth,
    height: el.offsetHeight
  }));
  
  // 批量状态更新
  setState(measurements);
}, [elements]);
\`\`\`

### 3. 防抖处理
\`\`\`javascript
const debouncedMeasure = useCallback(
  debounce(() => {
    // 昂贵的测量操作
  }, 16), // 约60fps
  []
);

useLayoutEffect(() => {
  debouncedMeasure();
}, [dependency]);
\`\`\``,
    
    code: `// 性能优化示例
function OptimizedComponent({ items, isVisible }) {
  const [measurements, setMeasurements] = useState([]);
  const containerRef = useRef();
  const measurementCache = useRef(new Map());

  // 使用缓存避免重复测量
  useLayoutEffect(() => {
    if (!isVisible || !containerRef.current) return;

    const container = containerRef.current;
    const newMeasurements = [];

    items.forEach((item, index) => {
      const cacheKey = item.id + '-' + item.content.length;
      
      if (measurementCache.current.has(cacheKey)) {
        newMeasurements[index] = measurementCache.current.get(cacheKey);
      } else {
        const element = container.children[index];
        if (element) {
          const measurement = {
            width: element.offsetWidth,
            height: element.offsetHeight
          };
          measurementCache.current.set(cacheKey, measurement);
          newMeasurements[index] = measurement;
        }
      }
    });

    setMeasurements(newMeasurements);
  }, [items, isVisible]);

  return (
    <div ref={containerRef}>
      {items.map((item, index) => (
        <div key={item.id} data-index={index}>
          {item.content}
          {measurements[index] && (
            <span>({measurements[index].width}x{measurements[index].height})</span>
          )}
        </div>
      ))}
    </div>
  );
}`,
    
    tags: ['性能优化', '防抖', '缓存', '批量操作']
  },
  
  {
    id: 'dom-measurement-patterns',
    question: '如何使用useLayoutEffect正确地进行DOM测量？有哪些常见陷阱？',
    difficulty: 'medium',
    category: 'DOM操作',
    answer: `使用useLayoutEffect进行DOM测量需要注意时机、准确性和性能等多个方面。

## 正确的测量模式

### 1. 基本测量
\`\`\`javascript
function Component() {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const ref = useRef();

  useLayoutEffect(() => {
    if (ref.current) {
      const { offsetWidth, offsetHeight } = ref.current;
      setSize({ width: offsetWidth, height: offsetHeight });
    }
  }, []); // 仅在挂载时测量

  return <div ref={ref}>内容</div>;
}
\`\`\`

### 2. 响应式测量
\`\`\`javascript
function ResponsiveComponent() {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const ref = useRef();

  useLayoutEffect(() => {
    const element = ref.current;
    if (!element) return;

    const updateSize = () => {
      setSize({
        width: element.offsetWidth,
        height: element.offsetHeight
      });
    };

    updateSize();
    
    const resizeObserver = new ResizeObserver(updateSize);
    resizeObserver.observe(element);

    return () => resizeObserver.disconnect();
  }, []);
}
\`\`\`

## 常见陷阱

### 1. 测量时机错误
\`\`\`javascript
// ❌ 元素可能还不存在
useLayoutEffect(() => {
  const width = ref.current.offsetWidth; // 可能报错
}, []);

// ✅ 检查元素存在性
useLayoutEffect(() => {
  if (ref.current) {
    const width = ref.current.offsetWidth;
  }
}, []);
\`\`\`

### 2. 无限循环
\`\`\`javascript
// ❌ 依赖size会导致无限循环
useLayoutEffect(() => {
  setSize(ref.current.offsetWidth);
}, [size]);

// ✅ 正确的依赖设置
useLayoutEffect(() => {
  setSize(ref.current.offsetWidth);
}, [content]); // 依赖内容变化
\`\`\`

### 3. 性能问题
\`\`\`javascript
// ❌ 每次都重新计算
useLayoutEffect(() => {
  elements.forEach(el => {
    console.log(el.getBoundingClientRect());
  });
});

// ✅ 批量处理
useLayoutEffect(() => {
  const rects = elements.map(el => el.getBoundingClientRect());
  processRects(rects);
}, [elements]);
\`\`\``,
    
    code: `// 完整的DOM测量解决方案
function AdvancedMeasurement({ content, onMeasure }) {
  const elementRef = useRef();
  const [measurements, setMeasurements] = useState(null);
  const [error, setError] = useState(null);

  useLayoutEffect(() => {
    const element = elementRef.current;
    
    if (!element) {
      setError('元素引用不存在');
      return;
    }

    try {
      // 获取多种测量值
      const rect = element.getBoundingClientRect();
      const computedStyle = window.getComputedStyle(element);
      
      const measurementData = {
        // 基本尺寸
        width: element.offsetWidth,
        height: element.offsetHeight,
        
        // 包含边框的尺寸
        clientWidth: element.clientWidth,
        clientHeight: element.clientHeight,
        
        // 滚动尺寸
        scrollWidth: element.scrollWidth,
        scrollHeight: element.scrollHeight,
        
        // 相对视窗位置
        top: rect.top,
        left: rect.left,
        
        // 计算样式
        marginTop: parseInt(computedStyle.marginTop),
        marginLeft: parseInt(computedStyle.marginLeft),
        paddingTop: parseInt(computedStyle.paddingTop),
        paddingLeft: parseInt(computedStyle.paddingLeft)
      };

      setMeasurements(measurementData);
      setError(null);
      
      // 通知父组件
      onMeasure?.(measurementData);
      
    } catch (err) {
      setError('测量失败: ' + err.message);
    }
  }, [content, onMeasure]);

  return (
    <div>
      <div ref={elementRef} style={{ padding: '16px', border: '2px solid #ccc' }}>
        {content}
      </div>
      
      {error && <div style={{ color: 'red' }}>错误: {error}</div>}
      
      {measurements && (
        <div style={{ marginTop: '16px', fontSize: '12px' }}>
          <h4>测量结果:</h4>
          <pre>{JSON.stringify(measurements, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}`,
    
    tags: ['DOM测量', '错误处理', '响应式', 'ResizeObserver']
  },
  
  {
    id: 'third-party-integration',
    question: '如何使用useLayoutEffect集成需要DOM操作的第三方库？',
    difficulty: 'hard',
    category: '第三方集成',
    answer: `集成第三方DOM库时，useLayoutEffect确保库能在正确的时机获得准确的DOM状态。

## 集成步骤

### 1. 初始化时机
\`\`\`javascript
function ChartComponent({ data }) {
  const containerRef = useRef();
  const chartInstanceRef = useRef();

  useLayoutEffect(() => {
    const container = containerRef.current;
    if (container && container.offsetWidth > 0) {
      // 确保容器有实际尺寸后再初始化
      chartInstanceRef.current = new Chart(container, {
        type: 'line',
        data: data
      });
    }

    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
      }
    };
  }, []); // 仅初始化一次
}
\`\`\`

### 2. 数据同步
\`\`\`javascript
// 数据变化时更新图表
useLayoutEffect(() => {
  if (chartInstanceRef.current) {
    chartInstanceRef.current.data = data;
    chartInstanceRef.current.update();
  }
}, [data]);
\`\`\`

### 3. 尺寸响应
\`\`\`javascript
useLayoutEffect(() => {
  const handleResize = () => {
    if (chartInstanceRef.current) {
      chartInstanceRef.current.resize();
    }
  };

  window.addEventListener('resize', handleResize);
  return () => window.removeEventListener('resize', handleResize);
}, []);
\`\`\`

## 最佳实践

### 错误处理
\`\`\`javascript
useLayoutEffect(() => {
  try {
    const instance = new ThirdPartyLib(containerRef.current);
    instanceRef.current = instance;
  } catch (error) {
    console.error('第三方库初始化失败:', error);
    setError(error.message);
  }

  return () => {
    try {
      instanceRef.current?.destroy();
    } catch (error) {
      console.error('清理失败:', error);
    }
  };
}, []);
\`\`\`

### 加载状态
\`\`\`javascript
function Component() {
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState(null);

  useLayoutEffect(() => {
    const initLibrary = async () => {
      try {
        await loadLibrary();
        const instance = new Library(containerRef.current);
        setIsReady(true);
      } catch (err) {
        setError(err);
      }
    };

    initLibrary();
  }, []);

  if (error) return <div>错误: {error.message}</div>;
  if (!isReady) return <div>加载中...</div>;
  
  return <div ref={containerRef} />;
}
\`\`\``,
    
    code: `// 完整的第三方库集成示例
function D3ChartComponent({ data, width, height }) {
  const svgRef = useRef();
  const d3InstanceRef = useRef();
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);

  // 初始化D3图表
  useLayoutEffect(() => {
    const svg = svgRef.current;
    if (!svg) return;

    try {
      // 清理现有内容
      d3.select(svg).selectAll("*").remove();

      // 创建D3实例
      const margin = { top: 20, right: 20, bottom: 30, left: 40 };
      const innerWidth = width - margin.left - margin.right;
      const innerHeight = height - margin.top - margin.bottom;

      const g = d3.select(svg)
        .attr("width", width)
        .attr("height", height)
        .append("g")
        .attr("transform", "translate(" + margin.left + "," + margin.top + ")");

      // 保存实例引用
      d3InstanceRef.current = {
        svg: d3.select(svg),
        g: g,
        width: innerWidth,
        height: innerHeight,
        update: function(newData) {
          // 更新图表的方法
          this.g.selectAll(".bar")
            .data(newData)
            .enter().append("rect")
            .attr("class", "bar")
            .attr("x", (d, i) => i * (innerWidth / newData.length))
            .attr("y", d => innerHeight - d.value * 10)
            .attr("width", innerWidth / newData.length - 1)
            .attr("height", d => d.value * 10);
        }
      };

      setIsInitialized(true);
      setError(null);

    } catch (err) {
      setError(err.message);
      console.error('D3初始化失败:', err);
    }

    return () => {
      // 清理D3内容
      if (d3InstanceRef.current) {
        d3InstanceRef.current.svg.selectAll("*").remove();
        d3InstanceRef.current = null;
      }
      setIsInitialized(false);
    };
  }, [width, height]); // 尺寸变化时重新初始化

  // 数据更新
  useLayoutEffect(() => {
    if (isInitialized && d3InstanceRef.current && data) {
      try {
        d3InstanceRef.current.update(data);
      } catch (err) {
        console.error('D3更新失败:', err);
      }
    }
  }, [data, isInitialized]);

  if (error) {
    return <div style={{ color: 'red' }}>图表错误: {error}</div>;
  }

  return (
    <div>
      <svg ref={svgRef}>
        {!isInitialized && (
          <text x="50%" y="50%" textAnchor="middle" fill="#666">
            初始化中...
          </text>
        )}
      </svg>
    </div>
  );
}`,
    
    tags: ['第三方集成', 'D3.js', '错误处理', '生命周期管理']
  }
];

export default interviewQuestions; 