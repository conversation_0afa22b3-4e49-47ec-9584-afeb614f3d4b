import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 'dom-measurement',
    title: 'DOM元素测量',
    description: '使用useLayoutEffect进行精确的DOM尺寸和位置测量，确保获取准确的布局信息',
    difficulty: 'beginner',
    tags: ['DOM测量', '布局计算', '响应式设计'],
    
    problem: `在开发响应式组件时，经常需要根据内容动态调整元素尺寸。使用useEffect进行DOM测量会导致：
- 测量时机不准确，可能获取到过时的尺寸信息
- 异步执行导致视觉闪烁
- 多次测量和调整造成性能问题`,
    
    solution: `使用useLayoutEffect在DOM更新后立即同步测量，确保：
- 获取最新、准确的DOM尺寸信息
- 在浏览器绘制前完成尺寸调整
- 避免视觉闪烁，提供流畅的用户体验`,
    
    code: `import React, { useState, useRef, useLayoutEffect } from 'react';

// 场景1：自适应文本区域
function AutoResizeTextarea() {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [content, setContent] = useState('');

  useLayoutEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      // 同步重置高度
      textarea.style.height = 'auto';
      
      // 测量滚动高度
      const scrollHeight = textarea.scrollHeight;
      
      // 立即设置新高度，避免闪烁
      textarea.style.height = scrollHeight + 'px';
    }
  }, [content]); // 内容变化时重新测量

  return (
    <div className="textarea-container">
      <label>自适应文本区域：</label>
      <textarea
        ref={textareaRef}
        value={content}
        onChange={(e) => setContent(e.target.value)}
        placeholder="输入内容，高度会自动调整..."
        style={{
          width: '100%',
          minHeight: '60px',
          resize: 'none',
          overflow: 'hidden',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '8px'
        }}
      />
    </div>
  );
}

// 场景2：工具提示位置计算
function SmartTooltip({ children, content }: { children: ReactNode; content: string }) {
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });

  useLayoutEffect(() => {
    if (isVisible && triggerRef.current && tooltipRef.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // 计算最佳位置
      let top = triggerRect.bottom + 8;
      let left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;

      // 边界检查和调整
      if (left < 8) left = 8;
      if (left + tooltipRect.width > viewportWidth - 8) {
        left = viewportWidth - tooltipRect.width - 8;
      }
      if (top + tooltipRect.height > viewportHeight - 8) {
        top = triggerRect.top - tooltipRect.height - 8;
      }

      setPosition({ top, left });
    }
  }, [isVisible]);

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        style={{ display: 'inline-block', cursor: 'pointer' }}
      >
        {children}
      </div>
      
      {isVisible && (
        <div
          ref={tooltipRef}
          style={{
            position: 'fixed',
            top: position.top,
            left: position.left,
            background: '#333',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '4px',
            fontSize: '14px',
            zIndex: 1000,
            maxWidth: '200px',
            pointerEvents: 'none',
            opacity: position.top ? 1 : 0, // 位置计算完成后才显示
            transition: 'opacity 0.2s'
          }}
        >
          {content}
        </div>
      )}
    </>
  );
}

// 场景3：虚拟列表项高度测量
function VirtualListItem({ index, content, onHeightChange }: {
  index: number;
  content: string;
  onHeightChange: (index: number, height: number) => void;
}) {
  const itemRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (itemRef.current) {
      const height = itemRef.current.offsetHeight;
      onHeightChange(index, height);
    }
  }, [content, index, onHeightChange]);

  return (
    <div
      ref={itemRef}
      style={{
        padding: '16px',
        borderBottom: '1px solid #eee',
        wordWrap: 'break-word'
      }}
    >
      <strong>项目 {index + 1}</strong>
      <p>{content}</p>
    </div>
  );
}

// 使用示例
function DomMeasurementDemo() {
  const [items] = useState([
    '这是一个简短的内容',
    '这是一个比较长的内容，用来演示不同高度的列表项如何被正确测量和处理。在实际应用中，内容长度可能会有很大差异。',
    '中等长度的内容示例，展示了自适应布局的能力。'
  ]);

  const handleHeightChange = (index: number, height: number) => {
    console.log('项目', index, '的高度:', height, 'px');
  };

  return (
    <div style={{ maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
      <h2>DOM测量示例</h2>
      
      <section style={{ marginBottom: '40px' }}>
        <h3>1. 自适应文本区域</h3>
        <AutoResizeTextarea />
      </section>

      <section style={{ marginBottom: '40px' }}>
        <h3>2. 智能工具提示</h3>
        <p>
          移动鼠标到
          <SmartTooltip content="这是一个智能定位的工具提示，会自动避开视窗边界">
            <span style={{ background: '#e1f5fe', padding: '2px 6px', borderRadius: '3px' }}>
              这个链接
            </span>
          </SmartTooltip>
          上查看效果
        </p>
      </section>

      <section>
        <h3>3. 虚拟列表项测量</h3>
        <div style={{ border: '1px solid #ddd', borderRadius: '4px' }}>
          {items.map((content, index) => (
            <VirtualListItem
              key={index}
              index={index}
              content={content}
              onHeightChange={handleHeightChange}
            />
          ))}
        </div>
      </section>
    </div>
  );
}

export default DomMeasurementDemo;`,
    
    keyPoints: [
      'useLayoutEffect确保在DOM更新后立即同步测量',
      '避免使用useEffect导致的测量时机问题',
      '通过精确测量实现响应式布局和自适应组件',
      '工具提示定位需要考虑视窗边界和最佳用户体验',
      '虚拟列表等场景需要准确的元素高度信息'
    ],
    
    bestPractices: [
      '只在真正需要同步DOM操作时使用useLayoutEffect',
      '保持测量逻辑简洁高效，避免复杂计算',
      '使用防抖优化频繁的尺寸变化',
      '考虑边界情况和异常处理',
      '合理使用依赖数组控制测量时机'
    ]
  },
  
  {
    id: 'animation-optimization',
    title: '动画闪烁优化',
    description: '使用useLayoutEffect消除动画和过渡效果中的视觉闪烁，提供流畅的用户体验',
    difficulty: 'intermediate',
    tags: ['动画优化', '性能提升', '用户体验'],
    
    problem: `在实现动画效果时，使用useEffect会导致：
- 状态更新和DOM变更之间的时间差导致闪烁
- 动画开始前出现中间状态的视觉跳跃
- 过渡效果不够平滑，影响用户体验`,
    
    solution: `使用useLayoutEffect在浏览器绘制前同步处理动画相关的DOM操作：
- 在渲染后立即设置初始动画状态
- 确保过渡效果的连续性
- 避免中间状态的闪现`,
    
    code: `import React, { useState, useRef, useLayoutEffect, useCallback } from 'react';

// 场景1：手风琴动画优化
function SmoothAccordion({ title, children }: { title: string; children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(0);

  useLayoutEffect(() => {
    if (contentRef.current) {
      if (isOpen) {
        // 展开时：先测量内容高度，然后设置过渡
        const scrollHeight = contentRef.current.scrollHeight;
        setHeight(scrollHeight);
      } else {
        // 收起时：立即设置高度为0
        setHeight(0);
      }
    }
  }, [isOpen]);

  return (
    <div style={{ border: '1px solid #ddd', borderRadius: '4px', marginBottom: '8px' }}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        style={{
          width: '100%',
          padding: '12px 16px',
          background: '#f5f5f5',
          border: 'none',
          textAlign: 'left',
          cursor: 'pointer',
          fontSize: '16px',
          fontWeight: 'bold'
        }}
      >
        {title} {isOpen ? '−' : '+'}
      </button>
      
      <div
        style={{
          height: height,
          overflow: 'hidden',
          transition: 'height 0.3s ease-in-out'
        }}
      >
        <div ref={contentRef} style={{ padding: '16px' }}>
          {children}
        </div>
      </div>
    </div>
  );
}

// 场景2：滑动卡片动画
function SlideCard({ isVisible, children }: { isVisible: boolean; children: ReactNode }) {
  const cardRef = useRef<HTMLDivElement>(null);
  
  useLayoutEffect(() => {
    const card = cardRef.current;
    if (card) {
      if (isVisible) {
        // 显示动画：从右侧滑入
        card.style.transform = 'translateX(100%)';
        card.style.opacity = '0';
        
        // 强制重绘，然后设置最终状态
        card.offsetHeight; // 触发重绘
        
        card.style.transition = 'transform 0.4s ease-out, opacity 0.4s ease-out';
        card.style.transform = 'translateX(0)';
        card.style.opacity = '1';
      } else {
        // 隐藏动画：滑出到左侧
        card.style.transition = 'transform 0.4s ease-in, opacity 0.4s ease-in';
        card.style.transform = 'translateX(-100%)';
        card.style.opacity = '0';
      }
    }
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div
      ref={cardRef}
      style={{
        background: 'white',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        padding: '24px',
        margin: '16px 0'
      }}
    >
      {children}
    </div>
  );
}

// 场景3：进度条动画优化
function AnimatedProgressBar({ progress, duration = 1000 }: { progress: number; duration?: number }) {
  const barRef = useRef<HTMLDivElement>(null);
  const [displayProgress, setDisplayProgress] = useState(0);

  useLayoutEffect(() => {
    const bar = barRef.current;
    if (bar) {
      // 立即设置起始状态
      bar.style.width = displayProgress + '%';
      bar.style.transition = 'none';
      
      // 强制重绘
      bar.offsetWidth;
      
      // 设置过渡和目标状态
      bar.style.transition = 'width ' + duration + 'ms ease-out';
      bar.style.width = progress + '%';
      
      // 同步更新显示的进度值
      const startTime = Date.now();
      const startProgress = displayProgress;
      const progressDiff = progress - startProgress;
      
      const updateProgress = () => {
        const elapsed = Date.now() - startTime;
        const ratio = Math.min(elapsed / duration, 1);
        
        const currentProgress = startProgress + progressDiff * ratio;
        setDisplayProgress(Math.round(currentProgress));
        
        if (ratio < 1) {
          requestAnimationFrame(updateProgress);
        }
      };
      
      requestAnimationFrame(updateProgress);
    }
  }, [progress, duration, displayProgress]);

  return (
    <div style={{ marginBottom: '20px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
        <span>进度</span>
        <span>{displayProgress}%</span>
      </div>
      <div style={{
        width: '100%',
        height: '8px',
        background: '#f0f0f0',
        borderRadius: '4px',
        overflow: 'hidden'
      }}>
        <div
          ref={barRef}
          style={{
            height: '100%',
            background: 'linear-gradient(90deg, #4CAF50, #45a049)',
            borderRadius: '4px',
            width: '0%'
          }}
        />
      </div>
    </div>
  );
}

// 场景4：列表项动画
function AnimatedList({ items }: { items: string[] }) {
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  
  useLayoutEffect(() => {
    // 逐个显示列表项
    items.forEach((_, index) => {
      setTimeout(() => {
        setVisibleItems(prev => new Set([...prev, index]));
      }, index * 100);
    });
  }, [items]);

  return (
    <div>
      {items.map((item, index) => (
        <AnimatedListItem
          key={index}
          isVisible={visibleItems.has(index)}
          delay={index * 100}
        >
          {item}
        </AnimatedListItem>
      ))}
    </div>
  );
}

function AnimatedListItem({ 
  isVisible, 
  delay, 
  children 
}: { 
  isVisible: boolean; 
  delay: number; 
  children: ReactNode; 
}) {
  const itemRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    const item = itemRef.current;
    if (item && isVisible) {
      // 设置初始状态
      item.style.opacity = '0';
      item.style.transform = 'translateY(20px)';
      item.style.transition = 'none';
      
      // 强制重绘
      item.offsetHeight;
      
      // 设置动画
      setTimeout(() => {
        item.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
        item.style.opacity = '1';
        item.style.transform = 'translateY(0)';
      }, 50);
    }
  }, [isVisible]);

  return (
    <div
      ref={itemRef}
      style={{
        padding: '12px 16px',
        margin: '4px 0',
        background: '#f8f9fa',
        borderRadius: '4px',
        border: '1px solid #dee2e6'
      }}
    >
      {children}
    </div>
  );
}

// 使用示例
function AnimationOptimizationDemo() {
  const [accordionOpen, setAccordionOpen] = useState(false);
  const [cardVisible, setCardVisible] = useState(true);
  const [progress, setProgress] = useState(0);
  const [listItems] = useState(['项目 1', '项目 2', '项目 3', '项目 4', '项目 5']);

  const handleProgressChange = () => {
    const newProgress = Math.floor(Math.random() * 101);
    setProgress(newProgress);
  };

  return (
    <div style={{ maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
      <h2>动画优化示例</h2>
      
      <section style={{ marginBottom: '40px' }}>
        <h3>1. 流畅手风琴动画</h3>
        <SmoothAccordion title="点击展开/收起">
          <p>这是手风琴的内容区域。使用useLayoutEffect确保高度过渡动画的流畅性。</p>
          <p>内容可以是任意长度，动画会根据实际内容高度进行调整。</p>
        </SmoothAccordion>
      </section>

      <section style={{ marginBottom: '40px' }}>
        <h3>2. 滑动卡片效果</h3>
        <button 
          onClick={() => setCardVisible(!cardVisible)}
          style={{ marginBottom: '16px', padding: '8px 16px' }}
        >
          {cardVisible ? '隐藏' : '显示'}卡片
        </button>
        <SlideCard isVisible={cardVisible}>
          <h4>动画卡片</h4>
          <p>这个卡片使用了流畅的滑动动画效果，没有闪烁问题。</p>
        </SlideCard>
      </section>

      <section style={{ marginBottom: '40px' }}>
        <h3>3. 进度条动画</h3>
        <button 
          onClick={handleProgressChange}
          style={{ marginBottom: '16px', padding: '8px 16px' }}
        >
          随机进度
        </button>
        <AnimatedProgressBar progress={progress} />
      </section>

      <section>
        <h3>4. 列表项逐个显示</h3>
        <AnimatedList items={listItems} />
      </section>
    </div>
  );
}

export default AnimationOptimizationDemo;`,
    
    keyPoints: [
      'useLayoutEffect确保动画状态在浏览器绘制前设置完成',
      '通过强制重绘（offsetHeight/offsetWidth）确保样式应用',
      '合理使用setTimeout和requestAnimationFrame优化动画时序',
      '避免在动画过程中频繁的DOM查询和修改',
      '使用CSS过渡配合JavaScript状态管理获得最佳效果'
    ],
    
    bestPractices: [
      '优先使用CSS过渡和动画，JavaScript仅控制状态',
      '避免在useLayoutEffect中执行耗时操作',
      '使用transform和opacity进行动画，避免引起重排',
      '合理设置动画持续时间，平衡流畅度和性能',
      '考虑用户的动画偏好设置（prefers-reduced-motion）'
    ]
  },
  
  {
    id: 'third-party-integration',
    title: '第三方库同步集成',
    description: '使用useLayoutEffect与需要同步DOM操作的第三方库进行集成，确保初始化时机正确',
    difficulty: 'advanced',
    tags: ['第三方集成', '图表库', '地图组件', 'Canvas操作'],
    
    problem: `集成第三方DOM库时经常遇到的问题：
- 库初始化时DOM尚未完全准备好
- 异步初始化导致尺寸计算错误
- 组件重新渲染时库状态不同步
- 内存泄漏和事件监听器未清理`,
    
    solution: `使用useLayoutEffect确保：
- 在DOM完全准备好后立即初始化第三方库
- 同步的尺寸计算和配置
- 正确的清理和重新初始化机制
- 与React组件生命周期的完美配合`,
    
    code: `import React, { useRef, useLayoutEffect, useState, useCallback } from 'react';

// 模拟图表库接口
interface ChartInstance {
  destroy(): void;
  resize(): void;
  setOption(option: any): void;
}

interface ChartLib {
  init(container: HTMLElement): ChartInstance;
}

// 模拟的图表库
const mockChartLib: ChartLib = {
  init(container: HTMLElement): ChartInstance {
    console.log('初始化图表，容器尺寸：', container.offsetWidth, 'x', container.offsetHeight);
    
    // 模拟图表初始化
    const canvas = document.createElement('canvas');
    canvas.width = container.offsetWidth;
    canvas.height = container.offsetHeight;
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    container.appendChild(canvas);
    
    const ctx = canvas.getContext('2d');
    if (ctx) {
      // 绘制简单的图表
      ctx.fillStyle = '#4CAF50';
      ctx.fillRect(10, 10, canvas.width - 20, canvas.height - 20);
      ctx.fillStyle = 'white';
      ctx.font = '16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('模拟图表', canvas.width / 2, canvas.height / 2);
    }
    
    return {
      destroy() {
        if (container.contains(canvas)) {
          container.removeChild(canvas);
        }
      },
      resize() {
        canvas.width = container.offsetWidth;
        canvas.height = container.offsetHeight;
        // 重新绘制
        if (ctx) {
          ctx.fillStyle = '#4CAF50';
          ctx.fillRect(10, 10, canvas.width - 20, canvas.height - 20);
          ctx.fillStyle = 'white';
          ctx.font = '16px Arial';
          ctx.textAlign = 'center';
          ctx.fillText('图表已调整', canvas.width / 2, canvas.height / 2);
        }
      },
      setOption(option: any) {
        console.log('更新图表配置：', option);
      }
    };
  }
};

// 场景1：图表组件封装
function ChartComponent({ 
  data, 
  width = '100%', 
  height = '400px' 
}: { 
  data: any; 
  width?: string; 
  height?: string; 
}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<ChartInstance | null>(null);
  const [isReady, setIsReady] = useState(false);

  // 初始化图表
  useLayoutEffect(() => {
    const container = containerRef.current;
    if (container) {
      // 确保容器有实际尺寸
      if (container.offsetWidth > 0 && container.offsetHeight > 0) {
        try {
          chartInstanceRef.current = mockChartLib.init(container);
          setIsReady(true);
        } catch (error) {
          console.error('图表初始化失败：', error);
        }
      }
    }
    
    // 清理函数
    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
        chartInstanceRef.current = null;
        setIsReady(false);
      }
    };
  }, []); // 仅在组件挂载时初始化

  // 数据更新
  useLayoutEffect(() => {
    if (isReady && chartInstanceRef.current && data) {
      chartInstanceRef.current.setOption(data);
    }
  }, [data, isReady]);

  // 尺寸变化处理
  useLayoutEffect(() => {
    const handleResize = () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isReady]);

  return (
    <div
      ref={containerRef}
      style={{
        width,
        height,
        border: '1px solid #ddd',
        borderRadius: '4px',
        position: 'relative'
      }}
    >
      {!isReady && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: '#666'
        }}>
          加载中...
        </div>
      )}
    </div>
  );
}

// 场景2：富文本编辑器集成
function RichTextEditor({ 
  value, 
  onChange 
}: { 
  value: string; 
  onChange: (value: string) => void; 
}) {
  const editorRef = useRef<HTMLDivElement>(null);
  const editorInstanceRef = useRef<any>(null);

  useLayoutEffect(() => {
    const container = editorRef.current;
    if (container) {
      // 模拟富文本编辑器初始化
      container.contentEditable = 'true';
      container.style.border = '1px solid #ccc';
      container.style.borderRadius = '4px';
      container.style.padding = '12px';
      container.style.minHeight = '150px';
      container.style.outline = 'none';
      
      // 设置初始内容
      container.innerHTML = value || '请输入内容...';
      
      // 绑定事件
      const handleInput = () => {
        const content = container.innerHTML;
        onChange(content);
      };
      
      const handleFocus = () => {
        if (container.innerHTML === '请输入内容...') {
          container.innerHTML = '';
        }
      };
      
      const handleBlur = () => {
        if (container.innerHTML.trim() === '') {
          container.innerHTML = '请输入内容...';
        }
      };
      
      container.addEventListener('input', handleInput);
      container.addEventListener('focus', handleFocus);
      container.addEventListener('blur', handleBlur);
      
      // 保存清理函数
      editorInstanceRef.current = {
        destroy: () => {
          container.removeEventListener('input', handleInput);
          container.removeEventListener('focus', handleFocus);
          container.removeEventListener('blur', handleBlur);
        }
      };
    }
    
    return () => {
      if (editorInstanceRef.current) {
        editorInstanceRef.current.destroy();
      }
    };
  }, []);

  // 外部值变化时同步更新
  useLayoutEffect(() => {
    const container = editorRef.current;
    if (container && value !== container.innerHTML) {
      container.innerHTML = value || '请输入内容...';
    }
  }, [value]);

  return <div ref={editorRef} />;
}

// 场景3：地图组件集成
function MapComponent({ 
  center, 
  zoom, 
  markers 
}: { 
  center: [number, number]; 
  zoom: number; 
  markers: Array<{id: string; position: [number, number]; title: string}>; 
}) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);

  useLayoutEffect(() => {
    const container = mapRef.current;
    if (container) {
      // 模拟地图初始化
      container.style.background = '#e6f3ff';
      container.style.border = '1px solid #b3d9ff';
      container.style.borderRadius = '4px';
      container.style.position = 'relative';
      container.style.overflow = 'hidden';
      
      // 添加地图内容
      container.innerHTML = '<div style="padding: 20px; text-align: center; color: #0066cc;">模拟地图视图</div>';
      
      // 模拟地图实例
      mapInstanceRef.current = {
        setCenter: (newCenter: [number, number]) => {
          console.log('地图中心设置为：', newCenter);
        },
        setZoom: (newZoom: number) => {
          console.log('地图缩放设置为：', newZoom);
        },
        addMarkers: (newMarkers: any[]) => {
          console.log('添加标记：', newMarkers);
          // 清除现有标记
          const existingMarkers = container.querySelectorAll('.marker');
          existingMarkers.forEach(marker => marker.remove());
          
          // 添加新标记
          newMarkers.forEach((marker, index) => {
            const markerElement = document.createElement('div');
            markerElement.className = 'marker';
            markerElement.style.cssText = 
              'position: absolute; ' +
              'top: ' + (50 + index * 30) + 'px; ' +
              'left: ' + (50 + index * 40) + 'px; ' +
              'width: 20px; height: 20px; ' +
              'background: red; border-radius: 50%; ' +
              'cursor: pointer;';
            markerElement.title = marker.title;
            container.appendChild(markerElement);
          });
        },
        destroy: () => {
          container.innerHTML = '';
        }
      };
      
      // 设置初始状态
      mapInstanceRef.current.setCenter(center);
      mapInstanceRef.current.setZoom(zoom);
      mapInstanceRef.current.addMarkers(markers);
    }
    
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.destroy();
      }
    };
  }, []); // 仅初始化一次

  // 响应属性变化
  useLayoutEffect(() => {
    if (mapInstanceRef.current) {
      mapInstanceRef.current.setCenter(center);
    }
  }, [center]);

  useLayoutEffect(() => {
    if (mapInstanceRef.current) {
      mapInstanceRef.current.setZoom(zoom);
    }
  }, [zoom]);

  useLayoutEffect(() => {
    if (mapInstanceRef.current) {
      mapInstanceRef.current.addMarkers(markers);
    }
  }, [markers]);

  return (
    <div
      ref={mapRef}
      style={{
        width: '100%',
        height: '300px'
      }}
    />
  );
}

// 使用示例
function ThirdPartyIntegrationDemo() {
  const [chartData, setChartData] = useState({ title: '销售数据', value: 100 });
  const [editorValue, setEditorValue] = useState('<p>欢迎使用富文本编辑器</p>');
  const [mapCenter, setMapCenter] = useState<[number, number]>([39.9042, 116.4074]);
  const [mapZoom, setMapZoom] = useState(10);
  const [mapMarkers, setMapMarkers] = useState([
    { id: '1', position: [39.9042, 116.4074] as [number, number], title: '北京' },
    { id: '2', position: [31.2304, 121.4737] as [number, number], title: '上海' }
  ]);

  const updateChartData = () => {
    setChartData({
      title: '更新的数据',
      value: Math.floor(Math.random() * 200)
    });
  };

  const addMapMarker = () => {
    const newMarker = {
      id: Date.now().toString(),
      position: [
        39.9042 + (Math.random() - 0.5) * 0.1,
        116.4074 + (Math.random() - 0.5) * 0.1
      ] as [number, number],
      title: '新标记'
    };
    setMapMarkers(prev => [...prev, newMarker]);
  };

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '20px' }}>
      <h2>第三方库集成示例</h2>
      
      <section style={{ marginBottom: '40px' }}>
        <h3>1. 图表组件</h3>
        <button 
          onClick={updateChartData}
          style={{ marginBottom: '16px', padding: '8px 16px' }}
        >
          更新图表数据
        </button>
        <ChartComponent data={chartData} height="300px" />
      </section>

      <section style={{ marginBottom: '40px' }}>
        <h3>2. 富文本编辑器</h3>
        <RichTextEditor 
          value={editorValue} 
          onChange={setEditorValue} 
        />
        <p style={{ marginTop: '16px', fontSize: '14px', color: '#666' }}>
          当前内容长度: {editorValue.length} 字符
        </p>
      </section>

      <section>
        <h3>3. 地图组件</h3>
        <div style={{ marginBottom: '16px' }}>
          <button 
            onClick={() => setMapZoom(zoom => zoom + 1)}
            style={{ marginRight: '8px', padding: '4px 12px' }}
          >
            放大
          </button>
          <button 
            onClick={() => setMapZoom(zoom => Math.max(1, zoom - 1))}
            style={{ marginRight: '8px', padding: '4px 12px' }}
          >
            缩小
          </button>
          <button 
            onClick={addMapMarker}
            style={{ padding: '4px 12px' }}
          >
            添加标记
          </button>
        </div>
        <MapComponent 
          center={mapCenter}
          zoom={mapZoom}
          markers={mapMarkers}
        />
      </section>
    </div>
  );
}

export default ThirdPartyIntegrationDemo;`,
    
    keyPoints: [
      'useLayoutEffect确保第三方库在DOM完全准备好后立即初始化',
      '正确的清理机制防止内存泄漏和事件监听器累积',
      '同步的尺寸计算确保库初始化时获得正确的容器尺寸',
      '属性变化时的增量更新策略提高性能',
      '错误处理和降级方案确保组件的健壮性'
    ],
    
    bestPractices: [
      '在useLayoutEffect中进行库的初始化和销毁',
      '使用ref保存库实例，避免重复创建',
      '合理分离初始化、更新和清理逻辑',
      '添加加载状态和错误处理',
      '考虑库的异步加载和懒加载策略'
    ]
  }
];

export default businessScenarios; 