import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  overview: `useLayoutEffect作为同步执行的Hook，在调试时容易出现性能问题、执行时机错误、DOM操作失效等问题。这些问题往往表现为页面卡顿、视觉闪烁或功能异常，需要专门的调试方法来快速定位和解决。

本指南提供4种核心调试策略、6个实用调试工具，以及5个预防技巧，帮助开发者掌握useLayoutEffect的调试精髓，避免常见陷阱，实现高效的同步DOM操作。`,
  
  troubleshooting: [
    {
      symptom: 'useLayoutEffect导致页面卡顿或无响应',
      possibleCauses: [
        '在useLayoutEffect中执行了耗时的同步操作',
        '频繁的DOM测量导致布局抖动',
        '无限循环的依赖更新'
      ],
      solutions: [
        '使用Performance API测量执行时间',
        '将非DOM操作移到useEffect中',
        '添加依赖数组避免无限循环'
      ],
      codeExample: `// 调试useLayoutEffect性能问题
function PerformanceDebugger() {
  const [data, setData] = useState([]);
  const [metrics, setMetrics] = useState({});
  const elementRef = useRef(null);
  
  // ❌ 问题：耗时操作阻塞渲染
  useLayoutEffect(() => {
    const start = performance.now();
    
    // 模拟耗时操作
    for (let i = 0; i < 100000; i++) {
      Math.random();
    }
    
    const end = performance.now();
    console.warn(\`useLayoutEffect执行时间: \${end - start}ms\`);
    
    if (end - start > 16) { // 超过一帧的时间
      console.error('⚠️ useLayoutEffect执行时间过长，可能导致卡顿');
    }
  }, [data]);
  
  // ✅ 正确：性能监控和优化
  useLayoutEffect(() => {
    const start = performance.now();
    
    // 只执行必要的DOM操作
    if (elementRef.current) {
      const rect = elementRef.current.getBoundingClientRect();
      setMetrics(prev => ({
        ...prev,
        width: rect.width,
        height: rect.height
      }));
    }
    
    const end = performance.now();
    const duration = end - start;
    
    // 性能监控
    setMetrics(prev => ({
      ...prev,
      lastExecutionTime: duration,
      isPerformanceGood: duration < 5 // 5ms以内认为性能良好
    }));
    
    if (duration > 10) {
      console.warn(\`useLayoutEffect执行时间: \${duration}ms，建议优化\`);
    }
  });
  
  // 调试信息显示
  return (
    <div ref={elementRef}>
      <div>元素尺寸: {metrics.width} x {metrics.height}</div>
      <div>执行时间: {metrics.lastExecutionTime?.toFixed(2)}ms</div>
      <div style={{ 
        color: metrics.isPerformanceGood ? 'green' : 'red' 
      }}>
        性能状态: {metrics.isPerformanceGood ? '良好' : '需要优化'}
      </div>
    </div>
  );
}

// 高级调试：执行时机可视化
function ExecutionTimingDebugger() {
  const [logs, setLogs] = useState([]);
  
  const addLog = (message, type = 'info') => {
    setLogs(prev => [...prev, {
      message,
      type,
      timestamp: performance.now(),
      time: new Date().toLocaleTimeString()
    }]);
  };
  
  useEffect(() => {
    addLog('useEffect 执行', 'effect');
  });
  
  useLayoutEffect(() => {
    addLog('useLayoutEffect 执行', 'layout');
  });
  
  addLog('组件渲染', 'render');
  
  return (
    <div>
      <h3>执行时机调试</h3>
      <div style={{ maxHeight: '200px', overflow: 'auto' }}>
        {logs.map((log, index) => (
          <div key={index} style={{
            color: log.type === 'layout' ? 'red' : 
                   log.type === 'effect' ? 'blue' : 'black',
            fontSize: '12px',
            fontFamily: 'monospace'
          }}>
            [{log.time}] {log.message}
          </div>
        ))}
      </div>
    </div>
  );
}`,
      severity: 'high'
    },
    {
      symptom: 'DOM操作在useLayoutEffect中不生效',
      possibleCauses: [
        'DOM元素还未挂载到页面',
        'ref引用为null或undefined',
        '操作的DOM元素不是预期的元素'
      ],
      solutions: [
        '检查ref的有效性',
        '确认DOM元素已正确挂载',
        '使用MutationObserver监控DOM变化'
      ],
      codeExample: `// 调试DOM操作问题
function DOMOperationDebugger() {
  const [mounted, setMounted] = useState(false);
  const elementRef = useRef(null);
  const [debugInfo, setDebugInfo] = useState({});
  
  useLayoutEffect(() => {
    console.log('🔍 useLayoutEffect执行');
    console.log('elementRef.current:', elementRef.current);
    
    // 详细的DOM状态检查
    const debugData = {
      refExists: !!elementRef.current,
      refType: elementRef.current?.constructor.name,
      isConnected: elementRef.current?.isConnected,
      parentNode: elementRef.current?.parentNode?.tagName,
      boundingRect: elementRef.current?.getBoundingClientRect(),
      computedStyle: elementRef.current ? 
        window.getComputedStyle(elementRef.current).display : null
    };
    
    setDebugInfo(debugData);
    
    if (!elementRef.current) {
      console.error('❌ DOM元素引用为空');
      return;
    }
    
    if (!elementRef.current.isConnected) {
      console.error('❌ DOM元素未连接到文档');
      return;
    }
    
    // 安全的DOM操作
    try {
      elementRef.current.style.backgroundColor = 'lightblue';
      elementRef.current.style.padding = '10px';
      console.log('✅ DOM操作成功');
    } catch (error) {
      console.error('❌ DOM操作失败:', error);
    }
  });
  
  // 组件挂载状态跟踪
  useEffect(() => {
    setMounted(true);
    console.log('📍 组件已挂载');
    
    return () => {
      console.log('📍 组件即将卸载');
    };
  }, []);
  
  return (
    <div>
      <div ref={elementRef}>
        目标DOM元素 (挂载状态: {mounted ? '已挂载' : '未挂载'})
      </div>
      
      <div style={{ marginTop: '10px', fontSize: '12px' }}>
        <h4>调试信息:</h4>
        <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
      </div>
    </div>
  );
}

// DOM变化监控工具
function DOMChangeMonitor({ children }) {
  const containerRef = useRef(null);
  const [changes, setChanges] = useState([]);
  
  useLayoutEffect(() => {
    if (!containerRef.current) return;
    
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        setChanges(prev => [...prev, {
          type: mutation.type,
          target: mutation.target.tagName,
          timestamp: Date.now()
        }]);
      });
    });
    
    observer.observe(containerRef.current, {
      childList: true,
      attributes: true,
      subtree: true
    });
    
    return () => observer.disconnect();
  }, []);
  
  return (
    <div ref={containerRef}>
      {children}
      <div style={{ fontSize: '10px', color: 'gray' }}>
        DOM变化次数: {changes.length}
      </div>
    </div>
  );
}`,
      severity: 'medium'
    },
    {
      symptom: 'useLayoutEffect与useEffect执行顺序混乱',
      possibleCauses: [
        '对两者执行时机的理解错误',
        '依赖数组设置不当',
        '组件重渲染导致的执行顺序变化'
      ],
      solutions: [
        '理解浏览器渲染流程',
        '使用调试工具可视化执行顺序',
        '合理设计依赖数组'
      ],
      codeExample: `// 调试执行顺序问题
function ExecutionOrderDebugger() {
  const [count, setCount] = useState(0);
  const [executionLog, setExecutionLog] = useState([]);
  
  const addToLog = (message) => {
    const timestamp = performance.now();
    setExecutionLog(prev => [...prev, {
      message,
      timestamp,
      relativeTime: timestamp - (prev[0]?.timestamp || timestamp)
    }]);
  };
  
  // 渲染阶段
  console.log('🎨 组件渲染开始');
  addToLog('组件渲染');
  
  useLayoutEffect(() => {
    console.log('🔧 useLayoutEffect执行 (同步)');
    addToLog('useLayoutEffect执行');
    
    // 模拟DOM操作
    document.body.style.setProperty('--debug-color', 'red');
  }, [count]);
  
  useEffect(() => {
    console.log('⚡ useEffect执行 (异步)');
    addToLog('useEffect执行');
    
    // 模拟异步操作
    setTimeout(() => {
      addToLog('useEffect异步操作完成');
    }, 0);
  }, [count]);
  
  // 清理过多的日志
  useEffect(() => {
    if (executionLog.length > 20) {
      setExecutionLog(prev => prev.slice(-10));
    }
  }, [executionLog.length]);
  
  return (
    <div>
      <button onClick={() => setCount(c => c + 1)}>
        触发重渲染 (count: {count})
      </button>
      
      <div style={{ marginTop: '10px' }}>
        <h4>执行顺序日志:</h4>
        <div style={{ 
          maxHeight: '200px', 
          overflow: 'auto',
          fontSize: '12px',
          fontFamily: 'monospace',
          backgroundColor: '#f5f5f5',
          padding: '8px'
        }}>
          {executionLog.map((log, index) => (
            <div key={index}>
              [{log.relativeTime.toFixed(2)}ms] {log.message}
            </div>
          ))}
        </div>
      </div>
      
      <div style={{ marginTop: '10px', fontSize: '12px', color: 'gray' }}>
        💡 观察useLayoutEffect总是在useEffect之前执行
      </div>
    </div>
  );
}`,
      severity: 'medium'
    }
  ],
  
  tools: [
    {
      name: 'Chrome DevTools Performance',
      description: '浏览器性能分析工具，用于监控useLayoutEffect的执行时间和对渲染的影响',
      usage: '在Performance面板中记录页面性能，查看Layout和Paint事件的时间线',
      category: '浏览器工具',
      documentation: 'https://developer.chrome.com/docs/devtools/performance/'
    },
    {
      name: 'React DevTools Profiler',
      description: 'React性能分析工具，用于监控组件渲染和Hook执行',
      usage: '在Profiler面板中记录组件渲染，分析useLayoutEffect对性能的影响',
      category: '浏览器扩展',
      documentation: 'https://react.dev/blog/2018/09/10/introducing-the-react-profiler'
    },
    {
      name: 'Performance API',
      description: '浏览器原生性能API，用于精确测量代码执行时间',
      usage: '使用performance.mark()和performance.measure()测量useLayoutEffect执行时间',
      category: '浏览器API',
      documentation: 'https://developer.mozilla.org/en-US/docs/Web/API/Performance'
    }
  ],
  
  bestPractices: [
    '使用Performance API监控useLayoutEffect的执行时间',
    '在开发环境中添加性能警告，及时发现问题',
    '使用Chrome DevTools分析渲染性能',
    '建立执行时机的可视化调试工具',
    '定期检查DOM操作的有效性和必要性'
  ],
  
  commonMistakes: [
    {
      mistake: '在useLayoutEffect中执行异步操作',
      consequence: '异步操作不会阻塞渲染，失去了useLayoutEffect的意义',
      solution: '将异步操作移到useEffect中，或使用同步的DOM操作',
      prevention: '理解useLayoutEffect的同步特性，只用于同步DOM操作'
    },
    {
      mistake: '过度使用useLayoutEffect替代useEffect',
      consequence: '不必要的渲染阻塞，导致性能下降',
      solution: '只在需要同步DOM操作时使用useLayoutEffect',
      prevention: '建立使用原则：默认useEffect，特殊需求才用useLayoutEffect'
    },
    {
      mistake: '忽略useLayoutEffect的性能影响',
      consequence: '页面卡顿，用户体验下降',
      solution: '监控执行时间，优化耗时操作',
      prevention: '建立性能监控机制，设置执行时间阈值'
    }
  ]
};

export default debuggingTips;
