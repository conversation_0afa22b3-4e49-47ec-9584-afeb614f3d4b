import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'when-to-use',
    question: '什么时候应该使用useLayoutEffect而不是useEffect？',
    answer: `这是最常见的困惑。选择的关键在于是否需要在浏览器绘制前同步完成操作：

## 使用useLayoutEffect的场景

### 1. DOM测量操作
\`\`\`javascript
// 需要获取准确的DOM尺寸
function Component() {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const ref = useRef();

  useLayoutEffect(() => {
    // 必须在绘制前获取尺寸，避免闪烁
    if (ref.current) {
      setSize({
        width: ref.current.offsetWidth,
        height: ref.current.offsetHeight
      });
    }
  }, []);

  return <div ref={ref}>尺寸: {size.width}x{size.height}</div>;
}
\`\`\`

### 2. 防止布局闪烁
\`\`\`javascript
// 防止高度变化导致的视觉跳跃
function ExpandableContent({ isExpanded, children }) {
  const contentRef = useRef();
  const [height, setHeight] = useState(0);

  useLayoutEffect(() => {
    if (contentRef.current) {
      // 同步设置高度，避免闪烁
      setHeight(isExpanded ? contentRef.current.scrollHeight : 0);
    }
  }, [isExpanded]);

  return (
    <div style={{ height, overflow: 'hidden', transition: 'height 0.3s' }}>
      <div ref={contentRef}>{children}</div>
    </div>
  );
}
\`\`\`

### 3. 第三方库初始化
\`\`\`javascript
// 确保DOM准备好后立即初始化
function ChartComponent({ data }) {
  const containerRef = useRef();
  const chartRef = useRef();

  useLayoutEffect(() => {
    if (containerRef.current && !chartRef.current) {
      // 第三方库需要准确的容器尺寸
      chartRef.current = new Chart(containerRef.current, data);
    }
  }, []);

  return <div ref={containerRef} />;
}
\`\`\`

## 继续使用useEffect的场景

### 1. 数据获取
\`\`\`javascript
// 异步操作，不需要阻塞绘制
useEffect(() => {
  fetchData().then(setData);
}, []);
\`\`\`

### 2. 事件监听
\`\`\`javascript
// 事件绑定，不影响视觉呈现
useEffect(() => {
  const handler = () => console.log('click');
  document.addEventListener('click', handler);
  return () => document.removeEventListener('click', handler);
}, []);
\`\`\`

### 3. 副作用清理
\`\`\`javascript
// 定时器等清理工作
useEffect(() => {
  const timer = setInterval(() => {}, 1000);
  return () => clearInterval(timer);
}, []);
\`\`\`

## 判断原则

1. **会影响布局吗？** → useLayoutEffect
2. **需要防止闪烁吗？** → useLayoutEffect  
3. **是DOM测量吗？** → useLayoutEffect
4. **其他情况** → useEffect

记住：当不确定时，先用useEffect，如果出现视觉问题再改为useLayoutEffect。`,
    
    code: `// 完整示例：选择合适的Hook
function ResponsiveComponent({ content }) {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [data, setData] = useState(null);
  const [clickCount, setClickCount] = useState(0);
  const elementRef = useRef();

  // ✅ 使用useLayoutEffect - DOM测量，防止闪烁
  useLayoutEffect(() => {
    if (elementRef.current) {
      const { offsetWidth, offsetHeight } = elementRef.current;
      setDimensions({ width: offsetWidth, height: offsetHeight });
    }
  }, [content]); // 内容变化时重新测量

  // ✅ 使用useEffect - 数据获取，不阻塞渲染
  useEffect(() => {
    fetch('/api/data')
      .then(res => res.json())
      .then(setData);
  }, []);

  // ✅ 使用useEffect - 事件监听，不影响布局
  useEffect(() => {
    const handleClick = () => setClickCount(count => count + 1);
    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, []);

  return (
    <div ref={elementRef} style={{ padding: '16px', border: '1px solid #ccc' }}>
      <p>内容: {content}</p>
      <p>尺寸: {dimensions.width} x {dimensions.height}</p>
      <p>点击次数: {clickCount}</p>
      {data && <p>数据: {JSON.stringify(data)}</p>}
    </div>
  );
}`,
    
    tags: ['选择指南', '最佳实践', '性能优化']
  },
  
  {
    id: 'ssr-warnings',
    question: '在Next.js中使用useLayoutEffect总是出现警告，怎么解决？',
    answer: `这是SSR环境中最常见的问题。React在服务端检测到useLayoutEffect会发出警告，因为服务端没有DOM环境。

## 问题原因

在服务端渲染时：
- 没有DOM环境，无法执行DOM操作
- useLayoutEffect不会执行，但React会发出警告
- 客户端和服务端的行为可能不一致

## 解决方案

### 方案一：使用同构Hook
\`\`\`javascript
import { useLayoutEffect, useEffect } from 'react';

// 创建同构的layoutEffect Hook
const useIsomorphicLayoutEffect = 
  typeof window !== 'undefined' ? useLayoutEffect : useEffect;

function Component() {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const ref = useRef();

  useIsomorphicLayoutEffect(() => {
    if (ref.current) {
      setSize({
        width: ref.current.offsetWidth,
        height: ref.current.offsetHeight
      });
    }
  }, []);

  return <div ref={ref}>尺寸: {size.width}x{size.height}</div>;
}
\`\`\`

### 方案二：条件挂载
\`\`\`javascript
function Component() {
  const [isMounted, setIsMounted] = useState(false);
  const [size, setSize] = useState({ width: 0, height: 0 });
  const ref = useRef();

  // 标记客户端挂载状态
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useLayoutEffect(() => {
    if (isMounted && ref.current) {
      setSize({
        width: ref.current.offsetWidth,
        height: ref.current.offsetHeight
      });
    }
  }, [isMounted]);

  return (
    <div ref={ref}>
      {isMounted ? (
        <span>尺寸: {size.width}x{size.height}</span>
      ) : (
        <span>测量中...</span>
      )}
    </div>
  );
}
\`\`\`

### 方案三：动态导入（推荐）
\`\`\`javascript
import dynamic from 'next/dynamic';

// 包含useLayoutEffect的组件
function ClientOnlyComponent() {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const ref = useRef();

  useLayoutEffect(() => {
    if (ref.current) {
      setSize({
        width: ref.current.offsetWidth,
        height: ref.current.offsetHeight
      });
    }
  }, []);

  return <div ref={ref}>尺寸: {size.width}x{size.height}</div>;
}

// 动态导入，仅在客户端渲染
const DynamicComponent = dynamic(() => Promise.resolve(ClientOnlyComponent), {
  ssr: false,
  loading: () => <div>加载中...</div>
});

function ParentComponent() {
  return (
    <div>
      <h1>服务端渲染内容</h1>
      <DynamicComponent />
    </div>
  );
}
\`\`\`

### 方案四：自定义Hook封装
\`\`\`javascript
//import { useLayoutEffect, useEffect, useState } from 'react';

export function useClientLayoutEffect(callback, deps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useLayoutEffect(() => {
    if (isMounted) {
      return callback();
    }
  }, [isMounted, ...deps]);

  return isMounted;
}

// 使用
function Component() {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const ref = useRef();

  const isMounted = useClientLayoutEffect(() => {
    if (ref.current) {
      setSize({
        width: ref.current.offsetWidth,
        height: ref.current.offsetHeight
      });
    }
  }, []);

  return (
    <div ref={ref}>
      {isMounted ? '尺寸: ' + size.width + 'x' + size.height : '服务端渲染'}
    </div>
  );
}
\`\`\`

## 最佳实践

1. **优先使用方案一**（同构Hook）- 简单直接
2. **复杂组件使用方案三**（动态导入）- 性能更好
3. **避免滥用**：只有真正需要DOM操作时才使用
4. **提供降级方案**：确保服务端也有合理的显示内容`,
    
    code: `// Next.js完整解决方案示例
import { useState, useRef, useLayoutEffect, useEffect } from 'react';
import dynamic from 'next/dynamic';

// 同构Hook
const useIsomorphicLayoutEffect = 
  typeof window !== 'undefined' ? useLayoutEffect : useEffect;

// 客户端专用组件
function MeasurementComponent({ onMeasure }) {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const elementRef = useRef();

  useLayoutEffect(() => {
    if (elementRef.current) {
      const { offsetWidth, offsetHeight } = elementRef.current;
      const newDimensions = { width: offsetWidth, height: offsetHeight };
      setDimensions(newDimensions);
      onMeasure?.(newDimensions);
    }
  }, [onMeasure]);

  return (
    <div 
      ref={elementRef} 
      style={{ 
        padding: '20px', 
        border: '2px solid #007acc',
        borderRadius: '8px',
        background: '#f0f8ff'
      }}
    >
      <h3>DOM测量组件</h3>
      <p>当前尺寸: {dimensions.width} × {dimensions.height} 像素</p>
      <p>这个组件使用useLayoutEffect进行精确的DOM测量</p>
    </div>
  );
}

// 动态导入，禁用SSR
const DynamicMeasurement = dynamic(() => Promise.resolve(MeasurementComponent), {
  ssr: false,
  loading: () => (
    <div style={{ 
      padding: '20px', 
      border: '2px dashed #ccc', 
      borderRadius: '8px',
      textAlign: 'center',
      color: '#666'
    }}>
      📏 DOM测量组件加载中...
    </div>
  )
});

// 主组件
function SSRFriendlyPage() {
  const [measurements, setMeasurements] = useState(null);

  // 使用同构Hook处理简单的客户端逻辑
  useIsomorphicLayoutEffect(() => {
    console.log('页面已在客户端渲染');
  }, []);

  const handleMeasure = (dimensions) => {
    setMeasurements(dimensions);
  };

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '20px' }}>
      <h1>SSR兼容的useLayoutEffect示例</h1>
      
      <section style={{ marginBottom: '32px' }}>
        <h2>服务端渲染内容</h2>
        <p>这些内容在服务端和客户端都会正常显示，没有useLayoutEffect警告。</p>
      </section>

      <section style={{ marginBottom: '32px' }}>
        <h2>客户端专用测量组件</h2>
        <DynamicMeasurement onMeasure={handleMeasure} />
        
        {measurements && (
          <div style={{ 
            marginTop: '16px', 
            padding: '12px', 
            background: '#e8f5e9',
            borderRadius: '4px'
          }}>
            <strong>测量结果:</strong> 宽度 {measurements.width}px，高度 {measurements.height}px
          </div>
        )}
      </section>

      <section>
        <h2>技术说明</h2>
        <ul>
          <li>✅ 服务端渲染正常，无警告</li>
          <li>✅ 客户端DOM测量准确</li>
          <li>✅ 提供加载状态反馈</li>
          <li>✅ 优雅的降级体验</li>
        </ul>
      </section>
    </div>
  );
}

export default SSRFriendlyPage;`,
    
    tags: ['SSR', 'Next.js', '警告解决', '动态导入']
  },
  
  {
    id: 'performance-issues',
    question: 'useLayoutEffect导致页面卡顿，如何优化性能？',
    answer: `useLayoutEffect的同步执行特性可能导致性能问题，需要通过多种策略来优化。

## 性能问题的原因

### 1. 阻塞渲染
\`\`\`javascript
// ❌ 这会阻塞浏览器绘制
useLayoutEffect(() => {
  // 耗时的计算
  for (let i = 0; i < 1000000; i++) {
    doSomething(i);
  }
}, []);
\`\`\`

### 2. 频繁执行
\`\`\`javascript
// ❌ 每次滚动都会执行
useLayoutEffect(() => {
  updateScrollPosition();
}, [scrollY]); // scrollY频繁变化
\`\`\`

### 3. DOM操作过多
\`\`\`javascript
// ❌ 大量DOM查询
useLayoutEffect(() => {
  elements.forEach(el => {
    const rect = el.getBoundingClientRect(); // 引起重排
    updatePosition(el, rect);
  });
}, [elements]);
\`\`\`

## 优化策略

### 1. 减少执行频率
\`\`\`javascript
// ✅ 使用防抖
const debouncedUpdate = useCallback(
  debounce(() => {
    updateLayout();
  }, 16), // 约60fps
  []
);

useLayoutEffect(() => {
  debouncedUpdate();
}, [dependency]);

// ✅ 使用节流
const throttledUpdate = useCallback(
  throttle(() => {
    updateLayout();
  }, 16),
  []
);
\`\`\`

### 2. 批量DOM操作
\`\`\`javascript
// ✅ 批量读取DOM
useLayoutEffect(() => {
  // 一次性读取所有需要的DOM属性
  const measurements = elements.map(el => ({
    element: el,
    rect: el.getBoundingClientRect(),
    computed: window.getComputedStyle(el)
  }));

  // 批量处理数据
  const updates = measurements.map(processData);
  
  // 批量应用更新
  applyUpdates(updates);
}, [elements]);
\`\`\`

### 3. 使用缓存
\`\`\`javascript
// ✅ 缓存计算结果
function OptimizedComponent({ items }) {
  const [measurements, setMeasurements] = useState(new Map());
  const cacheRef = useRef(new Map());

  useLayoutEffect(() => {
    const newMeasurements = new Map();

    items.forEach(item => {
      const cacheKey = getCacheKey(item);
      
      if (cacheRef.current.has(cacheKey)) {
        // 使用缓存
        newMeasurements.set(item.id, cacheRef.current.get(cacheKey));
      } else {
        // 计算新值
        const measurement = calculateMeasurement(item);
        cacheRef.current.set(cacheKey, measurement);
        newMeasurements.set(item.id, measurement);
      }
    });

    setMeasurements(newMeasurements);
  }, [items]);
}
\`\`\`

### 4. 条件执行
\`\`\`javascript
// ✅ 只在必要时执行
useLayoutEffect(() => {
  if (!isVisible || !needsUpdate) {
    return;
  }

  performExpensiveOperation();
}, [isVisible, needsUpdate, data]);
\`\`\`

### 5. 拆分复杂操作
\`\`\`javascript
// ✅ 将复杂操作拆分为多个简单的useLayoutEffect
useLayoutEffect(() => {
  // 只处理尺寸测量
  measureElements();
}, [elements]);

useLayoutEffect(() => {
  // 只处理位置计算
  calculatePositions();
}, [measurements]);

useLayoutEffect(() => {
  // 只处理样式更新
  updateStyles();
}, [positions]);
\`\`\`

### 6. 使用Web Workers（适用场景有限）
\`\`\`javascript
// ✅ 对于纯计算可以使用Web Worker
useLayoutEffect(() => {
  const worker = new Worker('/calculation-worker.js');
  
  worker.postMessage({ type: 'CALCULATE', data });
  
  worker.onmessage = (e) => {
    // 应用计算结果
    applyResults(e.data);
  };

  return () => worker.terminate();
}, [data]);
\`\`\`

## 性能监控

### 1. 使用Performance API
\`\`\`javascript
useLayoutEffect(() => {
  const start = performance.now();
  
  performOperation();
  
  const end = performance.now();
  if (end - start > 16) { // 超过一帧的时间
    console.warn('useLayoutEffect执行时间过长:', end - start, 'ms');
  }
}, []);
\`\`\`

### 2. React DevTools Profiler
\`\`\`javascript
// 在开发环境中添加标记
useLayoutEffect(() => {
  if (process.env.NODE_ENV === 'development') {
    performance.mark('layout-effect-start');
  }
  
  performOperation();
  
  if (process.env.NODE_ENV === 'development') {
    performance.mark('layout-effect-end');
    performance.measure('layout-effect', 'layout-effect-start', 'layout-effect-end');
  }
}, []);
\`\`\`

## 最佳实践总结

1. **保持简短**：useLayoutEffect中的操作应该尽可能简短
2. **减少频率**：使用防抖、节流或条件执行
3. **批量操作**：将多个DOM操作合并为一次
4. **使用缓存**：避免重复计算相同的值
5. **监控性能**：定期检查执行时间
6. **考虑替代方案**：评估是否真的需要useLayoutEffect`,
    
    code: `// 完整的性能优化示例
import { useState, useRef, useLayoutEffect, useCallback } from 'react';

// 防抖和节流工具函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

function throttle(func, limit) {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

function PerformanceOptimizedComponent({ items, isVisible }) {
  const [measurements, setMeasurements] = useState(new Map());
  const [isProcessing, setIsProcessing] = useState(false);
  const containerRef = useRef();
  const cacheRef = useRef(new Map());
  const performanceRef = useRef({ count: 0, totalTime: 0 });

  // 缓存键生成
  const getCacheKey = useCallback((item) => {
    return item.id + '-' + item.content?.length + '-' + item.type;
  }, []);

  // 批量测量函数
  const performMeasurements = useCallback(() => {
    if (!containerRef.current || !isVisible || items.length === 0) {
      return;
    }

    const start = performance.now();
    setIsProcessing(true);

    try {
      const container = containerRef.current;
      const newMeasurements = new Map();
      const elementsToMeasure = [];

      // 第一轮：收集需要测量的元素
      items.forEach((item, index) => {
        const cacheKey = getCacheKey(item);
        
        if (cacheRef.current.has(cacheKey)) {
          // 使用缓存
          newMeasurements.set(item.id, cacheRef.current.get(cacheKey));
        } else {
          // 需要测量
          const element = container.children[index];
          if (element) {
            elementsToMeasure.push({ item, element, index });
          }
        }
      });

      // 第二轮：批量DOM读取（减少重排）
      const batchMeasurements = elementsToMeasure.map(({ item, element }) => {
        const rect = element.getBoundingClientRect();
        const computed = window.getComputedStyle(element);
        
        return {
          item,
          measurement: {
            width: element.offsetWidth,
            height: element.offsetHeight,
            top: rect.top,
            left: rect.left,
            marginTop: parseInt(computed.marginTop, 10) || 0,
            marginLeft: parseInt(computed.marginLeft, 10) || 0
          }
        };
      });

      // 第三轮：更新缓存和状态
      batchMeasurements.forEach(({ item, measurement }) => {
        const cacheKey = getCacheKey(item);
        cacheRef.current.set(cacheKey, measurement);
        newMeasurements.set(item.id, measurement);
      });

      setMeasurements(newMeasurements);

      // 性能统计
      const end = performance.now();
      const duration = end - start;
      performanceRef.current.count += 1;
      performanceRef.current.totalTime += duration;

      if (duration > 16) {
        console.warn('Layout effect执行时间过长:', duration.toFixed(2), 'ms');
      }

    } catch (error) {
      console.error('测量过程中出错:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [items, isVisible, getCacheKey]);

  // 防抖的测量函数
  const debouncedMeasure = useCallback(
    debounce(performMeasurements, 16),
    [performMeasurements]
  );

  // 优化的useLayoutEffect
  useLayoutEffect(() => {
    if (!isVisible) return;

    // 小数据量直接执行，大数据量使用防抖
    if (items.length < 10) {
      performMeasurements();
    } else {
      debouncedMeasure();
    }
  }, [items, isVisible, performMeasurements, debouncedMeasure]);

  // 清理缓存（可选）
  useLayoutEffect(() => {
    return () => {
      // 组件卸载时清理缓存，避免内存泄漏
      if (cacheRef.current.size > 100) {
        cacheRef.current.clear();
      }
    };
  }, []);

  return (
    <div ref={containerRef} style={{ position: 'relative' }}>
      {isProcessing && (
        <div style={{
          position: 'absolute',
          top: 0,
          right: 0,
          background: 'rgba(255, 255, 0, 0.8)',
          padding: '4px 8px',
          fontSize: '12px',
          borderRadius: '4px',
          zIndex: 1000
        }}>
          测量中...
        </div>
      )}
      
      {items.map((item, index) => {
        const measurement = measurements.get(item.id);
        return (
          <div
            key={item.id}
            style={{
              padding: '16px',
              margin: '8px 0',
              border: '1px solid #ddd',
              borderRadius: '4px',
              background: measurement ? '#f0f8ff' : '#f5f5f5'
            }}
          >
            <h4>{item.title}</h4>
            <p>{item.content}</p>
            {measurement && (
              <div style={{ fontSize: '12px', color: '#666' }}>
                尺寸: {measurement.width}×{measurement.height}px, 
                位置: ({measurement.left.toFixed(0)}, {measurement.top.toFixed(0)})
              </div>
            )}
          </div>
        );
      })}

      {/* 性能统计（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          marginTop: '16px',
          padding: '12px',
          background: '#f8f9fa',
          borderRadius: '4px',
          fontSize: '12px'
        }}>
          <strong>性能统计:</strong><br />
          执行次数: {performanceRef.current.count}<br />
          平均耗时: {(performanceRef.current.totalTime / Math.max(1, performanceRef.current.count)).toFixed(2)}ms<br />
          缓存命中: {cacheRef.current.size} 项
        </div>
      )}
    </div>
  );
}

export default PerformanceOptimizedComponent;`,
    
    tags: ['性能优化', '防抖节流', '批量操作', '缓存策略']
  },
  
  {
    id: 'measurement-inaccuracy',
    question: '为什么useLayoutEffect测量的DOM尺寸有时候不准确？',
    answer: `DOM测量不准确通常有以下几个原因，需要针对性地解决。

## 常见原因和解决方案

### 1. 元素尚未完全渲染
\`\`\`javascript
// ❌ 元素可能还没有实际尺寸
useLayoutEffect(() => {
  console.log(ref.current.offsetWidth); // 可能为0
}, []);

// ✅ 检查元素状态
useLayoutEffect(() => {
  const element = ref.current;
  if (element && element.offsetParent !== null) {
    // 确保元素可见且有尺寸
    console.log(element.offsetWidth);
  }
}, []);
\`\`\`

### 2. CSS样式未应用完成
\`\`\`javascript
// ❌ 样式可能还在加载
useLayoutEffect(() => {
  const width = ref.current.offsetWidth;
  setWidth(width);
}, []);

// ✅ 等待样式表加载完成
useLayoutEffect(() => {
  const checkAndMeasure = () => {
    const element = ref.current;
    if (!element) return;

    // 检查计算样式是否可用
    const computed = window.getComputedStyle(element);
    if (computed.display === 'none') {
      setTimeout(checkAndMeasure, 10);
      return;
    }

    setWidth(element.offsetWidth);
  };

  checkAndMeasure();
}, []);
\`\`\`

### 3. 图片等资源未加载完成
\`\`\`javascript
// ❌ 图片可能还在加载，影响容器尺寸
function ImageContainer({ src }) {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const containerRef = useRef();

  useLayoutEffect(() => {
    // 这时图片可能还没加载完成
    if (containerRef.current) {
      setSize({
        width: containerRef.current.offsetWidth,
        height: containerRef.current.offsetHeight
      });
    }
  }, [src]);

  return (
    <div ref={containerRef}>
      <img src={src} alt="" />
    </div>
  );
}

// ✅ 等待图片加载完成
function ImageContainer({ src }) {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const [imageLoaded, setImageLoaded] = useState(false);
  const containerRef = useRef();
  const imageRef = useRef();

  useLayoutEffect(() => {
    if (imageLoaded && containerRef.current) {
      setSize({
        width: containerRef.current.offsetWidth,
        height: containerRef.current.offsetHeight
      });
    }
  }, [imageLoaded]);

  return (
    <div ref={containerRef}>
      <img 
        ref={imageRef}
        src={src} 
        alt={'图片 ' + (index + 1)}
        onLoad={() => setImageLoaded(true)}
        onError={() => setImageLoaded(true)} // 即使出错也要测量
      />
    </div>
  );
}
\`\`\`

### 4. 动画过程中测量
\`\`\`javascript
// ❌ 在动画过程中测量
function AnimatedComponent() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [height, setHeight] = useState(0);
  const contentRef = useRef();

  useLayoutEffect(() => {
    // 动画过程中高度是变化的
    if (contentRef.current) {
      setHeight(contentRef.current.offsetHeight);
    }
  }, [isExpanded]);

  return (
    <div 
      style={{ 
        height: isExpanded ? 'auto' : '0',
        transition: 'height 0.3s',
        overflow: 'hidden'
      }}
    >
      <div ref={contentRef}>内容</div>
    </div>
  );
}

// ✅ 在动画开始前测量
function AnimatedComponent() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [height, setHeight] = useState(0);
  const contentRef = useRef();

  useLayoutEffect(() => {
    if (contentRef.current) {
      // 暂时移除过渡效果进行测量
      const content = contentRef.current;
      const parent = content.parentElement;
      
      parent.style.transition = 'none';
      parent.style.height = 'auto';
      
      // 强制重排后测量
      const measured = content.offsetHeight;
      
      // 恢复过渡效果
      parent.style.height = '0px';
      parent.offsetHeight; // 强制重排
      
      parent.style.transition = 'height 0.3s';
      
      if (isExpanded) {
        parent.style.height = measured + 'px';
      }
      
      setHeight(measured);
    }
  }, [isExpanded]);
}
\`\`\`

### 5. 字体加载影响
\`\`\`javascript
// ✅ 等待字体加载完成
useLayoutEffect(() => {
  const measureAfterFonts = async () => {
    // 等待字体加载
    if (document.fonts && document.fonts.ready) {
      await document.fonts.ready;
    }

    if (ref.current) {
      setDimensions({
        width: ref.current.offsetWidth,
        height: ref.current.offsetHeight
      });
    }
  };

  measureAfterFonts();
}, []);
\`\`\`

### 6. 使用ResizeObserver提高准确性
\`\`\`javascript
// ✅ 使用ResizeObserver获得最准确的测量
function AccurateMeasurement() {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const elementRef = useRef();

  useLayoutEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // 初始测量
    setSize({
      width: element.offsetWidth,
      height: element.offsetHeight
    });

    // 使用ResizeObserver监听后续变化
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setSize({ width, height });
      }
    });

    resizeObserver.observe(element);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <div 
      ref={elementRef}
      style={{ padding: '20px', border: '1px solid #ccc' }}
    >
      准确尺寸: {size.width.toFixed(1)} × {size.height.toFixed(1)}
    </div>
  );
}
\`\`\`

## 调试技巧

### 1. 添加调试信息
\`\`\`javascript
useLayoutEffect(() => {
  const element = ref.current;
  if (!element) {
    console.log('元素不存在');
    return;
  }

  console.log('DOM调试信息:', {
    offsetWidth: element.offsetWidth,
    offsetHeight: element.offsetHeight,
    clientWidth: element.clientWidth,
    clientHeight: element.clientHeight,
    scrollWidth: element.scrollWidth,
    scrollHeight: element.scrollHeight,
    offsetParent: element.offsetParent,
    display: window.getComputedStyle(element).display,
    visibility: window.getComputedStyle(element).visibility
  });
}, []);
\`\`\`

### 2. 延迟测量验证
\`\`\`javascript
useLayoutEffect(() => {
  // 立即测量
  const immediate = ref.current?.offsetWidth || 0;
  
  // 延迟测量对比
  setTimeout(() => {
    const delayed = ref.current?.offsetWidth || 0;
    if (immediate !== delayed) {
      console.warn('测量不一致:', { immediate, delayed });
    }
  }, 100);
}, []);
\`\`\`

## 最佳实践

1. **检查元素状态**：确保元素存在且可见
2. **等待资源加载**：图片、字体等资源影响尺寸
3. **避免动画期间测量**：等待动画完成或暂停动画
4. **使用ResizeObserver**：获得最准确的实时测量
5. **添加错误处理**：测量失败时的降级方案
6. **调试验证**：在不同时机对比测量结果`,
    
    code: `// 完整的准确测量解决方案
import { useState, useRef, useLayoutEffect, useCallback } from 'react';

function AccurateDOM Measurement({ content, images = [] }) {
  const [measurements, setMeasurements] = useState(null);
  const [loadingState, setLoadingState] = useState({
    fonts: false,
    images: false,
    styles: false
  });
  const [error, setError] = useState(null);
  const containerRef = useRef();
  const imageRefs = useRef([]);

  // 检查所有资源是否加载完成
  const checkResourcesLoaded = useCallback(() => {
    return loadingState.fonts && loadingState.images && loadingState.styles;
  }, [loadingState]);

  // 执行准确测量
  const performAccurateMeasurement = useCallback(() => {
    const container = containerRef.current;
    if (!container) {
      setError('容器元素不存在');
      return;
    }

    try {
      // 确保元素可见
      const computed = window.getComputedStyle(container);
      if (computed.display === 'none' || computed.visibility === 'hidden') {
        setError('元素不可见，无法准确测量');
        return;
      }

      // 多种测量方式
      const rect = container.getBoundingClientRect();
      const measurements = {
        // 基本尺寸
        offsetWidth: container.offsetWidth,
        offsetHeight: container.offsetHeight,
        
        // 内容区域尺寸
        clientWidth: container.clientWidth,
        clientHeight: container.clientHeight,
        
        // 滚动尺寸
        scrollWidth: container.scrollWidth,
        scrollHeight: container.scrollHeight,
        
        // 边界框尺寸（更精确）
        boundingWidth: rect.width,
        boundingHeight: rect.height,
        
        // 位置信息
        top: rect.top,
        left: rect.left,
        
        // 样式信息
        computedStyle: {
          padding: computed.padding,
          margin: computed.margin,
          border: computed.border,
          fontSize: computed.fontSize,
          lineHeight: computed.lineHeight
        },
        
        // 测量时间戳
        timestamp: Date.now(),
        
        // 验证信息
        isValid: container.offsetWidth > 0 && container.offsetHeight > 0
      };

      setMeasurements(measurements);
      setError(null);
      
    } catch (err) {
      setError('测量过程出错: ' + err.message);
    }
  }, []);

  // 字体加载检查
  useLayoutEffect(() => {
    const checkFonts = async () => {
      try {
        if (document.fonts && document.fonts.ready) {
          await document.fonts.ready;
        }
        setLoadingState(prev => ({ ...prev, fonts: true }));
      } catch (err) {
        console.warn('字体检查失败:', err);
        setLoadingState(prev => ({ ...prev, fonts: true }));
      }
    };

    checkFonts();
  }, []);

  // 图片加载检查
  useLayoutEffect(() => {
    if (images.length === 0) {
      setLoadingState(prev => ({ ...prev, images: true }));
      return;
    }

    let loadedCount = 0;
    const checkComplete = () => {
      loadedCount++;
      if (loadedCount >= images.length) {
        setLoadingState(prev => ({ ...prev, images: true }));
      }
    };

    imageRefs.current.forEach((img, index) => {
      if (img) {
        if (img.complete) {
          checkComplete();
        } else {
          img.addEventListener('load', checkComplete);
          img.addEventListener('error', checkComplete);
        }
      }
    });

    return () => {
      imageRefs.current.forEach(img => {
        if (img) {
          img.removeEventListener('load', checkComplete);
          img.removeEventListener('error', checkComplete);
        }
      });
    };
  }, [images]);

  // 样式加载检查
  useLayoutEffect(() => {
    // 简单的样式加载检查
    const timer = setTimeout(() => {
      setLoadingState(prev => ({ ...prev, styles: true }));
    }, 50); // 给样式应用留出时间

    return () => clearTimeout(timer);
  }, []);

  // 资源加载完成后进行测量
  useLayoutEffect(() => {
    if (checkResourcesLoaded()) {
      // 小延迟确保所有渲染完成
      const timer = setTimeout(performAccurateMeasurement, 0);
      return () => clearTimeout(timer);
    }
  }, [checkResourcesLoaded, performAccurateMeasurement]);

  // ResizeObserver持续监控
  useLayoutEffect(() => {
    const container = containerRef.current;
    if (!container || !measurements) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setMeasurements(prev => ({
          ...prev,
          observedWidth: width,
          observedHeight: height,
          lastUpdate: Date.now()
        }));
      }
    });

    resizeObserver.observe(container);

    return () => {
      resizeObserver.disconnect();
    };
  }, [measurements]);

  return (
    <div 
      ref={containerRef}
      style={{
        padding: '20px',
        border: '2px solid #007acc',
        borderRadius: '8px',
        background: '#f9f9f9',
        minHeight: '100px'
      }}
    >
      <h3>准确DOM测量示例</h3>
      <p>{content}</p>
      
      {/* 渲染图片 */}
      {images.map((src, index) => (
        <img
          key={index}
          ref={el => imageRefs.current[index] = el}
          src={src}
          alt={'图片 ' + (index + 1)}
          style={{ maxWidth: '100%', height: 'auto', margin: '10px 0' }}
        />
      ))}

      {/* 加载状态 */}
      {!checkResourcesLoaded() && (
        <div style={{ 
          background: '#fff3cd', 
          padding: '10px', 
          borderRadius: '4px',
          margin: '10px 0' 
        }}>
          <strong>资源加载中...</strong>
          <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
            <li>字体: {loadingState.fonts ? '✅' : '⏳'}</li>
            <li>图片: {loadingState.images ? '✅' : '⏳'}</li>
            <li>样式: {loadingState.styles ? '✅' : '⏳'}</li>
          </ul>
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div style={{ 
          background: '#f8d7da', 
          padding: '10px', 
          borderRadius: '4px',
          margin: '10px 0',
          color: '#721c24'
        }}>
          <strong>测量错误:</strong> {error}
        </div>
      )}

      {/* 测量结果 */}
      {measurements && (
        <div style={{ 
          background: '#d4edda', 
          padding: '15px', 
          borderRadius: '4px',
          margin: '15px 0',
          fontSize: '14px'
        }}>
          <strong>准确测量结果:</strong>
          <pre style={{ 
            background: '#f8f9fa', 
            padding: '10px', 
            borderRadius: '4px',
            overflow: 'auto',
            fontSize: '12px'
          }}>
            {JSON.stringify(measurements, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}

export default AccurateDOMMeasurement;`,
    
    tags: ['DOM测量', '准确性', '资源加载', 'ResizeObserver']
  }
];

export default commonQuestions; 