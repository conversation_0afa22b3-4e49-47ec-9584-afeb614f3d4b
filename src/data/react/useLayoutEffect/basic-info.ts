import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "useLayoutEffect是React提供的同步执行副作用Hook，在DOM变更后、浏览器绘制前同步触发。与useEffect不同，它会阻塞浏览器绘制，确保DOM操作在用户看到内容之前完成，主要用于DOM测量、防止视觉闪烁和第三方库集成等需要精确控制时机的场景。",

  introduction: 'useLayoutEffect是React提供的同步执行副作用Hook，在DOM变更后、浏览器绘制前同步触发，是精确控制DOM操作时机的专门工具，适用于DOM测量、防止视觉闪烁和第三方库集成等场景。它在React渲染生命周期中占据独特位置：DOM变更后 → useLayoutEffect → 浏览器绘制 → useEffect，通过同步特性阻塞浏览器绘制，确保操作在用户看到之前完成，保证DOM读取和状态更新的原子性操作。主要应用于DOM测量与布局计算、视觉闪烁消除、第三方库深度集成、性能关键路径优化等场景，是现代React应用中实现高质量用户体验的核心工具。',

  syntax: `useLayoutEffect(effect, dependencies?)

// 完整类型定义
function useLayoutEffect(
  effect: EffectCallback,
  deps?: DependencyList
): void

type EffectCallback = () => (void | (() => void | undefined))
type DependencyList = ReadonlyArray<any>`,

  parameters: [
    {
      name: 'effect',
      type: '() => void | (() => void)',
      required: true,
      description: `副作用函数，在DOM变更后、浏览器绘制前同步执行。

**函数特征**：
- 同步执行，会阻塞浏览器绘制
- 可以安全地读取DOM状态和进行测量
- 可选返回清理函数，用于资源释放

**返回值**：
- 无返回值：仅执行副作用操作
- 返回函数：作为清理函数，在下次effect执行前或组件卸载时调用

**最佳实践**：
- 保持函数简短，避免复杂计算
- 优先进行DOM操作，避免异步调用
- 合理使用清理函数防止内存泄漏`
    },
    {
      name: 'dependencies',
      type: 'any[] | undefined',
      required: false,
      description: `依赖数组，精确控制effect的执行时机。

**三种模式**：
- \`undefined\`：每次渲染都执行（谨慎使用）
- \`[]\`：仅在组件挂载时执行一次
- \`[dep1, dep2]\`：依赖项变化时执行

**依赖项选择原则**：
- 包含effect中使用的所有外部变量
- 使用React官方ESLint规则检查依赖完整性
- 避免频繁变化的依赖（如函数、对象字面量）
- 合理使用useCallback和useMemo稳定依赖

**性能优化技巧**：
- 拆分复杂依赖为多个简单effect
- 使用functional update减少依赖
- 通过ref存储不需要响应变化的值`
    }
  ],

  returnValue: {
    type: 'void',
    description: `useLayoutEffect不返回任何值，这是设计上的考虑：

**设计理念**：
- 专注于副作用操作，不涉及数据返回
- 通过状态更新间接影响组件渲染
- 保持API的简洁性和一致性

**数据获取模式**：
通过useState或useReducer管理测量得到的数据：
\`\`\`javascript
const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

useLayoutEffect(() => {
  if (elementRef.current) {
    setDimensions({
      width: elementRef.current.offsetWidth,
      height: elementRef.current.offsetHeight
    });
  }
}, []);
\`\`\``
  },

  limitations: [
    '🚨 **性能警告**：useLayoutEffect会同步阻塞浏览器绘制，执行时间直接影响页面响应性。单次执行应控制在16ms以内（60fps标准）',
    '📱 **SSR兼容性**：服务端渲染环境没有DOM，useLayoutEffect不会执行且会产生警告。需要使用同构Hook或动态导入解决',
    '🎨 **使用原则**：遵循"最小权力原则"，仅在真正需要同步DOM操作时使用。大多数情况应优先考虑useEffect',
    '🔧 **调试支持**：React DevTools Profiler可以显示Layout Effects的执行时间、依赖变化和性能影响，便于性能分析',
    '⚡ **代码质量**：保持effect函数简短快速，避免复杂计算、异步操作和深层嵌套。使用ESLint规则检查依赖完整性',
    '🔄 **并发兼容**：在React 18并发模式下，useLayoutEffect仍保持同步执行特性，与时间切片和Suspense协调工作',
    '🛡️ **错误处理**：添加边界检查和错误处理，防止DOM操作失败影响应用稳定性',
    '📏 **测量精度**：配合ResizeObserver、IntersectionObserver等现代Web API，获得更精确和高效的DOM监控能力'
  ]
};

export default basicInfo; 