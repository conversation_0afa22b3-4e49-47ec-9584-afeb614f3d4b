import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: '同步执行性能优化',
      description: 'useLayoutEffect会阻塞浏览器绘制，需要谨慎使用并优化执行性能',
      techniques: [
        {
          name: '避免不必要的同步操作',
          description: '只在真正需要同步DOM操作时使用useLayoutEffect，其他情况使用useEffect',
          code: `// ❌ 性能问题 - 不必要的同步操作
function BadComponent({ data }) {
  const [processedData, setProcessedData] = useState([]);
  
  useLayoutEffect(() => {
    // 这个数据处理不需要同步执行
    const processed = data.map(item => ({
      ...item,
      computed: heavyComputation(item)
    }));
    setProcessedData(processed);
  }, [data]);
  
  return <div>{processedData.length} items</div>;
}

// ✅ 性能优化 - 使用useEffect处理非DOM操作
function GoodComponent({ data }) {
  const [processedData, setProcessedData] = useState([]);
  
  useEffect(() => {
    // 数据处理可以异步执行
    const processed = data.map(item => ({
      ...item,
      computed: heavyComputation(item)
    }));
    setProcessedData(processed);
  }, [data]);
  
  return <div>{processedData.length} items</div>;
}

// ✅ 正确使用useLayoutEffect - DOM测量
function MeasureComponent() {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const elementRef = useRef(null);
  
  useLayoutEffect(() => {
    // 这里需要同步执行，避免闪烁
    if (elementRef.current) {
      const { offsetWidth, offsetHeight } = elementRef.current;
      setDimensions({ width: offsetWidth, height: offsetHeight });
    }
  });
  
  return (
    <div ref={elementRef}>
      尺寸: {dimensions.width} x {dimensions.height}
    </div>
  );
}`,
          impact: 'high',
          difficulty: 'easy'
        },
        {
          name: '批量DOM操作优化',
          description: '在useLayoutEffect中批量执行DOM操作，减少重排重绘次数',
          code: `// ❌ 性能问题 - 多次DOM操作导致多次重排
function BadBatchComponent({ items }) {
  const listRef = useRef(null);
  
  useLayoutEffect(() => {
    if (listRef.current) {
      const children = listRef.current.children;
      
      // 每次操作都会触发重排
      for (let i = 0; i < children.length; i++) {
        children[i].style.height = '50px';
        children[i].style.marginBottom = '10px';
        children[i].style.backgroundColor = '#f0f0f0';
      }
    }
  }, [items]);
  
  return (
    <ul ref={listRef}>
      {items.map(item => <li key={item.id}>{item.text}</li>)}
    </ul>
  );
}

// ✅ 性能优化 - 批量DOM操作
function GoodBatchComponent({ items }) {
  const listRef = useRef(null);
  
  useLayoutEffect(() => {
    if (listRef.current) {
      const children = listRef.current.children;
      
      // 使用DocumentFragment批量操作
      const fragment = document.createDocumentFragment();
      const tempContainer = document.createElement('div');
      
      // 批量设置样式，减少重排
      for (let i = 0; i < children.length; i++) {
        const child = children[i];
        // 一次性设置所有样式
        Object.assign(child.style, {
          height: '50px',
          marginBottom: '10px',
          backgroundColor: '#f0f0f0'
        });
      }
    }
  }, [items]);
  
  return (
    <ul ref={listRef}>
      {items.map(item => <li key={item.id}>{item.text}</li>)}
    </ul>
  );
}

// ✅ 更好的优化 - 使用CSS类而不是内联样式
function BestBatchComponent({ items }) {
  const listRef = useRef(null);
  
  useLayoutEffect(() => {
    if (listRef.current) {
      // 只添加CSS类，让浏览器优化样式应用
      listRef.current.classList.add('optimized-list');
    }
  }, [items]);
  
  return (
    <ul ref={listRef}>
      {items.map(item => <li key={item.id}>{item.text}</li>)}
    </ul>
  );
}`,
          impact: 'high',
          difficulty: 'medium'
        }
      ]
    },
    {
      title: 'DOM测量性能优化',
      description: '优化DOM测量操作，避免频繁的布局计算和重排',
      techniques: [
        {
          name: '缓存DOM测量结果',
          description: '缓存DOM测量结果，避免重复计算',
          code: `// ❌ 性能问题 - 频繁的DOM测量
function BadMeasureComponent() {
  const [scrollInfo, setScrollInfo] = useState({});
  const containerRef = useRef(null);
  
  useLayoutEffect(() => {
    const handleScroll = () => {
      if (containerRef.current) {
        // 每次滚动都重新测量，性能差
        const rect = containerRef.current.getBoundingClientRect();
        const scrollTop = containerRef.current.scrollTop;
        const scrollHeight = containerRef.current.scrollHeight;
        const clientHeight = containerRef.current.clientHeight;
        
        setScrollInfo({
          rect,
          scrollTop,
          scrollHeight,
          clientHeight,
          scrollPercentage: (scrollTop / (scrollHeight - clientHeight)) * 100
        });
      }
    };
    
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);
  
  return (
    <div ref={containerRef} style={{ height: '300px', overflow: 'auto' }}>
      {/* 内容 */}
    </div>
  );
}

// ✅ 性能优化 - 缓存和节流
function GoodMeasureComponent() {
  const [scrollInfo, setScrollInfo] = useState({});
  const containerRef = useRef(null);
  const cachedDimensions = useRef({});
  const rafId = useRef(null);
  
  useLayoutEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { scrollHeight, clientHeight } = containerRef.current;
        cachedDimensions.current = { scrollHeight, clientHeight };
      }
    };
    
    const handleScroll = () => {
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
      
      rafId.current = requestAnimationFrame(() => {
        if (containerRef.current) {
          const scrollTop = containerRef.current.scrollTop;
          const { scrollHeight, clientHeight } = cachedDimensions.current;
          
          setScrollInfo({
            scrollTop,
            scrollPercentage: (scrollTop / (scrollHeight - clientHeight)) * 100
          });
        }
      });
    };
    
    const container = containerRef.current;
    if (container) {
      updateDimensions(); // 初始化缓存
      container.addEventListener('scroll', handleScroll, { passive: true });
      
      // 监听尺寸变化
      const resizeObserver = new ResizeObserver(updateDimensions);
      resizeObserver.observe(container);
      
      return () => {
        container.removeEventListener('scroll', handleScroll);
        resizeObserver.disconnect();
        if (rafId.current) {
          cancelAnimationFrame(rafId.current);
        }
      };
    }
  }, []);
  
  return (
    <div ref={containerRef} style={{ height: '300px', overflow: 'auto' }}>
      {/* 内容 */}
    </div>
  );
}`,
          impact: 'high',
          difficulty: 'hard'
        }
      ]
    }
  ],

  performanceMetrics: {
    renderBlockingTime: {
      description: '测量useLayoutEffect阻塞渲染的时间',
      tool: 'Performance API',
      example: 'performance.mark("layout-start"); /* useLayoutEffect code */ performance.mark("layout-end");'
    },
    layoutThrashing: {
      description: '监控布局抖动和重排次数',
      tool: 'Chrome DevTools Performance',
      example: '在Performance面板中查看Layout事件的频率和耗时'
    },
    frameRate: {
      description: '监控帧率，确保useLayoutEffect不影响流畅度',
      tool: 'Chrome DevTools Rendering',
      example: '启用FPS meter监控帧率变化'
    }
  },

  bestPractices: [
    '只在需要同步DOM操作时使用useLayoutEffect',
    '优先考虑useEffect，除非确实需要阻塞渲染',
    '在useLayoutEffect中避免复杂计算和异步操作',
    '使用requestAnimationFrame优化动画相关的DOM操作',
    '批量执行DOM操作，减少重排重绘次数',
    '缓存DOM测量结果，避免重复计算',
    '使用CSS类而不是内联样式进行批量样式更新',
    '监控性能指标，确保不影响用户体验'
  ],

  commonPitfalls: [
    {
      issue: '在useLayoutEffect中执行耗时操作导致页面卡顿',
      cause: 'useLayoutEffect会阻塞浏览器绘制，耗时操作会导致明显的性能问题',
      solution: '将耗时操作移到useEffect中，或使用Web Workers处理'
    },
    {
      issue: '频繁的DOM测量导致性能下降',
      cause: '每次测量都会触发布局计算，频繁测量会影响性能',
      solution: '缓存测量结果，使用ResizeObserver监听尺寸变化'
    },
    {
      issue: '过度使用useLayoutEffect替代useEffect',
      cause: '误认为useLayoutEffect总是更好，导致不必要的性能损失',
      solution: '理解两者的区别，只在需要同步DOM操作时使用useLayoutEffect'
    }
  ]
};

export default performanceOptimization;
