import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'createContext在使用过程中经常会遇到一些典型问题，掌握这些问题的识别和解决方法是高效开发的关键。以下是最常见的createContext相关问题及其解决方案。',
        sections: [
          {
            title: 'Context值为undefined错误',
            description: '最常见的Context使用错误，通常由Provider位置不正确或Context消费位置错误导致',
            items: [
              {
                title: 'useContext返回undefined',
                description: '组件使用useContext时返回undefined，导致运行时错误',
                solution: '确保消费Context的组件在对应的Provider内部，并检查Context的默认值设置',
                prevention: '使用自定义Hook封装Context消费逻辑，提供更好的错误检查',
                code: `// ❌ 错误：组件不在Provider内部
const UserContext = createContext();

function App() {
  return (
    <div>
      <UserProfile /> {/* 错误：不在UserProvider内部 */}
    </div>
  );
}

function UserProfile() {
  const { user } = useContext(UserContext); // undefined!
  return <div>{user.name}</div>; // 运行时错误
}

// ✅ 解决方案1：正确放置Provider
function App() {
  return (
    <UserProvider>
      <UserProfile /> {/* 正确：在Provider内部 */}
    </UserProvider>
  );
}

// ✅ 解决方案2：自定义Hook with 错误检查
function useUser() {
  const context = useContext(UserContext);
  
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  
  return context;
}

// ✅ 解决方案3：提供有意义的默认值
const UserContext = createContext({
  user: null,
  setUser: () => {
    console.warn('setUser called outside UserProvider');
  }
});`
              },
              {
                title: 'Provider嵌套顺序错误',
                description: '多个Provider嵌套时顺序错误，导致Context无法正确访问',
                solution: '确保Provider的嵌套顺序符合依赖关系，子Context依赖的父Context应该在外层',
                prevention: '使用组合Provider模式，统一管理多个Context的嵌套关系',
                code: `// ❌ 错误：依赖顺序不正确
function App() {
  return (
    <UserProvider>
      <ThemeProvider> {/* ThemeProvider依赖UserContext */}
        <Layout />
      </ThemeProvider>
    </UserProvider>
  );
}

function ThemeProvider({ children }) {
  const { user } = useContext(UserContext); // 可能获取不到user
  const defaultTheme = user?.preferences?.theme || 'light';
  // ...
}

// ✅ 解决方案：正确的嵌套顺序
function App() {
  return (
    <UserProvider>
      <ThemeProvider> {/* 现在可以正确访问UserContext */}
        <Layout />
      </ThemeProvider>
    </UserProvider>
  );
}

// ✅ 更好的解决方案：组合Provider
function CombinedProviders({ children }) {
  return (
    <UserProvider>
      <ThemeProvider>
        <NotificationProvider>
          {children}
        </NotificationProvider>
      </ThemeProvider>
    </UserProvider>
  );
}

function App() {
  return (
    <CombinedProviders>
      <Layout />
    </CombinedProviders>
  );
}`
              },
              {
                title: 'Context值更新但组件未重渲染',
                description: 'Context的value已更新，但消费组件没有重新渲染',
                solution: '检查Context value的引用是否发生变化，确保使用正确的状态更新方式',
                prevention: '使用useMemo优化Context value，确保只有必要时才创建新对象',
                code: `// ❌ 错误：value引用未变化
function UserProvider({ children }) {
  const [user, setUser] = useState({ name: 'John' });
  
  const updateName = (newName) => {
    user.name = newName; // 直接修改对象，引用未变化！
    setUser(user); // React认为state没有变化
  };
  
  return (
    <UserContext.Provider value={{ user, updateName }}>
      {children}
    </UserContext.Provider>
  );
}

// ✅ 解决方案1：创建新对象
function UserProvider({ children }) {
  const [user, setUser] = useState({ name: 'John' });
  
  const updateName = (newName) => {
    setUser(prevUser => ({ ...prevUser, name: newName })); // 创建新对象
  };
  
  const value = useMemo(() => ({ user, updateName }), [user]);
  
  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}

// ✅ 解决方案2：使用useCallback优化函数
function UserProvider({ children }) {
  const [user, setUser] = useState({ name: 'John' });
  
  const updateName = useCallback((newName) => {
    setUser(prevUser => ({ ...prevUser, name: newName }));
  }, []);
  
  const value = useMemo(() => ({ user, updateName }), [user, updateName]);
  
  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}`
              }
            ]
          },
          {
            title: 'Context性能问题',
            description: 'Context更新导致的性能问题，包括不必要的重渲染和内存泄漏',
            items: [
              {
                title: '大量不必要的重渲染',
                description: 'Context更新时导致所有消费组件重渲染，即使组件不需要更新',
                solution: '使用React.memo包装组件，分割Context，或使用选择性订阅模式',
                prevention: '合理设计Context粒度，避免将所有状态放在一个Context中',
                code: `// ❌ 问题：所有组件都会重渲染
const AppContext = createContext();

function AppProvider({ children }) {
  const [user, setUser] = useState({});
  const [theme, setTheme] = useState('light');
  const [notifications, setNotifications] = useState([]);
  
  // 任何状态变化都会导致所有消费组件重渲染
  const value = { user, setUser, theme, setTheme, notifications, setNotifications };
  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
}

// ✅ 解决方案：分割Context + React.memo
const UserContext = createContext();
const ThemeContext = createContext();

const UserProfile = React.memo(function UserProfile() {
  const { user } = useContext(UserContext);
  console.log('UserProfile rendered'); // 只有user变化时才会打印
  return <div>{user.name}</div>;
});

const ThemeButton = React.memo(function ThemeButton() {
  const { theme, setTheme } = useContext(ThemeContext);
  console.log('ThemeButton rendered'); // 只有theme变化时才会打印
  return <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>{theme}</button>;
});`
              },
              {
                title: 'Context导致的内存泄漏',
                description: '组件卸载后Context相关的回调或订阅没有正确清理',
                solution: '在useEffect中正确清理订阅，使用useCallback避免创建新函数',
                prevention: '始终在useEffect的返回函数中清理资源，使用AbortController取消异步操作',
                code: `// ❌ 问题：未清理的订阅导致内存泄漏
function NotificationProvider({ children }) {
  const [notifications, setNotifications] = useState([]);
  
  useEffect(() => {
    const interval = setInterval(() => {
      // 定期检查新通知
      fetchNotifications().then(setNotifications);
    }, 5000);
    
    // 忘记清理定时器！
  }, []);
  
  return (
    <NotificationContext.Provider value={{ notifications }}>
      {children}
    </NotificationContext.Provider>
  );
}

// ✅ 解决方案：正确清理资源
function NotificationProvider({ children }) {
  const [notifications, setNotifications] = useState([]);
  
  useEffect(() => {
    const controller = new AbortController();
    
    const interval = setInterval(async () => {
      try {
        const newNotifications = await fetchNotifications({
          signal: controller.signal
        });
        setNotifications(newNotifications);
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Failed to fetch notifications:', error);
        }
      }
    }, 5000);
    
    // 正确清理资源
    return () => {
      clearInterval(interval);
      controller.abort();
    };
  }, []);
  
  const value = useMemo(() => ({ notifications }), [notifications]);
  
  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '掌握正确的调试工具和技巧能够大幅提升Context相关问题的诊断和解决效率。以下是最有效的createContext调试方法和工具使用指南。',
        sections: [
          {
            title: 'React DevTools调试技巧',
            description: '使用React DevTools深入分析Context的状态变化和组件渲染情况',
            items: [
              {
                title: '查看Context Provider和Consumer',
                description: '在React DevTools中识别和检查Context的Provider和Consumer组件',
                solution: '在Components面板中查找Context.Provider，检查其props中的value属性',
                prevention: '为Context设置displayName，便于在DevTools中识别',
                code: `// ✅ 设置Context的displayName便于调试
const UserContext = createContext();
UserContext.displayName = 'UserContext';

const ThemeContext = createContext();
ThemeContext.displayName = 'ThemeContext';

// 在DevTools中的显示效果：
// 📁 UserContext.Provider
//   📦 value: { user: {...}, setUser: f }
//   📁 Component
//     📁 ThemeContext.Provider
//       📦 value: { theme: "light", setTheme: f }

// ✅ 调试Context值变化
function UserProvider({ children }) {
  const [user, setUser] = useState({ name: 'John' });
  
  // 添加调试信息
  const value = useMemo(() => {
    const contextValue = { user, setUser };
    console.log('UserContext value updated:', contextValue);
    return contextValue;
  }, [user]);
  
  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}`
              },
              {
                title: '性能分析和重渲染追踪',
                description: '使用DevTools Profiler分析Context导致的性能问题',
                solution: '在Profiler面板中记录组件渲染，查看Context更新导致的渲染瀑布',
                prevention: '定期使用Profiler检查应用性能，识别不必要的重渲染',
                code: `// ✅ 启用Profiler分析Context性能
import { Profiler } from 'react';

function App() {
  const onRenderCallback = (id, phase, actualDuration, baseDuration) => {
    console.log('Profiler数据:', {
      组件ID: id,
      渲染阶段: phase, // "mount" | "update"
      实际渲染时间: actualDuration,
      基线渲染时间: baseDuration
    });
    
    // 警告：渲染时间过长
    if (actualDuration > 16) {
      console.warn(\`组件 \${id} 渲染时间过长: \${actualDuration}ms\`);
    }
  };
  
  return (
    <Profiler id="AppWithContext" onRender={onRenderCallback}>
      <UserProvider>
        <ThemeProvider>
          <Layout />
        </ThemeProvider>
      </UserProvider>
    </Profiler>
  );
}

// ✅ 组件级别的渲染追踪
function useRenderTracker(componentName) {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());
  
  useEffect(() => {
    renderCount.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    
    console.log(\`\${componentName} 第\${renderCount.current}次渲染，距上次: \${timeSinceLastRender}ms\`);
    
    lastRenderTime.current = now;
  });
}

function UserProfile() {
  useRenderTracker('UserProfile');
  const { user } = useContext(UserContext);
  return <div>{user.name}</div>;
}`
              },
              {
                title: 'Context调试工具和技巧',
                description: '自定义调试工具和实用技巧，帮助快速定位Context相关问题',
                solution: '创建Context调试工具，监控Context状态变化和使用情况',
                prevention: '在开发环境中启用详细的Context调试信息',
                code: `// ✅ Context调试工具
function createDebuggableContext(name, defaultValue) {
  const Context = createContext(defaultValue);
  Context.displayName = name;
  
  // 在开发环境中包装Provider
  const OriginalProvider = Context.Provider;
  
  if (process.env.NODE_ENV === 'development') {
    Context.Provider = function DebuggableProvider({ value, children }) {
      const prevValue = useRef(value);
      
      useEffect(() => {
        if (prevValue.current !== value) {
          console.group(\`📊 \${name} Context Updated\`);
          console.log('Previous value:', prevValue.current);
          console.log('New value:', value);
          console.log('Changed at:', new Date().toISOString());
          console.groupEnd();
          
          prevValue.current = value;
        }
      });
      
      return React.createElement(OriginalProvider, { value }, children);
    };
  }
  
  return Context;
}

// 使用调试Context
const UserContext = createDebuggableContext('User', null);

// ✅ Context使用情况统计
function useContextStats() {
  const stats = useRef(new Map());
  
  const trackContextUsage = (contextName) => {
    const current = stats.current.get(contextName) || 0;
    stats.current.set(contextName, current + 1);
  };
  
  const getStats = () => {
    console.table(Object.fromEntries(stats.current));
  };
  
  // 在控制台暴露统计函数
  useEffect(() => {
    window.getContextStats = getStats;
  }, []);
  
  return { trackContextUsage };
}

// ✅ Context错误边界
class ContextErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Context Error:', error);
    console.error('Component Stack:', errorInfo.componentStack);
    
    // 检查是否是Context相关错误
    if (error.message.includes('Context') || error.message.includes('Provider')) {
      console.error('🚨 这可能是Context相关的错误，请检查:');
      console.error('1. 组件是否在正确的Provider内部');
      console.error('2. Context的默认值是否正确设置');
      console.error('3. Provider的value是否正确传递');
    }
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', border: '1px solid red' }}>
          <h2>Context Error</h2>
          <p>{this.state.error?.message}</p>
          <button onClick={() => this.setState({ hasError: false, error: null })}>
            重试
          </button>
        </div>
      );
    }
    
    return this.props.children;
  }
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

// createContext调试技巧内容已完成
export default debuggingTips;