import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'question-1',
    question: '什么时候应该使用createContext而不是props或状态管理库？',
    answer: `选择Context的关键标准是**状态的共享范围**和**复杂度**：

**使用Context的场景：**
- 需要跨多层组件访问的状态（避免prop drilling）
- 相对稳定、不频繁变化的全局配置（主题、用户认证、多语言）
- 中等复杂度的状态管理需求
- 组件库中需要为组件提供配置的场景

**不适合Context的场景：**
- 简单的父子组件通信 → 使用props
- 频繁变化的状态（如输入框值、滚动位置） → 使用本地state
- 复杂的状态逻辑（需要时间旅行、中间件） → 使用Redux/Zustand
- 高性能要求的频繁更新 → 使用专门的状态管理库

**决策指导：**
1. **传递层级** > 3层 → 考虑Context
2. **更新频率** < 10次/秒 → 适合Context
3. **状态复杂度**：简单对象 → Context；复杂逻辑 → Redux`,
    code: `// ✅ 适合Context：主题配置（稳定、跨层级）
const ThemeContext = createContext();

function App() {
  return (
    <ThemeProvider>
      <Layout>
        <Sidebar>
          <UserProfile /> {/* 需要主题信息，但距离Provider很远 */}
        </Sidebar>
      </Layout>
    </ThemeProvider>
  );
}

// ❌ 不适合Context：频繁变化的搜索输入
function SearchProvider({ children }) {
  const [query, setQuery] = useState(''); // 每次输入都会导致所有消费组件重渲染
  return (
    <SearchContext.Provider value={{ query, setQuery }}>
      {children}
    </SearchContext.Provider>
  );
}

// ✅ 更好的方案：本地状态 + 防抖
function SearchInput() {
  const [localQuery, setLocalQuery] = useState('');
  const { setGlobalQuery } = useContext(AppContext);
  
  const debouncedSearch = useMemo(
    () => debounce(setGlobalQuery, 300),
    [setGlobalQuery]
  );
  
  useEffect(() => {
    debouncedSearch(localQuery);
  }, [localQuery, debouncedSearch]);
  
  return <input value={localQuery} onChange={e => setLocalQuery(e.target.value)} />;
}`,
    tags: ['状态管理选择', '架构决策', 'Context适用场景', 'prop drilling'],
    relatedQuestions: ['Context vs Redux的详细对比？', '如何避免Context性能问题？']
  },
  {
    id: 'question-2',
    question: 'Context更新时导致所有组件重渲染，如何优化性能？',
    answer: `Context性能问题的根本原因是**所有消费组件会在Context值变化时重渲染**。主要优化策略：

**1. 分割Context（最重要）**
将不同类型的状态分别创建Context，避免"巨型Context"。按更新频率和使用范围分割。

**2. 使用useMemo优化value**
Provider的value属性应该用useMemo包装，避免每次渲染创建新对象。

**3. React.memo包装消费组件**
用React.memo包装Context消费组件，只有相关数据变化时才重渲染。

**4. 自定义Hook优化**
创建细粒度的自定义Hook，组件只消费需要的数据片段。

**5. 状态结构优化**
将频繁变化和稳定的状态分开存储，减少不必要的更新传播。`,
    code: `// ❌ 性能问题：巨型Context + 对象字面量
function AppProvider({ children }) {
  const [user, setUser] = useState({});
  const [theme, setTheme] = useState('light');
  const [notifications, setNotifications] = useState([]);
  
  // 每次渲染都创建新对象，导致所有消费组件重渲染
  return (
    <AppContext.Provider value={{ user, setUser, theme, setTheme, notifications, setNotifications }}>
      {children}
    </AppContext.Provider>
  );
}

// ✅ 解决方案1：分割Context
const UserContext = createContext();
const ThemeContext = createContext();
const NotificationContext = createContext();

function UserProvider({ children }) {
  const [user, setUser] = useState({});
  
  // 使用useMemo优化
  const value = useMemo(() => ({ user, setUser }), [user]);
  
  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}

// ✅ 解决方案2：React.memo + 选择性消费
const UserProfile = React.memo(function UserProfile() {
  const { user } = useContext(UserContext); // 只消费UserContext
  return <div>{user.name}</div>;
});

const ThemeButton = React.memo(function ThemeButton() {
  const { theme, setTheme } = useContext(ThemeContext); // 只消费ThemeContext
  return <button onClick={() => setTheme(t => t === 'light' ? 'dark' : 'light')}>{theme}</button>;
});

// ✅ 解决方案3：细粒度Hook
function useUserName() {
  const { user } = useContext(UserContext);
  return user.name; // 只返回需要的部分
}

function UserGreeting() {
  const userName = useUserName(); // 只关心用户名
  return <div>Hello, {userName}!</div>;
}`,
    tags: ['性能优化', 'Context分割', 'React.memo', 'useMemo', '重渲染优化'],
    relatedQuestions: ['如何监控Context性能问题？', '什么是Context的最佳粒度？']
  },
  {
    id: 'question-3',
    question: '如何正确组织和管理多个Context？避免Provider地狱？',
    answer: `管理多个Context的关键是**合理的组织结构**和**清晰的依赖关系**：

**1. 按功能域分组**
将相关的Context按功能域分组，如认证、主题、国际化等。

**2. 建立依赖层次**
有依赖关系的Context要按正确顺序嵌套，依赖者在内层。

**3. 组合Provider模式**
创建组合Provider组件，统一管理多个Context的嵌套。

**4. Context工厂模式**
使用工厂函数创建标准化的Context，提供一致的API。

**5. 条件Provider**
只在需要时才渲染Provider，避免不必要的Context层级。

**避免Provider地狱的模式：**
- 使用组合组件减少嵌套层级
- 创建Context组合工具
- 按页面或功能模块组织Provider`,
    code: `// ❌ Provider地狱
function App() {
  return (
    <UserProvider>
      <ThemeProvider>
        <LanguageProvider>
          <NotificationProvider>
            <AuthProvider>
              <RouterProvider>
                <QueryProvider>
                  <Layout />
                </QueryProvider>
              </RouterProvider>
            </AuthProvider>
          </NotificationProvider>
        </LanguageProvider>
      </ThemeProvider>
    </UserProvider>
  );
}

// ✅ 解决方案1：组合Provider
function CoreProviders({ children }) {
  return (
    <UserProvider>
      <ThemeProvider>
        <LanguageProvider>
          {children}
        </LanguageProvider>
      </ThemeProvider>
    </UserProvider>
  );
}

function FeatureProviders({ children }) {
  return (
    <NotificationProvider>
      <AuthProvider>
        {children}
      </AuthProvider>
    </NotificationProvider>
  );
}

function App() {
  return (
    <CoreProviders>
      <FeatureProviders>
        <RouterProvider>
          <Layout />
        </RouterProvider>
      </FeatureProviders>
    </CoreProviders>
  );
}

// ✅ 解决方案2：Provider工厂
function createContextProvider(name, defaultValue) {
  const Context = createContext(defaultValue);
  Context.displayName = name;
  
  function Provider({ children, value }) {
    const memoizedValue = useMemo(() => value, [value]);
    return (
      <Context.Provider value={memoizedValue}>
        {children}
      </Context.Provider>
    );
  }
  
  function useContext() {
    const context = useContext(Context);
    if (context === undefined) {
      throw new Error(\`use\${name} must be used within \${name}Provider\`);
    }
    return context;
  }
  
  return { Provider, useContext };
}

// 使用工厂创建标准化Context
const { Provider: UserProvider, useContext: useUser } = createContextProvider('User', null);
const { Provider: ThemeProvider, useContext: useTheme } = createContextProvider('Theme', 'light');

// ✅ 解决方案3：条件Provider + 配置驱动
const providerConfig = [
  { component: UserProvider, condition: true },
  { component: ThemeProvider, condition: true },
  { component: AuthProvider, condition: (props) => props.requireAuth },
  { component: AdminProvider, condition: (props) => props.isAdmin }
];

function ConfigurableProviders({ children, ...props }) {
  return providerConfig.reduce((acc, { component: Component, condition }) => {
    const shouldRender = typeof condition === 'function' ? condition(props) : condition;
    return shouldRender ? <Component>{acc}</Component> : acc;
  }, children);
}

// 使用
function App() {
  return (
    <ConfigurableProviders requireAuth={true} isAdmin={false}>
      <Layout />
    </ConfigurableProviders>
  );
}`,
    tags: ['Context组织', 'Provider模式', '代码组织', '依赖管理', '架构设计'],
    relatedQuestions: ['如何设计可扩展的Context架构？', '多个Context之间如何通信？']
  }
];

// createContext常见问题已完成
export default commonQuestions;