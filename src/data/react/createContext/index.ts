import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const createContextData: ApiItem = {
  id: 'createContext',
  title: 'React.createContext',
  description: 'React.createContext是React中用于创建Context对象的API，实现跨层级组件状态共享，避免prop drilling问题',
  category: 'React APIs',
  difficulty: 'medium',
  
  syntax: `const MyContext = React.createContext(defaultValue);

// TypeScript完整接口
function createContext<T>(
  defaultValue: T
): React.Context<T>;

// 使用Provider提供值
<MyContext.Provider value={contextValue}>
  {children}
</MyContext.Provider>

// 使用Consumer消费值  
<MyContext.Consumer>
  {value => /* render something based on the context value */}
</MyContext.Consumer>`,
  example: `// 创建主题Context
const ThemeContext = React.createContext('light');

function App() {
  const [theme, setTheme] = useState('dark');
  
  return (
    <ThemeContext.Provider value={theme}>
      <div>
        <h1>Context示例应用</h1>
        <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
          切换主题
        </button>
        <Content />
      </div>
    </ThemeContext.Provider>
  );
}

function Content() {
  // 使用useContext Hook消费Context
  const theme = useContext(ThemeContext);
  
  return (
    <div style={{ background: theme === 'dark' ? '#333' : '#fff' }}>
      当前主题: {theme}
    </div>
  );
}`,
  notes: '默认值只在没有匹配的Provider时使用，通常用于测试或错误处理',
  
  version: 'React 16.3.0+',
  tags: ["React", "API", "Context", "状态共享", "跨层级通信"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default createContextData;