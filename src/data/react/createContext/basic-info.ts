import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "React.createContext是React中用于创建Context对象的API，实现跨层级组件状态共享，避免prop drilling问题",

  introduction: `React.createContext是React 16.3版本引入的核心API，用于创建Context对象来实现跨层级的状态共享。它解决了在组件树中深层传递props的"prop drilling"问题，提供了一种优雅的全局状态管理方案。

Context是React中实现依赖注入模式的基础设施，广泛应用于主题切换、用户认证、国际化、全局状态管理等场景。它与Provider/Consumer模式配合使用，形成了React生态中状态共享的标准解决方案。

在现代React开发中，createContext通常与useContext Hook配合使用，提供了比旧版Consumer更简洁的API。它也是许多状态管理库（如Context API状态管理、React Query等）的底层实现基础。`,

  syntax: `/**
 * 类型定义位置：
 * - 主要类型：@types/react/index.d.ts:398
 * - 实现文件：packages/react/src/ReactContext.js
 */

// 基础语法
const MyContext = React.createContext(defaultValue);

// TypeScript泛型语法
const MyContext = React.createContext<ContextType>(defaultValue);

// 完整接口定义
interface Context<T> {
  Provider: React.Provider<T>;
  Consumer: React.Consumer<T>;
  displayName?: string;
}

function createContext<T>(defaultValue: T): Context<T>;

// 使用模式
<MyContext.Provider value={contextValue}>
  {children}
</MyContext.Provider>

// Hook消费模式（推荐）
const value = useContext(MyContext);

// Consumer消费模式（传统）
<MyContext.Consumer>
  {value => <div>{value}</div>}
</MyContext.Consumer>`,

  quickExample: `// 创建用户认证Context
const AuthContext = React.createContext(null);

function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  const login = async (credentials) => {
    setLoading(true);
    try {
      const userData = await authService.login(credentials);
      setUser(userData);
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    authService.logout();
  };

  const value = {
    user,
    loading,
    login,
    logout,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// 在组件中使用
function UserProfile() {
  const { user, logout, isAuthenticated } = useContext(AuthContext);

  if (!isAuthenticated) {
    return <div>请先登录</div>;
  }

  return (
    <div>
      <h2>欢迎，{user.name}</h2>
      <button onClick={logout}>退出登录</button>
    </div>
  );
}`,

  scenarioDiagram: [
    {
      title: "Context创建与使用流程",
      description: "展示从createContext创建到Provider/Consumer使用的完整流程",
      diagram: `graph TB
        A[React.createContext] --> B[创建Context对象]
        B --> C[Context.Provider]
        B --> D[Context.Consumer]
        B --> E[useContext Hook]

        C --> F[包装组件树]
        F --> G[提供value属性]
        G --> H[子组件可访问]

        H --> I[使用useContext]
        H --> J[使用Consumer]

        I --> K[直接获取value]
        J --> L[render props模式]

        style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
        style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
        style G fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
        style K fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "Context值传递机制",
      description: "Context值如何从Provider传递到深层子组件的机制图",
      diagram: `graph TD
        A[App Component] --> B[Context.Provider]
        B --> C[value: authData]
        
        C --> D[Router]
        D --> E[Layout]
        E --> F[Header]
        E --> G[Main]
        
        G --> H[UserProfile]
        G --> I[Settings]
        
        F --> J[useContext获取user]
        H --> K[useContext获取user]
        I --> L[useContext获取user]

        C -.-> J
        C -.-> K
        C -.-> L

        style B fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
        style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
        style J fill:#fff3e0,stroke:#f57c00,stroke-width:2px
        style K fill:#fff3e0,stroke:#f57c00,stroke-width:2px
        style L fill:#fff3e0,stroke:#f57c00,stroke-width:2px`
    },
    {
      title: "多Context嵌套结构",
      description: "多个Context Provider嵌套使用，实现不同层级的状态管理",
      diagram: `graph TB
        A[App] --> B[ThemeProvider]
        B --> C[AuthProvider]
        C --> D[LanguageProvider]
        D --> E[Router]

        E --> F[Page Component]
        F --> G[useContext Theme]
        F --> H[useContext Auth]
        F --> I[useContext Language]

        B -.-> G
        C -.-> H 
        D -.-> I

        style B fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
        style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
        style D fill:#fff3e0,stroke:#f57c00,stroke-width:2px
        style F fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px`
    }
  ],
  
  parameters: [
    {
      name: "defaultValue",
      type: "T",
      required: true,
      description: "Context的默认值，当组件不在任何Provider内时使用。该值不会因Provider的value变化而改变",
      example: "null, {}, 'light', { user: null, loading: false }"
    }
  ],
  
  returnValue: {
    type: "React.Context<T>",
    description: "返回一个Context对象，包含Provider和Consumer组件，以及displayName属性（用于React DevTools）",
    example: "{ Provider: React.Provider<T>, Consumer: React.Consumer<T>, displayName?: string }"
  },
  
  keyFeatures: [
    {
      title: "跨层级状态共享",
      description: "无需逐层传递props，可直接在深层组件中访问状态，解决prop drilling问题",
      benefit: "大幅简化组件间的数据传递，提高代码可维护性"
    },
    {
      title: "类型安全支持", 
      description: "完整的TypeScript泛型支持，提供编译时类型检查和智能提示",
      benefit: "减少运行时错误，提升开发体验和代码质量"
    },
    {
      title: "Provider/Consumer模式",
      description: "基于发布订阅模式，Provider提供数据，Consumer或useContext消费数据",
      benefit: "清晰的数据流向，易于理解和调试"
    },
    {
      title: "React DevTools集成",
      description: "在React DevTools中显示Context的值变化，支持displayName自定义名称",
      benefit: "强大的调试能力，可视化状态变化"
    }
  ],
  
  limitations: [
    "Context值变化会导致所有消费组件重新渲染，可能影响性能",
    "过度使用Context会增加组件间的耦合度，难以测试",
    "Context不适合频繁变化的状态，会导致大量不必要的渲染",
    "嵌套过多Provider会增加组件树的复杂度",
    "在服务端渲染时需要确保Context值的一致性"
  ],
  
  bestPractices: [
    "为不同类型的状态创建单独的Context，避免单一巨大Context",
    "使用自定义Hook封装Context消费逻辑，提供更简洁的API",
    "为Context提供有意义的displayName，便于调试",
    "使用useMemo优化Context value，避免不必要的重新渲染",
    "在Provider中使用useState或useReducer管理复杂状态",
    "为Context提供默认值和错误边界处理",
    "使用组合模式将多个相关的Context组合在一起",
    "考虑使用状态管理库处理复杂的全局状态"
  ],
  
  warnings: [
    "避免在Context中存储频繁变化的状态，会导致性能问题",
    "不要在Context value中直接传递对象字面量，会导致无限重新渲染",
    "确保在Provider外使用useContext时有适当的错误处理",
    "避免创建过多的Context，会增加应用的复杂度",
    "注意Context的作用域，确保Consumer在对应的Provider内部"
  ]
};

// createContext基本信息已完成
export default basicInfo;