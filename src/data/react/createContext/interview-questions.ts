import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: '请解释React.createContext的作用，并说明如何使用它来避免prop drilling问题？',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'createContext用于创建Context对象，通过Provider/Consumer模式实现跨层级组件状态共享，避免逐层传递props的prop drilling问题。',
      detailed: `React.createContext是React 16.3引入的API，主要解决组件间状态传递的问题：

**核心作用：**
1. **跨层级数据传递**：避免通过props逐层传递数据
2. **全局状态管理**：为应用提供全局可访问的状态
3. **组件解耦**：减少组件间的props依赖关系

**使用模式：**
1. 使用createContext创建Context对象
2. 用Provider组件包装需要共享状态的组件树
3. 在子组件中通过useContext或Consumer消费状态

**解决prop drilling的原理：**
- 传统方式：数据需要通过每一层组件的props向下传递
- Context方式：数据通过Context"隧道"直接传递给需要的组件
- 中间层组件无需关心和传递这些数据

**适用场景：**
- 主题切换、用户认证、多语言、全局配置等`,
      code: `// 1. 创建Context
const UserContext = createContext(null);

// 2. 创建Provider
function UserProvider({ children }) {
  const [user, setUser] = useState({ name: 'John', role: 'admin' });
  
  return (
    <UserContext.Provider value={{ user, setUser }}>
      {children}
    </UserContext.Provider>
  );
}

// 3. 在深层组件中使用
function DeepChild() {
  const { user } = useContext(UserContext);
  return <div>Hello, {user.name}!</div>;
}

// 4. 应用结构
function App() {
  return (
    <UserProvider>
      <Layout>
        <Content>
          <DeepChild /> {/* 无需props传递 */}
        </Content>
      </Layout>
    </UserProvider>
  );
}`
    },
    tags: ['Context API', 'prop drilling', '状态管理', 'Provider/Consumer']
  },
  {
    id: 2,
    question: 'Context更新时会导致所有消费组件重新渲染，这可能造成性能问题。请说明几种优化Context性能的方法？',
    difficulty: 'medium',
    frequency: 'high',
    category: '性能优化',
    answer: {
      brief: '主要通过分割Context、使用useMemo优化value、React.memo包装组件、以及合理的Context结构设计来优化性能。',
      detailed: `Context性能优化是高频面试题，需要理解Context更新机制和优化策略：

**性能问题原因：**
1. Context value变化时，所有消费该Context的组件都会重新渲染
2. 即使组件只使用Context中的部分数据，整个组件仍会重渲染
3. Provider的value如果是对象字面量，每次渲染都会创建新对象

**优化策略：**

**1. 分割Context**
- 将不同类型的状态分别创建Context
- 避免将所有状态放在一个大Context中
- 按更新频率和使用范围分割

**2. 使用useMemo优化value**
- 用useMemo包装Context的value对象
- 避免每次渲染创建新的value对象

**3. 组件层面优化**
- 使用React.memo包装消费组件
- 自定义比较函数优化渲染条件

**4. Context结构设计**
- 将频繁变化的状态与稳定状态分开
- 使用状态提升将计算逻辑上移到Provider

**5. 选择性订阅**
- 创建多个小的Context而非一个大Context
- 组件只订阅需要的Context`,
      code: `// ❌ 性能问题示例
function BadProvider({ children }) {
  const [user, setUser] = useState({});
  const [theme, setTheme] = useState('light');
  
  // 每次渲染都创建新对象！
  return (
    <AppContext.Provider value={{ user, setUser, theme, setTheme }}>
      {children}
    </AppContext.Provider>
  );
}

// ✅ 优化方案1：分割Context
const UserContext = createContext();
const ThemeContext = createContext();

function OptimizedProviders({ children }) {
  const [user, setUser] = useState({});
  const [theme, setTheme] = useState('light');
  
  // 使用useMemo优化
  const userValue = useMemo(() => ({ user, setUser }), [user]);
  const themeValue = useMemo(() => ({ theme, setTheme }), [theme]);
  
  return (
    <UserContext.Provider value={userValue}>
      <ThemeContext.Provider value={themeValue}>
        {children}
      </ThemeContext.Provider>
    </UserContext.Provider>
  );
}

// ✅ 优化方案2：React.memo包装组件
const UserProfile = React.memo(function UserProfile() {
  const { user } = useContext(UserContext);
  // 只有user变化时才重渲染
  return <div>{user.name}</div>;
});

// ✅ 优化方案3：选择性订阅
function useUser() {
  const context = useContext(UserContext);
  if (!context) throw new Error('useUser must be used within UserProvider');
  return context;
}

function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) throw new Error('useTheme must be used within ThemeProvider');
  return context;
}`
    },
    tags: ['性能优化', 'useMemo', 'React.memo', '分割Context']
  },
  {
    id: 3,
    question: '在什么情况下应该使用Context而不是props或状态管理库（如Redux）？请分析Context、Props和Redux的使用场景和权衡。',
    difficulty: 'hard',
    frequency: 'medium',
    category: '架构设计',
    answer: {
      brief: 'Context适合中等复杂度的状态共享，Props适合简单的父子传递，Redux适合复杂的状态管理。选择依据状态复杂度、共享范围、性能要求和团队偏好。',
      detailed: `这是考察架构设计能力的高级问题，需要深入理解不同状态管理方案的适用场景：

**Context适用场景：**
1. **中等复杂度的全局状态**：主题、用户认证、多语言等
2. **组件库开发**：为组件提供配置和样式
3. **避免prop drilling**：深层组件需要访问上层状态
4. **相对稳定的状态**：不频繁变化的配置类数据

**Props适用场景：**
1. **父子组件通信**：直接的数据传递
2. **组件配置**：传递组件的配置参数
3. **简单的状态传递**：层级不深的数据传递
4. **类型安全要求高**：编译时类型检查

**Redux适用场景：**
1. **复杂的状态逻辑**：需要时间旅行、状态持久化
2. **频繁的状态更新**：大量异步操作和状态变化
3. **严格的状态管理**：需要可预测的状态更新
4. **大型团队协作**：统一的状态管理规范

**选择标准：**

**状态复杂度：**
- 简单：Props
- 中等：Context
- 复杂：Redux/Zustand

**性能要求：**
- 高频更新：避免Context，使用Redux或本地state
- 低频更新：Context适合
- 细粒度控制：Redux + Reselect

**团队和项目因素：**
- 学习曲线：Props < Context < Redux
- 代码量：Props < Context < Redux
- 调试能力：Redux > Context > Props
- 类型安全：Props > Context ≈ Redux`,
      code: `// 决策树示例
function chooseStateManagement() {
  // 1. 状态只在父子组件间传递？
  if (isParentChildCommunication) {
    return 'Props';
  }
  
  // 2. 状态需要跨多层组件访问？
  if (needsCrossComponentAccess) {
    // 3. 状态更新频繁且需要性能优化？
    if (isHighFrequencyUpdates) {
      return 'Redux + Reselect';
    }
    
    // 4. 状态逻辑复杂，需要中间件？
    if (hasComplexLogic) {
      return 'Redux + Saga/Thunk';
    }
    
    // 5. 中等复杂度，相对稳定的状态？
    if (isModerateComplexity) {
      return 'Context';
    }
  }
  
  return 'Local State (useState)';
}

// 实际应用示例
// ✅ 使用Props：组件配置
function Button({ variant, size, onClick, children }) {
  return <button className={getButtonClass(variant, size)} onClick={onClick}>{children}</button>;
}

// ✅ 使用Context：主题配置
const ThemeContext = createContext();
function App() {
  return (
    <ThemeProvider theme={darkTheme}>
      <Layout /> {/* 深层组件可直接访问主题 */}
    </ThemeProvider>
  );
}

// ✅ 使用Redux：复杂业务状态
const store = configureStore({
  reducer: {
    user: userReducer,      // 用户状态
    orders: ordersReducer,  // 订单状态  
    cart: cartReducer,      // 购物车状态
    products: productsReducer // 商品状态
  }
});

// 选择建议总结
const guidelines = {
  props: '简单的父子通信，类型安全重要',
  context: '中等复杂度的全局状态，相对稳定',
  redux: '复杂的状态逻辑，需要时间旅行和中间件',
  hybrid: '大型应用通常混合使用：Props处理组件配置，Context处理主题/认证，Redux处理业务状态'
};`
    },
    tags: ['架构设计', 'Context vs Redux', '状态管理', '权衡分析']
  }
];

// createContext面试问题已完成
export default interviewQuestions;