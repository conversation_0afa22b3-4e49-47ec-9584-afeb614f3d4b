import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      strategy: "shouldComponentUpdate优化",
      description: "通过实现shouldComponentUpdate方法，避免不必要的重新渲染",
      implementation: `class OptimizedComponent extends React.Component {
  shouldComponentUpdate(nextProps, nextState) {
    // 浅比较props
    if (this.props.id !== nextProps.id) return true;
    if (this.props.name !== nextProps.name) return true;

    // 浅比较state
    if (this.state.count !== nextState.count) return true;

    return false; // 没有变化，跳过渲染
  }

  render() {
    return <div>{this.props.name}: {this.state.count}</div>;
  }
}`,
      impact: "可以减少50-80%的不必要渲染，显著提升性能"
    },
    {
      strategy: "PureComponent使用",
      description: "使用PureComponent自动实现浅比较优化",
      implementation: `// 自动浅比较的组件
class PureCounter extends PureComponent {
  render() {
    const { count, name } = this.props;
    return <div>{name}: {count}</div>;
  }
}

// 等效的手动实现
class ManualPureComponent extends React.Component {
  shouldComponentUpdate(nextProps, nextState) {
    return !shallowEqual(this.props, nextProps) ||
           !shallowEqual(this.state, nextState);
  }

  render() {
    const { count, name } = this.props;
    return <div>{name}: {count}</div>;
  }
}`,
      impact: "零配置的性能优化，适合大多数场景"
    },
    {
      strategy: "事件处理器优化",
      description: "避免在render中创建新函数，减少子组件不必要的重新渲染",
      implementation: `class OptimizedEvents extends React.Component {
  constructor(props) {
    super(props);
    this.state = { items: [] };

    // 在构造函数中绑定，避免每次渲染创建新函数
    this.handleAddItem = this.handleAddItem.bind(this);
  }

  // 或使用箭头函数自动绑定
  handleRemoveItem = (id) => {
    this.setState(prev => ({
      items: prev.items.filter(item => item.id !== id)
    }));
  }

  handleAddItem() {
    const newItem = { id: Date.now(), name: 'New Item' };
    this.setState(prev => ({ items: [...prev.items, newItem] }));
  }

  render() {
    return (
      <div>
        <button onClick={this.handleAddItem}>添加</button>
        {this.state.items.map(item => (
          <ItemComponent
            key={item.id}
            item={item}
            onRemove={this.handleRemoveItem}
          />
        ))}
      </div>
    );
  }
}`,
      impact: "减少子组件重新渲染，提升列表性能"
    },
    {
      strategy: "状态结构优化",
      description: "合理设计state结构，减少不必要的状态更新",
      implementation: `// ❌ 不好的状态设计
class BadStateDesign extends React.Component {
  state = {
    user: {
      profile: { name: '', age: 0 },
      settings: { theme: 'light', notifications: true },
      posts: []
    }
  };

  updateUserName = (name) => {
    // 整个user对象都会被认为发生了变化
    this.setState({
      user: {
        ...this.state.user,
        profile: {
          ...this.state.user.profile,
          name
        }
      }
    });
  }
}

// ✅ 好的状态设计
class GoodStateDesign extends React.Component {
  state = {
    userProfile: { name: '', age: 0 },
    userSettings: { theme: 'light', notifications: true },
    userPosts: []
  };

  updateUserName = (name) => {
    // 只更新相关的状态片段
    this.setState({
      userProfile: {
        ...this.state.userProfile,
        name
      }
    });
  }
}`,
      impact: "减少状态更新的影响范围，提升渲染效率"
    }
  ],

  benchmarks: [
    {
      scenario: "大列表渲染性能",
      description: "1000个列表项的渲染性能对比",
      metrics: {
        "普通组件": "初始渲染: 150ms, 更新: 80ms",
        "PureComponent": "初始渲染: 150ms, 更新: 20ms",
        "shouldComponentUpdate": "初始渲染: 150ms, 更新: 15ms"
      },
      conclusion: "使用优化策略可以将更新性能提升75%"
    },
    {
      scenario: "深层组件树更新",
      description: "10层嵌套组件的状态更新性能",
      metrics: {
        "无优化": "更新时间: 45ms, 重新渲染组件: 10个",
        "PureComponent": "更新时间: 12ms, 重新渲染组件: 3个",
        "memo + useMemo": "更新时间: 8ms, 重新渲染组件: 1个"
      },
      conclusion: "合理的优化可以减少73%的渲染时间"
    },
    {
      scenario: "事件处理器性能",
      description: "100个按钮的事件处理性能对比",
      metrics: {
        "内联函数": "内存占用: 高, 子组件重渲染: 100%",
        "绑定方法": "内存占用: 低, 子组件重渲染: 0%",
        "useCallback": "内存占用: 中, 子组件重渲染: 0%"
      },
      conclusion: "避免内联函数可以完全消除不必要的重渲染"
    }
  ],

  monitoring: {
    tools: [
      {
        name: "React DevTools Profiler",
        description: "React官方性能分析工具，可以分析组件渲染时间和原因",
        usage: "在浏览器中安装React DevTools扩展，使用Profiler标签页记录和分析性能"
      },
      {
        name: "Chrome DevTools Performance",
        description: "浏览器内置的性能分析工具，可以分析整体页面性能",
        usage: "F12 -> Performance标签页，录制页面交互过程，分析火焰图"
      },
      {
        name: "why-did-you-render",
        description: "第三方库，帮助发现不必要的重新渲染",
        usage: "npm install @welldone-software/why-did-you-render，在开发环境中集成"
      },
      {
        name: "React.Profiler API",
        description: "程序化的性能监控API，可以在生产环境中收集性能数据",
        usage: "使用<Profiler>组件包装需要监控的组件树"
      }
    ],

    metrics: [
      {
        metric: "组件渲染时间",
        description: "单个组件从开始渲染到完成的时间",
        target: "< 16ms (60fps)",
        measurement: "使用React DevTools Profiler测量"
      },
      {
        metric: "不必要的重渲染次数",
        description: "props和state没有变化但仍然重新渲染的次数",
        target: "0次",
        measurement: "使用why-did-you-render检测"
      },
      {
        metric: "内存使用量",
        description: "组件实例和相关对象占用的内存",
        target: "稳定增长，无内存泄漏",
        measurement: "Chrome DevTools Memory标签页"
      },
      {
        metric: "首次内容绘制(FCP)",
        description: "页面首次渲染内容的时间",
        target: "< 1.8s",
        measurement: "Lighthouse或Chrome DevTools"
      }
    ]
  },

  bestPractices: [
    {
      practice: "合理使用PureComponent",
      description: "对于props和state变化频率较低的组件，使用PureComponent可以获得免费的性能优化",
      example: `// 适合使用PureComponent的场景
class UserCard extends PureComponent {
  render() {
    const { user, onEdit } = this.props;
    return (
      <div className="user-card">
        <img src={user.avatar} alt={user.name} />
        <h3>{user.name}</h3>
        <p>{user.email}</p>
        <button onClick={() => onEdit(user.id)}>编辑</button>
      </div>
    );
  }
}`
    },
    {
      practice: "避免在render中创建对象",
      description: "每次渲染都创建新对象会导致子组件不必要的重新渲染",
      example: `class GoodPractice extends React.Component {
  constructor(props) {
    super(props);
    // 在构造函数中创建稳定的对象引用
    this.styles = { color: 'blue', fontSize: '14px' };
    this.emptyArray = [];
  }

  render() {
    return (
      <div>
        {/* ✅ 好的做法：使用稳定的引用 */}
        <span style={this.styles}>文本</span>
        <List items={this.props.items || this.emptyArray} />

        {/* ❌ 避免：每次渲染都创建新对象 */}
        {/* <span style={{ color: 'blue' }}>文本</span> */}
        {/* <List items={this.props.items || []} /> */}
      </div>
    );
  }
}`
    },
    {
      practice: "合理拆分组件",
      description: "将大组件拆分为小组件，减少重新渲染的影响范围",
      example: `// ❌ 大组件：任何状态变化都会重新渲染整个组件
class BigComponent extends React.Component {
  state = { count: 0, user: null, posts: [] };

  render() {
    return (
      <div>
        <Counter count={this.state.count} />
        <UserProfile user={this.state.user} />
        <PostList posts={this.state.posts} />
      </div>
    );
  }
}

// ✅ 拆分后：每个部分独立更新
class App extends React.Component {
  render() {
    return (
      <div>
        <CounterContainer />
        <UserProfileContainer />
        <PostListContainer />
      </div>
    );
  }
}`
    },
    {
      practice: "使用key属性优化列表",
      description: "为列表项提供稳定且唯一的key，帮助React优化DOM操作",
      example: `class OptimizedList extends React.Component {
  render() {
    const { items } = this.props;

    return (
      <ul>
        {items.map(item => (
          // ✅ 使用稳定且唯一的key
          <li key={item.id}>
            <ItemComponent item={item} />
          </li>
        ))}
      </ul>
    );
  }
}

// ❌ 避免使用数组索引作为key（当列表会重新排序时）
// {items.map((item, index) => <li key={index}>...</li>)}`
    }
  ]
};

export default performanceOptimization;