import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "React.Component是React中用于创建类组件的基类，提供了组件生命周期方法、状态管理和渲染逻辑的完整框架。",

  introduction: `React.Component是React框架的核心基类，为开发者提供了创建有状态组件的强大能力。通过继承React.Component，你可以访问完整的组件生命周期、内置的状态管理系统，以及丰富的实例方法。

虽然在React 16.8引入Hooks后，函数组件成为了主流选择，但React.Component在某些特定场景下仍然具有不可替代的价值，特别是在需要精确控制生命周期、错误边界处理，或与传统类组件代码库集成时。

理解React.Component的工作原理对于深入掌握React框架至关重要，它不仅是React组件系统的基石，也是理解现代React Hooks设计思想的重要参考。`,

  syntax: `class MyComponent extends React.Component {
  constructor(props) {
    super(props);
    this.state = { count: 0 };
  }

  render() {
    return <div>{this.state.count}</div>;
  }
}`,

  quickExample: `// 基础类组件示例
class Counter extends React.Component {
  constructor(props) {
    super(props);
    this.state = { count: 0 };
  }

  increment = () => {
    this.setState({ count: this.state.count + 1 });
  }

  render() {
    return (
      <div>
        <p>计数: {this.state.count}</p>
        <button onClick={this.increment}>增加</button>
      </div>
    );
  }
}`,

  scenarioDiagram: [
    {
      title: "类组件生命周期流程",
      description: "展示React.Component从创建到销毁的完整生命周期过程",
      diagram: `graph TD
    A[组件实例化] --> B[constructor构造函数]
    B --> C[componentDidMount挂载完成]
    C --> D[组件运行中]
    D --> E[props/state变化]
    E --> F[shouldComponentUpdate判断]
    F -->|true| G[componentDidUpdate更新完成]
    F -->|false| D
    G --> D
    D --> H[componentWillUnmount卸载]
    H --> I[组件销毁]

    style A fill:#e1f5fe
    style I fill:#ffebee
    style D fill:#f3e5f5`
    },
    {
      title: "状态管理机制",
      description: "React.Component内置状态管理的工作原理",
      diagram: `graph LR
    A[用户交互] --> B[调用setState]
    B --> C[状态合并]
    C --> D[触发重新渲染]
    D --> E[调用render方法]
    E --> F[更新DOM]
    F --> G[componentDidUpdate]

    style B fill:#fff3e0
    style D fill:#e8f5e8
    style F fill:#f3e5f5`
    },
    {
      title: "组件通信模式",
      description: "React.Component在组件树中的数据流动方式",
      diagram: `graph TD
    A[父组件] -->|props传递| B[子组件]
    B -->|回调函数| A
    A --> C[Context Provider]
    C --> D[深层子组件]
    D -->|Context Consumer| C

    style A fill:#e3f2fd
    style B fill:#f1f8e9
    style C fill:#fff8e1
    style D fill:#fce4ec`
    }
  ],

  parameters: [
    {
      name: "props",
      type: "object",
      required: true,
      description: "组件接收的属性对象，由父组件传递，在组件内部为只读"
    },
    {
      name: "context",
      type: "object",
      required: false,
      description: "React Context对象，用于跨组件层级传递数据"
    }
  ],

  returnValue: {
    type: "ReactElement | null",
    description: "render方法必须返回一个React元素、null或false。React元素描述了你希望在屏幕上看到的内容。"
  },

  keyFeatures: [
    {
      name: "生命周期方法",
      description: "提供完整的组件生命周期钩子，包括挂载、更新、卸载阶段的精确控制"
    },
    {
      name: "内置状态管理",
      description: "通过this.state和this.setState()提供组件级别的状态管理能力"
    },
    {
      name: "实例方法",
      description: "支持定义实例方法，可以被子组件通过ref调用，实现命令式API"
    },
    {
      name: "错误边界",
      description: "可以实现componentDidCatch和getDerivedStateFromError来捕获子组件错误"
    },
    {
      name: "性能优化",
      description: "通过shouldComponentUpdate、PureComponent等机制实现渲染优化"
    }
  ],

  limitations: [
    {
      limitation: "代码复杂度较高",
      description: "相比函数组件，类组件需要更多的样板代码，学习曲线更陡峭"
    },
    {
      limitation: "this绑定问题",
      description: "需要手动绑定this，容易出现上下文丢失的问题"
    },
    {
      limitation: "逻辑复用困难",
      description: "在Hooks出现之前，类组件的逻辑复用主要依赖高阶组件和render props，较为复杂"
    },
    {
      limitation: "包体积影响",
      description: "类组件通常比等效的函数组件产生更大的打包体积"
    }
  ],

  bestPractices: [
    {
      practice: "合理使用生命周期",
      description: "只在必要时使用生命周期方法，避免在render中执行副作用操作"
    },
    {
      practice: "状态设计原则",
      description: "保持state结构扁平，避免深层嵌套，使用setState的函数形式处理依赖前一状态的更新"
    },
    {
      practice: "事件处理优化",
      description: "使用箭头函数或在constructor中绑定this，避免在render中创建新函数"
    },
    {
      practice: "错误边界实现",
      description: "在适当的层级实现错误边界，提供优雅的错误处理和用户体验"
    }
  ],

  warnings: [
    {
      warning: "避免直接修改state",
      description: "永远不要直接修改this.state，必须使用this.setState()来触发重新渲染"
    },
    {
      warning: "生命周期方法的副作用",
      description: "在componentDidMount中进行数据获取，在componentWillUnmount中清理资源，避免内存泄漏"
    },
    {
      warning: "render方法的纯净性",
      description: "render方法应该是纯函数，不应该修改组件状态或执行副作用操作"
    }
  ]
};

export default basicInfo;