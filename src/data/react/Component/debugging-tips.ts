import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: '社区工程师在使用React.Component时最常遇到的问题和解决方案。这些都是来自真实项目的经验总结，帮助你快速定位和解决调试问题。',
        sections: [
          {
            title: 'this绑定问题',
            description: '类组件中最常见的问题，通常导致事件处理器无法正常工作',
            items: [
              {
                title: '事件处理器中this为undefined',
                description: '在事件处理器中访问this.state或this.setState时报错"Cannot read property of undefined"',
                solution: '使用箭头函数或在构造函数中绑定this',
                prevention: '统一使用箭头函数定义事件处理器，避免this绑定问题',
                code: `// ❌ 问题代码
class MyComponent extends React.Component {
  handleClick() {
    // this为undefined，会报错
    this.setState({ clicked: true });
  }

  render() {
    return <button onClick={this.handleClick}>点击</button>;
  }
}

// ✅ 解决方案1：箭头函数
class MyComponent extends React.Component {
  handleClick = () => {
    this.setState({ clicked: true });
  }

  render() {
    return <button onClick={this.handleClick}>点击</button>;
  }
}

// ✅ 解决方案2：构造函数绑定
class MyComponent extends React.Component {
  constructor(props) {
    super(props);
    this.handleClick = this.handleClick.bind(this);
  }

  handleClick() {
    this.setState({ clicked: true });
  }

  render() {
    return <button onClick={this.handleClick}>点击</button>;
  }
}`
              }
            ]
          },
          {
            title: '状态更新问题',
            description: 'setState相关的常见问题和解决方案',
            items: [
              {
                title: '状态更新不生效',
                description: '调用setState后组件没有重新渲染，或者状态值没有更新',
                solution: '检查是否直接修改了state，确保使用setState更新状态',
                prevention: '始终使用setState更新状态，避免直接修改state对象',
                code: `// ❌ 错误：直接修改state
this.state.count++; // 不会触发重新渲染
this.state.items.push(newItem); // 不会触发重新渲染

// ✅ 正确：使用setState
this.setState({ count: this.state.count + 1 });
this.setState({ items: [...this.state.items, newItem] });

// ✅ 函数式更新（推荐）
this.setState(prev => ({ count: prev.count + 1 }));
this.setState(prev => ({ items: [...prev.items, newItem] }));`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'devtools-usage',
      title: '🛠️ DevTools使用',
      content: {
        introduction: '掌握开发工具的高级使用技巧，充分发挥React.Component的调试能力。这些技巧来自资深开发者的实战经验。',
        sections: [
          {
            title: 'React DevTools组件调试',
            description: '使用React DevTools深入分析类组件的状态和props',
            items: [
              {
                title: '组件状态实时查看和修改',
                description: '在React DevTools中实时查看和修改组件的state和props',
                steps: [
                  '安装React DevTools浏览器扩展',
                  '打开开发者工具，切换到React标签页',
                  '在组件树中选择目标组件',
                  '在右侧面板查看props和state',
                  '双击state值进行实时修改'
                ],
                tips: [
                  '使用搜索功能快速定位组件',
                  '可以直接在DevTools中调用组件方法',
                  '使用Profiler标签页分析性能问题'
                ],
                code: `// 在组件中添加displayName便于调试
class MyComponent extends React.Component {
  static displayName = 'MyComponent';

  // 添加调试方法
  debug = () => {
    console.log('Current state:', this.state);
    console.log('Current props:', this.props);
  }

  render() {
    // 在开发环境中暴露调试方法到window
    if (process.env.NODE_ENV === 'development') {
      window.debugMyComponent = this.debug;
    }

    return <div>...</div>;
  }
}`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'troubleshooting',
      title: '🔧 问题排查',
      content: {
        introduction: '系统化的问题排查方法，帮助你快速定位和解决React.Component相关的各种问题。这些方法经过大量实践验证。',
        sections: [
          {
            title: '渲染问题排查',
            description: '当组件渲染异常或不渲染时的系统化排查方法',
            items: [
              {
                title: '组件不渲染或渲染异常',
                description: '组件没有显示在页面上，或者显示的内容不符合预期',
                steps: [
                  '检查render方法是否返回有效的JSX',
                  '确认组件是否被正确挂载到DOM',
                  '检查CSS样式是否影响了显示',
                  '使用React DevTools查看组件树结构',
                  '检查条件渲染的逻辑是否正确'
                ],
                code: `// 排查工具：渲染调试组件
class RenderDebugger extends React.Component {
  render() {
    console.log('Rendering with props:', this.props);
    console.log('Rendering with state:', this.state);

    // 检查render返回值
    const result = (
      <div style={{ border: '1px solid red', padding: '10px' }}>
        <h3>Debug Info</h3>
        <pre>{JSON.stringify(this.props, null, 2)}</pre>
        <pre>{JSON.stringify(this.state, null, 2)}</pre>
        {this.props.children}
      </div>
    );

    console.log('Render result:', result);
    return result;
  }
}`
              }
            ]
          }
        ]
      }
    }
  ]
};

export default debuggingTips;