import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: 'React.Component的生命周期方法有哪些？请按执行顺序说明它们的作用。',
    difficulty: 'medium',
    frequency: 'high',
    category: '生命周期',
    answer: {
      brief: 'React.Component的生命周期分为挂载、更新、卸载三个阶段，每个阶段都有对应的生命周期方法。',
      detailed: `React.Component的生命周期方法按执行顺序分为三个阶段：

## 挂载阶段 (Mounting)
1. **constructor(props)** - 构造函数，初始化state和绑定方法
2. **render()** - 渲染方法，返回JSX描述UI结构
3. **componentDidMount()** - 组件挂载完成，可以进行DOM操作、数据获取

## 更新阶段 (Updating)
1. **shouldComponentUpdate(nextProps, nextState)** - 决定是否需要重新渲染
2. **render()** - 重新渲染组件
3. **componentDidUpdate(prevProps, prevState)** - 组件更新完成，可以进行DOM操作

## 卸载阶段 (Unmounting)
1. **componentWillUnmount()** - 组件即将卸载，清理定时器、取消网络请求等

## 错误处理
1. **componentDidCatch(error, errorInfo)** - 捕获子组件错误
2. **getDerivedStateFromError(error)** - 静态方法，从错误中派生state`,
      code: `class LifecycleDemo extends React.Component {
  constructor(props) {
    super(props);
    this.state = { count: 0 };
    console.log('1. constructor');
  }

  componentDidMount() {
    console.log('3. componentDidMount');
    // 数据获取、订阅事件
    this.timer = setInterval(() => {
      this.setState(prev => ({ count: prev.count + 1 }));
    }, 1000);
  }

  shouldComponentUpdate(nextProps, nextState) {
    console.log('4. shouldComponentUpdate');
    // 性能优化：只在count变化时更新
    return nextState.count !== this.state.count;
  }

  componentDidUpdate(prevProps, prevState) {
    console.log('6. componentDidUpdate');
    // 响应props或state变化
    if (prevState.count !== this.state.count) {
      document.title = \`计数: \${this.state.count}\`;
    }
  }

  componentWillUnmount() {
    console.log('7. componentWillUnmount');
    // 清理资源
    if (this.timer) {
      clearInterval(this.timer);
    }
  }

  render() {
    console.log('2/5. render');
    return <div>计数: {this.state.count}</div>;
  }
}`
    },
    tags: ['生命周期', '基础概念']
  },

  {
    id: 2,
    question: 'setState是同步的还是异步的？为什么这样设计？',
    difficulty: 'medium',
    frequency: 'high',
    category: '状态管理',
    answer: {
      brief: 'setState在大多数情况下是异步的，这是为了性能优化，允许React批量处理状态更新。',
      detailed: `setState的同步/异步行为取决于调用的上下文：

## 异步情况（大多数）
在React事件处理器和生命周期方法中，setState是异步的：
- React会将多个setState调用批量处理
- 避免不必要的重新渲染
- 提升性能

## 同步情况（少数）
在setTimeout、Promise、原生事件处理器中，setState是同步的：
- React无法控制这些异步操作的时机
- 每次setState都会立即触发重新渲染

## 设计原因
1. **性能优化**：批量更新减少渲染次数
2. **一致性**：确保props和state的一致性
3. **可预测性**：避免中间状态的不一致

## React 18的变化
在React 18中，通过Automatic Batching，所有更新都会被批量处理。`,
      code: `class SetStateDemo extends React.Component {
  state = { count: 0 };

  // 异步情况 - React事件处理器
  handleClick = () => {
    console.log('Before:', this.state.count); // 0

    this.setState({ count: this.state.count + 1 });
    console.log('After:', this.state.count); // 仍然是0

    this.setState({ count: this.state.count + 1 });
    console.log('After:', this.state.count); // 仍然是0

    // 最终count只会增加1，因为两次setState被批量处理
  }

  // 同步情况 - setTimeout
  handleAsyncClick = () => {
    setTimeout(() => {
      console.log('Before:', this.state.count);

      this.setState({ count: this.state.count + 1 });
      console.log('After:', this.state.count); // 立即更新

      this.setState({ count: this.state.count + 1 });
      console.log('After:', this.state.count); // 再次更新
    }, 0);
  }

  // 正确的异步更新方式
  handleCorrectUpdate = () => {
    this.setState(prev => ({ count: prev.count + 1 }));
    this.setState(prev => ({ count: prev.count + 1 }));
    // count会正确增加2
  }

  render() {
    return (
      <div>
        <p>Count: {this.state.count}</p>
        <button onClick={this.handleClick}>异步更新</button>
        <button onClick={this.handleAsyncClick}>同步更新</button>
        <button onClick={this.handleCorrectUpdate}>正确更新</button>
      </div>
    );
  }
}`
    },
    tags: ['setState', '异步', '状态管理']
  },

  {
    id: 3,
    question: '类组件和函数组件有什么区别？什么时候应该使用类组件？',
    difficulty: 'medium',
    frequency: 'high',
    category: '组件对比',
    answer: {
      brief: '类组件提供完整的生命周期和实例方法，函数组件更简洁。现在主要在错误边界和遗留代码中使用类组件。',
      detailed: `## 主要区别

### 语法和写法
- **类组件**：基于ES6类，需要继承React.Component
- **函数组件**：纯函数，接收props返回JSX

### 状态管理
- **类组件**：使用this.state和this.setState()
- **函数组件**：使用useState Hook

### 生命周期
- **类组件**：完整的生命周期方法
- **函数组件**：使用useEffect模拟生命周期

### 性能
- **类组件**：可以使用shouldComponentUpdate优化
- **函数组件**：使用React.memo和useMemo优化

## 何时使用类组件

### 必须使用的场景
1. **错误边界**：只有类组件能实现componentDidCatch
2. **遗留代码维护**：保持代码一致性
3. **第三方库集成**：某些库只支持类组件

### 可选使用的场景
1. **复杂的生命周期逻辑**：需要精确控制生命周期
2. **实例方法暴露**：需要通过ref调用组件方法
3. **团队偏好**：团队更熟悉类组件模式`,
      code: `// 类组件示例
class ClassComponent extends React.Component {
  constructor(props) {
    super(props);
    this.state = { count: 0 };
  }

  componentDidMount() {
    document.title = \`Count: \${this.state.count}\`;
  }

  componentDidUpdate() {
    document.title = \`Count: \${this.state.count}\`;
  }

  increment = () => {
    this.setState(prev => ({ count: prev.count + 1 }));
  }

  render() {
    return (
      <div>
        <p>{this.state.count}</p>
        <button onClick={this.increment}>增加</button>
      </div>
    );
  }
}

// 等效的函数组件
function FunctionComponent() {
  const [count, setCount] = useState(0);

  useEffect(() => {
    document.title = \`Count: \${count}\`;
  }, [count]);

  const increment = () => {
    setCount(prev => prev + 1);
  };

  return (
    <div>
      <p>{count}</p>
      <button onClick={increment}>增加</button>
    </div>
  );
}

// 错误边界 - 只能用类组件
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <h1>Something went wrong.</h1>;
    }
    return this.props.children;
  }
}`
    },
    tags: ['类组件', '函数组件', '对比', 'Hooks']
  }
];

export default interviewQuestions;