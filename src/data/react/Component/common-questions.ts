import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'this-binding',
    question: '为什么在类组件中需要绑定this？如何正确绑定？',
    answer: `在JavaScript中，类方法默认不会绑定this。当方法作为事件处理器传递时，this会丢失上下文，导致无法访问组件实例。

## 问题原因
- JavaScript的this绑定规则
- 事件处理器中this指向undefined（严格模式）
- 类方法不会自动绑定this

## 解决方案
1. **构造函数中绑定**：在constructor中使用bind()
2. **箭头函数**：利用箭头函数的词法作用域
3. **内联箭头函数**：在JSX中使用箭头函数（不推荐）`,
    code: `class MyComponent extends React.Component {
  constructor(props) {
    super(props);
    this.state = { count: 0 };

    // 方法1: 构造函数中绑定
    this.handleClick1 = this.handleClick1.bind(this);
  }

  // 传统方法 - 需要绑定this
  handleClick1() {
    this.setState({ count: this.state.count + 1 });
  }

  // 方法2: 箭头函数 - 自动绑定this (推荐)
  handleClick2 = () => {
    this.setState({ count: this.state.count + 1 });
  }

  render() {
    return (
      <div>
        <p>Count: {this.state.count}</p>

        {/* 正确：已绑定的方法 */}
        <button onClick={this.handleClick1}>方法1</button>
        <button onClick={this.handleClick2}>方法2</button>

        {/* 方法3: 内联箭头函数 - 不推荐，每次渲染都创建新函数 */}
        <button onClick={() => this.handleClick1()}>方法3</button>

        {/* 错误：this会丢失 */}
        {/* <button onClick={this.handleClick1}>错误</button> */}
      </div>
    );
  }
}`,
    tags: ['this绑定', '事件处理', '箭头函数'],
    relatedQuestions: ['arrow-functions', 'event-handling']
  },

  {
    id: 'state-mutation',
    question: '为什么不能直接修改state？直接修改会发生什么？',
    answer: `直接修改state是React中的常见错误，会导致组件不重新渲染，状态不一致等问题。

## 为什么不能直接修改
1. **React无法检测变化**：React通过引用比较检测state变化
2. **不会触发重新渲染**：只有通过setState才会触发渲染
3. **破坏不可变性**：违反React的设计原则
4. **调试困难**：状态变化无法追踪

## 正确的状态更新方式
- 使用setState()方法
- 对于对象和数组，创建新的副本
- 使用展开运算符或Object.assign()`,
    code: `class StateExample extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      count: 0,
      user: { name: 'John', age: 25 },
      items: ['apple', 'banana']
    };
  }

  // ❌ 错误：直接修改state
  wrongUpdate = () => {
    this.state.count++; // 不会触发重新渲染
    this.state.user.age = 26; // 不会触发重新渲染
    this.state.items.push('orange'); // 不会触发重新渲染
  }

  // ✅ 正确：使用setState
  correctUpdate = () => {
    // 更新基本类型
    this.setState({ count: this.state.count + 1 });

    // 更新对象 - 创建新对象
    this.setState({
      user: {
        ...this.state.user,
        age: this.state.user.age + 1
      }
    });

    // 更新数组 - 创建新数组
    this.setState({
      items: [...this.state.items, 'orange']
    });
  }

  // ✅ 使用函数式更新
  functionalUpdate = () => {
    this.setState(prevState => ({
      count: prevState.count + 1,
      user: {
        ...prevState.user,
        age: prevState.user.age + 1
      },
      items: [...prevState.items, 'orange']
    }));
  }

  render() {
    const { count, user, items } = this.state;

    return (
      <div>
        <p>Count: {count}</p>
        <p>User: {user.name}, Age: {user.age}</p>
        <p>Items: {items.join(', ')}</p>

        <button onClick={this.wrongUpdate}>错误更新</button>
        <button onClick={this.correctUpdate}>正确更新</button>
        <button onClick={this.functionalUpdate}>函数式更新</button>
      </div>
    );
  }
}`,
    tags: ['state', '不可变性', 'setState'],
    relatedQuestions: ['setState-async', 'immutability']
  },

  {
    id: 'lifecycle-cleanup',
    question: '如何在组件卸载时正确清理资源？常见的内存泄漏有哪些？',
    answer: `组件卸载时不清理资源会导致内存泄漏，影响应用性能。需要在componentWillUnmount中清理所有副作用。

## 常见的内存泄漏源
1. **定时器**：setInterval、setTimeout未清理
2. **事件监听器**：DOM事件、自定义事件未移除
3. **网络请求**：未取消的异步请求
4. **订阅**：第三方库的订阅未取消
5. **WebSocket连接**：未关闭的连接

## 清理策略
- 在componentDidMount中创建，在componentWillUnmount中清理
- 保存引用以便后续清理
- 使用AbortController取消网络请求`,
    code: `class ResourceCleanup extends React.Component {
  constructor(props) {
    super(props);
    this.state = { data: null, time: new Date() };

    // 保存引用以便清理
    this.timer = null;
    this.abortController = null;
    this.eventListener = null;
  }

  componentDidMount() {
    // 1. 设置定时器
    this.timer = setInterval(() => {
      this.setState({ time: new Date() });
    }, 1000);

    // 2. 添加事件监听器
    this.eventListener = this.handleResize.bind(this);
    window.addEventListener('resize', this.eventListener);

    // 3. 发起网络请求
    this.fetchData();

    // 4. 第三方库订阅
    this.subscription = SomeLibrary.subscribe(this.handleData);
  }

  componentWillUnmount() {
    // 1. 清理定时器
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }

    // 2. 移除事件监听器
    if (this.eventListener) {
      window.removeEventListener('resize', this.eventListener);
      this.eventListener = null;
    }

    // 3. 取消网络请求
    if (this.abortController) {
      this.abortController.abort();
    }

    // 4. 取消第三方库订阅
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  fetchData = async () => {
    try {
      this.abortController = new AbortController();

      const response = await fetch('/api/data', {
        signal: this.abortController.signal
      });

      if (!response.ok) throw new Error('Network error');

      const data = await response.json();
      this.setState({ data });
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Fetch error:', error);
      }
    }
  }

  handleResize = () => {
    console.log('Window resized');
  }

  handleData = (data) => {
    this.setState({ data });
  }

  render() {
    return (
      <div>
        <p>Time: {this.state.time.toLocaleTimeString()}</p>
        <p>Data: {JSON.stringify(this.state.data)}</p>
      </div>
    );
  }
}

// 使用自定义Hook的函数组件等效实现
function ResourceCleanupHook() {
  const [data, setData] = useState(null);
  const [time, setTime] = useState(new Date());

  useEffect(() => {
    // 定时器
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);

    // 事件监听器
    const handleResize = () => console.log('Window resized');
    window.addEventListener('resize', handleResize);

    // 网络请求
    const abortController = new AbortController();
    fetch('/api/data', { signal: abortController.signal })
      .then(res => res.json())
      .then(setData)
      .catch(err => {
        if (err.name !== 'AbortError') {
          console.error('Fetch error:', err);
        }
      });

    // 清理函数
    return () => {
      clearInterval(timer);
      window.removeEventListener('resize', handleResize);
      abortController.abort();
    };
  }, []);

  return (
    <div>
      <p>Time: {time.toLocaleTimeString()}</p>
      <p>Data: {JSON.stringify(data)}</p>
    </div>
  );
}`,
    tags: ['内存泄漏', '资源清理', 'componentWillUnmount'],
    relatedQuestions: ['useEffect-cleanup', 'memory-leaks']
  }
];

export default commonQuestions;