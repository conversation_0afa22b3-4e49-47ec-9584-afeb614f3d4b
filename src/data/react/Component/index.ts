import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ReactComponentData: ApiItem = {
  id: 'Component',
  title: 'React.Component',
  description: 'React类组件基类，提供完整的组件生命周期、状态管理和渲染能力',
  category: 'React Components',
  difficulty: 'medium',

  syntax: `class MyComponent extends React.Component {
  constructor(props) {
    super(props);
    this.state = { count: 0 };
  }

  render() {
    return <div>{this.state.count}</div>;
  }
}`,

  example: `// 基础类组件示例
class Counter extends React.Component {
  constructor(props) {
    super(props);
    this.state = { count: 0 };
  }

  increment = () => {
    this.setState(prev => ({ count: prev.count + 1 }));
  }

  render() {
    return (
      <div>
        <p>计数: {this.state.count}</p>
        <button onClick={this.increment}>增加</button>
      </div>
    );
  }
}`,

  notes: '虽然Hooks已成为主流，但React.Component在错误边界、遗留代码维护等场景中仍然重要。理解类组件有助于深入理解React原理。',

  version: 'React 0.13.0+',
  tags: ["React", "Component", "Class", "Lifecycle"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ReactComponentData;