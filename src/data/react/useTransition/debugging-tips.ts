import { DebuggingTips } from '../../../types/api';

const debuggingTips: DebuggingTips = {
  devToolsTips: [
    {
      technique: '使用React DevTools Profiler监控transition性能',
      description: '在React DevTools的Profiler标签中可以看到transition的执行时间和被中断的次数'
    },
    {
      technique: '开启并发特性标记',
      description: '在开发环境中启用React的并发标记，帮助识别潜在问题'
    },
    {
      technique: '使用Performance API测量具体耗时',
      description: '在transition回调中添加performance.mark和performance.measure来精确测量执行时间'
    }
  ],
  
  commonErrors: [
    {
      error: 'transition回调中的状态更新没有生效',
      cause: 'startTransition中包含了异步操作，或者更新被其他高优先级任务中断',
      solution: '确保startTransition只包含同步的状态更新，异步操作应在外部完成',
      prevention: '将数据获取和状态更新分离，使用useEffect或自定义Hook处理异步逻辑',
      code: `// ❌ 错误：异步操作在transition中
startTransition(async () => {
  const data = await fetchData();  // 这不会工作
  setData(data);
});

// ✅ 正确：异步操作在外部
const loadData = async () => {
  const data = await fetchData();
  startTransition(() => {
    setData(data);  // 只有状态更新在transition中
  });
};`
    },
    
    {
      error: 'isPending状态一直为true不会变为false',
      cause: 'transition回调中抛出了异常，或者存在无限循环',
      solution: '添加错误处理，检查transition回调中的逻辑',
      prevention: '使用try-catch包装复杂逻辑，避免在transition中进行可能失败的操作',
      code: `// ✅ 添加错误处理
const [isPending, startTransition] = useTransition();

const handleUpdate = (data) => {
  startTransition(() => {
    try {
      const processedData = complexProcessing(data);
      setData(processedData);
    } catch (error) {
      console.error('Processing failed:', error);
      setError(error.message);
    }
  });
};`
    },
    
    {
      error: 'transition效果不明显，界面仍然卡顿',
      cause: '操作本身太快不需要transition，或者包含了同步的DOM操作',
      solution: '只对真正耗时的操作使用transition，避免在transition中直接操作DOM',
      prevention: '使用性能分析工具确认操作确实耗时>50ms，避免过度使用',
      code: `// ❌ 不需要transition的情况
startTransition(() => {
  setCount(count + 1);  // 简单状态更新不需要transition
});

// ✅ 真正需要transition的情况
startTransition(() => {
  const filtered = largeArray.filter(item => 
    complexFilterLogic(item)  // 耗时的过滤操作
  );
  setFilteredData(filtered);
});`
    },
    
    {
      error: '在React 18之前的版本中useTransition不可用',
      cause: 'useTransition是React 18的新特性，在旧版本中不存在',
      solution: '升级到React 18，或者在旧版本中使用传统的性能优化方法',
      prevention: '检查React版本，为不同版本提供降级方案',
      code: `// ✅ 版本兼容检查
import { useTransition, useEffect, useState } from 'react';

function useCompatibleTransition() {
  // 检查useTransition是否可用
  if (typeof useTransition !== 'function') {
    // React 18之前的降级方案
    const [isPending, setIsPending] = useState(false);
    
    const startTransition = (callback) => {
      setIsPending(true);
      setTimeout(() => {
        callback();
        setIsPending(false);
      }, 0);
    };
    
    return [isPending, startTransition];
  }
  
  return useTransition();
}`
    },
    
    {
      error: 'transition被频繁中断，导致更新永远不完成',
      cause: '用户交互太频繁，或者有其他高优先级任务不断插入',
      solution: '添加防抖或节流机制，减少transition触发频率',
      prevention: '合理设计用户交互，避免过于频繁的更新',
      code: `// ✅ 添加防抖机制
import { useMemo } from 'react';
import { debounce } from 'lodash';

function useDebounceTransition(delay = 300) {
  const [isPending, startTransition] = useTransition();
  
  const debouncedStartTransition = useMemo(
    () => debounce((callback) => {
      startTransition(callback);
    }, delay),
    [startTransition, delay]
  );
  
  return [isPending, debouncedStartTransition];
}`
    }
  ]
};

export default debuggingTips; 