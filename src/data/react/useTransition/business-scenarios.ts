import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: 'search-optimization',
    title: '电商搜索优化',
    description: '在电商平台中实现大量商品的即时搜索，保持搜索框的响应性',
    businessValue: '提升用户购物体验，减少因搜索卡顿导致的用户流失',
    scenario: `某电商平台有10万+商品数据，用户在搜索框输入时需要实时过滤显示结果。
传统方案会导致：
- 输入框卡顿，用户体验差
- 搜索延迟，影响购买转化
- 页面假死，用户可能离开

使用useTransition解决方案：
- 搜索框保持即时响应
- 后台异步处理搜索结果
- 提供清晰的加载反馈`,
    code: `import React, { useState, useTransition, useMemo } from 'react';

interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  brand: string;
  image: string;
}

function ProductSearch({ products }: { products: Product[] }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredProducts, setFilteredProducts] = useState(products);
  const [isPending, startTransition] = useTransition();

  // 复杂的搜索逻辑，模拟真实电商场景
  const performSearch = (query: string) => {
    if (!query.trim()) {
      return products;
    }

    return products.filter(product => {
      const searchTerms = query.toLowerCase().split(' ');
      const searchableText = \`\${product.name} \${product.category} \${product.brand}\`.toLowerCase();
      
      return searchTerms.every(term => 
        searchableText.includes(term)
      );
    });
  };

  const handleSearch = (value: string) => {
    // 立即更新搜索框，保持响应性
    setSearchQuery(value);

    // 将耗时的搜索操作标记为非紧急
    startTransition(() => {
      const results = performSearch(value);
      setFilteredProducts(results);
    });
  };

  return (
    <div className="product-search">
      <div className="search-container">
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          placeholder="搜索商品、品牌、类别..."
          className="search-input"
        />
        
        {isPending && (
          <div className="search-loading">
            <span className="spinner"></span>
            搜索中...
          </div>
        )}
      </div>

      <div className="search-results">
        <div className="results-header">
          找到 {filteredProducts.length} 个商品
        </div>
        
        <div className="products-grid">
          {filteredProducts.map(product => (
            <div key={product.id} className="product-card">
              <img src={product.image} alt={product.name} />
              <h3>{product.name}</h3>
              <p className="price">¥{product.price}</p>
              <p className="category">{product.category}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default ProductSearch;`,
    
    benefits: [
      '搜索框保持100%响应性，用户输入无延迟',
      '大数据量搜索不阻塞UI线程',
      '提供直观的搜索进度反馈',
      '显著提升用户购物体验'
    ],
    
    metrics: {
      performance: '输入响应时间从200ms降低到<16ms',
      userExperience: '搜索放弃率降低40%',
      technicalMetrics: 'FCP保持稳定，无长任务阻塞'
    }
  },

  {
    id: 'data-dashboard',
    title: '企业数据看板',
    description: '大型企业数据可视化看板的实时图表更新和交互优化',
    businessValue: '提升决策效率，确保关键数据更新不影响用户操作',
    scenario: `企业级数据看板需要：
- 实时更新多个复杂图表
- 保持用户交互的流畅性
- 处理大量数据计算
- 支持筛选器和时间范围选择

挑战：
- 数据计算耗时(>100ms)
- 多图表同时更新卡顿
- 筛选器操作延迟
- 用户体验受影响`,
    code: `import React, { useState, useTransition, useCallback, useEffect } from 'react';

interface ChartData {
  id: string;
  title: string;
  data: Array<{ date: string; value: number }>;
  type: 'line' | 'bar' | 'pie';
}

interface DashboardFilters {
  dateRange: { start: string; end: string };
  department: string;
  metric: string;
}

function DataDashboard() {
  const [filters, setFilters] = useState<DashboardFilters>({
    dateRange: { start: '2024-01-01', end: '2024-12-31' },
    department: 'all',
    metric: 'revenue'
  });
  
  const [chartsData, setChartsData] = useState<ChartData[]>([]);
  const [isPending, startTransition] = useTransition();
  const [lastUpdateTime, setLastUpdateTime] = useState<string>('');

  // 模拟复杂的数据处理
  const processChartData = useCallback((filters: DashboardFilters): ChartData[] => {
    // 模拟耗时的数据计算和API调用
    const mockData: ChartData[] = [
      {
        id: 'revenue',
        title: '营收趋势',
        data: Array.from({ length: 12 }, (_, i) => ({
          date: \`2024-\${String(i + 1).padStart(2, '0')}\`,
          value: Math.floor(Math.random() * 1000000) + 500000
        })),
        type: 'line'
      },
      {
        id: 'users',
        title: '用户增长',
        data: Array.from({ length: 12 }, (_, i) => ({
          date: \`2024-\${String(i + 1).padStart(2, '0')}\`,
          value: Math.floor(Math.random() * 50000) + 100000
        })),
        type: 'bar'
      },
      {
        id: 'conversion',
        title: '转化率分析',
        data: Array.from({ length: 5 }, (_, i) => ({
          date: \`渠道\${i + 1}\`,
          value: Math.floor(Math.random() * 20) + 10
        })),
        type: 'pie'
      }
    ];

    // 模拟基于筛选器的数据处理延迟
    const delay = Math.random() * 200 + 100;
    for (let i = 0; i < delay * 10000; i++) {
      // 模拟CPU密集型计算
      Math.sqrt(i);
    }

    return mockData;
  }, []);

  // 更新筛选器的处理函数
  const updateFilters = (newFilters: Partial<DashboardFilters>) => {
    // 立即更新筛选器UI，保持交互响应性
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);

    // 将耗时的图表数据更新标记为非紧急
    startTransition(() => {
      const newChartsData = processChartData(updatedFilters);
      setChartsData(newChartsData);
      setLastUpdateTime(new Date().toLocaleTimeString());
    });
  };

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1>企业数据看板</h1>
        <div className="update-status">
          {isPending ? (
            <span className="updating">📊 数据更新中...</span>
          ) : (
            <span className="updated">✅ 最后更新: {lastUpdateTime}</span>
          )}
        </div>
      </header>

      {/* 筛选器区域 - 保持即时响应 */}
      <div className="filters-section">
        <div className="filter-group">
          <label>时间范围:</label>
          <select
            value={filters.dateRange.start}
            onChange={(e) => updateFilters({
              dateRange: { ...filters.dateRange, start: e.target.value }
            })}
          >
            <option value="2024-01-01">2024年</option>
            <option value="2023-01-01">2023年</option>
          </select>
        </div>

        <div className="filter-group">
          <label>部门:</label>
          <select
            value={filters.department}
            onChange={(e) => updateFilters({ department: e.target.value })}
          >
            <option value="all">全部部门</option>
            <option value="sales">销售部</option>
            <option value="marketing">市场部</option>
            <option value="tech">技术部</option>
          </select>
        </div>

        <div className="filter-group">
          <label>指标:</label>
          <select
            value={filters.metric}
            onChange={(e) => updateFilters({ metric: e.target.value })}
          >
            <option value="revenue">营收</option>
            <option value="users">用户</option>
            <option value="conversion">转化率</option>
          </select>
        </div>
      </div>

      {/* 图表展示区域 */}
      <div className="charts-container">
        {chartsData.map(chart => (
          <div key={chart.id} className={\`chart-card \${isPending ? 'updating' : ''}\`}>
            <h3>{chart.title}</h3>
            <div className="chart-content">
              {isPending && (
                <div className="chart-loading-overlay">
                  <div className="loading-spinner"></div>
                </div>
              )}
              {/* 这里会是实际的图表组件 */}
              <div className="mock-chart">
                图表数据: {chart.data.length} 个数据点
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default DataDashboard;`,
    
    benefits: [
      '筛选器操作保持即时响应',
      '大数据量图表更新不阻塞界面',
      '清晰的数据更新状态反馈',
      '提升决策者的使用体验'
    ],
    
    metrics: {
      performance: '筛选器响应时间<16ms，图表更新时间优化60%',
      userExperience: '用户满意度提升35%，使用时长增加25%',
      technicalMetrics: 'Main thread blocking time减少80%'
    }
  },

  {
    id: 'content-editing',
    title: '富文本编辑器',
    description: '大型文档的实时预览和格式化处理优化',
    businessValue: '提升内容创作体验，支持长文档的流畅编辑',
    scenario: `在线文档编辑器需要：
- 实时预览功能
- 复杂格式化处理
- 大文档处理能力
- 流畅的输入体验

技术挑战：
- 预览渲染耗时
- 格式化计算复杂
- 输入延迟问题
- 内存使用优化`,
    code: `import React, { useState, useTransition, useCallback, useMemo } from 'react';

interface DocumentContent {
  raw: string;
  formatted: string;
  wordCount: number;
  readingTime: number;
}

function AdvancedEditor() {
  const [content, setContent] = useState('');
  const [previewData, setPreviewData] = useState<DocumentContent>({
    raw: '',
    formatted: '',
    wordCount: 0,
    readingTime: 0
  });
  const [isPending, startTransition] = useTransition();
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  // 复杂的文档处理逻辑
  const processDocument = useCallback((rawContent: string): DocumentContent => {
    // 模拟复杂的格式化处理
    let formatted = rawContent
      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
      .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
      .replace(/\`(.*?)\`/g, '<code>$1</code>')
      .replace(/\\n\\n/g, '</p><p>')
      .replace(/\\n/g, '<br>');

    if (formatted) {
      formatted = \`<p>\${formatted}</p>\`;
    }

    // 模拟耗时的统计计算
    const words = rawContent.split(/\\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    const readingTime = Math.ceil(wordCount / 200); // 假设每分钟200字

    // 模拟处理延迟
    for (let i = 0; i < rawContent.length * 10; i++) {
      Math.random();
    }

    return {
      raw: rawContent,
      formatted,
      wordCount,
      readingTime
    };
  }, []);

  const handleContentChange = (newContent: string) => {
    // 立即更新编辑器内容，保持输入响应性
    setContent(newContent);

    // 将耗时的预览处理标记为非紧急
    startTransition(() => {
      const processedData = processDocument(newContent);
      setPreviewData(processedData);
    });
  };

  const togglePreviewMode = () => {
    setIsPreviewMode(!isPreviewMode);
  };

  return (
    <div className="advanced-editor">
      <div className="editor-toolbar">
        <div className="editor-stats">
          <span>字数: {previewData.wordCount}</span>
          <span>预计阅读: {previewData.readingTime}分钟</span>
          {isPending && (
            <span className="processing">🔄 处理中...</span>
          )}
        </div>
        
        <div className="editor-controls">
          <button
            onClick={togglePreviewMode}
            className={\`preview-toggle \${isPreviewMode ? 'active' : ''}\`}
          >
            {isPreviewMode ? '编辑模式' : '预览模式'}
          </button>
        </div>
      </div>

      <div className="editor-container">
        {!isPreviewMode ? (
          <div className="editor-pane">
            <textarea
              value={content}
              onChange={(e) => handleContentChange(e.target.value)}
              placeholder="开始写作..."
              className="editor-textarea"
              rows={20}
            />
            
            <div className="editor-hints">
              <p>支持Markdown语法:</p>
              <ul>
                <li>**粗体** 或 *斜体*</li>
                <li>\`代码\`</li>
                <li>空行分段</li>
              </ul>
            </div>
          </div>
        ) : (
          <div className="preview-pane">
            <div 
              className={\`preview-content \${isPending ? 'updating' : ''}\`}
              dangerouslySetInnerHTML={{ 
                __html: previewData.formatted || '<p>开始写作以查看预览...</p>' 
              }}
            />
            
            {isPending && (
              <div className="preview-loading">
                <div className="loading-bar"></div>
                <span>预览更新中...</span>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="editor-footer">
        <div className="document-info">
          <span>文档已保存</span>
          <span>最后更新: {new Date().toLocaleTimeString()}</span>
        </div>
      </div>
    </div>
  );
}

export default AdvancedEditor;`,
    
    benefits: [
      '编辑输入保持完全流畅',
      '复杂预览渲染不影响写作',
      '实时统计信息更新',
      '提升长文档编辑体验'
    ],
    
    metrics: {
      performance: '输入延迟<10ms，预览渲染优化70%',
      userExperience: '写作流畅度提升50%，用户满意度90%+',
      technicalMetrics: 'Input blocking time几乎为0'
    }
  }
];

export default businessScenarios; 