import { ApiItem } from '../../../types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import commonQuestions from './common-questions';

// 标准可选Tab
import performanceOptimization from './performance-optimization';
import learningPath from './learning-path';
import versionMigration from './version-migration';
import ecosystemTools from './ecosystem-tools';
import realWorldProjects from './real-world-projects';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useTransitionData: ApiItem = {
  id: 'useTransition',
  title: 'useTransition',
  description: 'React 18并发特性核心Hook，用于标记状态更新为非紧急，提升用户体验',
  content: `useTransition让你将状态更新标记为transitions（过渡），这些更新不会阻塞用户交互。
它是React 18并发特性的核心，通过时间切片和优先级调度，让应用保持响应性，特别适用于大数据量渲染、复杂计算等场景。`,
  
  syntax: `const [isPending, startTransition] = useTransition()

// 完整类型定义
function useTransition(): [
  isPending: boolean,
  startTransition: (callback: () => void) => void
]`,

  example: `import React, { useState, useTransition } from 'react';

// 大列表过滤示例：防止输入卡顿
function SearchableList({ items }) {
  const [query, setQuery] = useState('');
  const [filteredItems, setFilteredItems] = useState(items);
  const [isPending, startTransition] = useTransition();

  const handleSearch = (value) => {
    setQuery(value); // 立即更新输入框
    
    // 将过滤操作标记为非紧急
    startTransition(() => {
      const filtered = items.filter(item => 
        item.name.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredItems(filtered);
    });
  };

  return (
    <div>
      <input
        type="text"
        value={query}
        onChange={(e) => handleSearch(e.target.value)}
        placeholder="搜索商品..."
      />
      
      {isPending && <div className="loading">正在搜索...</div>}
      
      <div className="results">
        {filteredItems.map(item => (
          <div key={item.id} className="item">
            <h3>{item.name}</h3>
            <p>{item.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
}

// 这个示例展示了useTransition如何保持输入框的即时响应性
// 同时在后台处理耗时的过滤操作`,

  parameters: [
    {
      name: 'isPending',
      type: 'boolean',
      description: '指示transition是否正在进行中，可用于显示加载状态'
    },
    {
      name: 'startTransition',
      type: '(callback: () => void) => void',
      description: '开始一个transition的函数，接收包含状态更新的回调函数'
    }
  ],

  returnValue: {
    type: '[boolean, (callback: () => void) => void]',
    description: `useTransition返回一个包含两个元素的数组：

**返回值详解**：
- **isPending**: 布尔值，表示是否有transition正在进行
- **startTransition**: 函数，用于标记状态更新为非紧急

**使用模式**：
\`\`\`typescript
const [isPending, startTransition] = useTransition();

// 紧急更新：立即执行，不能被中断
setInputValue(value);

// 非紧急更新：可以被中断，不阻塞用户交互
startTransition(() => {
  setFilteredResults(heavyComputation(value));
});
\`\`\``
  },

  notes: '注意：useTransition是React 18的新特性，需要在支持并发渲染的环境中使用。startTransition中的更新会被标记为低优先级，可能被用户交互打断',

  commonMistakes: [
    {
      title: '在startTransition中包含紧急更新',
      description: '不应将用户直接交互的状态更新放在startTransition中',
      wrongCode: `// ❌ 错误：输入框更新应该是紧急的
startTransition(() => {
  setInputValue(value); // 会导致输入延迟
  setFilteredResults(filter(value));
});`,
      correctCode: `// ✅ 正确：分离紧急和非紧急更新
setInputValue(value); // 紧急更新
startTransition(() => {
  setFilteredResults(filter(value)); // 非紧急更新
});`
    },
    {
      title: '过度使用startTransition',
      description: '不是所有状态更新都需要包装在startTransition中',
      wrongCode: `// ❌ 过度使用：简单更新不需要transition
startTransition(() => {
  setCount(count + 1);
});`,
      correctCode: `// ✅ 合理使用：只对耗时操作使用transition
setCount(count + 1); // 简单更新
startTransition(() => {
  setHeavyComputedData(expensiveCalculation());
});`
    }
  ],

  relatedHooks: ['useDeferredValue', 'useSyncExternalStore', 'useState'],
  category: 'react18',

  // 6个必选Tab的完整数据结构
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  knowledgeArchaeology,
  commonQuestions,
  
  // 7个可选Tab的数据结构（按照标准规范）
  performanceOptimization,
  learningPath,
  versionMigration,
  ecosystemTools,
  realWorldProjects,
  debuggingTips,
  essenceInsights
};

export default useTransitionData; 