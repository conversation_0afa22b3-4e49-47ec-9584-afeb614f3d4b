import { VersionMigration } from '../../../types/api';

const versionMigration: VersionMigration = {
  currentVersion: '18.3.1',
  
  migrationGuides: [
    {
      fromVersion: '17.x',
      toVersion: '18.0+',
      title: '从React 17迁移到React 18：启用并发特性',
      description: 'React 18引入了useTransition等并发特性，需要更新渲染方式和依赖',
      
      breakingChanges: [
        'ReactDOM.render被标记为过时，需要使用createRoot',
        '严格模式下的双重渲染检查更严格',
        '自动批处理可能改变状态更新的行为',
        '某些第三方库可能不兼容并发特性'
      ],
      
      steps: [
        {
          step: 1,
          title: '更新React版本',
          description: '将React和ReactDOM升级到18.x版本',
          codeExample: `// package.json
{
  "dependencies": {
-   "react": "^17.0.2",
-   "react-dom": "^17.0.2"
+   "react": "^18.3.1",
+   "react-dom": "^18.3.1"
  }
}

// 安装命令
npm install react@^18.3.1 react-dom@^18.3.1`
        },
        
        {
          step: 2,
          title: '更新根渲染方式',
          description: '使用createRoot替代ReactDOM.render启用并发特性',
          codeExample: `// ❌ React 17的渲染方式
import ReactDOM from 'react-dom';

ReactDOM.render(<App />, document.getElementById('root'));

// ✅ React 18的渲染方式
import { createRoot } from 'react-dom/client';

const container = document.getElementById('root');
const root = createRoot(container);
root.render(<App />);`
        },
        
        {
          step: 3,
          title: '逐步引入useTransition',
          description: '识别性能瓶颈，逐步使用useTransition优化',
          codeExample: `// React 17中的性能问题代码
function SearchResults({ query }) {
  const [results, setResults] = useState([]);
  
  useEffect(() => {
    // 这会阻塞UI
    const filtered = largeDataSet.filter(item => 
      item.title.includes(query)
    );
    setResults(filtered);
  }, [query]);
  
  return <ResultsList results={results} />;
}

// React 18使用useTransition优化
function SearchResults({ query }) {
  const [results, setResults] = useState([]);
  const [isPending, startTransition] = useTransition();
  
  useEffect(() => {
    startTransition(() => {
      const filtered = largeDataSet.filter(item => 
        item.title.includes(query)
      );
      setResults(filtered);
    });
  }, [query]);
  
  return (
    <>
      {isPending && <div>搜索中...</div>}
      <ResultsList results={results} />
    </>
  );
}`
        }
      ]
    },
    
    {
      fromVersion: '18.0-18.1',
      toVersion: '18.2+',
      title: 'React 18.2+的useTransition改进',
      description: '18.2版本修复了一些并发特性的bug并改进了性能',
      
      breakingChanges: [
        'transition的中断行为更加一致',
        'Suspense与transition的交互得到改进'
      ],
      
      steps: [
        {
          step: 1,
          title: '更新到18.2+版本',
          description: '获得更稳定的并发特性支持',
          codeExample: `// package.json更新
{
  "dependencies": {
-   "react": "^18.0.0",
-   "react-dom": "^18.0.0"
+   "react": "^18.3.1",
+   "react-dom": "^18.3.1"
  }
}`
        },
        
        {
          step: 2,
          title: '优化Suspense与transition的配合',
          description: '利用改进的Suspense行为',
          codeExample: `// 18.2+中更稳定的Suspense+transition模式
function TabContainer() {
  const [tab, setTab] = useState('home');
  const [isPending, startTransition] = useTransition();
  
  const switchTab = (newTab) => {
    startTransition(() => {
      setTab(newTab);
    });
  };
  
  return (
    <div>
      <TabButtons 
        currentTab={tab} 
        onSwitch={switchTab}
        isPending={isPending}  // 更准确的pending状态
      />
      <Suspense fallback={<TabSkeleton />}>
        <TabContent tab={tab} />
      </Suspense>
    </div>
  );
}`
        }
      ]
    }
  ],
  
  legacyAlternatives: [
    {
      scenario: 'React 17及以下版本的性能优化',
      description: '在不支持useTransition的版本中实现类似效果',
      approach: '使用setTimeout、requestIdleCallback或时间分片技术',
      codeExample: `// React 17中的性能优化方案
function useLegacyTransition() {
  const [isPending, setIsPending] = useState(false);
  
  const startTransition = useCallback((callback) => {
    setIsPending(true);
    
    // 使用setTimeout模拟低优先级更新
    setTimeout(() => {
      callback();
      setIsPending(false);
    }, 0);
  }, []);
  
  return [isPending, startTransition];
}

// 或者使用requestIdleCallback
function useIdleTransition() {
  const [isPending, setIsPending] = useState(false);
  
  const startTransition = useCallback((callback) => {
    setIsPending(true);
    
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        callback();
        setIsPending(false);
      });
    } else {
      setTimeout(() => {
        callback();
        setIsPending(false);
      }, 0);
    }
  }, []);
  
  return [isPending, startTransition];
}`
    },
    
    {
      scenario: '大量数据处理的时间分片',
      description: '在旧版本React中实现时间分片处理',
      approach: '手动将大任务分解成小片段',
      codeExample: `function useTimeSlicing(chunkSize = 1000) {
  const [isPending, setIsPending] = useState(false);
  
  const processWithTimeSlicing = useCallback((data, processor, callback) => {
    setIsPending(true);
    
    let index = 0;
    const chunks = [];
    
    function processChunk() {
      const chunk = data.slice(index, index + chunkSize);
      if (chunk.length === 0) {
        callback(chunks.flat());
        setIsPending(false);
        return;
      }
      
      const processed = processor(chunk);
      chunks.push(processed);
      index += chunkSize;
      
      // 让出控制权给浏览器
      setTimeout(processChunk, 0);
    }
    
    processChunk();
  }, [chunkSize]);
  
  return [isPending, processWithTimeSlicing];
}`
    }
  ],
  
  migrationTips: [
    '首先确保应用在React 18中正常运行，再逐步添加并发特性',
    '使用React Strict Mode检测并发安全问题',
    '从最明显的性能瓶颈开始应用useTransition',
    '测试在低性能设备上的表现，确保改进有效',
    '关注用户体验指标的变化，如FID、LCP等'
  ]
};

export default versionMigration; 