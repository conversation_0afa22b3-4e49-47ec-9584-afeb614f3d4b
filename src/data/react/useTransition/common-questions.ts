import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: 'transition-failures',
    question: "useTransition在什么情况下会失效或不起作用？",
    answer: `useTransition在某些情况下可能无法发挥预期效果：

## 主要失效场景

### 1. 非React 18环境
- **问题**：旧版本React不支持并发特性
- **解决**：升级到React 18+
- **检查方法**：\`console.log(React.version)\`

### 2. 非并发模式渲染
- **问题**：使用legacy模式渲染
- **解决**：使用\`createRoot\`替代\`ReactDOM.render\`

### 3. 同步阻塞操作
- **问题**：startTransition中包含同步的CPU密集型任务
- **示例**：大循环、同步计算、DOM操作

### 4. 异步操作包装
- **问题**：试图在startTransition中包装异步操作
- **原因**：startTransition只能标记同步的状态更新

## 实际排查指南

### 检查React版本和渲染模式
\`\`\`javascript
// 检查React版本
console.log('React版本:', React.version);

// 正确的渲染方式
import { createRoot } from 'react-dom/client';
const root = createRoot(document.getElementById('root'));
root.render(<App />);

// ❌ 错误的legacy模式
import ReactDOM from 'react-dom';
ReactDOM.render(<App />, document.getElementById('root'));
\`\`\`

### 优化CPU密集型任务
\`\`\`javascript
// ❌ 问题：同步密集计算
startTransition(() => {
  const result = [];
  for (let i = 0; i < 1000000; i++) {
    result.push(heavyCalculation(i)); // 阻塞主线程
  }
  setData(result);
});

// ✅ 解决：分片处理
startTransition(() => {
  const batchSize = 1000;
  const result = [];
  
  function processBatch(startIndex) {
    const endIndex = Math.min(startIndex + batchSize, 1000000);
    for (let i = startIndex; i < endIndex; i++) {
      result.push(heavyCalculation(i));
    }
    
    if (endIndex < 1000000) {
      setTimeout(() => processBatch(endIndex), 0);
    } else {
      setData(result);
    }
  }
  
  processBatch(0);
});
\`\`\``,
    tags: ['故障排查', '性能优化', '版本兼容']
  },

  {
    id: 'pending-stuck-true',
    question: "为什么我的useTransition有时候isPending一直是true？",
    answer: `isPending持续为true通常由以下原因造成：

## 常见原因分析

### 1. 无限循环更新
\`\`\`javascript
// ❌ 问题：依赖项变化导致无限循环
function ProblematicComponent() {
  const [data, setData] = useState([]);
  const [isPending, startTransition] = useTransition();
  
  useEffect(() => {
    startTransition(() => {
      setData(processData(data)); // data变化又触发useEffect
    });
  }, [data]); // ❌ 依赖data导致循环
}

// ✅ 解决：正确的依赖管理
function FixedComponent() {
  const [rawData, setRawData] = useState([]);
  const [processedData, setProcessedData] = useState([]);
  const [isPending, startTransition] = useTransition();
  
  useEffect(() => {
    startTransition(() => {
      setProcessedData(processData(rawData));
    });
  }, [rawData]); // ✅ 依赖原始数据
}
\`\`\`

### 2. 异步操作未正确处理
\`\`\`javascript
// ❌ 问题：异步操作在startTransition外部
function AsyncProblem() {
  const [data, setData] = useState(null);
  const [isPending, startTransition] = useTransition();
  
  const loadData = () => {
    startTransition(async () => {
      const result = await fetchData(); // ❌ 异步操作
      setData(result); // 永远不会执行
    });
  };
}

// ✅ 解决：正确处理异步操作
function AsyncFixed() {
  const [data, setData] = useState(null);
  const [isPending, startTransition] = useTransition();
  
  const loadData = async () => {
    const result = await fetchData(); // 异步操作在外部
    startTransition(() => {
      setData(result); // 只有状态更新在transition中
    });
  };
}
\`\`\`

### 3. 错误处理不当
\`\`\`javascript
// ❌ 问题：错误导致transition未完成
startTransition(() => {
  try {
    const result = riskyOperation();
    setData(result);
  } catch (error) {
    // 错误被忽略，transition可能未正确结束
  }
});

// ✅ 解决：确保transition总是完成
startTransition(() => {
  try {
    const result = riskyOperation();
    setData(result);
  } catch (error) {
    setData(null); // 确保有状态更新
    setError(error.message);
  }
});
\`\`\`

## 调试技巧

### 添加调试日志
\`\`\`javascript
function DebugTransition() {
  const [isPending, startTransition] = useTransition();
  
  useEffect(() => {
    console.log('isPending changed:', isPending);
  }, [isPending]);
  
  const handleUpdate = () => {
    console.log('Starting transition');
    startTransition(() => {
      console.log('Inside transition');
      setData(newData);
      console.log('Transition operation completed');
    });
  };
}
\`\`\`

### 使用React DevTools
- 在Profiler中查看transition的执行情况
- 检查是否有意外的重新渲染
- 观察组件的更新模式`,
    tags: ['调试技巧', '常见错误', '性能问题']
  },

  {
    id: 'vs-settimeout-raf',
    question: "useTransition和setTimeout/requestAnimationFrame有什么区别？",
    answer: `这三种方式都可以延迟执行，但工作原理和适用场景完全不同：

## 核心区别对比

### setTimeout
- **调度方式**：基于时间的延迟
- **优先级**：无优先级概念
- **中断性**：不可中断
- **适用场景**：简单的延迟执行

\`\`\`javascript
// setTimeout方式
setTimeout(() => {
  setData(newData); // 固定延迟，不考虑用户交互
}, 300);
\`\`\`

### requestAnimationFrame
- **调度方式**：与浏览器刷新率同步
- **优先级**：渲染优先级
- **中断性**：不可中断
- **适用场景**：动画和视觉更新

\`\`\`javascript
// requestAnimationFrame方式
requestAnimationFrame(() => {
  setData(newData); // 在下一帧执行
});
\`\`\`

### useTransition
- **调度方式**：基于优先级的智能调度
- **优先级**：支持多级优先级
- **中断性**：可被高优先级任务中断
- **适用场景**：复杂的状态更新优化

\`\`\`javascript
// useTransition方式
startTransition(() => {
  setData(newData); // 智能调度，可被中断
});
\`\`\`

## 实际应用场景对比

### 搜索功能实现
\`\`\`javascript
// 1. setTimeout实现
function SearchWithTimeout() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  
  const handleSearch = (value) => {
    setQuery(value);
    setTimeout(() => {
      setResults(search(value)); // 固定300ms延迟
    }, 300);
  };
  
  // 问题：用户快速输入时会有多个定时器
}

// 2. requestAnimationFrame实现
function SearchWithRAF() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  
  const handleSearch = (value) => {
    setQuery(value);
    requestAnimationFrame(() => {
      setResults(search(value)); // 下一帧执行
    });
  };
  
  // 问题：每次输入都会在下一帧更新，可能过于频繁
}

// 3. useTransition实现
function SearchWithTransition() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isPending, startTransition] = useTransition();
  
  const handleSearch = (value) => {
    setQuery(value); // 立即更新
    startTransition(() => {
      setResults(search(value)); // 智能调度
    });
  };
  
  // 优势：输入立即响应，搜索智能调度
}
\`\`\`

## 性能特征对比

| 特性 | setTimeout | requestAnimationFrame | useTransition |
|------|------------|----------------------|---------------|
| **响应性** | 延迟固定 | 延迟到下一帧 | 立即开始 |
| **可中断性** | 否 | 否 | 是 |
| **优先级** | 无 | 渲染优先级 | 多级优先级 |
| **用户体验** | 可能延迟 | 流畅但可能过度 | 最优 |
| **复杂度** | 低 | 低 | 中等 |

## 选择建议

### 使用setTimeout的场景
- 简单的延迟执行
- 不需要考虑优先级
- 兼容性要求高

### 使用requestAnimationFrame的场景
- 动画效果
- 需要与浏览器刷新同步
- 视觉更新优化

### 使用useTransition的场景
- 复杂的状态更新
- 需要保持用户交互响应性
- 现代React应用的性能优化

## 组合使用示例
\`\`\`javascript
function HybridApproach() {
  const [isPending, startTransition] = useTransition();
  
  const handleComplexUpdate = () => {
    // 立即反馈
    setLoadingState(true);
    
    // 使用useTransition处理数据更新
    startTransition(() => {
      setData(processData());
      setLoadingState(false);
    });
    
    // 使用requestAnimationFrame处理动画
    requestAnimationFrame(() => {
      startAnimation();
    });
  };
}
\`\`\``,
    tags: ['API对比', '性能优化', '最佳实践']
  },

  {
    id: 'ssr-considerations',
    question: "在服务端渲染(SSR)中使用useTransition需要注意什么？",
    answer: `在SSR环境中使用useTransition需要特别注意水合(hydration)和状态同步问题：

## 主要注意事项

### 1. isPending在服务端总是false
\`\`\`javascript
function SSRComponent() {
  const [isPending, startTransition] = useTransition();
  
  // 服务端：isPending总是false
  // 客户端：isPending会根据实际情况变化
  
  return (
    <div>
      {isPending && <div>Loading...</div>} {/* 服务端不会渲染 */}
      <Content />
    </div>
  );
}
\`\`\`

### 2. 水合不一致问题
\`\`\`javascript
// ❌ 问题：可能导致水合不匹配
function ProblematicSSR() {
  const [data, setData] = useState(null);
  const [isPending, startTransition] = useTransition();
  
  useEffect(() => {
    // 只在客户端执行
    startTransition(() => {
      setData(fetchInitialData());
    });
  }, []);
  
  // 服务端渲染null，客户端可能有数据
  return <div>{data ? <DataDisplay data={data} /> : <div>No data</div>}</div>;
}

// ✅ 解决：确保SSR和客户端一致
function SSRFriendly() {
  const [data, setData] = useState(null);
  const [isPending, startTransition] = useTransition();
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
    startTransition(() => {
      setData(fetchInitialData());
    });
  }, []);
  
  if (!isClient) {
    return <div>Loading...</div>; // 服务端固定渲染
  }
  
  return (
    <div>
      {isPending && <div>Updating...</div>}
      {data ? <DataDisplay data={data} /> : <div>No data</div>}
    </div>
  );
}
\`\`\`

### 3. 初始数据处理
\`\`\`javascript
// 推荐：使用初始props避免不一致
function SSRWithInitialData({ initialData }) {
  const [data, setData] = useState(initialData);
  const [isPending, startTransition] = useTransition();
  
  const updateData = (newData) => {
    startTransition(() => {
      setData(newData);
    });
  };
  
  return (
    <div>
      {isPending && <div>Updating...</div>}
      <DataDisplay data={data} />
      <button onClick={() => updateData(newDataValue)}>
        Update
      </button>
    </div>
  );
}

// 服务端渲染时传入初始数据
const serverRenderedHTML = renderToString(
  <SSRWithInitialData initialData={serverData} />
);
\`\`\`

## Next.js中的最佳实践

### 1. 在页面组件中使用
\`\`\`javascript
// pages/search.js
export default function SearchPage({ initialResults }) {
  const [results, setResults] = useState(initialResults);
  const [isPending, startTransition] = useTransition();
  
  const handleSearch = (query) => {
    startTransition(() => {
      setResults(searchData(query));
    });
  };
  
  return (
    <div>
      <SearchBox onSearch={handleSearch} />
      {isPending && <div>Searching...</div>}
      <ResultsList results={results} />
    </div>
  );
}

export async function getServerSideProps() {
  const initialResults = await getInitialSearchResults();
  return { props: { initialResults } };
}
\`\`\`

### 2. 使用动态导入
\`\`\`javascript
import dynamic from 'next/dynamic';

// 客户端组件，避免SSR问题
const TransitionComponent = dynamic(
  () => import('../components/TransitionComponent'),
  { ssr: false }
);

export default function Page() {
  return (
    <div>
      <ServerComponent />
      <TransitionComponent />
    </div>
  );
}
\`\`\`

## 通用SSR解决方案

### 条件渲染Helper
\`\`\`javascript
function useIsomorphicLayoutEffect(callback, deps) {
  const isServer = typeof window === 'undefined';
  const effect = isServer ? useEffect : useLayoutEffect;
  return effect(callback, deps);
}

function ClientOnly({ children, fallback = null }) {
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  return isClient ? children : fallback;
}

// 使用示例
function MyComponent() {
  const [isPending, startTransition] = useTransition();
  
  return (
    <div>
      <ServerContent />
      <ClientOnly fallback={<div>Loading...</div>}>
        {isPending && <div>Transition in progress...</div>}
        <TransitionContent />
      </ClientOnly>
    </div>
  );
}
\`\`\`

## 调试SSR问题

### 检查水合不匹配
\`\`\`javascript
// 在开发环境启用严格模式检查
if (process.env.NODE_ENV === 'development') {
  const root = createRoot(container);
  root.render(
    <StrictMode>
      <App />
    </StrictMode>
  );
}

// 或在Next.js中
module.exports = {
  reactStrictMode: true,
}
\`\`\``,
    tags: ['SSR', 'Next.js', '水合问题', '同构应用']
  }
];

export default commonQuestions; 