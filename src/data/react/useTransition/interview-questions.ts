import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: "useTransition的核心作用是什么？它解决了React中的什么问题？",
    answer: `useTransition的核心作用是实现**非阻塞式状态更新**，解决了React应用中的性能瓶颈问题。

## 解决的核心问题

### 1. 界面卡顿问题
- **问题**：大量数据更新时，整个应用失去响应
- **原因**：JavaScript单线程特性，长时间任务阻塞UI
- **解决**：将更新标记为非紧急，允许被中断

### 2. 用户体验问题  
- **问题**：用户输入延迟，点击无响应
- **原因**：所有更新同等优先级处理
- **解决**：优先级调度，用户交互优先

### 3. 性能优化问题
- **问题**：无法区分紧急和非紧急更新
- **原因**：传统React缺乏优先级概念
- **解决**：并发特性，智能调度

## 实际应用价值
- 搜索功能：输入流畅，结果异步更新
- 数据表格：排序筛选不阻塞其他操作
- 页面导航：切换页面时保持当前页面交互性`,
    
    difficulty: 'medium',
    frequency: 'high',
    category: '核心概念',
    
    code: `// 问题示例：传统方式的卡顿
function BadExample() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  
  const handleSearch = (value) => {
    setQuery(value); // 这两个更新会一起执行
    setResults(heavySearchOperation(value)); // 导致输入卡顿
  };
  
  return (
    <input 
      value={query}
      onChange={(e) => handleSearch(e.target.value)}
    />
  );
}

// 解决方案：使用useTransition
function GoodExample() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isPending, startTransition] = useTransition();
  
  const handleSearch = (value) => {
    setQuery(value); // 紧急更新：立即执行
    
    startTransition(() => {
      setResults(heavySearchOperation(value)); // 非紧急：可被中断
    });
  };
  
  return (
    <div>
      <input 
        value={query}
        onChange={(e) => handleSearch(e.target.value)}
      />
      {isPending && <div>搜索中...</div>}
    </div>
  );
}`
  },

  {
    id: 2,
    question: "useTransition返回的isPending和startTransition分别有什么作用？如何正确使用？",
    answer: `useTransition返回一个数组，包含两个关键元素，各有不同的作用：

## isPending的作用

### 1. 状态指示器
- **类型**：boolean值
- **含义**：指示是否有transition正在进行
- **更新时机**：startTransition开始时变true，完成时变false

### 2. 用户体验增强
- 显示加载状态
- 禁用某些交互
- 提供视觉反馈

## startTransition的作用

### 1. 优先级标记
- **类型**：函数 \`(callback: () => void) => void\`
- **作用**：将回调中的状态更新标记为低优先级
- **特点**：这些更新可以被用户交互中断

### 2. 非阻塞更新
- 包装的更新不会阻塞UI
- 允许React进行时间切片
- 支持并发渲染

## 正确使用原则

### ✅ 正确用法
1. **紧急vs非紧急分离**
2. **合理使用isPending**
3. **避免在startTransition中包含异步操作**

### ❌ 错误用法
1. 将用户交互更新放在startTransition中
2. 忽略isPending状态
3. 过度使用startTransition`,
    
    difficulty: 'medium',
    frequency: 'high',
    category: 'API详解',
    
    code: `function CorrectUsage() {
  const [input, setInput] = useState('');
  const [data, setData] = useState([]);
  const [isPending, startTransition] = useTransition();
  
  const handleInputChange = (value) => {
    // ✅ 正确：用户输入立即响应
    setInput(value);
    
    // ✅ 正确：数据处理可以延迟
    startTransition(() => {
      const processedData = expensiveProcessing(value);
      setData(processedData);
    });
  };
  
  return (
    <div>
      <input
        value={input}
        onChange={(e) => handleInputChange(e.target.value)}
        disabled={false} // ✅ 不要因为isPending就禁用输入
      />
      
      {/* ✅ 正确：使用isPending提供反馈 */}
      {isPending && (
        <div className="loading">
          处理中...
        </div>
      )}
      
      {/* ✅ 正确：显示数据，即使在更新中 */}
      <div className={isPending ? 'updating' : ''}>
        {data.map(item => <div key={item.id}>{item.name}</div>)}
      </div>
    </div>
  );
}

// ❌ 错误用法示例
function IncorrectUsage() {
  const [input, setInput] = useState('');
  const [isPending, startTransition] = useTransition();
  
  const handleInputChange = (value) => {
    // ❌ 错误：用户输入被延迟了
    startTransition(() => {
      setInput(value); // 这会导致输入卡顿
    });
  };
  
  return (
    <input
      value={input}
      onChange={(e) => handleInputChange(e.target.value)}
      disabled={isPending} // ❌ 错误：完全禁用交互
    />
  );
}`
  },

  {
    id: 3,
    question: "useTransition与useDeferredValue有什么区别？什么时候选择使用哪个？",
    answer: `useTransition和useDeferredValue都是React 18的并发特性，但用途和场景不同：

## 核心区别

### useTransition
- **控制对象**：状态更新的优先级
- **主动性**：主动标记更新为非紧急
- **适用场景**：你可以控制状态更新的代码

### useDeferredValue  
- **控制对象**：值的同步时机
- **被动性**：被动延迟值的更新
- **适用场景**：你无法控制值的来源（如props）

## 使用场景对比

### 使用useTransition的场景
1. **自己触发的更新**：搜索、筛选、排序
2. **事件处理**：用户交互触发的大量计算
3. **状态管理**：可以控制setState的调用

### 使用useDeferredValue的场景  
1. **外部传入的值**：props、context值
2. **第三方组件**：无法修改组件内部逻辑
3. **值的缓存**：延迟昂贵的计算

## 选择策略

### 优先选择useTransition
- 你控制状态更新
- 需要isPending状态
- 想要精确控制哪些更新被延迟

### 选择useDeferredValue
- 值来自外部（props/context）
- 无法修改更新逻辑
- 只需要延迟值的应用`,
    
    difficulty: 'high',
    frequency: 'medium',
    category: '对比分析',
    
    code: `// useTransition示例：控制更新
function SearchWithTransition() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [isPending, startTransition] = useTransition();
  
  const handleSearch = (value) => {
    setQuery(value); // 立即更新
    
    // 主动控制这个更新的优先级
    startTransition(() => {
      setResults(searchData(value));
    });
  };
  
  return (
    <div>
      <input onChange={(e) => handleSearch(e.target.value)} />
      {isPending && <div>搜索中...</div>}
      <ResultsList results={results} />
    </div>
  );
}

// useDeferredValue示例：延迟值
function SearchWithDeferredValue({ externalQuery }) {
  // 无法控制externalQuery的更新，只能延迟使用
  const deferredQuery = useDeferredValue(externalQuery);
  const results = useMemo(() => {
    return searchData(deferredQuery);
  }, [deferredQuery]);
  
  return (
    <div>
      <div>当前搜索: {externalQuery}</div>
      <div>实际查询: {deferredQuery}</div>
      <ResultsList results={results} />
    </div>
  );
}

// 组合使用示例
function CombinedExample() {
  const [query, setQuery] = useState('');
  const [isPending, startTransition] = useTransition();
  
  // 延迟的查询值
  const deferredQuery = useDeferredValue(query);
  
  const handleInputChange = (value) => {
    // 立即更新输入
    setQuery(value);
  };
  
  // 基于延迟值进行搜索
  const results = useMemo(() => {
    return searchData(deferredQuery);
  }, [deferredQuery]);
  
  return (
    <div>
      <input 
        value={query}
        onChange={(e) => handleInputChange(e.target.value)}
      />
      <div>
        输入值: {query}
        实际搜索: {deferredQuery}
      </div>
      <ResultsList results={results} />
    </div>
  );
}`
  }
];

export default interviewQuestions; 