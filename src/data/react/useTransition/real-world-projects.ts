import { RealWorldProjects } from '../../../types/api';

const realWorldProjects: RealWorldProjects = {
  projects: [
    {
      title: 'GitHub代码搜索',
      description: '模拟GitHub的代码搜索功能，支持大量结果的即时搜索和高亮',
      difficulty: 'intermediate',
      codeExample: `import React, { useState, useTransition, useMemo, useCallback } from 'react';

interface SearchResult {
  id: string;
  filename: string;
  path: string;
  content: string;
  language: string;
  matches: number;
}

function GitHubCodeSearch() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [totalResults, setTotalResults] = useState(0);
  const [isPending, startTransition] = useTransition();
  
  // 模拟大量代码数据
  const codeDatabase = useMemo(() => generateCodeDatabase(10000), []);
  
  const performCodeSearch = useCallback((searchQuery: string) => {
    if (!searchQuery.trim()) return [];
    
    return codeDatabase.filter(file => {
      const searchRegex = new RegExp(searchQuery, 'gi');
      const matches = (file.content.match(searchRegex) || []).length;
      if (matches > 0) {
        return { ...file, matches };
      }
      return false;
    }).sort((a, b) => b.matches - a.matches);
  }, [codeDatabase]);
  
  const handleSearch = (value: string) => {
    setQuery(value);  // 立即更新搜索框
    
    startTransition(() => {
      const searchResults = performCodeSearch(value);
      setResults(searchResults.slice(0, 50)); // 限制显示结果
      setTotalResults(searchResults.length);
    });
  };
  
  return (
    <div className="github-search">
      <div className="search-header">
        <input
          type="text"
          value={query}
          onChange={(e) => handleSearch(e.target.value)}
          placeholder="搜索代码..."
          className="search-input"
        />
        
        {isPending && (
          <div className="search-status">
            <span className="spinner"></span>
            搜索中...
          </div>
        )}
      </div>
      
      {query && !isPending && (
        <div className="results-summary">
          找到 {totalResults} 个结果
        </div>
      )}
      
      <div className="search-results">
        {results.map(result => (
          <CodeSearchResult 
            key={result.id} 
            result={result} 
            query={query}
          />
        ))}
      </div>
    </div>
  );
}`,
      highlights: [
        '即时搜索响应：搜索框输入无延迟',
        '大数据处理：高效处理万级代码文件',
        '智能排序：按匹配度排序结果',
        '语法高亮：实时高亮搜索关键词'
      ],
      useCase: '在大型代码库中搜索代码片段，需要保持搜索框的响应性',
      technologies: ['React 18', 'useTransition', 'debounce', '语法高亮'],
      complexity: 'medium'
    },
    
    {
      title: '数据可视化仪表板',
      description: '企业级数据仪表板，支持多图表实时更新和复杂筛选',
      difficulty: 'advanced',
      codeExample: `import React, { useState, useTransition, useEffect, useCallback } from 'react';

function DataVisualizationDashboard() {
  const [filters, setFilters] = useState({
    timeRange: '24h',
    category: 'all',
    region: 'global'
  });
  
  const [chartsData, setChartsData] = useState(new Map());
  const [isPending, startTransition] = useTransition();
  const [lastUpdate, setLastUpdate] = useState(Date.now());
  
  // 模拟实时数据流
  useEffect(() => {
    const interval = setInterval(() => {
      startTransition(() => {
        const newData = generateRealtimeData(filters);
        setChartsData(prev => new Map(prev).set('realtime', newData));
        setLastUpdate(Date.now());
      });
    }, 5000);
    
    return () => clearInterval(interval);
  }, [filters]);
  
  const handleFilterChange = useCallback((newFilters) => {
    // 立即更新筛选器UI
    setFilters(newFilters);
    
    // 重新计算所有图表数据
    startTransition(() => {
      const charts = ['revenue', 'users', 'conversion', 'geographic'];
      const newChartsData = new Map();
      
      charts.forEach(chartId => {
        const data = processChartData(chartId, newFilters);
        newChartsData.set(chartId, data);
      });
      
      setChartsData(newChartsData);
      setLastUpdate(Date.now());
    });
  }, []);
  
  return (
    <div className="dashboard">
      <DashboardHeader 
        filters={filters}
        onFilterChange={handleFilterChange}
        isPending={isPending}
        lastUpdate={lastUpdate}
      />
      
      <div className="charts-grid">
        <ChartContainer
          title="收入趋势"
          data={chartsData.get('revenue')}
          type="line"
          isPending={isPending}
        />
        
        <ChartContainer
          title="用户增长"
          data={chartsData.get('users')}
          type="bar"
          isPending={isPending}
        />
        
        <ChartContainer
          title="转化漏斗"
          data={chartsData.get('conversion')}
          type="area"
          isPending={isPending}
        />
        
        <ChartContainer
          title="地理分布"
          data={chartsData.get('geographic')}
          type="pie"
          isPending={isPending}
        />
      </div>
    </div>
  );
}`,
      highlights: [
        '实时数据更新：5秒自动刷新',
        '多图表协调：同时更新4个图表',
        '智能筛选：筛选器操作立即响应',
        '优雅降级：更新时保持旧数据可见'
      ],
      useCase: '处理实时数据流，多个图表同时更新，保持用户交互响应性',
      technologies: ['React 18', 'useTransition', 'Chart.js', 'WebSocket'],
      complexity: 'complex'
    },
    
    {
      title: '社交媒体动态流',
      description: '类似Twitter的无限滚动动态流，支持实时更新和内容过滤',
      difficulty: 'intermediate',
      codeExample: `import React, { useState, useTransition, useEffect, useCallback } from 'react';

function SocialMediaFeed() {
  const [posts, setPosts] = useState<Post[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<Post[]>([]);
  const [filter, setFilter] = useState({
    category: 'all',
    sortBy: 'newest'
  });
  const [isPending, startTransition] = useTransition();
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  
  // 模拟实时新动态
  useEffect(() => {
    const interval = setInterval(() => {
      const newPosts = generateNewPosts(3);
      setPosts(prev => [...newPosts, ...prev]);
      
      // 非紧急：更新过滤后的列表
      startTransition(() => {
        const filtered = applyFilters([...newPosts, ...posts], filter);
        setFilteredPosts(filtered);
      });
    }, 10000);
    
    return () => clearInterval(interval);
  }, [posts, filter]);
  
  const handleFilterChange = useCallback((newFilter) => {
    setFilter(newFilter);  // 立即更新筛选器
    
    startTransition(() => {
      const filtered = applyFilters(posts, newFilter);
      setFilteredPosts(filtered);
    });
  }, [posts]);
  
  const loadMorePosts = useCallback(async () => {
    setIsLoadingMore(true);
    
    try {
      const morePosts = await fetchMorePosts(posts.length);
      setPosts(prev => [...prev, ...morePosts]);
      
      startTransition(() => {
        const allPosts = [...posts, ...morePosts];
        const filtered = applyFilters(allPosts, filter);
        setFilteredPosts(filtered);
        setIsLoadingMore(false);
      });
    } catch (error) {
      setIsLoadingMore(false);
    }
  }, [posts, filter]);
  
  return (
    <div className="social-feed">
      <FeedHeader 
        filter={filter}
        onFilterChange={handleFilterChange}
        isPending={isPending}
      />
      
      <div className="posts-container">
        {isPending && (
          <div className="feed-updating">
            <span className="pulse-dot"></span>
            更新动态中...
          </div>
        )}
        
        <VirtualizedPostList
          posts={filteredPosts}
          onLoadMore={loadMorePosts}
          isLoading={isLoadingMore}
        />
      </div>
    </div>
  );
}`,
      highlights: [
        '无限滚动：流畅的内容加载',
        '实时更新：新动态自动出现',
        '智能过滤：类别和排序筛选',
        '虚拟化渲染：优化大量内容性能'
      ],
      useCase: '处理大量动态内容，支持无限滚动、实时更新和内容筛选',
      technologies: ['React 18', 'useTransition', '虚拟滚动', 'Intersection Observer'],
      complexity: 'medium'
    }
  ],
  
  patterns: [
    {
      name: '搜索即时响应模式',
      description: '保持搜索框即时响应，搜索结果异步更新',
      when: '需要处理大量搜索结果时',
      code: `const handleSearch = (value) => {
  setQuery(value);  // 立即更新输入框
  
  startTransition(() => {
    const results = performHeavySearch(value);
    setResults(results);
  });
};`
    },
    
    {
      name: '数据流更新模式',
      description: '实时数据流更新不阻塞用户交互',
      when: '处理实时数据更新时',
      code: `useEffect(() => {
  const interval = setInterval(() => {
    startTransition(() => {
      setData(fetchLatestData());
    });
  }, 1000);
  
  return () => clearInterval(interval);
}, []);`
    }
  ]
};

export default realWorldProjects; 