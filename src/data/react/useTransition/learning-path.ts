import { LearningPath } from '../../../types/api';

const learningPath: LearningPath = {
  prerequisites: [
    'React基础：熟悉useState、useEffect等基础Hooks',
    'JavaScript异步编程：理解Promise、async/await',
    '浏览器渲染机制：了解主线程阻塞概念',
    'React 18基础：了解并发特性和createRoot'
  ],
  
  skillLevel: 'intermediate',
  estimatedTime: '3-5天',
  
  learningSteps: [
    {
      step: 1,
      title: '理解并发渲染基础',
      description: '掌握React并发渲染的核心概念和useTransition的基本原理',
      resources: [
        'React官方文档 - useTransition',
        'React 18新特性详解视频',
        'JavaScript事件循环和渲染机制'
      ],
      practiceExercises: [
        {
          title: '基础transition示例',
          description: '实现一个简单的搜索框，体验transition的效果',
          difficulty: 'easy',
          estimatedTime: '30分钟'
        },
        {
          title: '对比有无transition的差异',
          description: '创建两个版本的列表过滤，感受性能差异',
          difficulty: 'easy', 
          estimatedTime: '45分钟'
        }
      ]
    },
    
    {
      step: 2,
      title: '掌握使用场景和最佳实践',
      description: '学会识别何时使用useTransition以及如何正确使用',
      resources: [
        'React性能优化最佳实践',
        'useTransition vs useDeferredValue对比',
        '并发特性性能分析工具'
      ],
      practiceExercises: [
        {
          title: '搜索功能优化',
          description: '实现一个复杂的搜索界面，包含多种筛选条件',
          difficulty: 'medium',
          estimatedTime: '2小时'
        },
        {
          title: '数据表格优化',
          description: '优化大数据量表格的排序和筛选性能',
          difficulty: 'medium',
          estimatedTime: '1.5小时'
        }
      ]
    },
    
    {
      step: 3,
      title: '高级应用和性能优化',
      description: '深入了解transition的内部机制，掌握高级优化技巧',
      resources: [
        'React Fiber架构深入解析',
        '时间切片和优先级调度原理',
        'React DevTools性能分析'
      ],
      practiceExercises: [
        {
          title: '实时数据流处理',
          description: '构建实时图表系统，处理高频数据更新',
          difficulty: 'hard',
          estimatedTime: '3小时'
        },
        {
          title: '复杂状态管理优化',
          description: '结合useReducer实现并发安全的状态管理',
          difficulty: 'hard',
          estimatedTime: '2.5小时'
        }
      ]
    },
    
    {
      step: 4,
      title: '实战项目应用',
      description: '在真实项目中应用useTransition，解决实际性能问题',
      resources: [
        '企业级React项目架构',
        '性能监控和调试技巧',
        '用户体验优化策略'
      ],
      practiceExercises: [
        {
          title: '完整项目重构',
          description: '选择一个现有项目，使用useTransition进行性能优化',
          difficulty: 'hard',
          estimatedTime: '1-2天'
        },
        {
          title: '性能对比分析',
          description: '使用性能分析工具量化优化效果',
          difficulty: 'medium',
          estimatedTime: '1小时'
        }
      ]
    }
  ],
  
  nextSteps: [
    '学习useDeferredValue与useTransition的配合使用',
    '深入理解React 18的其他并发特性',
    '掌握Suspense和并发渲染的结合',
    '学习服务端渲染中的并发特性应用',
    '探索React 19的新性能特性'
  ]
};

export default learningPath; 