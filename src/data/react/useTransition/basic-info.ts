import { BasicInfo } from '../../../types/api';

const basicInfo: BasicInfo = {
  definition: 'useTransition是React 18引入的并发特性核心Hook，用于将状态更新标记为非紧急的transitions（过渡）。它通过优先级调度机制，让高优先级的用户交互（如输入、点击）不被低优先级的更新（如大量数据渲染）阻塞，从而显著提升应用的响应性和用户体验。',
  
  introduction: 'useTransition解决了React应用中长期存在的性能问题：当大量数据更新时，整个应用会卡顿，用户交互失去响应。通过React 18的时间切片（Time Slicing）技术，useTransition让React能够在处理耗时操作时保持对用户输入的即时响应，实现了真正的并发渲染。这是React向现代化、高性能用户界面框架演进的重要里程碑。',

  syntax: `const [isPending, startTransition] = useTransition()

// 完整类型定义
function useTransition(): [
  isPending: boolean,
  startTransition: (callback: () => void) => void
]

// 使用模式
const [isPending, startTransition] = useTransition();

// 紧急更新（立即执行）
setInputValue(value);

// 非紧急更新（可被中断）
startTransition(() => {
  setExpensiveData(computeHeavyData(value));
});`,

  parameters: [
    {
      name: 'isPending',
      type: 'boolean',
      description: '指示是否有transition正在进行中',
      required: false,
      details: `isPending状态的特征：
- 在startTransition开始时变为true
- 在所有transition更新完成后变为false
- 可用于显示加载指示器或禁用UI
- 不会因为transition被中断而立即变为false`
    },
    {
      name: 'startTransition',
      type: '(callback: () => void) => void',
      description: '开始一个transition的函数',
      required: true,
      details: `startTransition函数的特征：
- 接收一个包含状态更新的回调函数
- 将回调中的更新标记为低优先级
- 这些更新可以被用户交互中断
- 回调函数应该是同步的，不能包含异步操作`
    }
  ],

  returnValue: {
    type: '[boolean, (callback: () => void) => void]',
    description: `useTransition返回一个数组，包含两个元素：

**返回值结构**：
\`\`\`typescript
const [isPending, startTransition] = useTransition();
\`\`\`

**使用场景对比**：
| 更新类型 | 使用方式 | 优先级 | 可中断性 |
|---------|---------|--------|----------|
| **紧急更新** | 直接调用setState | 高 | 不可中断 |
| **非紧急更新** | startTransition包装 | 低 | 可中断 |

**实际应用**：
- isPending：控制加载状态的显示
- startTransition：包装耗时的状态更新操作`
  },

  keyFeatures: [
    {
      feature: '并发渲染支持',
      description: 'React 18时间切片技术的核心实现',
      importance: 'critical',
      details: '通过将更新标记为不同优先级，实现真正的并发渲染，让应用在处理大量数据时保持响应性'
    },
    {
      feature: '优先级调度',
      description: '智能的更新优先级管理系统',
      importance: 'high',
      details: '高优先级更新（用户交互）会中断低优先级更新（数据渲染），确保用户体验流畅'
    },
    {
      feature: '非阻塞更新',
      description: 'startTransition中的更新不会阻塞UI',
      importance: 'high',
      details: '大量数据更新时，用户仍然可以正常输入、点击等操作，避免界面卡顿'
    },
    {
      feature: '加载状态管理',
      description: 'isPending提供内置的加载状态',
      importance: 'medium',
      details: '无需手动管理loading状态，React自动提供transition的进行状态'
    }
  ],

  commonUseCases: [
    {
      title: '大列表搜索过滤',
      description: '在大数据量列表中实现即时搜索，保持输入框响应性',
      code: `const [query, setQuery] = useState('');
const [results, setResults] = useState([]);
const [isPending, startTransition] = useTransition();

const handleSearch = (value) => {
  setQuery(value); // 立即更新输入框
  
  startTransition(() => {
    // 大量数据过滤不会阻塞输入
    const filtered = bigDataList.filter(item => 
      item.name.includes(value)
    );
    setResults(filtered);
  });
};`
    },
    {
      title: '路由切换优化',
      description: '页面导航时保持当前页面的交互性',
      code: `const [currentPage, setCurrentPage] = useState('home');
const [isPending, startTransition] = useTransition();

const navigateTo = (page) => {
  startTransition(() => {
    setCurrentPage(page); // 页面切换不阻塞当前交互
  });
};

return (
  <div>
    {isPending && <div>页面加载中...</div>}
    <button onClick={() => navigateTo('profile')}>
      个人中心
    </button>
  </div>
);`
    },
    {
      title: '复杂计算结果更新',
      description: '图表数据计算、统计分析等耗时操作',
      code: `const [chartData, setChartData] = useState([]);
const [isPending, startTransition] = useTransition();

const updateChart = (newParams) => {
  startTransition(() => {
    // 复杂计算不会卡住界面
    const processedData = expensiveDataProcessing(newParams);
    setChartData(processedData);
  });
};`
    }
  ],

  bestPractices: [
    '🎯 **紧急vs非紧急分离**：用户直接交互的更新保持紧急，后台计算使用transition',
    '⚡ **合理使用isPending**：用于显示加载状态，提升用户体验感知',
    '🚫 **避免过度包装**：简单的状态更新不需要startTransition',
    '🔄 **同步回调**：startTransition的回调必须是同步的，不能包含异步操作',
    '📱 **移动端优化**：在性能较弱的设备上，transition效果更明显',
    '🧪 **渐进式采用**：可以逐步将耗时更新迁移到startTransition',
    '📊 **性能监控**：使用React DevTools监控transition的效果',
    '🔧 **条件性使用**：根据数据量大小决定是否使用transition'
  ],

  warnings: [
    '🚨 **React 18专属**：只能在React 18+的并发模式下使用',
    '⚠️ **SSR注意事项**：服务端渲染时isPending始终为false',
    '🔄 **异步操作限制**：startTransition回调中不能直接包含异步操作',
    '📈 **内存考虑**：被中断的更新仍会消耗内存，需要合理管理',
    '🎭 **开发vs生产**：开发模式下可能看不到明显效果，生产环境更明显',
    '🔍 **调试复杂性**：transition的中断和恢复增加了调试难度'
  ],

  notes: [
    'useTransition是React并发特性的核心，标志着React向现代化框架的重要演进',
    '配合useDeferredValue使用可以获得更好的性能优化效果',
    'React 18的Automatic Batching与useTransition协同工作',
    '在大型应用中，合理使用useTransition可以显著提升用户体验'
  ]
};

export default basicInfo; 