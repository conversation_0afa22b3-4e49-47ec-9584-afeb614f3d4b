import { EcosystemTools } from '../../../types/api';

const ecosystemTools: EcosystemTools = {
  libraries: [
    {
      name: 'React DevTools',
      description: 'React官方开发者工具，支持并发特性的性能分析',
      category: 'developer-tools',
      website: 'https://react.dev/learn/react-developer-tools',
      features: [
        'Profiler中的并发特性可视化',
        'Transition执行时间分析',
        '组件渲染优先级显示',
        '中断和恢复过程监控'
      ],
      usageExample: `// 在React DevTools Profiler中：
// 1. 开启"Record why each component rendered"
// 2. 执行包含useTransition的操作
// 3. 查看Timeline中的transition标记
// 4. 分析被中断的渲染和恢复过程`
    },
    
    {
      name: '@testing-library/react',
      description: '支持测试并发特性的React测试库',
      category: 'testing',
      website: 'https://testing-library.com/docs/react-testing-library/intro/',
      features: [
        'act()函数支持并发更新',
        'waitFor对transition的支持',
        '异步状态更新测试',
        'isPending状态验证'
      ],
      usageExample: `import { render, screen, act, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

test('useTransition搜索功能', async () => {
  render(<SearchComponent />);
  
  const input = screen.getByRole('textbox');
  
  await act(async () => {
    await userEvent.type(input, 'search term');
  });
  
  // 等待transition完成
  await waitFor(() => {
    expect(screen.getByText('结果列表')).toBeInTheDocument();
  });
});`
    },
    
    {
      name: 'React Suspense',
      description: 'React内置的异步组件加载机制，与useTransition协同工作',
      category: 'react-feature',
      website: 'https://react.dev/reference/react/Suspense',
      features: [
        '与transition结合的数据获取',
        '延迟显示loading状态',
        '优雅的错误边界处理',
        '代码分割和懒加载支持'
      ],
      usageExample: `import { Suspense, useTransition } from 'react';

function App() {
  const [isPending, startTransition] = useTransition();
  const [tab, setTab] = useState('home');
  
  const switchTab = (newTab) => {
    startTransition(() => {
      setTab(newTab);  // 切换可能触发Suspense的组件
    });
  };
  
  return (
    <div>
      <TabButtons onSwitch={switchTab} />
      <Suspense fallback={<Loading />}>
        <TabContent tab={tab} />
      </Suspense>
      {isPending && <div>切换中...</div>}
    </div>
  );
}`
    }
  ],
  
  tools: [
    {
      name: 'React Performance Timeline',
      description: '浏览器内置的性能分析工具，可视化React渲染性能',
      category: 'performance',
      purpose: '分析transition的执行性能和中断情况',
      setupInstructions: [
        '打开Chrome DevTools的Performance标签',
        '勾选"Web Vitals"和"User Timing"',
        '开始录制并触发包含transition的操作',
        '在Timeline中查看React相关的性能标记'
      ]
    },
    
    {
      name: 'React Strict Mode',
      description: 'React开发模式，帮助发现并发相关的问题',
      category: 'development',
      purpose: '检测不符合并发安全的代码模式',
      setupInstructions: [
        '在根组件外包装<React.StrictMode>',
        '观察控制台中的双重渲染警告',
        '修复副作用相关的问题',
        '确保组件在并发环境下正常工作'
      ]
    },
    
    {
      name: 'Web Vitals',
      description: 'Google的用户体验性能指标库',
      category: 'performance',
      purpose: '量化useTransition对用户体验的改善效果',
      setupInstructions: [
        'npm install web-vitals',
        '集成到应用中监控FID、LCP等指标',
        '对比使用transition前后的性能数据',
        '设置性能预算和报警'
      ]
    }
  ],
  
  relatedHooks: [
    {
      name: 'useDeferredValue',
      description: '延迟更新值，与useTransition配合使用',
      relationship: '互补关系：useDeferredValue处理值延迟，useTransition处理更新延迟',
      combinedUsage: `const [query, setQuery] = useState('');
const [isPending, startTransition] = useTransition();
const deferredQuery = useDeferredValue(query);

// query立即更新UI，deferredQuery延迟触发搜索
const results = useMemo(() => {
  return search(deferredQuery);
}, [deferredQuery]);`
    },
    
    {
      name: 'useId',
      description: '生成唯一ID，在并发渲染中保持稳定',
      relationship: '支持关系：在并发环境下提供稳定的组件ID',
      combinedUsage: `function SearchableList() {
  const id = useId();
  const [isPending, startTransition] = useTransition();
  
  // 即使在transition期间，ID也保持稳定
  return (
    <div>
      <label htmlFor={\`\${id}-search\`}>搜索:</label>
      <input id={\`\${id}-search\`} />
    </div>
  );
}`
    }
  ]
};

export default ecosystemTools; 