import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  optimizationStrategies: [
    {
      title: "识别真正需要优化的计算",
      description: "不是所有计算都需要useMemo，学会识别真正的性能瓶颈",
      techniques: [
        {
          name: "使用React DevTools Profiler",
          description: "通过Profiler找出耗时的组件和渲染",
          impact: "high",
          difficulty: "easy",
          code: `// 1. 使用Profiler API测量
import { Profiler } from 'react';

function App() {
  const onRender = (id, phase, actualDuration) => {
    console.log(id + " (" + phase + ") took " + actualDuration + "ms");
  };
  
  return (
    <Profiler id="ExpensiveComponent" onRender={onRender}>
      <ExpensiveComponent data={data} />
    </Profiler>
  );
}

// 2. 在组件内测量具体计算
function ExpensiveComponent({ data }) {
  // 测量计算时间
  const processedData = useMemo(() => {
    const start = performance.now();
    
    const result = data
      .filter(item => complexFilter(item))
      .map(item => transform(item))
      .sort((a, b) => customSort(a, b));
    
    const end = performance.now();
    console.log("Processing took " + (end - start) + "ms");
    
    // 只有超过16ms（一帧）的计算才真正需要优化
    return result;
  }, [data]);
  
  return <DataList items={processedData} />;
}`
        },
        {
          name: "基准测试对比",
          description: "对比使用和不使用useMemo的性能差异",
          impact: "medium",
          difficulty: "medium",
          code: `// 创建性能对比Hook
function usePerformanceComparison() {
  const [metrics, setMetrics] = useState({
    withMemo: { renders: 0, totalTime: 0 },
    withoutMemo: { renders: 0, totalTime: 0 }
  });
  
  // 不使用useMemo的版本
  const withoutMemo = (data) => {
    const start = performance.now();
    const result = expensiveCalculation(data);
    const duration = performance.now() - start;
    
    setMetrics(prev => ({
      ...prev,
      withoutMemo: {
        renders: prev.withoutMemo.renders + 1,
        totalTime: prev.withoutMemo.totalTime + duration
      }
    }));
    
    return result;
  };
  
  // 使用useMemo的版本
  const withMemo = useMemo(() => {
    const start = performance.now();
    const result = expensiveCalculation(data);
    const duration = performance.now() - start;
    
    setMetrics(prev => ({
      ...prev,
      withMemo: {
        renders: prev.withMemo.renders + 1,
        totalTime: prev.withMemo.totalTime + duration
      }
    }));
    
    return result;
  }, [data]);
  
  return { withMemo, withoutMemo, metrics };
}`
        }
      ]
    },
    {
      title: "优化依赖项以减少重新计算",
      description: "通过优化依赖项设计，减少不必要的缓存失效",
      techniques: [
        {
          name: "使用稳定的依赖项",
          description: "避免使用每次渲染都会变化的对象或数组作为依赖",
          impact: "high",
          difficulty: "medium",
          code: `// ❌ 问题：不稳定的依赖项
function BadExample({ users, config }) {
  // config.filters 可能每次都是新对象
  const filteredUsers = useMemo(() => {
    return users.filter(user => 
      matchesFilters(user, config.filters)
    );
  }, [users, config.filters]); // 可能频繁重新计算
  
  // 内联对象作为依赖
  const processedData = useMemo(() => {
    return processData(data, { sortBy: 'name', order: 'asc' });
  }, [data, { sortBy: 'name', order: 'asc' }]); // 总是重新计算！
}

// ✅ 解决方案1：使用具体的值作为依赖
function GoodExample1({ users, config }) {
  const filteredUsers = useMemo(() => {
    return users.filter(user => 
      matchesFilters(user, config.filterType, config.filterValue)
    );
  }, [users, config.filterType, config.filterValue]); // 更稳定
}

// ✅ 解决方案2：在上层组件稳定化对象
function ParentComponent() {
  // 使用useMemo稳定化配置对象
  const filters = useMemo(() => ({
    type: 'active',
    value: true
  }), []); // 只创建一次
  
  const config = useMemo(() => ({
    filters,
    sortBy: 'name'
  }), [filters]); // filters稳定，config也稳定
  
  return <ChildComponent users={users} config={config} />;
}

// ✅ 解决方案3：使用引用稳定的常量
const SORT_CONFIG = { sortBy: 'name', order: 'asc' };

function OptimizedComponent({ data }) {
  const processedData = useMemo(() => {
    return processData(data, SORT_CONFIG);
  }, [data]); // SORT_CONFIG是常量，不需要作为依赖
}`
        },
        {
          name: "依赖项粒度优化",
          description: "选择合适的依赖项粒度，避免过粗或过细",
          impact: "medium",
          difficulty: "medium",
          code: `// ❌ 粒度过粗：整个对象作为依赖
function CoarseGrained({ userSettings }) {
  const theme = useMemo(() => {
    return generateTheme(userSettings);
  }, [userSettings]); // userSettings任何属性变化都会重新计算
}

// ✅ 适当的粒度：只依赖相关属性
function FineGrained({ userSettings }) {
  const theme = useMemo(() => {
    return generateTheme({
      darkMode: userSettings.darkMode,
      primaryColor: userSettings.primaryColor,
      fontSize: userSettings.fontSize
    });
  }, [
    userSettings.darkMode,
    userSettings.primaryColor,
    userSettings.fontSize
  ]); // 只有主题相关的设置变化才重新计算
}

// 使用解构优化可读性
function Destructured({ userSettings }) {
  const { darkMode, primaryColor, fontSize } = userSettings;
  
  const theme = useMemo(() => {
    return generateTheme({ darkMode, primaryColor, fontSize });
  }, [darkMode, primaryColor, fontSize]); // 更清晰
}

// 对于复杂对象，考虑序列化
function ComplexDependency({ complexFilter }) {
  // 使用序列化比较复杂对象
  const filterKey = JSON.stringify(complexFilter);
  
  const filtered = useMemo(() => {
    return data.filter(item => matchesComplexFilter(item, complexFilter));
  }, [data, filterKey]); // 只有filter内容变化才重新计算
}`
        }
      ]
    },
    {
      title: "结合React.memo优化子组件",
      description: "useMemo与React.memo配合使用，实现最大化的性能优化",
      techniques: [
        {
          name: "优化props传递",
          description: "使用useMemo确保传递给memo组件的props稳定",
          impact: "high",
          difficulty: "easy",
          code: `// 子组件使用React.memo
const ExpensiveChild = React.memo(({ data, config, onUpdate }) => {
  console.log('ExpensiveChild render');
  // 复杂的渲染逻辑
  return <ComplexVisualization data={data} config={config} />;
});

// ❌ 没有优化的父组件
function UnoptimizedParent({ rawData }) {
  const [filter, setFilter] = useState('');
  
  // 每次渲染都创建新数组
  const filteredData = rawData.filter(item => 
    item.name.includes(filter)
  );
  
  // 每次渲染都创建新对象
  const config = {
    theme: 'dark',
    showLabels: true
  };
  
  // 每次渲染都创建新函数
  const handleUpdate = (item) => {
    console.log('Updated:', item);
  };
  
  // ExpensiveChild每次都会重新渲染！
  return (
    <ExpensiveChild 
      data={filteredData}
      config={config}
      onUpdate={handleUpdate}
    />
  );
}

// ✅ 优化后的父组件
function OptimizedParent({ rawData }) {
  const [filter, setFilter] = useState('');
  
  // 使用useMemo缓存过滤结果
  const filteredData = useMemo(() => {
    console.log('Filtering data...');
    return rawData.filter(item => 
      item.name.includes(filter)
    );
  }, [rawData, filter]);
  
  // 使用useMemo保持config引用稳定
  const config = useMemo(() => ({
    theme: 'dark',
    showLabels: true
  }), []); // 不依赖任何值，永远不变
  
  // 使用useCallback保持函数引用稳定
  const handleUpdate = useCallback((item) => {
    console.log('Updated:', item);
  }, []); // 不依赖任何值
  
  // ExpensiveChild只在必要时重新渲染
  return (
    <ExpensiveChild 
      data={filteredData}
      config={config}
      onUpdate={handleUpdate}
    />
  );
}`
        },
        {
          name: "组件拆分策略",
          description: "将频繁更新和稳定的部分拆分成不同组件",
          impact: "medium",
          difficulty: "medium",
          code: `// ❌ 单一大组件，任何状态变化都会触发整体重新计算
function MonolithicComponent({ data }) {
  const [search, setSearch] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [selectedId, setSelectedId] = useState(null);
  
  // 所有计算都在一个组件中
  const filtered = useMemo(() => 
    data.filter(item => item.name.includes(search)),
    [data, search]
  );
  
  const sorted = useMemo(() => 
    [...filtered].sort((a, b) => compare(a, b, sortBy)),
    [filtered, sortBy]
  );
  
  const stats = useMemo(() => 
    calculateStatistics(sorted),
    [sorted]
  );
  
  return (
    <div>
      <SearchBar value={search} onChange={setSearch} />
      <SortControl value={sortBy} onChange={setSortBy} />
      <Statistics data={stats} />
      <DataList 
        items={sorted} 
        selectedId={selectedId}
        onSelect={setSelectedId}
      />
    </div>
  );
}

// ✅ 拆分后的组件结构
// 搜索组件 - 独立管理搜索状态
const SearchableList = React.memo(({ data }) => {
  const [search, setSearch] = useState('');
  
  const filtered = useMemo(() => 
    data.filter(item => item.name.includes(search)),
    [data, search]
  );
  
  return (
    <>
      <SearchBar value={search} onChange={setSearch} />
      <SortableList data={filtered} />
    </>
  );
});

// 排序组件 - 独立管理排序状态
const SortableList = React.memo(({ data }) => {
  const [sortBy, setSortBy] = useState('name');
  
  const sorted = useMemo(() => 
    [...data].sort((a, b) => compare(a, b, sortBy)),
    [data, sortBy]
  );
  
  return (
    <>
      <SortControl value={sortBy} onChange={setSortBy} />
      <DataDisplay data={sorted} />
    </>
  );
});

// 统计组件 - 只依赖数据
const Statistics = React.memo(({ data }) => {
  const stats = useMemo(() => 
    calculateStatistics(data),
    [data]
  );
  
  return <StatsDisplay stats={stats} />;
});

// 主组件 - 组合各个部分
function OptimizedComponent({ data }) {
  return (
    <div>
      <Statistics data={data} />
      <SearchableList data={data} />
    </div>
  );
}`
        }
      ]
    },
    {
      title: "处理大数据集的优化策略",
      description: "针对大数据集的特殊优化技术",
      techniques: [
        {
          name: "分页和虚拟化",
          description: "避免一次性处理所有数据",
          impact: "high",
          difficulty: "hard",
          code: `// 使用虚拟化只渲染可见部分
import { FixedSizeList } from 'react-window';

function VirtualizedList({ data }) {
  // 只对可见数据进行昂贵计算
  const Row = ({ index, style }) => {
    const item = data[index];
    
    // 对单个项目的计算
    const processedItem = useMemo(() => {
      return expensiveItemProcess(item);
    }, [item]);
    
    return (
      <div style={style}>
        <ItemDisplay item={processedItem} />
      </div>
    );
  };
  
  return (
    <FixedSizeList
      height={600}
      itemCount={data.length}
      itemSize={50}
      width="100%"
    >
      {Row}
    </FixedSizeList>
  );
}

// 分页处理
function PaginatedData({ allData }) {
  const [page, setPage] = useState(0);
  const pageSize = 50;
  
  // 只处理当前页的数据
  const currentPageData = useMemo(() => {
    const start = page * pageSize;
    const end = start + pageSize;
    return allData.slice(start, end);
  }, [allData, page, pageSize]);
  
  // 只对当前页数据进行昂贵计算
  const processedData = useMemo(() => {
    return currentPageData.map(item => expensiveProcess(item));
  }, [currentPageData]);
  
  return (
    <>
      <DataList items={processedData} />
      <Pagination 
        currentPage={page}
        totalPages={Math.ceil(allData.length / pageSize)}
        onPageChange={setPage}
      />
    </>
  );
}`
        },
        {
          name: "Web Worker处理",
          description: "将昂贵计算移到Web Worker",
          impact: "high",
          difficulty: "hard",
          code: `// 创建Web Worker处理复杂计算
// worker.js
self.addEventListener('message', (e) => {
  const { data, operation } = e.data;
  
  let result;
  switch (operation) {
    case 'sort':
      result = data.sort((a, b) => complexSort(a, b));
      break;
    case 'filter':
      result = data.filter(item => complexFilter(item));
      break;
    case 'transform':
      result = data.map(item => complexTransform(item));
      break;
  }
  
  self.postMessage({ result });
});

// React组件中使用
function HeavyDataProcessor({ rawData }) {
  const [processedData, setProcessedData] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const workerRef = useRef();
  
  useEffect(() => {
    // 创建Worker
    workerRef.current = new Worker('/worker.js');
    
    workerRef.current.onmessage = (e) => {
      setProcessedData(e.data.result);
      setIsProcessing(false);
    };
    
    return () => workerRef.current.terminate();
  }, []);
  
  // 使用useMemo避免重复发送到Worker
  const processData = useMemo(() => {
    return () => {
      setIsProcessing(true);
      workerRef.current.postMessage({
        data: rawData,
        operation: 'transform'
      });
    };
  }, [rawData]);
  
  useEffect(() => {
    processData();
  }, [processData]);
  
  if (isProcessing) {
    return <LoadingSpinner />;
  }
  
  return <DataDisplay data={processedData} />;
}`
        }
      ]
    }
  ],
  
  performanceMetrics: {
    renderCount: {
      tool: "React DevTools Profiler",
      description: "监控组件渲染次数和时长",
      example: "查看Ranked视图找出渲染最频繁的组件"
    },
    computationTime: {
      tool: "Performance API",
      description: "精确测量计算时间",
      example: "使用performance.now()测量useMemo内的计算耗时"
    },
    memoryUsage: {
      tool: "Chrome DevTools Memory Profiler",
      description: "监控useMemo缓存的内存占用",
      example: "对比使用useMemo前后的内存快照"
    }
  },
  
  bestPractices: [
    "先测量，后优化 - 使用工具确认性能瓶颈",
    "只缓存真正昂贵的计算（通常超过1-2ms）",
    "保持依赖项稳定，减少缓存失效",
    "配合React.memo使用，优化整个组件树",
    "考虑内存成本，避免缓存过大的对象",
    "对于超大数据集，考虑分页、虚拟化或Web Worker"
  ],
  
  commonPitfalls: [
    {
      issue: "过度使用useMemo",
      cause: "对简单计算也使用useMemo",
      solution: "只对真正昂贵的计算使用，通过性能测试确认"
    },
    {
      issue: "依赖项设置错误导致频繁重算",
      cause: "使用不稳定的对象或函数作为依赖",
      solution: "使用基础类型或稳定的引用作为依赖"
    },
    {
      issue: "内存泄漏",
      cause: "缓存了大量不再使用的数据",
      solution: "及时清理不需要的缓存，考虑使用WeakMap"
    },
    {
      issue: "阻塞主线程",
      cause: "在useMemo中执行超长时间的同步计算",
      solution: "考虑分片计算、Web Worker或异步处理"
    }
  ]
};

export default performanceOptimization; 