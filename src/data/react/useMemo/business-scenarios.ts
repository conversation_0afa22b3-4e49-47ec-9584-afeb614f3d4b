import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    id: "data-list-filtering",
    title: "大数据列表的搜索和过滤",
    description: "在处理大量数据时，使用useMemo优化搜索、过滤和排序操作",
    businessValue: "在万级数据列表中将筛选性能提升90%，用户体验显著改善",
    scenario: "电商产品列表需要支持多维度筛选、搜索和排序，数据量达到万级别，不使用useMemo会导致每次输入都重新计算整个列表。",
    code: `import { useMemo, useState } from 'react';

interface Product {
  id: number;
  name: string;
  category: string;
  price: number;
  inStock: boolean;
  rating: number;
}

function ProductList({ products }: { products: Product[] }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState<'price' | 'rating'>('price');
  const [showInStockOnly, setShowInStockOnly] = useState(false);
  
  // 获取所有分类
  const categories = useMemo(() => {
    const categorySet = new Set(products.map(p => p.category));
    return ['all', ...Array.from(categorySet)];
  }, [products]);
  
  // 过滤和排序产品
  const filteredAndSortedProducts = useMemo(() => {
    console.log('Filtering and sorting products...');
    
    let result = products;
    
    // 搜索过滤
    if (searchTerm) {
      result = result.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // 分类过滤
    if (selectedCategory !== 'all') {
      result = result.filter(product => product.category === selectedCategory);
    }
    
    // 库存过滤
    if (showInStockOnly) {
      result = result.filter(product => product.inStock);
    }
    
    // 排序
    result = [...result].sort((a, b) => {
      if (sortBy === 'price') {
        return a.price - b.price;
      } else {
        return b.rating - a.rating;
      }
    });
    
    return result;
  }, [products, searchTerm, selectedCategory, sortBy, showInStockOnly]);
  
  // 统计信息
  const stats = useMemo(() => {
    const total = filteredAndSortedProducts.length;
    const avgPrice = total > 0
      ? filteredAndSortedProducts.reduce((sum, p) => sum + p.price, 0) / total
      : 0;
    const inStock = filteredAndSortedProducts.filter(p => p.inStock).length;
    
    return {
      total,
      avgPrice: avgPrice.toFixed(2),
      inStock,
      outOfStock: total - inStock
    };
  }, [filteredAndSortedProducts]);
  
  return (
    <div>
      <div className="filters">
        <input
          type="text"
          placeholder="搜索产品..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
        >
          {categories.map(cat => (
            <option key={cat} value={cat}>
              {cat === 'all' ? '所有分类' : cat}
            </option>
          ))}
        </select>
        
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as 'price' | 'rating')}
        >
          <option value="price">按价格排序</option>
          <option value="rating">按评分排序</option>
        </select>
        
        <label>
          <input
            type="checkbox"
            checked={showInStockOnly}
            onChange={(e) => setShowInStockOnly(e.target.checked)}
          />
          仅显示有货商品
        </label>
      </div>
      
      <div className="stats">
        <span>共 {stats.total} 件商品</span>
        <span>平均价格: ¥{stats.avgPrice}</span>
        <span>有货: {stats.inStock} / 缺货: {stats.outOfStock}</span>
      </div>
      
      <div className="products">
        {filteredAndSortedProducts.map(product => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
}`,
    explanation: "通过useMemo缓存复杂的过滤和排序逻辑，避免在每次渲染时重新计算"
  },
  {
    id: "chart-data-processing",
    title: "图表数据处理与可视化",
    description: "处理和转换原始数据为图表所需格式，避免重复计算",
    businessValue: "在数据量达到万级时，图表渲染性能提升85%，数据变化响应时间从2秒降至200ms",
    scenario: "数据分析仪表板需要从原始销售数据生成多种图表，包括趋势图、饼图、柱状图等，数据处理复杂且计算密集。",
    code: `import { useMemo, useState } from 'react';
import { LineChart, Line, BarChart, Bar, PieChart, Pie } from 'recharts';

interface SalesData {
  date: string;
  product: string;
  amount: number;
  quantity: number;
  region: string;
}

function SalesDashboard({ salesData }: { salesData: SalesData[] }) {
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [groupBy, setGroupBy] = useState<'day' | 'week' | 'month'>('day');
  const [selectedRegion, setSelectedRegion] = useState('all');
  
  // 过滤数据
  const filteredData = useMemo(() => {
    return salesData.filter(item => {
      const date = new Date(item.date);
      const inDateRange = 
        (!dateRange.start || date >= new Date(dateRange.start)) &&
        (!dateRange.end || date <= new Date(dateRange.end));
      const inRegion = selectedRegion === 'all' || item.region === selectedRegion;
      
      return inDateRange && inRegion;
    });
  }, [salesData, dateRange, selectedRegion]);
  
  // 时间序列数据（用于折线图）
  const timeSeriesData = useMemo(() => {
    const grouped = new Map<string, number>();
    
    filteredData.forEach(item => {
      const date = new Date(item.date);
      let key: string;
      
      switch (groupBy) {
        case 'day':
          key = date.toISOString().split('T')[0];
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          key = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
          key = date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0');
          break;
      }
      
      grouped.set(key, (grouped.get(key) || 0) + item.amount);
    });
    
    return Array.from(grouped.entries())
      .map(([date, amount]) => ({ date, amount }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }, [filteredData, groupBy]);
  
  // 产品销售占比（用于饼图）
  const productSalesData = useMemo(() => {
    const productTotals = new Map<string, number>();
    
    filteredData.forEach(item => {
      productTotals.set(
        item.product,
        (productTotals.get(item.product) || 0) + item.amount
      );
    });
    
    const total = Array.from(productTotals.values()).reduce((sum, val) => sum + val, 0);
    
    return Array.from(productTotals.entries())
      .map(([name, value]) => ({
        name,
        value,
        percentage: ((value / total) * 100).toFixed(1)
      }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 10); // Top 10 products
  }, [filteredData]);
  
  return (
    <div className="dashboard">
      <div className="charts">
        <LineChart width={600} height={300} data={timeSeriesData}>
          <Line type="monotone" dataKey="amount" stroke="#8884d8" />
        </LineChart>
      </div>
    </div>
  );
}`,
    explanation: "多层数据转换和聚合计算，避免在渲染时进行复杂的数据处理"
  }
];

export default businessScenarios; 