import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "useMemo是React提供的性能优化Hook，用于缓存计算结果。它接收一个创建函数和依赖项数组，只有当依赖项发生变化时才重新计算。这对于避免在每次渲染时都进行昂贵的计算非常有用。",
  
  syntax: `const memoizedValue = useMemo(() => {
  return expensiveCalculation(a, b);
}, [a, b]);`,

  overview: {
    description: "useMemo是React提供的性能优化Hook，用于缓存计算结果。它接收一个创建函数和依赖项数组，只有当依赖项发生变化时才重新计算。这对于避免在每次渲染时都进行昂贵的计算非常有用。",
    
    keyFeatures: [
      "缓存计算结果，避免重复计算",
      "只在依赖项变化时重新计算",
      "优化组件性能，减少不必要的计算开销",
      "可以用来缓存对象和数组，保持引用稳定",
      "与React.memo配合使用效果更佳",
      "是一种性能优化手段，不是语义上的保证"
    ]
  },
  
  basicUsage: {
    description: "useMemo的基本用法展示如何缓存计算结果和保持引用稳定",
    examples: [
      {
        title: "缓存昂贵的计算",
        description: "避免在每次渲染时重复执行耗时计算",
        code: `import { useMemo, useState } from 'react';

function ExpensiveComponent({ data }) {
  const [filter, setFilter] = useState('');
  
  // 使用useMemo缓存过滤结果
  const filteredData = useMemo(() => {
    console.log('Filtering data...');
    // 模拟昂贵的计算
    return data.filter(item => {
      // 复杂的过滤逻辑
      return item.name.toLowerCase().includes(filter.toLowerCase()) &&
             item.value > 100 &&
             item.status === 'active';
    }).sort((a, b) => b.value - a.value);
  }, [data, filter]); // 只有data或filter变化时才重新计算
  
  return (
    <div>
      <input 
        value={filter} 
        onChange={(e) => setFilter(e.target.value)}
        placeholder="Filter items..."
      />
      <ul>
        {filteredData.map(item => (
          <li key={item.id}>{item.name}: {item.value}</li>
        ))}
      </ul>
    </div>
  );
}`
      },
      {
        title: "保持对象引用稳定",
        description: "避免因对象引用变化导致子组件不必要的重渲染",
        code: `import { useMemo, useState, memo } from 'react';

// 子组件使用React.memo优化
const ChildComponent = memo(({ config, data }) => {
  console.log('ChildComponent rendered');
  return (
    <div style={config.style}>
      <h3>{config.title}</h3>
      <p>Items: {data.length}</p>
    </div>
  );
});

function ParentComponent() {
  const [count, setCount] = useState(0);
  const [items, setItems] = useState([1, 2, 3]);
  
  // ❌ 错误：每次渲染都创建新对象
  // const config = {
  //   title: 'Item List',
  //   style: { color: 'blue', fontSize: '16px' }
  // };
  
  // ✅ 正确：使用useMemo保持引用稳定
  const config = useMemo(() => ({
    title: 'Item List',
    style: { color: 'blue', fontSize: '16px' }
  }), []); // 空依赖数组，config永不改变
  
  // 缓存处理后的数据
  const processedData = useMemo(() => {
    return items.map(item => item * 2);
  }, [items]);
  
  return (
    <div>
      <button onClick={() => setCount(count + 1)}>
        Count: {count}
      </button>
      <ChildComponent config={config} data={processedData} />
    </div>
  );
}`
      }
    ]
  },
  
  parameters: {
    required: [
      {
        name: "calculateValue",
        type: "() => T",
        description: "计算并返回要缓存值的函数。这个函数应该是纯函数，不应该有副作用"
      },
      {
        name: "dependencies",
        type: "DependencyList",
        description: "依赖项数组。只有当数组中的值发生变化时，才会重新执行计算函数。如果是空数组，计算只会在组件挂载时执行一次"
      }
    ],
    optional: []
  },
  
  returnValue: {
    type: "T",
    description: "缓存的计算结果",
    details: [
      {
        name: "首次渲染",
        description: "执行calculateValue函数并返回结果"
      },
      {
        name: "后续渲染",
        description: "如果依赖项没有变化，返回上次缓存的值；如果依赖项变化，重新执行计算并缓存新结果"
      }
    ]
  },
  
  historicalContext: {
    origin: "useMemo在React 16.8中与其他Hooks一起引入，是为了解决函数组件中的性能优化问题",
    evolution: [
      {
        version: "16.8",
        changes: "首次引入useMemo Hook"
      },
      {
        version: "18.0",
        changes: "在并发特性下，useMemo的行为更加可预测"
      }
    ],
    designPhilosophy: "useMemo的设计理念是提供一种声明式的方式来优化性能，让开发者能够明确指定哪些计算是昂贵的并需要缓存。它不是语义上的保证，React可能在某些情况下丢弃缓存值"
  }
};

export default basicInfo; 