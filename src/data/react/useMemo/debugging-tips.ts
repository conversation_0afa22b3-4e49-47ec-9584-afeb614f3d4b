import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  overview: `useMemo作为性能优化工具，在实际使用中容易出现依赖数组错误、过度优化、缓存失效等调试难题。这些问题往往表现为性能不升反降、计算结果不正确或内存泄漏，需要系统的调试方法来快速定位和解决。

本指南提供4种核心调试策略、6个实用调试工具，以及5个预防技巧，帮助开发者掌握useMemo的调试精髓，避免常见陷阱，实现真正的性能优化。`,
  
  troubleshooting: [
    {
      symptom: 'useMemo没有起到优化效果，性能反而下降',
      possibleCauses: [
        '依赖数组变化过于频繁，缓存失效',
        '计算本身不昂贵，缓存开销超过收益',
        '依赖项包含每次都变化的对象或数组'
      ],
      solutions: [
        '使用React DevTools Profiler分析渲染性能',
        '添加性能监控代码，对比使用前后的耗时',
        '检查依赖数组的稳定性，使用useMemo或useCallback稳定引用'
      ],
      codeExample: `// 调试useMemo性能问题
function PerformanceDebugger({ data, config }) {
  const [metrics, setMetrics] = useState({ 
    withMemo: 0, 
    withoutMemo: 0, 
    memoHits: 0, 
    memoMisses: 0 
  });
  
  // ❌ 问题：config对象每次都是新的，导致缓存失效
  const expensiveResult = useMemo(() => {
    const start = performance.now();
    console.log('🔄 useMemo计算执行');
    
    const result = data.map(item => ({
      ...item,
      processed: heavyCalculation(item, config)
    }));
    
    const duration = performance.now() - start;
    setMetrics(prev => ({
      ...prev,
      withMemo: duration,
      memoMisses: prev.memoMisses + 1
    }));
    
    return result;
  }, [data, config]); // config每次都变化！
  
  // ✅ 正确：稳定化config对象
  const stableConfig = useMemo(() => config, [
    config.threshold,
    config.algorithm,
    config.options
  ]);
  
  const optimizedResult = useMemo(() => {
    const start = performance.now();
    console.log('✅ 优化后的useMemo执行');
    
    const result = data.map(item => ({
      ...item,
      processed: heavyCalculation(item, stableConfig)
    }));
    
    const duration = performance.now() - start;
    setMetrics(prev => ({
      ...prev,
      withMemo: duration,
      memoHits: prev.memoHits + 1
    }));
    
    return result;
  }, [data, stableConfig]);
  
  // 性能监控组件
  useEffect(() => {
    console.log('📊 useMemo性能指标:', {
      缓存命中率: (metrics.memoHits / (metrics.memoHits + metrics.memoMisses) * 100).toFixed(2) + '%',
      平均计算时间: metrics.withMemo.toFixed(2) + 'ms',
      缓存命中次数: metrics.memoHits,
      缓存失效次数: metrics.memoMisses
    });
  }, [metrics]);
  
  return (
    <div>
      <div>性能指标: 命中率 {((metrics.memoHits / (metrics.memoHits + metrics.memoMisses)) * 100).toFixed(1)}%</div>
      <div>数据处理结果: {optimizedResult.length} 项</div>
    </div>
  );
}`,
      severity: 'high'
    },
    {
      symptom: 'useMemo计算结果不正确或过期',
      possibleCauses: [
        '依赖数组不完整，遗漏了影响计算的变量',
        '依赖项使用了错误的比较方式',
        '闭包捕获了过期的值'
      ],
      solutions: [
        '启用ESLint的react-hooks规则检查完整依赖',
        '使用useCallback包装函数依赖',
        '添加调试日志跟踪依赖变化'
      ],
      codeExample: `// 调试useMemo依赖问题
function DependencyDebugger({ users, filters, sortBy }) {
  const [debugInfo, setDebugInfo] = useState([]);
  
  // ❌ 问题：遗漏了sortBy依赖
  const processedUsers = useMemo(() => {
    const timestamp = Date.now();
    console.log('🔍 useMemo执行，时间戳:', timestamp);
    
    let result = users.filter(user => {
      return filters.every(filter => 
        user[filter.field].includes(filter.value)
      );
    });
    
    // 这里使用了sortBy，但没有在依赖数组中声明！
    result.sort((a, b) => a[sortBy].localeCompare(b[sortBy]));
    
    setDebugInfo(prev => [...prev, {
      timestamp,
      usersCount: users.length,
      filtersCount: filters.length,
      sortBy: sortBy, // 记录当时的sortBy值
      resultCount: result.length
    }]);
    
    return result;
  }, [users, filters]); // 缺少sortBy依赖！
  
  // ✅ 正确：包含所有依赖
  const correctProcessedUsers = useMemo(() => {
    const timestamp = Date.now();
    console.log('✅ 正确的useMemo执行，时间戳:', timestamp);
    
    let result = users.filter(user => {
      return filters.every(filter => 
        user[filter.field].includes(filter.value)
      );
    });
    
    result.sort((a, b) => a[sortBy].localeCompare(b[sortBy]));
    
    return result;
  }, [users, filters, sortBy]); // 包含所有依赖
  
  // 依赖变化监控
  useEffect(() => {
    console.log('📋 依赖变化监控:', {
      users: users.length,
      filters: filters.map(f => f.field + ':' + f.value).join(','),
      sortBy
    });
  }, [users, filters, sortBy]);
  
  // 调试信息显示
  const latestDebug = debugInfo[debugInfo.length - 1];
  
  return (
    <div>
      <div>处理结果: {correctProcessedUsers.length} 个用户</div>
      {latestDebug && (
        <div>
          <p>最后执行: {new Date(latestDebug.timestamp).toLocaleTimeString()}</p>
          <p>当时排序字段: {latestDebug.sortBy}</p>
          <p>当前排序字段: {sortBy}</p>
          {latestDebug.sortBy !== sortBy && (
            <p style={{color: 'red'}}>⚠️ 排序字段已变化，但useMemo未重新执行！</p>
          )}
        </div>
      )}
    </div>
  );
}`,
      severity: 'high'
    },
    {
      symptom: 'useMemo导致内存泄漏或内存使用过高',
      possibleCauses: [
        '缓存了大量数据但很少被清理',
        '依赖数组设计不当，导致缓存无法释放',
        '循环引用导致垃圾回收失效'
      ],
      solutions: [
        '使用Chrome DevTools Memory面板监控内存使用',
        '合理设计依赖数组，确保适时清理缓存',
        '避免在useMemo中创建循环引用'
      ],
      codeExample: `// 调试useMemo内存问题
function MemoryDebugger({ largeDataSet, activeFilters }) {
  const [memoryStats, setMemoryStats] = useState({});
  
  // ❌ 问题：缓存大量数据，很少清理
  const expensiveCache = useMemo(() => {
    console.log('🔄 创建大型缓存');
    
    // 创建大量数据的缓存
    const cache = new Map();
    largeDataSet.forEach(item => {
      // 每个item都创建复杂的处理结果
      cache.set(item.id, {
        original: item,
        processed: createExpensiveProcessedData(item),
        metadata: createMetadata(item),
        relationships: findRelationships(item, largeDataSet)
      });
    });
    
    return cache;
  }, [largeDataSet]); // 只有整个数据集变化才清理
  
  // ✅ 优化：分页缓存，及时清理
  const optimizedCache = useMemo(() => {
    console.log('✅ 创建优化缓存');
    
    // 只缓存当前需要的数据
    const relevantData = largeDataSet.filter(item => 
      activeFilters.some(filter => item.category === filter)
    );
    
    const cache = new Map();
    relevantData.forEach(item => {
      cache.set(item.id, {
        processed: createExpensiveProcessedData(item),
        // 避免循环引用
        relatedIds: findRelatedIds(item, relevantData)
      });
    });
    
    return cache;
  }, [largeDataSet, activeFilters]); // 过滤器变化时也会清理
  
  // 内存监控
  useEffect(() => {
    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMemoryStats({
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024),
          cacheSize: optimizedCache.size,
          timestamp: Date.now()
        });
      }
    };
    
    checkMemory();
    const interval = setInterval(checkMemory, 5000);
    
    return () => clearInterval(interval);
  }, [optimizedCache]);
  
  // 手动清理缓存
  const clearCache = useCallback(() => {
    console.log('🗑️ 手动清理缓存');
    // 强制重新创建useMemo
    setMemoryStats(prev => ({ ...prev, forceRefresh: Date.now() }));
  }, []);
  
  return (
    <div>
      <div>缓存大小: {optimizedCache.size} 项</div>
      <div>内存使用: {memoryStats.used}MB / {memoryStats.total}MB</div>
      <button onClick={clearCache}>清理缓存</button>
      {memoryStats.used > memoryStats.limit * 0.8 && (
        <div style={{color: 'red'}}>⚠️ 内存使用过高！</div>
      )}
    </div>
  );
}`,
      severity: 'medium'
    }
  ],
  
  tools: [
    {
      name: 'React DevTools Profiler',
      description: 'React官方性能分析工具，用于监控useMemo的执行和性能影响',
      usage: '在Profiler面板中记录组件渲染，查看useMemo的执行时间和频率',
      category: '浏览器扩展',
      documentation: 'https://react.dev/learn/react-developer-tools'
    },
    {
      name: 'Chrome DevTools Performance',
      description: '浏览器性能分析工具，用于深入分析JavaScript执行性能',
      usage: '使用Performance面板记录页面性能，分析useMemo计算的CPU占用',
      category: '浏览器工具',
      documentation: 'https://developer.chrome.com/docs/devtools/performance/'
    },
    {
      name: 'Chrome DevTools Memory',
      description: '内存分析工具，用于检测useMemo相关的内存泄漏',
      usage: '对比使用useMemo前后的内存快照，查找内存泄漏和过度缓存',
      category: '浏览器工具',
      documentation: 'https://developer.chrome.com/docs/devtools/memory/'
    }
  ],
  
  bestPractices: [
    '使用ESLint的react-hooks规则检查完整依赖',
    '添加性能监控代码，量化useMemo的优化效果',
    '使用React DevTools Profiler验证性能改进',
    '合理设计依赖数组，平衡缓存命中率和内存使用',
    '避免在useMemo中创建循环引用或大量闭包'
  ],
  
  commonMistakes: [
    {
      mistake: '过度使用useMemo，在简单计算中也使用缓存',
      consequence: '缓存开销超过计算开销，性能反而下降',
      solution: '只在计算确实昂贵时使用useMemo，简单计算直接执行',
      prevention: '建立性能基准测试，量化useMemo的实际效果'
    },
    {
      mistake: '依赖数组包含不稳定的对象引用',
      consequence: '缓存频繁失效，useMemo失去优化效果',
      solution: '使用useMemo或useCallback稳定对象引用，或只依赖对象的具体属性',
      prevention: '使用ESLint规则检查依赖数组，建立代码审查流程'
    },
    {
      mistake: '在useMemo中执行副作用操作',
      consequence: '违反React的纯函数原则，可能导致不可预测的行为',
      solution: '将副作用移到useEffect中，useMemo只用于纯计算',
      prevention: '理解useMemo的设计目的，建立清晰的代码规范'
    }
  ]
};

export default debuggingTips;
