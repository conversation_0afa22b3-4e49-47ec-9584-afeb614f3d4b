import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  background: `## useMemo 的诞生背景

在 React Hooks 出现之前，函数组件被认为是"无状态"的，每次渲染都会重新执行所有代码，包括昂贵的计算操作。这导致了严重的性能问题。

### 历史时间线

**2015年** - React组件性能优化的早期探索
- 社区开始探讨函数组件的性能问题，使用各种模式如shouldComponentUpdate
- 认识到需要更好的性能优化工具

**2017年** - React 16发布，引入Fiber架构
- 新的协调算法为未来的性能优化特性奠定基础
- 使得细粒度的性能控制成为可能

**2019年2月** - React 16.8发布，引入Hooks
- useMemo作为核心Hooks之一被引入，提供声明式的计算缓存
- 函数组件获得了类组件同等的性能优化能力

**2022年3月** - React 18发布
- 并发特性影响了useMemo的行为，缓存可能被丢弃
- 强调useMemo是性能优化而非语义保证

### 设计灵感

useMemo的设计灵感来自多个方面：
- **计算机科学中的memoization概念** - 缓存函数调用结果的优化技术
- **函数式编程的纯函数思想** - 相同输入产生相同输出
- **Vue的computed属性** - 自动追踪依赖并缓存结果

### 关键人物

- **Sophie Alpert** - 前React核心团队成员，参与设计Hooks API，包括性能相关的Hooks
- **Dan Abramov** - React核心团队成员，推广Hooks使用模式和最佳实践`,

  evolution: `## useMemo 的演进历程

### Hooks之前的性能优化方案

**类组件时代的痛点**：
\`\`\`javascript
// 使用PureComponent
class ExpensiveList extends PureComponent {
  // 浅比较props，相同则不重渲染
  render() {
    const { items } = this.props;
    const processed = this.processItems(items); // 每次render都执行
    return <List data={processed} />;
  }
  
  processItems(items) {
    // 昂贵的计算
    return items.map(item => transform(item));
  }
}

// 使用shouldComponentUpdate手动优化
class OptimizedList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      processedItems: this.processItems(props.items)
    };
  }
  
  shouldComponentUpdate(nextProps, nextState) {
    if (nextProps.items !== this.props.items) {
      // 只在items变化时更新
      this.setState({
        processedItems: this.processItems(nextProps.items)
      });
      return true;
    }
    return false;
  }
  
  render() {
    return <List data={this.state.processedItems} />;
  }
}
\`\`\`

**存在的问题**：
- 需要手动管理缓存逻辑
- 代码复杂且容易出错
- 难以处理多个缓存值
- 生命周期方法使逻辑分散

### Hooks带来的革命性变化

**现代方式（useMemo）**：
\`\`\`javascript
// 简洁的函数组件
function OptimizedList({ items }) {
  // 声明式的缓存
  const processedItems = useMemo(() => {
    console.log('Processing items...');
    return items.map(item => transform(item));
  }, [items]); // 清晰的依赖关系
  
  return <List data={processedItems} />;
}

// 处理多个缓存值
function ComplexComponent({ data, filters, sortBy }) {
  // 可以轻松管理多个缓存
  const filtered = useMemo(() => {
    return data.filter(item => matchFilters(item, filters));
  }, [data, filters]);
  
  const sorted = useMemo(() => {
    return [...filtered].sort(getSortFunction(sortBy));
  }, [filtered, sortBy]);
  
  const stats = useMemo(() => {
    return calculateStats(sorted);
  }, [sorted]);
  
  return (
    <Dashboard data={sorted} stats={stats} />
  );
}
\`\`\`

**优势**：
- 声明式API，易于理解
- 自动的依赖追踪
- 与函数组件完美集成
- 更容易测试和推理

### 最佳实践的演进

- **2019年初**：对所有昂贵计算使用useMemo（过度谨慎）
- **2019年中**：只对确实昂贵的计算使用（理解优化价值）
- **2020年后**：基于实际性能瓶颈决定（工具成熟，可以准确测量）
- **现在**：配合React DevTools Profiler精确优化`,

  comparisons: `## 与其他方案的对比

### useMemo vs useCallback

\`\`\`javascript
// useMemo - 缓存计算结果
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(a, b);
}, [a, b]);

// useCallback - 缓存函数本身
const handleClick = useCallback(() => {
  doSomething(a, b);
}, [a, b]);

// 等价关系
const handleClick1 = useCallback(fn, deps);
const handleClick2 = useMemo(() => fn, deps);
\`\`\`

### 与类组件方法的对比

| 特性 | 类组件优化 | useMemo |
|-----|-----------|---------|
| 复杂度 | 需要手动管理状态和生命周期 | 声明式，简洁明了 |
| 多缓存管理 | 困难，逻辑分散 | 容易，每个缓存独立 |
| 依赖管理 | 手动比较 | 自动依赖追踪 |
| 测试友好度 | 难以测试 | 纯函数，易于测试 |

### 与其他框架的对比

**Vue的computed属性**：
\`\`\`javascript
// Vue 3 computed
const filtered = computed(() => {
  return items.value.filter(item => item.active);
});

// React useMemo
const filtered = useMemo(() => {
  return items.filter(item => item.active);
}, [items]);
\`\`\`

**差异**：
- Vue的响应式系统自动追踪依赖
- React需要手动声明依赖数组
- Vue的computed具有语义保证，React的useMemo只是优化

**Angular的纯管道**：
- Angular使用纯管道优化模板中的计算
- React使用useMemo优化组件内的计算
- 都基于输入不变则跳过计算的思想`,

  philosophy: `## 设计哲学

### 核心理念

**1. Memoization（记忆化）**

通过缓存函数调用结果来优化性能的技术：

\`\`\`javascript
// Memoization的核心思想
function memoize(fn) {
  const cache = new Map();
  
  return function(...args) {
    const key = JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key); // 缓存命中
    }
    
    const result = fn.apply(this, args);
    cache.set(key, result);
    return result;
  };
}

// React的useMemo是这个概念在组件级别的应用
function Component({ data }) {
  const processed = useMemo(() => {
    return expensiveProcess(data);
  }, [data]); // data变化时缓存失效
}
\`\`\`

**2. 引用透明性（Referential Transparency）**

相同输入总是产生相同输出，这是useMemo能够工作的基础：

\`\`\`javascript
// ✅ 引用透明的函数
const pure = (a, b) => a + b;
pure(2, 3); // 总是返回 5

// ❌ 非引用透明的函数
let counter = 0;
const impure = (a) => a + counter++;
impure(2); // 返回 2, 3, 4...

// useMemo依赖引用透明性
function Component({ items }) {
  // ✅ 引用透明：相同的items总是产生相同的结果
  const sorted = useMemo(() => {
    return [...items].sort((a, b) => a.value - b.value);
  }, [items]);
  
  // ❌ 非引用透明：依赖外部状态
  const badExample = useMemo(() => {
    return items.filter(item => item.date > Date.now()); // Date.now()会变化！
  }, [items]); // 结果不稳定
}
\`\`\`

**3. 空间换时间（Space-Time Tradeoff）**

useMemo体现了经典的算法权衡：使用额外内存来减少计算时间

### 设计原则

- **性能优化而非语义保证**：React可能会丢弃缓存
- **声明式API**：开发者只需声明依赖，React负责缓存管理
- **渐进式采用**：可以逐步添加到现有代码中
- **开发体验优先**：简单的API，与现有Hook一致

### 常见误区

- ❌ **误区**：useMemo让计算更快
- ✅ **现实**：useMemo只是避免重复计算，单次计算速度不变

- ❌ **误区**：所有计算都应该使用useMemo
- ✅ **现实**：只有昂贵的计算才值得缓存

- ❌ **误区**：依赖数组可以选择性添加
- ✅ **现实**：必须包含所有使用的外部变量`,

  presentValue: `## 现实应用价值

### 核心应用场景

**1. 数据处理优化**
- 大数据集的过滤、排序、聚合操作
- 复杂的数据转换和格式化
- API响应数据的预处理

**2. 可视化和图表**
- 图表数据的计算和转换
- Canvas绘制数据的预处理
- 3D渲染的矩阵计算

**3. 表单和用户输入**
- 复杂的表单验证逻辑
- 实时搜索结果的计算
- 自动完成功能的数据准备

### 生态系统影响

**对React生态的推动**：
- 促进了函数组件性能优化的标准化
- 影响了状态管理库的设计（如React Query、Recoil）
- 推动了编译时优化工具的发展

**开发体验提升**：
- 降低了性能优化的技术门槛
- 提供了可测量的性能改进
- 与TypeScript的完美集成

### 实际项目价值

**性能提升**：
- 在数据密集型应用中可以减少50-80%的计算时间
- 特别适合仪表板、数据可视化应用
- 移动端性能优化的重要工具

**代码质量**：
- 促进了更好的数据处理逻辑设计
- 鼓励开发者编写纯函数
- 提高了代码的可预测性和可维护性

**商业价值**：
- 提升用户体验，减少页面卡顿
- 降低服务器压力（减少不必要的计算）
- 提高开发效率和代码复用性

### 未来发展方向

**React Compiler (React Forget)**：
- 自动优化组件，可能不再需要手动使用useMemo
- 基于编译时分析自动插入优化代码
- 更智能的缓存策略和依赖分析

**更智能的默认行为**：
- React可能会自动检测哪些计算需要缓存
- 基于运行时分析自动决定是否启用缓存
- 更好的内存管理和缓存回收机制

**与新特性的结合**：
- 服务端组件和客户端组件的协作优化
- Suspense和并发特性的深度集成
- AI辅助的性能优化建议和自动化`
};

export default knowledgeArchaeology;
