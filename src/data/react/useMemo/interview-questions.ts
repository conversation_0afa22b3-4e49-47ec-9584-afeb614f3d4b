import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    id: 1,
    question: "什么时候应该使用useMemo？什么时候不应该使用？",
    answer: {
      brief: "useMemo应该用于真正需要优化的场景，而不是到处使用。适合昂贵计算和保持对象引用稳定。",
      detailed: `useMemo应该用于真正需要优化的场景：

**适合使用的场景：**
- 计算确实很昂贵（如大数据处理、复杂算法）
- 需要保持对象/数组引用稳定以避免子组件重渲染
- 计算结果被多个地方使用

**不适合使用的场景：**
- 简单计算不需要useMemo，反而会增加开销
- 每次都会变化的值
- 创建基础对象`,
      code: `// ✅ 适合使用useMemo的场景
function DataGrid({ data, filters }) {
  // 1. 昂贵的计算
  const processedData = useMemo(() => {
    return data
      .filter(item => applyFilters(item, filters))
      .sort((a, b) => complexSort(a, b))
      .map(item => transformItem(item));
  }, [data, filters]);
  
  // 2. 保持引用稳定
  const chartConfig = useMemo(() => ({
    type: 'bar',
    options: {
      responsive: true,
      maintainAspectRatio: false
    }
  }), []); // 避免Chart组件不必要的重渲染
  
  return (
    <>
      <Chart data={processedData} config={chartConfig} />
      <Table data={processedData} />
    </>
  );
}

// ❌ 不适合使用useMemo的场景
function SimpleComponent({ a, b }) {
  // 1. 简单计算
  const sum = useMemo(() => a + b, [a, b]); // 过度优化
  
  // 2. 每次都会变化的值
  const random = useMemo(() => Math.random(), [a, b]); // 无意义
  
  // 3. 创建基础对象
  const style = useMemo(() => ({ color: 'red' }), []); // 过度优化
}`
    }
  },
  {
    id: 2,
    question: "useMemo和useCallback有什么区别？它们之间有什么关系？",
    answer: {
      brief: "useMemo缓存计算结果，useCallback缓存函数本身。useCallback(fn, deps) 等价于 useMemo(() => fn, deps)。",
      detailed: `主要区别：

**useMemo：**
- 缓存计算的值
- 返回计算结果

**useCallback：**
- 缓存函数本身
- 返回函数引用

**关系：**
- useCallback(fn, deps) 等价于 useMemo(() => fn, deps)
- 都使用相同的依赖项比较机制`,
      code: `// useMemo 缓存值
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(a, b);
}, [a, b]);

// useCallback 缓存函数
const handleClick = useCallback(() => {
  doSomething(a, b);
}, [a, b]);

// 等价关系
const handleClick1 = useCallback(() => {
  doSomething(a, b);
}, [a, b]);

const handleClick2 = useMemo(() => {
  return () => doSomething(a, b);
}, [a, b]);

// handleClick1 === handleClick2 (功能上等价)`
    }
  },
  {
    id: 3,
    question: "useMemo的依赖数组应该如何正确设置？常见的错误有哪些？",
    answer: {
      brief: "依赖数组必须包含计算函数中使用的所有外部变量。常见错误包括遗漏依赖项、不稳定的依赖项等。",
      detailed: `依赖数组设置规则：

**必须包含的依赖：**
- 计算函数中使用的所有外部变量
- props、state、context等
- 其他hook的返回值

**常见错误：**
- 遗漏依赖项会导致使用过时的值
- 添加不必要的依赖项会导致过度计算
- 对象和函数作为依赖项需要特别注意`,
      code: `// ❌ 错误：遗漏依赖项
function Component({ userId, filters }) {
  const data = useMemo(() => {
    // 使用了filters但没有添加到依赖数组
    return fetchData(userId).filter(item => matchFilters(item, filters));
  }, [userId]); // 缺少 filters
}

// ✅ 正确：完整且稳定的依赖项
function Component({ userId, filters, config }) {
  // 1. 包含所有依赖项
  const data = useMemo(() => {
    return fetchData(userId).filter(item => matchFilters(item, filters));
  }, [userId, filters]);
  
  // 2. 使用具体的属性而不是整个对象
  const processedData = useMemo(() => {
    return processData(data, config.sortBy, config.filterBy);
  }, [data, config.sortBy, config.filterBy]);
}`
    }
  }
];

export default interviewQuestions; 