import { RealWorldProjects } from '@/types/api';

const realWorldProjects: RealWorldProjects = {
  projects: [
    {
      title: "实时数据可视化仪表板",
      description: "构建一个处理大量实时数据的金融仪表板，使用useMemo优化图表渲染和数据处理",
      difficulty: "hard",
      highlights: [
        "处理每秒更新的实时数据流",
        "多种图表类型的数据转换",
        "复杂的聚合计算和统计分析",
        "内存优化和性能监控"
      ],
      codeExample: `import { useMemo, useState, useEffect } from 'react';

function FinancialDashboard() {
  const [marketData, setMarketData] = useState([]);
  const [timeRange, setTimeRange] = useState('1D');
  const [selectedSymbols, setSelectedSymbols] = useState(['AAPL', 'GOOGL']);
  
  // 过滤选中的股票数据
  const filteredData = useMemo(() => {
    console.log('Filtering market data...');
    return marketData.filter(item => 
      selectedSymbols.includes(item.symbol)
    );
  }, [marketData, selectedSymbols]);
  
  // 计算统计指标
  const statistics = useMemo(() => {
    console.log('Calculating statistics...');
    
    return selectedSymbols.reduce((acc, symbol) => {
      const symbolData = filteredData.filter(d => d.symbol === symbol);
      
      if (symbolData.length === 0) {
        acc[symbol] = null;
        return acc;
      }
      
      const prices = symbolData.map(d => d.price);
      const currentPrice = prices[prices.length - 1];
      const openPrice = prices[0];
      const change = currentPrice - openPrice;
      const changePercent = (change / openPrice) * 100;
      
      acc[symbol] = {
        currentPrice,
        openPrice,
        high: Math.max(...prices),
        low: Math.min(...prices),
        change,
        changePercent,
        volume: symbolData.reduce((sum, d) => sum + d.volume, 0)
      };
      
      return acc;
    }, {});
  }, [filteredData, selectedSymbols]);
  
  return (
    <div className="dashboard">
      <div className="statistics-grid">
        {selectedSymbols.map(symbol => (
          <StatCard
            key={symbol}
            symbol={symbol}
            stats={statistics[symbol]}
          />
        ))}
      </div>
    </div>
  );
}`
    }
  ],
  
  bestPractices: [
    "识别真正需要优化的计算瓶颈",
    "合理设计依赖项，减少不必要的重算",
    "考虑数据规模，选择适当的优化策略",
    "监控性能指标，持续优化",
    "平衡计算成本和内存成本",
    "使用Web Worker处理超大规模计算"
  ]
};

export default realWorldProjects; 