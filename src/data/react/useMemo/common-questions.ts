import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    id: "memo-vs-component-memo",
    question: "useMemo和React.memo有什么区别？如何配合使用？",
    answer: `useMemo缓存计算结果，React.memo缓存组件渲染结果，两者配合可以实现最佳性能优化。

**主要区别：**
- useMemo：在组件内部优化计算
- React.memo：在组件层面优化渲染

**配合使用策略：**
- 使用useMemo优化组件内的昂贵计算
- 使用useMemo保持对象/数组引用稳定
- 使用React.memo包装不常变化的子组件
- 配合useCallback优化传递给子组件的函数`,
    code: `// React.memo - 组件级别的优化
const ExpensiveComponent = React.memo(({ data, config }) => {
  console.log('ExpensiveComponent render');
  return <div>{/* 复杂的渲染逻辑 */}</div>;
});

// useMemo - 值级别的优化
function ParentComponent({ items }) {
  // 使用useMemo优化计算
  const processedData = useMemo(() => {
    return items.map(item => transformItem(item));
  }, [items]);
  
  // 使用useMemo保持对象引用稳定
  const config = useMemo(() => ({
    theme: 'dark',
    showDetails: true
  }), []);
  
  // 配合使用：稳定的props + memo组件 = 最少的重渲染
  return <ExpensiveComponent data={processedData} config={config} />;
}`
  },
  {
    id: "debugging-unnecessary-computation",
    question: "如何调试和发现不必要的重复计算？",
    answer: `使用React DevTools Profiler、console.log和自定义Hook来识别性能问题。

**调试策略：**
- 先测量，后优化 - 不要凭感觉优化
- 使用Profiler识别真正的性能瓶颈
- 在开发环境添加性能日志
- 建立性能基准和监控`,
    code: `function Component({ data, filter }) {
  // 方法1：直接在计算中添加日志
  const filteredData = useMemo(() => {
    console.log('Filtering data...', { dataLength: data.length, filter });
    console.time('filter-duration');
    
    const result = data.filter(item => item.name.includes(filter));
    
    console.timeEnd('filter-duration');
    return result;
  }, [data, filter]);
  
  // 方法2：使用自定义Hook
  const useDebugMemo = (fn, deps, name) => {
    const renderRef = useRef(0);
    const depsRef = useRef(deps);
    
    renderRef.current++;
    
    // 检查哪个依赖项变化了
    const changedDeps = deps.map((dep, i) => {
      const changed = !Object.is(dep, depsRef.current[i]);
      return { index: i, changed, prev: depsRef.current[i], current: dep };
    }).filter(item => item.changed);
    
    console.log('[' + name + '] Render #' + renderRef.current, {
      changedDeps,
      allDeps: deps
    });
    
    depsRef.current = deps;
    
    return useMemo(fn, deps);
  };
}`
  },
  {
    id: "circular-dependencies",
    question: "如何处理useMemo中的循环依赖？",
    answer: `循环依赖通常表示设计问题，需要重新思考数据流和状态结构。

**解决策略：**
- 避免创建相互依赖的useMemo
- 考虑将相关计算合并到一个useMemo中
- 使用更清晰的数据流设计
- 必要时使用useReducer处理复杂状态逻辑`,
    code: `// ❌ 错误：循环依赖
function BadComponent() {
  const valueA = useMemo(() => {
    return valueB * 2; // Error: valueB 在定义前使用
  }, [valueB]);
  
  const valueB = useMemo(() => {
    return valueA + 1;
  }, [valueA]);
}

// ✅ 解决方案：合并相关计算
function GoodComponent() {
  const [count, setCount] = useState(0);
  
  // 将相互依赖的值一起计算
  const { valueA, valueB } = useMemo(() => {
    const base = count * 10;
    const a = base * 2;
    const b = base + 1;
    return { valueA: a, valueB: b };
  }, [count]);
}`
  }
];

export default commonQuestions; 