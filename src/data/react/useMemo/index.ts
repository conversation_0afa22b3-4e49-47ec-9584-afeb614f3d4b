import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import realWorldProjects from './real-world-projects';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const useMemoApi: ApiItem = {
  id: 'use-memo',
  title: 'useMemo()',
  description: '缓存计算结果，避免昂贵计算的重复执行',
  category: 'React Hooks',
  difficulty: 'medium',
  syntax: 'const memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b])',
  example: `import React, { useState, useMemo } from 'react';

function ExpensiveComponent({ data }) {
  const [filter, setFilter] = useState('');
  
  // 缓存计算结果，只在data或filter改变时重新计算
  const filteredData = useMemo(() => {
    console.log('Filtering data...');
    return data.filter(item => 
      item.name.toLowerCase().includes(filter.toLowerCase())
    );
  }, [data, filter]);
  
  return (
    <div>
      <input 
        value={filter}
        onChange={(e) => setFilter(e.target.value)}
        placeholder="Filter items..."
      />
      <ul>
        {filteredData.map(item => (
          <li key={item.id}>{item.name}</li>
        ))}
      </ul>
    </div>
  );
}`,
  notes: '⚠️ 不要过度使用useMemo。只在计算确实昂贵时使用，否则缓存本身的开销可能超过收益。',
  isNew: false,
  version: 'React 16.8+',
  tags: ['性能优化', '记忆化', '缓存', 'Hook'],
  
  // 扩展内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  realWorldProjects,
  debuggingTips,
  essenceInsights
};

export default useMemoApi;

export {
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  realWorldProjects,
  debuggingTips,
  essenceInsights
};