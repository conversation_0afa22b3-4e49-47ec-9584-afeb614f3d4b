import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 内容已完成
  completionStatus: '内容已完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: 'ReactElement相关的常见调试问题及其解决方案，包括TypeScript类型错误、运行时异常、性能问题等。',
        sections: [
          {
            title: 'TypeScript类型错误',
            description: 'ReactElement在TypeScript中的常见类型问题及解决方法',
            items: [
              {
                title: '泛型参数类型不匹配错误',
                description: '当ReactElement的泛型参数与实际Props类型不匹配时出现的编译错误',
                solution: '确保ReactElement的泛型参数与组件的Props接口完全匹配，使用正确的类型约束',
                prevention: '建立严格的类型定义，使用TypeScript的严格模式，定期进行类型检查',
                code: `// ❌ 错误：类型不匹配
interface ButtonProps {
  variant: 'primary' | 'secondary';
  disabled?: boolean;
  children: ReactNode;
}

// 错误的泛型参数
const buttonElement: ReactElement<{ text: string }> = (
  <Button variant="primary" disabled={false}>点击我</Button>
);
// TypeScript错误：类型参数不匹配

// ✅ 解决方案：正确的类型匹配
const correctButtonElement: ReactElement<ButtonProps> = (
  <Button variant="primary" disabled={false}>点击我</Button>
);

// ✅ 更好的解决方案：使用类型推断
const inferredButtonElement = (
  <Button variant="primary" disabled={false}>点击我</Button>
);

// ✅ 调试技巧：使用类型守卫验证
function isValidButtonElement(
  element: ReactElement
): element is ReactElement<ButtonProps> {
  return React.isValidElement(element) && 
         typeof element.type === 'function' &&
         'variant' in (element.props as any);
}

// 使用类型守卫进行调试
if (isValidButtonElement(buttonElement)) {
  console.log('按钮变体:', buttonElement.props.variant);
} else {
  console.error('无效的按钮元素:', buttonElement);
}`
              },
              {
                title: 'Children类型错误',
                description: '当ReactElement的children属性类型不正确时产生的错误',
                solution: '正确使用ReactNode类型，理解children的多种可能类型',
                prevention: '使用React.FC类型，明确定义children的类型约束',
                code: `// ❌ 错误：children类型定义不当
interface ContainerProps {
  children: ReactElement; // 过于严格，只允许单个ReactElement
}

const BadContainer: React.FC<ContainerProps> = ({ children }) => {
  return <div className="container">{children}</div>;
};

// 使用时出错
const app1 = (
  <BadContainer>
    <p>第一个子元素</p>
    <p>第二个子元素</p> {/* 错误：不能有多个子元素 */}
  </BadContainer>
);

const app2 = (
  <BadContainer>
    文本内容 {/* 错误：字符串不是ReactElement */}
  </BadContainer>
);

// ✅ 解决方案：使用正确的children类型
interface CorrectContainerProps {
  children: ReactNode; // 允许任何有效的React子元素
}

const GoodContainer: React.FC<CorrectContainerProps> = ({ children }) => {
  return <div className="container">{children}</div>;
};

// 现在这些都可以正常工作
const correctApp1 = (
  <GoodContainer>
    <p>第一个子元素</p>
    <p>第二个子元素</p>
  </GoodContainer>
);

const correctApp2 = (
  <GoodContainer>
    文本内容
  </GoodContainer>
);

// ✅ 调试技巧：验证children类型
function debugChildren(children: ReactNode) {
  React.Children.forEach(children, (child, index) => {
    console.log('Child ' + index + ':', {
      type: typeof child,
      isElement: React.isValidElement(child),
      elementType: React.isValidElement(child) ? child.type : null,
      content: child
    });
  });
}

// 使用调试函数
const DebugContainer: React.FC<{ children: ReactNode }> = ({ children }) => {
  React.useEffect(() => {
    debugChildren(children);
  }, [children]);
  
  return <div>{children}</div>;
};`
              },
              {
                title: 'createElement返回值错误',
                description: 'React.createElement调用返回null或undefined导致的运行时错误',
                solution: '检查createElement的参数，确保组件和props的有效性',
                prevention: '使用TypeScript严格检查，添加运行时验证',
                code: `// ❌ 常见错误：createElement参数无效
const invalidElement1 = React.createElement(null, {}); // 错误：type不能为null
const invalidElement2 = React.createElement(undefined, {}); // 错误：type不能为undefined

// 动态组件时常见的错误
const componentMap: Record<string, React.ComponentType> = {
  'Button': Button,
  'Input': Input
};

const createDynamicElement = (type: string, props: any) => {
  // 错误：没有检查组件是否存在
  return React.createElement(componentMap[type], props);
};

// ✅ 解决方案：添加验证和错误处理
const createSafeDynamicElement = (
  type: string, 
  props: any
): ReactElement | null => {
  // 验证组件类型
  if (!type || typeof type !== 'string') {
    console.error('Invalid component type:', type);
    return null;
  }
  
  // 检查组件是否存在
  const Component = componentMap[type];
  if (!Component) {
    console.error('Component not found:', type);
    return null;
  }
  
  // 验证props
  if (props && typeof props !== 'object') {
    console.error('Invalid props:', props);
    return null;
  }
  
  try {
    return React.createElement(Component, props);
  } catch (error) {
    console.error('Error creating element:', error);
    return null;
  }
};

// ✅ 调试技巧：createElement包装器
const debugCreateElement = <P extends {}>(
  type: string | React.ComponentType<P>,
  props: P | null,
  ...children: ReactNode[]
): ReactElement<P> | null => {
  console.log('Creating element:', {
    type: typeof type === 'string' ? type : type.name,
    props,
    childrenCount: children.length
  });
  
  try {
    const element = React.createElement(type, props, ...children);
    console.log('Element created successfully:', element);
    return element;
  } catch (error) {
    console.error('Failed to create element:', error);
    return null;
  }
};

// 使用调试包装器
const debugElement = debugCreateElement(
  'button',
  { className: 'btn', onClick: () => console.log('clicked') },
  'Click me'
);`
              }
            ]
          },
          {
            title: '运行时异常',
            description: 'ReactElement在运行时产生的常见错误及调试方法',
            items: [
              {
                title: '"Objects are not valid as a React child"错误',
                description: '当试图渲染对象而不是ReactElement时产生的错误',
                solution: '确保渲染的内容是有效的React元素，使用JSON.stringify显示对象内容',
                prevention: '使用TypeScript类型检查，添加渲染前验证',
                code: `// ❌ 错误：尝试渲染对象
const BadComponent: React.FC = () => {
  const user = { name: 'John', age: 30 };
  
  return (
    <div>
      <h1>用户信息</h1>
      {user} {/* 错误：不能直接渲染对象 */}
    </div>
  );
};

// ✅ 解决方案：正确渲染对象内容
const GoodComponent: React.FC = () => {
  const user = { name: 'John', age: 30 };
  
  return (
    <div>
      <h1>用户信息</h1>
      <p>姓名: {user.name}</p>
      <p>年龄: {user.age}</p>
    </div>
  );
};

// ✅ 调试技巧：安全渲染函数
const safeRender = (content: any): ReactNode => {
  if (React.isValidElement(content)) {
    return content;
  }
  
  if (typeof content === 'string' || typeof content === 'number') {
    return content;
  }
  
  if (content === null || content === undefined) {
    return null;
  }
  
  if (typeof content === 'object') {
    console.warn('Attempting to render object, converting to JSON:', content);
    return JSON.stringify(content, null, 2);
  }
  
  return String(content);
};

// 使用安全渲染
const SafeRenderComponent: React.FC<{ data: any }> = ({ data }) => {
  return (
    <div>
      <h1>数据显示</h1>
      <pre>{safeRender(data)}</pre>
    </div>
  );
};

// ✅ 高级调试：错误边界捕获
class ReactElementErrorBoundary extends React.Component<
  { children: ReactNode },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ReactElement错误:', error);
    console.error('错误信息:', errorInfo);
    
    // 分析错误类型
    if (error.message.includes('Objects are not valid as a React child')) {
      console.error('检测到对象渲染错误，请检查render方法返回值');
    }
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', border: '1px solid red' }}>
          <h2>渲染错误</h2>
          <p>错误信息: {this.state.error?.message}</p>
          <details>
            <summary>错误堆栈</summary>
            <pre>{this.state.error?.stack}</pre>
          </details>
        </div>
      );
    }
    
    return this.props.children;
  }
}`
              },
              {
                title: 'Key警告和性能问题',
                description: 'ReactElement列表渲染时的key相关警告和性能调试',
                solution: '为每个列表项提供稳定且唯一的key，避免使用数组索引',
                prevention: '建立key生成规范，使用唯一标识符',
                code: `// ❌ 错误：使用数组索引作为key
const BadList: React.FC<{ items: string[] }> = ({ items }) => {
  return (
    <ul>
      {items.map((item, index) => (
        <li key={index}>{item}</li> // 警告：key应该稳定
      ))}
    </ul>
  );
};

// ✅ 解决方案：使用稳定的key
const GoodList: React.FC<{ items: Array<{ id: string; name: string }> }> = ({ items }) => {
  return (
    <ul>
      {items.map(item => (
        <li key={item.id}>{item.name}</li>
      ))}
    </ul>
  );
};

// ✅ 调试技巧：Key冲突检测
const validateKeys = (elements: ReactElement[]): void => {
  const keys = new Set<string>();
  const duplicateKeys: string[] = [];
  
  elements.forEach(element => {
    if (element.key) {
      if (keys.has(element.key)) {
        duplicateKeys.push(element.key);
      } else {
        keys.add(element.key);
      }
    }
  });
  
  if (duplicateKeys.length > 0) {
    console.warn('发现重复的key:', duplicateKeys);
  }
};

// Key性能分析
const KeyPerformanceAnalyzer: React.FC<{ 
  items: Array<{ id: string; name: string }> 
}> = ({ items }) => {
  const [renderCount, setRenderCount] = React.useState(0);
  const prevItemsRef = React.useRef(items);
  
  React.useEffect(() => {
    setRenderCount(prev => prev + 1);
    
    // 分析key变化
    const prevItems = prevItemsRef.current;
    const addedItems = items.filter(item => 
      !prevItems.some(prev => prev.id === item.id)
    );
    const removedItems = prevItems.filter(prev => 
      !items.some(item => item.id === prev.id)
    );
    
    if (addedItems.length > 0 || removedItems.length > 0) {
      console.log('Key变化分析:', {
        renderCount,
        添加: addedItems.length,
        删除: removedItems.length,
        总数: items.length
      });
    }
    
    prevItemsRef.current = items;
  }, [items, renderCount]);
  
  const elements = items.map(item => (
    <li key={item.id}>
      {item.name} (渲染次数: {renderCount})
    </li>
  ));
  
  // 验证keys
  React.useEffect(() => {
    validateKeys(elements);
  }, [elements]);
  
  return <ul>{elements}</ul>;
};

// ✅ 复杂场景：嵌套组件key管理
const generateCompositeKey = (...parts: (string | number)[]): string => {
  return parts.join('-');
};

const NestedList: React.FC<{
  groups: Array<{
    groupId: string;
    items: Array<{ itemId: string; name: string }>;
  }>;
}> = ({ groups }) => {
  return (
    <div>
      {groups.map(group => (
        <div key={group.groupId}>
          <h3>组 {group.groupId}</h3>
          <ul>
            {group.items.map(item => (
              <li key={generateCompositeKey(group.groupId, item.itemId)}>
                {item.name}
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
};`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: 'ReactElement调试的专业工具配置和使用技巧，包括React DevTools、TypeScript编译器、Chrome DevTools等。',
        sections: [
          {
            title: 'React DevTools配置',
            description: '使用React DevTools深入调试ReactElement的创建、更新和性能问题',
            items: [
              {
                title: 'Profiler性能分析',
                description: '使用React DevTools Profiler分析ReactElement渲染性能',
                solution: '配置Profiler记录，分析组件渲染时间和原因',
                prevention: '定期进行性能分析，建立性能基准',
                code: `// React DevTools Profiler配置
import { Profiler } from 'react';

// 1. 基础Profiler使用
const ProfiledComponent: React.FC = () => {
  const onRenderCallback = (
    id: string,
    phase: 'mount' | 'update',
    actualDuration: number,
    baseDuration: number,
    startTime: number,
    commitTime: number,
    interactions: Set<any>
  ) => {
    console.log('Profiler数据:', {
      组件ID: id,
      渲染阶段: phase,
      实际耗时: actualDuration + 'ms',
      基准耗时: baseDuration + 'ms',
      开始时间: startTime,
      提交时间: commitTime,
      交互数: interactions.size
    });
    
    // 性能警告
    if (actualDuration > 16) {
      console.warn('性能警告: 组件' + id + '渲染超过16ms，可能影响60fps体验');
    }
  };
  
  return (
    <Profiler id="MyComponent" onRender={onRenderCallback}>
      <MyExpensiveComponent />
    </Profiler>
  );
};

// 2. 高级Profiler：性能数据收集
class PerformanceCollector {
  private data: Array<{
    id: string;
    phase: string;
    duration: number;
    timestamp: number;
  }> = [];
  
  collect = (
    id: string,
    phase: 'mount' | 'update',
    actualDuration: number,
    baseDuration: number,
    startTime: number,
    commitTime: number
  ) => {
    this.data.push({
      id,
      phase,
      duration: actualDuration,
      timestamp: commitTime
    });
    
    // 保持最近100条记录
    if (this.data.length > 100) {
      this.data.shift();
    }
  };
  
  getReport() {
    const report = this.data.reduce((acc, item) => {
      if (!acc[item.id]) {
        acc[item.id] = {
          mountCount: 0,
          updateCount: 0,
          totalDuration: 0,
          averageDuration: 0,
          maxDuration: 0
        };
      }
      
      const stats = acc[item.id];
      if (item.phase === 'mount') {
        stats.mountCount++;
      } else {
        stats.updateCount++;
      }
      
      stats.totalDuration += item.duration;
      stats.maxDuration = Math.max(stats.maxDuration, item.duration);
      stats.averageDuration = stats.totalDuration / (stats.mountCount + stats.updateCount);
      
      return acc;
    }, {} as Record<string, any>);
    
    return report;
  }
}

const performanceCollector = new PerformanceCollector();

// 使用性能收集器
const MonitoredApp: React.FC = () => (
  <Profiler id="App" onRender={performanceCollector.collect}>
    <Header />
    <Main />
    <Footer />
  </Profiler>
);

// 3. ReactElement创建监控
const createElementMonitor = (() => {
  const originalCreateElement = React.createElement;
  let createCount = 0;
  
  React.createElement = function(...args) {
    createCount++;
    
    // 记录创建信息
    console.debug('ReactElement创建 #' + createCount + ':', {
      type: typeof args[0] === 'string' ? args[0] : args[0]?.name || 'Unknown',
      props: args[1],
      childrenCount: args.length - 2
    });
    
    return originalCreateElement.apply(this, args);
  };
  
  return {
    getCount: () => createCount,
    reset: () => { createCount = 0; }
  };
})();

// DevTools扩展配置
if (typeof window !== 'undefined' && window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = (id, root) => {
    console.log('Fiber root committed:', { id, root });
  };
}`
              },
              {
                title: 'TypeScript编译器诊断',
                description: '配置TypeScript编译器以获得更好的ReactElement类型检查和错误报告',
                solution: '优化tsconfig.json配置，使用编译器API进行深度诊断',
                prevention: '建立严格的编译检查流程，集成到CI/CD中',
                code: `// tsconfig.json优化配置
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "jsx": "react-jsx",
    "jsxImportSource": "react",
    
    // 增强类型检查
    "skipLibCheck": false, // 检查库文件类型
    "declaration": true, // 生成声明文件
    "declarationMap": true, // 生成声明映射
    
    // 调试选项
    "sourceMap": true,
    "inlineSourceMap": false,
    "listFiles": true, // 列出编译的文件
    "listEmittedFiles": true, // 列出生成的文件
    
    // 类型检查增强
    "plugins": [
      {
        "name": "typescript-plugin-css-modules"
      }
    ]
  },
  
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"],
  
  // 自定义类型检查
  "ts-node": {
    "compilerOptions": {
      "module": "CommonJS"
    }
  }
}

// TypeScript编译器API使用
import * as ts from 'typescript';

// 1. 程序化类型检查
function createTypeChecker(configPath: string) {
  const configFile = ts.readConfigFile(configPath, ts.sys.readFile);
  const parsedConfig = ts.parseJsonConfigFileContent(
    configFile.config,
    ts.sys,
    process.cwd()
  );
  
  const program = ts.createProgram(parsedConfig.fileNames, parsedConfig.options);
  return program.getTypeChecker();
}

// 2. ReactElement类型验证
function validateReactElementTypes(sourceFile: ts.SourceFile, checker: ts.TypeChecker) {
  const diagnostics: Array<{
    message: string;
    line: number;
    column: number;
  }> = [];
  
  function visit(node: ts.Node) {
    // 检查JSX元素
    if (ts.isJsxElement(node) || ts.isJsxSelfClosingElement(node)) {
      const type = checker.getTypeAtLocation(node);
      const symbol = type.getSymbol();
      
      if (symbol) {
        console.log('JSX元素类型:', {
          name: symbol.getName(),
          type: checker.typeToString(type),
          location: sourceFile.getLineAndCharacterOfPosition(node.getStart())
        });
      }
    }
    
    // 检查React.createElement调用
    if (ts.isCallExpression(node)) {
      const signature = checker.getResolvedSignature(node);
      if (signature) {
        const declaration = signature.getDeclaration();
        if (declaration && declaration.name?.getText() === 'createElement') {
          // 验证createElement参数类型
          node.arguments.forEach((arg, index) => {
            const argType = checker.getTypeAtLocation(arg);
            console.log('createElement参数' + index + ':', {
              type: checker.typeToString(argType),
              text: arg.getText()
            });
          });
        }
      }
    }
    
    ts.forEachChild(node, visit);
  }
  
  visit(sourceFile);
  return diagnostics;
}

// 3. 自定义lint规则
const reactElementLintRules = {
  'no-inline-object-props': (node: ts.Node, checker: ts.TypeChecker) => {
    if (ts.isJsxAttribute(node) && ts.isObjectLiteralExpression(node.initializer)) {
      return {
        message: '避免在JSX中使用内联对象，这会导致不必要的重新渲染',
        severity: 'warning'
      };
    }
    return null;
  },
  
  'require-element-key': (node: ts.Node, checker: ts.TypeChecker) => {
    if (ts.isJsxElement(node) || ts.isJsxSelfClosingElement(node)) {
      const parent = node.parent;
      if (ts.isJsxExpression(parent) && ts.isCallExpression(parent.parent)) {
        // 检查是否在map中且缺少key
        const hasKey = node.attributes?.properties.some(prop => 
          ts.isJsxAttribute(prop) && prop.name?.getText() === 'key'
        );
        
        if (!hasKey) {
          return {
            message: '在数组渲染中缺少key属性',
            severity: 'error'
          };
        }
      }
    }
    return null;
  }
};

// 4. 编译时类型报告生成
function generateTypeReport(program: ts.Program) {
  const checker = program.getTypeChecker();
  const report = {
    reactElements: 0,
    jsxElements: 0,
    createElementCalls: 0,
    typeErrors: 0
  };
  
  program.getSourceFiles().forEach(sourceFile => {
    if (!sourceFile.isDeclarationFile) {
      validateReactElementTypes(sourceFile, checker);
    }
  });
  
  return report;
}`
              },
              {
                title: 'Chrome DevTools集成',
                description: '使用Chrome DevTools调试ReactElement相关的运行时问题',
                solution: '配置断点、性能分析、内存监控来深入调试ReactElement',
                prevention: '建立调试工作流，使用自动化测试减少手动调试需求',
                code: `// Chrome DevTools集成脚本
class ChromeDevToolsIntegration {
  // 1. 性能监控集成
  static startPerformanceMonitoring() {
    if (typeof window === 'undefined') return;
    
    // 监控长任务
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.duration > 50) {
          console.warn('长任务检测:', {
            name: entry.name,
            duration: entry.duration + 'ms',
            startTime: entry.startTime
          });
        }
      });
    });
    
    observer.observe({ entryTypes: ['longtask'] });
    
    // 监控React渲染性能
    const renderObserver = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.name.startsWith('⚛️')) {
          console.log('React渲染:', {
            name: entry.name,
            duration: entry.duration + 'ms'
          });
        }
      });
    });
    
    renderObserver.observe({ entryTypes: ['measure'] });
  }
  
  // 2. 内存泄漏检测
  static detectMemoryLeaks() {
    if (typeof window === 'undefined' || !('memory' in performance)) return;
    
    let lastHeapSize = (performance as any).memory.usedJSHeapSize;
    
    setInterval(() => {
      const currentHeapSize = (performance as any).memory.usedJSHeapSize;
      const growth = currentHeapSize - lastHeapSize;
      
      if (growth > 1024 * 1024) { // 1MB增长
        console.warn('内存增长检测:', {
          growth: (growth / 1024 / 1024).toFixed(2) + 'MB',
          total: (currentHeapSize / 1024 / 1024).toFixed(2) + 'MB'
        });
      }
      
      lastHeapSize = currentHeapSize;
    }, 5000);
  }
  
  // 3. ReactElement调试助手
  static installReactElementDebugger() {
    if (typeof window === 'undefined') return;
    
    // 全局调试函数
    (window as any).debugReactElement = (element: any) => {
      if (!React.isValidElement(element)) {
        console.error('不是有效的ReactElement:', element);
        return;
      }
      
      console.group('ReactElement调试信息:');
      console.log('类型:', element.type);
      console.log('属性:', element.props);
      console.log('Key:', element.key);
      console.log('Ref:', element.ref);
      
      // 分析children
      if (element.props.children) {
        console.log('Children类型:', typeof element.props.children);
        if (Array.isArray(element.props.children)) {
          console.log('Children数量:', element.props.children.length);
          element.props.children.forEach((child, index) => {
            console.log('Child ' + index + ':', {
              type: typeof child,
              isElement: React.isValidElement(child)
            });
          });
        }
      }
      console.groupEnd();
    };
    
    // ReactElement性能监控
    (window as any).monitorReactElementPerformance = () => {
      const originalCreateElement = React.createElement;
      const timings: Array<{ type: string; duration: number }> = [];
      
      React.createElement = function(...args) {
        const start = performance.now();
        const result = originalCreateElement.apply(this, args);
        const end = performance.now();
        
        timings.push({
          type: typeof args[0] === 'string' ? args[0] : args[0]?.name || 'Unknown',
          duration: end - start
        });
        
        return result;
      };
      
      // 定期报告
      setInterval(() => {
        if (timings.length > 0) {
          const report = timings.reduce((acc, timing) => {
            if (!acc[timing.type]) {
              acc[timing.type] = { count: 0, totalTime: 0 };
            }
            acc[timing.type].count++;
            acc[timing.type].totalTime += timing.duration;
            return acc;
          }, {} as Record<string, { count: number; totalTime: number }>);
          
          console.table(Object.entries(report).map(([type, stats]) => ({
            组件类型: type,
            创建次数: stats.count,
            总耗时: stats.totalTime.toFixed(2) + 'ms',
            平均耗时: (stats.totalTime / stats.count).toFixed(2) + 'ms'
          })));
          
          timings.length = 0; // 清空记录
        }
      }, 10000);
    };
  }
  
  // 4. 断点助手
  static createReactElementBreakpoints() {
    const originalCreateElement = React.createElement;
    
    React.createElement = function(...args) {
      const type = args[0];
      const props = args[1];
      
      // 条件断点：特定组件
      if (typeof type === 'string' && type === 'button') {
        debugger; // 在button元素创建时断点
      }
      
      // 条件断点：特定props
      if (props && props.onClick && typeof props.onClick !== 'function') {
        debugger; // onClick不是函数时断点
      }
      
      // 条件断点：性能问题
      const start = performance.now();
      const result = originalCreateElement.apply(this, args);
      const duration = performance.now() - start;
      
      if (duration > 1) { // 创建耗时超过1ms
        debugger; // 性能问题断点
      }
      
      return result;
    };
  }
}

// 开发环境自动启用
if (process.env.NODE_ENV === 'development') {
  ChromeDevToolsIntegration.startPerformanceMonitoring();
  ChromeDevToolsIntegration.detectMemoryLeaks();
  ChromeDevToolsIntegration.installReactElementDebugger();
}

// 使用示例
console.log('Chrome DevTools集成已启用');
console.log('可用命令:');
console.log('- debugReactElement(element): 调试ReactElement');
console.log('- monitorReactElementPerformance(): 监控性能');`
              }
            ]
          }
        ]
      }
    }
  ]
};

// ReactElement调试技巧内容已完成
export default debuggingTips;