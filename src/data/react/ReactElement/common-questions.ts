import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-1',
    question: 'ReactElement和ReactNode的区别是什么？什么时候应该使用哪一个？',
    answer: `这是React TypeScript开发中最常见的困惑之一。理解它们的区别对于编写类型安全的React代码至关重要。

**ReactElement**：
- 更加具体和严格的类型
- 专指通过JSX或React.createElement()创建的元素对象
- 包含type、props、key、ref等属性
- 不包括字符串、数字、null、undefined等原始类型

**ReactNode**：
- 更加宽泛的联合类型
- 包括ReactElement以及字符串、数字、boolean、null、undefined、数组等
- 是React组件可以渲染的所有内容的总和
- 通常用于children属性的类型定义

**使用场景**：
- 当你明确需要JSX元素时，使用ReactElement
- 当你需要接受任何可渲染内容时，使用ReactNode
- 组件的children属性通常使用ReactNode
- 函数返回值如果可能为null，应该使用ReactElement | null

**性能考虑**：
ReactElement比ReactNode更严格，能够在编译时提供更好的类型检查，有助于及早发现错误。`,
    code: `// ReactElement vs ReactNode 对比

// 1. 类型定义对比
interface ElementProps {
  element: ReactElement; // 只接受JSX元素
}

interface NodeProps {
  children: ReactNode; // 接受任何可渲染内容
}

// 2. 使用示例对比
const ElementComponent: React.FC<ElementProps> = ({ element }) => {
  // element 确保是一个JSX元素，可以安全访问其属性
  console.log('元素类型:', element.type);
  console.log('元素属性:', element.props);
  
  return <div>{element}</div>;
};

const NodeComponent: React.FC<NodeProps> = ({ children }) => {
  // children 可能是任何可渲染内容，需要进行类型检查
  return <div>{children}</div>;
};

// 3. 正确的使用方式
const App: React.FC = () => {
  // ✅ 正确：传递ReactElement给ElementComponent
  const buttonElement = <button>点击我</button>;
  
  return (
    <div>
      <ElementComponent element={buttonElement} />
      
      {/* ✅ 正确：NodeComponent可以接受各种内容 */}
      <NodeComponent>
        <p>段落文本</p>
        文字内容
        {123}
        {null}
        {[<span key="1">数组项1</span>, <span key="2">数组项2</span>]}
      </NodeComponent>
    </div>
  );
};

// 4. 错误示例
const WrongUsage: React.FC = () => {
  return (
    <div>
      {/* ❌ 错误：字符串不是ReactElement */}
      {/* <ElementComponent element="文本内容" /> */}
      
      {/* ❌ 错误：数字不是ReactElement */}
      {/* <ElementComponent element={123} /> */}
      
      {/* ❌ 错误：null不是ReactElement */}
      {/* <ElementComponent element={null} /> */}
    </div>
  );
};

// 5. 实用的类型检查函数
function isReactElement(node: ReactNode): node is ReactElement {
  return React.isValidElement(node);
}

function processChildren(children: ReactNode): {
  elements: ReactElement[];
  textNodes: string[];
  numberNodes: number[];
} {
  const elements: ReactElement[] = [];
  const textNodes: string[] = [];
  const numberNodes: number[] = [];
  
  React.Children.forEach(children, (child) => {
    if (isReactElement(child)) {
      elements.push(child);
    } else if (typeof child === 'string') {
      textNodes.push(child);
    } else if (typeof child === 'number') {
      numberNodes.push(child);
    }
  });
  
  return { elements, textNodes, numberNodes };
}

// 6. 高级类型约束
interface StrictElementProps<T extends React.ElementType> {
  element: ReactElement<any, T>; // 约束特定组件类型
}

const StrictButtonComponent: React.FC<StrictElementProps<'button'>> = ({ element }) => {
  // element 确保是button元素
  return <div>按钮元素: {element}</div>;
};

// 使用示例
const buttonElement = <button type="button">点击</button>;
const strictExample = <StrictButtonComponent element={buttonElement} />;

// 7. 条件渲染的类型处理
interface ConditionalProps {
  condition: boolean;
  element?: ReactElement;
  fallback?: ReactNode;
}

const ConditionalRenderer: React.FC<ConditionalProps> = ({ 
  condition, 
  element, 
  fallback = null 
}) => {
  if (condition && element) {
    return element; // 返回ReactElement
  }
  return <>{fallback}</>; // 返回ReactNode
};`,
    tags: ['类型系统', 'TypeScript'],
    relatedQuestions: [
      '如何在TypeScript中正确定义组件的children类型？',
      'React.isValidElement()函数的作用和使用场景是什么？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-2',
    question: '如何正确地动态创建ReactElement？有哪些最佳实践？',
    answer: `动态创建ReactElement是React开发中的重要技能，特别是在构建配置驱动的组件、CMS系统或者可视化页面搭建工具时。正确的做法不仅要考虑功能实现，还要注重类型安全、性能优化和错误处理。

**主要方法**：
1. **React.createElement()** - 最基础的方法，直接调用React的API
2. **JSX工厂函数** - 通过配置对象生成JSX
3. **组件映射** - 建立字符串到组件的映射关系
4. **高阶函数** - 封装复杂的动态创建逻辑

**最佳实践**：
- 始终进行类型检查和验证
- 建立组件注册机制
- 处理错误情况和边界条件
- 考虑性能优化，如缓存和memoization
- 提供清晰的错误提示和调试信息

**性能考虑**：
- 避免在渲染过程中重复创建相同的元素
- 使用useMemo缓存复杂的动态元素
- 建立组件工厂模式减少重复计算
- 考虑懒加载和条件创建

**安全性**：
- 验证组件存在性
- 检查props类型和必需属性
- 防止XSS攻击（特别是处理用户输入时）
- 建立白名单机制限制可创建的组件类型`,
    code: `// 动态创建ReactElement的完整指南

// 1. 基础的React.createElement使用
const createBasicElement = (
  type: string,
  props: Record<string, any> = {},
  children?: ReactNode
): ReactElement => {
  return React.createElement(type, props, children);
};

// 使用示例
const dynamicButton = createBasicElement(
  'button',
  { 
    className: 'btn btn-primary',
    onClick: () => console.log('动态按钮被点击')
  },
  '点击我'
);

// 2. 组件映射系统
interface ComponentRegistry {
  [key: string]: React.ComponentType<any>;
}

const componentRegistry: ComponentRegistry = {
  'Button': ({ children, ...props }) => <button {...props}>{children}</button>,
  'Input': ({ label, ...props }) => (
    <div>
      <label>{label}</label>
      <input {...props} />
    </div>
  ),
  'Card': ({ title, children, ...props }) => (
    <div className="card" {...props}>
      <h3>{title}</h3>
      <div>{children}</div>
    </div>
  )
};

// 安全的动态组件创建器
const createDynamicComponent = (
  componentName: string,
  props: Record<string, any> = {},
  children?: ReactNode
): ReactElement | null => {
  // 验证组件是否存在
  const Component = componentRegistry[componentName];
  if (!Component) {
    console.error('未找到组件:', componentName);
    return null;
  }
  
  // 验证props（简化版，实际项目中可以使用更严格的验证）
  if (typeof props !== 'object' || props === null) {
    console.error('无效的props:', props);
    return null;
  }
  
  try {
    return React.createElement(Component, props, children);
  } catch (error) {
    console.error('创建组件失败:', error);
    return null;
  }
};

// 3. 配置驱动的元素创建
interface ElementConfig {
  type: string;
  props?: Record<string, any>;
  children?: ElementConfig[] | string;
  key?: string;
}

const createFromConfig = (config: ElementConfig): ReactElement | null => {
  const { type, props = {}, children, key } = config;
  
  // 处理原生HTML元素
  if (typeof type === 'string' && type.toLowerCase() === type) {
    const processedChildren = Array.isArray(children) 
      ? children.map(child => 
          typeof child === 'string' ? child : createFromConfig(child)
        )
      : children;
    
    return React.createElement(type, { ...props, key }, processedChildren);
  }
  
  // 处理自定义组件
  return createDynamicComponent(type, { ...props, key }, 
    Array.isArray(children) 
      ? children.map(child => 
          typeof child === 'string' ? child : createFromConfig(child)
        )
      : children
  );
};

// 使用配置创建复杂结构
const complexConfig: ElementConfig = {
  type: 'Card',
  props: { title: '用户信息', className: 'user-card' },
  children: [
    {
      type: 'Input',
      props: { label: '姓名', placeholder: '请输入姓名' },
      key: 'name-input'
    },
    {
      type: 'Input',
      props: { label: '邮箱', type: 'email', placeholder: '请输入邮箱' },
      key: 'email-input'
    },
    {
      type: 'Button',
      props: { className: 'submit-btn' },
      children: '提交',
      key: 'submit-button'
    }
  ],
  key: 'user-form'
};

// 4. 高性能的动态元素工厂
class ElementFactory {
  private cache = new Map<string, ReactElement>();
  private registry: ComponentRegistry;
  
  constructor(registry: ComponentRegistry) {
    this.registry = registry;
  }
  
  // 带缓存的元素创建
  createCached(
    componentName: string,
    props: Record<string, any>,
    cacheKey?: string
  ): ReactElement | null {
    const key = cacheKey || this.generateCacheKey(componentName, props);
    
    if (this.cache.has(key)) {
      return this.cache.get(key)!;
    }
    
    const element = createDynamicComponent(componentName, props);
    if (element) {
      this.cache.set(key, element);
    }
    
    return element;
  }
  
  private generateCacheKey(componentName: string, props: Record<string, any>): string {
    return componentName + '-' + JSON.stringify(props);
  }
  
  // 批量创建
  createMultiple(configs: ElementConfig[]): ReactElement[] {
    return configs
      .map(config => createFromConfig(config))
      .filter((element): element is ReactElement => element !== null);
  }
  
  // 清空缓存
  clearCache(): void {
    this.cache.clear();
  }
}

// 5. React Hook集成
const useDynamicElement = (
  componentName: string,
  props: Record<string, any>,
  dependencies: any[] = []
): ReactElement | null => {
  return React.useMemo(() => {
    return createDynamicComponent(componentName, props);
  }, [componentName, JSON.stringify(props), ...dependencies]);
};

// 6. 类型安全的动态创建（TypeScript高级用法）
type ComponentProps<T extends keyof ComponentRegistry> = 
  Parameters<ComponentRegistry[T]>[0];

function createTypedComponent<T extends keyof ComponentRegistry>(
  componentName: T,
  props: ComponentProps<T>
): ReactElement {
  const Component = componentRegistry[componentName];
  return React.createElement(Component, props);
}

// 类型安全的使用
const typedButton = createTypedComponent('Button', {
  children: '类型安全的按钮',
  onClick: () => console.log('点击')
});

// 7. 实际使用示例
const DynamicForm: React.FC<{ config: ElementConfig }> = ({ config }) => {
  const [elementFactory] = React.useState(() => new ElementFactory(componentRegistry));
  
  const dynamicElement = React.useMemo(() => {
    return createFromConfig(config);
  }, [config]);
  
  React.useEffect(() => {
    // 定期清理缓存
    const interval = setInterval(() => {
      elementFactory.clearCache();
    }, 300000); // 5分钟清理一次
    
    return () => clearInterval(interval);
  }, [elementFactory]);
  
  if (!dynamicElement) {
    return <div>无法创建表单</div>;
  }
  
  return (
    <div className="dynamic-form">
      {dynamicElement}
    </div>
  );
};

// 8. 错误处理和调试
const createElementWithDebug = (
  type: string,
  props: Record<string, any> = {},
  children?: ReactNode
): ReactElement | null => {
  console.log('创建元素:', { type, props, children });
  
  try {
    const element = React.createElement(type, props, children);
    console.log('元素创建成功:', element);
    return element;
  } catch (error) {
    console.error('元素创建失败:', error);
    
    // 返回错误提示元素
    return React.createElement(
      'div',
      { style: { color: 'red', border: '1px solid red', padding: '8px' } },
      '元素创建失败: ' + (error as Error).message
    );
  }
};`,
    tags: ['动态渲染', '性能优化'],
    relatedQuestions: [
      'React.createElement和JSX的性能差异是什么？',
      '如何在动态组件中实现类型安全？'
    ]
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 'question-3',
    question: '在企业级组件库中使用ReactElement有哪些最佳实践？如何确保类型安全和性能？',
    answer: `在企业级组件库开发中，ReactElement的正确使用对于构建高质量、可维护的组件系统至关重要。这不仅涉及技术实现，还包括团队协作、代码规范和长期维护等方面。

**核心原则**：
1. **类型优先** - 严格的TypeScript类型定义和检查
2. **性能第一** - 考虑组件的渲染性能和内存使用
3. **开发体验** - 提供清晰的API和良好的调试信息
4. **可扩展性** - 设计灵活的组件接口，支持未来扩展

**架构模式**：
- 使用组合模式而不是继承
- 建立统一的属性传递机制
- 实现render props和children function模式
- 支持受控和非受控组件模式

**质量保证**：
- 完整的单元测试覆盖
- 性能基准测试
- 可访问性(a11y)合规
- 浏览器兼容性测试

**团队协作**：
- 统一的代码风格和命名规范
- 完善的文档和使用示例
- 组件设计评审流程
- 版本管理和向后兼容性`,
    code: `// 企业级组件库中ReactElement最佳实践

// 1. 严格的类型定义系统
interface BaseComponentProps {
  className?: string;
  'data-testid'?: string;
  id?: string;
}

interface ComponentVariant {
  size: 'small' | 'medium' | 'large';
  variant: 'primary' | 'secondary' | 'outline';
  disabled?: boolean;
}

// 通用的ReactElement接口
interface ElementWrapper<P = {}> extends BaseComponentProps {
  as?: React.ElementType;
  children?: ReactNode;
}

// 2. 高阶组件类型安全包装
function withElementWrapper<P extends BaseComponentProps>(
  WrappedComponent: React.ComponentType<P>
): React.FC<P & ElementWrapper> {
  return React.forwardRef<HTMLElement, P & ElementWrapper>(
    ({ as: Component = 'div', className, children, ...props }, ref) => {
      const elementProps = {
        ref,
        className,
        'data-component': WrappedComponent.displayName || WrappedComponent.name,
        ...props
      };
      
      return React.createElement(
        Component,
        elementProps,
        React.createElement(WrappedComponent, props as P),
        children
      );
    }
  );
}

// 3. 企业级Button组件实现
interface ButtonProps extends BaseComponentProps, ComponentVariant {
  children: ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
  loading?: boolean;
  icon?: ReactElement;
  iconPosition?: 'left' | 'right';
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    children, 
    className = '', 
    size = 'medium', 
    variant = 'primary',
    disabled = false,
    loading = false,
    icon,
    iconPosition = 'left',
    type = 'button',
    onClick,
    'data-testid': testId,
    ...rest 
  }, ref) => {
    // 计算最终的className
    const finalClassName = [
      'btn',
      'btn-' + variant,
      'btn-' + size,
      loading && 'btn-loading',
      disabled && 'btn-disabled',
      className
    ].filter(Boolean).join(' ');
    
    // 处理图标元素
    const iconElement = icon ? React.cloneElement(icon, {
      className: 'btn-icon btn-icon-' + iconPosition,
      'aria-hidden': true
    }) : null;
    
    // 加载状态元素
    const loadingElement = loading ? (
      React.createElement('span', { 
        className: 'btn-spinner',
        'aria-label': '加载中'
      })
    ) : null;
    
    // 按钮内容组织
    const buttonContent = [
      iconPosition === 'left' && iconElement,
      loadingElement,
      React.createElement('span', { className: 'btn-text' }, children),
      iconPosition === 'right' && iconElement
    ].filter(Boolean);
    
    return React.createElement(
      'button',
      {
        ref,
        type,
        className: finalClassName,
        disabled: disabled || loading,
        onClick: loading ? undefined : onClick,
        'data-testid': testId || 'button',
        'aria-busy': loading,
        ...rest
      },
      ...buttonContent
    );
  }
);

Button.displayName = 'Button';

// 4. 复合组件模式
interface CardProps extends BaseComponentProps {
  children: ReactNode;
  elevation?: number;
}

interface CardHeaderProps extends BaseComponentProps {
  children: ReactNode;
  actions?: ReactElement;
}

interface CardContentProps extends BaseComponentProps {
  children: ReactNode;
}

interface CardFooterProps extends BaseComponentProps {
  children: ReactNode;
  align?: 'left' | 'center' | 'right';
}

// 主卡片组件
const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ children, className = '', elevation = 1, ...rest }, ref) => {
    return React.createElement(
      'div',
      {
        ref,
        className: 'card card-elevation-' + elevation + ' ' + className,
        ...rest
      },
      children
    );
  }
);

// 卡片子组件
const CardHeader: React.FC<CardHeaderProps> = ({ children, actions, className = '', ...rest }) => {
  return React.createElement(
    'div',
    { className: 'card-header ' + className, ...rest },
    React.createElement('div', { className: 'card-header-content' }, children),
    actions && React.createElement('div', { className: 'card-header-actions' }, actions)
  );
};

const CardContent: React.FC<CardContentProps> = ({ children, className = '', ...rest }) => {
  return React.createElement(
    'div',
    { className: 'card-content ' + className, ...rest },
    children
  );
};

const CardFooter: React.FC<CardFooterProps> = ({ 
  children, 
  align = 'right', 
  className = '', 
  ...rest 
}) => {
  return React.createElement(
    'div',
    { 
      className: 'card-footer card-footer-' + align + ' ' + className, 
      ...rest 
    },
    children
  );
};

// 复合组件模式
Card.Header = CardHeader;
Card.Content = CardContent;
Card.Footer = CardFooter;
Card.displayName = 'Card';

// 5. Render Props模式
interface DataFetcherProps<T> {
  url: string;
  children: (state: {
    data: T | null;
    loading: boolean;
    error: Error | null;
  }) => ReactElement;
}

function DataFetcher<T>({ url, children }: DataFetcherProps<T>): ReactElement {
  const [data, setData] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<Error | null>(null);
  
  React.useEffect(() => {
    setLoading(true);
    fetch(url)
      .then(response => response.json())
      .then(setData)
      .catch(setError)
      .finally(() => setLoading(false));
  }, [url]);
  
  return children({ data, loading, error });
}

// 6. 组件组合工具
interface ComponentComposerProps {
  components: Array<{
    component: React.ComponentType<any>;
    props?: Record<string, any>;
  }>;
  children?: ReactNode;
}

const ComponentComposer: React.FC<ComponentComposerProps> = ({ 
  components, 
  children 
}) => {
  return components.reduceRight(
    (acc, { component: Component, props = {} }) => 
      React.createElement(Component, props, acc),
    children as ReactElement
  );
};

// 7. 性能优化的组件工厂
const createMemoizedComponent = <P extends object>(
  component: React.ComponentType<P>,
  displayName?: string
): React.NamedExoticComponent<P> => {
  const MemoComponent = React.memo(component);
  MemoComponent.displayName = displayName || component.displayName || component.name;
  return MemoComponent;
};

// 8. 类型安全的样式属性处理
type StyleVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error';
type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

interface StyledComponentProps extends BaseComponentProps {
  variant?: StyleVariant;
  size?: ComponentSize;
  fullWidth?: boolean;
}

const createStyledElement = (
  baseClass: string,
  { variant, size, fullWidth, className, ...props }: StyledComponentProps
): React.HTMLAttributes<HTMLElement> => ({
  className: [
    baseClass,
    variant && baseClass + '-' + variant,
    size && baseClass + '-' + size,
    fullWidth && baseClass + '-full-width',
    className
  ].filter(Boolean).join(' '),
  ...props
});

// 9. 企业级使用示例
const EnterpriseExample: React.FC = () => {
  const [loading, setLoading] = React.useState(false);
  
  const handleSubmit = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };
  
  return (
    <Card elevation={2}>
      <Card.Header
        actions={
          <Button variant="outline" size="small">
            设置
          </Button>
        }
      >
        用户管理
      </Card.Header>
      
      <Card.Content>
        <DataFetcher<{ users: any[] }>
          url="/api/users"
        >
          {({ data, loading: dataLoading, error }) => {
            if (dataLoading) return <div>加载中...</div>;
            if (error) return <div>错误: {error.message}</div>;
            if (!data) return <div>无数据</div>;
            
            return (
              <ul>
                {data.users.map(user => (
                  <li key={user.id}>{user.name}</li>
                ))}
              </ul>
            );
          }}
        </DataFetcher>
      </Card.Content>
      
      <Card.Footer>
        <Button
          variant="primary"
          loading={loading}
          onClick={handleSubmit}
          icon={<SaveIcon />}
        >
          保存更改
        </Button>
      </Card.Footer>
    </Card>
  );
};

// 10. 组件测试工具
const createTestElement = (
  component: React.ComponentType,
  props: Record<string, any> = {}
): ReactElement => {
  return React.createElement(component, {
    'data-testid': 'test-component',
    ...props
  });
};

// 导出组件库
export {
  Button,
  Card,
  DataFetcher,
  ComponentComposer,
  createMemoizedComponent,
  createStyledElement,
  withElementWrapper
};`,
    tags: ['组件库', '企业开发'],
    relatedQuestions: [
      '如何在组件库中实现主题系统和样式定制？',
      '企业级组件库的版本管理和向后兼容性如何保证？'
    ]
  }
];

// ReactElement常见问题内容已完成
export default commonQuestions;