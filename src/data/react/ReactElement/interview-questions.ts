import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 1,
    question: 'ReactElement是什么？它与JSX有什么关系？请解释JSX编译后的对象结构。',
    difficulty: 'easy',
    frequency: 'high',
    category: '基础概念',
    answer: {
      brief: 'ReactElement是React中表示虚拟DOM元素的TypeScript类型接口，JSX语法编译后会生成ReactElement对象，包含type、props、key、ref等属性。',
      detailed: `ReactElement是React生态系统中的核心概念，它有以下几个重要方面：

**定义和作用：**
ReactElement是一个TypeScript接口，用于表示React虚拟DOM中的元素对象。它定义了JSX表达式编译后生成的对象结构，是React内部机制的基础数据类型。

**与JSX的关系：**
当我们写JSX代码时，Babel会将其编译为React.createElement()调用，而createElement返回的就是ReactElement对象。例如：
- JSX: <div className="container">Hello</div>
- 编译后: React.createElement('div', {className: 'container'}, 'Hello')
- 返回: ReactElement对象

**ReactElement接口结构：**
interface ReactElement<P = any, T = string | JSXElementConstructor<any>> {
  type: T;        // 元素类型（标签名或组件）
  props: P;       // 属性对象
  key: Key | null;    // 列表渲染的唯一标识
  ref: LegacyRef<T> | null; // 引用对象
}

**实际应用价值：**
- 类型安全：确保组件返回值类型正确
- 开发体验：提供完整的TypeScript智能提示
- React内部：作为Virtual DOM的基础数据结构
- 工具集成：支持开发工具和测试框架`,
      code: `// JSX与ReactElement的对应关系
// 1. JSX语法
const jsxElement = <button onClick={handleClick}>点击我</button>;

// 2. 等价的createElement调用
const createElementCall = React.createElement(
  'button',
  { onClick: handleClick },
  '点击我'
);

// 3. 生成的ReactElement对象结构
const reactElement: ReactElement = {
  type: 'button',
  props: {
    onClick: handleClick,
    children: '点击我'
  },
  key: null,
  ref: null
};

// 4. 函数组件返回ReactElement
const MyComponent: React.FC = (): ReactElement => {
  return <div>这是一个ReactElement</div>;
};

// 5. 验证对象是否为ReactElement
function isReactElement(obj: any): obj is ReactElement {
  return React.isValidElement(obj);
}

// 使用示例
console.log(isReactElement(jsxElement)); // true
console.log(isReactElement('string')); // false
console.log(isReactElement(<MyComponent />)); // true

// 6. ReactElement的实际使用
function renderConditionally(condition: boolean): ReactElement {
  if (condition) {
    return <div>条件为真</div>;
  }
  return <div>条件为假</div>;
}

// 7. 类型约束示例
interface ComponentProps {
  title: string;
  content: ReactElement; // 明确要求传入ReactElement
}

const Container: React.FC<ComponentProps> = ({ title, content }) => {
  return (
    <div>
      <h1>{title}</h1>
      {content} {/* 这里content必须是ReactElement */}
    </div>
  );
};

// 正确使用
const app = (
  <Container 
    title="我的应用" 
    content={<p>这是内容</p>} 
  />
);`
    },
    tags: ['JSX', 'Virtual DOM']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 2,
    question: '在TypeScript中如何正确使用ReactElement泛型？常见的类型错误有哪些，如何避免？',
    difficulty: 'medium',
    frequency: 'high',
    category: '类型系统',
    answer: {
      brief: 'ReactElement支持泛型约束，可以指定props类型和组件类型。常见错误包括类型不匹配、children类型混淆、泛型约束缺失等，需要正确使用泛型参数和类型检查。',
      detailed: `ReactElement的泛型使用是TypeScript React开发的重要技能，涉及以下几个方面：

**泛型参数说明：**
ReactElement<P, T>有两个泛型参数：
- P: Props类型，默认为any
- T: 组件类型，默认为string | JSXElementConstructor<any>

**正确的泛型使用方式：**
1. **指定Props类型**：明确组件接受的属性类型
2. **约束组件类型**：限制可接受的组件类型
3. **处理children**：正确处理children的类型推断
4. **函数返回值**：为函数组件指定准确的返回类型

**常见类型错误：**
1. **Props类型不匹配**：传入的props与期望类型不符
2. **Children类型混淆**：children类型处理不当
3. **泛型约束缺失**：没有提供足够的类型约束
4. **返回值类型错误**：函数组件返回值类型不正确
5. **null处理不当**：忽略了ReactElement可能为null的情况

**最佳实践：**
- 始终为ReactElement提供明确的类型参数
- 使用联合类型处理可能的null值
- 利用工具函数进行类型检查和转换
- 建立类型守卫函数确保类型安全`,
      code: `// 1. 基础泛型使用
interface ButtonProps {
  variant: 'primary' | 'secondary';
  children: ReactNode;
}

// 明确指定Props类型
const buttonElement: ReactElement<ButtonProps> = (
  <Button variant="primary">点击我</Button>
);

// 2. 约束组件类型
type HTMLElementType = ReactElement<
  React.HTMLAttributes<HTMLDivElement>, 
  'div'
>;

const divElement: HTMLElementType = <div className="container">内容</div>;

// 3. 常见错误示例
// ❌ 错误：Props类型不匹配
interface CardProps {
  title: string;
  width: number;
}

const Card: React.FC<CardProps> = ({ title, width }) => (
  <div style={{ width }}>{title}</div>
);

// 类型错误：缺少required属性
// const errorElement: ReactElement<CardProps> = <Card title="测试" />;

// ✅ 正确：提供所有必需属性
const correctElement: ReactElement<CardProps> = (
  <Card title="测试" width={300} />
);

// 4. Children类型处理
interface ContainerProps {
  children: ReactNode;
  padding?: number;
}

const Container: React.FC<ContainerProps> = ({ children, padding = 16 }) => (
  <div style={{ padding }}>{children}</div>
);

// ❌ 错误：children类型不匹配
// const errorContainer: ReactElement<{children: string}> = (
//   <Container>{<div>这是ReactElement，不是string</div>}</Container>
// );

// ✅ 正确：使用正确的children类型
const correctContainer: ReactElement<ContainerProps> = (
  <Container>
    <div>正确的children</div>
  </Container>
);

// 5. 函数组件返回值类型
// ❌ 错误：可能返回null但没有在类型中声明
const BadComponent: React.FC = (): ReactElement => {
  const shouldRender = Math.random() > 0.5;
  // 类型错误：可能返回null
  // return shouldRender ? <div>内容</div> : null;
  return <div>内容</div>; // 修复：总是返回ReactElement
};

// ✅ 正确：明确处理null的情况
const GoodComponent: React.FC = (): ReactElement | null => {
  const shouldRender = Math.random() > 0.5;
  return shouldRender ? <div>内容</div> : null;
};

// 6. 类型守卫和工具函数
function isValidReactElement<P = any>(
  element: any
): element is ReactElement<P> {
  return React.isValidElement(element);
}

function ensureReactElement<P>(
  element: ReactNode
): ReactElement<P> | null {
  if (isValidReactElement<P>(element)) {
    return element;
  }
  if (typeof element === 'string' || typeof element === 'number') {
    return React.createElement('span', null, element) as ReactElement<P>;
  }
  return null;
}

// 7. 高级泛型约束
interface GenericListProps<T> {
  items: T[];
  renderItem: (item: T) => ReactElement;
  keyExtractor: (item: T) => string;
}

function GenericList<T>(): React.FC<GenericListProps<T>> {
  return ({ items, renderItem, keyExtractor }) => {
    return (
      <ul>
        {items.map(item => (
          <li key={keyExtractor(item)}>
            {renderItem(item)}
          </li>
        ))}
      </ul>
    );
  };
}

// 使用示例
interface User {
  id: string;
  name: string;
  email: string;
}

const UserList = GenericList<User>();

const userListElement: ReactElement<GenericListProps<User>> = (
  <UserList
    items={[
      { id: '1', name: '张三', email: '<EMAIL>' },
      { id: '2', name: '李四', email: '<EMAIL>' }
    ]}
    renderItem={(user) => <span>{user.name}</span>}
    keyExtractor={(user) => user.id}
  />
);

// 8. 元素克隆与类型保持
function cloneWithProps<P>(
  element: ReactElement<P>,
  additionalProps: Partial<P>
): ReactElement<P> {
  return React.cloneElement(element, additionalProps);
}

// 使用示例
const originalButton = <Button variant="primary">原始按钮</Button>;
const clonedButton = cloneWithProps(originalButton, { 
  variant: 'secondary' 
});`
    },
    tags: ['TypeScript', '泛型约束']
  },
  {
    // 🏗️ Tab完成状态标识 - 内容已完成
    completionStatus: '内容已完成',
    
    id: 3,
    question: 'ReactElement与React.createElement的关系是什么？请实现一个基于配置的动态组件渲染系统，要求支持组件类型推断和错误处理。',
    difficulty: 'hard',
    frequency: 'medium',
    category: '高级应用',
    answer: {
      brief: 'React.createElement是创建ReactElement的工厂函数，ReactElement是其返回值类型。动态渲染系统需要结合React.createElement、类型推断、组件注册机制和错误处理来实现类型安全的配置驱动渲染。',
      detailed: `这是一个深度的React架构问题，涉及React内部机制和高级TypeScript应用：

**React.createElement与ReactElement的关系：**
1. **工厂模式**：createElement是创建ReactElement的工厂函数
2. **类型约束**：createElement的返回值类型就是ReactElement
3. **编译转换**：JSX通过Babel编译为createElement调用
4. **Virtual DOM**：ReactElement是Virtual DOM树的节点类型

**动态渲染系统的设计挑战：**
1. **类型安全**：如何在运行时保持TypeScript的类型检查
2. **组件注册**：建立字符串到组件的映射机制
3. **Props验证**：确保传入的props与组件期望匹配
4. **错误处理**：处理未知组件、属性错误等异常情况
5. **性能优化**：避免不必要的重新创建和渲染

**实现要点：**
- 使用Map建立组件注册表
- 利用泛型约束确保类型安全
- 实现Props类型推断和验证
- 建立完善的错误处理机制
- 支持嵌套结构和复杂配置

**应用场景：**
- CMS内容管理系统
- 可视化页面搭建工具
- 动态表单生成器
- 微前端组件加载
- A/B测试系统`,
      code: `// 1. React.createElement与ReactElement的基础关系
// JSX编译过程演示
const jsxCode = <div className="hello">World</div>;

// 编译后的等价代码
const compiledCode = React.createElement(
  'div',
  { className: 'hello' },
  'World'
);

// createElement函数签名（简化版）
function createElement<P extends {}>(
  type: string | React.ComponentType<P>,
  props: P | null,
  ...children: ReactNode[]
): ReactElement<P> {
  return {
    type,
    props: { ...props, children: children.length === 1 ? children[0] : children },
    key: props?.key || null,
    ref: props?.ref || null
  } as ReactElement<P>;
}

// 2. 动态组件渲染系统实现
interface ComponentConfig {
  type: string;
  props?: Record<string, any>;
  children?: ComponentConfig[] | string;
  key?: string;
}

interface ComponentRegistration<P = {}> {
  component: React.ComponentType<P>;
  propTypes?: Record<keyof P, any>;
  defaultProps?: Partial<P>;
}

class DynamicRenderer {
  private componentRegistry = new Map<string, ComponentRegistration>();
  private errorBoundary: React.ComponentType<{ error: Error }> | null = null;

  // 注册组件
  registerComponent<P extends {}>(
    name: string,
    registration: ComponentRegistration<P>
  ): void {
    this.componentRegistry.set(name, registration);
  }

  // 批量注册
  registerComponents(components: Record<string, ComponentRegistration>): void {
    Object.entries(components).forEach(([name, registration]) => {
      this.registerComponent(name, registration);
    });
  }

  // 设置错误边界
  setErrorBoundary(ErrorBoundary: React.ComponentType<{ error: Error }>): void {
    this.errorBoundary = ErrorBoundary;
  }

  // 验证Props类型
  private validateProps(
    componentName: string,
    props: any,
    registration: ComponentRegistration
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!registration.propTypes) {
      return { isValid: true, errors };
    }

    Object.entries(registration.propTypes).forEach(([propName, validator]) => {
      const value = props[propName];
      
      // 简化的类型验证（实际项目中可以使用prop-types或yup）
      if (validator.required && (value === undefined || value === null)) {
        errors.push('属性 ' + propName + ' 是必需的');
      }
      
      if (value !== undefined && validator.type) {
        const expectedType = validator.type;
        const actualType = typeof value;
        
        if (expectedType === 'array' && !Array.isArray(value)) {
          errors.push('属性 ' + propName + ' 应该是数组');
        } else if (expectedType !== 'array' && actualType !== expectedType) {
          errors.push('属性 ' + propName + ' 应该是 ' + expectedType + ' 类型');
        }
      }
    });

    return { isValid: errors.length === 0, errors };
  }

  // 渲染单个组件
  renderComponent(config: ComponentConfig): ReactElement | null {
    try {
      const { type, props = {}, children, key } = config;
      
      // 检查组件是否已注册
      const registration = this.componentRegistry.get(type);
      if (!registration) {
        throw new Error('未注册的组件类型: ' + type);
      }

      // 验证Props
      const { isValid, errors } = this.validateProps(type, props, registration);
      if (!isValid) {
        throw new Error('Props验证失败: ' + errors.join(', '));
      }

      // 合并默认Props
      const finalProps = {
        ...registration.defaultProps,
        ...props,
        key
      };

      // 处理children
      let processedChildren: ReactNode = null;
      
      if (children) {
        if (typeof children === 'string') {
          processedChildren = children;
        } else if (Array.isArray(children)) {
          processedChildren = children.map((childConfig, index) => 
            this.renderComponent({ ...childConfig, key: childConfig.key || index.toString() })
          );
        }
      }

      // 创建ReactElement
      return React.createElement(
        registration.component,
        finalProps,
        processedChildren
      );
    } catch (error) {
      return this.renderError(error as Error, config);
    }
  }

  // 错误处理
  private renderError(error: Error, config: ComponentConfig): ReactElement {
    console.error('组件渲染失败:', error, config);
    
    if (this.errorBoundary) {
      return React.createElement(this.errorBoundary, { error });
    }

    // 默认错误显示
    return React.createElement(
      'div',
      { 
        style: { 
          border: '1px solid red', 
          padding: '8px', 
          backgroundColor: '#fee' 
        } 
      },
      React.createElement('h4', null, '组件渲染错误'),
      React.createElement('p', null, error.message),
      React.createElement('pre', null, JSON.stringify(config, null, 2))
    );
  }

  // 渲染配置树
  render(config: ComponentConfig | ComponentConfig[]): ReactElement {
    if (Array.isArray(config)) {
      const elements = config.map((item, index) => 
        this.renderComponent({ ...item, key: item.key || index.toString() })
      );
      
      return React.createElement(React.Fragment, null, ...elements);
    }
    
    return this.renderComponent(config) || React.createElement('div', null, '渲染失败');
  }
}

// 3. 使用示例
// 定义一些基础组件
interface ButtonProps {
  variant: 'primary' | 'secondary';
  onClick?: () => void;
  children: ReactNode;
}

const Button: React.FC<ButtonProps> = ({ variant, onClick, children }) => (
  <button 
    className={'btn btn-' + variant}
    onClick={onClick}
  >
    {children}
  </button>
);

interface CardProps {
  title: string;
  children: ReactNode;
  width?: number;
}

const Card: React.FC<CardProps> = ({ title, children, width = 300 }) => (
  <div style={{ width, border: '1px solid #ccc', padding: '16px' }}>
    <h3>{title}</h3>
    <div>{children}</div>
  </div>
);

// 错误边界组件
const ErrorBoundary: React.FC<{ error: Error }> = ({ error }) => (
  <div style={{ color: 'red' }}>
    <h4>渲染错误</h4>
    <p>{error.message}</p>
  </div>
);

// 创建渲染器实例
const renderer = new DynamicRenderer();

// 注册组件
renderer.registerComponents({
  Button: {
    component: Button,
    propTypes: {
      variant: { type: 'string', required: true },
      onClick: { type: 'function', required: false },
      children: { type: 'any', required: true }
    },
    defaultProps: {
      variant: 'primary'
    }
  },
  Card: {
    component: Card,
    propTypes: {
      title: { type: 'string', required: true },
      children: { type: 'any', required: true },
      width: { type: 'number', required: false }
    },
    defaultProps: {
      width: 300
    }
  }
});

renderer.setErrorBoundary(ErrorBoundary);

// 4. 配置驱动的渲染
const pageConfig: ComponentConfig = {
  type: 'Card',
  props: {
    title: '用户操作面板',
    width: 400
  },
  children: [
    {
      type: 'Button',
      props: {
        variant: 'primary',
        onClick: () => alert('保存成功')
      },
      children: '保存'
    },
    {
      type: 'Button',
      props: {
        variant: 'secondary',
        onClick: () => alert('已取消')
      },
      children: '取消'
    }
  ]
};

// 动态渲染组件
const DynamicPage: React.FC = () => {
  return renderer.render(pageConfig);
};

// 5. 高级功能：支持异步组件加载
class AsyncDynamicRenderer extends DynamicRenderer {
  private asyncComponents = new Map<string, Promise<React.ComponentType>>();

  registerAsyncComponent(name: string, loader: () => Promise<React.ComponentType>): void {
    this.asyncComponents.set(name, loader());
  }

  async renderAsync(config: ComponentConfig): Promise<ReactElement> {
    const { type } = config;
    
    // 检查是否为异步组件
    if (this.asyncComponents.has(type)) {
      const ComponentClass = await this.asyncComponents.get(type)!;
      
      // 注册已加载的组件
      this.registerComponent(type, { component: ComponentClass });
    }
    
    return this.renderComponent(config) || React.createElement('div', null, '加载失败');
  }
}

// 异步渲染使用示例
const asyncRenderer = new AsyncDynamicRenderer();`
    },
    tags: ['createElement', '动态渲染']
  }
];

// ReactElement面试问题内容已完成
export default interviewQuestions;