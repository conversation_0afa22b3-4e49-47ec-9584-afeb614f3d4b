import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ReactElementData: ApiItem = {
  id: 'ReactElement',
  title: 'ReactElement',
  description: 'ReactElement是React中用于表示虚拟DOM元素的TypeScript类型接口，定义了JSX元素的结构和属性约束',
  category: 'React Types',
  difficulty: 'easy',
  
  syntax: `// 基本类型定义
interface ReactElement<P = any, T extends string | JSXElementConstructor<any> = string | JSXElementConstructor<any>> {
  type: T;
  props: P;
  key: Key | null;
  ref: LegacyRef<T> | null;
}

// 使用示例
const element: ReactElement = <div>Hello World</div>;

// 函数组件返回类型
const MyComponent: React.FC = (): ReactElement => {
  return <div>这是一个ReactElement</div>;
};`,
  example: `// 基础ReactElement使用
const buttonElement: ReactElement = (
  <button onClick={() => console.log('clicked')}>
    点击我
  </button>
);

// 类型约束的ReactElement
interface ButtonProps {
  variant: 'primary' | 'secondary';
  children: ReactNode;
}

const typedButton: ReactElement<ButtonProps> = (
  <Button variant="primary">类型安全的按钮</Button>
);

// 动态创建ReactElement
const dynamicElement = React.createElement(
  'div',
  { className: 'container' },
  'Dynamic content'
);`,
  notes: 'ReactElement是React类型系统的核心，确保JSX元素的类型安全。建议在函数组件返回值中明确使用ReactElement类型，特别是在需要类型约束的场景下',
  
  version: 'React 16.0.0+',
  tags: ["React","TypeScript","ReactElement","Types"],
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ReactElementData;