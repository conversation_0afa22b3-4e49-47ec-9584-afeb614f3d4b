import { ApiItem } from '@/types/api';
import refApi from './ref';
import reactiveApi from './reactive';

// 导入各个分类下的所有API
// 这里先导入ref作为示例，后续可以添加更多API

// Composition API
const compositionApis: ApiItem[] = [
  refApi,
  reactiveApi,
  // 可以添加更多API:
  // computedApi,
  // watchApi,
  // watchEffectApi,
  // provideInjectApi
];

// Lifecycle Hooks
const lifecycleApis: ApiItem[] = [
  // onMountedApi,
  // onUpdatedApi,
  // onUnmountedApi
];

// Template Directives
const directiveApis: ApiItem[] = [
  // vModelApi,
  // vIfApi,
  // vShowApi,
  // vForApi
];

// Script Setup
const scriptSetupApis: ApiItem[] = [
  // definePropsApi,
  // defineEmitsApi,
  // defineModelApi
];

// State Management
const stateManagementApis: ApiItem[] = [
  // defineStoreApi
];

// Utilities
const utilityApis: ApiItem[] = [
  // nextTickApi,
  // toRefApi
];

// Vue 3.5 新特性
const vue35Apis: ApiItem[] = [
  // useTemplateRefApi
];

// 合并所有API
export const allVueApis: ApiItem[] = [
  ...compositionApis,
  ...lifecycleApis,
  ...directiveApis,
  ...scriptSetupApis,
  ...stateManagementApis,
  ...utilityApis,
  ...vue35Apis
];

// 按分类导出
export const apisByCategory = {
  'Composition API': compositionApis,
  'Lifecycle': lifecycleApis,
  'Template Directives': directiveApis,
  'Script Setup': scriptSetupApis,
  'State Management': stateManagementApis,
  'Utilities': utilityApis,
  'Vue 3.5': vue35Apis
};

// 按ID查询API的辅助函数
export function getApiById(id: string): ApiItem | undefined {
  return allVueApis.find(api => api.id === id);
}

// 导出默认对象
export default {
  allApis: allVueApis,
  apisByCategory,
  getApiById
}; 