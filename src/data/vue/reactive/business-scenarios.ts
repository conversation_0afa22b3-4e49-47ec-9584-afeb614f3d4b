import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    title: '表单状态管理',
    description: '使用reactive管理复杂表单的多个字段状态，实现双向绑定和验证',
    difficulty: 'easy',
    tags: ['表单', '状态管理', '双向绑定'],
    code: `import { reactive } from 'vue'

// 表单状态管理
const formState = reactive({
  user: {
    name: '',
    email: '',
    age: null
  },
  errors: {},
  isSubmitting: false
})

// 表单提交
const submitForm = async () => {
  formState.isSubmitting = true
  
  try {
    // 模拟API调用
    await fetch('/api/user', {
      method: 'POST',
      body: JSON.stringify(formState.user)
    })
    
    // 重置表单
    Object.assign(formState.user, { name: '', email: '', age: null })
    formState.errors = {}
  } catch (error) {
    formState.errors = { general: '提交失败，请重试' }
  } finally {
    formState.isSubmitting = false
  }
}

// 字段验证
const validateField = (field, value) => {
  if (!value) {
    formState.errors[field] = '此字段不能为空'
  } else {
    delete formState.errors[field]
  }
}`,
    explanation: `这个例子展示了reactive在表单管理中的优势：

**核心特点：**
- 嵌套对象的深度响应性
- 直接修改属性，无需.value
- 保持对象结构的完整性

**实际应用：**
- 复杂表单的状态统一管理
- 嵌套数据结构的响应式更新
- 错误状态和加载状态的集中管理`
  },
  
  {
    title: '购物车系统',
    description: '使用reactive创建购物车状态，支持商品添加、删除、数量修改和价格计算',
    difficulty: 'medium',
    tags: ['购物车', '列表操作', '计算属性'],
    code: `import { reactive, computed } from 'vue'

// 购物车状态
const cart = reactive({
  items: [
    { id: 1, name: 'iPhone 15', price: 5999, quantity: 1 },
    { id: 2, name: 'MacBook Pro', price: 12999, quantity: 2 }
  ],
  discount: 0.1,
  shippingFee: 30
})

// 计算总价
const totalPrice = computed(() => {
  const subtotal = cart.items.reduce((sum, item) => 
    sum + item.price * item.quantity, 0
  )
  const discountAmount = subtotal * cart.discount
  return subtotal - discountAmount + cart.shippingFee
})

// 购物车操作
const cartActions = {
  // 添加商品
  addItem(product) {
    const existingItem = cart.items.find(item => item.id === product.id)
    if (existingItem) {
      existingItem.quantity++
    } else {
      cart.items.push({ ...product, quantity: 1 })
    }
  },
  
  // 删除商品
  removeItem(productId) {
    const index = cart.items.findIndex(item => item.id === productId)
    if (index > -1) {
      cart.items.splice(index, 1)
    }
  },
  
  // 更新数量
  updateQuantity(productId, quantity) {
    const item = cart.items.find(item => item.id === productId)
    if (item && quantity > 0) {
      item.quantity = quantity
    }
  },
  
  // 清空购物车
  clearCart() {
    cart.items.length = 0
  }
}`,
    explanation: `购物车案例展示了reactive在复杂业务逻辑中的应用：

**设计优势：**
- 数组操作的响应式更新
- 嵌套对象的自动跟踪
- 与computed结合计算衍生状态

**业务价值：**
- 实时价格计算
- 购物车状态同步
- 用户体验流畅
- 代码逻辑清晰`
  },
  
  {
    title: '多级菜单导航',
    description: '创建响应式的多级菜单结构，支持动态展开收起和激活状态管理',
    difficulty: 'hard',
    tags: ['导航菜单', '树形结构', '状态管理'],
    code: `import { reactive } from 'vue'

// 菜单数据结构
const menuState = reactive({
  activeMenu: 'dashboard',
  expandedMenus: new Set(['system']),
  menus: [
    {
      id: 'dashboard',
      title: '工作台',
      icon: 'dashboard',
      path: '/dashboard'
    },
    {
      id: 'system',
      title: '系统管理',
      icon: 'system',
      children: [
        {
          id: 'user-management',
          title: '用户管理',
          path: '/system/users',
          children: [
            { id: 'user-list', title: '用户列表', path: '/system/users/list' },
            { id: 'user-roles', title: '角色管理', path: '/system/users/roles' }
          ]
        },
        {
          id: 'settings',
          title: '系统设置',
          path: '/system/settings'
        }
      ]
    }
  ]
})

// 菜单操作方法
const menuActions = {
  // 切换菜单展开状态
  toggleExpand(menuId) {
    if (menuState.expandedMenus.has(menuId)) {
      menuState.expandedMenus.delete(menuId)
    } else {
      menuState.expandedMenus.add(menuId)
    }
  },
  
  // 设置激活菜单
  setActiveMenu(menuId, path) {
    menuState.activeMenu = menuId
    
    // 自动展开父级菜单
    const findParentPath = (menus, targetId, currentPath = []) => {
      for (const menu of menus) {
        const newPath = [...currentPath, menu.id]
        if (menu.id === targetId) {
          return newPath
        }
        if (menu.children) {
          const result = findParentPath(menu.children, targetId, newPath)
          if (result) return result
        }
      }
      return null
    }
    
    const parentPath = findParentPath(menuState.menus, menuId)
    if (parentPath) {
      parentPath.forEach(id => menuState.expandedMenus.add(id))
    }
  },
  
  // 递归查找菜单
  findMenuById(menus, targetId) {
    for (const menu of menus) {
      if (menu.id === targetId) return menu
      if (menu.children) {
        const found = this.findMenuById(menu.children, targetId)
        if (found) return found
      }
    }
    return null
  }
}`,
    explanation: `多级菜单案例展示了reactive处理复杂嵌套结构的能力：

**技术亮点：**
- 深层嵌套对象的响应式
- Set数据结构的响应式支持
- 复杂状态逻辑的优雅处理

**实现特色：**
- 自动父级菜单展开
- 状态持久化友好
- 性能优化的状态更新
- 易于扩展的数据结构`
  }
];

export default businessScenarios; 