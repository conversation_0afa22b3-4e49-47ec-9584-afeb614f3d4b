import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  background: `reactive() API 是 Vue 3 Composition API 的核心组成部分，标志着 Vue 从 Options API 向更灵活的组合式API的重大转变。它的设计灵感来自于 React Hooks 和 RxJS 的响应式编程理念。`,
  
  evolution: `Vue 1.x → Vue 2.x → Vue 3.x 的响应式演进：
  
**Vue 1.x (2014)：** 简单的观察者模式
**Vue 2.x (2016)：** Object.defineProperty + 虚拟DOM
**Vue 3.x (2020)：** Proxy-based reactive system + Composition API`,
  
  philosophy: `reactive() 体现了 Vue 3 的设计哲学：
- **渐进式增强**：可以与现有代码共存
- **性能优先**：基于 Proxy 的更高效实现  
- **开发体验**：更直观的响应式编程模型
- **类型安全**：更好的 TypeScript 支持`,
  
  presentValue: `在现代前端开发中，reactive() 已成为状态管理的重要工具，特别适合构建复杂的企业级应用。它为开发者提供了一种声明式的、可预测的状态管理方案。`
};

export default knowledgeArchaeology; 