import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    question: 'Vue3中reactive和ref的区别是什么？分别适用于什么场景？',
    difficulty: 'medium',
    tags: ['reactive', 'ref', '响应式系统'],
    answer: `**主要区别：**

1. **数据类型支持**
   - reactive：只能用于对象类型（对象、数组、Map、Set等）
   - ref：可以用于任意类型，包括基本类型和对象类型

2. **访问方式**
   - reactive：直接访问属性，如 \`state.count\`
   - ref：需要通过 \`.value\` 访问，如 \`count.value\`

3. **响应式原理**
   - reactive：基于ES6 Proxy实现，直接代理整个对象
   - ref：内部也可能使用reactive，但提供了.value的访问接口

**适用场景：**
- reactive：适合复杂的数据结构，如表单数据、组件状态等
- ref：适合单一值，如计数器、开关状态等`,
    code: `// reactive 适用场景
const state = reactive({
  user: { name: 'Vue', age: 3 },
  items: [1, 2, 3],
  loading: false
})

// ref 适用场景  
const count = ref(0)
const message = ref('Hello')
const isVisible = ref(true)`
  },
  
  {
    question: '解释Vue3响应式系统的工作原理，reactive是如何实现的？',
    difficulty: 'hard',
    tags: ['响应式原理', 'Proxy', '依赖收集'],
    answer: `**Vue3响应式系统核心机制：**

1. **Proxy代理**
   - 使用ES6 Proxy拦截对象的get/set操作
   - 比Vue2的Object.defineProperty更强大，支持数组和新属性

2. **依赖收集（track）**
   - 在getter中收集当前活动的effect
   - 建立数据属性与effect的依赖关系

3. **派发更新（trigger）**
   - 在setter中触发相关的effect重新执行
   - 实现数据变化到视图更新的自动化

4. **effect系统**
   - watchEffect、computed、组件更新都基于effect
   - 形成完整的响应式生态系统`,
    code: `// 简化的reactive实现原理
function reactive(target) {
  return new Proxy(target, {
    get(target, key, receiver) {
      const result = Reflect.get(target, key, receiver)
      
      // 依赖收集
      track(target, key)
      
      // 如果值是对象，递归代理
      return isObject(result) ? reactive(result) : result
    },
    
    set(target, key, value, receiver) {
      const result = Reflect.set(target, key, value, receiver)
      
      // 派发更新
      trigger(target, key)
      
      return result
    }
  })
}`
  }
];

export default interviewQuestions; 