import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    question: 'reactive对象解构后为什么会失去响应性？如何解决？',
    tags: ['解构', '响应性', 'toRefs'],
    answer: `**原因：**
解构操作获取的是对象属性的值（基本类型）或引用（对象类型），而不是响应式代理对象本身。基本类型失去了响应式代理的包装。

**解决方案：**
1. 使用 toRefs() 转换
2. 使用 toRef() 转换单个属性
3. 避免解构，直接使用对象`,
    code: `// 问题示例
const state = reactive({ count: 0, name: 'Vue' })
const { count, name } = state // 失去响应性

// 解决方案1：toRefs
const { count, name } = toRefs(state) // 保持响应性
count.value++ // 需要.value访问

// 解决方案2：toRef
const count = toRef(state, 'count')
count.value++

// 解决方案3：直接使用
state.count++ // 推荐方式`
  },
  
  {
    question: 'reactive可以直接赋值一个新对象吗？',
    tags: ['赋值', '响应性丢失'],
    answer: `**不可以！** 直接赋值会导致响应性丢失。

**原因：**
reactive返回的是原对象的代理，直接赋值会切断响应式连接。

**正确做法：**
使用Object.assign()或展开运算符更新对象属性。`,
    code: `const state = reactive({ count: 0, user: { name: 'Vue' } })

// ❌ 错误 - 失去响应性
state = reactive({ count: 10, user: { name: 'React' } })

// ✅ 正确方式
Object.assign(state, { count: 10, user: { name: 'React' } })

// ✅ 或者逐个更新
state.count = 10
state.user.name = 'React'`
  }
];

export default commonQuestions; 