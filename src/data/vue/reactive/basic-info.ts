import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  definition: "reactive()是Vue 3 Composition API中用于创建深度响应式对象的核心函数，基于ES6 Proxy实现透明的响应式转换，自动追踪数据变化并触发相关更新。",
  
  introduction: 'reactive() 函数用于创建响应式对象。它接收一个普通对象或数组，返回该对象的响应式代理。与 ref() 不同，reactive() 直接作用于对象本身，不需要通过 .value 访问。',
  
  syntax: 'const state = reactive(target)',
  
  quickExample: `import { reactive } from 'vue'

// 创建响应式对象
const state = reactive({
  count: 0,
  user: {
    name: 'Vue',
    email: '<EMAIL>'
  },
  todos: ['学习Vue', '掌握reactive']
})

// 直接修改属性 - 自动触发响应式更新
state.count++
state.user.name = 'Vue 3'
state.todos.push('构建项目')

// 在模板中使用
// <div>{{ state.count }}</div>
// <div>{{ state.user.name }}</div>`,

  scenarioDiagram: `graph TD
    A[reactive使用场景] --> B[复杂状态管理]
    A --> C[表单数据处理]
    A --> D[数据集合操作]

    B --> B1[多层嵌套对象]
    B --> B2[组件间状态共享]
    B --> B3[全局状态管理]

    C --> C1[表单字段验证]
    C --> C2[动态表单生成]
    C --> C3[表单数据序列化]

    D --> D1[列表增删改查]
    D --> D2[数据过滤排序]
    D --> D3[批量操作处理]

    E[核心特性] --> F[深度响应式]
    E --> G[Proxy拦截]
    E --> H[自动依赖追踪]

    F --> F1[嵌套对象自动转换]
    F --> F2[数组变更检测]
    F --> F3[Map/Set支持]

    G --> G1[属性访问拦截]
    G --> G2[属性修改拦截]
    G --> G3[删除操作拦截]

    H --> H1[依赖收集]
    H --> H2[更新触发]
    H --> H3[性能优化]

    style A fill:#e1f5fe
    style E fill:#f3e5f5`,
  
  parameters: [
    {
      name: 'target',
      type: 'object | array',
      description: '要转换为响应式的对象或数组',
      required: true
    }
  ],
  
  returnValue: {
    type: 'reactive proxy',
    description: '返回原对象的响应式代理对象'
  },
  
  keyFeatures: [
    {
      title: "深度响应式转换",
      description: "自动将嵌套对象和数组转换为响应式，支持任意深度的嵌套结构",
      benefit: "无需手动处理深层数据结构，简化复杂状态管理"
    },
    {
      title: "透明代理访问",
      description: "使用原生JavaScript语法操作数据，无需特殊的访问方式",
      benefit: "学习成本低，代码自然易读，无需改变编程习惯"
    },
    {
      title: "高效依赖追踪",
      description: "基于Proxy实现精确的依赖收集，只追踪实际访问的属性",
      benefit: "避免不必要的更新，提升应用性能"
    },
    {
      title: "支持复杂数据类型",
      description: "原生支持Array、Map、Set、WeakMap、WeakSet等复杂数据结构",
      benefit: "满足各种数据操作需求，无需特殊处理"
    },
    {
      title: "自动更新触发",
      description: "数据变化时自动触发相关组件更新，无需手动通知",
      benefit: "确保UI与数据状态的同步，减少手动管理负担"
    }
  ],
  
  limitations: [
    '只能用于对象类型（object、array、Map、Set等）',
    '解构后会失去响应性，需要使用 toRefs() 或 toRef()',
    '不能替换整个响应式对象，会失去响应式连接',
    '仅限于引用类型，基本类型需要使用 ref()'
  ]
};

export default basicInfo; 