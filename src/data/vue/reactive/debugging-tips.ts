import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  introduction: `Vue的reactive响应式系统虽然功能强大，但在调试时可能遇到一些特殊的挑战。由于reactive基于Proxy实现，调试时需要理解响应式系统的工作机制、依赖追踪原理以及可能的性能陷阱。

掌握reactive的调试技巧，不仅能快速定位响应式相关的问题，还能帮助你编写更高效、更可维护的Vue应用。`,

  troubleshooting: [
    {
      symptom: "响应式数据变化但视图不更新",
      possibleCauses: [
        "修改了响应式对象的嵌套属性，但没有正确触发响应",
        "直接替换了reactive对象，破坏了响应式连接",
        "修改了数组索引或长度属性的问题",
        "在非响应式上下文中修改数据"
      ],
      solutions: [
        "确保所有嵌套对象都是响应式的，使用reactive或ref包装",
        "避免直接替换reactive对象，使用Object.assign()或展开语法",
        "对数组操作使用Vue提供的变异方法",
        "在setup()或computed/watch等响应式上下文中修改数据",
        "使用Vue DevTools检查响应式依赖关系"
      ]
    },
    {
      symptom: "响应式系统性能问题",
      possibleCauses: [
        "对大型对象或深层嵌套对象使用reactive",
        "频繁修改响应式数据导致过多的重新计算",
        "循环引用导致的依赖追踪问题",
        "不必要的深度监听"
      ],
      solutions: [
        "对简单数据类型使用ref，对复杂对象才使用reactive",
        "使用shallowReactive进行浅层响应式",
        "合并多个数据更新，减少触发频率",
        "使用markRaw标记不需要响应式的对象",
        "优化数据结构，避免不必要的深层嵌套"
      ]
    },
    {
      symptom: "响应式丢失问题",
      possibleCauses: [
        "解构赋值破坏了响应式连接",
        "将响应式对象传递给非响应式函数",
        "使用错误的方式访问响应式数据",
        "在模板中直接解构响应式对象"
      ],
      solutions: [
        "使用toRefs()保持解构后的响应式",
        "传递整个响应式对象而不是单个属性",
        "在模板中直接使用响应式对象",
        "使用computed创建派生的响应式数据",
        "正确理解响应式代理的工作原理"
      ]
    },
    {
      symptom: "内存泄漏和循环引用",
      possibleCauses: [
        "响应式对象之间的循环引用",
        "没有正确清理watch监听器",
        "组件卸载时响应式数据未清理",
        "全局响应式状态过度使用"
      ],
      solutions: [
        "避免创建循环引用的响应式对象",
        "在组件卸载时停止watch监听器",
        "使用onUnmounted清理响应式引用",
        "合理使用全局状态，避免内存累积",
        "使用WeakMap等弱引用数据结构"
      ]
    }
  ],

  debuggingTools: [
    {
      name: "Vue DevTools",
      purpose: "检查响应式数据和依赖关系",
      usage: `
// 在组件中暴露响应式数据给DevTools
import { reactive } from 'vue';

export default {
  setup() {
    const state = reactive({
      count: 0,
      user: { name: 'Vue' }
    });
    
    // DevTools会自动检测这些响应式数据
    return { state };
  }
}`,
      example: "使用DevTools的Reactivity面板查看依赖追踪、触发更新的源头"
    },
    {
      name: "自定义响应式调试器",
      purpose: "追踪响应式数据的变化",
      usage: `
import { reactive, watchEffect } from 'vue';

function createDebugReactive(obj, name) {
  const reactiveObj = reactive(obj);
  
  // 创建调试代理
  return new Proxy(reactiveObj, {
    set(target, key, value) {
      console.log('Reactive Update:', name, key, {
        oldValue: target[key],
        newValue: value,
        timestamp: Date.now(),
        stack: new Error().stack
      });
      return Reflect.set(target, key, value);
    },
    get(target, key) {
      console.log('Reactive Access:', name, key);
      return Reflect.get(target, key);
    }
  });
}

// 使用示例
const debugState = createDebugReactive({
  count: 0
}, 'AppState');`,
      example: "帮助追踪响应式数据的读取和写入，定位不期望的触发源"
    },
    {
      name: "依赖追踪分析器",
      purpose: "分析响应式依赖关系",
      usage: `
import { reactive, watchEffect, getCurrentScope } from 'vue';

function analyzeDependencies(fn, name) {
  let dependencies = new Set();
  
  const scope = getCurrentScope();
  const originalTrack = scope?.track;
  
  // 劫持track函数来收集依赖
  if (scope) {
    scope.track = function(target, key) {
      dependencies.add('[' + target.constructor.name + ']' + key);
      return originalTrack?.call(this, target, key);
    };
  }
  
  try {
    const result = fn();
    console.log(name + ' 依赖分析:', Array.from(dependencies));
    return result;
  } finally {
    if (scope && originalTrack) {
      scope.track = originalTrack;
    }
  }
}

// 使用示例
const state = reactive({ a: 1, b: 2 });
analyzeDependencies(() => {
  return state.a + state.b;
}, 'computed');`,
      example: "分析computed或watchEffect的具体依赖项，优化性能"
    },
    {
      name: "响应式性能监控",
      purpose: "监控响应式系统的性能影响",
      usage: `
import { reactive, watchEffect } from 'vue';

class ReactiveProfiler {
  constructor() {
    this.updateCount = 0;
    this.startTime = performance.now();
  }
  
  wrapReactive(obj, name) {
    const reactiveObj = reactive(obj);
    
    watchEffect(() => {
      // 访问所有属性触发依赖收集
      JSON.stringify(reactiveObj);
      
      this.updateCount++;
      const now = performance.now();
      const duration = now - this.startTime;
      
      if (this.updateCount % 100 === 0) {
        console.log('Reactive Performance:', name, {
          updates: this.updateCount,
          duration: duration + 'ms',
          avgTime: (duration / this.updateCount).toFixed(2) + 'ms/update'
        });
      }
    });
    
    return reactiveObj;
  }
}

// 使用示例
const profiler = new ReactiveProfiler();
const state = profiler.wrapReactive({ count: 0 }, 'AppState');`,
      example: "监控响应式更新频率和性能影响，发现性能瓶颈"
    }
  ],

  commonMistakes: [
    {
      mistake: "解构响应式对象导致响应式丢失",
      example: `
// ❌ 错误做法
const state = reactive({ count: 0, name: 'Vue' });
const { count, name } = state; // 失去响应式

// 在模板中使用
// <div>{{ count }}</div> // 不会响应变化`,
      solution: `
// ✅ 正确做法
const state = reactive({ count: 0, name: 'Vue' });
const { count, name } = toRefs(state); // 保持响应式

// 或者在模板中直接使用
// <div>{{ state.count }}</div>`
    },
    {
      mistake: "直接替换reactive对象",
      example: `
// ❌ 错误做法
let state = reactive({ users: [] });
state = reactive({ users: newUsers }); // 破坏响应式连接`,
      solution: `
// ✅ 正确做法
const state = reactive({ users: [] });
state.users = newUsers; // 修改属性而不是替换对象

// 或使用Object.assign
Object.assign(state, { users: newUsers });`
    },
    {
      mistake: "对原始类型使用reactive",
      example: `
// ❌ 错误做法
const count = reactive(0); // reactive不能用于原始类型
const message = reactive('hello'); // 会导致错误`,
      solution: `
// ✅ 正确做法
const count = ref(0); // 原始类型使用ref
const message = ref('hello');

// 或者包装在对象中
const state = reactive({ 
  count: 0, 
  message: 'hello' 
});`
    }
  ],

  bestPractices: [
    "优先使用ref处理原始类型，reactive处理对象类型",
    "避免深层嵌套的响应式对象，考虑使用shallowReactive",
    "使用toRefs()在解构时保持响应式",
    "在组件卸载时清理不必要的响应式引用",
    "使用Vue DevTools监控响应式数据的依赖关系",
    "避免在响应式对象中存储非序列化的数据",
    "合理使用markRaw标记不需要响应式的对象",
    "在模板中直接使用响应式对象，避免不必要的计算属性",
    "使用computed创建派生数据，而不是在响应式对象中存储计算结果"
  ],

  preventionTips: [
    "设计数据结构时考虑响应式的性能影响",
    "建立响应式数据的命名约定，便于调试识别",
    "定期使用Vue DevTools检查应用的响应式依赖",
    "编写单元测试验证响应式行为的正确性",
    "在团队中建立响应式数据的最佳实践规范",
    "使用TypeScript提供更好的响应式类型检查",
    "建立响应式性能监控，及时发现性能问题",
    "在代码review中特别关注响应式数据的使用"
  ]
};

export default debuggingTips; 