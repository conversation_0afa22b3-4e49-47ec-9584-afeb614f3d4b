import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import commonQuestions from './common-questions';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const reactiveApi: ApiItem = {
  id: 'reactive',
  title: 'reactive()',
  subtitle: 'Vue3响应式对象创建函数，基于Proxy实现深度响应式转换',
  description: '创建响应式对象，深度响应式转换，适用于对象和数组。reactive通过ES6 Proxy实现透明的响应式转换，是Vue3 Composition API的核心功能之一。',
  category: 'Vue Composition API',
  difficulty: 'medium',
  syntax: 'const state = reactive(obj)',
  example: `import { reactive } from 'vue'

const state = reactive({
  count: 0,
  todos: ['学习Vue', '掌握reactive']
})

// 直接修改属性
state.count++
state.todos.push('构建项目')

console.log(state.count) // 1
console.log(state.todos.length) // 3`,
  notes: '注意：reactive对象解构会失去响应性，使用toRefs()解决',
  isNew: false,
  version: 'Vue 3.0+',
  tags: ['响应式', '对象', '深度转换', 'Proxy', 'Composition API'],
  
  // 扩展内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  performanceOptimization,
  debuggingTips,
  knowledgeArchaeology,
  essenceInsights,

  // 元数据
  readTime: '20分钟',
  practiceTime: '40分钟',
  
  // 相关主题
  relatedAPIs: ['ref', 'toRefs', 'toRef', 'computed', 'watch'],
  
  // 更新信息
  lastUpdated: '2024-12-27',
  
  // 完成状态
  isCompleted: true,
  completedTabs: [
    'basicInfo',
    'businessScenarios', 
    'implementation',
    'interviewQuestions',
    'commonQuestions',
    'performanceOptimization',
    'debuggingTips',
    'knowledgeArchaeology',
    'essenceInsights'
  ]
};

export default reactiveApi; 