import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  introduction: `Vue的reactive响应式系统虽然在大多数情况下性能优秀，但在处理大量数据、复杂嵌套对象或频繁更新的场景下，仍然需要进行针对性的性能优化。

理解reactive的性能特征和优化策略，能够帮助你构建更高效的Vue应用，避免不必要的性能瓶颈，同时保持代码的简洁性和可维护性。`,

  bestPractices: [
    {
      practice: "使用shallowReactive优化大型对象",
      description: "对于只需要监听第一层属性变化的大型对象，使用shallowReactive可以显著提升性能",
      example: `
// ❌ 深度响应式导致性能开销
const largeData = reactive({
  list: Array(10000).fill().map((_, i) => ({
    id: i,
    name: 'Item ' + i,
    details: { /* 复杂嵌套对象 */ }
  }))
});

// ✅ 使用浅响应式优化
const largeData = shallowReactive({
  list: Array(10000).fill().map((_, i) => ({
    id: i,
    name: 'Item ' + i,
    details: { /* 复杂嵌套对象 */ }
  }))
});

// 只监听list的替换，不监听内部对象变化
largeData.list = newList; // 会触发更新
largeData.list[0].name = 'New Name'; // 不会触发更新`
    },
    {
      practice: "使用markRaw标记不需要响应式的对象",
      description: "对于第三方库实例、DOM元素等不需要响应式的对象，使用markRaw避免不必要的代理创建",
      example: `
import { reactive, markRaw } from 'vue';

// ❌ 将第三方库实例变为响应式浪费性能
const state = reactive({
  chart: new Chart(canvas, config), // Chart实例被不必要地代理
  user: { name: 'Vue' }
});

// ✅ 标记第三方实例为原始对象
const state = reactive({
  chart: markRaw(new Chart(canvas, config)), // 不会被代理
  user: { name: 'Vue' }
});

// 或者在需要时动态标记
state.domElement = markRaw(document.createElement('div'));`
    },
    {
      practice: "合理使用computed缓存计算结果",
      description: "使用computed缓存昂贵的计算，避免在每次访问时重复计算",
      example: `
// ❌ 每次访问都重新计算
const state = reactive({
  items: [/* 大量数据 */]
});

// 在模板中直接计算
// <div>{{ state.items.filter(item => item.active).length }}</div>

// ✅ 使用computed缓存计算结果
const state = reactive({
  items: [/* 大量数据 */]
});

const activeItemsCount = computed(() => {
  return state.items.filter(item => item.active).length;
});

// 在模板中使用缓存的计算属性
// <div>{{ activeItemsCount }}</div>`
    },
    {
      practice: "批量更新减少触发频率",
      description: "使用nextTick或批量更新模式减少频繁的响应式触发",
      example: `
// ❌ 频繁触发响应式更新
const state = reactive({ count: 0, total: 0 });

function updateMany() {
  for (let i = 0; i < 1000; i++) {
    state.count++; // 每次都触发更新
    state.total += i; // 每次都触发更新
  }
}

// ✅ 批量更新模式
const state = reactive({ count: 0, total: 0 });

function updateMany() {
  // 方式1：使用临时变量
  let newCount = state.count;
  let newTotal = state.total;
  
  for (let i = 0; i < 1000; i++) {
    newCount++;
    newTotal += i;
  }
  
  // 一次性更新
  state.count = newCount;
  state.total = newTotal;
}

// 方式2：使用Object.assign
function updateManyV2() {
  const updates = { count: 0, total: 0 };
  
  for (let i = 0; i < 1000; i++) {
    updates.count++;
    updates.total += i;
  }
  
  Object.assign(state, updates);
}`
    }
  ],

  commonBottlenecks: [
    {
      bottleneck: "深层嵌套对象的响应式转换",
      impact: "创建响应式对象时间长，内存占用大",
      solution: "使用shallowReactive或者拆分数据结构",
      codeExample: `
// ❌ 性能瓶颈：深层嵌套响应式
const state = reactive({
  users: Array(1000).fill().map(i => ({
    profile: {
      personal: {
        name: 'User' + i,
        details: {
          // 更深层的嵌套...
        }
      }
    }
  }))
});

// ✅ 优化方案：使用浅响应式
const state = shallowReactive({
  users: Array(1000).fill().map(i => ({
    profile: {
      personal: {
        name: 'User' + i,
        details: {
          // 更深层的嵌套...
        }
      }
    }
  }))
});

// 或者拆分数据结构
const usersList = ref([]);
const userDetails = reactive(new Map()); // 按需存储详细信息`
    },
    {
      bottleneck: "频繁的数组操作",
      impact: "大量数组变更导致性能问题",
      solution: "使用虚拟滚动、分页或优化数组操作方式",
      codeExample: `
// ❌ 性能瓶颈：频繁的数组操作
const list = reactive([]);

function addManyItems() {
  for (let i = 0; i < 10000; i++) {
    list.push({ id: i, name: 'Item' + i }); // 每次push都触发更新
  }
}

// ✅ 优化方案：批量操作
const list = ref([]);

function addManyItems() {
  const newItems = [];
  for (let i = 0; i < 10000; i++) {
    newItems.push({ id: i, name: 'Item' + i });
  }
  list.value = [...list.value, ...newItems]; // 一次性更新
}

// 或使用shallowRef + 手动触发更新
const list = shallowRef([]);

function addManyItems() {
  for (let i = 0; i < 10000; i++) {
    list.value.push({ id: i, name: 'Item' + i });
  }
  triggerRef(list); // 手动触发更新
}`
    }
  ],

  optimizationStrategies: [
    {
      strategy: "响应式数据分层策略",
      description: "根据数据的使用模式选择合适的响应式API",
      implementation: `
// 分层响应式策略
class DataStore {
  constructor() {
    // 第一层：频繁变化的UI状态
    this.ui = reactive({
      loading: false,
      currentPage: 1,
      selectedItems: []
    });
    
    // 第二层：相对稳定的业务数据
    this.data = shallowReactive({
      users: [],
      products: [],
      orders: []
    });
    
    // 第三层：静态配置数据
    this.config = readonly({
      apiEndpoints: { /* ... */ },
      constants: { /* ... */ }
    });
    
    // 第四层：不需要响应式的数据
    this.cache = markRaw(new Map());
    this.workers = markRaw([]);
  }
  
  // 根据数据特征选择合适的更新方式
  updateUsers(newUsers) {
    // 大量数据使用替换而不是逐个修改
    this.data.users = newUsers;
  }
  
  updateUserPartial(userId, updates) {
    // 小量更新使用targeted update
    const user = this.data.users.find(u => u.id === userId);
    if (user) {
      Object.assign(user, updates);
    }
  }
}`
    },
    {
      strategy: "延迟响应式转换",
      description: "对大型数据集使用懒加载和按需响应式转换",
      implementation: `
// 延迟响应式转换策略
class LazyReactiveStore {
  constructor() {
    this.cache = new Map();
    this.data = shallowReactive({
      items: []
    });
  }
  
  // 懒加载响应式数据
  getReactiveItem(id) {
    if (!this.cache.has(id)) {
      const item = this.data.items.find(item => item.id === id);
      if (item) {
        // 只有在需要时才转换为响应式
        this.cache.set(id, reactive(item));
      }
    }
    return this.cache.get(id);
  }
  
  // 分批处理大量数据
  async loadLargeDataset(data) {
    const BATCH_SIZE = 100;
    
    for (let i = 0; i < data.length; i += BATCH_SIZE) {
      const batch = data.slice(i, i + BATCH_SIZE);
      
      // 分批添加，避免阻塞主线程
      await new Promise(resolve => {
        setTimeout(() => {
          this.data.items.push(...batch);
          resolve();
        }, 0);
      });
    }
  }
  
  // 虚拟滚动支持
  getVisibleItems(startIndex, endIndex) {
    return this.data.items.slice(startIndex, endIndex).map(item => 
      this.getReactiveItem(item.id)
    );
  }
}`
    },
    {
      strategy: "响应式数据内存优化",
      description: "优化响应式数据的内存使用和垃圾回收",
      implementation: `
// 内存优化策略
class MemoryOptimizedStore {
  constructor() {
    this.activeData = reactive({});
    this.inactiveData = new WeakMap(); // 使用WeakMap自动垃圾回收
    this.cleanupTimer = null;
  }
  
  // 智能数据管理
  activateData(key, data) {
    // 激活数据时转为响应式
    this.activeData[key] = reactive(data);
    
    // 设置自动清理
    this.scheduleCleanup();
  }
  
  deactivateData(key) {
    // 移动到非活跃存储
    if (this.activeData[key]) {
      this.inactiveData.set(key, toRaw(this.activeData[key]));
      delete this.activeData[key];
    }
  }
  
  scheduleCleanup() {
    if (this.cleanupTimer) {
      clearTimeout(this.cleanupTimer);
    }
    
    this.cleanupTimer = setTimeout(() => {
      this.cleanup();
    }, 5000); // 5秒后清理
  }
  
  cleanup() {
    // 清理长时间未使用的数据
    const now = Date.now();
    for (const key in this.activeData) {
      if (this.getLastAccessTime(key) < now - 30000) { // 30秒未访问
        this.deactivateData(key);
      }
    }
  }
  
  // 对象池重用
  createReactiveObject(template) {
    const pool = this.objectPool || (this.objectPool = []);
    
    let obj = pool.pop();
    if (!obj) {
      obj = reactive({});
    }
    
    // 重置对象
    Object.keys(obj).forEach(key => delete obj[key]);
    Object.assign(obj, template);
    
    return obj;
  }
  
  recycleObject(obj) {
    if (this.objectPool.length < 100) { // 限制池大小
      this.objectPool.push(obj);
    }
  }
}`
    }
  ],

  performanceMetrics: [
    {
      metric: "响应式对象创建时间",
      description: "测量reactive()调用的耗时",
      measurement: `
function measureReactiveCreation(data, name) {
  const start = performance.now();
  const reactiveData = reactive(data);
  const end = performance.now();
  
  console.log(name + '响应式创建耗时:', (end - start).toFixed(2) + 'ms');
  console.log('对象大小:', JSON.stringify(data).length + ' bytes');
  
  return reactiveData;
}`,
      tools: "Performance API、Vue DevTools的Performance面板"
    },
    {
      metric: "内存使用监控",
      description: "监控响应式对象的内存占用",
      measurement: `
class MemoryMonitor {
  constructor() {
    this.baseline = this.getMemoryUsage();
  }
  
  getMemoryUsage() {
    return performance.memory ? {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit
    } : null;
  }
  
  measureReactiveMemory(fn, name) {
    const before = this.getMemoryUsage();
    const result = fn();
    
    // 强制垃圾回收（仅在开发环境）
    if (window.gc) window.gc();
    
    setTimeout(() => {
      const after = this.getMemoryUsage();
      if (before && after) {
        console.log(name + '内存影响:', {
          增加: ((after.used - before.used) / 1024 / 1024).toFixed(2) + 'MB',
          总计: (after.used / 1024 / 1024).toFixed(2) + 'MB'
        });
      }
    }, 100);
    
    return result;
  }
}`,
      tools: "Chrome DevTools Memory面板、Heap Snapshot"
    },
    {
      metric: "响应式更新频率",
      description: "统计响应式触发的频率和性能影响",
      measurement: `
function createUpdateTracker() {
  let updateCount = 0;
  let lastUpdate = performance.now();
  
  return {
    track() {
      updateCount++;
      const now = performance.now();
      const interval = now - lastUpdate;
      
      if (updateCount % 100 === 0) {
        console.log('响应式更新统计:', {
          总次数: updateCount,
          平均间隔: (interval / 100).toFixed(2) + 'ms',
          更新频率: (1000 / (interval / 100)).toFixed(1) + 'updates/sec'
        });
      }
      
      lastUpdate = now;
    }
  };
}

// 使用watchEffect监控更新
const tracker = createUpdateTracker();
watchEffect(() => {
  // 访问响应式数据
  state.someValue;
  tracker.track();
});`,
      tools: "自定义监控、React DevTools Profiler"
    }
  ],

  tools: [
    {
      name: "Vue DevTools",
      description: "Vue官方开发工具，提供响应式数据监控",
      usage: "使用Performance面板分析响应式更新的性能影响，使用Components面板查看响应式依赖"
    },
    {
      name: "Chrome DevTools Performance",
      description: "浏览器性能分析工具",
      usage: "记录性能配置文件，分析响应式操作对主线程的影响"
    },
    {
      name: "Memory Profiler",
      description: "内存使用分析工具",
      usage: "使用Heap Snapshot分析响应式对象的内存占用和泄漏"
    }
  ]
};

export default performanceOptimization; 