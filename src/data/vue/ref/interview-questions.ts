export const interviewQuestions = [
  {
    question: 'Vue 3 中的 ref 和 reactive 有什么区别？何时使用 ref，何时使用 reactive？',
    answer: `**ref 和 reactive 的区别：**

1. **类型适用范围**
   - ref 可以包装任何类型的值（基本类型和对象类型）
   - reactive 只能用于对象类型（Object, Array, Map, Set等）

2. **访问方式**
   - ref 创建的响应式数据需要通过 .value 访问和修改
   - reactive 创建的响应式对象可以直接访问和修改属性

3. **解构行为**
   - ref 解构后保持响应性（因为是通过 .value 访问）
   - reactive 对象解构后会失去响应性

4. **模板中使用**
   - ref 在模板中自动解包，不需要 .value
   - reactive 在模板中直接使用属性

**何时使用 ref，何时使用 reactive：**

使用 ref 的场景：
- 需要响应式的独立基本类型值（数字、字符串、布尔值）
- 需要保持响应性的解构数据
- 将响应式数据传递给函数时
- 单一的、独立的状态管理

使用 reactive 的场景：
- 管理相关联的数据结构（如表单数据、用户信息）
- 处理嵌套的对象状态
- 需要直接访问属性而不是通过 .value
- 习惯面向对象编程风格

实际应用中的最佳实践：
- 组合使用：可以用 reactive 存储对象，用 ref 存储独立值
- 使用 toRefs 转换 reactive 对象，获得解构安全的引用
- 当不确定时，优先选择 ref，因为它适用范围更广`,
    visualization: `graph TB
    A["选择响应式API"] --> B{"数据类型?"}
    B -->|基本类型| C["使用ref"]
    B -->|对象类型| D{"是否需要解构?"}
    D -->|是| E["使用ref"]
    D -->|否| F["使用reactive"]
    
    G["使用场景"] --> H{"单一值?"}
    H -->|是| C
    H -->|否| I{"相关数据集?"}
    I -->|是| F
    I -->|否| C`
  },
  {
    question: '为什么在使用 ref 时需要 .value，而在模板中却不需要？Vue 是如何实现这种自动解包的？',
    answer: `**为什么需要 .value**

在 JavaScript 中访问 ref 值需要使用 .value 的原因：
1. **基本类型的限制**：JavaScript 的基本类型（如数字、字符串）是按值传递的，无法直接拦截对它们的操作
2. **包装对象的必要性**：需要一个对象包装器来使用 Proxy 或 getter/setter 实现响应式
3. **区分普通变量**：.value 语法明确标识这是一个响应式引用，而不是普通变量

**模板中自动解包的原理**

Vue 在模板编译阶段处理自动解包：

1. **编译时转换**：
   - Vue 模板编译器会分析模板中的表达式
   - 检测到 ref 类型的变量时，自动插入 .value 访问

2. **运行时判断**：
   - 在渲染时，Vue 检查表达式结果是否为 ref 对象
   - 如果是 ref，自动提取其 .value 值
   - 这种检测通过 isRef 方法实现

3. **具体实现过程**：`,
    visualization: `graph TD
    A["模板编译"] --> B["解析表达式"]
    B --> C{"是否为ref?"}
    C -->|是| D["插入.value访问"]
    C -->|否| E["保持原样"]
    D --> F["生成渲染函数"]
    E --> F
    F --> G["运行时渲染"]
    G --> H{"访问的是ref?"}
    H -->|是| I["自动读取.value"]
    H -->|否| J["直接使用值"]`
  }
]; 