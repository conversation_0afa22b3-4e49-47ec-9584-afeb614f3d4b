export const basicInfo = {
  introduction: 'ref() 是 Vue 3 中用于创建响应式引用的函数。它可以将基本类型（如数字、字符串、布尔值）或对象类型包装成响应式对象。',
  syntax: 'const refValue = ref(initialValue)',
  parameters: [
    {
      name: 'initialValue',
      type: 'T',
      description: '任意类型的初始值，可选',
      default: 'undefined'
    }
  ],
  returnValue: {
    type: 'Ref<T>',
    description: '一个包含 .value 属性的响应式引用对象，访问或修改该属性会触发视图更新'
  },
  limitations: [
    '基本类型值必须通过 .value 访问或修改',
    '在模板中使用时会自动解包，不需要 .value',
    '当 ref 作为响应式对象的属性被访问时也会自动解包'
  ]
}; 