export const commonQuestions = [
  {
    question: 'ref 对象是否支持嵌套对象的深层响应性？',
    answer: '是的，当 ref 的值是一个对象时，Vue 会使用 reactive() 函数处理这个对象，使其成为深层响应式的。这意味着对象内部的嵌套属性也会是响应式的。如果你不希望有深层响应性，可以使用 shallowRef() 作为替代。',
    code: `const user = ref({
  name: 'John',
  profile: {
    age: 30,
    address: {
      city: 'New York'
    }
  }
})

// 深层属性修改也是响应式的
user.value.profile.address.city = 'Boston' // 会触发视图更新`
  },
  {
    question: '为什么在模板中 ref 自动解包，但在 JavaScript 中需要 .value？',
    answer: '这是 Vue 3 的设计决策，目的是平衡开发体验和清晰度。在模板中，Vue 编译器可以确定性地处理表达式并自动解包 ref，保持代码简洁。而在 JavaScript 中，自动解包会导致混淆（无法区分普通变量和响应式引用）并破坏类型推断，因此需要显式访问 .value 属性。这种区别也帮助开发者清楚地知道他们正在使用响应式引用。',
    code: null
  },
  {
    question: '如何在响应式对象之间共享或转换状态？',
    answer: 'Vue 提供了多种工具函数来处理响应式对象之间的转换：\n\n1. `toRef`：将响应式对象的属性转为 ref\n2. `toRefs`：将响应式对象的所有属性转为 ref 对象\n3. `unref`：获取 ref 的原始值（ref.value 或原值）\n4. `isRef`：检查值是否为 ref 对象\n\n这些工具可以帮助在不同响应式系统之间转换，特别是当你需要解构响应式对象但又想保持响应性时。',
    code: `import { reactive, toRefs, toRef, unref, isRef } from 'vue'

const state = reactive({
  count: 0,
  name: 'Vue'
})

// 转换单个属性为 ref
const countRef = toRef(state, 'count')

// 解构但保持响应性
const { count, name } = toRefs(state)

// 它们保持连接
countRef.value++ // state.count 也会更新
count.value++ // state.count 也会更新
state.count++ // countRef.value 和 count.value 也会更新

// 获取原始值
const rawCount = unref(count) // 等同于 isRef(count) ? count.value : count

// 检查是否为 ref
console.log(isRef(count)) // true
console.log(isRef(rawCount)) // false`
  },
  {
    question: 'ref 和 reactive 性能有何不同？何时选择一个而非另一个？',
    answer: '从性能角度来看：\n\n1. **内存占用**：ref 通常比 reactive 轻量，因为它只需要包装单个值\n2. **访问成本**：reactive 的属性访问更高效，因为不需要 .value 间接层\n3. **更新成本**：对于大型对象，ref 的更新可能更高效，因为 reactive 需要递归设置代理\n\n选择建议：\n\n- 对于独立的、简单的值，使用 ref\n- 对于相关联的数据结构（如表单数据），使用 reactive\n- 需要解构并保持响应性时，使用 ref 或 toRefs\n- 性能关键场景，可以基于具体情况进行测试',
    code: null
  },
  {
    question: '为什么在使用 watch 监听 ref 时有时需要 .value 有时不需要？',
    answer: '这取决于你想监听的是 ref 对象本身还是其内部值：\n\n1. **直接监听 ref**：`watch(myRef, (newVal, oldVal) => {})` - 不需要 .value，监听的是 ref 的内部值变化\n\n2. **使用 getter 函数**：`watch(() => myRef.value, (newVal, oldVal) => {})` - 需要 .value，监听的是 getter 返回的值\n\n第一种形式更简洁，适用于监听整个 ref 值。第二种形式更灵活，可以监听 ref 值的某个属性或进行计算。',
    code: `import { ref, watch } from 'vue'

const count = ref(0)
const user = ref({ name: 'John', age: 30 })

// 直接监听 ref - 不需要 .value
watch(count, (newCount, oldCount) => {
  console.log(\`Count changed from \${oldCount} to \${newCount}\`)
})

// 使用 getter 监听 ref 的属性 - 需要 .value
watch(() => user.value.age, (newAge, oldAge) => {
  console.log(\`Age changed from \${oldAge} to \${newAge}\`)
})

// 监听多个 ref
watch([count, user], ([newCount, newUser], [oldCount, oldUser]) => {
  console.log(\`Count: \${oldCount} -> \${newCount}, Name: \${oldUser.name} -> \${newUser.name}\`)
})`
  }
]; 