export const businessScenarios = [
  {
    title: '表单输入控制',
    description: '在表单处理中使用 ref 跟踪输入值和验证状态',
    code: `<script setup>
import { ref, computed } from 'vue'

// 创建响应式引用
const username = ref('')
const password = ref('')
const isSubmitting = ref(false)

// 基于响应式值的计算属性
const isValid = computed(() => {
  return username.value.length >= 3 && password.value.length >= 6
})

// 表单提交处理
const handleSubmit = async () => {
  if (!isValid.value) return
  
  isSubmitting.value = true
  
  try {
    await submitToServer({
      username: username.value,
      password: password.value
    })
    // 重置表单
    username.value = ''
    password.value = ''
  } catch (error) {
    console.error(error)
  } finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <form @submit.prevent="handleSubmit">
    <div>
      <label for="username">用户名:</label>
      <input id="username" v-model="username" />
    </div>
    <div>
      <label for="password">密码:</label>
      <input id="password" type="password" v-model="password" />
    </div>
    <button type="submit" :disabled="!isValid || isSubmitting">
      {{ isSubmitting ? '提交中...' : '登录' }}
    </button>
  </form>
</template>`,
    explanation: '在这个登录表单示例中，我们使用 ref 创建了三个响应式状态：username、password 和 isSubmitting。当用户输入内容或表单提交时，这些状态的变化会自动触发视图更新。通过计算属性 isValid，我们可以根据输入值的状态动态控制提交按钮的可用性。'
  },
  {
    title: '异步数据加载',
    description: '使用 ref 管理异步数据加载状态和结果',
    code: `<script setup>
import { ref, onMounted } from 'vue'

// 数据和加载状态
const users = ref([])
const isLoading = ref(false)
const error = ref(null)

// 加载数据函数
const fetchUsers = async () => {
  isLoading.value = true
  error.value = null
  
  try {
    const response = await fetch('https://api.example.com/users')
    if (!response.ok) throw new Error('Failed to fetch users')
    
    const data = await response.json()
    users.value = data
  } catch (err) {
    error.value = err.message
    users.value = []
  } finally {
    isLoading.value = false
  }
}

// 组件挂载时加载数据
onMounted(fetchUsers)

// 刷新数据的处理函数
const handleRefresh = () => {
  fetchUsers()
}
</script>

<template>
  <div>
    <h1>用户列表</h1>
    <button @click="handleRefresh" :disabled="isLoading">刷新</button>
    
    <!-- 加载中状态 -->
    <div v-if="isLoading">加载中...</div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="error">
      {{ error }}
    </div>
    
    <!-- 数据展示 -->
    <ul v-else-if="users.length > 0">
      <li v-for="user in users" :key="user.id">
        {{ user.name }}
      </li>
    </ul>
    
    <!-- 空状态 -->
    <div v-else>没有用户数据</div>
  </div>
</template>`,
    explanation: '这个例子展示了如何使用 ref 管理异步数据流：我们创建了三个响应式状态 users、isLoading 和 error，它们协同工作来处理数据加载的各个阶段。当数据加载状态变化时，视图会自动切换不同的显示内容。这种模式非常适合处理 API 请求和异步操作。'
  }
]; 