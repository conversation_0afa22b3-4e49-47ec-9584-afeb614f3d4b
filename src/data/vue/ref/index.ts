import { ApiItem } from "@/types/api";
import { basicInfo } from './basic-info';
import { businessScenarios } from './business-scenarios';
import { implementation } from './implementation';
import { interviewQuestions } from './interview-questions';
import { knowledgeArchaeology } from './knowledge-archaeology';
import { commonQuestions } from './common-questions';

const refApi: ApiItem = {
  id: 'ref',
  title: 'ref()',
  description: '接受一个内部值并返回一个响应式且可变的 ref 对象',
  category: 'Composition API',
  difficulty: 'easy',
  syntax: 'const refValue = ref(value)',
  example: `import { ref } from 'vue'

const count = ref(0)
console.log(count.value) // 0

count.value++
console.log(count.value) // 1`,
  notes: '在模板中访问时会自动解包，不需要 .value',
  isNew: false,
  version: 'Vue 3.0+',
  tags: ['响应式', '基础', '常用'],
  
  // 使用导入的各个部分
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  knowledgeArchaeology,
  commonQuestions,
  
  relatedApis: ['reactive', 'computed', 'watch', 'unref', 'toRef']
};

export default refApi; 