/**
 * 统一设计系统 - 确保所有Tab组件样式一致性
 * 参考现代化设计原则：克制、专业、高级感
 */

// 颜色系统 - 克制的配色方案
export const colors = {
  // 主色调 - 使用中性色为主
  primary: {
    50: 'rgb(248 250 252)',   // slate-50
    100: 'rgb(241 245 249)',  // slate-100
    200: 'rgb(226 232 240)',  // slate-200
    300: 'rgb(203 213 225)',  // slate-300
    400: 'rgb(148 163 184)',  // slate-400
    500: 'rgb(100 116 139)',  // slate-500
    600: 'rgb(71 85 105)',    // slate-600
    700: 'rgb(51 65 85)',     // slate-700
    800: 'rgb(30 41 59)',     // slate-800
    900: 'rgb(15 23 42)',     // slate-900
  },
  
  // 功能色彩 - 语义化颜色
  semantic: {
    success: {
      50: 'rgb(240 253 244)',   // emerald-50
      100: 'rgb(209 250 229)',  // emerald-100
      500: 'rgb(16 185 129)',   // emerald-500
      600: 'rgb(5 150 105)',    // emerald-600
      700: 'rgb(4 120 87)',     // emerald-700
      800: 'rgb(6 95 70)',      // emerald-800
    },
    warning: {
      50: 'rgb(255 251 235)',   // amber-50
      100: 'rgb(254 243 199)',  // amber-100
      500: 'rgb(245 158 11)',   // amber-500
      600: 'rgb(217 119 6)',    // amber-600
      700: 'rgb(180 83 9)',     // amber-700
      800: 'rgb(146 64 14)',    // amber-800
    },
    error: {
      50: 'rgb(254 242 242)',   // red-50
      100: 'rgb(254 226 226)',  // red-100
      500: 'rgb(239 68 68)',    // red-500
      600: 'rgb(220 38 38)',    // red-600
      700: 'rgb(185 28 28)',    // red-700
      800: 'rgb(153 27 27)',    // red-800
    },
    info: {
      50: 'rgb(239 246 255)',   // blue-50
      100: 'rgb(219 234 254)',  // blue-100
      500: 'rgb(59 130 246)',   // blue-500
      600: 'rgb(37 99 235)',    // blue-600
      700: 'rgb(29 78 216)',    // blue-700
      800: 'rgb(30 64 175)',    // blue-800
    }
  }
};

// 间距系统
export const spacing = {
  xs: '0.5rem',    // 8px
  sm: '0.75rem',   // 12px
  md: '1rem',      // 16px
  lg: '1.5rem',    // 24px
  xl: '2rem',      // 32px
  '2xl': '3rem',   // 48px
  '3xl': '4rem',   // 64px
};

// 圆角系统
export const borderRadius = {
  sm: '0.375rem',   // 6px
  md: '0.5rem',     // 8px
  lg: '0.75rem',    // 12px
  xl: '1rem',       // 16px
  '2xl': '1.5rem',  // 24px
};

// 阴影系统
export const shadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
};

// 字体系统
export const typography = {
  fontFamily: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    mono: ['JetBrains Mono', 'Menlo', 'Monaco', 'Consolas', 'monospace'],
  },
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
  },
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  lineHeight: {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.625',
  }
};

// 组件样式类 - 可复用的样式组合
export const componentStyles = {
  // 卡片容器
  card: {
    base: `relative border border-slate-200/50 rounded-2xl bg-white/90 backdrop-blur-sm`,
    hover: `hover:shadow-lg transition-all duration-300`,
    gradient: `bg-gradient-to-br from-slate-50 to-gray-100/50`,
  },
  
  // 按钮样式
  button: {
    primary: `bg-slate-600 hover:bg-slate-700 text-white border-slate-600 hover:border-slate-700`,
    secondary: `bg-white hover:bg-slate-50 text-slate-600 hover:text-slate-900 border-slate-300 hover:border-slate-400`,
    ghost: `bg-transparent hover:bg-slate-50 text-slate-600 hover:text-slate-900 border-transparent`,
  },
  
  // 标签样式
  tag: {
    base: `px-3 py-1.5 text-xs font-medium rounded-lg border transition-all duration-200`,
    primary: `bg-slate-100 text-slate-700 border-slate-200/50 hover:shadow-sm`,
    success: `bg-emerald-50 text-emerald-700 border-emerald-200`,
    warning: `bg-amber-50 text-amber-700 border-amber-200`,
    error: `bg-red-50 text-red-700 border-red-200`,
  },
  
  // 代码块样式
  codeBlock: {
    container: `relative border border-slate-200/50 rounded-2xl overflow-hidden bg-white shadow-sm`,
    header: `flex items-center justify-between px-6 py-4 bg-gradient-to-r from-slate-50 to-gray-50 border-b border-slate-200/50`,
    content: `relative pl-14 pr-6 py-6 max-h-96 overflow-y-auto`,
    lineNumbers: `absolute left-0 top-0 bottom-0 w-12 bg-gradient-to-r from-slate-100/80 to-transparent border-r border-slate-200/50 flex flex-col justify-start pt-6 text-xs text-slate-400 font-mono z-10`,
    code: `text-slate-800 text-sm leading-5 font-mono`,
  },
  
  // 导航标签样式
  tabNavigation: {
    container: `flex gap-2 p-1.5 bg-slate-100/80 backdrop-blur-sm rounded-xl border border-slate-200/50`,
    tab: {
      base: `relative flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 group`,
      active: `bg-white shadow-lg shadow-slate-200/50 text-gray-900 border border-slate-200/50`,
      inactive: `text-gray-600 hover:text-gray-900 hover:bg-white/50`,
    },
    indicator: `absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-slate-600 to-gray-700 rounded-full`,
  },
  
  // 图标容器样式
  iconContainer: {
    sm: `w-6 h-6 rounded-lg flex items-center justify-center`,
    md: `w-8 h-8 rounded-lg flex items-center justify-center`,
    lg: `w-10 h-10 rounded-lg flex items-center justify-center`,
  },
  
  // 渐变背景
  gradients: {
    subtle: `bg-gradient-to-br from-slate-50/50 to-gray-100/30`,
    card: `bg-gradient-to-br from-slate-50 to-gray-100/50`,
    primary: `bg-gradient-to-br from-slate-600 to-gray-700`,
    success: `bg-gradient-to-br from-emerald-500 to-green-600`,
    warning: `bg-gradient-to-br from-amber-500 to-orange-600`,
    error: `bg-gradient-to-br from-red-500 to-rose-600`,
  }
};

// Tab特定的样式配置
export const tabStyles = {
  // 每个Tab的主题色
  themes: {
    basic: {
      primary: colors.semantic.info,
      icon: '📖',
      gradient: 'from-blue-50 to-indigo-50',
      border: 'border-blue-500',
    },
    business: {
      primary: colors.primary,
      icon: '💼',
      gradient: 'from-slate-50 to-gray-100',
      border: 'border-slate-500',
    },
    implementation: {
      primary: colors.semantic.info,
      icon: '🔬',
      gradient: 'from-blue-50 to-indigo-50',
      border: 'border-blue-500',
    },
    interview: {
      primary: colors.semantic.error,
      icon: '🎯',
      gradient: 'from-red-50 to-rose-50',
      border: 'border-red-500',
    },
    questions: {
      primary: colors.semantic.info,
      icon: '❓',
      gradient: 'from-blue-50 to-indigo-50',
      border: 'border-blue-500',
    },
    archaeology: {
      primary: colors.primary,
      icon: '🏛️',
      gradient: 'from-slate-50 to-gray-100',
      border: 'border-slate-500',
    },
    performance: {
      primary: colors.semantic.success,
      icon: '🚀',
      gradient: 'from-emerald-50 to-green-50',
      border: 'border-emerald-500',
    },
    debugging: {
      primary: colors.semantic.warning,
      icon: '🔧',
      gradient: 'from-amber-50 to-yellow-50',
      border: 'border-amber-500',
    },
    insights: {
      primary: colors.primary,
      icon: '💡',
      gradient: 'from-slate-50 to-gray-100',
      border: 'border-slate-500',
    },
  }
};

// 动画配置
export const animations = {
  transition: 'transition-all duration-300',
  hover: 'hover:shadow-lg hover:shadow-slate-200/20',
  focus: 'focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2',
};

// 响应式断点
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};
