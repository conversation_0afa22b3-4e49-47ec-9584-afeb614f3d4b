#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// 需要添加 Mermaid 图的特性及其对应的图表内容
const diagramsToAdd = {
  'promises': `graph TD
    A[Promises使用场景] --> B[异步操作管理]
    A --> C[错误处理]
    A --> D[状态管理]
    A --> E[链式操作]

    B --> B1[API请求]
    B --> B2[定时器操作]
    B --> B3[文件I/O]
    B --> B4[数据库查询]

    C --> C1[try/catch替代]
    C --> C2[错误传播]
    C --> C3[统一错误处理]

    D --> D1[pending状态]
    D --> D2[fulfilled状态]
    D --> D3[rejected状态]

    E --> E1[then链式调用]
    E --> E2[数据转换]
    E --> E3[条件执行]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`,

  'object-values': `graph TD
    A[Object.values使用场景] --> B[数据统计分析]
    A --> C[数组操作转换]
    A --> D[配置处理]
    A --> E[状态管理]

    B --> B1[求和计算]
    B --> B2[平均值统计]
    B --> B3[最值查找]
    B --> B4[数据聚合]

    C --> C1[map转换]
    C --> C2[filter过滤]
    C --> C3[reduce归约]

    D --> D1[环境变量]
    D --> D2[主题设置]
    D --> D3[功能开关]

    E --> E1[Redux状态]
    E --> E2[表单数据]
    E --> E3[缓存管理]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`,

  'symbols': `graph TD
    A[Symbol使用场景] --> B[私有属性]
    A --> C[元编程]
    A --> D[迭代器协议]
    A --> E[库开发]

    B --> B1[类私有字段]
    B --> B2[对象隐藏属性]
    B --> B3[命名空间隔离]

    C --> C1[Symbol.iterator]
    C --> C2[Symbol.toPrimitive]
    C --> C3[Symbol.hasInstance]

    D --> D1[自定义迭代]
    D --> D2[生成器函数]
    D --> D3[for...of支持]

    E --> E1[框架内部实现]
    E --> E2[插件系统]
    E --> E3[API扩展]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`,

  'nullish-coalescing': `graph TD
    A[空值合并使用场景] --> B[默认值设置]
    A --> C[配置对象处理]
    A --> D[API响应处理]
    A --> E[表单数据处理]

    B --> B1[函数参数默认值]
    B --> B2[对象属性默认值]
    B --> B3[环境变量处理]

    C --> C1[用户偏好设置]
    C --> C2[主题配置]
    C --> C3[功能开关]

    D --> D1[可选字段处理]
    D --> D2[嵌套对象访问]
    D --> D3[错误数据兜底]

    E --> E1[输入框默认值]
    E --> E2[选择器默认选项]
    E --> E3[复选框状态]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`,

  'generators': `graph TD
    A[生成器使用场景] --> B[惰性求值]
    A --> C[状态机实现]
    A --> D[异步流控制]
    A --> E[数据流处理]

    B --> B1[无限序列]
    B --> B2[大数据集处理]
    B --> B3[按需计算]

    C --> C1[游戏状态管理]
    C --> C2[工作流引擎]
    C --> C3[协议解析]

    D --> D1[异步迭代]
    D --> D2[背压控制]
    D --> D3[流式处理]

    E --> E1[数据转换管道]
    E --> E2[事件流处理]
    E --> E3[响应式编程]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`,

  'modules': `graph TD
    A[ES模块使用场景] --> B[代码组织]
    A --> C[依赖管理]
    A --> D[命名空间]
    A --> E[构建优化]

    B --> B1[功能模块化]
    B --> B2[组件封装]
    B --> B3[工具函数库]

    C --> C1[第三方库引入]
    C --> C2[内部模块依赖]
    C --> C3[动态导入]

    D --> D1[避免全局污染]
    D --> D2[作用域隔离]
    D --> D3[API暴露控制]

    E --> E1[Tree Shaking]
    E --> E2[代码分割]
    E --> E3[懒加载]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`,

  'object-entries': `graph TD
    A[Object.entries使用场景] --> B[对象遍历]
    A --> C[数据转换]
    A --> D[表单处理]
    A --> E[配置管理]

    B --> B1[键值对处理]
    B --> B2[条件过滤]
    B --> B3[数据映射]

    C --> C1[对象转数组]
    C --> C2[数据重构]
    C --> C3[格式转换]

    D --> D1[表单验证]
    D --> D2[数据序列化]
    D --> D3[字段映射]

    E --> E1[环境配置]
    E --> E2[主题设置]
    E --> E3[功能开关]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`,

  'iterators': `graph TD
    A[迭代器使用场景] --> B[自定义遍历]
    A --> C[数据结构实现]
    A --> D[惰性计算]
    A --> E[协议实现]

    B --> B1[树形结构遍历]
    B --> B2[图形算法]
    B --> B3[自定义顺序]

    C --> C1[链表实现]
    C --> C2[队列栈实现]
    C --> C3[集合类型]

    D --> D1[无限序列]
    D --> D2[按需生成]
    D --> D3[内存优化]

    E --> E1[Symbol.iterator]
    E --> E2[for...of支持]
    E --> E3[解构赋值]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`,

  'string-padding': `graph TD
    A[字符串填充使用场景] --> B[文本对齐]
    A --> C[格式化输出]
    A --> D[数据展示]
    A --> E[日志系统]

    B --> B1[表格列对齐]
    B --> B2[数字右对齐]
    B --> B3[文本居中]

    C --> C1[报表生成]
    C --> C2[控制台输出]
    C --> C3[文件格式化]

    D --> D1[数据表格]
    D --> D2[进度条显示]
    D --> D3[状态指示器]

    E --> E1[日志级别对齐]
    E --> E2[时间戳格式]
    E --> E3[结构化输出]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec`
};

function addDiagramToFile(filePath, diagramContent) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否已经有 scenarioDiagram
    if (content.includes('scenarioDiagram')) {
      console.log(`⏭️  跳过: ${filePath} (已有图表)`);
      return false;
    }
    
    // 找到文件结尾的 }; 并在之前插入图表
    const insertPoint = content.lastIndexOf('};');
    if (insertPoint === -1) {
      console.log(`❌ 错误: ${filePath} (找不到插入点)`);
      return false;
    }
    
    // 在最后的 }; 之前插入图表
    const beforeInsert = content.substring(0, insertPoint);
    const afterInsert = content.substring(insertPoint);
    
    // 构建要插入的内容
    const diagramCode = `,

  scenarioDiagram: \`${diagramContent}\``;
    
    const newContent = beforeInsert + diagramCode + '\n' + afterInsert;
    
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`✅ 添加: ${filePath}`);
    return true;
  } catch (error) {
    console.error(`❌ 错误: ${filePath} - ${error.message}`);
    return false;
  }
}

function main() {
  console.log('🎨 开始添加 Mermaid 图表...\n');
  
  let addedCount = 0;
  let totalCount = 0;
  
  for (const [featureName, diagramContent] of Object.entries(diagramsToAdd)) {
    const filePath = `src/data/ecma/${featureName}/basic-info.ts`;
    totalCount++;
    
    if (addDiagramToFile(filePath, diagramContent)) {
      addedCount++;
    }
  }
  
  console.log(`\n📊 添加完成:`);
  console.log(`   总特性数: ${totalCount}`);
  console.log(`   添加图表数: ${addedCount}`);
  console.log(`   跳过数: ${totalCount - addedCount}`);
}

main();
