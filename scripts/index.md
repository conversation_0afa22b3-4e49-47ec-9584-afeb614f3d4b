# Scripts 目录说明

本目录包含用于API文档开发和测试的自动化脚本。

## 📁 脚本列表

### 🚀 主要工作流程脚本

#### `api-workflow.ts`
**完整的API文档开发工作流程**
- **作用**: 一站式API文档生成和开发流程
- **功能**: 
  - 生成API骨架结构
  - 初始语法验证
  - 提供开发指引
  - 全面测试和修复
  - 更新进度文件
- **使用方法**: 
  ```bash
  # 基础用法
  tsx scripts/api-workflow.ts <api-id>
  
  # 带测试的完整流程
  tsx scripts/api-workflow.ts <api-id> --test
  
  # 预设API示例
  tsx scripts/api-workflow.ts useDebugValue
  tsx scripts/api-workflow.ts ReactMemo --test
  ```
- **预设配置**: 支持 useDebugValue, useImperativeHandle, useDeferredValue, ReactMemo
- **推荐**: ⭐⭐⭐⭐⭐ 新API开发的首选工具

---

### 🏗️ 骨架生成脚本

#### `generate-api-skeleton.ts`
**生成API文档骨架结构**
- **作用**: 创建完整的9个Tab文件骨架
- **功能**:
  - 生成所有必需的Tab文件（basic-info.ts, business-scenarios.ts等）
  - 创建主配置文件（index.ts）
  - 自动更新瀑布流主文件
  - 包含占位符和TODO标记
- **使用方法**:
  ```bash
  tsx scripts/generate-api-skeleton.ts <api-id>
  ```
- **生成文件**:
  - `src/data/react/hooks/<api-id>/` 目录
  - 9个Tab文件 + index.ts
  - 自动添加到主索引文件
- **推荐**: ⭐⭐⭐⭐ 快速创建API结构

---

### 🔍 验证和测试脚本

#### `validate-api-syntax.ts`
**API语法验证工具**
- **作用**: 检查单个API的语法正确性
- **功能**:
  - TypeScript语法检查
  - 文件结构验证
  - 导入导出检查
  - 占位符和TODO检测
- **使用方法**:
  ```bash
  tsx scripts/validate-api-syntax.ts <api-name>
  ```
- **推荐**: ⭐⭐⭐⭐ 开发过程中的必备工具

#### `unified-test.ts`
**统一测试工具 (替代多个分散脚本)**
- **作用**: 集成语法验证、渲染测试、综合检测于一体
- **功能**:
  - 语法检测 (TypeScript编译检查)
  - 渲染测试 (数据结构验证)
  - 综合检测 (文件完整性、注册状态)
  - 批量测试所有API
  - 详细报告生成
- **使用方法**:
  ```bash
  # 测试所有API (推荐)
  tsx scripts/unified-test.ts

  # 测试单个API
  tsx scripts/unified-test.ts ReactMemo

  # 只进行语法检测
  tsx scripts/unified-test.ts --mode syntax

  # 详细模式
  tsx scripts/unified-test.ts ReactMemo --verbose
  ```
- **输出**: `test-results/unified-test-report.json`
- **推荐**: ⭐⭐⭐⭐⭐ 替代所有测试脚本的统一工具

#### `capture-render-errors.ts`
**浏览器渲染错误捕获工具**
- **作用**: 自动化访问页面并收集所有JavaScript错误和渲染问题
- **功能**:
  - 自动打开浏览器访问测试页面
  - 捕获所有控制台错误、警告、日志
  - 监听页面错误、网络错误、运行时异常
  - 检测Tab渲染状态和错误
  - 自动截图保存错误现场
  - 生成详细的HTML和JSON报告
- **使用方法**:
  ```bash
  # 测试单个API
  tsx scripts/capture-render-errors.ts ReactMemo

  # 测试所有API
  tsx scripts/capture-render-errors.ts --all

  # 无头模式运行
  tsx scripts/capture-render-errors.ts ReactMemo --headless

  # 自定义URL
  tsx scripts/capture-render-errors.ts ReactMemo --url http://localhost:3000
  ```
- **输出**:
  - `test-results/render-error-report.json` (详细数据)
  - `test-results/render-error-report.html` (可视化报告)
  - `test-results/screenshots/` (错误截图)
- **推荐**: ⭐⭐⭐⭐⭐ 真实浏览器环境的错误检测

---

## 🔄 推荐工作流程

### 新API开发流程
1. **生成骨架**: `tsx scripts/api-workflow.ts <api-id>`
2. **开发内容**: 按优先级完善各Tab文件
3. **验证语法**: `tsx scripts/validate-api-syntax.ts <api-id>`
4. **全面测试**: `tsx scripts/test-single-api.ts <api-id>`
5. **最终验证**: `npm run build`

### 问题排查流程
1. **浏览器错误捕获**: `tsx scripts/capture-render-errors.ts <api-name>`
2. **统一测试**: `tsx scripts/unified-test.ts <api-name> --verbose`
3. **语法验证**: `tsx scripts/validate-api-syntax.ts <api-name>`
4. **查看可视化报告**: `test-results/render-error-report.html`

### 批量测试流程
1. **全量浏览器测试**: `tsx scripts/capture-render-errors.ts --all`
2. **全量统一测试**: `tsx scripts/unified-test.ts`
3. **查看报告**: `test-results/render-error-report.html`

---

## 📊 脚本优先级

| 脚本 | 优先级 | 使用频率 | 适用场景 |
|------|--------|----------|----------|
| `api-workflow.ts` | 🔴 高 | 每个新API | 新API开发 |
| `capture-render-errors.ts` | 🔴 高 | 问题排查时 | 浏览器错误捕获 |
| `unified-test.ts` | 🔴 高 | 开发/测试时 | 统一测试 |
| `validate-api-syntax.ts` | 🟡 中 | 开发过程中 | 语法检查 |
| `generate-api-skeleton.ts` | 🟡 中 | 需要时 | 快速骨架 |

---

## 💡 使用建议

1. **新手开发者**: 直接使用 `api-workflow.ts`，它会提供完整的开发指引
2. **经验开发者**: 使用 `generate-api-skeleton.ts` + `validate-api-syntax.ts` 组合
3. **问题排查**: 使用 `comprehensive-api-test.ts` 进行深度检测
4. **批量验证**: 使用 `test-api-rendering.ts` 检测所有API状态

## 🔧 环境要求

- Node.js 18+
- TypeScript 5.0+
- tsx (用于直接运行TypeScript)

## 📝 注意事项

- 所有脚本都支持 `--help` 参数查看详细用法
- 测试报告会保存在 `test-results/` 目录
- 建议在开发过程中频繁运行验证脚本
- 遇到问题时，优先查看脚本输出的建议和错误信息
