#!/usr/bin/env tsx

import * as fs from 'fs-extra';
import { readdir, readFile, writeFile } from 'fs/promises';
import * as path from 'path';
import chalk from 'chalk';

/**
 * Vue API目录结构重构脚本
 * 
 * 目标：将 vue/composition-api/xxx 统一重构为 vue/xxx (平铺结构)
 * 
 * 重构内容：
 * 1. 移动所有API目录到平铺结构
 * 2. 更新所有import路径
 * 3. 与React保持一致的目录结构
 */

interface VueRestructureConfig {
  sourceRoot: string;
  backupDir: string;
  dryRun: boolean;
}

class VueApiRestructurer {
  private config: VueRestructureConfig;
  private operations: Array<{ type: 'move' | 'update'; from: string; to: string; description: string }> = [];

  constructor(config: VueRestructureConfig) {
    this.config = config;
  }

  async run() {
    console.log(chalk.blue('🚀 开始Vue API目录结构重构'));
    console.log(chalk.gray(`模式: ${this.config.dryRun ? '预览模式（不执行实际操作）' : '执行模式'}`));
    
    try {
      // 1. 创建备份
      if (!this.config.dryRun) {
        await this.createBackup();
      }

      // 2. 分析现有结构
      const structure = await this.analyzeCurrentStructure();
      
      // 3. 执行重构
      await this.executeRestructure(structure);

      // 4. 更新引用
      await this.updateReferences();

      console.log(chalk.green('✅ Vue重构完成！'));
      return true;

    } catch (error) {
      console.error(chalk.red('❌ Vue重构失败：'), error);
      return false;
    }
  }

  private async createBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(this.config.backupDir, `vue-backup-${timestamp}`);
    
    console.log(chalk.yellow('📦 创建Vue备份...'));
    await fs.copy(this.config.sourceRoot, backupPath);
    console.log(chalk.green(`✅ Vue备份已创建: ${backupPath}`));
  }

  private async analyzeCurrentStructure() {
    const vueRoot = this.config.sourceRoot;
    const structure = {
      compositionApi: [] as string[]
    };

    // 分析composition-api目录
    const compositionApiDir = path.join(vueRoot, 'composition-api');
    if (await fs.pathExists(compositionApiDir)) {
      const apiDirs = await readdir(compositionApiDir, { withFileTypes: true });
      structure.compositionApi = apiDirs
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);
    }

    console.log(chalk.blue('📊 Vue当前结构分析：'));
    console.log(chalk.gray(`  Composition API: ${structure.compositionApi.length}个`));
    console.log(chalk.gray(`  APIs: ${structure.compositionApi.join(', ')}`));
    
    return structure;
  }

  private async executeRestructure(structure: { compositionApi: string[] }) {
    const vueRoot = this.config.sourceRoot;
    
    console.log(chalk.blue('🔧 执行Vue目录重构...'));

    // 移动composition-api下的所有API
    for (const apiName of structure.compositionApi) {
      const fromPath = path.join(vueRoot, 'composition-api', apiName);
      const toPath = path.join(vueRoot, apiName);
      
      this.operations.push({
        type: 'move',
        from: fromPath,
        to: toPath,
        description: `移动Vue API: composition-api/${apiName} → ${apiName}`
      });

      if (!this.config.dryRun) {
        await fs.move(fromPath, toPath);
      }
      console.log(chalk.green(`  ✅ composition-api/${apiName} → ${apiName}`));
    }

    // 删除空目录
    if (!this.config.dryRun) {
      const compositionApiDir = path.join(vueRoot, 'composition-api');
      
      if (await fs.pathExists(compositionApiDir) && (await readdir(compositionApiDir)).length === 0) {
        await fs.remove(compositionApiDir);
        console.log(chalk.gray('  🗑️  删除空的composition-api目录'));
      }
    }
  }

  private async updateReferences() {
    console.log(chalk.blue('🔄 更新Vue引用路径...'));

    const filesToUpdate = [
      // 主index文件
      path.join(this.config.sourceRoot, 'index.ts'),
      // 其他可能的引用文件
    ];

    for (const filePath of filesToUpdate) {
      if (await fs.pathExists(filePath)) {
        await this.updateFileReferences(filePath);
      }
    }
  }

  private async updateFileReferences(filePath: string) {
    if (this.config.dryRun) {
      console.log(chalk.gray(`  预览: 将更新 ${filePath}`));
      return;
    }

    const content = await readFile(filePath, 'utf-8');
    let updatedContent = content;

    // 更新import路径
    updatedContent = updatedContent.replace(
      /from ['"](\.\/composition-api\/|@\/data\/vue\/composition-api\/)([^'"]+)['"]/g,
      (match, prefix, apiName) => {
        // 提取API名称（去掉可能的/index部分）
        const cleanApiName = apiName.replace(/\/index$/, '');
        return match.replace(prefix + apiName, `./${cleanApiName}`);
      }
    );

    // 更新动态import
    updatedContent = updatedContent.replace(
      /import\([`'"]@\/data\/vue\/composition-api\/([^`'"]+)[`'"]\)/g,
      'import(`@/data/vue/$1`)'
    );

    if (updatedContent !== content) {
      await writeFile(filePath, updatedContent);
      console.log(chalk.green(`  ✅ 已更新: ${path.relative(process.cwd(), filePath)}`));
    }
  }

  printSummary() {
    console.log(chalk.blue('\n📋 Vue重构操作摘要：'));
    console.log(chalk.gray(`总操作数: ${this.operations.length}`));
    
    const moveOps = this.operations.filter(op => op.type === 'move');
    const updateOps = this.operations.filter(op => op.type === 'update');
    
    console.log(chalk.gray(`目录移动: ${moveOps.length}`));
    console.log(chalk.gray(`引用更新: ${updateOps.length}`));

    if (this.config.dryRun) {
      console.log(chalk.yellow('\n⚠️  这是预览模式，未执行实际操作'));
      console.log(chalk.yellow('若要执行，请运行: tsx scripts/restructure-vue-apis.ts --execute'));
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  
  const config: VueRestructureConfig = {
    sourceRoot: path.join(process.cwd(), 'src/data/vue'),
    backupDir: path.join(process.cwd(), 'backups'),
    dryRun
  };

  // 确保备份目录存在
  await fs.ensureDir(config.backupDir);

  const restructurer = new VueApiRestructurer(config);
  const success = await restructurer.run();
  
  restructurer.printSummary();
  
  if (!success) {
    process.exit(1);
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error(chalk.red('💥 Vue重构脚本执行失败：'), error);
    process.exit(1);
  });
}

export default VueApiRestructurer; 