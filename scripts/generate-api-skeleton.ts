#!/usr/bin/env tsx

/**
 * 🚨 重要说明：API骨架生成器
 * 
 * 这个脚本的作用：
 * ✅ 生成API文档的骨架结构（9个Tab文件 + index.ts）
 * ✅ 生成占位符模板（如 {API_NAME}、{FRAMEWORK} 等）
 * ❌ 不生成真正的文档内容！
 * 
 * 参数说明：
 * --generate-content: 🚨 误导性命名！实际上只生成占位符骨架
 * 
 * 真正的内容生成需要：
 * 1. 手动编辑每个Tab文件
 * 2. 替换 {XXX} 占位符为实际内容  
 * 3. 使用 Cursor + MDC模板进行AI辅助
 * 4. 修改 completionStatus: '内容已完成'
 */

import fs from 'fs';
import path from 'path';

interface ApiConfig {
  id: string;
  title: string;
  description: string;
  category: 'React Hooks' | 'React Components' | 'React APIs';
  difficulty: 'easy' | 'medium' | 'hard';
  version: string;
  tags: string[];
}

// 🚨 安全的模板生成函数 - 严格遵循骨架生成器.mdc规范
function generateBasicInfo(): string {
  return `import { BasicInfo } from '@/types/api';

const basicInfo: BasicInfo = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '当前tab内容未完成',
  
  definition: "{API_NAME}是{FRAMEWORK}中用于{CORE_PURPOSE}的{API_TYPE}",
  
  introduction: \`{API_NAME}是{FRAMEWORK} {VERSION}引入的{API_TYPE}，主要用于{PRIMARY_USE_CASE}、{SECONDARY_USE_CASE}和{TERTIARY_USE_CASE}。它采用{DESIGN_PATTERN}的设计模式，提供了{KEY_BENEFIT}。\`,

  syntax: \`{FULL_SYNTAX}\`,

  quickExample: \`function {API_NAME}Example() {
  // {API调用说明 - 中文注释}
  const [变量名] = {API_NAME}({参数说明});

  return (
    <div>
      {/* {使用场景说明 - 中文注释} */}
      <{组件名}
        {属性名}={变量名}
        {其他属性}="{值}"
      >
        {内容说明}
      </{组件名}>
    </div>
  );
}\`,

  scenarioDiagram: \`graph TD
    A[用户交互场景] --> B[{场景1}]
    A --> C[{场景2}]
    A --> D[{场景3}]

    B --> B1[{具体用法1}]
    B --> B2[{具体用法2}]
    B --> B3[{具体用法3}]

    C --> C1[{具体用法1}]
    C --> C2[{具体用法2}]
    C --> C3[{具体用法3}]

    D --> D1[{具体用法1}]
    D --> D2[{具体用法2}]
    D --> D3[{具体用法3}]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8\`,
  
  parameters: [
    {
      name: "{PARAM_NAME}",
      type: "{PARAM_TYPE}",
      required: true,
      description: "{PARAM_DESCRIPTION}",
      example: "{PARAM_EXAMPLE}"
    }
  ],
  
  returnValue: {
    type: "{RETURN_TYPE}",
    description: "{RETURN_DESCRIPTION}",
    example: "{RETURN_EXAMPLE}"
  },
  
  keyFeatures: [
    {
      title: "{FEATURE_1_TITLE}",
      description: "{FEATURE_1_DESCRIPTION}",
      benefit: "{FEATURE_1_BENEFIT}"
    }
  ],
  
  limitations: [
    "{LIMITATION_1}",
    "{LIMITATION_2}",
    "{LIMITATION_3}"
  ],
  
  bestPractices: [
    "{BEST_PRACTICE_1}",
    "{BEST_PRACTICE_2}",
    "{BEST_PRACTICE_3}",
    "{BEST_PRACTICE_4}",
    "{BEST_PRACTICE_5}"
  ],
  
  warnings: [
    "{WARNING_1}",
    "{WARNING_2}",
    "{WARNING_3}"
  ]
};

// 占位内容，具体内容请参考 @1-基本信息.mdc
export default basicInfo;`;
}

function generateBusinessScenarios(): string {
  return `import { BusinessScenario } from '@/types/api';

const businessScenarios: BusinessScenario[] = [
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '当前tab内容未完成',
    
    id: 'scenario-1',
    title: '{SCENARIO_1_TITLE}',
    description: '{SCENARIO_1_DESCRIPTION}',
    businessValue: '{SCENARIO_1_BUSINESS_VALUE}',
    scenario: '{SCENARIO_1_SCENARIO}',
    code: \`{SCENARIO_1_CODE}\`,
    explanation: '{SCENARIO_1_EXPLANATION}',
    benefits: [
      '{SCENARIO_1_BENEFIT_1}',
      '{SCENARIO_1_BENEFIT_2}',
      '{SCENARIO_1_BENEFIT_3}'
    ],
    metrics: {
      performance: '{SCENARIO_1_PERFORMANCE_METRIC}',
      userExperience: '{SCENARIO_1_UX_METRIC}',
      technicalMetrics: '{SCENARIO_1_TECHNICAL_METRIC}'
    },
    difficulty: 'easy',
    tags: ['{SCENARIO_1_TAG_1}', '{SCENARIO_1_TAG_2}', '{SCENARIO_1_TAG_3}']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '当前tab内容未完成',
    
    id: 'scenario-2',
    title: '{SCENARIO_2_TITLE}',
    description: '{SCENARIO_2_DESCRIPTION}',
    businessValue: '{SCENARIO_2_BUSINESS_VALUE}',
    scenario: '{SCENARIO_2_SCENARIO}',
    code: \`{SCENARIO_2_CODE}\`,
    explanation: '{SCENARIO_2_EXPLANATION}',
    benefits: [
      '{SCENARIO_2_BENEFIT_1}',
      '{SCENARIO_2_BENEFIT_2}',
      '{SCENARIO_2_BENEFIT_3}',
      '{SCENARIO_2_BENEFIT_4}'
    ],
    metrics: {
      performance: '{SCENARIO_2_PERFORMANCE_METRIC}',
      userExperience: '{SCENARIO_2_UX_METRIC}',
      technicalMetrics: '{SCENARIO_2_TECHNICAL_METRIC}'
    },
    difficulty: 'medium',
    tags: ['{SCENARIO_2_TAG_1}', '{SCENARIO_2_TAG_2}', '{SCENARIO_2_TAG_3}']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '当前tab内容未完成',
    
    id: 'scenario-3',
    title: '{SCENARIO_3_TITLE}',
    description: '{SCENARIO_3_DESCRIPTION}',
    businessValue: '{SCENARIO_3_BUSINESS_VALUE}',
    scenario: '{SCENARIO_3_SCENARIO}',
    code: \`{SCENARIO_3_CODE}\`,
    explanation: '{SCENARIO_3_EXPLANATION}',
    benefits: [
      '{SCENARIO_3_BENEFIT_1}',
      '{SCENARIO_3_BENEFIT_2}',
      '{SCENARIO_3_BENEFIT_3}',
      '{SCENARIO_3_BENEFIT_4}',
      '{SCENARIO_3_BENEFIT_5}'
    ],
    metrics: {
      performance: '{SCENARIO_3_PERFORMANCE_METRIC}',
      userExperience: '{SCENARIO_3_UX_METRIC}',
      technicalMetrics: '{SCENARIO_3_TECHNICAL_METRIC}'
    },
    difficulty: 'hard',
    tags: ['{SCENARIO_3_TAG_1}', '{SCENARIO_3_TAG_2}', '{SCENARIO_3_TAG_3}', '{SCENARIO_3_TAG_4}']
  }
];

// 占位内容，具体内容请参考 @2-业务场景.mdc
export default businessScenarios;`;
}

function generateImplementation(): string {
  return `import { Implementation } from '@/types/api';

const implementation: Implementation = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '当前tab内容未完成',
  
  mechanism: \`{IMPLEMENTATION_MECHANISM}\`,

  visualization: \`graph TD
    A["{NODE_A}"] --> B["{NODE_B}"]
    B --> C["{NODE_C}"]
    C --> D["{NODE_D}"]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0\`,
    
  plainExplanation: \`{IMPLEMENTATION_PLAIN_EXPLANATION}\`,

  designConsiderations: [
    '{DESIGN_CONSIDERATION_1}',
    '{DESIGN_CONSIDERATION_2}',
    '{DESIGN_CONSIDERATION_3}',
    '{DESIGN_CONSIDERATION_4}',
    '{DESIGN_CONSIDERATION_5}'
  ],
  
  relatedConcepts: [
    '{RELATED_CONCEPT_1}',
    '{RELATED_CONCEPT_2}',
    '{RELATED_CONCEPT_3}',
    '{RELATED_CONCEPT_4}'
  ]
};

// 占位内容，具体内容请参考 @3-原理解析.mdc
export default implementation;`;
}

function generateInterviewQuestions(): string {
  return `import { InterviewQuestion } from '@/types/api';

const interviewQuestions: InterviewQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '当前tab内容未完成',
    
    id: 1,
    question: '{QUESTION_1}',
    difficulty: 'easy',
    frequency: 'high',
    category: '{CATEGORY_1}',
    answer: {
      brief: '{ANSWER_1_BRIEF}',
      detailed: \`{ANSWER_1_DETAILED}\`,
      code: \`{ANSWER_1_CODE}\`
    },
    tags: ['{TAG_1_1}', '{TAG_1_2}']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '当前tab内容未完成',
    
    id: 2,
    question: '{QUESTION_2}',
    difficulty: 'medium',
    frequency: 'high',
    category: '{CATEGORY_2}',
    answer: {
      brief: '{ANSWER_2_BRIEF}',
      detailed: \`{ANSWER_2_DETAILED}\`,
      code: \`{ANSWER_2_CODE}\`
    },
    tags: ['{TAG_2_1}', '{TAG_2_2}']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '当前tab内容未完成',
    
    id: 3,
    question: '{QUESTION_3}',
    difficulty: 'hard',
    frequency: 'medium',
    category: '{CATEGORY_3}',
    answer: {
      brief: '{ANSWER_3_BRIEF}',
      detailed: \`{ANSWER_3_DETAILED}\`,
      code: \`{ANSWER_3_CODE}\`
    },
    tags: ['{TAG_3_1}', '{TAG_3_2}']
  }
];

// 占位内容，具体内容请参考 @4-面试准备.mdc
export default interviewQuestions;`;
}

function generateCommonQuestions(): string {
  return `import { CommonQuestion } from '@/types/api';

const commonQuestions: CommonQuestion[] = [
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '当前tab内容未完成',
    
    id: 'question-1',
    question: '{QUESTION_1}',
    answer: \`{ANSWER_1}\`,
    code: \`{CODE_1}\`,
    tags: ['{TAG_1_1}', '{TAG_1_2}'],
    relatedQuestions: ['{RELATED_1_1}', '{RELATED_1_2}']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '当前tab内容未完成',
    
    id: 'question-2',
    question: '{QUESTION_2}',
    answer: \`{ANSWER_2}\`,
    code: \`{CODE_2}\`,
    tags: ['{TAG_2_1}', '{TAG_2_2}'],
    relatedQuestions: ['{RELATED_2_1}', '{RELATED_2_2}']
  },
  {
    // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
    completionStatus: '当前tab内容未完成',
    
    id: 'question-3',
    question: '{QUESTION_3}',
    answer: \`{ANSWER_3}\`,
    code: \`{CODE_3}\`,
    tags: ['{TAG_3_1}', '{TAG_3_2}'],
    relatedQuestions: ['{RELATED_3_1}', '{RELATED_3_2}']
  }
];

// 占位内容，具体内容请参考 @5-常见问题.mdc
export default commonQuestions;`;
}

function generateKnowledgeArchaeology(): string {
  return `import { KnowledgeArchaeology } from '@/types/api';

const knowledgeArchaeology: KnowledgeArchaeology = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '当前tab内容未完成',
  
  introduction: \`{INTRODUCTION}\`,
  
  background: \`{BACKGROUND}\`,

  evolution: \`{EVOLUTION}\`,

  timeline: [
    {
      year: '{YEAR_1}',
      event: '{EVENT_1}',
      description: '{DESCRIPTION_1}',
      significance: '{SIGNIFICANCE_1}'
    },
    {
      year: '{YEAR_2}',
      event: '{EVENT_2}',
      description: '{DESCRIPTION_2}',
      significance: '{SIGNIFICANCE_2}'
    }
  ],

  keyFigures: [
    {
      name: '{NAME_1}',
      role: '{ROLE_1}',
      contribution: '{CONTRIBUTION_1}',
      significance: '{SIGNIFICANCE_1}'
    }
  ],

  concepts: [
    {
      term: '{TERM_1}',
      definition: '{DEFINITION_1}',
      evolution: '{EVOLUTION_1}',
      modernRelevance: '{MODERN_RELEVANCE_1}'
    }
  ],

  designPhilosophy: \`{DESIGN_PHILOSOPHY}\`,

  impact: \`{IMPACT}\`,

  modernRelevance: \`{MODERN_RELEVANCE}\`
};

// 占位内容，具体内容请参考 @6-知识考古.mdc
export default knowledgeArchaeology;`;
}

function generatePerformanceOptimization(): string {
  return `import { PerformanceOptimization } from '@/types/api';

const performanceOptimization: PerformanceOptimization = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '当前tab内容未完成',
  
  optimizationStrategies: [
    {
      strategy: '{STRATEGY_1}',
      description: '{DESCRIPTION_1}',
      implementation: \`{IMPLEMENTATION_1}\`,
      impact: '{IMPACT_1}'
    }
  ],

  benchmarks: [
    {
      scenario: '{SCENARIO_1}',
      description: '{DESCRIPTION_1}',
      metrics: {
        '{METRIC_1_NAME}': '{METRIC_1_VALUE}',
        '{METRIC_2_NAME}': '{METRIC_2_VALUE}'
      },
      conclusion: '{CONCLUSION_1}'
    }
  ],

  monitoring: {
    tools: [
      {
        name: '{TOOL_1_NAME}',
        description: '{TOOL_1_DESCRIPTION}',
        usage: \`{TOOL_1_USAGE}\`
      }
    ],
    
    metrics: [
      {
        metric: '{METRIC_1}',
        description: '{METRIC_1_DESCRIPTION}',
        target: '{METRIC_1_TARGET}',
        measurement: '{METRIC_1_MEASUREMENT}'
      }
    ]
  },

  bestPractices: [
    {
      practice: '{PRACTICE_1}',
      description: '{PRACTICE_1_DESCRIPTION}',
      example: \`{PRACTICE_1_EXAMPLE}\`
    }
  ]
};

// 占位内容，具体内容请参考 @7-性能优化.mdc
export default performanceOptimization;`;
}

function generateDebuggingTips(): string {
  return `import { DebuggingTips } from '@/types/api';

const debuggingTips: DebuggingTips = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '当前tab内容未完成',
  
  subTabs: [
    {
      key: 'common-issues',
      title: '🚨 常见问题',
      content: {
        introduction: '{INTRODUCTION}',
        sections: [
          {
            title: '{SECTION_1_TITLE}',
            description: '{SECTION_1_DESCRIPTION}',
            items: [
              {
                title: '{ITEM_1_TITLE}',
                description: '{ITEM_1_DESCRIPTION}',
                solution: '{ITEM_1_SOLUTION}',
                prevention: '{ITEM_1_PREVENTION}',
                code: \`{ITEM_1_CODE}\`
              }
            ]
          }
        ]
      }
    },
    {
      key: 'dev-tools',
      title: '🔧 开发工具',
      content: {
        introduction: '{INTRODUCTION}',
        sections: [
          {
            title: '{SECTION_2_TITLE}',
            description: '{SECTION_2_DESCRIPTION}',
            items: [
              {
                title: '{ITEM_2_TITLE}',
                description: '{ITEM_2_DESCRIPTION}',
                solution: '{ITEM_2_SOLUTION}',
                prevention: '{ITEM_2_PREVENTION}',
                code: \`{ITEM_2_CODE}\`
              }
            ]
          }
        ]
      }
    }
  ]
};

// 占位内容，具体内容请参考 @8-调试技巧.mdc
export default debuggingTips;`;
}

function generateEssenceInsights(): string {
  return `import { EssenceInsights } from '@/types/api';

const essenceInsights: EssenceInsights = {
  // 🏗️ Tab完成状态标识 - 请在内容完成后修改为 '内容已完成'
  completionStatus: '当前tab内容未完成',
  
  coreQuestion: \`{CORE_QUESTION}\`,

  designPhilosophy: {
    worldview: \`{WORLDVIEW}\`,
    methodology: \`{METHODOLOGY}\`,
    tradeoffs: \`{TRADEOFFS}\`,
    evolution: \`{EVOLUTION}\`
  },

  hiddenTruth: {
    surfaceProblem: \`{SURFACE_PROBLEM}\`,
    realProblem: \`{REAL_PROBLEM}\`,
    hiddenCost: \`{HIDDEN_COST}\`,
    deeperValue: \`{DEEPER_VALUE}\`
  },

  deeperQuestions: [],

  paradigmShift: {
    oldParadigm: {
      assumption: \`{OLD_ASSUMPTION}\`,
      limitation: \`{OLD_LIMITATION}\`,
      worldview: \`{OLD_WORLDVIEW}\`
    },
    newParadigm: {
      breakthrough: \`{NEW_BREAKTHROUGH}\`,
      possibility: \`{NEW_POSSIBILITY}\`,
      cost: \`{NEW_COST}\`
    },
    transition: {
      resistance: \`{TRANSITION_RESISTANCE}\`,
      catalyst: \`{TRANSITION_CATALYST}\`,
      tippingPoint: \`{TRANSITION_TIPPING_POINT}\`
    }
  },

  universalPrinciples: []
};

// 占位内容，具体内容请参考 @9-本质洞察.mdc
export default essenceInsights;`;
}

function generateApiSkeleton(config: ApiConfig) {
  // 根据category确定目录
  const categoryDir = config.category === 'React Components' ? 'components' :
                     config.category === 'React APIs' ? 'apis' : 'hooks';
  const apiDir = path.join('src/data/react', categoryDir, config.id);
  
  // 创建目录
  if (!fs.existsSync(apiDir)) {
    fs.mkdirSync(apiDir, { recursive: true });
  }

  // 生成文件映射
  const fileGenerators = {
    'basic-info.ts': generateBasicInfo,
    'business-scenarios.ts': generateBusinessScenarios,
    'implementation.ts': generateImplementation,
    'interview-questions.ts': generateInterviewQuestions,
    'common-questions.ts': generateCommonQuestions,
    'knowledge-archaeology.ts': generateKnowledgeArchaeology,
    'performance-optimization.ts': generatePerformanceOptimization,
    'debugging-tips.ts': generateDebuggingTips,
    'essence-insights.ts': generateEssenceInsights
  };

  // 生成9个Tab文件
  Object.entries(fileGenerators).forEach(([fileName, generator]) => {
    const filePath = path.join(apiDir, fileName);
    const content = generator();
    fs.writeFileSync(filePath, content);
    console.log(`✅ 生成文件: ${filePath}`);
  });

  // 生成主配置文件
  generateIndexFile(config, apiDir);
  
  // 更新瀑布流主文件
  updateMainIndex(config);
  
  // 🚨 强制数据一致性提醒
  displayDataConsistencyReminder(config);
  
  console.log('🎉 ' + config.title + ' API骨架生成完成！');
  console.log('');
  console.log('⚠️ 重要提醒：');
  console.log('1. 使用Mermaid图表时必须用MermaidChart组件渲染');
  console.log('2. 确保数据结构与前端组件期望一致');
  console.log('3. 运行 tsx scripts/puppeteer/index.ts ' + config.id + ' --headless 验证渲染');
  console.log('');
}

function generateIndexFile(config: ApiConfig, apiDir: string) {
  const indexContent = `import { ApiItem } from '@/types/api';
import basicInfo from './basic-info';
import businessScenarios from './business-scenarios';
import implementation from './implementation';
import interviewQuestions from './interview-questions';
import commonQuestions from './common-questions';
import knowledgeArchaeology from './knowledge-archaeology';
import performanceOptimization from './performance-optimization';
import debuggingTips from './debugging-tips';
import essenceInsights from './essence-insights';

const ` + config.id.replace(/-/g, '') + `Data: ApiItem = {
  id: '` + config.id + `',
  title: '` + config.title + `',
  description: '{API_BRIEF_DESCRIPTION}',
  category: '` + config.category + `',
  difficulty: '` + config.difficulty + `',
  
  syntax: \`{API_SYNTAX}\`,
  example: \`{API_EXAMPLE}\`,
  notes: '{API_NOTES}',
  
  version: '` + config.version + `',
  tags: ` + JSON.stringify(config.tags) + `,
  
  // 9个Tab内容
  basicInfo,
  businessScenarios,
  implementation,
  interviewQuestions,
  commonQuestions,
  knowledgeArchaeology,
  performanceOptimization,
  debuggingTips,
  essenceInsights
};

export default ` + config.id.replace(/-/g, '') + `Data;`;

  const indexPath = path.join(apiDir, 'index.ts');
  fs.writeFileSync(indexPath, indexContent);
  console.log(`✅ 生成主配置文件: ${indexPath}`);
}

function updateMainIndex(config: ApiConfig) {
  const mainIndexPath = 'src/data/react/index.ts';
  const content = fs.readFileSync(mainIndexPath, 'utf-8');

  // 根据category确定导入路径
  const categoryDir = config.category === 'React Components' ? 'components' :
                     config.category === 'React APIs' ? 'apis' : 'hooks';

  // 添加导入
  const importName = config.id.replace(/-/g, '') + 'Data';
  const importLine = 'import ' + importName + " from './" + categoryDir + '/' + config.id + "';";
  
  // 检查是否已存在
  if (content.includes(importLine)) {
    console.log(`⚠️ ${config.title} 已存在于主索引文件中`);
    return;
  }
  
  // 在最后一个import后添加新的import
  const lines = content.split('\n');
  const lastImportIndex = lines.findLastIndex(line => line.startsWith('import'));
  lines.splice(lastImportIndex + 1, 0, importLine);
  
  // 在数组中添加新的数据
  const exportArrayStart = lines.findIndex(line => line.includes('export const reactApis'));
  const exportArrayEnd = lines.findIndex((line, index) => 
    index > exportArrayStart && line.includes('];')
  );
  
  // 在数组最后一个元素前添加
  lines.splice(exportArrayEnd, 0, '  ' + importName + ',');
  
  fs.writeFileSync(mainIndexPath, lines.join('\n'));
  console.log(`✅ 更新主索引文件: ${mainIndexPath}`);
}

// 🛡️ 数据一致性强制提醒
function displayDataConsistencyReminder(config: ApiConfig) {
  console.log('\n🛡️ 重要提醒：数据一致性检查\n');
  console.log('生成骨架完成后，请务必：\n');
  console.log('1. ✅ 更新各个Tab文件的实际内容');
  console.log('2. ⚠️ 【关键】同时更新 index.ts 文件中的顶层字段：');
  console.log("   - description: '{API_BRIEF_DESCRIPTION}' → 实际描述");
  console.log("   - syntax: '{API_SYNTAX}' → 实际语法");
  console.log("   - example: '{API_EXAMPLE}' → 实际示例");
  console.log("   - notes: '{API_NOTES}' → 实际注意事项");
  console.log("   - version: '" + config.version + "' → 实际版本");
  console.log("   - tags: " + JSON.stringify(config.tags) + " → 实际标签\n");
  console.log('3. 🔍 检查页面是否还显示 {XXX} 格式的骨架内容');
  console.log('4. 🚀 访问 http://localhost:8080/react 验证效果\n');
  console.log('如果忘记更新 index.ts，页面会显示骨架内容而不是实际内容！\n');
  
  // 🎯 新增 completionStatus 工作流程说明
  console.log('🏗️ Tab完成状态管理工作流程：\n');
  console.log('📝 开发流程：');
  console.log('   1. 编写Tab内容（替换{XXX}占位符）');
  console.log("   2. 修改 completionStatus: '当前tab内容未完成' → '内容已完成'");
  console.log('   3. 访问 http://localhost:8080/full-tab-render/' + config.id + ' 验证');
  console.log('   4. 确认页面不再显示"当前tab内容未完成"标识\n');
  
  console.log('🧪 测试验证：');
  console.log('   • 骨架状态: tsx scripts/puppeteer/index.ts ' + config.id + ' --headless');
  console.log('   • 页面检测: 脚本会统计"当前tab内容未完成"出现次数');
  console.log('   • 完成标准: 0个"当前tab内容未完成" = 100%完成\n');
  
  console.log('📊 Tab类型说明：');
  console.log('   • 单对象Tab: basic-info.ts, implementation.ts 等');
  console.log("     修改: completionStatus: '内容已完成'");
  console.log('   • 数组Tab: business-scenarios.ts, interview-questions.ts 等');
  console.log("     修改: 数组每个元素的 completionStatus: '内容已完成'\n");
  
  console.log('⚡ 快速完成检查：');
  console.log('   访问 → http://localhost:8080/full-tab-render/' + config.id);
  console.log('   查看 → "🏗️ Tab完成状态检测" 区域');
  console.log('   目标 → 所有Tag都是绿色"内容已完成"\n');
}

// 命令行接口
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
使用方法: tsx scripts/generate-api-skeleton.ts <api-id>

示例: tsx scripts/generate-api-skeleton.ts ReactMemo

这将生成一个完整的API文档骨架，包括：
- 9个Tab文件的基础结构（纯占位符）
- 主配置文件 (index.ts)
- 自动更新到瀑布流主文件
- 数据一致性提醒
    `);
    process.exit(1);
  }
  
  const apiId = args[0];
  
  // 预设配置
  const presetConfigs: Record<string, Partial<ApiConfig>> = {
    'ReactMemo': {
      title: 'React.memo',
      description: 'React高阶组件，通过浅比较props来优化函数组件的重新渲染性能',
      category: 'React Components',
      difficulty: 'medium',
      version: 'React 16.6.0+',
      tags: ['React', 'Component', '性能优化', 'HOC']
    },
    'ReactSuspense': {
      title: 'React.Suspense',
      description: 'React组件，用于包装可能挂起的组件，提供loading状态的优雅处理',
      category: 'React Components',
      difficulty: 'medium',
      version: 'React 16.6.0+',
      tags: ['React', 'Component', '异步', 'Suspense', '代码分割']
    }
  };
  
  const config: ApiConfig = {
    id: apiId,
    title: apiId,
    description: apiId + '的详细说明',
    category: 'React Hooks',
    difficulty: 'medium',
    version: 'React 18.0.0+',
    tags: ['React', 'Hook'],
    ...presetConfigs[apiId]
  };
  
  generateApiSkeleton(config);
}

export { generateApiSkeleton, type ApiConfig };
