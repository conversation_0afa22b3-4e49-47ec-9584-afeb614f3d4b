# 🚨 工作流程错误防范检查清单

## 每次执行自动化任务前，必须检查：

### ✅ 基本理解检查
- [ ] 我明确知道 `generate-api-skeleton.ts` 只生成占位符骨架
- [ ] 我明确知道 `--generate-content` 不会生成真正的内容
- [ ] 我明确知道 0% → 100% 需要手动内容填充

### ✅ 执行前状态检查
- [ ] 已查看 `progress.md` 确定真正需要处理的API
- [ ] 已运行 Puppeteer 确认当前完成度
- [ ] 已确认目标：骨架生成 vs 内容填充 vs 状态更新

### ✅ 执行中行为检查
- [ ] 如果0%完成且文件存在 → 需要内容填充，不是重新生成骨架
- [ ] 如果需要内容填充 → 使用MDC模板+AI辅助，不是运行脚本
- [ ] 如果100%完成 → 更新progress.md，不是重复测试

### ✅ 完成后验证检查
- [ ] 已检查所有 `{XXX}` 占位符被替换为实际内容
- [ ] 已修改所有相关的 `completionStatus: '内容已完成'`
- [ ] 已运行 Puppeteer 验证达到 100% 完成度
- [ ] 已更新 `progress.md` 中的状态和进度统计

## 🚨 红旗警告信号

### 如果发现以下情况，立即停止并重新评估：
- 🚩 连续多次运行骨架生成脚本
- 🚩 API一直保持0%完成状态
- 🚩 页面上仍显示 `{API_NAME}` 等占位符
- 🚩 声称"生成完成"但实际无实质内容

## 📋 快速自检命令

```bash
# 检查是否还有占位符（应该为空）
grep -r "{API_NAME}" src/data/react/components/ReactSuspense/

# 检查完成状态（应该都是"内容已完成"）
grep -r "completionStatus" src/data/react/components/ReactSuspense/

# 验证最终结果（应该是100%）
tsx scripts/puppeteer/index.ts ReactSuspense --headless
```

## 🎯 正确的执行口诀

**记住：**
1. **检测** → 确定真实状态和需求
2. **区分** → 骨架生成 vs 内容填充 vs 状态更新  
3. **执行** → 选择正确的操作类型
4. **验证** → 确认实际达到预期结果

**永远不要：**
- 重复生成骨架来"解决"0%完成问题
- 认为运行脚本就完成了文档工作
- 忽略内容填充这个核心步骤 