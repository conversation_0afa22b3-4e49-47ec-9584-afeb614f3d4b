#!/usr/bin/env tsx

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

/**
 * 🎯 React API 命名修复脚本
 * 
 * 解决问题：
 * 1. 带小数点的错误命名（React.ComponentClass）
 * 2. React前缀不一致
 * 3. 重复内容删除
 * 4. 统一命名风格
 */

// 重命名映射表
const RENAME_MAP: Record<string, string> = {
  // 修复带小数点的错误命名
  'React.ComponentClass': 'ComponentClass',
  'React.FunctionComponent': 'FunctionComponent', 
  'React.PureComponent': 'PureComponent',
  'React.ReactElement': 'ReactElement',
  'React.ReactNode': 'ReactNode',
  
  // 统一去掉React前缀，使用驼峰
  'ReactComponent': 'Component',
  'ReactDevTools': 'DevTools',
  'ReactForwardRef': 'ForwardRef',
  'ReactLazy': 'Lazy',
  'ReactMemo': 'Memo',
  'ReactSuspense': 'Suspense',
  'ReactVersion': 'Version',
  
  // 修复其他命名
  'customHooks': 'CustomHooks'
};

// 需要删除的目录（错误重复内容）
const DELETE_DIRS = [
  'hooks/ReactPortal',
  'hooks' // 如果清空后删除
];

const REACT_DATA_DIR = 'src/data/react';

function log(message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info') {
  const colors = {
    info: '\x1b[36m',    // cyan
    success: '\x1b[32m', // green
    error: '\x1b[31m',   // red
    warning: '\x1b[33m'  // yellow
  };
  const reset = '\x1b[0m';
  console.log(`${colors[type]}${message}${reset}`);
}

function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = `backups/react-naming-fix-${timestamp}`;
  
  log('🔄 创建备份...', 'info');
  execSync(`mkdir -p ${backupDir}`);
  execSync(`cp -r ${REACT_DATA_DIR} ${backupDir}/`);
  log(`✅ 备份创建完成: ${backupDir}`, 'success');
  
  return backupDir;
}

function renameDirectories() {
  log('🏗️ 开始重命名目录...', 'info');
  
  for (const [oldName, newName] of Object.entries(RENAME_MAP)) {
    const oldPath = path.join(REACT_DATA_DIR, oldName);
    const newPath = path.join(REACT_DATA_DIR, newName);
    
    if (fs.existsSync(oldPath)) {
      log(`📁 重命名: ${oldName} → ${newName}`, 'info');
      fs.renameSync(oldPath, newPath);
      log(`✅ 完成: ${oldName} → ${newName}`, 'success');
    } else {
      log(`⚠️ 目录不存在: ${oldPath}`, 'warning');
    }
  }
}

function deleteErrorDirectories() {
  log('🗑️ 删除错误的重复目录...', 'info');
  
  for (const dirPath of DELETE_DIRS) {
    const fullPath = path.join(REACT_DATA_DIR, dirPath);
    
    if (fs.existsSync(fullPath)) {
      log(`🗑️ 删除: ${dirPath}`, 'warning');
      fs.rmSync(fullPath, { recursive: true, force: true });
      log(`✅ 已删除: ${dirPath}`, 'success');
    } else {
      log(`⚠️ 目录不存在: ${dirPath}`, 'warning');
    }
  }
  
  // 检查hooks目录是否为空，如果为空则删除
  const hooksPath = path.join(REACT_DATA_DIR, 'hooks');
  if (fs.existsSync(hooksPath)) {
    const hooksContents = fs.readdirSync(hooksPath);
    if (hooksContents.length === 0) {
      log('🗑️ 删除空的 hooks 目录', 'warning');
      fs.rmdirSync(hooksPath);
      log('✅ 已删除空的 hooks 目录', 'success');
    }
  }
}

function updateReferences() {
  log('🔗 更新引用...', 'info');
  
  // 需要更新的文件列表
  const filesToUpdate = [
    `${REACT_DATA_DIR}/index.ts`,
    `${REACT_DATA_DIR}/progress.md`
  ];
  
  // 查找其他可能的引用文件
  const findOtherReferences = () => {
    try {
      const output = execSync(`find src -type f -name "*.ts" -o -name "*.tsx" -o -name "*.md" | grep -v node_modules`).toString();
      return output.split('\n').filter(file => file.trim());
    } catch (error) {
      return [];
    }
  };
  
  const allFiles = [...filesToUpdate, ...findOtherReferences()];
  
  for (const filePath of allFiles) {
    if (!fs.existsSync(filePath)) continue;
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;
      
      // 更新目录引用
      for (const [oldName, newName] of Object.entries(RENAME_MAP)) {
        const patterns = [
          new RegExp(`from ['"]@/data/react/${oldName}/`, 'g'),
          new RegExp(`from ['"]\\./react/${oldName}/`, 'g'),
          new RegExp(`from ['"]\\.\\./${oldName}/`, 'g'),
          new RegExp(`react/${oldName}`, 'g'),
          new RegExp(`\\b${oldName}\\b`, 'g')
        ];
        
        for (const pattern of patterns) {
          if (pattern.test(content)) {
            content = content.replace(pattern, (match) => {
              return match.replace(oldName, newName);
            });
            hasChanges = true;
          }
        }
      }
      
      // 删除对已删除目录的引用
      for (const deleteDir of DELETE_DIRS) {
        const patterns = [
          new RegExp(`from ['"]@/data/react/${deleteDir}/['"],[^\\n]*`, 'g'),
          new RegExp(`import.*${deleteDir}.*`, 'g'),
          new RegExp(`\\s*${deleteDir}.*,?\\n`, 'g')
        ];
        
        for (const pattern of patterns) {
          if (pattern.test(content)) {
            content = content.replace(pattern, '');
            hasChanges = true;
          }
        }
      }
      
      if (hasChanges) {
        fs.writeFileSync(filePath, content);
        log(`✅ 更新文件: ${filePath}`, 'success');
      }
    } catch (error) {
      log(`❌ 更新文件失败: ${filePath} - ${error}`, 'error');
    }
  }
}

function generateSummary() {
  log('\n📊 重命名总结:', 'info');
  log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'info');
  
  log('🔄 重命名操作:', 'info');
  for (const [oldName, newName] of Object.entries(RENAME_MAP)) {
    log(`  ${oldName} → ${newName}`, 'success');
  }
  
  log('\n🗑️ 删除操作:', 'info');
  for (const dir of DELETE_DIRS) {
    log(`  ${dir}`, 'warning');
  }
  
  log('\n✅ 统一后的命名规范:', 'info');
  log('  • Hook APIs: useEffect, useState, useCallback (小写开头)', 'success');
  log('  • Component APIs: Fragment, Suspense, Portal (大写开头)', 'success');
  log('  • 去掉所有 React. 前缀', 'success');
  log('  • 使用驼峰命名法', 'success');
  
  log('\n🎯 修复的问题:', 'info');
  log('  ✅ 修复带小数点的错误命名', 'success');
  log('  ✅ 统一React前缀使用', 'success');
  log('  ✅ 删除重复的错误内容', 'success');
  log('  ✅ 统一驼峰命名风格', 'success');
}

function validateResult() {
  log('\n🔍 验证结果...', 'info');
  
  try {
    const directories = fs.readdirSync(REACT_DATA_DIR, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name)
      .sort();
    
    log('📁 当前目录列表:', 'info');
    directories.forEach(dir => {
      log(`  ${dir}`, 'success');
    });
    
    // 检查是否还有问题
    const problematicDirs = directories.filter(dir => 
      dir.includes('.') || 
      (dir.startsWith('React') && !['ReactElement', 'ReactNode'].includes(dir))
    );
    
    if (problematicDirs.length > 0) {
      log('\n⚠️ 仍有问题的目录:', 'warning');
      problematicDirs.forEach(dir => log(`  ${dir}`, 'error'));
    } else {
      log('\n✅ 所有目录命名符合规范！', 'success');
    }
    
  } catch (error) {
    log(`❌ 验证失败: ${error}`, 'error');
  }
}

function main() {
  log('🚀 开始 React API 命名修复', 'info');
  log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━', 'info');
  
  try {
    // 1. 创建备份
    const backupDir = createBackup();
    
    // 2. 删除错误目录
    deleteErrorDirectories();
    
    // 3. 重命名目录
    renameDirectories();
    
    // 4. 更新引用
    updateReferences();
    
    // 5. 生成总结
    generateSummary();
    
    // 6. 验证结果
    validateResult();
    
    log('\n🎉 React API 命名修复完成！', 'success');
    log(`📁 备份位置: ${backupDir}`, 'info');
    
  } catch (error) {
    log(`❌ 修复过程中出现错误: ${error}`, 'error');
    process.exit(1);
  }
}

// 直接运行主函数
main();

export default main; 