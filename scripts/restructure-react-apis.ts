#!/usr/bin/env tsx

import * as fs from 'fs-extra';
import { readdir, readFile, writeFile } from 'fs/promises';
import * as path from 'path';
import chalk from 'chalk';

/**
 * React API目录结构重构脚本
 * 
 * 目标：将 react/hooks/xxx 和 react/components/xxx 统一重构为 react/xxx
 * 
 * 重构内容：
 * 1. 移动所有API目录到平铺结构
 * 2. 更新所有import路径
 * 3. 更新路由配置
 * 4. 更新检测脚本配置
 */

interface RestructureConfig {
  sourceRoot: string;
  backupDir: string;
  dryRun: boolean;
}

class ReactApiRestructurer {
  private config: RestructureConfig;
  private operations: Array<{ type: 'move' | 'update'; from: string; to: string; description: string }> = [];

  constructor(config: RestructureConfig) {
    this.config = config;
  }

  async run() {
    console.log(chalk.blue('🚀 开始React API目录结构重构'));
    console.log(chalk.gray(`模式: ${this.config.dryRun ? '预览模式（不执行实际操作）' : '执行模式'}`));
    
    try {
      // 1. 创建备份
      if (!this.config.dryRun) {
        await this.createBackup();
      }

      // 2. 分析现有结构
      const structure = await this.analyzeCurrentStructure();
      
      // 3. 检查冲突
      const conflicts = this.checkConflicts(structure);
      if (conflicts.length > 0) {
        console.error(chalk.red('❌ 发现冲突：'));
        conflicts.forEach(conflict => console.error(chalk.red(`  - ${conflict}`)));
        return false;
      }

      // 4. 执行重构
      await this.executeRestructure(structure);

      // 5. 更新引用
      await this.updateReferences();

      console.log(chalk.green('✅ 重构完成！'));
      return true;

    } catch (error) {
      console.error(chalk.red('❌ 重构失败：'), error);
      return false;
    }
  }

  private async createBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(this.config.backupDir, `react-backup-${timestamp}`);
    
    console.log(chalk.yellow('📦 创建备份...'));
    await fs.copy(this.config.sourceRoot, backupPath);
    console.log(chalk.green(`✅ 备份已创建: ${backupPath}`));
  }

  private async analyzeCurrentStructure() {
    const reactRoot = this.config.sourceRoot;
    const structure = {
      hooks: [] as string[],
      components: [] as string[]
    };

    // 分析hooks目录
    const hooksDir = path.join(reactRoot, 'hooks');
    if (await fs.pathExists(hooksDir)) {
      const hooksDirs = await readdir(hooksDir, { withFileTypes: true });
      structure.hooks = hooksDirs
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);
    }

    // 分析components目录
    const componentsDir = path.join(reactRoot, 'components');
    if (await fs.pathExists(componentsDir)) {
      const componentsDirs = await readdir(componentsDir, { withFileTypes: true });
      structure.components = componentsDirs
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);
    }

    console.log(chalk.blue('📊 当前结构分析：'));
    console.log(chalk.gray(`  Hooks: ${structure.hooks.length}个`));
    console.log(chalk.gray(`  Components: ${structure.components.length}个`));
    
    return structure;
  }

  private checkConflicts(structure: { hooks: string[]; components: string[] }) {
    const conflicts: string[] = [];
    const intersection = structure.hooks.filter(name => structure.components.includes(name));
    
    if (intersection.length > 0) {
      conflicts.push(`hooks和components中存在同名API: ${intersection.join(', ')}`);
    }

    return conflicts;
  }

  private async executeRestructure(structure: { hooks: string[]; components: string[] }) {
    const reactRoot = this.config.sourceRoot;
    
    console.log(chalk.blue('🔧 执行目录重构...'));

    // 移动hooks
    for (const hookName of structure.hooks) {
      const fromPath = path.join(reactRoot, 'hooks', hookName);
      const toPath = path.join(reactRoot, hookName);
      
      this.operations.push({
        type: 'move',
        from: fromPath,
        to: toPath,
        description: `移动Hook: hooks/${hookName} → ${hookName}`
      });

      if (!this.config.dryRun) {
        await fs.move(fromPath, toPath);
      }
      console.log(chalk.green(`  ✅ hooks/${hookName} → ${hookName}`));
    }

    // 移动components
    for (const componentName of structure.components) {
      const fromPath = path.join(reactRoot, 'components', componentName);
      const toPath = path.join(reactRoot, componentName);
      
      this.operations.push({
        type: 'move',
        from: fromPath,
        to: toPath,
        description: `移动Component: components/${componentName} → ${componentName}`
      });

      if (!this.config.dryRun) {
        await fs.move(fromPath, toPath);
      }
      console.log(chalk.green(`  ✅ components/${componentName} → ${componentName}`));
    }

    // 删除空目录
    if (!this.config.dryRun) {
      const hooksDir = path.join(reactRoot, 'hooks');
      const componentsDir = path.join(reactRoot, 'components');
      
      if (await fs.pathExists(hooksDir) && (await readdir(hooksDir)).length === 0) {
        await fs.remove(hooksDir);
        console.log(chalk.gray('  🗑️  删除空的hooks目录'));
      }
      
      if (await fs.pathExists(componentsDir) && (await readdir(componentsDir)).length === 0) {
        await fs.remove(componentsDir);
        console.log(chalk.gray('  🗑️  删除空的components目录'));
      }
    }
  }

  private async updateReferences() {
    console.log(chalk.blue('🔄 更新引用路径...'));

    const filesToUpdate = [
      // 主index文件
      path.join(this.config.sourceRoot, 'index.ts'),
      // 路由文件
      path.join(process.cwd(), 'src/pages/FullTabRenderPage.tsx'),
      // 其他可能的引用文件
    ];

    for (const filePath of filesToUpdate) {
      if (await fs.pathExists(filePath)) {
        await this.updateFileReferences(filePath);
      }
    }
  }

  private async updateFileReferences(filePath: string) {
    if (this.config.dryRun) {
      console.log(chalk.gray(`  预览: 将更新 ${filePath}`));
      return;
    }

    const content = await readFile(filePath, 'utf-8');
    let updatedContent = content;

    // 更新import路径
    updatedContent = updatedContent.replace(
      /from ['"](\.\/hooks\/|\.\/components\/|@\/data\/react\/hooks\/|@\/data\/react\/components\/)([^'"]+)['"]/g,
      (match, prefix, apiName) => {
        // 提取API名称（去掉可能的/index部分）
        const cleanApiName = apiName.replace(/\/index$/, '');
        return match.replace(prefix + apiName, `./${cleanApiName}`);
      }
    );

    // 更新动态import
    updatedContent = updatedContent.replace(
      /import\([`'"]@\/data\/react\/(hooks|components)\/([^`'"]+)[`'"]\)/g,
      'import(`@/data/react/$2`)'
    );

    if (updatedContent !== content) {
      await writeFile(filePath, updatedContent);
      console.log(chalk.green(`  ✅ 已更新: ${path.relative(process.cwd(), filePath)}`));
    }
  }

  printSummary() {
    console.log(chalk.blue('\n📋 重构操作摘要：'));
    console.log(chalk.gray(`总操作数: ${this.operations.length}`));
    
    const moveOps = this.operations.filter(op => op.type === 'move');
    const updateOps = this.operations.filter(op => op.type === 'update');
    
    console.log(chalk.gray(`目录移动: ${moveOps.length}`));
    console.log(chalk.gray(`引用更新: ${updateOps.length}`));

    if (this.config.dryRun) {
      console.log(chalk.yellow('\n⚠️  这是预览模式，未执行实际操作'));
      console.log(chalk.yellow('若要执行，请运行: tsx scripts/restructure-react-apis.ts --execute'));
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  
  const config: RestructureConfig = {
    sourceRoot: path.join(process.cwd(), 'src/data/react'),
    backupDir: path.join(process.cwd(), 'backups'),
    dryRun
  };

  // 确保备份目录存在
  await fs.ensureDir(config.backupDir);

  const restructurer = new ReactApiRestructurer(config);
  const success = await restructurer.run();
  
  restructurer.printSummary();
  
  if (!success) {
    process.exit(1);
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error(chalk.red('💥 脚本执行失败：'), error);
    process.exit(1);
  });
}

export default ReactApiRestructurer; 