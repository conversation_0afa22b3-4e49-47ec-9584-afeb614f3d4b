#!/usr/bin/env tsx

import * as fs from 'fs-extra';
import { readFile } from 'fs/promises';
import * as path from 'path';
import chalk from 'chalk';

/**
 * 增强版API内容完成度检测脚本
 * 
 * 解决问题：
 * 1. ✅ 精确检测真正的骨架字符串（基于generate-api-skeleton.ts）
 * 2. ❌ 排除代码示例中的正常变量（如{count}、{firstName}等）
 * 3. 支持新的平铺目录结构
 * 4. 🆕 数据结构验证（与forwardRef标准对比）
 * 5. 提供更详细的检测报告
 */

// 🎯 精确的骨架字符串模式（基于generate-api-skeleton.ts）
const SKELETON_PATTERNS = [
  // 🔥 真正的骨架占位符（全大写+下划线格式）
  /\{[A-Z][A-Z0-9_]*[A-Z0-9]\}/g,  // {API_NAME}, {SCENARIO_1_TITLE}, {IMPLEMENTATION_MECHANISM} 等
  
  // 🔥 特定的骨架模式（来自generate-api-skeleton.ts）
  /\{API_NAME\}/g,
  /\{FRAMEWORK\}/g,
  /\{CORE_PURPOSE\}/g,
  /\{API_TYPE\}/g,
  /\{SCENARIO_\d+_[A-Z_]+\}/g,      // {SCENARIO_1_TITLE}, {SCENARIO_2_DESCRIPTION} 等
  /\{QUESTION_\d+\}/g,              // {QUESTION_1}, {QUESTION_2} 等
  /\{ANSWER_\d+_[A-Z_]+\}/g,        // {ANSWER_1_BRIEF}, {ANSWER_2_DETAILED} 等
  /\{STRATEGY_\d+\}/g,              // {STRATEGY_1} 等
  /\{IMPLEMENTATION_[A-Z_]+\}/g,    // {IMPLEMENTATION_MECHANISM} 等
  /\{CORE_QUESTION\}/g,
  /\{INTRODUCTION\}/g,
  /\{NODE_[A-Z]\}/g,                // {NODE_A}, {NODE_B} 等
  /\{[A-Z_]+_\d+_[A-Z_]+\}/g,       // 复合格式如 {TAG_1_1}, {BENEFIT_2_3} 等
  
  // 🔥 MDC文档注释和状态标识
  /占位内容，具体内容请参考.*\.mdc/g,
  /请在内容完成后修改/g,
  /当前tab内容未完成/g,
  /completionStatus.*未完成/g,
  
  // 🔥 空白内容检测
  /^\s*$/, // 完全空白
  /^\/\/ TODO.*$/gm, // TODO注释
  /^\/\* TODO.*\*\/$/gm, // TODO块注释
];

// ❌ 排除的正常代码模式（不应该被检测为骨架）
const EXCLUDED_PATTERNS = [
  /\{[a-z][a-zA-Z0-9]*\}/g,         // {count}, {firstName}, {error} 等小写变量
  /\{[a-z][a-zA-Z0-9]*\.[a-zA-Z0-9]*\}/g,  // {user.name} 等对象属性
  /\{[a-z][a-zA-Z0-9]*\(\)\}/g,     // {handleSubmit()} 等函数调用
  /\{[a-z][a-zA-Z0-9]*\[\d+\]\}/g,  // {items[0]} 等数组访问
];

// Tab文件配置（适配新的平铺结构）
const TAB_FILES = [
  'basic-info.ts',
  'business-scenarios.ts', 
  'common-questions.ts',
  'debugging-tips.ts',
  'essence-insights.ts',
  'implementation.ts',
  'interview-questions.ts',
  'knowledge-archaeology.ts',
  'performance-optimization.ts'
];

// 🆕 数据结构验证配置
interface DataStructureStandard {
  file: string;
  expectedType: string;
  expectedImport: string;
  requiredFields: string[];
  arrayStructure: boolean;
}

const DATA_STRUCTURE_STANDARDS: { [key: string]: DataStructureStandard } = {
  'interview-questions.ts': {
    file: 'interview-questions.ts',
    expectedType: 'InterviewQuestion[]',
    expectedImport: 'import { InterviewQuestion } from \'@/types/api\'',
    requiredFields: ['id', 'question', 'answer', 'difficulty', 'frequency', 'category'],
    arrayStructure: true
  },
  'common-questions.ts': {
    file: 'common-questions.ts',
    expectedType: 'CommonQuestion[]',
    expectedImport: 'import { CommonQuestion } from \'@/types/api\'',
    requiredFields: ['id', 'question', 'answer', 'tags'],
    arrayStructure: true
  },
  'business-scenarios.ts': {
    file: 'business-scenarios.ts',
    expectedType: 'BusinessScenario[]',
    expectedImport: 'import { BusinessScenario } from \'@/types/api\'',
    requiredFields: ['id', 'title', 'description', 'businessValue', 'scenario', 'code'],
    arrayStructure: true
  }
};

interface SkeletonDetection {
  file: string;
  line: number;
  column: number;
  match: string;
  pattern: string;
  severity: 'high' | 'medium' | 'low';
  context: string;
}

interface DataStructureIssue {
  type: 'import' | 'type' | 'structure' | 'field' | 'export';
  severity: 'critical' | 'high' | 'medium';
  description: string;
  expected: string;
  actual: string;
  line?: number;
  suggestion: string;
}

interface TabStatus {
  file: string;
  exists: boolean;
  completed: boolean;
  skeletons: SkeletonDetection[];
  issues: string[];
  // 🆕 数据结构验证
  structureValid: boolean;
  structureIssues: DataStructureIssue[];
  forwardRefComparison: {
    score: number; // 0-100, 与forwardRef标准的匹配度
    missingFields: string[];
    incorrectTypes: string[];
    suggestions: string[];
  };
}

interface ApiAnalysis {
  apiName: string;
  basePath: string;
  overallProgress: number;
  tabsStatus: TabStatus[];
  totalIssues: number;
  criticalIssues: number;
  // 🆕 数据结构分析
  structureScore: number;
  totalStructureIssues: number;
  criticalStructureIssues: number;
}

class EnhancedContentDetector {
  private basePath: string;
  private supportLegacyStructure: boolean;
  private referenceApiPath: string;

  constructor(basePath: string, supportLegacyStructure = true) {
    this.basePath = basePath;
    this.supportLegacyStructure = supportLegacyStructure;
    this.referenceApiPath = path.join(basePath, 'src/data/react/ReactForwardRef');
  }

  async analyzeApi(apiName: string): Promise<ApiAnalysis> {
    console.log(chalk.blue(`🔍 深度分析 ${apiName}...`));

    // 尝试找到API目录
    const apiPath = await this.findApiPath(apiName);
    if (!apiPath) {
      throw new Error(`未找到API目录: ${apiName}`);
    }

    const analysis: ApiAnalysis = {
      apiName,
      basePath: apiPath,
      overallProgress: 0,
      tabsStatus: [],
      totalIssues: 0,
      criticalIssues: 0,
      structureScore: 0,
      totalStructureIssues: 0,
      criticalStructureIssues: 0
    };

    // 分析每个Tab文件
    for (const tabFile of TAB_FILES) {
      const tabStatus = await this.analyzeTabFile(apiPath, tabFile);
      analysis.tabsStatus.push(tabStatus);
    }

    // 计算整体进度
    const completedTabs = analysis.tabsStatus.filter(tab => tab.completed).length;
    analysis.overallProgress = Math.round((completedTabs / TAB_FILES.length) * 100);

    // 统计问题
    analysis.totalIssues = analysis.tabsStatus.reduce((sum, tab) => sum + tab.skeletons.length, 0);
    analysis.criticalIssues = analysis.tabsStatus.reduce(
      (sum, tab) => sum + tab.skeletons.filter(s => s.severity === 'high').length, 
      0
    );

    // 🆕 统计数据结构问题
    analysis.totalStructureIssues = analysis.tabsStatus.reduce(
      (sum, tab) => sum + tab.structureIssues.length, 0
    );
    analysis.criticalStructureIssues = analysis.tabsStatus.reduce(
      (sum, tab) => sum + tab.structureIssues.filter(i => i.severity === 'critical').length, 0
    );

    // 🆕 计算数据结构分数
    const validStructureTabs = analysis.tabsStatus.filter(tab => tab.structureValid).length;
    const structurableTabs = analysis.tabsStatus.filter(tab => 
      DATA_STRUCTURE_STANDARDS[tab.file]
    ).length;
    analysis.structureScore = structurableTabs > 0 ? 
      Math.round((validStructureTabs / structurableTabs) * 100) : 100;

    return analysis;
  }

  private async findApiPath(apiName: string): Promise<string | null> {
    // 🆕 支持框架前缀：vue:reactive, react:useState
    let framework = 'react'; // 默认框架
    let actualApiName = apiName;
    
    if (apiName.includes(':')) {
      [framework, actualApiName] = apiName.split(':', 2);
    }
    
    const frameworkDataPath = path.join(this.basePath, `src/data/${framework}`);
    
    // 1. 尝试新的平铺结构
    const flatPath = path.join(frameworkDataPath, actualApiName);
    if (await fs.pathExists(flatPath)) {
      return flatPath;
    }

    // 2. 如果支持遗留结构，尝试hooks和components（仅React）
    if (this.supportLegacyStructure && framework === 'react') {
      const hooksPath = path.join(frameworkDataPath, 'hooks', actualApiName);
      if (await fs.pathExists(hooksPath)) {
        return hooksPath;
      }

      const componentsPath = path.join(frameworkDataPath, 'components', actualApiName);
      if (await fs.pathExists(componentsPath)) {
        return componentsPath;
      }
    }

    // 3. Vue遗留结构支持
    if (this.supportLegacyStructure && framework === 'vue') {
      const compositionApiPath = path.join(frameworkDataPath, 'composition-api', actualApiName);
      if (await fs.pathExists(compositionApiPath)) {
        return compositionApiPath;
      }
    }

    return null;
  }

  private async analyzeTabFile(apiPath: string, tabFile: string): Promise<TabStatus> {
    const filePath = path.join(apiPath, tabFile);
    const status: TabStatus = {
      file: tabFile,
      exists: false,
      completed: true,
      skeletons: [],
      issues: [],
      structureValid: true,
      structureIssues: [],
      forwardRefComparison: {
        score: 0,
        missingFields: [],
        incorrectTypes: [],
        suggestions: []
      }
    };

    // 检查文件是否存在
    if (!(await fs.pathExists(filePath))) {
      status.issues.push(`文件不存在: ${tabFile}`);
      status.completed = false;
      status.structureValid = false;
      return status;
    }

    status.exists = true;

    try {
      const content = await readFile(filePath, 'utf-8');
      status.skeletons = this.detectSkeletons(content, tabFile);
      
      // 检查特定问题
      status.issues = this.checkSpecificIssues(content, tabFile);
      
      // 🆕 数据结构验证
      await this.validateDataStructure(content, tabFile, status);
      
      // 如果有骨架字符串、严重问题或数据结构问题，标记为未完成
      if (status.skeletons.length > 0 || status.issues.length > 0 || !status.structureValid) {
        status.completed = false;
      }

    } catch (error) {
      status.issues.push(`读取文件失败: ${error instanceof Error ? error.message : '未知错误'}`);
      status.completed = false;
      status.structureValid = false;
    }

    return status;
  }

  // 🆕 数据结构验证方法
  private async validateDataStructure(content: string, fileName: string, status: TabStatus): Promise<void> {
    const standard = DATA_STRUCTURE_STANDARDS[fileName];
    if (!standard) {
      // 不需要验证此文件
      return;
    }

    status.structureIssues = [];

    // 1. 检查import语句
    const hasCorrectImport = content.includes(standard.expectedImport.replace(/'/g, '"')) || 
                            content.includes(standard.expectedImport);
    if (!hasCorrectImport) {
      status.structureIssues.push({
        type: 'import',
        severity: 'critical',
        description: `错误的import语句`,
        expected: standard.expectedImport,
        actual: this.extractImportStatement(content) || '未找到import语句',
        suggestion: `使用正确的import: ${standard.expectedImport}`
      });
    }

    // 2. 检查类型声明
    const expectedTypePattern = new RegExp(`:\\s*${standard.expectedType.replace(/\[|\]/g, '\\$&')}`);
    if (!expectedTypePattern.test(content)) {
      status.structureIssues.push({
        type: 'type',
        severity: 'critical',
        description: `错误的类型声明`,
        expected: standard.expectedType,
        actual: this.extractTypeDeclaration(content) || '未找到类型声明',
        suggestion: `使用正确的类型: const data: ${standard.expectedType} = ...`
      });
    }

    // 3. 检查export语句
    if (!content.includes('export default')) {
      status.structureIssues.push({
        type: 'export',
        severity: 'critical',
        description: `缺少export default语句`,
        expected: 'export default variableName',
        actual: this.extractExportStatement(content) || '未找到export语句',
        suggestion: `添加: export default ${fileName.replace('.ts', '').replace('-', '')}`
      });
    }

    // 4. 🆕 与forwardRef标准对比
    await this.compareWithForwardRef(content, fileName, status);

    // 设置结构有效性
    status.structureValid = status.structureIssues.filter(i => i.severity === 'critical').length === 0;
  }

  // 🆕 与forwardRef标准对比
  private async compareWithForwardRef(content: string, fileName: string, status: TabStatus): Promise<void> {
    const standard = DATA_STRUCTURE_STANDARDS[fileName];
    if (!standard) return;

    try {
      // 读取forwardRef的对应文件作为参考
      const referencePath = path.join(this.referenceApiPath, fileName);
      if (!(await fs.pathExists(referencePath))) {
        return;
      }

      const referenceContent = await readFile(referencePath, 'utf-8');
      
      status.forwardRefComparison = {
        score: 0,
        missingFields: [],
        incorrectTypes: [],
        suggestions: []
      };

      // 分析当前内容的数据结构
      const currentStructure = this.analyzeContentStructure(content);
      const referenceStructure = this.analyzeContentStructure(referenceContent);

      // 计算匹配度
      let score = 100;
      const suggestions: string[] = [];

      // 检查import语句匹配
      if (currentStructure.import !== referenceStructure.import) {
        score -= 20;
        suggestions.push(`参考forwardRef的import: ${referenceStructure.import}`);
      }

      // 检查类型声明匹配
      if (currentStructure.typeDeclaration !== standard.expectedType) {
        score -= 30;
        suggestions.push(`参考forwardRef的类型声明: ${standard.expectedType}`);
      }

      // 检查数据结构完整性
      const missingFields = standard.requiredFields.filter(field => 
        !this.hasRequiredField(content, field)
      );
      
      if (missingFields.length > 0) {
        score -= missingFields.length * 10;
        status.forwardRefComparison.missingFields = missingFields;
        suggestions.push(`缺少字段: ${missingFields.join(', ')}，参考forwardRef的完整结构`);
      }

      // 检查wrapper object问题
      if (this.hasWrapperObjectStructure(content)) {
        score -= 25;
        suggestions.push('移除wrapper object，直接使用数组结构，参考forwardRef的标准格式');
      }

      status.forwardRefComparison.score = Math.max(0, score);
      status.forwardRefComparison.suggestions = suggestions;

      // 如果分数太低，添加结构问题
      if (score < 70) {
        status.structureIssues.push({
          type: 'structure',
          severity: score < 50 ? 'critical' : 'high',
          description: `数据结构与forwardRef标准差异较大 (匹配度: ${score}%)`,
          expected: `参考 ${this.referenceApiPath}/${fileName}`,
          actual: `当前结构匹配度仅${score}%`,
          suggestion: `建议参考forwardRef的标准结构进行修复: ${suggestions.join('; ')}`
        });
      }

    } catch (error) {
      // 如果无法比较，不影响主要功能
      console.warn(chalk.yellow(`⚠️ 无法与forwardRef对比 ${fileName}: ${error}`));
    }
  }

  // 🆕 分析内容结构的辅助方法
  private analyzeContentStructure(content: string): {
    import: string;
    typeDeclaration: string;
    hasDefaultExport: boolean;
    hasWrapperObject: boolean;
  } {
    const importMatch = content.match(/import\s+{[^}]+}\s+from\s+['"][^'"]+['"]/);
    const typeMatch = content.match(/:\s*([A-Za-z\[\]]+)\s*=/);
    
    return {
      import: importMatch ? importMatch[0] : '',
      typeDeclaration: typeMatch ? typeMatch[1] : '',
      hasDefaultExport: content.includes('export default'),
      hasWrapperObject: this.hasWrapperObjectStructure(content)
    };
  }

  // 🆕 检查是否有wrapper object结构
  private hasWrapperObjectStructure(content: string): boolean {
    // 检查是否有类似 { scenarios: [...] } 或 { questions: [...] } 的结构
    const wrapperPatterns = [
      /{\s*scenarios:\s*\[/,
      /{\s*questions:\s*\[/,
      /{\s*data:\s*\[/,
      /{\s*items:\s*\[/
    ];
    
    return wrapperPatterns.some(pattern => pattern.test(content));
  }

  // 🆕 检查是否有必需字段
  private hasRequiredField(content: string, field: string): boolean {
    const fieldPattern = new RegExp(`${field}\\s*:`);
    return fieldPattern.test(content);
  }

  // 🆕 提取各种语句的辅助方法
  private extractImportStatement(content: string): string | null {
    const match = content.match(/import\s+{[^}]+}\s+from\s+['"][^'"]+['"]/);
    return match ? match[0] : null;
  }

  private extractTypeDeclaration(content: string): string | null {
    const match = content.match(/:\s*([A-Za-z\[\]]+)\s*=/);
    return match ? match[1] : null;
  }

  private extractExportStatement(content: string): string | null {
    const match = content.match(/export\s+(default\s+)?[^;]+/);
    return match ? match[0] : null;
  }

  private detectSkeletons(content: string, fileName: string): SkeletonDetection[] {
    const detections: SkeletonDetection[] = [];
    const lines = content.split('\n');

    for (const pattern of SKELETON_PATTERNS) {
      const globalPattern = new RegExp(pattern.source, 'g');
      let match;

      while ((match = globalPattern.exec(content)) !== null) {
        const matchText = match[0];
        
        // 🛡️ 排除正常代码变量
        if (this.isExcludedPattern(matchText)) {
          continue;
        }
        
        // 找到匹配的行号和列号
        const position = this.getLineAndColumn(content, match.index);
        const line = lines[position.line - 1];
        
        detections.push({
          file: fileName,
          line: position.line,
          column: position.column,
          match: matchText,
          pattern: pattern.source,
          severity: this.getSeverity(matchText, fileName),
          context: line.trim()
        });
      }
    }

    return detections;
  }

  // 🛡️ 新增：检查是否为应该排除的正常代码模式
  private isExcludedPattern(match: string): boolean {
    for (const excludePattern of EXCLUDED_PATTERNS) {
      if (excludePattern.test(match)) {
        return true;
      }
    }
    return false;
  }

  private checkSpecificIssues(content: string, fileName: string): string[] {
    const issues: string[] = [];

    // 检查文件是否几乎为空
    const meaningfulContent = content
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
      .replace(/\/\/.*$/gm, '') // 移除行注释
      .replace(/\s+/g, ' ') // 压缩空白
      .trim();

    if (meaningfulContent.length < 100) {
      issues.push('文件内容过少，可能未完成');
    }

    // 检查export语句
    if (!content.includes('export default')) {
      issues.push('缺少export default语句');
    }

    // 检查特定文件的特殊要求
    switch (fileName) {
      case 'basic-info.ts':
        if (!content.includes('definition') || !content.includes('syntax')) {
          issues.push('缺少必需的基本信息字段');
        }
        break;
      case 'business-scenarios.ts':
        if (!content.includes('BusinessScenario')) {
          issues.push('缺少业务场景类型定义');
        }
        break;
      case 'implementation.ts':
        if (!content.includes('mechanism') || !content.includes('visualization')) {
          issues.push('缺少实现机制或可视化内容');
        }
        break;
    }

    return issues;
  }

  private getLineAndColumn(content: string, index: number): { line: number; column: number } {
    const beforeMatch = content.substring(0, index);
    const lines = beforeMatch.split('\n');
    return {
      line: lines.length,
      column: lines[lines.length - 1].length + 1
    };
  }

  private getSeverity(match: string, fileName: string): 'high' | 'medium' | 'low' {
    // 高严重性：关键占位符
    if (match.includes('API_NAME') || match.includes('CORE_QUESTION') || match.includes('IMPLEMENTATION_MECHANISM')) {
      return 'high';
    }

    // 高严重性：状态标识
    if (match.includes('未完成') || match.includes('占位内容')) {
      return 'high';
    }

    // 中等严重性：内容占位符
    if (match.includes('SCENARIO') || match.includes('QUESTION') || match.includes('STRATEGY')) {
      return 'medium';
    }

    // 低严重性：其他
    return 'low';
  }

  generateReport(analysis: ApiAnalysis): void {
    console.log('\n' + '='.repeat(60));
    console.log(chalk.bold.blue(`📊 ${analysis.apiName} 详细分析报告`));
    console.log('='.repeat(60));

    // 总体状态
    const progressColor = analysis.overallProgress === 100 ? 'green' : 
                         analysis.overallProgress >= 70 ? 'yellow' : 'red';
    
    console.log(chalk.bold('📈 总体进度:'), chalk[progressColor](`${analysis.overallProgress}% (${analysis.tabsStatus.filter(t => t.completed).length}/${TAB_FILES.length})`));
    console.log(chalk.bold('⚠️  总问题数:'), chalk.red(analysis.totalIssues));
    console.log(chalk.bold('🚨 严重问题:'), chalk.red(analysis.criticalIssues));
    
    // 🆕 数据结构状态
    console.log(chalk.bold('🏗️  数据结构分数:'), chalk[analysis.structureScore >= 80 ? 'green' : analysis.structureScore >= 60 ? 'yellow' : 'red'](`${analysis.structureScore}%`));
    console.log(chalk.bold('📐 结构问题数:'), chalk.red(analysis.totalStructureIssues));
    console.log(chalk.bold('🚨 严重结构问题:'), chalk.red(analysis.criticalStructureIssues));
    console.log('');

    // Tab详情
    console.log(chalk.bold.blue('📋 Tab文件详情:'));
    for (const tab of analysis.tabsStatus) {
      const statusIcon = tab.completed ? '✅' : '❌';
      const statusColor = tab.completed ? 'green' : 'red';
      
      console.log(`${statusIcon} ${chalk[statusColor](tab.file.padEnd(25))} ${tab.exists ? '存在' : '不存在'}`);
      
      // 🆕 数据结构状态
      if (DATA_STRUCTURE_STANDARDS[tab.file]) {
        const structureIcon = tab.structureValid ? '🏗️✅' : '🏗️❌';
        const structureColor = tab.structureValid ? 'green' : 'red';
        console.log(`   ${structureIcon} 数据结构: ${chalk[structureColor](tab.structureValid ? '正确' : '有问题')}`);
        
        if (tab.forwardRefComparison.score > 0) {
          const scoreColor = tab.forwardRefComparison.score >= 80 ? 'green' : 
                           tab.forwardRefComparison.score >= 60 ? 'yellow' : 'red';
          console.log(`   📊 vs forwardRef: ${chalk[scoreColor](`${tab.forwardRefComparison.score}%`)}`);
        }
      }
      
      if (tab.skeletons.length > 0) {
        console.log(chalk.gray(`    💀 骨架字符串: ${tab.skeletons.length}个`));
        for (const skeleton of tab.skeletons.slice(0, 3)) { // 只显示前3个
          const severityColor = skeleton.severity === 'high' ? 'red' : 
                               skeleton.severity === 'medium' ? 'yellow' : 'gray';
          console.log(chalk[severityColor](`       ${skeleton.line}:${skeleton.column} "${skeleton.match}"`));
        }
        if (tab.skeletons.length > 3) {
          console.log(chalk.gray(`       ... 还有 ${tab.skeletons.length - 3} 个`));
        }
      }
      
      // 🆕 数据结构问题
      if (tab.structureIssues.length > 0) {
        console.log(chalk.red(`    🏗️ 结构问题: ${tab.structureIssues.length}个`));
        for (const issue of tab.structureIssues.slice(0, 2)) {
          const issueColor = issue.severity === 'critical' ? 'red' : 'yellow';
          console.log(chalk[issueColor](`       ${issue.type}: ${issue.description}`));
        }
      }
      
      if (tab.issues.length > 0) {
        console.log(chalk.gray(`    ⚠️  其他问题: ${tab.issues.join(', ')}`));
      }
    }

    // 🆕 数据结构修复建议
    console.log('\n' + chalk.bold.blue('🏗️ 数据结构修复建议:'));
    const structureProblems = analysis.tabsStatus.filter(t => !t.structureValid);
    if (structureProblems.length > 0) {
      console.log(chalk.red(`发现 ${structureProblems.length} 个文件有数据结构问题:`));
      for (const tab of structureProblems) {
        console.log(chalk.yellow(`📄 ${tab.file}:`));
        for (const suggestion of tab.forwardRefComparison.suggestions.slice(0, 2)) {
          console.log(chalk.gray(`   • ${suggestion}`));
        }
        if (tab.structureIssues.length > 0) {
          const criticalIssue = tab.structureIssues.find(i => i.severity === 'critical');
          if (criticalIssue) {
            console.log(chalk.red(`   🚨 ${criticalIssue.suggestion}`));
          }
        }
      }
      console.log(chalk.blue(`\n💡 参考标准: ${this.referenceApiPath}`));
    } else {
      console.log(chalk.green('✅ 所有可验证的文件数据结构都正确'));
    }

    // 建议
    console.log('\n' + chalk.bold.blue('💡 修复建议:'));
    if (analysis.criticalStructureIssues > 0) {
      console.log(chalk.red('1. 🚨 优先修复严重数据结构问题'));
    }
    if (analysis.criticalIssues > 0) {
      console.log(chalk.red('2. 优先修复高严重性骨架字符串'));
    }
    if (analysis.tabsStatus.some(t => !t.exists)) {
      console.log(chalk.yellow('3. 创建缺失的Tab文件'));
    }
    if (analysis.structureScore < 80) {
      console.log(chalk.blue('4. 参考forwardRef标准修复数据结构'));
    }
    if (analysis.overallProgress < 100) {
      console.log(chalk.blue('5. 继续完善未完成的Tab内容'));
    }

    // 🎯 新增检测精度说明
    console.log('\n' + chalk.bold.green('🎯 检测精度优化:'));
    console.log(chalk.gray('✅ 只检测真正的骨架占位符（如 {API_NAME}、{SCENARIO_1_TITLE}）'));
    console.log(chalk.gray('❌ 排除代码示例中的正常变量（如 {count}、{firstName}）'));
    console.log(chalk.gray('🛡️ 基于 generate-api-skeleton.ts 的精确模式匹配'));
    console.log(chalk.gray('🏗️ 新增数据结构验证，与forwardRef标准对比'));

    // 退出码
    process.exitCode = analysis.overallProgress === 100 && analysis.structureScore >= 80 ? 0 : 1;
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const apiName = args[0];

  if (!apiName) {
    console.error(chalk.red('❌ 请提供API名称'));
    console.log(chalk.gray('用法: tsx scripts/enhanced-content-completion.ts <api-name>'));
    console.log(chalk.gray(''));
    console.log(chalk.blue('📋 支持的格式：'));
    console.log(chalk.gray('  react:useState     # React API（显式指定框架）'));
    console.log(chalk.gray('  vue:reactive       # Vue API（显式指定框架）'));
    console.log(chalk.gray('  useState           # 默认React API'));
    console.log(chalk.gray(''));
    console.log(chalk.blue('📂 支持的框架：'));
    console.log(chalk.gray('  react - React Hooks和Components'));
    console.log(chalk.gray('  vue   - Vue Composition API'));
    console.log(chalk.gray(''));
    console.log(chalk.blue('🆕 数据结构验证：'));
    console.log(chalk.gray('  自动与forwardRef标准对比'));
    console.log(chalk.gray('  检测import/export/类型声明问题'));
    console.log(chalk.gray('  识别wrapper object等结构问题'));
    process.exit(1);
  }

  try {
    const detector = new EnhancedContentDetector(process.cwd());
    const analysis = await detector.analyzeApi(apiName);
    detector.generateReport(analysis);

  } catch (error) {
    console.error(chalk.red('💥 检测失败:'), error instanceof Error ? error.message : error);
    process.exit(1);
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default EnhancedContentDetector; 