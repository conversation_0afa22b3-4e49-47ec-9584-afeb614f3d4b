#!/usr/bin/env tsx

/**
 * 🔍 API文档内容完成度检测脚本
 * 
 * 基于骨架字符串检测文件是否完成内容填充
 * 替代之前的 completionStatus 字段方案
 */

import fs from 'fs-extra';
import path from 'path';
import chalk from 'chalk';

// 📋 各Tab文件的骨架字符串检测规则
const SKELETON_DETECTION_RULES: Record<string, string[]> = {
  'basic-info': ['API_NAME', '{API_NAME}'],
  'business-scenarios': ['SCENARIO_1_TITLE', '{SCENARIO_1_TITLE}'],
  'common-questions': ['QUESTION_1', '{QUESTION_1}'],
  'debugging-tips': ['INTRODUCTION', '{INTRODUCTION}'],
  'essence-insights': ['CORE_QUESTION', '{CORE_QUESTION}'],
  'implementation': ['IMPLEMENTATION_MECHANISM', '{IMPLEMENTATION_MECHANISM}'],
  'interview-questions': ['QUESTION_1', '{QUESTION_1}'],
  'knowledge-archaeology': ['INTRODUCTION', '{INTRODUCTION}'],
  'performance-optimization': ['STRATEGY_1', '{STRATEGY_1}']
};

// 📂 9个Tab文件列表
const TAB_FILES = [
  'basic-info.ts',
  'business-scenarios.ts', 
  'common-questions.ts',
  'debugging-tips.ts',
  'essence-insights.ts',
  'implementation.ts',
  'interview-questions.ts',
  'knowledge-archaeology.ts',
  'performance-optimization.ts'
];

interface CompletionResult {
  apiId: string;
  totalTabs: number;
  completedTabs: number;
  incompleteTabs: string[];
  completionPercentage: number;
  details: Record<string, {
    completed: boolean;
    skeletonStrings: string[];
    filePath: string;
  }>;
}

/**
 * 🚨 验证API名称格式（不能包含小数点）
 */
function validateApiName(apiName: string): { valid: boolean; error?: string } {
  if (apiName.includes('.')) {
    return {
      valid: false,
      error: '❌ API名称不能包含小数点: "' + apiName + '"\n   正确格式: "ReactComponentClass" 而不是 "React.ComponentClass"'
    };
  }
  
  return { valid: true };
}

/**
 * 🔍 检测单个文件是否包含骨架字符串
 */
async function checkFileCompletion(filePath: string, tabName: string): Promise<{
  completed: boolean;
  skeletonStrings: string[];
}> {
  try {
    if (!await fs.pathExists(filePath)) {
      return { completed: false, skeletonStrings: ['文件不存在'] };
    }

    const content = await fs.readFile(filePath, 'utf-8');
    const rules = SKELETON_DETECTION_RULES[tabName] || [];
    const foundSkeletons: string[] = [];

    // 检查是否包含任何骨架字符串
    for (const skeleton of rules) {
      if (content.includes(skeleton)) {
        foundSkeletons.push(skeleton);
      }
    }

    return {
      completed: foundSkeletons.length === 0, // 没有骨架字符串 = 已完成
      skeletonStrings: foundSkeletons
    };
  } catch (error) {
    return { completed: false, skeletonStrings: ['读取文件错误'] };
  }
}

/**
 * 🎯 检测API文档完成度
 */
async function checkApiCompletion(apiId: string, framework: string = 'react'): Promise<CompletionResult> {
  // 🚨 验证API名称格式
  const nameValidation = validateApiName(apiId);
  if (!nameValidation.valid) {
    throw new Error(nameValidation.error);
  }

  // 🔍 查找API目录
  const basePath = 'src/data/' + framework;
  let apiDir: string | null = null;

  // 首先检查直接在框架目录下的API
  const directApiPath = path.join(basePath, apiId);
  if (await fs.pathExists(directApiPath)) {
    apiDir = directApiPath;
  } else {
    // 检查所有可能的分类目录
    const categories = ['hooks', 'components', 'api'];
    for (const category of categories) {
      const categoryPath = path.join(basePath, category);
      if (await fs.pathExists(categoryPath)) {
        const apiPath = path.join(categoryPath, apiId);
        if (await fs.pathExists(apiPath)) {
          apiDir = apiPath;
          break;
        }
      }
    }
  }

  if (!apiDir) {
    throw new Error('❌ 未找到API目录: ' + apiId);
  }

  // 📊 检测结果
  const result: CompletionResult = {
    apiId,
    totalTabs: TAB_FILES.length,
    completedTabs: 0,
    incompleteTabs: [],
    completionPercentage: 0,
    details: {}
  };

  // 🔍 逐个检测Tab文件
  for (const tabFile of TAB_FILES) {
    const tabName = tabFile.replace('.ts', '');
    const filePath = path.join(apiDir, tabFile);
    
    const fileResult = await checkFileCompletion(filePath, tabName);
    
    result.details[tabName] = {
      completed: fileResult.completed,
      skeletonStrings: fileResult.skeletonStrings,
      filePath
    };

    if (fileResult.completed) {
      result.completedTabs++;
    } else {
      result.incompleteTabs.push(tabName);
    }
  }

  result.completionPercentage = Math.round((result.completedTabs / result.totalTabs) * 100);

  return result;
}

/**
 * 🎨 格式化输出结果
 */
function formatCompletionReport(result: CompletionResult): string {
  const { apiId, completedTabs, totalTabs, completionPercentage, incompleteTabs, details } = result;

  let report = '\n📊 API文档完成度报告: ' + chalk.cyan(apiId) + '\n';
  report += '─'.repeat(50) + '\n';
  report += '📈 完成进度: ' + chalk.yellow(completedTabs) + '/' + chalk.yellow(totalTabs) + ' (' + chalk.yellow(completionPercentage + '%') + ')\n\n';

  if (incompleteTabs.length > 0) {
    report += '🚧 未完成的Tab文件:\n';
    for (const tabName of incompleteTabs) {
      const detail = details[tabName];
      report += '   ' + chalk.red('❌') + ' ' + tabName + '.ts\n';
      if (detail.skeletonStrings.length > 0) {
        report += '      🔍 发现骨架字符串: ' + chalk.gray(detail.skeletonStrings.join(', ')) + '\n';
      }
    }
    report += '\n';
  }

  if (completedTabs > 0) {
    report += '✅ 已完成的Tab文件:\n';
    for (const [tabName, detail] of Object.entries(details)) {
      if (detail.completed) {
        report += '   ' + chalk.green('✅') + ' ' + tabName + '.ts\n';
      }
    }
  }

  return report;
}

/**
 * 🚀 主函数
 */
async function main() {
  const apiId = process.argv[2];
  const framework = process.argv[3] || 'react';

  if (!apiId) {
    console.error(chalk.red('❌ 错误:') + ' 请提供API名称');
    console.log(chalk.yellow('📖 用法:') + ' tsx scripts/check-content-completion.ts <api-name> [framework]');
    console.log(chalk.yellow('📖 示例:') + ' tsx scripts/check-content-completion.ts useState react');
    process.exit(1);
  }

  try {
    console.log(chalk.blue('🔍 检测中...') + ' ' + apiId);
    
    const result = await checkApiCompletion(apiId, framework);
    const report = formatCompletionReport(result);
    
    console.log(report);

    // 🎯 返回适当的退出码
    if (result.completionPercentage === 100) {
      console.log(chalk.green('🎉 完成!') + ' 所有Tab文件都已填充内容');
      process.exit(0);
    } else {
      console.log(chalk.yellow('⚠️  未完成') + ' 还有 ' + result.incompleteTabs.length + ' 个Tab文件需要完善');
      process.exit(1);
    }

  } catch (error) {
    console.error(chalk.red('❌ 错误:') + ' ' + error.message);
    process.exit(1);
  }
}

// 🏃‍♂️ 执行主函数
if (import.meta.url === 'file://' + process.argv[1]) {
  main().catch(console.error);
}

// 📤 导出函数供其他脚本使用
export { checkApiCompletion, validateApiName, type CompletionResult }; 