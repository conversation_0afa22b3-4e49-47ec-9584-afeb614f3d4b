#!/usr/bin/env tsx

/**
 * 测试文件清理工具
 * 用于清理Puppeteer测试生成的文件
 */

import fs from 'fs';
import path from 'path';

interface CleanOptions {
  outputDir: string;
  keepLast: number;
  dryRun: boolean;
  help: boolean;
}

class TestCleaner {
  private options: CleanOptions;

  constructor() {
    this.options = this.parseArgs();
  }

  private parseArgs(): CleanOptions {
    const args = process.argv.slice(2);
    const options: CleanOptions = {
      outputDir: 'test-results',
      keepLast: 0,
      dryRun: false,
      help: false
    };

    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      
      switch (arg) {
        case '--help':
        case '-h':
          options.help = true;
          break;
        case '--output':
        case '-o':
          options.outputDir = args[++i];
          break;
        case '--keep-last':
        case '-k':
          options.keepLast = parseInt(args[++i]);
          break;
        case '--dry-run':
        case '-n':
          options.dryRun = true;
          break;
      }
    }

    return options;
  }

  private showHelp(): void {
    console.log(`
🧹 测试文件清理工具

用法:
  tsx scripts/puppeteer/clean.ts [选项]

选项:
  --output, -o <DIR>   指定测试结果目录 (默认: test-results)
  --keep-last, -k <N>  保留最新的N个截图文件 (默认: 0 - 删除所有)
  --dry-run, -n        预览模式，不实际删除文件
  --help, -h           显示帮助信息

示例:
  # 清理所有测试文件
  tsx scripts/puppeteer/clean.ts
  
  # 保留最新5个截图文件
  tsx scripts/puppeteer/clean.ts --keep-last 5
  
  # 预览要删除的文件（不实际删除）
  tsx scripts/puppeteer/clean.ts --dry-run
  
  # 清理指定目录
  tsx scripts/puppeteer/clean.ts --output ./my-test-results

清理范围:
  📸 所有截图文件 (*.png)
  📊 测试报告文件 (*.html, *.json)
    `);
  }

  async run(): Promise<void> {
    if (this.options.help) {
      this.showHelp();
      return;
    }

    const outputDir = path.resolve(this.options.outputDir);
    const screenshotsDir = path.join(outputDir, 'screenshots');

    if (!fs.existsSync(outputDir)) {
      console.log('📁 测试输出目录不存在:', outputDir);
      return;
    }

    console.log(`🧹 ${this.options.dryRun ? '预览' : '开始'}清理测试文件...`);
    console.log(`📁 目录: ${outputDir}`);

    try {
      let totalDeleted = 0;
      let totalKept = 0;

      // 清理截图文件
      if (fs.existsSync(screenshotsDir)) {
        const files = fs.readdirSync(screenshotsDir);
        const pngFiles = files.filter(f => f.endsWith('.png'));
        
        if (pngFiles.length === 0) {
          console.log('📸 没有找到截图文件');
        } else {
          console.log(`📸 发现 ${pngFiles.length} 个截图文件`);

          if (this.options.keepLast > 0) {
            // 保留最新的N个文件
            const fileStats = pngFiles.map(f => ({
              name: f,
              path: path.join(screenshotsDir, f),
              mtime: fs.statSync(path.join(screenshotsDir, f)).mtime
            })).sort((a, b) => b.mtime.getTime() - a.mtime.getTime());

            const filesToKeep = fileStats.slice(0, this.options.keepLast);
            const filesToDelete = fileStats.slice(this.options.keepLast);
            
            console.log(`🗂️  将保留最新 ${filesToKeep.length} 个文件:`);
            filesToKeep.forEach(f => console.log(`   ✅ ${f.name} (${f.mtime.toLocaleString()})`));

            if (filesToDelete.length > 0) {
              console.log(`🗑️  将删除 ${filesToDelete.length} 个文件:`);
              for (const file of filesToDelete) {
                console.log(`   ❌ ${file.name} (${file.mtime.toLocaleString()})`);
                if (!this.options.dryRun) {
                  fs.unlinkSync(file.path);
                }
                totalDeleted++;
              }
            }
            
            totalKept = filesToKeep.length;
          } else {
            // 删除所有截图文件
            console.log(`🗑️  将删除所有 ${pngFiles.length} 个截图文件:`);
            for (const file of pngFiles) {
              console.log(`   ❌ ${file}`);
              if (!this.options.dryRun) {
                fs.unlinkSync(path.join(screenshotsDir, file));
              }
              totalDeleted++;
            }
          }
        }
      } else {
        console.log('📸 截图目录不存在');
      }

      // 清理报告文件
      const reportFiles = ['render-error-report.html', 'render-error-report.json', 'unified-test-report.json'];
      const existingReports = reportFiles.filter(f => fs.existsSync(path.join(outputDir, f)));
      
      if (existingReports.length === 0) {
        console.log('📊 没有找到报告文件');
      } else {
        console.log(`📊 发现 ${existingReports.length} 个报告文件`);
        console.log(`🗑️  将删除报告文件:`);
        
        for (const reportFile of existingReports) {
          console.log(`   ❌ ${reportFile}`);
          if (!this.options.dryRun) {
            fs.unlinkSync(path.join(outputDir, reportFile));
          }
          totalDeleted++;
        }
      }

      // 输出统计
      console.log(`\n📊 清理统计:`);
      console.log(`${'='.repeat(30)}`);
      if (this.options.dryRun) {
        console.log(`🔍 预览模式 - 未实际删除文件`);
        console.log(`📝 计划删除: ${totalDeleted} 个文件`);
        console.log(`📝 计划保留: ${totalKept} 个文件`);
      } else {
        console.log(`🗑️  已删除: ${totalDeleted} 个文件`);
        console.log(`🗂️  已保留: ${totalKept} 个文件`);
        console.log(`✅ 清理完成`);
      }

    } catch (error) {
      console.error('❌ 清理过程中出错:', error);
      process.exit(1);
    }
  }
}

// 主函数
async function main() {
  const cleaner = new TestCleaner();
  await cleaner.run();
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { TestCleaner }; 