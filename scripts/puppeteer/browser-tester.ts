#!/usr/bin/env tsx

/**
 * 统一的Puppeteer浏览器测试工具
 * 整合所有浏览器自动化测试功能
 */

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';

export interface BrowserTestConfig {
  headless?: boolean;
  viewport?: { width: number; height: number };
  timeout?: number;
  baseUrl?: string;
  screenshotDir?: string;
}

export interface TestResult {
  apiName: string;
  url: string;
  success: boolean;
  loadTime: number;
  errors: ErrorLog[];
  warnings: ErrorLog[];
  tabStatus: Record<string, TabStatus>;
  contentStatus: ContentStatus;
  screenshots?: ScreenshotResult;
}

export interface ErrorLog {
  timestamp: string;
  type: 'error' | 'warning' | 'log' | 'info';
  source: 'console' | 'page' | 'network' | 'runtime';
  message: string;
  stack?: string;
  url?: string;
  line?: number;
  column?: number;
  details?: any;
}

export interface TabStatus {
  rendered: boolean;
  hasError: boolean;
  errorMessage?: string;
  isSkeletonContent?: boolean;
  contentQuality?: 'skeleton' | 'partial' | 'complete';
}

export interface ContentStatus {
  overallQuality: 'skeleton' | 'partial' | 'complete';
  skeletonTabs: string[];
  partialTabs: string[];
  completeTabs: string[];
  totalTabs: number;
  completionRate: number;
}

export interface ScreenshotResult {
  full: string;
  errors: string[];
}

export class BrowserTester {
  private browser: puppeteer.Browser | null = null;
  private page: puppeteer.Page | null = null;
  private logs: ErrorLog[] = [];
  private config: BrowserTestConfig;

  constructor(config: BrowserTestConfig = {}) {
    this.config = {
      headless: false,
      viewport: { width: 1920, height: 1080 },
      timeout: 30000,
      baseUrl: 'http://localhost:8080',
      screenshotDir: 'test-results/screenshots',
      ...config
    };
  }

  async init(): Promise<void> {
    console.log('🚀 启动浏览器...');
    
    this.browser = await puppeteer.launch({
      headless: this.config.headless,
      devtools: false,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });

    this.page = await this.browser.newPage();
    await this.page.setViewport(this.config.viewport!);
    this.setupLogListeners();
  }

  private setupLogListeners(): void {
    if (!this.page) return;

    // 监听控制台消息
    this.page.on('console', (msg) => {
      const type = msg.type() as 'error' | 'warning' | 'log' | 'info';
      this.logs.push({
        timestamp: new Date().toISOString(),
        type,
        source: 'console',
        message: msg.text(),
        details: {
          args: msg.args().map(arg => arg.toString())
        }
      });
    });

    // 监听页面错误
    this.page.on('pageerror', (error) => {
      this.logs.push({
        timestamp: new Date().toISOString(),
        type: 'error',
        source: 'page',
        message: error.message,
        stack: error.stack
      });
    });

    // 监听请求失败
    this.page.on('requestfailed', (request) => {
      this.logs.push({
        timestamp: new Date().toISOString(),
        type: 'error',
        source: 'network',
        message: `Request failed: ${request.url()}`,
        details: {
          method: request.method(),
          failure: request.failure()?.errorText
        }
      });
    });

    // 监听响应错误
    this.page.on('response', (response) => {
      if (response.status() >= 400) {
        this.logs.push({
          timestamp: new Date().toISOString(),
          type: 'error',
          source: 'network',
          message: `HTTP ${response.status()}: ${response.url()}`,
          details: {
            status: response.status(),
            statusText: response.statusText()
          }
        });
      }
    });

    // 监听运行时异常
    this.page.on('error', (error) => {
      this.logs.push({
        timestamp: new Date().toISOString(),
        type: 'error',
        source: 'runtime',
        message: error.message,
        stack: error.stack
      });
    });
  }

  async testApi(apiName: string, isDirectUrl = false): Promise<TestResult> {
    if (!this.page) throw new Error('页面未初始化');

    const url = isDirectUrl
      ? `${this.config.baseUrl}${apiName}`
      : `${this.config.baseUrl}/full-tab-render/${apiName}`;
    console.log(`📋 测试API: ${apiName}`);
    console.log(`🌐 访问URL: ${url}`);

    // 清空之前的日志
    this.logs = [];
    
    const startTime = Date.now();
    
    try {
      // 访问页面
      await this.page.goto(url, { 
        waitUntil: 'networkidle0',
        timeout: this.config.timeout 
      });

      // 等待页面完全加载
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 检查Tab渲染状态
      const tabStatus = await this.checkTabStatus();

      // 检查内容质量状态
      const contentStatus = await this.checkContentStatus(apiName);

      // 截图
      const screenshots = await this.takeScreenshots(apiName);

      const loadTime = Date.now() - startTime;
      const errors = this.logs.filter(log => log.type === 'error');
      const warnings = this.logs.filter(log => log.type === 'warning');

      console.log(`⏱️  加载时间: ${loadTime}ms`);
      console.log(`❌ 错误数量: ${errors.length}`);
      console.log(`⚠️  警告数量: ${warnings.length}`);
      console.log(`🎭 页面渲染: ${errors.length === 0 ? '成功' : '失败'}`);
      console.log(`📊 内容检测: 请使用 tsx scripts/check-content-completion.ts ${apiName}`);

      if (errors.length > 0) {
        console.log(`🚨 页面错误详情:`);
        errors.slice(0, 3).forEach((error, index) => {
          console.log(`   ${index + 1}. ${error.message}`);
        });
      }

      return {
        apiName,
        url,
        success: errors.length === 0,
        loadTime,
        errors,
        warnings,
        tabStatus,
        contentStatus,
        screenshots
      };

    } catch (error) {
      const loadTime = Date.now() - startTime;
      
      this.logs.push({
        timestamp: new Date().toISOString(),
        type: 'error',
        source: 'runtime',
        message: `页面访问失败: ${error}`,
        stack: error instanceof Error ? error.stack : undefined
      });

      return {
        apiName,
        url,
        success: false,
        loadTime,
        errors: this.logs.filter(log => log.type === 'error'),
        warnings: this.logs.filter(log => log.type === 'warning'),
        tabStatus: {},
        contentStatus: {
          overallQuality: 'skeleton',
          skeletonTabs: [],
          partialTabs: [],
          completeTabs: [],
          totalTabs: 0,
          completionRate: 0
        }
      };
    }
  }

  async testMultipleApis(apiNames: string[]): Promise<TestResult[]> {
    const results: TestResult[] = [];
    
    for (const apiName of apiNames) {
      console.log(`\n${'='.repeat(50)}`);
      const result = await this.testApi(apiName);
      results.push(result);
      
      // 短暂等待，避免过快请求
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    return results;
  }

  private async checkTabStatus(): Promise<Record<string, TabStatus>> {
    if (!this.page) return {};

    try {
      const tabStatus = await this.page.evaluate(() => {
        const result: Record<string, TabStatus> = {};

        // 查找所有Tab卡片
        const tabCards = document.querySelectorAll('[class*="ant-card"]');

        tabCards.forEach((card, index) => {
          const titleElement = card.querySelector('.ant-card-head-title');
          const title = titleElement?.textContent || `Tab-${index + 1}`;

          // 检查是否有错误标识
          const hasErrorIcon = card.querySelector('[class*="ExclamationCircleOutlined"]');
          const errorAlert = card.querySelector('.ant-alert-error');

          // 检查内容质量
          const cardBody = card.querySelector('.ant-card-body');
          const textContent = cardBody?.textContent || '';

          // 骨架内容检测
          const isSkeletonContent = textContent.includes('TODO: 实现') ||
                                   textContent.includes('暂无内容') ||
                                   textContent.includes('PLACEHOLDER') ||
                                   textContent.includes('示例代码') ||
                                   textContent.includes('待完善') ||
                                   textContent.length < 500;

          // 内容质量评估
          let contentQuality: 'skeleton' | 'partial' | 'complete' = 'complete';
          if (isSkeletonContent) {
            contentQuality = 'skeleton';
          } else if (textContent.length < 2000) {
            contentQuality = 'partial';
          }

          result[title] = {
            rendered: true,
            hasError: !!hasErrorIcon || !!errorAlert,
            errorMessage: errorAlert?.textContent || undefined,
            isSkeletonContent,
            contentQuality
          };
        });

        return result;
      });

      return tabStatus;
    } catch (error) {
      console.error('检查Tab状态失败:', error);
      return {};
    }
  }

  private async checkContentStatus(apiName: string): Promise<ContentStatus> {
    // 🎯 Puppeteer专注于页面渲染测试，内容检测由 check-content-completion.ts 负责
    console.log(`📋 Puppeteer专注于页面渲染测试，内容检测请使用: tsx scripts/check-content-completion.ts ${apiName}`);
    
    return {
      overallQuality: 'complete', // Puppeteer不再判断内容质量
      skeletonTabs: [],
      partialTabs: [],
      completeTabs: [],
      totalTabs: 9,
      completionRate: 100 // 假设渲染成功就是100%（内容质量由专门脚本检测）
    };
  }

  private async takeScreenshots(apiName: string): Promise<ScreenshotResult> {
    if (!this.page) return { full: '', errors: [] };

    const screenshotDir = path.join(process.cwd(), this.config.screenshotDir!);
    fs.mkdirSync(screenshotDir, { recursive: true });

    try {
      // 全页面截图
      const fullScreenshotPath = path.join(screenshotDir, `${apiName}-full.png`);
      await this.page.screenshot({ 
        path: fullScreenshotPath, 
        fullPage: true 
      });

      // 错误区域截图
      const errorScreenshots: string[] = [];
      const errorElements = await this.page.$$('.ant-alert-error, [class*="error"]');
      
      for (let i = 0; i < errorElements.length; i++) {
        const errorScreenshotPath = path.join(screenshotDir, `${apiName}-error-${i + 1}.png`);
        await errorElements[i].screenshot({ path: errorScreenshotPath });
        errorScreenshots.push(errorScreenshotPath);
      }

      return {
        full: fullScreenshotPath,
        errors: errorScreenshots
      };
    } catch (error) {
      console.error('截图失败:', error);
      return { full: '', errors: [] };
    }
  }

  async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
    }
  }
}
