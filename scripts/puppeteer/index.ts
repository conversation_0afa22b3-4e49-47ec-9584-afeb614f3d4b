#!/usr/bin/env tsx

/**
 * Puppeteer工具统一入口
 * 提供所有浏览器自动化测试功能
 */

import fs from 'fs';
import path from 'path';
import { BrowserTester, BrowserTestConfig } from './browser-tester';
import { ReportGenerator } from './report-generator';

interface CliOptions {
  apiName?: string;
  all: boolean;
  headless: boolean;
  baseUrl: string;
  help: boolean;
  outputDir: string;
  clean: boolean;
  keepLast: number;
  directUrl: boolean;
}

class PuppeteerCli {
  private options: CliOptions;

  constructor() {
    this.options = this.parseArgs();
  }

  private parseArgs(): CliOptions {
    const args = process.argv.slice(2);
    const options: CliOptions = {
      all: false,
      headless: false,
      baseUrl: 'http://localhost:8080',
      help: false,
      outputDir: 'test-results',
      clean: false,
      keepLast: 0,
      directUrl: false
    };

    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      
      switch (arg) {
        case '--help':
        case '-h':
          options.help = true;
          break;
        case '--all':
          options.all = true;
          break;
        case '--headless':
          options.headless = true;
          break;
        case '--url':
          options.baseUrl = args[++i];
          break;
        case '--output':
        case '-o':
          options.outputDir = args[++i];
          break;
        case '--clean':
          options.clean = true;
          break;
        case '--keep-last':
          options.keepLast = parseInt(args[++i]);
          break;
        case '--direct-url':
          options.directUrl = true;
          break;
        default:
          if (!arg.startsWith('--') && !options.apiName) {
            options.apiName = arg;
          }
          break;
      }
    }

    return options;
  }

  private showHelp(): void {
    console.log(`
🎭 Puppeteer浏览器测试工具

用法:
  tsx scripts/puppeteer/index.ts [API名称 or URL路径] [选项]

选项:
  --all                 测试所有API
  --headless           无头模式运行（后台运行，不显示浏览器）
  --url <URL>          指定基础URL (默认: http://localhost:8080)
  --output, -o <DIR>   指定输出目录 (默认: test-results)
  --clean              清理旧的测试文件
  --keep-last <N>      保留最新的N个测试文件
  --direct-url         将API名称参数视为直接的URL路径（不加前缀）
  --help, -h           显示帮助信息

示例:
  # 测试单个API（显示浏览器）
  tsx scripts/puppeteer/index.ts ReactMemo
  
  # 直接测试一个URL路径
  tsx scripts/puppeteer/index.ts /test-api/mermaid-debug --direct-url

  # 测试所有API（无头模式）
  tsx scripts/puppeteer/index.ts --all --headless
  
  # 指定自定义URL和输出目录
  tsx scripts/puppeteer/index.ts ReactMemo --url http://localhost:3000 --output ./reports
  
  # 快速测试（推荐用于CI/CD）
  tsx scripts/puppeteer/index.ts --all --headless --output ./ci-reports

  # 清理旧文件后测试
  tsx scripts/puppeteer/index.ts --all --clean
  
  # 保留最新5个测试文件，清理其余
  tsx scripts/puppeteer/index.ts ReactMemo --clean --keep-last 5

功能特性:
  ✅ 自动化浏览器测试
  ✅ 错误日志收集
  ✅ 截图功能
  ✅ Tab渲染状态检查
  ✅ HTML/JSON报告生成
  ✅ 网络请求监控
  ✅ 控制台日志捕获
    `);
  }

  private getAllApis(): string[] {
    try {
      const componentsDir = path.join(process.cwd(), 'src/data/react/components');
      if (!fs.existsSync(componentsDir)) {
        console.warn('⚠️ 组件目录不存在，尝试查找其他API目录...');
        
        // 尝试查找hooks目录
        const hooksDir = path.join(process.cwd(), 'src/data/react/hooks');
        if (fs.existsSync(hooksDir)) {
          return fs.readdirSync(hooksDir).filter(dir => 
            fs.statSync(path.join(hooksDir, dir)).isDirectory()
          );
        }
        
        return [];
      }
      
      return fs.readdirSync(componentsDir).filter(dir => 
        fs.statSync(path.join(componentsDir, dir)).isDirectory()
      );
    } catch (error) {
      console.error('❌ 获取API列表失败:', error);
      return [];
    }
  }

  private async cleanTestResults(): Promise<void> {
    const outputDir = path.resolve(this.options.outputDir);
    const screenshotsDir = path.join(outputDir, 'screenshots');

    if (!fs.existsSync(outputDir)) {
      console.log('📁 测试输出目录不存在，无需清理');
      return;
    }

    console.log('🧹 开始清理测试文件...');

    try {
      // 清理截图文件
      if (fs.existsSync(screenshotsDir)) {
        const files = fs.readdirSync(screenshotsDir);
        const pngFiles = files.filter(f => f.endsWith('.png'));
        
        if (this.options.keepLast > 0) {
          // 保留最新的N个文件
          const fileStats = pngFiles.map(f => ({
            name: f,
            path: path.join(screenshotsDir, f),
            mtime: fs.statSync(path.join(screenshotsDir, f)).mtime
          })).sort((a, b) => b.mtime.getTime() - a.mtime.getTime());

          const filesToDelete = fileStats.slice(this.options.keepLast);
          
          for (const file of filesToDelete) {
            fs.unlinkSync(file.path);
          }
          
          console.log(`🗂️  保留最新 ${Math.min(this.options.keepLast, fileStats.length)} 个截图文件`);
          console.log(`🗑️  删除 ${filesToDelete.length} 个旧截图文件`);
        } else {
          // 删除所有截图文件
          for (const file of pngFiles) {
            fs.unlinkSync(path.join(screenshotsDir, file));
          }
          console.log(`🗑️  删除 ${pngFiles.length} 个截图文件`);
        }
      }

      // 清理报告文件
      const reportFiles = ['render-error-report.html', 'render-error-report.json', 'unified-test-report.json'];
      let deletedReports = 0;
      
      for (const reportFile of reportFiles) {
        const reportPath = path.join(outputDir, reportFile);
        if (fs.existsSync(reportPath)) {
          fs.unlinkSync(reportPath);
          deletedReports++;
        }
      }

      if (deletedReports > 0) {
        console.log(`📊 删除 ${deletedReports} 个报告文件`);
      }

      console.log('✅ 测试文件清理完成');
    } catch (error) {
      console.error('❌ 清理过程中出错:', error);
      throw error;
    }
  }

  async run(): Promise<void> {
    if (this.options.help) {
      this.showHelp();
      return;
    }

    if (!this.options.all && !this.options.apiName) {
      console.error('❌ 请指定API名称或使用 --all 参数');
      console.log('💡 使用 --help 查看帮助信息');
      process.exit(1);
    }

    const config: BrowserTestConfig = {
      headless: this.options.headless,
      baseUrl: this.options.baseUrl,
      viewport: { width: 1920, height: 1080 },
      timeout: 30000,
      screenshotDir: path.join(this.options.outputDir, 'screenshots')
    };

    const tester = new BrowserTester(config);
    const reportGenerator = new ReportGenerator();

    try {
      console.log('🎭 初始化Puppeteer浏览器测试...');
      console.log(`📊 配置: ${this.options.headless ? '无头模式' : '可视模式'} | ${this.options.baseUrl}`);
      
      // 清理旧文件（如果需要）
      if (this.options.clean) {
        await this.cleanTestResults();
      }
      
      await tester.init();

      let results;

      if (this.options.all) {
        const apiNames = this.getAllApis();
        if (apiNames.length === 0) {
          console.error('❌ 未找到任何API进行测试');
          process.exit(1);
        }
        
        console.log(`🔍 发现 ${apiNames.length} 个API，开始批量测试...`);
        console.log(`📋 API列表: ${apiNames.join(', ')}`);
        
        results = await tester.testMultipleApis(apiNames);
      } else {
        console.log(`🎯 测试单个API: ${this.options.apiName}`);
        const result = await tester.testApi(this.options.apiName!, this.options.directUrl);
        results = [result];
      }

      // 生成报告
      await reportGenerator.generateReport(results, this.options.outputDir);

      // 输出简要统计
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;
      const totalErrors = results.reduce((sum, r) => sum + r.errors.length, 0);
      const totalWarnings = results.reduce((sum, r) => sum + r.warnings.length, 0);

      console.log(`\n📊 Puppeteer测试完成统计:`);
      console.log(`${'='.repeat(40)}`);
      console.log(`总计API: ${results.length}`);
      console.log(`✅ 页面渲染成功: ${successful}`);
      console.log(`❌ 页面渲染失败: ${failed}`);
      console.log(`🐛 总页面错误: ${totalErrors}`);
      console.log(`⚠️  总警告: ${totalWarnings}`);

      console.log(`\n📋 内容完成度检测:`);
      console.log(`${'='.repeat(40)}`);
      console.log(`🎯 请使用专门的检测脚本:`);
      console.log(`   tsx scripts/check-content-completion.ts <api-name>`);

      if (failed > 0) {
        console.log(`\n❌ 页面渲染失败的API:`);
        results.filter(r => !r.success).forEach(r => {
          console.log(`   - ${r.apiName} (${r.errors.length} 个错误)`);
        });
      }

      console.log(`\n📁 报告位置: ${path.resolve(this.options.outputDir)}`);
      console.log(`🌐 查看HTML报告: file://${path.resolve(this.options.outputDir, 'render-error-report.html')}`);

      // 根据结果设置退出码
      process.exit(failed > 0 ? 1 : 0);

    } catch (error) {
      console.error('❌ 测试过程失败:', error);
      process.exit(1);
    } finally {
      await tester.close();
    }
  }
}

// 主函数
async function main() {
  const cli = new PuppeteerCli();
  await cli.run();
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

// 优雅退出
process.on('SIGINT', () => {
  console.log('\n👋 收到退出信号，正在清理...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 收到终止信号，正在清理...');
  process.exit(0);
});

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { BrowserTester, ReportGenerator, type BrowserTestConfig };