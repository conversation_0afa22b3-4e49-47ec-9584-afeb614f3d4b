#!/usr/bin/env tsx

/**
 * 测试报告生成器
 * 生成HTML和JSON格式的测试报告
 */

import fs from 'fs';
import path from 'path';
import { TestResult } from './browser-tester';

export interface ReportData {
  timestamp: string;
  summary: {
    total: number;
    successful: number;
    failed: number;
    totalErrors: number;
    totalWarnings: number;
    averageLoadTime: number;
    skeletonApis: number;
    partialApis: number;
    completeApis: number;
    averageCompletionRate: number;
  };
  results: TestResult[];
}

export class ReportGenerator {
  async generateReport(results: TestResult[], outputDir: string = 'test-results'): Promise<void> {
    fs.mkdirSync(outputDir, { recursive: true });

    // 生成JSON报告
    const reportData = this.createReportData(results);
    const jsonPath = path.join(outputDir, 'render-error-report.json');
    fs.writeFileSync(jsonPath, JSON.stringify(reportData, null, 2));

    // 生成HTML报告
    const htmlReport = this.generateHtmlReport(reportData);
    const htmlPath = path.join(outputDir, 'render-error-report.html');
    fs.writeFileSync(htmlPath, htmlReport);

    console.log(`\n📊 报告生成完成:`);
    console.log(`📄 JSON报告: ${jsonPath}`);
    console.log(`🌐 HTML报告: ${htmlPath}`);
  }

  private createReportData(results: TestResult[]): ReportData {
    const skeletonApis = results.filter(r => r.contentStatus.overallQuality === 'skeleton').length;
    const partialApis = results.filter(r => r.contentStatus.overallQuality === 'partial').length;
    const completeApis = results.filter(r => r.contentStatus.overallQuality === 'complete').length;
    const averageCompletionRate = results.length > 0
      ? results.reduce((sum, r) => sum + r.contentStatus.completionRate, 0) / results.length
      : 0;

    return {
      timestamp: new Date().toISOString(),
      summary: {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        totalErrors: results.reduce((sum, r) => sum + r.errors.length, 0),
        totalWarnings: results.reduce((sum, r) => sum + r.warnings.length, 0),
        averageLoadTime: results.reduce((sum, r) => sum + r.loadTime, 0) / results.length,
        skeletonApis,
        partialApis,
        completeApis,
        averageCompletionRate
      },
      results
    };
  }

  private generateHtmlReport(data: ReportData): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>渲染错误报告</title>
    <meta charset="utf-8">
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; }
        .header h1 { margin: 0; font-size: 28px; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .summary { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            padding: 30px; 
            background: #f8f9fa; 
        }
        .summary-card { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
            text-align: center; 
        }
        .summary-card h3 { margin: 0 0 10px 0; color: #666; font-size: 14px; text-transform: uppercase; }
        .summary-card .value { font-size: 32px; font-weight: bold; margin: 0; }
        .success .value { color: #52c41a; }
        .error .value { color: #ff4d4f; }
        .warning .value { color: #faad14; }
        .info .value { color: #1890ff; }
        .results { padding: 30px; }
        .api-result { 
            border: 1px solid #e8e8e8; 
            margin-bottom: 20px; 
            border-radius: 8px; 
            overflow: hidden;
        }
        .api-header { 
            background: #fafafa; 
            padding: 15px 20px; 
            font-weight: 600; 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
        }
        .success-border { border-left: 4px solid #52c41a; }
        .error-border { border-left: 4px solid #ff4d4f; }
        .logs { padding: 20px; }
        .log-section { margin-bottom: 20px; }
        .log-section h4 { margin: 0 0 10px 0; color: #333; }
        .log-item { 
            margin: 8px 0; 
            padding: 12px; 
            border-radius: 6px; 
            border-left: 3px solid; 
        }
        .log-error { background: #fff2f0; border-left-color: #ff4d4f; }
        .log-warning { background: #fffbe6; border-left-color: #faad14; }
        .log-source { font-weight: 600; color: #666; font-size: 12px; text-transform: uppercase; }
        .log-message { margin-top: 4px; }
        .stack-trace { 
            background: #f5f5f5; 
            padding: 10px; 
            margin-top: 8px; 
            border-radius: 4px; 
            font-family: 'Monaco', 'Consolas', monospace; 
            font-size: 12px; 
            overflow-x: auto; 
            white-space: pre-wrap; 
        }
        .tab-status { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px; }
        .tab-item { 
            padding: 10px; 
            border-radius: 6px; 
            border: 1px solid #e8e8e8; 
            display: flex; 
            align-items: center; 
            gap: 8px; 
        }
        .tab-success { background: #f6ffed; border-color: #b7eb8f; }
        .tab-error { background: #fff2f0; border-color: #ffccc7; }
        .status-icon { font-size: 16px; }
        .load-time { 
            background: #e6f7ff; 
            color: #1890ff; 
            padding: 4px 8px; 
            border-radius: 4px; 
            font-size: 12px; 
            font-weight: 500; 
        }
        .no-issues { 
            text-align: center; 
            padding: 40px; 
            color: #52c41a; 
            font-size: 16px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 渲染错误报告</h1>
            <p>生成时间: ${new Date(data.timestamp).toLocaleString('zh-CN')}</p>
        </div>
        
        <div class="summary">
            <div class="summary-card info">
                <h3>总计API</h3>
                <p class="value">${data.summary.total}</p>
            </div>
            <div class="summary-card success">
                <h3>渲染成功</h3>
                <p class="value">${data.summary.successful}</p>
            </div>
            <div class="summary-card error">
                <h3>渲染失败</h3>
                <p class="value">${data.summary.failed}</p>
            </div>
            <div class="summary-card error">
                <h3>总错误数</h3>
                <p class="value">${data.summary.totalErrors}</p>
            </div>
            <div class="summary-card warning">
                <h3>总警告数</h3>
                <p class="value">${data.summary.totalWarnings}</p>
            </div>
            <div class="summary-card info">
                <h3>平均加载时间</h3>
                <p class="value">${data.summary.averageLoadTime.toFixed(0)}<span style="font-size: 14px;">ms</span></p>
            </div>
            <div class="summary-card error">
                <h3>骨架内容</h3>
                <p class="value">${data.summary.skeletonApis}</p>
            </div>
            <div class="summary-card warning">
                <h3>部分完成</h3>
                <p class="value">${data.summary.partialApis}</p>
            </div>
            <div class="summary-card success">
                <h3>完整内容</h3>
                <p class="value">${data.summary.completeApis}</p>
            </div>
            <div class="summary-card info">
                <h3>平均完成度</h3>
                <p class="value">${data.summary.averageCompletionRate.toFixed(1)}<span style="font-size: 14px;">%</span></p>
            </div>
        </div>

        <div class="results">
            <h2>详细结果</h2>
            ${data.results.map((result) => `
                <div class="api-result ${result.success ? 'success-border' : 'error-border'}">
                    <div class="api-header">
                        <span>${result.success ? '✅' : '❌'} ${result.apiName}</span>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <span style="background: ${result.contentStatus.overallQuality === 'complete' ? '#52c41a' : result.contentStatus.overallQuality === 'partial' ? '#faad14' : '#ff4d4f'}; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">
                                ${result.contentStatus.overallQuality === 'complete' ? '完整内容' : result.contentStatus.overallQuality === 'partial' ? '部分完成' : '骨架内容'}
                            </span>
                            <span style="background: #1890ff; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">
                                ${result.contentStatus.completionRate.toFixed(1)}%
                            </span>
                            <span class="load-time">${result.loadTime}ms</span>
                        </div>
                    </div>
                    <div class="logs">
                        <p><strong>URL:</strong> <a href="${result.url}" target="_blank">${result.url}</a></p>
                        
                        ${result.errors.length > 0 ? `
                            <div class="log-section">
                                <h4>❌ 错误 (${result.errors.length})</h4>
                                ${result.errors.map(error => `
                                    <div class="log-item log-error">
                                        <div class="log-source">[${error.source}]</div>
                                        <div class="log-message">${error.message}</div>
                                        ${error.stack ? `<div class="stack-trace">${error.stack}</div>` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                        
                        ${result.warnings.length > 0 ? `
                            <div class="log-section">
                                <h4>⚠️ 警告 (${result.warnings.length})</h4>
                                ${result.warnings.map(warning => `
                                    <div class="log-item log-warning">
                                        <div class="log-source">[${warning.source}]</div>
                                        <div class="log-message">${warning.message}</div>
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                        
                        <div class="log-section">
                            <h4>📋 Tab渲染状态</h4>
                            ${Object.keys(result.tabStatus).length > 0 ? `
                                <div class="tab-status">
                                    ${Object.entries(result.tabStatus).map(([tabName, status]) => `
                                        <div class="tab-item ${status.hasError ? 'tab-error' : 'tab-success'}">
                                            <span class="status-icon">${status.hasError ? '❌' : '✅'}</span>
                                            <span>${tabName}</span>
                                            ${status.contentQuality ? `<span style="margin-left: 8px; background: ${status.contentQuality === 'complete' ? '#52c41a' : status.contentQuality === 'partial' ? '#faad14' : '#ff4d4f'}; color: white; padding: 1px 6px; border-radius: 3px; font-size: 10px;">${status.contentQuality === 'complete' ? '完整' : status.contentQuality === 'partial' ? '部分' : '骨架'}</span>` : ''}
                                            ${status.errorMessage ? `<br><small>${status.errorMessage}</small>` : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            ` : '<p>无Tab状态信息</p>'}
                        </div>

                        <div class="log-section">
                            <h4>📊 内容完成度分析</h4>
                            <div style="margin-bottom: 15px;">
                                <div style="background: #f5f5f5; padding: 15px; border-radius: 6px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                        <span><strong>总体质量:</strong> ${result.contentStatus.overallQuality === 'complete' ? '✅ 完整内容' : result.contentStatus.overallQuality === 'partial' ? '🔄 部分完成' : '🏗️ 骨架内容'}</span>
                                        <span><strong>完成度:</strong> ${result.contentStatus.completionRate.toFixed(1)}% (${result.contentStatus.completeTabs.length}/${result.contentStatus.totalTabs})</span>
                                    </div>
                                    <div style="background: #e6e6e6; height: 8px; border-radius: 4px; overflow: hidden;">
                                        <div style="background: linear-gradient(90deg, #ff4d4f 0%, #faad14 50%, #52c41a 100%); height: 100%; width: ${result.contentStatus.completionRate}%; transition: width 0.3s;"></div>
                                    </div>
                                </div>
                            </div>
                            ${result.contentStatus.skeletonTabs.length > 0 ? `
                                <div style="margin-bottom: 10px;">
                                    <strong>🏗️ 骨架内容 (${result.contentStatus.skeletonTabs.length}个):</strong>
                                    <div style="margin-top: 5px;">
                                        ${result.contentStatus.skeletonTabs.map(tab => `<span style="background: #fff2f0; color: #ff4d4f; padding: 2px 6px; border-radius: 3px; margin: 2px; display: inline-block; font-size: 12px;">${tab}</span>`).join('')}
                                    </div>
                                </div>
                            ` : ''}
                            ${result.contentStatus.partialTabs.length > 0 ? `
                                <div style="margin-bottom: 10px;">
                                    <strong>🔄 部分完成 (${result.contentStatus.partialTabs.length}个):</strong>
                                    <div style="margin-top: 5px;">
                                        ${result.contentStatus.partialTabs.map(tab => `<span style="background: #fffbe6; color: #faad14; padding: 2px 6px; border-radius: 3px; margin: 2px; display: inline-block; font-size: 12px;">${tab}</span>`).join('')}
                                    </div>
                                </div>
                            ` : ''}
                            ${result.contentStatus.completeTabs.length > 0 ? `
                                <div style="margin-bottom: 10px;">
                                    <strong>✅ 完整内容 (${result.contentStatus.completeTabs.length}个):</strong>
                                    <div style="margin-top: 5px;">
                                        ${result.contentStatus.completeTabs.map(tab => `<span style="background: #f6ffed; color: #52c41a; padding: 2px 6px; border-radius: 3px; margin: 2px; display: inline-block; font-size: 12px;">${tab}</span>`).join('')}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        
                        ${result.errors.length === 0 && result.warnings.length === 0 ? `
                            <div class="no-issues">🎉 该API渲染完全正常，无任何问题！</div>
                        ` : ''}
                    </div>
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>`;
  }
}
