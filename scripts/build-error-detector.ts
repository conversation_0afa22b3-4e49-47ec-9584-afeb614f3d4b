#!/usr/bin/env tsx

/**
 * 🏗️ 构建错误检测和修复脚本
 * 
 * 检测 npm run build 错误并尝试自动修复
 */

import { execSync, spawn } from 'child_process';
import chalk from 'chalk';
import fs from 'fs-extra';
import path from 'path';

interface BuildError {
  type: 'typescript' | 'syntax' | 'import' | 'unknown';
  file: string;
  line?: number;
  message: string;
  suggestion?: string;
}

/**
 * 🔍 解析构建错误
 */
function parseBuildErrors(output: string): BuildError[] {
  const errors: BuildError[] = [];
  const lines = output.split('\n');

  for (const line of lines) {
    // TypeScript错误模式
    const tsMatch = line.match(/(.+\.tsx?)(\((\d+),\d+\))?: error TS\d+: (.+)/);
    if (tsMatch) {
      errors.push({
        type: 'typescript',
        file: tsMatch[1],
        line: parseInt(tsMatch[3]) || undefined,
        message: tsMatch[4],
        suggestion: getTypeScriptSuggestion(tsMatch[4])
      });
      continue;
    }

    // 语法错误模式
    const syntaxMatch = line.match(/SyntaxError: (.+) in (.+)/);
    if (syntaxMatch) {
      errors.push({
        type: 'syntax',
        file: syntaxMatch[2],
        message: syntaxMatch[1],
        suggestion: getSyntaxSuggestion(syntaxMatch[1])
      });
      continue;
    }

    // 导入错误模式
    const importMatch = line.match(/Module not found: (.+)/);
    if (importMatch) {
      errors.push({
        type: 'import',
        file: 'unknown',
        message: importMatch[1],
        suggestion: '检查导入路径和依赖安装'
      });
      continue;
    }
  }

  return errors;
}

/**
 * 💡 获取TypeScript错误建议
 */
function getTypeScriptSuggestion(message: string): string {
  if (message.includes('Cannot find name')) {
    return '检查变量声明和导入语句';
  }
  if (message.includes('Type')) {
    return '检查类型定义和接口匹配';
  }
  if (message.includes('Property') && message.includes('does not exist')) {
    return '检查对象属性名和接口定义';
  }
  return '检查TypeScript语法和类型定义';
}

/**
 * 💡 获取语法错误建议
 */
function getSyntaxSuggestion(message: string): string {
  if (message.includes('template literal')) {
    return '检查模板字符串语法，避免使用 ${} 插值';
  }
  if (message.includes('Unexpected token')) {
    return '检查括号、引号和分号';
  }
  if (message.includes('Invalid regular expression')) {
    return '检查正则表达式语法';
  }
  return '检查JavaScript/TypeScript基础语法';
}

/**
 * 🔧 自动修复常见错误
 */
async function autoFixErrors(errors: BuildError[]): Promise<{
  fixed: number;
  failed: BuildError[];
}> {
  let fixedCount = 0;
  const failedErrors: BuildError[] = [];

  for (const error of errors) {
    try {
      if (error.type === 'syntax' && error.file !== 'unknown') {
        const success = await fixSyntaxError(error);
        if (success) {
          fixedCount++;
          console.log(`${chalk.green('✅ 修复:')} ${error.file} - ${error.message}`);
        } else {
          failedErrors.push(error);
        }
      } else {
        failedErrors.push(error);
      }
    } catch (err) {
      failedErrors.push(error);
    }
  }

  return { fixed: fixedCount, failed: failedErrors };
}

/**
 * 🔧 修复语法错误
 */
async function fixSyntaxError(error: BuildError): Promise<boolean> {
  const { file, message } = error;
  
  if (!await fs.pathExists(file)) {
    return false;
  }

  let content = await fs.readFile(file, 'utf-8');
  let modified = false;

  // 修复模板字符串插值错误
  if (message.includes('template literal')) {
    // 将 ${xxx} 替换为 + xxx +（简单修复）
    const templateRegex = /`([^`]*)\$\{([^}]+)\}([^`]*)`/g;
    if (templateRegex.test(content)) {
      content = content.replace(templateRegex, "'$1' + $2 + '$3'");
      modified = true;
    }
  }

  // 修复其他常见语法错误...
  // (可以根据需要添加更多修复规则)

  if (modified) {
    await fs.writeFile(file, content, 'utf-8');
    return true;
  }

  return false;
}

/**
 * 🎯 运行构建检测
 */
async function runBuildCheck(): Promise<{
  success: boolean;
  errors: BuildError[];
  output: string;
}> {
  console.log(`${chalk.blue('🏗️  运行构建检测...')}`);
  
  try {
    const output = execSync('npm run build', { 
      encoding: 'utf-8',
      timeout: 120000, // 2分钟超时
      cwd: process.cwd()
    });

    console.log(`${chalk.green('✅ 构建成功!')}`);
    return {
      success: true,
      errors: [],
      output: output.toString()
    };

  } catch (error) {
    const output = error.stdout?.toString() || error.stderr?.toString() || error.message;
    const errors = parseBuildErrors(output);

    console.log(`${chalk.red('❌ 构建失败')}`);
    console.log(`${chalk.yellow('🔍 发现错误:')} ${errors.length} 个`);

    return {
      success: false,
      errors,
      output
    };
  }
}

/**
 * 📋 格式化错误报告
 */
function formatErrorReport(errors: BuildError[]): string {
  if (errors.length === 0) {
    return `${chalk.green('🎉 没有发现构建错误!')}`;
  }

  let report = `\n${chalk.red('🚨 构建错误报告')}\n`;
  report += `${'─'.repeat(50)}\n`;

  errors.forEach((error, index) => {
    report += `\n${chalk.yellow(`${index + 1}.`)} ${chalk.red(error.type.toUpperCase())} 错误\n`;
    report += `   📁 文件: ${chalk.cyan(error.file)}\n`;
    if (error.line) {
      report += `   📍 行号: ${chalk.yellow(error.line)}\n`;
    }
    report += `   💬 消息: ${error.message}\n`;
    if (error.suggestion) {
      report += `   💡 建议: ${chalk.green(error.suggestion)}\n`;
    }
  });

  return report;
}

/**
 * 🚀 主函数
 */
async function main() {
  const autoFix = process.argv.includes('--auto-fix');
  
  try {
    // 1. 运行构建检测
    const buildResult = await runBuildCheck();
    
    if (buildResult.success) {
      process.exit(0);
    }

    // 2. 显示错误报告
    const errorReport = formatErrorReport(buildResult.errors);
    console.log(errorReport);

    // 3. 尝试自动修复（如果启用）
    if (autoFix && buildResult.errors.length > 0) {
      console.log(`\n${chalk.blue('🔧 尝试自动修复...')}`);
      
      const fixResult = await autoFixErrors(buildResult.errors);
      
      if (fixResult.fixed > 0) {
        console.log(`${chalk.green('✅ 自动修复:')} ${fixResult.fixed} 个错误`);
        
        // 重新运行构建检测
        console.log(`${chalk.blue('🔄 重新检测构建...')}`);
        const retryResult = await runBuildCheck();
        
        if (retryResult.success) {
          console.log(`${chalk.green('🎉 修复成功! 构建通过')}`);
          process.exit(0);
        } else {
          console.log(`${chalk.yellow('⚠️  仍有未修复的错误')}`);
          console.log(formatErrorReport(retryResult.errors));
        }
      } else {
        console.log(`${chalk.yellow('⚠️  无法自动修复这些错误，需要手动处理')}`);
      }
    }

    process.exit(1);

  } catch (error) {
    console.error(`${chalk.red('❌ 检测失败:')} ${error.message}`);
    process.exit(1);
  }
}

// 🏃‍♂️ 执行主函数
if (import.meta.url === 'file://' + process.argv[1]) {
  main().catch(console.error);
}

// 📤 导出函数供其他脚本使用
export { runBuildCheck, parseBuildErrors, autoFixErrors, type BuildError }; 