#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'src/data/ecma/const-let/business-scenarios.ts',
  'src/data/ecma/const-let/basic-info.ts',
  'src/data/ecma/destructuring/business-scenarios.ts',
  'src/data/ecma/destructuring/debugging-tips.ts',
  'src/data/ecma/destructuring/common-questions.ts',
  'src/data/ecma/destructuring/interview-questions.ts',
  'src/data/ecma/destructuring/basic-info.ts',
  'src/data/ecma/classes/business-scenarios.ts',
  'src/data/ecma/classes/common-questions.ts',
  'src/data/ecma/classes/interview-questions.ts',
  'src/data/ecma/classes/basic-info.ts',
  'src/data/ecma/optional-chaining/debugging-tips.ts',
  'src/data/ecma/async-await/business-scenarios.ts',
  'src/data/ecma/async-await/interview-questions.ts',
  'src/data/ecma/async-await/basic-info.ts',
  'src/data/ecma/promises/business-scenarios.ts',
  'src/data/ecma/promises/debugging-tips.ts',
  'src/data/ecma/promises/interview-questions.ts',
  'src/data/ecma/promises/basic-info.ts',
  'src/data/ecma/arrow-functions/performance-optimization.ts',
  'src/data/ecma/arrow-functions/business-scenarios.ts',
  'src/data/ecma/arrow-functions/debugging-tips.ts',
  'src/data/ecma/arrow-functions/common-questions.ts',
  'src/data/ecma/object-values/debugging-tips.ts',
  'src/data/ecma/object-values/common-questions.ts',
  'src/data/ecma/object-values/interview-questions.ts',
  'src/data/ecma/object-values/basic-info.ts',
  'src/data/ecma/symbols/interview-questions.ts',
  'src/data/ecma/nullish-coalescing/performance-optimization.ts',
  'src/data/ecma/nullish-coalescing/business-scenarios.ts',
  'src/data/ecma/nullish-coalescing/debugging-tips.ts',
  'src/data/ecma/nullish-coalescing/common-questions.ts',
  'src/data/ecma/nullish-coalescing/interview-questions.ts',
  'src/data/ecma/nullish-coalescing/basic-info.ts',
  'src/data/ecma/generators/business-scenarios.ts',
  'src/data/ecma/generators/debugging-tips.ts',
  'src/data/ecma/generators/common-questions.ts',
  'src/data/ecma/generators/interview-questions.ts',
  'src/data/ecma/generators/basic-info.ts',
  'src/data/ecma/modules/business-scenarios.ts',
  'src/data/ecma/modules/interview-questions.ts',
  'src/data/ecma/object-entries/performance-optimization.ts',
  'src/data/ecma/object-entries/business-scenarios.ts',
  'src/data/ecma/object-entries/debugging-tips.ts',
  'src/data/ecma/object-entries/interview-questions.ts',
  'src/data/ecma/object-entries/basic-info.ts',
  'src/data/ecma/iterators/business-scenarios.ts',
  'src/data/ecma/iterators/debugging-tips.ts',
  'src/data/ecma/string-padding/performance-optimization.ts',
  'src/data/ecma/string-padding/business-scenarios.ts',
  'src/data/ecma/string-padding/debugging-tips.ts',
  'src/data/ecma/string-padding/common-questions.ts',
  'src/data/ecma/string-padding/interview-questions.ts',
  'src/data/ecma/string-padding/basic-info.ts'
];

function fixTemplateStrings(content) {
  // 修复模板字符串插值语法
  // 匹配 ${variable} 模式，但排除代码示例中的正常用法
  
  // 1. 修复简单的变量插值
  content = content.replace(/`([^`]*)\$\{([^}]+)\}([^`]*)`/g, (match, before, variable, after) => {
    // 如果是在代码示例中，保持原样
    if (before.includes('//') || before.includes('/*') || match.includes('console.log')) {
      return match;
    }
    
    // 转换为字符串拼接
    const beforePart = before ? `'${before}' + ` : '';
    const afterPart = after ? ` + '${after}'` : '';
    return `${beforePart}${variable}${afterPart}`;
  });
  
  // 2. 修复更复杂的模板字符串
  content = content.replace(/`([^`]*\$\{[^}]+\}[^`]*)`/g, (match) => {
    // 如果是在代码示例中，保持原样
    if (match.includes('//') || match.includes('/*') || match.includes('console.log')) {
      return match;
    }
    
    // 简单替换为普通字符串（需要手动检查）
    return match.replace(/`/g, "'").replace(/\$\{([^}]+)\}/g, "' + $1 + '");
  });
  
  return content;
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixTemplateStrings(content);
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`✅ 修复: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  跳过: ${filePath} (无需修复)`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 错误: ${filePath} - ${error.message}`);
    return false;
  }
}

function main() {
  console.log('🔧 开始修复模板字符串问题...\n');
  
  let fixedCount = 0;
  let totalCount = 0;
  
  for (const filePath of filesToFix) {
    totalCount++;
    if (processFile(filePath)) {
      fixedCount++;
    }
  }
  
  console.log(`\n📊 修复完成:`);
  console.log(`   总文件数: ${totalCount}`);
  console.log(`   修复文件数: ${fixedCount}`);
  console.log(`   跳过文件数: ${totalCount - fixedCount}`);
}

if (require.main === module) {
  main();
}
