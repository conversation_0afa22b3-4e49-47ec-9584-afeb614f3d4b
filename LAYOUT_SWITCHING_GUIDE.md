# 布局切换功能使用指南

## 功能概述

我们为所有的 API 速查手册页面（ECMAScript、React、Vue）添加了布局切换功能，用户可以在传统的卡片布局和新的树状布局之间自由切换。

## 功能特点

### 🎯 双布局模式
- **卡片布局**：传统的瀑布流卡片展示，适合快速浏览和搜索
- **树状布局**：左右分栏的树状导航，适合深度学习和系统性浏览

### 🎨 主题适配
- **ECMAScript**：黄橙色渐变按钮
- **React**：蓝青色渐变按钮  
- **Vue**：绿翠色渐变按钮

### 💾 偏好记忆
- 用户的布局选择会自动保存到 localStorage
- 下次访问时会自动恢复上次选择的布局模式
- 每个页面的偏好独立保存

## 使用方法

### 1. 访问页面
打开任意 API 速查手册页面：
- http://localhost:8081/ecma
- http://localhost:8081/react  
- http://localhost:8081/vue

### 2. 切换布局
点击右上角的布局切换按钮：
- 🔲 **卡片布局** → 点击显示 "树状布局"
- 🔳 **树状布局** → 点击显示 "卡片布局"

### 3. 布局特点

#### 卡片布局
- 瀑布流展示所有 API 卡片
- 支持搜索、过滤、排序
- 点击卡片打开详情抽屉
- 适合快速查找特定 API

#### 树状布局  
- 左侧：分类树状导航
- 右侧：详细内容展示
- 支持多级分类浏览
- 适合系统性学习

## 技术实现

### 文件结构
```
src/pages/
├── EcmaCheatSheet.tsx    # ECMAScript 页面（已更新）
├── ReactCheatSheet.tsx   # React 页面（已更新）
└── VueCheatSheet.tsx     # Vue 页面（已更新）

src/components/
├── CheatSheet.tsx              # 原始卡片布局组件
└── CheatSheet-TreeLayout.tsx   # 新的树状布局组件
```

### 状态管理
- 使用 React useState 管理当前布局模式
- 使用 localStorage 持久化用户偏好
- 每个页面独立的存储键：
  - `ecma-layout-mode`
  - `react-layout-mode` 
  - `vue-layout-mode`

### 样式设计
- 固定定位的切换按钮（右上角）
- 渐变色主题适配
- 悬停动画效果
- 阴影和圆角设计

## 开发说明

### 添加新页面支持
如需为新页面添加布局切换功能，参考以下模式：

```typescript
import React, { useState, useEffect } from 'react';
import { Button } from 'antd';
import { AppstoreOutlined, BarsOutlined } from '@ant-design/icons';
import CheatSheet from '@/components/CheatSheet';
import CheatSheetTreeLayout from '@/components/CheatSheet-TreeLayout';

type LayoutMode = 'card' | 'tree';

const YourPage: React.FC = () => {
  const [layoutMode, setLayoutMode] = useState<LayoutMode>(() => {
    const saved = localStorage.getItem('your-page-layout-mode');
    return (saved as LayoutMode) || 'card';
  });

  useEffect(() => {
    localStorage.setItem('your-page-layout-mode', layoutMode);
  }, [layoutMode]);

  const toggleLayout = () => {
    setLayoutMode(prev => prev === 'card' ? 'tree' : 'card');
  };

  return (
    <div className="relative">
      {/* 布局切换按钮 */}
      <div className="fixed top-6 right-6 z-50">
        <Button
          type="primary"
          size="large"
          icon={layoutMode === 'card' ? <BarsOutlined /> : <AppstoreOutlined />}
          onClick={toggleLayout}
          className="shadow-lg hover:shadow-xl transition-all duration-200"
        >
          {layoutMode === 'card' ? '树状布局' : '卡片布局'}
        </Button>
      </div>

      {/* 条件渲染 */}
      {layoutMode === 'card' ? (
        <CheatSheet {...props} />
      ) : (
        <CheatSheetTreeLayout {...props} />
      )}
    </div>
  );
};
```

## 注意事项

1. **组件兼容性**：确保 CheatSheet-TreeLayout 组件支持所需的 props
2. **数据格式**：两种布局使用相同的数据源，确保数据格式兼容
3. **性能考虑**：布局切换时会重新渲染整个组件树
4. **响应式设计**：切换按钮在移动端可能需要调整位置

## 未来改进

- [ ] 添加布局切换动画效果
- [ ] 支持键盘快捷键切换
- [ ] 添加布局预览功能
- [ ] 优化移动端体验
- [ ] 添加更多布局选项
