<!DOCTYPE html>
<html>
<head>
    <title>Mermaid Test</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
    <h1>Mermaid Test</h1>
    
    <h2>Test 1: Simple Graph</h2>
    <div class="mermaid">
graph TD
    A[Promise创建] --> B[pending等待态]
    B --> C{executor执行器函数}
    C -->|调用resolve| D[fulfilled完成态]
    C -->|调用reject| E[rejected拒绝态]
    C -->|抛出异常| E

    D --> F[触发onFulfilled回调]
    E --> G[触发onRejected回调]

    F --> H[微任务队列]
    G --> H
    H --> I[执行回调函数]

    J[调用then/catch] --> K[注册回调函数]
    K --> L[返回新Promise]

    style A fill:#e1f5fe,stroke:#01579b
    style B fill:#fff3e0,stroke:#ef6c00
    style D fill:#e8f5e8,stroke:#2e7d32
    style E fill:#ffebee,stroke:#c62828
    style H fill:#f3e5f5,stroke:#4a148c
    </div>

    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
</body>
</html>
