// W3C新API版本 + 自动定时检测发送 + Accept All支持
(function() {
    // 🚀 方法1: 使用Template元素（W3C标准，不受TrustedHTML限制）
    window.setContentViaTemplate = function(element, htmlString) {
        try {
            var template = document.createElement('template');
            template.innerHTML = htmlString; // Template内部不受TrustedHTML限制
            
            // 清空目标元素
            while (element.firstChild) {
                element.removeChild(element.firstChild);
            }
            
            // 克隆template内容
            var content = template.content.cloneNode(true);
            element.appendChild(content);
            
            return true;
        } catch (e) {
            console.log('Template方法失败:', e);
            return false;
        }
    };
    
    // 🚀 方法2: 使用execCommand（老API但可能绕过检查）
    window.setContentViaExecCommand = function(element, text) {
        try {
            element.focus();
            
            // 选中全部内容
            var range = document.createRange();
            range.selectNodeContents(element);
            var selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
            
            // 使用execCommand插入文本
            document.execCommand('insertText', false, text);
            
            return true;
        } catch (e) {
            console.log('execCommand方法失败:', e);
            return false;
        }
    };
    
    // 🚀 方法3: 使用DocumentFragment + Range
    window.setContentViaFragment = function(element, htmlString) {
        try {
            var range = document.createRange();
            
            // 先尝试createContextualFragment
            try {
                var fragment = range.createContextualFragment(htmlString);
                
                // 清空元素
                while (element.firstChild) {
                    element.removeChild(element.firstChild);
                }
                
                element.appendChild(fragment);
                return true;
            } catch (e) {
                // 如果失败，使用纯文本方式
                range.selectNodeContents(element);
                range.deleteContents();
                
                var textNode = document.createTextNode(htmlString.replace(/<[^>]*>/g, ''));
                range.insertNode(textNode);
                return true;
            }
        } catch (e) {
            console.log('Fragment方法失败:', e);
            return false;
        }
    };
    
    // 🚀 方法4: 使用Clipboard API（现代浏览器）
    window.setContentViaClipboard = function(element, text) {
        try {
            element.focus();
            
            // 选中全部内容
            var range = document.createRange();
            range.selectNodeContents(element);
            var selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
            
            // 创建模拟粘贴事件
            var pasteEvent = new ClipboardEvent('paste', {
                clipboardData: new DataTransfer()
            });
            
            pasteEvent.clipboardData.setData('text/plain', text);
            element.dispatchEvent(pasteEvent);
            
            return true;
        } catch (e) {
            console.log('Clipboard方法失败:', e);
            return false;
        }
    };
    
    // 🚀 方法5: 使用Shadow DOM（Web Components）
    window.setContentViaShadowDOM = function(element, htmlString) {
        try {
            // 创建一个新的元素作为Shadow Host
            var shadowHost = document.createElement('div');
            var shadowRoot = shadowHost.attachShadow({mode: 'open'});
            
            // 在Shadow DOM中设置内容（可能不受TrustedHTML限制）
            shadowRoot.innerHTML = htmlString;
            
            // 将Shadow DOM的内容移动到目标元素
            while (element.firstChild) {
                element.removeChild(element.firstChild);
            }
            
            while (shadowRoot.firstChild) {
                element.appendChild(shadowRoot.firstChild);
            }
            
            return true;
        } catch (e) {
            console.log('Shadow DOM方法失败:', e);
            return false;
        }
    };

    // 🎯 新增：Accept All按钮点击功能
    window.clickAcceptAll = function() {
        console.log('🔍 寻找Accept All按钮...');
        
        // 方法1：通过文本内容查找
        var acceptButtons = Array.from(document.querySelectorAll('*')).filter(function(el) {
            return el.textContent && el.textContent.trim().includes('Accept all');
        });
        
        if (acceptButtons.length > 0) {
            var button = acceptButtons[0];
            console.log('✅ 找到Accept All按钮:', button);
            
            try {
                // 尝试点击元素本身
                button.click();
                console.log('🎯 已点击Accept All按钮');
                return true;
            } catch (e) {
                console.log('❌ 点击Accept All失败:', e);
            }
        }
        
        // 方法2：通过CSS类名查找
        var secondaryButtons = document.querySelectorAll('.anysphere-secondary-button');
        for (var i = 0; i < secondaryButtons.length; i++) {
            if (secondaryButtons[i].textContent && secondaryButtons[i].textContent.includes('Accept all')) {
                try {
                    secondaryButtons[i].click();
                    console.log('🎯 通过CSS类名点击Accept All成功');
                    return true;
                } catch (e) {
                    console.log('❌ 通过CSS类名点击失败:', e);
                }
            }
        }
        
        // 方法3：通过快捷键模拟
        console.log('🔄 尝试使用快捷键 ⌘⏎');
        try {
            var event = new KeyboardEvent('keydown', {
                key: 'Enter',
                metaKey: true,  // ⌘ 键
                bubbles: true
            });
            document.dispatchEvent(event);
            console.log('⌨️ 已触发快捷键 ⌘⏎');
            return true;
        } catch (e) {
            console.log('❌ 快捷键触发失败:', e);
        }
        
        console.log('❌ 未找到Accept All按钮');
        return false;
    };
    
    // 🔍 检测发送按钮状态
    window.checkSendButtonStatus = function() {
        // 查找包含codicon-arrow-up-two的按钮
        var sendButton = document.querySelector('.codicon-arrow-up-two');
        
        if (sendButton) {
            console.log('✅ 找到发送按钮，当前可以发送');
            
            // 找到按钮的父容器，确保能点击
            var clickableButton = sendButton.closest('.anysphere-icon-button') || sendButton.parentNode;
            
            return {
                canSend: true,
                button: clickableButton,
                buttonElement: sendButton
            };
        } else {
            console.log('❌ 未找到发送按钮或当前不可发送');
            return {
                canSend: false,
                button: null,
                buttonElement: null
            };
        }
    };
    
    // 🎯 超级更新函数 - 尝试所有新API方法
    window.superUpdateLexicalEditor = function(newText) {
        var inputBox = document.querySelector('.aislash-editor-input');
        
        if (!inputBox) {
            console.log('❌ 未找到输入框 .aislash-editor-input');
            return false;
        }
        
        console.log('🎯 找到输入框，尝试多种新API方法...');
        
        var methods = [
            {
                name: 'execCommand',
                func: function() { return setContentViaExecCommand(inputBox, newText); }
            },
            {
                name: 'Template元素',
                func: function() { return setContentViaTemplate(inputBox, '<p>' + newText + '</p>'); }
            },
            {
                name: 'DocumentFragment',
                func: function() { return setContentViaFragment(inputBox, '<p>' + newText + '</p>'); }
            },
            {
                name: 'Shadow DOM',
                func: function() { return setContentViaShadowDOM(inputBox, '<p>' + newText + '</p>'); }
            },
            {
                name: '纯DOM操作',
                func: function() {
                    // 最后的保底方法
                    while (inputBox.firstChild) {
                        inputBox.removeChild(inputBox.firstChild);
                    }
                    
                    var p = document.createElement('p');
                    var textNode = document.createTextNode(newText);
                    p.appendChild(textNode);
                    inputBox.appendChild(p);
                    
                    return true;
                }
            }
        ];
        
        for (var i = 0; i < methods.length; i++) {
            try {
                console.log('🧪 尝试方法: ' + methods[i].name);
                var success = methods[i].func();
                
                if (success) {
                    // 触发输入事件
                    inputBox.dispatchEvent(new Event('input', { bubbles: true }));
                    inputBox.dispatchEvent(new Event('change', { bubbles: true }));
                    
                    console.log('✅ 成功使用 ' + methods[i].name + ' 方法更新内容！');
                    return true;
                }
            } catch (e) {
                console.log('❌ ' + methods[i].name + ' 方法失败:', e.message);
                continue;
            }
        }
        
        console.log('❌ 所有方法都失败了');
        return false;
    };
    
    // 🚀 完整的自动任务流程（新增Accept All支持）
    window.autoTask = function() {
        console.log('🔄 开始执行自动任务...');
        
        // 步骤1：先点击Accept All按钮（保存之前的编辑）
        console.log('📋 步骤1: 点击Accept All保存之前的编辑');
        var acceptSuccess = clickAcceptAll();
        
        if (acceptSuccess) {
            console.log('✅ Accept All点击成功，等待1秒再继续...');
            // 等待Accept All处理完成
            setTimeout(function() {
                continueAutoTask();
            }, 1000);
        } else {
            console.log('⚠️ Accept All点击失败，直接继续任务');
            continueAutoTask();
        }
    };
    
    // 🔄 继续执行自动任务的主要逻辑
    window.continueAutoTask = function() {
        console.log('🔄 继续执行自动任务主逻辑...');
        
        // 步骤2: 检查发送按钮状态
        console.log('📋 步骤2: 检查发送按钮状态');
        var buttonStatus = checkSendButtonStatus();
        
        if (!buttonStatus.canSend) {
            console.log('⏸️ 当前不可发送，跳过此次任务');
            return false;
        }
        
        // 步骤3: 修改输入框内容
        console.log('📋 步骤3: 修改输入框内容');
        var targetText = `如果刚才的修复或生成工作进行了一半，请立刻继续开始！，其他情况参考，我们的核心流程(智能选择分支后，立马开始)：🤖 ECMA文档自动化检测（新版骨架字符串检测 + Puppeteer验收测试）

## 🚨 ECMA特性名称格式验证
⚠️ **必须检查**: 特性名称使用kebab-case格式！
- ❌ 错误: arrowFunctions, Arrow_Functions
- ✅ 正确: arrow-functions, optional-chaining

## 🎯 新版平铺目录结构（已重构完成）

### 📂 统一的平铺结构
\`\`\`
src/data/
├── react/
│   ├── useOptimistic/     # Hook APIs 直接平铺
│   ├── createContext/     # Component APIs 直接平铺
│   ├── useState/
│   ├── useEffect/
│   └── ... (35个API统一平铺)
└── vue/
    ├── reactive/          # Vue APIs 直接平铺
    ├── ref/
    └── ... (所有API统一平铺)
\`\`\`

**🚀 重构优势：**
- ✅ **简化维护**：无需区分hooks/components层级
- ✅ **路径统一**：所有API都是 \`framework/api-name\`
- ✅ **错误消除**：解决useOptimistic等路由加载问题

## 🔍 新版检测流程（基于骨架字符串 + Puppeteer验收）

### 📋 快速状态检测
\`\`\`bash
# 🎯 增强版检测命令 - 支持平铺结构
tsx scripts/enhanced-content-completion.ts <api-name>

# 📊 示例输出：
# ✅ 已完成: basic-info.ts
# ❌ 未完成: business-scenarios.ts (发现骨架字符串: SCENARIO_1_TITLE)
# 📈 完成进度: 3/9 (33%)
\`\`\`

## ⚡ 智能决策分支（五阶段工作流）

### A. 骨架生成（文件不存在）
\`\`\`bash
# 🏗️ 生成React API骨架
tsx scripts/generate-api-skeleton.ts <api-name> react

# 🚀 生成ECMA特性骨架
tsx scripts/generate-api-skeleton.ts <feature-name> ecma

# 📝 示例：
tsx scripts/generate-api-skeleton.ts arrow-functions ecma
tsx scripts/generate-api-skeleton.ts optional-chaining ecma
\`\`\`

### B. 内容填充（发现骨架字符串）⭐ 核心工作
\`\`\`bash
# 🚨 重要：检测到这些骨架字符串表示需要内容填充：
# basic-info → API_NAME, {API_NAME}
# business-scenarios → SCENARIO_1_TITLE
# common-questions → QUESTION_1
# debugging-tips → INTRODUCTION
# essence-insights → CORE_QUESTION
# implementation → IMPLEMENTATION_MECHANISM
# interview-questions → QUESTION_1
# knowledge-archaeology → INTRODUCTION
# performance-optimization → STRATEGY_1

# 📝 手动内容完善步骤：
# 1. 打开包含骨架字符串的Tab文件
# 2. 替换 {API_NAME} 等占位符为实际内容
# 3. 使用 Cursor + Ctrl+K + MDC模板生成内容
# 4. 重复完成所有未完成的Tab文件
# 5. 运行检测验证: tsx scripts/enhanced-content-completion.ts <api-name>
\`\`\`

### C. 构建检测与修复
\`\`\`bash
# 🏗️ 构建错误检测（新增功能）
tsx scripts/build-error-detector.ts --auto-fix

# 🔧 自动修复：
# - 模板字符串语法错误 (\${xxx})
# - TypeScript类型错误
# - 导入路径错误
\`\`\`

### 🚨 D. Puppeteer验收测试（关键质量保障）
\`\`\`bash
# 🎭 页面渲染验收测试（必需）
tsx scripts/puppeteer/index.ts <api-name> --headless

# 🎯 关键验证项目：
# ✅ 页面是否正确渲染？
# ✅ Mermaid图表是否正常显示？
# ✅ 数据结构是否与前端组件匹配？
# ✅ 是否有运行时错误？
# ✅ 组件是否正确渲染对象和数组？

# ⚠️ 重要发现：即使骨架检测显示100%完成，Puppeteer验收可能发现渲染错误！
# 📊 实测案例：useState骨架检测100%完成，但Puppeteer发现19个页面渲染错误
\`\`\`

### E. 状态更新（通过所有验收测试）
\`\`\`bash
# 🎉 只有通过所有验收测试后，才更新progress.md状态：⏳ 待完成 → ✅ 已完成
\`\`\`

## 🎯 新版核心优势

### ✅ 骨架字符串检测
- 📊 **100%准确率**: 直接检测 {API_NAME} 等占位符
- ⚡ **3-5倍更快**: 无需页面渲染，直接文件分析
- 🎯 **精确定位**: 明确显示哪个文件的哪个字段未完成

### ✅ 智能构建检测
- 🏗️ **自动运行**: npm run build 集成检测
- 🔧 **自动修复**: 常见语法错误自动处理
- 🔄 **重试机制**: 修复后自动重新验证

### 🚨 Puppeteer验收测试（新增关键环节）
- 🎭 **页面渲染验证**: 确保所有页面能够正确渲染
- 🔍 **错误监控**: 实时捕获JavaScript运行时错误
- 📊 **详细报告**: 生成HTML和JSON格式的测试报告
- 🚨 **质量保障**: 防止骨架检测通过但页面渲染失败的问题
- 🧩 **组件验证**: 确保React组件正确渲染复杂对象

### ✅ 平铺目录结构
- 📂 **统一管理**: React和Vue都采用平铺结构
- 🔄 **自动重构**: 批量目录重组工具
- 🛠️ **路径修复**: 自动更新所有引用路径

### ❌ 已抛弃的旧方案 vs ✅ 保留的重要方案
- ~hooks/components分层~ → ✅ 统一平铺结构
- ~completionStatus字段检测~ → ✅ 骨架字符串检测
- ~启发式算法判断~ → ✅ 精确字符串匹配
- ✅ **保留Puppeteer验收测试**: 骨架检测无法发现页面渲染错误，Puppeteer验收测试是必需的质量保障！

## 🎭 Puppeteer验收测试重要性

### 🚨 实际案例验证
最近测试发现的关键问题：
- **useState**: 骨架检测100%完成 ✅，但Puppeteer发现19个渲染错误 ❌
- **useOptimistic**: 骨架检测100%完成 ✅，但Puppeteer发现19个渲染错误 ❌

### 📊 常见验收失败原因
1. **"Objects are not valid as a React child"错误** - 复杂对象未正确渲染
2. **Mermaid图表显示失败** - 图表组件配置问题
3. **JavaScript运行时错误** - 代码语法或逻辑问题
4. **组件数据结构不匹配** - 前端组件期望与数据结构冲突

### 🛠️ 修复指导
\`\`\`javascript
// ❌ 常见错误：直接渲染对象
<div>{complexObject}</div>

// ✅ 正确做法：使用组件或序列化
<ObjectRenderer data={complexObject} />
// 或
<div>{JSON.stringify(complexObject, null, 2)}</div>
\`\`\`

## � ECMA特性快速开始

### ES6核心特性开发（优先级）
\`\`\`bash
# 1. 箭头函数（最高优先级）
tsx scripts/generate-api-skeleton.ts arrow-functions ecma

# 2. 解构赋值
tsx scripts/generate-api-skeleton.ts destructuring ecma

# 3. 模板字符串
tsx scripts/generate-api-skeleton.ts template-literals ecma

# 4. 块级作用域
tsx scripts/generate-api-skeleton.ts const-let ecma

# 5. 异步函数
tsx scripts/generate-api-skeleton.ts async-await ecma
\`\`\`

### ECMA检测和验收
\`\`\`bash
# 检测ECMA特性进度
tsx scripts/enhanced-content-completion.ts arrow-functions

# ECMA特性验收测试
tsx scripts/puppeteer/index.ts arrow-functions --headless
\`\`\`

## �📋 完整工作流程
严格按照 \`/scripts/api-workflow.md\` 和 \`/.cursor/rules/核心流程.mdc\` 执行

**🚀 五阶段工作流：** A(骨架生成) → B(内容填充) → C(构建检测) → D(Puppeteer验收) → E(状态更新)

**🎭 关键提醒：** 必须通过Puppeteer验收测试才能标记为完成！`;
        
        var updateSuccess = superUpdateLexicalEditor(targetText);
        
        if (!updateSuccess) {
            console.log('❌ 更新文本失败，终止任务');
            return false;
        }
        
        // 步骤4: 等待一小会儿，确保文本更新完成
        console.log('📋 步骤4: 等待500ms后发送消息');
        setTimeout(function() {
            // 步骤5: 点击发送按钮
            try {
                buttonStatus.button.click();
                console.log('🚀 已点击发送按钮！');
                
                // 显示成功提示
                console.log('✅ 自动任务执行成功：Accept All → 文本更新 → 发送完成');
                
            } catch (e) {
                console.log('❌ 点击发送按钮失败:', e);
            }
        }, 500); // 等待500ms
        
        return true;
    };
    
    // 🕒 启动定时器 - 每50秒执行一次
    var autoTaskInterval;
    
    window.startAutoTask = function() {
        if (autoTaskInterval) {
            console.log('⚠️ 自动任务已在运行');
            return;
        }
        
        console.log('🎯 启动自动任务定时器 - 每50秒检测一次');
        
        // 立即执行一次
        autoTask();
        
        // 设置定时器
        autoTaskInterval = setInterval(function() {
            console.log('⏰ 定时器触发 - ' + new Date().toLocaleTimeString());
            autoTask();
        }, 50000); // 50秒 = 50000毫秒
        
        console.log('✅ 自动任务定时器已启动');
        return autoTaskInterval;
    };
    
    window.stopAutoTask = function() {
        if (autoTaskInterval) {
            clearInterval(autoTaskInterval);
            autoTaskInterval = null;
            console.log('🛑 自动任务定时器已停止');
            return true;
        } else {
            console.log('⚠️ 没有运行中的自动任务');
            return false;
        }
    };

    // 🛑 全局停止函数（用户请求的新功能）
    window.stop = function() {
        console.log('🛑 执行全局停止命令...');
        var stopped = stopAutoTask();
        if (stopped) {
            console.log('✅ 自动任务已成功停止！');
            console.log('💡 如需重新启动，请输入: startAutoTask()');
        } else {
            console.log('⚠️ 当前没有运行中的任务');
        }
        return stopped;
    };
    
    // 🧪 测试所有新API的函数
    window.testAllAPIs = function() {
        var testDiv = document.createElement('div');
        testDiv.id = 'test-new-apis';
        testDiv.style.border = '2px solid blue';
        testDiv.style.padding = '10px';
        testDiv.style.margin = '10px';
        document.body.appendChild(testDiv);
        
        console.log('🧪 测试所有W3C新API...');
        
        var testHTML = '<p style="color: red; font-weight: bold;">🎉 测试成功！</p>';
        var methods = [
            { name: 'Template', func: function() { return setContentViaTemplate(testDiv, testHTML); } },
            { name: 'DocumentFragment', func: function() { return setContentViaFragment(testDiv, testHTML); } },
            { name: 'Shadow DOM', func: function() { return setContentViaShadowDOM(testDiv, testHTML); } }
        ];
        
        for (var i = 0; i < methods.length; i++) {
            try {
                if (methods[i].func()) {
                    console.log('✅ ' + methods[i].name + ' API 测试成功！');
                    break;
                }
            } catch (e) {
                console.log('❌ ' + methods[i].name + ' 失败:', e.message);
            }
        }
        
        // 5秒后清理
        setTimeout(function() {
            if (document.getElementById('test-new-apis')) {
                document.body.removeChild(testDiv);
            }
        }, 5000);
    };
    
    // 添加快捷测试函数
    window.quickTestNewAPI = function() {
        console.log('🚀 快速测试新API方法...');
        superUpdateLexicalEditor('这是使用W3C新API的测试消息 - ' + new Date().toLocaleTimeString());
    };

    // 🧪 测试Accept All功能
    window.testAcceptAll = function() {
        console.log('🧪 测试Accept All功能...');
        return clickAcceptAll();
    };
    
    // 🎯 启动自动任务
    startAutoTask();
    
    console.log('🚀 W3C新API脚本已加载！自动任务已启动！');
    console.log('📖 使用方法：');
    console.log('1. startAutoTask() - 启动50秒定时自动任务');
    console.log('2. stopAutoTask() - 停止自动任务');
    console.log('3. stop() - 🛑 全局停止命令（推荐）');
    console.log('4. autoTask() - 手动执行一次完整任务');
    console.log('5. checkSendButtonStatus() - 检查发送按钮状态');
    console.log('6. superUpdateLexicalEditor("文本") - 手动更新文本');
    console.log('7. clickAcceptAll() - 手动点击Accept All');
    console.log('8. testAcceptAll() - 测试Accept All功能');
    console.log('9. quickTestNewAPI() - 快速测试功能');
    console.log('');
    console.log('🎯 新增功能：');
    console.log('• 自动点击Accept All保存之前的编辑');
    console.log('• 全局stop()函数，可在控制台输入stop()停止任务');
    console.log('• 优化了任务执行流程：Accept All → 文本编辑 → 发送');
    console.log('• 🔄 已更新为新版骨架字符串检测工作流程');
    console.log('• 🎭 新增Puppeteer验收测试支持，确保页面渲染质量');
    console.log('');
    console.log('🎭 API文档工作流程（五阶段）：');
    console.log('A. 骨架生成：tsx scripts/generate-api-skeleton.ts <api-name>');
    console.log('B. 内容填充：手动编辑Tab文件，替换{XXX}占位符');
    console.log('C. 构建检测：tsx scripts/build-error-detector.ts --auto-fix');
    console.log('D. 🚨 Puppeteer验收：tsx scripts/puppeteer/index.ts <api-name> --headless');
    console.log('E. 状态更新：通过验收测试后更新progress.md');
    console.log('');
    console.log('⚠️ 重要提醒：');
    console.log('• 骨架检测100%完成 ≠ 页面渲染正常');
    console.log('• 必须通过Puppeteer验收测试才能标记为完成');
    console.log('• 验收测试发现"Objects are not valid as a React child"等渲染错误');
})(); 