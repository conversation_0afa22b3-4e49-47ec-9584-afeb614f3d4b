# 🤖 Cursor对话监控器

自动监控Cursor编辑器的对话状态，在检测到对话结束后自动发送继续消息。

## 🎯 功能特性

- ✅ **智能检测**：监控Cursor编辑器的活动状态
- ⏰ **自动触发**：在对话空闲指定时间后自动发送消息
- 🔧 **可配置**：支持自定义消息内容、时间阈值等参数
- 📱 **系统通知**：实时显示监控状态和操作结果
- 📝 **日志记录**：完整记录监控活动和操作历史

## 📦 方案选择

### 方案一：AppleScript版本 (推荐新手)
- **适用场景**：简单易用，适合Mac用户
- **优点**：无需额外依赖，系统原生支持
- **文件**：`cursor_monitor.applescript`

### 方案二：Node.js版本 (推荐进阶)
- **适用场景**：功能丰富，可扩展性强
- **优点**：支持更多配置选项和高级功能
- **文件**：`cursor-monitor.cjs`

### 方案三：Shell脚本版本 (推荐极简)
- **适用场景**：轻量级，资源占用少
- **优点**：简单直接，易于理解和修改
- **文件**：`cursor_monitor.sh`

## 🚀 快速开始

### 方案一：AppleScript版本

1. **设置权限**
   ```bash
   # 确保系统允许AppleScript控制其他应用
   # 前往：系统设置 > 隐私与安全性 > 辅助功能
   # 添加并允许 AppleScript Editor 和 Script Editor
   ```

2. **配置脚本**
   - 打开AppleScript Editor
   - 复制`cursor_monitor.applescript`的内容
   - 修改配置参数（如需要）：
     ```applescript
     set conversationEndThreshold to 300 -- 5分钟阈值
     set messageToSend to "请继续刚才的完善工作" -- 自定义消息
     ```

3. **保存并运行**
   - 选择"文件" > "存储"
   - 文件格式选择"应用程序"
   - ✅ 勾选"运行后保持打开"
   - 保存并运行应用程序

### 方案二：Node.js版本

1. **安装依赖**
   ```bash
   # 确保已安装Node.js
   node --version
   ```

2. **运行监控器**
   ```bash
   # 基本运行
   node cursor-monitor.cjs
   
   # 自定义参数
   node cursor-monitor.cjs --message "继续优化代码" --threshold 600
   
   # 查看帮助
   node cursor-monitor.cjs --help
   ```

3. **后台运行**
   ```bash
   # 使用nohup后台运行
   nohup node cursor-monitor.cjs > monitor.log 2>&1 &
   
   # 或者使用pm2管理
   npm install -g pm2
   pm2 start cursor-monitor.cjs --name cursor-monitor
   ```

### 方案三：Shell脚本版本

1. **设置执行权限**
   ```bash
   chmod +x cursor_monitor.sh
   ```

2. **运行监控器**
   ```bash
   # 基本运行
   ./cursor_monitor.sh
   
   # 自定义参数
   ./cursor_monitor.sh --message "继续完善" --threshold 600
   
   # 查看帮助
   ./cursor_monitor.sh --help
   ```

3. **后台运行**
   ```bash
   # 后台运行
   nohup ./cursor_monitor.sh > monitor.log 2>&1 &
   ```

## ⚙️ 配置参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `conversationEndThreshold` | 对话结束判定时间(秒) | 300 | 600 |
| `messageToSend` | 自动发送的消息内容 | "请继续刚才的完善工作" | "继续优化代码" |
| `checkInterval` | 检查间隔时间(秒) | 30 | 60 |
| `appName` | 目标应用名称 | "Cursor" | "Cursor" |

## 🔧 系统要求

- **操作系统**：macOS 10.14+
- **权限要求**：
  - 辅助功能权限（用于模拟键盘操作）
  - 通知权限（用于系统通知）
- **依赖软件**：
  - AppleScript版本：无额外依赖
  - Node.js版本：Node.js 12+
  - Shell版本：bash 4+

## 🛠️ 权限设置

### 1. 辅助功能权限
```
系统设置 > 隐私与安全性 > 辅助功能
添加并启用：
- AppleScript Editor
- Terminal (如果使用命令行运行)
- 你的脚本应用程序
```

### 2. 自动化权限
```
系统设置 > 隐私与安全性 > 自动化
允许脚本控制：
- System Events
- Cursor
```

## 📱 使用流程

1. **启动监控**：运行对应的脚本
2. **正常使用**：在Cursor中进行对话
3. **自动检测**：脚本检测到你离开Cursor或长时间无活动
4. **自动发送**：达到阈值时间后自动发送继续消息
5. **继续对话**：返回Cursor继续你的工作

## 🎯 实用技巧

### 1. 开机自启动
```bash
# 将脚本添加到启动项
# macOS: 系统设置 > 通用 > 登录项 > 添加应用程序
```

### 2. 自定义消息
根据不同场景使用不同的消息：
- 代码优化：`"继续优化代码，注意性能和可读性"`
- 功能开发：`"请继续完善这个功能的实现"`
- 调试问题：`"继续分析和解决这个问题"`

### 3. 时间调优
根据你的工作习惯调整时间阈值：
- 专注工作：300秒（5分钟）
- 日常开发：600秒（10分钟）
- 长期项目：900秒（15分钟）

## 🐛 常见问题

### Q: 脚本提示权限错误？
A: 检查系统设置中的辅助功能和自动化权限，确保脚本有权控制其他应用。

### Q: 消息没有自动发送？
A: 检查Cursor是否在运行，确认脚本正常监控，查看日志文件排查问题。

### Q: 如何停止监控？
A: 
- AppleScript版本：退出应用程序
- Node.js版本：`Ctrl+C` 或 `pm2 stop cursor-monitor`
- Shell版本：`Ctrl+C` 或杀死进程

### Q: 可以监控其他编辑器吗？
A: 可以，修改配置中的`appName`参数为目标应用名称。

## 🔍 日志查看

```bash
# 查看实时日志
tail -f cursor-monitor.log

# 查看历史日志
cat cursor-monitor.log | grep "消息发送"
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License - 详见LICENSE文件

## 🙏 致谢

感谢以下资源和项目的启发：
- [GitHub: cursor-chat-export](https://github.com/somogyijanos/cursor-chat-export)
- [GitHub: awesome-cursor-mpc-server](https://github.com/kleneway/awesome-cursor-mpc-server)
- Apple开发者文档中的AppleScript指南

---

**🎯 提示**：首次使用建议从Shell脚本版本开始，熟悉后可以升级到Node.js版本获得更多功能。
