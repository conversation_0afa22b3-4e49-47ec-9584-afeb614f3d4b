{"permissions": {"allow": ["Bash(npm run build:*)", "WebFetch(domain:localhost)", "Bash(grep:*)", "Bash(npx tsc:*)", "Bash(for file in implementation.ts interview-questions.ts performance-optimization.ts)", "Bash(do echo \"=== $file ===\")", "Bash(done)", "Bash(rg:*)", "Bash(node:*)", "Bash(rm:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(tsx scripts/enhanced-content-completion.ts:*)", "Bash(tsx scripts/puppeteer/index.ts:*)", "Bash(tsx scripts/generate-api-skeleton.ts:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(find:*)", "Bash(ls:*)"], "deny": []}}